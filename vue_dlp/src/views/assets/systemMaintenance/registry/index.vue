<template>
  <div class="app-container">
    <TransferTree
      ref="transferTree"
      :show-tree.sync="showTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      :terminal-filter-key="terminalFilter"
      @data-change="strategyTargetNodeChange"
    />
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" :disabled="refreshDisable" size="mini" @click="refreshRegistry(undefined, undefined)">
          {{ $t('button.refresh') }}
        </el-button>
        <div class="breadcrumb" :style="`width: calc(100% - ${ showTree ? '220' : '235'}px);`" :title="breadcrumbItem.join('\\')">
          <el-breadcrumb separator="\">
            <el-breadcrumb-item v-for="(item, index) in breadcrumbItem" :key="index">{{ item }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <el-tooltip
          :content="$t('pages.lockScreenText19')"
          placement="bottom"
        >
          <el-button size="mini" style="float: right" icon="el-icon-document-copy" @click="copyPath"></el-button>
        </el-tooltip>
      </div>
      <div class="tableBox" style="height: calc(100% - 40px);">
        <div class="tree-container" style="width: 300px">
          <tree-menu
            ref="registryItemTree"
            :width="290"
            :data="registryTreeData"
            :is-filter="true"
            :resizeable="true"
            :expand-on-click-node="false"
            :render-content="renderContent"
            @node-click="nodeClick"
          />
        </div>
        <div class="table-container">
          <div>
            <el-button size="mini" :disabled="!addable" icon="el-icon-plus" style="margin-bottom: 2px" @click.native="handleAddKV">{{ $t('button.add') }}</el-button>
          </div>
          <grid-table
            ref="registryKVTable"
            v-loading="tableLoading"
            :col-model="colModel"
            :row-datas="rowDatas"
            :multi-select="false"
            :show-pager="false"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
      </div>
    </div>
    <registry-item-dlg ref="itemDlg" @submitEnd="itemSubmitEnd"/>
    <RegistryKVDlg ref="kvDlg" @submitEnd="submitEnd"/>

    <!--   重命名键值弹窗-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.rename')"
      :visible.sync="renameDialogVisible"
      width="400px"
    >
      <Form ref="dataForm" :rules="rules" :model="renameTemp" label-position="right" label-width="60px">
        <FormItem :label="$t('pages.newName')" prop="keyName">
          <el-input v-model="renameTemp.keyName" maxlength="260" clearable style="width: 100%;"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="renameData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="renameDialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteRegistryItem,
  deleteRegistryKV,
  getRegistry,
  updateRegistryKVName
} from '@/api/assets/systemMaintenance/registry'
import RegistryItemDlg from './registryItemDlg'
import RegistryKVDlg from './registryKVDlg.vue'
import { countUtf8Byte } from '@/utils/utf8'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'Registry',
  components: { RegistryItemDlg, RegistryKVDlg },
  data() {
    return {
      registryTreeData: [],
      termId: undefined,
      currentNode: undefined,
      breadcrumbItem: [],
      colModel: [
        { prop: 'keyName', label: 'name', width: '120', sort: true, formatter: this.keyNameFormatter },
        { prop: 'type', label: 'type', width: '100', sort: true, formatter: this.typeFormatter },
        { prop: 'value', label: this.$t('pages.data'), width: '200', sort: true, formatter: this.valueFormatter },
        { label: 'operate', type: 'button', width: '100',
          buttons: [
            { label: 'edit', disabledFormatter: this.editDisabledFormatter, click: this.handleUpdateKV },
            { label: this.$t('pages.rename'), disabledFormatter: this.deleteDisabledFormatter, click: this.handleRenameKV },
            { label: 'delete', disabledFormatter: this.deleteDisabledFormatter, click: this.handleDeleteKV }
          ]
        }
      ],
      rowDatas: [],
      rowDataCache: {},
      addable: false,
      tableLoading: false,
      showTree: true,
      ctrlAble: false,
      refreshDisable: true,
      treeChildNodes: [], // 收集每节点的孩子节点
      errorCode: {
        2: this.$t('pages.systemNotFindPath'),
        5: this.$t('pages.notAccess'),
        1379: this.$t('pages.alreadyExistsInfo', { info: this.$t('table.name') })
      },
      registryLevel1Item: ['MapHKEY_CLASSES_ROOT', 'MapHKEY_CURRENT_USER', 'MapHKEY_LOCAL_MACHINE', 'MapHKEY_USERS', 'MapHKEY_CURRENT_CONFIG'],
      deletable: false,
      renameTemp: {
        termId: undefined,
        pathLen: 0,
        path: '',
        keyName: '',
        oldNameLen: 0,
        oldName: '',
        newNameLen: 0,
        newName: ''
      },
      renameDialogVisible: false,
      submitting: false,
      rules: {
        keyName: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.keyNameValidator, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    registryItemTree() {
      return this.$refs['registryItemTree']
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  created() {

  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('registry', termId, [2], '5.01').then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange(tabName, data) {
      if (this.termId) {
        // 切换了终端
        if (this.termId != data.dataId) {
          // 将表格缓存的数据清除
          this.rowDataCache = {}
          // 清除选中节点，避免刷新时选中的节点还存在
          this.registryItemTree.clearSelectedNode()
          this.tableLoading = false
        } else {
          // 未切换终端
          return
        }
      }
      this.termId = undefined
      this.addable = false
      this.registryTreeData = []
      this.rowDatas = []
      this.breadcrumbItem = []
      this.refreshDisable = true
      if (data.id.indexOf('G' + data.dataId) < 0) { // 点击终端
        this.checkCtrlAble(data.dataId).then(() => {
          if (!this.ctrlAble) return
          this.termId = data.dataId
          this.registryTreeData = [{ id: 'Map0', dataId: '0', label: this.$t('pages.computer'), patentId: 'Map-1', isLeaf: false, children: [
            { id: 'MapHKEY_CLASSES_ROOT', dataId: 'HKEY_CLASSES_ROOT', label: 'HKEY_CLASSES_ROOT', parentId: 'Map0', isLeaf: false },
            { id: 'MapHKEY_CURRENT_USER', dataId: 'HKEY_CURRENT_USER', label: 'HKEY_CURRENT_USER', parentId: 'Map0', isLeaf: false },
            { id: 'MapHKEY_LOCAL_MACHINE', dataId: 'HKEY_LOCAL_MACHINE', label: 'HKEY_LOCAL_MACHINE', parentId: 'Map0', isLeaf: false },
            { id: 'MapHKEY_USERS', dataId: 'HKEY_USERS', label: 'HKEY_USERS', parentId: 'Map0', isLeaf: false },
            { id: 'MapHKEY_CURRENT_CONFIG', dataId: 'HKEY_CURRENT_CONFIG', label: 'HKEY_CURRENT_CONFIG', parentId: 'Map0', isLeaf: false }
          ] }]
          this.breadcrumbItem.push(this.$t('pages.computer'))
        })
      } else { // 点击终端分组

      }
    },
    nodeClick(data, node, el) {
      this.refreshDisable = false
      if (this.currentNode && this.currentNode.key != node.key) {
        // 切换节点后，未加载完毕的节点加载中的图标隐藏掉
        this.currentNode.loading = false
      }
      this.rowDatas = []
      this.breadcrumbItem = []
      if (data.id == 'Map0') {
        this.breadcrumbItem.push(this.$t('pages.computer'))
        this.addable = false
        this.refreshDisable = true
        return
      }
      const nodePaths = this.getTreeNodePath(node)
      this.breadcrumbItem.splice(0, this.breadcrumbItem.length, ...nodePaths)
      this.currentNode = node
      // 如果缓存的表格数据存在，则从缓存取数据（非实时），可通过刷新方式实时拿到树节点的子节点及表格数据
      const dataCache = this.rowDataCache[data.id]
      if (dataCache) {
        this.rowDatas = dataCache
        this.addable = true
      } else {
        const query = this.getQueryObj(node)
        this.getRegistryInfo(query, data, node)
      }
    },
    getRegistryInfo(query, data, node) {
      node.loading = true
      this.tableLoading = true
      // 先删除当前节点的孩子节点，再添加查询得到的孩子节点
      const childKeys = this.registryItemTree.getChildKeys(data)
      const childKeysExcludeSelf = childKeys.filter((key) => key != data.id)
      this.registryItemTree.removeNode(childKeysExcludeSelf)
      this.treeChildNodes = []
      // 先清空rowDatas，再添加查询得到的kvNodes
      this.rowDatas = []
      getRegistry(query).then(response => {
        this.$socket.subscribeToAjax(response, this.termId + '/getRegistryInfo', (respond, handle) => {
          const childNodes = respond.data.registryItemNodes || []
          this.treeChildNodes.push(...childNodes)
          const kvNodes = respond.data.registryKVNodes || []
          this.rowDatas.push(...kvNodes)
          const hasMore = respond.data.hasMore || 0
          if (hasMore === 0) { // 没有下一帧数据了
            this.treeChildNodes.sort((a, b) => {
              if (!a.label) return -1
              if (!b.label || b.label === '*') return 1
              if (a.label && b.label) {
                return a.label.localeCompare(b.label)
              }
            }).forEach((child) => {
              this.registryItemTree.addNode(child)
            })
            const index = this.rowDatas.map(kv => kv.keyName).indexOf('')
            if (index < 0) { // 如果表格没有默认名称为空的行数据时，构造一条
              this.rowDatas.unshift({ keyName: '', type: 1, value: undefined })
            }
            // 缓存当前节点表格数据
            this.rowDataCache[data.id] = this.rowDatas.sort((a, b) => {
              if (!a.keyName) return -1
              if (!b.keyName) return 1
              if (a.keyName && b.keyName) {
                return a.keyName.localeCompare(b.keyName)
              }
            })
            // 如果当前节点的子节点有缓存过数据，需要清除子节点缓存的数据
            const childKeys = this.registryItemTree.getChildKeys(data)
            const childKeysExcludeSelf = childKeys.filter((key) => key != data.id)
            childKeysExcludeSelf.forEach((key) => { this.rowDataCache[key] = undefined })
            this.addable = true
            node.loading = false
            node.expanded = true
            this.tableLoading = false
            handle.close() // 关闭handle
          } else {
            // 还存在下一帧数据， handle不关闭，会继续调用该回调函数
          }
        })
      }).catch(e => {
        node.loading = false
        this.tableLoading = false
      })
    },
    refreshRegistry: function(treeData, treeNode) {
      const data = treeData || this.registryItemTree.getCurrentNode()
      const node = treeNode || this.registryItemTree.getNode(data)
      if (!data || data.id == 'Map0') {
        return
      }
      const query = this.getQueryObj(node)
      this.getRegistryInfo(query, data, node)
    },
    renderContent(h, { node, data, store }) {
      const showFlag = !this.registryLevel1Item.includes(data.id)
      if (data.id == 'Map0') {
        return <span title={data.label}>{data.label}</span>
      } else {
        return (
          <div class='custom-tree-node'>
            <span title={data.label}>{data.label}</span>
            <span class='el-ic'>
              <svg-icon icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleRegistryItemCreate(data)} />
              <svg-icon v-show={showFlag} icon-class='edit' title={this.$t('button.edit')} className='icon-space' on-click={r => this.handleRegistryItemUpdate(data)}/>
              <svg-icon v-show={showFlag} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.handleRegistryItemDelete(data)}/>
            </span>
          </div>
        )
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTreeNodePath(node) {
      // 获取节点路径
      const paths = this.registryItemTree && this.registryItemTree.getNodePath(node)
      return paths.map(path => path.label)
    },
    getTreeNodePathExceptRoot(node) {
      // 获取节点路径
      const paths = this.registryItemTree && this.registryItemTree.getNodePath(node)
      return paths.filter(path => path.id !== 'Map0').map(path => path.label)
    },
    handleRegistryItemCreate(nodeData) {
      const termId = this.termId
      const pathExceptRoot = this.getTreeNodePathExceptRoot(nodeData)
      const path = pathExceptRoot.join('\\')
      this.$refs.itemDlg.show('create', termId, path)
    },
    handleRegistryItemUpdate(nodeData) {
      const termId = this.termId
      // 修改时，路径为父路径
      const pathExceptRoot = this.getTreeNodePathExceptRoot(nodeData.parentId)
      const path = pathExceptRoot.join('\\')
      const oldName = nodeData.label
      this.$refs.itemDlg.show('update', termId, path, oldName, nodeData)
    },
    handleRegistryItemDelete(nodeData) {
      this.$confirmBox(this.$t('pages.confirmDeleteRegistry', { operator: this.$t('button.delete'), info: this.$t('table.registryItem') }), this.$t('text.warning'), 'warning').then(() => {
        const termId = this.termId
        const pathExceptRoot = this.getTreeNodePathExceptRoot(nodeData)
        const path = pathExceptRoot.join('\\')
        const pathLen = countUtf8Byte(path)
        deleteRegistryItem({ termId, pathLen, path }).then(respond => {
          if (respond.data == 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
            // 刷新被删除节点的父节点
            const node = this.registryItemTree.getNode(nodeData)
            this.registryItemTree.setCurrentKey(node.parent.data.id)
            const nodePaths = this.getTreeNodePath(node.parent)
            this.breadcrumbItem.splice(0, this.breadcrumbItem.length, ...nodePaths)
            this.refreshRegistry(node.parent.data, node.parent)
          } else {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.errorCode[respond.data] ? this.errorCode[respond.data] : '错误码' + respond.data,
              type: 'error',
              duration: 2000
            })
          }
        }).catch(() => {

        })
      })
    },
    itemSubmitEnd(dialogStatus, nodeData) {
      if (dialogStatus === 'create') {
        this.refreshRegistry(undefined, undefined)
      } else {
        // 刷新被修改节点的父节点
        const node = this.registryItemTree.getNode(nodeData)
        this.registryItemTree.setCurrentKey(node.parent.data.id)
        const nodePaths = this.getTreeNodePath(node.parent)
        this.breadcrumbItem.splice(0, this.breadcrumbItem.length, ...nodePaths)
        this.refreshRegistry(node.parent.data, node.parent)
      }
    },
    getQueryObj(node) {
      const termId = this.termId
      const pathExceptRoot = this.getTreeNodePathExceptRoot(node)
      const pathLen = countUtf8Byte(pathExceptRoot.join('\\'))
      const path = pathExceptRoot.join('\\')
      return { termId, pathLen, path }
    },
    typeFormatter(row, data) {
      const map = {
        0: 'REG_NONE',
        1: 'REG_SZ',
        2: 'REG_EXPAND_SZ',
        3: 'REG_BINARY',
        4: 'REG_DWORD',
        7: 'REG_MULTI_SZ',
        8: 'REG_RESOURCE_LIST',
        9: 'REG_FULL_RESOURCE_DESCRIPTOR',
        10: 'REG_RESOURCE_REQUIREMENTS_LIST',
        11: 'REG_QWORD'
      }
      return map[data] || data
    },
    keyNameFormatter(row, data) {
      if (data === '') {
        return `<span style="font-style: italic">(${this.$t('text.default')})</span>`
      } else {
        return data
      }
    },
    valueFormatter(row, data) {
      if (row.keyName === '' && data === undefined) {
        return `<span style="font-style: italic">(${this.$t('pages.dataUnSet')})</span>`
      } else {
        if (row.type === 4 || row.type === 11) { // DWORD、QWORD
          return this.toHexString(data) + `(${data})`
        } else if (row.type === 7) { // REG_MULTI_SZ
          data = data.replaceAll(' ', '&nbsp;') // 由于html多个空格会当作一个空格处理，所有空格替换为&nbsp;
          return data.replaceAll('#end#', ' ')
        } else if (row.type === 0 && data === '') { // REG_NONE
          return `<span style="font-style: italic">(${this.$t('pages.zeroLenBin')})</span>`
        } else {
          data = data.replaceAll(' ', '&nbsp;')
          return data
        }
      }
    },
    getPath() {
      const data = this.registryItemTree.getCurrentNode()
      const node = this.registryItemTree.getNode(data)
      const pathExceptRoot = this.getTreeNodePathExceptRoot(node)
      return pathExceptRoot.join('\\')
    },
    handleAddKV() {
      const termId = this.termId
      const path = this.getPath()
      this.$refs.kvDlg.show(termId, path, 'create')
    },
    handleUpdateKV(row) {
      const termId = this.termId
      const path = this.getPath()
      this.$refs.kvDlg.show(termId, path, 'update', row)
    },
    handleRenameKV(row) {
      this.resetRenameTemp()
      const path = this.getPath()
      this.renameTemp.termId = this.termId
      this.renameTemp.pathLen = countUtf8Byte(path)
      this.renameTemp.path = path
      this.renameTemp.keyName = row.keyName
      this.renameTemp.oldNameLen = countUtf8Byte(row.keyName)
      this.renameTemp.oldName = row.keyName
      this.renameDialogVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    renameData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirmBox(this.$t('pages.confirmDeleteRegistry', { operator: this.$t('pages.rename'), info: this.$t('table.registryKV') }), this.$t('text.warning')).then(() => {
            this.formatFormData(this.renameTemp)
            updateRegistryKVName(this.renameTemp).then(respond => {
              if (respond.data == 0) {
                this.$emit('submitEnd', this.dialogStatus, this.curNode)
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.editSuccess'),
                  type: 'success',
                  duration: 2000
                })
                this.refreshRegistry(undefined, undefined)
              } else {
                this.$notify({
                  title: this.$t('text.fail'),
                  message: this.errorCode[respond.data] ? this.errorCode[respond.data] : '错误码' + respond.data,
                  type: 'error',
                  duration: 2000
                })
              }
              this.submitting = false
              this.renameDialogVisible = false
            }).catch(() => {
              this.submitting = false
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatFormData(temp) {
      this.renameTemp.newNameLen = countUtf8Byte(temp.keyName)
      this.renameTemp.newName = temp.keyName
    },
    resetRenameTemp() {
      this.renameTemp = {
        termId: undefined,
        pathLen: 0,
        path: '',
        keyName: '',
        oldNameLen: 0,
        oldName: '',
        newNameLen: 0,
        newName: ''
      }
    },
    handleDeleteKV(row) {
      this.$confirmBox(this.$t('pages.confirmDeleteRegistry', { operator: this.$t('button.delete'), info: this.$t('table.registryKV') }), this.$t('text.warning')).then(() => {
        const termId = this.termId
        const path = this.getPath()
        const pathLen = countUtf8Byte(path)
        const keyName = row.keyName
        const keyNameLen = countUtf8Byte(keyName)
        deleteRegistryKV({ termId, pathLen, path, keyNameLen, keyName }).then(respond => {
          if (respond.data == 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
            this.refreshRegistry(undefined, undefined)
          } else {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.errorCode[respond.data] ? this.errorCode[respond.data] : '错误码' + respond.data,
              type: 'error',
              duration: 2000
            })
          }
        }).catch(() => {

        })
      })
    },
    submitEnd(formData) {
      this.refreshRegistry(undefined, undefined)
    },
    toHexString(decimal) {
      // eslint-disable-next-line no-undef
      const bigInt = BigInt(decimal + '')
      return '0x' + (bigInt.toString(16));
    },
    editDisabledFormatter(row, data) {
      // REG_RESOURCE_LIST、REG_FULL_RESOURCE_DESCRIPTOR、REG_RESOURCE_REQUIREMENTS_LIST 不允许修改
      if (row.type === 8 || row.type === 9 || row.type === 10) {
        return true
      } else {
        return false
      }
    },
    deleteDisabledFormatter(row, data) {
      if (row.keyName === '') {
        return true
      } else {
        return false
      }
    },
    selectionChangeEnd(rowDatas) {
      this.deletable = rowDatas && rowDatas.length > 0
    },
    copyPath() {
      const input = document.createElement('input');
      input.value = this.breadcrumbItem.join('\\');
      document.body.appendChild(input);
      input.select(); // 选择对象;
      document.execCommand('Copy'); // 执行浏览器复制命令
      this.$message({
        message: this.$t('pages.copy_success'),
        type: 'success'
      });
      input.remove()
    },
    keyNameValidator(rule, value, callback) {
      const oldName = this.renameTemp.oldName
      if (value == oldName) {
        callback(new Error(this.$t('pages.notSameWithOldName')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.breadcrumb {
  display: inline-block;
  position: absolute;
  margin: 1px 0 0 13px;
  width: calc(90% - 43px);
  height: 29px;
  border: 1px solid #aaa;
  border-radius: 3px;
  padding-left: 10px;
  >>>.el-breadcrumb {
    line-height: 25px;
    white-space: nowrap;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    .el-breadcrumb__item {
      float: none;
    }
  }
}
</style>
