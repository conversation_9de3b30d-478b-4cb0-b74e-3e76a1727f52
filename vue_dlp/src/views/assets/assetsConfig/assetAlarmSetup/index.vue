<template>
  <div class="app-container">
    <Form
      ref="dataForm"
      :model="temp"
      :hide-required-asterisk="true"
      label-position="right"
      label-width="107px"
      class="asset-alarm"
    >
      <el-card class="box-card">
        <!-- <div slot="header">
          <span></span>
        </div> -->
        <el-checkbox v-model="temp.hardwareAlarm" :true-label="1" :false-label="0" style="width: auto;">{{ $t('pages.hardwareAssetChangeAlarmSetting') }}</el-checkbox>
        <FormItem :label="`${$t('pages.operateTypeOptions1')}：`" prop="hardwareOperateType">
          <el-checkbox-group v-model="temp.hardwareOperateTypeList" :disabled="temp.hardwareAlarm == 0">
            <el-checkbox v-for="item in operateTypeOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="`${$t('pages.operateTypeOptions2')}：`" prop="hardwareAssetsType" label-position="top">
          <el-button type="text" :disabled="!temp.hardwareAlarm" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
          <el-button type="text" :disabled="!temp.hardwareAlarm" @click="selectAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
          <el-checkbox-group v-model="temp.hardwareAssetsTypeList" :disabled="temp.hardwareAlarm == 0">
            <el-checkbox v-for="item in hardwareAssetOption" :key="item.value" :label="item.value" style="line-height: 0">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <div style="padding-top: 10px">
          <ResponseContent
            class="response_content"
            :status="status"
            :show-select="true"
            read-only
            :editable="temp.hardwareAlarm == 1"
            :prop-check-rule="!!temp.isCheck"
            :prop-rule-id="temp.ruleId"
            @getRuleId="getRuleId"
            @ruleIsCheck="ruleIsCheck"
          />
        </div>
      </el-card>

      <el-card class="box-card">
        <el-checkbox v-model="temp.hardwareRecord" :true-label="1" :false-label="2" style="width: auto;">{{ $t('pages.hardwareAssetChangeRecordSetting') }}</el-checkbox>
        <FormItem :label="`${$t('pages.operateTypeOptions1')}：`" prop="hardwareOperateType2">
          <el-checkbox-group v-model="temp.hardwareOperateTypeRecordList" :disabled="temp.hardwareRecord != 1">
            <el-checkbox v-for="item in operateTypeOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="`${$t('pages.operateTypeOptions2')}：`" prop="hardwareAssetsTypeRecord" label-position="top">
          <el-button type="text" :disabled="temp.hardwareRecord != 1" @click="selectAll2(true)">{{ $t('button.selectAll') }}</el-button>
          <el-button type="text" :disabled="temp.hardwareRecord != 1" @click="selectAll2(false)">{{ $t('button.cancelSelectAll') }}</el-button>
          <el-checkbox-group v-model="temp.hardwareAssetsTypeRecordList" :disabled="temp.hardwareRecord != 1">
            <el-checkbox v-for="item in hardwareAssetOption2" :key="item.value" :label="item.value" style="line-height: 0">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
      </el-card>
    </Form>
    <div class="save-btn-container">
      <el-button :loading="submitting" type="primary" size="mini" @click="createData()">
        {{ $t('button.save') }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { get, update } from '@/api/assets/assetsConfig/assetAlarmSetup'
import { listPropType } from '@/api/assets/assetsConfig/assetLog'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'

export default {
  name: 'AssetAlarmSetup',
  components: { ResponseContent },
  data() {
    return {
      submitting: false,
      operateTypeOptions: [
        { label: this.$t('button.add1'), value: 1 },
        { label: this.$t('button.delete'), value: 2 }
      ],
      hardwareAssetOption: [],
      hardwareAssetOption2: [],
      temp: {
        hardwareAlarm: 0,
        hardwareOperateType: 0,
        hardwareAssetsType: '',
        hardwareOperateTypeList: [],
        hardwareAssetsTypeList: [],
        hardwareRecord: 1, // 1，开启指定资产类型审计日志记录 2，不开启审计日志记录
        hardwareOperateTypeRecord: 3,
        hardwareAssetsTypeRecord: '1001|1002|1003|1004|1005|1006|1007|1008|1011|1012|1013|1014|1015|1017|1018|1019|1020|1021|1022|1023|1024|1025|1026|1027|1028|1029|1030|1031',
        hardwareOperateTypeRecordList: [],
        hardwareAssetsTypeRecordList: [],
        ruleId: null,
        isCheck: 0
      },
      status: ''
    }
  },
  created() {
    this.getSetting()
    this.getAssetsOptions()
  },
  methods: {
    getSetting() {
      get().then(res => {
        this.temp = Object.assign(this.temp, res.data)
        this.temp.isCheck = !!this.temp.ruleId
        this.temp.hardwareOperateTypeList = this.numToList(this.temp.hardwareOperateType, 3)
        this.temp.hardwareOperateTypeRecordList = this.numToList(this.temp.hardwareOperateTypeRecord, 3)
        this.temp.hardwareAssetsTypeList = this.splitToNum(this.temp.hardwareAssetsType, '|')
        this.temp.hardwareAssetsTypeRecordList = this.splitToNum(this.temp.hardwareAssetsTypeRecord, '|')
        this.status = 'update'
      })
    },
    selectAll(boolean) {
      if (boolean) {
        this.temp.hardwareAssetsTypeList = this.hardwareAssetOption.map(item => item.value)
      } else {
        this.temp.hardwareAssetsTypeList.splice(0)
      }
    },
    selectAll2(boolean) {
      if (boolean) {
        this.temp.hardwareAssetsTypeRecordList = this.hardwareAssetOption2.map(item => item.value)
      } else {
        this.temp.hardwareAssetsTypeRecordList.splice(0)
      }
    },
    createData() {
      const msg = this.validateSettings()
      if (msg) {
        this.$message({
          title: this.$t('text.prompt'),
          message: msg,
          type: 'error',
          duration: 2000
        })
        return;
      }
      this.temp.hardwareOperateType = this.getSum(this.temp.hardwareOperateTypeList)
      this.temp.hardwareOperateTypeRecord = this.getSum(this.temp.hardwareOperateTypeRecordList)
      this.temp.hardwareAssetsType = this.temp.hardwareAssetsTypeList.join('|')
      this.temp.hardwareAssetsTypeRecord = this.temp.hardwareAssetsTypeRecordList.join('|')
      if (this.temp.isCheck == 1 && this.temp.ruleId == null) return
      this.submitting = true
      update(this.temp).then(res => {
        this.submitting = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.saveSuccess'),
          type: 'success',
          duration: 2000
        })
        this.getSetting()
      })
    },
    getAssetsOptions() { // 1硬件2软件
      listPropType({ type: 1 }).then(res => {
        const options = this.hardwareAssetOption
        const options2 = this.hardwareAssetOption2
        options.splice(0)
        options2.splice(0)
        res.data.forEach(item => {
          options.push({ label: item.name, value: item.propId })
          options2.push({ label: item.name, value: item.propId })
        })
      })
    },
    // 响应规则选中传值ruleId
    getRuleId(value) {
      this.temp.ruleId = value
    },
    // 响应规则前面的复选框是否选中(0/1)--否/是
    ruleIsCheck(value) {
      this.temp.isCheck = value
    },
    validateSettings() {
      if (this.temp.hardwareAlarm === 1) {
        if (this.temp.hardwareOperateTypeList.length === 0) {
          return this.$t('pages.saveAlarmSettingTip', { info: this.$t('pages.operateTypeOptions1') })
        }
        if (this.temp.hardwareAssetsTypeList.length === 0) {
          return this.$t('pages.saveAlarmSettingTip', { info: this.$t('pages.operateTypeOptions2') })
        }
      }
      if (this.temp.hardwareRecord === 1) {
        if (this.temp.hardwareOperateTypeRecordList.length === 0) {
          return this.$t('pages.saveRecordSettingTip', { info: this.$t('pages.operateTypeOptions1') })
        }
        if (this.temp.hardwareAssetsTypeRecordList.length === 0) {
          return this.$t('pages.saveRecordSettingTip', { info: this.$t('pages.operateTypeOptions2') })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .app-container{
    overflow: auto;
  }
  >>>.el-form-item{
    margin:0px;
  }
  .box-card {
    margin-top: 20px;
    background-color: transparent;
  }
  .asset-alarm{
    width: 100%;
    height: calc(100% - 40px);
    padding-right: 10px;
    overflow: auto;
    >>>.el-card__header{
      padding: 0;
    }
    >>>.el-card__body{
      padding-bottom: 10px;
      padding-top: 10px;
    }
    >>>.el-checkbox{
      width: 150px;
    }
    .response_content{
      >>>.el-checkbox{
        width: 450px;
      }
      >>>.el-checkbox:nth-child(2){
        width: 160px;
      }
    }
  }
  .save-btn-container{
    height: 40px;
    position: absolute;
    right: 30px;
    bottom: 0px;
    left: 16px;
    z-index: 1;
    text-align: right;
  }
  >>>.el-radio__input.is-disabled+span.el-radio__label{
    color: #888;
  }
</style>
