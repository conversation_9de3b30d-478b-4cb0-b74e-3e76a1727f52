<template>
  <assets-log
    ref="assetsLog"
    :type="2"
    :detail-able="hasPermission('265')"
    :export-able="hasPermission('311')"
    :delete-able="$store.getters.auditingDeleteAble && hasPermission('420')"
    :query-func="getSoftLogPage"
    :export-func="exportSoftLog"
  />
</template>

<script>
import AssetsLog from '@/views/assets/assetsConfig/assetLog'
import { getSoftLogPage, exportSoftLog } from '@/api/assets/assetsConfig/assetLog'
export default {
  name: 'SoftwareAssetLog',
  components: { AssetsLog },
  methods: {
    getSoftLogPage,
    exportSoftLog
  }
}
</script>
