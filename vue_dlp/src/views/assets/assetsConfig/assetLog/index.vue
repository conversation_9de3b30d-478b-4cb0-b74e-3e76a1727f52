<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.propId" :value="query.propId">
          <span>{{ $t('pages.operateTypeOptions2') }}：</span>
          <el-select v-model="query.propId" :placeholder="$t('text.select')" style="width: 150px;">
            <el-option :label="$t('pages.all')" value=""/>
            <el-option
              v-for="item in propTypeList"
              :key="item.propId"
              :label="item.name"
              :value="item.propId"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.logType" :value="query.logType">
          <span>{{ $t('pages.operateTypeOptions1') }}：</span>
          <el-select v-model="query.logType" :placeholder="$t('text.select')" style="width: 150px;">
            <el-option :label="$t('pages.all')" value=""/>
            <el-option :label="$t('button.add1')" value="1"/>
            <el-option :label="$t('button.delete')" value="2"/>
          </el-select>
        </SearchItem>
        <SearchItem v-if="dealAble" model-key="query.isRead" :value="query.isRead">
          <span>{{ $t('table.processState') }}：</span>
          <el-select v-model="query.isRead" :placeholder="$t('text.select')" style="width: 150px;">
            <el-option :label="$t('pages.all')" value=""/>
            <el-option :label="$t('pages.undeal')" value="0"/>
            <el-option :label="$t('pages.dealed')" value="1"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.desp" :value="query.desp">
          <span>{{ $t('table.fileDescription') }}：</span>
          <el-input
            v-model="query.desp"
            v-trim
            clearable
            style="width: 150px;"
          />
        </SearchItem>
        <audit-log-exporter v-if="exportAble" slot="append" :request="handleExport"/>
        <audit-log-delete v-if="deleteAble" slot="append" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
        <audit-log-batch-op
          v-if="dealAble"
          slot="append"
          :name="$t('pages.deal')"
          type="primary"
          :selection="selection"
          :filter="canDeal"
          :confirm="$t('pages.assetAlarmSetup_text2')"
          @click="handleDealData"
        />
      </SearchToolbar>
      <grid-table
        ref="logList"
        :autoload="false"
        :col-model="colModel"
        :multi-select="dealAble || deleteAble"
        row-key="logId"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :sortable="sortable"
        :custom-col="true"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="1 === type ? $t('pages.hardwareChangeDetails') : $t('pages.softwareChangeDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.event')">
            {{ logTypeFormat(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.processState')">
            {{ readFormat(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.asset')">
            {{ rowDetail.propName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileDescription')">
            {{ rowDetail.desp }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { listPropType, deleteLog } from '@/api/assets/assetsConfig/assetLog'
import { get } from '@/api/assets/assetsConfig/assetAlarmSetup'
import { get as getSoftwareAssetAlarmSetup } from '@/api/softwareManage/assetAlarm/assetAlarmSetup'
import AuditLogBatchOp from '@/components/AuditLogBatchOp'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'AssetLog',
  components: { AuditLogBatchOp },
  props: {
    type: { // 硬或软：1硬件 2软件
      type: Number,
      default: 1
    },
    isAlarm: { // 是否告警信息
      type: Boolean,
      default: false
    },
    dealAble: { // 是否显示处理按钮
      type: Boolean,
      default: false
    },
    exportAble: { // 是否显示导出按钮
      type: Boolean,
      default: false
    },
    detailAble: { // 是否显示查看详情按钮
      type: Boolean,
      default: false
    },
    queryFunc: { // 查询函数
      type: Function,
      default: null
    },
    exportFunc: { // 导出函数
      type: Function,
      default: null
    },
    dealFunc: { // 处理函数
      type: Function,
      default: null
    },
    deleteAble: { // 是否显示删除按钮
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isShowWarning: 1,  // 是否弹出警告。1：不弹，不能处理数据或者可以处理数据且后台配置了报警。 0：弹出警告，可以处理数据但后台未配置报警
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true, formatter: logSourceFormatter },
        {
          prop: 'terminalName',
          label: 'terminalName',
          width: '100',
          type: 'showDetail',
          searchType: 'terminal',
          searchParam: 'terminalId'
        },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'propName', label: this.$t('pages.operateTypeOptions2'), width: '150' },
        { prop: 'logType', label: this.$t('pages.operateTypeOptions1'), width: '100', formatter: this.logTypeFormat },
        { prop: 'desp', label: 'fileDescription', width: '250' },
        { prop: 'isRead', label: 'processState', width: '100', hidden: !this.dealAble, formatter: this.readFormat },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.detailAble,
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '', // 结束日期，显示在弹框里的
        isTimes: false,
        propId: '',
        logType: '',
        desp: '',
        parentId: this.type,
        isRead: '',
        alarmFlag: this.canDeal ? 1 : 0,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      propTypeList: [],
      showTree: true,
      selection: [],
      firstLoad: true,
      rowDetail: {},
      dialogFormVisible: false,
      queryVideoMethod: undefined,
      sortable: true
    }
  },
  watch: {
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    listPropType({ type: this.type }).then(res => {
      this.propTypeList = res.data
    })
    if (this.firstLoad) {
      this.getSetting()
      this.$nextTick(() => {
        this.firstLoad = false
      })
    }
    addViewVideoBtn(this)
  },
  activated() {
    !this.firstLoad && this.getSetting()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    getSetting() {  // 得到提醒设置
      const apiOption = this.type == 1 ? { func: get, showParam: 'hardwareAlarm' }
        : { func: getSoftwareAssetAlarmSetup, showParam: 'softwareAlarm' }
      apiOption.func().then(res => {
        if (this.dealAble) {
          if (res.data) {
            this.isShowWarning = res.data[apiOption.showParam]
          } else {
            this.isShowWarning = 0
          }
        }
        this.gridTable().execRowDataApi()
      })
    },
    logTypeFormat: function(row) {
      const map = {
        1: this.$t('button.add1'),
        2: this.$t('button.delete'),
        3: this.$t('button.edit')
      }
      return map[row.logType]
    },
    readFormat: function(row) {
      const map = {
        1: this.$t('pages.dealed'),
        0: this.$t('pages.undeal')
      }
      return map[row.isRead]
    },
    rowDataApi: function(option) {
      if (this.isShowWarning == 0) {
        const typeValue = this.type == 1 ? this.$t('pages.HardwareAssets') : this.$t('pages.softwareAssets')
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.assetAlarmMsg', { type: typeValue }),
          type: 'error',
          duration: 2000
        })
        return new Promise((resolve, reject) => {
          resolve({
            code: 20000,
            data: {
              total: 0,
              items: []
            }
          })
        })
      } else {
        const searchQuery = Object.assign({}, this.query, option)
        this.query.sortName = option.sortName
        this.query.sortOrder = option.sortOrder
        this.changeSortable()
        return this.queryFunc(searchQuery)
      }
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    canDeal(row) {
      return !row.isRead
    },
    selectable(row, index) {
      return row.isRead == 0 || !this.dealAble || this.hasPermission('412')
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleDealData(data) {
      // const flag = data.some(item => {
      //   return item.isRead == 0
      // })
      // if (!flag) {
      //   this.$message({
      //     message: this.$t('pages.assetAlarmSetup_text1'),
      //     type: 'error',
      //     duration: 2000
      //   })
      //   return
      // }
      this.dealFunc(data).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.dealSuccess'),
          type: 'success',
          duration: 2000
        })
        this.gridTable().execRowDataApi()
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, () => this.detailAble, this.query.searchReport)
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 180px;
}
</style>

