<template>
  <assets-log
    ref="assetsLog"
    :type="1"
    is-alarm
    :deal-able="hasPermission('238')"
    :detail-able="hasPermission('262')"
    :export-able="hasPermission('308')"
    :delete-able="$store.getters.auditingDeleteAble && hasPermission('418')"
    :query-func="getHardAlarmPage"
    :deal-func="dealHardAlarm"
    :export-func="exportHardAlarm"
  />
</template>

<script>
import AssetsLog from '@/views/assets/assetsConfig/assetLog'
import { getHardAlarmPage, dealHardAlarm, exportHardAlarm } from '@/api/assets/assetsConfig/assetLog'
export default {
  name: 'HardwareChangeAlarm',
  components: { AssetsLog },
  methods: {
    getHardAlarmPage,
    dealHardAlarm,
    exportHardAlarm
  }
}
</script>
