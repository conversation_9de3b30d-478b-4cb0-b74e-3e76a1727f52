<template>
  <assets-log
    ref="assetsLog"
    :type="2"
    is-alarm
    :deal-able="hasPermission('239')"
    :detail-able="hasPermission('264')"
    :export-able="hasPermission('312')"
    :delete-able="$store.getters.auditingDeleteAble && hasPermission('421')"
    :query-func="getSoftAlarmPage"
    :deal-func="dealSoftAlarm"
    :export-func="exportSoftAlarm"
  />
</template>

<script>
import { getSoftAlarmPage, dealSoftAlarm, exportSoftAlarm } from '@/api/assets/assetsConfig/assetLog'
import AssetsLog from '@/views/assets/assetsConfig/assetLog'
export default {
  name: 'SoftwareChangeAlarm',
  components: { AssetsLog },
  methods: {
    getSoftAlarmPage,
    dealSoftAlarm,
    exportSoftAlarm
  }
}
</script>
