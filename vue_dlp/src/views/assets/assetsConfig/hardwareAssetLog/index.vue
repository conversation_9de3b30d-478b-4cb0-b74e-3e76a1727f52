<template>
  <assets-log
    ref="assetsLog"
    :type="1"
    :detail-able="hasPermission('263')"
    :export-able="hasPermission('309')"
    :delete-able="$store.getters.auditingDeleteAble && hasPermission('419')"
    :query-func="getHardLogPage"
    :export-func="exportHardLog"
  />
</template>

<script>
import AssetsLog from '@/views/assets/assetsConfig/assetLog'
import { getHardLogPage, exportHardLog } from '@/api/assets/assetsConfig/assetLog'
export default {
  name: 'HardwareAssetLog',
  components: { AssetsLog },
  methods: {
    getHardLogPage,
    exportHardLog
  }
}
</script>
