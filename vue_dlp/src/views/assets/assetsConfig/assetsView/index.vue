<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" :os-type-filter="7" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button size="mini" @click="handleAddAttribute">
          {{ $t('pages.customAttribute') }}
        </el-button>
        <el-button size="mini" @click="dialogFormVisible1 = true">
          {{ $t('pages.customQuery') }}
        </el-button>
        <sync-asset-btn :show-button="syncAssetBtnVisible"/>
        <common-downloader v-permission="'156'" :name="filename" :button-name="$t('button.export')" button-size="mini" @download="exportAsset"/>
        <div class="searchCon">
          <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleFilter">
            {{ $t('button.refresh') }}
          </el-button>
        </div>
      </div>
      <!-- 资产列表 -->
      <grid-table
        ref="assetsList"
        :col-model="assetsColModel"
        :row-data-api="rowDataApi"
        :default-sort="{ prop: 'term_id' }"
        :custom-col="true"
        :custom-col-func="handleConfig"
        retain-pages
        :show-pager="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <!-- 自定义列弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.customColumnSetting')"
      :visible.sync="dialogFormVisible"
      width="800px"
      @closed="refreshTransferData"
    >
      <tree-transfer
        ref="colTransfer"
        pid="parentId"
        :title="transferTitle"
        :from_data="fromData"
        :to_data="toData"
        :placeholder="$t('components.enterContent')"
        :default-props="{label:'label'}"
        :root-pid-value="rootPidValue"
        height="460px"
        filter
        open-all
        :filter-node="filterNode"
        style="width: 700px; margin: auto;"
        @add-btn="add"
        @remove-btn="remove"
      />
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!--编辑自定义资产属性值弹窗-->
    <!--    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[type]"
      :visible.sync="dialogFormVisible4"
      width="800px"
    >
      <div class="tree-container" style="height: 450px;">
        <tree-menu ref="customDetailTree" :data="detailTreeData" :default-expand-all="true" :is-filter="true" @node-click="detailTreeNodeClick1" />
      </div>
      <div style="margin-left: 210px; display: flex;">
        <grid-table
          ref="customAssetTable"
          :show-pager="false"
          :height="450"
          :autoload="false"
          :multi-select="false"
          :col-model="colModel4"
          :row-datas="rowDatas"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleSave()">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="dialogFormVisible4 = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>-->

    <!-- 自定义资产属性弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.customAttribute')"
      :visible.sync="dialogFormVisible3"
      width="800px"
    >
      <data-editor
        append-to-body
        :popover-width="500"
        :updateable="updateable"
        :deletable="deleteable"
        :add-func="createAssetProp"
        :update-func="updateAssetProp"
        :delete-func="deleteAssetProp"
        :cancel-func="cancelAssetProp"
        :before-add="beforeAddAssetProp"
        :before-update="beforeUpdateAssetProp"
      >
        <Form ref="assetPropForm" :model="assetPropTemp" :rules="assetPropRules" label-position="right" label-width="100px" style="margin-left: -10px">
          <FormItem :label="$t('table.parentAsset')" prop="parentId">
            <tree-select
              ref="parentSelectTree"
              v-model="assetPropTemp.parentId"
              :disabled="assetPropTemp.id"
              :data="assetTreeData"
              node-key="dataId"
              :checked-keys="[assetPropTemp.parentId]"
              @change="parentSelectChange"
            />
          </FormItem>
          <FormItem :label="$t('table.propName')" prop="name">
            <el-input v-model="assetPropTemp.name" maxlength="60"/>
          </FormItem>
          <FormItem :label="$t('table.remark')" prop="remark">
            <el-input v-model="assetPropTemp.remark" type="textarea" show-word-limit maxlength="200"/>
          </FormItem>
        </Form>
      </data-editor>
      <div>
        <grid-table
          ref="assetPropTable"
          :row-data-api="rowDataApi3"
          :show-pager="false"
          :height="200"
          :col-model="assetPropColModel"
          @selectionChangeEnd="handleSelectionChange3"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <!--<el-button :loading="submitting" type="primary" @click="handleFilter()">
          {{ $t('button.confirm') }}
        </el-button>-->
        <el-button @click="dialogFormVisible3 = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 自定义查询弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.customQuery')"
      :visible.sync="dialogFormVisible1"
      width="800px"
    >
      <div class="toolbar">
        <el-button @click="addCondition()">
          {{ $t('button.insert') }}
        </el-button>
        <el-button :disabled="!deleteable1" @click="removeCondition()">
          {{ $t('button.delete') }}
        </el-button>
        <el-button @click="clearCondition()">
          {{ $t('button.clear') }}
        </el-button>
      </div>
      <div>
        <grid-table
          ref="conditionTable"
          :row-datas="queryDatas"
          :show-pager="false"
          :height="200"
          :col-model="colModel1"
          @selectionChangeEnd="handleSelectionChange1"
        />
        <div style="padding-top: 10px">
          {{ $t('pages.assetsView_text2') }}
        </div>
        <!--<el-tooltip class="item" effect="dark" :content="$t('pages.assetsView_text1')" placement="top-start">
          <el-checkbox v-model="query.mark" true-label="or" false-label="and" style="padding-top: 10px">{{ $t('pages.assetsView_text2') }}</el-checkbox>
        </el-tooltip>-->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleFilter()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible1 = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 资产配置弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[type]"
      :visible.sync="dialogFormVisible2"
      width="800px"
    >
      <el-container>
        <el-aside width="210px">
          <tree-menu ref="detailTree" :height="430" :data="detailTreeData" :is-filter="true" :filter-node-method="filterNodeMethod" @node-click="detailTreeNodeClick" />
        </el-aside>
        <el-main>
          <grid-table
            ref="detailTable"
            :show-pager="false"
            :height="410"
            :autoload="false"
            :multi-select="false"
            :col-model="detailColModel"
            :row-datas="rowDatas"
            :default-sort="{ prop: 'recordNo', order: 'ascending' }"
          />
          <div style="line-height: 20px; padding-top: 3px; color: #409eff; font-size: small; font-weight:bold;">
            {{ $t('pages.hardAssetCustomeTip') }}
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleSave()">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="closeDetail()">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  batchSaveAssetInfo,
  countAssetInfo,
  createAssetProp,
  deleteAssetProp,
  existAssetInfo,
  exportAssetInfo,
  getAssetInfo,
  getAssetInfoDetail,
  getAssetPropPage,
  getColumns,
  getDetailTree,
  getFromDataTree,
  getToDataTree,
  listCustomAssetIno,
  saveSetting,
  updateAssetProp,
  activeSyncAssetBtn
} from '@/api/assets/assetsConfig/assetsView'
import TreeTransfer from 'el-tree-transfer'
import CommonDownloader from '@/components/DownloadManager/common'
import SyncAssetBtn from '@/views/softwareManage/components/syncAssetBtn'

export default {
  name: 'AssetsView',
  components: { TreeTransfer, CommonDownloader, SyncAssetBtn },
  props: {
    // 数据类型，1 硬件资产 2 软件资产
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      filename: this.$t('route.' + this.$route.meta.title) + '.xls',
      fromData: [],
      toData: [],
      detailTreeData: [],
      assetsColModel: [
        { prop: 'terminalId', label: 'terminalCode', width: '100', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'deptName', label: 'terminalGroup', width: '150' }
      ],
      detailButton: { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
        buttons: [
          { label: 'assetConfig', click: this.handleAssetConfig, isShow: () => this.hasPermission('510') }
        ]
      },
      colModel1: [
        { prop: 'propId', label: 'assetProp', width: '100', sort: true, type: 'treeSelect', alwaysEdit: true,
          checkedKeysFieldName: 'checkedKeys', treeData: [], isFilter: true, filterNodeMethod: this.filterNodeMethod, nodeChange: this.nodeChange },
        { prop: 'value', label: 'assetPropValue', width: '150', type: 'input', ellipsis: false }
      ],
      detailColModel: [
        { prop: 'name', label: 'params', width: '100' },
        { prop: 'value', label: 'value', width: '200', type: 'input', editMode: true, showText: this.showTextFormatter, change: this.valueChange }
      ],
      assetPropColModel: [
        { prop: 'parentId', label: 'parentAsset', width: '100', sort: 'custom', formatter: this.parentIdFormatter },
        { prop: 'name', label: 'propName', width: '100', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' }
      ],
      colModel4: [
        { prop: 'name', label: 'params', width: '100' },
        { prop: 'value', label: 'value', width: '200', type: 'input', ellipsis: false }
      ],
      temp: {
        id: null,
        propId: null,
        value: null
      },
      assetPropTemp: {},
      defaultAssetPropTemp: {
        id: undefined,
        type: this.type,
        parentId: undefined,
        name: '',
        remark: ''
      },
      queryDatas: [], // 查询条件数据
      rowData: {},  // 查看详细的数据
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        mark: 'or',
        conditionList: []
      },
      showTree: true,
      addAble: false,
      submitting: false,
      dialogFormVisible: false,
      dialogFormVisible1: false,
      dialogFormVisible2: false,
      dialogFormVisible3: false,
      dialogFormVisible4: false,
      saveBtnVisible: false,
      updateable: false,
      deleteable: false,
      textMap: {
        1: this.$t('pages.assetsView_textMap1'),
        2: this.$t('pages.assetsView_textMap2'),
        3: this.$t('pages.assetsView_textMap3')
      },
      deleteable1: false,
      rules: {
        taskName: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      assetPropRules: {
        parentId: [
          { required: true, validator: this.validateParentId, trigger: 'change' }
        ],
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.validateUniqueName, trigger: 'blur' }
        ]
      },
      sortInfo: {},
      assetTreeData: [],
      customAssetMap: {},
      rowDatas: [],
      filterValue: '', // 资产配置：资产树的搜索值
      filterNodeIds: [], // 资产配置：资产树搜索到的节点id
      syncAssetBtnVisible: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['assetsList']
    },
    rootPidValue() {
      return { 1: '101', 2: '102' }[this.type]
    },
    transferTitle() {
      return [this.$t('pages.sourceList'), this.$t('pages.targetList')]
    }
  },
  watch: {
    dialogFormVisible1(val) {
      if (!val) {
        for (let i = 0; i < this.queryDatas.length; i++) {
          if (!this.queryDatas[i].propId) {
            this.queryDatas.splice(i, 1)
            i--
          }
        }
      }
    },
    showTree() {
      this.$emit('stg-tree-params', {
        showTree: this.showTree
      })
    }
  },
  created() {
    this.getSyncAssetBtnPermission()
    this.loadAssetTree()
    this.loadTableColumn()
    this.$emit('stg-tree-params', {
      showTree: this.showTree
    })
  },
  activated() {
    this.getSyncAssetBtnPermission()
  },
  methods: {
    getSyncAssetBtnPermission() {
      activeSyncAssetBtn().then(res => {
        this.syncAssetBtnVisible = res.data
      })
    },
    loadAssetTree() {
      this.assetTreeData = []
      // 得到资产属性树的数据
      getFromDataTree({ type: this.type, showAll: false }).then(respond => {
        // 树形穿梭框左侧树数据
        this.fromData.splice(0, this.fromData.length, ...respond.data.filter(data => data.children))
      })
      getFromDataTree({ type: this.type, showAll: true }).then(respond => {
        // 自定义查询，资产属性的树数据
        this.colModel1[0].treeData = respond.data
        // 自定义属性，父资产属性树数据
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(item => {
            const assetNode = {
              id: item.id,
              dataId: item.dataId,
              label: item.label
            }
            this.assetTreeData.push(assetNode)
          })
        }
      })
    },
    resetAssetPropTemp() {
      this.assetPropTemp = JSON.parse(JSON.stringify(this.defaultAssetPropTemp))
    },
    assetPropTable() {
      return this.$refs['assetPropTable']
    },
    assetPropForm() {
      return this.$refs['assetPropForm']
    },
    createAssetProp() {
      let validate
      this.assetPropForm().validate(async(valid) => {
        validate = valid
      })
      return new Promise((resolve, reject) => {
        if (validate) {
          const rowData = Object.assign({}, this.assetPropTemp)
          resolve(createAssetProp(rowData).then(res => {
            this.assetPropTable().execRowDataApi()
            this.loadAssetTree()
            this.cancelAssetProp()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            return true
          }).catch(reason => {
            return false
          }))
        } else {
          reject(false)
        }
      })
    },
    updateAssetProp() {
      let validate
      this.assetPropForm().validate(async(valid) => {
        validate = valid
      })
      return new Promise((resolve, reject) => {
        if (validate) {
          const rowData = Object.assign({}, this.assetPropTemp)
          resolve(updateAssetProp(rowData).then(res => {
            this.assetPropTable().execRowDataApi()
            this.loadAssetTree()
            this.cancelAssetProp()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            return true
          }).catch(reason => {
            return false
          }))
        } else {
          reject(false)
        }
      })
    },
    deleteData(toDeleteIds) {
      deleteAssetProp({ ids: toDeleteIds.join(',') }).then(() => {
        this.queryDatas = this.queryDatas.filter(item => !toDeleteIds.includes(Number(item.propId)))
        this.query.conditionList = JSON.parse(JSON.stringify(this.queryDatas))
        this.assetPropTable().execRowDataApi()
        this.loadAssetTree()
        this.assetPropTable().execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.deleteSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    deleteAssetProp() {
      const toDeleteIds = this.assetPropTable().getSelectedIds()
      existAssetInfo({ propIds: toDeleteIds.join(',') }).then(res => {
        if (res.data) {
          this.$confirmBox(this.$t('pages.assetsView_textMap5'), this.$t('text.prompt')).then(() => {
            this.deleteData(toDeleteIds)
          }).catch(() => {})
        } else {
          this.deleteData(toDeleteIds)
        }
      })
    },
    cancelAssetProp() {
      this.assetPropTable() && this.assetPropTable().setCurrentRow()
      this.assetPropForm() && this.assetPropForm().clearValidate()
      this.resetAssetPropTemp()
      this.loadTableColumn()
    },
    beforeAddAssetProp() {
      this.resetAssetPropTemp()
      this.$refs['assetPropForm'].clearValidate()
    },
    beforeUpdateAssetProp() {
      this.resetAssetPropTemp()
      Object.assign(this.assetPropTemp, this.assetPropTable().getSelectedDatas()[0])
      this.$refs['assetPropForm'].clearValidate()
    },
    handleSelectionChange3(rowDatas) {
      this.deleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.updateable = true
      } else {
        this.updateable = false
        this.cancelAssetProp()
      }
    },
    showTextFormatter(col, row) {
      return !row.custome
    },
    valueChange(row) {
      this.saveBtnVisible = true
    },
    parentIdFormatter(row, data) {
      let parentName = this.$t('pages.unknownAsset')
      this.assetTreeData.forEach(node => {
        if (data == node.dataId) {
          parentName = node.label
        }
      })
      return parentName
    },
    validateParentId(rule, value, callback) {
      if (this.assetPropTemp.parentId) {
        callback()
      } else {
        callback(new Error(this.$t('components.required')))
      }
    },
    validateUniqueName(rule, value, callback) {
      const assetRows = this.assetPropTable().getDatas()
      if (!assetRows) {
        callback()
      } else {
        let validate = true
        assetRows.forEach(row => {
          if (row.id != this.assetPropTemp.id && row.parentId == this.assetPropTemp.parentId && row.name == this.assetPropTemp.name) {
            validate = false
          }
        })
        if (validate) {
          callback()
        } else {
          callback(new Error(this.$t('pages.assetsView_textMap6')))
        }
      }
    },
    nodeChange(data, node, vm, row, col) {
      if (data.leaf) {
        const id = data.id
        const key = node.key
        const label = `${node.parent.label}-${node.label}`
        // 修改选中节点显示的label
        const newNode = { id, label, key }
        row[col.prop] = id
        row.checkedKeys = [newNode]
        // 因为要显示新的label名，所以设置选中新节点，并返回 false，阻止默认选中节点
        vm.checkNode(newNode, true)
        vm.isShowSelect = !vm.isShowSelect
        return false
      } else {
        return false
      }
    },
    addCondition() {
      const temp = JSON.parse(JSON.stringify(this.temp))
      temp.id = new Date().getTime()
      this.queryDatas.unshift(temp)
    },
    removeCondition() {
      const ids = this.$refs.conditionTable.getSelectedDatas().map(data => data.id)
      this.queryDatas = this.queryDatas.filter(data => ids.indexOf(data.id) == -1)
    },
    // 清空查询数据
    clearCondition() {
      this.queryDatas.splice(0)
    },
    handleSelectionChange1(val) {
      this.deleteable1 = val.length > 0
    },
    // 监听穿梭框组件添加
    add(fromData, toData, obj) {
      toData.push(obj.nodes[0])
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      // 根据节点id进行排序
      const newFromData = [...fromData.sort((a, b) => a.id - b.id)]
      const newToData = [...toData.sort((a, b) => a.id - b.id)]
      this.$nextTick(() => {
        this.fromData = newFromData
        this.toData = newToData
      })
    },
    filterNode(keyword, node, form) {
      keyword = keyword.trim().toLowerCase()
      if (!keyword) return true
      // 子节点且父节点是搜索结果
      const flag = node.leaf ? this.fromData.some(item => item.id == node.parentId && item.label.toLowerCase().indexOf(keyword) !== -1) : false
      return flag || node.label.toLowerCase().indexOf(keyword) !== -1
    },
    // 监听穿梭框组件移除
    remove(fromData, toData, obj) {
      // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
      // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
      // 根据节点id进行排序
      const newFromData = [...fromData.sort((a, b) => a.id - b.id)]
      const newToData = [...toData.sort((a, b) => a.id - b.id)]
      this.$nextTick(() => {
        this.fromData = newFromData
        this.toData = newToData
      })
    },
    loadTableColumn() { // 加载表头
      getColumns({ type: this.type }).then(respond => {
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(item => {
            item.formatter = this.dataFormatter
          })
        }
        this.assetsColModel = [...respond.data, this.detailButton]
        this.$nextTick(() => {
          this.gridTable.execRowDataApi()
        })
      })
    },
    dataFormatter(row, data) {
      if (typeof data === 'string') {
        return data.replace(/\s/g, '&nbsp;')
      }
      return data
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({ type: this.type }, this.query, option)
      if ('terminalId' === searchQuery.sortName) {
        searchQuery.sortName = 'termId'
      }
      this.sortInfo.sortName = searchQuery.sortName
      this.sortInfo.sortOrder = searchQuery.sortOrder
      if (option.searchCount === false) {
        return getAssetInfo(searchQuery)
      }
      return this.rowTotalApi(searchQuery)
    },
    async rowTotalApi(option) {
      const result = {}
      await countAssetInfo(option).then(resp => {
        result.total = resp.data
      })
      await getAssetInfo(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    rowDataApi2: function(option) {
      const searchQuery = Object.assign({ type: this.type }, option)
      return getAssetInfoDetail(searchQuery)
    },
    rowDataApi3: function(option) {
      const searchQuery = Object.assign({ type: this.type }, option)
      return getAssetPropPage(searchQuery)
    },
    parentSelectChange: function(data, node, vm) {
      this.assetPropTemp.parentId = data
    },
    selectionChangeEnd: function(rowDatas) {
      this.addAble = rowDatas && rowDatas.length == 1
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 0x20 || (type & 0xf0) === 0x80 || type == 3 || (type & 0xf0) === 0x10)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    filterNodeMethod(value, data, node) {
      if (this.filterValue !== value) {
        this.filterNodeIds.splice(0)
        this.filterValue = value
      }
      let filterTextVisible = true
      if (value) {
        filterTextVisible = data.label && data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1
        if (filterTextVisible) {
          this.filterNodeIds.push(data.id)
        }
        if (this.filterNodeIds.length > 0 && this.filterNodeIds.includes(data.parentId)) {
          filterTextVisible = true
        }
      }
      return filterTextVisible
    },
    detailTreeNodeClick: function(data, node, element) {
      // 未选中具体硬件，不加载
      if (data.parentId == 'AssetProp101') {
        this.rowDatas.splice(0)
        return
      }
      const datas = this.$refs['detailTable'].getDatas()
      if (datas && datas.length > 0 && datas[0].assetId) {
        this.customAssetMap[datas[0].assetId] = datas
      }
      if (this.customAssetMap && this.customAssetMap[data.oriData.assetId] && this.customAssetMap[data.oriData.assetId].length > 0) {
        this.rowDatas = this.customAssetMap[data.oriData.assetId]
      } else {
        const row = this.rowData
        let queryParan
        if (this.type == 1) {
          queryParan = { parentId: data.oriData.parentId, terminalId: row.terminalId, type: this.type, assetId: data.oriData.assetId }
        } else if (this.type == 2 && data.id.indexOf('_') != -1) {
          const recordNo = data.id.split('_')[1]
          queryParan = { parentId: data.oriData.parentId, recordNo: recordNo, terminalId: row.terminalId, type: this.type }
        }
        getAssetInfoDetail(queryParan).then(res => {
          this.rowDatas = res.data
        })
      }
    },
    formatFormData() {
      const formData = []
      const datas = this.$refs['detailTable'].getDatas()
      if (datas && datas.length > 0 && datas[0].assetId) {
        this.customAssetMap[datas[0].assetId] = datas
      }
      if (this.customAssetMap) {
        for (var key of Object.keys(this.customAssetMap)) {
          const assets = this.customAssetMap[key]
          assets.forEach(item => {
            if (item.custome) {
              formData.push(item)
            }
          })
        }
      }
      return formData
    },
    closeDetail() {
      if (!this.saveBtnVisible) {
        this.dialogFormVisible2 = false
      } else {
        this.$confirmBox(this.$t('pages.assetsView_textMap7'), this.$t('text.prompt')).then(() => {
          this.dialogFormVisible2 = false
        }).catch(() => {})
      }
    },
    handleSave() {
      this.submitting = true
      const datas = this.formatFormData()
      batchSaveAssetInfo(datas).then(res => {
        this.submitting = false
        this.saveBtnVisible = false
        this.dialogFormVisible2 = false
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.saveSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    detailTreeNodeClick1: function(data, node, element) {
      const datas = this.$refs['customAssetTable'].getDatas()
      if (datas && datas.length > 0) {
        this.customAssetMap[datas[0].parentId] = datas
      }
      if (this.customAssetMap && this.customAssetMap[data.id]) {
        this.rowDatas = this.customAssetMap[data.id]
      } else {
        const row = this.rowData
        listCustomAssetIno({ parentId: data.id, terminalId: row.terminalId }).then(res => {
          this.rowDatas = res.data
        })
      }
    },
    handleFilter() {
      let flag = this.queryDatas.some(item => !item.propId || item.propId == '')
      if (flag) {
        this.$message({
          message: this.$t('pages.assetsView_text3'),
          type: 'error',
          duration: 2000
        })
        return
      }
      flag = this.queryDatas.some(item => !item.value || item.value == '')
      if (flag) {
        this.$message({
          message: this.$t('pages.assetsView_text4'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.dialogFormVisible1 = false
      this.query.page = 1
      this.query.conditionList = JSON.parse(JSON.stringify(this.queryDatas))
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleAddAttribute() {
      this.dialogFormVisible3 = true
      this.assetPropTable() && this.assetPropTable().execRowDataApi()
    },
    handleConfig() {
      this.dialogFormVisible = true
      if (this.$refs.colTransfer) {
        this.$refs.colTransfer.filterFrom = ''
        this.$refs.colTransfer.filterTo = ''
      }
      // 得到资产属性树的数据
      getToDataTree({ type: this.type }).then(respond => {
        this.toData.push(...respond.data)
      })
    },
    handleEditAttribute(row) {
      this.customAssetMap = {}
      this.rowData = row
      this.dialogFormVisible4 = true
      this.$refs.customDetailTree && this.$refs.customDetailTree.clearFilter()
      getDetailTree({ type: this.type, terminalId: row.terminalId }).then(res => {
        this.detailTreeData = res.data
      })
      this.$refs.customAssetTable && this.$refs.customAssetTable.clearRowData()
    },
    handleAssetConfig(row) {
      this.customAssetMap = {}
      this.rowData = row
      this.saveBtnVisible = false
      this.dialogFormVisible2 = true
      this.$refs.detailTree && this.$refs.detailTree.clearFilter()
      getDetailTree({ type: this.type, terminalId: row.terminalId }).then(res => {
        this.detailTreeData = res.data
      })
      this.$refs.detailTable && this.$refs.detailTable.clearRowData()
    },
    exportAsset(file) {
      const searchQuery = Object.assign({ type: this.type }, this.query, this.sortInfo)
      const rows = this.gridTable.getSelectedDatas()
      if (rows.length > 0) {
        const ids = rows.map(item => {
          return item.terminalId
        })
        searchQuery.terminalIds = ids.join(',')
      }
      const opts = { file, jwt: true, topic: this.$route.name }
      exportAssetInfo(searchQuery, opts)
    },
    createData() {
      this.submitting = true
      const tempData = {
        list: this.toData,
        type: this.type
      }
      saveSetting(tempData).then(() => {
        this.submitting = false
        this.dialogFormVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
        this.loadTableColumn()
      }).catch(res => {
        this.submitting = false
      })
    },
    refreshTransferData() {
      getFromDataTree({ type: this.type, showAll: false }).then(respond => {
        this.fromData.splice(0, this.fromData.length, ...respond.data.filter(data => data.children))
      })
      this.toData.splice(0)
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.wl-transfer .transfer-title {
    margin: 0;
  }
</style>
