<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.patchKb" :value="query.patchKb">
          <span>{{ $t('pages.patchID') }}：</span>
          <el-input v-model="query.patchKb" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.installState" :value="query.installState">
          <span>{{ $t('table.status') }}：</span>
          <el-select v-model="query.installState" clearable>
            <el-option
              v-for="item in Object.entries(stateMap)"
              :key="item[0]"
              :label="item[1]"
              :value="item[0]"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.patchLevel" :value="query.patchLevel">
          <span>{{ $t('pages.patchLevel') }}：</span>
          <el-select v-model="query.patchLevel" clearable >
            <el-option
              v-for="item in Object.entries(patchLevelMap).slice().reverse()"
              :key="item[0]"
              :label="item[1]"
              :value="item[0]"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.installStyle" :value="query.installStyle">
          <span>安装方式：</span>
          <el-select v-model="query.installStyle" clearable >
            <el-option
              v-for="item in Object.entries(installStyleMap)"
              :key="item[0]"
              :label="item[1]"
              :value="item[0]"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'537'" :request="exportFunc"/>
        <el-button slot="append" v-permission="'538'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="patchLogList"
        row-key="id"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('538')"
        :row-data-api="rowDataApi"
        :custom-col="true"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('route.patchInstallLogDetail')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.patchID')">
            {{ rowDetail.patchKb }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.status')">
            {{ stateFormat(rowDetail,rowDetail.installState) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.patchLevel')">
            {{ patchLevelFormat(null, rowDetail.patchLevel) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.installMode')">
            {{ installStyleFormatter(null, rowDetail.installStyle) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getPage, exportLog, deleteLog } from '@/api/behaviorAuditing/terminal/patchInstallLog'
import AuditLogExporter from '@/components/AuditFileDownloader/exporter'
import { enableStgDelete } from '@/utils'
import { uploadSoftwareStatus } from '@/api/system/terminalManage/softwareRepository'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import TransferTree from '@/components/StrategyTargetTree/TransferTree.vue';

export default {
  name: 'PatchInstallLog',
  components: { TransferTree, AuditLogExporter },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '200', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100' },
        { prop: 'installState', label: 'status', width: '150', formatter: this.stateFormat },
        { prop: 'patchLevel', label: this.$t('pages.patchLevel'), width: '150', formatter: this.patchLevelFormat },
        { prop: 'installStyle', label: this.$t('pages.installMode'), width: '150', formatter: this.installStyleFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('513'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        installState: null,
        installResult: null,
        patchKb: null,
        patchLevel: null,
        installStyle: null,
        menuCode: this.$route.meta.code,
        tableKey: 'patchLogList'
      },
      showTree: true,
      rowDetail: {},
      queryVideoMethod: undefined,
      dialogFormVisible: false,
      deleteable: false,
      stateMap: {
        0: this.$t('pages.installFailed'), // '安装失败',
        1: this.$t('pages.installSuccessful') // '安装成功'
      },
      patchLevelMap: {
        4: this.$t('pages.severityLevel'),
        3: this.$t('pages.importanceLevel'),
        2: this.$t('pages.midLevel'),
        1: this.$t('pages.lowLevel'),
        0: this.$t('pages.undefinedLevel')
      },
      installStyleMap: {
        1: this.$t('pages.automaticPolicyInstallation'), // '策略自动安装',
        2: this.$t('pages.protocolInstallation') // '协议下发安装'
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['patchLogList']
    }
  },
  created() {
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    stateFormat: function(row, data) {
      let status = this.stateMap[data]
      if (row.installResult) {
        status = status + '(' + row.installResult + ')'
      }
      return status
    },
    patchLevelFormat: function(row, data) {
      return this.patchLevelMap[data]
    },
    installStyleFormatter: function(row, data) {
      return this.installStyleMap[data]
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportLog({ exportType, ...this.query })
    },
    async handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        console.log('toDeletetoDelete', toDelete)
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    // taskActiveFormatter(data) {
    //   return this.taskActiveOptions[data]
    // },
    stateFormatter(row, data) {
      if (data == 1) {
        return this.$t('pages.transferStatusDict34')
      } else if (data == 2) {
        return this.$t('pages.excuteFail')
      } else if (data == 3) {
        return this.$t('pages.consoleEndTask')
      } else if (data == 4) {
        return this.$t('pages.termEndTask')
      }
    },
    taskFailCodeFormatter(row, data) {
      if (data == 0) {
        return ''
      } else if (data == 1) {
        return this.$t('pages.uninstallSuscess')
      } else if (data == 2) {
        return this.$t('pages.callInstallFail')
      } else if (data == 3) {
        return this.$t('pages.sendFail1')
      } else if (data == 4) {
        return this.$t('pages.uncontroller')
      } else if (data == 5) {
        return this.$t('pages.failCodeText1')
      } else if (data == 6) {
        return this.$t('pages.failCodeText2')
      } else if (data == 7) {
        return this.$t('pages.failCodeText3')
      } else if (data == 8) {
        return this.$t('pages.failCodeText4')
      } else {
        const status = uploadSoftwareStatus[data]
        return !status ? this.$t('pages.encOrDecLog_Msg14') + ':' + data : status
      }
    },
    afterLoad(rowData) {
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '513')
    }
  }
}
</script>
