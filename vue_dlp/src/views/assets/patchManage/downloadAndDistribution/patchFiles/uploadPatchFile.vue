<template>
  <div>
    <el-dialog
      v-if="dialogFormVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.uploadPatch')"
      :visible.sync="dialogFormVisible"
      width="800px"
      :before-close="close"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('button.uploadPatch') }}
        <el-tooltip effect="dark" placement="right">
          <div slot="content">
            {{ $t('pages.uploadPatchTip1') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="dataForm">
        <!--        @change = "changeUploadMethod"-->
        <FormItem :label="$t('pages.uploadMethod')" style="margin-left: 0px;margin-bottom: 15px">
          <el-radio-group v-model="type" size="small">
            <el-radio :label="2" @click.native.prevent="changeUploadMethod(2)">{{ $t('pages.scanServerFiles') }}
              <span style="height: 32px; align-items: center; margin-right: 5px">
                <el-tooltip>
                  <template slot="content">
                    <i18n path="pages.scanServerFileTip">
                      <br slot="br"/>
                    </i18n>
                  </template>
                  <i class="el-icon-info"/>
                </el-tooltip>
              </span></el-radio>
            <el-radio :label="1" style="margin-left: 10px;" @click.native.prevent="changeUploadMethod(1)">{{ $t('pages.uploadFilesThroughBrowser') }}
              <span style="height: 32px; align-items: center; margin-right: 5px">
                <el-tooltip>
                  <template slot="content">
                    {{ $t('pages.uploadPatchTip2') }}
                    <!--                    <i18n path="pages.scanServerFileTip">-->
                    <!--                      <br slot="br"/>-->
                    <!--                    </i18n>-->
                  </template>
                  <i class="el-icon-info"/>
                </el-tooltip>
              </span>
            </el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <patch-batch-upload v-if="type === 1" ref="patchBatchUpload" @uploadPatchList="uploadPatchList"/>
      <patch-sync v-if="type === 2" ref="patchSync"/>

      <!--      <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">-->
      <!--        <el-tab-pane label="浏览器上传文件" name="patchBatchUpload" >-->
      <!--          <patch-batch-upload v-if="isPatchBatchUpload" ref="patchBatchUpload"></patch-batch-upload>-->
      <!--        </el-tab-pane>-->
      <!--        <el-tab-pane label="扫描服务器文件" name="patchSync" >-->
      <!--          <patch-sync/>-->
      <!--        </el-tab-pane>-->
      <!--      </el-tabs>-->
    </el-dialog>
  </div>
</template>
<script>
import PatchSync from './patchSync';
import PatchBatchUpload from './patchBatchUpload';

export default {
  name: 'UploadPatchFile',
  components: { PatchBatchUpload, PatchSync },
  props: { },
  data() {
    return {
      dialogFormVisible: false,
      activeName: 'patchBatchUpload',
      isPatchBatchUpload: true,
      isPatchSync: false,
      type: 2,
      browserPatchList: []
    }
  },
  watch: {
    // 'type'(newValue, oldValue) {
    //   if (newValue == 1) {
    //     this.$refs.patchBatchUpload.show()
    //   }
    //   if (newValue == 2) {
    //     this.$refs.patchSync.show()
    //   }
    // }
  },
  methods: {
    handleDrag() {
    },
    close(done) {
      console.log(this.type, this.$refs.patchBatchUpload, this.dialogFormVisible)
      if (this.type === 1) {
        if (this.browserPatchList.length > 0) {
          // 关闭会中断上传，是否继续？
          this.$confirmBox(this.$t('pages.uploadPatchTip3'), this.$t('text.prompt')).then(() => {
            if (this.$refs.patchBatchUpload) {
              this.$refs.patchBatchUpload.close()
            }
            this.dialogFormVisible = false;
            this.type = 2;
            done()
          }).catch(() => {})
        } else {
          this.type = 2;
          done()
        }
      }
      if (this.type == 2) {
        this.type = 2;
        if (this.$refs.patchBatchUpload) {
          this.$refs.patchBatchUpload.close()
        }
        this.dialogFormVisible = false;
        done()
      }
    },
    show() {
      this.dialogFormVisible = true;
      // this.$refs.patchBatchUpload.show()
    },
    changeUploadMethod(event) {
      console.log(event)
      if (event == 2 && this.browserPatchList.length > 0) {
        // 浏览器上传文件切换方式会中断上传，是否继续？uploadPatchTip4
        this.$confirmBox(this.$t('pages.uploadPatchTip4'), this.$t('text.prompt')).then(() => {
          this.type = event
          console.log(event)
          this.browserPatchList = []
        }).catch(() => {})
      } else {
        this.type = event
      }
    },
    uploadPatchList(patchList) {
      console.log('uploadPatchList', patchList)
      this.browserPatchList = patchList
    },
    isPatchBatchUploadPanel() {
      return this.activeName === 'patchBatchUpload'
    },
    isPatchSyncPanel() {
      return this.activeName === 'patchSync'
    },
    // tab菜单点击事件
    tabClick(pane, event) {
      if (this.isPatchBatchUploadPanel()) {
        this.isPatchBatchUpload = true
        this.$refs.patchBatchUpload.show()
        this.isPatchSync = false
      } else if (this.isPatchSyncPanel()) {
        this.isPatchBatchUpload = false
        this.isPatchSync = true
      }
    }
  }
}
</script>
