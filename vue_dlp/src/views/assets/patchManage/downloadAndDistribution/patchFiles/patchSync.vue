<template v-if="dialogFormVisible">
  <div class="app-main">
    <Form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="120px"
      :hide-required-asterisk="true"
    >
      <el-row>
        <el-col :span="15">
          <FormItem :label="$t('pages.patchFilePath')" label-width="90px" prop="path">
            <el-input v-model="query.path" clearable :readonly="submitting" ></el-input>
          </FormItem>
        </el-col>
        <el-col :span="9">
          <div class="btn-groups">
            <el-button v-if="formable" :loading="submitting" :disabled="submitting" type="primary" @click="handleFilter">
              {{ $t('pages.read1') }}
            </el-button>
            <el-button v-if="formable" :loading="submitting" :disabled="submitting" type="primary" @click="createData()">
              {{ $t('button.sync') }}
            </el-button>
            <span>{{ $t('pages.completedTotal') + finish + '/' + total }} </span>
          </div>
        </el-col>
      </el-row>
      <grid-table
        ref="fileList"
        :height="300"
        :multi-select="false"
        :show-pager="false"
        :col-model="colModel"
        :row-datas="rowDatas"
        :row-data-api="rowDataApi"
        row-key="guid"
        :checked-row-keys="checkedRowKeys"
      />
    </Form>
  </div>
</template>
<script>
import { getPatchFilesByPath, syncFile, getSyncParam } from '@/api/assets/patchManage/patchFiles';

export default {
  name: 'PatchSync',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      dialogFormVisible: false,
      submitting: false,
      colModel: [
        { prop: 'id', label: 'keyId', width: '80', fixed: true, sort: true, sortOriginal: true },
        { prop: 'guid', label: 'GUID', width: '150', fixed: true, sort: true, sortOriginal: true },
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100', fixed: true, sort: true, formatter: this.patchKbFormatter, disabled: true },
        { prop: 'downloadAddr', label: this.$t('pages.downloadAddress'), width: '120' },
        { prop: 'downloadState', label: this.$t('pages.patchStatus'), width: '120', sort: true, sortOriginal: true, formatter: this.downloadStateFormat }
      ],
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        // 0: this.$t('pages.incompleteCollection'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      fileList: [],
      fileGuids: [],
      finish: 0,
      total: 0,
      checkedRowKeys: [],
      temp: {
        path: ''
      },
      query: {
        page: 1,
        path: ''
      },
      rules: {
      },
      rowDatas: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['fileList']
    }
  },
  created() {
    this.dialogFormVisible = true
    this.getSyncParam()
    this.syncPatchFileResult()
  },
  beforeDestroy() {
    this.$socket.unsubscribe('/topic/syncPatchFileResult');
  },
  methods: {
    close() {
      this.$socket.unsubscribe('/topic/syncPatchFileResult');
    },
    handleDrag() {
    },
    downloadStateFormat: function(row, data) {
      return this.downloadStateMap[data]
    },
    show() {
      this.dialogFormVisible = true
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.getSyncParam()
    },
    getSyncParam() {
      console.log('调用 getSyncParam')
      getSyncParam().then(res => {
        if (this.query.path === '' && res.data.path !== '') {
          this.query.path = res.data.path
        }
        this.submitting = res.data.syncFileStatus
        // console.log('getSyncParam', res)
      })
    },
    rowDataApi: function(option) {
      console.log('this.query', this.query)
      const searchQuery = Object.assign({}, this.query, option)
      const data = getPatchFilesByPath(searchQuery);
      data.then(res => {
        // 统计数量
        this.fileList = res.data.items
        this.fileGuids = this.fileList.map(file => file.guid)
        this.finish = res.data.items.filter(data => this.fileGuids.includes(data.guid)).filter(data => data.downloadState == 7).length
        this.total = this.fileList.length
        console.log('getPatchFilesPage', res.data.items)
      })
      return data
    },
    createData() {
      this.submitting = true
      syncFile(this.query).then(res => {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.syncFileMsg1'),
          type: 'success',
          duration: 2000
        })
        // this.$socket.subscribe({ url: '/topic/syncPatchFileResult', callback: (respond, handle) => {
        //   that.submitting = false
        //   console.log('同步成功')
        //   getPatchFilesByPath(that.query).then(respond => {
        //     console.log('respond', respond)
        //     let finish = 0;
        //     respond.data.items.forEach(r => {
        //       if (r.downloadState === 7) {
        //         finish++
        //       }
        //       // console.log('this.$refs[\'fileList\']', this.$refs['fileList'])
        //       this.$refs['fileList'].updateRowData(r)
        //     })
        //     this.finish = finish
        //   })
        // } });
      }).catch((reason) => {
        console.log('reason', reason)
        this.submitting = false
      })
    },
    syncPatchFileResult() {
      console.log('user', this.$store.state.user.userId)
      let exec = true
      this.$socket.subscribe({ url: '/topic/syncPatchFileResult', callback: (respond, handle) => {
        // 读取文件上传结果
        if (exec) {
          exec = false
          getPatchFilesByPath(this.query).then(respond => {
            console.log('respond', respond)
            let finish = 0;
            respond.data.items.forEach(r => {
              if (this.fileGuids.includes(r.guid)) {
                if (r.downloadState === 7) {
                  finish++
                }
                this.$refs['fileList'].updateRowData(r)
              }
            })
            console.log('this.finish', this.finish)
            this.finish = finish
            exec = true
          }).catch(() => {
            exec = true
          })
        }
        // 读取同步按钮是否禁用
        this.getSyncParam()
      } });
    }
  }
}
</script>

<style lang="scss" scoped>
  .btn-groups {
    padding: 1px 5px 0;
    >>>.el-button {
      height: 30px;
    }
  }
</style>
