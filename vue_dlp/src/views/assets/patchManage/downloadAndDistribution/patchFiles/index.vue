<template>
  <div v-loading="showLoading" class="app-container serverInfo">
    <div class="table-container">
      <div class="toolbar">
        <!--        <el-button type="primary" icon="el-icon-timer" size="mini" @click="handlePatchCycleShow">-->
        <!--          {{ $t('pages.patchDownloadTimePeriodSettings') }}-->
        <!--        </el-button>-->
        <el-button v-permission="'511'" type="primary" :disabled="patchList.length < 1" icon="el-icon" size="mini" @click="handleBatchDownload">
          {{ $t('button.batchDownload') }}
        </el-button>
        <el-button v-show="true" v-permission="'534'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-show="true" v-permission="'535'" icon="el-icon-download" size="mini" @click="handleUploadPatch">
          {{ $t('button.uploadPatch') }}
        </el-button>
        <!--        <el-button v-show="true" icon="el-icon-download" size="mini" @click="handleUpload">-->
        <!--          {{ $t('button.batchUploadPatches') }}-->
        <!--        </el-button>-->
        <!--        <el-button v-show="true" icon="el-icon-download" size="mini" @click="handlePatchSync">-->
        <!--          {{ $t('button.patchSynchronization') }}-->
        <!--        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.patchKb" v-trim clearable :placeholder="$t('pages.validateMsg_enterName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="65px">
              <FormItem :label="$t('pages.patchID')">
                <el-input v-model="query.patchKb" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.patchStatus')">
                <el-select v-model="query.downloadState" clearable style="width: 100%;">
                  <el-option v-for="item in Object.entries(queryDownloadStateMap)" :key="item[0]" :label="item[1]" :value="item[0]"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.patchLevel')">
                <el-select v-model="query.patchLevel" clearable style="width: 100%;">
                  <el-option v-for="item in Object.entries(patchLevelMap).slice().reverse()" :key="item[0]" :label="item[1]" :value="item[0]"></el-option>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="patchFileTable"
        :col-model="detectingFileColModel"
        :row-data-api="rowDataApi"
        :multi-select="true"
        :selectable="checkboxFormatter"
        row-key="guid"
        @selectionChangeEnd="patchSelectionChangeEnd"
      />
    </div>
    <!--    <patch-cycle ref="patchCycle" @submitEnd="submitEnd" />-->
    <export-dlg ref="exportDlg" :title="$t('pages.patch')" file-extension=".csv" :export-func="exportFunc"/>
    <upload-patch-file ref="uploadPatchFile"></upload-patch-file>
    <!--    <Patch-Batch-Upload-dlg ref="patchBatchUploadDlg" />-->
    <!--    <Patch-Sync ref="patchSync"></Patch-Sync>-->
  </div>
</template>
<script type="text/jsx">
import { getPatchFilesPage, clickToDownload, exportExcel } from '@/api/assets/patchManage/patchFiles';
import ExportDlg from '@/views/common/export.vue'
import UploadPatchFile from './uploadPatchFile'
// import PatchBatchUploadDlg from './patchBatchUpload';
// import PatchSync from './patchSync';
// import UploadPatchFile from '@/views/assets/patchManage/downloadAndDistribution/patchFiles/uploadPatchFile.vue';
// import PatchCycle from './patchCycle'

export default {
  name: 'PatchFiles',
  components: { UploadPatchFile, ExportDlg },
  props: {},
  data() {
    return {
      showLoading: false,
      detectingFileColModel: [
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100', fixed: true, sort: true, formatter: this.patchKbFormatter, disabled: true },
        { prop: 'patchTitle', label: this.$t('pages.patchTitle'), width: '200' },
        { prop: 'patchDescribe', label: this.$t('pages.patchDescription'), width: '200' },
        { prop: 'patchSize', label: 'size', width: '100', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'patchLevel', label: this.$t('pages.patchLevel'), width: '100', sort: true, sortOriginal: true, formatter: this.patchLevelFormat },
        { prop: 'downloadState', label: this.$t('pages.patchStatus'), width: '100', formatter: this.downloadStateFormat },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
          buttons: [
            { label: 'download', formatter: this.buttonFormatter, click: this.handleDownloading, isShow: this.isShowDownloadFormat },
            { label: 'resultDetail', formatter: this.buttonFormatter, isShow: this.isShowButtonFormat, click: this.handleDetail }
          ]
        }
      ],
      // subColModel: [
      //   { prop: 'guid', label: 'guid', width: '100', fixed: true, sort: true }
      // ],
      // 下载状态0：未收集完整 1：数据已收集完成 2：正在下载 3：下载失败 4：已下载到-Server 5：正在分发ftp 6：分发ftp失败 7：FTP分发完成
      // downloadStateMap: {
      //   0: this.$t('pages.incompleteCollection'),
      //   1: this.$t('pages.dataCollectionCompleted'),
      //   2: this.$t('pages.downloading'),
      //   3: this.$t('pages.downloadFailed'),
      //   4: this.$t('pages.downloaded'),
      //   5: this.$t('pages.distributingFtp'),
      //   6: this.$t('pages.failedToDistributeFtp'),
      //   7: this.$t('pages.FTPDistributionCompleted')
      // },
      queryDownloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        // 3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        // 5: '下载中',
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        // 0: this.$t('pages.incompleteCollection'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      patchLevelMap: {
        4: this.$t('pages.severityLevel'),
        3: this.$t('pages.importanceLevel'),
        2: this.$t('pages.midLevel'),
        1: this.$t('pages.lowLevel'),
        0: this.$t('pages.undefinedLevel')
      },
      patchList: [], // 选中的补丁数据
      query: {
        page: 1,
        searchInfo: undefined,
        patchKb: '',
        patchTitle: undefined,
        downloadState: undefined,
        patchLevel: undefined
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['patchFileTable']
    },
    // patchCycle() {
    //   return this.$refs['patchCycle']
    // },
    // 使用计算属性来返回倒序后的数组
    reversedItems() {
      return this.patchLevelMap.slice().reverse();
    }
  },
  created() {
  },
  activated() {
  },
  mounted() {
  },
  methods: {
    patchKbFormatter: function(row, data) {
      if (data != null && data != '') {
        return data
      } else {
        return '';
      }
    },
    checkboxFormatter(row, index) {
      return row.parentGuid === ''
    },
    sizeFormatter: function(row, data) {
      if (row.downloadSize && [1, 2].includes(row.downloadState)) {
        return this.convertSize(data) + '(' + this.convertSize(row.downloadSize) + ')'
      } else {
        return this.convertSize(data)
      }
    },
    patchLevelFormat: function(row, data) {
      return this.patchLevelMap[data]
    },
    convertSize: function(size) {
      if (!size) {
        return '0 Bytes';
      }
      const sizeNames = [' Bytes', ' KB', ' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
      const i = Math.floor(Math.log(size) / Math.log(1024));
      const p = (i > 0) ? 2 : 1;
      if (p == 1) {
        return 1 + sizeNames[1];
      } else {
        return (size / Math.pow(1024, Math.floor(i))).toFixed(p) + sizeNames[i];
      }
    },
    downloadStateFormat: function(row, data) {
      if (row.progress && [1, 2].includes(row.downloadState)) {
        return this.downloadStateMap[data] + '(' + row.progress + '%)'
      } else if (row.downloadDetails && [3].includes(row.downloadState)) {
        return this.downloadStateMap[data] + '(' + row.downloadDetails + ')'
      } else {
        return this.downloadStateMap[data]
      }
    },
    isShowDownloadFormat: function(row) {
      return this.hasPermission('511') && row.patchTitle
    },
    isShowButtonFormat: function(row) {
      return false
      // return row.parentGuid !== undefined && row.parentGuid !== ''
    },
    patchSelectionChangeEnd: function(rowDatas) {
      const guids = rowDatas.map(r => r.guid)
      const patchList = []
      guids.forEach(guid => {
        const patch = { guid: guid, downloadState: 2 };
        patchList.push(patch)
      })
      this.patchList = patchList
    },
    resetQuery() {
      this.query.page = 1
      this.query.patchKb = ''
      this.query.searchInfo = ''
      this.query.patchTitle = ''
      this.query.downloadState = ''
      this.query.patchLevel = ''
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      const data = getPatchFilesPage(searchQuery);
      console.log('getPatchFilesPage', data)
      return data
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleBatchDownload() {
      const guids = this.gridTable.getSelectedKeys();
      console.log('selectedKeys', guids)
      const vo = { guidList: guids }
      // const patchList = []
      // guids.forEach(guid => {
      //   const patch = { guid: guid, downloadState: 2 };
      //   patchList.push(patch)
      // })
      // this.patchList = patchList
      // 确定批量下载补丁
      this.$confirmBox(this.$t('pages.batchDownloadPatchMsg'), this.$t('text.prompt')).then(() => {
        const that = this;
        clickToDownload(vo).then(res => {
          that.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.downloadingWait'),
            type: 'success',
            duration: 2000
          })
          // that.handleFilter()
          // TRDLP-15801 【补丁管理】补丁库，切换到第二页下载不到，下载中会自动刷新到第一页，建议保留在第二页
          that.gridTable.execRowDataApi()
        })
      }).catch(() => {})
    },
    handleDownloading(row) {
      let msg = '';
      if (row.downloadState) {
        msg = this.$t('pages.sureDownloadAgain')
      } else {
        msg = this.$t('pages.sureDownload')
      }
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        // const data = [{ guid: row.guid, downloadState: 2 }]
        const data = { guidList: [row.guid] }
        const that = this;
        clickToDownload(data).then(res => {
          that.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.downloadingWait'),
            type: 'success',
            duration: 2000
          })
          // that.handleFilter()
          // TRDLP-15801 【补丁管理】补丁库，切换到第二页下载不到，下载中会自动刷新到第一页，建议保留在第二页
          that.gridTable.execRowDataApi()
        })
      }).catch(() => {})
    },
    handleDetail(row) {
      this.$emit('show-result', row.downloadDetails)
    },
    // 导出
    handleExport() {
      console.log('handleExport', this.gridTable.getSelectedKeys())
      this.$refs.exportDlg.show(this.gridTable.getSelectedKeys())
    },
    exportFunc(formData, opts) {
      console.log('exportFunc', formData)
      console.log('exportFunc', opts)
      const { patchKb, downloadState, patchLevel, sortName, sortOrder } = this.query
      return exportExcel({
        parentGuidList: formData.type === 1 ? formData.dataIds : null,
        patchKb: formData.type === 3 ? patchKb : null,
        downloadState: formData.type === 3 ? downloadState : null,
        patchLevel: formData.type === 3 ? patchLevel : null,
        sortName,
        sortOrder
      }, opts)
    },
    handleUploadPatch() {
      this.$refs.uploadPatchFile.show()
    },
    // 上传
    handleUpload() {
      this.$refs.patchBatchUploadDlg.show()
    },
    // 同步
    handlePatchSync() {
      this.$refs.patchSync.show()
    }
    // submitEnd() {
    //   // this.notifySuccess(this.$t('text.updateSuccess'))
    //   this.gridTable.execRowDataApi(this.query)
    // },
    // handlePatchCycleShow() {
    //   this.patchCycle.show()
    // }
  }
}
</script>
