<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.patchDownloadTimePeriodSettings')"
    :close-on-click-modal="false"
    :modal="false"
    destroy-on-close
    :visible.sync="editVisible"
    width="600px"
    @dragDialog="handleDrag"
    @close="handleRemove"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 430px;">
      <!--      <FormItem label-width="0">-->
      <!--        <el-checkbox v-model="temp.isLimitTime" :false-label="0" :true-label="1">{{ $t('pages.limitPatchDownloadTimePeriod') }}</el-checkbox>-->
      <!--        <el-input v-model="temp.limitTime" style="width: 90%" readonly :disabled="temp.isLimitTime==0"></el-input>-->
      <!--        <el-popover-->
      <!--          v-model="showTimeConponent"-->
      <!--          :disabled="temp.isLimitTime==0"-->
      <!--          placement="right"-->
      <!--          width="400"-->
      <!--          trigger="click"-->
      <!--        >-->
      <!--          <div class="edit-item">-->
      <!--            <FormItem>-->
      <!--              <el-row v-for="(value, index) in timeList" :key="index">-->
      <!--                <el-col :span="20">-->
      <!--                  <el-time-picker-->
      <!--                    v-model="timeList[index]"-->
      <!--                    is-range-->
      <!--                    :editable="true"-->
      <!--                    value-format="HH:mm:ss"-->
      <!--                    range-separator=" &#45;&#45; "-->
      <!--                    :start-placeholder="$t('pages.startTime')"-->
      <!--                    :end-placeholder="$t('pages.endTime')"-->
      <!--                    :placeholder="$t('pages.timeInfo_text3')"-->
      <!--                    style="width: 100%"-->
      <!--                    :clearable="false"-->
      <!--                  >-->
      <!--                  </el-time-picker>-->
      <!--                </el-col>-->
      <!--                <el-col :span="1">&nbsp;</el-col>-->
      <!--                <el-col :span="1">-->
      <!--                  <svg-icon icon-class="delete" class-name="delete-time" @click="rmlastitems(index)"/>-->
      <!--                </el-col>-->
      <!--                <el-col :span="1" style="padding-left: 5px">-->
      <!--                  <svg-icon icon-class="add" :class-name="{'add-time-item': true, 'is-disabled': !editable}" @click="addlastitems"/>-->
      <!--                </el-col>-->
      <!--              </el-row>-->
      <!--            </FormItem>-->
      <!--            <div class="edit-btns">-->
      <!--              <el-button type="primary" size="mini" @click="saveItems">-->
      <!--                {{ $t('button.confirm') }}-->
      <!--              </el-button>-->
      <!--              <el-button size="mini" @click="clearItems">-->
      <!--                {{ $t('button.clear') }}-->
      <!--              </el-button>-->
      <!--              <el-button size="mini" @click="resetItem">-->
      <!--                {{ $t('button.cancel') }}-->
      <!--              </el-button>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--          <span slot="reference" style="cursor: pointer;">-->
      <!--            <svg-icon icon-class="add" :class-name="{'download-time-icon': true, 'is-disabled': temp.isLimitTime==0}" />-->
      <!--          </span>-->
      <!--        </el-popover>-->
      <!--      </FormItem>-->

      <div >
        <el-row>
          <el-checkbox v-model="temp.isLimitTime" :false-label="0" :true-label="1">{{ $t('pages.limitPatchDownloadTimePeriod') }}</el-checkbox>
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.patchDownloadTimePeriodTip">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-row>
        <el-row>
          <el-col :span="20">
            <FormItem label-width="0">
              <el-input v-model="temp['limitTime']" readonly :maxlength="90"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="4">
            <svg-icon v-show="!showTimeConponent" icon-class="add" class-name="add-time" @click="showItems()" />
            <svg-icon v-show="showTimeConponent" icon-class="active" class-name="add-time" @click="saveItems()" />
          </el-col>
        </el-row>
        <div v-show="showTimeConponent" class="edit-item">
          <FormItem label-width="0">
            <el-row v-for="(value, index) in timeList" :key="index">
              <el-col :span="20">
                <el-time-picker
                  v-model="timeList[index]"
                  is-range
                  :editable="true"
                  value-format="HH:mm:ss"
                  range-separator=" -- "
                  :start-placeholder="$t('pages.startTime')"
                  :end-placeholder="$t('pages.endTime')"
                  :placeholder="$t('pages.timeInfo_text3')"
                  style="width: 330px;margin-left: 10px;"
                >
                </el-time-picker>
              </el-col>
              <el-col :span="4" style="float:right;width: 32px;cursor: pointer;color: #68a8d0;">
                <i v-show="editable" class="el-icon-circle-plus-outline" @click="addTimeItem(index + 1)"></i>
                <i class="el-icon-remove-outline" @click="deleteTimeItem(index)"></i>
              </el-col>
            </el-row>
          </FormItem>
          <span style="padding-left: 10px; color: #0c60a5;">{{ $t('pages.timeInfo_text2') }}</span>
        </div>
      </div>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="editVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getDownloadCycle, updateDownloadCycle } from '@/api/assets/patchManage/patchFiles';

export default {
  name: 'PatchCycle',
  components: { },
  props: {},
  data() {
    return {
      editVisible: false,
      showTimeConponent: false,
      timeList: [],
      editable: true,
      rules: {},
      temp: {}, // 检测文件
      defaultTemp: { // 表单字段
        limitTime: '00:00:00-05:59:59',
        isLimitTime: false
      }
    }
  },
  watch: {
    showTimeConponent(val) {
      if (val) {
        this.showItems()
      }
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  mounted() {
  },
  methods: {
    // 通用方法
    handleDrag() {
    },
    handleRemove(file, fileList) {
    },
    resetTemp() {
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
    },
    show() {
      this.editVisible = true;
      this.showTimeConponent = false;
      this.getDownloadCycle()
    },
    handleInput(value) {
    },
    showItems() {
      this.timeList = []
      this.editable = true
      this.showTimeConponent = true
      const timeArr = this.temp.limitTime.split(';')
      if (timeArr.length >= 5) {
        this.editable = false
      }
      timeArr.forEach(el => {
        const strArr = el.split('-')
        this.timeList.push(strArr)
      })
    },
    // rmlastitems(index) {
    //   if (this.timeList.length > 1) {
    //     this.timeList.splice(index, 1)
    //   } else {
    //     this.timeList = [['00:00:00', '00:00:00']]
    //   }
    //   this.editable = true
    // },
    // addlastitems() {
    //   if (this.timeList.length < 5) {
    //     this.timeList.push(['00:00:00', '23:59:59'])
    //   }
    //   if (this.timeList.length >= 5) {
    //     this.editable = false
    //   }
    // },
    // clearItems() {
    //   this.timeList = [['00:00:00', '00:00:00']]
    //   this.editable = true
    // },
    saveItems() {
      console.log('this.timeList', this.timeList)
      if (this.timeList.length > 0) {
        this.temp.limitTime = ''
        this.timeList.forEach(el => {
          const str = el[0] + '-' + el[1]
          if (this.temp.limitTime) {
            if (this.temp.limitTime.indexOf(str) == -1) {
              this.temp.limitTime = this.temp.limitTime + ';' + str
            }
          } else {
            this.temp.limitTime = str
          }
        })
      }
      // this.timeList = []
      this.editable = true
      this.showTimeConponent = false
    },
    // resetItem() {
    //   this.editable = true
    //   this.showTimeConponent = false
    // },
    deleteTimeItem(index) {
      this.timeList.splice(index, 1)
      if (this.timeList.length === 0) {
        this.addTimeItem(0)
      }
      if (this.timeList.length >= 5) {
        this.editable = false
      } else {
        this.editable = true
      }
    },
    addTimeItem(index) {
      if (this.timeList.length < 5) {
        this.timeList.splice(index, 0, ['00:00:00', '23:59:59'])
      }
      if (this.timeList.length >= 5) {
        this.editable = false
      } else {
        this.editable = true
      }
    },
    getDownloadCycle() {
      getDownloadCycle().then(res => {
        const cycle = JSON.parse(res.data.value)
        this.temp = Object.assign({}, cycle, { isLimitTime: Number(cycle.isLimitTime) })
        console.log('this.temp', this.temp)
      })
    },
    updateData() {
      this.saveItems()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          console.log('this.temp', JSON.stringify(this.temp))
          const that = this
          const tempData = Object.assign({}, this.temp)
          updateDownloadCycle(tempData).then(respond => {
            that.submitting = false
            that.editVisible = false
            that.$emit('submitEnd')
            that.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.add-time{
  margin: 9px 0 0 10px;
  cursor: pointer;
}
.edit-item{
  max-height: 200px;
  width: 420px;
  padding: 5px 7px;
  margin: 0px 45px 5px 0px;
  border: 1px solid #aaa;
  >>>.el-form-item{
    max-height: 160px;
    overflow-y: auto;
  }
}
</style>
