<template >
  <div v-if="dialogFormVisible">
    <Form ref="dataForm2" :rules="rules" :model="temp" label-position="right" label-width="140px">
      <div class="toolbar">
        <upload-dir ref="uploadDir" :file-suffix="['cab','exe','psf','msu','mpk','rkp','div','wim']" :text="$t('pages.scanDir')" :popover-height="235" :limit-size="1024" style="display: inline-block" @changeFile="changeFile" />
        <el-button type="primary" :loading="uploadSubmitting" :disabled="fileList.length === 0 || uploadSubmitting" style="display: inline-block;" size="mini" @click="handleBatchUpload">
          {{ $t('button.uploadPatch') }}
        </el-button>
      </div>
      <grid-table
        ref="fileList"
        :height="300"
        :multi-select="true"
        :show-pager="false"
        :col-model="colModel"
        :row-datas="fileList"
        row-key="guid"
        :checked-row-keys="checkedRowKeys"
        @selectionChangeEnd="patchSelectionChangeEnd"
      />
    </Form>
  </div>
</template>
<script>
import { checkFile, uploadPatchLog, uploadChunk } from '@/api/assets/patchManage/patchFiles';
import UploadDir from '@/components/UploadDir/index.vue';
import axios from 'axios';
import md5 from '@/utils/md5';

export default {
  name: 'PatchBatchUploadDlg',
  components: { UploadDir },
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    groupTreeData: { type: Array, default() { return [] } }
  },
  data() {
    return {
      dialogFormVisible: false,
      colModel: [
        { prop: 'id', label: 'keyId', width: '70', sort: true },
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100', sort: true, disabled: true },
        { prop: 'guid', label: 'GUID', width: '150', sort: 'custom' },
        { prop: 'downloadAddr', label: this.$t('pages.downloadAddress'), width: '180' },
        { prop: 'progress', label: 'progress', width: '70', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'reUpload', click: this.handleReUpload }
          ]
        }
        // { prop: 'downloadState', label: '补丁状态', width: '120', formatter: this.downloadStateFormat }
      ],
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        // 0: this.$t('pages.incompleteCollection'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      fileSubmitting: false, // 扫描目录按钮，文件提交
      uploadSubmitting: false, // 上传文件按钮
      fileLimitSize: 1024,
      uploadPercent: 0,
      fileList: [], // 对比补丁库，得出可上传文件列表
      uploadFiles: [], // 扫描上传的补丁文件
      selectPatchList: [], // 选中的补丁文件
      waitUploadGuids: [], // 等待上传的文件guid
      checkedRowKeys: [],
      uploadCanceled: false,
      queue: [], // 上传队列
      execUpload: false,
      defaultTemp: {
        guid: ''
      },
      temp: {},
      rules: {
      }
    }
  },
  created() {
    this.dialogFormVisible = true
  },
  beforeDestroy() {

  },
  methods: {
    handleDrag() {
      // 移动弹窗时回调
    },
    close() {
      console.log('close', '关闭弹窗')
      this.resetDate()
      this.$refs.uploadDir.clearValue()
      this.dialogFormVisible = false
      this.queue = [];
    },
    downloadStateFormat: function(row, data) {
      return this.downloadStateMap[data]
    },
    patchSelectionChangeEnd: function(rowDatas) {
      const guids = rowDatas.map(r => r.guid)
      const patchList = []
      guids.forEach(guid => {
        const patch = { guid: guid, downloadState: 2 };
        patchList.push(patch)
      })
      this.selectPatchList = patchList
    },
    show() {
      this.dialogFormVisible = true
      // 列表有文件，切换方式才需要提示终止上传。
      this.$emit('uploadPatchList', this.fileList)
    },
    resetDate() {
      this.fileList.splice(0)
    },
    // 扫描目录
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    // 单个补丁上传
    handleReUpload(row) {
      console.log('handleReUpload', row)
      console.log('this.uploadFiles', this.uploadFiles)
      const file = this.uploadFiles.filter(f => row.guid === f.name.split('.')[0])
      console.log('this.uploadFiles', file)
      this.execUpload = true;
      if (file[0].size < 1) {
        console.log(file[0].name, file[0].length)
        // 文件大小为0KB，无法上传
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.uploadPatchMsg1'),
          type: 'warning',
          duration: 2000
        })
        return
      }
      // 记录上传管理员日志
      const vo = { guidList: [row.guid] }
      this.uploadPatchLog(vo)
      md5.md5File(file[0],
        async fileMd5 => await this.afterMd5Calculated(fileMd5, file[0]),
        (loaded, total, abort) => this.onUploadProgress(file[0].name.split('.')[0], loaded, total, 0, 30, abort))
    },
    handleBatchUpload() {
      const selectGuids = this.$refs['fileList'].getSelectedKeys();
      // const selectGuids = this.fileList.map(f => f.guid);
      // const rmGuid = this.fileList.filter(f => f.downloadState >= 4).map(f => f.guid);
      const uploadFiles = this.uploadFiles.filter(f => selectGuids.includes(f.name.split('.')[0])).filter(f => f.size > 1)
      const errorFiles = this.uploadFiles.filter(f => selectGuids.includes(f.name.split('.')[0])).filter(f => f.size < 1)
      if (errorFiles.length > 0) {
        // 存在大小为0KB的文件,无法上传
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.uploadPatchMsg2'),
          type: 'warning',
          duration: 2000
        })
      }
      if (uploadFiles.length < 1) {
        // 请选择补丁文件
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.uploadPatchMsg3'),
          type: 'info',
          duration: 2000
        })
      }
      this.waitUploadGuids = uploadFiles.map(f => f.name.split('.')[0])
      if (this.waitUploadGuids.length > 0) {
        this.uploadSubmitting = true
      }
      // 记录上传管理员日志
      const vo = { guidList: selectGuids }
      this.uploadPatchLog(vo)
      this.execUpload = true;
      for (const file of uploadFiles) {
        md5.md5File(file,
          async fileMd5 => await this.afterMd5Calculated(fileMd5, file),
          (loaded, total, abort) => this.onUploadProgress(file.name.split('.')[0], loaded, total, 0, 30, abort))
      }
    },
    uploadPatchLog(vo) {
      uploadPatchLog(vo).then(res => {
      })
    },
    changeFile(files) {
      // this.fileList = []
      this.uploadFiles = files
      this.resetDate()
      console.warn('files:', files)
      let guidList = []
      guidList = files.map(f => f.name.split('.')[0])
      if (guidList.length == 0) {
        return
      }
      console.warn('files guidList', guidList)
      const vo = { guidList: guidList }
      checkFile(vo).then(res => {
        this.fileSubmitting = true
        const items = res.data.items
        items.forEach((item, index) => {
          item.id = index + 1
          if (item.downloadState >= 4) {
            item.progress = 100
          } else {
            item.progress = 0
          }
          this.fileList.push(item)
        })
        console.warn('db fileList', this.fileList)
      })
      // 列表有文件，切换方式才需要提示终止上传。
      this.$emit('uploadPatchList', this.fileList)
    },
    cancelUpload() {
      this.uploadCanceled = true
    },
    onUploadProgress(guid, loaded, total, start, weight, abort) {
      if (this.uploadCanceled) {
        abort('canceled')
        return
      }
      const updateList = [...this.fileList]
      for (let i = 0; i < updateList.length; i++) {
        const file = updateList[i]
        if (file.guid === guid) {
          file.progress = start + Math.floor(loaded / total * weight)
          console.log(file.guid, file.progress)
          if (file.progress > 98) {
            this.waitUploadGuids = this.waitUploadGuids.filter(guid => guid != file.guid)
            if (this.waitUploadGuids.length == 0) {
              this.uploadSubmitting = false
            }
          }
        }
        updateList[i] = file
      }
      this.fileList.splice(0)
      this.fileList = updateList
      // 列表有文件，切换方式才需要提示终止上传。
      this.$emit('uploadPatchList', this.fileList)
    },
    async uploadChunks(file, md5, index) {
      return await uploadChunk(file, md5, index, (guid, loaded, total, abort) => {
        this.onUploadProgress(guid, loaded, total, 30, 70, abort)
        if (loaded === total) {
          console.warn('uploadChunk', loaded === total, guid)
          return Promise.resolve(guid)
        }
        return this.uploadCanceled
      }).catch((reason) => {
        this.cancelUpload()
        console.warn('uploadChunks catch', reason)
        throw new Error(reason.message);
      })
    },
    up(file, fileMd5) {
      this.uploadChunks(file, fileMd5, 0).then((guid) => {
        console.warn('then guid', guid)
        if (guid == this.queue[0].file.name.split('.')[0]) {
          console.warn('请求完成：', this.queue[0].file.name.split('.')[0])
          this.queue.shift()
          this.uploadCanceled = false
          // console.log('this.queue', this.queue.length)
          if (this.queue.length > 0) {
            console.log('请求开始：', this.queue[0].file.name.split('.')[0])
            const file = this.queue[0].file
            const md5 = this.queue[0].md5
            this.up(file, md5)
          } else {
            this.execUpload = true
          }
        }
      }
      ).catch((reason) => {
        console.warn('catch reason', reason)
        if (reason.message == 'Network Error' || reason.message == 'canceled' || reason.message == this.queue[0].file.name.split('.')[0]) {
          console.warn('请求结束：', this.queue[0].file.name.split('.')[0])
          this.queue.shift()
          this.uploadCanceled = false
          // console.log('this.queue', this.queue.length)
          if (this.queue.length > 0) {
            console.warn('请求开始：', this.queue[0].file.name.split('.')[0])
            const file = this.queue[0].file
            const md5 = this.queue[0].md5
            this.up(file, md5)
          } else {
            this.execUpload = true
          }
        }
      })
    },
    afterMd5Calculated(fileMd5, file) {
      // console.log('file', file.name, fileMd5)
      const o = {
        md5: fileMd5,
        file: file
      }
      this.queue.push(o);
      // console.log('this.queue', this.queue.length)
      if (this.execUpload) {
        this.execUpload = false
        const file = this.queue[0].file
        const md5 = this.queue[0].md5
        this.up(file, md5)
      }
    }
  }
}
</script>
