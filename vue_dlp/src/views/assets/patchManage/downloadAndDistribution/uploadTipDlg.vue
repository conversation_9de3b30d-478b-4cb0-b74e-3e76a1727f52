<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('table.detail')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="resultTipVisible"
      width="650px"
      @dragDialog="handleDrag"
    >
      <div class="app-container" style="height: 350px;">
        <grid-table
          ref="picTip"
          :col-model="picTipColModel"
          :height="340"
          :multi-select="false"
          :show-pager="false"
          :row-datas="picTipDatas"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resultTipVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script type="text/jsx">
import { getTransferStatus } from '@/api/behaviorManage/hardware/pictureLib';

export default {
  name: 'UploadTipDlg',
  props: {
    picTipProp: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      resultTipVisible: false, // 图片上传结果弹窗
      picTipColModel: [
        { prop: 'devId', label: 'serverId', width: '100' },
        { prop: 'devName', label: 'serverName', width: '150' },
        { prop: 'statusInfo', label: 'uploadResult', width: '150' }
      ],
      statusDict: {},
      picTipDatas: [] // 图片上传结果数据
    }
  },
  created() {
  },
  methods: {
    getStatusInfo() {
      getTransferStatus().then(res => {
        this.statusDict = res.data || {}
      })
    },
    // 通用方法
    handleDrag() {
    },
    resetData() {
      // 清空原图片的上传结果
      this.picTipDatas.splice(0)
    },
    // 单张图片上传解决，需要合并每台文件服务器的结果
    show(data) {
      //  获取文件下载任务状态字典
      this.getStatusInfo()
      this.resultTipVisible = true
      const uploadReslut = {
        devId: data.devId,
        devName: data.devName,
        status: data.status,
        statusInfo: this.$t(this.statusDict[data.status]) || data.statusInfo
      }
      // 如果要更新服务器上传状态的上一次结果已经是上传成功，那就不再替换显示新的状态。
      const lastResult = this.picTipDatas.filter(pic => pic.devId === uploadReslut.devId)
      if (lastResult.status === 6) {
        return
      }
      // 过滤历史数据
      const tipArr = this.picTipDatas.filter(pic => pic.devId !== uploadReslut.devId);
      tipArr.push(uploadReslut)
      tipArr.sort((a, b) => a.devId - b.devId)
      this.picTipDatas.splice(0)
      this.picTipDatas = tipArr
      // TRDLP-8075【计算机个性化】建议图片上传成功后，图片上传结果界面可以自动关闭 	潘小娥
      const that = this
      window.setTimeout(() => {
        const success = tipArr.filter(pic => pic.status === 6);
        if (success.length !== 0 && success.length === tipArr.length) {
          that.hide();
        }
      }, 1000)
      console.log('this.picTipDatas', this.picTipDatas)
    },
    // 扫描目录批量上传，直接显示最终多个服务器上传结果
    showBatchUpload(resultContentArr) {
      //  获取文件下载任务状态字典
      this.getStatusInfo()
      this.resultTipVisible = true
      this.picTipDatas.splice(0, this.picTipDatas.length)
      resultContentArr.sort((a, b) => a.devId - b.devId)
      this.picTipDatas = resultContentArr
      console.log('this.picTipDatas', this.picTipDatas)
    },
    hide() {
      this.resultTipVisible = false
    }
  }
}
</script>
