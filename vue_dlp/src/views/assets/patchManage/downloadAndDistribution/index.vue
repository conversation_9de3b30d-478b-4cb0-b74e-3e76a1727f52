<template>
  <div class="app-container">
    <div class="table-container">
      <!--      <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">-->
      <!--        <el-tab-pane :label="$t('pages.textFile')" name="detectingFiles">-->
      <!--          <detecting-files v-if="isDetectingFiles" ref="detectingFiles" @show-result="showResult"/>-->
      <!--        </el-tab-pane>-->
      <!--        <el-tab-pane :label="$t('pages.patchLibrary')" name="patchFile">-->
      <patch-files v-if="isPatchFiles" ref="patchFiles" @show-result="showResult"/>
      <!--        </el-tab-pane>-->
      <!--      </el-tabs>-->
    </div>
    <upload-tip-dlg ref="uploadTipDlg" > </upload-tip-dlg>
  </div>
</template>
<script>
import UploadTipDlg from './uploadTipDlg'
import PatchFiles from './patchFiles/index';
export default {
  name: 'PatchLibManagement',
  components: { UploadTipDlg, PatchFiles },
  data() {
    return {
      activeName: 'patchFile',
      isDetectingFiles: false,
      isPatchFiles: true
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    isDetectingFilesPanel() {
      // 是否离线检测包panel
      return this.activeName === 'detectingFiles'
    },
    // tab菜单点击事件
    tabClick(pane, event) {
      if (this.isDetectingFilesPanel()) {
        this.isDetectingFiles = true
        this.isPatchFiles = false
      } else {
        this.isDetectingFiles = false
        this.isPatchFiles = true
      }
    },
    showResult(resultArr) {
      console.log('resultArr', resultArr == '')
      // this.$refs['uploadTipDlg'].show(JSON.parse(resultArr)[0])
      if (undefined == resultArr || null == resultArr || resultArr == '') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.noResult'),
          type: 'success',
          duration: 2000
        })
        return
      } else {
        if (this.isJSON(resultArr)) {
          this.$refs['uploadTipDlg'].showBatchUpload(JSON.parse(resultArr))
        } else {
          this.$notify({
            title: this.$t('text.success'),
            message: resultArr,
            type: 'success',
            duration: 2000
          })
        }
      }
    },
    isJSON(data) {
      try {
        JSON.parse(data)
        return true
      } catch (e) {
        return false
      }
    }
  }
}
</script>
