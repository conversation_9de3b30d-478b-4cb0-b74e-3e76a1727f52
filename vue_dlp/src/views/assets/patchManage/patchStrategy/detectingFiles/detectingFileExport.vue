<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.exportInfo', { info: title })"
      :visible.sync="dialogVisible"
      width="550px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="40px" :extra-width="{}" style="width:500px;">
        <!-- <FormItem v-if="temp.dataIds && temp.dataIds.length > 0">
          <el-radio v-model="temp.type" :label="1">{{ $t('pages.processStgLib_Msg99') }}{{ title }}</el-radio>
        </FormItem>
        <FormItem>
          <el-radio v-model="temp.type" :label="3">{{ $t('pages.processStgLib_Msg107') }}</el-radio>
        </FormItem>
        <FormItem v-if="groupTreeData.length > 0">
          <el-radio v-model="temp.type" :label="2">{{ $t('pages.processStgLib_Msg100') }}{{ title }}</el-radio>
        </FormItem>
        <FormItem v-if="groupTreeData.length > 0" label-width="65px" prop="groupId">
          <tree-select
            ref="groupTree"
            :data="groupTreeData"
            :node-key="nodeKey"
            is-filter
            :disabled="temp.type != 2"
            :checked-keys="[temp.groupId]"
            :width="300"
            style="width: 300px;"
            @change="treeSelectChange"
          />
        </FormItem> -->
        <span style="">确定导出win7以上系统补丁检测包下载地址？</span>
      </Form>

      <div slot="footer" class="dialog-footer">
        <common-downloader
          :loading="submitting"
          :name="title + '.csv'"
          :button-name="$t('button.confirm')"
          button-type="primary"
          button-icon=""
          :before-download="beforeDownload"
          @download="exportExcel"
        />
        <el-button @click="hide">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'DetectingFileExportDlg',
  components: { CommonDownloader },
  props: {
    title: {
      type: String,
      default: function() {
        // return this.$t('route.' + this.$route.meta.title)
        return '补丁检测包'
      }
    },
    groupTreeData: { type: Array, default() { return [] } },
    exportFunc: { // 上传函数
      type: Function,
      default: null
    },
    groupTreeId: {
      type: [Number, String],
      default: '0'
    },
    nodeKey: {
      type: String,
      default: 'dataId'
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      errorMsg: '',
      temp: {
        type: 2,
        groupId: undefined,
        groupName: undefined,
        dataIds: []
      },
      rules: {
        groupId: [{ trigger: 'blur', validator: this.exportGroupIdValidator }]
      }
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show(dataIds) {
      this.resetTemp()
      this.dialogVisible = true
      // Object.assign(this.temp, {
      //   type: 3,
      //   groupId: this.groupTreeId ? this.groupTreeId : '0',
      //   dataIds: dataIds
      // })
      // if (this.temp.dataIds && this.temp.dataIds.length > 0) {
      //   this.temp.type = 1
      // }
      // this.$nextTick(() => {
      //   this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      // })
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
      this.errorMsg = ''
    },
    handleDrag() {
    },
    beforeDownload() {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    exportExcel(file) {
      this.submitting = true
      const opts = { file, jwt: true, topic: this.$route.name }
      this.exportFunc(this.temp, opts).then(() => {
        this.submitting = false
      })
      this.dialogVisible = false
    },
    exportGroupIdValidator(rule, value, callback) {
      setTimeout(() => {
        const nodeData = this.$refs['groupTree'].getSelectedNode()
        if (this.temp.type === 2 && nodeData.length === 0) {
          callback(new Error(this.$t('pages.processStgLib_Msg101')))
        } else {
          // v3.51.20231218做的修改，如果有传nodeKey,则groupId不在此赋值，审批流程的导出用到了这个
          if (nodeData.length > 0 && this.nodeKey != 'id') {
            this.temp.groupId = nodeData[0].dataId
            this.temp.groupName = nodeData[0].label
          }
          callback()
        }
      }, 150)
    },
    treeSelectChange(key, nodeData) {
      const data = nodeData
      this.$emit('childSelectNodeData', data)
    }
  }
}
</script>
