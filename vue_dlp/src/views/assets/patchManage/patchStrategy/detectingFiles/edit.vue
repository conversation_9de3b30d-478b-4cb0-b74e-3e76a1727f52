<template>
  <el-dialog
    v-el-drag-dialog
    :title="titleMap[dialogStatus]"
    :close-on-click-modal="false"
    :modal="false"
    destroy-on-close
    :visible.sync="editVisible"
    width="450px"
    @dragDialog="handleDrag"
    @close="handleRemove"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 380px;">
      <FormItem :label="$t('pages.applicableOperatingSystem')" prop="systemVersion">
        <tree-select ref="picGroupTreeSelect" :disabled="true" :data="treeSelectNode" node-key="dataId" :placeholder="$t('pages.validaGroup')" :checked-keys="[temp.systemVersion]" :width="296" @change="systemVersionSelectChange" />
      </FormItem>
      <FormItem :label="$t('pages.testFileName')" prop="name">
        <el-input v-model="temp.name" :disabled="true" :placeholder="$t('text.pleaseEnterInfo', { info: $t('table.fileName1')})" maxlength="20" @input="handleInput" />
      </FormItem>
      <FormItem v-if="temp.systemVersion == 0" :label="$t('pages.downloadAddress')" prop="downloadAddr" >
        <el-input v-model="temp.downloadAddr" :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.downloadAddress')})" maxlength="300" @input="handleInput" />
      </FormItem>
      <el-row>
        <el-col :span="1" style="padding: 5px 0 0 0px"> <el-checkbox v-if="temp.systemVersion == 0" v-model="temp.enableCycle" @change="temp.cycle = 0" > </el-checkbox></el-col>
        <el-col :span="23">
          <FormItem v-if="temp.systemVersion == 0" :label="$t('pages.updateCycle')" tooltip-placement="bottom-start" label-width="104px">
            <i18n path="pages.updateCycle1">
              <el-input-number slot="num" v-model="temp.cycle" :controls="false" style="width: 100px" :disabled="!temp.enableCycle" :min="1" :max="31" :precision="0" ></el-input-number>
            </i18n>
            <!--          <el-tooltip effect="dark" placement="bottom-end">-->
            <!--            <div slot="content">{{ $t('pages.updateCycleTip') }}</div>-->
            <!--            <i class="el-icon-info" />-->
            <!--          </el-tooltip>-->
          </FormItem>
        </el-col>
      </el-row>
      <FormItem label-width="0">
        <el-upload
          ref="upload"
          accept=".cab"
          :disabled="disabledUpload"
          class="upload-demo"
          name="uploadFile"
          action="aaaaaa"
          :limit="1"
          :show-file-list="true"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :file-list="fileList"
        >
          <el-button size="small" :disabled="disabledUpload" type="primary" @click="handleSelectFile">
            {{ $t('pages.uploadDetectionFile') }}
            <el-tooltip effect="dark" placement="bottom-end">
              <div slot="content">{{ $t('pages.detectionFileFormat') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-button>
          <div slot="tip" class="el-upload__tip">
            <div v-show="loading" class="file-upload-progress">
              <el-progress type="line" :stroke-width="5" :percentage="uploadPercent"/>
              <i class="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancelUpload(null)"/>
            </div>
          </div>
        </el-upload>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="hide">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { uploadFile, updatePatchDetection, cancelUploadFile } from '@/api/assets/patchManage/detectingFiles';

export default {
  name: 'EditDetectingFiles',
  components: { },
  props: {},
  data() {
    return {
      dialogStatus: 'update',
      editVisible: false,
      uploadHandle: undefined, // 记录上传的websocket
      uploadPercent: 0,
      uploadCanceled: false,
      currentTaskId: undefined,
      lastTaskId: undefined,
      loading: false,
      disabledUpload: false,
      fileList: [], // 表单 检测包列表
      treeSelectNode: [{ 'id': 'G0', 'dataId': '0', 'label': this.$t('pages.windows7AndAboveSystems'), 'parentId': 'G-1', 'type': 'G', 'disabled': false },
        { 'id': 'G1', 'dataId': '1', 'label': this.$t('pages.windows7AndBelowSystems'), 'parentId': 'G-1', 'type': 'G', 'disabled': false }],
      titleMap: {
        update: this.$t('text.edit')
      },
      rules: {},
      temp: {}, // 检测文件
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        fileMd5: '',
        suffix: '',
        ftpGuid: undefined,
        downloadAddr: '',
        downloadStatus: '',
        downloadDetails: '',
        systemVersion: undefined,
        fileSize: undefined,
        cycle: 1,
        enableCycle: 1
      }
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  mounted() {
  },
  methods: {
    // 通用方法
    handleDrag() {
    },
    handleRemove(file, fileList) {
      // this.fileList.splice(0)
      // this.temp = Object.assign({}, this.temp, {
      //   guid: '',
      //   name: '',
      //   md5: '',
      //   ext: ''
      // })
      // this.$refs.upload.clearFiles()
    },
    resetTemp() {
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
    },
    show(row) {
      this.editVisible = true;
      this.temp = Object.assign({}, this.defaultTemp, row)
      console.log('show', row, this.temp)
      this.fileList.splice(0)
      if (row.fileMd5 !== '') {
        const img = {
          name: this.temp.name,
          url: process.env.VUE_APP_BASE_API + '/TRDLP/file/patchDetectionFile/' + this.temp.md5 + '.' + this.temp.suffix.toLowerCase()
        }
        this.fileList.push(img)
      }
    },
    systemVersionSelectChange: function(data) {
      this.temp.systemVersion = data
    },
    handleInput(value) {
    },
    handleSelectFile() {
      // 任务ID，只要确保唯一即可
      this.currentTaskId = Date.now().toString(16) + '-' + Math.random().toString(16).slice(2)
      this.lastTaskId = undefined
      this.uploadCanceled = false
    },
    cancelUpload(taskId) {
      this.uploadCanceled = true
      // 清理进度条数字
      this.uploadPercent = 0
      // 关闭定时器
      clearInterval(window.importPatchDetectingFiletimer)
      window.importPatchDetectingFiletimer = undefined
      // 取消确认按钮加载中状态
      this.loading = false
      // 取消禁用
      this.disabledUpload = false
      this.submitting = false
      if (!taskId) {
        taskId = this.currentTaskId
      }
      if (taskId) {
        cancelUploadFile(taskId).then(resp => {
          this.lastTaskId = taskId
          this.currentTaskId = undefined
          this.submitting = false
        })
      }
    },
    beforeUpload(file) {
      this.uploadPercent = 0
      const testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      if (testmsg !== '') {
        const extension = testmsg.toLowerCase() === 'cab'
        if (!extension) {
          this.$message({
            message: this.$t('pages.detectionFileFormat'),
            type: 'error'
          });
          return
        }
      } else {
        this.$message({
          message: this.$t('pages.detectionFileFormat'),
          type: 'error'
        });
        return
      }
      this.loading = true
      this.disabledUpload = true
      window.importPatchDetectingFiletimer = setInterval(() => {
        if (this.uploadCanceled || this.uploadPercent >= 99) {
          clearInterval(window.importPatchDetectingFiletimer)
          window.importPatchDetectingFiletimer = undefined
          this.uploadPercent = 0
          return
        }
        const u = Number(this.uploadPercent)
        console.log(u, u < 28, (30 <= u && u < 58), (60 <= u && u < 98))
        if (u < 97) {
          this.uploadPercent = this.uploadPercent + 1
        }
      }, 1000)
      if (this.uploadHandle !== undefined) {
        // 原上传图片的订阅socket未关闭时，将它关闭
        this.uploadHandle.close()
        this.uploadHandle = undefined
      }

      this.submitting = true
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      fd.append('systemVersion', this.temp.systemVersion)
      fd.append('taskId', this.currentTaskId)
      // fd.append('controlCode', this.productMap[this.temp.competitorId].controlCode)
      // const loading = this.$loading({
      //   lock: true,
      //   text: this.$t('pages.fileFp_Msg19'),
      //   spinner: 'el-icon-loading',
      //   background: 'rgba(0, 0, 0, 0.7)'
      // });

      // 订阅文件上传情况
      const that = this
      this.$socket.subscribeToUser('uploadResult', 1, (respond, handle) => {
        // 缓存订阅关闭
        that.uploadHandle = handle;
        // 此订阅当作所有消息的通知接口
        console.warn('uploadPercent', respond.data)
        if (that.uploadPercent < respond.data) {
          that.uploadPercent = respond.data
        }
        if (!this.currentTaskId) {
          // 如果点击取消上传的时候没有成功，则获取到状态的时候再取消一次
          this.cancelUpload(this.lastTaskId)
        }
      }, false)
      uploadFile(fd).then(res => {
        clearInterval(window.importPatchDetectingFiletimer)
        this.uploadPercent = 100
        this.loading = false
        this.submitting = false
        this.disabledUpload = false
        // resultCode: 4:未有上传成功的文件服务器 5 至少一台文件服务器传输成功
        // 取消上传-直接不显示结果
        if (!this.uploadCanceled) {
          this.temp.downloadState = res.data.downloadState
          this.temp.name = res.data.name
          this.fileList.splice(0)
          const img = {
            name: this.temp.name,
            url: process.env.VUE_APP_BASE_API + '/TRDLP/file/patchDetectionFile/' + this.temp.md5 + '.' + this.temp.suffix.toLowerCase()
          }
          this.fileList.push(img)
        }
        // 显示结束，清空原有进度
        this.uploadPercent = 0
      }).catch(res => {
        // loading.close()
        this.submitting = false
        // 启用上传按钮
        this.disabledUpload = false
        this.loading = false
        this.uploadPercent = 0
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.$t('text.requestTimeout'),
          type: 'fail',
          duration: 2000
        })
      })
      return false // 屏蔽了action的默认上传
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.systemVersion == 1 && this.fileList.length == 0) {
            // win7的包：修改功能验证包必须上传
            this.$message({
              message: this.$t('pages.pleaseUploadFile')
            })
            return
          }
          this.submitting = true
          const that = this
          const cycle = this.temp.enableCycle == 0 ? 0 : this.temp.cycle
          const tempData = Object.assign({}, this.temp, { cycle: cycle })
          updatePatchDetection(tempData).then(respond => {
            that.submitting = false
            that.editVisible = false
            that.$emit('submitEnd')
            that.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    hide() {
      this.cancelUpload(this.currentTaskId)
      this.submitting = false
      this.editVisible = false
    }
  }
}
</script>
<style  lang="scss" scoped>
.custom-input-number >>>.el-input-number__input {
  width: 10px; /* 设置输入框的宽度 */
}

.file-upload-progress {
  height: 14px;
  line-height: 14px;
  .el-progress {
    width: calc(100% - 20px);
    display: inline-block;
    >>>.el-progress-bar {
      padding-right: 40px;
      margin-right: -47px;
    }
    >>>.el-progress__text {
      vertical-align: unset;
    }
  }
  i.el-icon-switch-button {
    cursor: pointer;
    /*color: #68a8d0;*/
    color: #f56c6c;
    font-weight: 700;
    &:hover {
      /*color: #4995c5;*/
      color: #f78989;
    }
  }
}
</style>
