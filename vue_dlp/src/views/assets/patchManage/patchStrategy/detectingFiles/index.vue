<template>
  <div v-loading="showLoading" class="app-container flex">
    <!-- 补丁检测配置 -->
    <el-divider content-position="left" >{{ $t('pages.patchDetectionConfiguration') }}</el-divider>
    <Form
      ref="dataForm"
      class="server-form"
      :model="temp"
      :hide-required-asterisk="true"
      :rules="rules"
    >
      <el-row style="padding: 5px 30px">
        <el-col :span="24">
          <el-checkbox v-model="temp.patchDetectAble" style="margin-right: 30px;" :true-label="1" :false-label="0" @change="handlePatchDetectChange">{{ $t('pages.enablePatchDetectionFunction') }}</el-checkbox>
        </el-col>
      </el-row>
      <el-row >
        <FormItem label-width="130px" :label="$t('pages.patchDetectionCycle')" :extra-width="{ en: 65 }" prop="timeType" style="width: 610px; /*border: thin solid red*/ ">
          <el-select v-model="timeType" style="width: 180px" :disabled="timeType === 0" class="targetType" @change="timeChange">
            <el-option v-for="(item, index) in timeTypeArray" :key="index" :value="item.value" :label="item.label">{{ item.label }}</el-option>
          </el-select>
          <el-input-number v-if="timeType === -2" v-model="selfTime" :placeholder="$t('pages.inputIntervalDay')" :controls="false" :min="1" style="width: 180px" :max="365" @blur="selfTimeChange"></el-input-number>
          <span style="height: 32px; align-items: center; margin-right: 5px">
            <el-tooltip>
              <template slot="content">
                <i18n path="pages.patchDetectionCycleTip">
                  <br slot="br"/>
                </i18n>
              </template>
              <i class="el-icon-info"/>
            </el-tooltip>
          </span>
          <el-button :loading="submitting" style="margin:5px 5px;" type="primary" size="mini" @click="createData()">
            {{ $t('button.save') }}
          </el-button>
        </FormItem>
      </el-row>
      <el-row>

      </el-row>
    </Form>
    <!--        <el-row >-->
    <!--          <div style="margin-left: 500px">-->
    <!--            <el-button :loading="submitting" type="primary" @click="createData()">-->
    <!--              {{ $t('button.confirm') }}-->
    <!--            </el-button>-->
    <!--          </div>-->
    <!--        </el-row>-->
    <!--        <FormItem :label="$t('pages.patchAllowDownloadTime')" label-width="130px" prop="timeId">-->
    <!--          <el-select v-model="temp.timeId" :placeholder="$t('text.select')" >-->
    <!--            <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
    <!--          </el-select>-->
    <!--          <link-button :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'" btn-style="margin: 1px 0 0;"/>-->
    <!--        </FormItem>-->

    <!-- 补丁检测包配置 -->
    <el-divider content-position="left">{{ $t('pages.patchDetectionPackageConfiguration') }}</el-divider>
    <div class="toolbar">
      <div><span style="line-height: 30px; color: rgb(43, 122, 172); font-size:14px;">{{ $t('pages.patchDetectionPackagePrompt') }}</span></div>
      <!-- <el-button v-show="true" icon="el-icon-download" size="mini" @click="handleExport">
        {{ $t('button.export') }}
      </el-button> -->
      <download-tool :show-type="4" />
      <div class="searchCon">
        <el-input v-model="query.searchInfo" clearable :placeholder="$t('pages.validateMsg_enterName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="detectingFileTable"
      :col-model="detectingFileColModel"
      :row-data-api="rowDataApi"
      :multi-select="false"
      :show-pager="false"
    />
    <edit-detecting-files ref="editDetectingFiles" @submitEnd="submitEnd" />
    <detecting-file-export-dlg ref="detectingFileExportDlg" :title="$t('pages.patchDetectionPackage')" :export-func="exportFunc"/>
  </div>
</template>
<script type="text/jsx">
import { getDetectingFilesPage, clickToDownload, exportExcel } from '@/api/assets/patchManage/detectingFiles';
import EditDetectingFiles from './edit.vue';
import DetectingFileExportDlg from './detectingFileExport.vue';
import { getSetting, saveSetting } from '@/api/assets/patchManage/paramSetting';
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool.vue';
export default {
  name: 'DetectingFiles',
  components: { DownloadTool, EditDetectingFiles, DetectingFileExportDlg },
  props: {},
  data() {
    return {
      showLoading: false,
      submitting: false,
      temp: {
        patchDetectAble: 0,
        checkDay: 0
      },
      timeType: 7,
      // 1:每天 3：每三天 7：每七天 30：每30天 0：关闭 -1：执行一次 -2：自定义
      timeTypeArray: [
        { value: 0, label: this.$t('pages.close') }, //  关闭
        { value: 1, label: this.$t('pages.everyDay') }, //  每天
        { value: 3, label: this.$t('pages.selfSetDay', { day: 3 }) },  //  每3天
        { value: 7, label: this.$t('pages.selfSetDay', { day: 7 }) }, //  每7天
        { value: 30, label: this.$t('pages.selfSetDay', { day: 30 }) }, //  每30天
        { value: -2, label: this.$t('pages.selfDay') }, //  自定义
        { value: -1, label: this.$t('pages.executeOnce') } //  执行一次
      ],
      selfTime: 15,
      rules: {
      },
      detectingFileColModel: [
        { prop: 'name', label: this.$t('pages.testFileName'), width: '150', fixed: true, sort: true },
        { prop: 'systemVersion', label: this.$t('pages.applicableOperatingSystem'), width: '150', sort: true, formatter: this.systemFormatter },
        { prop: 'fileSize', label: 'size', width: '100', sort: true, formatter: this.sizeFormatter },
        { prop: 'downloadStatus', label: this.$t('pages.recentUpdateStatus'), width: '150', sort: true, sortOriginal: true, formatter: this.downloadStateFormat },
        { prop: 'updateTime', label: this.$t('pages.checkTheLastUpdateTimeOfTheFile'), width: '190', sort: true, sortOriginal: true },
        { prop: 'lastSuccessTime', label: this.$t('pages.successfulUpdateTime'), width: '190', sort: true, sortOriginal: true },
        { prop: '', label: 'remark', width: '300', formatter: this.remarksFormat },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'download', formatter: this.buttonFormatter, isShow: this.isShowButtonFormat, click: this.downloadDetectFile },
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate },
            { label: 'export', formatter: this.buttonFormatter, isShow: this.isShowExportButtonFormat, click: this.handleExport }
            // { label: 'details', formatter: this.buttonFormatter, click: this.handleDetail }
          ]
        }
      ],
      systemMap: {
        0: this.$t('pages.windows7AndAboveSystems'),
        1: this.$t('pages.windows7AndBelowSystems')
      },
      // 下载状态0：未下载 1：待下载 2：开始下载 3：下载失败 4：下载中（已下载到web服务器） 5：正在分发ftp 6：分发ftp失败 7：FTP分发完成
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      query: {
        page: 1,
        searchInfo: undefined
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['detectingFileTable']
    }
  },
  created() {
    this.getStrategy()
  },
  activated() {
  },
  mounted() {
  },
  methods: {
    //  检测周期
    handlePatchDetectChange(val) {
      console.log('handlePatchDetectChange', val)
      if (0 == val) {
        this.timeType = 0
        this.selfTime = 0
        // 关闭检测功能
        this.temp.checkDay = 0
      } else {
        this.timeType = 1
        this.temp.checkDay = 1
      }
    },
    timeChange(time) {
      if (time !== -2) {
        this.temp.checkDay = time
      }
      if (time === 0) {
        this.temp.patchDetectAble = 0
      }
    },
    selfTimeChange(event) {
      const val = event.target.value.trim()
      if (val === undefined || val === null || val === '') {
        this.selfTime = 1;
      }
    },
    getStrategy() {
      getSetting().then(res => {
        console.log('getStrategy', JSON.stringify(res))
        this.temp = Object.assign(this.temp, res.data)
        this.formatCheckDay(this.temp.checkDay)
        // this.formatAutoPatchLevelList(res.data.autoPatchLevel)
      })
    },
    // 格式化检测周期
    formatCheckDay(checkDay) {
      console.log('formatCheckDay', checkDay)
      if ([1, 3, 7, 30, -2, -1, 0].includes(checkDay)) {
        this.timeType = checkDay
      } else {
        this.timeType = -2
        this.selfTime = checkDay
      }
      if (this.timeType !== 0) {
        this.temp.patchDetectAble = 1
      }
    },
    // 补丁检测包配置
    systemFormatter: function(row, data) {
      return this.systemMap[data]
    },
    sizeFormatter: function(row, data) {
      if (row.downloadSize && [1, 2].includes(row.downloadStatus)) {
        return this.convertSize(data) + '(' + this.convertSize(row.downloadSize) + ')'
      } else {
        return this.convertSize(data)
      }
    },
    convertSize: function(size) {
      if (!size) {
        return '0 Bytes';
      }
      var sizeNames = [' Bytes', ' KB', ' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
      var i = Math.floor(Math.log(size) / Math.log(1024));
      var p = (i > 0) ? 2 : 1;
      if (p == 1) {
        return 1 + sizeNames[1];
      } else {
        return (size / Math.pow(1024, Math.floor(i))).toFixed(p) + sizeNames[i];
      }
    },
    downloadStateFormat: function(row, data) {
      if (data == '') {
        return ''
      }
      if (row.progress && [1, 2].includes(row.downloadStatus)) {
        return this.downloadStateMap[data] + '(' + row.progress + '%)'
      } else if (row.downloadDetails && [3].includes(row.downloadStatus)) {
        return this.downloadStateMap[data] + '(' + row.downloadDetails + ')'
      } else {
        return this.downloadStateMap[data]
      }
    },
    remarksFormat: function(row, data) {
      if (row.systemVersion == 0) {
        // win7以上 'win7以上的离线检测包自动下载'
        return this.$t('pages.detectingFilesRemark1')
      }
      if (row.systemVersion == 1) {
        // win7及以下 'win7及以下的离线检测包需管理员通过修改功能手动上传'
        return this.$t('pages.detectingFilesRemark2')
      }
    },
    isShowButtonFormat: function(row) {
      return row.systemVersion == 0 && this.hasPermission('512')
    },
    isShowExportButtonFormat: function(row) {
      return row.systemVersion == 0 && this.hasPermission('533')
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getDetectingFilesPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    downloadDetectFile(row) {
      let msg = '';
      if ([3, 4, 5, 6, 7].includes(row.downloadStatus)) {
        msg = this.$t('pages.sureDownloadAgain')
      } else {
        msg = this.$t('pages.sureDownload')
      }
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        const that = this;
        clickToDownload(row).then(res => {
          that.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.downloadingWait'),
            type: 'success',
            duration: 2000
          })
          that.handleFilter()
        })
      }).catch(() => {})
    },
    handleUpdate(row) {
      this.$refs['editDetectingFiles'].show(row)
    },
    handleDetail(row) {
      this.$emit('show-result', row.downloadDetails)
    },
    submitEnd() {
      // this.notifySuccess(this.$t('text.updateSuccess'))
      this.gridTable.execRowDataApi(this.query)
    },
    createData() {
      this.submitting = true
      console.log('this.temp', JSON.stringify(this.temp))
      console.log('this.timeType', this.timeType)
      console.log('this.timeType 11', this.timeType === -2)
      if (this.timeType === -2) {
        this.temp.checkDay = this.selfTime
      } else {
        this.temp.checkDay = this.timeType
      }
      const that = this
      const tempData = Object.assign({}, this.temp, { name: '检测配置' })
      saveSetting(tempData).then(respond => {
        // this.dialogFormVisible = false
        that.submitting = false
        // that.editVisible = false
        // that.$emit('submitEnd')
        that.getStrategy()
        that.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    // 检测文件导出
    handleExport() {
      this.$refs.detectingFileExportDlg.show()
    },
    exportFunc(formData, opts) {
      console.log('exportFunc', formData)
      console.log('exportFunc', opts)
      const { sortName, sortOrder } = this.query
      return exportExcel({
        parentGuidList: formData.type === 1 ? formData.dataIds : null,
        sortName,
        sortOrder
      }, opts)
    }
  }
}
</script>
<style lang="scss" scoped>
  .save-btn-container{
    width: 700px;
    margin-top: 5px;
    text-align: right;
  }
  .flex {
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .el-divider--horizontal {
    flex-shrink: 0;
  }
</style>
