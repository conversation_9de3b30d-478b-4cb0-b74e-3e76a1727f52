<template>
  <div class="app-container">
    <div class="table-container">
      <el-tabs v-if="listable" ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.patchDetectionPackage')" name="detectingFiles" >
          <template slot="label">
            {{ $t('pages.detectingConfiguration') }}
            <!--            {{ $t('pages.patchDetectionPackage') }}-->
            <!--            <el-tooltip slot="content" class="item" effect="dark" placement="right" :content="$t('pages.patchDetectionPackagePrompt')">-->
            <!--              <div></div>-->
            <!--              <i class="el-icon-info"/>-->
            <!--            </el-tooltip>-->
          </template>
          <detecting-files v-if="isDetectingFiles" ref="detectingFiles" />
        </el-tab-pane>
        <!--        <el-tab-pane :label="$t('pages.detectionStrategy')" name="paramSetting">-->
        <!--          <param-setting v-if="isParamSetting" ref="paramSetting"/>-->
        <!--        </el-tab-pane>-->
        <el-tab-pane :label="$t('route.installStrategy')" name="patchInstallationStrategy">
          <template slot="label">
            {{ $t('route.installStrategy') }}
            <el-tooltip slot="content" class="item" effect="dark" placement="right" :content="$t('pages.installStrategyPrompt')">
              <div></div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </template>
          <patch-installation-strategy v-if="isPatchInstallationStrategy" ref="patchInstallationStrategy"/>
        </el-tab-pane>
        <el-tab-pane :label="$t('route.autoInstallStrategy')" name="patchAutoInstallationStrategy">
          <patch-auto-installation-strategy v-if="isPatchAutoInstallationStrategy" ref="patchAutoInstallationStrategy"/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import DetectingFiles from './detectingFiles/index';
import PatchInstallationStrategy from './patchInstallationStrategy/index';
import PatchAutoInstallationStrategy from './patchAutoInstallationStrategy/index';

export default {
  name: 'PatchStrategy',
  components: { DetectingFiles, PatchInstallationStrategy, PatchAutoInstallationStrategy },
  props: {
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      activeName: 'detectingFiles',
      isDetectingFiles: true,
      isParamSetting: false,
      isPatchInstallationStrategy: false,
      isPatchAutoInstallationStrategy: false,
      isTermModel: false,
      isPatchModel: false
    }
  },
  beforeRouteEnter(to, from, next) {
    // 策略总览切换到当前策略时使用
    next(vm => {
      vm.activeName = to.query.tabName || vm.activeName
      vm.isParamSetting = vm.activeName === 'paramSetting'
      vm.isPatchInstallationStrategy = vm.activeName === 'patchInstallationStrategy'
      vm.isPatchAutoInstallationStrategy = vm.activeName === 'patchAutoInstallationStrategy'
      vm.$router.push({ query: {}})
    })
  },
  created() {
  },
  methods: {
    // tab菜单点击事件
    tabClick(pane, event) {
      this.isDetectingFiles = this.activeName === 'detectingFiles'
      this.isParamSetting = this.activeName === 'paramSetting'
      this.isPatchInstallationStrategy = this.activeName === 'patchInstallationStrategy'
      this.isPatchAutoInstallationStrategy = this.activeName === 'patchAutoInstallationStrategy'
    }
  }
}
</script>
