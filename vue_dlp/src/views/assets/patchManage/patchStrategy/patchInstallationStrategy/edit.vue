<template>
  <div>
    <!--    v-if="dialogFormVisible"-->
    <el-dialog
      v-if="dialogFormVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="120px"
        :hide-required-asterisk="true"
        style="width: 750px;"
      >
        <FormItem :label="$t('pages.effectiveObject')" prop="objectIds">
          <tree-select
            ref="objectTree"
            :placeholder="$t('pages.placeChooseApplicationObj')"
            node-key="id"
            :height="350"
            :width="468"
            multiple
            check-strictly
            collapse-tags
            is-filter
            :filter-key="{ prop: 'dataType', value: '3', showIfNoProp: true }"
            :checked-keys="temp.checkedKeys"
            :disabled="!formable"
            :local-search="false"
            leaf-key="terminal"
            @change="checkedIdChange"
          />
        </FormItem>
        <!--        <FormItem :label="$t('text.effectTime')" prop="timeId">-->
        <!--          <el-select v-model="temp.timeId" :disabled="!formable" :placeholder="$t('text.select')" style="width: calc(100% - 30px);">-->
        <!--            <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
        <!--          </el-select>-->
        <!--          <link-button btn-class="editBtn" :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'" btn-style="margin: 1px 0 0;"/>-->
        <!--        </FormItem>-->
        <FormItem :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem label-width="0">
          <div>
            <el-button v-show="formable" size="small" style="margin-bottom: 0" @click="addPatch">
              {{ $t('button.insert') }}
            </el-button>
            <el-button v-show="formable" size="small" style="margin-bottom: 0" @click="deletePatch">
              {{ $t('button.delete') }}
            </el-button>
            <div v-show="formable" style="float: right;">
              <el-input v-model="searchPatchName" v-trim clearable :placeholder="$t('pages.patchID')" style="width: 150px;height: 27px;margin-bottom: 8px" @keyup.enter.native="handleFilter(searchPatchName)" />
              <el-button type="primary" icon="el-icon-search" size="small" :title="$t('table.search')" @click="handleFilter(searchPatchName)"/>
            </div>
            <grid-table
              ref="patchGridTable"
              :height="350"
              row-key="guid"
              :row-datas="temp.patchList"
              :col-model="patchColModel"
              :show-pager="false"
              @selectionChangeEnd="patchSelectionChangeEnd"
            />
          </div>

        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <patch-table ref="patchTable" @add-data="addData"></patch-table>
  </div>
</template>
<script>
import {
  getStrategyByName, getStrategyById, createStrategy, updateStrategy
} from '@/api/assets/patchManage/autoPatch'
import moment from 'moment';
import { isEmpty, validatePolicy } from '@/utils/validate';
import PatchTable from './patchTable';

export default {
  name: 'EditPatchInstallation',
  components: { PatchTable },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      patchColModel: [
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100', fixed: true, sort: true, formatter: this.patchKbFormatter },
        { prop: 'patchTitle', label: this.$t('pages.patchTitle'), width: '120', sort: true },
        { prop: 'patchLevel', label: this.$t('pages.patchLevel'), width: '100', sort: true, sortOriginal: true, formatter: this.patchLevelFormat },
        { prop: 'downloadState', label: 'status', width: '100', sort: true, formatter: this.downloadStateFormat },
        { prop: 'patchDescribe', label: this.$t('pages.patchDescription'), width: '120', sort: true },
        { prop: 'patchSize', label: 'size', width: '100', sort: true, sortOriginal: true, formatter: this.sizeFormatter }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      // textMap: {
      //   update: this.formable ? this.i18nConcatText(this.$t('pages.automaticPatchStg'), 'update') : this.i18nConcatText(this.$t('pages.automaticPatchStg'), 'details'),
      //   create: this.i18nConcatText(this.$t('pages.automaticPatchStg'), 'create')
      // },
      textMap: {
        update: this.formable ? this.i18nConcatText(this.$t('route.installStrategy'), 'update') : this.i18nConcatText(this.$t('route.installStrategy'), 'details'),
        create: this.i18nConcatText(this.$t('route.installStrategy'), 'create')
      },
      rules: {
        // objectIds: [
        //   { required: true, message: this.$t('components.required'), trigger: 'blur' },
        //   { validator: this.objectIdsValidator, trigger: 'blur' }
        // ],
        name: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        limitSize: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ]
      },
      submitting: false,
      // 下载状态0：未收集完整 1：数据已收集完成 2：正在下载 3：下载失败 4：已下载到-Server 5：正在分发ftp 6：分发ftp失败 7：FTP分发完成
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      patchLevelMap: {
        0: this.$t('pages.undefinedLevel'),
        1: this.$t('pages.lowLevel'),
        2: this.$t('pages.mediumPatch'),
        3: this.$t('pages.importanceLevel'),
        4: this.$t('pages.severityLevel')
      },
      temp: { // 表单字段
        id: undefined,
        name: '',
        active: true,
        timeId: undefined,
        remark: '',
        entityType: '',
        entityId: undefined,
        patchList: [],
        patchIdList: [],
        checkedKeys: []
      },
      patchTableSelectionData: [],
      selectedTerminalData: [],
      searchPatchName: '',
      allRowDatas: []
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    patchGridTable() {
      return this.$refs['patchGridTable']
    },
    patchTable() {
      return this.$refs['patchTable']
    }
  },
  watch: {
    allRowDatas(val) {
      this.handleFilter()
    }
  },
  created() {
  },
  methods: {
    getRowKey(row) {
      console.log('getRowKey', row)
      return row.patchKb + '_' + moment(row.create_time).unix()
    },
    handleDrag() {
    },
    patchKbFormatter: function(row, data) {
      return data
    },
    sizeFormatter: function(row, data) {
      return this.convertSize(data)
    },
    patchLevelFormat: function(row, data) {
      return this.patchLevelMap[data]
    },
    convertSize: function(size) {
      if (!size) {
        return '0 Bytes';
      }
      var sizeNames = [' Bytes', ' KB', ' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
      var i = Math.floor(Math.log(size) / Math.log(1024));
      var p = (i > 0) ? 2 : 1;
      if (p == 1) {
        return 1 + sizeNames[1];
      } else {
        return (size / Math.pow(1024, Math.floor(i))).toFixed(p) + sizeNames[i];
      }
    },
    downloadStateFormat: function(row, data) {
      return this.downloadStateMap[data]
    },
    objectIdsValidator(rule, value, callback) {
      console.log('objectIdsValidator', value)
      if (value.length == 0) {
        callback(new Error(this.$t('pages.placeChooseApplicationObj')))
      } else {
        callback()
      }
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    resetTemp() {
      this.temp = { // 表单字段
        id: undefined,
        name: '',
        active: true,
        timeId: undefined,
        remark: '',
        entityType: '',
        entityId: undefined,
        patchList: [],
        patchIdList: [],
        checkedKeys: []
      }
      this.searchPatchName = ''
      this.selectedTerminalData = []
      // this.$nextTick(() => {
      //   this.$refs['objectTree'].clearFilter()
      // })
    },
    initFormObject(row) {
      if (!row) {
        return
      }
      const { objectIds, objectGroupIds } = row
      console.log('objectIds', objectIds)
      console.log('objectGroupIds', objectGroupIds)
      console.log('this.temp.checkedKeys', this.temp.checkedKeys)
      this.temp.checkedKeys.splice(0)
      if (objectIds) {
        objectIds.forEach(objId => {
          this.temp.checkedKeys.splice(0, 0, ('T') + objId)
        })
      }
      if (objectGroupIds) {
        objectGroupIds.forEach(objId => {
          this.temp.checkedKeys.splice(0, 0, 'G' + objId)
        })
      }
    },
    handleCreate(data) {
      this.submitting = false
      console.log('handleCreate', 'create')
      this.resetTemp()
      this.allRowDatas = []
      this.initFormObject(data)
      // this.temp.entityType = this.query.objectType
      // this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      // this.$nextTick(() => {
      //   this.$refs['dataForm'].clearValidate()
      // })
    },
    handleUpdate(row) {
      console.log('handleUpdate patch install', this.formable)
      this.submitting = false
      this.resetTemp()
      console.log('handleUpdate', row)
      this.temp = Object.assign(this.temp, row) // copy obj
      this.allRowDatas = this.temp.patchList
      this.initFormObject(row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        // this.$refs['dataForm'].clearValidate()
        if (isEmpty(this.temp.patchList)) {
          getStrategyById(this.temp.id).then(resp => {
            this.temp = Object.assign(this.temp, resp.data)
            this.initFormObject(resp.data)
          })
        }
      })
    },
    handleTermModel(data) {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.temp = Object.assign(this.temp, data) // copy obj
      this.allRowDatas = this.temp.patchList
      const { patchList } = data
      this.initFormObject(data)
      console.log('data', data)
      const repeatData = []
      for (let i = 0, len = this.allRowDatas.length; i < len; i++) {
        for (let j = 0, length = patchList.length; j < length; j++) {
          if (this.allRowDatas[i].guid === patchList[j].guid) {
            repeatData.push(patchList[j].guid)
          }
        }
      }
      const resArr = patchList.filter(item => !repeatData.includes(item.guid))
      const arr = resArr.map((item) => {
        return {
          guid: item['guid'],
          patchKb: item['patchKb'],
          patchTitle: item['patchTitle'],
          patchDescribe: item['patchDescribe'],
          patchSize: item['patchSize'],
          patchLevel: item['patchLevel'],
          downloadState: item['downloadState']
        }
      })
      // 存在的数据更新为新的数据
      for (let i = 0, len = this.allRowDatas.length; i < len; i++) {
        for (let j = 0, length = patchList.length; j < length; j++) {
          if (this.allRowDatas[i].guid === patchList[j].guid) {
            this.allRowDatas[i].patchKb = patchList[j].patchKb
            this.allRowDatas[i].patchTitle = patchList[j].patchTitle
            this.allRowDatas[i].patchDescribe = patchList[j].patchDescribe
            this.allRowDatas[i].patchSize = patchList[j].patchSize
            this.allRowDatas[i].patchLevel = patchList[j].patchLevel
          }
        }
      }
      const union = [...this.allRowDatas, ...arr]
      this.allRowDatas = union
    },
    checkedIdChange(keys, options) {
      this.selectedTerminalData.splice(0, this.selectedTerminalData.length, ...options)
      console.log('this.selectedTerminalData', this.selectedTerminalData)
    },
    addPatch() {
      this.patchTable.show();
    },
    deletePatch() {
      if (this.patchTableSelectionData.length > 0) {
        this.patchTableSelectionData.forEach(pic => {
          const index = this.allRowDatas.findIndex(c => c.guid === pic.guid)
          this.allRowDatas.splice(index, 1)
        })
        this.patchTableSelectionData.splice(0) // 删除customTable列表勾选
      }
    },
    patchSelectionChangeEnd(val) {
      this.patchTableSelectionData = val
      console.log('this.patchTableSelectionData', val)
    },
    addData(data) {
      console.log('data', data)
      const repeatData = []
      for (let i = 0, len = this.allRowDatas.length; i < len; i++) {
        for (let j = 0, length = data.length; j < length; j++) {
          if (this.allRowDatas[i].guid === data[j].guid) {
            repeatData.push(data[j].guid)
          }
        }
      }
      const resArr = data.filter(item => !repeatData.includes(item.guid))
      const arr = resArr.map((item) => {
        return {
          id: item['id'],
          guid: item['guid'],
          patchKb: item['patchKb'],
          patchTitle: item['patchTitle'],
          patchDescribe: item['patchDescribe'],
          patchSize: item['patchSize'],
          patchLevel: item['patchLevel'],
          downloadState: item['downloadState']
        }
      })
      // 存在的数据更新为新的数据
      for (let i = 0, len = this.allRowDatas.length; i < len; i++) {
        for (let j = 0, length = data.length; j < length; j++) {
          if (this.allRowDatas[i].guid === data[j].guid) {
            this.allRowDatas[i].id = data[j].id
            this.allRowDatas[i].patchKb = data[j].patchKb
            this.allRowDatas[i].patchTitle = data[j].patchTitle
            this.allRowDatas[i].patchDescribe = data[j].patchDescribe
            this.allRowDatas[i].patchSize = data[j].patchSize
            this.allRowDatas[i].patchLevel = data[j].patchLevel
          }
        }
      }
      const union = [...this.allRowDatas, ...arr]
      this.allRowDatas = union
      this.patchTable.hide()
    },
    // 功能API
    formatSubmitData() {
      const temp = this.temp
      console.log('formatSubmitData', temp)
      // js map updateId
      temp.patchIdList = this.allRowDatas.map(p =>
        ({
          id: p.id,
          guid: p.guid
        }))
      temp.objectIds = []
      temp.objectGroupIds = []
      this.selectedTerminalData.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          temp.objectIds.push(nodeData.dataId)
        } else {
          temp.objectGroupIds.push(nodeData.dataId)
        }
      })
    },
    createData() {
      this.submitting = true
      this.formatSubmitData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.objectIds.length == 0 && this.temp.objectGroupIds.length == 0) {
            this.$message({
              message: this.$t('pages.placeChooseApplicationObj')
            })
            this.submitting = false
            return
          }
          // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断 this.strategyDefType = 0 应用策略
          if (!validatePolicy(this.temp, this, 0)) {
            this.submitting = false
            return
          }
          this.$confirmBox('确定以最新的启用状态应用于生效对象对应的策略？', this.$t('text.prompt')).then(() => {
            const that = this
            createStrategy(this.temp).then(() => {
              this.submitting = false
              this.dialogFormVisible = false
              that.$emit('submitEnd', 'create')
            }).catch(res => {
              this.submitting = false
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.formatSubmitData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.objectIds.length == 0 && this.temp.objectGroupIds.length == 0) {
            this.$message({
              message: this.$t('pages.placeChooseApplicationObj')
            })
            this.submitting = false
            return
          }
          const that = this
          console.log('this.temp', this.temp)
          updateStrategy(this.temp).then(() => {
            that.submitting = false
            that.dialogFormVisible = false
            that.$emit('submitEnd', 'update')
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    cancel() {
      this.dialogFormVisible = false
      this.$emit('submitEnd', 'cancel')
    },
    handleFilter(searchPatchName) {
      if (searchPatchName) {
        const searchInfo = searchPatchName.toLowerCase()
        this.temp.patchList = this.allRowDatas.filter(item => item.patchKb && item.patchKb.toLowerCase().indexOf(searchInfo) !== -1)
      } else {
        this.temp.patchList = this.allRowDatas
      }
    }
  }
}
</script>
