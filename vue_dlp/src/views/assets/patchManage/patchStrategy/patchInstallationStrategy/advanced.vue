<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.highConfig')"
      :visible.sync="dialogFormVisible"
      width="700px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="120px"
        :hide-required-asterisk="true"
        style="width: 632px; margin-left: 20px;"
      >
        <div >
          <el-row>
            <el-col :span="6" style="width: 22%;line-height: 30px">
              <el-checkbox v-model="temp.isLimitTime" :false-label="0" :true-label="1">
                {{ $t('pages.limitPatchDownloadTimePeriod') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.patchDownloadTimePeriodTip">
                      <br slot="br"/>
                    </i18n>
                  </div>
                  <i class="el-icon-info" style="align-items: center" />
                </el-tooltip>
              </el-checkbox>
            </el-col>
            <el-col :span="14">
              <FormItem label-width="0" prop="limitTime">
                <el-input v-model="temp['limitTime']" :disabled="showTimeConponent" :maxlength="90"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="4">
              <svg-icon v-show="!showTimeConponent" icon-class="add" class-name="add-time" style="margin: 8px;cursor: pointer" @click="showItems()" />
              <svg-icon v-show="showTimeConponent" icon-class="active" class-name="add-time" style="margin: 8px;cursor: pointer" @click="saveItems()" />
            </el-col>
          </el-row>
          <div v-show="showTimeConponent" class="edit-item" style="margin:0 50px 0 139px">
            <FormItem label-width="0">
              <el-row v-for="(value, index) in timeList" :key="index" style="margin-bottom: 5px">
                <el-col :span="20">
                  <el-time-picker
                    v-model="timeList[index]"
                    is-range
                    :editable="true"
                    value-format="HH:mm:ss"
                    range-separator=" -- "
                    :start-placeholder="$t('pages.startTime')"
                    :end-placeholder="$t('pages.endTime')"
                    :placeholder="$t('pages.timeInfo_text3')"
                    style="width:100%;"
                  >
                  </el-time-picker>
                </el-col>
                <el-col :span="4" style="cursor: pointer;color: #68a8d0;padding-left: 8px">
                  <i v-show="editable" class="el-icon-circle-plus-outline" @click="addTimeItem(index + 1)"></i>
                  <i class="el-icon-remove-outline" @click="deleteTimeItem(index)"></i>
                </el-col>
              </el-row>
            </FormItem>
            <span style="padding-left: 10px; color: #0c60a5;">{{ $t('pages.timeInfo_text2') }}</span>
          </div>
          <el-row>
            <FormItem :label="$t('pages.patchAllowDownloadTime')" label-width="130px" prop="timeId" style="width:550px;margin-right: 10px">
              <el-select v-model="temp.timeId" :placeholder="$t('text.select')" style="width: 371px;margin-left: 7px">
                <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <link-button :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'" btn-style="margin: 1px 0 0;"/>
            </FormItem>
          </el-row>
          <FormItem :label="$t('pages.patchDownloadPath')" style="margin-top: 5px">
            <span >
              <el-tooltip effect="dark" placement="top">
                <div slot="content" class="tooltipStyle">
                  {{ $t('pages.fileDetectionFilePathTip1') }}<br>
                  <span v-for="item in filePathShortcutKeys" :key="item.path">
                    {{ item.label }}：{{ item.path }}{{ item.remark }}<br>
                  </span>
                  <!--                {{ $t('pages.fileDetectionFilePathTip2') }}<br>-->
                  {{ $t('text.cantNullInfo', { info: $t('pages.path')}) }}<br>
                  <!--                {{ $t('pages.fileDetectionFilePathTip3') }}<br>-->
                  {{ $t('pages.fileDetectionFilePathMsg2') }}<br>
                  {{ $t('pages.fileDetectionFilePathTip4') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </span>
            <el-button v-for="item in filePathShortcutKeys" :key="item.path" size="mini" @click="filePathShortcutKey(item.path)">{{ item.label }}</el-button>
          </FormItem>
          <FormItem prop="savePath" label-width="0px">
            <el-input v-model="temp.savePath" :placeholder="$t('pages.userTempDirectory')" style="margin-left: 135px;width: 471px" clearable maxlength="40" show-word-limit @input="filPathInputHandler"></el-input>
          </FormItem>
        </div>
        <!--        <el-divider content-position="left">-->
        <!--          {{ $t('pages.autoInstallPatch') }}-->
        <!--          &lt;!&ndash;          <el-tooltip>&ndash;&gt;-->
        <!--          &lt;!&ndash;            <template slot="content">&ndash;&gt;-->
        <!--          &lt;!&ndash;              <div>补丁检测周期设置关闭，限制无法配置自动安装补丁策略</div>&ndash;&gt;-->
        <!--          &lt;!&ndash;            </template>&ndash;&gt;-->
        <!--          &lt;!&ndash;            <i class="el-icon-info"/>&ndash;&gt;-->
        <!--          &lt;!&ndash;          </el-tooltip>&ndash;&gt;-->
        <!--        </el-divider>-->
        <!--        <el-row style="padding-top: 15px">-->
        <!--          <el-col :span="24">-->
        <!--            <el-checkbox v-model="temp.autoInstall" style="margin-right: 30px;" :true-label="1" :false-label="0" @change="handleAutoInstallChange">{{ $t('pages.autoInstallPatchByLevel') }}</el-checkbox>-->
        <!--          </el-col>-->
        <!--        </el-row>-->
        <!--        <el-row>-->
        <!--          <el-col :span="24">-->
        <!--            <FormItem prop="autoPatchLevelList" label-width="0">-->
        <!--              <el-checkbox-group v-model="temp.autoPatchLevelList" style="display: inline-block;" :disabled="temp.autoInstall === 0" @change="handlePatchLevelChange">-->
        <!--                <el-checkbox v-for="(key,value) in patchLevelMap" :key="key" :label="value">{{ key }}</el-checkbox>-->
        <!--              </el-checkbox-group>-->
        <!--            </FormItem>-->
        <!--          </el-col>-->
        <!--        </el-row>-->

      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="createData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { getDownloadCycle } from '@/api/assets/patchManage/patchFiles';
// import { getStrategy, createStrategy } from '@/api/assets/patchManage/patchAutoInstallStrategy';
import { getSetting, saveInstallStrategy } from '@/api/assets/patchManage/paramSetting';
import { parseTime } from '@/utils';
export default {
  name: 'Advanced',
  components: { },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      dialogFormVisible: false,
      submitting: false,
      showTimeConponent: false,
      timeList: [],
      editable: true,
      defaultTemp: { // 表单字段
        name: '安装策略',
        limitTime: '00:00:00-23:59:59',
        isLimitTime: true,
        timeId: 1, // 补丁允许安装时间
        savePath: '#USERTEMP#\\' // 保存路径
      },
      //  文件路径快捷按钮 this.$t('pages.userTempDirectory')
      filePathShortcutKeys: [
        { label: this.$t('pages.maximumRemainingDiskSpace'), path: '#MAX#\\', remark: '' },
        { label: this.$t('pages.desktop'), path: '#DESKTOP#\\', remark: '' },       //  桌面
        { label: this.$t('pages.userTempDirectory') + '(' + this.$t('pages.defaultValue') + ')', path: '#USERTEMP#\\', remark: this.$t('pages.userTempDirectoryRemark') },       //  用户临时目录
        { label: this.$t('pages.sysTempTempDirectory'), path: '#SYSTEMTEMP#\\', remark: this.$t('pages.sysTempTempDirectoryRemark') }       //  系统临时目录
      ],
      temp: { // 表单字段
      },
      // patchLevelMap: {
      //   1: this.$t('pages.severityLevel'),
      //   2: this.$t('pages.importanceLevel'),
      //   4: this.$t('pages.midLevel'),
      //   8: this.$t('pages.lowLevel')
      // },
      rules: {
        limitTime: [{ required: true, trigger: 'blur', validator: this.timesValidator }]
      }
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    }
  },
  watch: {
    showTimeConponent(val) {
      if (val) {
        this.showItems()
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    handleDrag() {
    },
    timesValidator(rule, value, callback) {
      const ranges = value.split(';')
      const dateStr = parseTime(new Date(), 'y-m-d')
      for (let i = 0; i < ranges.length; i++) {
        const times = ranges[i].split('-')
        const date1 = dateStr + ' ' + times[0]
        const date2 = dateStr + ' ' + times[1]
        // ie浏览器不支持 new Date('xxxx-xx-xx xx:xx:xx')，修改为 new Date('xxxx/xx/xx xx:xx:xx')
        const nDate1 = parseTime(new Date(date1.replace(/-/g, '/')), 'y-m-d h:i:s')
        const nDate2 = parseTime(new Date(date2.replace(/-/g, '/')), 'y-m-d h:i:s')
        if (date1 != nDate1 || date2 != nDate2) {
          callback(new Error(this.$t('pages.validateMsg_time1')))
          break
        } else if (new Date(date1) > new Date(date2)) {
          callback(new Error(this.$t('pages.validateMsg_time2')))
          break
        }
      }
      callback()
    },
    resetTemp() {
      this.submitting = false
      // 因为defaultTemp中存在数组，为了避免数组被改变，使用此方法
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    show() {
      this.dialogFormVisible = true
      this.editVisible = true;
      this.showTimeConponent = false;
      this.getDownloadCycle()
      this.getStrategy()
    },
    getDownloadCycle() {
      getDownloadCycle().then(res => {
        const cycle = JSON.parse(res.data.value)
        console.log('getDownloadCycle', res.data.value)
        this.temp = Object.assign(this.temp, cycle, { isLimitTime: Number(cycle.isLimitTime) })
        console.log('this.temp', this.temp)
      })
    },
    getStrategy() {
      getSetting().then(res => {
        console.log('getStrategy', res)
        this.temp = Object.assign(this.temp, res.data)
        // this.formatCheckDay(this.temp.checkDay)
        // this.formatAutoPatchLevelList(res.data.autoPatchLevel)
      })
    },
    // getStrategy() {
    //   getStrategy().then(res => {
    //     this.temp = Object.assign(this.temp, res.data)
    //     console.log('res.data.autoPatchLevel', res.data.autoPatchLevel)
    //     if (res.data.autoPatchLevel !== undefined) {
    //       this.formatAutoPatchLevelList(res.data.autoPatchLevel)
    //     }
    //   })
    // },
    // 补丁等级
    // formatAutoPatchLevelList(num) {
    //   if (num === null || num === undefined) {
    //     this.temp.autoPatchLevelList = []
    //     return
    //   }
    //   const arrStr = num.toString(2).split('').reverse();
    //   const resArr = []
    //   for (let i = 0; i < arrStr.length; i++) {
    //     if (arrStr[i] == 1) {
    //       resArr.push(Math.pow(2, i).toString())
    //     }
    //   }
    //   this.temp.autoPatchLevelList = resArr
    //   console.log('this.temp.autoPatchLevelList', this.temp.autoPatchLevelList)
    // },
    // 下载周期
    showItems() {
      this.timeList = []
      this.editable = true
      this.showTimeConponent = true
      const timeArr = this.temp.limitTime.split(';')
      if (timeArr.length >= 5) {
        this.editable = false
      }
      timeArr.forEach(el => {
        const strArr = el.split('-')
        this.timeList.push(strArr)
      })
    },
    saveItems() {
      console.log('this.timeList', this.timeList)
      if (this.timeList && this.timeList.length > 0) {
        this.temp.limitTime = ''
        this.timeList.forEach(el => {
          const str = el[0] + '-' + el[1]
          if (this.temp.limitTime) {
            console.log('this.temp.limitTime1', this.temp.limitTime)
            if (this.temp.limitTime.indexOf(str) == -1) {
              this.temp.limitTime = this.temp.limitTime + ';' + str
            }
          } else {
            this.temp.limitTime = str
            console.log('this.temp.limitTime2', this.temp.limitTime)
          }
        })
      }
      // this.timeList = []
      this.editable = true
      this.showTimeConponent = false
    },
    deleteTimeItem(index) {
      this.timeList.splice(index, 1)
      if (this.timeList.length === 0) {
        this.addTimeItem(0)
      }
      if (this.timeList.length >= 5) {
        this.editable = false
      } else {
        this.editable = true
      }
    },
    addTimeItem(index) {
      if (this.timeList.length < 5) {
        this.timeList.splice(index, 0, ['00:00:00', '23:59:59'])
      }
      if (this.timeList.length >= 5) {
        this.editable = false
      } else {
        this.editable = true
      }
    },
    // 补丁自动安装策略
    // handleAutoInstallChange(val) {
    //   if (0 == val) {
    //     this.temp.autoPatchLevelList = []
    //     this.temp.autoPatchLevel = 0
    //   }
    // },
    // handlePatchLevelChange(val) {
    //   const sum = val.reduce((sum, current) => sum + Number(current), 0)
    //   this.temp.autoPatchLevel = sum
    // },
    // 补丁下载路径
    filePathShortcutKey(path) {
      this.temp.savePath = path
    },
    removeDuplicates(str, pattern) {
      // 使用正则表达式捕获组来保留第一个匹配项
      const regex = new RegExp(pattern, 'ig');
      let matchCount = 0
      return str.replace(regex, (match, index) => {
        console.log('regex.lastIndex', match, index)
        if (matchCount !== 0) {
          return ''
        }
        matchCount++
        return match
      });
    },
    //  不允许输入 / : * ? " < > |
    filPathInputHandler(value) {
      console.log('filPathInputHandler', value)
      const notAllowExits = ['/', ':', '*', '?', '"', '<', '>', '|'];
      for (let i = 0; i < notAllowExits.length; i++) {
        value = value.replaceAll(notAllowExits[i], notAllowExits[i] === '/' ? '\\' : '')
      }
      const pattern = '#MAX#|#DESKTOP#|#USERTEMP#|#SYSTEMTEMP#'
      // 防止管理员重复输入#MAX#|#DESKTOP#|#USERTEMP#|#SYSTEMTEMP#
      value = this.removeDuplicates(value, pattern)
      // 防止管理员重复输入反斜线
      value = value.replace(/\\+/g, '\\');
      this.temp.savePath = value
    },
    createData() {
      this.saveItems()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const that = this
          console.log('this.temp', this.temp)
          const tempData = Object.assign({}, this.temp, { name: '安装策略' })
          console.log('this.temp', JSON.stringify(tempData))
          saveInstallStrategy(tempData).then(respond => {
            this.dialogFormVisible = false
            that.submitting = false
            that.editVisible = false
            that.$emit('submitEnd')
            that.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    }

  }
}
</script>
