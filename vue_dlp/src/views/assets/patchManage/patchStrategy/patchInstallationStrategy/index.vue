<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange" />
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleAdvanced">
          {{ $t('button.highConfig') }}
        </el-button>
        <!--        <div class="searchCon">-->
        <!--          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.patchID') } )" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>-->
        <!--          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">-->
        <!--            {{ $t('table.search') }}-->
        <!--          </el-button>-->
        <!--        </div>-->

      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <edit-patch-installation ref="editPatchInstallation" @submitEnd="submitEnd"></edit-patch-installation>
    <advanced ref="advanced" @submitEnd="submitEnd"></advanced>
  </div>
</template>
<script>
import { getStrategyPage, deleteStrategy } from '@/api/assets/patchManage/autoPatch'
import {
  enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity,
  objectFormatter, entityLink, refreshPage, buttonFormatter
} from '@/utils'
import EditPatchInstallation from './edit.vue'
import Advanced from './advanced.vue'
export default {
  name: 'PatchInstallationStrategy',
  components: { EditPatchInstallation, Advanced },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '300', formatter: this.strategyFormatter },
        { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    editPatchInstallation() {
      return this.$refs['editPatchInstallation']
    },
    advanced() {
      return this.$refs['advanced']
    }
  },
  watch: {
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      const objectData = {}
      if (this.query.objectType && this.query.objectId) {
        if (this.query.objectType == 1) {
          objectData.objectIds = [this.query.objectId]
        } else {
          objectData.objectGroupIds = [this.query.objectId]
        }
      }
      this.editPatchInstallation.handleCreate(objectData)
    },
    handleUpdate: function(row) {
      this.editPatchInstallation.handleUpdate(row)
    },
    handleAdvanced: function() {
      this.advanced.show()
    },
    submitEnd(action) {
      console.log('action', action)
      if (action == 'cancel') {
        return
      }
      this.handleFilter()
      if (action === 'create') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }
      if (action === 'update') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      // let arr = []
      let str = '';
      if (row.patchList !== undefined) {
        str = this.$t('pages.patch') + '：'
        const arr = row.patchList
        // const str2 = row.patchListt.reduce((a, b) => a.patchTitle.concat(';').concat(b))
        // str = str.concat(str2).concat(';')
        for (let i = 0; i < arr.length; i++) {
          str = str.concat(arr[i].patchTitle).concat(';')
        }
      }

      // console.log('strategyFormatter', data.patchList)
      // data.patchList.reduce((a, b) => a.patchTitle.concat(';').concat(b))
      // const obj = JSON.parse(JSON.stringify(data))
      // const arr = Array.of(obj.patchList)
      return str;
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    }
  }
}
</script>
