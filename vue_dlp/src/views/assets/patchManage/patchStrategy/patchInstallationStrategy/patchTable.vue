<template>
  <div>
    <!--      v-if="dlgVisible"-->
    <el-dialog
      v-if="dlgVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.patchInfo')"
      :visible.sync="dlgVisible"
      width="800px"
      @close="hide()"
    >
      <div style="height: 400px">
        <div class="table-container">
          <div class="toolbar">
            <label>{{ $t('pages.findCategory') }}：</label>
            <el-select v-model="query2.searchType" style="width: 150px;">
              <el-option :label="$t('pages.allPatches')" :value="0"></el-option>
              <el-option :label="$t('pages.patchID')" :value="3"></el-option>
              <el-option :label="$t('pages.patchLevel')" :value="9"></el-option>
              <!--              <el-option :label="$t('pages.patchTitle')" :value="4"></el-option>-->
              <!--              <el-option :label="$t('pages.patchStatus')" :value="6"></el-option>-->
              <!--              <el-option :label="$t('pages.executionStatus')" :value="7"></el-option>-->
              <!--              <el-option :label="$t('pages.patchDescription')" :value="8"></el-option>-->
              <el-option :label="$t('pages.microsoftAnnouncementNo')" :value="5"></el-option>

            </el-select>
            <el-input v-if="[0,3,4,5,8].indexOf(query2.searchType) != -1" v-model="query2.searchContent" v-trim clearable :disabled="query2.searchType==0" style="width: 150px;" />
            <el-select v-if="[6,7,9].indexOf(query2.searchType) != -1" v-model="query2.searchContent" style="width: 150px;">
              <el-option :label="$t('pages.all')" :value="null"/>
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

            <el-button type="primary" size="mini" @click="handleFilter2">{{ $t('pages.findPatches') }}</el-button>
          </div>
          <div class="">
            <grid-table
              ref="patchList"
              :multi-select="true"
              :height="360"
              :after-load="afterLoad"
              :show-pager="true"
              :autoload="true"
              :row-key="getRowKey()"
              :col-model="colModel2"
              :row-data-api="rowDataApi2"
              @selectionChangeEnd="selectionChangeEnd"
            />
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="addStgData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="hide()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getPatchInfoPage } from '@/api/assets/patchManage/termModel';
import moment from 'moment/moment';

export default {
  name: 'PatchTable',
  components: {},
  props: {},
  data() {
    return {
      dlgVisible: false,
      submitting: false,
      query2: { // 查询条件
        page: 1,
        includeTermInfo: false,
        searchType: 0,
        searchContent: null,
        sortName: 'id',
        sortOrder: 'desc'
      },
      typeOptions: [],
      colModel2: [
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100', sort: true, formatter: this.patchKbFormatter },
        { prop: 'patchTitle', label: this.$t('pages.patchTitle'), width: '120' },
        { prop: 'patchLevel', label: this.$t('pages.patchLevel'), width: '100', sort: true, sortOriginal: true, formatter: this.patchLevelFormat },
        { prop: 'downloadState', label: 'status', width: '100', formatter: this.downloadStateFormat },
        { prop: 'patchDescribe', label: this.$t('pages.patchDescription'), width: '120' },
        { prop: 'patchSize', label: 'size', width: '100', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'msId', label: this.$t('pages.microsoftAnnouncementNo'), sort: true, width: '120' }
      ],
      // 下载状态0：未收集完整 1：数据已收集完成 2：正在下载 3：下载失败 4：已下载到-Server 5：正在分发ftp 6：分发ftp失败 7：FTP分发完成
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      patchLevelMap: {
        0: this.$t('pages.undefinedLevel'),
        1: this.$t('pages.lowLevel'),
        2: this.$t('pages.mediumPatch'),
        3: this.$t('pages.importanceLevel'),
        4: this.$t('pages.severityLevel')
      },
      patchSelectedDatas: []
    }
  },
  computed: {
  },
  watch: {
    'query2.searchType'(newValue, oldValue) {
      this.query2.searchContent = null
      if (newValue == 6) {
        this.typeOptions = [
          { value: 0, label: '' },
          { value: 1, label: this.$t('pages.notDownloaded') },
          { value: 2, label: this.$t('pages.downloading') },
          { value: 3, label: this.$t('pages.downloadFailed') },
          { value: 4, label: this.$t('pages.downloading1') },
          { value: 5, label: this.$t('pages.downloading1') },
          { value: 6, label: this.$t('pages.downloadFailed') },
          { value: 7, label: this.$t('pages.FTPDistributionCompleted') }
        ]
      } else if (newValue == 7) {
        this.typeOptions = [
          { value: 0, label: this.$t('pages.notInstalled') },
          { value: 1, label: this.$t('pages.alreadyInstalled') },
          { value: 2, label: this.$t('pages.downloading1') },
          { value: 3, label: this.$t('pages.terminalDownloadCompleted') }
        ]
      } else if (newValue == 9) {
        this.typeOptions = [
          { value: 4, label: this.$t('pages.severityLevel') },
          { value: 3, label: this.$t('pages.importanceLevel') },
          { value: 2, label: this.$t('pages.mediumPatch') },
          { value: 1, label: this.$t('pages.lowLevel') },
          { value: 0, label: this.$t('pages.undefinedLevel') }
        ]
      }
    }
  },
  methods: {
    getRowKey(row) {
      return row && (row.guid + '_' + moment(row.create_time).unix())
    },
    afterLoad(rowData, table) {
    },
    patchKbFormatter: function(row, data) {
      return data
    },
    patchLevelFormat: function(row, data) {
      return this.patchLevelMap[data]
    },
    sizeFormatter: function(row, data) {
      return this.convertSize(data)
    },
    convertSize: function(size) {
      if (!size) {
        return '0 Bytes';
      }
      var sizeNames = [' Bytes', ' KB', ' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
      var i = Math.floor(Math.log(size) / Math.log(1024));
      var p = (i > 0) ? 2 : 1;
      if (p == 1) {
        return 1 + sizeNames[1];
      } else {
        return (size / Math.pow(1024, Math.floor(i))).toFixed(p) + sizeNames[i];
      }
    },
    downloadStateFormat: function(row, data) {
      return this.downloadStateMap[data]
    },
    resetTemp() {
      this.query2 = { // 查询条件
        page: 1,
        includeTermInfo: false,
        searchType: 0,
        searchContent: null,
        sortName: 'id',
        sortOrder: 'desc'
      }
    },
    show() {
      this.dlgVisible = true
    },
    hide() {
      // this.gridTable && this.gridTable.clearSelection()
      this.dlgVisible = false
    },
    handleFilter2() {
      this.query2.page = 1
      this.$refs['patchList'].execRowDataApi(this.query2)
    },
    rowDataApi2: function(option) {
      const searchQuery = Object.assign({}, this.query2, option)
      return getPatchInfoPage(searchQuery)
    },
    selectionChangeEnd(rowData) {
      this.patchSelectedDatas = rowData
    },
    // 数据添加到策略
    addStgData() {
      const datas = this.patchSelectedDatas;
      if (datas.length === 0) {
        this.$message({
          message: this.$t('pages.selectAtLeastOnePatch')
        })
        return
      } else {
        this.$emit('add-data', datas)
      }
    }
  }
}
</script>
