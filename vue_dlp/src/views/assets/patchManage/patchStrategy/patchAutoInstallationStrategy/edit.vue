<template>
  <div>
    <el-dialog
      v-if="dialogFormVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @close="close"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="120px"
        :hide-required-asterisk="true"
        style="width: 700px; margin-left: 20px;"
      >
        <FormItem :label="$t('pages.effectiveObject')" prop="objectIds">
          <tree-select
            ref="objectTree"
            :placeholder="$t('pages.placeChooseApplicationObj')"
            node-key="id"
            :height="350"
            :width="468"
            multiple
            check-strictly
            collapse-tags
            is-filter
            :filter-key="{ prop: 'dataType', value: '3', showIfNoProp: true }"
            :checked-keys="temp.checkedKeys"
            :disabled="!formable"
            :local-search="false"
            leaf-key="terminal"
            @change="checkedIdChange"
          />
        </FormItem>
        <!--        <FormItem :label="$t('text.effectTime')" prop="timeId">-->
        <!--          <el-select v-model="temp.timeId" :disabled="!formable" :placeholder="$t('text.select')" style="width: calc(100% - 30px);">-->
        <!--            <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
        <!--          </el-select>-->
        <!--          <link-button btn-class="editBtn" :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'" btn-style="margin: 1px 0 0;"/>-->
        <!--        </FormItem>-->
        <FormItem :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" @change="handleAutoInstallChange"/>
        </FormItem>
        <el-divider content-position="left">
          {{ $t('pages.autoInstallPatchByLevel') }}
        </el-divider>
        <FormItem label-width="0" >
          <div style="padding-left: 55px" >
            <el-checkbox-group v-model="temp.autoPatchLevelList" style="display: inline-block;" :disabled="temp.autoInstall === 0" @change="handlePatchLevelChange">
              <el-checkbox v-for="item in patchLevelMap" :key="item.key" :disabled="!formable" :label="item.key">{{ item.value }}
                <div>{{ item.msg }}</div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment';
import { validatePolicy } from '@/utils/validate';
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/assets/patchManage/patchAutoInstallStrategy';

export default {
  name: 'EditPatchAutoInstallation',
  components: { },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.formable ? this.i18nConcatText(this.$t('pages.automaticPatchStg'), 'update') : this.i18nConcatText(this.$t('pages.automaticPatchStg'), 'details'),
        create: this.i18nConcatText(this.$t('pages.automaticPatchStg'), 'create')
      },
      rules: {
        // objectIds: [
        //   { required: true, message: this.$t('components.required'), trigger: 'blur' },
        //   { validator: this.objectIdsValidator, trigger: 'blur' }
        // ],
        name: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      patchLevelMap: [
        { key: '1', value: this.$t('pages.severityLevel'), msg: this.$t('pages.patchLevelSeverityLevelMsg') },
        { key: '2', value: this.$t('pages.importanceLevel'), msg: this.$t('pages.patchLevelImportanceLevelMsg') },
        { key: '4', value: this.$t('pages.midLevel'), msg: this.$t('pages.patchLevelMidLevelMsg') },
        { key: '8', value: this.$t('pages.lowLevel'), msg: this.$t('pages.patchLevelLowLevelMsg') }
      ],
      temp: { // 表单字段
      },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: true,
        timeId: undefined,
        remark: '',
        entityType: '',
        entityId: undefined,
        objectIds: [],
        objectGroupIds: [],
        checkedKeys: [],
        autoInstall: 1,
        autoPatchLevel: undefined,
        autoPatchLevelList: []
      },
      selectedTerminalData: []
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    close() {
      this.resetTemp();
    },
    getRowKey(row) {
      console.log('getRowKey', row)
      return row.patchKb + '_' + moment(row.create_time).unix()
    },
    handleDrag() {
    },
    // objectIdsValidator(rule, value, callback) {
    //   console.log('objectIdsValidator', value)
    //   if (value.length == 0) {
    //     callback(new Error(this.$t('pages.placeChooseApplicationObj')))
    //   } else {
    //     callback()
    //   }
    // },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.checkedKeys = []
    },
    initFormObject(row) {
      if (Object.keys(row).length === 0) {
        return
      }
      const { objectIds, objectGroupIds } = row
      this.temp.checkedKeys.splice(0)
      if (objectIds) {
        objectIds.forEach(objId => {
          this.temp.checkedKeys.splice(0, 0, ('T') + objId)
        })
      }
      if (objectGroupIds) {
        objectGroupIds.forEach(objId => {
          this.temp.checkedKeys.splice(0, 0, 'G' + objId)
        })
      }
    },
    handleCreate(data) {
      this.dialogFormVisible = true
      this.submitting = false
      this.resetTemp()
      this.initFormObject(data)
      this.dialogStatus = 'create'
    },
    handleUpdate(row) {
      this.submitting = false
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      this.initFormObject(row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        const vo = {}
        vo.name = this.temp.name
        this.getStrategy(vo)
      })
    },
    // 补丁等级
    formatAutoPatchLevelList(num) {
      if (num === null || num === undefined) {
        this.temp.autoPatchLevelList = []
        return
      }
      const arrStr = num.toString(2).split('').reverse();
      const resArr = []
      for (let i = 0; i < arrStr.length; i++) {
        if (arrStr[i] == 1) {
          resArr.push(Math.pow(2, i).toString())
        }
      }
      this.temp.autoPatchLevelList = resArr
    },
    getStrategy(vo) {
      getStrategyByName(vo).then(res => {
        this.temp = Object.assign({}, this.temp, res.data)
        if (res.data.autoPatchLevel !== undefined) {
          this.formatAutoPatchLevelList(res.data.autoPatchLevel)
        }
      })
    },
    checkedIdChange(keys, options) {
      this.selectedTerminalData.splice(0, this.selectedTerminalData.length, ...options)
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      this.selectedTerminalData.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          this.temp.objectIds.push(nodeData.dataId)
        } else {
          this.temp.objectGroupIds.push(nodeData.dataId)
        }
      })
      console.log('this.temp.objectIds checkedIdChange2', this.temp.objectIds)
      console.log('this.temp.objectGroupIds checkedIdChange2', this.temp.objectGroupIds)
    },
    // 补丁自动安装策略
    handleAutoInstallChange(val) {
      console.log('handleAutoInstallChange', val)
      if (!val) {
        this.temp.autoPatchLevelList = []
        this.temp.autoPatchLevel = 0
        this.temp.autoInstall = 0
      } else {
        this.temp.autoInstall = 1
      }
    },
    handlePatchLevelChange(val) {
      const sum = val.reduce((sum, current) => sum + Number(current), 0)
      console.log('handlePatchLevelChange', sum)
      this.temp.autoPatchLevel = sum
    },
    // 功能API
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.objectIds.length == 0 && this.temp.objectGroupIds.length == 0) {
            this.$message({
              message: this.$t('pages.placeChooseApplicationObj')
            })
            this.submitting = false
            return
          }
          // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断 this.strategyDefType = 0 应用策略
          if (!validatePolicy(this.temp, this, 0)) {
            this.submitting = false
            return
          }
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.resetTemp();
            this.$emit('submitEnd', 'create')
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          console.log('this.temp.objectIds', this.temp.objectIds)
          console.log('this.temp.objectGroupIds', this.temp.objectGroupIds)
          if (this.temp.objectIds.length == 0 && this.temp.objectGroupIds.length == 0) {
            this.$message({
              message: this.$t('pages.placeChooseApplicationObj')
            })
            this.submitting = false
            return
          }
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.resetTemp();
            this.$emit('submitEnd', 'update')
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    cancel() {
      this.dialogFormVisible = false
      this.resetTemp();
      this.$emit('submitEnd', 'cancel')
    }
  }
}
</script>
