<template>
  <div class="app-container">
    <div class="table-container">
      <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
        <!--        <el-tab-pane label="参数配置" name="paramSetting">-->
        <!--          <param-setting v-if="isParamSetting" ref="paramSetting"/>-->
        <!--        </el-tab-pane>-->
        <!--        <el-tab-pane label="安装策略" name="patchInstallationStrategy">-->
        <!--          <Patch-Installation-Strategy v-if="isPatchInstallationStrategy" ref="patchInstallationStrategy"/>-->
        <!--        </el-tab-pane>-->
        <el-tab-pane :label="$t('route.terminalModule')" name="termModel">
          <term-model v-if="isTermModel" ref="termModel"/>
        </el-tab-pane>
        <el-tab-pane :label="$t('route.patchPattern')" name="patchModel">
          <patch-model v-if="isPatchModel" ref="patchModel"/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import TermModel from './termModel/index';
import PatchModel from './patchModel/index'
export default {
  name: 'PatchManagement',
  components: { TermModel, PatchModel },
  data() {
    return {
      activeName: 'termModel',
      isTermModel: true,
      isPatchModel: false
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    isTermModelPanel() {
      // 是否离线检测包panel
      return this.activeName === 'termModel'
    },
    // tab菜单点击事件
    tabClick(pane, event) {
      if (this.isTermModelPanel()) {
        // this.isParamSetting = false
        // this.isPatchInstallationStrategy = false
        this.isTermModel = true
        this.isPatchModel = false
      } else {
        // this.isParamSetting = false
        // this.isPatchInstallationStrategy = false
        this.isTermModel = false
        this.isPatchModel = true
      }
    },
    destroy() {
    }
    // showResult(resultArr) {
    //   console.log('resultArr', resultArr)
    //   // this.$refs['uploadTipDlg'].show(JSON.parse(resultArr)[0])
    //   if (undefined == resultArr) {
    //     this.$notify({
    //       title: this.$t('text.success'),
    //       message: '尚无结果',
    //       type: 'success',
    //       duration: 2000
    //     })
    //     return
    //   }
    //   this.$refs['uploadTipDlg'].showBatchUpload(JSON.parse(resultArr))
    // }
  }
}
</script>
