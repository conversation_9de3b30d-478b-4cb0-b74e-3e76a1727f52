<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange" />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <label>{{ $t('pages.findCategory') }}：</label>
        <el-select v-model="query1.searchType" style="width: 120px;">
          <el-option :label="$t('pages.allTerminals')" :value="0"></el-option>
          <el-option :label="$t('table.terminalName')" :value="1"></el-option>
          <el-option :label="$t('pages.operatingSystem')" :value="2"></el-option>
          <el-option :label="$t('pages.terminalStatus')" :value="3"></el-option>
        </el-select>
        <el-input v-if="[0,1,2].indexOf(query1.searchType) != -1" v-model="query1.searchContent" v-trim :disabled="query1.searchType==0" style="width: 150px;" clearable/>
        <el-select v-if="[3].indexOf(query1.searchType) != -1" v-model="query1.searchContent" style="width: 150px;">
          <el-option :label="$t('pages.all')" :value="null"/>
          <el-option v-for="item in termTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button type="primary" size="mini" @click="handleFilter">{{ $t('pages.findTerminal') }}</el-button>
        <el-button type="primary" size="mini" @click="scanTerm">{{ $t('pages.scanTerminal') }}</el-button>
        <!--        <div style="padding: 10px 0 0 10px;"> query2.termIdList.length == 0 || selectDept-->
        <!--          <el-checkbox v-model="checkAll" style="margin-right: 30px;" @change="handleCheckAllChange">{{ $t('button.selectAll') }}</el-checkbox>-->
        <!--          <el-checkbox-group v-model="query1.levelList" style="display: inline-block;" @change="handleCheckedCitiesChange">-->
        <!--            <el-checkbox v-for="(key,value) in systemLevelMap" :key="key" :label="value">{{ key }}</el-checkbox>-->
        <!--          </el-checkbox-group>-->
        <!--        </div>-->
      </div>
      <grid-table
        ref="termList"
        :after-load="afterLoad"
        :col-model="colModel1"
        :multi-select="true"
        :is-saved-selected="true"
        saved-selected-prop="termId"
        row-key="termId"
        :row-data-api="rowDataApi1"
        style="min-height: 140px; margin-bottom: 10px;"
        @row-click="rowClick"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <div class="toolbar">
        <label>{{ $t('pages.findCategory') }}：</label>
        <el-select v-model="query2.searchType" style="width: 150px;">
          <el-option :label="$t('pages.allPatches')" :value="0"></el-option>
          <el-option :label="$t('pages.patchID')" :value="3"></el-option>
          <el-option :label="$t('pages.patchLevel')" :value="9"></el-option>
          <el-option :label="$t('pages.patchStatus')" :value="6"></el-option>
          <el-option :label="$t('pages.executionStatus')" :value="7"></el-option>
          <!--          <el-option :label="$t('pages.patchTitle')" :value="4"></el-option>-->
          <!--          <el-option :label="$t('pages.patchDescription')" :value="8"></el-option>-->
          <el-option :label="$t('pages.microsoftAnnouncementNo')" :value="5"></el-option>
        </el-select>
        <el-input v-if="[0,3,4,5,8].indexOf(query2.searchType) != -1" v-model="query2.searchContent" v-trim clearable :disabled="query2.searchType==0" style="width: 150px;" />
        <el-select v-if="[6,7,9].indexOf(query2.searchType) != -1" v-model="query2.searchContent" style="width: 150px;">
          <el-option :label="$t('pages.all')" :value="null"/>
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-button type="primary" :disabled="query2.termId == null" size="mini" @click="handleFilter2">{{ $t('pages.findPatches') }}</el-button>
        <el-button type="primary" :disabled="query2.termId == null" size="mini" @click="handleInstallStrategy">{{ $t('text.addInfo', { info: $t('route.installStrategy')}) }}</el-button>
        <el-button type="primary" :disabled="query2.termId == null || installTip" size="mini" @click="installPatch">{{ $t('pages.installPatches') }}
          <el-tooltip v-if="installTip" effect="dark" placement="bottom-end">
            <div slot="content">{{ $t('pages.patchInstallTip') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <!--<el-button type="primary" size="mini" @click="uninstallPatch">{{ $t('pages.cancelInstallation') }}</el-button>-->
        <el-button type="primary" :disabled="query2.termId == null" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
      </div>
      <grid-table
        ref="patchList"
        :show-pager="true"
        :autoload="false"
        :col-model="colModel2"
        :row-data-api="rowDataApi2"
        style="min-height: 140px; margin-bottom: 1px;"
        @selectionChangeEnd="patchSelectionChangeEnd"
      />
    </div>
    <edit-patch-installation ref="editPatchInstallation" @submitEnd="submitEnd"></edit-patch-installation>
    <scan-term ref="scanTerm" :group-tree-data="treeData" @submit="scanTermSubmit"></scan-term>
  </div>
</template>

<script>
import { termStatusIconFormatterByStore } from '@/utils/formatter'
import { getTermPatchScanPage, getPatchInfoPage, availableTerm, installPatch } from '@/api/assets/patchManage/termModel'
import ScanTerm from './scanTerm.vue';
import EditPatchInstallation from '@/views/assets/patchManage/patchStrategy/patchInstallationStrategy/edit';
// import { refreshPage } from '@/utils';

export default {
  name: 'TermModel',
  components: { ScanTerm, EditPatchInstallation },
  data() {
    return {
      colModel1: [
        { prop: 'termName', label: this.$t('pages.temNameTermId'), width: '150', iconFormatter: this.termIconFormatter, formatter: this.termNameTermIdFormatter },
        // { prop: 'systemLevel', label: this.$t('pages.safetyLevel'), sort: true, width: '100', formatter: this.systemLevelFormatter },
        { prop: 'systemVersion', label: this.$t('pages.operatingSystem'), sort: true, width: '200' },
        { prop: 'totalInstall', label: this.$t('pages.alreadyInstalled'), sort: true, sortOriginal: true, width: '85' },
        { prop: 'noInstall', label: this.$t('pages.notInstalled'), width: '85' },
        { prop: 'totalPatch', label: this.$t('pages.totalNumberOfPatches'), sort: true, sortOriginal: true, width: '105' },
        { prop: 'latelyTime', label: this.$t('pages.scanTime'), sort: true, width: '150' },
        { prop: 'scanState', label: 'scanStatus', width: '150', sort: true, sortOriginal: true, formatter: this.scanStateFormatter },
        { prop: 'termVersion', label: 'terminalVersion', width: '120' }
        // { prop: 'errMsg', label: this.$t('pages.scanResults'), width: '150' }
      ],
      colModel2: [
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100', sort: true, formatter: this.patchKbFormatter },
        { prop: 'patchTitle', label: this.$t('pages.patchTitle'), width: '200' },
        { prop: 'patchLevel', label: this.$t('pages.patchLevel'), width: '80', sort: true, formatter: this.patchLevelFormat },
        { prop: 'downloadState', label: this.$t('pages.patchStatus'), width: '100', formatter: this.downloadStateFormat },
        { prop: 'state', label: this.$t('pages.executionStatus'), width: '100', formatter: this.stateFormat },
        { prop: 'patchSize', label: 'size', width: '80', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'patchDescribe', label: this.$t('pages.patchDescription'), width: '200' },
        { prop: 'msId', label: this.$t('pages.microsoftAnnouncementNo'), width: '100' }
        // { prop: 'installResult', label: '安装详情', width: '100' }
      ],
      checkAll: true,
      treeData: [],
      query1: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        searchType: 0,
        searchContent: null,
        levelList: ['1', '2', '3'],
        sortName: undefined
      },
      query2: { // 查询条件
        page: 1,
        termId: null,
        searchType: 0,
        searchContent: null,
        termIdList: [],
        guidList: [],
        deptId: null,
        includeTermInfo: true
      },
      installTip: false,
      // scanButton: true, // 选中部门
      patchList: [], // 补丁列表
      scanTermHandle: undefined, // 扫描终端处理
      installHandle: undefined, // 安装补丁处理
      termTypeOptions: [],
      typeOptions: [],
      showTree: true,
      deleteable: false,
      systemLevelMap: {
        1: this.$t('pages.highRiskTerminal'),
        2: this.$t('pages.vulnerableTerminal'),
        3: this.$t('pages.healthTerminal')
      },
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      // 0:未安装 1：安装成功 2：安装成功重启生效（废弃） 3：安装失败
      // 4：下载补丁文件失败 5：待安装（废弃） 6、正在安装 7、已安装 8、终端获取文件的md5值与策略下发的md5值不同
      stateMap: {
        0: this.$t('pages.notInstalled'),
        1: this.$t('pages.installSuccessful'),
        // 2: this.$t('pages.installSuccessful2'),
        3: this.$t('pages.installFailed'),
        4: this.$t('pages.installFailed1'),
        // 5: this.$t('pages.toBeInstall'),
        6: this.$t('pages.installing'),
        7: this.$t('pages.alreadyInstalled'),
        8: this.$t('pages.scanFailed1')
      },
      patchLevelMap: {
        0: this.$t('pages.undefinedLevel'),
        1: this.$t('pages.lowLevel'),
        2: this.$t('pages.mediumPatch'),
        3: this.$t('pages.importanceLevel'),
        4: this.$t('pages.severityLevel')
      },
      scanStateMap: {
        0: this.$t('pages.notScanned'),
        1: this.$t('pages.toBeScanned'),
        2: this.$t('pages.startScan'),
        3: this.$t('pages.scanning'),
        4: this.$t('pages.scanSuccessful'),
        5: this.$t('pages.scanFailed3'),
        6: this.$t('pages.scanFailed'),
        7: this.$t('pages.scanFailed1'),
        8: this.$t('pages.scanFailed2'),
        9: this.$t('pages.scanFailed9')
      },
      systemByteMap: {
        0: 'x86',
        1: 'x64'
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['termList']
    },
    editPatchInstallation() {
      return this.$refs['editPatchInstallation']
    }
  },
  watch: {
    '$store.state.commonData.deptTree'() {
      this.initGroupTreeNode()
    },
    'query1.searchType'(newValue, oldValue) {
      this.query1.searchContent = null
      if (newValue == 0) {
        this.query1.searchContent = null
      }
      if (newValue == 3) {
        this.termTypeOptions = [
          { value: 0, label: this.$t('pages.terminalStatus2') },
          { value: 4, label: this.$t('pages.terminalStatus3') }
        ]
      }
    },
    'query2.searchType'(newValue, oldValue) {
      this.query2.searchContent = null
      if (newValue == 6) {
        this.typeOptions = [
          { value: 0, label: this.$t('pages.notDownloaded') },
          { value: 1, label: this.$t('pages.toBeDownloaded') },
          { value: 2, label: this.$t('pages.downloading') },
          // { value: 3, label: this.$t('pages.downloadFailed') },
          { value: 4, label: this.$t('pages.downloading1') },
          // { value: 5, label: this.$t('pages.distributingFtp') },
          { value: 6, label: this.$t('pages.downloadFailed') },
          { value: 7, label: this.$t('pages.downloaded') }
        ]
      } else if (newValue == 7) {
        this.typeOptions = [
          { value: 0, label: this.$t('pages.notInstalled') },
          { value: 1, label: this.$t('pages.installSuccessful') },
          // { value: 2, label: this.$t('pages.installSuccessful2') },
          { value: 3, label: this.$t('pages.installFailed') },
          { value: 4, label: this.$t('pages.installFailed1') },
          // { value: 5, label: this.$t('pages.toBeInstall') },
          { value: 6, label: this.$t('pages.installing') },
          { value: 7, label: this.$t('pages.alreadyInstalled') },
          { value: 8, label: this.$t('pages.scanFailed1') }
        ]
      } else if (newValue == 9) {
        this.typeOptions = [
          { value: 4, label: this.$t('pages.severityLevel') },
          { value: 3, label: this.$t('pages.importanceLevel') },
          { value: 2, label: this.$t('pages.mediumPatch') },
          { value: 1, label: this.$t('pages.lowLevel') },
          { value: 0, label: this.$t('pages.undefinedLevel') }
        ]
      }
    },
    'query2.termIdList'(newValue, oldValue) {
      // this.scanButton = false
    },
    'query2.deptId'(newValue, oldValue) {
      // this.scanButton = false
    }
  },
  created() {
    this.initGroupTreeNode()
  },
  beforeDestroy() {
    // 在销毁前取消订阅，否则可能会有提示语
    this.closeScanHandle()
    this.closeInstallHandle()
    console.log('销毁终端模式beforeDestroy')
  },
  methods: {
    initGroupTreeNode: function() {
      const treeNode = JSON.parse(JSON.stringify(this.$store.getters.deptTree))
      // // 分组，不允许选择公司根节点
      // if (Array.isArray(treeNode) && treeNode.length === 1 && treeNode[0].dataId == 0) {
      //   this.groupTreeSelectData = treeNode[0].children
      // } else {
      //   this.groupTreeSelectData = treeNode
      // }
      console.log('initGroupTreeNode', treeNode)
      this.treeData = treeNode
    },
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        table.setCurrentRow(null)
        this.resetSelect()
      })
    },
    resetSelect() {
      this.query2.termId = null
      this.$refs.patchList.clearRowData()
      this.$refs.patchList.clearPageData()
    },
    handleCheckAllChange(val) {
      this.query1.levelList = val ? ['1', '2', '3'] : []
    },
    handleCheckedCitiesChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === Object.keys(this.systemLevelMap).length
    },
    getVersionNumber(str) {
      const versionPattern = /[0-9]+\.[0-9]+/;
      const match = str.match(versionPattern);
      return match ? match[0] : null;
    },
    rowClick(rowData, column, event) {
      console.log('current term version', Number(this.getVersionNumber(rowData.termVersion)), Number(this.getVersionNumber(rowData.termVersion)) > 3.55)
      if (rowData) {
        this.query2.termId = rowData.termId
        this.query2.termIdList = [rowData.termId]
        this.query2.deptId = null
        this.$refs.patchList.execRowDataApi(this.query2)
      } else {
        this.resetSelect()
      }
      const enableVersion = Number(this.getVersionNumber(rowData.termVersion)) < 3.55
      this.installTip = enableVersion
    },
    rowDataApi1: function(option) {
      const searchQuery = Object.assign({}, this.query1, option)
      console.log('option', searchQuery)
      return getTermPatchScanPage(searchQuery)
    },
    rowDataApi2: function(option) {
      console.log('rowDataApi2 option', option)
      const searchQuery = Object.assign({}, this.query2, option, { guidList: [] })
      console.log('rowDataApi2', searchQuery)
      return getPatchInfoPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
      this.patchList = rowDatas
      this.query2.termIdList = rowDatas.map(data => data.termId)
      console.log('终端', this.$refs['termList'].getSelectedKeys())
    },
    patchSelectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
      console.log('patchSelectionChangeEnd', rowDatas.map(r => r.guid))
      this.patchList = rowDatas
      this.query2.guidList = rowDatas.map(r => r.guid)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query1.objectType = checkedNode.type
        this.query1.objectId = checkedNode.dataId
        console.log('this.query1.objectType', this.query1.objectType)
        console.log('this.query1.objectId', this.query1.objectId)
        if (this.query1.objectType == 1) {
          // 该类型为终端
          // 选中终端时，默认允许设置当前终端扫描
          const termId = checkedNode.dataId
          console.log('strategyTargetNodeChange', typeof termId)
          this.query2.termId = Number(termId)
          this.query2.termIdList = [Number(termId)]
          this.query2.deptId = null
          this.availableTerm()
        }
        if (this.query1.objectType == 3) {
          // 该类型为部门
          const deptId = checkedNode.dataId
          console.log('选中部门：', deptId)
          this.query2.deptId = deptId
          this.query2.termIdList = []
        }
      } else {
        this.query1.objectType = undefined
        this.query1.objectId = undefined
        console.log('清空termIdList')
        this.query2.termIdList = []
      }

      this.gridTable.execRowDataApi()
    },
    handleFilter() {
      this.query1.page = 1
      this.gridTable.execRowDataApi(this.query1)
    },
    handleFilter2() {
      this.query2.page = 1
      this.$refs.patchList.execRowDataApi(this.query2)
    },
    refresh() {
      // TRDLP-15853【补丁管理】补丁管理-终端模式，建议添加一个刷新按钮
      // 要求刷新保留在当前页，不回到首页
      this.$refs.patchList.execRowDataApi()
    },
    closeScanHandle() {
      if (this.scanTermHandle !== undefined) {
        // 原扫描终端的订阅socket未关闭时，将它关闭
        this.scanTermHandle.close()
        this.scanTermHandle = undefined
      }
    },
    closeInstallHandle() {
      if (this.installHandle !== undefined) {
        // 原扫描终端的订阅socket未关闭时，将它关闭
        this.installHandle.close()
        this.installHandle = undefined
      }
    },
    availableTerm() {
      availableTerm(this.query2).then(res => {
        console.log('availableTerm', res)
      })
    },
    getSelectTermIds() {
      const datas = this.$refs.termList.getSelectedDatas()
      return datas.map(item => {
        return item.termId
      })
    },
    scanTerm() {
      const termIds = this.getSelectTermIds();
      console.log('scanTerm', termIds)
      this.$refs['scanTerm'].show(termIds)
      // 历史版本直接扫描
      // this.closeScanHandle()
      // let termId;
      // if (this.query2.termId != null) {
      //   termId = this.query2.termId
      //   this.query2.termIdList = [termId]
      // }
      // termId = this.query2.termIdList[0]
      // console.log('扫描开启', this.query2.termIdList)
      // const that = this
      // scanTerm(this.query2).then(res => {
      //   console.log('扫描开启订阅')
      //   this.$socket.subscribeToUser('scanResult', '' + 1, (respond, handle) => {
      //     that.scanTermHandle = handle
      //     console.log('respond.data', respond.data)
      //     if (that.query2.termIdList.length > 0) {
      //       that.$message({
      //         title: this.$t('text.prompt'),
      //         message: this.$t('pages.patchModelMsg1'),
      //         type: 'success',
      //         duration: 2000
      //       })
      //     } else {
      //       that.$message({
      //         title: this.$t('text.prompt'),
      //         message: this.$t('pages.notifyTermGroupToScan'),
      //         type: 'success',
      //         duration: 2000
      //       })
      //     }
      //
      //     setTimeout(() => {
      //       // 延时刷新：防止采集未把数据入库，控制台查询不到结果
      //       that.handleFilter()
      //     }, 3000)
      //     that.closeScanHandle()
      //   }, false)
      // })
    },
    scanTermSubmit() {
      setTimeout(() => {
        // 延时刷新：防止采集未把数据入库，控制台查询不到结果
        this.handleFilter()
      }, 3000)
    },
    installPatch() {
      this.closeInstallHandle()
      let termId;
      if (this.query2.termId != null) {
        termId = this.query2.termId
        this.query2.termIdList = [termId]
      }
      termId = this.query2.termIdList[0]
      console.log('安装补丁的终端', this.query2.termIdList)
      const updateIds = this.getSelectUpdateIds()
      this.query2.guidList = updateIds

      const datas = this.$refs.patchList.getSelectedDatas()
      console.log('选中的补丁', datas)
      // if (datas.length == 1) {
      //   // 下载状态0：未收集完整1：数据已收集完成2：正在下载3：下载失败4：已下载到-Server5：正在分发ftp6：分发ftp失败7：FTP分发完成
      //   const downloading = datas.filter(item => [2, 4, 5].includes(item.downloadState));
      //   if (downloading.length == 1) {
      //     this.$message({
      //       title: this.$t('text.prompt'),
      //       message: '补丁未下载完成，无法安装补丁',
      //       type: 'success',
      //       duration: 2000
      //     })
      //     return
      //   }
      // }
      // 是否已安装 0:未安装 1:安装成功 2:安装成功，重启后生效 3:安装失败 4:下载补丁包失败 5:待安装 6:正在安装 7:已安装
      const successInstall = datas.filter(item => [1, 2, 7].includes(item.state));
      let msg = ''
      if (successInstall.length > 0) {
        // 选中的补丁存在安装成功的终端，是否重新安装？
        msg = this.$t('pages.patchInstallAgain')
      } else {
        // 确定要安装选择的补丁吗?
        msg = this.$t('pages.patchModelMsg2')
      }

      if (this.validCanSubmit()) {
        this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
          const that = this
          installPatch(this.query2).then(res => {
            // 清理过滤数据
            that.query2.guidList = []
            console.log('安装补丁开启订阅')
            this.$socket.subscribeToUser('installResult', '' + 1, (respond, handle) => {
              that.installHandle = handle
              console.log('安装补丁', respond.data)
              // 已通知终端进行安装
              that.$message({
                title: this.$t('text.prompt'),
                message: this.$t('pages.patchModelMsg3'),
                type: 'success',
                duration: 2000
              })
              // 终端返回数据时，采集还未对数据入库，所以延时刷新列表
              setTimeout(() => {
                // that.handleFilter2()
                // 安装完成，测试要求保留在当前页面
                that.$refs.patchList.execRowDataApi()
              }, 3000)
              that.closeInstallHandle()
            }, false)
          }).catch(() => {
            // 清理过滤数据
            that.query2.guidList = []
          })
        }).catch(() => {
          // 清理过滤数据
          this.query2.guidList = []
        })
      }
    },
    handleInstallStrategy() {
      const data = {
        objectIds: [this.query2.termId],
        patchList: this.patchList
      }
      this.editPatchInstallation.handleTermModel(data)
    },
    submitEnd(action) {
      if (action == 'cancel') {
        return
      }
      this.patchList = this.patchList.splice(0, this.patchList.length)
      this.query2.guidList.splice(0, this.patchList.length)
      this.handleFilter2()
      if (action === 'create') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }
      if (action === 'update') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }
    },
    // installPatch() {
    //   if (this.validCanSubmit()) {
    //     this.$confirmBox(this.$t('pages.patchModelMsg2'), this.$t('text.prompt')).then(() => {
    //       const updateIds = this.getSelectUpdateIds()
    //       const config = {
    //         termId: this.query2.termId,
    //         updateIds: updateIds,
    //         state: 2
    //       }
    //       addPatchConfig(config).then(res => {
    //         this.$message({
    //           title: this.$t('text.prompt'),
    //           message: this.$t('pages.patchModelMsg3'),
    //           type: 'success',
    //           duration: 2000
    //         })
    //       })
    //     }).catch(() => {})
    //   }
    // },
    validCanSubmit() {
      if (this.query2.termId == null) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.termModelMsg'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.$refs.patchList.getSelectedDatas() == null || this.$refs.patchList.getSelectedDatas().length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.pleaseTickContent', { content: this.$t('pages.patch') }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    getSelectUpdateIds() {
      const datas = this.$refs.patchList.getSelectedDatas()
      return datas.map(item => {
        return item.guid
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    patchKbFormatter: function(row, data) {
      return data
    },
    systemLevelFormatter: function(row, data) {
      return this.systemLevelMap[data]
    },
    systemVersionFormatter: function(row, data) {
      return this.systemByteMap[data]
    },
    downloadStateFormat: function(row, data) {
      return this.downloadStateMap[data]
    },
    stateFormat: function(row, data) {
      let status = this.stateMap[data]
      if (row.installResult) {
        status = status + '(' + row.installResult + ')'
      }
      return status
    },
    patchLevelFormat: function(row, data) {
      return this.patchLevelMap[data]
    },
    sizeFormatter: function(row, data) {
      return this.convertSize(data)
    },
    convertSize: function(size) {
      if (!size) {
        return '0 Bytes';
      }
      var sizeNames = [' Bytes', ' KB', ' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
      var i = Math.floor(Math.log(size) / Math.log(1024));
      var p = (i > 0) ? 2 : 1;
      if (p == 1) {
        return 1 + sizeNames[1];
      } else {
        return (size / Math.pow(1024, Math.floor(i))).toFixed(p) + sizeNames[i];
      }
    },
    scanStateFormatter(row, data) {
      let status = this.scanStateMap[data]
      // 扫描失败错误码
      if (data == '6') {
        // 采集添加错误码 1:简易信息入库失败 2:获取终端系统语言失败
        if (row.errMsg == 1) {
          // '简易信息入库失败'
          row.errMsg = this.$t('pages.scanStateMsg1')
        }
        if (row.errMsg == 2) {
          // '获取终端系统语言失败'
          row.errMsg = this.$t('pages.scanStateMsg2')
        }
      }
      if (row.errMsg) {
        status = status + '(' + row.errMsg + ')'
      }
      return status
    },
    termIconFormatter(row, data) {
      return termStatusIconFormatterByStore(row.termType, row.termId)
    },
    termNameTermIdFormatter(row, data) {
      if (row.termName === undefined) {
        return row.termId
      }
      return row.termName + '(' + row.termId + ')'
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-container {
    padding-right: 5px;
  }
  .table-container {
    padding-right: 10px;
    overflow: auto;
  }
</style>
