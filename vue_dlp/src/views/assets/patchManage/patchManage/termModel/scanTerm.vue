<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="扫描终端"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="beforeClose"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('pages.scanTerminal') }}
        <el-tooltip effect="dark" placement="top">
          <div slot="content">{{ $t('pages.scanTerminalTitleTip') }}</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="20px">
        <el-row >
          <el-col :span="span1">
            <FormItem v-if="temp.dataIds && temp.dataIds.length > 0">
              <el-radio v-model="temp.type" :label="1" @change="typeChangeFunc">{{ $t('pages.scanTerminalTermColumn') }}</el-radio>
            </FormItem>
          </el-col>
          <el-col :span="span2">
            <FormItem label-width="5px">
              <el-radio v-model="temp.type" :label="2" @change="typeChangeFunc">{{ $t('pages.scanTerminalDeptColumn') }}</el-radio>
            </FormItem>
          </el-col>
          <el-col :span="9">
            <FormItem v-if="temp.type===2" label-width="0px" prop="groupId">
              <tree-select
                ref="groupTree"
                :disabled="temp.type !== 2"
                :data="groupTreeData"
                node-key="dataId"
                multiple
                check-strictly
                clearable
                is-filter
                :width="290"
                :checked-keys="groupCheckedKeys"
                @change="groupIdSelectChange"
              />
            </FormItem>
          </el-col>
        </el-row>
        <FormItem v-if="temp.type===2" label-width="0px">
          <el-checkbox v-model="multiSelect2">{{ $t('pages.terminal_text26') }}</el-checkbox>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <span>
                <i18n path="pages.selectTerminalTaskTips">
                  <br slot="br"/>
                </i18n>
              </span>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <grid-table
          v-show="temp.type===1"
          ref="termTable"
          :col-model="terminalColModel"
          :row-data-api="rowDataApi"
          :height="200"
          :multi-select="multiSelect"
          :is-saved-selected="multiSelect"
          row-key="id"
          :checked-row-keys="checkedRowKeys"
          :after-load="afterLoad"
          pager-small
          @select="selectData"
        />
        <grid-table
          v-show="temp.type===2"
          ref="termTable2"
          :col-model="terminalColModel"
          :row-data-api="rowDataApi"
          :height="200"
          :multi-select="multiSelect2"
          :is-saved-selected="multiSelect2"
          row-key="id"
          :checked-row-keys="checkedRowKeys2"
          :after-load="afterLoad"
          pager-small
          @select="selectData"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="scanTerm">{{ $t('button.confirm') }}</el-button>
        <el-button @click="hide">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { scanTerm, availableScanTerm } from '@/api/assets/patchManage/termModel';

export default {
  name: 'ScanTerm',
  props: {
    groupTreeData: { type: Array, default() { return [] } }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      multiSelect: false,
      multiSelect2: false,
      defaultTemp: {
        groupId: [],
        dataIds: []
      },
      temp: {
        type: 2,
        groupId: [],
        dataIds: []
      },
      groupCheckedKeys: [], // 分组选中数据
      checkedRowKeys: [],
      checkedRowKeys2: [],
      terminalColModel: [
        { prop: 'name', label: 'terminalName', width: '150', sort: 'custom', fixed: true },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'computerName', label: 'computerName', sort: 'custom', width: '150' },
        { prop: 'mainIp', label: 'IP', width: '150', sort: 'custom' },
        { prop: 'mainMac', label: 'macAddr', width: '150', sort: 'custom' }
      ],
      rules: {
      }
    }
  },
  computed: {
    span1() {
      if (this.isEnglish() && this.temp.dataIds && this.temp.dataIds.length > 0) {
        return 24;
      }
      return 7;
    },
    span2() {
      return this.isEnglish() ? 12 : 8;
    }
  },
  watch: {},
  created() {
  },
  methods: {
    resetTemp() {
      this.temp = Object.assign(this.temp, this.defaultTemp)
      this.groupCheckedKeys = []
      this.checkedRowKeys = []
      this.checkedRowKeys2 = []
      if (this.temp.type === 2) {
        // 当为部门选项时，才能进行清理，否则会报错
        this.$refs['groupTree'].clearSelectedKeys()
        this.$refs['termTable2'].clearRowData()
        this.$refs['termTable2'].clearPageData()
      }
      if (this.temp.type === 1) {
        this.$refs['termTable'].clearRowData()
        this.$refs['termTable'].clearPageData()
      }
    },
    handleDrag() {
    },
    afterLoad(rowData, table) {
      if (this.temp.type === 1) {
        this.$nextTick(() => {
          if (rowData.length > 0) {
            this.$refs['termTable'].checkSelectedRows(this.checkedRowKeys);
            // rowData.forEach(r => {
            //   console.log('this.checkedRowKeys.indexOf(r[\'id\']) >= 0', r['id'], this.checkedRowKeys.indexOf(r['id']) >= 0)
            //   if (this.checkedRowKeys.indexOf(r['id']) >= 0) {
            //     this.$refs['termTable'].toggleRowSelection(r, true)
            //   }
            // })
          }
        })
      }
      if (this.temp.type === 2) {
        this.$nextTick(() => {
          if (rowData.length > 0) {
            this.$refs['termTable'].checkSelectedRows(this.checkedRowKeys2);
          }
        })
      }
    },
    selectData(selection, row) {
      if (this.temp.type === 1) {
        this.checkedRowKeys = selection.map(s => s.id)
      }
      if (this.temp.type === 2) {
        this.checkedRowKeys2 = selection.map(s => s.id)
      }
    },
    show(dataIds) {
      this.dialogVisible = true
      this.multiSelect = false
      this.multiSelect2 = false
      Object.assign(this.temp, {
        type: 2,
        groupId: [],
        dataIds: dataIds
      })
      if (this.temp.dataIds && this.temp.dataIds.length > 0) {
        this.temp.type = 1
        this.multiSelect = true
        this.$nextTick(() => {
          this.$refs['termTable'].execRowDataApi()
          this.$nextTick(() => {
            this.checkedRowKeys.splice(0, this.checkedRowKeys.length, ...this.temp.dataIds)
            console.log('this.checkedRowKeys', this.checkedRowKeys)
          })
        })
      }
    },
    beforeClose() {
      this.resetTemp()
      this.dialogVisible = false
      this.submitting = false
      return true
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
      this.submitting = false
    },
    rowDataApi: function(option) {
      const vo = { sortName: option.sortName, sortOrder: option.sortOrder, page: option.page, limit: option.limit }
      if (this.temp.type === 2) {
        vo.deptIds = this.temp.groupId.length == 0 ? [-1] : this.temp.groupId
      } else {
        vo.termIdList = this.temp.dataIds
      }
      return availableScanTerm(vo)
    },
    typeChangeFunc(val) {
      if (this.temp.type === 1) {
        this.$refs['termTable'].execRowDataApi()
      }
      if (this.temp.type === 2) {
        this.$refs['termTable2'].execRowDataApi()
      }
    },
    groupIdSelectChange(data, node, vm) {
      console.log('groupIdSelectChange', data)
      if (data.length === 0) {
        this.temp.groupId = [-1]
      } else {
        this.temp.groupId = data
        this.groupCheckedKeys.splice(0, this.groupCheckedKeys.length, ...data)
      }
      this.$refs['termTable2'].execRowDataApi()
    },
    closeScanHandle() {
      if (this.scanTermHandle !== undefined) {
        // 原扫描终端的订阅socket未关闭时，将它关闭
        this.scanTermHandle.close()
        this.scanTermHandle = undefined
      }
    },
    scanTerm() {
      console.log('scanTerm', this.submitting)
      if (this.submitting) {
        return
      }
      this.submitting = true
      this.closeScanHandle()
      const vo = {}
      // 终端
      if (this.temp.type === 1) {
        vo.termIdList = this.$refs['termTable'].getIds()
        console.log('scanTerm', this.multiSelect, this.temp.type, this.$refs['termTable'].getIds(), vo.termIdList.length)
        // 未有符合扫描条件的终端
        if (vo.termIdList.length === 0) {
          this.$message({
            message: this.$t('pages.scanTerminalMsg0'),
            type: 'error',
            duration: 2000
          })
          this.submitting = false
          return false
        }
      }
      // 部门
      if (this.temp.type === 2) {
        if (this.temp.groupId == null) {
          this.$message({
            message: this.$t('pages.pleaseSelectAtLeastOneDepartment', { content: this.$t('pages.dept') }),
            type: 'error',
            duration: 2000
          })
          this.submitting = false
          return false
        }
        vo.termIdList = this.$refs['termTable2'].getIds()
        console.log('vo.termIdList', vo.termIdList)
        if (this.multiSelect2) {
          vo.deptId = undefined
          const selectIds = this.$refs['termTable2'].getSelectedKeys()
          vo.termIdList = selectIds.length > 0 ? selectIds : Array.of(0)
        }
        if (vo.termIdList.length === 0) {
          // 未有符合扫描条件的终端
          this.$message({
            message: this.$t('pages.scanTerminalMsg0'),
            type: 'error',
            duration: 2000
          })
          this.submitting = false
          return false
        }
      }
      const that = this
      scanTerm(vo).then(res => {
        this.$socket.subscribeToUser('scanResult', '' + 1, (respond, handle) => {
          that.scanTermHandle = handle
          if (vo.termIdList.length > 0) {
            that.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.patchModelMsg1'),
              type: 'success',
              duration: 2000
            })
          } else {
            that.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.notifyTermGroupToScan'),
              type: 'success',
              duration: 2000
            })
          }
          that.$emit('submit')
          that.closeScanHandle()
          this.submitting = false
        }, false)
        that.hide()
      }).catch(() => {
        this.submitting = false
      })
    }
  }
}
</script>
