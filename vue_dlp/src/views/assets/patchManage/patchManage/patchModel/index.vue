<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <label>{{ $t('pages.findCategory') }}：</label>
        <el-select v-model="query2.searchType" style="width: 150px;">
          <el-option :label="$t('pages.allPatches')" :value="0"></el-option>
          <el-option :label="$t('pages.patchID')" :value="3"></el-option>
          <el-option :label="$t('pages.patchLevel')" :value="9"></el-option>
          <el-option :label="$t('pages.patchStatus')" :value="6"></el-option>
          <!-- <el-option :label="$t('pages.patchTitle')" :value="4"></el-option> -->
          <!--          <el-option :label="$t('pages.patchDescription')" :value="8"></el-option>-->
          <el-option :label="$t('pages.microsoftAnnouncementNo')" :value="5"></el-option>
        </el-select>
        <el-input v-if="[0,3,4,5,8].indexOf(query2.searchType) != -1" v-model="query2.searchContent" v-trim clearable :disabled="query2.searchType==0" style="width: 150px;" />
        <el-select v-if="[6,7,9].indexOf(query2.searchType) != -1" v-model="query2.searchContent" style="width: 150px;">
          <el-option :label="$t('pages.all')" :value="null"/>
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>

        <el-button type="primary" size="mini" @click="handleFilter2">{{ $t('pages.findPatches') }}</el-button>
      </div>
      <grid-table
        ref="patchList"
        :multi-select="false"
        :after-load="afterLoad"
        :show-pager="true"
        :autoload="true"
        :col-model="colModel2"
        :row-data-api="rowDataApi2"
        style="min-height: 140px; margin-bottom: 10px;"
        @row-click="rowClick"
      />
      <div class="toolbar">
        <label>{{ $t('pages.findCategory') }}：</label>
        <el-select v-model="query1.searchType" style="width: 120px;">
          <el-option :label="$t('pages.allTerminals')" :value="0"></el-option>
          <el-option :label="$t('table.terminalName')" :value="1"></el-option>
          <el-option :label="$t('pages.operatingSystem')" :value="2"></el-option>
          <el-option :label="$t('pages.terminalStatus')" :value="3"></el-option>
        </el-select>
        <el-input v-if="[0,1,2].indexOf(query1.searchType) != -1" v-model="query1.searchContent" v-trim :disabled="query1.searchType==0" style="width: 150px;" clearable/>
        <el-select v-if="[3].indexOf(query1.searchType) != -1" v-model="query1.searchContent" style="width: 150px;">
          <el-option :label="$t('pages.all')" :value="null"/>
          <el-option v-for="item in termTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button type="primary" size="mini" :disabled="query1.guid == null" @click="handleFilter" >{{ $t('pages.findTerminal') }}</el-button>
        <el-button type="primary" size="mini" :disabled="query2.termIdList.length < 1" @click="scanTerm">{{ $t('pages.scanTerminal') }}</el-button>
        <el-button type="primary" :disabled="query2.termIdList.length == 0" size="mini" @click="handleInstallStrategy">{{ $t('text.addInfo', { info: $t('route.installStrategy')}) }}</el-button>
        <el-button type="primary" :disabled="query2.termIdList.length == 0 || query1.guid == null || installTip" size="mini" @click="installPatch">{{ $t('pages.installPatches') }}
          <el-tooltip v-if="installTip" effect="dark" placement="bottom-end">
            <div slot="content">{{ $t('pages.patchInstallTip') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <el-button type="primary" size="mini" :disabled="query1.guid == null" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <!--        <el-button type="primary" size="mini" @click="uninstallPatch">{{ $t('pages.cancelInstallation') }}</el-button>-->
      </div>
      <grid-table
        ref="termList"
        :autoload="false"
        :col-model="colModel1"
        :row-data-api="rowDataApi1"
        style="min-height: 140px; margin-bottom: 2px;"
        @selectionChangeEnd="termSelectionChangeEnd"
      />
    </div>
    <edit-patch-installation ref="editPatchInstallation" @submitEnd="submitEnd"></edit-patch-installation>
  </div>
</template>

<script>
import {
  getTermPatchScanPage,
  getPatchInfoPage,
  scanTerm,
  installPatch
} from '@/api/assets/patchManage/termModel'
import { termStatusIconFormatterByStore } from '@/utils/formatter';
import EditPatchInstallation from '@/views/assets/patchManage/patchStrategy/patchInstallationStrategy/edit';

export default {
  name: 'PatchModel',
  components: { EditPatchInstallation },
  data() {
    return {
      colModel1: [
        { prop: 'termName', label: this.$t('pages.temNameTermId'), width: '150', iconFormatter: this.termIconFormatter, formatter: this.termNameTermIdFormatter },
        // { prop: 'systemLevel', label: this.$t('pages.safetyLevel'), width: '100', sort: true, formatter: this.systemLevelFormatter },
        { prop: 'systemVersion', label: this.$t('pages.operatingSystem'), width: '200', sort: true },
        { prop: 'totalInstall', label: this.$t('pages.alreadyInstalled'), width: '85', sort: true, sortOriginal: true },
        { prop: 'noInstall', label: this.$t('pages.notInstalled'), width: '85' },
        { prop: 'totalPatch', label: this.$t('pages.totalNumberOfPatches'), width: '105', sort: true, sortOriginal: true },
        { prop: 'latelyTime', label: this.$t('pages.scanTime'), width: '150', sort: true },
        { prop: 'scanState', label: 'scanStatus', width: '150', sort: true, sortOriginal: true, formatter: this.scanStateFormatter },
        { prop: 'state', label: this.$t('pages.executionStatus'), width: '120', formatter: this.stateFormat },
        { prop: 'termVersion', label: 'terminalVersion', width: '120' }
        // { prop: 'installResult', label: '安装详情', width: '80' },

      ],
      colModel2: [
        { prop: 'patchKb', label: this.$t('pages.patchID'), width: '100', sort: true, formatter: this.patchKbFormatter },
        { prop: 'patchTitle', label: this.$t('pages.patchTitle'), width: '200' },
        { prop: 'patchLevel', label: this.$t('pages.patchLevel'), width: '100', sort: true, sortOriginal: true, formatter: this.patchLevelFormat },
        { prop: 'downloadState', label: this.$t('pages.patchStatus'), width: '100', formatter: this.downloadStateFormat },
        { prop: 'patchSize', label: 'size', width: '80', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'patchDescribe', label: this.$t('pages.patchDescription'), width: '200' },
        { prop: 'msId', label: this.$t('pages.microsoftAnnouncementNo'), width: '100' }
      ],
      query1: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        guid: null,
        includePatchResult: true,
        searchType: 0,
        searchContent: null
      },
      patchList: [],
      query2: { // 查询条件
        page: 1,
        includeTermInfo: false,
        searchType: 0,
        searchContent: null,
        termIdList: [],
        guidList: []
      },
      termTypeOptions: [],
      typeOptions: [],
      showTree: true,
      deleteable: false,
      installTip: false,
      systemLevelMap: {
        1: this.$t('pages.highRiskTerminal'),
        2: this.$t('pages.vulnerableTerminal'),
        3: this.$t('pages.healthTerminal')
      },
      downloadStateMap: {
        0: this.$t('pages.notDownloaded'),
        1: this.$t('pages.toBeDownloaded'),
        2: this.$t('pages.downloading'),
        3: this.$t('pages.downloadFailed'),
        4: this.$t('pages.downloading1'),
        5: this.$t('pages.downloading1'),
        6: this.$t('pages.downloadFailed'),
        7: this.$t('pages.downloaded')
      },
      scanStateMap: {
        0: this.$t('pages.notScanned'),
        1: this.$t('pages.toBeScanned'),
        2: this.$t('pages.startScan'),
        3: this.$t('pages.scanning'),
        4: this.$t('pages.scanSuccessful'),
        5: this.$t('pages.scanFailed3'),
        6: this.$t('pages.scanFailed'),
        7: this.$t('pages.scanFailed1'),
        8: this.$t('pages.scanFailed2'),
        9: this.$t('pages.scanFailed9')
      },
      // 0：未安装 1：安装成功
      stateMap: {
        0: this.$t('pages.notInstalled'),
        1: this.$t('pages.installSuccessful'),
        2: this.$t('pages.installSuccessful2'),
        3: this.$t('pages.installFailed'),
        4: this.$t('pages.installFailed1'),
        // 5: this.$t('pages.toBeInstall'),
        6: this.$t('pages.installing'),
        7: this.$t('pages.alreadyInstalled'),
        8: this.$t('pages.scanFailed1')
      },
      patchLevelMap: {
        0: this.$t('pages.undefinedLevel'),
        1: this.$t('pages.lowLevel'),
        2: this.$t('pages.mediumPatch'),
        3: this.$t('pages.importanceLevel'),
        4: this.$t('pages.severityLevel')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['termList']
    },
    editPatchInstallation() {
      return this.$refs['editPatchInstallation']
    }
  },
  watch: {
    'query1.searchType'(newValue, oldValue) {
      this.query1.searchContent = null
      if (newValue == 0) {
        this.query1.searchContent = null
      }
      if (newValue == 3) {
        this.termTypeOptions = [
          { value: 0, label: this.$t('pages.terminalStatus2') },
          { value: 4, label: this.$t('pages.terminalStatus3') }
        ]
      }
    },
    'query2.searchType'(newValue, oldValue) {
      this.query2.searchContent = null
      if (newValue == 6) {
        this.typeOptions = [
          { value: 0, label: this.$t('pages.notDownloaded') },
          { value: 1, label: this.$t('pages.toBeDownloaded') },
          { value: 2, label: this.$t('pages.downloading') },
          // { value: 3, label: this.$t('pages.downloadFailed') },
          { value: 4, label: this.$t('pages.downloading1') },
          // { value: 5, label: this.$t('pages.distributingFtp') },
          { value: 6, label: this.$t('pages.downloadFailed') },
          { value: 7, label: this.$t('pages.downloaded') }
        ]
      } else if (newValue == 7) {
        this.typeOptions = [
          { value: 0, label: this.$t('pages.notInstalled') },
          { value: 1, label: this.$t('pages.installSuccessful') },
          { value: 2, label: this.$t('pages.installSuccessful2') },
          { value: 3, label: this.$t('pages.installFailed') },
          { value: 4, label: this.$t('pages.scanFailed3') },
          // { value: 5, label: this.$t('pages.toBeInstall') },
          { value: 6, label: this.$t('pages.installing') },
          { value: 7, label: this.$t('pages.alreadyInstalled') },
          { value: 8, label: this.$t('pages.scanFailed1') }
        ]
      } else if (newValue == 9) {
        this.typeOptions = [
          { value: 4, label: this.$t('pages.severityLevel') },
          { value: 3, label: this.$t('pages.importanceLevel') },
          { value: 2, label: this.$t('pages.mediumPatch') },
          { value: 1, label: this.$t('pages.lowLevel') },
          { value: 0, label: this.$t('pages.undefinedLevel') }
        ]
      }
    }
  },
  created() {
  },
  beforeDestroy() {
    // 在销毁前取消订阅，否则可能会有提示语
    this.closeScanHandle()
    this.closeInstallHandle()
  },
  methods: {
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        table.setCurrentRow(null)
        this.resetSelect()
      })
    },
    resetSelect() {
      this.query1.guid = null
      this.$refs.termList.clearRowData()
      this.$refs.termList.clearPageData()
    },
    rowClick(rowData, column, event) {
      if (rowData) {
        this.query1.guid = rowData.guid
        console.log('rowClick rowData', rowData)
        // this.query2.guidList = [rowData.guid]
        console.log('this.query2.guidList', this.query2.guidList)
        this.patchList = Array.of(rowData)
        this.$refs.termList.execRowDataApi(this.query1)
      } else {
        this.resetSelect()
      }
    },
    rowDataApi1: function(option) {
      const searchQuery = Object.assign({}, this.query1, option)
      console.log('rowDataApi1', searchQuery)
      return getTermPatchScanPage(searchQuery)
    },
    rowDataApi2: function(option) {
      const searchQuery = Object.assign({}, this.query2, option)
      return getPatchInfoPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    getVersionNumber(str) {
      const versionPattern = /[0-9]+\.[0-9]+/;
      const match = str.match(versionPattern);
      return match ? match[0] : null;
    },
    termSelectionChangeEnd: function(val) {
      // 选中的终端中存在版本号<3.55的，显示提示
      const termList = val.filter(term => Number(this.getVersionNumber(term.termVersion)) < 3.55)
      this.installTip = termList.length > 0

      console.log('termSelectionChangeEnd', val)
      this.query2.termIdList = val.map(obj => obj.termId)
      console.log('this.query2.termIdList', this.query2.termIdList.length)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query1.objectType = checkedNode.type
        this.query1.objectId = checkedNode.dataId
      } else {
        this.query1.objectType = undefined
        this.query1.objectId = undefined
      }
      if (this.query1.guid != null) {
        this.gridTable.execRowDataApi()
      }
    },
    handleFilter() {
      this.query1.page = 1
      this.gridTable.execRowDataApi(this.query1)
    },
    refresh() {
      this.gridTable.execRowDataApi()
    },
    handleFilter2() {
      this.query2.page = 1
      this.$refs.patchList.execRowDataApi(this.query2)
    },
    closeScanHandle() {
      if (this.scanTermHandle !== undefined) {
        // 原扫描终端的订阅socket未关闭时，将它关闭
        this.scanTermHandle.close()
        this.scanTermHandle = undefined
      }
    },
    closeInstallHandle() {
      if (this.installHandle !== undefined) {
        // 原扫描终端的订阅socket未关闭时，将它关闭
        this.installHandle.close()
        this.installHandle = undefined
      }
    },
    scanTerm() {
      this.closeScanHandle()
      const that = this
      console.log('this.query2.termIdList', this.query2.termIdList)
      const termId = this.query2.termIdList[0]
      scanTerm(this.query2).then(res => {
        console.log('扫描开启订阅', termId)
        that.$socket.subscribeToUser('/scanResult', '' + 1, (respond, handle) => {
          that.scanTermHandle = handle
          console.log('扫描开启订阅结果', termId)
          console.log('respond.data', respond)
          that.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.patchModelMsg1'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            that.handleFilter()
          }, 3000)
          that.closeScanHandle()
        }, false)
        //
      })
    },
    installPatch() {
      this.closeInstallHandle()
      // 补丁点击时，将updateId 存储 this.query2.updateIdList
      this.query2.termIdList = this.getSelectTermIds()
      const guids = this.query1.guid
      this.query2.guidList = [guids]

      const datas = this.$refs.termList.getSelectedDatas()
      // 是否已安装 0:未安装 1:安装成功 2:安装成功，重启后生效 3:安装失败 4:下载补丁包失败 5:待安装 6:正在安装 7:已安装
      const successInstall = datas.filter(item => [1, 2, 7].includes(item.state));
      let msg = ''
      if (successInstall.length > 0) {
        msg = this.$t('pages.patchInstallAgain')
      } else {
        msg = this.$t('pages.patchModelMsg2')
      }
      console.log('this.query2.termIdList', msg)
      if (this.validCanSubmit()) {
        this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
          const that = this
          installPatch(this.query2).then(res => {
            console.log('安装补丁开启订阅')
            this.$socket.subscribeToUser('installResult', '' + 1, (respond, handle) => {
              that.installHandle = handle
              console.log('安装补丁', respond.data)
              that.$message({
                title: this.$t('text.prompt'),
                message: this.$t('pages.patchModelMsg3'),
                type: 'success',
                duration: 2000
              })
              // 清理过滤数据
              that.query2.termIdList = []
              that.query2.guidList = []
              setTimeout(() => {
                that.handleFilter()
              }, 3000)
              that.closeInstallHandle()
            }, false)
          }).finally(() => {
            // 清理过滤数据
            that.query2.termIdList = []
            that.query2.guidList = []
            that.gridTable.clearSelection();
          })
        }).catch(() => {})
      }
    },
    handleInstallStrategy() {
      const data = {
        objectIds: [...this.query2.termIdList],
        patchList: this.patchList
      }
      this.editPatchInstallation.handleTermModel(data)
    },
    submitEnd(action) {
      if (action == 'cancel') {
        return
      }
      this.patchList = this.patchList.splice(0, this.patchList.length)
      this.query2.guidList.splice(0, this.patchList.length)
      this.handleFilter()
      if (action === 'create') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }
      if (action === 'update') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }
    },
    validCanSubmit() {
      if (this.query2.guidList == null) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.patchModelMsg5'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.getSelectTermIds() == null || this.getSelectTermIds().length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.pleaseTickContent', { content: this.$t('pages.terminal') }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    getSelectTermIds() {
      const datas = this.$refs.termList.getSelectedDatas()
      return datas.map(item => {
        return item.termId
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    patchKbFormatter: function(row, data) {
      return data
    },
    systemLevelFormatter: function(row, data) {
      return this.systemLevelMap[data]
    },
    downloadStateFormat: function(row, data) {
      return this.downloadStateMap[data]
    },
    stateFormat: function(row, data) {
      let status = this.stateMap[data]
      if (row.installResult) {
        status = status + '(' + row.installResult + ')'
      }
      return status
      // return this.stateMap[data]
    },
    patchLevelFormat: function(row, data) {
      return this.patchLevelMap[data]
    },
    sizeFormatter: function(row, data) {
      return this.convertSize(data)
    },
    convertSize: function(size) {
      if (!size) {
        return '0 Bytes';
      }
      var sizeNames = [' Bytes', ' KB', ' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
      var i = Math.floor(Math.log(size) / Math.log(1024));
      var p = (i > 0) ? 2 : 1;
      if (p == 1) {
        return 1 + sizeNames[1];
      } else {
        return (size / Math.pow(1024, Math.floor(i))).toFixed(p) + sizeNames[i];
      }
    },
    scanStateFormatter: function(row, data) {
      let status = this.scanStateMap[data]
      // 扫描失败错误码
      if (data == '6') {
        // 采集添加错误码 1:简易信息入库失败 2:获取终端系统语言失败 用于提示用户
        if (row.errMsg == 1) {
          // '简易信息入库失败'
          row.errMsg = this.$t('pages.scanStateMsg1')
        }
        if (row.errMsg == 2) {
          // '获取终端系统语言失败'
          row.errMsg = this.$t('pages.scanStateMsg2')
        }
      }
      if (row.errMsg) {
        status = status + '(' + row.errMsg + ')'
      }
      return status
    },
    termNameTermIdFormatter(row, data) {
      if (row.termName === undefined) {
        return row.termId
      }
      return row.termName + '(' + row.termId + ')'
    },
    termIconFormatter(row, data) {
      return termStatusIconFormatterByStore(row.termType, row.termId)
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-container {
    padding-right: 5px;
  }
  .table-container {
    padding-right: 10px;
    overflow: auto;
  }
</style>
