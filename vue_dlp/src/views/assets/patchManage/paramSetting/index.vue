<template>
  <div class="app-container">
    <Form
      ref="dataForm"
      :model="temp"
      :hide-required-asterisk="true"
      :rules="rules"
      style="width: 700px;"
    >
      <el-card class="box-card" style="background-color: transparent;">
        <el-divider content-position="left">{{ $t('pages.termPatchDetectionStg') }}</el-divider>
        <FormItem label-width="130px" :label="$t('pages.patchDetectionCycle')" :extra-width="{ en: 65 }" prop="timeType">
          <el-select v-model="timeType" style="width: 180px" class="targetType" @change="timeChange">
            <el-option v-for="(item, index) in timeTypeArray" :key="index" :value="item.value" :label="item.label">{{ item.label }}</el-option>
          </el-select>
          <el-input-number v-if="timeType === -2" v-model="selfTime" :placeholder="$t('pages.inputIntervalDay')" :controls="false" :min="1" style="width: 180px" :max="365" @blur="selfTimeChange"></el-input-number>
          <span style="height: 32px; align-items: center; margin-right: 5px">
            <el-tooltip>
              <template slot="content">
                <i18n path="pages.patchDetectionCycleTip">
                  <br slot="br"/>
                </i18n>
              </template>
              <i class="el-icon-info"/>
            </el-tooltip>
          </span>
        </FormItem>
        <FormItem :label="$t('pages.patchAllowDownloadTime')" label-width="130px" prop="timeId">
          <el-select v-model="temp.timeId" :placeholder="$t('text.select')" >
            <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <link-button :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'" btn-style="margin: 1px 0 0;"/>
        </FormItem>

        <!--  <FormItem label-width="130px" label="终端安装次数">-->
        <!--    <el-input-number v-model="temp.termInstallNum" :controls="false" :min="1" :max="5" :precision="0" ></el-input-number>-->
        <!--  </FormItem>-->
        <!--  <FormItem label-width="130px" label="补丁下载路径">-->
        <!--    <el-select v-model="temp.savePath" style="width: 250px;">-->
        <!--      <el-option label="磁盘剩余空间最大磁盘（默认值）" value="#max#"></el-option>-->
        <!--      <el-option label="桌面" value="#desktop#"></el-option>-->
        <!--      <el-option label="用户临时目录" value="#usertemp#"></el-option>-->
        <!--      <el-option label="系统临时目录" value="#systemtemp# "></el-option>-->
        <!--    </el-select>-->
        <!--  </FormItem>-->
        <FormItem :label="$t('pages.patchDownloadPath')" style="margin-left: 15px">
          <span >
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltipStyle">
                {{ $t('pages.fileDetectionFilePathTip1') }}<br>
                <span v-for="item in filePathShortcutKeys" :key="item.path">
                  {{ item.label }}：{{ item.path }}{{ item.remark }}<br>
                </span>
                <!--                {{ $t('pages.fileDetectionFilePathTip2') }}<br>-->
                {{ $t('text.cantNullInfo', { info: $t('pages.path')}) }}<br>
                <!--                {{ $t('pages.fileDetectionFilePathTip3') }}<br>-->
                {{ $t('pages.fileDetectionFilePathMsg2') }}<br>
                {{ $t('pages.fileDetectionFilePathTip4') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </span>
          <el-button v-for="item in filePathShortcutKeys" :key="item.path" size="mini" @click="filePathShortcutKey(item.path)">{{ item.label }}</el-button>
        </FormItem>
        <FormItem prop="savePath">
          <el-input v-model="temp.savePath" :placeholder="$t('pages.userTempDirectory')" clearable maxlength="40" show-word-limit @input="filPathInputHandler"></el-input>
        </FormItem>
      </el-card>

      <!--      <el-card class="box-card" style="background-color: transparent;margin-top: 15px;">-->

      <!--        <el-divider content-position="left">终端自动安装补丁策略-->
      <!--          <el-tooltip>-->
      <!--            <template slot="content">-->
      <!--              <div>补丁检测周期设置关闭，限制无法配置自动安装补丁策略</div>-->
      <!--            </template>-->
      <!--            <i class="el-icon-info"/>-->
      <!--          </el-tooltip>-->
      <!--        </el-divider>-->

      <!--        <FormItem prop="autoPatchLevelList">-->
      <!--          <div style="padding: 10px 0 0 10px;">-->
      <!--            <el-checkbox v-model="temp.autoInstall" style="margin-right: 30px;" :true-label="1" :false-label="0" :disabled="temp.checkDay === 0" @change="handleAutoInstallChange">终端按照补丁等级自动安装补丁</el-checkbox>-->
      <!--            <el-checkbox-group v-model="temp.autoPatchLevelList" style="display: inline-block;" :disabled="temp.autoInstall === 0" @change="handlePatchLevelChange">-->
      <!--              <el-checkbox v-for="(key,value) in patchLevelMap" :key="key" :label="value">{{ key }}</el-checkbox>-->
      <!--            </el-checkbox-group>-->
      <!--          </div>-->
      <!--        </FormItem>-->
      <!--      </el-card>-->
      <!--      <el-card class="box-card" style="background-color: transparent;margin-top: 15px;">-->
      <!--        <el-divider content-position="left">终端安全等级定义</el-divider>-->
      <!--        <el-tabs ref="tabs" v-model="tabName" style="height: 240px;" @tab-click="tabClick">-->
      <!--          <el-tab-pane :label="$t('pages.highRiskTerminal')" name="highRiskTerminalTab" style="padding: 10px; overflow: auto;">-->
      <!--            <el-row style="padding-top: 15px">-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isCritical1" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.severityLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.critical1" :disabled="temp.isCritical1==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--              <el-col :span="1">&nbsp;</el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isImportant1" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.importanceLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.important1" :disabled="temp.isImportant1==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--            </el-row>-->
      <!--            <el-row style="padding-top: 5px">-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isMedia1" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.mediumPatch') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.media1" :disabled="temp.isMedia1==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--              <el-col :span="1">&nbsp;</el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isLow1" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.lowLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.low1" :disabled="temp.isLow1==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--            </el-row>-->
      <!--            <el-row style="padding-top: 5px">-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isUndefine1" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.undefinedLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.undefine1" :disabled="temp.isUndefine1==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--            </el-row>-->
      <!--          </el-tab-pane>-->

      <!--          <el-tab-pane :label="$t('pages.vulnerableTerminal')" name="vulnerableTerminalTab" style="padding: 10px; overflow: auto;">-->
      <!--            <el-row style="padding-top: 15px">-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isCritical2" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.severityLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.critical2" :disabled="temp.isCritical2==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--              <el-col :span="1">&nbsp;</el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isImportant2" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.importanceLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.important2" :disabled="temp.isImportant2==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--            </el-row>-->

      <!--            <el-row style="padding-top: 5px">-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isMedia2" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.mediumPatch') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.media2" :disabled="temp.isMedia2==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--              <el-col :span="1">&nbsp;</el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isLow2" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.lowLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.low2" :disabled="temp.isLow2==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--            </el-row>-->
      <!--            <el-row style="padding-top: 5px">-->
      <!--              <el-col :span="5">-->
      <!--                <el-checkbox v-model="temp.isUndefine2" :false-label="0" :true-label="1">{{ $t('pages.containInfo', { info: $t('pages.undefinedLevel') }) }}</el-checkbox>-->
      <!--              </el-col>-->
      <!--              <el-col :span="5">-->
      <!--                <el-input-number v-model="temp.undefine2" :disabled="temp.isUndefine2==0" :controls="false" :min="0" :max="999999" size="mini"/>-->
      <!--              </el-col>-->
      <!--              <el-col :span="1" style="padding-left: 10px">个</el-col>-->
      <!--            </el-row>-->
      <!--          </el-tab-pane>-->
      <!--        </el-tabs>-->
      <!--      </el-card>-->

      <div class="save-btn-container" style="margin-top: 15px;">
        <el-button :loading="submitting" type="primary" size="mini" @click="createData()">
          {{ $t('button.save') }}
        </el-button>
      </div>
    </Form>
  </div>
</template>

<script>
import { getSetting, saveSetting } from '@/api/assets/patchManage/paramSetting'
import { timeIdValidator } from '@/utils/validate'

export default {
  name: 'ParamSetting',
  data() {
    return {
      dialogFormVisible: false,
      submitting: false,
      tabName: 'highRiskTerminalTab',
      query1: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        updateId: null,
        includePatchResult: true,
        searchType: 0,
        searchContent: null
      },
      systemLevelType: 1,
      showTimeConponent: false,
      timeList: [],
      editable: true,
      temp: {
        checkDay: 0,
        isLimitTime: 0,
        limitTime: '00:00:00-23:59:59',
        timeId: 1,
        // termInstallNum: 0,
        savePath: '#USERTEMP#\\',
        autoInstall: 0,
        autoPatchLevel: 0,
        autoPatchLevelList: [],
        systemLevelList: [],
        latelyTime: '',
        repetitionTime: 0,
        isCritical1: 0,
        isImportant1: 0,
        isMedia1: 0,
        isLow1: 0,
        isUndefine1: 0,
        isCritical2: 0,
        isImportant2: 0,
        isMedia2: 0,
        isLow2: 0,
        isUndefine2: 0,
        critical1: 0,
        important1: 0,
        media1: 0,
        low1: 0,
        undefine1: 0,
        critical2: 0,
        important2: 0,
        media2: 0,
        low2: 0,
        undefine2: 0,
        downloadType: 1
      },
      rules: {
        timeId: [
          { required: true, message: this.$t('components.selectEffectTime'), trigger: 'change' },
          { validator: timeIdValidator, trigger: 'change' }
        ],
        savePath: [
          { required: true, message: this.$t('text.cantNullInfo', { info: this.$t('pages.patchDownloadPath') }), trigger: 'blur' },
          { validator: this.savePathValidator, trigger: 'change' }
        ],
        autoPatchLevelList: [
          { validator: this.autoPatchLevelListValidator, trigger: 'blur' }
        ]
      },
      timeType: 7, // 检测时间类型
      // 1:每天 3：每三天 7：每七天 30：每30天 0：关闭 -1：执行一次 -2：自定义
      timeTypeArray: [
        { value: 0, label: this.$t('pages.close') }, //  关闭
        { value: 1, label: this.$t('pages.everyDay') }, //  每天
        { value: 3, label: this.$t('pages.selfSetDay', { day: 3 }) },  //  每3天
        { value: 7, label: this.$t('pages.selfSetDay', { day: 7 }) }, //  每7天
        { value: 30, label: this.$t('pages.selfSetDay', { day: 30 }) }, //  每30天
        { value: -2, label: this.$t('pages.selfDay') }, //  自定义
        { value: -1, label: this.$t('pages.executeOnce') } //  执行一次
      ],
      selfTime: 15,
      //  文件路径快捷按钮 this.$t('pages.userTempDirectory')
      filePathShortcutKeys: [
        { label: this.$t('pages.maximumRemainingDiskSpace'), path: '#MAX#\\', remark: '' },
        { label: this.$t('pages.desktop'), path: '#DESKTOP#\\', remark: '' },       //  桌面
        { label: this.$t('pages.userTempDirectory') + '(' + this.$t('pages.defaultValue') + ')', path: '#USERTEMP#\\', remark: this.$t('pages.userTempDirectoryRemark') },       //  用户临时目录
        { label: this.$t('pages.sysTempTempDirectory'), path: '#SYSTEMTEMP#\\', remark: this.$t('pages.sysTempTempDirectoryRemark') }       //  系统临时目录
      ],
      patchLevelMap: {
        1: this.$t('pages.severityLevel'),
        2: this.$t('pages.importanceLevel'),
        4: this.$t('pages.midLevel'),
        8: this.$t('pages.lowLevel')
      }
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    }
  },
  watch: {
    showTimeConponent(val) {
      if (val) {
        this.showItems()
      }
    }
  },
  created() {
    getSetting().then(res => {
      this.temp = Object.assign(this.temp, res.data)
      this.formatCheckDay(this.temp.checkDay)
      this.formatAutoPatchLevelList(res.data.autoPatchLevel)
      if (this.temp.systemLevelList != null && this.temp.systemLevelList.length > 0) {
        this.temp.systemLevelList.forEach(item => {
          if (item.patchLevel == 4) {
            this.temp['critical' + item.modeType] = item.count
            this.temp['isCritical' + item.modeType] = 1
          } else if (item.patchLevel == 3) {
            this.temp['important' + item.modeType] = item.count
            this.temp['isImportant' + item.modeType] = 1
          } else if (item.patchLevel == 2) {
            this.temp['media' + item.modeType] = item.count
            this.temp['isMedia' + item.modeType] = 1
          } else if (item.patchLevel == 1) {
            this.temp['low' + item.modeType] = item.count
            this.temp['isLow' + item.modeType] = 1
          } else if (item.patchLevel == 0) {
            this.temp['undefine' + item.modeType] = item.count
            this.temp['isUndefine' + item.modeType] = 1
          }
        })
      }
    })
  },
  methods: {
    //  检测周期
    timeChange(time) {
      if (time !== -2) {
        this.temp.checkDay = time
      }
      if (time == 0) {
        this.temp.autoInstall = 0
        this.temp.autoPatchLevelList = []
      }
    },
    selfTimeChange(event) {
      const val = event.target.value.trim()
      if (val === undefined || val === null || val === '') {
        this.selfTime = 1;
      }
    },
    // 时间段
    showItems() {
      this.timeList = []
      this.editable = true
      const timeArr = this.temp.limitTime.split(';')
      if (timeArr.length >= 5) {
        this.editable = false
      }
      timeArr.forEach(el => {
        const strArr = el.split('-')
        this.timeList.push(strArr)
      })
    },
    rmlastitems(index) {
      if (this.timeList.length > 1) {
        this.timeList.splice(index, 1)
      } else {
        this.timeList = [['00:00:00', '00:00:00']]
      }
      this.editable = true
    },
    addlastitems() {
      if (this.timeList.length < 5) {
        this.timeList.push(['00:00:00', '23:59:59'])
      }
      if (this.timeList.length >= 5) {
        this.editable = false
      }
    },
    clearItems() {
      this.timeList = [['00:00:00', '00:00:00']]
      this.editable = true
    },
    saveItems() {
      if (this.timeList.length > 0) {
        this.temp.limitTime = ''
        this.timeList.forEach(el => {
          const str = el[0] + '-' + el[1]
          if (this.temp.limitTime) {
            if (this.temp.limitTime.indexOf(str) == -1) {
              this.temp.limitTime = this.temp.limitTime + ';' + str
            }
          } else {
            this.temp.limitTime = str
          }
        })
      } else {
        this.temp.limitTime = '00:00:00-00:00:00'
      }
      this.timeList = []
      this.editable = true
      this.showTimeConponent = false
    },
    resetItem() {
      this.editable = true
      this.showTimeConponent = false
    },
    handleAutoInstallChange(val) {
      if (0 == val) {
        this.temp.autoPatchLevelList = []
        this.temp.autoPatchLevel = 0
      }
    },
    // 补丁下载路径
    filePathShortcutKey(path) {
      this.temp.savePath = path
    },
    removeDuplicates(str, pattern) {
      // 使用正则表达式捕获组来保留第一个匹配项
      const regex = new RegExp(pattern, 'ig');
      let matchCount = 0
      return str.replace(regex, (match, index) => {
        console.log('regex.lastIndex', match, index)
        if (matchCount !== 0) {
          return ''
        }
        matchCount++
        return match
      });
    },
    //  不允许输入 / : * ? " < > |
    filPathInputHandler(value) {
      console.log('filPathInputHandler', value)
      const notAllowExits = ['/', ':', '*', '?', '"', '<', '>', '|'];
      for (let i = 0; i < notAllowExits.length; i++) {
        value = value.replaceAll(notAllowExits[i], notAllowExits[i] === '/' ? '\\' : '')
      }
      const pattern = '#MAX#|#DESKTOP#|#USERTEMP#|#SYSTEMTEMP#'
      // 防止管理员重复输入#MAX#|#DESKTOP#|#USERTEMP#|#SYSTEMTEMP#
      value = this.removeDuplicates(value, pattern)
      // 防止管理员重复输入反斜线
      value = value.replace(/\\+/g, '\\');
      this.temp.savePath = value
    },
    // 补丁等级
    formatAutoPatchLevelList(num) {
      if (num === null || num === undefined) {
        this.temp.autoPatchLevelList = []
        return
      }
      const arrStr = num.toString(2).split('').reverse();
      const resArr = []
      for (let i = 0; i < arrStr.length; i++) {
        if (arrStr[i] == 1) {
          resArr.push(Math.pow(2, i).toString())
        }
      }
      this.temp.autoPatchLevelList = resArr
    },
    isHighRiskTerminalTab() {
      return this.activeName === 'highRiskTerminalTab'
    },
    isVulnerableTerminalTab() {
      return this.activeName === 'highRiskTerminalTab'
    },
    tabClick(pane, event) {
      if (this.isHighRiskTerminalTab()) {
        this.systemLevelType = 1
      }
      if (this.isVulnerableTerminalTab()) {
        this.systemLevelType = 2
      }
    },
    // 格式化检测周期
    formatCheckDay(checkDay) {
      if ([1, 3, 7, 30, -2, -1, 0].includes(checkDay)) {
        this.timeType = checkDay
      } else {
        this.timeType = -2
        this.selfTime = checkDay
      }
    },
    handlePatchLevelChange(val) {
      const sum = val.reduce((sum, current) => sum + Number(current), 0)
      this.temp.autoPatchLevel = sum
    },
    createData() {
      // if (!this.$refs['dataForm'].valid()) {
      //   return false
      // }
      this.$refs['dataForm'].validate((valid) => {
        console.log('valid', valid)
        if (valid) {
          if (this.temp.timeId === undefined || this.temp.timeId === null) {
            this.$notify({
              title: this.$t('text.warning'),
              message: this.$t('text.cantNullInfo', { info: this.$t('pages.patchAllowDownloadTime') }),
              type: 'warning',
              duration: 2000
            })
            return;
          }
          const levelList = [
            { modeType: 1, patchLevel: 4, count: this.temp.critical1, isSetting: this.temp.isCritical1 },
            { modeType: 1, patchLevel: 3, count: this.temp.important1, isSetting: this.temp.isImportant1 },
            { modeType: 1, patchLevel: 2, count: this.temp.media1, isSetting: this.temp.isMedia1 },
            { modeType: 1, patchLevel: 1, count: this.temp.low1, isSetting: this.temp.isLow1 },
            { modeType: 1, patchLevel: 0, count: this.temp.undefine1, isSetting: this.temp.isUndefine1 },
            { modeType: 2, patchLevel: 4, count: this.temp.critical2, isSetting: this.temp.isCritical2 },
            { modeType: 2, patchLevel: 3, count: this.temp.important2, isSetting: this.temp.isImportant2 },
            { modeType: 2, patchLevel: 2, count: this.temp.media2, isSetting: this.temp.isMedia2 },
            { modeType: 2, patchLevel: 1, count: this.temp.low2, isSetting: this.temp.isLow2 },
            { modeType: 2, patchLevel: 0, count: this.temp.undefine2, isSetting: this.temp.isUndefine2 }
          ]
          this.temp.systemLevelList = []
          levelList.forEach(item => {
            if (item.isSetting == 1 && item.count != null && item.count > 0) {
              this.temp.systemLevelList.push(item)
            }
          })
          this.submitting = true
          if (this.timeType == -2) {
            this.temp.checkDay = this.selfTime
          } else {
            this.temp.checkDay = this.timeType
          }
          console.log(this.temp.checkDay, this.temp)
          saveSetting(this.temp).then(res => {
            this.submitting = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.saveSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    savePathValidator(rule, value, callback) {
      if (!this.temp.savePath.startsWith('#MAX#\\') &&
          !this.temp.savePath.startsWith('#DESKTOP#\\') &&
          !this.temp.savePath.startsWith('#USERTEMP#\\') &&
          !this.temp.savePath.startsWith('#SYSTEMTEMP#\\')
      ) {
        callback(new Error(this.$t('pages.patchDownloadPathVerification')))
      } else {
        callback()
      }
    },
    autoPatchLevelListValidator(rule, value, callback) {
      if (this.temp.autoInstall === 1) {
        if (this.temp.autoPatchLevelList.length === 0) {
          callback(new Error(this.$t('pages.patchLevelTip')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item{
  margin-bottom: 5px;
}
  .app-container{
    overflow: auto;
  }
  .delete-time{
    cursor: pointer;
  }
  .download-time-icon{
    outline: none;
    color: #68a8d0;
    cursor: pointer;
    &.is-disabled{
      color: #777a80;
      cursor: not-allowed;
    }
  }
  .save-btn-container{
    width: 700px;
    margin-top: 5px;
    text-align: right;
  }
  .edit-item svg{
    cursor: pointer;
  }
  .add-time-item{
    color: green;
    &.is-disabled{
      color: #ccc;
      cursor: not-allowed;
    }
  }
  .edit-btns{
    text-align: right;
  }
</style>
