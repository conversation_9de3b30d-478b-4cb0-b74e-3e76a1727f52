<template>
  <div v-if="visible">
    <div class="tree-container">
      <tree-menu
        ref="tree"
        style="width: 200px"
        :local-search="true"
        node-key="dataId"
        :data="strategyTypes"
        :resizeable="false"
        :multiple="true"
        :check-strictly="false"
        :is-filter="true"
        @check-change="checkChange"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button size="mini" @click="back">{{ $t('button.return') }}</el-button>
        <el-button style="margin-left: 10px" size="mini" @click="getStrategyTableData">{{ $t('button.refresh') }}</el-button>
        <el-button
          style="margin-left: 10px"
          size="mini"
          icon="el-icon-download"
          :disabled="query.stgTypeNumbers === ''"
          @click="exportElag"
        >{{ $t('button.export') }}</el-button>
        <el-button type="primary" style="float: right; margin-right: 10px;" icon="el-icon-search" size="mini" @click="handleSearch">
          {{ $t('table.search') }}
        </el-button>
        <el-input v-model="query.searchInfo" v-trim clearable :placeholder="this.$t('pages.validateStgName')" style="width: 200px; float: right"></el-input>
      </div>
      <grid-table
        ref="strategyTable"
        row-key="dataId"
        :multi-select="true"
        :col-model="colModel"
        :is-saved-selected="true"
        show-pager
        :selectable="selectable"
        :row-data-api="rowDataApi"
        @updateLimit="updateLimit"
        @updatePage="updatePage"
      />
    </div>
    <el-dialog
      v-if="exportVisible"
      v-el-drag-dialog
      :title="$t('pages.strategyExport')"
      :modal="false"
      :visible.sync="exportVisible"
      :width="dialogWidth + 'px'"
      @click="exportDlagClick"
    >
      <Form
        ref="dataForm"
        :model="temp"
        label-width="120px"
      >
        <FormItem :label="$t('pages.exportRange')" prop="exportScope">
          <el-radio-group v-model="temp.exportScope">
            <el-radio :label="0">{{ $t('pages.exportSelectStrategy') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.exportSelectStrategyTypeAllData') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.exportFileFormatter')" prop="exportFormatter">
          <el-radio-group v-model="temp.exportFormatter">
            <el-radio v-for="item in exportFormatters" :key="item.value" :label="item.value">{{ $t(item.label) }}</el-radio>
          </el-radio-group>
        </FormItem>
        <div class="dialog-footer" >
          <el-row>
            <el-col :span="exportErrorMessage !== '' ? 12 : 0" style="text-align: center">
              <div v-if="exportErrorMessage !== ''" style=" color: red;">
                {{ exportErrorMessage }}
              </div>
            </el-col>
            <el-col :span="exportErrorMessage !== '' ? 12 : 24">
              <div style="float: right">
                <el-button type="primary" size="mini" @click="handleExport">{{ $t('button.confirm') }}</el-button>
                <el-button size="mini" @click="exportDlagClose">{{ $t('button.cancel') }}</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </Form>
    </el-dialog>
    <component :is="componentName" :ref="refName" v-bind="comProps" />
  </div>
</template>
<script>
import PrinterSet from '@/views/behaviorManage/hardware/printerSet'
import WaterMarkDialog from '@/views/behaviorManage/hardware/waterMark/waterMarkDialog'
import ImTool from '@/views/behaviorManage/application/imTool'
import ImFile from '@/views/behaviorManage/application/imFile'
import AppBlock from '@/views/behaviorManage/application/appBlock/editDlg'
import WinTitleBlock from '@/views/behaviorManage/application/winTitle'
import AppVersionLimit from '@/views/behaviorManage/application/appVersion'
import InstallPacket from '@/views/behaviorManage/application/install/installPacket/editDlg'
import SpecialPath from '@/views/behaviorManage/application/install/specialPath'
import Url from '@/views/behaviorManage/network/url'
import WebPost from '@/views/behaviorManage/network/webPost'
import WebPort from '@/views/behaviorManage/network/webPort/editDlg'
import WebFlow from '@/views/behaviorManage/network/webFlow/editDlg'
import NetCtrl from '@/views/behaviorManage/network/isolation'
import EmailKeywordBlock from '@/views/behaviorManage/network/emailKeyword'
import VideoStrategy from '@/views/behaviorManage/monitor/video/editDlg'
import BurnConfig from '@/views/behaviorManage/burner/burnConfig'
import ShareConfig from '@/views/behaviorManage/network/shareConfig'
import FileFilterSetup from '@/views/behaviorManage/monitor/fileFilter'
import Driver from '@/views/behaviorManage/hardware/driver'
import OtherDeviceLimit from '@/views/behaviorManage/hardware/otherDeviceLimit'
import Content from '@/views/contentStrategy/strategy/content'
import EffectiveContentStrategy from '@/views/contentStrategy/strategy/effectiveContentStrategy'
import EffectiveContentConfig from '@/views/contentStrategy/strategy/effectiveContentConfig'
import BlueToothConfig from '@/views/behaviorManage/hardware/blueTooth'
import LogFilter from '@/views/behaviorManage/monitor/logFilter'
import MailReceiver from '@/views/dataEncryption/encryption/mailWhiteList/mailReceiver'
import MailCopy from '@/views/dataEncryption/encryption/mailWhiteList/mailCopy'
import EncryptSpecialPath from '@/views/dataEncryption/encryption/specialPath/editDlg'
import HttpWhiteList from '@/views/dataEncryption/encryption/httpWhiteList'
import BrowserFile from '@/views/behaviorAuditing/network/browserFileStrategy'
import Clipboard from '@/views/dataEncryption/encryption/clipboard/editDlg'
import Screenshot from '@/views/dataEncryption/encryption/screenshotStrategy/editDlg'
import WorkMode from '@/views/dataEncryption/encryption/workMode/editDlg'
import Process from '@/views/dataEncryption/encryption/processStg/editDlg'
import ProcessConfigStg from '@/views/dataEncryption/encryption/processStg/configEditDlg'
import SpecialSuffix from '@/views/dataEncryption/encryption/specialSuffix/editDlg'
import IpAndMacBind from '@/views/assets/systemMaintenance/IPAndMAC'
import ReadPermission from '@/views/dataEncryption/encryption/readPermission'
import ProcessCollectRule from '@/views/behaviorManage/application/processCollectRule'
import DiskScan from '@/views/dataEncryption/encryption/diskScan/editDlg'
import SmartEnc from '@/views/dataEncryption/encryption/smartEncStrategy/editDlg'
import TranslucentEnc from '@/views/dataEncryption/encryption/translucentEncStrategy/editDlg'
import UserDense from '@/views/dataEncryption/encryption/userDense'
import BackUpConfig from '@/views/dataEncryption/encryption/backUpConfig/editDlg'
import mcode from '@/views/dataEncryption/encryption/fileOutgoing/mcode'
import FileOutgoingConfig from '@/views/dataEncryption/encryption/fileOutgoing/config'
import ProcessMonitor from '@/views/behaviorManage/hardware/processMonitor'
import TerminalConfig from '@/views/system/terminalManage/terminalConfig'
import SysBaseConfig from '@/views/behaviorManage/application/sysBaseConfig'
import outgoingProcess from '@/views/dataEncryption/encryption/fileOutgoing/app'
import SoftwareBlacklist from '@/views/dataEncryption/encryption/fileOutgoing/softwareBlacklist'
import outgoingTemplate from '@/views/dataEncryption/encryption/fileOutgoing/template'
import outgoingScreenWaterMark from '@/views/dataEncryption/encryption/fileOutgoing/screenWaterMark'
import outgoingPrintWaterMark from '@/views/dataEncryption/encryption/fileOutgoing/printWaterMark'
import BatchEncOrDec from '@/views/dataEncryption/encryption/encOrDecStg/editDlg'
import TerminalMenu from '@/views/system/terminalManage/terminalMenu'
import ShortOffline from '@/views/dataEncryption/encryption/shortOfflineStrategy'
import NetDisk from '@/views/behaviorManage/network/netDisk'
import ApplySecurityAccess from '@/views/dataEncryption/encryption/applySecurityAccess'
import AppReportSet from '@/views/system/terminalManage/appReportSet'
import SensitiveBackupConfig from '@/views/contentStrategy/strategy/sensitiveBackupConfig'
import EnDeFileScan from '@/views/dataEncryption/encryption/EnDeFileScan'
import AppLogConfig from '@/views/behaviorManage/application/appLogConfig'
import ProcessInject from '@/views/system/terminalManage/processInject/editDlg'
import OperatorConfig from '@/views/dataEncryption/encryption/operatorConfig'
import OfficeWaterMark from '@/views/dataEncryption/encryption/officeWaterMark/editDlg'
import SoftwareLimit from '@/views/softwareManage/strategy/softwareLimit/editDlg'
import RequireInstall from '@/views/softwareManage/strategy/requireInstall/editDlg'
import RequireRun from '@/views/softwareManage/strategy/requireRun/editDlg'
import UsbFileConfig from '@/views/behaviorManage/hardware/usbFileConfig'
import SysAlarmConfig from '@/views/behaviorManage/application/sysAlarmConfig/editDlg'
import WifiCollect from '@/views/behaviorManage/network/wifiCollect/editDlg'
import WifiBlock from '@/views/behaviorManage/network/wifiBlock/editDlg'
import FtpControlConfig from '@/views/behaviorManage/network/ftpControl/editDlg'
import GroupPolicy from '@/views/behaviorManage/application/groupPolicy/editDlg'
import ForumUrlFilter from '@/views/behaviorManage/network/forumUrlFilter'
import WebBrowseUrlFilter from '@/views/behaviorManage/network/webBrowseURLFilter'
import OfflineLockScreen from '@/views/system/terminalManage/offlineLockScreen/editDlg'
import SoftWareTask from '@/views/assets/systemMaintenance/softWareTaskStrategy/taskList'
import PersonalizePolicy from '@/views/behaviorManage/hardware/personalizePolicy'
import IconRefreshDir from '@/views/dataEncryption/encryption/iconRefreshDir'
import DocumentTrack from '@/views/dataEncryption/fileTrace/documentTrack'
import BlindWatermark from '@/views/dataEncryption/fileTrace/blindWatermark'
import AdbLimit from '@/views/behaviorManage/hardware/adbLimit/editDlg'
import { getExportPageStg, getExportSuffix, multiExportStg } from '@/api/stgCommon';

export default {
  name: 'ExportStrategy',
  //  组件中的名称必须和策略类型名称一致
  components: {
    PrinterSet, WaterMarkDialog, ImTool, ImFile, AppBlock, WinTitleBlock,
    AppVersionLimit, InstallPacket, SpecialPath, Url, WebPost, WebPort, WebFlow, NetCtrl, EmailKeywordBlock, VideoStrategy, BurnConfig,
    ShareConfig, FileFilterSetup, Driver, OtherDeviceLimit, EffectiveContentStrategy, EffectiveContentConfig, Content, BlueToothConfig, LogFilter,
    EncryptSpecialPath, HttpWhiteList, BrowserFile, Clipboard, Screenshot, WorkMode, Process, ProcessConfigStg, SpecialSuffix,
    IpAndMacBind, ReadPermission, ProcessCollectRule, DiskScan, SmartEnc, TranslucentEnc, UserDense, BackUpConfig, mcode,
    FileOutgoingConfig, ProcessMonitor, TerminalConfig, SysBaseConfig, outgoingProcess, SoftwareBlacklist, outgoingTemplate, outgoingScreenWaterMark, outgoingPrintWaterMark, BatchEncOrDec, TerminalMenu,
    ShortOffline, NetDisk, ApplySecurityAccess, AppReportSet, SensitiveBackupConfig, EnDeFileScan, AppLogConfig, ProcessInject, OperatorConfig, OfficeWaterMark,
    SoftwareLimit, RequireInstall, RequireRun, UsbFileConfig, SysAlarmConfig, WifiCollect, WifiBlock, FtpControlConfig, GroupPolicy, ForumUrlFilter, WebBrowseUrlFilter, MailReceiver, MailCopy,
    SoftWareTask, OfflineLockScreen, PersonalizePolicy, IconRefreshDir, DocumentTrack, BlindWatermark, AdbLimit },
  props: {
    visible: { type: Boolean, default: true },
    strategyTree: { type: Array, default() { return [] } }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '100', fixed: true, sort: true },
        { prop: 'osType', label: 'osType', width: '100', formatter: this.osTypeFormatter },
        { prop: 'entityName', label: 'source', type: 'text', width: '100' },
        { prop: 'stgTypeName', label: 'stgTypeName', width: '100', formatter: this.stgTypeNameFormatter },
        { prop: 'strategyTypeNumber', label: 'details', width: '100', type: 'button',
          buttons: [
            { label: 'details', click: this.showDetail }
          ]
        },
        { prop: 'remark', label: 'remark', width: '100' }
      ],
      dialogWidth: 600,
      exportVisible: false,
      query: {
        page: 1,
        limit: 20,
        strategyDefType: 0,
        stgTypeNumbers: '',
        //  策略名称
        searchInfo: ''
      },
      temp: {
        exportScope: 0,
        exportFormatter: 0,
        strategyTypes: '',
        ids: []
      },
      exportFormatters: [],
      strategyProps: { label: 'strategyTypeName' },
      strategyTypes: [],  //  策略类型
      componentName: '',
      refName: '',
      comProps: {},
      exportErrorMessage: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs.strategyTable
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getStrategyTypes();
      }
    }
  },
  created() {
    // this.getStrategyTypes();
  },
  methods: {
    getStrategyTypes() {
      this.notNeedStrategyFilter(this.strategyTree)
      this.strategyTypes = this.strategyTree
    },
    //  获取列表数据
    getStrategyTableData() {
      this.$refs.strategyTable.execRowDataApi(this.query);
    },
    rowDataApi() {
      const fun = getExportPageStg(this.query);
      fun.then(res => {
        res.data.items = res.data.items || []
        res.data.items.forEach(item => {
          item.dataId = item.stgTypeNumber + '-' + item.id
        });
      })
      return fun;
    },
    updateLimit(val) {
      if (this.query.limit !== val) {
        this.query.limit = val
        this.getStrategyTableData()
      }
    },
    updatePage(val) {
      if (this.query.page !== val) {
        this.query.page = val
        this.getStrategyTableData()
      }
    },
    exportElag() {
      getExportSuffix(this.query.stgTypeNumbers).then(res => {
        this.exportFormatters = []
        if (res.data === '.lds') {
          this.exportFormatters.push({ label: 'pages.exportLdsFile', value: 1 })
          this.temp.exportFormatter = 1
        } else {
          const radioMessage = this.getExportRadioMessageBySuffix(res.data);
          if (radioMessage !== '') {
            this.exportFormatters.push({ label: radioMessage, value: 0 })
            this.temp.exportFormatter = 0
          } else {
            this.temp.exportFormatter = 1
          }
          this.exportFormatters.push({ label: 'pages.exportLdsFile', value: 1 })
        }
        this.exportVisible = true
      }).catch(() => {
        this.exportFormatters.push({ label: 'pages.exportLdsFile', value: 1 })
        this.temp.exportFormatter = 1
        this.exportVisible = true
      })
    },
    //  根据文件后缀名获取radio的名称
    getExportRadioMessageBySuffix(suffix) {
      let radioMessage = ''
      switch (suffix) {
        case '.ldi' : radioMessage = 'pages.exportLdiFile'; break;
        default: radioMessage = '';
      }
      return radioMessage;
    },
    setPageTotal(total) {
      const _this = this
      this.$nextTick(() => {
        if (_this.$refs.strategyTable) {
          _this.$refs.strategyTable.total = total || 0
        }
      })
    },
    //  改变策略树节点时，触发
    checkChange(data, checked, indeterminate) {
      data = data || []
      this.query.stgTypeNumbers = ''
      if (data.length > 0) {
        const stgTypeNumbers = []
        data.forEach(id => {
          if (id.startsWith('Type')) {
            stgTypeNumbers.push(id.substr(4))
          }
        })
        this.query.stgTypeNumbers = stgTypeNumbers.join(',') || ''
        this.getStrategyTableData();
      } else {
        this.$refs.strategyTable.clearSaveSelection()
        this.$refs.strategyTable.clearSelection()
        this.$refs.strategyTable.clearRowData();
        this.$refs.strategyTable.clearPageData();
      }
      this.temp.strategyTypes = this.query.stgTypeNumbers
    },
    selectable(row, index) {
      return true
    },
    osTypeFormatter(row, data) {
      let osType = row.osType || null
      if (osType !== undefined && osType != null) {
        switch (osType) {
          case 1 : osType = 'windows'; break;
          case 2 : osType = 'linux'; break;
          case 4 : osType = 'mac'; break;
          case 7 : osType = 'windows, linux, mac'; break;
          case 8 : osType = this.$t('pages.mobileTerminal'); break;
          case 15: osType = 'windows, linux, mac,' + this.$t('pages.mobileTerminal'); break;
          default: osType = '{ ' + osType + ' } ' + this.$t('pages.unknownSystem');
        }
      }
      return osType;
    },
    stgTypeNameFormatter(row, data) {
      const node = this.getStgTypeNode(row.stgTypeNumber, this.strategyTypes)
      return node != null ? node.name : '';
    },
    getStgTypeNode(stgTypeNumber, tree) {
      if (tree == null) {
        return null;
      }
      let node = null;
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].type === 'type' && tree[i].id === stgTypeNumber) {
          node = tree[i];
        }
        node = node == null ? this.getStgTypeNode(stgTypeNumber, tree[i].children) : node;
        if (node) {
          return node;
        }
      }
    },
    exportDlagClose() {
      this.exportErrorMessage = ''
      this.exportVisible = false
      this.resetTemp()
    },
    resetTemp() {
      this.temp.exportScope = 0
      this.temp.exportFormatter = 0
      this.temp.ids = []
    },
    //  查询
    handleSearch() {
      this.getStrategyTableData();
    },
    //  导出弹窗确定事件
    handleExport() {
      //  校验是否选中策略类型
      if (this.temp.strategyTypes === undefined || this.temp.strategyTypes === null || this.temp.strategyTypes === '') {
        this.exportErrorMessage = this.$t('pages.exportErrorMessage1')
        return;
      }
      //  校验是否选中策略
      const ids = this.$refs.strategyTable.getSelectedKeys() || [];
      if (this.temp.exportScope === 0) {
        if (ids.length === 0) {
          this.exportErrorMessage = this.$t('pages.exportErrorMessage2')
          return;
        }
        let dataIdArr = []
        const array = []
        for (let i = 0; i < ids.length; i++) {
          dataIdArr = ids[i].toString().split('-') || []
          array.push({ strategyTypeNumber: dataIdArr[0], id: dataIdArr[1] });
        }
        this.temp.ids = array || []
      }
      multiExportStg(this.temp).then(() => {
        this.resetTemp()
      });
      this.$nextTick(() => {
        this.$refs.strategyTable.clearSaveSelection()
        this.$refs.strategyTable.clearSelection()
      })
      this.exportErrorMessage = ''
      this.exportVisible = false
    },
    exportDlagClick() {
      this.exportVisible = true
    },
    //  查看详情
    showDetail(row) {
      const object = JSON.parse(row.strategyInfo)
      object.active = row.active || false
      object.remark = row.remark || object.remark
      object.strategyDefType = row.strategyDefType || 0
      object.entityType = row.objectType
      object.entityId = row.objectId
      const strategyType = row.strategyType
      const strategyTypeNumber = row.stgTypeNumber
      if (object.ruleGroupIds != undefined) {
        // 这边使用大写开头的Content，因为content是html标签
        const refName = object.dripScan ? 'dripContent' : 'Content'
        this.resetComponent(refName)
        this.comProps['formable'] = false
        this.$nextTick(() => {
          this.$refs[refName].handleUpdate(object)
        })
      } else {
        // objectType和objectId有值，且不等于6表示不等于策略包
        if (row.objectType && (row.objectId || row.objectId == 0) && row.objectType != 6) {
          object.entityType = row.objectType
          object.entityId = row.objectId
        }
        this.resetComponent(strategyType)
        this.comProps['formable'] = false
        this.$nextTick(() => {
          //  这些策略类型编号调用handleShow
          if ([6, 14, 22, 50, 68, 69, 73, 163, 205, 211, 212, 213, 218].includes(strategyTypeNumber)) {
            this.$refs[row.strategyType].handleShow(object, true)
          } else {
            this.$refs[row.strategyType].handleUpdate(object)
          }
        })
      }
    },
    resetComponent(name) {
      this.refName = name
      // ref与组件名不同的映射
      const nameOption = {
        waterMarkStrategy: 'water-mark-dialog',
        screenWaterMarkStrategy: 'water-mark-dialog',
        installStrategy: 'install-packet',
        uninstallStrategy: 'install-packet',
        effectiveFunction: 'effective-content-strategy',
        effectiveFuncConfig: 'EffectiveContentConfig',
        httpWhiteListProcessFilter: 'http-white-list',
        autoBackupFilterStrategy: 'special-suffix',
        outgoingCodeWhiteList: 'mcode',
        outgoingConfig: 'file-outgoing-config',
        videoStrategy: 'video-strategy', // video是html标签
        dripContent: 'Content',
        softwareLimitStrategy: 'SoftwareLimit',
        softRequiredInstallStrategy: 'RequireInstall',
        softRequiredRunStrategy: 'RequireRun',
        groupPolicyStrategy: 'GroupPolicy',
        forumURLFilter: 'ForumUrlFilter',
        webBrowseURLFilter: 'webBrowseUrlFilter',
        ftpControllerConfig: 'FtpControlConfig',
        mailWhiteListStrategy: 'MailReceiver',
        mailCarbonCopyStrategy: 'MailCopy'
      }
      const index = name.toLowerCase().indexOf('strategy')
      // 根据ref名称，修改组件名
      if (!nameOption[name]) {
        this.componentName = index > 0 ? name.substring(0, index) : name
      } else {
        this.componentName = nameOption[name]
      }
      // 部分页面需要传入的 prop
      const option = {
        installStrategy: { defaultLimitType: 1 },
        uninstallStrategy: { defaultLimitType: 2 },
        waterMarkStrategy: { stgTypeNumber: 2 },
        screenWaterMarkStrategy: { stgTypeNumber: 44 },
        httpWhiteListProcessFilter: { tabName: 'processTab' },
        processStrategy: { tabName: 'strategyTab' },
        processConfigStg: { tabName: 'configTab' },
        specialSuffixStrategy: { tabName: 'specialFileExt' },
        autoBackupFilterStrategy: { tabName: 'autoBackupFilter' },
        dripContent: { dripAble: true }
      }
      this.comProps = { listable: false, formable: false, ...option[name] }
    },
    //  关闭当前导入页面
    back() {
      this.$emit('closeExport')
    },
    //  过滤掉不需要的策略（邮件白名单-发件人白名单）
    notNeedStrategyFilter(array) {
      this.deleteTypeNode(209, array);
    },
    deleteTypeNode(stgTypeNumber, tree) {
      if (tree == null) {
        return false;
      }
      let flag = false;
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].type === 'type' && tree[i].id === stgTypeNumber) {
          flag = true;
          tree.splice(i, 1);
        }
        flag = !flag ? this.deleteTypeNode(stgTypeNumber, tree[i].children) : flag;
        if (flag) {
          return flag;
        }
      }
    }
    //  需要导入导出功能的策略
    // getNeedImportExportStrategyMap() {
    //   return {
    //     processCollectRuleStrategy: true,
    //     appBlockStrategy: true,
    //     winTitleBlockStrategy: true,
    //     appLogConfig: true,
    //     appReportSet: true,
    //     videoStrategy: true,
    //     processMonitor: true,
    //     fileFilterSetup: true,
    //     printerSet: true,
    //     waterMarkStrategy: true,
    //     blueToothConfig: true,
    //     adbLimitStrategy: true,
    //     driverStrategy: true,
    //     usbFileConfig: true,
    //     burnConfigStrategy: true,
    //     sysBaseConfig: true,
    //     sysAlarmConfig: true,
    //     groupPolicyStrategy: true,
    //     otherDeviceLimit: true,
    //     personalizePolicy: true,
    //     logFilter: true,
    //     urlStrategy: true,
    //     webPostStrategy: true,
    //     browserFileStrategy: true,
    //     forumURLFilter: true,
    //     imToolStrategy: true,
    //     imFileStrategy: true,
    //     webPortStrategy: true,
    //     wifiBlockStrategy: true,
    //     webFlowStrategy: true,
    //     emailKeywordBlockStrategy: true,
    //     emailAttachFileStrategy: true,
    //     mailCarbonCopyStrategy: true,
    //     shareConfig: true,
    //     netDisk: true,
    //     ftpControllerConfig: true,
    //     smartEncStrategy: true,
    //     batchEncOrDec: true,
    //     diskScan: true,
    //     EnDeFileScan: true,
    //     userDense: true,
    //     clipboardStrategy: true,
    //     screenshotStrategy: true,
    //     specialSuffixStrategy: true,
    //     encryptSpecialPath: true,
    //     readPermission: true,
    //     screenWaterMarkStrategy: true,
    //     workMode: true,
    //     backUpConfig: true,
    //     shortOfflineStrategy: true,
    //     operatorConfig: true,
    //     iconRefreshDir: true,
    //     outgoingCodeWhiteList: true,
    //     outgoingProcess: true,
    //     outgoingScreenWaterMark: true,
    //     outgoingPrintWaterMark: true,
    //     outgoingConfig: true,
    //     outgoingTemplateStrategy: true,
    //     mailWhiteListStrategy: true,
    //     httpWhiteListStrategy: true,
    //     httpWhiteListProcessFilter: true,
    //     documentTrack: true,
    //     blindWatermark: true,
    //     officeWaterMark: true,
    //     effectiveFunction: true,
    //     sensitiveBackupConfig: true,
    //     applySecurityAccess: true,
    //     terminalConfig: true,
    //     terminalMenuStrategy: true,
    //     processInject: true,
    //     installStrategy: true,
    //     uninstallStrategy: true,
    //     specialPathStrategy: true,
    //     softwareLimitStrategy: true,
    //     softRequiredInstallStrategy: true,
    //     softRequiredRunStrategy: true,
    //     ipAndMacBind: true,
    //     dataDisclosureStg: true,
    //     dripDataDisclosureStg: true,
    //     unconventionalSensitive: true,
    //     processStrategy: true,
    //     processConfigStg: true,
    //     offlineLockScreen: true,
    //     translucentEncStrategy: true,
    //     softWareTaskStrategy: true
    //   }
    // }
  }
}
</script>
<style lang="scss" scoped>
  .top {
    display: flex;
    margin-left: 30px
  }
  .content {
    height: 100%;
  }
  .content>div {
    height: 100%;
  }
</style>
