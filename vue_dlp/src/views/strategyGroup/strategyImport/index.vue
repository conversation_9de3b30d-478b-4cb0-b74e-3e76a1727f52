<template>
  <div v-show="visible" class="app-container strategy-import">
    <div class="import-tree-container-1">
      <div class="import-tree-container-2">
        <div class="table-container">
          <div class="toolbar" style="display: flex">
            <div style="margin-right: 15px">
              <el-button size="mini" @click="back">{{ $t('button.return') }}</el-button>
            </div>
            <div style="margin-right: 20px; color: white">
              <span slot="label">
                {{ $t('pages.repeatNameDealType') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.isGlobalUnique') }}<br/>
                    {{ $t('pages.importAndUpdate_Msg') }}<br/>
                    {{ $t('pages.importAndIgnore_Msg') }}<br/>
                    {{ $t('pages.importAndAdd_Msg') }}<br/>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <el-select v-model="importType" :placeholder="$t('pages.placeChooseImportMode')">
                <el-option :value="1" :label="$t('pages.importAndUpdate')"></el-option>
                <el-option :value="0" :label="$t('pages.importAndIgnore')"></el-option>
                <el-option :value="2" :label="$t('pages.importAndRename')"></el-option>
              </el-select>
            </div>
            <div style="margin-right: 5px; color: white">
              <span slot="label">
                {{ $t('pages.batchProcessingSpecifiedGroups') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.batchProcessingSpecifiedGroupsContent">
                      <br slot="br"/>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <el-select v-model="objectType" class="objectType-select" :disabled="multiStrategyDisabled" :placeholder="$t('pages.placeChooseApplicationObjType')" @change="objectTypeChange">
                <el-option v-for="item in objectTypes" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </div>
            <div style="margin-right: 5px; color: white">
              <!-- 此标签会报错 -->
              <tree-select
                v-if="visible"
                ref="objectTree"
                :placeholder="$t('pages.placeChooseApplicationObj')"
                node-key="id"
                :disabled="objectTreeDisabled"
                :height="350"
                :width="350"
                multiple
                check-strictly
                :checked-keys="objectIds"
                is-filter
                :local-search="false"
                :leaf-key="objectType === 1 ? 'terminal' : 'user'"
                @change="treeSelectChange"
              />
            </div>
            <div style="margin-right: 5px">
              <el-button :disabled="objectTreeDisabled" size="mini" @click="multiStrategyToGroupChange">{{ $t('button.confirm2') }}</el-button>
            </div>
          </div >
          <grid-table
            ref="strategyTable"
            row-key="tempId"
            :col-model="colModel"
            :show-pager="false"
            :multi-select="true"
            :row-datas="strategyData"
            :is-saved-selected="false"
            @selectionChangeEnd="handleSelectionChange"
          />
        </div>
        <div style="float: right;">
          <el-button-group>
            <el-button type="primary" @click="reset">
              {{ $t('button.reset') }}
            </el-button>
            <el-button :loading="importLoading" @click="confirm">
              {{ $t('button.import') }}
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>
    <component :is="componentName" :ref="refName" v-bind="comProps" />
  </div>
</template>

<script>
import PrinterSet from '@/views/behaviorManage/hardware/printerSet'
import WaterMarkDialog from '@/views/behaviorManage/hardware/waterMark/waterMarkDialog'
import ImTool from '@/views/behaviorManage/application/imTool'
import ImFile from '@/views/behaviorManage/application/imFile'
import AppBlock from '@/views/behaviorManage/application/appBlock/editDlg'
import WinTitleBlock from '@/views/behaviorManage/application/winTitle'
import AppVersionLimit from '@/views/behaviorManage/application/appVersion'
import InstallPacket from '@/views/behaviorManage/application/install/installPacket/editDlg'
import SpecialPath from '@/views/behaviorManage/application/install/specialPath'
import Url from '@/views/behaviorManage/network/url'
import WebPost from '@/views/behaviorManage/network/webPost'
import WebPort from '@/views/behaviorManage/network/webPort/editDlg'
import WebFlow from '@/views/behaviorManage/network/webFlow/editDlg'
import NetCtrl from '@/views/behaviorManage/network/isolation'
import EmailKeywordBlock from '@/views/behaviorManage/network/emailKeyword'
import VideoStrategy from '@/views/behaviorManage/monitor/video/editDlg'
import BurnConfig from '@/views/behaviorManage/burner/burnConfig'
import ShareConfig from '@/views/behaviorManage/network/shareConfig'
import FileFilterSetup from '@/views/behaviorManage/monitor/fileFilter'
import Driver from '@/views/behaviorManage/hardware/driver/editDlg'
import OtherDeviceLimit from '@/views/behaviorManage/hardware/otherDeviceLimit'
import Content from '@/views/contentStrategy/strategy/content'
import EffectiveContentStrategy from '@/views/contentStrategy/strategy/effectiveContentStrategy'
import EffectiveContentConfig from '@/views/contentStrategy/strategy/effectiveContentConfig'
import BlueToothConfig from '@/views/behaviorManage/hardware/blueTooth'
import LogFilter from '@/views/behaviorManage/monitor/logFilter'
import MailReceiver from '@/views/dataEncryption/encryption/mailWhiteList/mailReceiver'
import MailCopy from '@/views/dataEncryption/encryption/mailWhiteList/mailCopy'
import EncryptSpecialPath from '@/views/dataEncryption/encryption/specialPath/editDlg'
import HttpWhiteList from '@/views/dataEncryption/encryption/httpWhiteList'
import BrowserFile from '@/views/behaviorAuditing/network/browserFileStrategy'
import Clipboard from '@/views/dataEncryption/encryption/clipboard/editDlg'
import Screenshot from '@/views/dataEncryption/encryption/screenshotStrategy/editDlg'
import WorkMode from '@/views/dataEncryption/encryption/workMode/editDlg'
import Process from '@/views/dataEncryption/encryption/processStg/editDlg'
import ProcessConfigStg from '@/views/dataEncryption/encryption/processStg/configEditDlg'
import SpecialSuffix from '@/views/dataEncryption/encryption/specialSuffix/editDlg'
import IpAndMacBind from '@/views/assets/systemMaintenance/IPAndMAC'
import ReadPermission from '@/views/dataEncryption/encryption/readPermission'
import ProcessCollectRule from '@/views/behaviorManage/application/processCollectRule'
import DiskScan from '@/views/dataEncryption/encryption/diskScan/editDlg'
import SmartEnc from '@/views/dataEncryption/encryption/smartEncStrategy/editDlg'
import TranslucentEnc from '@/views/dataEncryption/encryption/translucentEncStrategy/editDlg'
import UserDense from '@/views/dataEncryption/encryption/userDense'
import BackUpConfig from '@/views/dataEncryption/encryption/backUpConfig/editDlg'
import mcode from '@/views/dataEncryption/encryption/fileOutgoing/mcode'
import FileOutgoingConfig from '@/views/dataEncryption/encryption/fileOutgoing/config'
import ProcessMonitor from '@/views/behaviorManage/hardware/processMonitor'
import TerminalConfig from '@/views/system/terminalManage/terminalConfig'
import SysBaseConfig from '@/views/behaviorManage/application/sysBaseConfig'
import outgoingProcess from '@/views/dataEncryption/encryption/fileOutgoing/app'
import SoftwareBlacklist from '@/views/dataEncryption/encryption/fileOutgoing/softwareBlacklist'
import outgoingTemplate from '@/views/dataEncryption/encryption/fileOutgoing/template'
import outgoingScreenWaterMark from '@/views/dataEncryption/encryption/fileOutgoing/screenWaterMark'
import outgoingPrintWaterMark from '@/views/dataEncryption/encryption/fileOutgoing/printWaterMark'
import BatchEncOrDec from '@/views/dataEncryption/encryption/encOrDecStg/editDlg'
import TerminalMenu from '@/views/system/terminalManage/terminalMenu'
import ShortOffline from '@/views/dataEncryption/encryption/shortOfflineStrategy'
import NetDisk from '@/views/behaviorManage/network/netDisk'
import ApplySecurityAccess from '@/views/dataEncryption/encryption/applySecurityAccess'
import AppReportSet from '@/views/system/terminalManage/appReportSet'
import SensitiveBackupConfig from '@/views/contentStrategy/strategy/sensitiveBackupConfig'
import EnDeFileScan from '@/views/dataEncryption/encryption/EnDeFileScan'
import AppLogConfig from '@/views/behaviorManage/application/appLogConfig'
import ProcessInject from '@/views/system/terminalManage/processInject/editDlg'
import OperatorConfig from '@/views/dataEncryption/encryption/operatorConfig'
import OfficeWaterMark from '@/views/dataEncryption/encryption/officeWaterMark/editDlg'
import SoftwareLimit from '@/views/softwareManage/strategy/softwareLimit/editDlg'
import RequireInstall from '@/views/softwareManage/strategy/requireInstall/editDlg'
import RequireRun from '@/views/softwareManage/strategy/requireRun/editDlg'
import UsbFileConfig from '@/views/behaviorManage/hardware/usbFileConfig'
import SysAlarmConfig from '@/views/behaviorManage/application/sysAlarmConfig/editDlg'
import WifiCollect from '@/views/behaviorManage/network/wifiCollect/editDlg'
import WifiBlock from '@/views/behaviorManage/network/wifiBlock/editDlg'
import FtpControlConfig from '@/views/behaviorManage/network/ftpControl/editDlg'
import GroupPolicy from '@/views/behaviorManage/application/groupPolicy/editDlg'
import ForumUrlFilter from '@/views/behaviorManage/network/forumUrlFilter'
import WebBrowseUrlFilter from '@/views/behaviorManage/network/webBrowseURLFilter'
import OfflineLockScreen from '@/views/system/terminalManage/offlineLockScreen/editDlg'
import SoftWareTask from '@/views/assets/systemMaintenance/softWareTaskStrategy/taskList'
import PersonalizePolicy from '@/views/behaviorManage/hardware/personalizePolicy'
import IconRefreshDir from '@/views/dataEncryption/encryption/iconRefreshDir'
import DocumentTrack from '@/views/dataEncryption/fileTrace/documentTrack'
import BlindWatermark from '@/views/dataEncryption/fileTrace/blindWatermark'
import AdbLimit from '@/views/behaviorManage/hardware/adbLimit/editDlg'
import NetInterfaceLimit from '@/views/behaviorManage/hardware/netInterfaceLimit/editDlg'
import UsbInterfaceLimit from '@/views/behaviorManage/hardware/usbInterfaceLimit/editDlg'
import AppOpenSuffixStg from '@/views/system/terminalManage/mobileTerminalFileTool/appOpenSuffixStg'
import { autoImportStg } from '@/api/stgCommon';
import { stgActiveIconFormatter } from '@/utils/formatter';

export default {
  name: 'StrategyImport',
  components: {
    PrinterSet, WaterMarkDialog, ImTool, ImFile, AppBlock, WinTitleBlock,
    AppVersionLimit, InstallPacket, SpecialPath, Url, WebPost, WebPort, WebFlow, NetCtrl, EmailKeywordBlock, VideoStrategy, BurnConfig,
    ShareConfig, FileFilterSetup, Driver, OtherDeviceLimit, EffectiveContentStrategy, EffectiveContentConfig, Content, BlueToothConfig, LogFilter,
    EncryptSpecialPath, HttpWhiteList, BrowserFile, Clipboard, Screenshot, WorkMode, Process, ProcessConfigStg, SpecialSuffix,
    IpAndMacBind, ReadPermission, ProcessCollectRule, DiskScan, SmartEnc, TranslucentEnc, UserDense, BackUpConfig, mcode,
    FileOutgoingConfig, ProcessMonitor, TerminalConfig, SysBaseConfig, outgoingProcess, SoftwareBlacklist, outgoingTemplate, outgoingScreenWaterMark,
    outgoingPrintWaterMark, BatchEncOrDec, TerminalMenu, ShortOffline, NetDisk, ApplySecurityAccess, AppReportSet, SensitiveBackupConfig,
    EnDeFileScan, AppLogConfig, ProcessInject, OperatorConfig, OfficeWaterMark, SoftwareLimit, RequireInstall, RequireRun, UsbFileConfig,
    SysAlarmConfig, WifiCollect, WifiBlock, FtpControlConfig, GroupPolicy, ForumUrlFilter, WebBrowseUrlFilter, MailReceiver, MailCopy,
    SoftWareTask, OfflineLockScreen, PersonalizePolicy, IconRefreshDir, DocumentTrack, BlindWatermark, AdbLimit, NetInterfaceLimit, UsbInterfaceLimit, AppOpenSuffixStg },
  props: {
    //  是否显示
    visible: {
      type: Boolean,
      default: true
    },
    //  重置（将数据恢复到刚导入的状态）
    reset: {
      type: Function,
      default: function() {
      }
    }
  },
  data() {
    return {
      tableData: [],
      colModel: [
        { prop: 'tempId', label: 'keyId', fixWith: '50', sort: false },
        { prop: 'osType', label: 'osType', width: '100', formatter: this.osTypeFormatter },
        // { prop: 'strategyType', label: 'stgType', width: '100' },
        { prop: 'strategyTypeName', label: 'stgTypeName', width: '100' },
        { prop: 'name', label: 'stgName', width: '100', iconFormatter: stgActiveIconFormatter },
        { prop: 'strategyTypeNumber', label: 'details', width: '100', type: 'button',
          buttons: [
            { label: 'details', click: this.showDetail }
          ]
        },
        { prop: 'entityType', label: 'applicationObjType', width: '50px', type: 'select',
          options: [{ value: 1, label: 'terminal' }, { value: 2, label: 'user' }],
          rowOptions: 'rowOptions',
          change: this.changeEntityType },
        {
          prop: 'leafKey',
          label: 'applicationObj',
          width: '150px',
          type: 'treeSelect',
          checkedKeysFieldName: 'checkedKeys',
          isFilter: true,
          localSearch: false,
          multiple: true,
          checkStrictly: true,
          rewriteNodeClickFuc: false,
          disabled: (col, row) => {
            const entityType = [1, 3].includes(row.entityType) ? 1 : [2, 4].includes(row.entityType) ? 2 : row.entityType
            return entityType == null || entityType > 2 || entityType < 1
          },
          changeSelectedNodes: this.changeSelectedNodes,
          showContent: this.groupIdsFormatter
        },
        { prop: '', fixedWidth: 40, type: 'icon', iconClass: 'delete', iconFormatter: () => { return true }, style: 'cursor: pointer;', click: this.removeTerminal }
      ],
      importLoading: false,
      initData: [], //  初始化数据
      importType: 1,
      objectType: null,
      objectTypes: [],
      objectIds: [],
      multiStrategyDisabled: true,
      objectTreeDisabled: true,
      treeMultiple: false,     //  是否展示分组树的复选框
      componentName: '',
      refName: '',
      comProps: {},
      showDetailAble: false,
      showedTree: [{ value: 1, label: 'terminal' }, { value: 2, label: 'user' }],
      showedTreeInit: [{ value: 1, label: 'terminal' }, { value: 2, label: 'user' }],
      strategyData: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyTable']
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        // this.init()
      }
    }
  },
  created() {},
  methods: {
    init(strData) {
      this.gridTable.clearSelection();
      const data = strData || []
      if (data !== undefined && data !== null) {
        //  初始化数据
        let i = 1;
        data.forEach(item => {
          item.tempId = i++;
          item.checkedKeys = []
          item.leafKey = ''
          if (item.group != null) {
            item.checkedKeys.push(this.getIdPrefix(item.entityType) + item.objectId)
          }
          item.group = null
          item.rowEdit = { leafKey: false }
          this.loadStgBaseConfig(item)
        });
        this.strategyData = data
        this.initData = JSON.parse(JSON.stringify(data))
      }
    },
    clearData() {
      this.initData = []
      this.importType = 1
      this.objectType = null
      this.multiStrategyDisabled = true
      this.objectTreeDisabled = true
      this.objectIds = []
    },
    //  查看详情
    showDetail(row) {
      this.showDetailAble = true
      const object = row.metaData
      object.active = false
      object.remark = row.remark || object.remark
      object.strategyDefType = 0
      object.entityType = [1, 3].includes(row.entityType) ? 3 : [2, 4].includes(row.entityType) ? 4 : null;
      const array = row.checkedKeys || []
      object.entityId = array.length >= 1 ? array[0].substring(1) : null;
      if (object.ruleGroupIds != undefined) {
        // 这边使用大写开头的Content，因为content是html标签
        const refName = object.dripScan ? 'dripContent' : 'Content'
        this.resetComponent(refName)
        this.comProps['formable'] = false
        this.$nextTick(() => {
          this.$refs[refName].handleUpdate(object)
          //  不显示生效对象
          this.$refs[refName].query.strategyDefType = 1
        })
      } else {
        // objectType和objectId有值，且不等于6表示不等于策略包
        if (row.objectType && (row.objectId || row.objectId == 0) && row.objectType != 6) {
          object.entityType = row.objectType
          object.entityId = row.objectId
        }
        this.resetComponent(row.strategyType)
        this.comProps['formable'] = false
        this.$nextTick(() => {
          //  不显示生效对象
          this.notShowEffectiveObject(row.strategyType, row.strategyTypeNumber)
          //  调用handleShow 策略页面中存在elg弹窗组件
          if ([6, 14, 22, 50, 68, 69, 73, 87, 163, 205, 211, 212, 213, 218].includes(row.strategyTypeNumber)) {
            this.$refs[row.strategyType].handleShow(object, false)
          } else {
            this.$refs[row.strategyType].handleUpdate(object)
          }
        })
      }
    },
    //  不显示生效对象
    notShowEffectiveObject(strategyType, strategyTypeNumber) {
      //  如果elg弹窗存在stgDialog组件
      if (![68, 69, 163, 205, 212, 213, 218].includes(strategyTypeNumber)) {
        if (this.$refs[strategyType].$refs && this.$refs[strategyType].$refs['stgDlg'] !== undefined) {
          if (this.$refs[strategyType].$refs['stgDlg'].$refs['stgDlg'] !== undefined) {
            this.$refs[strategyType].$refs['stgDlg'].$refs['stgDlg'].isGeneralStrategy = false
          } else {
            this.$refs[strategyType].$refs['stgDlg'].isGeneralStrategy = false
          }
        } else {
          this.$refs[strategyType].query.strategyDefType = 1
        }
      }
    },
    //  确定多个策略指定部门
    multiStrategyToGroupChange() {
      const tempIds = this.gridTable.getSelectedKeys()
      this.strategyData = this.strategyData || []
      let item = null;
      for (let i = 0; i < this.strategyData.length; i++) {
        item = this.strategyData[i]
        if (tempIds.includes(item.tempId) && this.verifyObjectType(item, this.objectType)) {
          this.$set(this.strategyData[i], 'entityType', this.objectType);
          this.$set(this.strategyData[i], 'leafKey', this.getEntityTypeNameByEntityType(this.objectType))
          this.$set(this.strategyData[i].rowEdit, 'entityType', true);
          item.checkedKeys = []
          this.objectIds.forEach(i => item.checkedKeys.push(i))
          item.entityType = this.objectType
          item.leafKey = this.getEntityTypeNameByEntityType(this.objectType)
          item.group = this.getGroup(item.checkedKeys.length)
        }
      }
      for (let i = 0; i < this.strategyData.length; i++) {
        this.$set(this.strategyData[i].rowEdit, 'entityType', false);
      }
      this.$nextTick(() => {
        this.gridTable && this.gridTable.clearSelection()
        this.gridTable && this.gridTable.clearSaveSelection();
      })
      this.objectIds = []
      this.objectType = null
      this.objectTreeDisabled = true
      this.multiStrategyDisabled = true
    },
    //  校验是否满足此条件
    verifyObjectType(row, selectedEntityType) {
      let flag = false
      row.rowOptions.forEach(item => {
        if (!flag) {
          flag = selectedEntityType === item.value
        }
      })
      return flag;
    },
    //  改变单个分组信息
    changeSelectedNodes(nodes, row, index) {
      nodes = nodes || []
      row.checkedKeys = []
      for (let i = 0; i < nodes.length; i++) {
        row.checkedKeys.push(nodes[i])
      }
      row.group = this.getGroup(row.checkedKeys.length)
    },
    //  改变应用对象
    changeEntityType(row, col, rowIndex) {
      row.rowEdit['leafKey'] = true
      row.leafKey = this.getEntityTypeNameByEntityType(row.entityType)
      row.checkedKeys = []
      row.group = this.getGroup(row.checkedKeys.length)
    },
    objectTypeChange(selected) {
      if (this.objectType !== null) {
        this.objectTreeDisabled = false
      }
    },
    treeSelectChange(selected, row) {
      this.objectIds = [];
      (selected || []).forEach(item => this.objectIds.push(item));
    },
    getGroup(length) {
      return (length = length || 0) > 0 ? (this.$t('pages.importAssignGroupFew', { len: length })) : this.$t('pages.importNotAssignGroup');
    },
    //  获取Id前缀
    getIdPrefix(entityType) {
      let t = ''
      switch (entityType) {
        case 1: t = 'T'; break;
        case 2: t = 'U'; break;
        case 3: t = 'G'; break;
        case 4: t = 'G'; break;
      }
      return t;
    },
    getEntityType(entityType) {
      let t = 1
      if (entityType !== undefined && entityType != null) {
        switch (entityType) {
          case 1: t = 1; break;
          case 2: t = 2; break;
          case 3: t = 1; break;
          case 4: t = 2; break;
        }
      }
      return t;
    },
    handleFilter() {
      this.gridTable.execRowDataApi()
    },
    handleImport() {
    },
    handleExport() {
    },
    groupIdsFormatter(row) {
      return (row.group || null) == null ? this.$t('pages.noDepartmentSpecified') : row.group;
    },
    getEntityTypeNameByEntityType(entityType) {
      entityType = entityType || null
      if (entityType == null) {
        return '';
      }
      return [1, '1', 3, '3'].includes(entityType) ? 'terminal' : [2, '2', 4, '4'].includes(entityType) ? 'user' : '';
    },
    entityFormatter(row) {
      let t = 1;
      const entity = row.entityType || null
      if (entity != null) {
        switch (row.entityType) {
          case 1: t = 1; break;
          case 2: t = 2; break;
          case 3: t = 1; break;
          case 4: t = 2; break;
        }
      }
      return t;
    },
    osTypeFormatter(row, data) {
      let osType = row.osType || null
      if (osType !== undefined && osType != null) {
        switch (osType) {
          case 1 : osType = 'windows'; break;
          case 2 : osType = 'linux'; break;
          case 4 : osType = 'mac'; break;
          case 7 : osType = 'windows, linux, mac'; break;
          case 8 : osType = this.$t('pages.mobileTerminal'); break;
          case 15: osType = 'windows, linux, mac,' + this.$t('pages.mobileTerminal'); break;
          default: osType = '{ ' + osType + ' } ' + this.$t('pages.unknownSystem');
        }
      }
      return osType;
    },
    //  导入策略,提交到数据库
    confirm() {
      const _this = this
      const array = []
      let i = 1;
      this.strategyData = this.strategyData || []
      if (this.strategyData.length === 0) {
        this.clearData()
        this.back()
        return;
      }
      this.strategyData.forEach(strategy => {
        strategy.objectIds = []
        strategy.checkedKeys = strategy.checkedKeys || []
        strategy.checkedKeys.forEach(item => {
          if (strategy.entityType === 1) {
            strategy.objectIds.push({ objectType: item.startsWith('G') ? 3 : 1, objectId: item.substring(1) })
          } else if (strategy.entityType === 2) {
            strategy.objectIds.push({ objectType: item.startsWith('G') ? 4 : 2, objectId: item.substring(1) })
          }
        });
        if (strategy.objectIds.length <= 0) {
          array.push(i)
        }
        i++;
      });
      if (array.length > 0) {
        this.$message({
          showClose: true,
          message: this.$t('pages.importVerify_Msg', { info: array.join(',') }),
          customClass: array.length > 20 ? 'importWaringMessage' : '',
          type: 'warning'
        });
        return;
      }
      this.importLoading = true
      //  提交到数据库
      autoImportStg({ importType: this.importType, strategies: this.strategyData }).then(res => {
        if (res.code === 20000) {
          _this.$message({
            message: _this.$t('pages.strategyImportSuccess'),
            type: 'success',
            duration: 2000
          })
          _this.clearData()
          _this.back()
        } else {
          const msg = _this.$t('pages.strategyImportFail') + _this.$t('pages.strategyImportFail') + ': ' + res.message || ''
          console.log(msg)
          _this.$message({
            message: msg,
            type: 'error',
            duration: 2000
          })
        }
        this.importLoading = false
      }).catch((e) => {
        const msg = _this.$t('pages.strategyImportFail') + _this.$t('pages.strategyImportFail') + ': ' + e.message || ''
        console.log(msg)
        _this.$message({
          message: msg,
          type: 'error',
          duration: 2000
        })
        this.importLoading = false
      });
    },
    //  关闭所有
    closeAll() {
      this.gridTable.clearSelection();
    },
    //  节点改变时
    handleSelectionChange(rowDatas) {
      if (rowDatas.length > 0) {
        // this.getCommonEntityType(rowDatas)
        this.objectTypes = [{ value: 1, label: this.$t('components.terminal') }, { value: 2, label: this.$t('components.user') }]
        this.multiStrategyDisabled = false
      } else {
        this.objectIds = []
        this.objectType = null
        this.multiStrategyDisabled = true
        this.objectTreeDisabled = true
      }
    },
    //  获取交集应用对象
    getCommonEntityType(rows) {
      let array = null;
      let ctFlag = false;
      (rows || []).forEach(item => {
        if (array === null) {
          array = JSON.parse(JSON.stringify(item.rowOptions))
        } else {
          if (item.rowOptions && item.rowOptions.length < array.length) {
            array = JSON.parse(JSON.stringify(item.rowOptions))
          } else if (item.rowOptions && item.rowOptions.length === array.length && array.length === 1) {
            ctFlag = item.rowOptions[0].value !== array[0].value
          }
        }
      });
      if (ctFlag) {
        this.objectType = null
        this.objectTypes = []
      } else {
        this.objectTypes = array
      }
      this.objectTypes.forEach(item => {
        if (item.value === 1) {
          item.label = this.$t('components.terminal')
        } else if (item.value === 2) {
          item.label = this.$t('components.user')
        }
      })
    },
    resetComponent(name) {
      this.refName = name
      // ref与组件名不同的映射
      const nameOption = {
        waterMarkStrategy: 'water-mark-dialog',
        screenWaterMarkStrategy: 'water-mark-dialog',
        installStrategy: 'install-packet',
        uninstallStrategy: 'install-packet',
        effectiveFunction: 'effective-content-strategy',
        effectiveFuncConfig: 'EffectiveContentConfig',
        httpWhiteListProcessFilter: 'http-white-list',
        autoBackupFilterStrategy: 'special-suffix',
        outgoingCodeWhiteList: 'mcode',
        outgoingConfig: 'file-outgoing-config',
        videoStrategy: 'video-strategy', // video是html标签
        dripContent: 'Content',
        softwareLimitStrategy: 'SoftwareLimit',
        softRequiredInstallStrategy: 'RequireInstall',
        softRequiredRunStrategy: 'RequireRun',
        groupPolicyStrategy: 'GroupPolicy',
        forumURLFilter: 'ForumUrlFilter',
        webBrowseURLFilter: 'webBrowseUrlFilter',
        ftpControllerConfig: 'FtpControlConfig',
        mailWhiteListStrategy: 'MailReceiver',
        mailCarbonCopyStrategy: 'MailCopy',
        appOpenSuffixStrategy: 'AppOpenSuffixStg'
      }
      const index = name.toLowerCase().indexOf('strategy')
      // 根据ref名称，修改组件名
      if (!nameOption[name]) {
        this.componentName = index > 0 ? name.substring(0, index) : name
      } else {
        this.componentName = nameOption[name]
      }
      // 部分页面需要传入的 prop
      const option = {
        installStrategy: { defaultLimitType: 1 },
        uninstallStrategy: { defaultLimitType: 2 },
        waterMarkStrategy: { stgTypeNumber: 2 },
        screenWaterMarkStrategy: { stgTypeNumber: 44 },
        httpWhiteListProcessFilter: { tabName: 'processTab' },
        processStrategy: { tabName: 'strategyTab' },
        processConfigStg: { tabName: 'configTab' },
        specialSuffixStrategy: { tabName: 'specialFileExt' },
        autoBackupFilterStrategy: { tabName: 'autoBackupFilter' },
        dripContent: { dripAble: true }
      }
      this.comProps = { listable: false, formable: false, ...option[name] }
    },
    removeTerminal(e, row, rowIndex) {
      const index = this.strategyData.indexOf(row)
      this.$confirmBox(this.$t('pages.confirmDeleteTerm') + row.name + '？', this.$t('text.prompt')).then(() => {
        this.strategyData.splice(index, 1)
      }).catch(() => {
      })
    },
    //  关闭当前导入页面mousewheel
    back() {
      const _this = this
      this.$nextTick(() => {
        _this.$refs.strategyTable.total = 0
        _this.gridTable && this.gridTable.clearSelection()
      })
      this.$emit('closeImport')
    },
    /**
     * 加载策略的基本设置
     */
    loadStgBaseConfig(row) {
      row.rowOptions = [{ value: 1, label: 'terminal' }, { value: 2, label: 'user' }]
      const stgBaseConfigMap = this.$store.getters.stgBaseConfig
      const strategyType = row.strategyType
      const stgBaseConfig = Object.values(stgBaseConfigMap)
      for (let i = 0; i < stgBaseConfig.length; i++) {
        const c = stgBaseConfig[i]
        if (c['strategyKey'] === strategyType) {
          this.osType = c['osType'] // 策略支持的终端类型
          const usedScope = c['usedScope']
          if (usedScope != null) {
            // 策略限制只能应用终端或操作员
            if (usedScope === 1) {
              row.rowOptions = row.rowOptions.filter(item => { return item.label === 'terminal' })
            } else if (usedScope === 2) {
              row.rowOptions = row.rowOptions.filter(item => { return item.label === 'user' })
            }
          }
          break
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .app-container.strategy-import {
    width: 100%;
    height: calc(100vh - 90px);
    overflow-y: scroll;
    position: absolute;
  }
  >>>.el-table__indent {
    padding-left: 20px;
  }
  .import-tree-container-1 {
    position: absolute;
    width: 100%;
    height: 90%;
    margin: 0;
    background-color: #0c161f;
    z-index: 1;
    .app-container {
      height: 0;
      background-color: #00ccff;
    }
  }
  .import-tree-container-2 {
    width: 98%;
    height: 90%;
    float:left;
  }
  .objectType-select {
    .el-input__inner {
      height: 30px !important;
    }
  }
  .importWaringMessage {
    width: 50%;
  }
</style>

