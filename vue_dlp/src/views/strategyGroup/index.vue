<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStgGroup') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delStgGroup') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.stgGroupName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :autoload="false" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="180px" style="width: 700px;">
        <stg-target-form-item
          ref="stgTargetItem"
          :entity-type-span="isSetTarget ? 12 : 24"
          :tree-width="468"
          label-width="105px"
          :form-data="temp"
          :term-entity-type-value="1"
          :user-entity-type-value="2"
          :tree-visible="isSetTarget"
          :entity-type-disabled="dialogStatus == 'update'"
          @entityTypeData="data => formatStrategyTypeOptionByObjectType(data)"
          @entityNodeData="nodeData => checkedChange(nodeData)"
        />
        <FormItem :label="$t('pages.stgGroupName')" prop="name" label-width="105px">
          <el-input v-model="temp.name" v-trim maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark" label-width="105px">
          <el-input v-model="temp.remark" maxlength="100"></el-input>
        </FormItem>
        <FormItem v-if="isSetTarget" :label="$t('components.enable')" label-width="105px">
          <el-switch v-model="temp.active" :active-value="1" :inactive-value="0"/>
        </FormItem>
        <el-card ref="card" :body-style="cardStyle">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.behaviorGroup_text1') }}</span>
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                <i18n path="pages.behaviorGroup_text2">
                  <br slot="br"/>
                </i18n>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <span v-show="showStgError" style="color:red;float:right;">{{ $t('pages.behaviorGroup_text8') }}</span>
          </div>
          <FormItem v-for="(type, index) in strategyTypeOptions" :key="index" :label="generateTitle(type.title)" class="content-flex">
            <el-select
              :ref="`stg${index}`"
              v-model="selectedStgIds[type.number]"
              filterable
              clearable
              multiple
              :multiple-limit="type.osAble || type.multiActive ? 0 : 1"
              :placeholder="$t('text.select')"
              @visible-change="data => visibleChange(data, index)"
              @change="stgSelectChange(type)"
            >
              <el-option v-for="item in strategyOption[type.number]" :key="item.id" :label="item.name" :value="item.id" :disabled="item.disabled">
                <span style="float: left">{{ item.name }}</span>
                <span v-if="type.osAble" style="float: right; color: #8492a6; font-size: 13px; padding-right:20px;">{{ osTypeFormatter(item, item.osType) }}</span>
              </el-option>
            </el-select>
            <link-button :always-show="true" :click-func="'handleCreateStrategy'" @handleCreateStrategy="handleCreateStrategy(type.editUrl + '/prepare',type.param)"/>
          </FormItem>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="doSubmit">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getStrategyList, getByName, createStrategy, updateStrategy, deleteStrategy, listStgByGroupType, getStgIdCodeMap } from '@/api/behaviorManage/behaviorGroup'
import { getContentStgIdCodeMap } from '@/api/contentStrategy/strategy/content'
import { formatMenuCode } from '@/router'
import { initTimestamp } from '@/utils'
import { generateTitle } from '@/utils/i18n'
import { stgActiveIconFormatter, osTypeFormatter } from '@/utils/formatter'
import { getOsTypeDict, getDictLabel } from '@/utils/dictionary'
import { isEmpty } from '@/utils/validate'

export default {
  name: 'StrategyGroup',
  props: {
    groupId: { type: Number, default: 1 }, // 策略包类型 1数据安全策略包 2行为策略包 3内容策略包 (当groupId等于0时，这个策略包是不用来下发的，只作为离线策略导出来源)
    stgTypeOps: {
      type: Array,
      default: function() {
        return []
      }
    },
    groupName: {
      type: String,
      default: function() {
        return this.$t('pages.allGroup')
      }
    },
    targetOption: {
      type: Array,
      default() {
        return [
          { value: 1, label: this.$t('components.terminalG') },
          { value: 2, label: this.$t('components.userG') }
        ]
      }
    },
    isSetTarget: {    // 是否可以设置策略包的应用对象
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgGroupName', fixedWidth: '150', sort: 'custom', iconFormatter: this.activeFormatter },
        { prop: 'strategyIds', label: 'childStg', width: '200', formatter: this.strategyFormatter },
        { prop: 'entityName', label: 'effectiveObject', width: '200', formatter: this.targetFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      strategyTypeOptions: [],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined
      },
      deleteable: false,
      addBtnAble: false,
      showStgError: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: '',
        name: '',
        remark: '',
        groupType: '',
        active: 0,
        strategyIds: '',
        contentStgIds: '',
        objectType: 1, // 1按终端，2按操作员
        objectIds: null,
        objectGroupIds: null,
        inactiveStgIds: [], // 因为启用冲突，需要停用的策略ID集合
        checkedIds: []
      },
      checkedKeys: [],
      contentStgCodes: [23, 24], // 内容策略编码，这些编码的策略保存到contentStgIds
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.groupName, 'update'),
        create: this.i18nConcatText(this.groupName, 'create')
      },
      cardStyle: {
        height: 'auto',
        maxHeight: '270px',
        position: 'relative',
        overflow: 'auto'
      },
      expandStyle: 'transform: rotate(180deg);',
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        checkedIds: [
          { required: true, validator: this.checkedIdsValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      strategyOption: {}, // 数据结构为{1: [A类策略集合],2: [B类策略集合]}
      selectedStgIds: [],
      currentSelect: null, // 当前触发下拉框的select
      firstInit: true,
      osTypeValue: [],
      difActiveStgs: {},
      activeMode: 1
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    '$store.state.commonData.stgBaseConfig'() {
      this.loadStgBaseConfig()
    }
  },
  created() {
    getOsTypeDict().forEach(d => this.osTypeValue.push(d.value))
    this.query.groupType = this.groupId
    this.defaultTemp.groupType = this.groupId
    this.resetTemp()
    this.initStrategyType()
    this.initStrategyOption(true)
  },
  activated() {
    if (!this.firstInit) {
      this.initStrategyOption()
    }
  },
  methods: {
    generateTitle,
    getDictLabel,
    getOsTypeDict,
    osTypeFormatter,
    objectTree() {
      return this.$refs.objectTree
    },
    loadStgBaseConfig() {
      const stgBaseConfigMap = this.$store.getters.stgBaseConfig
      if (!stgBaseConfigMap || Object.keys(stgBaseConfigMap).length === 0) {
        this.$store.dispatch('commonData/setStgBaseConfig')
      } else {
        for (let i = 0; i < this.stgTypeOps.length; i++) {
          const c = this.stgTypeOps[i]
          const stgConfig = stgBaseConfigMap[c.number]
          if (stgConfig) {
            c.usedScope = stgConfig.usedScope
            c.osAble = stgConfig.osAble
            c.active = stgConfig.active
            c.multiActive = stgConfig.multiActive
          } else {
            c.osAble = false
          }
        }
        this.formatStrategyTypeOptionByObjectType()
      }
    },
    formatStrategyTypeOptionByObjectType(data) {
      const objType = !data ? { 1: 1, 3: 1, 2: 2, 4: 2 }[this.temp.objectType] : data
      this.strategyTypeOptions.splice(0)
      for (let i = 0; i < this.stgTypeOps.length; i++) {
        const c = this.stgTypeOps[i]
        if (c.active && (objType & c.usedScope) > 0) {
          this.strategyTypeOptions.push(c)
        }
      }
      this.strategyTypeOptions.forEach(item => this.stgSelectChange(item))
      // if (this.objectTree()) {
      //   this.objectTree().clearSelectedNode()
      // }
    },
    initStrategyType() {
      formatMenuCode(this.$store.getters.permission_routes, this.stgTypeOps)
      this.loadStgBaseConfig()
    },
    initStrategyOption(reloadTable) {
      const strategyOption = {}
      listStgByGroupType(this.groupId).then(respond => {
        respond.data.forEach(stg => {
          stg.disabled = false
          const stgs = strategyOption[stg.stgTypeNumber]
          if (stgs) {
            stgs.push(stg)
          } else {
            strategyOption[stg.stgTypeNumber] = [stg]
          }
        })
        this.strategyOption = strategyOption
        if (reloadTable) {
          this.firstInit = false
          this.handleFilter()
        }
        // 重新组装好数据后，通过以下代码触发vue重新渲染界面
        this.strategyTypeOptions.splice(0, 0)
        this.strategyTypeOptions.forEach(item => this.stgSelectChange(item))
      })
    },
    async initStrategySelectValue() {
      this.selectedStgIds = []
      if (this.temp.strategyIds) {
        await getStgIdCodeMap({ ids: this.temp.strategyIds }).then(resp => {
          this.temp.strategyIds.split(',').forEach(stgId => {
            const stgCode = resp.data[stgId]
            if (stgCode) {
              if (!this.selectedStgIds[stgCode]) this.selectedStgIds[stgCode] = []
              this.selectedStgIds[stgCode].push(Number.parseInt(stgId))
            }
          })
        })
      }
      if (this.temp.contentStgIds) {
        await getContentStgIdCodeMap({ ids: this.temp.contentStgIds }).then(resp => {
          this.temp.contentStgIds.split(',').forEach(stgId => {
            const stgCode = resp.data[stgId]
            if (stgCode) {
              if (!this.selectedStgIds[stgCode]) this.selectedStgIds[stgCode] = []
              this.selectedStgIds[stgCode].push(Number.parseInt(stgId))
            }
          })
        })
      }
    },
    // select 下拉框出现/隐藏时触发
    visibleChange(data, index) {
      if (data) {
        // 延时添加滚动事件，避免在子策略间切换时，无法添加滚动事件
        setTimeout(() => {
          const ref = `stg${index}`
          this.currentSelect = this.$refs[ref]
          // 下拉框出现时，添加滚动事件
          this.addScrollEvent()
        }, 200);
      } else {
        // 下拉框隐藏时，移除滚动事件
        this.removeScrollEvent()
      }
    },
    // 添加滚动事件
    addScrollEvent() {
      const scrollDom = this.$refs.card.$el.querySelector('.el-card__body')
      scrollDom.addEventListener('scroll', this.bodyScroll)
    },
    // 移除滚动事件
    removeScrollEvent() {
      const scrollDom = this.$refs.card.$el.querySelector('.el-card__body')
      scrollDom.removeEventListener('scroll', this.bodyScroll)
    },
    // cardBody 的滚动事件
    bodyScroll() {
      // 隐藏 select 的下拉框
      this.currentSelect[0].blur()
    },
    stgSelectChange(type) {
      this.showStgError = false
      if (type.osAble) {
        const selectedIds = this.selectedStgIds[type.number]
        const stgs = this.strategyOption[type.number]
        if (!selectedIds || selectedIds.length === 0) {
          if (stgs) stgs.forEach(stg => { stg.disabled = false })
          return
        }
        const selectedOsTypes = []
        for (let i = 0; i < stgs.length && selectedOsTypes.length < selectedIds.length; i++) {
          const stg = stgs[i]
          if (selectedIds.indexOf(stg.id) > -1) {
            selectedOsTypes.push(stg.osType)
          }
        }
        stgs.forEach(stg => {
          stg.disabled = false
          if (selectedIds.length > 0 && selectedIds.indexOf(stg.id) < 0) {
            stg.disabled = selectedOsTypes.indexOf(stg.osType) > -1 || this.osTypeValue.indexOf(stg.osType) < 0 || this.osTypeValue.indexOf(selectedOsTypes[0]) < 0
          }
        })
      }
    },
    validSubmitData() {
      if (!this.temp.stgSize || this.temp.stgSize < 1) {
        this.showStgError = true
        return false
      }
      return true
    },
    formatSubmitData() {
      const temp = this.temp
      // temp.objectIds = []
      // temp.objectGroupIds = []
      temp.strategyIds = []
      temp.contentStgIds = []
      temp.stgSize = 0
      const effectiveStgCode = []
      this.strategyTypeOptions.forEach(op => effectiveStgCode.push(op.number))
      this.selectedStgIds.filter((o, number) => {
        const isEffect = o && effectiveStgCode.indexOf(number) > -1
        if (isEffect) {
          if (Array.isArray(o)) {
            for (let i = 0; i < o.length; i++) {
              this.pushRelatedData(10, o[i], number)
              temp.stgSize++
            }
          } else {
            this.pushRelatedData(10, o, number)
            temp.stgSize++
          }
        }
        return true
      })
      // if (this.isSetTarget) {
      //   this.objectTree().getSelectedNode().forEach(nodeData => {
      //     if ('G' === nodeData.type) {
      //       const bizType = this.temp.objectType === 1 ? 3 : 4
      //       this.pushRelatedData(bizType, nodeData.dataId)
      //     } else {
      //       this.pushRelatedData(nodeData.type, nodeData.dataId)
      //     }
      //   })
      // }
    },
    pushRelatedData(type, value, code) {
      if (type === 10) {
        if (this.contentStgCodes.indexOf(code) < 0) {
          this.temp.strategyIds.push(value)
        } else {
          this.temp.contentStgIds.push(value)
        }
      } else if (type > 2) {
        this.temp.objectGroupIds.push(value)
      } else {
        this.temp.objectIds.push(value)
      }
    },
    initObjectSelectValue() {
      // if (this.temp.objectNode) {
      //   this.objectTree().clearSelectedNode()
      //   this.objectTree().checkSelectedNode(this.temp.objectNode)
      // }
    },
    rowDataApi: function(option) {
      const tempOption = Object.assign({}, this.query, option)
      return getStrategyList(tempOption)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleRefresh() {
      this.query.searchInfo = ''
      this.handleFilter()
    },
    resetTemp() {
      this.showStgError = false
      this.submitting = false
      this.selectedStgIds = []
      this.checkedKeys.splice(0)
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      // if (this.objectTree()) {
      //   this.objectTree().clearSelectedNode()
      //   this.objectTree().clearFilter()
      // }
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreateStrategy(url, query) {
      const queryTemp = Object.assign({}, query, { treeable: false })
      this.$router.push({ path: url, query: queryTemp })
    },
    handleCreate() {
      this.resetTemp()
      initTimestamp(this)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.formatStrategyTypeOptionByObjectType()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      // 下面resetTemp，其实是为了重置树形选中的数据，防止受之前的temp的影响
      this.temp = Object.assign({ checkedIds: [] }, row) // copy obj
      // if (row.objectNode) {
      //   this.checkedKeys = row.objectNode.map(node => node.id)
      // }
      if (this.temp.objectIds && !Array.isArray(this.temp.objectIds)) {
        this.temp.objectIds = this.temp.objectIds.split(',')
      }
      if (this.temp.objectGroupIds && !Array.isArray(this.temp.objectGroupIds)) {
        this.temp.objectGroupIds = this.temp.objectGroupIds.split(',')
      }
      initTimestamp(this)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.initStrategySelectValue().then(resp => {
          this.formatStrategyTypeOptionByObjectType()
          this.initObjectSelectValue()
          this.$refs['dataForm'].clearValidate()
        })
      })
    },
    getSelectStrategyInfo(stgTypeNumber) { // 获取某个编号已经选中的策略信息
      const stgInfos = []
      const stgIds = this.selectedStgIds[stgTypeNumber]
      if (stgIds && stgIds.length > 0) {
        const stgs = this.strategyOption[stgTypeNumber]
        for (let i = 0; i < stgs.length; i++) {
          const stg = stgs[i]
          if (stgIds.indexOf(stg.id) > -1) {
            stgInfos.push(stg)
          }
          if (stgInfos.length === stgIds.length) {
            break
          }
        }
      }
      return stgInfos
    },
    doSubmit() {
      const func = this.dialogStatus === 'create' ? this.createData : this.updateData
      this.submitting = true
      this.temp.checkedIds = !isEmpty(this.temp.objectIds) || !isEmpty(this.temp.objectGroupIds) ? [1] : []
      this.temp.inactiveStgIds = []
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          this.submitting = false
          return
        }
        this.formatSubmitData()
        if (!this.validSubmitData()) {
          this.submitting = false
          return
        }
        func()
      })
    },
    createData() {
      const tempData = Object.assign({}, this.temp, {
        objectIds: this.temp.objectIds ? this.temp.objectIds.toString() : null,
        objectGroupIds: this.temp.objectGroupIds ? this.temp.objectGroupIds.toString() : null,
        strategyIds: this.temp.strategyIds ? this.temp.strategyIds.toString() : null,
        contentStgIds: this.temp.contentStgIds ? this.temp.contentStgIds.toString() : null
      })
      createStrategy(tempData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.submitting = false
      })
    },
    updateData() {
      const tempData = Object.assign({}, this.temp, {
        objectIds: this.temp.objectIds ? this.temp.objectIds.toString() : null,
        objectGroupIds: this.temp.objectGroupIds ? this.temp.objectGroupIds.toString() : null,
        strategyIds: this.temp.strategyIds ? this.temp.strategyIds.toString() : null,
        contentStgIds: this.temp.contentStgIds ? this.temp.contentStgIds.toString() : null
      })
      updateStrategy(tempData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.submitting = false
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    strategyNameValidator(rule, value, callback) {
      getByName({ name: value, groupType: this.query.groupType }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    checkedChange(selected) {
      this.temp.checkedIds = selected
      this.$refs['dataForm'].validateField('checkedIds')
    },
    checkedIdsValidator(rule, value, callback) {
      if (this.temp.checkedIds.length > 0) {
        callback()
      } else {
        callback(this.$t('pages.behaviorGroup_text9'))
      }
    },
    strategyFormatter(row, data) {
      const stgName = []
      if (row.strategyIds) {
        const stgIdArr = row.strategyIds.split(',')
        for (const key in this.strategyOption) {
          const beans = this.strategyOption[key]
          beans.forEach(bean => {
            if (stgIdArr.indexOf(bean.id + '') > -1) {
              stgName.push(bean.name)
            }
          })
        }
      }
      if (row.contentStgIds) {
        const contentStgIdArr = row.contentStgIds.split(',')
        this.contentStgCodes.forEach(stgCode => {
          const beans = this.strategyOption[stgCode]
          if (beans) {
            beans.forEach(bean => {
              if (contentStgIdArr.indexOf(bean.id + '') > -1) {
                stgName.push(bean.name)
              }
            })
          }
        })
      }
      return stgName.join(', ')
    },
    activeFormatter(row, data) {
      if (this.isSetTarget) {
        return stgActiveIconFormatter(row)
      }
      return null
    },
    targetFormatter(row, data) {
      // 终端（组）、操作员（组）
      const type = row.objectType && row.objectType === 2 ? this.$t('components.userG') : this.$t('components.terminalG')
      // 离线策略包的应用对象只具体到分组，直接返回分组即可
      if (row.groupType === 0) {
        return type
      }
      // 生效对象label，需要对label转义
      // const label = row.objectNode ? row.objectNode.reduce((label, node) => label ? `${label}、${this.html2Escape(node.label)}` : this.html2Escape(node.label), '') : ''
      const label = this.html2Escape(row.entityName)
      return label ? `${type}：${label}` : ''
    }
  }
}
</script>
<style lang="scss" scoped>
  .strategyCon {
    height: 100%;
    overflow-y: auto;
    padding-bottom: 1px;
  }
  .target-tree {
    >>>.el-select__tags {
      margin-top: 2px;
    }
  }
</style>
