<template>
  <div class="app-container strategy">
    <div v-show="homVisible" style="position: absolute; width: 98%; height: 98%;">
      <div class="tree-container" :class="showTree?'':'hidden'">
        <strategy-target-tree
          ref="strategyTargetTree"
          :showed-tree="['terminal', 'user', 'strategyType']"
          :other-tab-panes="otherTabPanes"
          :expand-on-click-node="false"
          @data-change="strategyTargetNodeChange"
        />
      </div>

      <div class="table-container">
        <div class="toolbar">
          <el-button type="primary" size="mini" @click="toggleTreeMenu">
            <svg-icon icon-class="tree" />
          </el-button>
          <el-button type="primary" style="margin-left: 0" size="mini" :title="$t('button.refresh')" @click="refresh">
            <svg-icon icon-class="refresh" />
          </el-button>
          <el-button v-permission="'149'" style="margin-left: 0" icon="el-icon-download" size="mini" :disabled="paneName !== 'strategyType' && (query.objectType === undefined || query.objectId === undefined)" @click="excelStgExportHandler">{{ $t('pages.strategyOverViews_export') }}</el-button>
          <el-button v-permission="'523'" style="margin-left: 0" icon="el-icon-delete" size="mini" :disabled="objectTemp.length === 0" @click="clearStgHandler">{{ $t('pages.clearStrategy') }}</el-button>

          <!--<el-checkbox v-model="query.active">{{ $t('pages.showActive') }}</el-checkbox>-->
          <el-button type="primary" style="float: right; margin-right: 10px" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <div style="float: right; display: flex">
            <tree-select
              v-show="paneName !== 'strategyType'"
              ref="stgTypeTreeSelect"
              style="width: 200px; margin-right: 10px;"
              node-key="dataId"
              :height="350"
              :width="350"
              :data="objectTemp"
              :collapse-tags="true"
              :multiple="true"
              :clearable="true"
              :placeholder="$t('pages.strategyOverViews_text1')"
              @change="stgTypeChange"
            />
            <el-input v-model="query.searchInfo" v-trim clearable :placeholder="this.$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" ></el-input>
          </div>
          <el-dropdown v-if="selectOnlineTerm" style="float: right; margin-right: 10px;" trigger="click" @command="handleMoreClick">
            <el-button size="mini">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content">
                  {{ $t('pages.strategyOverViewsTermStgModeMessage1') }}<br>
                  {{ $t('pages.strategyOverViewsTermStgModeMessage2') }}
                </div>
                <span>{{ query.onLineUserPriority ? $t('pages.strategyOverViewsTermStgMode2') : $t('pages.strategyOverViewsTermStgMode1') }}<i class="el-icon-arrow-down el-icon--right"></i></span>
              </el-tooltip>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1" :disabled="!query.onLineUserPriority" icon="el-icon-help">{{ $t('pages.strategyOverViewsTermStgMode1') }}</el-dropdown-item>
              <el-dropdown-item :command="2" :disabled="query.onLineUserPriority" icon="el-icon-s-help">{{ $t('pages.strategyOverViewsTermStgMode2') }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button v-if="importAble" icon="el-icon-upload2" style="margin-left: 10px;" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
          <el-button v-if="exportAble" icon="el-icon-download" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        </div>
        <grid-table
          v-show="paneName === 'strategyType'"
          ref="strategyTypeList"
          :stripe="false"
          row-key="myDataId"
          default-expand-all
          :col-model="stgColModel"
          :cell-style="cellStyle"
          :show-pager="false"
          :multi-select="false"
          :row-data-api="stgRowDataApi"
          :after-load="stgFilterRowDatas"
        />
        <grid-table
          v-show="paneName !== 'strategyType'"
          ref="strategyList"
          :stripe="false"
          row-key="myDataId"
          default-expand-all
          :col-model="colModel"
          :cell-style="cellStyle"
          :show-pager="false"
          :multi-select="false"
          :row-data-api="rowDataApi"
          :after-load="filterRowDatas"
        />
      </div>
    </div>

    <!--
      提示：使用resetComponent 方法，动态切换组件。
      ref名称与组件名称不一样时，需要在resetComponent方法中添加映射。如：共用组件的情况。
      ref名称中包含Strategy时，import 组件时，组件不需要带Strategy。部分想带Strategy的需添加映射。
      eg: ref="xxxStrategy"
      import xxx from '@/xxxxx/xxxx'
    -->
    <component :is="componentName" :ref="refName" v-bind="comProps" />
    <strategy-import ref="strategyImport" :visible="strategyImportVisible" :reset="reset" @closeImport="closeImport"/>
    <export-strategy ref="strategyExport" style="position: absolute; width: 98%; height: 98%" :strategy-tree="stgTypeTree" :visible="strategyExportVisible" @closeExport="closeExport"/>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processStgLib_Msg89')"
      :visible.sync="dialogVisible"
      width="600px"
      @dragDialog="handleDrag"
      @close="uploadClose"
    >
      <el-upload
        ref="uploadFileList"
        action="1111"
        class="upload-demo"
        style="display:inline-block;max-width: 670px;"
        :limit="fileLimit"
        list-type="text"
        :file-list="fileList"
        :accept="accept"
        :auto-upload="false"
        :http-request="onUpload"
        :on-change="onChange"
        :on-remove="onRemove"
        :on-error="onError"
      >
        <el-tooltip class="item" effect="dark" :content="$t('pages.processStgLib_Msg74', { num: fileLimit })" placement="right-start">
          <el-button size="small" type="primary">{{ uploadBtnName }}</el-button>
        </el-tooltip>
        <i v-show="fileList.length > 0" class="el-icon-delete-solid" :title="$t('pages.processStgLib_Msg76')" @click="clearFiles"></i>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData">{{ $t('button.confirm') }}</el-button>
        <el-button @click="uploadClose">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 策略类型生效对象弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :modal="false"
      :title="$t('table.effectiveObject')"
      style="color: black"
      :visible.sync="objectIdsDialogVisible"
      width="600px"
    >
      <grid-table
        ref="objectIdsList"
        style="height: 300px; color: #303133"
        row-key="id"
        :multi-select="false"
        :show-pager="false"
        :col-model="objectIdsColModel"
        :row-datas="objectIds"
      >
      </grid-table>
    </el-dialog>

    <excel-stg-export ref="excelStgExport"/>

    <stg-different-dlg ref="stgDifferentDlg"/>

    <clear-stg-dlg ref="clearStgDlg"/>

  </div>
</template>

<script>
import {
  getByStgType,
  getParentName,
  getStrategyList,
  getSupportTimeStgTypeNumbers
} from '@/api/behaviorManage/StrategyOverViews/strategyOverViews'
import { formatMenuCode, toCodeMap } from '@/router'
import { entityLink } from '@/utils';
import { getOsTypeDict } from '@/utils/dictionary';
import { osTypeFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import { generateTitle } from '@/utils/i18n'
import request from '@/utils/request'
import i18n from '@/lang';
import StrategyImport from '../strategyImport'
import ExportStrategy from '../exportStrategy'
import subComponent from './subComponent'
import ExcelStgExport from './excelStgExport'
import StgDifferentDlg from './stgDifferentDlg'
import ClearStgDlg from './clearStgDlg';
import { mapGetters } from 'vuex'

export default {
  name: 'StrategyOverViews',
  components: { ClearStgDlg, StgDifferentDlg, StrategyImport, ExcelStgExport, ExportStrategy, ...subComponent },
  props: {
    importAble: { type: Boolean, default: false },
    exportAble: { type: Boolean, default: false },
    excelExportAble: { type: Boolean, default: true }   //  导出excel
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '300', formatter: this.nameFormatter, click: this.stgDifferentElg },
        { prop: 'entityId', label: 'source', width: '200', formatter: this.entityFormatter },
        { prop: 'timeId', label: 'stgApproveTime', width: '200', formatter: this.timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: this.timeInfoIdFormatter },
        { prop: 'osType', label: 'osType', width: '200', formatter: this.osTypeFormatter },
        // { prop: 'active', label: 'isActive', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        { label: 'detailInfo', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'view', isShow: this.isShow1, click: this.showDetail },
            { label: 'maintainStg', isShow: this.isShow2, click: this.gotoStrategyPage }
          ]
        }
      ],
      stgColModel: [
        { prop: 'name', label: 'stgName', width: '200', sort: 'custom', formatter: this.nameFormatter },
        { prop: 'stgTypeNumber', label: 'stgType', width: '200', sort: 'custom', formatter: this.stgTypeFormatter },
        { prop: 'timeId', label: 'stgApproveTime', width: '100', formatter: this.timeInfoStgFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: this.stgTimeInfoIdFormatter },
        { prop: 'objectIds', label: 'effectiveObject', width: '200', type: 'button',
          buttons: [
            { formatter: this.objectIdsFormatter, click: this.objectIdsClick }
          ]
        },
        { prop: 'strategyDefTypeName', label: 'stgConfig', width: '100', sort: 'custom' },
        { prop: 'osType', label: 'osType', width: '100', formatter: this.osTypeStgFormatter },
        { label: 'detailInfo', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'view', click: this.showStgDetail }
          ]
        }
      ],
      objectIdsColModel: [
        { prop: 'objectName', label: 'applicationObjectName', width: '200', sort: true },
        { prop: 'parentName', label: 'superiorDept', width: '200' }
      ],
      groupTypeMap: { // 策略包类型c
        1: this.$t('route.dataEncryption'),
        2: this.$t('route.behaviorManage'),
        3: this.$t('route.contentStrategy'),
        4: this.$t('route.netManage'),
        5: this.$t('route.terminalManage'),
        6: this.$t('pages.otherStg')
      },
      // 策略页面是tabs结构的，策略 key 对应的 tabName 的 map
      tabOption: {
        httpWhiteListStrategy: 'serverTab', // 服务器白名单
        httpWhiteListProcessFilter: 'processTab',
        processStrategy: 'strategyTab', // 透明加密
        processConfigStg: 'configTab',
        specialSuffixStrategy: 'specialFileExt', // 特殊文件后缀
        autoBackupFilterStrategy: 'autoBackupFilter',
        installStrategy: 'InstallPacketStrategy', // 软件安装卸载限制
        uninstallStrategy: 'uninstallPacketStrategy',
        specialPathStrategy: 'SpecialPathStrategy',
        ftpControllerConfig: 'FtpControlConfig',
        mailWhiteListStrategy: 'MailReceiver',
        mailSenderStrategy: 'MailSender',
        mailCarbonCopyStrategy: 'MailCopy',
        mobileTerminalUpgradeStrategy: 'MobileTerminalUpgradeStrategy',
        appOpenSuffixStrategy: 'AppOpenSuffixStg', // 移动终端文档阅读设置-文件指定打开程序策略
        mobileWPSConfigStrategy: 'WpsSetup', // 移动终端文档阅读设置-移动终端WPS配置策略
        patchCheckStrategy: 'paramSetting',   //  补丁策略-参数配置
        patchInstallationStrategy: 'patchInstallationStrategy',    //  补丁安装策略
        patchAutoInstallStrategy: 'patchAutoInstallationStrategy',
        timelyBackupStg: 'TimelyBackupStg',    //  即时备份策略
        fullDiskScanBackupStg: 'FullDiskScanBackupStg',  //  全盘扫描备份
        serverBackupConfigStg: 'ServerBackupConfigStg'  //  服务器存储配置策略
      },
      //  策略名称
      tabName: {
        httpWhiteListStrategy: this.$t('pages.httpWhiteList'), // 服务器白名单
        httpWhiteListProcessFilter: this.$t('pages.processFilter'), // 进程过滤设置
        processStrategy: this.$t('pages.encryptionPolicy'), // 透明加密策略
        processConfigStg: this.$t('pages.processStgLib_Msg57'), // 高级配置策略
        installStrategy: this.$t('pages.installLimit'), // 安装限制
        uninstallStrategy: this.$t('pages.uninstallLimit'), // 卸载限制
        specialPathStrategy: this.$t('pages.specialRelease'), // 特殊目录放行
        mailWhiteListStrategy: this.$t('pages.mailReceiver'),  // 邮件收件人白名单
        mailSenderStrategy: this.$t('pages.mailSender'),  //  发件人白名单
        mailCarbonCopyStrategy: this.$t('route.EmailCopy'),      // 邮箱抄送
        mobileTerminalUpgradeStrategy: this.$t('route.mobileTerminalUpgrade'),  // 移动端升级提醒详情
        appOpenSuffixStrategy: this.$t('route.appOpenSuffixStg'), // 移动终端文档阅读设置-文件指定打开程序策略
        mobileWPSConfigStrategy: this.$t('pages.mobileWPSConfig'), // 移动终端文档阅读设置-移动终端WPS配置策略
        patchInstallationStrategy: this.$t('route.installStrategy'), // 补丁安装策略
        patchAutoInstallStrategy: this.$t('pages.automaticPatchStg'), // 补丁自动安装策略
        patchCheckStrategy: '参数配置'
      },
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined,
        objectType: undefined,
        objectId: undefined,
        objectName: undefined,
        active: false,
        stgTypeNumbers: '',
        dataType: null,  //  终端类型，0：windows终端，1：linux终端，2：mac终端，3：移动终端 ...
        //  隐藏 231直接外发程序白名单, 219管理员权限，216文档追踪-高级配置, 122-敏感内容检测配置-例外设置, 123-敏感内容检测配置-共享管控设置, 133-敏感内容检测配置-高级配置
        //  隐藏 248文件外发检测统计设置
        //  隐藏 289-软件授权信息
        hideStgTypeNumbers: [231, 123, 289],
        onLineUserPriority: false //  在线操作员策略优先
      },
      showTree: true,
      downloadLoading: false,
      componentName: '',
      refName: '',
      comProps: {},
      strategyOptions: null,
      exportVO: {},
      homVisible: true,
      strategyImportVisible: false,
      strategyExportVisible: false,
      strategyData: [],
      fileList: [],
      dialogVisible: false,
      fileLimit: 1,
      submitting: false,
      accept: '.lds',
      fileUploadExist: false,
      uploadBtnName: '',
      fd: new FormData(),
      strategyTypes: [],
      stgTypeTree: [],  //  策略类型模块的策略类型树
      stgTypeMap: {}, //  策略类型编号与前端菜单名称的映射  key：策略类型id  value:前端菜单名称
      idMap: {}, // 保存 标题与策略类型的id映射关系， key 的取值范围 {终端行为，网络行为，数据安全，敏感内容识别， 其他策略}， value：对应的策略类型集合
      idKey: null,
      virsualType: null,
      stgQuery: {
        searchInfo: '',
        stgTypeNumbers: null
      },
      paneName: 'terminal',   //  当前选中的标签
      otherTabPanes: [
        {
          name: 'strategyType',
          label: this.$t('table.stgType'),
          type: '8',
          nodeKey: 'dataId',
          localSearch: true,
          treeData: [],
          init: false,
          expandedKeys: [],
          getTreeData: () => {
            return this.strategyTypeTreeData
          }
        }
      ],
      objectIdsDialogVisible: false,
      // strategyTypeTree: [], //  暂存策略类型树
      objectIds: [],  //  暂存生效对象的id
      objectTemp: [],  //  暂存部门树
      oldTabName: '',     //  保存上次点击的策略树标签（即：终端/操作员/策略类型）

      //  导出excel
      stgTypeData: [{ type: 'all', name: '0', label: this.$t('pages.all'), id: 0, dataId: 'all-0', myDataId: 'all-0', isLoadChildEnd: true, children: [] }],
      selectOnlineTerm: false,   //  选中的终端是否为在线终端
      strategyTypeTreeData: [{ type: 'all', name: '-1', label: this.$t('pages.all'), id: -1, dataId: 'all-1', isLoadChildEnd: true }],

      notSupportNotSameStg: ['fullDiskScanBackupStg', 'serverBackupConfigStg'],    //  不支持终端与服务端不一致提醒功能的策略类型，目前有 智能备份-全盘扫描备份，智能备份-服务器存储配置
      supportTimeStgTypeKeys: [],   //  支持展示生效时间的策略类型Key
      supportTimeStgTypeNumbers: [] //  支持展示生效时间的策略类型Id
    }
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'routesPath',
      'timeOptions',
      'stgBaseConfig'
    ]),
    gridTable() {
      return this.$refs['strategyList']
    },
    timeInfoOptions() {
      return this.timeOptions || []
    },
    timeInfoLabelMap() {
      return this.timeInfoOptions.reduce((map, option) => {
        const { value, label } = option
        map[value] = label
        return map
      }, {})
    },
    typeGridTable() {
      return this.$refs['strategyTypeList']
    }
  },
  created() {
    this.buildStrategyTypeTree()
    this.getTableTreeData()
    this.getSupportTimeStgTypeNumbers();
  },
  mounted() {
    setTimeout(() => {
      const { entityId, entityType } = this.$route.query
      if (entityId && entityType) {
        entityLink({ entityType, entityId }, {}, this)
      }
      // 根据路由查询条件entityId、entityType查询之后，清除查询
      this.$router.push({ query: {}});
    }, 150)
  },
  activated() {
    const { entityId, entityType } = this.$route.query
    if (entityId && entityType) {
      this.clearSearchCondition()
      entityLink({ entityType: entityType, entityId: entityId }, {}, this)
    }
    // 根据路由查询条件entityId、entityType查询之后，清除查询
    this.$router.push({ query: {}});
  },
  methods: {
    getSupportTimeStgTypeNumbers() {
      getSupportTimeStgTypeNumbers().then(res => {
        this.supportTimeStgTypeNumbers = res.data || []
        this.supportTimeStgTypeKeys = []
        if (this.stgBaseConfig) {
          this.supportTimeStgTypeNumbers.forEach(stgTypeNumber => {
            const data = this.stgBaseConfig[stgTypeNumber];
            data && data.strategyKey && this.supportTimeStgTypeKeys.push(data.strategyKey)
          })
        }
      })
    },
    generateTitle,
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        // 默认的上下父子节点之间的错位距离太小了，在这边设置大一点
        let size = 30
        if (row.type == 'type') {
          size = size * 2
          // 如果有子节点的话，会多一个下拉箭头，所以导致文字不对齐，因此要向左偏移一点点
          if (row.children && row.children.length > 0) {
            size = size - 3
          }
        } else if (row.type == 'strategy') {
          size = size * 3
        } else if (row.dataId && row.dataId.startsWith('VirsualType')) {
          size = size * 2 - 3
        }
        return `padding-left: ${size}px`
      }
      return ''
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.limit = null
      // 根据当前树对象，设置策略类型生效目标范围条件，例如用来过滤只对终端生效的策略
      searchQuery.scope = this.$refs.strategyTargetTree.checkedTab == 'user' ? 2 : 1
      return getStrategyList(searchQuery)
    },
    filterRowDatas(rowDatas, refs, flag) {
      this.stgTypeMap = {};
      const menuCodeMap = toCodeMap(this.permission_routes)
      for (let i = 0; i < rowDatas.length; i++) {
        const data = rowDatas[i]
        data.children = data.children.filter(item => {
          if (item.routerPath && this.hasPermission(item.routerPath)) {
            const menuPath = menuCodeMap[item.routerPath]
            if (menuPath && menuPath.title) {
              // 获取策略名称 或 菜单名称
              item.name = this.tabName[item.strategyType] || this.generateTitle(menuPath.title)
            }
            if (item.id && item.type === 'type') {
              this.stgTypeMap[item.id] = item.name
            }

            // 这里单独对二级的children做处理，修改名称、添加到stgTypeMap
            if (item.children && item.children.length > 0) {
              item.children.forEach(childItem => {
                const menuPath = menuCodeMap[childItem.routerPath]
                if (menuPath && menuPath.title) {
                  // 获取策略名称 或 菜单名称
                  childItem.name = this.tabName[childItem.strategyType] || this.generateTitle(menuPath.title)
                }
                if (childItem.id && childItem.type === 'type') {
                  this.stgTypeMap[childItem.id] = childItem.name
                }
              })
            }
            return true
          }
          return false
        })
        if (data.children.length === 0) {
          rowDatas.splice(i, 1)
          i--
        }
      }
      //  有选择模块筛选框或输入策略名称筛选框时，才会过滤掉策略数据为空的模块
      if ((flag === undefined || flag === null) && (this.strategyTypes.length > 0 || this.query.searchInfo !== '')) {
        //  过滤掉策略数据为空的模块
        this.filterNullModule(rowDatas)
      }
      this.addMyDataId(rowDatas)
      return rowDatas
    },
    addMyDataId(data) {
      if (data === undefined || data === null || data.length === 0) {
        return;
      }
      let item = null
      for (let i = 0; i < data.length; i++) {
        item = data[i]
        const { strategyType, dataId } = item
        //  对type为strategy的数据修改dataId
        const myDataId = strategyType === undefined || strategyType === null ? dataId : `${strategyType}_${dataId}`
        item.myDataId = `${myDataId}_${i}`
        this.addMyDataId(item.children || [])
      }
    },
    //  过滤掉策略数据为空的模块
    filterNullModule(data) {
      if (data == null || data.length === 0) {
        return [];
      }
      let item = null
      const nodes = []
      for (let i = 0; i < data.length; i++) {
        item = data[i]
        if (item.type !== 'strategy') {
          const result = this.filterNullModule(item.children || [])
          if (result === null || result.length === 0) {
            item.children = []
            data.splice(i, 1)
            i--;
          } else {
            nodes.push(item)
          }
        } else {
          nodes.push(item)
        }
      }
      return nodes;
    },
    checkItem: function(menuCodeMap, item) {
      if (!item.routerPath && this.hasPermission(item.routerPath)) {
        const menuPath = menuCodeMap[item.routerPath]
        if (menuPath && menuPath.title) {
          // 获取策略名称 或 菜单名称
          item.name = this.tabName[item.strategyType] || this.generateTitle(menuPath.title)
        }
        return true
      }
      return false
    },
    strategyTargetNodeChange: function(tabName, checkedNode, tab) {
      //  清空条件筛选框值
      if (this.oldTabName !== '' && this.oldTabName !== tabName) {
        this.query.searchInfo = ''
      }
      this.query.dataType = checkedNode && checkedNode.type == 1 ? checkedNode.dataType || null : null
      this.selectOnlineTerm = checkedNode && checkedNode.type == 1 && checkedNode.online || false
      this.query.onLineUserPriority = this.selectOnlineTerm && this.query.onLineUserPriority
      this.oldTabName = tabName

      this.paneName = tabName || ''
      //  为策略类型时
      if (tabName === 'strategyType') {
        //  选择“策略类型”标签或者 选中”全选“节点时，查询当前展示的所有根节点（终端Id）
        if ((tab.name && tab.name === 'strategyType') || (checkedNode && checkedNode.type && checkedNode.type === 'all')) {
          const keys = Object.keys(this.idMap) || []
          const array = {}
          for (let i = 0; i < keys.length; i++) {
            this.idMap[keys[i]].forEach(item => { array[item] = '' })
          }
          this.stgQuery.stgTypeNumbers = Object.keys(array).join(',') || null
        } else if (checkedNode != null) {
          //  如果为分组（终端行为，网络行为，数据安全，敏感内容识别，其他策略）
          if (checkedNode.type && checkedNode.type === 'group') {
            const nodeIds = this.idMap[checkedNode.name]
            this.stgQuery.stgTypeNumbers = nodeIds && nodeIds.join(',') || null
            //  如果为虚拟类型（服务器白名单设置，邮件白名单设置，透明加密，软件安装/卸载限制)
          } else if ((checkedNode.type === undefined || checkedNode.type === null) && (checkedNode.dataId + '').startsWith('VirsualType')) {
            const nodeIds = this.idMap[checkedNode.routerPath]
            this.stgQuery.stgTypeNumbers = nodeIds && nodeIds.join(',') || null
            //  类型
          } else if (checkedNode.type && checkedNode.type === 'type') {
            this.stgQuery.stgTypeNumbers = checkedNode.id + ''
          }
        }
        this.strategyHandleFilter()
        return;
      } else if (tab !== undefined && tab !== null && tab.paneName === tabName) {
        //  如果点击标签触发的事件，即：属于点击标签触发事件，清除筛选条件，重新查询策略类型
        this.clearSearchCondition()
        this.getTableTreeData()
      }
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        this.query.objectName = checkedNode.label
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query.objectName = undefined
      }
      //  改变终端策略状态列的显示
      const subModelIndex = this.colModel.findIndex(data => data.prop === 'stgDifferent')
      if (subModelIndex > -1) {
        this.$set(this.colModel[subModelIndex], 'hidden', this.query.objectType != 1);
      }
      this.handleFilter()
    },
    gotoStrategyPage(row) {
      formatMenuCode(this.permission_routes, row, 'routerPath')
      const routerPath = !row.editUrl ? row.routerPath : row.editUrl
      const path = routerPath ? routerPath.split('/').pop() : ''
      const fullPath = this.routesPath[routerPath]
      const params = []
      const param = path.indexOf('?') > -1 ? path.split('?').pop() : ''
      const tabName = this.tabOption[row.strategyType] ? 'tabName=' + this.tabOption[row.strategyType] : ''
      if (param) params.push(param)
      if (tabName) params.push(tabName)
      const paramsStr = params.length > 0 ? '?' + params.join('&') : ''

      if (fullPath) {
        // 带上点击的节点信息，跳转后在 AppMain.vue 页面监听 $route，实现选中节点的效果
        const { objectType: entityType, objectId: entityId } = this.query
        // 跳转后 GridTable 不自动加载数据
        const autoload = entityType === undefined || entityType === null
        this.$router.push({ path: fullPath + paramsStr, query: { entityId, entityType, autoload }})
      } else {
        this.$message({
          message: this.$t('pages.pathNotExist'),
          type: 'error'
        })
      }
    },
    //  兼容策略方法
    resetObject(row, object) {
      //  兼容 邮件外发文件设置，mailIds中的元素是数字，而非对象
      if (row.strategyType === 'mailSenderStrategy') {
        object.mailIds = object.mailIds || []
        const ids = []
        object.mailIds.forEach(item => item.id && ids.push(item.id));
        object.mailIds = ids.length > 0 ? ids : object.mailIds;
      }
      //  兼容24-D1单条策略支持指定多个生效对象, 因生效对象是通过objectGroupIds和objectIds确定的, 策略总览查看详情只查看指定的生效对象，固只加单条生效对象数据
      if (object.entityType) {
        if (object.entityType > 2 && !object.objectGroupIds) {
          object.objectGroupIds = []
          object.objectGroupIds.push(object.entityId)
        } else if (!object.objectIds) {
          object.objectIds = []
          object.objectIds.push(object.entityId)
        }
      }
    },
    resetComponent(name) {
      this.refName = name
      // ref与组件名不同的映射
      const nameOption = {
        waterMarkStrategy: 'water-mark-dialog',
        screenWaterMarkStrategy: 'water-mark-dialog',
        installStrategy: 'install-packet',
        uninstallStrategy: 'install-packet',
        effectiveFunction: 'effective-content-strategy',
        effectiveFuncConfig: 'EffectiveContentConfig',
        httpWhiteListStrategy: 'http-white-list',
        httpWhiteListProcessFilter: 'http-white-list',
        autoBackupFilterStrategy: 'special-suffix',
        outgoingCodeWhiteList: 'mcode',
        outgoingConfig: 'file-outgoing-config',
        videoStrategy: 'video-strategy', // video是html标签
        dripContent: 'Content',
        softwareLimitStrategy: 'SoftwareLimit',
        softRequiredInstallStrategy: 'RequireInstall',
        softRequiredRunStrategy: 'RequireRun',
        groupPolicyStrategy: 'GroupPolicy',
        forumURLFilter: 'ForumUrlFilter',
        webBrowseURLFilter: 'webBrowseUrlFilter',
        ftpControllerConfig: 'FtpControlConfig',
        mailWhiteListStrategy: 'MailReceiver',
        mailCarbonCopyStrategy: 'MailCopy',
        mailSenderStrategy: 'MailSender',
        mobileOpenSuffixStrategy: 'MobileTerminal',
        mobileTerminalUpgradeStrategy: 'MobileTerminalUpgradeStrategy',
        appOpenSuffixStrategy: 'AppOpenSuffixStg',
        softwareDownloadStrategy: 'SoftwareDownloadStrategy',
        // termSecurityDetectionStrategy: 'TermSecurityDetection', // 终端安全检测是跳转页面，不用导入组件
        patchInstallationStrategy: 'PatchInstallation',
        patchAutoInstallStrategy: 'PatchAutoInstallationStrategy',
        patchCheckStrategy: 'PatchSetting',
        browserFileDownloadStrategy: 'BrowserFileDownloadStrategy',
        mobileWPSConfigStrategy: 'WpsSetup',
        aiModelStrategy: 'AiModelStrategy',
        aiChatStrategy: 'AiChatStrategy',
        aiFileStrategy: 'AiUploadFile',
        softwareCopyrightStrategy: 'SoftwareCopyrightStrategy'
      }
      const index = name.toLowerCase().indexOf('strategy')
      // 根据ref名称，修改组件名
      if (!nameOption[name]) {
        this.componentName = index > 0 ? name.substring(0, index) : name
      } else {
        this.componentName = nameOption[name]
      }
      // 部分页面需要传入的 prop
      const option = {
        installStrategy: { defaultLimitType: 1 },
        uninstallStrategy: { defaultLimitType: 2 },
        waterMarkStrategy: { stgTypeNumber: 2 },
        screenWaterMarkStrategy: { stgTypeNumber: 44 },
        httpWhiteListStrategy: { tabName: 'serverTab' },
        httpWhiteListProcessFilter: { tabName: 'processTab' },
        processStrategy: { tabName: 'strategyTab' },
        processConfigStg: { tabName: 'configTab' },
        specialSuffixStrategy: { tabName: 'specialFileExt' },
        autoBackupFilterStrategy: { tabName: 'autoBackupFilter' },
        dripContent: { dripAble: true }
      }
      this.comProps = { listable: false, formable: false, ...option[name] }
    },
    showStgDetail(row) {
      if (row.data && row.data.length > 0) {
        // //  确保显示的是第一个启用的操作系统类型的策略数据
        this.showDetail(row.data[0])
      }
    },
    showDetail(row) {
      const object = JSON.parse(row.decodeStrategy)
      object.active = true
      object.remark = row.remark
      object.strategyDefType = row.strategyDefType
      if (row.stgObjects && row.stgObjects.length > 0) {
        //  策略类型树时，支持展示多个应用生效对象
        if (this.paneName === 'strategyType') {
          object.entityType = row.stgObjects[0].objType
          const types = [1, 3].includes(object.entityType) ? [1, 3] : [2, 4];
          const groupIds = []
          const ids = [];
          row.stgObjects.filter(stgObject => {
            if (types.includes(stgObject.objType)) {
              if (stgObject.objType == types[0]) {
                ids.push(stgObject.objId)
              } else {
                groupIds.push(stgObject.objId);
              }
            }
          })
          object.objectGroupIds = groupIds;
          object.objectIds = ids;
        } else {
          object.entityType = row.stgObjects[0].objType
          object.entityId = row.stgObjects[0].objId
        }
      } else if (row.objectType !== undefined && row.objectType !== null && row.objectId !== undefined && row.objectId !== null) {
        object.entityType = row.objectType
        object.entityId = row.objectId
      }
      if (object.ruleGroupIds != undefined && object.severity != undefined) {
        // 这边使用大写开头的Content，因为content是html标签
        const refName = object.dripScan ? 'dripContent' : 'Content'
        this.resetComponent(refName)
        this.$nextTick(() => {
          this.$refs[refName].handleUpdate(object)
        })
      } else {
        // objectType和objectId有值，且不等于6表示不等于策略包
        if (row.objectType && (row.objectId || row.objectId == 0) && row.objectType != 6) {
          object.entityType = row.objectType
          object.entityId = row.objectId
        }
        // 终端安全检测是跳转页面，不导入组件，所以不用切换组件
        if (row.strategyType !== 'termSecurityDetectionStrategy') {
          this.resetComponent(row.strategyType)
        }
        this.resetObject(row, object)
        this.$nextTick(() => {
          if (row.strategyType === 'mailSenderStrategy') {
            this.$refs[row.strategyType].handleConfig()
          } else {
            //  服务器白名单设置   进程过滤
            if (row.strategyType === 'httpWhiteListStrategy' || row.strategyType === 'httpWhiteListProcessFilter') {
              object.tabName = row.strategyType === 'httpWhiteListStrategy' ? 'serverTab' : 'processTab'
            } else if (row.strategyType === 'termSecurityDetectionStrategy') {
              //  如果为终端安全检测策略
              object.objectId = object.entityId
              object.objectType = object.entityType
              this.$router.push({ path: '/terminalManage/termSecurityManager/termSecurityDetection', query: { visible: false, stg: object }})
              return;
            } else if (row.strategyType === 'patchCheckStrategy') {
              //  如果为补丁策略-参数配置
              object.objectId = object.entityId
              object.objectType = object.entityType
              this.$router.push({ path: '/terminalManage/patchManage/PatchStrategy', query: { tabName: 'paramSetting' }})
              return;
            } else if (row.strategyType === 'sysBaseConfig') {  //  计算机设置
              this.$refs[row.strategyType].handleView(object)
            }
            console.log(row.strategyType, this.$refs[row.strategyType])
            this.$refs[row.strategyType].handleUpdate(object)
          }
        })
      }
    },
    handleFilter() {
      if (this.paneName === 'strategyType') {
        this.strategyHandleFilter();
        return
      }
      this.query.stgTypeNumbers = this.strategyTypes && this.strategyTypes.length > 0 ? this.strategyTypes.join(',') : ''
      this.gridTable.execRowDataApi(this.query)
    },
    strategyHandleFilter() {
      this.stgQuery.searchInfo = this.query.searchInfo
      this.typeGridTable.execRowDataApi(this.stgQuery)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleImport() {
      this.uploadBtnName = this.$t('pages.processStgLib_Msg94', { format: this.accept })
      this.dialogVisible = true
    },
    handleExport() {
      this.strategyExportVisible = true
      this.homVisible = false
    },
    timeInfoIdFormatter(row) {
      if (row.decodeStrategy) {
        const object = JSON.parse(row.decodeStrategy)
        let timeId = object.timeId
        if (!timeId && this.supportTimeStgTypeKeys.includes(row.strategyType)) {
          timeId = 1;
        }
        if (timeId != null) {
          return { 'timeId': timeId };
        }
      }
    },
    stgTimeInfoIdFormatter(row) {
      if (Array.isArray(row.data) && row.data.length > 0) {
        const { decodeStrategy, strategyType } = row.data[0]
        if (decodeStrategy && strategyType !== 'softRequiredRunStrategy' && strategyType !== 'softRequiredInstallStrategy') {
          const object = JSON.parse(decodeStrategy)
          let timeId = object.timeId
          if (!timeId && this.supportTimeStgTypeKeys.includes(strategyType)) {
            timeId = 1;
          }
          if (timeId != null) {
            return { 'timeId': timeId };
          }
        }
      }
    },
    timeInfoFormatter(row, data) {
      const { decodeStrategy, strategyType } = row
      if (this.supportTimeStgTypeKeys.includes(strategyType)) {
        if (decodeStrategy && strategyType !== 'softRequiredRunStrategy' && strategyType !== 'softRequiredInstallStrategy') {
          const object = JSON.parse(decodeStrategy)
          //  支持生效时间的策略，若是旧策略没有设置timeId，则默认显示全天候
          if (object && this.supportTimeStgTypeKeys.includes(row.strategyType)) {
            return timeDateInfoFormatter(object, object.timeId)
          }
        }
      }
      return ''
    },
    timeInfoStgFormatter(row, data) {
      if (this.supportTimeStgTypeNumbers.includes(row.stgTypeNumber)) {
        if (Array.isArray(row.data) && row.data.length) {
          return this.timeInfoFormatter(row.data[0], data)
        }
      }
      return '';
    },
    entityFormatter(row, data) {
      return row.objectName
    },
    osTypeStgFormatter: function(row, data) {
      let osTypeStr = ''
      if (row.osTypes !== undefined && row.osTypes !== null && row.osTypes.length > 0) {
        const types = row.osTypes || []
        const osTypeDict = [...getOsTypeDict()]
        for (let i = 0; i < types.length; i++) {
          for (let j = 0; j < osTypeDict.length; j++) {
            if (types[i] == osTypeDict[j].value) {
              osTypeStr = osTypeStr + osTypeDict[j].label + ','
            }
          }
        }
        osTypeStr = osTypeStr.substr(0, osTypeStr.length - 1);
      } else {
        if (row.decodeStrategy != null && row.decodeStrategy != undefined && row.decodeStrategy != '') {
          osTypeStr = osTypeFormatter(row, data);
        }
      }
      if (osTypeStr === '') {
        osTypeStr = i18n.t('pages.collectAll')
      }
      return osTypeStr
    },
    osTypeFormatter(row, data) {
      if (row.decodeStrategy != null && row.decodeStrategy != undefined && row.decodeStrategy != '') {
        return osTypeFormatter(row, data)
      }
      return ''
    },
    stgOsTypeFormatter(row, data) {
      const osTypes = []
      row.osTypes.forEach(item => {
        osTypes.push(osTypeFormatter(row, item))
      });
      return osTypes.join('，') || ''
    },
    stgDifferentElg(row, index) {
      if (row.stgDifferent && !this.notSupportNotSameStg.includes(row.strategyType)) {
        this.$refs.stgDifferentDlg.show(row)
      }
    },
    isShow1: function(row) {
      return row.type == 'strategy'
    },
    isShow2: function(row) {
      return row.type == 'type'
    },
    nameFormatter: function(row, data) {
      if (row.children && row.children.length > 0 && row.type == 'group') {
        return '<label>' + this.groupTypeMap[data] + '</label>'
      }
      if (row.type == 'strategy') {
        if (row.stgDifferent && !this.notSupportNotSameStg.includes(row.strategyType)) {
          return `<i class="el-icon-s-opportunity stg-diff"></i><span class="stg-diff">${row.name}</span>`
        } else {
          return row.name
        }
      }
      return '<label>' + data + '</label>'
    },
    //  初始化数据
    initImportData(data) {
      data = data || []
      if (data === undefined || data === null) {
        this.strategyImportVisible = false
        this.homVisible = true
        this.strategyExportVisible = false
        return;
      }
      //  初始化数据
      this.strategyData = data
      this.$nextTick(() => {
        this.$refs.strategyImport.init(this.strategyData)
      })
      if (data.length > 0) {
        this.strategyImportVisible = true
        this.homVisible = false
      }
    },
    importSuccess() {
    },
    closeImport() {
      this.strategyData = []
      this.strategyImportVisible = false
      this.homVisible = true
    },
    closeExport() {
      this.strategyExportVisible = false
      this.homVisible = true
    },
    reset() {
      const initData = this.$refs.strategyImport.initData || []
      this.strategyData = []
      initData.forEach(item => this.strategyData.push(item))
    },
    async onUpload(data) {
      const fd = new FormData()
      fd.append('file', data.file);// 传文件
      const res = (await this.defaultUploadDataFunc(fd));
      const strategyData = res.data || []
      //  关闭导入弹窗
      this.submitting = false
      this.dialogVisible = false
      this.initImportData(strategyData)
      return false
    },
    onChange(file, fileList) {
      this.fileUploadExist = fileList.length > 0
    },
    onRemove(file, fileList) {
      this.fileUploadExist = fileList.length > 0
    },
    onError(err) {
      console.error('文件上传失败 %o', err)
    },
    createData() {
      if (!this.fileUploadExist) {
        this.$message({
          message: this.$t('pages.notSelectedUploadFile'),
          type: 'error'
        })
        return;
      }
      this.submitting = true
      this.$refs.uploadFileList.submit();
    },
    clearFiles(e) {
      window.event ? window.event.cancelBubble = true : e.stopPropagation()
      this.fileList = []
      this.$refs.uploadFileList.clearFiles()
    },
    uploadClose() {
      this.dialogVisible = false
    },
    defaultUploadDataFunc(data) {
      return request.post('/stgCommon/importAnalysisFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    stgRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.stgQuery, option)
      return getByStgType(searchQuery);
    },
    stgFilterRowDatas(rowDatas) {
      this.addMyDataIdStg(rowDatas)
      // 部门、终端、操作员的详情页面跳转至策略总览，根据路由查询条件entityId、entityType查询之后，清除查询
      if (this.$route.query.entityId && this.$route.query.entityType) {
        this.$route.query.entityId = undefined
        this.$route.query.entityType = undefined
      }
    },
    /**
     * 给数据添加myDataId，确保唯一性
     * @param data
     */
    addMyDataIdStg(data) {
      if (data === undefined || data === null || data.length === 0) {
        return;
      }
      let item = null
      for (let i = 0; i < data.length; i++) {
        item = data[i]
        //  对type为strategy的数据修改dataId
        item.myDataId = item.stgTypeNumber + '_' + item.name
        this.addMyDataIdStg(item.children || [])
      }
    },
    /**
     * 构建其他树
     */
    buildStrategyTypeTree() {
      getStrategyList(this.query).then(res => {
        this.idMap = {}
        this.virsualType = null
        this.idKey = null
        this.filterRowDatas(res.data, null, true)
        this.setLabel(res.data);
        this.stgTypeTree = res.data || []
        this.strategyTypeTreeData.push(...this.stgTypeTree)
      })
    },
    //  设置label
    setLabel(rowDatas) {
      (rowDatas || []).forEach(item => {
        item.label = item.type === 'group' ? this.groupTypeMap[item.name] : item.name;
        if (item.type && item.type === 'group') {
          this.idKey = item.name
          this.idMap[this.idKey] = []
        } else if (item.type && item.type === 'type') {
          if (item.routerPath === this.virsualType) {
            this.idMap[this.virsualType] && item.id && this.idMap[this.virsualType].push(item.id)
          }
          this.idMap[this.idKey] && item.id && this.idMap[this.idKey].push(item.id)
        } else if ((item.dataId + '').startsWith('VirsualType')) {
          this.virsualType = item.routerPath
          this.idMap[this.virsualType] = []
        }
        item.children && this.setLabel(item.children)
      })
    },
    setLabel1(rowDatas) {
      (rowDatas || []).forEach(item => {
        item.label = item.type === 'group' ? this.groupTypeMap[item.name] : item.name;
        item.children && this.setLabel1(item.children)
      })
    },
    /**
     * 清除查询条件
     */
    clearSearchCondition() {
      this.query.stgTypeNumbers = ''
      this.query.searchInfo = ''
      //  清空策略类型选择框的值
      this.strategyTypes = []
      this.$refs['stgTypeTreeSelect'] && this.$refs['stgTypeTreeSelect'].clearSelectedNode();
    },
    //  生效对象
    objectIdsFormatter(row) {
      return row.entityName || ''
    },
    /**
     * 生效对象，查询应用对象的上级部门
     * @param row
     * @returns {Promise<void>}
     */
    async objectIdsClick(row) {
      this.objectIds = []
      let parentName = ''
      await getParentName(row.stgObjects).then(res => {
        parentName = res.data || ''
      })
      this.objectIds.push({ id: 1, objectName: row.entityName, parentName: parentName })
      this.objectIdsDialogVisible = true
    },
    /**
     * 策略类型格式化
     * @param row
     * @returns {*|string}
     */
    stgTypeFormatter(row) {
      const node = this.getStgTypeNode(row.stgTypeNumber, this.stgTypeTree)
      return node != null ? node.name : '';
    },
    /**
     * 根据策略类型编号，查找指定数据
     * @param stgTypeNumber   策略类型编号
     * @param tree            待查找数据
     * @returns {null|*}      策略类型数据
     */
    getStgTypeNode(stgTypeNumber, tree) {
      if (tree == null) {
        return null;
      }
      let node = null;
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].type === 'type' && tree[i].id === stgTypeNumber) {
          node = tree[i];
        }
        node = node == null ? this.getStgTypeNode(stgTypeNumber, tree[i].children) : node;
        if (node) {
          return node;
        }
      }
    },
    /**
     * 策略类型改变时
     * @param selectedKeys
     * @param options
     */
    stgTypeChange(selectedKeys, options) {
      this.strategyTypes = [];
      (options || []).forEach(item => { item.type === 'type' && item.id !== '' && this.strategyTypes.push(item.id + '') })
    },
    /**
     * 获取策略类型选择框中的数据
     */
    getTableTreeData() {
      const _this = this
      const searchQuery = {}
      searchQuery.scope = this.$refs.strategyTargetTree && this.$refs.strategyTargetTree.checkedTab == 'user' ? 2 : 1
      searchQuery.hideStgTypeNumbers = this.query.hideStgTypeNumbers || []
      getStrategyList(searchQuery).then(res => {
        const rowDatas = _this.filterRowDatas(res.data, null, true) || []
        //  保存策略树
        const array = JSON.parse(JSON.stringify(rowDatas));
        _this.setLabel1(array)
        _this.deleteStrategyData(array);
        _this.objectTemp = array
      })
    },
    /**
     * 删除策略数据,保留策略类型
     * @param tree
     */
    deleteStrategyData(tree) {
      if (tree == null) {
        return;
      }
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].type && tree[i].type === 'strategy') {
          tree.splice(i, 1);
          continue
        }
        this.deleteStrategyData(tree[i].children);
      }
    },
    /**
     * 策略导出
     */
    excelStgExportHandler() {
      if (this.paneName === 'strategyType') {
        const data = [...this.stgTypeData]
        data[0].children = []
        this.stgTypeTree.forEach(item => {
          data[0].children.push(item)
        })
        this.$refs.excelStgExport.stgTypeShow(data, this.query.searchInfo)
      } else {
        const usedScope = this.paneName === 'terminal' ? 1 : 2;
        this.$refs.excelStgExport.objTypeShow(this.query.onLineUserPriority && usedScope === 1, {
          stgTypeTree: this.stgTypeTree,
          objectType: this.query.objectType,
          objectId: this.query.objectId,
          objectName: this.query.objectName,
          usedScope,
          strategyTypes: this.strategyTypes,
          searchInfo: this.query.searchInfo,
          dataType: this.query.dataType,
          onLineUserPriority: this.query.onLineUserPriority
        });
      }
    },
    handleMoreClick(type) {
      this.query.onLineUserPriority = type === 2
      this.gridTable.execRowDataApi()
    },
    /**
     * 刷新
     */
    refresh() {
      this.handleFilter()
    },
    /**
     * 一键清空策略
     */
    clearStgHandler() {
      this.$refs.clearStgDlg.show(this.stgTypeTree, { objectId: this.query.objectId, objectType: this.query.objectType, tabName: this.paneName })
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.cell.el-tooltip {
    max-width: 100%;
  }
  >>>.stg-diff {
    color: red;
    cursor: pointer;
  }
  .el-table__indent {
    padding-left: 20px;
  }
  .app-container.strategy{
    position: relative;
    width: 100%;
    height: calc(100vh - 90px);
    padding: 5px 20px 20px;
  }
  .select_tags {
    ::v-deep .el-select__tags {
      flex-wrap: unset;
      overflow: auto;
    }
  }
</style>

