<template>
  <el-dialog
    v-el-drag-dialog
    :modal="false"
    :title="$t('pages.clearStrategy')"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="600px"
    @close="close"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" style="padding-top: 5px">
      <FormItem :label="$t('pages.clearObject')" prop="objectType">
        <el-row>
          <el-col :span="8">
            <el-select v-model="temp.objectType" @change="objectTypeChange">
              <el-option :value="1" :label="$t('components.terminalG')"/>
              <el-option :value="2" :label="$t('components.userG')"/>
            </el-select>
          </el-col>
          <el-col :span="16">
            <tree-select
              ref="objectTree"
              node-key="id"
              is-filter
              check-strictly
              multiple
              :width="300"
              :local-search="false"
              :filter-key="filterKey"
              :leaf-key="temp.objectType === 1 ? 'terminal' : 'user'"
              :filter-node-method="filterNodeMethod"
              @change="objectTreeChange"
            />
          </el-col>
        </el-row>
      </FormItem>
      <FormItem :label="$t('pages.clearStgType')" prop="stgType">
        <tree-select
          ref="stgTypeTree"
          node-key="myDataId"
          is-filter
          :data="stgTypeTree"
          :multiple="true"
          :width="440"
          :default-expanded-keys="defaultExpandedKeys"
          :placeholder="$t('pages.strategyOverViews_selectedStgType')"
          clearable
          @change="stgTypeTreeChange"
        />
      </FormItem>
      <FormItem>
        <label style="color: #409eff">{{ $t('pages.clearStrategyTip') }}</label>
      </FormItem>
    </Form>

    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="confirm()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="close">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { clearStrategy } from '@/api/behaviorManage/StrategyOverViews/strategyOverViews';

export default {
  name: 'ClearStgDlg',
  components: {},
  props: {
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        objectType: 1,
        objectIds: [],
        objectGroupIds: [],
        stgTypeIds: []
      },
      rules: {
        objectType: [{ validator: this.objectTypeValidator, trigger: 'blur' }],
        stgType: [{ validator: this.stgTypeValidator, trigger: 'blur' }]
      },
      filterKey: [], //  需要过滤的
      stgTypeTreeTemp: null,  //  保存策略类型树
      stgTypeTree: [],  //  策略类型树
      defaultExpandedKeys: ['all-0'],
      usedScopeMap: {},
      //  不支持清除的策略类型，但会显示在策略总览里， 95-全盘扫描，98-软件卸载策略，271-全盘扫描备份，209-邮件发件人白名单
      notSupportStgTypeNumbers: [95, 98, 271, 209]
    }
  },
  computed: {
    allowUsedScope() {
      const list = [3];
      list.push(this.temp.objectType);
      return list;
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    resetTemp() {
      // this.temp = Object.assign({}, this.defaultTemp)
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
    },
    /**
     *
     * @param stgTypeTree   策略树
     * @param query         指定对象
     */
    show(stgTypeTree, query) {
      this.loadUsedScopeMap()
      this.stgTypeTreeTemp = [{ id: -1, dataId: '-1', label: this.$t('route.all'), type: 'type', myDataId: '-1' }]
      this.stgTypeTreeTemp.push(...stgTypeTree)
      this.resetTemp()
      //  设置指定对象
      const keys = []
      if (query.tabName && (query.tabName === 'terminal' || query.tabName === 'user')) {
        if (query.objectType) {
          this.temp.objectType = query.objectType == 1 || query.objectType == 3 ? 1 : 2;
          //  回收站不支持设置
          if (query.objectId !== undefined && query.objectId !== null && query.objectId != -2) {
            const id = (query.objectType == 1 ? 'T' : query.objectType === '2' ? 'U' : 'G') + query.objectId
            keys.push(id);
          }
        }
      }
      this.loadObjectData()
      this.visible = true
      //  清空搜索内容
      this.$nextTick(() => {
        this.$refs.objectTree.clearFilter();
        this.$refs.stgTypeTree.clearFilter();
        this.$refs.objectTree && this.$refs.objectTree.clearSelectedNode()
        this.$refs.stgTypeTree && this.$refs.stgTypeTree.clearSelectedNode()
        this.$refs.dataForm.clearValidate()
        keys.length && this.$refs.objectTree.checkSelectedNode(keys)
      })
    },
    //  加载各策略支持的应用类型1-终端，2-操作员，3-两者都有   key：routerPath，value：usedScope
    loadUsedScopeMap: function() {
      this.usedScopeMap = {};
      const stgBaseConfig = this.$store.getters.stgBaseConfig || {}
      for (const key of Object.keys(stgBaseConfig)) {
        this.usedScopeMap[stgBaseConfig[key].routerPath] = stgBaseConfig[key].usedScope
      }
    },
    objectTypeChange() {
      this.loadObjectData()
      this.$nextTick(() => {
        this.$refs.dataForm.validateField(['objectType', 'stgType'])
      })
    },
    loadObjectData() {
      this.$refs.objectTree && this.$refs.objectTree.clearSelectedNode()
      this.$refs.stgTypeTree && this.$refs.stgTypeTree.clearSelectedNode()
      this.stgTypeTree.splice(0);
      this.stgTypeTree.push(...JSON.parse(JSON.stringify(this.stgTypeTreeTemp)))
      this.temp.stgTypeIds.splice(0)
      this.temp.objectIds.splice(0)
      this.temp.objectGroupIds.splice(0)
      this.filterNotSupportStgType(this.stgTypeTree);
    },
    objectTypeValidator(rule, value, callback) {
      if (this.temp.objectIds.length === 0 && this.temp.objectGroupIds.length === 0) {
        callback(new Error(this.$t('pages.clearObjectIsNotEmpty')))
      }
      callback();
    },
    stgTypeValidator(rule, value, callback) {
      if (this.temp.stgTypeIds.length === 0) {
        callback(new Error(this.$t('pages.stgTypeIsNotEmpty')))
      }
      callback();
    },
    objectTreeChange(keys, options) {
      //  获取生效对象Id
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      options && options.forEach(item => {
        if (item.type == 3 || item.type == 4) {
          this.temp.objectGroupIds.push(parseInt(item.dataId));
        } else {
          this.temp.objectIds.push(parseInt(item.dataId));
        }
      })
      this.$nextTick(() => {
        this.$refs.dataForm.validateField('objectType')
      })
    },
    stgTypeTreeChange(keys, stgTypeOptions) {
      //  获取策略类型Id
      this.temp.stgTypeIds = []
      stgTypeOptions && stgTypeOptions.forEach(item => {
        if (item.type === 'type') {
          this.temp.stgTypeIds.push(item.id);
        }
      })
      this.$nextTick(() => {
        this.$refs.dataForm.validateField('stgType')
      })
    },
    //  过滤掉不支持终端或操作员的策略类型,  也过滤不支持清除功能的策略类型
    filterNotSupportStgType(stgTypeTree) {
      for (let i = 0; i < stgTypeTree.length; i++) {
        const item = stgTypeTree[i]
        if ((item.routerPath && !this.allowUsedScope.includes(this.usedScopeMap[item.routerPath])) || (item.type === 'type' && this.notSupportStgTypeNumbers.includes(item.id))) {
          stgTypeTree.splice(i, 1);
          i--
        } else if (item.children) {
          this.filterNotSupportStgType(item.children)
        }
      }
    },
    isOfflineTerm(type) {
      const tag = type & 0xf0
      return tag === 0x20 || tag === 0x80 // U盘终端或永久离线终端
    },
    //  过滤永久离线终端和U盘终端
    filterNodeMethod(value, data, node) {
      return data.type != 1 || !this.isOfflineTerm(data.dataType);
    },
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          clearStrategy(this.temp).then(res => {
            this.submitting = false;
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.stgClearSuccess'),
              type: 'success',
              duration: 2000
            })
            this.close()
          }).catch(() => {
            this.submitting = false;
          })
        }
      })
    },
    close() {
      this.$refs.dataForm.clearValidate()
      this.visible = false
    }
  }
}
</script>
<style lang="scss" scoped>

</style>

