import PrinterSet from '@/views/behaviorManage/hardware/printerSet'
import WaterMarkDialog from '@/views/behaviorManage/hardware/waterMark/waterMarkDialog'
import ImTool from '@/views/behaviorManage/application/imTool'
import ImFile from '@/views/behaviorManage/application/imFile'
import AppBlock from '@/views/behaviorManage/application/appBlock/editDlg'
import WinTitleBlock from '@/views/behaviorManage/application/winTitle'
import AppVersionLimit from '@/views/behaviorManage/application/appVersion'
import InstallPacket from '@/views/behaviorManage/application/install/installPacket/editDlg'
import SpecialPath from '@/views/behaviorManage/application/install/specialPath'
import Url from '@/views/behaviorManage/network/url'
import WebPost from '@/views/behaviorManage/network/webPost'
import WebPort from '@/views/behaviorManage/network/webPort/editDlg'
import WebFlow from '@/views/behaviorManage/network/webFlow/editDlg'
import NetCtrl from '@/views/behaviorManage/network/isolation'
import Email<PERSON><PERSON><PERSON><PERSON>lock from '@/views/behaviorManage/network/emailKeyword'
import VideoStrategy from '@/views/behaviorManage/monitor/video/editDlg'
import BurnConfig from '@/views/behaviorManage/burner/burnConfig'
import ShareConfig from '@/views/behaviorManage/network/shareConfig'
import FileFilterSetup from '@/views/behaviorManage/monitor/fileFilter'
import Driver from '@/views/behaviorManage/hardware/driver'
import OtherDeviceLimit from '@/views/behaviorManage/hardware/otherDeviceLimit'
import Content from '@/views/contentStrategy/strategy/content'
import EffectiveContentStrategy from '@/views/contentStrategy/strategy/effectiveContentStrategy'
import EffectiveContentConfig from '@/views/contentStrategy/strategy/effectiveContentConfig'
import BlueToothConfig from '@/views/behaviorManage/hardware/blueTooth'
import MtpConfig from '@/views/behaviorManage/hardware/mtp'
import LogFilter from '@/views/behaviorManage/monitor/logFilter'
import MailReceiver from '@/views/dataEncryption/encryption/mailWhiteList/mailReceiver'
import MailSender from '@/views/dataEncryption/encryption/mailWhiteList/mailSender'
import MailCopy from '@/views/dataEncryption/encryption/mailWhiteList/mailCopy'
import EncryptSpecialPath from '@/views/dataEncryption/encryption/specialPath/editDlg'
import FilePermissionControl from '@/views/dataEncryption/encryption/filePermissionControl'
import HttpWhiteList from '@/views/dataEncryption/encryption/httpWhiteList'
import BrowserFile from '@/views/behaviorAuditing/network/browserFileStrategy'
import Clipboard from '@/views/dataEncryption/encryption/clipboard/editDlg'
import Screenshot from '@/views/dataEncryption/encryption/screenshotStrategy/editDlg'
import WorkMode from '@/views/dataEncryption/encryption/workMode/editDlg'
import Process from '@/views/dataEncryption/encryption/processStg/editDlg'
import ProcessConfigStg from '@/views/dataEncryption/encryption/processStg/configEditDlg'
import SpecialSuffix from '@/views/dataEncryption/encryption/specialSuffix/editDlg'
import IpAndMacBind from '@/views/assets/systemMaintenance/IPAndMAC'
import ReadPermission from '@/views/dataEncryption/encryption/readPermission'
import ProcessCollectRule from '@/views/behaviorManage/application/processCollectRule'
import DiskScan from '@/views/dataEncryption/encryption/diskScan/editDlg'
import SmartEnc from '@/views/dataEncryption/encryption/smartEncStrategy/editDlg'
import TranslucentEnc from '@/views/dataEncryption/encryption/translucentEncStrategy/editDlg'
import UserDense from '@/views/dataEncryption/encryption/userDense'
import BackUpConfig from '@/views/dataEncryption/encryption/backUpConfig/editDlg'
import mcode from '@/views/dataEncryption/encryption/fileOutgoing/mcode'
import FileOutgoingConfig from '@/views/dataEncryption/encryption/fileOutgoing/config'
import ProcessMonitor from '@/views/behaviorManage/hardware/processMonitor'
import TerminalConfig from '@/views/system/terminalManage/terminalConfig'
import SysBaseConfig from '@/views/behaviorManage/application/sysBaseConfig'
import outgoingProcess from '@/views/dataEncryption/encryption/fileOutgoing/app'
import SoftwareBlacklist from '@/views/dataEncryption/encryption/fileOutgoing/softwareBlacklist'
import outgoingTemplate from '@/views/dataEncryption/encryption/fileOutgoing/template'
import outgoingScreenWaterMark from '@/views/dataEncryption/encryption/fileOutgoing/screenWaterMark'
import outgoingPrintWaterMark from '@/views/dataEncryption/encryption/fileOutgoing/printWaterMark'
import BatchEncOrDec from '@/views/dataEncryption/encryption/encOrDecStg/editDlg'
import TerminalMenu from '@/views/system/terminalManage/terminalMenu'
import ShortOffline from '@/views/dataEncryption/encryption/shortOfflineStrategy'
import NetDisk from '@/views/behaviorManage/network/netDisk'
import ApplySecurityAccess from '@/views/dataEncryption/encryption/applySecurityAccess'
import AppReportSet from '@/views/system/terminalManage/appReportSet'
import SensitiveBackupConfig from '@/views/contentStrategy/strategy/sensitiveBackupConfig'
import EnDeFileScan from '@/views/dataEncryption/encryption/EnDeFileScan'
import AppLogConfig from '@/views/behaviorManage/application/appLogConfig'
import ProcessInject from '@/views/system/terminalManage/processInject/editDlg'
import OperatorConfig from '@/views/dataEncryption/encryption/operatorConfig'
import OfficeWaterMark from '@/views/dataEncryption/encryption/officeWaterMark/editDlg'
import SoftwareLimit from '@/views/softwareManage/strategy/softwareLimit/editDlg'
import RequireInstall from '@/views/softwareManage/strategy/requireInstall/editDlg'
import RequireRun from '@/views/softwareManage/strategy/requireRun/editDlg'
import UsbFileConfig from '@/views/behaviorManage/hardware/usbFileConfig'
import SysAlarmConfig from '@/views/behaviorManage/application/sysAlarmConfig/editDlg'
import WifiCollect from '@/views/behaviorManage/network/wifiCollect/editDlg'
import WifiBlock from '@/views/behaviorManage/network/wifiBlock/editDlg'
import FtpControlConfig from '@/views/behaviorManage/network/ftpControl/editDlg'
import GroupPolicy from '@/views/behaviorManage/application/groupPolicy/editDlg'
import ForumUrlFilter from '@/views/behaviorManage/network/forumUrlFilter'
import WebBrowseUrlFilter from '@/views/behaviorManage/network/webBrowseURLFilter'
import OfflineLockScreen from '@/views/system/terminalManage/offlineLockScreen/editDlg'
import SoftWareTask from '@/views/assets/systemMaintenance/softWareTaskStrategy/taskList'
import PersonalizePolicy from '@/views/behaviorManage/hardware/personalizePolicy'
import IconRefreshDir from '@/views/dataEncryption/encryption/iconRefreshDir'
import DocumentTrack from '@/views/dataEncryption/fileTrace/documentTrack'
import BlindWatermark from '@/views/dataEncryption/fileTrace/blindWatermark'
import EmailAttachFile from '@/views/behaviorManage/network/emailAttachFile'
import AdbLimit from '@/views/behaviorManage/hardware/adbLimit/editDlg'
import MobileTerminal from '@/views/system/terminalManage/mobileTerminal'
import DiskScanSelfCheck from '@/views/contentStrategy/strategy/diskScanSelfCheck/editDlg'
import ImportStg from '@/views/common/importStg'
import MobileTerminalUpgradeStrategy from '@/views/system/terminalManage/mobileTerminalUpgrade/mobileTerminalUpgradeStrategy'
import ComputerEnergySaving from '@/views/behaviorManage/monitor/energySaving'
import WebpagePasteAudit from '@/views/behaviorManage/network/webpagePasteAudit'
import WebpageBrowseAudit from '@/views/behaviorManage/network/webpageBrowseAudit'
import AppOpenSuffixStg from '@/views/system/terminalManage/mobileTerminalFileTool/appOpenSuffixStg'
import SoftwareDownloadStrategy from '@/views/system/terminalManage/softwareManager/downloadStrategy'
import NetInterfaceLimit from '@/views/behaviorManage/hardware/netInterfaceLimit/editDlg'
import UsbInterfaceLimit from '@/views/behaviorManage/hardware/usbInterfaceLimit/editDlg'
import SoftwareCopyrightStrategy from '@/views/softwareManage/copyrightManage/strategy/editDlg'
// 终端安全检测是跳转页面，不用导入组件
// import TermSecurityDetection from '@/views/system/terminalManage/softwareManager/termSecurity/index'
import PatchInstallation from '@/views/assets/patchManage/patchStrategy/patchInstallationStrategy/edit'
import PatchSetting from '@/views/assets/patchManage/patchStrategy'
import BrowserFileDownloadStrategy from '@/views/behaviorAuditing/network/browserFileDownloadStrategy/editDlg'
import mstscControl from '@/views/system/terminalManage/remoteDesktopControl'
import PatchAutoInstallationStrategy from '@/views/assets/patchManage/patchStrategy/patchAutoInstallationStrategy/edit';
import TelnetCommControl from '@/views/behaviorManage/network/telnetCommControl/editDlg'
import decToolLimitStg from '@/views/softwareManage/strategy/softwareBlackList/baseConfig'
import WpsSetup from '@/views/system/terminalManage/mobileTerminalFileTool/wpsSetup/editDlg'
import TimelyBackupStg from '@/views/dataEncryption/backup/timelyBackup/editDlg'
import FullDiskScanBackupStg from '@/views/dataEncryption/backup/fullDiskScanBackup/editDlg'
import ServerBackupConfigStg from '@/views/dataEncryption/backup/serverBackupConfig/editDlg'
import LabelPermissionControl from '@/views/behaviorManage/hardware/labelPermissionControl/editDlg'
import LandLabeling from '@/views/behaviorManage/hardware/landLabeling/editDlg'
import ManualLabel from '@/views/behaviorManage/hardware/manualLabel/editDlg'
import fileOutgoingCheckStatistics from '@/views/system/configManage/fileOutgoingCheckStatisticsConfig'
import AiModelStrategy from '@/views/behaviorManage/network/aiModelStrategy'
import AiChatStrategy from '@/views/behaviorManage/network/aiChatStrategy'
import AiUploadFile from '@/views/behaviorManage/network/aiUploadFile'

export default {
  PrinterSet, WaterMarkDialog, ImTool, ImFile, AppBlock, WinTitleBlock,
  AppVersionLimit, InstallPacket, SpecialPath, Url, WebPost, WebPort, WebFlow, NetCtrl, EmailKeywordBlock, VideoStrategy, BurnConfig,
  ShareConfig, FileFilterSetup, Driver, OtherDeviceLimit, EffectiveContentStrategy, EffectiveContentConfig, Content, BlueToothConfig, MtpConfig, LogFilter,
  EncryptSpecialPath, FilePermissionControl, HttpWhiteList, BrowserFile, Clipboard, Screenshot, WorkMode, Process, ProcessConfigStg, SpecialSuffix,
  IpAndMacBind, ReadPermission, ProcessCollectRule, DiskScan, SmartEnc, TranslucentEnc, UserDense, BackUpConfig, mcode,
  FileOutgoingConfig, ProcessMonitor, TerminalConfig, SysBaseConfig, outgoingProcess, SoftwareBlacklist, outgoingTemplate, outgoingScreenWaterMark,
  outgoingPrintWaterMark, BatchEncOrDec, TerminalMenu, ShortOffline, NetDisk, ApplySecurityAccess, AppReportSet, SensitiveBackupConfig,
  EnDeFileScan, AppLogConfig, ProcessInject, OperatorConfig, OfficeWaterMark, SoftwareLimit, RequireInstall, RequireRun, UsbFileConfig,
  SysAlarmConfig, WifiCollect, WifiBlock, FtpControlConfig, GroupPolicy, ForumUrlFilter, WebBrowseUrlFilter, MailReceiver, MailSender, MailCopy,
  SoftWareTask, OfflineLockScreen, PersonalizePolicy, IconRefreshDir, DocumentTrack, BlindWatermark, EmailAttachFile, AdbLimit,
  MobileTerminal, DiskScanSelfCheck, ImportStg, MobileTerminalUpgradeStrategy, ComputerEnergySaving, NetInterfaceLimit, UsbInterfaceLimit, WebpagePasteAudit, WebpageBrowseAudit,
  AppOpenSuffixStg, SoftwareDownloadStrategy, PatchInstallation, PatchAutoInstallationStrategy, PatchSetting, BrowserFileDownloadStrategy, mstscControl,
  TelnetCommControl, decToolLimitStg, WpsSetup, TimelyBackupStg, FullDiskScanBackupStg, ServerBackupConfigStg, LabelPermissionControl, LandLabeling, ManualLabel, fileOutgoingCheckStatistics,
  AiModelStrategy, AiChatStrategy, AiUploadFile, SoftwareCopyrightStrategy
}
