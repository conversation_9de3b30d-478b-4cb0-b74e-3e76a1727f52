<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogVisible"
      :width="dialogWidth + 'px'"
      :title="$t('pages.termStgAndServerStgDifferent')"
      @close="handleDrag"
    >
      <Form ref="dataForm" :model="temp" label-width="180px">
        <FormItem :label="$t('pages.termStgUpdateTime')">
          {{ temp.termStgModifyTime }}
        </FormItem>
        <FormItem :label="$t('pages.serverStgUpdateTime')">
          {{ temp.serverStgModifyTime }}
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDrag">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'StgDifferentDlg',
  props: {
    dialogWidth: {
      type: String,
      default: '500'
    }
  },
  data() {
    return {
      dialogVisible: false,
      temp: {},
      defaultTemp: {
        termStgModifyTime: null,
        serverStgModifyTime: null,
        stgDifferent: false
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    show(row) {
      this.temp = Object.assign({}, this.defaultTemp, row)
      this.dialogVisible = true
    },
    handleDrag() {
      this.dialogVisible = false
      this.temp = Object.assign({}, this.defaultTemp)
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
