<template>
  <!-- 策略类型导出 -->
  <el-dialog
    v-el-drag-dialog
    :modal="false"
    :title="$t('pages.strategyOverViews_export')"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="600px"
    @close="handleDrag"
  >
    <el-row style="padding: 10px 19px 10px;">
      <el-col v-if="paneName === 'strategyType'" :span="12" style="position: relative;">
        <tree-select
          ref="groupTree"
          node-key="myDataId"
          is-filter
          :data="treeData"
          :multiple="true"
          :width="300"
          :height="500"
          :default-expanded-keys="defaultExpandedKeys"
          :placeholder="$t('pages.strategyOverViews_selectedStgType')"
          clearable
          @change="stgTypeExportChange"
        />
        <div v-if="errorFlag" style="color: red; position:absolute; ">{{ $t('pages.strategyOverViews_selectedStgType') }}</div>
      </el-col>
      <el-col :span="2"><br></el-col>
      <el-col :span="paneName === 'strategyType' ? 10 : 22">
        <div style="align-items: center; height: 30px; display: flex">
          <el-checkbox v-model="isExportNotAble">{{ $t('pages.strategyOverViews_exportNotActiveStg') }}</el-checkbox>
        </div>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <common-downloader
        ref="commonDownloader"
        :loading="submitting"
        :name="getFileName"
        :button-name="$t('pages.strategyOverViews_export')"
        button-type="primary"
        button-icon=""
        :before-download="beforeDownload"
        @download="exportDataHandler"
      />
      <el-button @click="handleDrag">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { excelExportData } from '@/api/behaviorManage/StrategyOverViews/strategyOverViews';
import CommonDownloader from '@/components/DownloadManager/common'
import moment from 'moment'

export default {
  name: 'ExcelStgExport',
  components: { CommonDownloader },
  props: {
  },
  data() {
    return {
      visible: false,
      treeData: [],  //  左边策略树
      paneName: '',
      defaultExpandedKeys: ['all-0'],
      submitting: false,
      isExportNotAble: false,
      selectedIds: [],  //  选中的id
      objectId: null,
      objectType: null,
      objectName: null,
      usedScope: null,
      errorFlag: false,
      selectedStgTypeIds: [],  //  选中需要导出的策略类型Id，若为空，表示所有策略类型都导出
      searchInfo: null, //  模糊查询字段
      dataType: null,  //  若为终端，保存终端的操作系统类型，0：windows，1：linux，2：mac，3：移动终端
      selectTermOnline: false //  选中的终端是否为在线终
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    typeGridTable() {
      return this.$refs['strategyTypeList']
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    getFileName() {
      let fileName = this.$t('route.strategyOverViews')
      if (this.paneName !== 'strategyType') {
        fileName += '_' + this.getObjectTypeName() + '_' + this.objectName
      }
      fileName += '_' + moment().format('YYYYMMDD') + '.xlsx'
      return fileName
    },
    getObjectTypeName() {
      const objectTypeName = {
        1: 'table.terminal',
        2: 'table.user',
        3: 'pages.terminalGroup',
        4: 'pages.userGroup'
      }[this.objectType]
      if (objectTypeName) {
        return this.$t(objectTypeName)
      }
      return ''
    },
    stgTypeShow(stgTypeTree, searchInfo) {
      this.paneName = 'strategyType'
      this.treeData = stgTypeTree
      this.searchInfo = searchInfo
      this.visible = true
    },
    objTypeShow(download, data) {
      this.treeData = data.stgTypeTree
      this.objectType = data.objectType
      this.objectId = data.objectId
      this.objectName = data.objectName
      this.usedScope = data.usedScope
      this.selectedStgTypeIds = [...data.strategyTypes]
      this.searchInfo = data.searchInfo
      this.dataType = data.dataType
      this.onLineUserPriority = data.onLineUserPriority
      if (download) {
        this.$refs.commonDownloader.handleDownload()
      } else {
        this.visible = true
      }
    },
    stgTypeChange(selectedKeys, options) {

    },
    clearData() {
      this.paneName = ''
      this.treeData = []
      this.selectedIds = []
      this.visible = false
      this.isExportNotAble = false
      this.objectType = null
      this.objectId = null
      this.objectName = null
      this.errorFlag = false
      this.selectedStgTypeIds = []
      this.searchInfo = null
    },
    handleDrag() {
      this.clearData()
    },
    stgTypeExportChange(keys, options) {
      //  获取策略类型id
      const ids = [];
      (options || []).forEach(item => {
        if (item.type && item.type === 'type') {
          ids.push(item.id);
        }
      })
      this.selectedIds = ids;
      this.errorFlag = this.selectedIds.length === 0;
    },
    beforeDownload() {
      if (this.paneName === 'strategyType' && this.selectedIds.length === 0) {
        this.errorFlag = true
        return false
      }
      return true
    },
    exportDataHandler(file) {
      this.submitting = true
      const stgTypes = {}
      let data
      if (this.paneName === 'strategyType') {
        this.getStgNameByStgTypeNumbers(this.treeData, this.selectedIds, stgTypes);
        data = { type: 2, stgTypeNumbers: this.selectedIds.join(','), stgTypeMap: stgTypes, exportAll: this.isExportNotAble, searchInfo: this.searchInfo }
      } else {
        this.getStgName(this.treeData, stgTypes, this.selectedStgTypeIds);
        data = { type: 1,
          objectId: this.objectId,
          objectType: this.objectType,
          stgTypeMap: stgTypes,
          exportAll: this.isExportNotAble,
          usedScope: this.usedScope,
          searchInfo: this.searchInfo,
          dataType: this.dataType,
          onLineUserPriority: this.onLineUserPriority
        }
      }
      const opts = { file, jwt: true, topic: this.$route.name }
      excelExportData(data, opts).then(res => {
        this.clearData()
        this.submitting = false
      }).catch(() => { this.submitting = false })
    },
    getStgName(treeData, stgTypes, selectedStgTypeIds) {
      treeData = treeData || []
      treeData.forEach(item => {
        //  添加选中的策略数据
        if (item.type && item.type === 'type' && (selectedStgTypeIds.length === 0 || selectedStgTypeIds.includes(item.id + ''))) {
          stgTypes[item.id] = item.strategyType
        }
        this.getStgName(item.children || [], stgTypes, selectedStgTypeIds);
      })
    },
    //  获取对应策略类型id的策略名称
    getStgNameByStgTypeNumbers(treeData, stgNumbers, stgTypes) {
      treeData = treeData || []
      treeData.forEach(item => {
        if (item.type && item.type === 'type' && stgNumbers.includes(item.id)) {
          stgTypes[item.id] = item.strategyType
        }
        this.getStgNameByStgTypeNumbers(item.children || [], stgNumbers, stgTypes);
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table__indent {
  padding-left: 20px;
}
.app-container.strategy{
  position: relative;
  width: 100%;
  height: calc(100vh - 90px);
  padding: 5px 20px 20px;
}
.select_tags {
  ::v-deep .el-select__tags {
    flex-wrap: unset;
    overflow: auto;
  }
}
</style>

