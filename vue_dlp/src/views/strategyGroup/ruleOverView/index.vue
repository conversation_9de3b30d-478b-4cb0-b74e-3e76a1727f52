<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" style="float: right;" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.enterRespondRule')" style="width: 200px;float: right;" @keyup.enter.native="handleFilter"></el-input>
        <el-select v-model="query.active" style="float: right;">
          <el-option v-for="(item, index) in stgActiveOpts" :key="index" :value="item.value" :label="item.label"/>
        </el-select>
      </div>

      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="false"
      />
    </div>
  </div>
</template>

<script>
import { getRuleStrategyList } from '@/api/behaviorManage/StrategyOverViews/strategyOverViews'
import { refreshPage } from '@/utils'
import { formatMenuCode, toCodeMap } from '@/router'
import { stgActiveIconFormatter } from '@/utils/formatter'
import { generateTitle } from '@/utils/i18n';

export default {
  name: 'RuleStrategyOverViews',
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName', width: '100' },
        { prop: 'alarmLimitDescribe', label: 'ruleDescribe', width: '300', formatter: this.ruleInfoFormatter },
        { prop: 'strategyName', label: 'referencedStg', width: '200', formatter: this.strategyNameFormatter, iconFormatter: this.activeFormatter },
        { prop: 'entityName', label: 'referencedStgSource', width: '160' },
        { label: 'detailInfo', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'maintainStg', click: this.gotoStrategyPage }
          ]
        }
      ],
      stgActiveOpts: [
        { value: undefined, label: this.$t('pages.showRelevanceStg') },
        { value: true, label: this.$t('pages.showActiveStg') }
      ],
      query: { // 查询条件
        page: 1,
        active: undefined,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    },
    menuCodeMap() {
      return toCodeMap(this.$store.getters.permission_routes)
    }
  },
  created() {
    //  确保首次打开界面时，列表查询的数据是终端 或 操作员
    if (this.strategyTargetTree) {
      const currentPanel = this.strategyTargetTree.getCurrentPanel();
      if (currentPanel) {
        this.query.objectType = currentPanel.type === '3' ? 1 : 2;
      }
    }
  },
  methods: {
    generateTitle,
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRuleStrategyList(searchQuery)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = tabName === 'terminal' ? 1 : tabName === 'user' ? 2 : undefined;
        this.query.objectId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    gotoStrategyPage(row) {
      const isPreStg = row.stgDefType && row.stgDefType === 1
      formatMenuCode(this.$store.getters.permission_routes, row, 'routerPath')
      const routerPath = !row.editUrl ? row.routerPath : row.editUrl
      const path = routerPath ? routerPath.split('/').pop() : ''
      const fullPath = this.$store.getters.routesPath[routerPath]
      const params = path.indexOf('?') > -1 ? '?' + path.split('?').pop() : ''
      if (fullPath) {
        if (isPreStg) {
          this.$router.push({ path: fullPath + '/prepare' + params, query: { treeable: false }})
        } else {
          this.$router.push({ path: fullPath + params, query: { objectType: row.objectType, objectId: row.objectId }})
        }
      } else {
        console.warn('path is not exist')
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    ruleInfoFormatter(row, data) {
      const alarmLimitDescribe = row.alarmLimitDescribe ? `${this.$t('pages.treatmentMeasure')}: (${row.alarmLimitDescribe}); ` : ''
      const terminalAlarmParam = row.terminalAlarmParam ? `${this.$t('pages.alarmPara')}: (${row.terminalAlarmParam}); ` : ''
      const sysUsers = row.sysUsers ? `${this.$t('pages.alarmAdmin')}: (${row.sysUsers}); ` : ''
      return alarmLimitDescribe + terminalAlarmParam + sysUsers
    },
    activeFormatter(row, data) {
      return stgActiveIconFormatter({ active: row.active && (row.hasOwnProperty('objectId') || !row.stgDefType) })
    },
    // 后台返回的数据没有做多语言处理，这边只处理一部分。若后台处理了，则此处可删除
    strategyNameFormatter: function(row, data) {
      const { strategyName, strategyKey, routerPath } = row
      //  installStrategy: 安装限制  uninstallStrategy: 卸载限制
      if (['installStrategy', 'uninstallStrategy', 'specialPathStrategy'].includes(strategyKey)) {
        const index = strategyName.indexOf('：');
        if (index > -1) {
          const title = this.$t('route.installPackage');
          const extraTitle = strategyKey === 'installStrategy' ? this.$t('pages.installLimit')
            : strategyKey === 'uninstallStrategy' ? this.$t('pages.uninstallLimit')
              : strategyKey === 'specialPathStrategy' ? this.$t('pages.specialPathStrategy') : '';
          const stgName = strategyName.substr(index + 1)
          return `${title}-${extraTitle}：${stgName}`
        }
        return strategyName;
      }
      const menu = this.menuCodeMap[routerPath]
      if (menu) {
        const title = this.generateTitle(menu.title)
        const index = strategyName.indexOf('：');
        if (index > -1) {
          const extraMap = { mailSenderStrategy: this.$t('pages.mailSender'), mailWhiteListStrategy: this.$t('pages.mailReceiver') }
          const extraTitle = extraMap[strategyKey] ? ` - ${extraMap[strategyKey]}` : ''
          const stgName = strategyName.substr(index + 1)
          return `${title}${extraTitle}：${stgName}`
        }
      }
      return strategyName
    }
  }
}
</script>

