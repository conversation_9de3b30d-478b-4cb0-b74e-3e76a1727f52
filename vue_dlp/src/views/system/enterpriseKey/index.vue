<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">{{ $t('pages.addEnterpriseKey') }}</el-button>
        <el-upload
          ref="ldkfile"
          action="1111"
          style="display:inline-block"
          :limit="1"
          list-type="text"
          accept=".ldk"
          :show-file-list="false"
          :before-upload="beforeUpload"
        >
          <el-button v-permission="'148'" icon="el-icon-upload2" size="mini">{{ $t('button.import') }}</el-button>
        </el-upload>
        <common-downloader
          ref="downloader"
          v-permission="'147'"
          :loading="submitting"
          :disabled="!canExport"
          :name="getFilename"
          :steps="3"
          :button-name="$t('button.export')"
          button-size="mini"
          @download="exportInfo"
        />
        <el-button v-if="encAlgorithmConfigAble" icon="el-icon-setting" size="mini" @click="handleSetting">{{ $t("pages.algorithmConfig") }}</el-button>
        <span v-if="encAlgorithmConfigAble && currentEncAlgorithm != null" style="color:red;float:right;">{{ $t('pages.currentEncAlgorithm') + ': ' + currentEncAlgorithm }}</span>
      </div>
      <grid-table ref="enterpriseKeyList" :after-load="afterLoad" :col-model="colModel" :multi-select="false" :show-pager="false" :row-data-api="rowDataApi"/>
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      custom-class="enterpriseKey"
      width="400px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px">
        <FormItem :label="$t('pages.enterpriseKey')" prop="enterpriseKey">
          <el-input v-model="temp.enterpriseKey" maxlength="20" minlength="1" type="password" show-password/>
        </FormItem>

        <FormItem :label="$t('pages.confirmEnterpriseKey')" prop="confirmKey">
          <el-input v-model="temp.confirmKey" maxlength="20" minlength="1" type="password" show-password/>
        </FormItem>
        <span class="tip">{{ $t('pages.enterpriseKey_text1') }}</span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelCreateData()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <config-enc-key ref="configDlg" @submitEnd="getCurrentEncAlgorithm()"/>
  </div>
</template>
<script>
import { downloadFile, getTemplatePage, outPutInfo, saveInfo, uploadFile, getEncAlgorithm } from '@/api/system/enterpriseKey'
import { getDictLabel, getEncAlgorithmDict } from '@/utils/dictionary'
import { getEncVer4EditAble } from '@/api/property'
import CommonDownloader from '@/components/DownloadManager/common'
import ConfigEncKey from './configDlg'
import moment from 'moment'
import { aesEncode, formatAesKey } from '@/utils/encrypt'

export default {
  name: 'EnterpriseKey',
  components: { CommonDownloader, ConfigEncKey },
  data() {
    const validateKey = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('pages.enterpriseKey_text2')))
      } else {
        if (value === this.temp.enterpriseKey) {
          callback()
        } else {
          return callback(new Error(this.$t('pages.enterpriseKey_text3')))
        }
      }
    }
    const validateEnterpriseKey = (rule, value, callback) => {
      const message = this.$t('pages.enterpriseKey_text4')
      const reg = /^[A-Za-z0-9]{1,20}$/
      if (!value) {
        return callback(new Error(this.$t('pages.enterpriseKey_text5')))
      } else {
        if (reg.test(value)) {
          callback()
        } else {
          return callback(new Error(message))
        }
      }
    }
    return {
      result: '',
      query: { // 查询条件
        page: 1
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.$t('pages.enterpriseKey_update'),
        create: this.$t('pages.enterpriseKey_create')
      },
      temp: {
        enterpriseKey: '',
        confirmKey: ''
      },
      defaultTemp: {
        enterpriseKey: '',
        confirmKey: ''
      },
      rules: {
        enterpriseKey: [{ required: true, trigger: 'blur', validator: validateEnterpriseKey }],
        confirmKey: [{ required: true, trigger: 'blur', validator: validateKey }]
      },
      colModel: [
        {
          prop: 'keyId',
          label: 'keyId',
          width: '50',
          fixed: true
        },
        { prop: 'pkey', label: 'pkey', width: '300' },
        { prop: 'flag', label: 'flag', width: '90', formatter: (row, data) => { return row.flag == 1 ? this.$t('pages.takeEffect') : '' } }
        // {
        //   label: '操作',
        //   type: 'button',
        //   fixed: 'right',
        //   fixedWidth: '100',
        //   buttons: [{ label: '修改', click: this.handleUpdate }]
        // }
      ],
      statusCodeMap: {
        0: 'text.importFail',
        1: 'text.importSecretKeySuccess',
        2: 'text.importFileFail',
        3: 'text.importFileFail1',
        4: 'text.importFileFail2',
        9: 'text.importFileFail3',
        'other': 'text.unknownError'
      },
      pkeyinfoStatusCodeMap: {
        0: 'pages.enterpriseKeyUnexpectedFailure',
        1: 'pages.enterpriseKeySuccess',
        2: 'pages.enterpriseKeyLenOut',
        3: 'pages.enterpriseKeyContentAndLenMatch',
        4: 'pages.enterpriseKeyNotAuth',
        5: 'pages.enterpriseKeyMainKeyError',
        6: 'pages.enterpriseKeyCryptoOperationFail',
        7: 'pages.enterpriseKeyNumberOut',
        8: 'pages.enterpriseKeyNotFindSoftDog',
        9: 'pages.enterpriseKeyDbOperationFail',
        'other': 'pages.enterpriseKeyUnKnownError'
      },
      encAlgorithmConfigAble: false,
      currentEncAlgorithm: null, // 当前加密算法
      canExport: false  // 是否可以导出
    }
  },
  created() {
    this.getCurrentEncAlgorithm()
  },
  methods: {
    getFilename() {
      return this.$t('pages.pkeyExportFileName') + `_${moment().format('YYYYMMDD')}.ldk`
    },
    getCurrentEncAlgorithm() {
      getEncVer4EditAble().then(resp => {
        this.encAlgorithmConfigAble = resp.data && (this.$store.getters.isSuperRole || this.$store.getters.isSuperThreeUser)
        getEncAlgorithm().then(respond => {
          this.currentEncAlgorithm = null
          if (respond.data && !respond.data.closeEnc4) {
            const dict = getEncAlgorithmDict()
            this.currentEncAlgorithm = getDictLabel(dict, respond.data.algorithm)
          }
        })
      })
    },
    afterLoad(datas) {
      this.canExport = datas.length > 0
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTemplatePage(searchQuery)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleSetting() {
      this.$refs['configDlg'].show()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.dialogStatus = 'create'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    submitData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const formData = { pkey: aesEncode(this.temp.enterpriseKey, formatAesKey('tr838408', '')) }
          saveInfo(formData).then(response => {
            const that = this
            this.$socket.subscribeToAjax(response, 'pkeyinfo/result', (respond, handle) => {
              that.submitting = false // 关闭菊花
              handle.close()
              that.dialogFormVisible = false
              // 重刷列表
              this.$refs.enterpriseKeyList.execRowDataApi()
              const data = respond.data
              const msg = this.$t(this.pkeyinfoStatusCodeMap[data.type] || this.pkeyinfoStatusCodeMap['other'])
              if (data.type === 1) {
                that.$notify({
                  title: this.$t('text.success'),
                  message: msg,
                  type: 'success',
                  duration: 2000
                })
                that.$refs['downloader'] && that.$refs['downloader'].handleDownload()
              } else {
                that.$notify({
                  title: this.$t('text.fail'),
                  message: msg,
                  type: 'error',
                  duration: 2000
                })
              }
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    createData() {
      this.submitting = true // 按钮菊花开启
      const enterpriseKeys = this.$refs.enterpriseKeyList.getDatas()
      if (enterpriseKeys.length > 0) {
        this.$confirmBox(this.$t('pages.enterpriseKey_text7'), this.$t('text.prompt')).then(() => {
          this.submitData()
        }).catch(() => {
          this.submitting = false
        })
      } else {
        this.submitData()
      }
    },
    cancelCreateData() {
      this.submitting = false
      this.dialogFormVisible = false
    },
    updateData() {},
    exportInfo(file) {
      this.submitting = true
      outPutInfo().then(response => {
        file.active = 1
        file.percent = 0
        const that = this
        // console.log(response)
        this.$socket.subscribeToAjax(response, 'pkeyfile/result', (respond, handle) => {
          // 得到异步结果
          handle.close()
          file.active = 2
          file.percent = 0
          // 请求文件下载
          const data = { files: respond.data.fileName }
          const opts = { file, jwt: true, topic: this.$route.name }
          downloadFile(data, opts).then(() => {
            that.submitting = false
            that.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.enterpriseKey_text8'),
              type: 'success',
              duration: 2000
            })
          })
        })
      }).catch(reason => {
        if (reason) {
          file.error = reason || reason.message
        } else {
          file.error = true
        }
        this.submitting = false
      })
    },
    async beforeUpload() {
      const enterpriseKeys = this.$refs.enterpriseKeyList.getDatas()
      if (enterpriseKeys.length > 0) {
        await this.$confirmBox(this.$t('pages.enterpriseKey_text6'), this.$t('text.prompt')).then(() => {
          this.uploaded()
        }).catch(() => {
        })
      } else {
        this.uploaded()
      }
      return false
    },
    uploaded() {
      const formData = new FormData()
      const uploadFiles = this.$refs.ldkfile.uploadFiles
      // console.log(uploadFiles[0])
      formData.append('file', uploadFiles[0].raw)
      uploadFile(formData).then(response => {
        // console.log(this.$t('text.success'))
        const that = this
        this.$socket.subscribeToAjax(response, 'pkeyupload/result', (respond, handle) => {
          handle.close()
          const data = respond.data
          const msg = this.$t(this.statusCodeMap[data.type] || this.statusCodeMap['other'])
          if (data.type == '1') {
            that.$notify({
              title: this.$t('text.success'),
              message: msg,
              type: 'success',
              duration: 2000
            })
            // 导入成功之后刷新
            this.$refs.enterpriseKeyList.execRowDataApi()
          } else {
            that.$notify({
              title: this.$t('text.fail'),
              message: msg,
              type: 'error',
              duration: 2000
            })
          }
        })
        // const that = this
        // this.$socket.subscribeToAjax(response, 'pkeyinput/result', (respond, handle) => {
        //   handle.close()
        //   that.fileRegResult = respond.data
        // })
      })
    }
  }
}
</script>

<style lang="scss">
.enterpriseKey{
  .el-dialog__body{
    padding-bottom: 0;
  }
}

</style>

<style lang="scss" scoped>
.tip {
  color: #409EFF;
  font-size: 12px;
  display: inline-block;
  margin: 6px 0 0 0;
  padding-left: 10px;
  line-height: 20px;
}
>>>.el-input__inner{
  padding-right: 45px;
}
</style>

