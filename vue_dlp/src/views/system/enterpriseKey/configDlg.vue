<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.algorithmConfig')"
    :visible.sync="dialogVisible"
    width="400px"
    @dragDialog="handleDrag"
  >
    <Form
      ref="managerConfigForm"
      :rules="rules"
      :model="managerConfig"
      label-width="20px"
      :extra-width="{en: 0}"
      label-position="right"
    >
      <FormItem>
        <el-checkbox v-model="temp.closeEnc4" :true-label="1" :false-label="0">
          {{ $t('pages.closeEncVer4') }}
        </el-checkbox>
      </FormItem>
      <FormItem :label="$t('pages.encAlgorithm')" label-width="80px" :extra-width="{en: 70}">
        <el-radio-group v-model="temp.algorithm" :disabled="temp.closeEnc4 == 1">
          <el-radio :label="1">
            {{ $t('pages.internationalAlgorithm') }}
            <FormItem prop="algorithm">
              <el-select v-model="algorithm" :disabled="temp.algorithm != 1 || temp.closeEnc4 == 1">
                <el-option :label="$t('pages.encAlgorithmRc')" :value="0"></el-option>
                <el-option :label="$t('pages.encAlgorithmAes')" :value="1"></el-option>
              </el-select>
            </FormItem>
            <!-- {{ $t('pages.internationalAlgorithm') }} -->
          </el-radio>
          <el-radio :label="23">{{ $t('pages.encAlgorithmZuc') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleConfig">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getEncAlgorithm, updateEncAlgorithm } from '@/api/system/enterpriseKey'
import { getEncAlgorithmDict } from '@/utils/dictionary'

export default {
  name: 'ConfigEncKey',
  data() {
    return {
      dialogVisible: false,
      temp: {},
      defaultTemp: {
        algorithm: 0,
        closeEnc4: 0
      },
      algorithm: '',
      managerConfig: {},
      encAlgorithmOptions: [],
      submitting: false,
      rules: {
        algorithm: [
          { validator: this.algorithmValidator, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {

  },
  created() {
    this.getConfig()
  },
  methods: {
    show() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.encAlgorithmOptions = getEncAlgorithmDict()
      this.getConfig()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['managerConfigForm'].clearValidate()
      })
    },
    handleDrag() {
    },
    handleConfig() {
      this.$refs['managerConfigForm'].validate((valid) => {
        if (valid) {
          if (this.temp.algorithm == 1) {
            this.temp.algorithm = this.algorithm
          } else {
            this.algorithm = ''
          }
          updateEncAlgorithm(this.temp).then(res => {
            this.dialogVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.$emit('submitEnd', this.temp)
            this.submitting = false
          }).catch(e => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    getConfig() {
      getEncAlgorithm().then(respond => {
        this.temp = Object.assign({}, this.defaultTemp, respond.data)
        if (this.temp.algorithm == 0 || this.temp.algorithm == 1) {
          this.algorithm = this.temp.algorithm
          this.temp.algorithm = 1
        } else {
          this.algorithm = ''
        }
      })
      this.$forceUpdate()
    },
    algorithmValidator(rule, value, callback) {
      if (this.temp.closeEnc4 == 0 && this.temp.algorithm == 1 && (this.algorithm === '')) {
        callback(new Error(this.$t('pages.enterpriseKey_text10')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
