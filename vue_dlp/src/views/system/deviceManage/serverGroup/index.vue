<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="serverGroupTree"
        :data="treeNode"
        node-key="dataId"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!btnEnable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <!--<el-button type="primary" icon="el-icon-s-management" size="mini" @click="viewServer">
          服务器管理
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.whiteGroupName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <grid-table ref="serverGroupList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px" style="width: 500px; margin-left:25px;">
        <FormItem :label="$t('pages.whiteGroupName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"></el-input>
        </FormItem>
        <!--<FormItem label="上级服务器组" prop="parentId">
          <tree-select
            :data="treeSelectNode"
            node-key="dataId"
            :checked-keys="[temp.parentId]"
            :filter-key="filterKey"
            :width="296"
            @change="parentIdSelectChange"
          />
        </FormItem>-->
        <FormItem label="地区" prop="area">
          <el-input v-model="temp.area" rows="2" resize="none"></el-input>
        </FormItem>
        <el-row>
          <el-col :span="8" >
            <FormItem label="开启组内负载" prop="area">
              <el-switch v-model="temp.loadBanlance" :active-value="1" :inactive-value="0" />
            </FormItem>
          </el-col>
          <el-col :span="2" style="line-height: 35px; padding-left: 5px;">
            <el-tooltip class="item" effect="dark" placement="right">
              <div slot="content">开启组内负载，统一分组内同一类型的服务器将进行负载</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="服务器管理"
      :visible.sync="dialogFormServerVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="serverForm" :model="temp" label-position="right" label-width="104px" style="width: 750px;">
        <FormItem :label="$t('pages.whiteGroupName')">{{ temp.name }}</FormItem>
        <grid-select
          ref="serverGridSelect"
          :height="300"
          :col-model="serverColModel"
          searchable
          :search-prop="{ key: 'searchInfo', label: $t('pages.whiteGroupName')}"
          :select-row-data-api="selectServerRowDataApi"
          :selected-row-data-api="selectedServerRowDataApi"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="bindServer()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormServerVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getServerGroupPage, getServerGroupByName, getServerGroupTree, createServerGroup,
  updateServerGroup, deleteServerGroup, validateDelServerGroup
} from '@/api/system/deviceManage/serverGroup'
import { getServerPage, getServerByGroup, allotServer } from '@/api/system/deviceManage/server'
import GridSelect from '@/components/GridSelect'

export default {
  name: 'ServerGroup',
  components: { GridSelect },
  data() {
    return {
      colModel: [
        { prop: 'name', label: '服务器组名称', width: '150', fixed: true, sort: true },
        { prop: 'parentId', label: '上级服务器组', width: '200', formatter: this.parentFormatter },
        { prop: 'size', label: '服务器数', width: '80' },
        { prop: 'area', label: '地区', width: '150' },
        { prop: 'loadBanlance', label: '开启组内负载', active: 1, inactive: 0, width: '80', type: 'switch', switchChange: this.loadBanlanceClick },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: '服务器管理', click: this.handleBind }
          ]
        }
      ],
      serverColModel: [
        { prop: 'name', label: '服务器名称', width: '120', fixed: true, sort: true },
        { prop: 'group.name', label: '服务器组', width: '100', formatter: this.groupFormatter },
        { prop: 'devId', label: '设备编号', width: '80' }
      ],
      query: { // 查询条件
        page: 1,
        parentId: '',
        searchInfo: ''
      },
      showTree: false,
      btnEnable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        area: '',
        size: 0,
        parentId: undefined,
        loadBanlance: 1
      },
      dialogFormVisible: false,
      dialogTerminalVisible: false,
      dialogFormServerVisible: false,
      dialogStatus: '',
      textMap: {
        update: '修改服务器组',
        create: '新增服务器组'
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.validateUniqueName, trigger: 'blur' }
        ],
        parentId: [{ required: true, message: '请选择上级服务器组', trigger: 'blur' }]
      },
      submitting: false,
      treeNode: [{ label: '服务器组', dataId: '', children: [] }],
      defaultExpandedKeys: [''],
      treeNodeType: '',
      treeSelectNode: [],
      defaultTreeSelectNode: [{ id: '0', dataId: '0', label: '无' }],
      groupId: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverGroupList']
    },
    treeMenu() {
      return this.$refs['serverGroupTree']
    },
    filterKey() {
      return this.temp.id
    }
  },
  created() {
    this.getServerGroupTreeNode()
  },
  methods: {
    serverSelect() {
      return this.$refs['serverGridSelect']
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: '' + data.id,
        label: data.name,
        parentId: this.treeNodeType + data.parentId
      }
    },
    changeTreeSelectNode() {
      this.treeSelectNode = [...this.defaultTreeSelectNode]
      this.treeNode[0].children.forEach(node => {
        this.treeSelectNode.push(node)
      })
    },
    selectionChangeEnd(rowDatas) {
      this.btnEnable = rowDatas.length > 0
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerGroupPage(searchQuery)
    },
    getServerGroupTreeNode() {
      getServerGroupTree().then(respond => {
        if (respond.data && respond.data.length > 0) {
          this.treeNode[0].children = respond.data
          this.treeNodeType = respond.data[0].type
          this.changeTreeSelectNode()
        }
      })
    },
    parentIdSelectChange(data) {
      if (data && data !== this.temp.id) {
        this.temp.parentId = data
      }
    },
    selectServerRowDataApi(option) {
      return getServerPage(option)
    },
    selectedServerRowDataApi(option) {
      return getServerByGroup(Number(this.temp.id))
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // 单击树节点的回调函数
    handleNodeClick(data, node) {
      node.expanded = true
      this.query.parentId = data.dataId
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query.page)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    // viewServer() {
    //   this.groupId = undefined
    //   this.dialogFormServerVisible = true
    // },
    handleCreate() {
      this.resetTemp()
      this.temp.parentId = this.query.parentId || 0
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleBind(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormServerVisible = true
      if (this.serverSelect()) {
        this.serverSelect().reload()
      }
    },
    bindServer() {
      const selectedDatas = this.serverSelect().getSelectedDatas()
      const selectedServers = []
      selectedDatas.forEach(data => {
        selectedServers.push(data.devId)
      })
      allotServer({ groupId: this.temp.id, serverIds: selectedServers.join(',') }).then(() => {
        this.dialogFormServerVisible = false
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    loadBanlanceClick(data, row) {
      const tempData = Object.assign({}, row)
      let msg = ''
      if (data == 1) {
        msg = '已开启'
      } else {
        msg = '已关闭'
      }
      updateServerGroup(tempData).then(respond => {
        this.getServerGroupTreeNode()
        this.$notify({
          title: this.$t('text.success'),
          message: msg,
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {})
    },
    handleImport() {},
    handleExport() {},
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createServerGroup(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            // this.treeMenu.addNode(this.dataToTreeNode(respond.data))
            this.getServerGroupTreeNode()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateServerGroup(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.updateRowData(respond.data)
            // this.treeMenu.updateNode(this.dataToTreeNode(tempData))
            this.getServerGroupTreeNode()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      validateDelServerGroup({ ids: toDeleteIds.join(',') }).then(respond => {
        if (respond.data === 'disable_4_child') {
          this.$notify({
            title: this.$t('text.warning'),
            message: '待删除分组包含下辖分组，不允许删除',
            type: 'warning',
            duration: 2000
          })
        } else if (respond.data === 'disable_4_relate') {
          this.$notify({
            title: this.$t('text.warning'),
            message: '待删除分组包含下辖服务器，不允许删除',
            type: 'warning',
            duration: 2000
          })
        } else {
          this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
            deleteServerGroup({ ids: toDeleteIds.join(',') }).then(respond => {
              this.gridTable.deleteRowData(toDeleteIds)
              this.treeMenu.removeNode(toDeleteIds)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.deleteSuccess'),
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {})
        }
      })
    },
    validateUniqueName(rule, value, callback) {
      getServerGroupByName({
        name: value
      }).then(respond => {
        const group = respond.data
        if (group && group.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    parentFormatter(row, data) {
      const node = this.treeMenu && this.treeMenu.findNode(this.treeNode, data, 'dataId')
      return node ? node.label : ''
    }
  }
}
</script>
