<template>
  <div class="app-container serverInfo">
    <el-tabs ref="tabs" v-model="tabName" type="card">
      <el-tab-pane label="云服务管理" name="cloudAddress">
        <div class="app-container global-config">
          <Form ref="formRef" :model="server" :rules="rules" label-position="right" label-width="120px" style="width: 700px;">
            <!-- 云服务开关 -->
            <el-row :gutter="20" align="middle">
              <el-col :span="6">
                <FormItem label="云服务状态" prop="host">
                  <el-tag :type="cloudEnable ? 'success' : 'info'" size="small">
                    {{ cloudEnable ? '启用' : '停用' }}
                  </el-tag>
                  <!-- <el-switch v-model="cloudEnable" disabled></el-switch> -->
                </FormItem>
              </el-col>
              <el-col :span="6">
                <el-button
                  type="primary"
                  class="connect-btn"
                  size="mini"
                  :loading="loading"
                  @click="handleToggleCloudEnable"
                >
                  {{ switchBtnText }}
                </el-button>
              </el-col>
            </el-row>

            <!-- 地址配置 -->
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.cloudAddress')" prop="host">
                  <el-input v-model="server.host" :maxlength="64" clearable />
                </FormItem>
              </el-col>
              <el-col :span="8">
                <FormItem label-width="60px" :label="$t('pages.port')" prop="port">
                  <el-input v-model.number="server.port" />
                </FormItem>
              </el-col>
            </el-row>

            <!-- 代理配置 -->
            <!-- <el-row v-show="server.proxyEnable"> -->
            <!--  <el-col :span="12">-->
            <!--    <FormItem label="代理服务器">-->
            <!--      <el-input v-model="server.proxyHost" />-->
            <!--    </FormItem>-->
            <!--  </el-col>-->
            <!--  <el-col :span="8">-->
            <!--    <FormItem label-width="60px" :label="$t('pages.port')">-->
            <!--      <el-input v-model.number="server.proxyPort" />-->
            <!--    </FormItem>-->
            <!--  </el-col>-->
            <!--</el-row>-->

            <!--<el-row v-show="server.proxyEnable">-->
            <!--  <el-col :span="12">-->
            <!--    <FormItem label="用户名">-->
            <!--      <el-input v-model="server.proxyUsername" />-->
            <!--    </FormItem>-->
            <!--  </el-col>-->
            <!--  <el-col :span="8">-->
            <!--    <FormItem label-width="60px" label="密码">-->
            <!--      <el-input v-model="server.proxyPassword" type="password" />-->
            <!--    </FormItem>-->
            <!--  </el-col>-->
            <!--</el-row>-->

            <!-- 操作按钮 -->
            <el-row>
              <el-col :span="5">
                <div style="float: right">
                  <el-button
                    class="connect-btn"
                    type="primary"
                    size="mini"
                    :loading="testLoading"
                    @click="handleTest"
                  >
                    测试连接
                  </el-button>
                  <el-button
                    class="connect-btn"
                    type="primary"
                    size="mini"
                    :loading="saveLoading"
                    @click="updateAddress"
                  >
                    保存
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </Form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { index, saveAddress, testAddress, changeCloudEnable } from '@/api/system/deviceManage/cloudServiceApply'

export default {
  name: 'CloudServer',
  data() {
    return {
      tabName: 'cloudAddress',
      submitting: false,
      loading: false,     // 切换按钮 loading
      testLoading: false, // 测试连接 loading
      saveLoading: false, // 保存按钮 loading
      cloudEnable: false,
      server: {
        host: '',
        port: '',
        proxyEnable: false,
        proxyHost: '',
        proxyPort: undefined,
        proxyUsername: '',
        proxyPassword: ''
      },
      rules: {
        host: [
          { required: true, message: this.$t('pages.pleaseEnterCloudPlatformAddress'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    switchBtnText() {
      return this.cloudEnable ? this.$t('text.close') : this.$t('text.open')
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo(type) {
      this.submitting = true
      try {
        index().then(response => {
          this.server = response.data.server || {}
          // this.port = this.server.port || {}
          this.cloudEnable = response.data.cloudEnable || false
          // 强制更新
          this.$forceUpdate();
        })
      } catch (err) {
        this.$notify.error('获取信息失败')
      } finally {
        this.submitting = false
      }
    },

    /**
     * 通用 loading 控制函数
     */
    // async withLoading(fn, loadingKey = 'loading') {
    //   this[loadingKey] = true
    //   try {
    //     return await fn()
    //   } catch (error) {
    //     console.error(error)
    //   } finally {
    //     this[loadingKey] = false
    //   }
    // },

    handleToggleCloudEnable() {
      const value = !this.cloudEnable

      if (!value) {
        this.$confirmBox('关闭后本地云服务功能将无法使用，请谨慎操作。').then(() => {
          this.loading = true
          changeCloudEnable(value).then(resp => {
            this.cloudEnable = value
            this.$notify.success(this.$t('text.updateSuccess'))
          }).finally(() => {
            this.loading = false
          })
        })
      } else {
        this.loading = true
        changeCloudEnable(value).then(resp => {
          this.cloudEnable = value
          this.$notify.success(this.$t('text.updateSuccess'))
        }).finally(() => {
          this.loading = false
        })
      }
    },

    handleTest() {
      this.testLoading = true
      testAddress(this.server).then(() => {
        this.$notify.success(this.$t('text.connectSuccess'))
      }).finally(() => {
        this.testLoading = false
      })
      // this.withLoading(() => testAddress(this.server), 'testLoading')
    },

    updateAddress() {
      this.saveLoading = true
      saveAddress(this.server).then(response => {
        this.$notify.success(this.$t('text.updateSuccess'))
      }).finally(() => {
        this.saveLoading = false
      })
      // this.withLoading(() => saveAddress(this.server), 'saveLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
.connect-btn {
  padding: 5px;
  margin-top: 8px;
  margin-left: 8px;
}

.status-text {
  color: #999;
  font-weight: normal;
  &.active {
    color: #409EFF; /* 开启时高亮色 */
  }
}
/* 表单相关样式 */
</style>
