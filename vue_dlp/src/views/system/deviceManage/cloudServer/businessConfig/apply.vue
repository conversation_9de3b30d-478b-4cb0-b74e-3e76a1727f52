<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="applyTitle"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="105px">
        <el-divider content-position="left">客户信息</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem label="公司名称" prop="customer_name">
              <el-input v-model="temp.customer_name" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
          <el-col :span="12" style="float: right">
            <FormItem label="联系人" prop="contacts">
              <el-input v-model="temp.contacts" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem label="电话" prop="phone">
              <el-input v-model="temp.phone" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
          <el-col :span="12" style="float: right">
            <FormItem label="邮箱" prop="email">
              <el-input v-model="temp.email" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem label="法人" prop="legal_person">
              <el-input v-model="temp.legal_person" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
          <el-col :span="12" style="float: right">
            <FormItem label="所属行业" prop="industry">
              <el-select v-model="temp.industry" placeholder="请选择">
                <el-option
                  v-for="item in industryOptions"
                  :key="item.value"
                  :disabled="applyStatus==='adjustBusiness'"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem label="地址" prop="addressOptions">
              <el-cascader
                v-model="temp.addressOptions"
                size="large"
                :options="options"
                :disabled="applyStatus==='adjustBusiness'"
                style="width: 657px;margin-top: -5px"
                @change="addressChange"
              >
              </el-cascader>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem label="详细地址" prop="address">
              <el-input v-model="temp.address" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left">产品信息</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem label="产品名称" prop="product_name">
              <el-input v-model="temp.product_name" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
          <el-col :span="12" style="float: right">
            <FormItem label="产品编码" prop="product_code">
              <el-input v-model="temp.product_code" v-trim maxlength="20" :disabled="applyStatus==='adjustBusiness'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left">云服务配置</el-divider>
        <el-row>
          <FormItem label="" prop="checkList">
            <el-checkbox-group v-model="temp.checkList">
              <el-checkbox :label="0">预警消息通知</el-checkbox>
              <el-checkbox :label="1">报表消息通知</el-checkbox>
              <el-checkbox :label="2">策略云备份</el-checkbox>
            </el-checkbox-group>
          </FormItem>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="applyStatus==='apply'?applyData():businessData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { regionData, codeToText } from 'element-china-area-data'
export default {
  name: 'EditBackupServer',
  props: {
    applyTitle: {
      type: String,
      default() {
        return ''
      }
    },
    applyStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        customer_name: '',
        contacts: '',
        phone: '',
        email: '',
        legal_person: '',
        industry: '',
        checkList: [1, 2],
        // 省市区选择
        addressOptions: regionData,
        province: '',
        city: '',
        district: '',
        address: '',
        product_name: '',
        product_code: ''
      },
      // 省市区选择
      options: regionData,
      dialogFormVisible: false,
      submitting: false,
      industryOptions: [{
        value: '1',
        label: '计算机'
      }, {
        value: '2',
        label: '采矿'
      }, {
        value: '3',
        label: '能源'
      }, {
        value: '4',
        label: '餐饮'
      }, {
        value: '5',
        label: '房地产'
      }],
      rules: {
        customer_name: [
          { required: true, message: '公司名称不能为空', trigger: 'blur' }
        ],
        contacts: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '电话不能为空', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' }
        ],
        legal_person: [
          { required: true, message: '法人不能为空', trigger: 'blur' }
        ],
        industry: [
          { required: true, message: '请选择所属行业', trigger: 'change' }
        ],
        addressOptions: [
          { required: true, message: '地址不能为空', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '详细地址不能为空', trigger: 'blur' }
        ],
        product_name: [
          { required: true, message: '产品名称不能为空', trigger: 'blur' }
        ],
        product_code: [
          { required: true, message: '产品编码不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {

  },
  watch: {},
  created() {
    this.resetTemp()
  },
  activated() {

  },
  methods: {
    /**
     * 省、市、区选择后
     * 默认生成的编码，如：['11', '1101', '110102']
     * 需要通过codeToText转成文字格式,如：北京市/市辖区/西城区
     * 如果详情返回文字，回显时，需要将文字转成编码进行回显，但是没找到转成code的方法，网上说的试了都没用
     * */
    addressChange(arr) {
      console.log('arr', arr)
      const hh = `${codeToText[arr[0]]}/${codeToText[arr[1]]}/${
        codeToText[arr[2]]
      }`
      console.log('hhh', hh)
      const cc = hh.split('/')
      console.log('截取', cc)
    },
    /**
     * 打开申请弹框
     */
    handleOpenApply() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 打开业务调整弹框
     */
    handleOpenBusiness(row) {
      this.resetTemp()
      this.dialogFormVisible = true
      this.temp = Object.assign({}, this.defaultTemp, row) // copy obj
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 申请“确认按钮点击”
     */
    applyData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        // eslint-disable-next-line no-empty
        if (valid) {

        }
      })
    },
    /**
     * 业务调整“确认按钮点击”
     */
    businessData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        // eslint-disable-next-line no-empty
        if (valid) {

        }
      })
    },
    /**
     * 初始化数据
     */
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
      // this.$refs.select.blur()
    }
  }
}
</script>
