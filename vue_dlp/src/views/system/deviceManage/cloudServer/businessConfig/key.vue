<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="keyTitle"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="105px">
        <el-row>
          <el-col :span="24">
            <FormItem label="APPID" prop="aPPId">
              <el-input v-model="temp.aPPId" v-trim maxlength="20" :disabled="keyStatus==='view'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem label="APPKey" prop="aPPKey">
              <el-input v-model="temp.aPPKey" v-trim maxlength="20" :disabled="keyStatus==='view'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem label="密钥" prop="key">
              <el-input v-model="temp.key" v-trim maxlength="20" :disabled="keyStatus==='view'" />
            </FormItem>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="keyStatus === 'reset'" :loading="submitting" type="primary" @click="resetData()">
          {{ $t('button.reset') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="密码"
      :visible.sync="pwdVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataPwdForm" :rules="rulesPwd" :model="tempPwd" label-position="right" label-width="105px">
        <el-row>
          <el-col :span="24">
            <FormItem label="密码" prop="password">
              <el-input v-model="tempPwd.password" v-trim maxlength="20"/>
            </FormItem>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="pwdData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="pwdVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'Key',
  props: {
    keyTitle: {
      type: String,
      default() {
        return ''
      }
    },
    keyStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        aPPId: '',
        aPPKey: '',
        key: ''
      },
      dialogFormVisible: false,
      pwdVisible: false,
      submitting: false,
      rules: {
        aPPId: [
          { required: true, message: 'APPID不能为空', trigger: 'blur' }
        ],
        aPPKey: [
          { required: true, message: 'APPKey不能为空', trigger: 'blur' }
        ],
        key: [
          { required: true, message: '密钥不能为空', trigger: 'blur' }
        ]
      },
      rulesPwd: {
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ]
      },
      tempPwd: {},
      defaultTempPwd: {
        id: undefined,
        password: ''
      }
    }
  },
  computed: {

  },
  watch: {},
  created() {
    this.resetTemp()
  },
  activated() {

  },
  methods: {
    /**
       * 打开查看密钥弹框
       */
    handleOpenViewKey() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
       * 打开重置密钥弹框
       */
    handleOpenResetKey(row) {
      this.resetTemp()
      this.dialogFormVisible = true
      this.temp = Object.assign({}, this.defaultTemp, row) // copy obj
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 重置按钮点击
     */
    resetData() {
      this.pwdVisible = true
      // this.submitting = true
      // this.$refs['dataForm'].validate((valid) => {
      //   if (valid) {
      //
      //   }
      // })
    },
    /**
     * 密码确认按钮点击
     */
    pwdData() {
      this.dialogFormVisible = false
      this.pwdVisible = false
      // this.submitting = true
      // this.$refs['dataPwdForm'].validate((valid) => {
      //   if (valid) {
      //
      //   }
      // })
    },
    /**
       * 初始化数据
       */
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.tempPwd = Object.assign({}, this.defaultTempPwd)
    },
    handleDrag() {
      // this.$refs.select.blur()
    }
  }
}
</script>
