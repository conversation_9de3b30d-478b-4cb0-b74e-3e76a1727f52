<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button size="mini" @click="handleApply">
          {{ $t('pages.applyFor') }}
        </el-button>
        <el-button size="mini" @click="handleAdjustBusiness">
          {{ $t('pages.adjustingBusiness') }}
        </el-button>
        <el-button size="mini" @click="handleSyncAll">
          {{ $t('pages.synchronizeAllStates') }}
        </el-button>
        <el-button size="mini" @click="handleViewKey">
          {{ $t('pages.viewKey') }}
        </el-button>
        <el-button size="mini" @click="handleResetKey">
          {{ $t('pages.resetKey') }}
        </el-button>
      </div>
      <grid-table
        ref="serverTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
      />
    </div>
    <!--申请、调整业务-->
    <business-apply ref="businessApply" :apply-title="applyTitle" :apply-status="applyStatus"></business-apply>
    <!--查看、重置密钥-->
    <business-key ref="businessKey" :key-title="keyTitle" :key-status="keyStatus"/>
  </div>
</template>

<script>
import BusinessApply from '@/views/system/deviceManage/cloudServer/businessConfig/apply';
import BusinessKey from '@/views/system/deviceManage/cloudServer/businessConfig/key';
import { getServerPage } from '@/api/system/deviceManage/backupServer'

export default {
  name: 'BusinessConfig',
  components: { BusinessApply, BusinessKey },
  data() {
    return {
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { prop: 'name', label: '公司名称', width: '120', fixed: true, sort: true },
        { prop: 'devId', label: '联系人', width: '60' },
        { prop: 'intranetIp', label: '电话', width: '120' },
        { prop: 'intranetIpv6', label: '邮箱', width: '150' },
        { prop: 'intranetPort', label: '产品名称', width: '80' },
        { prop: 'internetIp', label: '产品编码', width: '120' },
        { prop: 'internetIpv6', label: '类型', width: '150', formatter: this.typeFormatter },
        { prop: 'internetPort', label: '状态', width: '80', formatter: this.statusormatter },
        { prop: 'headquartersName', label: '当前生效', width: '120', formatter: this.takeFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: '同步状态', click: this.handleSync }
          ]
        }
      ],
      applyStatus: '',
      applyTitle: '',
      keyStatus: '',
      keyTitle: '',
      deleteable: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    busenessMap() {
      const map = {}
      this.defaultBussDatas.forEach(item => {
        map[item.bussId] = item.label
      })
      return map
    }
  },
  created() {

  },
  activated() {

  },
  methods: {
    /**
     * 接口获取列表数据
     * @param option
     * @returns {AxiosPromise}
     */
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerPage(searchQuery)
    },
    /**
     * 申请
     * */
    handleApply() {
      this.applyStatus = 'apply'
      this.applyTitle = '申请'
      this.$refs.businessApply.handleOpenApply()
    },
    /**
     * 调整业务
     * */
    handleAdjustBusiness(row) {
      this.applyStatus = 'adjustBusiness'
      this.applyTitle = '调整业务'
      this.$refs.businessApply.handleOpenBusiness(row)
    },
    /**
     * 同步全部状态
     * */
    handleSyncAll() {
      this.$confirmBox('确定同步全部状态？', '提示').then(() => {
        // hardwareDelete({ id: deleteId }).then(respond => {
        //   this.$notify({
        //     title: '成功',
        //     message: '同步成功',
        //     type: 'success',
        //     duration: 2000
        //   })
        // })
      }).catch(() => {})
    },
    /**
     * 查看密钥
     * */
    handleViewKey() {
      this.keyStatus = 'view'
      this.keyTitle = '查看密钥'
      this.$refs.businessKey.handleOpenViewKey()
    },
    /**
     * 重置密钥
     * */
    handleResetKey() {
      this.keyStatus = 'reset'
      this.keyTitle = '重置密钥'
      this.$refs.businessKey.handleOpenResetKey()
    },
    /**
     * 同步状态(列表)
     * */
    handleSync(row) {
      console.log('row', JSON.parse(JSON.stringify(row)))
      this.$confirmBox('确定同步当前状态？', '提示').then(() => {
        // hardwareDelete({ id: deleteId }).then(respond => {
        //   this.$notify({
        //     title: '成功',
        //     message: '同步成功',
        //     type: 'success',
        //     duration: 2000
        //   })
        // })
      }).catch(() => {})
    },
    /**
     * 列表类型
     * @param row 当前行数据
     * @returns {*}
     */
    typeFormatter: function(row) {
      // return row.apply_type === 1 ? '申请' : '调整业务'
    },
    /**
     * 列表状态
     * @param row 当前行数据
     * @returns {*}
     */
    statusormatter: function(row) {
      // if (row.audit_result === 0) {
      //   return '待审批'
      // }
      // if (row.audit_result === 1) {
      //   return '审批通过'
      // }
      // if (row.audit_result === 2) {
      //   return '已拒绝' + '拒绝原因'
      // }
      // if (row.audit_result === 3) {
      //   return '流程完成'
      // }
    },
    /**
     * 列表当前生效
     * @param row 当前行数据
     * @returns {*}
     */
    takeFormatter: function(row) {
      // return row.take === 0 ? '是' : '否'
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
