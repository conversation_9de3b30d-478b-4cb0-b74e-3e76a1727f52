<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.importInfo', { info: title })"
      :visible.sync="dialogVisible"
      :width="dialogWidth + 'px'"
    >
      <Form ref="certInfoForm" :model="form" label-position="right" label-width="120px" style="width: 500px;">
        <el-row>
          <el-col :span="24">
            <FormItem label="证书" prop="">
              <el-upload
                ref="certUpload"
                action=""
                :limit="1"
                :accept="accept"
                :auto-upload="false"
                :on-change="handleChange"
                :on-exceed="handleExceed"
                :file-list="fileList"
              >
                <el-button size="small" type="primary">选择证书</el-button>
                <div slot="tip" class="el-upload__tip">只能上传asc文件</div>
              </el-upload>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="18">
            <FormItem label="证书导入码">
              <el-input v-model="certPassword"/>
            </FormItem>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleUpload()">
          {{ $t('button.import') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { importCert } from '@/api/system/deviceManage/cloudServer';
export default {
  name: 'ImportCertDlg',
  props: {
    title: {
      type: String,
      default: function() {
        return this.$t('button.import')
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      accept: '.asc',
      dialogWidth: 600,
      file: '',
      fileList: [],
      certPassword: '',
      form: {

      }
    }
  },
  methods: {
    show() {
      this.resetForm()
      this.dialogVisible = true
    },
    resetForm() {
      this.fileList.splice(0)
      this.certPassword = ''
    },
    handleUpload() {
      const formData = new FormData();
      formData.append('file', this.file.raw)
      formData.append('certPassword', this.certPassword)
      importCert(formData).then(response => {
        this.dialogVisible = false
        this.$emit('success', response.data)
      })
    },
    handleChange(file, fileList) {
      this.file = file;
      this.fileList = fileList;
    },
    handleExceed(files, fileList) {
      this.$refs.certUpload.clearFiles();
      this.$refs.certUpload.handleStart(files[0])
    }
  }
}
</script>
