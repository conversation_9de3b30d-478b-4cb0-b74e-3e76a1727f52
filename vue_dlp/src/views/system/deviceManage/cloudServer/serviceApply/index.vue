<template>
  <div class="app-container">
    <div class="table-container">
      <!--      <div class="toolbar">-->
      <!--        <el-button size="mini" @click="handleApply">-->
      <!--          {{ $t('pages.applyFor') }}-->
      <!--        </el-button>-->
      <!--      </div>-->
      <div class="toolbar">
        <div class="searchCon">
          <el-input v-model="query.applyNo" v-trim clearable placeholder="申请编号" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="applyTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
      />
    </div>
    <!--申请、调整业务-->
    <business-apply ref="businessApply"></business-apply>
    <!--查看、重置密钥-->
    <!--    <business-key ref="businessKey" :key-title="keyTitle" :key-status="keyStatus"/>-->
  </div>
</template>

<script>
import BusinessApply from '@/views/system/deviceManage/cloudServer/serviceApply/apply';
import { deleteApply, get, pageCloudServiceApply, submit, sync } from '@/api/system/deviceManage/cloudServiceApply'

export default {
  name: 'ServiceApply',
  components: { BusinessApply },
  data() {
    return {
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { prop: 'applyNo', label: '申请编号', width: '120', fixed: true, sort: true },
        { prop: 'companyName', label: '公司名称', width: '120', fixed: true, sort: true },
        { prop: 'corporation', label: '法人', width: '120' },
        { prop: 'industryName', label: '所属行业', width: '150' },
        // { prop: 'businessLicense', label: '营业执照', width: '120' },
        { prop: 'address', label: '地址', width: '120' },
        { prop: 'contact', label: '联系人', width: '60' },
        { prop: 'phone', label: '手机号', width: '120' },
        { prop: 'status', label: '状态', width: '120', formatter: this.statusFormatter },
        { prop: 'createTime', label: '申请时间', width: '140' },
        { prop: 'reason', label: '审核结果说明', width: '140' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '230',
          buttons: [
            { label: '提交', click: this.handleSubmit, btnType: 'submit', isShow: this.btnShow },
            { label: '同步状态', click: this.handleSync, btnType: 'sync', isShow: this.btnShow },
            { label: '编辑', click: this.handleUpdate, btnType: 'edit', isShow: this.btnShow },
            { label: '删除', click: this.handleDelete, btnType: 'delete', isShow: this.btnShow },
            { label: '查看详情', click: this.handleDetail, btnType: 'detail', isShow: this.btnShow }
          ]
        }
      ],
      query: {
        applyNo: '',
        page: 1,
        limit: 20
      }
    }
  },
  methods: {
    /**
     * 接口获取列表数据
     * @param option
     * @returns {AxiosPromise}
     */
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return pageCloudServiceApply(searchQuery)
    },
    refresh() {
      this.query.page = 1
      this.$refs['applyTable'].execRowDataApi(this.query)
    },
    // handleApply() {
    //   this.$refs.businessApply.handleApply()
    // },

    handleFilter() {
      this.query.page = 1
      this.$refs['applyTable'].execRowDataApi(this.query)
    },
    handleSubmit(row) {
      this.$confirmBox('确定将申请单提交到云平台吗?, 提交后此申请单不允许编辑和重新提交。', '提示').then(() => {
        submit(row.id).then(response => {
          this.$notify({
            title: '成功',
            message: '提交成功',
            type: 'success',
            duration: 2000
          })
          this.refresh()
        })
      })
    },
    handleSync(row) {
      this.$confirmBox('确定同步云平台申请单状态？', '提示').then(() => {
        sync(row.id).then(response => {
          this.$notify({
            title: '成功',
            message: '同步成功',
            type: 'success',
            duration: 2000
          })
          this.refresh()
        })
      }).catch(() => {})
    },
    handleDelete(row) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteApply(row.id).then(response => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.refresh()
        })
      })
    },
    handleDetail(row) {
      get(row.id).then(response => {
        this.$refs.businessApply.handleDetail(response.data)
      })
    },
    handleUpdate(row) {
      get(row.id).then(response => {
        this.$refs.businessApply.handleUpdate(response.data)
      })
    },
    statusFormatter: function(row) {
      if (row.status === 1) {
        return '待提交'
      }
      if (row.status === 2) {
        return '待审批'
      }
      if (row.status === 3) {
        return '审批通过'
      }
      if (row.status === 4) {
        return '审批不通过'
      }
    },
    btnShow(row, element) {
      // 状态： 1-待提交， 2-待审核，3-审核通过，4-审核不通过
      if (element.btnType === 'submit') {
        // 提交按钮
        return row.status === 1 || row.status === 4
      }
      if (element.btnType === 'sync') {
        // 同步按钮
        return row.status === 2
        // return row.status !== 1
      }
      if (element.btnType === 'edit') {
        // 修改按钮
        return row.status === 1 || row.status === 4
      }
      if (element.btnType === 'delete') {
        // 修改按钮
        return row.status === 1
      }
      return true
    }
  }
}
</script>
