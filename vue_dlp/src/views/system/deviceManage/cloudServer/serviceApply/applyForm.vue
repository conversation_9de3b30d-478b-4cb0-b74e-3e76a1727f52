<template>
  <div>
    <Form ref="dataForm" :rules="rules" :model="form" label-position="right" label-width="80px" style="width: 90%">
      <el-row>
        <el-col :span="8">
          <FormItem label="公司名称" prop="companyName">
            <el-input v-model="form.companyName" v-trim maxlength="64" :disabled="type==='detail'" />
          </FormItem>
        </el-col>
        <el-col :span="8">
          <FormItem label="法人" prop="corporation">
            <el-input v-model="form.corporation" v-trim maxlength="32" :disabled="type==='detail'" />
          </FormItem>
        </el-col>
        <el-col :span="8">
          <FormItem label="所属行业" prop="industry">
            <el-cascader
              v-model="form.industry"
              size="large"
              :options="industryOptions"
              :disabled="type==='detail'"
              :props="{label: 'name', value: 'name'}"
              style="width: 100%;margin-top: -5px"
              @change="industryChange"
            >
            </el-cascader>
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <FormItem label="联系人" prop="contact">
            <el-input v-model="form.contact" v-trim maxlength="32" :disabled="type==='detail'" />
          </FormItem>
        </el-col>
        <el-col :span="8">
          <FormItem label="手机号" prop="phone">
            <el-input v-model="form.phone" v-trim maxlength="32" :disabled="type==='detail'" />
          </FormItem>
        </el-col>
        <el-col :span="8">
          <FormItem label="邮箱" prop="mail">
            <el-input v-model="form.mail" v-trim maxlength="64" :disabled="type==='detail'" />
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <FormItem label="传真" prop="fax">
            <el-input v-model="form.fax"/>
          </FormItem>
        </el-col>
        <el-col :span="8">
          <FormItem label="QQ" prop="fax">
            <el-input v-model="form.qq"/>
          </FormItem>
        </el-col>
        <el-col :span="8">
          <FormItem label="微信" prop="fax">
            <el-input v-model="form.wx"/>
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <FormItem label="地址" prop="regionCode">
            <el-cascader
              v-model="form.regionCode"
              size="large"
              :options="regionOptions"
              :disabled="type==='detail'"
              style="width: 100%;margin-top: -5px"
              @change="addressChange"
            >
            </el-cascader>
          </FormItem>
        </el-col>
        <el-col :span="8">
          <FormItem label="详细地址" prop="address">
            <el-input v-model="form.address" v-trim maxlength="128" :disabled="type==='detail'" />
          </FormItem>
        </el-col>

        <el-col :span="8">
          <FormItem label="邮编" prop="fax">
            <el-input v-model="form.postcode"/>
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <FormItem label="申请说明" prop="address">
            <el-input v-model="form.remark" v-trim maxlength="128" :disabled="type==='detail'" />
          </FormItem>
        </el-col>
      </el-row>
      <el-row style="text-align: right">
        <el-button class="connect-btn" type="primary" size="mini">保持并提交</el-button>
        <el-button class="connect-btn" type="primary" size="mini">{{ $t('button.save') }}</el-button>
      </el-row>
    </Form>
  </div>
</template>

<script>
import { regionData, codeToText } from 'element-china-area-data'
import industryOptions from '@/utils/industry'

export default {
  name: 'ApplyForm',
  props: {

  },
  data() {
    return {
      type: '',
      form: {},
      defaultForm: {
        id: undefined,
        applyNo: '',
        companyName: '',
        contact: '',
        phone: '',
        mail: '',
        corporation: '',
        industry: '',
        industryName: '',
        fax: '',
        regionCode: '',
        regionName: '',
        address: '',
        postcode: '',
        qq: '',
        wx: '',
        productName: '',
        productCode: '',
        serviceCodeList: [],
        remark: ''
      },
      regionOptions: regionData,
      industryOptions,
      rules: {}
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.form = Object.assign({}, this.defaultForm)
    },
    addressChange(arr) {
      const hh = `${codeToText[arr[0]]}/${codeToText[arr[1]]}/${codeToText[arr[2]]}`
      const cc = hh.split('/')
      this.temp.regionCode = arr[2]
      this.temp.regionName = cc[0] + cc[1] + cc[2]
    },
    industryChange(arr) {
      // 这里是二级行业
      this.temp.industryName = arr[0] + ' / ' + arr[1]
      this.temp.industry = arr[1]
    }
  }
}
</script>
