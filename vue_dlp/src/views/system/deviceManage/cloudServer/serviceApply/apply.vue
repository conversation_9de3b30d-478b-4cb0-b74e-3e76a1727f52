<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="105px">
        <el-divider content-position="left">客户信息</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem label="公司名称" prop="companyName">
              <el-input v-model="temp.companyName" v-trim maxlength="64" :disabled="type==='detail'" />
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem label="法人" prop="corporation">
              <el-input v-model="temp.corporation" v-trim maxlength="32" :disabled="type==='detail'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem label="联系人" prop="contact">
              <el-input v-model="temp.contact" v-trim maxlength="32" :disabled="type==='detail'" />
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem label="手机号" prop="phone">
              <el-input v-model="temp.phone" v-trim maxlength="32" :disabled="type==='detail'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" style="float: right">
            <FormItem label="邮箱" prop="mail">
              <el-input v-model="temp.mail" v-trim maxlength="64" :disabled="type==='detail'" />
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem label="所属行业" prop="industry">
              <el-cascader
                v-model="temp.industry"
                size="large"
                :options="industryOptions"
                :disabled="type==='detail'"
                :props="{label: 'name', value: 'name'}"
                style="width:100%;margin-top: -5px"
                @change="industryChange"
              >
              </el-cascader>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem label="QQ" prop="qq">
              <el-input v-model="temp.qq" v-trim :disabled="type==='detail'"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem label="微信" prop="wx">
              <el-input v-model="temp.wx" v-trim :disabled="type==='detail'"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem label="传真" prop="fax">
              <el-input v-model="temp.fax" v-trim :disabled="type==='detail'"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem label="邮编" prop="postCode" >
              <el-input v-model="temp.postCode" v-trim :disabled="type==='detail'"/>
            </FormItem>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem label="地址" prop="regionCode">
              <el-cascader
                v-model="temp.regionCode"
                size="large"
                :options="options"
                :disabled="type==='detail'"
                style="width: 100%;margin-top: -5px"
                @change="addressChange"
              >
              </el-cascader>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem label="详细地址" prop="address">
              <el-input v-model="temp.address" v-trim maxlength="128" :disabled="type==='detail'" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem label="申请说明" prop="address">
              <el-input v-model="temp.remark" v-trim type="textarea" maxlength="128" :disabled="type==='detail'" />
            </FormItem>
          </el-col>
        </el-row>
        <!--        <el-divider content-position="left">产品信息</el-divider>-->
        <!--        <el-row>-->
        <!--          <el-col :span="12">-->
        <!--            <FormItem label="产品名称" prop="productName">-->
        <!--              <el-input v-model="temp.productName" v-trim maxlength="20" :disabled="type==='detail'" />-->
        <!--            </FormItem>-->
        <!--          </el-col>-->
        <!--          <el-col :span="12" style="float: right">-->
        <!--            <FormItem label="产品编码" prop="productCode">-->
        <!--              <el-input v-model="temp.productCode" v-trim maxlength="20" :disabled="type==='detail'" />-->
        <!--            </FormItem>-->
        <!--          </el-col>-->
        <!--        </el-row>-->
        <el-divider content-position="left">申请云服务</el-divider>
        <el-row>
          <el-col :span="24">
            <FormItem label="" prop="serviceCodes">
              <el-checkbox-group v-model="temp.serviceCodeList" :disabled="type==='detail'" >
                <el-checkbox v-for="(item, index) in serviceCodeList" :key="index" :label="item.code" >{{ item.name }}</el-checkbox>
              </el-checkbox-group>
            </FormItem>
          </el-col>
        </el-row>
        <el-divider v-if="type !== 'apply'" content-position="left">审批结果</el-divider>
        <el-row>
          <el-col :span="24">
            <FormItem v-if="type !== 'apply'" label="状态">
              {{ statusFormatter(temp.status) }}
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem v-if="type !== 'apply'" label="审批结果说明">
              {{ temp.reason }}
            </FormItem>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="type!== 'detail'" :loading="submitting" type="primary" @click="type==='apply'?save():update()">
          {{ $t('button.save') }}
        </el-button>
        <!--        <el-button v-if="type=== 'apply'" :loading="submitting" type="primary" @click="saveAndSubmit()">-->
        <!--          {{ $t('button.saveAndSubmit') }}-->
        <!--        </el-button>-->
        <el-button v-if="type=== 'detail'" :loading="submitting" type="primary" @click="dialogFormVisible = false">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button v-if="type!== 'detail'" @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { regionData, codeToText } from 'element-china-area-data'
import industryOptions from '@/utils/industry'

import { listAllServiceCodes, listActivatedServiceCode, add, update } from '@/api/system/deviceManage/cloudServiceApply'
export default {
  name: 'Apply',
  props: {
  },
  data() {
    return {
      title: '',
      type: '', // apply, edit, detail
      temp: {
        serviceCodeList: []
      }, // 表单字段
      defaultTemp: {
        id: undefined,
        applyNo: '',
        companyName: '',
        contact: '',
        phone: '',
        mail: '',
        fax: '',
        corporation: '',
        postCode: '',
        qq: '',
        wx: '',
        industry: '',
        industryName: '',
        regionCode: '',
        regionName: '',
        address: '',
        productName: '',
        productCode: '',
        serviceCodeList: [],
        remark: ''
      },
      // 省市区选择
      options: regionData,
      dialogFormVisible: false,
      submitting: false,
      serviceCodeList: [],
      activatedServiceCodeList: [],
      industryOptions: industryOptions,
      rules: {
      }
    }
  },
  created() {
    this.resetTemp()
    this.getAllServiceCodeList()
    this.getActivatedServiceCodeList()
  },
  activated() {

  },
  methods: {
    getAllServiceCodeList() {
      listAllServiceCodes().then(response => {
        this.serviceCodeList = response.data
      })
    },
    getActivatedServiceCodeList() {
      listActivatedServiceCode().then(response => {
        this.activatedServiceCodeList = response.data
      })
    },
    /**
     * 省、市、区选择后
     * 默认生成的编码，如：['11', '1101', '110102']
     * 需要通过codeToText转成文字格式,如：北京市/市辖区/西城区
     * 如果详情返回文字，回显时，需要将文字转成编码进行回显，但是没找到转成code的方法，网上说的试了都没用
     * */
    addressChange(arr) {
      const hh = `${codeToText[arr[0]]}/${codeToText[arr[1]]}/${codeToText[arr[2]]}`
      const cc = hh.split('/')
      this.temp.regionCode = arr[2]
      this.temp.regionName = cc[0] + cc[1] + cc[2]
    },
    industryChange(arr) {
      // 这里是二级行业
      this.temp.industryName = arr[0] + ' / ' + arr[1]
      this.temp.industry = arr[1]
    },
    /**
     * 打开申请弹框
     */
    handleApply() {
      this.title = '云服务申请'
      this.type = 'apply'
      this.resetTemp({ serviceCodeList: this.activatedServiceCodeList })
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(args) {
      this.title = '申请单修改'
      this.type = 'edit'
      this.resetTemp()
      Object.assign(this.temp, args)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDetail(args) {
      this.title = '申请单详情'
      this.type = 'detail'
      Object.assign(this.temp, args)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 打开业务调整弹框
     */
    handleOpenBusiness(row) {
      this.resetTemp()
      this.dialogFormVisible = true
      this.temp = Object.assign({}, this.defaultTemp, row) // copy obj
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    save() {
      add(this.temp).then(response => {
        this.$notify({
          title: '成功',
          message: '添加成功',
          type: 'success',
          duration: 2000
        })
        this.dialogFormVisible = false
        this.$parent.refresh()
      })
    },
    update() {
      update(this.temp).then(response => {
        this.$notify({
          title: '成功',
          message: '修改成功',
          type: 'success',
          duration: 2000
        })
        this.dialogFormVisible = false
        this.$parent.refresh()
      })
    },
    /**
     * 业务调整“确认按钮点击”
     */
    businessData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        // eslint-disable-next-line no-empty
        if (valid) {

        }
      })
    },
    /**
     * 初始化数据
     */
    resetTemp(options) {
      this.temp = Object.assign({}, this.defaultTemp, options)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    statusFormatter(status) {
      if (status === 1) {
        return '待提交'
      }
      if (status === 2) {
        return '待审批'
      }
      if (status === 3) {
        return '审批通过'
      }
      if (status === 4) {
        return '审批不通过'
      }
      return '未知状态';
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-form-item__content{
    color: #666666;
  }
</style>
