<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createDevice') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delDevice') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.serverName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('pages.emailAccount')">
                <el-input v-model="query.account" v-trim clearable maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('pages.outgoingServer')" prop="internetIp">
                <el-input v-model="query.internetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.port')" prop="internetPort">
                <el-input v-model.number="query.internetPort" v-trim maxlength="5" clearable :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('internetPort')"/>
              </FormItem>
              <FormItem :label="$t('table.enableSSl')" prop="ssl">
                <el-select v-model="query.ssl" clearable style="width: 100%">
                  <el-option :label="$t('text.yes')" :value="1"></el-option>
                  <el-option :label="$t('text.no')" :value="0"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.sendingName')" prop="senderName">
                <el-input v-model="query.senderName" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('pages.defaultCC')" prop="cc">
                <el-input v-model="query.cc" v-trim clearable maxlength="100"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="serverTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        :selectable="selectable"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 540px;">
        <FormItem :label="$t('pages.serverName')" prop="name">
          <el-input v-model="temp.name" v-trim maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" rows="1" resize="none" maxlength="100" show-word-limit />
        </FormItem>
        <el-tabs ref="tabs" v-model="tabName">
          <el-tab-pane :label="$t('pages.serverSetting')" name="serverTab" style="padding: 10px;height:180px;overflow: auto;">
            <FormItem :label="$t('pages.emailAccount')" prop="account">
              <el-autocomplete
                v-model="temp.account"
                class="inline-input"
                :fetch-suggestions="accountSearch"
                :placeholder="$t('pages.pleaseEnterEmailAddress')"
                suffix-icon="el-icon-message"
                @select="accountSelect"
              ></el-autocomplete>
            </FormItem>
            <FormItem :label="$t('pages.emailPassword')" encrypt prop="password">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.mailServerContent_1') }}<br/>{{ $t('pages.mailServerContent_2') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <encrypt-input v-model="temp.password" maxlength="50" non-blank show-password/>
            </FormItem>
            <FormItem :label="$t('pages.outgoingServer')" prop="intranetIp">
              <el-input v-model="temp.internetIp" maxlength="64" />
            </FormItem>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.port')" prop="internetPort">
                  <el-input v-model="temp.internetPort" maxlength="5" />
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem prop="ssl">
                  <el-checkbox v-model="temp.ssl" :true-label="1" :false-label="0" label="SSL" @change="sslChange"></el-checkbox>
                </FormItem>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.fileOutgoingConfig')" name="businessTab" style="padding: 10px; height:180px; overflow: auto;">
            <FormItem :label="$t('pages.sendingName')" prop="senderName">
              <el-input v-model="temp.senderName" maxlength="100"/>
            </FormItem>
            <FormItem :label="$t('pages.defaultCC')" prop="cc">
              <el-autocomplete
                v-model="temp.cc"
                class="inline-input"
                maxlength="200"
                :fetch-suggestions="mailDictSearch"
                :placeholder="$t('pages.pleaseEnterEmailAddress')"
                suffix-icon="el-icon-message"
                @select="selectCC"
              ></el-autocomplete>
            </FormItem>
            <!--<FormItem label="默认密送" prop="bcc">
              <el-autocomplete
                v-model="temp.bcc"
                class="inline-input"
                :fetch-suggestions="mailDictSearch"
                :placeholder="$t('pages.pleaseEnterEmailAddress')"
                suffix-icon="el-icon-message"
                @select="selectBCC"
              ></el-autocomplete>
            </FormItem>-->
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="check()">
          {{ $t('button.test') }}
        </el-button>
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getServerPage, createServer, updateServer, deleteServer, getServerByName, checkConnect } from '@/api/system/deviceManage/mailServer'
import { getMailServerDict } from '@/utils/dictionary'
import EncryptInput from '@/components/EncryptInput'
import { isIPv4 } from '@/utils/validate'

export default {
  name: 'MailServer',
  components: { EncryptInput },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { prop: 'name', label: 'serverName', width: '120', fixed: true, sort: true },
        { prop: 'account', label: 'emailAccount', width: '150', sort: true },
        { prop: 'internetIp', label: 'outgoingServer', width: '150', sort: true },
        { prop: 'internetPort', label: 'port', width: '80', sort: true },
        { prop: 'ssl', label: 'enableSSl', width: '80', sort: true, formatter: this.sslFormatter },
        { prop: 'senderName', label: 'sendingName', width: '80', sort: true },
        { prop: 'cc', label: 'defaultCC', width: '80', sort: true },
        // { prop: 'bcc', label: '默认密送', width: '80' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '80',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.validateUniqueName, trigger: 'blur' }
        ],
        account: [
          { required: true, type: 'email', validator: this.validateAccount, trigger: 'blur' }
        ],
        password: [{ required: true, message: this.$t('pages.pleaseEnterEmailPassword'), trigger: 'blur' }],
        internetIp: [{ required: true, validator: this.validateHost, trigger: 'blur' }],
        internetPort: [{ required: true, validator: this.validatePort, trigger: 'blur' }],
        cc: [{ type: 'email', validator: this.validateCC, trigger: 'blur' }],
        bcc: [{ type: 'email', message: this.$t('pages.pleaseEnterEmailAddress'), trigger: 'blur' }]
      },
      query: { // 查询条件
        page: 1,
        name: '',
        account: '',
        internetIp: '',
        internetPort: undefined,
        ssl: undefined,
        senderName: '',
        cc: ''
      },
      tabName: 'serverTab',
      deleteable: false,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        account: '',
        password: undefined,
        senderName: undefined,
        internetIp: undefined,
        internetPort: 25,
        remark: '',
        ssl: 0,
        cc: undefined,
        bcc: undefined
      },
      selectedServerDict: undefined,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.updateMailServer'),
        create: this.$t('pages.addMailServer')
      },
      submitting: false,
      validateArr: [] // tab页面有字段验证不通过，就将tab名放到数组中，以实现tab跳转
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {

  },
  methods: {
    selectable(row, index) {
      return row.devId !== 3001
    },
    mailDictSearch(queryString, cb) {
      const result = []
      if (queryString) {
        const index = queryString.indexOf('@')
        const suffix = index < 0 ? '' : queryString.substring(index)
        const address = index < 0 ? queryString : queryString.substring(0, index)
        getMailServerDict().forEach(dict => {
          if (dict.suffix.startsWith(suffix)) {
            result.push(Object.assign({ value: address + dict.suffix }, dict))
          }
        })
      }
      if (cb) {
        cb(result)
      } else {
        return result
      }
    },
    selectCC(item) {
      this.$refs['dataForm'].validateField('cc')
    },
    selectBCC(item) {
      this.$refs['dataForm'].validateField('bcc')
    },
    accountSearch(queryString, cb) {
      this.selectedServerDict = null
      if (queryString) {
        this.mailDictSearch(queryString, cb)
      } else {
        cb([])
      }
    },
    accountSelect(item) {
      this.selectedServerDict = item
      this.temp.internetIp = item.sendHost
      this.temp.internetPort = this.temp.ssl ? item.sendSslPort : item.sendPort
      this.$refs['dataForm'].validateField('account')
    },
    sslChange(val) {
      if (this.selectedServerDict) {
        this.temp.internetPort = this.temp.ssl ? this.selectedServerDict.sendSslPort : this.selectedServerDict.sendPort
      } else {
        this.temp.internetPort = this.temp.ssl ? 465 : 25
      }
      this.$refs['dataForm'].clearValidate()
    },
    handleCreate() {
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.defaultTemp, row) // copy obj
      this.tabName = 'serverTab'
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      const dict = this.mailDictSearch(this.temp.account)
      if (dict) {
        this.selectedServerDict = dict[0]
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerPage(searchQuery)
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    resetTemp() {
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
      this.selectedServerDict = null
      this.tabName = 'serverTab'
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    check() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          checkConnect(tempData).then(respond => {
            this.submitting = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.connectSuccess'), type: 'success', duration: 2000 })
          }).catch(r => { this.submitting = false })
        } else {
          this.submitting = false
        }
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          // 拷贝2份， 为了请求头可以带上加密的属性名
          const tempData2 = Object.assign({}, tempData)
          checkConnect(tempData).then(respond => {
            createServer(tempData2).then(respond => {
              this.temp.id = respond.data.id
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi(this.query)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(r => { this.submitting = false })
          }).catch(r => { this.submitting = false })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          // 拷贝2份， 为了请求头可以带上加密的属性名
          const tempData2 = Object.assign({}, tempData)
          checkConnect(tempData).then(respond => {
            updateServer(tempData2).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi(this.query)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(r => { this.submitting = false })
          }).catch(r => { this.submitting = false })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    validateUniqueName(rule, value, callback) {
      getServerByName({
        name: value
      }).then(respond => {
        const server = respond.data
        if (server && server.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    validateAccount(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/
      if (!value || value === '') {
        callback(new Error(this.$t('pages.pleaseEnterEmailAddress')))
      } else if (!(reg.test(value))) {
        callback(new Error(this.$t('pages.validaEmail')))
      } else {
        callback()
      }
    },
    validateCC(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/
      if (!value || value.length === 0) {
        callback()
      } else if (!(reg.test(value))) {
        callback(new Error(this.$t('pages.validaEmail')))
      } else {
        callback()
      }
    },
    validateHost(rule, value, callback) {
      let msg = ''
      if (value) {
        if (isIPv4(value)) {
          msg = undefined
        } else {
          msg = this.$t('pages.serverLibrary_text4')
        }
      } else {
        msg = this.$t('pages.validateIP')
      }
      if (msg) callback(msg)
      else callback()
    },
    validatePort(rule, value, callback) {
      let msg = null
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (!value) {
        msg = this.$t('pages.validateMsg_enterPort')
      } else if (!number.test(value)) {
        msg = this.$t('pages.validateNaturalTree')
      } else if (value < 0 || value > 65535) {
        msg = this.$t('pages.validatePortRange')
      }
      if (msg) callback(msg)
      else callback()
    },
    validateRequired(rule, value, callback) {
      let msg = ''
      if (!value) {
        msg = {
          userName: this.$t('pages.validateUserName'),
          diskSpaceLimit: this.$t('pages.validateNetTaskWrite'),
          diskShortageAlert: this.$t('pages.validateNetTaskSpace'),
          maxOccurs: this.$t('pages.validateUserLogin'),
          intranetIp: this.$t('pages.validateIP')
        }[rule.field]
      } else {
        msg = undefined
      }
      callback(msg)
    },
    sslFormatter(row, data) {
      return data ? this.$t('text.yes') : ''
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.account = ''
      this.query.internetIp = ''
      this.query.internetPort = undefined
      this.query.ssl = undefined
      this.query.senderName = ''
      this.query.cc = ''
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .inline-input {
    width: 100%;
  }
</style>
