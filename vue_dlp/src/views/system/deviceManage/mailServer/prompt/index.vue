<template>
  <p v-if="!hasMailServer" style="color: #004ca4; font-size: 14px; margin: 4px 0" :style="commonStyle">
    <span>{{ $t('text.prompt') }}: </span>
    <i18n v-permission="'A49'" path="pages.multiLoginAuth_needConfigEmailServer">
      <span slot="mallServer" style="color: #0c60a5; font-size: 17px; text-decoration: underline; cursor: pointer" :style="linkStyle">
        <router-link to="/system/configManage/MailServer" tag="span">
          {{ $t('route.MailServer') }}
        </router-link>
      </span>
    </i18n>
    <span v-permission="'!A49'">
      {{ $t('pages.validateMessage1') }}
    </span>
  </p>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'EmptyMailServerPrompt',
  props: {
    commonStyle: {
      type: Object,
      default() {
        return {}
      }
    },
    linkStyle: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  computed: {
    ...mapGetters(['hasMailServer'])
  }
}
</script>

<style scoped>

</style>
