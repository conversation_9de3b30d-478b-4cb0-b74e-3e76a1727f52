<template>
  <div class="app-container">
    <div class="server-form">
      <label>{{ $t('pages.intranetAddress') }}:</label>
      <el-input v-model="serverList.intranetIp" v-trim style="width:180px" :maxlength="64" clearable/>
      <label style="margin-left:10px">{{ $t('pages.port') }}:</label>
      <el-input v-model.number="serverList.intranetPort" v-trim style="width:100px" clearable/>

      <el-button type="primary" size="mini" :loading="loading1" :disabled="!serverList.intranetIp&&!serverList.intranetPort" @click="testAddress(1)">{{ $t('button.test') }}</el-button>
    </div>
    <div class="server-form">
      <label>{{ $t('pages.internetAddress') }}:</label>
      <el-input v-model="serverList.internetIp" v-trim style="width:180px" :maxlength="64" clearable/>
      <label style="margin-left:10px">{{ $t('pages.port') }}:</label>
      <el-input v-model.number="serverList.internetPort" v-trim style="width:100px" clearable/>

      <el-button type="primary" size="mini" :loading="loading2" :disabled="!serverList.internetIp&&!serverList.internetPort" @click="testAddress(2)">{{ $t('button.test') }}</el-button>

      <div style="float:left;margin-top:20px;">
        <el-tooltip class="item" effect="light" :content="$t('pages.needsSavedBeforeActivation')" placement="bottom-end">
          <i style="color: #68a8d0" class="el-icon-question"/>
        </el-tooltip>
        <el-button type="primary" class="activeButton" size="mini" :title="!isEqual(serverList,oldServerList)?$t('pages.serverIpHasChanged'):''" :disabled="!isEqual(serverList,oldServerList) || serverListIsNull(serverList)" @click="handleSetActive">{{ hasActive?this.$t('pages.reactivate'):this.$t('pages.activation') }}</el-button>
        <el-button type="primary" size="mini" :loading="loading" @click="updateAddress">{{ $t('button.save') }}</el-button>
      </div>
    </div>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.activation')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="visible"
      width="400px"
    >
      <Form ref="vcForm" :model="vForm" label-position="right" :rules="rules" label-width="80px">
        <FormItem :label="$t('pages.activationCode')" prop="activeCode">
          <el-input v-model="vForm.activeCode" v-trim></el-input>
        </FormItem>
      </Form>
      <span slot="footer" class="dialog-footer" >
        <el-button
          type="primary"
          style="float:left;"
          :loading="loading"
          @click="activeServer"
        >{{ hasActive?this.$t('pages.reactivate'):this.$t('pages.activation') }}</el-button>
        <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
import {
  activeServer,
  getActiveCode,
  getAddress,
  testConnect,
  updateAddress
} from '@/api/system/configManage/reportConfig'

export default {
  name: 'ReportServer',
  data() {
    return {
      visible: false,
      serverList: {},
      oldServerList: {},
      loading: false,
      loading1: false,
      loading2: false,
      submitting: false,
      query: { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: ''
      },
      vForm: {
        activeCode: ''
      },
      hasActive: false,
      rules: {
        activeCode: [{ required: true, message: this.$t('pages.pleaseEnterTheActivationCode'), trigger: 'blur' }]
      }
    }
  },
  watch: {
    serverList: {
      handler(newValue, oldValue) {
        console.log(this.isEqual(this.serverList, this.oldServerList))
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    getAddress().then(res => {
      res.data.internetIp = res.data.internetIp || ''
      res.data.intranetIp = res.data.intranetIp || ''
      res.data.internetPort = res.data.internetPort || ''
      res.data.intranetPort = res.data.intranetPort || ''
      this.serverList = !res.data ? {} : JSON.parse(JSON.stringify(res.data))
      if (this.serverList.internetPort == undefined) {
        // 必须要设置个属性给他，不然如果界面新增了这个属性，而就对象没有这个属性，会导致判断是否修改时一直是有修改
        this.$set(this.serverList, 'internetPort', '')
      }

      this.oldServerList = !res.data ? {} : JSON.parse(JSON.stringify(res.data))
      if (this.oldServerList.internetPort == undefined) {
        // 必须要设置个属性给他，不然如果界面新增了这个属性，而就对象没有这个属性，会导致判断是否修改时一直是有修改
        this.$set(this.oldServerList, 'internetPort', '')
      }
    })
    getActiveCode().then(res => {
      if (res.data.activeCode) {
        this.hasActive = true
        this.vForm.activeCode = res.data.activeCode
      }
    })
  },
  methods: {
    handleSetActive() {
      this.visible = true
      this.$refs['vcForm'] && this.$refs['vcForm'].clearValidate()
      getActiveCode().then(res => {
        if (res.data.activeCode) {
          this.hasActive = true
          this.vForm.activeCode = res.data.activeCode
        }
      })
    },
    testAddress(type) {
      if (type === 1) {
        this.loading1 = true
      } else {
        this.loading2 = true
      }
      const ip = type === 1 ? this.serverList.intranetIp : this.serverList.internetIp
      const port = type === 1 ? this.serverList.intranetPort : this.serverList.internetPort
      testConnect({ ip, port }).then(res => {
        this.$message({
          message: res.data,
          type: 'success'
        })
        if (type === 1) {
          this.loading1 = false
        } else {
          this.loading2 = false
        }
      }).catch(err => {
        console.log(err)
        if (type === 1) {
          this.loading1 = false
        } else {
          this.loading2 = false
        }
      })
    },
    updateAddress() {
      this.loading = true
      updateAddress(this.serverList).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.$store.dispatch('commonData/setReportTypeChange')
        this.oldServerList = JSON.parse(JSON.stringify(this.serverList))
        this.loading = false
      }).catch(err => {
        console.log(err)
        this.loading = false
      })
    },
    activeServer() {
      this.loading = true
      this.$refs['vcForm'].validate((valid) => {
        if (valid) {
          activeServer(this.vForm).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.activationSuccess'),
              type: 'success',
              duration: 2000
            })
            this.hasActive = true
            this.loading = false
            this.visible = false
          }).catch(err => {
            console.log(err)
            this.loading = false
          })
        } else {
          this.loading = false
        }
      })
    },
    serverListIsNull(serverList) {
      return serverList.internetIp == '' && serverList.internetPort == '' && serverList.intranetIp == '' && serverList.intranetPort == ''
    }
  }
}
</script>

<style lang="scss" scoped>
.server-form{
    width: 700px;
    font-size: 14px;
    margin:20px 0 0 20px;
    .el-button{
        min-width: 80px;
        margin-left: 20px;
    }
}
.activeButton {
  margin-left: 5px !important;
}
</style>
