<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.dbIPExceptionTip')"
    :visible.sync="visible"
    width="500px"
    @close="close"
  >
    <div>
      <i18n path="pages.adminDbNotAllow127001">
        <span slot="sysmgrDbId">{{ sysmgrDbId }}</span>
        <el-button slot="dbServerBtn" type="text" style="padding: 0; margin-bottom: 2px;" :disabled="!hasPermission('A46')" @click="toDbServer">【{{ $t('route.DBServer') }}】</el-button>
      </i18n>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">
        {{ $t('button.close') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'DbIpExceptionDlg',
  components: {},
  data() {
    return {
      visible: false,
      sysmgrDbId: null
    }
  },
  created() {
  },
  methods: {
    initData() {
      this.sysmgrDbId = null
    },
    show(sysmgrDbId) {
      this.initData();
      this.sysmgrDbId = sysmgrDbId || null
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    /**
     * 前后数据库服务器
     */
    toDbServer() {
      this.$router.push({ path: '/system/deviceManage/DBServer', query: { tabName: 'DbInfo' }})
    }
  }
}
</script>

<style scoped>

</style>
