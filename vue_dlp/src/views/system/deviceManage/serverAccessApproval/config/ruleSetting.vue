<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="'匹配接入规则'"
    :visible.sync="visible"
    width="700px"
    @close="close"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="30px" style="width: 600px;">
      <el-row>
        <div style="display: flex">
          <FormItem label="设备类型：" label-width="90px">
            <el-select v-model="temp.devType" style="width: 190px">
              <el-option v-for="(item) in devTypes" :key="item.id" :value="item.id" :label="item.label"/>
            </el-select>
          </FormItem>

          <FormItem label-width="120px" prop="priority" label="规则优先级">
            <div slot="label">
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  规则优先级：服务器接入时，从小到大进行规则匹配。当优先级小的规则先匹配成功后，后续的规则不再进行匹配；<br>
                  例如：存在规则1和规则2，规则1的优先级为1，规则2优先级为2，在新服务器接入时，会优先确认是否和规则1匹配，若匹配成功直接接入，规则2就不再进行匹配。<br>
                  注意：优先级不能重复<br>
                  {{ '优先级使用情况：' + (exitsPriorities.length ? `已使用的优先级: ${exitsPriorities.join(',')}` : '尚未有优先级被使用') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              规则优先级：
            </div>
            <el-input-number v-model="temp.priority" style="width: 80px" :controls="false" :step="1" step-strictly :max="999" :min="1"></el-input-number>
          </FormItem>
        </div>

        <FormItem label-width="15px">
          <label>启用</label>
          <el-switch v-model="temp.rulesEnable" :inactive-value="0" :active-value="1"></el-switch>
        </FormItem>

        <el-divider content-position="left">{{ '匹配规则' }}</el-divider>
        <!-- 备注关键字先隐藏 -->
        <el-checkbox v-if="false" v-model="remarkCheck" class="prefixClass" label="备注信息包含以下关键字" @change="checkChange(['remark'], $event)"/>
        <FormItem v-if="false" prop="remark">
          <el-input v-model="temp.remark" :disabled="!remarkCheck" style="width: 250px" maxlength="20" show-word-limit/>
        </FormItem>

        <el-checkbox v-model="ipCheck" class="prefixClass" label="IP地址范围" @change="checkChange(['ipStart', 'ipEnd'], $event)"/>
        <div style="display: flex">
          <FormItem prop="ipStart">
            <el-input v-model="temp.ipStart" :disabled="!ipCheck" style="width: 250px" maxlength="35" show-word-limit/>
          </FormItem>
          <label style="margin: 0 10px">~</label>
          <FormItem prop="ipEnd" label-width="0">
            <el-input v-model="temp.ipEnd" :disabled="!ipCheck" style="width: 250px" maxlength="35" show-word-limit/>
          </FormItem>
        </div>

        <FormItem v-if="ruleNotSet">
          <div style="color: red">请设置匹配规则！</div>
        </FormItem>

        <el-divider content-position="left">{{ '接入规则' }}</el-divider>
        <FormItem label-width="20px">
          <el-checkbox v-model="temp.defAuthorizedStatus" :disabled="temp.devType === 219" :label="$t('pages.enableServer')" :true-label="1" :false-label="0"/>
          <el-tooltip v-if="temp.devType === 0" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ '注意：数据库服务器固定为启用' }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>

        <FormItem label-width="90px" label="接入方式：">
          <el-radio-group v-model="temp.accessMode">
            <el-radio :key="2" :value="2" :label="2">审批接入</el-radio>
            <el-radio :key="1" :value="1" :label="1">自动接入</el-radio>
          </el-radio-group>
        </FormItem>

        <!-- 采集或检测服务器绑定的主数据库Id -->
        <FormItem v-if="[0, 12, 6].includes(temp.devType)" :label-width="temp.devType === 0 ? '230px' : '75px'" :label="sysmgrTitle" prop="sysmgrDbId">
          <el-select v-model="temp.sysmgrDbId" style="width: 250px">
            <el-option v-for="item in dbServers" :key="item.devId" :value="item.devId" :label="$t('pages.serverAccessDbLabel', { devName: item.name, devId: item.devId })"/>
          </el-select>
        </FormItem>
        <!-- 主数据库Ip不能为127.0.0.1提示 -->
        <FormItem v-if="sysmgrIsVirtualIp" style="width: 100%" label-width="15px">
          <div style="color: #ea8511">
            <i18n path="pages.adminDbNotAllow127001">
              <span slot="sysmgrDbId">{{ sysmgrDbId }}</span>
              <el-button slot="dbServerBtn" type="text" style="padding: 0; margin-bottom: 2px;" :disabled="!hasPermission('A46')" @click="toDbServer">【{{ $t('route.DBServer') }}】</el-button>
            </i18n>
          </div>
        </FormItem>
      </el-row>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="confirm()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="close()">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { isIPv4, isIPv6 } from '@/utils/validate';
import { getRuleDevTypes, listDevServer } from '@/api/system/deviceManage/serverAccessApproval';

export default {
  name: 'RuleSettingDlg',
  components: {},
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {
      },
      defaultTemp: {
        id: null,
        devType: 0,     //  设备类型（0：所有服务器，6：采集服务器，11：文件服务器，12：检测服务器，172：软件中心服务器，219：数据库服务器）
        priority: 1,    //  规则优先级 （优先级不能重复）
        rulesEnable: 1, //  规则开关
        remark: '',     //  备注关键字
        accessMode: 2,  //  匹配的接入方式, 1:自动接入 2:审批接入 规则匹配成功后的自动接入
        defAuthorizedStatus: 1,  //  服务器接入后的默认授权状态   0：未授权，1：已授权
        ipStart: null,  //  IP地址-起始
        ipEnd: null,     //  IP地址-结尾
        sysmgrDbId: null  //  采集或检测服务器绑定的主数据库Id
      },
      remarkCheck: false, //  备注-关键字
      ipCheck: false, //  IP地址范围
      exitsPriorities: [],  //  存储已配置的优先级（优先级不能重复）
      oldPriorities: null,  //  临时保存修改之前规则中的优先级
      rules: {
        priority: [
          { validator: this.priorityValidator, trigger: 'blur' }
        ],
        ipStart: [
          { validator: this.ipStartValidator, trigger: 'blur' }
        ],
        ipEnd: [
          { validator: this.ipEndValidator, trigger: 'blur' }
        ],
        remark: [
          { validator: this.remarkValidator, trigger: 'blur' }
        ],
        sysmgrDbId: [
          { validator: this.sysmgrDbIdValidator, trigger: 'change' }
        ]
      },
      //  各服务器设备类型
      devTypes: getRuleDevTypes(),
      dbServers: [],  //  在线的数据库信息
      isConfirm: false  //  表示是否点击过确认按钮
    }
  },
  computed: {
    //  匹配规则是否未设置
    ruleNotSet() {
      return !(this.ipCheck || this.remarkCheck) && this.isConfirm
    },
    //  主数据库Id字段名称
    sysmgrTitle() {
      let value = '';
      if (this.temp.devType === 0) {
        value = '采集或检测服务器绑定的管理库：'
      } else if (this.temp.devType === 6 || this.temp.devType === 12) {
        value = '管理库：'
      }
      return value;
    },
    //  检测服务器或采集服务器设置的主数据库的Ip地址为虚拟Ip地址，即为127.0.0.1
    sysmgrIsVirtualIp() {
      if (this.temp.rulesEnable && (this.temp.devType === 6 || this.temp.devType === 12 || this.temp.devType === 0) && this.temp.sysmgrDbId != null) {
        const db = this.dbServers.find(t => t.devId === this.temp.sysmgrDbId);
        return !!db && !!db.intranetIp && ['127.0.0.1', '0:0:0:0:0:0:0:1'].includes(db.intranetIp.trim())
      }
      return false;
    }
  },
  created() {
  },
  activated() {
    if (this.temp.devType === 6 || this.temp.devType === 12 || this.temp.devType === 0) {
      this.loadDbServer().then(res => {
        this.$refs.dataForm && this.$refs.dataForm.validateField('sysmgrDbId')
      })
    }
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
      this.remarkCheck = false
      this.ipCheck = false
      this.oldPriorities = null
      this.isConfirm = false
      this.dbServers = []
    },
    /**
     * 加载数据库信息
     */
    loadDbServer() {
      return listDevServer(219, 2).then(res => {
        this.dbServers = res.data || []
      })
    },
    /**
     * 展示
     * @param rule  数据
     * @param exitsPriorities   已配置的优先级
     */
    show(rule, exitsPriorities) {
      this.initData();
      this.loadDbServer()
      this.formatRow(rule, exitsPriorities);
      this.visible = true;
    },
    /**
     * 拷贝
     * @param rule    数据
     * @param exitsPriorities   已配置的优先级
     */
    copy(rule, exitsPriorities) {
      this.initData();
      this.formatRow(rule, exitsPriorities);
      this.temp.id = null
      this.oldPriorities = null
      this.temp.dataId = new Date().getTime();
      this.initPriority(this.exitsPriorities);
      this.visible = true;
    },
    /**
     * 对初始数据格式化处理
     */
    formatRow(rule, exitsPriorities) {
      this.exitsPriorities = exitsPriorities || []
      if (rule) {
        Object.assign(this.temp, rule);
        //  保存当前规则修改之前的优先级
        this.oldPriorities = this.temp.priority
        this.remarkCheck = !!this.temp.remark
        this.ipCheck = !!this.temp.ipStart && !!this.temp.ipEnd
      } else {
        //  初始化Id，仅作用在前端识别每个规则
        this.temp.dataId = new Date().getTime();
        this.initPriority(this.exitsPriorities);
      }
      //  若为数据库服务器，授权状态固定为启用（目前数据库服务器尚不支持设置授权状态，所以默认启用）
      if (this.temp.devType === 219) {
        this.temp.defAuthorizedStatus = 1
      }
    },
    /**
     * 初始化优先级
     * @param exitsPriorities 已存在的优先级
     * @return 返回最大优先级+1 值
     */
    initPriority(exitsPriorities) {
      //  查找最大优先级
      let max = 0;
      exitsPriorities && exitsPriorities.forEach(priority => {
        if (max < priority) {
          max = priority;
        }
      })
      //  已存在的最大优先级+1
      this.temp.priority = max + 1;
    },
    /**
     * 确认之前的格式化处理
     * @param temp
     */
    formatData(temp) {
      const row = JSON.parse(JSON.stringify(temp));
      row.remark = this.remarkCheck ? row.remark : null;
      if (!this.ipCheck) {
        row.ipStart = null;
        row.ipEnd = null;
      }
      return row;
    },
    confirm() {
      this.isConfirm = true;
      this.$refs.dataForm.validate((valid) => {
        if (valid && !this.ruleNotSet && !this.sysmgrIsVirtualIp) {
          const rule = this.formatData(this.temp);
          this.$emit('submit', rule);
          this.close();
        }
      });
    },
    close() {
      this.visible = false;
    },
    /**
     * 取消勾选时，重新校验是否符合规则
     * @param keys
     * @param value
     */
    checkChange(keys, value) {
      if (!value) {
        this.$refs.dataForm.validateField(keys)
      }
    },
    /**
     * 优先级校验
     * @param rule
     * @param value
     * @param callback
     */
    priorityValidator(rule, value, callback) {
      if (!value) {
        callback(new Error('优先级不能为空'));
      } else if (this.exitsPriorities.includes(value) && value !== this.oldPriorities) {
        callback(new Error('该优先级已存在，请重新输入'));
      }
      callback();
    },
    /**
     * 起始 IP 地址校验
     * @param rule
     * @param value
     * @param callback
     */
    ipStartValidator(rule, value, callback) {
      if (this.ipCheck) {
        if (!isIPv4(value) && !isIPv6(value)) {
          callback(new Error('IP地址格式不正确，请检查修改!'));
        }
      }
      callback();
    },
    /**
     * 结尾 IP 地址校验
     * @param rule
     * @param value
     * @param callback
     */
    ipEndValidator(rule, value, callback) {
      if (this.ipCheck) {
        if (!isIPv4(value) && !isIPv6(value)) {
          callback(new Error('IP地址格式不正确，请检查修改！'));
        } else if (isIPv4(this.temp.ipStart) !== isIPv4(this.temp.ipEnd)) {
          callback(new Error('起始IP地址与结尾IP地址必须都为Ipv4或Ipv6！'))
        } else if (this.temp.ipEnd < this.temp.ipStart) {
          callback(new Error('结尾IP地址需大于起始IP地址！'));
        }
      }
      callback();
    },

    /**
     * 备注-关键字校验
     * @param rule
     * @param value
     * @param callback
     */
    remarkValidator(rule, value, callback) {
      if (this.remarkCheck && value.length === 0) {
        callback(new Error('请输入内容'));
      }
      callback();
    },
    /**
     * 采集或检测服务绑定的主数据库
     * @param rule
     * @param value
     * @param callback
     */
    sysmgrDbIdValidator(rule, value, callback) {
      if (value === null) {
        callback(new Error(this.$t('pages.adminDbNotNull') + ''));
      }
      callback();
    },
    /**
     * 前后数据库服务器
     */
    toDbServer() {
      this.$router.push({ path: '/system/deviceManage/DBServer', query: { tabName: 'DbInfo' }})
    }
  }
}
</script>

<style scoped>
.prefixClass {
  margin-left: 20px;
}
</style>
