<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button v-permission="'524'" icon="el-icon-setting" size="mini" @click="handleAccessConfig">
          {{ $t('pages.accessConfig') }}
        </el-button>
        <el-button v-permission="'526'" size="mini" @click="handleBlacklist">
          {{ $t('pages.blacklistList') }}
        </el-button>
        <el-button v-if="false" :disabled="!deleteable" size="mini" @click="handleBatchApproval">
          {{ '批量审批' }}
        </el-button>
        <div class="searchCon">
          <TimeQuery ref="time" :is-clearable="true" :placeholder="$t('table.applicationTime')" :auto-current-value="false" @getTimeParams="getTimeParams"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="342"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('table.devType')" prop="computerName">
                <el-select v-model="devTypes" multiple clearable class="multipleSelect" collapse-tags @change="devTypeChange">
                  <el-option v-for="item in devTypeOptions" :key="item.id" :value="item.id" :label="item.label"/>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.ip')" prop="ips">
                <el-input v-model="query.ip" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.mac')" prop="macs">
                <el-input v-model="query.devMac" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.devGuid')" prop="devGuid">
                <el-input v-model="query.devGuidLike" v-trim clearable :maxlength="50"/>
              </FormItem>
              <FormItem :label="$t('table.version')" prop="version">
                <el-input v-model="query.version" v-trim clearable :maxlength="20"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="reSetCondition">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="approvalAccessList"
        :is-saved-selected="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="false"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <server-access-setting ref="serverAccessSetting"/>
    <server-approval-dlg ref="serverApprovalDlg" @submit="approvalSubmit"/>
    <black-list-dlg ref="blackListDlg"/>
    <batch-server-approval-dlg ref="batchServerApprovalDlg"/>
  </div>
</template>

<script>
import ServerAccessSetting from '@/views/system/deviceManage/serverAccessApproval/config/accessSetting';
import { getPage, getSupportDevTypes/*, supportDevTypeFormatter*/ } from '@/api/system/deviceManage/serverAccessApproval';
import ServerApprovalDlg from '@/views/system/deviceManage/serverAccessApproval/config/serverApprovalDlg';
import BlackListDlg from '@/views/system/deviceManage/serverAccessApproval/config/blackListDlg';
import BatchServerApprovalDlg from '@/views/system/deviceManage/serverAccessApproval/config/batchServerApprovalDlg';

export default {
  name: 'ServerAccessApprovalConfig',
  components: { BatchServerApprovalDlg, BlackListDlg, ServerApprovalDlg, ServerAccessSetting },
  data() {
    return {
      colModel: [
        { prop: 'devType', label: 'devType', width: '120', formatter: this.devTypeFormatter },
        { prop: 'intranetIpv4', label: 'IPv4Addr', width: '120' },
        { prop: 'intranetIpv6', label: 'IPv6Addr', width: '120' },
        { prop: 'devMac', label: 'mac', width: '120' },
        { prop: 'devGuid', label: 'devGuid', width: '180' },
        { prop: 'showVersion', label: 'version', width: '100', formatter: this.devVersionFormatter },
        { prop: 'approveType', label: 'approveType', width: '100', formatter: this.approveTypeFormatter },
        { prop: 'createTime', label: 'applicationTime', width: '130', sort: true },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'approval', click: this.handleApproval }
          ]
        }
      ],
      devTypes: [], //  待查询的设备类型
      query: {
        devId: null,
        devGuid: null,      //  精确匹配guid
        devGuidLike: null,  //  模糊匹配guid
        approveResults: '',
        devTypes: '',
        devMac: '',
        ip: null,
        startDate: null,
        version: '',
        endDate: null
      },
      deleteable: false,
      //  支持接入审批的设备类型
      devTypeOptions: getSupportDevTypes(),
      approveTypeOptions: {     //  审批类型
        '1': this.$t('pages.approvalAccess'),    //  审批接入
        '2': this.$t('pages.intelligentApproveAccess'),   //  智能接入-审批接入
        '3': this.$t('pages.ipChange'),    //  IP变更
        '4': this.$t('pages.deviceNumberConflict'),    //  设备编号冲突
        '5': this.$t('pages.mappingConflict'),   //  映射冲突-绑定的分部数据库已绑定其他数据库
        '6': this.$t('pages.macChange')        //  MAC变更
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['approvalAccessList']
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    /**
     * 刷新
     */
    refresh() {
      this.query = {}
      this.devTypes = []
      this.$refs.time.clearDate();
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    /**
     * 接入审批
     */
    handleAccessConfig() {
      this.$refs.serverAccessSetting.show();
    },
    /**
     * 黑名单列表
     */
    handleBlacklist() {
      this.$refs.blackListDlg.show();
    },
    /**
     * 批量审批
     */
    handleBatchApproval() {
      const datas = this.gridTable.getSelectedDatas() || []
      datas.length && this.$refs.batchServerApprovalDlg.show(datas);
    },
    /**
     * 查询
     */
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    /**
     * 重置高级搜索条件
     */
    reSetCondition() {
      this.devTypes = []
      this.query.devTypes = ''
      this.query.ip = null
      this.query.devMac = null
      this.query.devGuidLike = null
      this.query.version = null
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    /**
     * 审批
     */
    handleApproval(row) {
      this.$refs.serverApprovalDlg.show(row);
    },
    devTypeChange(value) {
      this.query.devTypes = this.devTypes.join(',')
    },
    /**
     * 设备类型格式化
     * @param row
     * @param data
     */
    devTypeFormatter(row, data) {
      const devType = this.devTypeOptions.find(data => data.id === row.devType);
      if (devType) {
        return devType.label;
      }
      return null;
      // return supportDevTypeFormatter(row.devType, row.optInfoData ? row.optInfoData.pkgType : null);
    },
    /**
     * 审批类型
     * 审批类型 1-审批接入，2-智能接入-审批接入，3-IP变更，4-设备编号冲突，5-映射冲突
     * @param row
     * @param data
     */
    approveTypeFormatter: function(row, data) {
      return this.approveTypeOptions[data + ''];
    },
    /**
     * 服务器版本号
     * showVersion：表示展示的版本号
     * devVersion：内置的版本号
     * 添加showVersion的目的：
     *    兼容新华三的安装包和我们内置的版本号不一致问题，新增showVersion表示展示的版本号，普通包showVersion展示内置版本号，新华三的包展示新华三的版本号
     * @param row
     * @param data
     * @returns {string|default.methods.temp.devVersion|*}
     */
    devVersionFormatter: function(row, data) {
      return !row.showVersion ? row.devVersion : row.showVersion
    },
    /**
     * 审批结束后
     */
    approvalSubmit() {
      this.handleFilter()
    }
  }
}
</script>
<style lang="scss" scoped>
.multipleSelect {
  >>>.el-input el-input--suffix .input{
    height: 30px !important;
  }
}
</style>
