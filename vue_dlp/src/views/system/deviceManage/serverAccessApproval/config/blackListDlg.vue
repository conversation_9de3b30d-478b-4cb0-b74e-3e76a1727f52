<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.blacklistList')"
    :visible.sync="visible"
    width="800px"
    @close="close"
  >
    <div slot="title" class="el-dialog__title">
      <span>{{ $t('pages.blacklistList') }}</span>
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          {{ $t('pages.blacklistTip1') }}<br>
          {{ $t('pages.blacklistTip2') }}
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <div class="toolbar">
      <el-select v-model="query.devTypes" multiple clearable collapse-tags style="width: 200px" :placeholder="$t('table.devType')">
        <el-option v-for="item in $parent.devTypeOptions" :key="item.id" :label="item.label" :value="item.id"/>
      </el-select>
      <el-input v-model="query.searchInfo" v-trim style="width: 200px" clearable :placeholder="$t('pages.devGuidOrMacAddress')"></el-input>
      <el-button type="primary" icon="el-icon-search" size="mini" :maxlength="50" @click="handleFilter">
        {{ $t('table.search') }}
      </el-button>
      <el-button icon="el-icon-delete" size="mini" :disabled="!selected" @click="handleDeleteFun">
        {{ $t('button.delete') }}
      </el-button>
    </div>
    <grid-table
      ref="blackList"
      row-key="id"
      :col-model="colModel"
      :row-datas="rowDatas"
      :multi-select="true"
      :height="300"
      :show-pager="false"
      @selectionChangeEnd="handleSelectableFun"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="close()">
        {{ $t('button.close') }}
      </el-button>
    </div>
  </el-dialog>

</template>

<script>
import { blackList, removeBlackList } from '@/api/system/deviceManage/serverAccessApproval';

export default {
  name: 'BlackListDlg',
  components: {},
  data() {
    return {
      visible: false,
      submitting: false,
      selected: false,
      colModel: [
        { prop: 'devType', label: 'devType', width: '100', formatter: (row) => { return this.$parent.devTypeFormatter(row); } },
        { prop: 'devMac', label: 'mac', width: '100' },
        { prop: 'devGuid', label: 'devGuid', width: '200' },
        { prop: 'showVersion', label: 'version', width: '100', formatter: this.devVersionFormatter }
      ],
      rowDatas: [],
      query: {}
    }
  },
  created() {
  },
  methods: {
    initData() {
      this.selected = false
      this.rowDatas = []
      this.query = {}
    },
    show() {
      this.initData();
      this.handleFilter();
      this.visible = true
    },
    /**
     * 加载黑名单列表数据
     */
    loadData(query) {
      blackList(query).then(res => {
        this.rowDatas = res.data || []
      })
    },
    close() {
      this.visible = false
    },
    /**
     * 查询
     */
    handleFilter() {
      this.loadData(this.query);
    },
    /**
     * 删除
     */
    handleDeleteFun() {
      const ids = this.$refs.blackList.getSelectedKeys() || [];
      ids.length && this.$confirmBox(this.$t('pages.removeFromBlacklistTip'), this.$t('text.prompt')).then(() => {
        const datas = this.$refs.blackList.getSelectedDatas() || [];
        const devGuids = []
        datas.forEach(t => devGuids.push(t.devGuid));
        ids.length && removeBlackList({ ids: ids.join(','), devGuids: devGuids.join(',') }).then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.removeSuccess'),
            type: 'success',
            duration: 2000
          })
          this.handleFilter();
        })
      }).catch(() => {})
    },
    handleSelectableFun(datas) {
      this.selected = datas.length > 0
    },
    /**
     * 服务器版本号
     * showVersion：表示展示的版本号
     * devVersion：内置的版本号
     * 添加showVersion的目的：
     *    兼容新华三的安装包和我们内置的版本号不一致问题，新增showVersion表示展示的版本号，普通包showVersion展示内置版本号，新华三的包展示新华三的版本号
     * @param row
     * @param data
     * @returns {string|default.methods.temp.devVersion|*}
     */
    devVersionFormatter: function(row, data) {
      return !row.showVersion ? row.devVersion : row.showVersion
    }
  }
}
</script>

<style scoped>

</style>
