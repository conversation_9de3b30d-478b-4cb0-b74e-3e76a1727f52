<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="'批量审批接入'"
    :visible.sync="visible"
    width="750px"
    @close="close"
  >
    <div slot="title" class="el-dialog__title">
      {{ '批量审批接入' }}
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          {{ '各服务器的默认配置信息如下:' }}<br>
          {{ '文件服务器、软件中心服务器：备份主路径默认选择剩余空间最多的盘符；' }}<br>
          {{ '数据库服务器：默认使用总部接入、非映射服务器' }}
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 700px;">
      <grid-table
        ref="approvalAccessList"
        :col-model="colModel"
        :row-datas="rowDatas"
        :multi-select="false"
        :show-pager="false"
        :height="200"
      />
      <el-divider content-position="left">{{ '接入配置' }}</el-divider>
      <div class="container">
        <FormItem label="接入节点：">
          <el-select v-model="accessNode" :disabled="true" class="width">
            <el-option :key="1" :value="1" :label="'新节点'"/>
            <el-option :key="2" :value="2" :label="'旧节点'"/>
          </el-select>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="temp.authorizedStatus" :true-label="1" :false-label="0" :label="$t('pages.enableServer')"/>
        </FormItem>
      </div>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="confirm(1)">
        {{ '接入' }}
      </el-button>
      <el-tooltip :content="'拒绝后，会将此设备纳入到黑名单中，该设备将无法接入引擎'" placement="top">
        <el-button :loading="submitting" style="background: linear-gradient(#d7d7d7, #d30404)" @click="confirm(0)">
          {{ '拒绝' }}
        </el-button>
      </el-tooltip>
      <el-button @click="close()">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { isIPv4, isIPv6, isPort } from '@/utils/validate';
import { batchApprove, getSupportDevTypes } from '@/api/system/deviceManage/serverAccessApproval';
import { fileServerDefaultBusses } from '@/views/system/deviceManage/backupServer/backupServerCommonInfo';

export default {
  name: 'BatchServerApprovalDlg',
  components: {},
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {
      },
      defaultTemp: {
        authorizedStatus: 0,  //  是否开启服务器（授权状态）
        approveResult: 0,    //  审批结果，0：待审批，1：使用新节点，2：使用旧节点，3：审批拒绝
        extranetIpv4: '',    //  外网IPv4
        extranetIpv6: '',    // 外网IPv6
        extranetPort: null   // 外网端口
      },
      rules: {
        extranetIpv4: [
          { validator: this.extranetIpv4Validator, trigger: 'blur' }
        ],
        extranetIpv6: [
          { validator: this.extranetIpv6Validator, trigger: 'blur' }
        ],
        extranetPort: [
          { validator: this.portValidator, trigger: 'blur' }
        ]
      },
      //  各服务器设备类型
      devTypes: getSupportDevTypes(),
      accessNode: 1,     //  接入节点   1-新节点，2-旧节点
      enableExternalIp: 0,   //  是否开启外网IP
      rowDatas: [],
      colModel: [
        { prop: 'devType', label: '设备类型', width: '100', formatter: this.$parent.devTypeFormatter },
        { prop: 'ip', label: 'ip', width: '100', formatter: this.$parent.ipFormatter },
        { prop: 'devMac', label: 'mac', width: '100' },
        { prop: 'devVersion', label: 'version', width: '100' },
        { prop: 'createTime', label: '申请接入时间', width: '100' },
        { prop: 'remark', label: 'remark', width: '100' }
      ]
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.submitting = false;
      this.rowDatas = []
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
    },
    /**
     * 展示
     * @param rowDatas
     */
    show(rowDatas) {
      this.initData();
      this.formatRows(rowDatas);
      this.visible = true;
    },
    /**
     * 对初始数据格式化处理
     */
    formatRows(rowDatas) {
      this.rowDatas = rowDatas
    },
    /**
     * 确认之前的格式化处理
     * @param temp
     */
    formatData(temp) {
      const row = JSON.parse(JSON.stringify(temp));
      //  若为审批拒绝
      if (row.approveResult == 3) {
        row.devGuids = this.rowDatas.map(t => t.devGuid).join(',');
        row.rejectDevList = [];
        this.rowDatas.forEach(t => {
          row.rejectDevList.push({ devGuid: t.devGuid, devType: t.devType, devMac: t.devMac, devVersion: t.devVersion })
        })
      }
      //  若为文件服务器，添加备份服务器业务关联表的初始信息
      if (row.devType === 11) {
        row.buss = {}
        fileServerDefaultBusses.forEach(t => {
          row.buss[t.bussId] = [t.bussName || '', t.relativePath || '']
        })
      }
      return row;
    },
    /**
     * 接入
     * @param status
     * 1: 审批通过
     * 0：拒绝
     */
    confirm(status) {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          const rowTemp = this.formatData(this.temp);
          rowTemp.approveResult = status ? this.accessNode : 3;
          batchApprove(rowTemp).then(res => {
            this.$emit('submit', rowTemp);
            this.$notify({
              title: this.$t('text.success'),
              message: status ? '接入成功' : '拒绝成功',
              type: 'success',
              duration: 2000
            })
            this.submitting = false;
            this.close();
          }).catch(() => { this.submitting = false })
        }
      });
    },
    close() {
      this.visible = false;
    },
    /**
     * 内网端口校验
     * @param rule
     * @param value
     * @param callback
     */
    portValidator(rule, value, callback) {
      if (!value) {
        callback(new Error('端口不能为空'))
      } else if (!isPort(value)) {
        callback(new Error('端口格式不正确，请检查修改!'))
      }
      callback();
    },
    /**
     * 外网IPv4校验
     * @param rule
     * @param value
     * @param callback
     */
    extranetIpv4Validator(rule, value, callback) {
      if (!this.temp.extranetIpv4 && !this.temp.extranetIpv6) {
        callback(new Error('内网IPv4不能为空！'));
      } else if (this.temp.extranetIpv4 && !isIPv4(value)) {
        callback(new Error('IP地址格式不正确，请检查修改!'));
      }
      callback();
    },
    /**
     * 外网IPv6校验
     * @param rule
     * @param value
     * @param callback
     */
    extranetIpv6Validator(rule, value, callback) {
      if (!this.temp.extranetIpv4 && !this.temp.extranetIpv6) {
        callback(new Error('内网IPv6不能为空！'));
      } else if (this.temp.extranetIpv6 && !isIPv6(value)) {
        callback(new Error('IP地址格式不正确，请检查修改!'));
      }
      callback();
    }
  }
}
</script>

<style scoped>
.width {
  width: 200px;
}
.container {
  display: grid;
  grid-template-columns: 50% 50%;
}
</style>
