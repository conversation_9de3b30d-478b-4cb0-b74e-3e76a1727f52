<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.accessConfig')"
      :visible.sync="visible"
      width="800px"
      @close="close"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('pages.accessConfig') }}
        <el-tooltip slot="reference" effect="dark" placement="top">
          <div slot="content">
            {{ $t('pages.accessSettingTip') }}
          </div>
          <i class="el-icon-info"/>
        </el-tooltip>
      </div>
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 700px; margin-left:30px;">
        <el-row>
          <FormItem :label="$t('pages.serverAccessMode') + '：'" :extra-width="{en: 70}">
            <el-col v-for="item in accessModeOptions" :key="item.id" :label="item.id" :value="item.id" :span="12">
              <!-- 智能接入暂时隐藏 -->
              <el-radio v-model="temp.accessMode" :label="parseInt(item.id)" class="ellipsis" style="width: 98%;">
                <span :title="item.label">
                  {{ item.label }}
                </span>
                <el-tooltip v-if="item.id === 2" class="item" effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.serverAccessTip') }}<br>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-radio>
            </el-col>
          </FormItem>
          <FormItem v-if="temp.accessMode !== 0" label-width="0">
            <el-checkbox v-model="temp.lowVerLogin" :true-label="1" :false-label="0" :label="$t('pages.allowEarlyVersionLogin', { version: currentServerVersion })" @change="lowVerLoginChange"/>
          </FormItem>
          <FormItem v-if="temp.accessMode !== 0" label-width="0">
            <el-checkbox v-model="temp.lowVerAccess" :disabled="!temp.lowVerLogin" :true-label="1" :false-label="0" :label="$t('pages.allowEarlyVersionAccess', { version: currentServerVersion })"/>
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                {{ $t('pages.serverAccessTip2') }}
              </div>
              <i class="el-icon-info" :style="{ color: temp.lowVerAccess ? '#409EFF' : null }"/>
            </el-tooltip>
          </FormItem>
          <FormItem v-if="temp.accessMode !== 0" label-width="0">
            <el-checkbox v-model="temp.autoChangeIp" :true-label="0" :false-label="1" :label="$t('pages.accessServerIPChangeReApproval')"/>
          </FormItem>
          <FormItem v-if="temp.accessMode === 1" label-width="0" prop="rules">
            <el-card class="box-card" :body-style="{'padding': ' 5px'}">
              <div slot="header">
                <el-row>
                  匹配接入规则
                  <div style="float: right">
                    <el-button icon="el-icon-plus" size="small" @click="handleRuleAdd">
                      添加
                    </el-button>
                    <el-button :disabled="!selected" icon="el-icon-delete" size="small" @click="handleRuleDelete">
                      删除
                    </el-button>
                  </div>
                </el-row>
              </div>
              <grid-table
                ref="ruleTable"
                :height="200"
                row-key="dataId"
                :multi-select="true"
                :col-model="ruleColModel"
                :row-datas="temp.rules"
                :show-pager="false"
                @selectionChangeEnd="selectionChangeEnd"
              />
              <FormItem v-if="temp.accessMode === 1" label-width="0" style="margin-top: 10px">
                <span>
                  不符合接入规则的服务器使用以下接入方式
                </span>
                <el-select v-model="temp.defaultMode" class="width">
                  <el-option :key="2" :value="2" label="审批接入"/>
                  <el-option :key="0" :value="0" label="禁止接入"/>
                </el-select>
              </FormItem>
            </el-card>

          </FormItem>

          <FormItem v-if="temp.accessMode === 1" label-width="0">
            <label>
              自动接入成功后，各设备的默认名称前缀：
            </label>
          </FormItem>
          <div v-if="temp.accessMode === 1" class="container">
            <FormItem label="采集服务器：">
              <el-input v-model="cjServerNamePrefix" class="width"/>
            </FormItem>
            <FormItem label="检测服务器：">
              <el-input v-model="jcServerNamePrefix" class="width"/>
            </FormItem>
            <FormItem label="文件服务器：">
              <el-input v-model="fileServerNamePrefix" class="width"/>
            </FormItem>
            <FormItem label="软件中心服务器：">
              <el-input v-model="softServerNamePrefix" class="width"/>
            </FormItem>
            <FormItem label="数据库服务器：">
              <el-input v-model="dbServerNamePrefix" class="width"/>
            </FormItem>
          </div>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="close()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <rule-setting-dlg ref="ruleSettingDlg" @submit="ruleSettingSubmit"/>
  </div>
</template>

<script>
import GridTable from '@/components/GridTable';
import RuleSettingDlg from '@/views/system/deviceManage/serverAccessApproval/config/ruleSetting';
import { getConfig, getRuleDevTypes, saveConfig } from '@/api/system/deviceManage/serverAccessApproval';

export default {
  name: 'ServerAccessSetting',
  components: { RuleSettingDlg, GridTable },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {
      },
      defaultTemp: {
        accessMode: 2,    //  接入模式  0：禁止接入，1：智能接入，2：审批接入
        lowVerAccess: 1,  //  允许低于当前引擎版本的服务器接入  1：允许，0：禁止
        lowVerLogin: 1,   //  允许低于当前引擎版本的服务器登录  1：允许，0：禁止
        autoChangeIp: 1,  //  已接入的服务器IP变更后需要重新审批 默认1，即IP变更后自动审批
        defaultMode: 2,   //  在智能接入时，若不匹配规则时，设备的接入方式   0：禁止接入，2：审批接入
        oldLowVerAccess: null,
        oldLowVerLogin: null,
        oldAutoChangeIp: null,
        rules: []         //  匹配规则
      },
      selected: false,    //  是否选中
      currentServerVersion: null,   //  当前服务器版本号
      rules: {
        rules: [
          { validator: this.rulesValidator, trigger: 'blur' }
        ]
      },
      ruleColModel: [
        { prop: 'devType', label: 'devType', width: '100', formatter: this.devTypeFormatter },
        { prop: 'priority', label: '规则优先级', width: '120', sort: true, type: 'input-number', editMode: true, maxlength: 3 },
        { prop: 'rulesEnable', label: '是否开启', width: '100', formatter: (row) => { return row.rulesEnable ? '是' : '否' } },
        { prop: 'switchOnRules', label: '接入规则信息', width: '150', formatter: this.switchOnRulesFormatter },
        { prop: 'matchRules', label: '匹配规则信息', width: '150', formatter: this.matchRulesFormatter },
        { prop: '', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handlerRuleUpdate },
            { label: '拷贝', click: this.handlerRuleCopy }
          ]
        }
      ],
      //  匹配规则接入方式
      ruleAccessModeOptions: {
        '1': this.$t('pages.autoAccess'),    //  自动接入
        '2': this.$t('pages.approvalAccess')     //  审批接入
      },
      //  支持接入审批的设备
      devTypes: getRuleDevTypes(),
      cjServerNamePrefix: this.$t('route.daqServer'),  //  采集服务器-设备名称前缀
      jcServerNamePrefix: this.$t('route.DetectionServer'),  //  检测服务器-设备名称前缀
      fileServerNamePrefix: this.$t('route.backupServer'), //  文件服务器-设备名称前缀
      softServerNamePrefix: this.$t('route.softwareServer'), //  软件中心服务器-设备名称前缀
      dbServerNamePrefix: this.$t('route.DBServer')  //  数据库服务器-设备名称前缀
    }
  },
  computed: {
    //  接入方式
    accessModeOptions() {
      return [
        // { id: 1, label: '智能接入' },
        { id: 2, label: '审批接入' },
        { id: 0, label: '禁止接入' }]
    },
    //  已配置的优先级
    exitsPriorities() {
      return this.temp.rules.map(t => t.priority);
    }
  },
  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
      this.currentServerVersion = null
      this.selected = false;
      this.submitting = false;
      this.cjServerNamePrefix = this.$t('route.daqServer')  //  采集服务器-设备名称前缀
      this.jcServerNamePrefix = this.$t('route.DetectionServer')  //  检测服务器-设备名称前缀
      this.fileServerNamePrefix = this.$t('route.backupServer') //  文件服务器-设备名称前缀
      this.softServerNamePrefix = this.$t('route.softwareServer') //  软件中心服务器-设备名称前缀
      this.dbServerNamePrefix = this.$t('route.DBServer')  //  数据库服务器-设备名称前缀
    },
    async show() {
      this.initData();
      await this.loadAccessSetting();
      this.visible = true;
    },
    /**
     * 加载服务器接入审批配置信息
     */
    loadAccessSetting() {
      return getConfig().then(res => {
        const data = res.data
        Object.assign(this.temp, data);
        this.temp.rules = data.rules || []
        this.currentServerVersion = data.currentServerVersion;
        this.temp.rules.forEach(data => {
          data.dataId = data.id;
        });
        this.cjServerNamePrefix = data.defaultPrefix ? data.defaultPrefix['6'] : this.cjServerNamePrefix   //  采集服务器
        this.jcServerNamePrefix = data.defaultPrefix ? data.defaultPrefix['12'] : this.jcServerNamePrefix   //  检测服务器
        this.fileServerNamePrefix = data.defaultPrefix ? data.defaultPrefix['11'] : this.fileServerNamePrefix   //  文件服务器
        this.softServerNamePrefix = data.defaultPrefix ? data.defaultPrefix['172'] : this.softServerNamePrefix   //  软件中心服务器
        this.dbServerNamePrefix = data.defaultPrefix ? data.defaultPrefix['219'] : this.dbServerNamePrefix   //  数据库服务器

        //  若为禁止接入时，审批接入、智能接入的配置信息使用上次存储的
        if (this.temp.accessMode === 0) {
          this.temp.lowVerAccess = this.isUndefined(this.temp.oldLowVerAccess) ? this.temp.lowVerAccess : this.temp.oldLowVerAccess
          this.temp.lowVerLogin = this.isUndefined(this.temp.oldLowVerLogin) ? this.temp.lowVerLogin : this.temp.oldLowVerLogin
          this.temp.autoChangeIp = this.isUndefined(this.temp.oldAutoChangeIp) ? this.temp.autoChangeIp : this.temp.oldAutoChangeIp
        }
      })
    },
    /** 数据是否为空（未定义或者Null） **/
    isUndefined(data) {
      return data === undefined || data === null;
    },
    /**
     * 确认之前的数据校验
     */
    formDataValid() {
      //  校验是否存在优先级相同的规则
      if (this.exitsPriorities.length) {
        const map = new Map();
        for (let i = 0; i < this.exitsPriorities.length; i++) {
          const t = this.exitsPriorities[i];
          if (map.get(t)) {
            this.$message({
              message: '匹配规则存在相同优先级，请重新设置！',
              type: 'error',
              duration: 2000
            })
            return false;
          }
          map.set(t, 1);
        }
      }
      return true;
    },
    /**
     * 确认之前的格式化处理
     * @param temp
     */
    formatData(temp) {
      const row = JSON.parse(JSON.stringify(temp))
      //  设置默认设备名称前缀
      row.defaultPrefix = {};
      //  若为智能接入时，配置设备名称前缀
      if (row.accessMode === 1) {
        row.defaultPrefix['6'] = this.cjServerNamePrefix
        row.defaultPrefix['12'] = this.jcServerNamePrefix
        row.defaultPrefix['11'] = this.fileServerNamePrefix
        row.defaultPrefix['172'] = this.softServerNamePrefix
        row.defaultPrefix['219'] = this.dbServerNamePrefix
      }
      row.currentServerVersion = null
      //  若为禁止接入时，将审批接入的配置保存起来
      if (row.accessMode === 0) {
        row.oldLowVerAccess = row.lowVerAccess
        row.oldLowVerLogin = row.lowVerLogin
        row.oldAutoChangeIp = row.autoChangeIp
      }
      return row;
    },
    /**
     * 确认
     */
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid && this.formDataValid()) {
          this.submitting = true;
          const rowTemp = this.formatData(this.temp);
          saveConfig(rowTemp).then(res => {
            this.submitting = false;
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.settingConfigSuccess'),
              type: 'success',
              duration: 2000
            })
            this.close();
          }).catch(() => {
            this.submitting = false;
          })
        }
      });
    },
    /**
     * 关闭
     */
    close() {
      this.$refs.ruleTable && this.$refs.ruleTable.clearSelection();
      this.visible = false;
    },
    /**
     * 规则添加
     */
    handleRuleAdd() {
      this.$refs.ruleSettingDlg.show(null, this.exitsPriorities);
    },
    /**
     * 规则删除
     */
    handleRuleDelete: function() {
      const dataIds = this.$refs.ruleTable.getSelectedKeys();
      if (dataIds && dataIds.length) {
        this.temp.rules = this.temp.rules.filter(rule => !dataIds.includes(rule.dataId))
        this.selected = false;
      }
    },
    /**
     * 规则拷贝
     */
    handlerRuleCopy(row) {
      this.$refs.ruleSettingDlg.copy(row, this.exitsPriorities)
    },
    /**
     * 规则修改
     * @param row
     */
    handlerRuleUpdate(row) {
      this.$refs.ruleSettingDlg.show(row, this.exitsPriorities)
    },
    /**
     * 匹配规则设置回调
     * @param rule
     */
    ruleSettingSubmit(rule) {
      const data = this.temp.rules.find(t => t.dataId === rule.dataId);
      if (data) {
        Object.assign(data, rule);
      } else {
        this.temp.rules.push(rule);
      }
      this.$refs.dataForm.validateField('rules')
    },
    /**
     * 列表选中
     * @param data
     */
    selectionChangeEnd(rowDatas) {
      this.selected = rowDatas && rowDatas.length
    },
    /**
     * 设备类型格式化
     * @param row
     * @param data
     * @returns {string}
     */
    devTypeFormatter(row, data) {
      const type = this.devTypes.find(data => data.id === row.devType);
      if (type) {
        return type.label;
      }
      return '';
    },
    /**
     * 接入规则格式化
     * @param row
     * @param data
     */
    switchOnRulesFormatter(row, data) {
      let result = ''
      if (row.accessMode) {
        result += '接入方式：' + this.ruleAccessModeOptions[row.accessMode] + '；'
      }
      if (row.defAuthorizedStatus) {
        result += '启用服务器；'
      }
      return result;
    },
    /**
     * 匹配规则格式化
     * @param row
     * @param data
     */
    matchRulesFormatter(row, data) {
      let result = '';
      if (row.remark) {
        result += `备注信息需包含“${row.remark}”关键字；`
      }
      if (row.ipStart && row.ipEnd) {
        result += `IP地址范围：${row.ipStart}~${row.ipEnd}；`
      }
      return result;
    },
    /**
     * 规则校验
     * @param rule
     * @param value
     * @param callback
     */
    rulesValidator(rule, value, callback) {
      value = value.filter(t => !!t.rulesEnable);
      if (!value.length) {
        callback(new Error('请设置已开启的匹配规则！'))
      }
      callback();
    },
    /**
     *
     */
    lowVerLoginChange(value) {
      if (!value) {
        this.temp.lowVerAccess = 0
      }
    }
  }
}
</script>

<style scoped>
.width {
  width: 200px;
}
.container {
  display: grid;
  grid-template-columns: 50% 50%;
}
</style>
