<template>
  <div>
    <!-- 磁盘信息, 仅windows显示-->
    <el-card v-if="osType === 1" class="diskStyle" :shadow="viewDisk ? 'always' : 'never'" :style="cardStyle">
      <div slot="header">
        <span>{{ $t('pages.diskInfo') }}</span>
        <el-button style="float: right; padding: 3px 0; margin-bottom: 0; color: black; background-color: transparent; border: 0" class="el-icon-close" @click="close"/>
      </div>
      <div>
        <el-row v-for="(disk, index) in disks" :key="index">
          <el-col :span="8">
            <FormItem :label="$t('table.diskName') + '：'" label-width="100px">
              {{ disk[labelFormat.diskName] }}
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="$t('pages.totalCapacity') + '：'" label-width="100px">
              {{ disk[labelFormat.totalDiskSize] + ' GB' }}
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="$t('pages.residualCapacity') + '：'" label-width="100px">
              {{ disk[labelFormat.freeSize] + ' GB' }}
            </FormItem>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>

export default {
  name: 'DiskInfo',
  components: {},
  props: {
    osType: { type: Number, default: 1 },    //  操作系统类型，默认为1-windows
    diskList: { type: Array, default() { return [] } },  //  磁盘信息
    labelFormat: { type: Object, default() { return { diskName: 'diskName', totalDiskSize: 'totalDiskSize', freeSize: 'freeSize' } } }
  },
  data() {
    return {
      viewDisk: false,   //  显示磁盘信息
      disks: [],
      diskIds: [] //  盘符

    }
  },
  computed: {
    cardStyle() {
      const style = { 'max-height': this.viewDisk ? '235px' : '0', 'transition': 'max-height 0.3s ease-out', 'margin-bottom': this.viewDisk ? '10px' : '0', border: 'none' };
      if (this.viewDisk) {
        delete style.border;
      }
      return style;
    }
  },
  watch: {
    diskList(value) {
      this.loadData();
    }
  },
  created() {
    this.loadData();
  },
  methods: {
    /**
     * 显示磁盘信息
     */
    show() {
      this.loadData();
      this.viewDisk = true;
    },
    loadData() {
      const diskList = JSON.parse(JSON.stringify(this.diskList))
      diskList.forEach(disk => {
        //  设置盘符名称
        const name = disk[this.labelFormat.diskName]
        disk.keyId = name && name.length > 4 ? name.substr(name.length - 3, 1).toLocaleUpperCase() : '';
        this.diskIds.push(disk.keyId);
        disk[this.labelFormat.totalDiskSize] = (disk[this.labelFormat.totalDiskSize] / 1024 / 1024 / 1024).toFixed(2);
        disk[this.labelFormat.freeSize] = (disk[this.labelFormat.freeSize] / 1024 / 1024 / 1024).toFixed(2)
      })
      this.disks = diskList
    },
    //  获取盘符信息
    getDiskIds() {
      return JSON.parse(JSON.stringify(this.diskIds));
    },
    close() {
      this.viewDisk = false;
      this.$emit('closeFuc')
    }
  }
}
</script>

<style lang='scss' scoped>
//  磁盘信息样式
.diskStyle {
  >>>.el-card__body {
    overflow-y: auto;
    max-height: 200px;
    padding: 10px 20px;
    .el-form-item {
      margin-bottom: 0;
      .el-form-item__label {
        font-size: 13px;
      }
      .el-form-item__content {
        font-size: 13px;
      }
    }
  }
}
</style>
