<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.approvalAccess')"
      :visible.sync="visible"
      width="760px"
      @close="close"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="130px" style="width: 710px;">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.devType') + '：'">
              {{ devTypeFormatter(temp.devType) }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.osTypeText') + '：'">
              {{ osTypeFormatter(temp.osType) }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.versionNumber') + '：'">
              {{ $parent.devVersionFormatter(temp, temp.devVersion) }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.applicationTime') + '：'">
              {{ temp.createTime }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.approveType') + '：'">
              {{ $parent.approveTypeFormatter(temp, temp.approveType) }}
            </FormItem>
          </el-col>
          <!-- 数据库服务器分为外部数据库和内部数据库 -->
          <el-col v-if="temp.devType === 219" :span="12">
            <FormItem :label="$t('pages.installMode') + '：'">
              {{ dbInstallType ? $t('pages.dbServerInstallType1') : $t('pages.dbServerInstallType2') }}
            </FormItem>
          </el-col>
        </el-row>

        <el-divider content-position="left">{{ $t('pages.accessConfiguration') }}</el-divider>
        <!-- 数据库服务器接入时，数据库新设备的IP和端口已存在系统中，强制使用旧节点接入，外置数据库强烈建议IP和端口不能和已存在系统中 -->
        <FormItem v-if="temp.devType === 219 && dbExitsSameIpAndPort">
          <span style="color: red">
            {{ !dbInstallType ? `注：新数据库的IP和端口和系统中设备编号为${dbExitsDevId}的数据库相同，已强制选中该节点` : `注：新数据库的IP和端口和系统中设备编号为${dbExitsDevId}的数据库相同，该数据库不允许使用新节点接入` }}
          </span>
        </FormItem>
        <!-- 旧节点接入提示 -->
        <FormItem v-if="isFileServerOldNode" label-width="0" style="margin-left: 45px; margin-right: 25px">
          <span style="color: red">{{ $t('pages.serverAccessOldNodeTip', { devType: temp.devType === 172 ? $t('route.softwareServer') : $t('route.backupServer') }) }}</span>
        </FormItem>
        <!-- 主数据库Ip不能为127.0.0.1提示 -->
        <FormItem v-if="sysmgrIsVirtualIp" label-width="0" style="margin-left: 58px; margin-right: 25px">
          <div style="color: #ea8511">
            <i18n path="pages.adminDbNotAllow127001">
              <span slot="sysmgrDbId">{{ temp.sysmgrDbId }}</span>
              <el-button slot="dbServerBtn" type="text" style="padding: 0; margin-bottom: 2px;" :disabled="!hasPermission('A46')" @click="toDbServer">【{{ $t('route.DBServer') }}】</el-button>
            </i18n>
          </div>
        </FormItem>
        <FormItem v-if="dbIsBind" label-width="0" style="margin-left: 58px; margin-right: 25px">
          <span style="color:#ea8511;">
            {{ dbNotBindTitle }}
          </span>
        </FormItem>
        <div class="container">
          <FormItem :label="$t('pages.accessNode')">
            <div v-if="temp.devType === 11 || temp.devType === 172" slot="label">
              {{ $t('pages.accessNode') }}
              <el-tooltip effect="dark" placement="top-start">
                <div slot="content">
                  {{ `旧节点使用场景：当需要将${fileServerTitle}1迁移到${fileServerTitle}2时，${fileServerTitle}2在接入审批时旧节点选择${fileServerTitle}1，在审批之前请确保${fileServerTitle}1处于离线状态，且${fileServerTitle}1中的文件需先迁移到${fileServerTitle}2中。` }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
            <el-select v-model="accessNode" class="width" :disabled="dbExitsSameIpAndPort">
              <el-option :key="1" :value="1" :label="$t('pages.newNode')"/>
              <el-option :key="2" :value="2" :label="$t('pages.oldNode')"/>
            </el-select>
          </FormItem>
          <FormItem v-if="accessNode === 2" label="指定节点" prop="devId">
            <div slot="label">
              <span style="color: red;">*</span>
              {{ $t('pages.appointNode') }}
              <el-tooltip effect="dark" placement="top-start">
                <div slot="content">
                  {{ $t('pages.serverAccessOldNodeTip1') }}<br>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
            <el-select v-model="temp.devId" class="width" :disabled="dbExitsSameIpAndPort && !dbInstallType" @change="devIdChange">
              <el-option v-for="item in exitsDevServers" :key="item.devId" :value="item.devId" :label="$t('pages.serverAccessDbLabel', { devName: item.name, devId: item.devId })"/>
            </el-select>
          </FormItem>
          <FormItem :label="$t('table.deviceName')" prop="devName">
            <el-input v-model="temp.devName" v-trim class="width" maxlength="60" clearable/>
          </FormItem>
          <!-- 采集服务器、检测服务器和只能备份服务器的主数据库Id -->
          <FormItem v-show="[12, 6, 186].includes(temp.devType)" :label="$t('pages.adminLibrary')" prop="sysmgrDbId">
            <div slot="label">
              <span style="color: red;">*</span>
              {{ $t('pages.adminLibrary') }}
              <el-tooltip effect="dark" placement="top-start">
                <div slot="content">
                  {{ $t('pages.serverAccessAdminDbTip') }}<br>
                  {{ $t('pages.serverAccessAdminDbTip1') }}<br>
                  {{ $t('pages.serverAccessAdminDbTip3') }}<br>
                  {{ $t('pages.serverAccessAdminDbTip4') }}<br>
                  {{ $t('pages.serverAccessAdminDbTip5') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
            <el-select v-model="temp.sysmgrDbId" class="width">
              <el-option v-for="item in dbServers" :key="item.devId" :value="item.devId" :label="$t('pages.serverAccessDbLabel', { devName: item.name, devId: item.devId })">
                <span :style="{ fontWeight: item.devId === 9001 ? 'bold' : null }">
                  {{ $t('pages.serverAccessDbLabel', { devName: item.name, devId: item.devId }) }}
                </span>
              </el-option>
            </el-select>
          </FormItem>
          <FormItem :label="$t('table.remark')">
            <el-input v-model="temp.remark" v-trim type="textarea" :row="temp.type === 219 ? 2 : 1" class="width" maxlength="100" clearable/>
          </FormItem>

          <!-- 目前仅采集服务器、智能备份服务器有分组可指定   智能备份文件服务器-->
          <FormItem v-if="[6, 186].includes(temp.devType)" :label="$t('pages.serverAccessGroup')" prop="groupId">
            <div style="display: flex">
              <tree-select
                ref="treeSelect"
                :data="collectGroups"
                node-key="dataId"
                :checked-keys="temp.groupId !== undefined && temp.groupId !== null ? [temp.groupId + ''] : []"
                :width="296"
                style="width: 160px"
                @change="collectGroupSelectChange"
              />
              <el-button class="editBtn" style="margin-left: 5px; margin-bottom: 0" @click="handleGroupCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </div>
          </FormItem>
          <FormItem>
            <el-checkbox v-model="temp.authorizedStatus" :disabled="temp.devType === 219" :true-label="1" :false-label="0" :label="$t('pages.enableServer')"/>
          </FormItem>
        </div>

        <!-- 智能备份文件服务器 -->
        <FormItem v-if="isSmartBackupFileServer" :label="$t('route.intelligentBackupServer')" prop="smartBackupId">
          <tree-select
            ref="smartBackupServerTree"
            :data="smartBackupServerTree"
            node-key="id"
            :width="296"
            style="width: 200px"
            :render-content="renderContent"
            :rewrite-node-click-fuc="true"
            :node-click-fuc="smartServerNodeClickFuc"
            @change="smartServerSelectChange"
          />
        </FormItem>

        <div class="container">
          <FormItem label="内网类型：">
            <div slot="label">
              {{ $t('pages.intranetType') }}
              <el-tooltip effect="dark" placement="top-start">
                <div slot="content">
                  {{ $t('pages.intranetTypeTip1') }}<br>
                  {{ $t('pages.intranetTypeTip2') }}<br>
                  {{ $t('pages.intranetTypeTip3') }}<br>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
            <el-select v-model="temp.intranetType" style="width: 200px" :disabled="!!dbInstallType" @change="intranetTypeChange">
              <el-option :key="0" :value="0" :label="$t('pages.truthIpAndPort')"/>
              <el-option :key="1" :value="1" :label="$t('pages.null')"/>
            </el-select>
          </FormItem>

          <FormItem v-if="temp.devType !== 219" :label="$t('pages.serverAccessIntranetIpv4')" prop="intranetIpv4" clearable>
            <el-select v-if="!temp.intranetType" v-model="temp.intranetIpv4" class="width" @change="validateFieldClick('intranetIpv4')">
              <el-option v-for="(ipv4, index) in intranetIpv4s" :key="index" :value="ipv4" :label="ipv4"/>
            </el-select>
            <el-input v-if="temp.intranetType" v-model="temp.intranetIpv4" v-trim class="width" clearable/>
          </FormItem>
          <FormItem v-if="temp.devType !== 219" :label="$t('pages.serverAccessIntranetIpv6')" prop="intranetIpv6">
            <el-select v-if="!temp.intranetType" v-model="temp.intranetIpv6" class="width" @change="validateFieldClick('intranetIpv6')">
              <el-option v-for="(ipv6, index) in intranetIpv6s" :key="index" :value="ipv6" :label="ipv6"/>
            </el-select>
            <el-input v-if="temp.intranetType" v-model="temp.intranetIpv6" v-trim class="width" clearable/>
          </FormItem>

          <!-- 若为数据库服务器时 -->
          <FormItem v-if="temp.devType === 219" :label="$t('pages.serverAccessIntranetIP')" prop="intranetIp">
            <el-select v-if="!temp.intranetType" v-model="temp.intranetIp" :disabled="!!dbInstallType" class="width" @change="validateFieldClick('intranetIp')">
              <el-option v-for="(ip, index) in intranetIps" :key="index" :value="ip" :label="ip"/>
            </el-select>
            <el-input v-if="temp.intranetType" v-model="temp.intranetIp" v-trim class="width" clearable/>
          </FormItem>

          <FormItem :label="$t('pages.serverAccessIntranetPort')" prop="intranetPort">
            <el-input v-model="temp.intranetPort" :disabled="!temp.intranetType" class="width" clearable/>
          </FormItem>

          <!-- 数据库服务器接入方式 -->
          <FormItem v-if="temp.devType === 219" :label="$t('pages.accessMode')">
            <el-tooltip effect="dark" placement="top-start" :disabled="!isEnglish() || !accessDisabled">
              <div slot="content">
                {{ getDictLabel(accessOptions, temp.accessMode) }}
              </div>
              <el-select v-model="temp.accessMode" :disabled="accessDisabled || !!isRefDb || dbIsBind" :placeholder="$t('text.select')" class="width">
                <el-option v-for="item in accessOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-tooltip>
          </FormItem>
        </div>

        <!-- 文件服务器扩展 -->
        <FormItem v-if="temp.devType === 11 || temp.devType === 172" :label="$t('pages.backupPrimaryPath')" prop="mainPath">
          <el-input v-model="temp.mainPath" v-trim :maxlength="255" :style="{ width: temp.osType === 1 && !viewDisk ? 'calc(550px - 50px)' : '550px' } "/>
          <el-button v-if="temp.osType === 1 && !viewDisk" type="primary" icon="el-icon-view" size="mini" style="margin-bottom: 0" @click="viewDiskInfo"/>
        </FormItem>
        <FormItem v-if="temp.devType === 11 && temp.osType === 1" :label="$t('pages.backupExpansionPath')" :tooltip-content="$t('pages.backupExpansionPathTip')" tooltip-placement="bottom-start">
          <tag ref="extPathTag" style="width: 550px;" :border="true" editable :list="temp.extPaths" :valid-rule="rules.pathRule" :limit-size="5" :input-width="536" input-length="255" @tagChange="tagChange"/>
        </FormItem>

        <!-- 磁盘信息 -->
        <disk-info v-if="temp.devType === 11 || temp.devType === 172" ref="diskInfo" :disk-list="disks" :os-type="temp.osType" :label-format="{ diskName: 'name', totalDiskSize: 'total', freeSize: 'free' }" @closeFuc="viewDisk = false"/>

        <!-- 数据库的映射服务器配置信息 -->
        <el-row v-if="temp.devType === 219 && !accessDisabled">
          <el-col :span="12">
            <FormItem>
              <el-checkbox v-model="isRefDb" :disabled="isRefDbDisabled" :true-label="1" :false-label="0" :label="$t('table.mappingServer')" @change="isRefDbChange"/>
            </FormItem>
          </el-col>
          <el-col v-if="isRefDb" :span="12">
            <FormItem label="分部数据库：" prop="srcDbId">
              <div slot="label">
                <span style="color: red;">*</span>
                {{ $t('pages.branchDatabase') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.branchDbTip') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </div>
              <el-select v-model="temp.srcDbId" class="width" @change="branchDbChange">
                <el-option v-for="(item, index) in branchDbServer" :key="index" :disabled="item.devId === temp.devId" :value="item.devId" :label="item.name"/>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>

        <!-- 外网配置 -->
        <div v-if="temp.devType !== 219">
          <FormItem label-width="45px">
            <el-checkbox v-model="enableExternalIp" :true-label="1" :false-label="0" :label="$t('pages.enableExternalIP')"/>
          </FormItem>
          <div v-if="enableExternalIp" class="container">
            <FormItem :label="$t('pages.externalIPv4')" prop="extranetIpv4" clearable>
              <el-input v-model="temp.extranetIpv4" v-trim maxlength="64" class="width" />
            </FormItem>
            <FormItem :label="$t('pages.externalIpv6')" prop="extranetIpv6">
              <el-input v-model="temp.extranetIpv6" v-trim maxlength="64" class="width" clearable/>
            </FormItem>
            <FormItem :label="$t('pages.externalPort')" prop="extranetPort">
              <el-input v-model="temp.extranetPort" v-trim class="width" clearable/>
            </FormItem>
          </div>
        </div>

      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="confirm(1)">
          {{ $t('pages.access') }}
        </el-button>
        <el-tooltip :content="$t('pages.serverAccessRejectTip')" placement="top">
          <el-button :loading="submitting" style="background: linear-gradient(#d7d7d7, #d30404)" @click="confirm(0)">
            {{ $t('pages.reject') }}
          </el-button>
        </el-tooltip>
        <el-button @click="close()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <db-ip-exception-dlg ref="dbIpExceptionDlg"/>
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="collectGroups"
      :edit-valid-func="getServerGroupByName"
      :add-func="createServerGroup"
      @addEnd="createGroupNode"
    />
  </div>
</template>

<script>

import { createServerGroup, getServerGroupByName, getServerGroupTree } from '@/api/system/deviceManage/serverGroup';
import { isIPv4, isIPv6, isPort } from '@/utils/validate';
import {
  approve, getLocalIp, getRelDbId,
  getSupportDevTypes,
  listCanBindBranchDbServer,
  listDevServer,
  supportDevTypeFormatter
} from '@/api/system/deviceManage/serverAccessApproval';
import { getDictLabel } from '@/utils/dictionary';
import DbIpExceptionDlg from './dbIpExceptionDlg'
import DiskInfo from '@/views/system/deviceManage/serverAccessApproval/config/DiskInfo';
import EditGroupDlg from '@/views/common/editGroupDlg'
import { getServerByDevId } from '@/api/system/deviceManage/backupServer';
import {
  createSmartBackupServerGroup,
  getSmartBackupServerGroupByName,
  getSmartBackupServerGroupTree
} from '@/api/system/deviceManage/smartBackupServer';
import { getServerTree } from '@/api/system/deviceManage/server';

export default {
  name: 'ServerApprovalDlg',
  components: { DiskInfo, DbIpExceptionDlg, EditGroupDlg },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {
      },
      defaultTemp: {
        id: null,
        devId: null,            //  设备编号
        devType: 0,             //  设备类型（0：所有服务器，6：采集服务器，11：文件服务器，12：检测服务器，219：数据库服务器, 0xAC/172：软件中心服务器）
        devVersion: '',         //  版本号
        createTime: null,       //  申请接入时间
        groupId: null,          //  采集分组
        devName: null,          // 设备名称
        remark: null,           //  备注
        intranetType: 0,        //  内网类型
        intranetIp: null,       //  IP（仅前端在审批数据库服务器时，用于暂存内网IP信息）
        intranetIpv4: null,     //  内网IPv4
        intranetIpv6: null,     //  内网IPv6
        intranetPort: null,     //  内网端口
        authorizedStatus: 0,    //  是否开启服务器（授权状态）
        approveResult: 0,       //  审批结果，0：待审批，1：使用新节点，2：使用旧节点，3：审批拒绝
        sysInfoData: null,      //  数据库信息   type包含数据库安装类型  0-内置数据库，1-外部数据库
        optInfoData: null,      //  网卡信息 包含MAC地址，ipv4，ipv6
        extranetIpv4: '',       //  外网IPv4
        extranetIpv6: '',       // 外网IPv6
        extranetPort: null,     // 外网端口
        mainPath: '',           //  备份主路径-文件服务器
        extPaths: [],           //  扩展备份路径-文件服务器
        accessMode: 0,          //  数据库服务器接入方式 0-总部接入，1-分布接入
        srcDbId: null,          // 数据库服务器-映射服务器devId
        sysmgrDbId: null,        //  采集服务器或检测服务器的主数据库Id
        pkgType: null,           //  安装包类型  1-DLP  2-文件朔源服务器  3-综合报表  4-加解密  5-智能备份 （不可变）
        smartBackupId: null,     //  智能备份服务器的设备Id
        approveType: null //  审批类型
      },
      rules: {
        devId: [
          { validator: this.devIdValidator, trigger: 'change' }
        ],
        devName: [
          { required: true, validator: this.devNameValidator, trigger: 'blur' }
        ],
        intranetIpv4: [
          { validator: this.intranetIpv4Validator, trigger: 'blur' }
        ],
        intranetIpv6: [
          { validator: this.intranetIpv6Validator, trigger: 'blur' }
        ],
        intranetIp: [
          { required: true, validator: this.intranetIpValidator, trigger: 'blur' }
        ],
        intranetPort: [
          { required: true, validator: this.portValidator, trigger: 'blur' }
        ],
        extranetIpv4: [
          { validator: this.extranetIpv4Validator, trigger: 'blur' }
        ],
        extranetIpv6: [
          { validator: this.extranetIpv6Validator, trigger: 'blur' }
        ],
        extranetPort: [
          { required: true, validator: this.portValidator, trigger: 'blur' }
        ],
        mainPath: [
          { required: true, validator: this.validateMainPath, trigger: 'blur' }
        ],
        pathRule: [
          { validator: this.validatePath, trigger: 'blur' }
        ],
        srcDbId: [
          { validator: this.validateSrcDbId, trigger: 'change' }
        ],
        sysmgrDbId: [
          { validator: this.sysmgrDbIdValidator, trigger: 'change' }
        ],
        groupId: [
          { required: true, validator: this.groupIdValidator, trigger: 'change' }
        ],
        remark: [
          { validator: this.remarkValidator, trigger: 'blur' }
        ],
        smartBackupId: [
          { required: true, validator: this.smartBackupIdValidator, trigger: 'change' }
        ]
      },
      dbInstallType: 0,     //  数据库安装类型 0-内部安装，1-外部安装
      intranetIpv4s: [],    //  网卡中的Ipv4地址
      intranetIpv6s: [],    //  网卡信息中的IPv6地址
      intranetIps: [],      //  所有IP地址信息，包括IPv4，IPv6
      disks: [],            //  磁盘信息 name: 磁盘名称，total：磁盘总容量，单位B，free：磁盘空余空间，单位B
      //  各服务器设备类型
      devTypes: getSupportDevTypes(),
      accessNode: 1,     //  接入节点   1-新节点，2-旧节点
      collectGroups: [],  //  采集分组
      enableExternalIp: 0,   //  是否开启外网IP
      exitsDevServers: [],     //  存储已存在的离线设备服务器信息

      viewDisk: false,   //  显示磁盘信息
      accessOptions: [
        { value: 0, label: this.$t('pages.intranetAccess') },
        { value: 1, label: this.$t('pages.internetAccess') }
      ],
      accessDisabled: !this.$store.getters.isMasterSubAble,   //  是否开启数据库服务器总分映射
      isRefDb: 0,     //  是否为映射服务器
      branchDbServer: [],    //  可绑定的分部数据库设备信息
      dbServers: [], //  在线数据库信息
      smartBackupServerTree: [],  //  指定分组下的智能备份服务器
      dbIsBind: false, //  旧节点数据库已绑定其他数据库（例如：总部数据库已分配分部数据库，分部数据库已映射总部数据库）
      dbNotBindTitle: '', // 无法绑定的提示信息
      dbExitsSameIpAndPort: false,   //  审批的数据库IP和端口是否已存在系统中
      dbExitsDevId: null,    //  审批的数据库IP和端口与系统中相同时，记录系统中该数据库的设备Id
      localIps: []  //  本地IP地址
    }
  },
  computed: {
    //  是否为文件服务器或软件中心服务器，且使用旧节点接入
    isFileServerOldNode() {
      return (this.temp.devType === 11 || this.temp.devType === 172) && this.accessNode === 2
    },
    //  检测服务器或采集服务器、智能备份服务器设置的主数据库的Ip地址为虚拟Ip地址，即为127.0.0.1
    sysmgrIsVirtualIp() {
      if (([6, 12, 186].includes(this.temp.devType)) && this.temp.sysmgrDbId != null) {
        const db = this.dbServers.find(t => t.devId === this.temp.sysmgrDbId);
        return !!db && !!db.intranetIp && ['127.0.0.1', '0:0:0:0:0:0:0:1'].includes(db.intranetIp.trim())
      }
      return false;
    },
    fileServerTitle() {
      return this.temp.devType === 11 ? this.$t('route.backupServer') : this.temp.devType === 172 ? this.$t('route.softwareServer') : ''
    },
    //  是否为智能备份的文件服务器
    isSmartBackupFileServer() {
      return this.temp.devType === 11 && this.temp.pkgType && this.temp.pkgType === 5;
    },
    //  映射服务器不可编辑
    isRefDbDisabled() {
      const dbServer = this.exitsDevServers.find(t => t.devId === this.temp.devId)
      return !!(this.dbIsBind && dbServer && dbServer.access);
    }
  },
  created() {
    this.getLocalIp();
  },
  activated() {
    if (([6, 12, 186].includes(this.temp.devType))) {
      this.loadDbServer().then(res => {
        this.$refs.dataForm && this.$refs.dataForm.validateField('sysmgrDbId')
      })
      //  加载分组信息
      if (this.temp.devType === 6) {
        this.loadCollectGroups();
      } else if (this.temp.devType === 186) {
        this.loadSmartBackupGroups()
      }
    }
  },
  methods: {
    getDictLabel,
    /**
     * 初始化数据
     */
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
      this.dbInstallType = 0
      this.intranetIpv4s = []
      this.intranetIpv6s = []
      this.intranetIps = []
      this.accessNode = 1
      this.collectGroups = []
      this.exitsDevServers = []
      this.enableExternalIp = 0
      this.disks = []
      this.viewDisk = false
      this.isRefDb = 0
      this.submitting = false;
      this.dbIsBind = false;
      this.dbNotBindTitle = ''
      this.smartBackupServerTree = []
      this.branchDbServer = []
      this.dbServers = []
      this.dbExitsSameIpAndPort = false
      this.dbExitsDevId = null
    },
    /**
     * 展示
     * @param row
     */
    async show(row) {
      this.initData();
      const pkgType = row.pkgType ? row.pkgType : null;
      await this.loadDevServerData(row.devType, pkgType);
      //  若为采集服务器或检测服务器
      if (([6, 12, 186].includes(row.devType))) {
        //  若为采集服务器时，加载采集服务器分组
        row.devType === 6 && await this.loadCollectGroups();
        //  加载策略库信息
        await this.loadDbServer()
      }
      //  若为数据库服务器
      if (row.devType === 219) {
        //  若为3-IP变更 或 6-MAC变更时
        const dbIds = row.approveType && [3, 6].includes(row.approveType) ? row.devId : null
        await this.listCanBindBranchDbServer(dbIds);
      } else if (row.devType === 11 && pkgType && pkgType === 5) {  //  智能备份文件服务器
        await this.loadBackupServer();
      } else if (row.devType === 186) {   //  智能备份服务器
        //  加载智能备份服务器分组
        await this.loadSmartBackupGroups();
      }
      this.formatRow(row);
      this.$nextTick(() => {
        this.$refs.dataForm && this.$refs.dataForm.clearValidate();
      })
      this.visible = true;
    },
    /**
     * 对初始数据格式化处理
     */
    formatRow: function(row) {
      this.temp = Object.assign(this.temp, row);
      //  设备名称默认置空
      this.temp.devName = ''
      //  当devId = 0 或者 当前devId不在已存在的设备库中时
      this.temp.devId = !this.temp.devId || (!this.exitsDevServers.find(t => t.devId === this.temp.devId)) ? null : this.temp.devId;
      //  数据库安装方式
      this.dbInstallType = this.temp.optInfoData && this.temp.optInfoData.dbInfo ? this.temp.optInfoData.dbInfo.method || 0 : this.dbInstallType
      if (this.temp.devType === 6) {  //  设置采集分组
        this.temp.groupId = '0'
      } else if (this.temp.devType === 186) {
        this.temp.groupId = null
      }
      //  服务器安装包类型
      this.temp.pkgType = this.temp.pkgType || null
      this.intranetIpv4s = []
      this.intranetIpv6s = []
      //  磁盘信息
      this.disks = []
      if (this.temp.sysInfoData) {
        if (this.temp.sysInfoData.adapters) {
          const ipv4s = []
          const ipv6s = []
          this.temp.sysInfoData.adapters.forEach(t => {
            t.ipv4 && ipv4s.push(...t.ipv4);
            t.ipv6 && ipv6s.push(...t.ipv6);
          })
          this.intranetIpv4s = ipv4s;
          this.intranetIpv6s = ipv6s;
        }
        //  解析磁盘信息
        if (this.temp.sysInfoData.disks) {
          this.disks = this.temp.sysInfoData.disks
        }
      }
      //  若审批类型为3-IP变更 或 4-MAC变更时，使用旧节点数据
      if (this.temp.devId && [3, 6].includes(this.temp.approveType)) {
        this.accessNode = 2;
        const devServer = this.exitsDevServers.find(t => t.devId === this.temp.devId)
        this.temp.devName = devServer.name
        //  授权状态
        this.temp.authorizedStatus = devServer.authorizedStatus
        this.temp.remark = devServer.remark
        if ([6, 12, 186].includes(this.temp.devType)) {
          //  采集、检测服务器、智能备份服务器时，设置策略库
          const sysmgrDbId = devServer.extraInfo ? devServer.extraInfo.sysmgrDbId : null
          this.temp.sysmgrDbId = sysmgrDbId && this.dbServers.find(t => t.devId === sysmgrDbId) ? sysmgrDbId : null;
          //  设置采集分组或智能备份服务器分组
          if ([6, 186].includes(this.temp.devType)) {
            this.temp.groupId = devServer.groupId !== undefined && devServer.groupId !== null ? devServer.groupId + '' : null
            const groupId = this.temp.groupId
            if (this.temp.groupId) {
              this.$nextTick(() => {
                this.$refs.treeSelect && this.$refs.treeSelect.checkSelectedNode([groupId])
              })
            }
          }
        } else if (this.temp.devType === 219) {
          //  设置接入方式
          this.temp.accessMode = devServer.access
          //  若旧节点为总部接入的数据库时，设置是否为映射数据库，若为分部接入数据库时，需要将接入方式设置为分部接入
          if (devServer.access === 0) {
            //  若为IP变更，且为映射数据库,额外查找该映射数据库已绑定的分部数据库信息
            const branchDb = this.branchDbServer.find(t => t.sourceId);
            if (branchDb) {
              this.temp.srcDbId = branchDb.devId;
              this.isRefDb = 1;
            }
          }
          this.$nextTick(() => {
            this.$refs.dataForm && this.$refs.dataForm.validateField(['devId'])
          })
        } else if ([11, 172].includes(this.temp.devType)) {
          //  设置文件服务器和软件中心服务器主备份路径，扩展备份路径 根据设备Id查找文件服务器的拓展信息（包括主备份路径，扩展备份路径）
          getServerByDevId(this.temp.devId).then(res => {
            if (res.data) {
              this.temp.mainPath = res.data.mainPath
              this.temp.extPaths = res.data.extPaths || []
              //  设置智能备份服务器的Id
              this.temp.smartBackupId = res.data.smartServerId || null
              if (this.temp.smartBackupId) {
                this.$nextTick(() => {
                  this.$refs.smartBackupServerTree && this.$refs.smartBackupServerTree.checkSelectedNode(['S' + this.temp.smartBackupId])
                })
              }
            }
          })
        }
        //  设置外网IPv4、IPv6、port
        this.temp.extranetIpv4 = devServer.internetIp || null
        this.temp.extranetIpv6 = devServer.internetIpv6 || null
        this.temp.extranetPort = devServer.internetPort || null
        if (this.temp.extranetIpv4 || this.temp.extranetIpv6) {
          this.enableExternalIp = 1
        }
      }

      if (this.temp.devType === 219) {
        //  若为数据库服务器，且为外部数据库时，Ip地址只允许使用intranet_ipv4或intranet_ipv6中的值，不再读取网卡中的ip地址信息
        if (this.dbInstallType) {
          this.intranetIpv4s = this.temp.intranetIpv4 ? [this.temp.intranetIpv4] : [];
          this.intranetIpv6s = this.temp.intranetIpv6 ? [this.temp.intranetIpv6] : [];
        }
        this.intranetIps.push(...this.intranetIpv4s)
        this.intranetIps.push(...this.intranetIpv6s)
        this.temp.intranetIp = null
        if (this.temp.intranetIpv4) {
          this.temp.intranetIp = this.temp.intranetIpv4
        } else if (this.temp.intranetIpv6) {
          this.temp.intranetIp = this.temp.intranetIpv6
        } else if (this.intranetIps.length > 0) {
          this.temp.intranetIp = this.intranetIps[0]
        }
        //  若为数据库服务器，授权状态固定为启用（目前数据库服务器尚不支持设置授权状态，所以默认启用）
        this.temp.authorizedStatus = 1

        //  审批接入时-新节点接入
        //  内置-数据库服务器新设备接入时，若库中已存在相同IP和端口的服务器时，只允许使用旧节点接入，（同一局域网内，IP+端口只能有一个设备）
        if (this.temp.approveType === 1 && !this.temp.devId) {
          const devServer = this.exitsDevServers.find(dev =>
            dev.intranetIp && this.validIPIsSame(dev.intranetIp, this.intranetIps) &&
            dev.intranetPort === this.temp.intranetPort
          )
          if (devServer) {
            this.accessNode = 2;
            this.dbExitsSameIpAndPort = true
            this.dbExitsDevId = devServer.devId
            //  外置数据库不自动设置旧节点数据
            if (this.dbInstallType === 0) {
              this.temp.devId = devServer.devId
              this.temp.devName = devServer.name
              this.temp.remark = devServer.remark
            }
          }
        }
      }
    },
    /**
     * 校验IP是否相同
     * @param dbIp  数据库中的IP
     * @param newDevIp  新设备接入的IP集
     */
    validIPIsSame(dbIp, newDevIps) {
      if (!dbIp || !newDevIps || !newDevIps.length) {
        return false;
      }
      let flag = false;
      if (newDevIps.includes(dbIp)) {
        flag = true;
      } else if (this.localIps.length > 0 && ['127.0.0.1', '0:0:0:0:0:0:0:1'].includes(dbIp)) {
        //  若数据库中的IP为127.0.0.1时，转换成控制台所在环境的本地IP地址
        flag = this.localIps.findIndex(localIp => newDevIps.includes(localIp)) > -1
      }
      return flag;
    },
    /**
     * 确认之前的格式化处理
     * @param temp
     */
    formatData(temp) {
      const row = JSON.parse(JSON.stringify(temp))
      //  多个扩展路径用|分隔开
      row.extPaths = row.extPaths ? row.extPaths.join('|') : '';

      //  若为数据库服务器，且数据库是外置数据库时，内网类型设置成0  (数据库服务器没有外网信息）
      if (row.devType === 219) {
        if (this.dbInstallType === 1) {
          row.intranetType = 0
        }
        //  根据输入的Ip地址填写到对应字段中
        if (isIPv4(row.intranetIp)) {
          row.intranetIpv4 = row.intranetIp
          row.intranetIpv6 = ''
        } else {
          row.intranetIpv6 = row.intranetIp
          row.intranetIpv4 = ''
        }
        row.intranetIp = null
      } else if (!this.enableExternalIp) {
        //  若未开启外网IP，外网IP地址和端口设置为空
        row.extranetIpv4 = null
        row.extranetIpv6 = null
        row.extranetPort = null
      }
      //  新节点接入时，devId设置为null，（不改变原有的devId值
      if (this.accessNode === 1) {
        row.devId = null
      }
      //  若未开启映射库服务器时，分部数据库为空
      if (!this.isRefDb) {
        row.srcDbId = null
      }
      return row;
    },
    /**
     * 接入前的校验，目前仅校验以下内容：
     * - 校验采集服务器、检测服务器、智能备份服务器是否选择策略库
     * - 校验策略库是否设置成127.0.0.1
     * - 数据库服务器接入，若使用新节点接入时，系统中已存在该IP和端口，则不允许接入（所有安装包类型，基础包和智能备份服务器包）
     * @return true: 校验通过  false：失败
     */
    validData: async function(temp) {
      //  若为检测或采集服务器时，校验策略库是否选中（解决可能存在sysmgrDbId为null无法触发校验的问题）
      if ([12, 6, 186].includes(temp.devType) && !temp.sysmgrDbId) {
        this.$message({
          type: 'error',
          message: this.$t('pages.adminDbNotNull'),
          duration: 2000
        })
        return false;
      }

      //  校验策略库是否设置成127.0.0.1了
      if (this.sysmgrIsVirtualIp) {
        this.$refs.dbIpExceptionDlg.show(temp.sysmgrDbId);
        return false;
      }
      //  新设备审批时
      if (temp.approveType === 1 && temp.devType === 219) {
        //  先使用离线的数据库信息
        // let devServer = this.exitsDevServers.find(dev => dev.intranetIp && this.intranetIps.includes(dev.intranetIp) && dev.intranetPort === temp.intranetPort)
        let devServer = this.exitsDevServers.find(dev =>
          dev.intranetIp && this.validIPIsSame(dev.intranetIp, this.intranetIps) &&
          dev.intranetPort === temp.intranetPort
        )
        //  若不存在，直接查询数据库
        if (!devServer) {
          const res = await listDevServer(219, 2, temp.pkgType)
          const dbServers = res.data || []
          devServer = dbServers.find(dev =>
            dev.intranetIp && this.validIPIsSame(dev.intranetIp, this.intranetIps) &&
            dev.intranetPort === temp.intranetPort
          )
          // devServer = dbServers.find(dev => dev.intranetIp && this.intranetIps.includes(dev.intranetIp) && dev.intranetPort === temp.intranetPort)
        }
        //   数据库中查找到相同IP和端口的数据，且本次审批接入选择  新节点，或旧节点但旧节点设备Id不相同
        if (devServer && (this.accessNode === 1 || temp.devId !== devServer.devId)) {
          this.$message({
            message: `新数据库的IP和端口和系统中设备编号为${devServer.devId}的数据库相同，不允许接入，请确保新数据库与系统中的IP和端口不一致！`,
            type: 'error',
            duration: 2000
          })
          return false;
        }
      }
      return true;
    },
    /**
     * 接入
     * @param status
     * 1: 审批通过
     * 0：拒绝
     */
    confirm(status) {
      if (status) {
        this.$refs.dataForm.validate(async(valid) => {
          if (valid) {
            if (!await this.validData(this.temp)) {
              return;
            }

            this.submitting = true
            //  文件服务器审批时，若使用旧节点接入，原文件数据需手动迁移到新文件服务器中，否则原文件数据无法被访问
            const message = !this.isFileServerOldNode ? this.$t('pages.serverAccessOldNodeConfirmTip') : "<span style='color:red'>" + this.$t('pages.serverAccessOldNodeTip', { devType: this.temp.devType === 172 ? this.$t('route.softwareServer') : this.$t('route.backupServer') }) + '</span>'
            this.$confirmBox(message, !this.isFileServerOldNode ? this.$t('text.prompt') : this.$t('text.warning'), {
              confirmButtonText: this.$t('pages.access'),
              type: 'warning',
              dangerouslyUseHTMLString: true
            }).then(() => {
              const rowTemp = this.formatData(this.temp);
              rowTemp.approveResult = this.accessNode;
              approve(rowTemp).then(res => {
                if (res.data === -1) {
                  this.$notify({
                    title: '失败',
                    message: '审批数据发生改变，请重新审批',
                    type: 'warning',
                    duration: 2000
                  })
                  this.submitting = false;
                  this.close();
                  this.$parent.handleFilter()
                  return;
                }
                this.$emit('submit', rowTemp);
                this.$notify({
                  title: this.$t('text.success'),
                  message: '接入成功',
                  type: 'success',
                  duration: 2000
                })
                this.submitting = false;
                this.close();
              }).catch(() => {
                this.submitting = false
              });
            }).catch(() => {
              this.submitting = false;
            })
          }
        });
      } else {
        this.$refs.dataForm.clearValidate()
        this.$confirmBox(this.$t('pages.confirmRejectDeviceAccess'), this.$t('text.prompt')).then(() => {
          this.submitting = true;
          const rowTemp = {
            devGuid: this.temp.devGuid,
            id: this.temp.id,
            approveResult: 3,
            devMac: this.temp.devMac,
            devType: this.temp.devType,
            devVersion: this.temp.devVersion,
            showVersion: this.temp.showVersion
          };
          approve(rowTemp).then(res => {
            if (res.data === -1) {
              this.$notify({
                title: '失败',
                message: '审批数据发生改变，请重新审批',
                type: 'warning',
                duration: 2000
              })
              this.submitting = false;
              this.close();
              this.$parent.handleFilter()
              return;
            }
            this.$emit('submit', rowTemp);
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.rejectSuccess'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false;
            this.close();
          })
        }).catch(() => {
          this.submitting = false;
        })
      }
    },
    close() {
      this.$refs.dataForm.clearValidate()
      this.visible = false;
    },
    /**
     * 设备类型格式化
     * @param devType
     */
    devTypeFormatter(devType) {
      return supportDevTypeFormatter(devType, this.temp.pkgType);
    },
    /**
     * 操作系统类型格式化
     * @param osType
     */
    osTypeFormatter(osType) {
      let result = ''
      if (osType) {
        switch (osType + '') {
          case '1': result = 'Windows'; break;
          case '2': result = 'Linux'; break;
        }
      }
      return result
    },
    /**
     * 加载已存在的离线设备服务器信息
     * @param devType   设备类型
     * @param pkgType   安装包类型
     */
    async loadDevServerData(devType, pkgType) {
      if (devType) {
        //  获取离线数据库服务器
        await listDevServer(devType, 0, pkgType).then(res => {
          this.exitsDevServers = res.data || []
        })
      }
    },
    /**
     * 获取可绑定的分部数据库设备信息
     */
    listCanBindBranchDbServer(dbIds) {
      return listCanBindBranchDbServer(dbIds).then(res => {
        this.branchDbServer = res.data || []
      })
    },
    /**
     * 加载数据库信息
     */
    loadDbServer() {
      return listDevServer(219, 2).then(res => {
        this.dbServers = res.data || []
      })
    },
    /**
     * 加载采集分组
     */
    loadCollectGroups() {
      this.collectGroups = [{ label: this.$t('pages.ungrouped'), id: 'G0', dataId: '0' }]
      return getServerGroupTree().then(respond => {
        this.collectGroups = [{ label: this.$t('pages.ungrouped'), id: 'G0', dataId: '0' }]
        respond.data.forEach(el => {
          this.collectGroups.push(el)
        })
      })
    },
    /**
     * 加载智能备份服务器分组
     */
    loadSmartBackupGroups() {
      this.collectGroups = []
      return getSmartBackupServerGroupTree().then(respond => {
        this.collectGroups = []
        respond.data.forEach(el => {
          this.collectGroups.push(el)
        })
      })
    },
    /**
     * 加载智能备份服务器
     */
    loadBackupServer() {
      return getServerTree({ devType: 186 }).then(res => {
        //  过滤掉非智能备份服务器的分组信息
        const datas = res.data || []
        this.smartBackupServerTree = datas.filter(data => data.oriData && data.oriData.type && data.oriData.type === 186)
      })
    },
    /**
     * 分组发生改变时
     */
    collectGroupSelectChange(data) {
      if (data !== undefined && data !== null && data !== '') {
        this.temp.groupId = data
      }
    },
    /**
     * 智能备份服务器
     * @param data
     */
    smartServerSelectChange(key, node) {
      if (node && node.type === 'S') {
        //  智能备份服务器Id
        this.temp.smartBackupId = node.dataId
      }
    },
    /**
     * 内网类型发生变更
     * @param value
     */
    intranetTypeChange(value) {
      if (this.intranetIpv4s.length > 0 && !this.intranetIpv4s.includes(this.temp.intranetIpv4)) {
        this.temp.intranetIpv4 = this.intranetIpv4s[0]
      }
      if (this.intranetIpv6s.length > 0 && !this.intranetIpv6s.includes(this.temp.intranetIpv6)) {
        this.temp.intranetIpv6 = this.intranetIpv6s[0]
      }
      this.$refs.dataForm.validateField(['intranetIpv4', 'intranetIpv6', 'intranetPort'])
    },
    /**
     * 旧节点校验
     * @param rule
     * @param value
     * @param callback
     */
    devIdValidator(rule, value, callback) {
      if (this.accessNode === 2 && !this.temp.devId) {
        callback(new Error(this.$t('pages.nodeNotNull') + ''));
      } else if (this.accessNode === 2 && this.temp.devType === 219) {
        getRelDbId(this.temp.devId).then(res => {
          this.setDbIsBind(!!res.data)
          callback();
        })
      } else {
        callback();
      }
    },
    /**
     * 设备名称校验
     * @param rule
     * @param value
     * @param callback
     */
    devNameValidator(rule, value, callback) {
      if (!value.length) {
        callback(new Error(this.$t('pages.devNameNotNull') + ''))
      }
      callback();
    },
    /**
     * 内网IPv4校验
     * @param rule
     * @param value
     * @param callback
     */
    intranetIpv4Validator(rule, value, callback) {
      if (!this.temp.intranetIpv4 && !this.temp.intranetIpv6) {
        callback(new Error(this.$t('pages.ipv4NotNull') + ''));
      } else if (this.temp.intranetIpv4 && !isIPv4(this.temp.intranetIpv4)) {
        callback(new Error(this.$t('pages.ipAddressFormatError') + ''));
      }
      callback();
    },
    /**
     * 内网IPv6校验
     * @param rule
     * @param value
     * @param callback
     */
    intranetIpv6Validator(rule, value, callback) {
      if (!this.temp.intranetIpv4 && !this.temp.intranetIpv6) {
        callback(new Error(this.$t('pages.ipv6NotNull') + ''));
      } else if (this.temp.intranetIpv6 && !isIPv6(this.temp.intranetIpv6)) {
        callback(new Error(this.$t('pages.ipAddressFormatError') + ''));
      }
      callback();
    },
    /**
     * 若为数据库服务器时，内网IP地址校验
     * @param rule
     * @param value
     * @param callback
     */
    intranetIpValidator(rule, value, callback) {
      if (!this.temp.intranetIp) {
        callback(new Error(this.$t('pages.ipNotNull') + ''));
      } else if (!isIPv4(this.temp.intranetIp) && !isIPv6(this.temp.intranetIp)) {
        callback(new Error(this.$t('pages.ipAddressFormatError') + ''));
      }
      callback();
    },
    /**
     * 内网端口校验
     * @param rule
     * @param value
     * @param callback
     */
    portValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.portNotNull') + ''))
      } else if (!isPort(value)) {
        callback(new Error(this.$t('pages.portFormatError') + ''))
      }
      callback();
    },
    /**
     * 外网IPv4校验
     * @param rule
     * @param value
     * @param callback
     */
    extranetIpv4Validator(rule, value, callback) {
      if (!this.temp.extranetIpv4 && !this.temp.extranetIpv6) {
        callback(new Error(this.$t('pages.extIpv4NotNull') + ''));
      }
      callback();
    },
    /**
     * 外网IPv6校验
     * @param rule
     * @param value
     * @param callback
     */
    extranetIpv6Validator(rule, value, callback) {
      if (!this.temp.extranetIpv4 && !this.temp.extranetIpv6) {
        callback(new Error(this.$t('pages.extIpv6NotNull') + ''));
      }
      callback();
    },
    /**
     * 校验备份主路径-文件服务器
     * @param rule
     * @param value
     * @param callback
     */
    validateMainPath(rule, value, callback) {
      let msg = ''
      const reg = /^[c-zC-Z]:\\([^/:*?"<>|])*$/
      let drive = ''
      //  获取输入值的盘符
      if (value) {
        drive = value.substr(0, 1).toLocaleUpperCase()
      }
      if (!value) {
        msg = this.$t('pages.validateFBackupPath')
      } else if (!reg.test(value) && this.temp.osType == 1) {
        if (!/^[c-zC-Z]:\\/.test(value)) {
          msg = this.$t('pages.validateFBackupPath_1')
        } else {
          msg = this.$t('pages.pathValidTip', { characters: '/ : * ? " < > |' })
        }
      } else if (this.temp.osType == 1 && !this.$refs.diskInfo.getDiskIds().includes(drive)) {
        msg = this.$t('pages.notExitsDriveTip', { drive })
      } else if (this.temp.devType === 11 && this.temp.osType == 1 && this.temp.extPaths.find(path => path.substr(0, 1).toLocaleUpperCase() === value.substr(0, 1).toLocaleUpperCase())) {  //  文件服务器时，校验主路径填写的信息是否已在扩展备份路径中
        msg = this.$t('pages.validPathSameDiskByWin')
      } else {
        msg = undefined
      }
      callback(msg)
    },
    /**
     * 校验扩展备份路径
     * @returns {boolean}
     */
    validatePath(rule, value, callback, index) {
      if (value) {
        let msg
        const mainPath = this.temp.mainPath
        if (value == mainPath) {
          msg = this.$t('pages.validSameFilePath')
        } else if (this.temp.osType == 1) {
          const drive = value.substr(0, 1).toLocaleUpperCase()
          if (!/^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(value)) {
            if (!/^[c-zC-Z]:\\/.test(value)) {
              msg = this.$t('pages.pathPrefixValidTip')
            } else {
              msg = this.$t('pages.pathValidTip', { characters: '/ : * ? " < > |' })
            }
          } else if (!this.$refs.diskInfo.getDiskIds().includes(drive)) {
            msg = this.$t('pages.notExitsDriveTip', { drive })
          } else {
            const compareList = [...this.temp.extPaths]
            mainPath && compareList.push(mainPath)
            const disk = value.substr(0, 1).toLowerCase()
            for (let i = 0; i < compareList.length; i++) {
              if (i !== index && /^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(compareList[i]) && compareList[i].substr(0, 1).toLowerCase() === disk) {
                msg = this.$t('pages.validPathSameDiskByWin')
                break
              }
            }
          }
        } else if (this.temp.osType == 2) {
          if (value.includes('|')) {
            msg = this.$t('pages.pathValidTip', { characters: '|' })
          } else {
            const takePathPrefix = str => {
              const splitIndex = str.indexOf('/', 1)
              const result = splitIndex < 0 ? str : str.substr(0, splitIndex)
              return (result[0] !== '/' ? '/' : '') + result
            }
            const compareList = [...this.temp.extPaths]
            mainPath && compareList.push(mainPath)
            const pathPrefix = takePathPrefix(value)
            for (let i = 0; i < compareList.length; i++) {
              if (i !== index && takePathPrefix(compareList[i]) === pathPrefix) {
                msg = this.$t('pages.validPathSameDiskByLinux')
                break
              }
            }
          }
        }
        msg && callback(msg)
      }
      callback()
    },
    /**
     * 映射数据库校验-数据库服务器
     * @param rule
     * @param value
     * @param callback
     */
    validateSrcDbId(rule, value, callback) {
      if (this.isRefDb && !value) {
        callback(new Error(this.$t('pages.branchDbNotNull') + ''));
      }
      callback();
    },
    /**
     * 采集、检测、智能备份服务器绑定的主数据库
     * @param rule
     * @param value
     * @param callback
     */
    sysmgrDbIdValidator(rule, value, callback) {
      if ([6, 12, 186].includes(this.temp.devType) && !value) {
        callback(new Error(this.$t('pages.adminDbNotNull') + ''));
      }
      callback();
    },
    /**
     * 采集-智能备份服务器分组不能为空
     * @param rule
     * @param value
     * @param callback
     */
    groupIdValidator(rule, value, callback) {
      if ([6, 186].includes(this.temp.devType) && (value === undefined || value === null)) {
        callback(new Error(this.$t('pages.accessGroupNotNull') + ''));
      }
      callback();
    },
    /**
     * 备注信息校验
     * @param rule
     * @param value
     * @param callback
     */
    remarkValidator(rule, value, callback) {
      if (value !== undefined && value !== null && value.length > 100) {
        callback(new Error('备注信息长度不能超过100个字'));
      }
      callback();
    },
    /**
     * 智能备份文件服务器校验
     * @param rule
     * @param value
     * @param callback
     */
    smartBackupIdValidator(rule, value, callback) {
      if (this.isSmartBackupFileServer && !value) {
        callback(new Error(this.$t('pages.smartBackupServerNotNull') + ''));
      }
      callback();
    },
    /**
     * 映射数据库开关
     */
    isRefDbChange(value) {
      if (value) {
        this.temp.accessMode = 0
      } else {
        this.temp.srcDbId = null
      }
    },
    /**
     * 显示磁盘信息
     */
    viewDiskInfo() {
      this.viewDisk = !this.viewDisk;
      if (this.viewDisk) {
        this.$refs.diskInfo.show();
      } else {
        this.$refs.diskInfo.close();
      }
    },
    /**
     * 前后数据库服务器
     */
    toDbServer() {
      this.$router.push({ path: '/system/deviceManage/DBServer', query: { tabName: 'DbInfo' }})
    },
    /**
     * 校验属性正确性
     * @param field
     */
    validateFieldClick(field) {
      this.$refs.dataForm && this.$refs.dataForm.validateField(field);
    },
    /**
     * 数据库服务器-映射服务器时，选择分部数据库时，设置备注信息
     */
    branchDbChange(value) {
      this.temp.remark = this.$t('pages.DBServerMapperBranchServerContent', { devId: this.temp.srcDbId });
    },
    /**
     * 旧节点发生改变时
     * @param devId
     */
    devIdChange(devId) {
      //  当旧节点设备Id和分部数据库设备Id相同时，清空分部数据库设备Id，（确保映射库设备Id不与分部数据库设备Id相同）
      if (this.temp.devType === 219 && this.temp.srcDbId && this.temp.devId && this.temp.srcDbId === this.temp.devId) {
        this.temp.srcDbId = null
      }
    },
    /**
     * 创建采集分组
     */
    handleGroupCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    createGroupNode(data) {
      this.collectGroups.push(this.dataToTreeNode(data))
      this.temp.groupId = data.id + ''
      this.$nextTick(() => {
        this.$refs.treeSelect && this.$refs.treeSelect.checkSelectedNode([data.id + ''])
      })
    },
    dataToTreeNode(data) {
      return {
        id: 'G' + data.id,
        type: 'G',
        dataId: data.id + '',
        parentId: 'G0',
        label: data.name
      }
    },
    setDbIsBind(flag) {
      this.dbIsBind = flag;
      if (flag) {
        const dbServer = this.exitsDevServers.find(t => t.devId === this.temp.devId);
        if (dbServer) {
          this.temp.accessMode = dbServer.access
          //  分部接入时，关闭映射数据库
          if (this.temp.accessMode) {
            this.isRefDb = false;
          }
          this.dbNotBindTitle = `注意：指定节点为${this.temp.accessMode ? '分部数据库已映射总部数据库' : '总部数据库已映射分部数据库'}，无法更改接入方式`
        }
      }
    },
    /**
     * 根据分组名称获取分组信息
     */
    getServerGroupByName(data) {
      return this.temp.devType === 6 ? getServerGroupByName(data) : getSmartBackupServerGroupByName(data);
    },
    /**
     * 创建分组信息
     */
    createServerGroup(data) {
      return this.temp.devType === 6 ? createServerGroup(data) : createSmartBackupServerGroup(data);
    },
    /**
     * 扩展备份路径发生改变
     */
    tagChange() {
      this.$nextTick(() => {
        this.$refs.dataForm.validateField('mainPath')
      })
    },
    renderContent(h, { node, data, store }) {
      const iconClass = this.getIconClass(node, data);
      return (
        <div class='custom-tree-node'>
          <svg-icon v-show={!!iconClass} icon-class={!iconClass ? '' : iconClass} />
          <span>{data.label}</span>
        </div>
      )
    },
    getIconClass(node, data) {
      if (data.type === 'G') {
        return 'dir'
      } else if (data.type === 'S') {
        if (data.oriData && data.oriData.access) {
          return 'branch'
        }
        if (this.refMap && this.refMap[data.dataId]) {
          // 说明此服务器为分部服务器：因为已经映射到总部
          return 'branch'
        }
        if (this.refDevIds && this.refDevIds.indexOf(Number.parseInt(data.dataId)) > -1) {
          // 说明此服务器为总部服务器：因为已经被分部映射
          return 'controller'
        }
        return 'server'
      }
      return null
    },
    //  单击智能备份服务器树   仅智能备份服务器节点可选中，智能备份服务器分组不支持选中
    smartServerNodeClickFuc(data, node, vm) {
      return data.type && data.type === 'S';
    },
    /**
     * 获取本地IP地址，包括IPv4和IPv6
     */
    getLocalIp() {
      getLocalIp().then(res => {
        this.localIps = res.data || []
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.width {
  width: 200px;
}
.container {
  display: grid;
  grid-template-columns: 50% 50%;
}
.editBtn{
  height: 30px;
  padding: 0 10px !important;
  float: right;
}
</style>
