<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <audit-log-exporter v-permission="'525'" :request="exportFunc"/>
        <div class="searchCon">
          <TimeQuery ref="time" @getTimeParams="getTimeParams"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="342"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('table.devType')">
                <el-select v-model="devTypes" multiple clearable class="multipleSelect" collapse-tags @change="devTypeChange">
                  <el-option v-for="item in devTypeOptions" :key="item.id" :value="item.id" :label="item.label"/>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.deviceNum')" prop="devIdLike" :tooltip-content="'设备编号：允许输入1~999999999的数字'">
                <el-input v-model.number="query.devIdLike" v-trim type="text" class="devIdStyle" clearable :max="999999999" @input="validateDevId"/>
              </FormItem>
              <FormItem :label="$t('table.accessMode')">
                <el-select v-model="accessCodes" multiple clearable class="multipleSelect" collapse-tags @change="accessModeChange">
                  <el-option v-for="(value, key) in accessModeOptions" :key="parseInt(key)" :value="parseInt(key)" :label="value"/>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.accessResult')">
                <el-select v-model="approveResults" multiple clearable class="multipleSelect" collapse-tags @change="approveResultChange">
                  <el-option v-for="(value, key) in approveResultOptions" :key="parseInt(key)" :value="parseInt(key)" :label="value"/>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="reSetCondition">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="false"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <log-details ref="logDetails" />
  </div>
</template>

<script>

import { getLogPage, getSupportDevTypes, logExport } from '@/api/system/deviceManage/serverAccessApproval';
import LogDetails from '@/views/system/deviceManage/serverAccessApproval/log/logDetails';

export default {
  name: 'ServerAccessApprovalLog',
  components: { LogDetails },
  props: {},
  data() {
    return {
      query: {},
      devTypes: [], //  待查询的设备类型
      accessCodes: [],  //  待查询的接入方式
      approveResults: [],  //  待查询的接入结果
      colModel: [
        { prop: 'createTime', label: 'serverAccessTime', fixedWidth: '200', sort: true },
        { prop: 'devId', label: 'deviceNum', width: '100' },
        { prop: 'devName', label: 'deviceName', width: '100' },
        { prop: 'devType', label: 'devType', width: '100', formatter: this.devTypeFormatter },
        { prop: 'showVersion', label: 'version', width: '100', formatter: this.devVersionFormatter },
        { prop: 'operationType', label: 'accessMode', width: '100', formatter: this.operationTypeFormatter },
        { prop: 'accessResult', label: 'accessResult', width: '100', formatter: this.accessResultFormatter },
        { prop: 'intranetIpv4', label: 'IPv4Addr', width: '120' },
        { prop: 'intranetIpv6', label: 'IPv6Addr', width: '120' },
        { prop: 'devMac', label: 'mac', width: '120' },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'details', click: this.handleDetails }
          ]
        }
      ],
      deleteable: false,
      //  支持的服务器设备类型
      devTypeOptions: getSupportDevTypes(),
      //  接入方式
      accessModeOptions: {
        // '1': this.$t('pages.autoAccess'),    //  自动接入
        '2': this.$t('pages.approvalAccess'),    //  审批接入
        '3': this.$t('pages.changeAutoAccess'),    //  变更自动接入
        '4': this.$t('pages.oldDevUpgradesAccess'),    //  旧设备升级接入
        '5': this.$t('pages.portAutoChange')  //  端口自动变更
      },
      approveResultOptions: {
        '1': this.$t('pages.useNewNode'),   //  新节点接入
        '2': this.$t('pages.useOldNode'),   //  旧节点接入
        '3': this.$t('pages.rejectionAccess')   //  拒绝接入
      },
      sort: {     //  当前查询的排序顺序
        sortName: '',
        sortOrder: ''
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      this.sort.sortName = option.sortName || null
      this.sort.sortOrder = option.sortOrder || null
      const searchQuery = Object.assign({}, this.query, option)
      return getLogPage(searchQuery)
    },
    /**
     * 查询
     */
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    /**
     * 刷新
     */
    refresh() {
      const date = this.formatDate(new Date());
      this.$refs.time.setDate(date);
      this.reSetCondition()
      this.handleFilter();
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    /**
     * 日期格式成 yyyy-mm-dd
     * @param date
     * @returns {string}
     */
    formatDate(date) {
      // 提取年、月、日
      const year = date.getFullYear();
      let month = date.getMonth() + 1; // 月份是从0开始的
      let day = date.getDate();

      // 填充月份和日期，确保它们是两位数
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;

      return `${year}-${month}-${day}`;
    },
    /**
     * 导出数据
     * @param exportType
     * @returns {AxiosPromise}
     */
    exportFunc(exportType) {
      return logExport({ exportType, ...this.query, ...this.sort })
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    /**
     * 查看详情
     * @param row
     */
    handleDetails(row) {
      this.$refs.logDetails.show(JSON.parse(JSON.stringify(row)));
    },
    /**
     * 设备类型格式化
     * @param row
     * @param data
     */
    devTypeFormatter(row, data) {
      const devType = this.devTypeOptions.find(data => data.id === row.devType);
      if (devType) {
        return devType.label;
      }
      return null;
    },
    /**
     * 服务器版本号
     * showVersion：表示展示的版本号
     * devVersion：内置的版本号
     * 添加showVersion的目的：
     *    兼容新华三的安装包和我们内置的版本号不一致问题，新增showVersion表示展示的版本号，普通包showVersion展示内置版本号，新华三的包展示新华三的版本号
     * @param row
     * @param data
     * @returns {string|default.methods.temp.devVersion|*}
     */
    devVersionFormatter: function(row, data) {
      return !row.showVersion ? row.devVersion : row.showVersion
    },
    /**
     * 接入方式
     * @param row
     * @param data
     */
    operationTypeFormatter: function(row, data) {
      return data ? this.accessModeOptions[data + ''] : null;
    },
    /**
     * 接入结果
     * @param row
     * @param data
     */
    accessResultFormatter: function(row, data) {
      return data ? this.approveResultOptions[data + ''] : null;
    },
    /**
     * 重置高级搜索条件
     */
    reSetCondition() {
      this.devTypes = []
      this.accessCodes = []
      this.approveResults = []
      this.query.devTypes = ''
      this.$set(this.query, 'devIdLike', null)
      this.query.accessCodes = ''
      this.query.approveResults = ''
    },
    devTypeChange(value) {
      this.query.devTypes = this.devTypes.join(',')
    },
    accessModeChange(value) {
      this.query.accessCodes = this.accessCodes.join(',')
    },
    approveResultChange(value) {
      this.query.approveResults = this.approveResults.join(',')
    },
    validateDevId() {
      if (this.query.devIdLike !== undefined && this.query.devIdLike !== null) {
        if (!Number.isInteger(this.query.devIdLike) || parseInt(this.query.devIdLike) <= 0) {
          this.query.devIdLike = null;
        } else if (parseInt(this.query.devIdLike) > 999999999) {
          this.query.devIdLike = 999999999
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.devIdStyle {
  >>>.el-input .el-input__inner {
    text-align: left;
  }
}

</style>
