<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="'接入审批详情'"
    :visible.sync="visible"
    width="750px"
    @close="close"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.deviceNum')">
          {{ rowDetail.devId }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.deviceName')">
          {{ rowDetail.devName }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.devType')">
          {{ $parent.devTypeFormatter(rowDetail, rowDetail.devType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.version')">
          {{ $parent.devVersionFormatter(rowDetail, rowDetail.devVersion) }}
        </el-descriptions-item>
        <el-descriptions-item v-if="[6, 186].includes(rowDetail.devType)" :label="$t('table.group')">
          {{ collectGroupFormatter(rowDetail) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.intranetPort')">
          {{ rowDetail.intranetPort }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.IPv4Addr')">
          {{ rowDetail.intranetIpv4 }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.IPv6Addr')">
          {{ rowDetail.intranetIpv6 }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.macAddr')">
          {{ rowDetail.devMac }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.accessMode')">
          {{ $parent.operationTypeFormatter(rowDetail, rowDetail.operationType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.enableServer')">
          {{ authorizedStatusFormatter(rowDetail.authorizedStatus) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.accessResult')">
          {{ $parent.accessResultFormatter(rowDetail, rowDetail.accessResult) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.serverAccessTime')">
          {{ rowDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.remark')">
          {{ rowDetail.remark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close()">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { getServerGroupTree } from '@/api/system/deviceManage/serverGroup';
import { getSupportDevTypes } from '@/api/system/deviceManage/serverAccessApproval';
import { getSmartBackupServerGroupTree } from '@/api/system/deviceManage/smartBackupServer';

export default {
  name: 'LogDetails',
  components: {},
  data() {
    return {
      visible: false,
      submitting: false,
      rowDetail: {},
      devTypes: getSupportDevTypes()
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.rowDetail = {};
    },
    /**
     * 展示
     * @param row
     */
    async show(row) {
      this.initData();
      this.rowDetail = row;
      //  若为采集服务器时，加载采集服务器分组
      if (this.rowDetail.devType === 6) {
        await this.loadCollectGroups();
      } else if (this.rowDetail.devType === 186) {
        await this.loadSmartBackupGroups();
      }
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    /**
     * 加载采集分组
     */
    loadCollectGroups() {
      this.collectGroups = [{ label: this.$t('pages.ungrouped'), id: 'G0', dataId: '0' }]
      return getServerGroupTree().then(respond => {
        this.collectGroups = [{ label: this.$t('pages.ungrouped'), id: 'G0', dataId: '0' }]
        respond.data.forEach(el => {
          this.collectGroups.push(el)
        })
      })
    },
    /**
     * 加载智能备份服务器分组
     */
    loadSmartBackupGroups() {
      this.collectGroups = []
      return getSmartBackupServerGroupTree().then(respond => {
        this.collectGroups = []
        respond.data.forEach(el => {
          this.collectGroups.push(el)
        })
      })
    },
    /**
     * 采集分组格式化
     * @param row
     * @returns {null|{dataId: string, label: *, id: string}}
     */
    collectGroupFormatter(row) {
      const list = this.collectGroups;
      for (let i = 0; i < list.length; i++) {
        const data = list[i];
        if (data.dataId == row.groupId) {
          return data.label;
        }
      }
      return null;
    },
    /**
     * 是否启用服务器
     * @param authorizedStatus
     * @returns {string}
     */
    authorizedStatusFormatter(authorizedStatus) {
      return authorizedStatus ? '是' : '否';
    }
  }
}
</script>

<style scoped>
.width {
  width: 200px;
}
.container {
  display: grid;
  grid-template-columns: 50% 50%;
}
</style>
