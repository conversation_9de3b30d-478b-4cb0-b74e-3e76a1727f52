<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <TimeQuery ref="time" @getTimeParams="getTimeParams"/>
        <el-select v-model="devTypes" multiple clearable class="multipleSelect" :placeholder="$t('table.devType')" collapse-tags @change="devTypeChange">
          <el-option v-for="item in devTypeOptions" :key="item.id" :value="item.id" :label="item.label"/>
        </el-select>
        <label style="margin-left: 10px">{{ $t('table.ipAddr') }}：</label>
        <el-input v-model="query.ip" v-trim style="width: 150px;" clearable/>

        <label style="margin-left: 10px">{{ $t('table.mac') }}：</label>
        <el-input v-model="query.devMac" v-trim style="width: 150px;" clearable/>

        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
      </div>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="false"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
  </div>
</template>

<script>

import { getLoginLogPage, getSupportDevTypes } from '@/api/system/deviceManage/serverAccessApproval';

export default {
  name: 'ServerLoginLog',
  components: {},
  props: {},
  data() {
    return {
      query: {},
      colModel: [
        { prop: 'createTime', label: 'loginTime', fixedWidth: '200', sort: true },
        { prop: 'devId', label: 'deviceNum', width: '100' },
        { prop: 'devType', label: 'devType', width: '100', formatter: this.devTypeFormatter },
        { prop: 'showVersion', label: 'version', width: '100', formatter: this.devVersionFormatter },
        { prop: 'intranetIpv4', label: 'IPv4Addr', width: '120' },
        { prop: 'intranetIpv6', label: 'IPv6Addr', width: '120' },
        { prop: 'devMac', label: 'mac', width: '120' },
        { prop: 'intranetPort', label: 'port', width: '120' }
      ],
      deleteable: false,
      //  支持的服务器设备类型
      devTypeOptions: getSupportDevTypes(),
      devTypes: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getLoginLogPage(searchQuery)
    },
    /**
     * 查询
     */
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    /**
     * 刷新
     */
    refresh() {
      this.query = {}
      const date = this.formatDate(new Date());
      this.$refs.time.setDate(date);
      this.devTypes = []
      this.handleFilter();
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    /**
     * 日期格式成 yyyy-mm-dd
     * @param date
     * @returns {string}
     */
    formatDate(date) {
      // 提取年、月、日
      const year = date.getFullYear();
      let month = date.getMonth() + 1; // 月份是从0开始的
      let day = date.getDate();

      // 填充月份和日期，确保它们是两位数
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;

      return `${year}-${month}-${day}`;
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    /**
     * 设备类型格式化
     * @param row
     * @param data
     */
    devTypeFormatter(row, data) {
      const devType = this.devTypeOptions.find(data => data.id === row.devType);
      if (devType) {
        return devType.label;
      }
      return null;
    },
    /**
     * 服务器版本号
     * showVersion：表示展示的版本号
     * devVersion：内置的版本号
     * 添加showVersion的目的：
     *    兼容新华三的安装包和我们内置的版本号不一致问题，新增showVersion表示展示的版本号，普通包showVersion展示内置版本号，新华三的包展示新华三的版本号
     * @param row
     * @param data
     * @returns {string|default.methods.temp.devVersion|*}
     */
    devVersionFormatter: function(row, data) {
      return !row.showVersion ? row.devVersion : row.showVersion
    },
    devTypeChange(value) {
      this.query.devTypes = this.devTypes.join(',')
    }
  }
}
</script>

<style scoped>

</style>
