<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabChange">
      <el-tab-pane :label="$t('route.serverAccessApproval')" name="config">
        <Config ref="config"/>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('route.serverAccessLog')" name="log">
        <Log ref="log"/>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.accessDevice')" name="mappingLog">
        <MappingLog ref="mappingLog"/>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.serverLoginLog')" name="serverLoginLog">
        <ServerLoginLog ref="serverLoginLog"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Config from './config'
import Log from './log'
import MappingLog from './mappingLog'
import ServerLoginLog from '@/views/system/deviceManage/serverAccessApproval/serverLoginLog';
export default {
  name: 'ServerAccessApproval',
  components: { ServerLoginLog, Config, Log, MappingLog },
  props: {
    tabName: { type: String, default: 'config' }
  },
  data() {
    return {
      activeName: this.tabName
    }
  },
  watch: {
    '$store.state.commonData.notice.serverAccessApproval'(val) {
      this.activeName = 'config'
    }
  },
  created() {
  },
  methods: {
    tabChange() {
      this.$refs[this.activeName].handleFilter();
    }
  }
}
</script>

<style scoped>

</style>
