<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-select v-model="devTypes" multiple clearable class="multipleSelect" :placeholder="$t('table.devType')" collapse-tags @change="devTypeChange">
          <el-option v-for="item in devTypeOptions" :key="item.id" :value="item.id" :label="item.label"/>
        </el-select>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleDeviceMgr">
          {{ $t('pages.deviceMgr') }}
        </el-button>
      </div>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'devId', order: 'ascending' }"
        :multi-select="false"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <server-mgr-dlg ref="serverMgrDlg" :default-expanded-keys="['-2']" @submitEnd="refresh"/>
  </div>
</template>

<script>
import ServerMgrDlg from '@/views/system/deviceManage/serverMgrDlg'
import { getMappingPage, getSupportDevTypes } from '@/api/system/deviceManage/serverAccessApproval';

export default {
  name: 'ServerAccessMappingLog',
  components: { ServerMgrDlg },
  props: {},
  data() {
    return {
      query: {
        devTypes: ''
      },
      colModel: [
        { label: 'type', fixedWidth: '55', fixed: true, iconFormatter: this.isBranch },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: true },
        { prop: 'devName', label: 'deviceName', width: '150' },
        { prop: 'devType', label: 'devType', width: '100', formatter: this.devTypeFormatter },
        { prop: 'intranetIpv4', label: 'IPv4Addr', width: '120' },
        { prop: 'intranetIpv6', label: 'IPv6Addr', width: '120' },
        { prop: 'devMac', label: 'mac', width: '120' },
        { prop: 'showVersion', label: 'version', width: '100', formatter: this.devVersionFormatter },
        { prop: 'createTime', label: 'serverAccessTime', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'maintenance', disabledFormatter: this.toLikeDisabledFormatter, click: this.toLike }
          ]
        }
      ],
      deleteable: false,
      //  支持的服务器设备类型
      devTypeOptions: getSupportDevTypes(),
      devTypes: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getMappingPage(searchQuery)
    },
    /**
     * 查询
     */
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    /**
     * 刷新
     */
    refresh() {
      this.query = {}
      this.devTypes = []
      this.handleFilter();
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    /**
     * 查看详情
     * @param row
     */
    handleDetails(row) {
      this.$refs.logDetails.show(JSON.parse(JSON.stringify(row)));
    },
    /**
     * 设备类型格式化
     * @param row
     * @param data
     */
    devTypeFormatter(row, data) {
      const devType = this.devTypeOptions.find(data => data.id === row.devType);
      if (devType) {
        return devType.label;
      }
      return null;
    },
    /**
     * IP地址格式化
     * @param row
     * @param data
     * @returns {null|*}
     */
    ipFormatter: function(row, data) {
      if (row.intranetIpv4) {
        return row.intranetIpv4
      }
      if (row.intranetIpv6) {
        return row.intranetIpv6;
      }
      return null;
    },
    isBranch: function(row) {
      const online = row.onlineStatus == 1
      const style = online ? 'color: green' : 'color: #888'
      const labelPre = (online ? this.$t('pages.online') : this.$t('pages.offline')) + '\r\n'
      return row.isBranch ? { class: 'branch', title: labelPre + this.$t('pages.branchServer'), style } : row.syncType ? { class: 'controller', title: labelPre + this.$t('pages.headquartersServer'), style } : { class: 'server', title: labelPre + this.$t('pages.commonServer'), style }
    },
    toLikeDisabledFormatter(row) {
      const devAuths = {
        '6': 'A42', //  采集服务器
        '11': 'A43',  //  文件服务器
        '12': 'A48',  //  检测服务器
        '172': 'A4K', //  软件中心服务器
        '219': 'A46', //  数据库服务器
        '11-5': 'A4L', //  智能备份文件服务器
        '186': 'A4L'    //  智能备份服务器
      }
      let devType = row.devType
      if (devType === 11 && row.pkgType && row.pkgType === 5) {
        devType = '11-5'
      }
      return !this.hasPermission(devAuths[devType + ''])
    },
    /**
     * 服务器版本号
     * showVersion：表示展示的版本号
     * devVersion：内置的版本号
     * 添加showVersion的目的：
     *    兼容新华三的安装包和我们内置的版本号不一致问题，新增showVersion表示展示的版本号，普通包showVersion展示内置版本号，新华三的包展示新华三的版本号
     * @param row
     * @param data
     * @returns {string|default.methods.temp.devVersion|*}
     */
    devVersionFormatter: function(row, data) {
      return !row.showVersion ? row.devVersion : row.showVersion
    },
    /**
     * 跳转到各设备对应页面
     * @param row
     */
    async toLike(row) {
      const devLinks = {
        '6': '/system/deviceManage/daqServer', //  采集服务器
        '11': '/system/deviceManage/backupServer', //  文件服务器
        '12': '/system/deviceManage/DetectionServer',  //  检测服务器
        '172': '/system/deviceManage/softwareServer', //  软件中心服务器
        '219': '/system/deviceManage/DBServer', //  数据库服务器
        '11-5': '/system/deviceManage/smartBackupServer', //  智能备份文件服务器
        '186': '/system/deviceManage/smartBackupServer'   //  智能备份服务器
      }
      let devType = row.devType
      if (devType) {
        const query = { name: row.devName }
        if (devType == 219) {
          query.tabName = 'DbInfo'
        }
        //  若为智能备份文件服务器时，跳转到智能备份服务器界面
        if (devType === 11 && row.pkgType && row.pkgType === 5) {
          devType = '11-5'
          //  智能备份文件服务器Id
          query.fileServerId = row.devId
        }
        this.$router.push({ path: devLinks[devType + ''], query })
      }
    },
    devTypeChange(value) {
      this.query.devTypes = this.devTypes.join(',')
    },
    handleDeviceMgr() {
      this.$refs['serverMgrDlg'].show()
    }
  }
}
</script>

<style scoped>

</style>
