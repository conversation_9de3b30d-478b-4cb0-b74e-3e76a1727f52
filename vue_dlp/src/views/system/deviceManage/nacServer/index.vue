<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createDevice') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delDevice') }}
        </el-button>
        <el-button :disabled="!only" size="mini" @click="handleConfigDept">
          {{ $t('pages.configureTerminal') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.serverName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.deviceNum')" prop="devId">
                <el-input v-model.number="query.devId" v-trim clearable :maxlength="4" :placeholder="this.$t('pages.serverLibrary_text5')" @input="number('devId')"/>
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
                <el-input v-model="query.intranetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
                <el-input v-model="query.intranetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
                <el-input v-model.number="query.intranetPort" v-trim maxlength="5" clearable :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('intranetPort')"/>
              </FormItem>
              <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
                <el-input v-model="query.internetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIp">
                <el-input v-model="query.internetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetPort')" prop="internetPort">
                <el-input v-model.number="query.internetPort" v-trim maxlength="5" clearable :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('internetPort')"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="serverTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <edit-nac-server ref="editNacServer" @submitEnd="handleFilter"></edit-nac-server>
    <config-dept ref="configDept" @submitEnd="handleFilter"></config-dept>
  </div>
</template>

<script>
import EditNacServer from './editNacServer'
import ConfigDept from './configDept'
import { getServerPage, deleteServer, unbindNacServer } from '@/api/system/deviceManage/nacServer'

export default {
  name: 'NacServer',
  components: { EditNacServer, ConfigDept },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { prop: 'name', label: 'serverName', width: '120', fixed: true, sort: true },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: true },
        { prop: 'intranetIp', label: this.$t('pages.intranetIPV4Address'), width: '130', sort: true },
        { prop: 'intranetIpv6', label: this.$t('pages.intranetIPV6Address'), width: '130', sort: true },
        { prop: 'intranetPort', label: this.$t('pages.intranetPort'), width: '100', sort: true },
        { prop: 'internetIp', label: this.$t('pages.internetIPV4Address'), width: '130', sort: true },
        { prop: 'internetIpv6', label: this.$t('pages.internetIPV6Address'), width: '130', sort: true },
        { prop: 'internetPort', label: 'internetPort', width: '100', sort: true },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        intranetIp: '',
        intranetIpv6: '',
        intranetPort: undefined,
        internetIp: '',
        internetIpv6: '',
        internetPort: undefined,
        devId: undefined
      },
      showTree: false,
      deleteable: false,
      only: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    filterKey() {
      return this.tempG.id
    }
  },
  created() {
  },
  activated() {
    this.handleFilter()
  },
  methods: {
    filterNodeMethod(value, data, node) {
      // 过滤节点及其子节点
      node.visible = node.key != this.filterKey && node.parent.visible
      return node.visible
    },
    parentIdSelectChange(data) {
      if (data && data !== this.tempG.id) {
        this.tempG.parentId = data
      }
    },
    terminalSelect() {
      return this.$refs['terminalGridSelect']
    },
    handleCreate() {
      this.$refs.editNacServer.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.editNacServer.handleUpdate(row)
    },
    handleConfigDept() {
      this.$refs.configDept.show(this.gridTable.getSelectedDatas());
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerPage(searchQuery)
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
      this.only = val.length === 1
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      // '确认要删除勾选的记录？'
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg2'), this.$t('text.prompt')).then(() => {
        unbindNacServer({ ids: toDeleteIds.join(',') }).then(respond => {
          console.log('respond', respond)
          // const msg1 = '网络准入服务器删除:成功('.concat(respond.data.success).concat('),').concat('失败(').concat(respond.data.fail).concat(') ')
          const msg1 = this.$t('pages.deleteNacServerMgs1', { success: respond.data.success, fail: respond.data.fail })
          if (respond.data.fail > 0) {
            // const msg2 = ' 失败原因：IP地址不可访问 或 网络准入服务器不存在 或 网络准入服务器执行失败. '
            // const msg3 = ' 是否直接删除？'
            const msg2 = this.$t('pages.deleteNacServerMgs2')
            const msg3 = this.$t('pages.deleteNacServerMgs3')
            // msg1 = msg1.concat(msg2).concat(msg3)
            // 把写的提示信心需要换行的地方分成数组 confirmText
            const confirmText = [msg1, msg2, msg3]
            const newDatas = []
            const h = this.$createElement
            for (const i in confirmText) {
              newDatas.push(h('p', null, confirmText[i]))
            }
            this.$confirmBox(h('div', null, newDatas), this.$t('text.prompt')).then(() => {
              this.handleDeleteServerApi(toDeleteIds)
            }).catch(() => {})
          } else {
            // msg = msg.concat(' 是否继续执行删除？')
            this.handleDeleteServerApi(toDeleteIds)
          }
        }).catch(() => {})
      })
    },
    handleDeleteServerApi(toDeleteIds) {
      deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
        this.gridTable.deleteRowData(toDeleteIds)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.deleteSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.intranetIp = ''
      this.query.intranetIpv6 = ''
      this.query.intranetPort = undefined
      this.query.internetIp = ''
      this.query.internetIpv6 = ''
      this.query.internetPort = undefined
      this.query.devId = undefined
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card{
    position: relative;
    >>>.el-card__header{
      padding: 10px 20px;
    }
  }
  .btn-box{
    position: absolute;
    top: 5px;
    right: 20px;
  }

</style>
