<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 500px; margin-left:25px;">
        <FormItem :label="$t('pages.serverName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem v-show="dialogStatus==='update'" :label="$t('pages.deviceNum')" prop="devId">
          <el-input v-model="temp.devId" :maxlength="60" :disabled="true"/>
        </FormItem>
        <input type="text" class="autocomplete">
        <input type="password" class="autocomplete">
        <!--        <FormItem :label="$t('pages.devPassword')" prop="accessPwd">-->
        <!--          <el-input v-model="temp.accessPwd" maxlength="20" type="password" show-password />-->
        <!--        </FormItem>-->
        <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
          <el-input v-model="temp.intranetIp" maxlength="64" />
        </FormItem>
        <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
          <el-input v-model="temp.intranetIpv6" maxlength="64" />
        </FormItem>
        <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
          <el-input v-model="temp.intranetPort" maxlength="5" />
        </FormItem>
        <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
          <el-input v-model="temp.internetIp" maxlength="64" />
        </FormItem>
        <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
          <el-input v-model="temp.internetIpv6" maxlength="64" />
        </FormItem>
        <FormItem :label="$t('pages.internetPort')" prop="internetPortVal">
          <el-input v-model="temp.internetPortVal" maxlength="5" />
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createServer, getServerByName, updateServer } from '@/api/system/deviceManage/nacServer';
import { isIPv4, isIPv6 } from '@/utils/validate'

export default {
  name: 'EditNacServer',
  data() {
    return {
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.validateUniqueName, trigger: 'blur' }
        ],
        accessPwd: [
          { required: true, message: this.$t('pages.validateMsg_enterDevPassword'), trigger: 'blur' },
          { validator: this.validatePassword, trigger: 'blur' }
        ],
        intranetIp: [
          { validator: this.validateIp, trigger: 'blur' },
          { validator: this.ipv4Rule, trigger: 'blur' }
        ],
        intranetIpv6: [
          { validator: this.validateIp, trigger: 'blur' },
          { validator: this.ipv6Rule, trigger: 'blur' }
        ],
        internetIp: [
          { validator: this.ipv4Rule, trigger: 'blur' }
        ],
        internetIpv6: [
          { validator: this.ipv6Rule, trigger: 'blur' }
        ],
        intranetPort: [
          { required: true, message: this.$t('pages.validateMsg_enterPort'), trigger: 'blur' }, // 暂时不需要，设置为非必填
          { validator: this.validatePort, trigger: 'blur' }
        ],
        internetPortVal: [
          { validator: this.validatePort, trigger: 'blur' }
        ]
      },
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        pwd: '',
        accessPwd: '',
        devId: undefined,
        intranetIp: '',
        intranetIpv6: undefined,
        internetIp: '',
        internetIpv6: undefined,
        intranetPort: 7000,
        internetPortVal: '',
        remark: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.updateNacServer'),
        create: this.$t('pages.addNacServer')
      },
      submitting: false
    }
  },
  computed: {
    groupTree() {
      return this.$refs['groupTree']
    }
  },
  watch: {},
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.editable = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      // this.temp.intranetIpv6 = undefined
      // this.temp.internetIpv6 = undefined
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 表单验证
     */
    validateUniqueName(rule, value, callback) {
      getServerByName({
        name: value
      }).then(respond => {
        const server = respond.data
        if (server && server.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    validateIPFormat(rule, value, callback) {
      if (value) {
        if (isIPv4(value)) {
          callback()
        } else {
          callback(new Error(this.$t('pages.validateFile_6')))
        }
      } else {
        callback()
      }
    },
    validatePort(rule, value, callback) {
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (value) {
        if (!number.test(value)) {
          callback(new Error(this.$t('pages.validateNaturalTree')))
        } else if (value < 0 || value > 65535) {
          callback(new Error(this.$t('pages.validatePortRange')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateConnections(rule, value, callback) {
      if (rule.maxConnections) {
        if (rule.maxConnections < rule.ratedConnections) {
          callback(new Error(this.$t('pages.validateCollect_1')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateIPNum(rule, value, callback) {
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (!number.test(value) || value < 0 || value > 255) {
        callback(new Error(this.$t('pages.validateCollect_2')))
      } else {
        callback()
      }
    },
    validatePassword(rule, value, callback) {
      const numReg = /^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9_\-]{8,20})$/
      const number = new RegExp(numReg)
      if (!number.test(value)) {
        callback(new Error(this.$t('pages.validateCollect_3')))
      } else {
        callback()
      }
    },
    validateIp(rule, value, callback) {
      let msg = ''
      if (!this.temp.intranetIp && !this.temp.intranetIpv6) {
        msg = this.$t('pages.ipValidate', { net: '' })
        callback(msg)
      } else {
        // 清除验证会导致多次点击保存时，没有错误提示，同时又提交不了
        // this.$refs['dataForm'].clearValidate(['intranetIp', 'intranetIpv6'])
        callback()
      }
    },
    ipv4Rule(rule, value, callback) {
      if (value && !isIPv4(value)) {
        callback(new Error(this.$t('pages.ipv4_text1')))
      } else {
        callback()
      }
    },
    ipv6Rule(rule, value, callback) {
      let msg = ''
      console.log('value6', value)
      if (value && !isIPv6(value)) {
        msg = this.$t('pages.ipv6_text1', { net: '' })
        callback(msg)
      } else {
        callback()
      }
    },
    /**
     * 新增、修改接口方法
     */
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.internetPort = this.temp.internetPortVal
          const tempData = Object.assign({}, this.temp)
          createServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            // this.gridTable.execRowDataApi(this.query)
            // this.$parent.handleFilter()
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.internetPort = this.temp.internetPortVal
          const tempData = Object.assign({}, this.temp)
          updateServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            // this.gridTable.execRowDataApi(this.query)
            // this.$parent.handleFilter()
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>
