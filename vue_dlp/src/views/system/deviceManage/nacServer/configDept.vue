<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.configureTerminal')"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 500px; margin-left:25px;">
        <FormItem :label="$t('pages.serverName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60" :disabled="true"/>
        </FormItem>
        <FormItem :label="$t('pages.deviceNum')" prop="serverId">
          <el-input v-model="temp.serverId" :maxlength="60" :disabled="true"/>
        </FormItem>
        <FormItem :label="$t('pages.termOrTermGroup')" prop="checkedKeys" >
          <tree-select
            ref="objectTree"
            node-key="id"
            :height="350"
            :width="410"
            multiple
            check-strictly
            :checked-keys="checkedKeys"
            is-filter
            :local-search="false"
            :leaf-key="'terminal'"
            @change="checkChange"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import { updateDeptRelatedNac, listDeptIdByServerId } from '@/api/system/deviceManage/nacServer';

export default {
  name: 'ConfigDept',
  data() {
    return {
      dialogFormVisible: false,
      treeLoading: false,
      submitting: false,
      checkedKeys: [],
      defaultTemp: {
        serverId: undefined,
        groupIds: [],
        termIds: []
      },
      temp: {},
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.validateUniqueName, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {},
  created() {
    // this.resetTemp()
  },
  activated() {
    // this.resetTemp()
    // this.show();
  },
  methods: {
    resetTemp() {
      console.log('defaultTemp', this.defaultTemp)
      this.temp = Object.assign({}, {
        serverId: undefined,
        groupIds: [],
        termIds: []
      })
      console.log('temp', this.temp)
    },
    checkDept(data) {
      const that = this;
      listDeptIdByServerId(data).then(respond => {
        that.checkedKeys.splice(0)
        if (respond.data.length > 0) {
          // console.log('respond.data', respond.data)
          const arr = []
          respond.data.forEach(data => {
            const obj = {}
            // console.log('checkDept', data)
            if (data.groupId !== undefined) {
              obj['G' + data.groupId] = data.groupName
              // arr.push('{G' + data.groupId + ',' + data.groupName + '}')
              arr.push('G' + data.groupId)
            }
            if (data.termId) {
              arr.push('T' + data.termId)
            }
          })
          // that.checkedKeys.push(...arr)
          that.checkedKeys = arr
        }
        // console.log('sthat.checkedKey', that.checkedKeys)
      })
    },
    show(datas) {
      // console.log('row', datas)
      this.dialogFormVisible = true
      // this.resetTemp()
      this.temp = Object.assign(this.defaultTemp, { 'name': datas[0].name, 'serverId': datas[0].devId })
      this.checkDept(this.temp)
    },
    checkChange(selected, options) {
      this.checkedKeys = selected
      // console.log('checkedKeys11111111111', this.checkedKeys)
      // console.log('options11111111111', options)
      this.temp.groupIds.splice(0)
      this.temp.termIds.splice(0)
      selected.forEach(data => {
        if (data.includes('G')) {
          if (!this.temp.groupIds.includes(data.slice(1, data.length))) {
            this.temp.groupIds.push(data.slice(1, data.length))
          }
        }
        if (data.includes('T')) {
          if (!this.temp.termIds.includes(data.slice(1, data.length))) {
            this.temp.termIds.push(data.slice(1, data.length))
          }
        }
      })
      // options.forEach(data => {
      //   if (data.id.includes('G')) {
      //     if (!this.temp.groupIds.includes(data.dataId)) {
      //       this.temp.groupIds.push(data.dataId)
      //     }
      //   }
      //   if (data.id.includes('T')) {
      //     if (!this.temp.termIds.includes(data.dataId)) {
      //       this.temp.termIds.push(data.dataId)
      //     }
      //   }
      // })
    },
    updateData() {
      this.submitting = true
      const tempData = Object.assign({}, this.temp)
      updateDeptRelatedNac(tempData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.submitting = false
      })
    }
  }
}
</script>
