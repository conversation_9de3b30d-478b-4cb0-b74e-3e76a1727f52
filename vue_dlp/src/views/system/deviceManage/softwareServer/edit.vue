<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div>
      <input type="text" class="autocomplete">
      <input type="password" class="autocomplete">
    </div>
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="166px">
      <el-tabs ref="tabs" v-model="tabName" :before-leave="changeTab" style="height: 400px;">
        <el-tab-pane :label="$t('pages.basicInformation')" name="serverTab" style="padding: 10px; overflow: auto;">
          <FormItem v-if="temp.authorizedStatus != null" :label="$t('pages.grantAuthor')">
            <el-switch v-model="temp.authorizedStatus" :active-value="1" :inactive-value="0" />
          </FormItem>
          <FormItem :label="$t('pages.serverName')" prop="name">
            <el-input v-model="temp.name" v-trim maxlength="60"/>
          </FormItem>
          <FormItem v-if="false" :label="$t('pages.devPassword')" prop="accessPwd">
            <el-input v-model="temp.accessPwd" maxlength="20" type="password" show-password />
          </FormItem>
          <!-- 给userName, password 加密 v-if会查找不到dom-->
          <div v-if="showFileServerAccount">
            <FormItem :label="$t('pages.loginUser')" encrypt prop="userName">
              <el-input v-model="temp.userName" v-trim maxlength="20"/>
            </FormItem>
            <FormItem :label="$t('pages.loginPwd')" encrypt prop="password">
              <encrypt-input v-model="temp.password" v-trim show-password maxlength="20" type="password"/>
            </FormItem>
          </div>
          <FormItem label="内网类型">
            <div slot="label">
              <el-tooltip effect="dark" placement="top-start">
                <div slot="content">
                  {{ $t('pages.intranetTypeTip1') }}<br>
                  {{ $t('pages.intranetTypeTip2') }}<br>
                  {{ $t('pages.intranetTypeTip3') }}<br>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              {{ $t('pages.intranetType') }}
            </div>
            <el-select v-model="temp.intranetType" style="width: 50%" @change="intranetTypeChange">
              <el-option :key="0" :value="0" :disabled="temp.authorizedStatus === null" :label="$t('pages.truthIpAndPort')"/>
              <el-option :key="1" :value="1" :label="$t('pages.typeless')"/>
            </el-select>
          </FormItem>
          <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
            <el-select v-if="!temp.intranetType" v-model="temp.intranetIp" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
              <el-option v-for="(item, index) in ipv4s" :key="index" :value="item" :label="item"/>
            </el-select>
            <el-input v-else v-model="temp.intranetIp" v-trim maxlength="64" @blur="inputBlur('intranetIpv6')" />
          </FormItem>
          <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
            <el-select v-if="!temp.intranetType" v-model="temp.intranetIpv6" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
              <el-option v-for="(item, index) in ipv6s" :key="index" :value="item" :label="item"/>
            </el-select>
            <el-input v-else v-model="temp.intranetIpv6" v-trim maxlength="64" @blur="inputBlur('intranetIp')" />
          </FormItem>
          <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
            <el-input v-model="temp.intranetPort" v-trim :disabled="!temp.intranetType" maxlength="5" />
          </FormItem>
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.enableInternetIP')" prop="enableInternet">
                <el-switch v-model="temp.enableInternet" :active-value="1" :inactive-value="0" @change="enableInternetChange" />
              </FormItem>
            </el-col>
            <el-col :span="12" style="float: right">
              <FormItem :label="$t('pages.returnPublicNetwork')" :tooltip-content="$t('pages.fileServerContent_1')" prop="enableForceRetIP" tooltip-placement="bottom-start">
                <el-switch v-model="temp.enableForceRetIP" :active-value="1" :inactive-value="0" :disabled="temp.enableInternet==0" />
              </FormItem>
            </el-col>
          </el-row>
          <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
            <el-input v-model="temp.internetIp" v-trim maxlength="64" :disabled="temp.enableInternet==0" @blur="inputBlur('internetIpv6')" />
          </FormItem>
          <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
            <el-input v-model="temp.internetIpv6" v-trim maxlength="64" :disabled="temp.enableInternet==0" @blur="inputBlur('internetIp')" />
          </FormItem>
          <FormItem :label="$t('pages.internetPort')" prop="internetPort">
            <el-input v-model="temp.internetPort" v-trim maxlength="5" :placeholder="$t('pages.fileServerContent_2')" :disabled="temp.enableInternet==0" />
          </FormItem>
          <FormItem :label="$t('pages.remark')" prop="remark">
            <el-input v-model="temp.remark" rows="2" resize="none" maxlength="100" show-word-limit />
          </FormItem>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.systemSetting')" name="extTab" style="padding: 10px; overflow: auto;">
          <FormItem :label="$t('pages.operatingSystem')" prop="osType">
            <el-radio-group v-model="temp.osType" @change="selectOsTypeValidate">
              <el-radio :label="1">Windows</el-radio>
              <el-radio :label="2">Linux</el-radio>
            </el-radio-group>
          </FormItem>
          <FormItem :label="$t('pages.backupPrimaryPath')" prop="mainPath">
            <el-input v-model="temp.mainPath" :maxlength="255" :style="{ width: temp.osType === 1 && !viewDisk ? 'calc(100% - 50px)' : '100%' }"/>
            <el-button v-if="temp.osType === 1 && !viewDisk" :disabled="!temp.onlineStatus" type="primary" icon="el-icon-view" size="mini" style="margin-bottom: 0" @click="viewDiskInfo"/>
          </FormItem>
          <!-- 磁盘信息 -->
          <disk-info v-if="temp.devType === 172" ref="diskInfo" :disk-list="disks" :os-type="temp.osType" @closeFuc="viewDisk = false"/>
          <el-row>
            <el-col :span="14">
              <FormItem :label="$t('pages.dataPortRange')" :tooltip-content="$t('pages.fileServerContent_3')" prop="minDataPort" tooltip-placement="bottom-start">
                <el-input v-model="temp.minDataPort" maxlength="5" />
              </FormItem>
            </el-col>
            <el-col :span="1" style="line-height: 30px; padding-left: 10px;">
              --
            </el-col>
            <el-col :span="9">
              <FormItem prop="maxDataPort" label-width="0px">
                <el-input v-model="temp.maxDataPort" maxlength="5" />
              </FormItem>
            </el-col>
          </el-row>
          <FormItem :label="$t('pages.uploadTrafficLimit')" :tooltip-content="$t('pages.fileServerContent_4')" prop="uploadSpeedLimit" tooltip-placement="bottom-start">
            <el-input v-model="temp.uploadSpeedLimit" maxlength="4" @focus="focus">
              <template slot="append">KB/S</template>
            </el-input>
          </FormItem>
          <FormItem :label="$t('pages.downloadTrafficLimit')" :tooltip-content="$t('pages.fileServerContent_4')" prop="downSpeedLimit" tooltip-placement="bottom-start">
            <el-input v-model="temp.downSpeedLimit" maxlength="4" @focus="focus">
              <template slot="append">KB/S</template>
            </el-input>
          </FormItem>
          <FormItem :label="$t('pages.diskWriteLimit')" :tooltip-content="$t('pages.fileServerContent_5')" prop="diskSpaceLimit" tooltip-placement="bottom-start">
            <el-input v-model="temp.diskSpaceLimit" type="number" maxlength="" @input="numberLimit(arguments[0], 'diskSpaceLimit', 1, 99999)" >
              <template slot="append"><span style="margin: 0px 10px 0px 10px ">M</span></template>
            </el-input>
          </FormItem>
          <FormItem :label="$t('pages.maxTransferNum')" :tooltip-content="$t('pages.fileServerContent_6')" prop="maxOccurs" tooltip-placement="bottom-start">
            <el-input v-model="temp.maxOccurs" type="number" maxlength="" @input="numberLimit(arguments[0], 'maxOccurs', 1, 99999)" />
          </FormItem>
        </el-tab-pane>
      </el-tabs>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createServer, getServerByName, updateServer } from '@/api/system/deviceManage/softwareServer'
import { getPropertyByCode } from '@/api/property'
import EncryptInput from '@/components/EncryptInput'
import { isIPv4 } from '@/utils/validate'
import { getServerInfoByDevId } from '@/api/system/deviceManage/serverAccessApproval';
import DiskInfo from '@/views/system/deviceManage/serverAccessApproval/config/DiskInfo';

export default {
  name: 'EditSoftwareServer',
  components: { EncryptInput, DiskInfo },
  data() {
    return {
      serverTabValidFields: ['name', 'userName', 'password', 'intranetIp', 'internetIp', 'intranetIpv6', 'internetIpv6', 'intranetPort', 'internetPort'],
      extTabValidFields: ['mainPath', 'uploadSpeedLimit', 'downSpeedLimit', 'diskSpaceLimit', 'diskShortageAlert', 'maxOccurs', 'minDataPort', 'maxDataPort', 'transferMode', 'osType'],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
          // { validator: this.validateUniqueName, trigger: 'blur' }   // 设备名称重复限制去除
        ],
        intranetIp: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        internetIp: [
          { validator: this.validateInternetIp, trigger: 'blur' }
        ],
        intranetIpv6: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        internetIpv6: [
          { validator: this.validateInternetIp, trigger: 'blur' }
        ],
        intranetPort: [
          { required: true, validator: this.validatePort, trigger: 'blur' }
        ],
        internetPort: [
          { validator: this.validateInternetPort, trigger: 'blur' }
        ],
        mainPath: [
          { required: true, validator: this.validateMainPath, trigger: 'blur' }
        ],
        userName: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        password: [
          { required: true, validator: this.validatePassword, trigger: 'blur' }
        ],
        accessPwd: [
          { required: true, validator: this.validatePassword, trigger: 'blur' }
        ],
        uploadSpeedLimit: [
          { required: true, validator: this.speedLimitValidate, trigger: 'blur' }
        ],
        downSpeedLimit: [
          { required: true, validator: this.speedLimitValidate, trigger: 'blur' }
        ],
        diskSpaceLimit: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        diskShortageAlert: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        maxOccurs: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        minDataPort: [
          { required: true, validator: this.validatePort, trigger: 'blur' }
        ],
        maxDataPort: [
          { required: true, validator: this.validatePort, trigger: 'blur' }
        ],
        transferMode: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        osType: [
          { required: true, validator: this.validateOsType, trigger: 'blur' }
        ]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      tabName: 'serverTab',
      showTree: false,
      showFileServerAccount: false,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        devId: undefined,
        groupId: 0,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: null,
        internetPort: undefined,
        active: false,
        version: '',
        remark: '',
        mainPath: '',
        userName: undefined,
        password: undefined,
        uploadSpeedLimit: 0,
        downSpeedLimit: 0,
        minDataPort: 12000,
        maxDataPort: 12030,
        maxOccurs: 200,
        diskSpaceLimit: 1024,
        diskShortageAlert: 5120,
        enableInternet: 0,
        enableForceRetIP: 0,
        businesses: [],
        transferMode: 0,
        // 后台数据库要求必填，给一个默认值
        accessPwd: 'dev-server_12345',
        // 操作系统（1：windows 2：linux）
        osType: 1,
        encryptProps: ['accessPwd'],
        authorizedStatus: null,   //  授权状态，即开关,0-关闭，1-打开， null：表示该设备数据是旧数据，且尚未连接引擎，此数据不允许修改开关
        intranetType: null     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('text.editInfo', { info: this.$t('route.softwareServer') }),
        create: this.$t('text.addInfo', { info: this.$t('route.softwareServer') })
      },
      submitting: false,
      validateArr: [], // tab页面有字段验证不通过，就将tab名放到数组中，以实现tab跳转
      moduleIds: [],
      editable: false,
      severNetworkInfoList: [],  //  获取网卡中的信息
      ipv4s: [],  //  网卡中的IPv6
      ipv6s: [],  //  网卡中的IPv4
      disks: [],   //  磁盘信息 name: 磁盘名称，total：磁盘总容量，单位B，free：磁盘空余空间，单位B
      viewDisk: false //  是否显示磁盘信息
    }
  },
  created() {
    this.resetTemp(false)
  },
  methods: {
    /**
     * 获取设备服务器信息
     * @param devId
     */
    getServerInfoByDevId(devId) {
      this.ipv4s = []
      this.ipv6s = []
      this.disks = []
      getServerInfoByDevId(devId).then(res => {
        if (res.data) {
          //  网卡信息
          this.severNetworkInfoList = res.data.serverNetworkInfoList || []
          //  ip地址信息
          this.severNetworkInfoList.forEach(t => {
            t.ips && this.ipv4s.push(...t.ips.split(';'))
            t.ipv6s && this.ipv6s.push(...t.ipv6s.split(';'))
          })
          //  磁盘信息
          this.disks = res.data.serverDiskInfoList || []
        }
      })
    },
    enableInternetChange(val) {
      this.$refs['dataForm'].clearValidate(['internetIp', 'internetIpv6', 'internetPort'])
      if (val === 0) {
        this.temp.enableForceRetIP = 0
        this.temp.internetIp = ''
        this.temp.internetIpv6 = ''
        this.temp.internetPort = ''
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    /**
     * 表单事件方法
     */
    changeTab(activeName, oldActiveName) {
      // let result = true
      // if (oldActiveName === 'serverTab') {
      //   this.$refs['dataForm'].validateField(this.serverTabValidFields, errorMessage => {
      //     if (errorMessage.length > 0) {
      //       result = false
      //     }
      //   })
      // } else if (oldActiveName === 'extTab') {
      //   this.$refs['dataForm'].validateField(this.extTabValidFields, errorMessage => {
      //     if (errorMessage.length > 0) {
      //       result = false
      //     }
      //   })
      // }
      // return result
      return true
    },
    numberLimit(value, type, min, max) {
      this.temp[type] = value > max ? max : value < min ? min : parseInt(value)
    },
    focus(event) {
      event.currentTarget.select()
    },
    handleCreate() {
      this.resetTemp(true).then(() => {
        this.temp.groupId = this.query.groupId
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      })
    },
    handleUpdate(row) {
      this.viewDisk = false
      this.getServerInfoByDevId(row.devId);
      this.resetTemp(true).then(() => {
        this.temp = Object.assign({}, this.defaultTemp, row) // copy obj
        this.temp = JSON.parse(JSON.stringify(this.temp))
        if (!/^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(row.mainPath)) {
          this.temp.osType = 2
        }
        //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
        if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
          this.temp.intranetType = 1
        }
        this.dialogStatus = 'update'
        this.dialogFormVisible = true
      })
    },
    resetTemp(isLoadConfig) {
      this.tabName = 'serverTab'
      return this.$nextTick().then(() => {
        this.temp = Object.assign({}, this.defaultTemp)
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        if (isLoadConfig) {
          return getPropertyByCode('server.config').then(res => {
            this.showFileServerAccount = res.data && res.data.value.split(',').indexOf('2') > -1
          })
        }
        return null
      })
    },
    validateUniqueName(rule, value, callback) {
      getServerByName({
        name: value
      }).then(respond => {
        const server = respond.data
        if (server && server.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    appendValidateArr(tab) {
      if (!this.validateArr.includes(tab)) {
        this.validateArr.push(tab)
      }
    },
    validateIPFormat(rule, value, callback) {
      let msg = ''
      if (value) {
        if (value === '127.0.0.1') {
          msg = this.$t('pages.validateFile_5')
        } else if (isIPv4(value)) {
          msg = undefined
        } else {
          msg = this.$t('pages.validateFile_6')
        }
      } else {
        msg = this.$t('pages.validateIP')
      }
      if (msg) {
        const tab = 'serverTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    inputBlur(validateProp) {
      this.$refs['dataForm'].validateField(validateProp)
    },
    validateInternetIp(rule, value, callback) {
      let msg = ''
      if (this.temp.enableInternet === 1) {
        if (!this.temp.internetIp && !this.temp.internetIpv6) {
          msg = this.$t('pages.ipValidate', { net: this.$t('pages.extranet') })
          this.appendValidateArr('serverTab')
          callback(msg)
        // } else if (value && rule.field.indexOf('Ipv6') < 0) {
        //   // 暂时不对ipv6格式进行验证，后续添加验证规则再修改条件
        //   this.validateIPFormat(rule, value, callback)
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateIp(rule, value, callback) {
      let msg = ''
      if (!this.temp.intranetIp && !this.temp.intranetIpv6) {
        msg = this.$t('pages.ipValidate', { net: this.$t('pages.intranet') })
        this.appendValidateArr('serverTab')
        callback(msg)
      // } else if (value && rule.field.indexOf('Ipv6') < 0) {
      //   // 暂时不对ipv6格式进行验证，后续添加验证规则再修改条件
      //   this.validateIPFormat(rule, value, callback)
      } else {
        callback()
      }
    },
    validateInternetPort(rule, value, callback) {
      if (this.temp.enableInternet === 1) {
        if (value === '' || value === undefined) {
          callback(new Error(this.$t('pages.validateFile_9')))
          this.appendValidateArr('serverTab')
        } else {
          this.validatePort(rule, value, callback)
        }
      } else {
        callback()
      }
    },
    validatePort(rule, value, callback) {
      let msg = ''
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (value === '' || value === undefined) {
        msg = this.$t('pages.validateMsg_enterPort')
      } else if (!number.test(value)) {
        msg = this.$t('pages.validateNaturalTree')
      } else if (value <= 0 || value > 65535) {
        msg = this.$t('pages.validatePortRange')
      } else if (rule.field === 'minDataPort' && value > this.temp.maxDataPort) {
        msg = this.$t('pages.validateFile_10')
      } else if (rule.field === 'maxDataPort' && value < this.temp.minDataPort) {
        msg = this.$t('pages.validateFile_11')
      } else {
        msg = undefined
        if (rule.field === 'minDataPort') {
          this.$refs['dataForm'].clearValidate('minDataPort')
        } else if (rule.field === 'maxDataPort') {
          this.$refs['dataForm'].clearValidate('maxDataPort')
        }
      }
      if (msg) {
        const tab = { internetPort: 'serverTab', minDataPort: 'extTab', maxDataPort: 'extTab' }[rule.field]
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    validateMainPath(rule, value, callback) {
      let msg = ''
      const reg = /^[c-zC-Z]:\\([^/:*?"<>|])*$/
      let drive = ''
      //  获取输入值的盘符
      if (value) {
        drive = value.substr(0, 1).toLocaleUpperCase()
      }
      if (!value) {
        msg = this.$t('pages.validateFBackupPath')
      } else if (!reg.test(value) && this.temp.osType === 1) {
        msg = this.$t('pages.validateFBackupPath_1')
      } else if (this.temp.osType == 1 && this.temp.onlineStatus && !this.$refs.diskInfo.getDiskIds().includes(drive)) {
        msg = this.$t('pages.notExitsDriveTip', { drive })
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = 'extTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    validateOsType(rule, value, callback) {
      let msg = ''
      if (undefined === value) {
        msg = this.$t('pages.validateFBackupOsType')
      } else {
        msg = undefined;
      }
      callback(msg)
    },
    validateRequired(rule, value, callback) {
      let msg = ''
      if (!value) {
        msg = {
          userName: this.$t('pages.validateUserName'),
          diskSpaceLimit: this.$t('pages.validateNetTaskWrite'),
          diskShortageAlert: this.$t('pages.validateNetTaskSpace'),
          maxOccurs: this.$t('pages.validateUserLogin'),
          intranetIp: this.$t('pages.validateIP')
        }[rule.field]
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = rule.field === 'intranetIp' ? 'serverTab' : 'extTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    validatePassword(rule, value, callback) {
      let msg = ''
      const numReg = /^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9_\-]{8,20})$/
      const number = new RegExp(numReg)
      if (!value) {
        if (rule.field === 'accessPwd') {
          msg = this.$t('pages.validateMsg_enterDevPassword')
        } else if (rule.field === 'password') {
          msg = this.$t('pages.validateMsg_enterLoginPassword')
        }
      } else if (!number.test(value)) {
        if (rule.field == 'password' && !value.trim()) {
          callback()
        } else {
          msg = this.$t('pages.validateCollect_3')
        }
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = { accessPwd: 'serverTab', password: 'extTab' }[rule.field]
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    speedLimitValidate(rule, value, callback) {
      let msg = ''
      if (!value && value !== 0) {
        msg = { uploadSpeedLimit: this.$t('pages.validateUploadLimit'), downSpeedLimit: this.$t('pages.validateDownloadLimit') }[rule.field]
      } else if (value !== 0 && (isNaN(Number(value)) || value.toString().indexOf('.') > -1 || value.toString().indexOf('+') > -1 || Number(value) < 0)) {
        msg = this.$t('pages.validateGreaterThanOrEqual_0')
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = 'extTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    formatFormData() {
      if (!this.showFileServerAccount) {
        // 不显示时，去除表单数据，避免加密处理错误
        this.temp.userName = undefined
        this.temp.password = undefined
      }
    },
    createServer(tempData) {
      tempData = JSON.parse(JSON.stringify(tempData))
      createServer(tempData).then(respond => {
        this.temp.id = respond.data.id
        this.submitting = false
        this.dialogFormVisible = false
        // this.gridTable.execRowDataApi(this.query)
        this.$emit('submitEnd')
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    async createData() {
      this.submitting = true
      const tabFlag = await this.validateServerExtTabValid()
      if (!tabFlag) { this.submitting = false }
      tabFlag && this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          this.createServer(tempData)
        } else {
          this.tabName = this.validateArr.indexOf(this.tabName) > -1 ? this.tabName : this.validateArr[0]
          this.validateArr.splice(0)
          this.submitting = false
        }
      })
    },
    updateServer(tempData) {
      tempData = JSON.parse(JSON.stringify(tempData))
      updateServer(tempData).then(() => {
        this.submitting = false
        this.dialogFormVisible = false
        // this.gridTable.execRowDataApi(this.query)
        this.$emit('submitEnd')
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    async updateData() {
      this.submitting = true
      const tabFlag = await this.validateServerExtTabValid()
      if (!tabFlag) { this.submitting = false }
      tabFlag && this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          this.updateServer(tempData)
        } else {
          this.tabName = this.validateArr.indexOf(this.tabName) > -1 ? this.tabName : this.validateArr[0]
          this.validateArr.splice(0)
          this.submitting = false
        }
      })
    },
    /** 校验severTab 和 extTab **/
    async validateServerExtTabValid() {
      let tab = null
      await tab === null && this.$refs['dataForm'].validateField(this.serverTabValidFields, errorMessage => {
        if (errorMessage.length > 0) {
          tab = 'serverTab'
        }
      })
      await tab === null && this.$refs['dataForm'].validateField(this.extTabValidFields, errorMessage => {
        if (errorMessage.length > 0) {
          tab = 'extTab'
        }
      })
      if (tab !== null) {
        this.validateArr.splice(0)
        this.appendValidateArr(tab)
        this.tabName = tab
      }
      return tab === null
    },
    // 切换操作系统(1:windows 2:linux)时触发备份主路径校验
    selectOsTypeValidate() {
      this.$refs['dataForm'].validateField('mainPath');
    },
    /**
     * 内网类型发生变更
     * @param value
     */
    intranetTypeChange(value) {
      //  IP有值且不在实际网卡IP中时，自动默认为实际网卡中的IP
      if (this.temp.intranetIp && this.ipv4s.length > 0 && !this.ipv4s.includes(this.temp.intranetIp)) {
        this.temp.intranetIp = this.ipv4s[0]
      }
      if (this.temp.intranetIpv6 && this.ipv6s.length > 0 && !this.ipv6s.includes(this.temp.intranetIpv6)) {
        this.temp.intranetIpv6 = this.ipv6s[0]
      }
      this.$refs.dataForm.validateField(['intranetIp', 'intranetIpv6', 'intranetPort'])
    },
    /**
     * 手动触发校验规则
     * @param fields
     */
    validateFieldClick(fields) {
      this.$refs.dataForm.validateField(fields)
    },
    /**
     * 显示磁盘信息
     */
    viewDiskInfo() {
      this.viewDisk = !this.viewDisk;
      if (this.viewDisk) {
        this.$refs.diskInfo.show();
      } else {
        this.$refs.diskInfo.close();
      }
    }
  }
}
</script>
