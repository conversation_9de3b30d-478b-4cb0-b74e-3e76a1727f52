<template>
  <div>
    <Drawer
      :title="$t('table.detail')"
      :visible.sync="detailVisible"
      status="update"
      :with-footer="false"
      :size="'60%'"
      wrapper-closable
      append-to-body
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
    >
      <div class="app-container show-detail-panel" >
        <div style="flex: 1">
          <el-descriptions class="margin-top" :title="$t('pages.basicInformation')" :column="3" size="small" border>
            <el-descriptions-item :label="$t('pages.serverName')">
              {{ temp.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.deviceNum')">
              {{ temp.devId }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.remark')">
              {{ temp.remark }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.grantAuthor')">
              {{ statusTableFilter(temp.authorizedStatus) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetIPV4Address')">
              {{ temp.intranetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetIPV6Address')">
              {{ temp.intranetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetPort')">
              {{ temp.intranetPort }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.enableInternetIP')">
              {{ yesOrNoMap[temp.enableInternet] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.returnPublicNetwork')">
              {{ yesOrNoMap[temp.enableForceRetIP] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV4Address')">
              {{ temp.internetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV6Address')">
              {{ temp.internetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetPort')">
              {{ temp.internetPort }}
            </el-descriptions-item>
            <!--            服务器状态-->
            <el-descriptions-item :label="$t('pages.versionNumber')">
              {{ temp.version }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              {{ temp.onlineStatus }}
            </el-descriptions-item>
          </el-descriptions>
          <div style="margin-bottom: 10px">
            <server-detail-public v-if="isServerDetail" ref="ServerDetail" :dev-id="temp.devId" @serverInfo="serverInfo"/>
          </div>

          <el-descriptions class="margin-top" :title="$t('pages.systemConfig')" :column="3" size="small" border>
            <el-descriptions-item :label="$t('pages.operatingSystem')">
              {{ osTypeMap[temp.osType] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.backupPrimaryPath')">
              {{ temp.mainPath }}
            </el-descriptions-item>
            <!--            <el-descriptions-item :span="2" :label="$t('pages.backupExpansionPath')">-->
            <!--              <span v-if="temp.extPaths && temp.extPaths.length>0">{{ temp.extPaths }}</span>-->
            <!--            </el-descriptions-item>-->
            <el-descriptions-item :label="$t('pages.dataPortRange')">
              {{ temp.minDataPort }} -- {{ temp.maxDataPort }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.uploadTrafficLimit')">
              {{ temp.uploadSpeedLimit }}
              KB/S
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.downloadTrafficLimit')">
              {{ temp.downSpeedLimit }}
              KB/S
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.diskWriteLimit')">
              {{ temp.diskSpaceLimit }}
              {{ unitMap[temp.diskSpaceLimitUnit] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.maxTransferNum')">
              {{ temp.maxOccurs }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </Drawer>
  </div>
</template>
<script>
import { getPropertyByCode } from '@/api/property';
import { fileServerDefaultBusses } from '@/views/system/deviceManage/backupServer/backupServerCommonInfo';
import ServerDetailPublic from '@/views/system/deviceManage/serverInfo/serverDetailPublic/index.vue';

export default {
  name: 'DetailSoftwareServer',
  components: { ServerDetailPublic },
  data() {
    return {
      detailVisible: false,
      submitting: false,                 // 提交数据中...
      isServerDetail: false, // 开启服务器详情组件
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        devId: undefined,
        groupId: 0,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: null,
        internetPort: undefined,
        active: false,
        version: '',
        remark: '',
        mainPath: '',
        extPaths: [],
        userName: undefined,
        password: undefined,
        uploadSpeedLimit: 0,
        downSpeedLimit: 0,
        minDataPort: 12000,
        maxDataPort: 12030,
        maxOccurs: 200,
        diskSpaceLimit: 51200,
        diskSpaceLimitUnit: 1,
        diskShortageAlert: 5120,
        enableInternet: 0,
        enableForceRetIP: 0,
        businesses: [],
        transferMode: 0,
        // 后台数据库要求必填，给一个默认值
        accessPwd: 'dev-server_12345',
        // 操作系统（1：windows 2：linux）
        osType: 1,
        encryptProps: ['accessPwd'],
        authorizedStatus: null,   //  授权状态，即开关,0-关闭，1-打开， null：表示该设备数据是旧数据，且尚未连接引擎，此数据不允许修改开关
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        onlineStatus: 0 // 服务器在线状态0:不在线 1：在线
      },
      bussTemp: {},
      batchBussTemp: {}, // 批量配置业务备份路径与文件保留时间存储对象
      checkedModuleIds: [],
      selectedCheckedModuleIds: [], // 用于批量配置时存储选中的业务的bussId
      specialBussIds: [13],
      specialBussDatas: [],
      bussDatas: [],
      selectedDatas: [], // 用于批量配置时存储选中的业务
      defaultBussDatas: fileServerDefaultBusses,
      yesOrNoMap: {
        0: this.$t('text.no'),
        1: this.$t('text.yes')
      },
      osTypeMap: {
        1: 'Windows',
        2: 'Linux'
      },
      unitMap: {
        1: 'GB',
        0: 'MB'
      }
    }
  },
  computed: {

  },
  watch: {
  },
  activated() {
  },
  created() {
  },
  methods: {
    show(row) {
      this.handleUpdate(row)
      this.detailVisible = true
      // 进入详细信息后，选中双击节点的编号
      // const devId = row.devId
      this.isServerDetail = true
      this.$nextTick(() => {
        this.$refs['ServerDetail'].query2.devId = row.devId + ''// 编号
      })
    },
    // 服务器信息
    serverInfo(data) {
      Object.assign(this.temp, { 'onlineStatus': data.onlineStatus })
    },
    resetTemp(isLoadConfig) {
      this.tabName = 'serverTab'
      return this.$nextTick().then(() => {
        this.diskSpaceLimit = 50
        this.temp = Object.assign({}, this.defaultTemp)
        this.temp.extPaths.splice(0)
        this.bussDatas.splice(0)
        this.specialBussDatas.splice(0)
        this.defaultBussDatas.forEach(data => {
          if (this.specialBussIds.indexOf(data.bussId) < 0) {
            this.bussDatas.push(JSON.parse(JSON.stringify(data)))
          } else {
            this.specialBussDatas.push(JSON.parse(JSON.stringify(data)))
          }
        })
        this.checkedModuleIds = []
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        if (isLoadConfig) {
          return getPropertyByCode('server.config').then(res => {
            this.showFileServerAccount = res.data && res.data.value.split(',').indexOf('1') > -1
          })
        }
        return null
      })
    },
    // 格式化授权状态
    statusTableFilter(data) {
      const statusMap = {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      }
      return statusMap[data]
    },
    handleUpdate(row) {
      console.log('backup', row)
      this.smartServerId = undefined
      this.viewDisk = false
      this.resetTemp(true).then(() => {
        this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
        this.diskSpaceLimit = this.temp.diskSpaceLimitUnit ? Math.floor(this.temp.diskSpaceLimit / 1024) ? Math.floor(this.temp.diskSpaceLimit / 1024) : 1 : this.temp.diskSpaceLimit
        this.temp.password && (this.temp.password = '         ')
        if (!/^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(row.mainPath)) {
          this.temp.osType = 2
        }
        this.getBussTypeChecked(this.temp.businesses || [])
        // 业务数据合并
        this.bussDatas.forEach(data => {
          const opt = this.checkedModuleIds.indexOf(data.bussId) > -1 ? 1 : 0
          data.enabled = opt
          row.businesses && row.businesses.forEach(item => {
            if (data.bussId == item.bussId) {
              data.fileRetainDays = item.fileRetainDays
              data.relativePath = item.relativePath
              data.enableDelete = item.enableDelete
            }
          })
        })
        this.specialBussDatas.forEach(data => {
          const opt = this.checkedModuleIds.indexOf(data.bussId) > -1 ? 1 : 0
          data.enabled = opt
          row.businesses.forEach(item => {
            if (data.bussId == item.bussId) {
              data.fileRetainDays = item.fileRetainDays
              data.relativePath = item.relativePath
              data.enableDelete = item.enableDelete
            }
          })
        })

        //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
        if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
          this.temp.intranetType = 1
        }
        this.dialogStatus = 'update'
        this.dialogFormVisible = true
      })
    },
    // 关闭弹窗前，校验数据是否发生变化，并提示
    beforeClose(done) {
      this.isServerDetail = false
      done()
    },
    // 点击确认提交数据
    async handleSaveConfig() {
    }
  }
}
</script>
<style lang="scss" scoped>
.show-detail-panel {
  color: #666;
  display: flex;
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
