<template>
  <Form ref="serverForm" :model="temp" :rules="rules" label-position="right" label-width="135px" style="width: 640px;">
    <el-divider content-position="left"> {{ $t('pages.softwareServiceMessage') }} </el-divider>
    <el-row>
      <el-col :span="12">
        <FormItem :label="$t('pages.serverName') + '：'">{{ temp.name }}</FormItem>
      </el-col>
      <el-col :span="12">
        <FormItem :label="$t('pages.deviceNum') + '：'">{{ temp.devId }}</FormItem>
      </el-col>
      <el-col :span="12">
        <FormItem :label="$t('pages.intranetAddress') + '：'">{{ temp.intranetIp }}</FormItem>
      </el-col>
      <el-col :span="12">
        <FormItem :label="$t('pages.intranetPort') + '：'">{{ temp.intranetPort }}</FormItem>
      </el-col>
    </el-row>
    <el-divider content-position="left">{{ $t('pages.softwareServiceBindService') }}</el-divider>
    <FormItem :label="$t('pages.server_daq')" :tooltip-content="$t('pages.bindGatherGroupInfo')" tooltip-placement="bottom-start">
      <el-select v-model="temp.daqIds" multiple :placeholder="$t('text.select')" @change="changeValue($event)">
        <el-option v-for="item in daqServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
      </el-select>
    </FormItem>
  </Form>
</template>

<script>
import { deepMerge, equalsObj } from '@/utils'
import { bindServer, getBindServer, getDbAndDeviceRelation } from '@/api/system/deviceManage/softwareServer'

export default {
  name: 'SoftwareBindServerFrom',
  props: {
    daqServer: {
      type: Array,
      default: null
    },
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      masterAble: false,
      temp: {},
      oldTemp: {},
      newTemp: {},
      groupTemp: {},
      nochangeTemp: {},
      rules: {
        refDevId: [
          { required: true, validator: this.validateRefServerId, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    changeValue(value) {
      const oldOptions = [...this.temp.daqIds];
      let exist = false
      let tempVariable = ''
      for (var i = 0; i < this.groupTemp.length; i++) {
        let exist1 = false
        for (var j = 0; j < oldOptions.length; j++) {
          if (this.groupTemp[i] == oldOptions[j]) {
            exist1 = true
          }
        }
        if (!exist1) {
          exist = true
          tempVariable = this.groupTemp[i]
        }
      }

      if (exist) {
        var position = this.nochangeTemp.daqIds.indexOf(tempVariable)
        this.temp.daqIds.splice(position, 0, tempVariable);  
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.unableRemoveGatherGroup'),
          type: 'warning',
          duration: 1000
        })
        return
      }
      const stroeTemp = []
      var q = 0
      for (var k = 0; k < this.temp.daqIds.length; k++) {
        if (!this.groupTemp.includes(this.temp.daqIds[k])) {
          stroeTemp[q] = this.temp.daqIds[k]
          q++
        }
      }
      this.newTemp.daqIds = []
      for (var p = 0; p < stroeTemp.length; p++) {
        this.newTemp.daqIds[p] = stroeTemp[p]
      }
      this.nochangeTemp.daqIds = []
      for (var arr = 0; arr < this.temp.daqIds.length; arr++) {
        this.nochangeTemp.daqIds[arr] = this.temp.daqIds[arr]
      }
    },
    setFormData(data) {
      this.resetTemp()
      this.temp = deepMerge({}, data)
      this.oldTemp = deepMerge({}, this.temp)
      this.newTemp = deepMerge({}, this.temp)
      getDbAndDeviceRelation(data.devId).then(resp => {
        if (resp.data) {
          this.groupTemp = resp.data
          getBindServer(data.devId).then(resp => {
            if (resp.data) {
              this.temp = deepMerge({
                daqIds: resp.data.daqIds
              }, resp.data.server)
              this.oldTemp = deepMerge({}, this.temp)
              this.newTemp = deepMerge({}, this.temp)
              var k = this.temp.daqIds.length
              for (var i = 0; i < this.groupTemp.length; i++) {
                this.temp.daqIds[k] = this.groupTemp[i]
                k++
              }
              this.nochangeTemp = deepMerge({}, this.temp)
            }
          })
        }
      })
    },
    isFormDataChange() {
      const obj1 = this.getFormData(this.newTemp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData) {
      return {
        devType: tempData.devType,
        devId: tempData.devId,
        daqServers: tempData.daqIds
      }
    },
    submitData(callback) {
      if (!this.newTemp.daqIds) {
        return
      }
      bindServer(this.getFormData(this.newTemp)).then(() => {
        callback()
        this.oldTemp = deepMerge({}, this.newTemp)
      })
    },
    resetTemp() {
      this.temp = {
        daqServers: []
      }
      this.newTemp = {
        daqServers: []
      }
    },
    toOptionByServer(data) {
      let label = data.name;
      if (data.intranetIpv6) {
        label += ' (' + data.devId + '_' + data.intranetIpv6 + ':' + data.intranetPort + ')'
      } else if (data.internetIpv6) {
        label += ' (' + data.devId + '_' + data.internetIpv6 + ':' + data.internetPort + ')'
      } else if (data.intranetIp) {
        label += ' (' + data.devId + '_' + data.intranetIp + ':' + data.intranetPort + ')'
      } else if (data.internetIp) {
        label += ' (' + data.devId + '_' + data.internetIp + ':' + data.internetPort + ')'
      }
      return label
    }
  }
}
</script>

<style scoped>

</style>
