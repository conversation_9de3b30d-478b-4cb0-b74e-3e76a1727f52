<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dlgVisible"
    width="900px"
    @dragDialog="handleDrag"
  >
    <div slot="title" class="el-dialog__title">
      {{ $t('pages.deviceMgr') }}
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          {{ $t('pages.serverMgrDlgContent', { saveAndContinue: this.$t('button.saveAndContinue') }) }}
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <el-container style="max-height: 465px;">
      <el-aside width="200px">
        <tree-menu
          ref="serverTree"
          :height="465"
          :data="serverTreeData"
          :node-key="nodeKey"
          :render-content="renderContent"
          :default-expanded-keys="expandedKeys"
          @node-click="handleNodeClick"
        />
      </el-aside>
      <el-main v-loading="tableLoading">
        <daq-bind-server-from
          v-if="type === 'Daq'"
          ref="formDaq"
          :groups="groupTreeSelectData"
          :db-server="getServerOptions('Db')"
          :backup-server="getServerOptions('Back')"
          :detect-server="getServerOptions('Detec')"
          :daq-server="getServerOptions('Daq')"
          :software-server="getServerOptions('Soft')"
          :smart-backup-server-group-data="smartBackupServerGroupData"
        />
        <daq-bind-server-group-from
          v-if="type === 'G'"
          ref="formG"
          :db-server="getServerOptions('Db')"
          :backup-server="getServerOptions('Back')"
          :detect-server="getServerOptions('Detec')"
          :daq-server="getServerOptions('Daq')"
          :software-server="getServerOptions('Soft')"
          :smart-backup-server-group-data="smartBackupServerGroupData"
        />
        <backup-bind-server-from
          v-if="type === 'Back'"
          ref="formBack"
          :daq-server="getServerOptions('Daq')"
          :backup-server="getServerOptions('Back')"
          :approval-server="getServerOptions('Appro')"
        />
        <db-bind-server-from
          v-if="type === 'Db'"
          ref="formDb"
          :daq-server="getServerOptions('Daq')"
          :db-server="getServerOptions('Db')"
          :smart-server="getServerOptions('Smart')"
        />
        <detection-bind-server-from
          v-if="type === 'Detec'"
          ref="formDetec"
          :daq-server="getServerOptions('Daq')"
        />
        <approval-bind-server-from
          v-if="type === 'Appro'"
          ref="formAppro"
          :backup-server="getServerOptions('Back')"
        />
        <software-bind-server-from
          v-if="type === 'Soft'"
          ref="formSoft"
          :daq-server="getServerOptions('Daq')"
        />
        <smart-bind-server-group-from
          v-if="type === 'GSmart'"
          ref="formGSmart"
          :daq-server="getServerOptions('Daq')"
          :db-server="getServerOptions('Db')"
        />
        <smart-bind-server-from
          v-if="type === 'Smart'"
          ref="formSmart"
          :db-server="getServerOptions('Db')"
          :backup-server="getServerOptions('Back')"
        />
      </el-main>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="submitData">
        {{ $t('button.saveAndContinue') }}
      </el-button>
      <el-button @click="cancel()">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script type="text/jsx">
import { getServerGroupTree } from '@/api/system/deviceManage/serverGroup'
import { getServerTree, getServerByType, getReflectServerMap } from '@/api/system/deviceManage/server'
import { generateTitle } from '@/utils/i18n'
import DaqBindServerFrom from './daqServer/bindServerItem'
import DaqBindServerGroupFrom from './daqServer/bindServerGroupItem'
import DbBindServerFrom from './dbServer/bindServerItem'
import DetectionBindServerFrom from './detectionServer/bindServerItem'
import BackupBindServerFrom from './backupServer/bindServerItem'
import ApprovalBindServerFrom from './approvalServer/bindServerItem'
import SoftwareBindServerFrom from './softwareServer/bindServerItem.vue'
import SmartBindServerGroupFrom from './smartBackupServer/bindServerGroupItem'
import SmartBindServerFrom from './smartBackupServer/bindServerItem'
import { getSmartBackupServerGroupTree } from '@/api/system/deviceManage/smartBackupServer';

export default {
  name: 'ServerMgrDlg',
  components: {
    ApprovalBindServerFrom,
    BackupBindServerFrom,
    DetectionBindServerFrom,
    DbBindServerFrom,
    DaqBindServerGroupFrom,
    DaqBindServerFrom,
    SoftwareBindServerFrom,
    SmartBindServerGroupFrom,
    SmartBindServerFrom
  },
  props: {
    defaultExpandedKeys: {
      type: Array,
      default() {
        return ['-1']
      }
    }
  },
  data() {
    return {
      expandedKeys: ['-1'],
      firstRequest: true, // 是否首次请求
      dlgVisible: false,
      submitting: false,
      serverTreeData: [],
      serverTreeMap: {
        6: { label: this.$t('pages.server_daq'), dataId: '-1', typeLabel: 'Daq', menuCode: 'A42', children: [] },
        11: { label: this.$t('pages.BackupServer'), dataId: '-2', typeLabel: 'Back', menuCode: 'A43', children: [] },
        9: { label: this.$t('pages.server_approval'), dataId: '-5', typeLabel: 'Appro', menuCode: 'A45', children: [] },
        219: { label: this.$t('pages.DBServer'), dataId: '-3', typeLabel: 'Db', menuCode: 'A46', children: [] },
        12: { label: this.$t('pages.DetectionServer'), dataId: '-4', typeLabel: 'Detec', menuCode: 'A48', children: [] },
        0xAC: { label: this.$t('route.softwareServer'), dataId: '-6', typeLabel: 'Soft', menuCode: 'A4K', children: [] },
        0xba: { label: this.$t('route.intelligentBackupServer'), dataId: '-7', typeLabel: 'Smart', menuCode: 'A4L', children: [] }
      },
      groupTreeSelectData: [],
      curClickNodeKey: '',
      curClickParentId: 0,
      curNode: {},
      curCheckDevId: 0,
      temp: {},
      defaultTemp: { // 表单字段
        types: undefined,
        roleId: undefined,
        roleName: '',
        parentRoleId: undefined,
        permissionIds: [],
        menuIds: [],
        deptIds: []
      },
      tableLoading: false,
      serverOptionMap: {},
      refMap: [],
      refDevIds: [],
      type: 0,
      serverInfo: null,
      nodeKey: 'dataId',
      smartBackupServerGroupData: []
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.resetServerOptionMap()
    this.getSmartBackupServerGroupData()
  },
  activated() {
    this.getSmartBackupServerGroupData()
  },
  updated() {
    if (this.curCheckDevId != null && this.$refs.serverTree != undefined) {
      // 拓扑图下，首次打开设备管理
      const data = this.$refs.serverTree.findNode(this.serverTreeData, this.curCheckDevId, 'dataId')
      if (data != null && this.firstRequest) {
        this.loadFormAfterNodeClick(data, this.$refs.serverTree.getNode(this.curCheckDevId))
        const pathArr = this.$refs.serverTree.getNodePath(data);
        const arr = pathArr.filter(path => path.dataId != this.curCheckDevId).map(d => String(d.dataId));
        this.expandedKeys = arr
        this.firstRequest = false
      }
      // 将节点设置为选中状态会触发 updated 钩子，导致陷入循环卡死。这里增加判断，如果节点已选中，则不再设置
      const curKey = this.$refs.serverTree.getCurrentKey()
      if (curKey != this.curCheckDevId) {
        this.$refs.serverTree.setCurrentKey(this.curCheckDevId)
      }
    }
  },
  methods: {
    generateTitle, // generateTitle by vue-i18n
    show(val) {
      this.expandedKeys = this.defaultExpandedKeys
      this.firstRequest = true
      this.resetTemp()
      this.dlgVisible = true
      this.tableLoading = false
      this.clearFilters()
      this.initGroupTreeNode()
      this.getSmartBackupServerGroupData()
      this.initServerTree()
      this.initServerOption()
      this.initReflectServerMap()
      this.curCheckDevId = val
    },
    initReflectServerMap() {
      getReflectServerMap().then(resp => {
        this.refMap = resp.data
        this.refDevIds = Object.values(resp.data)
      })
    },
    initGroupTreeNode() {
      this.groupTreeSelectData = []
      getServerGroupTree().then(respond => {
        respond.data.forEach(el => {
          this.groupTreeSelectData.push(el)
        })
      })
    },
    initServerTree() {
      getServerTree({}).then(resp => {
        for (const type in this.serverTreeMap) {
          this.serverTreeMap[type].children.splice(0)
        }
        resp.data.forEach(nodeData => {
          if (('G' === nodeData.type && nodeData.oriData.type === 6) || nodeData.oriData.devType === 6) {
            this.serverTreeMap[6].children.push(nodeData)
          } else if (('G' === nodeData.type && nodeData.oriData.type === 0xba) || nodeData.oriData.devType === 0xba) {
            this.serverTreeMap[0xba].children.push(nodeData)
          } else {
            const item = this.serverTreeMap[nodeData.oriData.devType]
            if (item) {
              item.children.push(nodeData)
            }
          }
        })
        this.serverTreeData.splice(0)
        for (const devType in this.serverTreeMap) {
          const item = this.serverTreeMap[devType]
          if (this.hasPermission(item.menuCode)) {
            this.serverTreeData.push(item)
          }
        }
      })
    },
    resetServerOptionMap() {
      for (const type in this.serverTreeMap) {
        const treeNode = this.serverTreeMap[type]
        this.serverOptionMap[type] = {
          label: treeNode.label, type: Number.parseInt(type), typeLabel: treeNode.typeLabel, children: []
        }
      }
    },
    initServerOption() {
      getServerByType({}).then(resp => {
        for (const type in this.serverOptionMap) {
          this.serverOptionMap[type].children.splice(0)
        }
        resp.data.forEach(item => {
          const serverOption = this.serverOptionMap[item.devType]
          if (serverOption) {
            serverOption.children.push(item)
          }
        })
      })
    },
    getServerOptions(typeLabel) {
      for (const type in this.serverOptionMap) {
        const root = this.serverOptionMap[type]
        if (root.typeLabel === typeLabel) {
          return root.children
        }
      }
      return []
    },
    renderContent(h, { node, data, store }) {
      const iconClass = this.getIconClass(node, data);
      return (
        <div class='custom-tree-node'>
          <svg-icon v-show={!!iconClass} icon-class={!iconClass ? '' : iconClass} />
          <span>{data.label}</span>
        </div>
      )
    },
    getIconClass(node, data) {
      if (data.type === 'G') {
        return 'dir'
      } else if (data.type === 'S') {
        if (data.oriData && data.oriData.access) {
          return 'branch'
        }
        if (this.refMap && this.refMap[data.dataId]) {
          // 说明此服务器为分部服务器：因为已经映射到总部
          return 'branch'
        }
        if (this.refDevIds && this.refDevIds.indexOf(Number.parseInt(data.dataId)) > -1) {
          // 说明此服务器为总部服务器：因为已经被分部映射
          return 'controller'
        }
        return 'server'
      }
      return null
    },
    clearFilters() {
      this.$refs.serverTree && this.$refs.serverTree.clearFilter()
    },
    handleNodeClick: function(data, node, el) {
      this.curCheckDevId = data.devId
      if (data.dataId < 0) {
        // 服务器类型节点
        return
      }
      if (this.isFormItemChange()) {
        this.$confirmBox(this.$t('pages.serverMgrDlgSaveConfirm'), this.$t('text.prompt')).then(() => {
          this.submitData()
        }).catch(() => {
          this.loadFormAfterNodeClick(data, node)
        })
      } else {
        this.loadFormAfterNodeClick(data, node)
      }
    },
    loadFormAfterNodeClick: function(data, node) {
      this.curNode = node
      if (data.type === 'G' && data.oriData.type === 6) {
        this.type = 'G'
      } else if (data.type === 'G' && data.oriData.type === 0xba) {
        this.type = 'GSmart'
      } else {
        let tempType = null
        let tempNode = node
        while (!tempType) {
          if (tempNode.parent && tempNode.parent.data && tempNode.parent.data.dataId < 0) {
            tempType = tempNode.parent.data.typeLabel
            break
          } else {
            tempNode = tempNode.parent
          }
        }
        this.type = tempType
      }
      this.$nextTick(() => {
        this.serverInfo = data.oriData
        this.loadFormItem()
      })
    },
    loadFormItem() {
      const formItem = this.$refs['form' + this.type]
      if (formItem) {
        formItem.setFormData(this.serverInfo)
      }
    },
    isFormItemChange() {
      const formItem = this.$refs['form' + this.type]
      if (formItem) {
        return formItem.isFormDataChange()
      }
      return false
    },
    handleFilter() {
      this.listQuery.page = 1
    },
    resetTemp() {
      this.type = 0
      this.temp = Object.assign({}, this.defaultTemp)
      this.$refs.serverTree && this.$refs.serverTree.checkSelectedNode([])
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    submitData() {
      const formItem = this.$refs['form' + this.type]
      if (!formItem) {
        return
      }
      this.tableLoading = true
      formItem.submitData((resp) => {
        if (resp && resp.hasOwnProperty('valid') && !resp.valid) {
          // 校验失败时，不做其他处理
          this.tableLoading = false
          return
        }
        if (resp && resp.refresh) {
          this.loadFormItem()
        }
        if (resp && resp.change) {
          this.initGroupTreeNode()
          this.initServerTree()
          this.initServerOption()
        }
        if (resp && resp.refChange) {
          this.initReflectServerMap()
        }
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
        this.tableLoading = false
        this.$emit('submitEnd', resp)
      })
    },
    cancel() {
      this.dlgVisible = false
      this.$emit('submitEnd')
    },
    getSmartBackupServerGroupData: function() {
      this.smartBackupServerGroupData = []
      getSmartBackupServerGroupTree().then((respond) => {
        respond.data.forEach(el => {
          this.smartBackupServerGroupData.push(el)
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
