<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="$t('pages.service')"
    :remark="$t('pages.serverRemark')"
    :visible.sync="dlgDialogVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div class="toolbar">
      <el-row>
        <el-col v-show="!selectHidden" :span="1" style="font-weight: 700;padding-top: 7px;">{{ $t('pages.classify') }}</el-col>
        <el-col v-show="!selectHidden" :span="7">
          <el-select v-model="query.devType" @change="devTypeChange">
            <el-option v-for="item in devTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="16" style="float: right;">
          <div style="float: right;">
            <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.serverNickname')" style="width: 200px;"/>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <grid-table
      ref="serverTable"
      :height="420"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :default-sort="{ prop: 'name' }"
      :page-sizes="[ 20, 50, 100, 500, 1000 ]"
      @selectionChangeEnd="selectionChangeEnd"
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="handleSelect()">
        {{ $t('button.addSelectedServer') }}
      </el-button>
      <el-button @click="dlgDialogVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getBaseServerPage as getDatabasePage } from '@/api/system/deviceManage/dbServer'
import { getServerPage as getBackupPage, getBindAbleBackupServerPage } from '@/api/system/deviceManage/backupServer'
import { getServerPage as getDetectionPage } from '@/api/system/deviceManage/detectionServer'
import { getBindAbleServerPage, getBindAbleDaqServerPage } from '@/api/system/deviceManage/daqServer'

export default {
  name: 'ServerSelectDlg',
  props: {
    appendToBody: { type: Boolean, default: false },
    devTypes: {
      type: Array,
      default() {
        return [
          { value: 0, label: this.$t('pages.collectAll') },
          { value: 11, label: this.$t('route.backupServer') },
          { value: 12, label: this.$t('route.DetectionServer') },
          { value: 20, label: this.$t('route.DBServer') }
        ]
      }
    },
    dlgVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'devType', label: 'serviceType', width: '100', formatter: this.typeFormatter },
        { prop: 'name', label: 'serviceNickName', width: '100' },
        { prop: 'devId', label: 'deviceNum', width: '100' },
        { prop: 'intranetIp', label: 'intranetIp', width: '100' },
        { prop: 'intranetPort', label: 'intranetPort', width: '100' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        devType: 0,
        exceptServerIds: ''
      },
      dlgDialogVisible: false,
      selectHidden: false, // 是否隐藏服务器类型可选框
      submitting: false,
      hidden: false,
      getPageFunc: null,
      selectedRowData: []
    }
  },
  computed: {
    serverTable: function() {
      return this.$refs['serverTable']
    }
  },
  watch: {
    dlgVisible(val) {
      this.dlgDialogVisible = val
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    show(serverId, flag, selectHidden) {
      this.dlgDialogVisible = true
      this.selectHidden = selectHidden
      if (flag === 'toBindDaqServer') {
        this.query.devType = 6 // 6表示采集服务器类型
        this.selectHidden = true
      } else {
        if (selectHidden) {
          this.query.devType = this.devTypes[1].value
        } else {
          this.query.devType = this.devTypes[0].value
        }
      }

      this.query.exceptServerIds = serverId
      this.query.name = null
      this.selectedRowData = []
      this.$nextTick(() => {
        this.devTypeChange(this.query.devType)
        this.serverTable && this.serverTable.selectedDatasDelete()
      })
    },
    devTypeChange(val) {
      if (val === 0) {
        this.getPageFunc = getBindAbleServerPage
      } else if (val === 11) {
        this.getPageFunc = this.selectHidden ? getBindAbleBackupServerPage : getBackupPage
      } else if (val === 12) {
        this.getPageFunc = getDetectionPage
      } else if (val === 20) {
        this.getPageFunc = getDatabasePage
      } else if (val === 6) {
        this.getPageFunc = getBindAbleDaqServerPage
      }
      this.handleFilter()
    },
    selectionChangeEnd(rowDatas) {
      this.selectedRowData = rowDatas
    },
    handleFilter() {
      this.query.page = 1
      this.serverTable && this.serverTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const newOption = Object.assign(this.query, option)
      newOption.sortName = newOption.devType === 20 ? 'db_name' : 'name'
      if (!this.getPageFunc) {
        this.getPageFunc = () => {
          return new Promise((resolve, reject) => {
            resolve({ code: 20000, data: { total: 0, items: [] }})
          })
        }
      }
      return this.getPageFunc(newOption)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      this.$emit('select', this.selectedRowData)
    },
    typeFormatter(row, data) {
      if (data === 6) {
        return this.$t('route.daqServer')
      }
      for (let i = 0; i < this.devTypes.length; i++) {
        if (this.devTypes[i].value === data) {
          return this.devTypes[i].label
        }
      }
      return ''
    }
  }
}
</script>
