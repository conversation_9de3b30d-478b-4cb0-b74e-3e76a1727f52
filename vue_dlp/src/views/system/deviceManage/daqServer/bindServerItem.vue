<template>
  <div>
    <Form ref="serverForm" :model="temp" label-position="right" label-width="150px" style="width: 640px;">
      <el-divider content-position="left"> {{ $t('pages.collectServiceMessage') }} </el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.serverNickname') + '：'">{{ temp.name }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.deviceNum') + '：'">{{ temp.devId }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetAddress') + '：'">{{ temp.intranetIp }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetPort') + '：'">{{ temp.intranetPort }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.collectionGroup') + '：'">
            <el-select v-model="temp.groupId" clearable :placeholder="$t('text.select')">
              <el-option v-for="item in groups" :key="item.dataId" :label="item.label" :value="Number.parseInt(item.dataId)"/>
            </el-select>
          </FormItem>
        </el-col>
      </el-row>
      <el-divider content-position="left">
        {{ $t('pages.collectionBindService') }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.collectionBindServiceDescription">
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </el-divider>
      <FormItem :label="$t('pages.DBServer') + '：'">
        <el-select v-model="temp.dbIds" multiple :disabled="temp.dbReadOnly" :placeholder="$t('text.select')">
          <el-option v-for="item in dbServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
        <span v-if="temp.dbReadOnly" class="tip-text">{{ $t('pages.currentCollectionServerUseServer',{content:$t('route.DBServer')}) }}</span>
      </FormItem>
      <FormItem :label="$t('pages.BackupServer') + '：'">
        <el-select v-model="temp.backupIds" multiple :disabled="temp.backupReadOnly" :placeholder="$t('text.select')">
          <el-option v-for="item in backupServer.filter(server => !server.hidden)" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
        <span v-if="temp.backupReadOnly" class="tip-text">{{ $t('pages.currentCollectionServerUseServer',{content:$t('pages.BackupServer')}) }}</span>
      </FormItem>
      <FormItem v-permission="'A48'" :label="$t('pages.DetectionServer') + '：'">
        <el-select v-model="temp.detectionIds" multiple :disabled="temp.detectReadOnly" :placeholder="$t('text.select')">
          <el-option v-for="item in detectServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
        <span v-if="temp.detectReadOnly" class="tip-text">{{ $t('pages.currentCollectionServerUseServer',{content:$t('pages.DetectionServer')}) }}</span>
      </FormItem>
      <FormItem :label="$t('route.softwareServer') + '：'">
        <el-select v-model="temp.softwareIds" multiple :disabled="temp.softwareReadOnly" :placeholder="$t('text.select')">
          <el-option v-for="item in softwareServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
        <span v-if="temp.softwareReadOnly" class="tip-text">{{ $t('pages.currentCollectionServerUseServer',{content:$t('route.softwareServer')}) }}</span>
      </FormItem>
      <FormItem v-permission="'A4L'" :label="$t('table.intelligentBackupServerGroup') + '：'">
        <el-select v-model="temp.smartBackupServerGroupId" clearable :disabled="temp.smartBackupReadOnly">
          <el-option
            v-for="item in smartBackupServerGroupData"
            :key="item.dataId"
            :label="item.label"
            :value="parseInt(item.dataId)"
          />
        </el-select>
        <span v-if="temp.smartBackupReadOnly" class="tip-text">{{ $t('pages.currentCollectionServerUseServer',{content:$t('route.intelligentBackupServer')}) }}</span>
      </FormItem>
      <!--<FormItem label-width="40px">
        <el-checkbox v-model="masterSubsectionAble">开启总分采集服务器映射</el-checkbox>
      </FormItem>
      <el-divider v-if="masterSubsectionAble" content-position="left">{{ '映射到总部采集服务器' }}</el-divider>
      <el-row v-if="masterSubsectionAble">
        <el-col :span="12">
          <FormItem :label="'采集组：'">
            <el-select />
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem label-width="10px">
            <el-checkbox>打开采集组的“总分同步”开关</el-checkbox>
          </FormItem>
        </el-col>
        <el-col :span="24">
          <FormItem :label="'总部采集服务器：'">
            <el-select v-model="temp.daqIds" :placeholder="$t('text.select')" style="height: 30px;">
              <el-option-group label="采集服务器">
                <el-option v-for="item in daqServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
              </el-option-group>
            </el-select>
          </FormItem>
        </el-col>
        <el-col :span="24">
          <ul>
            <label>说明：开启总分采集服务器映射必须满足以下两个条件</label>
            <li>1、当前采集服务器必须添加到采集组，</li>
            <li>2、采集组必须开启总分同步</li>
          </ul>
        </el-col>
      </el-row>-->
    </Form>
  </div>
</template>

<script>
import { deepMerge, equalsObj } from '@/utils'
import { bindServer, getBindServer } from '@/api/system/deviceManage/daqServer'

export default {
  name: 'DaqBindServerFrom',
  components: { },
  props: {
    groups: {
      type: Array,
      default: null
    },
    daqServer: {
      type: Array,
      default: null
    },
    dbServer: {
      type: Array,
      default: null
    },
    backupServer: {
      type: Array,
      default: null
    },
    detectServer: {
      type: Array,
      default: null
    },
    softwareServer: {
      type: Array,
      default: null
    },
    smartBackupServerGroupData: {
      type: Array,
      default: null
    },
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      masterSubsectionAble: false,
      temp: {},
      oldGroupId: 0
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  activated() {
  },
  methods: {
    setFormData(data) {
      this.temp = deepMerge({}, data)
      this.oldTemp = deepMerge({}, this.temp)
      getBindServer(data.devId).then(resp => {
        if (resp.data) {
          this.$nextTick(() => {
            this.temp = deepMerge({
              groupId: '',
              refMap: {},
              dbIds: [],
              backupIds: [],
              detectionIds: [],
              softwareIds: [],
              smartBackupServerGroupId: undefined
            }, resp.data.server)
            if (this.temp.groupId === 0) {
              this.temp.groupId = undefined
            }
            this.oldGroupId = this.temp.groupId
            if (resp.data.bind) {
              this.temp = deepMerge(this.temp, {
                dbIds: resp.data.bind.dbServers,
                backupIds: resp.data.bind.backServers,
                detectionIds: resp.data.bind.detectServers,
                softwareIds: resp.data.bind.softwareServers,
                smartBackupServerGroupId: resp.data.bind.smartBackupServerGroupId
              })
            }
            if (resp.data.groupBind) {
              if (resp.data.groupBind.dbServers && resp.data.groupBind.dbServers.length > 0) {
                this.temp.oldDbIds = this.temp.dbIds
                this.temp.dbIds = resp.data.groupBind.dbServers
                this.temp.dbReadOnly = true
              }
              if (resp.data.groupBind.backServers && resp.data.groupBind.backServers.length > 0) {
                this.temp.oldBackupIds = this.temp.backupIds
                this.temp.backupIds = resp.data.groupBind.backServers
                this.temp.backupReadOnly = true
              }
              if (resp.data.groupBind.detectServers && resp.data.groupBind.detectServers.length > 0) {
                this.temp.oldDetectionIds = this.temp.detectionIds
                this.temp.detectionIds = resp.data.groupBind.detectServers
                this.temp.detectReadOnly = true
              }
              if (resp.data.groupBind.softwareServers && resp.data.groupBind.softwareServers.length > 0) {
                this.temp.oldSoftwareIds = this.temp.softwareIds
                this.temp.softwareIds = resp.data.groupBind.softwareServers
                this.temp.softwareReadOnly = true
              }
              if (resp.data.groupBind.smartBackupServerGroupId) {
                this.temp.oldSmartBackupServerGroupId = this.temp.smartBackupServerGroupId
                this.temp.smartBackupServerGroupId = resp.data.groupBind.smartBackupServerGroupId
                this.temp.smartBackupReadOnly = true
              }
            }
            this.oldTemp = deepMerge({}, this.temp)
          })
        }
      })
    },
    groupIdSelectChange: function(data) {
      this.temp.groupId = data
    },
    isFormDataChange() {
      const obj1 = this.getFormData(this.temp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData) {
      return {
        groupId: tempData.groupId ? tempData.groupId : 0,
        devId: tempData.devId,
        dbServers: tempData.dbReadOnly ? tempData.oldDbIds : tempData.dbIds,
        backServers: tempData.backupReadOnly ? tempData.oldBackupIds : tempData.backupIds,
        detectServers: tempData.detectReadOnly ? tempData.oldDetectionIds : tempData.detectionIds,
        softwareServers: tempData.softwareReadOnly ? tempData.oldSoftwareIds : tempData.softwareIds,
        smartBackupServerGroupId: tempData.smartBackupReadOnly ? tempData.oldSmartBackupServerGroupId : tempData.smartBackupServerGroupId
      }
    },
    submitData(callback) {
      const curGroupId = this.temp.groupId ? this.temp.groupId : 0
      const isGroupChange = this.oldGroupId !== curGroupId
      bindServer(this.getFormData(this.temp)).then(() => {
        callback({ change: isGroupChange, refChange: isGroupChange, refresh: true })
        this.oldTemp = deepMerge({}, this.temp)
      }).catch((e) => {
        callback({ valid: false })
        console.log('提交失败！{}', e)
      })
    },
    toOptionByServer(data) {
      let label = data.name;
      const { devId, intranetIpv6, internetIpv6, intranetIp, internetIp, intranetPort, internetPort } = data
      const ip = intranetIpv6 || internetIpv6 || intranetIp || internetIp
      if (ip) {
        const port = (intranetIpv6 || intranetIp) ? intranetPort : internetPort
        label += ` (${devId}_${ip}_${port})`
      }
      return label
    }
  }
}
</script>
<style lang="scss" scoped>
  .tip-text {
    color: #68a8d0;
    font-size: 12px;
  }
</style>
