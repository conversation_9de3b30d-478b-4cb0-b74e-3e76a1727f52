<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="groupForm" :rules="rules" :model="temp" label-position="right" label-width="135px" style="width: 520px; margin-left:15px;">
        <FormItem :label="$t('pages.collectionGroupName')" prop="name">
          <el-input v-model="temp.name" :maxlength="60"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.region')" prop="area">
          <el-input v-model="temp.area" rows="2" :maxlength="60" resize="none"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.openLoad')">
          <el-switch v-model="temp.loadBanlance" :active-value="1" :inactive-value="0" />
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <!-- {{ $t('pages.openLoadContent_1') }}<br/> -->
              <i18n path="pages.openLoadContent_1">
                <br slot="br" />
              </i18n><br/>
              {{ $t('pages.openLoadContent_2') }}<br/>
              {{ $t('pages.openLoadContent_3') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.DBServer')">
          <el-select v-model="temp.dbIds" multiple>
            <el-option
              v-for="item in dbList"
              :key="item.devId"
              :label="item.name + '-' + item.devId + '(' + item.ip + ')'"
              :value="item.devId"
            ></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.BackupServer')">
          <el-select v-model="temp.backupIds" multiple>
            <el-option
              v-for="item in backupServerList"
              :key="item.devId"
              :label="item.name + '-' + item.devId + '(' + item.intranetIp + ')'"
              :value="item.devId"
            ></el-option>
          </el-select>
        </FormItem>
        <FormItem v-permission="'A48'" :label="$t('pages.DetectionServer')">
          <el-select v-model="temp.detectionIds" multiple>
            <el-option
              v-for="item in detectionList"
              :key="item.devId"
              :label="item.name + '-' + item.devId + '(' + item.intranetIp + ')'"
              :value="item.devId"
            ></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('route.softwareServer')">
          <el-select v-model="temp.softwareIds" multiple>
            <el-option
              v-for="item in softwareList"
              :key="item.devId"
              :label="item.name + '-' + item.devId + '(' + item.intranetIp + ')'"
              :value="item.devId"
            />
          </el-select>
        </FormItem>
        <FormItem v-permission="'A4L'" :label="$t('table.intelligentBackupServerGroup')">
          <el-select v-model="temp.smartBackupServerGroupId" clearable>
            <el-option
              v-for="item in smartBackupServerGroupData"
              :key="item.dataId"
              :label="item.label"
              :value="parseInt(item.dataId)"
            />
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="doSubmit">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <server-select-dlg ref="serverSelectDlg" :dev-types="devTypes" @select="addItem"/>
  </div>
</template>

<script>
import ServerSelectDlg from '@/views/system/deviceManage/serverSelectDlg'
import {
  getServerGroupByName,
  createServerGroup,
  updateServerGroup,
  deleteServerGroup,
  validateDelServerGroup,
  getServerGroupById
} from '@/api/system/deviceManage/serverGroup'
import { getDbServerPage, getBackupServerPage, getDetectionServerPage } from '@/api/system/deviceManage/daqServer'
import { getServerPage as getSoftwareServerPage } from '@/api/system/deviceManage/softwareServer'
import { deepMerge } from '@/utils'
import { getSmartBackupServerGroupTree } from '@/api/system/deviceManage/smartBackupServer';

export default {
  name: 'EditDaqServerGroupDlg',
  components: { ServerSelectDlg },
  data() {
    return {
      updateColModel: [
        { prop: 'devType', label: 'serviceType', width: '70', sort: true, formatter: this.typeFormatter },
        { prop: 'name', label: 'serviceNickName', width: '100' },
        { prop: 'devId', label: 'deviceNum', width: '80' },
        { prop: 'intranetIp', label: 'intranetIp', width: '80' },
        { prop: 'intranetPort', label: 'intranetPort', width: '80' }
      ],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.validateUniqueGroupName, trigger: 'blur' }
        ],
        parentId: [{ required: true, message: this.$t('pages.validateMsg_selectParentGroup'), trigger: 'blur' }]
      },
      devTypes: [
        { value: 0, label: this.$t('pages.collectAll') },
        { value: 11, label: this.$t('route.backupServer') },
        { value: 12, label: this.$t('route.DetectionServer') },
        { value: 219, label: this.$t('route.DBServer') }
      ],
      textMap: {
        update: this.$t('pages.updateCollectionGroup'),
        create: this.$t('pages.addCollectionGroup')
      },
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        area: '',
        size: 0,
        parentId: 0,
        loadBanlance: 1,
        dbIds: [],
        backupIds: [],
        detectionIds: [],
        softwareIds: [],
        smartBackupServerGroupId: undefined
      },
      query: { // 查询条件
        page: 1,
        limit: 100,
        serverId: undefined
      },
      dbList: [],
      backupServerList: [],
      detectionList: [],
      softwareList: [],
      serverList: [],
      rowData: [],
      dialogStatus: '',
      dialogVisible: false,
      submitting: false,
      updateRowData: [],
      activeName: 'toBindServer',
      syncRate: 0,
      syncTypeChecked: [],
      syncTypeOptions: [
        { value: 1, label: this.$t('pages.syncType_1') },
        { value: 2, label: this.$t('pages.syncType_2') },
        { value: 4, label: this.$t('pages.syncType_3') }
      ],
      smartBackupServerGroupData: []
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.getSmartBackupServerGroupData()
  },
  activated() {
    this.getSmartBackupServerGroupData()
  },
  methods: {
    resetTemp() {
      this.temp = deepMerge({}, this.defaultTemp)
    },
    initServerData() {
      getDbServerPage({ page: 1, limit: null }).then(respond => {
        this.dbList = respond.data.items
      })
      getBackupServerPage({ page: 1, limit: null }).then(respond => {
        this.backupServerList = respond.data.items
      })
      getDetectionServerPage({ page: 1, limit: null }).then(respond => {
        this.detectionList = respond.data.items
      })
      getSoftwareServerPage({ page: 1, limit: null }).then(respond => {
        this.softwareList = respond.data.items
      })
    },
    parentIdSelectChange(data) {
      if (data && data !== this.tempG.id) {
        this.tempG.parentId = data
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    doSubmit() {
      if (this.dialogStatus === 'create') {
        this.createData()
      } else {
        this.updateData()
      }
    },
    formatterSyncTypeChecked(syncType) {
      this.syncTypeChecked.splice(0)
      if (syncType) {
        this.syncTypeOptions.forEach(option => {
          if (syncType & option.value) {
            this.syncTypeChecked.push(option.value)
          }
        })
      }
    },
    handleCreate() {
      this.resetTemp()
      this.initServerData()
      this.getSmartBackupServerGroupData()
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    async handleUpdate(data) {
      this.resetTemp()
      this.initServerData()
      this.getSmartBackupServerGroupData()
      if (data && data.id) {
        await getServerGroupById(data.id).then(resp => {
          this.temp = Object.assign({}, this.defaultTemp, resp.data)
        })
      } else {
        this.temp = Object.assign({}, this.defaultTemp, data)
      }
      this.dialogStatus = 'update'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    handleDelete(toDeleteIds) {
      validateDelServerGroup({ ids: toDeleteIds }).then(respond => {
        if (respond.data === 'disable_4_child') {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.collectService_notifyMsg6'),
            type: 'warning',
            duration: 2000
          })
        } else {
          this.$confirmBox(this.$t('pages.collectService_notifyMsg7'), this.$t('text.prompt')).then(() => {
            deleteServerGroup({ ids: toDeleteIds }).then(respond => {
              this.$emit('deleteEnd', toDeleteIds)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.collectService_notifyMsg8'),
                type: 'success', duration: 2000 })
            })
          }).catch(() => {})
        }
      })
    },
    selectItem() {
      this.$refs.serverSelectDlg.show(this.activeName, this.temp.devId)
    },
    addItem(datas) {
      const dataMap = {}
      this.rowData.forEach(item => {
        dataMap['' + item.devType + item.devId] = item
      })
      for (let i = 0; i < datas.length; i++) {
        const item = datas[i]
        const oldItem = dataMap['' + item.devType + item.devId]
        if (!oldItem) {
          item.id = new Date().getTime() + i
          this.rowData.push(item)
        }
      }
    },
    removeItem() {
      const ids = this.$refs['toBindServerTable'].getSelectedIds()
      for (let i = 0; i < this.rowData.length; i++) {
        if (ids.indexOf(this.rowData[i].id) > -1) {
          this.rowData.splice(i, 1)
          i--
        }
      }
    },
    createData() {
      this.submitting = true
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          createServerGroup(this.temp).then(respond => {
            this.submitting = false
            this.dialogVisible = false
            this.$emit('submitEnd')
            this.$emit('addEnd', this.temp)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.collectService_notifyMsg3'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateServerGroup(tempData).then(respond => {
            this.submitting = false
            this.dialogVisible = false
            this.$emit('submitEnd')
            this.$emit('updateEnd', tempData)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.collectService_notifyMsg4'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    validateUniqueGroupName(rule, value, callback) {
      getServerGroupByName({ name: value }).then(respond => {
        const group = respond.data
        if (group && group.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    typeFormatter(row, data) {
      for (let i = 0; i < this.devTypes.length; i++) {
        if (this.devTypes[i].value === data) {
          return this.devTypes[i].label
        }
      }
      return ''
    },
    getSmartBackupServerGroupData: function() {
      this.smartBackupServerGroupData = []
      getSmartBackupServerGroupTree().then((respond) => {
        respond.data.forEach(el => {
          this.smartBackupServerGroupData.push(el)
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
