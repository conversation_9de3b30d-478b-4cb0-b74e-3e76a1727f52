<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :default-expand-all="true"
        resizeable
        :render-content="renderContent"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button v-if="false" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('pages.createDevice') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delDevice') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleManage">
          {{ $t('pages.deviceManage1') }}
        </el-button>
        <!--<el-button icon="el-icon-plus" size="mini" :disabled="!balanceable" @click="handleOpen">
          开启负载
        </el-button>
        <el-button icon="el-icon-plus" size="mini" :disabled="!balanceable" @click="handleClose">
          关闭负载
        </el-button>-->
        <!--<el-button disabled size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.serverName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.deviceNum')" prop="devId">
                <el-input v-model.number="query.devId" v-trim clearable :maxlength="4" :placeholder="this.$t('pages.serverLibrary_text5')" @input="number('devId')"/>
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
                <el-input v-model="query.intranetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
                <el-input v-model="query.intranetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
                <el-input v-model.number="query.intranetPort" v-trim maxlength="5" clearable :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('intranetPort')"/>
              </FormItem>
              <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
                <el-input v-model="query.internetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
                <el-input v-model="query.internetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetPort')" prop="internetPort">
                <el-input v-model.number="query.internetPort" v-trim maxlength="5" clearable :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('internetPort')"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="serverTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <edit-daq-server ref="editDaqServer" :group-tree-data="formTreeData" @submitEnd="submitEnd"></edit-daq-server>
    <edit-daq-server-group-dlg ref="daqServerGroupEditDlg" @submitEnd="getGroupTreeNode" @deleteEnd="groupDeleteEnd"/>
    <server-mgr-dlg ref="serverMgrDlg" @submitEnd="serverMgrSubmitEnd"/>
    <detail-daq-server ref="detailDaqServer"></detail-daq-server>
  </div>
</template>

<script>
import EditDaqServerGroupDlg from './editGroupDlg'
import ServerMgrDlg from '../serverMgrDlg'
import EditDaqServer from './editDaqServer'
import DetailDaqServer from './detailDaqServer'
import { getServerPage, updateServer, deleteServer, updateLoadBanlance } from '@/api/system/deviceManage/daqServer'
import { getServerGroupTree } from '@/api/system/deviceManage/serverGroup'
import { updateAssignServer } from '@/api/system/terminalManage/terminal'
import { aesEncode, formatAesKey } from '@/utils/encrypt'

export default {
  name: 'DAQServer',
  components: { ServerMgrDlg, EditDaqServerGroupDlg, EditDaqServer, DetailDaqServer },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { label: 'type', fixedWidth: '55', fixed: true, iconFormatter: this.isBranch },
        { prop: 'name', label: 'serverName', width: '120', fixed: true, sort: 'custom' },
        { prop: 'groupId', label: 'collectionGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: 'custom' },
        { prop: 'intranetIp', label: 'intranetIPV4Address', width: '140', sort: 'custom' },
        { prop: 'intranetIpv6', label: 'intranetIPV6Address', width: '140', sort: 'custom' },
        { prop: 'intranetPort', label: 'intranetPort', width: '100', sort: 'custom' },
        { prop: 'internetIp', label: 'internetIPV4Address', width: '140', sort: 'custom' },
        { prop: 'internetIpv6', label: 'internetIPV6Address', width: '140', sort: 'custom' },
        { prop: 'internetPort', label: 'internetPort', width: '100', sort: 'custom' },
        { prop: 'loadBanlance', label: 'loadState', width: '80', formatter: this.loadBanlanceFormatter },
        { prop: 'adminAuthorized', label: 'accessStatus', width: '80', formatter: this.statusTableFilter },
        { prop: 'version', label: 'version', width: '100', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '250',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'detail', click: this.handleDetail },
            { label: 'grantAuthor', disabledFormatter: this.grantButtonFormatter, click: this.handleAdminAuthorized },
            { label: 'cancelGrantAuthor', disabledFormatter: this.cancelGrantButtonFormatter, click: this.handleCancelAdminAuthorized }
          ]
        }
      ],
      terminalColModel: [
        { prop: 'name', label: 'terminalName', width: '100', sort: true },
        { prop: 'computerName', label: 'computerName', width: '100' }
      ],
      serverRules: {},
      addBtnAble: false,
      query: { // 查询条件
        page: 1,
        groupId: undefined,
        name: '',
        intranetIp: '',
        intranetIpv6: '',
        intranetPort: undefined,
        internetIp: '',
        internetIpv6: '',
        internetPort: undefined,
        devId: undefined
      },
      defaultCheckedKeys: [],
      showTree: true,
      deleteable: false,
      balanceable: false,
      tempG: {},
      tempUser: {},

      forbidIpSegments: [],

      forbidIpDeleteable: false,
      dialogTerminalVisible: false,
      dialogServerVisible: false,
      treeData: [
        { label: this.$t('pages.collectionGroup'),
          id: 'G0',
          dataId: '0',
          children: [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }]
        }
      ],
      formTreeData: [],
      defaultExpandedKeys: ['0'],
      groupTreeSelectData: [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }],
      submitting: false,
      checkedGroupId: undefined,
      treeSelectNode: [],
      defaultTreeSelectNode: [{ id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped') }],
      treeNodeType: 'G',
      serverTab: 'file'
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    detailDaqServer() {
      return this.$refs['detailDaqServer']
    },
    filterKey() {
      return this.tempG.id
    }
  },
  watch: {
    'treeData': {
      deep: true,
      handler() {
        this.resetSelectTreeData()
      }
    }
  },
  created() {
    // this.resetTemp()
    this.getGroupTreeNode()
    //  支持设备名称查询
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.$nextTick(() => {
      this.handleFilter()
    })
  },
  activated() {
    this.getGroupTreeNode()
    //  支持设备名称查询
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.handleFilter()
  },
  methods: {
    submitEnd() {
      this.gridTable.execRowDataApi(this.query)
    },
    terminalSelect() {
      return this.$refs['terminalGridSelect']
    },
    selectDataFuncEnd(data) {
      const terminalIds = this.terminalSelect().getSelectedIds()
      if (terminalIds.length > this.tempUser.maxConnections) {
        this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.collectService_notifyMsg1'), type: 'warning', duration: 2000 })
      } else if (terminalIds.length > this.tempUser.ratedConnections) {
        this.$notify({ title: this.$t('text.prompt'), message: this.$t('pages.collectService_notifyMsg2'), type: 'warning', duration: 2000 })
      }
    },
    changeTreeSelectNode() {
      this.treeSelectNode = [...this.defaultTreeSelectNode]
      this.treeData[0].children.forEach(node => {
        if (node.dataId != 0) this.treeSelectNode.push(node)
      })
    },
    resetSelectTreeData() {
      this.formTreeData = JSON.parse(JSON.stringify(this.treeData[0].children))
    },
    handleSelectionChange2(val) {
      this.forbidIpDeleteable = val.length > 0
    },
    loadTypeTreeExceptRoot() {
      return this.treeData[0].children
    },
    getGroupTreeNode: function() {
      getServerGroupTree().then(respond => {
        this.groupTreeSelectData = [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }]
        this.treeData = [{ label: this.$t('pages.collectionGroup'), id: 'G0', dataId: '0', children: [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }] }]
        respond.data.forEach(el => {
          this.groupTreeSelectData.push(el)
          this.treeData[0].children.push(el)
          if (respond.data.length > 0) {
            this.treeNodeType = respond.data[0].type
          }
        })
        this.changeTreeSelectNode()
      })
    },
    renderContent(h, { node, data, store }) {
      const topShow = data.dataId == '0'
      const unGroupShow = data.dataId == '-1'
      const loadBanlance = data.oriData && data.oriData.loadBanlance == 1
      return (
        <div class='custom-tree-node'>
          <svg-icon v-show={loadBanlance} icon-class='loadBanlance' class='icon-space' />
          <span>{data.label}</span>
          <el-tooltip v-show={topShow && !unGroupShow} class='item' effect='dark' placement='bottom-start' style='margin-left:5px;color: #acbdca;'>
            <div slot='content'>
              {this.$t('pages.renderContent_1')}<br/>
              {this.$t('pages.renderContent_2')}<br/>
              {this.$t('pages.renderContent_3')}<br/>
              {this.$t('pages.renderContent_4')}<br/>
              {this.$t('pages.renderContent_5')}
            </div>
            <i class='el-icon-info' />
          </el-tooltip>
          <svg-icon v-show={topShow && !unGroupShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' style='margin-left:5px;' on-click={r => this.handleGroupCreate(data)} />
          <span class='el-ic'>
            <svg-icon v-show={!topShow && !unGroupShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleGroupUpdate(data)} />
            <svg-icon v-show={!topShow && !unGroupShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.handleGroupDelete(data)} />
          </span>
        </div>
      )
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createGroupData(data) {
      this.groupTree.addNode(this.dataToTreeNode(data))
    },
    updateGroupData(data) {
      this.groupTree.updateNode(this.dataToTreeNode(data))
    },
    // 单击树节点的回调函数
    handleNodeClick(data, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      this.query.groupId = checkedNode.data.dataId == '0' ? null : checkedNode.data.dataId
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleGroupCreate(data) {
      this.$refs['daqServerGroupEditDlg'].handleCreate()
    },
    handleGroupUpdate(data) {
      this.$refs['daqServerGroupEditDlg'].handleUpdate(data.oriData)
    },
    handleGroupDelete(node) {
      this.$refs['daqServerGroupEditDlg'].handleDelete(node.dataId)
    },
    groupDeleteEnd(dataId) {
      const nodeData = this.groupTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    handleCreate() {
      this.$refs.editDaqServer.handleCreate(this.query.groupId)
    },
    handleUpdate(row) {
      this.$refs.editDaqServer.handleUpdate(row)
      this.$nextTick(() => {
        this.gridTable.execRowDataApi(this.query)
      })
    },
    handleDetail(row) {
      this.$refs.detailDaqServer.show(row)
    },
    handleOpen() {
      this.$confirmBox(this.$t('pages.collectService_notifyMsg9'), this.$t('text.prompt')).then(() => {
        const selectedIds = this.gridTable.getSelectedIds()
        this.updateLoadBanlance(selectedIds.join(','), 1, this.$t('pages.collectService_notifyMsg10'))
      }).catch(() => {})
    },
    handleClose() {
      this.$confirmBox(this.$t('pages.collectService_notifyMsg11'), this.$t('text.prompt')).then(() => {
        const selectedIds = this.gridTable.getSelectedIds()
        this.updateLoadBanlance(selectedIds.join(','), 0, this.$t('pages.collectService_notifyMsg12'))
      }).catch(() => {})
    },
    updateLoadBanlance(ids, loadBanlance, msg) {
      updateLoadBanlance({ ids: ids, loadBanlance: loadBanlance }).then(respond => {
        this.balanceable = false
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: msg,
          type: 'success',
          duration: 2000
        })
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerPage(searchQuery)
    },
    statusTableFilter(row, data) {
      const statusMap = {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      }
      return statusMap[data]
    },
    loadBanlanceFormatter(row, data) {
      let status = this.$t('pages.loadOff')
      if (data == 1) {
        if (row.loadRate) {
          status = this.$t('pages.loadBalancing')
        } else {
          status = this.$t('pages.sequentiaLoad')
        }
      }
      return status
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
      let flag = true
      val.forEach(el => {
        if (!el.group || el.group.loadBanlance === 0) flag = false
      })
      if (val.length > 0 && flag === true) {
        this.balanceable = true
      } else {
        this.balanceable = false
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFresh() {
      this.gridTable.execRowDataApi()
    },
    serverMgrSubmitEnd(data) {
      if (data && data.hasOwnProperty('balanceChange') && data.balanceChange) {
        this.getGroupTreeNode()
      }
      this.handleFresh()
    },
    updateTerminal() {
      const terminalIds = this.terminalSelect().getSelectedIds()
      // const terminals = this.terminalSelect().getSelectedDatas()
      const tempData = Object.assign({}, this.tempUser)
      if (tempData.maxConnections && terminalIds.length > tempData.maxConnections) {
        this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.collectService_notifyMsg13'), type: 'warning', duration: 2000 })
      } else {
        const flag = false
        // todo: 需要校验是否存在跨组终端，后台需要新写一个方法
        if (flag == true) {
          this.$confirmBox(this.$t('pages.collectService_notifyMsg14'), this.$t('text.prompt')).then(() => {
            updateAssignServer({ assignServerId: tempData.devId, ids: terminalIds.join(',') }).then(respond => {
              this.dialogTerminalVisible = false
              this.tempUser.terminalSize = terminalIds.length
              this.gridTable.execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.collectService_notifyMsg15'),
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {})
        } else {
          updateAssignServer({ assignServerId: tempData.devId, ids: terminalIds.join(',') }).then(respond => {
            this.dialogTerminalVisible = false
            this.tempUser.terminalSize = terminalIds.length
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.collectService_notifyMsg16'),
              type: 'success',
              duration: 2000
            })
          })
        }
      }
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.collectService_notifyMsg8'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleManage() {
      this.$refs['serverMgrDlg'].show()
    },
    handleAdminAuthorized(row) {
      const tempData = JSON.parse(JSON.stringify(row))
      tempData.accessPwd = aesEncode(tempData.accessPwd, formatAesKey('tr838408', tempData.name || ''))
      tempData.adminAuthorized = 1
      updateServer(tempData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.gridTable.updateRowData(respond.data)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.collectService_notifyMsg19'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handleCancelAdminAuthorized(row) {
      const tempData = JSON.parse(JSON.stringify(row))
      tempData.accessPwd = aesEncode(tempData.accessPwd, formatAesKey('tr838408', tempData.name || ''))
      tempData.adminAuthorized = 0
      updateServer(tempData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.gridTable.updateRowData(respond.data)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.collectService_notifyMsg20'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handleTerminal(row) {
      this.tempUser = Object.assign({}, row)
      this.dialogTerminalVisible = true
      if (this.terminalSelect()) {
        this.terminalSelect().reload()
        this.terminalSelect().clearFilter()
      }
    },
    isBranch: function(row) {
      const online = row.onlineStatus == 1
      const style = online ? 'color: green' : 'color: #888'
      const labelPre = (online ? this.$t('pages.online') : this.$t('pages.offline')) + '\r\n'
      return row.isBranch ? { class: 'branch', title: labelPre + this.$t('pages.branchServer'), style } : row.syncType ? { class: 'controller', title: labelPre + this.$t('pages.headquartersServer'), style } : { class: 'server', title: labelPre + this.$t('pages.commonServer'), style }
    },
    groupFormatter: function(row, data) {
      const groupId = row.groupId;
      if (groupId === 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.treeData, data)
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    groupClick: function(row, data) {
      this.checkedGroupId = row.groupId
    },
    grantButtonFormatter: function(row, btn) {
      let disabled = true
      if (row.adminAuthorized == 0) {
        disabled = false
      }
      return disabled
    },
    cancelGrantButtonFormatter: function(row, btn) {
      let disabled = true
      if (row.adminAuthorized == 1) {
        disabled = false
      }
      return disabled
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.intranetIp = ''
      this.query.intranetIpv6 = ''
      this.query.intranetPort = undefined
      this.query.internetIp = ''
      this.query.internetIpv6 = ''
      this.query.internetPort = undefined
      this.query.devId = undefined
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card{
    position: relative;
    >>>.el-card__header{
      padding: 10px 20px;
    }
  }
  .btn-box{
    position: absolute;
    top: 5px;
    right: 20px;
  }

</style>
