<template>
  <div>
    <Drawer
      :title="$t('table.detail')"
      :visible.sync="detailVisible"
      status="update"
      :with-footer="false"
      :size="'60%'"
      wrapper-closable
      append-to-body
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
    >
      <div class="app-container show-detail-panel" >
        <div style="flex: 1">
          <el-descriptions class="margin-top" :title="$t('pages.basicInformation')" size="small" :column="3" border>
            <el-descriptions-item :label="$t('pages.serverName')">
              {{ temp.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.deviceNum')">
              {{ temp.devId }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.collectionGroup')">
              {{ groupFormatter(temp.groupId) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.remark')">
              {{ temp.remark }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.grantAuthor')">
              {{ statusTableFilter(temp.adminAuthorized) }}
            </el-descriptions-item>
            <!--            内外网+端口-->
            <el-descriptions-item :label="$t('pages.intranetIPV4Address')">
              {{ temp.intranetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetIPV6Address')">
              {{ temp.intranetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetPort')">
              {{ temp.intranetPort }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV4Address')">
              {{ temp.internetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV6Address')">
              {{ temp.internetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetPort')">
              {{ temp.internetPort }}
            </el-descriptions-item>
            <!--            服务器状态-->
            <el-descriptions-item :label="$t('pages.versionNumber')">
              {{ temp.version }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              {{ temp.onlineStatus }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              <div slot="label">{{ $t('pages.onlineTerminals') }} /{{ $t('pages.terminalAll') }} :</div>
              {{ temp.allDevOnlineNum }}
            </el-descriptions-item>
          </el-descriptions>
          <server-detail-public v-if="isServerDetail" ref="ServerDetail" @serverInfo="serverInfo"/>
          <el-descriptions class="margin-top" :title="$t('pages.systemConfig')" :column="2" size="small" border>
            <el-descriptions-item :label="$t('pages.ratedConnections')">
              {{ temp.ratedConnections }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.maximumConnection')">
              {{ temp.maxConnections }}
            </el-descriptions-item>
            <el-descriptions-item :span="2" :label="$t('pages.terminalIPAccessRange')">
              <grid-table
                ref="allowIpSegmentsTable"
                :height="160"
                row-no-label=" "
                :col-model="colModel1"
                :show-pager="false"
                :row-datas="allowIpSegments"
                :default-sort="{ prop: 'name', order: 'desc' }"
                :multi-select="false"
              />
            </el-descriptions-item>
          </el-descriptions>
        </div>

      </div>
    </Drawer>
  </div>
</template>
<script>
import { getServerInfoByDevId, isSetAdminDb, listDevServer } from '@/api/system/deviceManage/serverAccessApproval';
import { getServerGroupTree } from '@/api/system/deviceManage/serverGroup';
import ServerDetailPublic from '@/views/system/deviceManage/serverInfo/serverDetailPublic/index.vue';

export default {
  name: 'DetailDaqServer',
  components: { ServerDetailPublic },
  data() {
    return {
      detailVisible: false,
      submitting: false,                 // 提交数据中...
      isServerDetail: false, // 开启服务器详情组件
      groupTreeSelectData: [],
      treeSelectNode: [],
      defaultTreeSelectNode: [{ id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped') }],
      treeData: [],
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        pwd: '',
        accessPwd: 'dev-server_12345',
        devId: undefined,
        groupId: undefined,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: undefined,
        internetPort: undefined,
        version: '',
        remark: '',
        allowIpSegments: '',
        ratedConnections: 2000,
        isSetMax: 0,
        maxConnections: undefined, // isSetMax==1时，maxConnections默认为3000
        loadRate: undefined,
        adminAuthorized: 1,     //  授权状态  与authorizedStatus同一个意思
        authorizedStatus: null, //  授权状态  若为null代表mapping表中不存在该设备信息，表示未接入，无法操作主数据库
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        loadBanlance: 0,
        encryptProps: ['accessPwd'],
        sysmgrDbId: null, //  主数据库Id
        extraInfo: {
          sysmgrDbId: null  //  主数据库Id
        },
        onlineStatus: 0 // 服务器在线状态0:不在线 1：在线
      },
      allowIpSegments: [],
      colModel1: [
        { prop: 'startIp', label: 'startIP', type: 'text', width: '120' },
        { prop: 'endIp', label: 'endIP', type: 'text', width: '120' }
      ]
    }
  },
  computed: {

  },
  watch: {
  },
  activated() {
  },
  created() {
    this.getGroupTreeNode()
  },
  methods: {
    show(row) {
      this.handleUpdate(row)
      this.detailVisible = true
      // 进入详细信息后，选中双击节点的编号
      const devId = row.devId
      console.log('ServerDetail devId', devId)
      this.isServerDetail = true
      this.$nextTick(() => {
        this.$refs['ServerDetail'].query2.devId = row.devId + ''// 编号
      })
    },
    // 服务器信息
    serverInfo(data) {
      Object.assign(this.temp, data)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    /**
     * 判断是否拥有设置采集服务器和检测服务器的主数据库权限
     */
    isSetAdminDb() {
      isSetAdminDb().then(res => {
        this.isSetAdminDbAuth = res.data || false
      })
    },
    /**
     * 加载数据库信息
     */
    loadDbServer() {
      return listDevServer(219, 2).then(res => {
        this.dbServers = res.data || []
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = [...this.defaultTreeSelectNode]
      this.treeData[0].children.forEach(node => {
        if (node.dataId != 0) this.treeSelectNode.push(node)
      })
    },
    getGroupTreeNode: function() {
      getServerGroupTree().then(respond => {
        this.groupTreeSelectData = [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }]
        this.treeData = [{ label: this.$t('pages.collectionGroup'), id: 'G0', dataId: '0', children: [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }] }]
        respond.data.forEach(el => {
          this.groupTreeSelectData.push(el)
          this.treeData[0].children.push(el)
        })
        this.changeTreeSelectNode()
      })
    },

    /**
     * 获取设备网卡信息
     * @param devId
     */
    getServerNetworkInfoList(devId) {
      this.ipv4s = []
      this.ipv6s = []
      getServerInfoByDevId(devId).then(res => {
        if (res.data) {
          this.severNetworkInfoList = res.data.serverNetworkInfoList || []
          this.severNetworkInfoList.forEach(t => {
            t.ips && this.ipv4s.push(...t.ips.split(';'))
            t.ipv6s && this.ipv6s.push(...t.ipv6s.split(';'))
          })
        }
      })
    },
    formatIPTable() {
      this.allowIpSegments.splice(0)
      if (this.temp.allowIpSegments) {
        const time = new Date().getTime()
        let i = 1
        const ipSegments = this.temp.allowIpSegments.split(',')
        ipSegments.forEach(segment => {
          const ips = segment.split('-')
          this.allowIpSegments.push({
            id: time + i,
            startIp: ips[0],
            endIp: ips[1]
          })
          i++
        })
      }
    },
    findNode: function(nodes, keyValue, keyField) {
      if (nodes && nodes.length > 0) {
        for (const i in nodes) {
          const nodei = nodes[i]
          if (nodei[keyField] + '' === keyValue + '') {
            return nodei
          } else {
            const temp = this.findNode(nodei.children, keyValue, keyField)
            if (temp) return temp
          }
        }
      }
      return null
    },
    async handleUpdate(row) {
      console.log('handleUpdate11111', row)
      this.resetTemp()
      this.isSetAdminDb();
      //  若内网类型为0，即Ip地址和端口为实际IP地址和端口时，仅支持选择网卡中的IP信息，且端口不支持修改，为1时，端口和IP地址都可修改
      this.getServerNetworkInfoList(row.devId);
      await this.loadDbServer();
      this.temp = Object.assign(this.temp, row) // copy obj
      if (this.temp.groupId == 0) {
        this.temp.groupId = -1
      }
      if (this.temp.maxConnections && this.temp.maxConnections === -1) {
        this.temp.maxConnections = undefined
      }
      const node = this.findNode(this.treeData[0].children, this.temp.groupId, 'dataId')
      if (node && node.oriData && node.oriData.loadBanlance == 0) {
        this.unLoadBanlance = true
      } else {
        this.unLoadBanlance = false
      }
      //  主数据库是否为空
      this.sysmgrDbIdIsNull = row.extraInfo ? !row.extraInfo.sysmgrDbId : true
      //  设置主数据库Id
      this.temp.sysmgrDbId = row.extraInfo ? row.extraInfo.sysmgrDbId || null : null
      //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
      if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
        this.temp.intranetType = 1
      }

      if (this.temp.maxConnections) this.temp.isSetMax = 1
      this.formatIPTable()
    },
    // 格式化授权状态
    statusTableFilter(data) {
      const statusMap = {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      }
      return statusMap[data]
    },
    // 格式化采集组
    groupFormatter: function(groupId) {
      // const groupId = row.groupId;
      if (groupId === 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.treeData, groupId)
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    // 关闭弹窗前，校验数据是否发生变化，并提示
    beforeClose(done) {
      this.isServerDetail = false
      done()
    },
    // 点击确认提交数据
    async handleSaveConfig() {
    }
  }
}
</script>
<style lang="scss" scoped>
.show-detail-panel {
  color: #666;
  display: flex;
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
