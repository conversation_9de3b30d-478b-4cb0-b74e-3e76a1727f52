<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.collectionServiceEquipmentManagement')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="serverForm" :model="temp" label-position="right" label-width="120px" style="width: 750px;">
        <el-divider content-position="left">{{ $t('pages.collectServiceMessage') }}</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.serverNickname') + '：'">
              {{ temp.name }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.deviceNum') + '：'">
              {{ temp.devId }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.intranetAddress') + '：'">
              {{ temp.intranetIp }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.intranetPort') + '：'">
              {{ temp.intranetPort }}
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left">{{ $t('pages.collectionBindService') }}</el-divider>
        <div style="position: relative;top: -20px;">
          <i class="el-icon-remove-outline" style="float: right;color: #73b6e0;font-size: 20px;background-color: #e4e7e9;" @click="removeItem"/>
          <i class="el-icon-circle-plus-outline" style="float: right;color: #73b6e0;font-size: 20px;background-color: #e4e7e9;" @click="selectItem"/>
        </div>
        <el-tabs v-model="activeName" style="width: 99%;" @tab-click="tabClick">
          <el-tab-pane :label="$t('pages.server_other')" name="toBindServer">
            <grid-table
              ref="toBindServerTable"
              :col-model="updateColModel"
              :row-datas="rowData"
              :height="280"
              :show-pager="false"
              style="padding-bottom: 5px;"
              @selectionChangeEnd="handleSelectionChange"
            />
          </el-tab-pane>
          <!--屏蔽，暂时不使用-->
          <!--<el-tab-pane :label="$t('pages.server_daq')" name="toBindDaqServer">
            <grid-table
              v-show="activeName === 'toBindDaqServer'"
              ref="toBindDaqServerTable"
              :col-model="updateColModel1"
              :row-datas="rowData1"
              :height="183"
              :show-pager="false"
              style="padding-bottom: 5px;"
              @selectionChangeEnd="handleSelectionChange1"
            />
            <div v-show="activeName === 'toBindDaqServer'">
              <el-divider content-position="left" style="margin-top: 5px;">{{ $t('pages.serverConfigToBranch') }}</el-divider>
              <FormItem :label="$t('pages.syncType') + '：'" >
                <el-checkbox-group v-model="syncTypeChecked">
                  <el-checkbox v-for="item in syncTypeOptions" :key="item.value" :label="item.value">
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </FormItem>
              &lt;!&ndash;<FormItem :label="$t('pages.syncRate') + '：'" >
                <el-input v-model="syncRate" type="number" maxlength="11" :min="0" :max="99999999999"/>
              </FormItem>&ndash;&gt;
            </div>
          </el-tab-pane>-->
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <server-select-dlg ref="serverSelectDlg" :dlg-visible="dlgVisible" :dev-types="devTypes" @select="addItem"/>
  </div>
</template>

<script>
import ServerSelectDlg from '@/views/system/deviceManage/serverSelectDlg'
import { listBoundDaqServer, listBoundServer, allotServer } from '@/api/system/deviceManage/daqServer'
import { deepMerge } from '@/utils'

export default {
  name: 'DaqBindServerDlg',
  components: { ServerSelectDlg },
  data() {
    return {
      updateColModel: [
        { prop: 'devType', label: 'serviceType', width: '70', sort: true, formatter: this.typeFormatter },
        { prop: 'name', label: 'serviceNickName', width: '100' },
        { prop: 'devId', label: 'deviceNum', width: '80' },
        { prop: 'intranetIp', label: 'intranetIp', width: '80' },
        { prop: 'intranetPort', label: 'intranetPort', width: '80' }
      ],
      updateColModel1: [
        { prop: 'name', label: 'serviceNickName', width: '100' },
        { prop: 'devId', label: 'deviceNum', width: '80' },
        { prop: 'intranetIp', label: 'intranetIp', width: '80' },
        { prop: 'intranetPort', label: 'intranetPort', width: '80' }
      ],
      devTypes: [
        { value: 0, label: this.$t('pages.collectAll') },
        { value: 11, label: this.$t('route.backupServer') },
        { value: 12, label: this.$t('route.DetectionServer') },
        { value: 219, label: this.$t('route.DBServer') }
      ],
      tempUser: {},
      temp: {}, // 表单字段
      defaultTemp: {
        name: '',
        id: undefined,
        userId: undefined
      },
      query: { // 查询条件
        page: 1,
        limit: 100,
        serverId: undefined
      },
      query1: { // 查询条件
        page: 1,
        limit: 100,
        serverId: undefined
      },
      rowData: [],
      rowData1: [],
      isBindUser: true,
      dialogVisible: false,
      dlgVisible: false,
      submitting: false,
      updateRowData: [],
      activeName: 'toBindServer',
      syncRate: 0,
      syncTypeChecked: [],
      syncTypeOptions: [
        { value: 1, label: this.$t('pages.syncType_1') },
        { value: 2, label: this.$t('pages.syncType_2') },
        { value: 4, label: this.$t('pages.syncType_3') }
      ]

    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    tabClick(pane, event) {
    },
    hideTabs() {
      this.$nextTick(() => {
        if (this.temp.isBranch === true) {
          document.getElementById('tab-toBindDaqServer').style.display = 'none'
        }
        this.activeName = 'toBindServer'
      })
    },
    handleSelectionChange(val) {
    },
    handleSelectionChange1(val) {
    },
    resetTemp() {
      this.temp = deepMerge({ user: { name: '' }}, this.defaultTemp)
      this.activeName = 'toBindServer'
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    formatterSyncTypeChecked(syncType) {
      this.syncTypeChecked.splice(0)
      if (syncType) {
        this.syncTypeOptions.forEach(option => {
          if (syncType & option.value) {
            this.syncTypeChecked.push(option.value)
          }
        })
      }
    },
    handleUpdate(row) {
      this.dialogVisible = true
      this.temp = Object.assign({}, row)
      this.activeName = 'toBindServer'
      this.query.serverId = this.temp.devId
      this.query1.serverId = this.temp.devId
      this.syncRate = this.temp.syncRate
      this.formatterSyncTypeChecked(this.temp.syncType)
      this.hideTabs()
      this.$nextTick(() => {
        listBoundServer(this.query).then(resp => {
          this.rowData = resp.data
          this.rowData.forEach((item, i) => { item.id = i })
        })
        listBoundDaqServer(this.query1).then(resp => {
          this.rowData1 = resp.data
          this.rowData1.forEach((item, i) => { item.id = i })
        })
      })
    },
    selectItem() {
      this.dlgVisible = true
      this.$refs.serverSelectDlg.show(this.temp.devId, this.activeName)
    },
    addItem(datas) {
      this.dlgVisible = false
      const dataMap = {}
      if (this.activeName === 'toBindServer') {
        this.rowData.forEach(item => {
          dataMap['' + item.devType + item.devId] = item
        })
        for (let i = 0; i < datas.length; i++) {
          const item = datas[i]
          const oldItem = dataMap['' + item.devType + item.devId]
          if (!oldItem) {
            item.id = new Date().getTime() + i
            this.rowData.push(item)
          }
        }
      } else {
        this.rowData1.forEach(item => {
          dataMap['' + item.devType + item.devId] = item
        })
        for (let i = 0; i < datas.length; i++) {
          const item = datas[i]
          const oldItem = dataMap['' + item.devType + item.devId]
          if (!oldItem) {
            item.id = new Date().getTime() + i
            this.rowData1.push(item)
          }
        }
      }
    },
    removeItem() {
      const ids = this.activeName === 'toBindServer' ? this.$refs['toBindServerTable'].getSelectedIds() : this.$refs['toBindDaqServerTable'].getSelectedIds()
      if (this.activeName === 'toBindServer') {
        for (let i = 0; i < this.rowData.length; i++) {
          if (ids.indexOf(this.rowData[i].id) > -1) {
            this.rowData.splice(i, 1)
            i--
          }
        }
      } else {
        for (let i = 0; i < this.rowData1.length; i++) {
          if (ids.indexOf(this.rowData1[i].id) > -1) {
            this.rowData1.splice(i, 1)
            i--
          }
        }
      }
    },
    formatterSyncType() {
      let syncType = 0
      this.syncTypeChecked.forEach(type => {
        syncType += type
      })
      return syncType
    },
    updateData() {
      const toBindServers = this.$refs['toBindServerTable'].getDatas()
      /* const toBindDaqServers = this.$refs['toBindDaqServerTable'].getDatas() */
      const selectedServers = []
      const selectedDaqServers = []
      // 服务器名称，记录管理员日志用
      const selectedServerNames = []
      const selectedDaqServerNames = []
      toBindServers.forEach(data => {
        selectedServers.push(data.devId)
        selectedServerNames.push(data.name)
      })
      /* toBindDaqServers.forEach(data => {
        selectedDaqServers.push(data.devId)
        selectedDaqServerNames.push(data.name)
      }) */
      allotServer({
        serverName: this.temp.name,
        serverId: this.temp.devId,
        devServerIds: selectedServers.join(','),
        devServerNames: selectedServerNames.join(','),
        daqServerIds: selectedDaqServers.join(','),
        daqServerNames: selectedDaqServerNames.join(','),
        daqSyncType: this.formatterSyncType(),
        syncRate: this.syncRate
      }).then(() => {
        this.dialogVisible = false
        this.$emit('submitEnd')
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.collectService_notifyMsg21'),
          type: 'success',
          duration: 2000
        })
      })
    },
    typeFormatter(row, data) {
      for (let i = 0; i < this.devTypes.length; i++) {
        if (this.devTypes[i].value === data) {
          return this.devTypes[i].label
        }
      }
      return ''
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
