<template>
  <div>
    <Form ref="serverForm" :model="temp" label-position="right" label-width="150px" style="width: 640px;">
      <el-divider content-position="left"> {{ $t('pages.collectionGroupInfo') }} </el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.collectionGroupName') + '：'">{{ temp.name }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.region') + '：'">{{ temp.area }}</FormItem>
        </el-col>
        <el-col :span="6" >
          <FormItem :label="$t('pages.openLoad')">
            <el-switch v-model="temp.loadBanlance" :active-value="1" :inactive-value="0" />
          </FormItem>
        </el-col>
        <el-col :span="18" style="line-height: 35px; padding-left: 5px;">
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.openLoadContent_1">
                <br slot="br" />
              </i18n><br/>
              {{ $t('pages.openLoadContent_2') }}<br/>
              {{ $t('pages.openLoadContent_3') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-col>
      </el-row>
      <el-divider content-position="left">{{ $t('pages.collectionGroupBindServer') }}</el-divider>
      <FormItem :label="$t('pages.DBServer') + '：'">
        <el-select v-model="temp.dbIds" multiple :placeholder="$t('text.select')">
          <el-option v-for="item in dbServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <FormItem :label="$t('pages.BackupServer') + '：'">
        <el-select v-model="temp.backupIds" multiple :placeholder="$t('text.select')">
          <el-option v-for="item in backupServer.filter(server => !server.hidden)" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <FormItem v-permission="'A48'" :label="$t('pages.DetectionServer') + '：'">
        <el-select v-model="temp.detectionIds" multiple :placeholder="$t('text.select')">
          <el-option v-for="item in detectServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <FormItem :label="$t('route.softwareServer') + '：'">
        <el-select v-model="temp.softwareIds" multiple :disabled="temp.softwareReadOnly" :placeholder="$t('text.select')">
          <el-option v-for="item in softwareServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
        <span v-if="temp.softwareReadOnly" class="tip-text">{{ $t('pages.currentCollectionServerUseServer',{content:$t('route.softwareServer')}) }}</span>
      </FormItem>
      <FormItem v-permission="'A4L'" :label="$t('table.intelligentBackupServerGroup') + '：'">
        <el-select v-model="temp.smartBackupServerGroupId" clearable>
          <el-option
            v-for="item in smartBackupServerGroupData"
            :key="item.dataId"
            :label="item.label"
            :value="parseInt(item.dataId)"
          />
        </el-select>
      </FormItem>
      <div v-if="$store.getters.isMasterSubAble">
        <FormItem label-width="40px">
          <el-checkbox v-model="temp.syncEnable" :true-label="1" :false-label="0">{{ $t('pages.openTotalSync') }}</el-checkbox>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.totalSyncCollectionDescription">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <el-divider v-if="temp.syncEnable" content-position="left">{{ $t('pages.totalCollectionServerSyncMapper') }}</el-divider>
        <div v-if="temp.syncEnable && !isLoading">
          <label v-if="getSubDaqServer().length === 0" style="margin: 15px;color: #75B9E2FF;">{{ $t('pages.noAddBranchCollectionServer') }}</label>
          <label v-if="showMSError" style="color: #FF0000FF;">{{ $t('pages.branchCollectionServerMsg1') }}</label>
          <el-row v-for="item in getSubDaqServer()" :key="item.devId">
            <el-col :span="12">
              <FormItem :label="$t('pages.subsection') + $t('pages.server_daq') + '：'">
                <span style="white-space: nowrap;text-overflow: ellipsis;display: block;overflow: hidden;" :title="toOptionByServer(item)">
                  {{ toOptionByServer(item) }}
                </span>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.master') + $t('pages.server_daq') + '：'">
                <el-select v-model="temp.refMap[item.devId]" clearable :placeholder="$t('text.select')" style="height: 30px;">
                  <el-option v-for="item1 in getRefAbleDaqServer()" :key="item1.devId" :label="toOptionByServer(item1)" :value="item1.devId"/>
                </el-select>
              </FormItem>
            </el-col>
          </el-row>
        </div>
      </div>
    </Form>
  </div>
</template>

<script>
import { deepMerge, equalsObj } from '@/utils'
import { bindServer, getBindServer } from '@/api/system/deviceManage/serverGroup'

export default {
  name: 'DaqBindServerGroupFrom',
  props: {
    daqServer: {
      type: Array,
      default: null
    },
    dbServer: {
      type: Array,
      default: null
    },
    backupServer: {
      type: Array,
      default: null
    },
    detectServer: {
      type: Array,
      default: null
    },
    softwareServer: {
      type: Array,
      default: null
    },
    smartBackupServerGroupData: {
      type: Array,
      default: null
    },
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      oldLoadBalance: 0,
      oldMSAble: false,
      isLoading: false,
      showMSError: false,
      oldRefMap: {},
      temp: {},
      oldTemp: {}
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
  },
  activated() {
  },
  methods: {
    setFormData(data) {
      this.showMSError = false
      this.oldMSAble = 0
      this.oldLoadBalance = 0
      this.temp = deepMerge({
        refMap: {},
        syncEnable: 0
      }, data)
      this.oldTemp = deepMerge({}, this.temp)
      this.isLoading = true
      getBindServer(data.id).then(resp => {
        if (resp.data) {
          this.temp = deepMerge({
            refMap: {},
            dbIds: [],
            backupIds: [],
            detectionIds: [],
            softwareIds: [],
            smartBackupServerGroupId: undefined,
            subsectionDaqIds: resp.data.subsectionDaqIds,
            masterDaqIds: resp.data.masterDaqIds
          }, resp.data.server)
          if (resp.data.bind) {
            this.temp = deepMerge(this.temp, {
              dbIds: resp.data.bind.dbServers,
              backupIds: resp.data.bind.backServers,
              detectionIds: resp.data.bind.detectServers,
              softwareIds: resp.data.bind.softwareServers,
              smartBackupServerGroupId: resp.data.bind.smartBackupServerGroupId
            })
            if (resp.data.bind.refMap && Object.keys(resp.data.bind.refMap).length > 0) {
              this.temp.refMap = resp.data.bind.refMap
              this.oldRefMap = Object.assign({}, this.temp.refMap)
            }
          }
          this.oldMSAble = this.temp.syncEnable
          this.oldLoadBalance = this.temp.loadBanlance
          this.oldTemp = deepMerge({}, this.temp)
        }
        this.isLoading = false
      }).catch(() => { this.isLoading = false })
    },
    getSubDaqServer() {
      const result = []
      if (this.daqServer) {
        this.daqServer.forEach(item => {
          if (item.groupId && item.groupId == this.temp.id && (!this.temp.masterDaqIds || this.temp.masterDaqIds.indexOf(item.devId) < 0)) {
            result.push(item)
          }
        })
      }
      return result
    },
    getRefAbleDaqServer() {
      const list = [];
      this.daqServer.forEach(item => {
        if (this.temp.masterDaqIds && this.temp.masterDaqIds.indexOf(item.devId) > -1) {
          list.push(item)
        } else if (!item.groupId || item.groupId !== this.temp.id) {
          if (!this.temp.subsectionDaqIds || this.temp.subsectionDaqIds.indexOf(item.devId) < 0) {
            list.push(item)
          }
        }
      })
      return list
    },
    validSync() {
      if (this.temp.syncEnable && this.temp.refMap) {
        const subDevs = this.getSubDaqServer()
        for (let i = 0; i < subDevs.length; i++) {
          const subDevId = subDevs[i].devId
          const masterDevId = this.temp.refMap[subDevId]
          if (!masterDevId) {
            return false
          }
        }
      }
      return true
    },
    isFormDataChange() {
      const obj1 = this.getFormData(this.temp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData) {
      return {
        loadBanlance: tempData.loadBanlance,
        syncEnable: tempData.syncEnable,
        groupId: tempData.id,
        devType: 6,
        dbServers: tempData.dbIds,
        backServers: tempData.backupIds,
        detectServers: tempData.detectionIds,
        softwareServers: tempData.softwareIds,
        smartBackupServerGroupId: tempData.smartBackupServerGroupId,
        refMap: tempData.refMap
      }
    },
    submitData(callback) {
      this.showMSError = false
      if (!this.validSync()) {
        this.showMSError = true
        callback({ valid: false })
        return;
      }
      bindServer(this.getFormData(this.temp)).then(() => {
        callback({ refChange: this.isReflectChange(), balanceChange: this.temp.loadBanlance != this.oldLoadBalance })
        this.oldLoadBalance = this.temp.loadBanlance
        this.oldTemp = deepMerge({}, this.temp)
      }).catch((e) => {
        callback({ valid: false })
        console.log('提交失败！{}', e)
      })
    },
    isReflectChange() {
      if (this.oldMSAble !== this.temp.syncEnable) {
        return true
      } else if (!this.temp.syncEnable) { // 如果相等等，同时没有开启总分同步
        return false
      }
      if (Object.keys(this.oldRefMap).length !== Object.keys(this.temp.refMap).length) {
        return true
      }
      for (const key in this.oldRefMap) {
        const oldVal = this.oldRefMap[key]
        const curVal = this.temp.refMap[key]
        if (oldVal && oldVal != curVal) {
          return true
        }
      }
      return false
    },
    toOptionByServer(data) {
      let label = data.name;
      const { devId, intranetIpv6, internetIpv6, intranetIp, internetIp, intranetPort, internetPort } = data
      const ip = intranetIpv6 || internetIpv6 || intranetIp || internetIp
      if (ip) {
        const port = (intranetIpv6 || intranetIp) ? intranetPort : internetPort
        label += ` (${devId}_${ip}_${port})`
      }
      return label
    }
  }
}
</script>
<style lang="scss" scoped>
  .tip-text {
    color: #68a8d0;
    font-size: 12px;
  }
</style>
