<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 700px; margin-left:25px;">
        <el-row>
          <el-col :span="dialogStatus=='update' ? 12 : 24">
            <FormItem :label="$t('pages.serverName')" prop="name">
              <el-input v-model="temp.name" v-trim :maxlength="60"/>
            </FormItem>
          </el-col>
          <el-col v-show="dialogStatus=='update'" :span="12">
            <FormItem :label="$t('pages.deviceNum')" prop="devId">
              <el-input v-model="temp.devId" :maxlength="60" :disabled="true"/>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.collectionGroup')" prop="groupId">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.collectionGroupContent_1') }}
              <br/>
              <!-- {{ $t('pages.collectionGroupContent_2') }} -->
              <i18n path="pages.collectionGroupContent_2">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tree-select :data="groupTreeSelectData" node-key="dataId" :checked-keys="[temp.groupId]" :width="296" @change="groupIdSelectChange" />
        </FormItem>
        <!-- 主数据库ID authorizedStatus == null 表示该设备服务器未接入，不支持设置主数据库ID，仅旧设备数据存在这种情况-->
        <FormItem v-if="isSetAdminDbAuth && temp.authorizedStatus != null" :label="$t('pages.adminLibrary')" prop="sysmgrDbId">
          <div slot="label">
            <span style="color: red;">*</span>
            {{ $t('pages.adminLibrary') }}
            <el-tooltip effect="dark" placement="top-start">
              <div slot="content">
                {{ $t('pages.serverAccessAdminDbTip') }}<br>
                {{ $t('pages.serverAccessAdminDbTip1') }}<br>
                {{ $t('pages.serverAccessAdminDbTip3') }}<br>
                {{ $t('pages.serverAccessAdminDbTip4') }}<br>
                {{ $t('pages.serverAccessAdminDbTip5') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <el-select v-model="temp.sysmgrDbId">
            <el-option v-for="item in dbServers" :key="item.devId" :value="item.devId" :label="$t('pages.serverAccessDbLabel', { devName: item.name, devId: item.devId })"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="1" resize="none" maxlength="100" show-word-limit />
        </FormItem>
        <el-row>
        <!--<el-col :span="4">
          <FormItem label="开启均衡负载" label-width="100px">
            <el-switch v-model="temp.loadBanlance" :active-value="1" :inactive-value="0" :disabled="unLoadBanlance" />
          </FormItem>
        </el-col>-->
        <!--<el-col v-show="temp.loadBanlance==1" :span="9" >
          <FormItem label="负载率" prop="loadRate">
            <el-input v-model="temp.loadRate" :maxlength="3" />
          </FormItem>
        </el-col>
        <el-col v-show="temp.loadBanlance==1" :span="2" style="line-height: 30px; padding-left: 5px;">
          <el-tooltip class="item" effect="dark" placement="right">
            <div slot="content">负载率,为空时表示顺序负载；值为0时，接入的终端数表示不得超过额定连接数；其它数值表示均衡负载，其数值表示负载服务不能超过该数值。</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-col>-->
        </el-row>
        <FormItem :label="$t('pages.grantAuthor')" label-width="100px">
          <el-switch v-model="temp.adminAuthorized" :active-value="1" :inactive-value="0" />
        </FormItem>
        <el-tabs ref="tabs" v-model="tabName" style="min-height: 300px;">
          <el-tab-pane :label="$t('pages.basicInformation')" name="serverTab" style="padding: 5px 10px;">
            <input type="text" class="autocomplete">
            <input type="password" class="autocomplete">
            <FormItem v-if="false" :label="$t('pages.devPassword')" prop="accessPwd">
              <el-input v-model="temp.accessPwd" maxlength="20" type="password" show-password />
            </FormItem>
            <FormItem label="内网类型">
              <div slot="label">
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.intranetTypeTip1') }}<br>
                    {{ $t('pages.intranetTypeTip2') }}<br>
                    {{ $t('pages.intranetTypeTip3') }}<br>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.intranetType') }}
              </div>
              <el-select v-model="temp.intranetType" style="width: 200px" @change="intranetTypeChange">
                <el-option :key="0" :value="0" :disabled="temp.authorizedStatus === null" :label="$t('pages.truthIpAndPort')"/>
                <el-option :key="1" :value="1" :label="$t('pages.typeless')"/>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
              <el-select v-if="!temp.intranetType" v-model="temp.intranetIp" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
                <el-option v-for="(item, index) in ipv4s" :key="index" :value="item" :label="item"/>
              </el-select>
              <el-input v-else v-model="temp.intranetIp" v-trim maxlength="64" />
            </FormItem>
            <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
              <el-select v-if="!temp.intranetType" v-model="temp.intranetIpv6" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
                <el-option v-for="(item, index) in ipv6s" :key="index" :value="item" :label="item"/>
              </el-select>
              <el-input v-else v-model="temp.intranetIpv6" v-trim maxlength="64" />
            </FormItem>
            <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
              <el-input v-model="temp.intranetPort" :disabled="!temp.intranetType" maxlength="5" />
            </FormItem>
            <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
              <el-input v-model="temp.internetIp" v-trim maxlength="64" :disabled="temp.enableInternet==0" />
            </FormItem>
            <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
              <el-input v-model="temp.internetIpv6" v-trim maxlength="64" :disabled="temp.enableInternet==0" />
            </FormItem>
            <FormItem :label="$t('pages.internetPort')" prop="internetPort">
              <el-input v-model="temp.internetPort" maxlength="5" />
            </FormItem>
          <!--目前用不到这个字段，暂时隐藏-->
          <!--<FormItem label="版本号" prop="version" :disabled="true">
            <el-input v-model="temp.version" />
          </FormItem>-->
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.systemConfig')" name="extTab" style="padding: 5px; overflow: hidden;">
            <el-row>
              <el-col :span="11">
                <FormItem :label="$t('pages.ratedConnections')" label-width="120px" :extra-width="{en: 80}" prop="ratedConnections">
                  <el-input-number v-model="temp.ratedConnections" :controls="false" :max="99999" :min="1" :step="1" step-strictly @change="ratedConnectionsChange" />
                </FormItem>
              </el-col>

              <el-col :span="13">
                <FormItem label="1" label-width="120px" :extra-width="{en: 140}">
                  <span slot="label" :title="$t('pages.maximumConnection')">
                    <label>
                      <el-checkbox v-model="temp.isSetMax" :false-label="0" :true-label="1" @change="isSetMaxChange">
                        <span style="font-weight: bold;">{{ $t('pages.maximumConnection') }}</span>
                      </el-checkbox>
                    </label>
                  </span>
                  <el-input-number v-model="temp.maxConnections" :controls="false" :max="99999" :min="1" :step="1" step-strictly :disabled="temp.isSetMax===0" @change="maxConnectionsChange" />
                </FormItem>
              </el-col>
            </el-row>
            <el-card class="box-card" :body-style="{'padding': '5px'}">
              <div slot="header">
                <span>{{ $t('pages.terminalIPAccessRange') }}（{{ $t('pages.defaultUnlimited') }}
                  <el-tooltip class="item" effect="dark" :content="$t('pages.terminalIPAccessRangeContent')" placement="bottom-start">
                    <i class="el-icon-info" />
                  </el-tooltip>
                  ）
                </span>
                <span style="color:red">{{ allowIpValidMsg }}</span>
              </div>
              <div class="btn-box">
                <el-button size="small" @click="createIP">{{ $t('button.insert') }}</el-button>
                <el-button size="small" :disabled="!allowIpDeleteable" @click="deleteIP">{{ $t('button.delete') }}</el-button>
              </div>
              <grid-table
                ref="allowIpSegmentsTable"
                :height="160"
                row-no-label=" "
                :col-model="colModel1"
                :show-pager="false"
                :row-datas="allowIpSegments"
                :default-sort="defaultSort"
                :multi-select="multiSelect"
                @selectionChangeEnd="handleSelectionChange1"
              />
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="doSubmit">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <db-ip-exception-dlg ref="dbIpExceptionDlg"/>
  </div>
</template>

<script>
import { createServer, getServerByName, updateServer } from '@/api/system/deviceManage/daqServer';
import { compareIP, isIPv4, isIPv6 } from '@/utils/validate';
import { getServerGroupTree } from '@/api/system/deviceManage/serverGroup';
import { listDevServer, isSetAdminDb, getServerInfoByDevId } from '@/api/system/deviceManage/serverAccessApproval';
import DbIpExceptionDlg from '@/views/system/deviceManage/serverAccessApproval/config/dbIpExceptionDlg'

export default {
  name: 'EditDaqServer',
  components: { DbIpExceptionDlg },
  props: {
    groupTreeData: { type: Array, default() { return [] } }
  },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'name', order: 'desc' },
      dialogFormVisible: false,
      dialogStatus: '',
      groupTreeSelectData: [],
      treeSelectNode: [],
      defaultTreeSelectNode: [{ id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped') }],
      tabName: 'serverTab',
      allowIpDeleteable: false,
      editable: false,
      unLoadBanlance: false,
      allowIpValidMsg: '',
      submitting: false,
      textMap: {
        update: this.$t('pages.updateCollectionServer'),
        create: this.$t('pages.addCollectionServer'),
        updateGroup: this.$t('pages.updateCollectionGroup'),
        createGroup: this.$t('pages.addCollectionGroup')
      },
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        pwd: '',
        accessPwd: 'dev-server_12345',
        devId: undefined,
        groupId: undefined,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: undefined,
        internetPort: undefined,
        version: '',
        remark: '',
        allowIpSegments: '',
        ratedConnections: 2000,
        isSetMax: 0,
        maxConnections: undefined, // isSetMax==1时，maxConnections默认为3000
        loadRate: undefined,
        adminAuthorized: 1,     //  授权状态  与authorizedStatus同一个意思
        authorizedStatus: null, //  授权状态  若为null代表mapping表中不存在该设备信息，表示未接入，无法操作主数据库
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        loadBanlance: 0,
        encryptProps: ['accessPwd'],
        sysmgrDbId: null, //  主数据库Id
        extraInfo: {
          sysmgrDbId: null  //  主数据库Id
        }
      },
      allowIpSegments: [],
      treeData: [],
      colModel1: [
        { prop: 'startIp', label: 'startIP', type: 'input', width: '120' },
        { prop: 'endIp', label: 'endIP', type: 'input', width: '120' }
      ],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
          // { validator: this.validateUniqueName, trigger: 'blur' }   // 设备名称重复限制去除
        ],
        accessPwd: [
          { required: true, message: this.$t('pages.validateMsg_enterDevPassword'), trigger: 'blur' },
          { validator: this.validatePassword, trigger: 'blur' }
        ],
        loadRate: [
          { validator: this.validateLoadRateFormat, trigger: 'blur' }
        ],
        intranetIp: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        intranetIpv6: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        intranetPort: [
          { required: true, message: this.$t('pages.validateMsg_enterPort'), trigger: 'blur' }, // 暂时不需要，设置为非必填
          { validator: this.validatePort, trigger: 'blur' }
        ],
        internetPort: [
          { validator: this.validatePort, trigger: 'blur' }
        ],
        ratedConnections: [
          { required: true, message: this.$t('pages.validateMsg_enterRatedConnections'), trigger: 'blur' },
          { validator: this.validateConnections, trigger: 'blur' }
        ],
        maxConnections: [{ validator: this.validateConnections, trigger: 'blur' }],
        sysmgrDbId: [{ validator: this.validateSysmgrDbId, trigger: 'change' }]
      },
      severNetworkInfoList: [],  //  获取网卡中的信息
      ipv4s: [],  //  网卡中的IPv6
      ipv6s: [],  //  网卡中的IPv4
      dbServers: [], //  在线数据库信息
      sysmgrDbIdIsNull: false, //  该服务器是否未配置主数据库信息
      isSetAdminDbAuth: false   // 判断是否拥有设置采集服务器和检测服务器的主数据库权限
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    //  检测服务器或采集服务器设置的主数据库的Ip地址为虚拟Ip地址，即为127.0.0.1
    sysmgrIsVirtualIp() {
      if (this.temp.sysmgrDbId != null && this.isSetAdminDbAuth) {
        const db = this.dbServers.find(t => t.devId === this.temp.sysmgrDbId);
        return !!db && !!db.intranetIp && ['127.0.0.1', '0:0:0:0:0:0:0:1'].includes(db.intranetIp.trim())
      }
      return false;
    }
  },
  watch: {
    groupTreeData: {
      deep: true,
      handler() {
        this.resetSelectTreeData()
      }
    }
  },
  created() {
    this.getGroupTreeNode()
  },
  activated() {
    this.loadDbServer();
  },
  methods: {
    /**
     * 获取设备网卡信息
     * @param devId
     */
    getServerNetworkInfoList(devId) {
      this.ipv4s = []
      this.ipv6s = []
      getServerInfoByDevId(devId).then(res => {
        if (res.data) {
          this.severNetworkInfoList = res.data.serverNetworkInfoList || []
          this.severNetworkInfoList.forEach(t => {
            t.ips && this.ipv4s.push(...t.ips.split(';'))
            t.ipv6s && this.ipv6s.push(...t.ipv6s.split(';'))
          })
        }
      })
    },
    /**
     * 加载数据库信息
     */
    loadDbServer() {
      return listDevServer(219, 2).then(res => {
        this.dbServers = res.data || []
      })
    },
    getGroupTreeNode: function() {
      getServerGroupTree().then(respond => {
        this.groupTreeSelectData = [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }]
        this.treeData = [{ label: this.$t('pages.collectionGroup'), id: 'G0', dataId: '0', children: [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }] }]
        respond.data.forEach(el => {
          this.groupTreeSelectData.push(el)
          this.treeData[0].children.push(el)
        })
        this.changeTreeSelectNode()
      })
    },
    doSubmit() {
      if (this.dialogStatus === 'create') {
        this.createData()
      } else {
        this.updateData()
      }
    },
    changeTreeSelectNode() {
      this.treeSelectNode = [...this.defaultTreeSelectNode]
      this.treeData[0].children.forEach(node => {
        if (node.dataId != 0) this.treeSelectNode.push(node)
      })
    },
    gridTable1() {
      return this.$refs['allowIpSegmentsTable']
    },
    handleDrag() {
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    /**
     * 页面显示隐藏、初始数据渲染
     */
    async handleCreate(data) {
      this.resetTemp()
      this.isSetAdminDb();
      await this.loadDbServer();
      this.allowIpSegments.splice(0)
      if (data == -1) {
        this.temp.groupId = '-1'
      } else {
        this.temp.groupId = data
      }
      this.tabName = 'serverTab'
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.editable = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    formatIPTable() {
      this.allowIpSegments.splice(0)
      if (this.temp.allowIpSegments) {
        const time = new Date().getTime()
        let i = 1
        const ipSegments = this.temp.allowIpSegments.split(',')
        ipSegments.forEach(segment => {
          const ips = segment.split('-')
          this.allowIpSegments.push({
            id: time + i,
            startIp: ips[0],
            endIp: ips[1]
          })
          i++
        })
      }
    },
    resetSelectTreeData() {
      this.groupTreeSelectData = JSON.parse(JSON.stringify(this.groupTreeData))
    },
    findNode: function(nodes, keyValue, keyField) {
      if (nodes && nodes.length > 0) {
        for (const i in nodes) {
          const nodei = nodes[i]
          if (nodei[keyField] + '' === keyValue + '') {
            return nodei
          } else {
            const temp = this.findNode(nodei.children, keyValue, keyField)
            if (temp) return temp
          }
        }
      }
      return null
    },
    async handleUpdate(row) {
      this.resetTemp()
      this.isSetAdminDb();
      //  若内网类型为0，即Ip地址和端口为实际IP地址和端口时，仅支持选择网卡中的IP信息，且端口不支持修改，为1时，端口和IP地址都可修改
      this.getServerNetworkInfoList(row.devId);
      await this.loadDbServer();
      this.temp = Object.assign(this.temp, row) // copy obj
      if (this.temp.groupId == 0) {
        this.temp.groupId = -1
      }
      if (this.temp.maxConnections && this.temp.maxConnections === -1) {
        this.temp.maxConnections = undefined
      }
      const node = this.findNode(this.treeData[0].children, this.temp.groupId, 'dataId')
      if (node && node.oriData && node.oriData.loadBanlance == 0) {
        this.unLoadBanlance = true
      } else {
        this.unLoadBanlance = false
      }
      //  主数据库是否为空
      this.sysmgrDbIdIsNull = row.extraInfo ? !row.extraInfo.sysmgrDbId : true
      //  设置主数据库Id
      this.temp.sysmgrDbId = row.extraInfo ? row.extraInfo.sysmgrDbId || null : null
      //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
      if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
        this.temp.intranetType = 1
      }

      if (this.temp.maxConnections) this.temp.isSetMax = 1
      this.formatIPTable()
      this.tabName = 'serverTab'
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.editable = false
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * Form表单事件
     */
    groupIdSelectChange: function(data) {
      this.temp.groupId = data
      const node = this.findNode(this.treeData[0].children, data, 'dataId')
      if (!node || !node.oriData || node.oriData.loadBanlance == 0) {
        this.temp.loadBanlance = 0
        this.temp.loadRate = undefined
        this.unLoadBanlance = true
      } else {
        this.temp.loadBanlance = 1
        this.unLoadBanlance = false
      }
    },
    ratedConnectionsChange(val) {
      if (this.temp.maxConnections && val > this.temp.maxConnections) {
        this.temp.maxConnections = val
      }
    },
    isSetMaxChange(val) {
      if (val === 1) {
        if (this.temp.ratedConnections > 3000) {
          this.temp.maxConnections = this.temp.ratedConnections
        } else {
          this.temp.maxConnections = 3000
        }
      } else {
        this.temp.maxConnections = undefined
      }
    },
    maxConnectionsChange(val) {
      if (val < this.temp.ratedConnections) {
        this.temp.ratedConnections = val
      }
    },
    createIP() {
      this.allowIpValidMsg = ''
      let tempData = null
      if (this.allowIpSegments && this.allowIpSegments.length > 0) {
        tempData = this.allowIpSegments[0]
      }
      if (!tempData || tempData.startIp || tempData.endIp) {
        this.allowIpSegments.splice(0, 0, {
          id: new Date().getTime(),
          startIp: '',
          endIp: ''
        })
      }
    },
    deleteIP() {
      this.allowIpValidMsg = ''
      const toDeleteIds = this.gridTable1().getSelectedIds()
      for (let i = 0; i < this.allowIpSegments.length; i++) {
        const item = this.allowIpSegments[i]
        if (toDeleteIds.indexOf(item.id) > -1) {
          this.allowIpSegments.splice(i, 1)
          i--
        }
      }
    },
    handleSelectionChange1(val) {
      this.allowIpDeleteable = val.length > 0
    },
    /**
     * 新增、修改接口-start
     */
    formatSubmitData() {
      const ipSegments = []
      const allowIps = this.gridTable1().getDatas()
      allowIps.forEach(ips => {
        const ipRange = ips.startIp + '-' + ips.endIp
        if (ipSegments.indexOf(ipRange) < 0) {
          ipSegments.push(ipRange)
        }
      })
      this.temp.allowIpSegments = ipSegments.join(',')
      if (this.isSetAdminDbAuth) {
        //  设置主数据库Id
        this.temp.extraInfo.sysmgrDbId = this.temp.sysmgrDbId ? this.temp.sysmgrDbId : null
      } else {
        this.temp.extraInfo = null
      }
    },
    getGroupName(groupId) {
      this.groupTreeSelectData.forEach(node => {
        if (node.dataId != 0 && node.dataId == groupId) node.label
      })
      return this.$t('pages.null')
    },
    validateUniqueName(rule, value, callback) {
      getServerByName({
        name: value
      }).then(respond => {
        const server = respond.data
        if (server && server.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    validateAllowIp() {
      this.allowIpValidMsg = ''
      for (let i = 0; this.allowIpValidMsg.length === 0 && i < this.allowIpSegments.length; i++) {
        const rowData = this.allowIpSegments[i]
        if (!isIPv4(rowData.startIp) && !isIPv6(rowData.startIp)) {
          this.allowIpValidMsg = this.$t('pages.validateCollect_ip', { row: i + 1 })
        } else if (!isIPv4(rowData.endIp) && !isIPv6(rowData.endIp)) {
          this.allowIpValidMsg = this.$t('pages.validateCollect_ip1', { row: i + 1 })
        } else {
          // 判断ip是否全是ipv6的或者全是ipv4的
          if ((isIPv4(rowData.startIp) && isIPv6(rowData.endIp)) || (isIPv6(rowData.startIp) && isIPv4(rowData.endIp))) {
            this.allowIpValidMsg = this.$t('pages.validateCollect_ip3', { row: i + 1 })
          } else {
            // 判断前后ip的大小
            if (isIPv4(rowData.startIp)) {
              if (compareIP(rowData.startIp, rowData.endIp) > 0) {
                this.allowIpValidMsg = this.$t('pages.validateCollect_ip2', { row: i + 1 })
              }
            } else {
              const fullbeginIpv6 = this.getFullIPv6(rowData.startIp)
              const fullendIpv6 = this.getFullIPv6(rowData.endIp)
              if (fullbeginIpv6 > fullendIpv6) {
                this.allowIpValidMsg = this.$t('pages.validateCollect_ip2', { row: i + 1 })
              }
            }
          }
        }
      }
      return this.allowIpValidMsg.length === 0
    },
    validateErrorEnd(errorDom) {
      this.submitting = false
      const errorKeys = Object.keys(errorDom)
      if (errorKeys.length > 0 && errorKeys.indexOf('maxConnections') < 0 && errorKeys.indexOf('ratedConnections') < 0) {
        this.tabName = 'serverTab'
      } else {
        this.tabName = 'extTab'
      }
    },
    validateConnections(rule, value, callback) {
      if (rule.maxConnections) {
        if (rule.maxConnections < rule.ratedConnections) {
          callback(new Error(this.$t('pages.validateCollect_1')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validatePassword(rule, value, callback) {
      const numReg = /^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9_\-]{8,20})$/
      const number = new RegExp(numReg)
      if (!number.test(value)) {
        callback(new Error(this.$t('pages.validateCollect_3')))
      } else {
        callback()
      }
    },
    validateLoadRateFormat(rule, value, callback) {
      const reg = /^([1-9]\d*|[0]{1,1})$/
      if (value && (!reg.test(value) || Number(value) > 100)) {
        callback(new Error(this.$t('valid.sameName')))
      } else {
        callback()
      }
    },
    validatePort(rule, value, callback) {
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (value !== undefined && value !== '') {
        if (!number.test(value)) {
          callback(new Error(this.$t('pages.validateNaturalTree')))
        } else if (value <= 0 || value > 65535) {
          callback(new Error(this.$t('pages.validatePortRange')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateIPNum(rule, value, callback) {
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (!number.test(value) || value < 0 || value > 255) {
        callback(new Error(this.$t('pages.validateCollect_2')))
      } else {
        callback()
      }
    },
    validateIp(rule, value, callback) {
      let msg = ''
      if (!this.temp.intranetIp && !this.temp.intranetIpv6) {
        msg = this.$t('pages.ipValidate', { net: this.$t('pages.intranet') })
        callback(msg)
      } else {
        this.$refs['dataForm'].clearValidate(['intranetIp', 'intranetIpv6'])
        callback()
      }
    },
    /**
     * 校验主数据库Id
     * @param rule
     * @param value
     * @param callback
     */
    validateSysmgrDbId(rule, value, callback) {
      //  由于旧采集升级上来无法设置对于的主数据库Id，所以对于旧采集升级上来的数据不强制设置主数据库Id，新接入的采集数据在审批时是必须设置主数据库
      if (!this.sysmgrDbIdIsNull && !value) {
        callback(new Error(this.$t('pages.adminDbNotNull') + ''))
      }
      callback();
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid, errorDom) => {
        this.allowIpValidMsg = ''
        if (valid && this.validateAllowIp()) {
          if (this.sysmgrIsVirtualIp) {
            this.$refs.dbIpExceptionDlg.show(this.temp.sysmgrDbId);
            this.submitting = false
            return;
          }
          this.formatSubmitData()
          // 分组名称，用来记录管理员日志
          this.temp.groupName = this.getGroupName(this.temp.groupId)
          const tempData = JSON.parse(JSON.stringify(this.temp))
          createServer(tempData).then(respond => {
            tempData.id = respond.data.id
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd')
            /* this.$emit('addEnd', tempData) */
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.collectService_notifyMsg17'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
          this.validateErrorEnd(errorDom)
        }
      })
    },
    groupFormatter: function(row, data) {
      let name = this.$t('pages.ungrouped')
      if (row.group) {
        name = row.group.name
      }
      return name
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid, errorDom) => {
        this.allowIpValidMsg = ''
        if (valid && this.validateAllowIp()) {
          if (this.sysmgrIsVirtualIp) {
            this.$refs.dbIpExceptionDlg.show(this.temp.sysmgrDbId);
            this.submitting = false
            return;
          }
          this.formatSubmitData()
          // 分组名称，用来记录管理员日志
          this.temp.groupName_new = this.getGroupName(this.temp.groupId)
          this.temp.groupName = this.getGroupName(this.temp.groupId)
          const tempData = JSON.parse(JSON.stringify(this.temp))
          updateServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            // this.gridTable.execRowDataApi(this.query)
            this.$emit('submitEnd')
            /* this.$emit('updateEnd', tempData) */
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.collectService_notifyMsg18'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
          this.validateErrorEnd(errorDom)
        }
      })
    },
    /**
     * 判断是否拥有设置采集服务器和检测服务器的主数据库权限
     */
    isSetAdminDb() {
      isSetAdminDb().then(res => {
        this.isSetAdminDbAuth = res.data || false
      })
    },
    /**
     * 内网类型发生变更
     * @param value
     */
    intranetTypeChange(value) {
      //  IP有值且不在实际网卡IP中时，自动默认为实际网卡中的IP
      if (this.temp.intranetIp && this.ipv4s.length > 0 && !this.ipv4s.includes(this.temp.intranetIp)) {
        this.temp.intranetIp = this.ipv4s[0]
      }
      if (this.temp.intranetIpv6 && this.ipv6s.length > 0 && !this.ipv6s.includes(this.temp.intranetIpv6)) {
        this.temp.intranetIpv6 = this.ipv6s[0]
      }
      this.$refs.dataForm.validateField(['intranetIp', 'intranetIpv6', 'intranetPort'])
    },
    /**
     * 手动触发校验规则
     * @param fields
     */
    validateFieldClick(fields) {
      this.$refs.dataForm.validateField(fields)
    }
  }
}
</script>
