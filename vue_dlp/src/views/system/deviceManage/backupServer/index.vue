<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="false" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createDevice') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delDevice') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleDeviceMgr">
          {{ $t('pages.deviceMgr') }}
        </el-button>
        <!--<el-button disabled size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.serverName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.deviceNum')" prop="devId">
                <el-input v-model.number="query.devId" v-trim clearable :maxlength="4" :placeholder="this.$t('pages.serverLibrary_text5')" @input="number('devId')"/>
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
                <el-input v-model="query.intranetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
                <el-input v-model="query.intranetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
                <el-input v-model.number="query.intranetPort" v-trim maxlength="5" clearable :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('intranetPort')"/>
              </FormItem>
              <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
                <el-input v-model="query.internetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
                <el-input v-model="query.internetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetPort')" prop="internetPort">
                <el-input v-model.number="query.internetPort" v-trim maxlength="5" clearable :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('internetPort')"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="serverTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        :selectable="selectable"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <edit-backup-server ref="editBackupServer" @submitEnd="handleFresh"></edit-backup-server>
    <server-mgr-dlg ref="serverMgrDlg" :default-expanded-keys="['-2']" @submitEnd="handleFresh"/>
    <detail-backup-server ref="detailBackupServer"></detail-backup-server>
  </div>
</template>

<script>
import ServerMgrDlg from '../serverMgrDlg'
import EditBackupServer from './editBackupServer'
import { getServerPage, deleteServer } from '@/api/system/deviceManage/backupServer'
import { initTimestamp } from '@/utils'
import { updateAuthorizedStatus } from '@/api/system/deviceManage/serverAccessApproval';
import DetailBackupServer from './detailBackupServer'

export default {
  name: 'BackupServer',
  components: { DetailBackupServer, ServerMgrDlg, EditBackupServer },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { label: 'type', fixedWidth: '50', fixed: true, iconFormatter: this.isBranch },
        { prop: 'name', label: 'serverName', width: '120', fixed: true, sort: 'custom' },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: 'custom' },
        { prop: 'intranetIp', label: this.$t('pages.intranetIPV4Address'), width: '130', sort: 'custom' },
        { prop: 'intranetIpv6', label: this.$t('pages.intranetIPV6Address'), width: '130', sort: 'custom' },
        { prop: 'intranetPort', label: 'intranetPort', width: '100', sort: 'custom' },
        { prop: 'internetIp', label: this.$t('pages.internetIPV4Address'), width: '130', sort: 'custom' },
        { prop: 'internetIpv6', label: this.$t('pages.internetIPV6Address'), width: '130', sort: 'custom' },
        { prop: 'internetPort', label: 'internetPort', width: '100', sort: 'custom' },
        { prop: 'headquartersName', label: 'mappingServer', width: '100', formatter: this.headquartersNameFormatter },
        { prop: 'version', label: 'version', width: '100', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '260',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'detail', click: this.handleDetail },
            { label: 'grantAuthor', isShow: (row) => { return !(row.authorizedStatus === undefined || row.authorizedStatus === null) },
              disabledFormatter: (row) => this.grantButtonFormatter(row, 1), click: (row) => this.handleAuthorizedStatus(row, 1) },
            { label: 'cancelGrantAuthor', isShow: (row) => { return !(row.authorizedStatus === undefined || row.authorizedStatus === null) },
              disabledFormatter: (row) => this.grantButtonFormatter(row, 0), click: (row) => this.handleAuthorizedStatus(row, 0) }
          ]
        }
      ],
      deleteable: false,
      query: { // 查询条件
        page: 1,
        name: '',
        intranetIp: '',
        intranetIpv6: '',
        intranetPort: undefined,
        internetIp: '',
        internetIpv6: '',
        internetPort: undefined,
        devId: undefined
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    busenessMap() {
      const map = {}
      this.defaultBussDatas.forEach(item => {
        map[item.bussId] = item.label
      })
      return map
    }
  },
  created() {
    initTimestamp(this)
    // this.getGroupTreeNode()
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.$nextTick(() => {
      this.handleFilter();
    })
  },
  activated() {
    //  支持设备名称查询
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.handleFilter();
    // if (!isSameTimestamp(this, 'BackupServerGroup')) {
    //   this.getGroupTreeNode()
    // }
  },
  methods: {
    selectable(row, index) {
      return row.devId !== 3001
    },
    // resetBussTemp() {
    //   this.bussTemp = JSON.parse(JSON.stringify(this.defaultBussTemp))
    // },
    // getGroupTreeNode: function() {
    //   getServerGroupTree().then(respond => {
    //     if (respond && respond.data) {
    //       respond.data.forEach(el => {
    //         this.treeData[0].children.push(el)
    //         this.groupTreeSelectData.push(el)
    //       })
    //     }
    //   })
    // },
    // 单击树节点的回调函数
    // handleNodeClick(data, node) {
    //   node.expanded = true
    //   this.query.groupId = data.dataId
    //   this.gridTable.execRowDataApi()
    // },
    handleNameChange(data) {
      this.bussDatas.forEach(el => {
        if (el.bussName.indexOf('-') > -1) {
          const index = el.bussName.lastIndexOf('-')
          el.bussName = data + el.bussName.substring(index, el.bussName.length)
        } else {
          el.bussName = data + '-' + el.bussName
        }
      })
    },

    isBranch: function(row) {
      const online = row.onlineStatus == 1
      const style = online ? 'color: green' : 'color: #888'
      const labelPre = (online ? this.$t('pages.online') : this.$t('pages.offline')) + '\r\n'
      return row.isBranch ? { class: 'branch', title: labelPre + this.$t('pages.branchServer'), style } : row.isHeadquarters ? { class: 'controller', title: labelPre + this.$t('pages.headquartersServer'), style } : { class: 'server', title: labelPre + this.$t('pages.commonServer'), style }
    },
    headquartersNameFormatter: function(row) {
      return row.headquartersName ? row.headquartersName : this.$t('pages.null')
    },
    manageButtonFormatter: function(row, btn) {
      return row.isBranch
    },
    intranetIpFormatter: function(row) {
      if (row.intranetIp && row.intranetIpv6) {
        return row.intranetIp + ';  ' + row.intranetIpv6
      } else if (row.intranetIp) {
        return row.intranetIp
      } else if (row.intranetIpv6) {
        return row.intranetIpv6
      } else {
        return ''
      }
    },
    internetIpFormatter: function(row) {
      if (row.internetIp && row.internetIpv6) {
        return row.internetIp + ';  ' + row.internetIpv6
      } else if (row.internetIp) {
        return row.internetIp
      } else if (row.internetIpv6) {
        return row.internetIpv6
      } else {
        return ''
      }
    },
    handleCreate() {
      this.$refs.editBackupServer.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.editBackupServer.handleUpdate(row)
    },
    handleDetail(row) {
      this.$refs.detailBackupServer.show(row)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerPage(searchQuery)
    },
    statusTableFilter(row, data) {
      const statusMap = {
        true: this.$t('pages.connected'),
        false: this.$t('pages.notConnected')
      }
      return statusMap[data]
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },

    groupIdSelectChange: function(data) {
      this.temp.groupId = data
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFresh() {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateFile_4'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleDeviceMgr() {
      this.$refs['serverMgrDlg'].show()
    },
    groupFormatter: function(row, data) {
      let name = ''
      if (this.groupTree && this.groupTree.findNode(this.treeData[0].children, data, 'dataId')) {
        const node = this.groupTree.findNode(this.treeData[0].children, data, 'dataId')
        name = node.label
      }
      return name
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.intranetIp = ''
      this.query.intranetIpv6 = ''
      this.query.intranetPort = undefined
      this.query.internetIp = ''
      this.query.internetIpv6 = ''
      this.query.internetPort = undefined
      this.query.devId = undefined
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    },
    grantButtonFormatter: function(row, type) {
      return row.authorizedStatus == type
    },
    handleAuthorizedStatus(row, status) {
      updateAuthorizedStatus({ devId: row.devId, devType: 11, authorizedStatus: status }).then(respond => {
        row.authorizedStatus = status
        this.gridTable.updateRowData(row)
        this.$notify({
          title: this.$t('text.success'),
          message: status ? this.$t('pages.collectService_notifyMsg19') : this.$t('pages.collectService_notifyMsg20'),
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  span.disabled{
    color: #888;
  }
</style>
