//  文件服务器配置
import i18n from '@/lang'

//  文件服务器的备份服务器业务关联表配置信息  (注意：在此处添加新的配置信息后，后端 BackupServerBussDict 类也需要对应添加）
export const fileServerDefaultBusses = [
  { moduleId: 12, devId: undefined, bussId: 1, bussName: i18n.t('pages.documentOperationRecord'), label: i18n.t('pages.documentOperationRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  // 终端相关功能未实现，暂时不可用
  { moduleId: 1, devId: undefined, bussId: 2, bussName: i18n.t('pages.taskPush'), label: i18n.t('pages.taskPush'), relativePath: 'TaskPush', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 1 },
  { moduleId: 18, devId: undefined, bussId: 3, bussName: i18n.t('pages.recorderUsageRecord'), label: i18n.t('pages.recorderUsageRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 14, devId: undefined, bussId: 4, bussName: i18n.t('pages.screenVideoRecording'), label: i18n.t('pages.screenVideoRecording'), relativePath: 'ScreenVideo', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  // 终端相关功能未实现，暂时不可用
  { moduleId: 1, devId: undefined, bussId: 5, bussName: i18n.t('pages.patchManagement'), label: i18n.t('pages.patchManagement'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 1 },
  { moduleId: 17, devId: undefined, bussId: 6, bussName: i18n.t('pages.blueRecord'), label: i18n.t('pages.blueRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 35, devId: undefined, bussId: 7, bussName: i18n.t('pages.printRecord'), label: i18n.t('pages.printRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 31, devId: undefined, bussId: 8, bussName: i18n.t('pages.forumRecord'), label: i18n.t('pages.forumRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 34, devId: undefined, bussId: 9, bussName: i18n.t('pages.mailRecord'), label: i18n.t('pages.mailRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 36, devId: undefined, bussId: 10, bussName: i18n.t('pages.communicationToolFileTransferRecord'), label: i18n.t('pages.communicationToolFileTransferRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 31, devId: undefined, bussId: 11, bussName: i18n.t('pages.webTransferFileRecord'), label: i18n.t('pages.webTransferFileRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 32, devId: undefined, bussId: 12, bussName: i18n.t('pages.networkDiskRecord'), label: i18n.t('pages.networkDiskRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 1, devId: undefined, bussId: 13, bussName: i18n.t('pages.approvalLog'), label: i18n.t('pages.approvalLog'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 80, devId: undefined, bussId: 14, bussName: i18n.t('pages.sensitiveContentRecords'), label: i18n.t('pages.sensitiveContentRecords'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 51, devId: undefined, bussId: 15, bussName: i18n.t('pages.fileEncryption'), label: i18n.t('pages.fileEncryption'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 52, devId: undefined, bussId: 16, bussName: i18n.t('pages.recordsDirectOutsourcing'), label: i18n.t('pages.recordsDirectOutsourcing'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 14, devId: undefined, bussId: 17, bussName: i18n.t('pages.programActionRecord'), label: i18n.t('pages.programActionRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 1, devId: undefined, bussId: 18, bussName: i18n.t('pages.alarmLog'), label: i18n.t('pages.alarmLog'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 16, devId: undefined, bussId: 19, bussName: i18n.t('pages.USBOutgoingRecord'), label: i18n.t('pages.USBOutgoingRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 51, devId: undefined, bussId: 20, bussName: i18n.t('pages.seamlessReplacement'), label: i18n.t('pages.seamlessReplacement'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 1, hidden: 1 },
  { moduleId: 32, devId: undefined, bussId: 21, bussName: i18n.t('pages.FTPTransferRecord'), label: i18n.t('pages.FTPTransferRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 19, devId: undefined, bussId: 22, bussName: i18n.t('pages.localFileBackupRecord'), label: i18n.t('pages.localFileBackupRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 19, devId: undefined, bussId: 23, bussName: i18n.t('pages.netFileBackupRecord'), label: i18n.t('pages.netFileBackupRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 54, devId: undefined, bussId: 24, bussName: i18n.t('pages.fileTraceBackupRecord'), label: i18n.t('pages.fileTraceBackupRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 17, devId: undefined, bussId: 25, bussName: i18n.t('pages.adbBackupRecord'), label: i18n.t('pages.adbBackupRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 17, devId: undefined, bussId: 27, bussName: i18n.t('pages.mtpBackupRecord'), label: i18n.t('pages.mtpBackupRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 51, devId: undefined, bussId: 28, bussName: i18n.t('pages.whiteBackupRecord'), label: i18n.t('pages.whiteBackupRecord'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  // 远程桌面管控销售模块待确定 可能需要修改
  { moduleId: 32, devId: undefined, bussId: 31, bussName: i18n.t('route.remoteDesktopControlLog'), label: i18n.t('route.remoteDesktopControlLog'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 1, devId: undefined, bussId: 32, bussName: i18n.t('route.intelligentBackupServer'), label: i18n.t('route.intelligentBackupServer'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 1 },
  { moduleId: 32, devId: undefined, bussId: 33, bussName: i18n.t('route.AiControl'), label: i18n.t('route.AiControl'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 0, hidden: 0 },
  { moduleId: 1, devId: undefined, bussId: 34, bussName: i18n.t('pages.softBlackList_Msg12'), label: i18n.t('pages.softBlackList_Msg12'), relativePath: 'Record', enableDelete: 0, fileRetainDays: 90, disabled: 0, enabled: 1, hidden: 1 }
];
