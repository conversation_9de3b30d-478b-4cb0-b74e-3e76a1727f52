<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('table.resyncEnabledConfig')"
    :visible.sync="dialogVisible"
    width="600px"
  >
    <div>
      <span style="color: #0c60a5">{{ $t('pages.resyncEnabledDesc') }}</span>
      <el-radio-group v-model="resyncEnabled" style="margin-top: 20px; padding-left: 50px;">
        <el-radio :label="0">
          {{ $t('pages.resyncEnabled1') }}
          <el-tooltip effect="dark" placement="top">
            <div slot="content">
              {{ $t('pages.resyncEnabled_text1') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
        <br>
        <el-radio :label="1">
          {{ $t('pages.resyncEnabled2') }}
          <el-tooltip effect="dark" placement="top">
            <div slot="content">
              {{ $t('pages.resyncEnabled_text2') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
      </el-radio-group>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="submitData">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'SyncTypeEditDlg',
  data() {
    return {
      submitting: false,
      dialogVisible: false,
      resyncEnabled: 0
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
  },
  activated() {

  },
  methods: {
    submitData() {
      this.dialogVisible = false
      this.$emit('submit', this.resyncEnabled)
    },
    show(resyncEnabled) {
      this.resyncEnabled = resyncEnabled
      this.dialogVisible = true
    }
  }
}
</script>
