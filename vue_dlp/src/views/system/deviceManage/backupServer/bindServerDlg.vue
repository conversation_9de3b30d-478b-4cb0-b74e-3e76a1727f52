<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.backupServiceEquipmentManagement')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="serverForm" :model="temp" label-position="right" label-width="120px" style="width: 750px;">
        <el-divider content-position="left">{{ $t('pages.backupServiceMessage') }}</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.serverNickname') + '：'">
              {{ temp.name }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.deviceNum') + '：'">
              {{ temp.devId }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.intranetAddress') + '：'">
              {{ temp.intranetIp }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.intranetPort') + '：'">
              {{ temp.intranetPort }}
            </FormItem>
          </el-col>
        </el-row>
        <div style="margin-top: 11px; float: right;">
          <i class="el-icon-remove-outline" style="float: right;color: #73b6e0;font-size: 20px;background-color: #e4e7e9;" @click="removeItem"/>
          <i class="el-icon-circle-plus-outline" style="float: right;color: #73b6e0;font-size: 20px;background-color: #e4e7e9;" @click="selectItem"/>
        </div>
        <el-divider content-position="left" class="divider1">{{ $t('pages.backupBindService') }}</el-divider>
        <grid-table
          ref="toBindServerTable"
          :col-model="updateColModel"
          :row-datas="rowData"
          :height="200"
          :show-pager="false"
          @selectionChangeEnd="handleSelectionChange"
        />
        <el-divider content-position="left" style="margin-top: 5px;">{{ $t('pages.serverConfigToBranch') }}</el-divider>
        <el-card class="box-card" style="margin: 5px;">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.syncServiceSwitch') }}</span>
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">{{ $t('pages.syncServiceSwitch_text') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <div>
            <el-button type="text" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
            <el-button type="text" @click="selectAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
            <el-button type="text" @click="selectByHeadquarters">{{ $t('button.selectByHeadquarters') }}</el-button>
          </div>
          <FormItem label-width="5px">
            <el-checkbox-group v-model="syncTypeChecked">
              <el-row>
                <el-col v-for="item in syncTypeOptions" :key="item.value" :span="6">
                  <el-checkbox :label="item.value">{{ item.label }}</el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </FormItem>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <server-select-dlg ref="serverSelectDlg" :dlg-visible="dlgVisible" :dev-types="devTypes" @select="addItem"/>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.resyncEnabledConfig')"
      :visible.sync="dialogResyncEnabledVisible"
      width="600px"
    >
      <div>
        <span style="color: #0c60a5">{{ $t('pages.resyncEnabledDesc') }}</span>
        <el-radio-group v-model="resyncEnabled" style="margin-top: 20px; padding-left: 50px;">
          <el-radio :label="0">
            {{ $t('pages.resyncEnabled1') }}
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                {{ $t('pages.resyncEnabled_text1') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
          <br>
          <el-radio :label="1">
            {{ $t('pages.resyncEnabled2') }}
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                {{ $t('pages.resyncEnabled_text2') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateRowData">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogResyncEnabledVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ServerSelectDlg from '@/views/system/deviceManage/serverSelectDlg'
import { listBoundServer, allotServer } from '@/api/system/deviceManage/backupServer'
import { deepMerge } from '@/utils'

export default {
  name: 'BackupBindServerDlg',
  components: { ServerSelectDlg },
  data() {
    return {
      updateColModel: [
        { label: '', fixedWidth: '50', fixed: true, iconFormatter: this.resyncEnabledFormatter },
        { prop: 'name', label: 'serviceNickName', width: '100' },
        { prop: 'devId', label: 'deviceNum', width: '80' },
        { prop: 'intranetIp', label: 'intranetIp', width: '80' },
        { prop: 'intranetPort', label: 'intranetPort', width: '80' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '110',
          buttons: [
            { label: 'resyncEnabledChange', click: this.handleUpdateResyncEnabled }
          ]
        }
      ],
      devTypes: [
        { value: 0, label: this.$t('pages.collectAll') },
        { value: 11, label: this.$t('route.backupServer') },
        { value: 12, label: this.$t('route.DetectionServer') },
        { value: 20, label: this.$t('route.DBServer') }
      ],
      tempUser: {},
      temp: {}, // 表单字段
      defaultTemp: {
        name: '',
        id: undefined,
        userId: undefined
      },
      query: { // 查询条件
        page: 1,
        limit: 100,
        serverId: undefined
      },
      rowData: [],
      toRowData: [],
      toUpdateDevId: undefined,
      toUpdateResyncEnabled: false,
      isBindUser: true,
      dialogVisible: false,
      dlgVisible: false,
      dialogResyncEnabledVisible: false,
      submitting: false,
      resyncEnabled: 0,
      syncTypeChecked: [],
      syncTypeOptions: [
        { value: 1, label: this.$t('pages.documentOperationRecord'), bussId: 1 },
        { value: 4, label: this.$t('pages.recorderUsageRecord'), bussId: 3 },
        { value: 8, label: this.$t('pages.screenVideoRecording'), bussId: 4 },
        { value: 32, label: this.$t('pages.blueRecord'), bussId: 6 },
        { value: 64, label: this.$t('pages.printRecord'), bussId: 7 },
        { value: 128, label: this.$t('pages.forumRecord'), bussId: 8 },
        { value: 256, label: this.$t('pages.mailRecord'), bussId: 9 },
        { value: 512, label: this.$t('pages.communicationRecord'), bussId: 10 },
        { value: 1024, label: this.$t('pages.webUploadRecord'), bussId: 11 },
        { value: 2048, label: this.$t('pages.networkDiskRecord'), bussId: 12 },
        { value: 8192, label: this.$t('pages.sensitiveContentRecords'), bussId: 14 },
        { value: 16386, label: this.$t('pages.fileEncryption'), bussId: 15 },
        { value: 32768, label: this.$t('pages.recordsDirectOutsourcing'), bussId: 16 },
        { value: 65536, label: this.$t('pages.programActionRecord'), bussId: 17 },
        { value: 131072, label: this.$t('pages.alarmLog'), bussId: 18 },
        { value: 262144, label: this.$t('pages.USBOutgoingRecord'), bussId: 19 },
        { value: 1048576, label: this.$t('pages.FTPTransferRecord'), bussId: 21 },
        { value: 2097152, label: this.$t('pages.localFileBackupRecord'), bussId: 22 },
        { value: 4194304, label: this.$t('pages.netFileBackupRecord'), bussId: 23 },
        { value: 8388608, label: this.$t('pages.fileTraceBackupRecord'), bussId: 24 },
        { value: 16777216, label: this.$t('pages.adbBackupRecord'), bussId: 25 },
        { value: 67108864, label: this.$t('pages.mtpBackupRecord'), bussId: 27 },
        { value: 134217728, label: this.$t('pages.whiteBackupRecord'), bussId: 28 },
        { value: 1073741824, label: this.$t('route.remoteDesktopControlLog'), bussId: 32 }
      ]

    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.resetTemp()
  },
  activated() {

  },
  methods: {
    selectByHeadquarters() {
      this.syncTypeChecked.splice(0)
      const bussIds = []
      this.temp.businesses.forEach(buss => {
        if (buss.enabled === 1) {
          bussIds.push(buss.bussId)
        }
      })
      this.syncTypeOptions.forEach(option => {
        if (bussIds.indexOf(option.bussId) >= 0) {
          this.syncTypeChecked.push(option.value)
        }
      })
    },
    selectAll(boolean) {
      this.syncTypeChecked.splice(0)
      if (boolean) {
        this.syncTypeOptions.forEach(option => {
          this.syncTypeChecked.push(option.value)
        })
      }
    },
    tabClick(pane, event) {
      setTimeout(() => {
        this.handleFilter()
      }, 0)
    },
    handleSelectionChange(val) {
    },
    handleSelectionChange1(val) {
    },
    resetTemp() {
      this.temp = deepMerge({ user: { name: '' }}, this.defaultTemp)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    formatterSyncTypeChecked(syncType, syncTypeEx) {
      this.syncTypeChecked.splice(0)
      if (syncType || syncType === 0) {
        this.syncTypeOptions.forEach(option => {
          if (syncType & option.value) {
            this.syncTypeChecked.push(option.value)
          }
        })
      } else {
        const bussIds = []
        this.temp.businesses.forEach(buss => {
          if (buss.enabled === 1) {
            bussIds.push(buss.bussId)
          }
        })
        this.syncTypeOptions.forEach(option => {
          if (bussIds.indexOf(option.bussId) >= 0) {
            this.syncTypeChecked.push(option.value)
          }
        })
      }
    },
    handleUpdate(row) {
      this.dialogVisible = true
      this.temp = Object.assign({}, row)
      this.query.serverId = this.temp.devId
      this.syncTypeChecked.splice(0)
      this.$nextTick(() => {
        listBoundServer(this.query).then(resp => {
          this.rowData = []
          if (resp.data && resp.data.length > 0) {
            this.rowData = resp.data
            this.formatterSyncTypeChecked(this.rowData[0].syncType, this.rowData[0].syncTypeEx)
            this.rowData.forEach((item, i) => { item.id = i })
          } else {
            this.formatterSyncTypeChecked()
          }
        })
      })
    },
    selectItem() {
      this.dlgVisible = true
      this.$refs.serverSelectDlg.show(this.temp.devId, '', true)
    },
    updateRowData() {
      this.dialogResyncEnabledVisible = false
      this.dlgVisible = false
      const dataMap = {}
      if (this.rowData && this.rowData.length > 0) {
        this.rowData.forEach(item => {
          dataMap['' + item.devId] = item
        })
      }
      if (!this.toUpdateResyncEnabled) {
        const toChangeDevIds = []
        this.toRowData.forEach(data => {
          const oldItem = dataMap['' + data.devId]
          if (oldItem) {
            toChangeDevIds.push(data.devId)
          } else {
            data['resyncEnabled'] = this.resyncEnabled
            this.rowData.push(data)
          }
        })
        this.rowData.forEach(data => {
          if (toChangeDevIds.indexOf(data.devId) >= 0) {
            data['resyncEnabled'] = this.resyncEnabled
          }
        })
      } else {
        this.rowData.forEach(data => {
          if (data.devId === this.toUpdateDevId) {
            data.resyncEnabled = this.resyncEnabled
          }
        })
      }
    },
    addItem(datas) {
      this.toRowData.splice(0)
      this.toUpdateResyncEnabled = false
      for (let i = 0; i < datas.length; i++) {
        const item = datas[i]
        item.id = new Date().getTime() + i
        this.toRowData.push(item)
      }
      this.dialogResyncEnabledVisible = true
      this.resyncEnabled = 0
    },
    removeItem() {
      const ids = this.$refs['toBindServerTable'].getSelectedIds()
      for (let i = 0; i < this.rowData.length; i++) {
        if (ids.indexOf(this.rowData[i].id) > -1) {
          this.rowData.splice(i, 1)
          i--
        }
      }
    },
    formatterSyncType() {
      let syncType = 0
      this.syncTypeChecked.forEach(type => {
        syncType += type
      })
      return syncType
    },
    updateData() {
      const toBindServers = this.$refs['toBindServerTable'].getDatas()
      // 服务器名称，记录管理员日志用
      const selectedServerNames = []
      toBindServers.forEach(data => {
        selectedServerNames.push(data.name)
        data['syncType'] = this.formatterSyncType()
      })
      allotServer({
        serverName: this.temp.name,
        serverId: this.temp.devId,
        backupServerNames: selectedServerNames.join(','),
        backupServerList: toBindServers
      }).then(() => {
        this.dialogVisible = false
        this.$emit('submitEnd')
        if (!toBindServers || toBindServers.length === 0) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.collectService_notifyMsg22'),
            type: 'success',
            duration: 2000
          })
        } else {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.collectService_notifyMsg21'),
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    handleUpdateResyncEnabled(row) {
      this.toUpdateDevId = row.devId
      this.resyncEnabled = row.resyncEnabled
      this.toUpdateResyncEnabled = true
      this.dialogResyncEnabledVisible = true
    },
    resyncEnabledFormatter(row, data) {
      return row.resyncEnabled ? { class: 'sync-repeat', title: this.$t('pages.sync_repeat') } : { class: 'sync-once', title: this.$t('pages.sync_once') }
    }
  }
}
</script>
<style lang="scss" scoped>
.divider1 {
  width: 93%;
}
</style>
