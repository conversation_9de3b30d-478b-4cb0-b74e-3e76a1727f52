<template>
  <div>
    <Form ref="serverForm" :model="temp" :rules="rules" label-position="right" label-width="135px" style="width: 640px;">
      <el-divider content-position="left"> {{ $t('pages.backupServiceMessage') }} </el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.serverName') + '：'" class="ellipsis"><span :title="temp.name">{{ temp.name }}</span></FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.deviceNum') + '：'" class="ellipsis"><span :title="temp.devId">{{ temp.devId }}</span></FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetAddress') + '：'" class="ellipsis"><span :title="temp.intranetIp">{{ temp.intranetIp }}</span></FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetPort') + '：'" class="ellipsis"><span :title="temp.intranetPort">{{ temp.intranetPort }}</span></FormItem>
        </el-col>
      </el-row>
      <el-divider content-position="left">{{ $t('pages.backupServiceBindService') }}</el-divider>
      <FormItem :label="$t('pages.server_daq')" :tooltip-content="$t('pages.bindGatherGroupInfo')" tooltip-placement="bottom-start">
        <el-select v-model="temp.daqIds" multiple :placeholder="$t('text.select')" @change="changeValue($event)">
          <el-option v-for="item in daqServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <FormItem :label="$t('pages.server_approval') + '：'">
        <el-select v-model="newTemp.apprIds" multiple :placeholder="$t('text.select')">
          <el-option v-for="item in approvalServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <div v-if="$store.getters.isMasterSubAble">
        <FormItem v-if="!newTemp.isMaster" label-width="40px">
          <el-checkbox v-model="newTemp.msAble" :true-label="1" :false-label="0">{{ $t('pages.openTotalSync') }}</el-checkbox>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.totalSyncBackupDescription">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <el-divider v-if="newTemp.msAble && !newTemp.isMaster" content-position="left">{{ $t('pages.totalBackupServerMapper') }}</el-divider>
        <div v-if="newTemp.msAble && !newTemp.isMaster">
          <FormItem :label="$t('pages.master') + $t('pages.BackupServer') + '：'" prop="refDevId">
            <el-select v-model="newTemp.refDevId" clearable :placeholder="$t('text.select')">
              <el-option v-for="item in getRefAbleBackServer()" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
            </el-select>
          </FormItem>
          <FormItem :label="$t('pages.backupServerSyncTypeMsg')" label-width="275px">
            <el-button type="text" style="color: #666;background-color: white;border: 1px solid #ddd;padding: 5px 20px;" @click="handleResyncEnabled">{{ resyncEnabledFormatter() }}</el-button>
          </FormItem>
          <el-card class="box-card" style="margin: 5px;">
            <div slot="header" class="clearfix">
              <span>{{ $t('pages.syncServiceSwitch') }}</span>
            </div>
            <div>
              <el-button type="text" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
              <el-button type="text" @click="selectAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
              <el-button type="text" @click="selectByMaster">{{ $t('button.selectByHeadquarters') }}</el-button>
            </div>
            <FormItem label-width="5px">
              <el-checkbox-group v-model="syncTypeChecked">
                <el-row>
                  <el-col v-for="item in syncTypeOptions" :key="item.value" :span="8" style="height: 40px;">
                    <el-checkbox v-if="item.hidden===0" :label="item.value" :title="item.label" class="ellipsis" style="width: 95%;">{{ item.label }}</el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </FormItem>
          </el-card>
        </div>
        <el-divider v-if="newTemp.isMaster" content-position="left">{{ $t('pages.branchBackupServerMapper') }}</el-divider>
        <div v-if="newTemp.isMaster">
          <FormItem :label="$t('pages.subsection') + $t('pages.BackupServer') + '：'">
            <el-select v-model="newTemp.backupIds" multiple disabled :placeholder="$t('text.select')">
              <el-option v-for="item in backupServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
            </el-select>
          </FormItem>
          <label style="margin: 15px;color: #75b9e2;">{{ $t('pages.branchBackupServerMapperMsg') }}</label>
        </div>
      </div>
    </Form>
    <sync-type-edit-dlg ref="syncTypeEditDlg" @submit="selectResyncEnabled"/>
  </div>
</template>

<script>
import SyncTypeEditDlg from './syncTypeEditDlg'
import { deepMerge, equalsObj } from '@/utils'
import { bindServer, getBindServer, listBusinessByDevId, getDbAndDeviceRelation } from '@/api/system/deviceManage/backupServer'
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig';

export default {
  name: 'BackupBindServerFrom',
  components: { SyncTypeEditDlg },
  props: {
    daqServer: {
      type: Array,
      default: null
    },
    backupServer: {
      type: Array,
      default: null
    },
    approvalServer: {
      type: Array,
      default: null
    },
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      masterAble: false,
      temp: {},
      oldTemp: {},
      newTemp: {},
      groupTemp: {},
      nochangeTemp: {},
      resyncEnabledLabel: this.$t('pages.repeatUpload'),
      syncTypeChecked: [],
      syncTypeOptions: [
        { value: 1, label: this.$t('pages.documentOperationRecord'), bussId: 1, moduleId: 12, hidden: 0 },
        { value: 4, label: this.$t('pages.recorderUsageRecord'), bussId: 3, moduleId: 18, hidden: 0 },
        { value: 8, label: this.$t('pages.screenVideoRecording'), bussId: 4, moduleId: 14, hidden: 0 },
        { value: 32, label: this.$t('pages.blueRecord'), bussId: 6, moduleId: 17, hidden: 0 },
        { value: 64, label: this.$t('pages.printRecord'), bussId: 7, moduleId: 35, hidden: 0 },
        { value: 128, label: this.$t('pages.forumRecord'), bussId: 8, moduleId: 31, hidden: 0 },
        { value: 256, label: this.$t('pages.mailRecord'), bussId: 9, moduleId: 34, hidden: 0 },
        { value: 512, label: this.$t('pages.communicationRecord'), bussId: 10, moduleId: 36, hidden: 0 },
        { value: 1024, label: this.$t('pages.webTransferFileRecord'), bussId: 11, moduleId: 31, hidden: 0 },
        { value: 2048, label: this.$t('pages.networkDiskRecord'), bussId: 12, moduleId: 32, hidden: 0 },
        { value: 8192, label: this.$t('pages.sensitiveContentRecords'), bussId: 14, moduleId: 80, hidden: 0 },
        { value: 16386, label: this.$t('pages.fileEncryption'), bussId: 15, moduleId: 51, hidden: 0 },
        { value: 32768, label: this.$t('pages.recordsDirectOutsourcing'), bussId: 16, moduleId: 52, hidden: 0 },
        { value: 65536, label: this.$t('pages.programActionRecord'), bussId: 17, moduleId: 14, hidden: 0 },
        { value: 131072, label: this.$t('pages.alarmLog'), bussId: 18, moduleId: 1, hidden: 0 },
        { value: 262144, label: this.$t('pages.USBOutgoingRecord'), bussId: 19, moduleId: 16, hidden: 0 },
        { value: 1048576, label: this.$t('pages.FTPTransferRecord'), bussId: 21, moduleId: 32, hidden: 0 },
        { value: 2097152, label: this.$t('pages.localFileBackupRecord'), bussId: 22, moduleId: 19, hidden: 0 },
        { value: 4194304, label: this.$t('pages.netFileBackupRecord'), bussId: 23, moduleId: 19, hidden: 0 },
        { value: 8388608, label: this.$t('pages.fileTraceBackupRecord'), bussId: 24, moduleId: 54, hidden: 0 },
        { value: 16777216, label: this.$t('pages.adbBackupRecord'), bussId: 25, moduleId: 17, hidden: 0 },
        { value: 67108864, label: this.$t('pages.mtpBackupRecord'), bussId: 27, moduleId: 17, hidden: 0 },
        { value: 134217728, label: this.$t('pages.whiteBackupRecord'), bussId: 28, moduleId: 51, hidden: 0 },
        { value: 1073741824, label: this.$t('route.remoteDesktopControlLog'), bussId: 31, moduleId: 32, hidden: 0 }
      ],
      moduleIds: [],
      rules: {
        refDevId: [
          { required: true, validator: this.validateRefServerId, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.formatSyncTypeOptions()
  },
  activated() {
  },
  methods: {
    async formatSyncTypeOptions() {
      await listSaleModuleId().then(resp => {
        this.moduleIds.splice(0, this.moduleIds.length, ...resp.data)
      })
      const realOptions = []
      this.syncTypeOptions.forEach((data) => {
        if (this.moduleIds.indexOf(data.moduleId) > -1) {
          realOptions.push(data)
        }
      })
      if (realOptions && realOptions.length > 0) {
        this.syncTypeOptions.splice(0, this.syncTypeOptions.length, ...realOptions)
      }
    },
    changeValue(value) {
      const oldOptions = [...this.temp.daqIds];
      let exist = false
      let tempVariable = ''
      for (var i = 0; i < this.groupTemp.length; i++) {
        let exist1 = false
        for (var j = 0; j < oldOptions.length; j++) {
          if (this.groupTemp[i] == oldOptions[j]) {
            exist1 = true
          }
        }
        if (!exist1) {
          exist = true
          tempVariable = this.groupTemp[i]
        }
      }
      if (exist) {
        var position = this.noChangeTemp.daqIds.indexOf(tempVariable)
        this.temp.daqIds.splice(position, 0, tempVariable);
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.unableRemoveGatherGroup'),
          type: 'warning',
          duration: 1000
        })
        return
      }
      const stroeTemp = []
      var q = 0
      for (var k = 0; k < this.temp.daqIds.length; k++) {
        if (!this.groupTemp.includes(this.temp.daqIds[k])) {
          stroeTemp[q] = this.temp.daqIds[k]
          q++
        }
      }
      this.newTemp.daqIds = []
      for (var p = 0; p < stroeTemp.length; p++) {
        this.newTemp.daqIds[p] = stroeTemp[p]
      }
      this.noChangeTemp.daqIds = []
      for (var arr = 0; arr < this.temp.daqIds.length; arr++) {
        this.noChangeTemp.daqIds[arr] = this.temp.daqIds[arr]
      }
    },
    setFormData(data) {
      this.syncTypeChecked.splice(0)
      this.temp = deepMerge({
        refDevId: undefined,
        isMaster: false,
        resyncEnabled: 0,
        msAble: 0,
        syncType: 0
      }, data)
      this.oldTemp = deepMerge({}, this.temp)
      this.newTemp = deepMerge({}, this.temp)
      getDbAndDeviceRelation(data.devId).then(resp => {
        if (resp.data) {
          this.groupTemp = resp.data
          getBindServer(data.devId).then(resp => {
            if (resp.data) {
              this.temp = deepMerge({
                refDevId: undefined,
                daqIds: resp.data.daqIds,
                apprIds: resp.data.apprIds,
                isMaster: resp.data.isMaster,
                backupIds: [],
                resyncEnabled: 0,
                msAble: 0,
                syncType: 0,
                subsectionBackIds: resp.data.subsectionBackIds
              }, resp.data.server)
              if (this.temp.isMaster) {
                resp.data.subServer.forEach(item => {
                  this.temp.backupIds.push(item.devId)
                })
              } else {
                this.temp.msAble = this.temp.refDevId ? 1 : 0
                this.checkedSyncType(this.temp.syncType, this.temp.syncTypeEx)
              }
              this.oldTemp = deepMerge({}, this.temp)
              this.newTemp = deepMerge({}, this.temp)
              var k = this.temp.daqIds.length
              for (var i = 0; i < this.groupTemp.length; i++) {
                this.temp.daqIds[k] = this.groupTemp[i]
                k++
              }
              this.noChangeTemp = deepMerge({}, this.temp)
            }
          })
        }
      })
    },
    getRefAbleBackServer() {
      const list = [];
      this.backupServer.forEach(item => {
        if (this.newTemp.subsectionBackIds.indexOf(item.devId) < 0 && item.devId !== this.newTemp.devId) {
          list.push(item)
        }
      })
      return list
    },
    checkedSyncType(syncType, syncTypeEx) {
      this.syncTypeChecked.splice(0)
      if (syncType || syncType === 0) {
        this.syncTypeOptions.forEach(option => {
          if (syncType & option.value) {
            this.syncTypeChecked.push(option.value)
          }
        })
      } else {
        this.selectByMaster()
      }
    },
    selectByMaster() {
      this.syncTypeChecked.splice(0)
      if (this.newTemp.refDevId) {
        listBusinessByDevId(this.newTemp.refDevId).then(resp => {
          if (resp.data) {
            const bussIds = []
            resp.data.forEach(buss => {
              if (buss.enabled === 1) {
                bussIds.push(buss.bussId)
              }
            })
            this.syncTypeOptions.forEach(option => {
              if (bussIds.indexOf(option.bussId) >= 0) {
                this.syncTypeChecked.push(option.value)
              }
            })
          }
        })
      }
    },
    selectAll(boolean) {
      this.syncTypeChecked.splice(0)
      if (boolean) {
        this.syncTypeOptions.forEach(option => {
          this.syncTypeChecked.push(option.value)
        })
      }
    },
    handleResyncEnabled() {
      this.$refs['syncTypeEditDlg'].show(this.newTemp.resyncEnabled)
    },
    selectResyncEnabled(enable) {
      this.newTemp.resyncEnabled = enable
    },
    isFormDataChange() {
      this.newTemp.syncType = 0
      this.syncTypeChecked.forEach(type => { this.newTemp.syncType += type })
      const obj1 = this.getFormData(this.newTemp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData, syncType) {
      return {
        devType: tempData.devType,
        devId: tempData.devId,
        daqServers: tempData.daqIds,
        approvalServers: tempData.apprIds,
        backServers: tempData.msAble ? [tempData.refDevId] : [],
        isMaster: tempData.isMaster,
        syncType: tempData.syncType,
        resyncEnabled: tempData.resyncEnabled
      }
    },
    submitData(callback) {
      if (!this.newTemp.daqIds) {
        return;
      }
      this.$refs['serverForm'].validate((valid) => {
        if (!valid) {
          callback({ valid: false })
          return
        }
        this.newTemp.syncType = 0
        this.syncTypeChecked.forEach(type => { this.newTemp.syncType += type })
        bindServer(this.getFormData(this.newTemp)).then(() => {
          callback({ refChange: this.oldTemp.msAble !== this.newTemp.msAble })
          this.oldTemp = deepMerge({}, this.newTemp)
        })
      })
    },
    toOptionByServer(data) {
      let label = data.name;
      if (data.intranetIpv6) {
        label += ' (' + data.devId + '_' + data.intranetIpv6 + ':' + data.intranetPort + ')'
      } else if (data.internetIpv6) {
        label += ' (' + data.devId + '_' + data.internetIpv6 + ':' + data.internetPort + ')'
      } else if (data.intranetIp) {
        label += ' (' + data.devId + '_' + data.intranetIp + ':' + data.intranetPort + ')'
      } else if (data.internetIp) {
        label += ' (' + data.devId + '_' + data.internetIp + ':' + data.internetPort + ')'
      }
      return label
    },
    resyncEnabledFormatter() {
      return this.newTemp.resyncEnabled ? this.$t('pages.repeatUpload') : this.$t('pages.noRepeatUpload')
    },
    validateRefServerId(rule, value, callback) {
      if (this.newTemp.msAble && !this.newTemp.refDevId) {
        callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.master') + this.$t('pages.BackupServer')))
      } else {
        callback()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
