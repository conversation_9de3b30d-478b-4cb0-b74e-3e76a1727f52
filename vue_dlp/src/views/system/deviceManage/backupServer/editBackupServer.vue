<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @close="close"
    >
      <div>
        <input type="text" class="autocomplete">
        <input type="password" class="autocomplete">
      </div>
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="166px">
        <el-tabs ref="tabs" v-model="tabName" :before-leave="changeTab" style="height: 400px;">
          <el-tab-pane :label="$t('pages.basicInformation')" name="serverTab" style="padding: 10px; overflow: auto;">
            <FormItem v-if="temp.authorizedStatus != null" :label="$t('pages.grantAuthor')">
              <el-switch v-model="temp.authorizedStatus" :active-value="1" :inactive-value="0" />
            </FormItem>
            <FormItem :label="$t('pages.serverName')" prop="name">
              <el-input v-model="temp.name" v-trim maxlength="60"/>
            </FormItem>
            <FormItem v-if="false" :label="$t('pages.devPassword')" prop="accessPwd">
              <el-input v-model="temp.accessPwd" maxlength="20" type="password" show-password />
            </FormItem>
            <!-- 给userName, password 加密, v-if会查找不到dom-->
            <div v-if="showFileServerAccount">
              <FormItem :label="$t('pages.loginUser')" encrypt prop="userName">
                <el-input v-model="temp.userName" v-trim maxlength="20"/>
              </FormItem>
              <FormItem :label="$t('pages.loginPwd')" encrypt prop="password">
                <encrypt-input v-model="temp.password" v-trim maxlength="20"/>
              </FormItem>
            </div>
            <FormItem label="内网类型">
              <div slot="label">
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.intranetTypeTip1') }}<br>
                    {{ $t('pages.intranetTypeTip2') }}<br>
                    {{ $t('pages.intranetTypeTip3') }}<br>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.intranetType') }}
              </div>
              <el-select v-model="temp.intranetType" style="width: 200px" @change="intranetTypeChange">
                <el-option :key="0" :value="0" :disabled="temp.authorizedStatus === null" :label="$t('pages.truthIpAndPort')"/>
                <el-option :key="1" :value="1" :label="$t('pages.typeless')"/>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
              <el-select v-if="!temp.intranetType" v-model="temp.intranetIp" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
                <el-option v-for="(item, index) in ipv4s" :key="index" :value="item" :label="item"/>
              </el-select>
              <el-input v-else v-model="temp.intranetIp" v-trim maxlength="64" @blur="inputBlur('intranetIpv6')" />
            </FormItem>
            <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
              <el-select v-if="!temp.intranetType" v-model="temp.intranetIpv6" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
                <el-option v-for="(item, index) in ipv6s" :key="index" :value="item" :label="item"/>
              </el-select>
              <el-input v-else v-model="temp.intranetIpv6" v-trim :disabled="!temp.intranetType" maxlength="64" @blur="inputBlur('intranetIp')" />
            </FormItem>
            <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
              <el-input v-model="temp.intranetPort" v-trim :disabled="!temp.intranetType" maxlength="5" />
            </FormItem>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.enableInternetIP')" prop="enableInternet">
                  <el-switch v-model="temp.enableInternet" :active-value="1" :inactive-value="0" @change="enableInternetChange" />
                </FormItem>
              </el-col>
              <el-col :span="12" style="float: right">
                <FormItem :label="$t('pages.returnPublicNetwork')" :tooltip-content="$t('pages.fileServerContent_1')" prop="enableForceRetIP" tooltip-placement="bottom">
                  <el-switch v-model="temp.enableForceRetIP" :active-value="1" :inactive-value="0" :disabled="temp.enableInternet==0" />
                </FormItem>
              </el-col>
            </el-row>
            <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
              <el-input v-model="temp.internetIp" v-trim maxlength="64" :disabled="temp.enableInternet==0" @blur="inputBlur('internetIpv6')" />
            </FormItem>
            <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
              <el-input v-model="temp.internetIpv6" v-trim maxlength="64" :disabled="temp.enableInternet==0" @blur="inputBlur('internetIp')" />
            </FormItem>
            <FormItem :label="$t('pages.internetPort')" prop="internetPort">
              <el-input v-model="temp.internetPort" v-trim maxlength="5" :placeholder="$t('pages.fileServerContent_2')" :disabled="temp.enableInternet==0" />
            </FormItem>
            <FormItem :label="$t('pages.remark')" prop="remark">
              <el-input v-model="temp.remark" rows="2" resize="none" maxlength="100" show-word-limit />
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('route.configManage')" name="extTab" style="padding: 10px; overflow: auto;">
            <FormItem :label="$t('pages.operatingSystem')" prop="osType">
              <el-radio-group v-model="temp.osType" @change="selectOsTypeValiate">
                <el-radio :label="1">Windows</el-radio>
                <el-radio :label="2">Linux</el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem :label="$t('pages.backupPrimaryPath')" prop="mainPath">
              <el-input v-model="temp.mainPath" v-trim :maxlength="255" :style="{ width: temp.osType === 1 && !viewDisk ? 'calc(100% - 50px)' : '100%' }"/>
              <el-button v-if="temp.osType === 1 && !viewDisk" :disabled="!temp.onlineStatus" type="primary" icon="el-icon-view" size="mini" style="margin-bottom: 0" @click="viewDiskInfo"/>
            </FormItem>
            <FormItem v-if="temp.osType == 1" :label="$t('pages.backupExpansionPath')" :tooltip-content="$t('pages.backupExpansionPathTip')" tooltip-placement="bottom-start">
              <tag ref="extPathTag" :border="true" editable :list="temp.extPaths" :valid-rule="rules.pathRule" :limit-size="24" :input-width="536" input-length="255" @tagChange="tagChange"/>
            </FormItem>
            <!-- 磁盘信息 -->
            <disk-info v-if="temp.devType === 11" ref="diskInfo" :disk-list="disks" :os-type="temp.osType" @closeFuc="viewDisk = false"/>

            <el-row>
              <el-col :span="14">
                <FormItem :label="$t('pages.dataPortRange')" :tooltip-content="$t('pages.fileServerContent_3')" prop="minDataPort" tooltip-placement="bottom-start">
                  <el-input v-model="temp.minDataPort" maxlength="5" />
                </FormItem>
              </el-col>
              <el-col :span="1" style="line-height: 30px; padding-left: 10px;">
                --
              </el-col>
              <el-col :span="9">
                <FormItem prop="maxDataPort" label-width="0px">
                  <el-input v-model="temp.maxDataPort" maxlength="5" />
                </FormItem>
              </el-col>
            </el-row>
            <FormItem :label="$t('pages.uploadTrafficLimit')" :tooltip-content="$t('pages.fileServerContent_4')" prop="uploadSpeedLimit" tooltip-placement="bottom-start">
              <el-input v-model="temp.uploadSpeedLimit" maxlength="4" @focus="focus">
                <template slot="append">KB/S</template>
              </el-input>
            </FormItem>
            <FormItem :label="$t('pages.downloadTrafficLimit')" :tooltip-content="$t('pages.fileServerContent_4')" prop="downSpeedLimit" tooltip-placement="bottom-start">
              <el-input v-model="temp.downSpeedLimit" maxlength="4" @focus="focus">
                <template slot="append">KB/S</template>
              </el-input>
            </FormItem>
            <FormItem :label="$t('pages.diskWriteLimit')" :tooltip-content="$t('pages.fileServerContent_5')" prop="diskSpaceLimit" tooltip-placement="bottom-start">
              <!-- 修改最大值为9999GB -->
              <el-input v-model="diskSpaceLimit" type="number" maxlength="5" @input="diskSpaceLimitChange(arguments[0], 'diskSpaceLimit', 1, 10238976)">
                <template slot="append">
                  <el-select v-model="temp.diskSpaceLimitUnit" style="width: 72px;" @change="diskSpaceLimitUnitChange">
                    <el-option label="GB" :value="1"/>
                    <el-option label="MB" :value="0"/>
                  </el-select>
                </template>
              </el-input>
            </FormItem>
            <FormItem :label="$t('pages.maxTransferNum')" :tooltip-content="$t('pages.fileServerContent_6')" prop="maxOccurs" tooltip-placement="bottom-start">
              <el-input v-model="temp.maxOccurs" type="number" maxlength="5" @input="numberLimit(arguments[0], 'maxOccurs', 1, 99999)" />
            </FormItem>
          </el-tab-pane>
          <el-tab-pane v-if="!smartServerId" :label="$t('pages.businessInformation')" name="businessTab" style="overflow: auto;">
            <el-card class="box-card" style="margin: 5px;">
              <div slot="header" class="clearfix">
                <span>{{ $t('pages.serviceSwitch') }}</span>
              </div>
              <div style="line-height: 22px;">
                <el-button type="text" @click="selectAll(true, 1)">{{ $t('button.selectAll') }}</el-button>
                <el-button type="text" @click="selectAll(false, 1)">{{ $t('button.cancelSelectAll') }}</el-button>
                <el-button type="text" @click="batchServiceConfig()">{{ $t('button.batchServiceConfig') }}</el-button>
                <el-checkbox-group v-model="checkedModuleIds" style="margin-top: 6px;">
                  <el-row>
                    <el-col v-for="(item, index) in bussDatas" :key="index" :span="8">
                      <el-checkbox v-if="item.hidden===0" :label="item.bussId" :disabled="item.disabled===1" @change="((val,$event)=>handleChange(val,$event,index))">
                        <span :title="item.label" class="ellipsis label-text">{{ item.label }}</span>
                        <el-tooltip v-show="item.enabled === 1" class="item" effect="dark" placement="bottom-start">
                          <div v-show="item.enableDelete === 1" slot="content">
                            {{ $t('pages.backup_buss_config_msg1',{ path: item.relativePath, day: item.fileRetainDays }) }}
                          </div>
                          <div v-show="item.enableDelete === 0" slot="content">
                            {{ $t('pages.backup_buss_config_msg4',{ path: item.relativePath }) }}
                          </div>
                          <i class="el-icon-info" />
                        </el-tooltip>
                        <i v-show="item.enabled === 1" class="el-icon-edit" :title="$t('pages.serviceConfig')" @click.stop.prevent="updateBussConfig(item)"/>
                      </el-checkbox>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </div>
            </el-card>
            <el-card class="box-card" style="margin: 5px;">
              <div slot="header" class="clearfix">
                <span>{{ $t('button.highConfig') }}</span>
              </div>
              <div style="line-height: 22px;">
                <!--暂时只有一个配置项，暂时不需要全选功能-->
                <!--<el-button type="text" @click="selectAll(true, 2)">{{ $t('button.selectAll') }}</el-button>
                <el-button type="text" @click="selectAll(false, 2)">{{ $t('button.cancelSelectAll') }}</el-button>-->
                <el-checkbox-group v-model="checkedModuleIds" style="margin-top: 6px;">
                  <el-row>
                    <el-col v-for="(item, index) in specialBussDatas" :key="index" :span="8">
                      <el-checkbox v-if="item.hidden===0" :label="item.bussId" :disabled="item.disabled===1" @change="((val,$event)=>handleSpecialChange(val,$event,index))">
                        <span :title="item.label" class="ellipsis label-text">{{ item.label }}</span>
                        <el-tooltip v-show="item.enabled === 1" class="item" effect="dark" :content="$t('pages.backup_buss_config_msg3')" placement="bottom-start">
                          <i class="el-icon-question" />
                        </el-tooltip>
                        <el-tooltip v-show="item.enabled === 1" class="item" effect="dark" placement="bottom-start">
                          <div v-show="item.enableDelete === 1" slot="content">
                            {{ $t('pages.backup_buss_config_msg1',{ path: item.relativePath, day: item.fileRetainDays }) }}
                          </div>
                          <div v-show="item.enableDelete === 0" slot="content">
                            {{ $t('pages.backup_buss_config_msg4',{ path: item.relativePath }) }}
                          </div>
                          <i class="el-icon-info" />
                        </el-tooltip>
                        <i v-show="item.enabled === 1" class="el-icon-edit" :title="$t('pages.serviceConfig')" @click.stop.prevent="updateBussConfig(item)"/>
                      </el-checkbox>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="close">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="bussTemp.bussName"
      :visible.sync="bussConfigDialogVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="bussConfigForm" :rules="bussRules" :model="bussTemp" label-position="right" label-width="125px" style="width: 450px;">
        <FormItem :label="$t('pages.businessBackupPath')" :tooltip-content="$t('pages.fileServerContent_7')" prop="relativePath" tooltip-placement="bottom-start">
          <el-input v-model="bussTemp.relativePath" :maxlength="255"/>
        </FormItem>
        <FormItem label-width="120px">
          <el-checkbox v-model="bussTemp.enableDelete" :false-label="0" :true-label="1" :label="$t('pages.saveOnlyRecent')" class="df-color">
            <i18n path="pages.fileServerContent_9">
              <el-input-number
                slot="days"
                v-model="bussTemp.fileRetainDays"
                :controls="false"
                :min="1"
                :max="999"
                maxlength="3"
                style="width: 60px;"
                :precision="0"
                :disabled="bussTemp.enableDelete === 0"
              />
            </i18n>
          </el-checkbox>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="bussSubmitting" type="primary" @click="updateBussConfigData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="bussConfigDialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="batchServiceConfigVisible"
      width="700px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('pages.businessInformationConfig') }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.backup_buff_config_msg6">
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="batchBussConfigForm" :rules="batchBussRules" :model="batchBussTemp" label-position="right" label-width="180px" style="width: 630px;">
        <div style="display: flex;align-items: center;">
          <el-checkbox v-model="checkRelativePath" @change="pathChange()"></el-checkbox>
          <FormItem :label="$t('pages.businessBackupPath')" :tooltip-content="$t('pages.fileServerContent_7')" prop="relativePath" tooltip-placement="bottom-start" style="margin-left: -5px;">
            <el-input v-model="batchBussTemp.relativePath" :maxlength="255" :disabled="pathInput"/>
          </FormItem>
        </div>
        <div style="display: flex;align-items: center;">
          <el-checkbox v-model="checkRelativeDays" @change="daysChange()"></el-checkbox>
          <FormItem :label="$t('pages.backup_buss_config_msg5')" prop="fileRetainDays" style="margin-left: -5px;">
            <el-input-number v-model="batchBussTemp.fileRetainDays" :min="1" :max="999" :maxlength="3" :precision="0" :controls="false" :disabled="daysInput"/>
          </FormItem>
        </div>
      </Form>

      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.serviceSwitch') }}</span>
        </div>
        <div style="line-height: 22px;">
          <el-checkbox-group v-model="selectedCheckedModuleIds" style="margin-top: 6px;">
            <el-row>
              <el-col v-for="(item, index) in selectedDatas" :key="index" :span="8">
                <el-checkbox v-if="item.hidden===0" :label="item.bussId" :disabled="item.disabled===1" @change="((val,$event)=>bacthHandleChange(val,$event,index))">
                  <span :title="item.label" class="ellipsis label-text">{{ item.label }}</span>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="batchBussSubmitting" type="primary" @click="updateBatchBussConfigData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelBatchConfig()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getServerGroupTree } from '@/api/system/deviceManage/serverGroup';
import { initTimestamp, isSameTimestamp } from '@/utils';
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig';
import { createServer, getServerByName, updateServer } from '@/api/system/deviceManage/backupServer';
import { getPropertyByCode } from '@/api/property'
import EncryptInput from '@/components/EncryptInput'
import { isIPv4 } from '@/utils/validate'
import { fileServerDefaultBusses } from '@/views/system/deviceManage/backupServer/backupServerCommonInfo'
import { getServerInfoByDevId } from '@/api/system/deviceManage/serverAccessApproval';
import DiskInfo from '@/views/system/deviceManage/serverAccessApproval/config/DiskInfo';

export default {
  name: 'EditBackupServer',
  components: { EncryptInput, DiskInfo },
  props: {},
  data() {
    return {
      serverTabValidFields: ['name', 'userName', 'password', 'intranetIp', 'internetIp', 'intranetIpv6', 'internetIpv6', 'intranetPort', 'internetPort'],
      extTabValidFields: ['mainPath', 'uploadSpeedLimit', 'downSpeedLimit', 'diskSpaceLimit', 'diskSpaceLimitUnit', 'diskShortageAlert', 'maxOccurs', 'minDataPort', 'maxDataPort', 'transferMode', 'osType'],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
          // { validator: this.validateUniqueName, trigger: 'blur' }   // 设备名称重复限制去除
        ],
        intranetIp: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        internetIp: [
          { validator: this.validateInternetIp, trigger: 'blur' }
        ],
        intranetIpv6: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        internetIpv6: [
          { validator: this.validateInternetIp, trigger: 'blur' }
        ],
        intranetPort: [
          { required: true, validator: this.validatePort, trigger: 'blur' }
        ],
        internetPort: [
          { validator: this.validateInternetPort, trigger: 'blur' }
        ],
        mainPath: [
          { required: true, validator: this.validateMainPath, trigger: 'blur' }
        ],
        pathRule: [
          { validator: this.validatePath, trigger: 'blur' }
        ],
        userName: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        password: [
          { required: true, validator: this.validatePassword, trigger: 'blur' }
        ],
        accessPwd: [
          { required: true, validator: this.validatePassword, trigger: 'blur' }
        ],
        uploadSpeedLimit: [
          { required: true, validator: this.speedLimitValidate, trigger: 'blur' }
        ],
        downSpeedLimit: [
          { required: true, validator: this.speedLimitValidate, trigger: 'blur' }
        ],
        diskSpaceLimit: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        diskShortageAlert: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        maxOccurs: [
          { required: true, validator: this.validateRequired, trigger: 'blur' }
        ],
        minDataPort: [
          { required: true, validator: this.validatePort, trigger: 'blur' }
        ],
        maxDataPort: [
          { required: true, validator: this.validatePort, trigger: 'blur' }
        ],
        transferMode: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        osType: [
          { required: true, validator: this.validateOsType, trigger: 'blur' }
        ]
      },
      bussRules: {
        relativePath: [
          { required: true, message: this.$t('pages.fileServerContent_8'), trigger: 'blur' }
        ]
      },
      batchBussRules: {
        // fileRetainDays: [
        //   { required: true, message: this.$t('pages.fileServerContent_12'), trigger: 'blur' }
        // ]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      checkedModuleIds: [],
      selectedCheckedModuleIds: [], // 用于批量配置时存储选中的业务的bussId
      specialBussIds: [13],
      specialBussDatas: [],
      bussDatas: [],
      selectedDatas: [], // 用于批量配置时存储选中的业务
      defaultBussDatas: fileServerDefaultBusses,
      tabName: 'serverTab',
      showTree: false,
      showFileServerAccount: false,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        devId: undefined,
        groupId: 0,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: null,
        internetPort: undefined,
        active: false,
        version: '',
        remark: '',
        mainPath: '',
        extPaths: [],
        userName: undefined,
        password: undefined,
        uploadSpeedLimit: 0,
        downSpeedLimit: 0,
        minDataPort: 12000,
        maxDataPort: 12030,
        maxOccurs: 200,
        diskSpaceLimit: 51200,
        diskSpaceLimitUnit: 1,
        diskShortageAlert: 5120,
        enableInternet: 0,
        enableForceRetIP: 0,
        businesses: [],
        transferMode: 0,
        // 后台数据库要求必填，给一个默认值
        accessPwd: 'dev-server_12345',
        // 操作系统（1：windows 2：linux）
        osType: 1,
        encryptProps: ['accessPwd'],
        authorizedStatus: null,   //  授权状态，即开关,0-关闭，1-打开， null：表示该设备数据是旧数据，且尚未连接引擎，此数据不允许修改开关
        intranetType: null     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
      },
      bussTemp: {},
      batchBussTemp: {}, // 批量配置业务备份路径与文件保留时间存储对象
      dialogFormVisible: false,
      bussConfigDialogVisible: false,
      batchServiceConfigVisible: false, // 批量业务配置弹窗控制
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.editFileServer'),
        create: this.$t('pages.addFileServer')
      },
      treeData: [
        { label: this.$t('pages.serverGroup'),
          dataId: '',
          children: [{ label: this.$t('pages.ungrouped'), dataId: '0' }]
        }
      ],
      groupTreeSelectData: [{ label: this.$t('pages.null'), dataId: '0' }],
      submitting: false,
      bussSubmitting: false,
      batchBussSubmitting: false,
      validateArr: [],                  // tab页面有字段验证不通过，就将tab名放到数组中，以实现tab跳转
      extPathError: false,              // 标记正在输入的备份路径是否有错误
      moduleIds: [],
      editable: false,
      checkRelativePath: false,
      checkRelativeDays: false,
      pathInput: true,
      daysInput: true,
      diskSpaceLimit: 1,
      smartServerId: undefined,
      severNetworkInfoList: [],  //  获取网卡中的信息
      ipv4s: [],  //  网卡中的IPv6
      ipv6s: [],  //  网卡中的IPv4
      disks: [],   //  磁盘信息 name: 磁盘名称，total：磁盘总容量，单位B，free：磁盘空余空间，单位B
      viewDisk: false //  是否显示磁盘信息
    }
  },
  computed: {
    groupTree() {
      return this.$refs['groupTree']
    },
    busenessMap() {
      const map = {}
      this.defaultBussDatas.forEach(item => {
        map[item.bussId] = item.label
      })
      return map
    }
  },
  watch: {},
  created() {
    initTimestamp(this)
    this.formatDefaultBussDatas()
    this.resetTemp(false)
    this.getGroupTreeNode()
  },
  activated() {
    if (!isSameTimestamp(this, 'BackupServerGroup')) {
      this.getGroupTreeNode()
    }
  },
  methods: {
    /**
     * 获取设备服务器信息
     * @param devId
     */
    getServerInfoByDevId(devId) {
      this.ipv4s = []
      this.ipv6s = []
      this.disks = []
      getServerInfoByDevId(devId).then(res => {
        if (res.data) {
          //  网卡信息
          this.severNetworkInfoList = res.data.serverNetworkInfoList || []
          //  ip地址信息
          this.severNetworkInfoList.forEach(t => {
            t.ips && this.ipv4s.push(...t.ips.split(';'))
            t.ipv6s && this.ipv6s.push(...t.ipv6s.split(';'))
          })
          //  磁盘信息
          this.disks = res.data.serverDiskInfoList || []
        }
      })
    },
    async formatDefaultBussDatas() {
      await listSaleModuleId().then(resp => {
        this.moduleIds.splice(0, this.moduleIds.length, ...resp.data)
      })
      this.defaultBussDatas.forEach(data => {
        if (this.moduleIds.indexOf(data.moduleId) < 0) {
          data.hidden = 1
        }
      })
    },
    enableInternetChange(val) {
      this.$refs['dataForm'].clearValidate(['internetIp', 'internetIpv6', 'internetPort'])
      if (val === 0) {
        this.temp.enableForceRetIP = 0
        this.temp.internetIp = ''
        this.temp.internetIpv6 = ''
        this.temp.internetPort = ''
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    /**
     * 表单事件方法
     */
    changeTab(activeName, oldActiveName) {
      // let result = true
      // if (oldActiveName === 'serverTab') {
      //   this.$refs['dataForm'].validateField(this.serverTabValidFields, errorMessage => {
      //     if (errorMessage.length > 0) {
      //       result = false
      //     }
      //   })
      // } else if (oldActiveName === 'extTab') {
      //   this.$refs['dataForm'].validateField(this.extTabValidFields, errorMessage => {
      //     if (errorMessage.length > 0) {
      //       result = false
      //     }
      //   })
      // }
      return true
    },
    numberLimit(value, type, min, max) {
      this.temp[type] = value > max ? max : value < min ? min : parseInt(value)
    },
    diskSpaceLimitChange(value, type, min, max) {
      let limitVal = this.temp.diskSpaceLimitUnit ? value * 1024 : value
      limitVal = limitVal > max ? max : limitVal < min ? min : parseInt(limitVal)
      this.diskSpaceLimit = this.temp.diskSpaceLimitUnit ? Math.floor(limitVal / 1024) ? Math.floor(limitVal / 1024) : 1 : limitVal
      this.temp.diskSpaceLimit = this.temp.diskSpaceLimitUnit ? this.diskSpaceLimit * 1024 : this.diskSpaceLimit
    },
    diskSpaceLimitUnitChange(val) {
      if (val) {
        this.diskSpaceLimit = Math.floor(this.diskSpaceLimit / 1024) ? Math.floor(this.diskSpaceLimit / 1024) : 1
      } else {
        this.diskSpaceLimit = this.diskSpaceLimit * 1024
      }
    },
    cancelBatchConfig() {
      this.batchBussTemp = {}
      this.selectedDatas = []
      this.selectedCheckedModuleIds = []
      this.batchServiceConfigVisible = false
      this.checkRelativePath = false
      this.checkRelativeDays = false
      this.pathInput = true
      this.daysInput = true
    },
    pathChange() {
      if (!this.checkRelativePath) {
        this.pathInput = true
        this.batchBussTemp.relativePath = ''
      } else {
        this.pathInput = false
      }
    },
    daysChange() {
      if (!this.checkRelativeDays) {
        this.daysInput = true
        this.batchBussTemp.fileRetainDays = undefined
      } else {
        this.daysInput = false
      }
    },
    batchServiceConfig() {
      // 打开批量配置弹窗前先获取选中的且不是隐藏的业务
      var j = 0;
      // 打开批量配置弹窗前先清空弹窗的数据，避免之前数据遗留造成其它错误
      this.batchBussTemp = {}
      this.selectedCheckedModuleIds = []
      this.selectedDatas = []
      const selectedTempModules = []
      for (var i = 0; i < this.bussDatas.length; i++) {
        if (this.bussDatas[i].enabled == 1 && this.bussDatas[i].hidden == 0) {
          this.selectedDatas[j] = Object.assign({}, this.bussDatas[i])
          selectedTempModules.push(this.bussDatas[i].bussId)
          j++
        }
      }
      this.selectedCheckedModuleIds = this.selectedCheckedModuleIds.concat(selectedTempModules)
      this.batchServiceConfigVisible = true
    },
    selectAll(boolean, type) {
      if (boolean) {
        const selectAllModuleIds = []
        if (type === 1) {
          this.bussDatas.forEach(data => {
            // 去除智能备份bussId=32的数据
            if (data.bussId === 32) {
              data.enabled = 0
              selectAllModuleIds.push(data.bussId)
            } else {
              data.enabled = 1
              selectAllModuleIds.push(data.bussId)
            }
          })
        } else {
          this.specialBussDatas.forEach(data => {
            data.enabled = 1
            selectAllModuleIds.push(data.bussId)
          })
        }
        this.checkedModuleIds = this.checkedModuleIds.concat(selectAllModuleIds)
      } else {
        this.checkedModuleIds.splice(0)
        if (type === 1) {
          this.bussDatas.forEach(data => {
            data.enabled = 0
          })
          this.specialBussDatas.forEach(data => {
            if (data.enabled === 1) this.checkedModuleIds.push(data.bussId)
          })
        } else {
          this.specialBussDatas.forEach(data => {
            data.enabled = 0
          })
          this.bussDatas.forEach(data => {
            if (data.enabled === 1) this.checkedModuleIds.push(data.bussId)
          })
        }
      }
    },
    bacthHandleChange: function(data, even, index) {
      if (data == true) {
        this.selectedDatas[index].enabled = 1
      } else {
        this.selectedDatas[index].enabled = 0
      }
    },
    handleChange: function(data, even, index) {
      if (!this.editable) {
        if (data == true) {
          this.bussDatas[index].enabled = 1
        } else {
          this.bussDatas[index].enabled = 0
        }
      } else {
        this.bussDatas[index].enabled = 0
        this.editable = false
      }
    },
    handleSpecialChange: function(data, even, index) {
      if (!this.editable) {
        if (data == true) {
          this.specialBussDatas[index].enabled = 1
        } else {
          this.specialBussDatas[index].enabled = 0
        }
      } else {
        this.specialBussDatas[index].enabled = 0
        this.editable = false
      }
    },
    focus(event) {
      event.currentTarget.select()
    },
    updateBussConfig(item) {
      item.enabled = 1
      this.bussConfigDialogVisible = true
      this.bussTemp = JSON.parse(JSON.stringify(item))
    },
    updateBatchBussConfigData() {
      if (this.checkRelativePath) {
        if (this.batchBussTemp.relativePath == null || this.batchBussTemp.relativePath == '' || this.batchBussTemp.relativePath == undefined) {
          this.$message({
            message: this.$t('pages.fileServerContent_13'),
            type: 'error',
            duration: 2000
          })
          return
        }
      }
      if (this.checkRelativeDays) {
        if (this.batchBussTemp.fileRetainDays == null || this.batchBussTemp.fileRetainDays == '' || this.batchBussTemp.fileRetainDays == undefined) {
          this.$message({
            message: this.$t('pages.fileServerContent_14'),
            type: 'error',
            duration: 2000
          })
          return
        }
      }
      if (!this.checkRelativePath && !this.checkRelativeDays) {
        this.$message({
          message: this.$t('pages.fileServerContent_15'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$refs['batchBussConfigForm'].validate((valid) => {
        this.batchBussSubmitting = true
        if (valid) {
          this.selectedDatas.forEach(el => {
            this.bussDatas.forEach(le => {
              // 最终要修改的是初始修改弹窗选中的数据
              if (el.bussId == le.bussId && el.enabled == 1) {
                if (this.batchBussTemp.relativePath != undefined) {
                  if (this.batchBussTemp.relativePath.trim().length != 0) {
                    le.relativePath = this.batchBussTemp.relativePath
                  }
                }
                if (this.batchBussTemp.fileRetainDays != undefined) {
                  // 备份天数不需要判断是否是空格组成，el-input-number会自动转为数字类型
                  le.fileRetainDays = this.batchBussTemp.fileRetainDays
                }
                // 修改enableDelete属性的修改
                le.enableDelete = 1
              }
            })
          })
          this.batchBussSubmitting = false
          // 修改完清空数据
          this.cancelBatchConfig()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.backup_buss_config_success'),
            type: 'success',
            duration: 2000
          })
        } else {
          this.batchBussSubmitting = false
        }
      })
    },
    updateBussConfigData() {
      this.$refs['bussConfigForm'].validate((valid) => {
        this.bussSubmitting = true
        if (valid) {
          if (this.specialBussIds.indexOf(this.bussTemp.bussId) < 0) {
            this.bussDatas.forEach(el => {
              if (el.bussId === this.bussTemp.bussId) {
                el.relativePath = this.bussTemp.relativePath
                el.enableDelete = this.bussTemp.enableDelete
                el.fileRetainDays = this.bussTemp.fileRetainDays
              }
            })
          } else {
            this.specialBussDatas.forEach(el => {
              if (el.bussId === this.bussTemp.bussId) {
                el.relativePath = this.bussTemp.relativePath
                el.enableDelete = this.bussTemp.enableDelete
                el.fileRetainDays = this.bussTemp.fileRetainDays
              }
            })
          }
          this.bussSubmitting = false
          this.bussConfigDialogVisible = false
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.backup_buss_config_success'),
            type: 'success',
            duration: 2000
          })
        } else {
          this.bussSubmitting = false
        }
      })
    },
    getGroupTreeNode: function() {
      getServerGroupTree().then(respond => {
        if (respond && respond.data) {
          respond.data.forEach(el => {
            this.treeData[0].children.push(el)
            this.groupTreeSelectData.push(el)
          })
        }
      })
    },
    getBussTypeChecked(businesses) { // 获取勾选的bussId数组
      this.checkedModuleIds.splice(0)
      businesses.forEach(buss => {
        if (buss.enabled === 1) {
          this.checkedModuleIds.push(buss.bussId)
        }
      })
    },
    handleCreate() {
      this.resetTemp(true).then(() => {
        this.temp.groupId = this.query.groupId
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
        // this.selectAll(true)
      })
    },
    handleUpdate(row) {
      this.smartServerId = undefined
      this.viewDisk = false
      this.resetTemp(true).then(() => {
        //  若内网类型为0，即Ip地址和端口为实际IP地址和端口时，仅支持选择网卡中的IP信息，且端口不支持修改，为1时，端口和IP地址都可修改
        this.getServerInfoByDevId(row.devId);
        this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
        this.diskSpaceLimit = this.temp.diskSpaceLimitUnit ? Math.floor(this.temp.diskSpaceLimit / 1024) ? Math.floor(this.temp.diskSpaceLimit / 1024) : 1 : this.temp.diskSpaceLimit
        this.temp.password && (this.temp.password = '         ')
        if (!/^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(row.mainPath)) {
          this.temp.osType = 2
        }
        this.getBussTypeChecked(this.temp.businesses || [])
        // 业务数据合并
        this.bussDatas.forEach(data => {
          const opt = this.checkedModuleIds.indexOf(data.bussId) > -1 ? 1 : 0
          data.enabled = opt
          row.businesses && row.businesses.forEach(item => {
            if (data.bussId == item.bussId) {
              data.fileRetainDays = item.fileRetainDays
              data.relativePath = item.relativePath
              data.enableDelete = item.enableDelete
            }
          })
        })
        this.specialBussDatas.forEach(data => {
          const opt = this.checkedModuleIds.indexOf(data.bussId) > -1 ? 1 : 0
          data.enabled = opt
          row.businesses.forEach(item => {
            if (data.bussId == item.bussId) {
              data.fileRetainDays = item.fileRetainDays
              data.relativePath = item.relativePath
              data.enableDelete = item.enableDelete
            }
          })
        })

        //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
        if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
          this.temp.intranetType = 1
        }
        this.dialogStatus = 'update'
        this.dialogFormVisible = true
      })
    },
    handleSmartCreate(serverId) {
      this.handleCreate();
      this.smartServerId = serverId
    },
    handleSmartUpdate(row) {
      this.handleUpdate(row)
      this.smartServerId = row.devId
    },
    resetTemp(isLoadConfig) {
      this.tabName = 'serverTab'
      return this.$nextTick().then(() => {
        this.diskSpaceLimit = 50
        this.temp = Object.assign({}, this.defaultTemp)
        this.temp.extPaths.splice(0)
        this.bussDatas.splice(0)
        this.specialBussDatas.splice(0)
        this.defaultBussDatas.forEach(data => {
          if (this.specialBussIds.indexOf(data.bussId) < 0) {
            this.bussDatas.push(JSON.parse(JSON.stringify(data)))
          } else {
            this.specialBussDatas.push(JSON.parse(JSON.stringify(data)))
          }
        })
        this.checkedModuleIds = []
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        if (isLoadConfig) {
          return getPropertyByCode('server.config').then(res => {
            this.showFileServerAccount = res.data && res.data.value.split(',').indexOf('1') > -1
          })
        }
        return null
      })
    },
    /**
     * 表单验证
     */
    validateBussDatas() {
      const bussIds = this.checkedModuleIds
      let flag = true
      for (let i = 0; i < this.bussDatas.length; i++) {
        const data = this.bussDatas[i]
        if (bussIds.indexOf(data.bussId) > -1 && (!data.relativePath || (data.enableDelete == 1 && !data.fileRetainDays))) {
          this.appendValidateArr('businessTab')
          flag = false
          break
        }
      }
      return flag
    },
    /** 校验severTab 和 extTab **/
    async validateServerExtTabValid() {
      let tab = null
      await tab === null && this.$refs['dataForm'].validateField(this.serverTabValidFields, errorMessage => {
        if (errorMessage.length > 0) {
          tab = 'serverTab'
        }
      })
      await tab === null && this.$refs['dataForm'].validateField(this.extTabValidFields, errorMessage => {
        if (errorMessage.length > 0) {
          tab = 'extTab'
        }
      })
      // 扩展备份路径验证
      if (tab === null && !this.validateExtPath()) {
        tab = 'extTab'
      }
      if (tab !== null) {
        this.validateArr.splice(0)
        this.appendValidateArr(tab)
        this.tabName = tab
      }
      return tab === null
    },
    validateUniqueName(rule, value, callback) {
      getServerByName({
        name: value
      }).then(respond => {
        const server = respond.data
        if (server && server.id !== this.temp.id) {
          this.appendValidateArr('serverTab')
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    appendValidateArr(tab) {
      this.validateArr.indexOf(tab) > -1 ? false : this.validateArr.push(tab)
    },
    validateIPFormat(rule, value, callback) {
      let msg = ''
      if (value) {
        if (value === '127.0.0.1') {
          msg = this.$t('pages.validateFile_5')
        } else if (isIPv4(value)) {
          msg = undefined
        } else {
          msg = this.$t('pages.validateFile_6')
        }
      } else {
        msg = this.$t('pages.validateIP')
      }
      if (msg) {
        const tab = 'serverTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    inputBlur(validateProp) {
      this.$refs['dataForm'].validateField(validateProp)
    },
    validateInternetIp(rule, value, callback) {
      let msg = ''
      if (this.temp.enableInternet === 1) {
        // ipv4与ipv6 不能同时为空
        if (!this.temp.internetIp && !this.temp.internetIpv6) {
          msg = this.$t('pages.ipValidate', { net: this.$t('pages.extranet') })
          this.appendValidateArr('serverTab')
          callback(msg)
        } else {
          // 不对填写内容格式进行验证，ipv4、ipv6、域名
          callback()
        }
      } else {
        callback()
      }
    },
    validateIp(rule, value, callback) {
      let msg = ''
      // ipv4与ipv6 不能同时为空
      if (!this.temp.intranetIp && !this.temp.intranetIpv6) {
        msg = this.$t('pages.ipValidate', { net: this.$t('pages.intranet') })
        this.appendValidateArr('serverTab')
        callback(msg)
      } else {
        // 不对填写内容格式进行验证，ipv4、ipv6、域名
        callback()
      }
    },
    validateInternetPort(rule, value, callback) {
      if (this.temp.enableInternet === 1) {
        if (value === '' || value === undefined) {
          callback(new Error(this.$t('pages.validateFile_9')))
          this.appendValidateArr('serverTab')
        } else {
          this.validatePort(rule, value, callback)
        }
      } else {
        callback()
      }
    },
    validatePort(rule, value, callback) {
      let msg = ''
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (value === '' || value === undefined) {
        msg = this.$t('pages.validateMsg_enterPort')
      } else if (!number.test(value)) {
        msg = this.$t('pages.validateNaturalTree')
      } else if (value <= 0 || value > 65535) {
        msg = this.$t('pages.validatePortRange')
      } else if (rule.field === 'minDataPort' && value > this.temp.maxDataPort) {
        msg = this.$t('pages.validateFile_10')
      } else if (rule.field === 'maxDataPort' && value < this.temp.minDataPort) {
        msg = this.$t('pages.validateFile_11')
      } else {
        msg = undefined
        if (rule.field === 'minDataPort') {
          this.$refs['dataForm'].clearValidate('minDataPort')
        } else if (rule.field === 'maxDataPort') {
          this.$refs['dataForm'].clearValidate('maxDataPort')
        }
      }
      if (msg) {
        const tab = { internetPort: 'serverTab', minDataPort: 'extTab', maxDataPort: 'extTab' }[rule.field]
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    validateMainPath(rule, value, callback) {
      let msg = ''
      const reg = /^[c-zC-Z]:\\([^/:*?"<>|])*$/
      let drive = ''
      //  获取输入值的盘符
      if (value) {
        drive = value.substr(0, 1).toLocaleUpperCase()
      }
      if (!value) {
        msg = this.$t('pages.validateFBackupPath')
      } else if (!reg.test(value) && this.temp.osType == 1) {
        if (!/^[c-zC-Z]:\\/.test(value)) {
          msg = this.$t('pages.validateFBackupPath_1')
        } else {
          msg = this.$t('pages.pathValidTip', { characters: '/ : * ? " < > |' })
        }
      } else if (this.temp.osType == 1 && this.temp.onlineStatus && !this.$refs.diskInfo.getDiskIds().includes(drive)) {
        msg = this.$t('pages.notExitsDriveTip', { drive })
      } else if (this.temp.osType == 1 && this.temp.extPaths.find(path => path.substr(0, 1).toLocaleUpperCase() === value.substr(0, 1).toLocaleUpperCase())) {  //  文件服务器时，校验主路径填写的信息是否已在扩展备份路径中
        msg = this.$t('pages.validPathSameDiskByWin')
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = 'extTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    validateExtPath() {
      const value = this.temp.extPaths || []
      // 如果有正在输入的路径，但报错了，则返回 false
      if (this.extPathError) return false
      if (value && value.length > 0) {
        if (value.includes(this.temp.mainPath)) {
          this.$message({
            message: `${this.$t('pages.validSameFilePath')}, ${this.$t('pages.pleaseCheckUpdate')} !`,
            type: 'error',
            duration: 4000,
            dangerouslyUseHTMLString: true
          });
          return false
        }
        const outOfLines = []
        for (let i = 0; i < value.length; i++) {
          this.validatePath(null, value[i], msg => {
            if (msg) {
              outOfLines.push(value[i])
            }
          }, i)
        }
        if (outOfLines.length > 0 && this.temp.osType == 1) {
          const prefix = `${this.$t('pages.pathPrefixValidTip')}, ${this.$t('pages.and1')}${this.$t('pages.pathValidTip', { characters: '/ : * ? " < > |' })}, ${this.$t('pages.validPathSameDiskByWin')}`
          this.$message({
            message: `${prefix}, ${this.$t('pages.pleaseUpdateExtPath')}</br>${outOfLines.join('</br>')}`,
            type: 'error',
            duration: 4000,
            dangerouslyUseHTMLString: true
          });
          return false
        }
      }
      return true
    },
    validatePath(rule, value, callback, index) {
      if (value) {
        let msg
        const mainPath = this.temp.mainPath
        if (value == mainPath) {
          msg = this.$t('pages.validSameFilePath')
        } else if (this.temp.osType == 1) {
          const drive = value.substr(0, 1).toLocaleUpperCase();
          if (!/^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(value)) {
            if (!/^[c-zC-Z]:\\/.test(value)) {
              msg = this.$t('pages.pathPrefixValidTip')
            } else {
              msg = this.$t('pages.pathValidTip', { characters: '/ : * ? " < > |' })
            }
          } else if (this.temp.onlineStatus && !this.$refs.diskInfo.getDiskIds().includes(drive)) {
            msg = this.$t('pages.notExitsDriveTip', { drive })
          } else {
            const compareList = [...this.temp.extPaths]
            mainPath && compareList.push(mainPath)
            const disk = value.substr(0, 1).toLowerCase()
            for (let i = 0; i < compareList.length; i++) {
              if (i !== index && /^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(compareList[i]) && compareList[i].substr(0, 1).toLowerCase() === disk) {
                msg = this.$t('pages.validPathSameDiskByWin')
                break
              }
            }
          }
        } else if (this.temp.osType == 2) {
          if (value.includes('|')) {
            msg = this.$t('pages.pathValidTip', { characters: '|' })
          } else {
            const takePathPrefix = str => {
              const splitIndex = str.indexOf('/', 1)
              const result = splitIndex < 0 ? str : str.substr(0, splitIndex)
              return (result[0] !== '/' ? '/' : '') + result
            }
            const compareList = [...this.temp.extPaths]
            mainPath && compareList.push(mainPath)
            const pathPrefix = takePathPrefix(value)
            for (let i = 0; i < compareList.length; i++) {
              if (i !== index && takePathPrefix(compareList[i]) === pathPrefix) {
                msg = this.$t('pages.validPathSameDiskByLinux')
                break
              }
            }
          }
        }
        // 根据是否有报错信息赋值
        this.extPathError = !!msg
        msg && callback(msg)
      }
      callback()
    },
    validateOsType(rule, value, callback) {
      let msg = ''
      if (undefined === value) {
        msg = this.$t('pages.validateFBackupOsType')
      } else {
        msg = undefined;
      }
      callback(msg)
    },
    validateRequired(rule, value, callback) {
      let msg = ''
      if (!value) {
        msg = {
          userName: this.$t('pages.validateUserName'),
          diskSpaceLimit: this.$t('pages.validateNetTaskWrite'),
          diskShortageAlert: this.$t('pages.validateNetTaskSpace'),
          maxOccurs: this.$t('pages.validateUserLogin'),
          intranetIp: this.$t('pages.validateIP')
        }[rule.field]
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = rule.field == 'intranetIp' ? 'serverTab' : 'extTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    validatePassword(rule, value, callback) {
      let msg = ''
      const numReg = /^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9_\-]{8,20})$/
      const number = new RegExp(numReg)
      if (!value) {
        if (rule.field == 'accessPwd') {
          msg = this.$t('pages.validateMsg_enterDevPassword')
        } else if (rule.field == 'password') {
          msg = this.$t('pages.validateMsg_enterLoginPassword')
        }
      } else if (!number.test(value)) {
        if (rule.field == 'password' && !value.trim()) {
          callback()
        } else {
          msg = this.$t('pages.validateCollect_3')
        }
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = { accessPwd: 'serverTab', password: 'extTab' }[rule.field]
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    speedLimitValidate(rule, value, callback) {
      let msg = ''
      if (!value && value !== 0) {
        msg = { uploadSpeedLimit: this.$t('pages.validateUploadLimit'), downSpeedLimit: this.$t('pages.validateDownloadLimit') }[rule.field]
      } else if (value !== 0 && (isNaN(Number(value)) || value.toString().indexOf('.') > -1 || value.toString().indexOf('+') > -1 || Number(value) < 0)) {
        msg = this.$t('pages.validateGreaterThanOrEqual_0')
      } else {
        msg = undefined
      }
      if (msg) {
        const tab = 'extTab'
        this.appendValidateArr(tab)
      }
      callback(msg)
    },
    /**
     * api调用方法
     */
    formatFormData() {
      this.temp.businesses.splice(0)
      this.temp.businesses = this.bussDatas.concat(this.specialBussDatas)
      if (!this.showFileServerAccount) {
        // 不显示时，去除表单数据，避免加密处理错误
        this.temp.userName = undefined
        this.temp.password = undefined
      }
      if (this.temp.osType == 2) {
        this.temp.extPaths.splice(0)
      }
      if (this.smartServerId) {
        this.checkedModuleIds.push(32)
        this.temp.businesses.forEach(item => {
          if (item.bussId === 32) {
            item.enabled = 1
          }
        })
        this.temp.smartServerId = this.smartServerId;
      }
    },
    createServer(tempData) {
      createServer(tempData).then(respond => {
        this.temp.id = respond.data.id
        this.submitting = false
        this.dialogFormVisible = false
        // this.gridTable.execRowDataApi(this.query)
        this.$emit('submitEnd')
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.submitting = false
      })
    },
    async createData() {
      this.submitting = true
      const flag = this.validateBussDatas()
      const tabFlag = await this.validateServerExtTabValid()
      if (!tabFlag) { this.submitting = false }
      tabFlag && this.$refs['dataForm'].validate((valid) => {
        if (valid && flag) {
          this.formatFormData()
          this.temp.bussNames = this.checkedModuleIds.map(item => {
            return this.busenessMap[item]
          }).join('、')
          const tempData = JSON.parse(JSON.stringify(this.temp))
          if (!tempData.bussNames) {
            this.$confirmBox(this.$t('pages.validateFile_1'), this.$t('text.prompt')).then(() => {
              this.createServer(tempData)
            }).catch(() => {
              this.submitting = false
            })
          } else {
            this.createServer(tempData)
          }
        } else {
          this.tabName = this.validateArr.indexOf(this.tabName) > -1 ? this.tabName : this.validateArr[0]
          this.validateArr.splice(0)
          this.submitting = false
          if (this.tabName == 'businessTab') {
            this.$message({
              message: this.$t('pages.validateFile_2'),
              type: 'error'
            })
          }
        }
      })
    },
    updateServer(tempData) {
      updateServer(tempData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        // this.gridTable.execRowDataApi(this.query)
        this.$emit('submitEnd')
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.submitting = false
      })
    },
    async updateData() {
      this.submitting = true
      const flag = this.validateBussDatas()
      const tabFlag = await this.validateServerExtTabValid()
      if (!tabFlag) { this.submitting = false }
      tabFlag && this.$refs['dataForm'].validate((valid) => {
        if (valid && flag) {
          this.formatFormData()
          this.temp.bussNames = this.checkedModuleIds.map(item => {
            return this.busenessMap[item]
          }).join('、')
          const tempData = JSON.parse(JSON.stringify(this.temp))
          if (!tempData.bussNames) {
            this.$confirmBox(this.$t('pages.validateFile_3'), this.$t('text.prompt')).then(() => {
              this.updateServer(tempData)
            }).catch(() => {
              this.submitting = false
            })
          } else {
            this.updateServer(tempData)
          }
        } else {
          this.tabName = this.validateArr.indexOf(this.tabName) > -1 ? this.tabName : this.validateArr[0]
          this.validateArr.splice(0)
          this.submitting = false
          if (this.tabName == 'businessTab') {
            this.$message({
              message: this.$t('pages.validateFile_2'),
              type: 'error'
            })
          }
        }
      })
    },
    // 切换操作系统(1:windows 2:linux)时触发备份主路径校验
    selectOsTypeValiate() {
      this.$refs['dataForm'].validateField('mainPath');
      const extPathTagRef = this.$refs['extPathTag'];
      if (extPathTagRef && extPathTagRef.inputVisible) {
        extPathTagRef.validateForm()
      }
    },
    close() {
      this.validateArr.splice(0)
      this.resetTemp()
      this.dialogFormVisible = false
      this.$refs['extPathTag'] && this.$refs['extPathTag'].clearInputValue()
    },
    /**
     * 内网类型发生变更
     * @param value
     */
    intranetTypeChange(value) {
      //  IP有值且不在实际网卡IP中时，自动默认为实际网卡中的IP
      if (this.temp.intranetIp && this.ipv4s.length > 0 && !this.ipv4s.includes(this.temp.intranetIp)) {
        this.temp.intranetIp = this.ipv4s[0]
      }
      if (this.temp.intranetIpv6 && this.ipv6s.length > 0 && !this.ipv6s.includes(this.temp.intranetIpv6)) {
        this.temp.intranetIpv6 = this.ipv6s[0]
      }
      this.$refs.dataForm.validateField(['intranetIp', 'intranetIpv6', 'intranetPort'])
    },
    /**
     * 手动触发校验规则
     * @param fields
     */
    validateFieldClick(fields) {
      this.$refs.dataForm.validateField(fields)
    },
    /**
     * 显示磁盘信息
     */
    viewDiskInfo() {
      this.viewDisk = !this.viewDisk;
      if (this.viewDisk) {
        this.$refs.diskInfo.show();
      } else {
        this.$refs.diskInfo.close();
      }
    },
    /**
     * 扩展备份路径发生改变
     */
    tagChange() {
      this.$nextTick(() => {
        this.$refs.dataForm.validateField('mainPath')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .el-col {
    .el-checkbox {
      width: 100%;
    }
  }
  .label-text {
    max-width: calc(100% - 80px);
    display: inline-block;
    vertical-align: sub;
  }
  .df-color.is-checked >>>.el-checkbox__label{
    color: #666;
  }
</style>
