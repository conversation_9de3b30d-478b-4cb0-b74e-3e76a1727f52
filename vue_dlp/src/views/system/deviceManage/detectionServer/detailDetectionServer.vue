<template>
  <div>
    <Drawer
      :title="$t('table.detail')"
      :visible.sync="detailVisible"
      status="update"
      :with-footer="false"
      :size="'80%'"
      wrapper-closable
      append-to-body
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
    >
      <div class="app-container show-detail-panel" >
        <div style="flex: 1">
          <el-descriptions class="margin-top" :title="$t('pages.basicInformation')" :column="3" size="small" border>
            <el-descriptions-item :label="$t('pages.serverName')">
              {{ temp.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.deviceNum')">
              {{ temp.devId }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.grantAuthor')">
              {{ statusTableFilter(temp.authorizedStatus) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetType')">
              <div slot="label">
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.intranetTypeTip1') }}<br>
                    {{ $t('pages.intranetTypeTip2') }}<br>
                    {{ $t('pages.intranetTypeTip3') }}<br>
                    {{ $t('pages.intranetTypeTip4') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.intranetType') }}
              </div>
              {{ intranetTypeOptions[temp.intranetType] }}
            </el-descriptions-item>

            <!--            内外网+端口-->
            <el-descriptions-item :label="$t('pages.intranetIPV4Address')">
              {{ temp.intranetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetIPV6Address')">
              {{ temp.intranetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetPort')">
              {{ temp.intranetPort }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV4Address')">
              {{ temp.internetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV6Address')">
              {{ temp.internetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetPort')">
              {{ temp.internetPort }}
            </el-descriptions-item>
            <!--            服务器状态-->
            <el-descriptions-item :label="$t('pages.versionNumber')">
              {{ temp.version }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              {{ temp.onlineStatus }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.remark')">
              {{ temp.remark }}
            </el-descriptions-item>
          </el-descriptions>
          <server-detail-public v-if="isServerDetail" ref="ServerDetail" :dev-id="temp.devId" @serverInfo="serverInfo"/>
        </div>
        <!--        <div style="flex: 1">-->
        <!--          <div class="el-descriptions__header" style="margin-bottom: 15px;">-->
        <!--            <div class="el-descriptions__title" style="margin-left: 18px">服务器状态</div>-->
        <!--            <div class="el-descriptions__extra"></div>-->
        <!--          </div>-->
        <!--          <server-detail v-if="true" ref="ServerDetail"/>-->
        <!--        </div>-->

      </div>
    </Drawer>
  </div>
</template>
<script>
// import ServerDetail from '@/views/system/deviceManage/serverInfo/serverDetail/index.vue';
import ServerDetailPublic from '@/views/system/deviceManage/serverInfo/serverDetailPublic/index.vue';

export default {
  name: 'DetailDetectionServer',
  components: { ServerDetailPublic/*, ServerDetail*/ },
  data() {
    return {
      detailVisible: false,
      submitting: false,                 // 提交数据中...
      isServerDetail: false, // 开启服务器详情组件
      groupTreeSelectData: [],
      treeSelectNode: [],
      defaultTreeSelectNode: [{ id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped') }],
      treeData: [],
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        pwd: '',
        accessPwd: 'dev-server_12345',
        devId: undefined,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: undefined,
        internetPort: undefined,
        remark: '',
        encryptProps: ['accessPwd'],
        authorizedStatus: null,   //  授权状态，即开关,0-关闭，1-打开， null：表示该设备数据是旧数据，且尚未连接引擎，此数据不允许修改开关
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        sysmgrDbId: null, //  主数据库Id
        extraInfo: {
          sysmgrDbId: null,  //  主数据库Id
          dbMethod: null  //  数据库安装方式  0：内置数据库，1：外置数据库
        },
        onlineStatus: null    //   服务器在线状态
      },
      intranetTypeOptions: {
        0: this.$t('pages.truthIpAndPort'),
        1: this.$t('pages.typeless')
      }
    }
  },
  computed: {

  },
  watch: {
  },
  activated() {
  },
  created() {
  },
  methods: {
    show(row) {
      this.handleUpdate(row)
      this.detailVisible = true
      // 进入详细信息后，选中双击节点的编号
      const devId = row.devId
      console.log('ServerDetail devId', devId)
      this.isServerDetail = true
      this.$nextTick(() => {
        this.$refs['ServerDetail'].query2.devId = row.devId + ''// 编号
      })
    },
    // 服务器信息
    serverInfo(data) {
      Object.assign(this.temp, {
        onlineStatus: data.onlineStatus
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleUpdate(row) {
      //  若authorizedStatus为undefined或null表示该数据库是系统旧版本升级到5.0版本后尚未接入的数据库服务器，映射表尚未有数据，暂不支持修改内网类型
      this.isAccessed = row.authorizedStatus === undefined || row.authorizedStatus === null
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
      if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
        this.temp.intranetType = 1
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.accessDisabled = !this.$store.getters.isMasterSubAble
    },
    // 格式化授权状态
    statusTableFilter(data) {
      const statusMap = {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      }
      return statusMap[data]
    },
    // 关闭弹窗前，校验数据是否发生变化，并提示
    beforeClose(done) {
      this.isServerDetail = false
      done()
    },
    // 点击确认提交数据
    async handleSaveConfig() {
    }
  }
}
</script>
<style lang="scss" scoped>
.show-detail-panel {
  color: #666;
  display: flex;
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
