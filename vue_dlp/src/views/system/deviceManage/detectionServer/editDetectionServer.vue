<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 500px; margin-left:25px;">
        <FormItem v-if="temp.authorizedStatus != null" :label="$t('pages.grantAuthor')">
          <el-switch v-model="temp.authorizedStatus" :active-value="1" :inactive-value="0" />
        </FormItem>
        <FormItem :label="$t('pages.serverName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem v-show="dialogStatus=='update'" :label="$t('pages.deviceNum')" prop="devId">
          <el-input v-model="temp.devId" :maxlength="60" :disabled="true"/>
        </FormItem>
        <input type="text" class="autocomplete">
        <input type="password" class="autocomplete">
        <FormItem v-if="false" :label="$t('pages.devPassword')" prop="accessPwd">
          <el-input v-model="temp.accessPwd" maxlength="20" type="password" show-password />
        </FormItem>
        <!-- 主数据库ID authorizedStatus == null 表示该设备服务器未接入，不支持设置主数据库ID，仅旧设备数据存在这种情况-->
        <FormItem v-if="isSetAdminDbAuth && temp.authorizedStatus != null" :label="$t('pages.adminLibrary')" prop="sysmgrDbId">
          <div slot="label">
            <span style="color: red;">*</span>
            {{ $t('pages.adminLibrary') }}
            <el-tooltip effect="dark" placement="top-start">
              <div slot="content">
                {{ $t('pages.serverAccessAdminDbTip') }}<br>
                {{ $t('pages.serverAccessAdminDbTip1') }}<br>
                {{ $t('pages.serverAccessAdminDbTip3') }}<br>
                {{ $t('pages.serverAccessAdminDbTip4') }}<br>
                {{ $t('pages.serverAccessAdminDbTip5') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <el-select v-model="temp.sysmgrDbId">
            <el-option v-for="item in dbServers" :key="item.devId" :value="item.devId" :label="$t('pages.serverAccessDbLabel', { devName: item.name, devId: item.devId })"/>
          </el-select>
        </FormItem>
        <FormItem label="内网类型">
          <div slot="label">
            <el-tooltip effect="dark" placement="top-start">
              <div slot="content">
                {{ $t('pages.intranetTypeTip1') }}<br>
                {{ $t('pages.intranetTypeTip2') }}<br>
                {{ $t('pages.intranetTypeTip3') }}<br>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            {{ $t('pages.intranetType') }}
          </div>
          <el-select v-model="temp.intranetType" style="width: 50%" @change="intranetTypeChange">
            <el-option :key="0" :value="0" :disabled="temp.authorizedStatus === null" :label="$t('pages.truthIpAndPort')"/>
            <el-option :key="1" :value="1" :label="$t('pages.typeless')"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
          <el-select v-if="!temp.intranetType" v-model="temp.intranetIp" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
            <el-option v-for="(item, index) in ipv4s" :key="index" :value="item" :label="item"/>
          </el-select>
          <el-input v-else v-model="temp.intranetIp" v-trim maxlength="64" />
        </FormItem>
        <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
          <el-select v-if="!temp.intranetType" v-model="temp.intranetIpv6" :disabled="!temp.onlineStatus" clearable @change="validateFieldClick(['intranetIp', 'intranetIpv6'])">
            <el-option v-for="(item, index) in ipv6s" :key="index" :value="item" :label="item"/>
          </el-select>
          <el-input v-else v-model="temp.intranetIpv6" v-trim maxlength="64" />
        </FormItem>
        <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
          <el-input v-model="temp.intranetPort" :disabled="!temp.intranetType" maxlength="5" />
        </FormItem>
        <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
          <el-input v-model="temp.internetIp" v-trim maxlength="64" :disabled="temp.enableInternet==0" />
        </FormItem>
        <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
          <el-input v-model="temp.internetIpv6" v-trim maxlength="64" :disabled="temp.enableInternet==0" />
        </FormItem>
        <FormItem :label="$t('pages.internetPort')" prop="internetPort">
          <el-input v-model="temp.internetPort" maxlength="5" />
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <db-ip-exception-dlg ref="dbIpExceptionDlg"/>
  </div>
</template>

<script>
import { createServer, getServerByName, updateServer } from '@/api/system/deviceManage/detectionServer';
import { isIPv4 } from '@/utils/validate'
import { getServerInfoByDevId, isSetAdminDb, listDevServer } from '@/api/system/deviceManage/serverAccessApproval';
import DbIpExceptionDlg from '@/views/system/deviceManage/serverAccessApproval/config/dbIpExceptionDlg';

export default {
  name: 'EditDetectionServer',
  components: { DbIpExceptionDlg },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
          // { validator: this.validateUniqueName, trigger: 'blur' }   // 设备名称重复限制去除
        ],
        accessPwd: [
          { required: true, message: this.$t('pages.validateMsg_enterDevPassword'), trigger: 'blur' },
          { validator: this.validatePassword, trigger: 'blur' }
        ],
        intranetIp: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        intranetIpv6: [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        intranetPort: [
          { required: true, message: this.$t('pages.validateMsg_enterPort'), trigger: 'blur' }, // 暂时不需要，设置为非必填
          { validator: this.validatePort, trigger: 'blur' }
        ],
        internetPort: [
          { validator: this.validatePort, trigger: 'blur' }
        ],
        sysmgrDbId: [{ validator: this.validateSysmgrDbId, trigger: 'change' }]
      },
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        pwd: '',
        accessPwd: 'dev-server_12345',
        devId: undefined,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: undefined,
        internetPort: undefined,
        remark: '',
        encryptProps: ['accessPwd'],
        authorizedStatus: null,   //  授权状态，即开关,0-关闭，1-打开， null：表示该设备数据是旧数据，且尚未连接引擎，此数据不允许修改开关
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        sysmgrDbId: null, //  主数据库Id
        extraInfo: {
          sysmgrDbId: null,  //  主数据库Id
          dbMethod: null  //  数据库安装方式  0：内置数据库，1：外置数据库
        },
        onlineStatus: null    //   服务器在线状态
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.updateDetectionServer'),
        create: this.$t('pages.addDetectionServer')
      },
      submitting: false,
      dbServers: [], //  在线数据库信息
      sysmgrDbIdIsNull: false, //  该服务器是否未配置主数据库信息
      isSetAdminDbAuth: false,   // 判断是否拥有设置采集服务器和检测服务器的主数据库权限
      severNetworkInfoList: [],  //  获取网卡中的信息
      ipv4s: [],  //  网卡中的IPv6
      ipv6s: []  //  网卡中的IPv4
    }
  },
  computed: {
    groupTree() {
      return this.$refs['groupTree']
    },
    //  检测服务器或采集服务器设置的主数据库的Ip地址为虚拟Ip地址，即为127.0.0.1
    sysmgrIsVirtualIp() {
      if (this.temp.sysmgrDbId != null && this.isSetAdminDbAuth) {
        const db = this.dbServers.find(t => t.devId === this.temp.sysmgrDbId);
        return !!db && !!db.intranetIp && ['127.0.0.1', '0:0:0:0:0:0:0:1'].includes(db.intranetIp.trim())
      }
      return false;
    }
  },
  watch: {},
  created() {
    this.resetTemp()
  },
  activated() {
    this.loadDbServer();
  },
  methods: {
    /**
     * 获取设备网卡信息
     * @param devId
     */
    getServerNetworkInfoList(devId) {
      this.ipv4s = []
      this.ipv6s = []
      this.severNetworkInfoList = []
      getServerInfoByDevId(devId).then(res => {
        if (res.data) {
          this.severNetworkInfoList = res.data.serverNetworkInfoList || []
          this.severNetworkInfoList.forEach(t => {
            t.ips && this.ipv4s.push(...t.ips.split(';'))
            t.ipv6s && this.ipv6s.push(...t.ipv6s.split(';'))
          })
        }
      })
    },
    /**
     * 加载数据库信息
     */
    loadDbServer() {
      return listDevServer(219, 2).then(res => {
        this.dbServers = res.data || []
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
    },
    handleCreate() {
      this.resetTemp()
      this.sysmgrDbIdIsNull = false
      this.isSetAdminDbAuth = false
      this.isSetAdminDb();
      this.loadDbServer();
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.editable = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.sysmgrDbIdIsNull = false
      this.isSetAdminDbAuth = false
      this.isSetAdminDb();
      //  若内网类型为0，即Ip地址和端口为实际IP地址和端口时，仅支持选择网卡中的IP信息，且端口不支持修改，为1时，端口和IP地址都可修改
      this.getServerNetworkInfoList(row.devId);
      this.loadDbServer();
      this.temp = Object.assign(this.temp, row) // copy obj
      //  主数据库是否为空
      this.sysmgrDbIdIsNull = row.extraInfo ? !row.extraInfo.sysmgrDbId : true
      //  设置主数据库Id
      this.temp.sysmgrDbId = row.extraInfo ? row.extraInfo.sysmgrDbId || null : null
      //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
      if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
        this.temp.intranetType = 1
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 表单验证
     */
    validateUniqueName(rule, value, callback) {
      getServerByName({
        name: value
      }).then(respond => {
        const server = respond.data
        if (server && server.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    validateIPFormat(rule, value, callback) {
      if (value) {
        if (isIPv4(value)) {
          callback()
        } else {
          callback(new Error(this.$t('pages.validateFile_6')))
        }
      } else {
        callback()
      }
    },
    validatePort(rule, value, callback) {
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (value !== undefined && value !== '') {
        if (!number.test(value)) {
          callback(new Error(this.$t('pages.validateNaturalTree')))
        } else if (value <= 0 || value > 65535) {
          callback(new Error(this.$t('pages.validatePortRange')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateConnections(rule, value, callback) {
      if (rule.maxConnections) {
        if (rule.maxConnections < rule.ratedConnections) {
          callback(new Error(this.$t('pages.validateCollect_1')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validateIPNum(rule, value, callback) {
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (!number.test(value) || value < 0 || value > 255) {
        callback(new Error(this.$t('pages.validateCollect_2')))
      } else {
        callback()
      }
    },
    validateIp(rule, value, callback) {
      let msg = ''
      if (!this.temp.intranetIp && !this.temp.intranetIpv6) {
        msg = this.$t('pages.ipValidate', { net: this.$t('pages.intranet') })
        callback(msg)
      } else {
        this.$refs['dataForm'].clearValidate(['intranetIp', 'intranetIpv6'])
        callback()
      }
    },
    validatePassword(rule, value, callback) {
      const numReg = /^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9_\-]{8,20})$/
      const number = new RegExp(numReg)
      if (!number.test(value)) {
        callback(new Error(this.$t('pages.validateCollect_3')))
      } else {
        callback()
      }
    },
    /**
     * 校验主数据库Id
     * @param rule
     * @param value
     * @param callback
     */
    validateSysmgrDbId(rule, value, callback) {
      //  由于旧采集升级上来无法设置对于的主数据库Id，所以对于旧采集升级上来的数据不强制设置主数据库Id，新接入的采集数据在审批时是必须设置主数据库
      if (!this.sysmgrDbIdIsNull && !value) {
        callback(new Error(this.$t('pages.adminDbNotNull') + ''))
      }
      callback();
    },
    formatSubmitData(temp) {
      const tempData = JSON.parse(JSON.stringify(temp))
      //  是否拥有配置管理库的权限
      if (this.isSetAdminDbAuth) {
        //  设置管理库ID（管理库=主数据库）
        tempData.extraInfo.sysmgrDbId = this.temp.sysmgrDbId ? this.temp.sysmgrDbId : null
      } else {
        tempData.extraInfo = null
      }
      return tempData
    },
    /**
     * 新增、修改接口方法
     */
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.sysmgrIsVirtualIp) {
            this.$refs.dbIpExceptionDlg.show(this.temp.sysmgrDbId);
            this.submitting = false
            return;
          }
          const tempData = this.formatSubmitData(this.temp)
          createServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.sysmgrIsVirtualIp) {
            this.$refs.dbIpExceptionDlg.show(this.temp.sysmgrDbId);
            this.submitting = false
            return;
          }
          const tempData = this.formatSubmitData(this.temp)
          updateServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            // this.gridTable.execRowDataApi(this.query)
            // this.$parent.handleFilter()
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    /**
     * 判断是否拥有设置采集服务器和检测服务器的主数据库权限
     */
    isSetAdminDb() {
      isSetAdminDb().then(res => {
        this.isSetAdminDbAuth = res.data || false
      })
    },
    /**
     * 内网类型发生变更
     * @param value
     */
    intranetTypeChange(value) {
      //  IP有值且不在实际网卡IP中时，自动默认为实际网卡中的IP
      if (this.temp.intranetIp && this.ipv4s.length > 0 && !this.ipv4s.includes(this.temp.intranetIp)) {
        this.temp.intranetIp = this.ipv4s[0]
      }
      if (this.temp.intranetIpv6 && this.ipv6s.length > 0 && !this.ipv6s.includes(this.temp.intranetIpv6)) {
        this.temp.intranetIpv6 = this.ipv6s[0]
      }
      this.$refs.dataForm.validateField(['intranetIp', 'intranetIpv6', 'intranetPort'])
    },
    /**
     * 手动触发校验规则
     * @param fields
     */
    validateFieldClick(fields) {
      this.$refs.dataForm.validateField(fields)
    }
  }
}
</script>
