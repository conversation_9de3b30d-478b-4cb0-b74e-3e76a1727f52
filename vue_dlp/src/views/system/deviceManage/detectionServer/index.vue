<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="false" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createDevice') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delDevice') }}
        </el-button>
        <!--<el-button disabled size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.serverName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.deviceNum')" prop="devId">
                <el-input v-model.number="query.devId" v-trim clearable :maxlength="4" :placeholder="this.$t('pages.serverLibrary_text5')" @input="number('devId')"/>
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
                <el-input v-model="query.intranetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
                <el-input v-model="query.intranetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
                <el-input v-model.number="query.intranetPort" v-trim clearable maxlength="5" :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('intranetPort')"/>
              </FormItem>
              <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
                <el-input v-model="query.internetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
                <el-input v-model="query.internetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetPort')" prop="internetPort">
                <el-input v-model.number="query.internetPort" v-trim clearable maxlength="5" :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('internetPort')"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="serverTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <edit-detection-server ref="editDetectionServer" @submitEnd="handleFilter"></edit-detection-server>
    <detail-detection-server ref="detailDetectionServer"></detail-detection-server>
  </div>
</template>

<script>
import EditDetectionServer from './editDetectionServer'
import { getServerPage, deleteServer } from '@/api/system/deviceManage/detectionServer'
import { updateAuthorizedStatus } from '@/api/system/deviceManage/serverAccessApproval';
import DetailDetectionServer from './detailDetectionServer'

export default {
  name: 'DetectionServer',
  components: { DetailDetectionServer, EditDetectionServer },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { label: 'type', fixedWidth: '55', fixed: true, iconFormatter: this.isBranch },
        { prop: 'name', label: 'serverName', width: '120', fixed: true, sort: 'custom' },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: 'custom' },
        { prop: 'intranetIp', label: 'intranetIPV4Address', width: '130', sort: 'custom' },
        { prop: 'intranetIpv6', label: 'intranetIPV6Address', width: '130', sort: 'custom' },
        { prop: 'intranetPort', label: 'intranetPort', width: '100', sort: 'custom' },
        { prop: 'internetIp', label: 'internetIPV4Address', width: '130', sort: 'custom' },
        { prop: 'internetIpv6', label: 'internetIPV6Address', width: '130', sort: 'custom' },
        { prop: 'internetPort', label: 'internetPort', width: '100', sort: 'custom' },
        { prop: 'version', label: 'version', width: '130' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '250',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'detail', click: this.handleDetail },
            { label: 'grantAuthor', isShow: (row) => { return !(row.authorizedStatus === undefined || row.authorizedStatus === null) },
              disabledFormatter: (row) => this.grantButtonFormatter(row, 1), click: (row) => this.handleAuthorizedStatus(row, 1) },
            { label: 'cancelGrantAuthor', isShow: (row) => { return !(row.authorizedStatus === undefined || row.authorizedStatus === null) },
              disabledFormatter: (row) => this.grantButtonFormatter(row, 0), click: (row) => this.handleAuthorizedStatus(row, 0) }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        intranetIp: '',
        intranetIpv6: '',
        intranetPort: undefined,
        internetIp: '',
        internetIpv6: '',
        internetPort: undefined,
        devId: undefined
      },
      showTree: false,
      deleteable: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    filterKey() {
      return this.tempG.id
    }
  },
  created() {
    //  支持设备名称查询
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.$nextTick(() => {
      this.handleFilter()
    })
  },
  activated() {
    //  支持设备名称查询
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.handleFilter()
  },
  methods: {
    filterNodeMethod(value, data, node) {
      // 过滤节点及其子节点
      node.visible = node.key != this.filterKey && node.parent.visible
      return node.visible
    },
    parentIdSelectChange(data) {
      if (data && data !== this.tempG.id) {
        this.tempG.parentId = data
      }
    },
    terminalSelect() {
      return this.$refs['terminalGridSelect']
    },
    handleCreate() {
      this.$refs.editDetectionServer.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.editDetectionServer.handleUpdate(row)
    },
    handleDetail(row) {
      this.$refs.detailDetectionServer.show(row)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerPage(searchQuery)
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.intranetIp = ''
      this.query.intranetIpv6 = ''
      this.query.intranetPort = undefined
      this.query.internetIp = ''
      this.query.internetIpv6 = ''
      this.query.internetPort = undefined
      this.query.devId = undefined
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    },
    grantButtonFormatter: function(row, type) {
      return row.authorizedStatus == type
    },
    handleAuthorizedStatus(row, status) {
      updateAuthorizedStatus({ devId: row.devId, devType: 12, authorizedStatus: status }).then(respond => {
        row.authorizedStatus = status
        this.gridTable.updateRowData(row)
        this.$notify({
          title: this.$t('text.success'),
          message: status ? this.$t('pages.collectService_notifyMsg19') : this.$t('pages.collectService_notifyMsg20'),
          type: 'success',
          duration: 2000
        })
      })
    },
    isBranch: function(row) {
      const online = row.onlineStatus && row.onlineStatus == 1
      const style = online ? 'color: green' : 'color: #888'
      const labelPre = (online ? this.$t('pages.online') : this.$t('pages.offline')) + '\r\n'
      return row.isBranch ? { class: 'branch', title: labelPre + this.$t('pages.branchServer'), style } : row.syncType ? { class: 'controller', title: labelPre + this.$t('pages.headquartersServer'), style } : { class: 'server', title: labelPre + this.$t('pages.commonServer'), style }
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card{
    position: relative;
    >>>.el-card__header{
      padding: 10px 20px;
    }
  }
  .btn-box{
    position: absolute;
    top: 5px;
    right: 20px;
  }

</style>
