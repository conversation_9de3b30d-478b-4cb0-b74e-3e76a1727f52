<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="visable"
      :title="textMap[dlgStatus]"
      width="800px"
    >
      <Form ref="serverForm" :model="temp" :rules="rules" label-position="right" label-width="104px" style="width: 748px;">
        <FormItem :label="$t('pages.effectiveObject')" prop="effectDevIds">
          <tree-select
            ref="serverTree"
            multiple
            clearable
            is-filter
            check-strictly
            default-expand-all
            :checked-keys="temp.effectDevIds"
            :width="465"
            :data="serverTreeData"
            :disabled="!editable"
            :render-content="renderContent"
            @change="groupIdSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable || !editable" maxlength="30" />
        </FormItem>
        <FormItem :label="$t('table.remark')" >
          <el-input v-model="temp.remark" :disabled="!editable" maxlength="100"/>
        </FormItem>
        <FormItem :label="$t('table.enable')" >
          <el-switch
            v-model="temp.active"
            active-color="#409EFF"
            inactive-color="#aaaaaa"
            :active-value="true"
            :inactive-value="false"
            :disabled="!editable"
          />
        </FormItem>
        <el-card>
          <el-divider content-position="left">{{ $t('pages.detectionRules') }}
            <el-tooltip effect="dark" placement="top">
              <div slot="content">{{ $t('pages.serverAlarmPrompt1') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-divider>
          <FormItem label-width="60px">
            <div v-for="(item, index) in temp.configs" :key="index" style="margin: 1px 0; display: flex; align-items: center">
              <el-checkbox v-model="temp.configs[index].checked" :disabled="!editable"></el-checkbox>
              <span class="rule-attribute" :style="{ color: temp.configs[index].checked && editable ? '#409eff' : '', cursor: editable ? '' : 'not-allowed' }" @click="selectRule(index)">
                <!--硬盘-->
                <i18n v-if="item.alarmType === 5" :path="'pages.programSpaceUsageAlarmText'">
                  <el-input-number
                    slot="threshold"
                    v-model="temp.configs[index].threshold"
                    :disabled="!item.checked || !editable"
                    :controls="false"
                    :precision="0"
                    :min="1"
                    :max="10000"
                    style="width: 74px;"
                    @focus="focusInput = true"
                    @blur="focusInput = false"
                  />
                </i18n>
                <i18n v-if="item.alarmType === 6" :path="'pages.serverDataSpaceUsageAlarmText'">
                  <el-input-number
                    slot="threshold"
                    v-model="temp.configs[index].threshold"
                    :disabled="!item.checked || !editable"
                    :controls="false"
                    :precision="0"
                    :min="1"
                    :max="10000"
                    style="width: 74px;"
                    @focus="focusInput = true"
                    @blur="focusInput = false"
                  />
                </i18n>
                <i18n v-if="item.alarmType === 7" :path="'pages.serverSpaceUsageAlarmText'">
                  <el-input-number
                    slot="threshold"
                    v-model="temp.configs[index].threshold"
                    :disabled="!item.checked || !editable"
                    :controls="false"
                    :precision="0"
                    :min="1"
                    :max="10000"
                    style="width: 74px;"
                    @focus="focusInput = true"
                    @blur="focusInput = false"
                  />
                </i18n>
                <!--cpu, 内存-->
                <i18n v-if="item.alarmType === 3 || item.alarmType === 4" :path="item.alarmType === 3 ? 'pages.serverCPUAlarmTriggerMsg' : 'pages.serverMemoryAlarmTriggerMsg'">
                  <el-input-number
                    slot="times"
                    v-model="item.duration"
                    :disabled="!item.checked || !editable"
                    :controls="false"
                    :precision="0"
                    :min="1"
                    :max="3600"
                    style="width: 70px;"
                    :style="{ marginLeft: index === 2 ? '2px': '' }"
                    @focus="focusInput = true"
                    @blur="focusInput = false"
                  />
                  <el-input-number
                    slot="threshold"
                    v-model="temp.configs[index].threshold"
                    :disabled="!item.checked || !editable"
                    :controls="false"
                    :precision="0"
                    :min="1"
                    :max="99"
                    style="width: 60px;"
                    @focus="focusInput = true"
                    @blur="focusInput = false"
                  />
                </i18n>
                <el-tooltip v-if="item.alarmType === 6" class="item" effect="dark" placement="bottom-start">
                  <div slot="content">
                    <span style="color: red">{{ $t('pages.attenion') }}</span>
                    <span>{{ $t('pages.serverDataDiskAlarmPrompt1') }}</span><br>
                    <span>{{ $t('pages.serverDataDiskAlarmPrompt2') }}</span><br>
                    <span>{{ $t('pages.serverDataDiskAlarmPrompt3') }}</span>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
            </div>
          </FormItem>
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <FormItem :label="$t('pages.require_Msg4')" label-width="192px" prop="alarmIntervalLimit">
            <el-input v-model="temp.alarmIntervalLimit" :disabled="!editable" style="width: 70px;" @input="ruleInput(arguments[0], temp, false)"></el-input>
            <span>{{ $t('pages.require_Msg5') }}</span>
          </FormItem>
          <FormItem :label="$t('pages.require_Msg6')" label-width="192px" prop="alarmTimesLimit">
            <el-input v-model.number="temp.alarmTimesLimit" :disabled="!editable" style="width: 70px;" @input="ruleInput(arguments[0], temp, true)"></el-input>
            <span>{{ $t('pages.require_Msg7') }}</span>
          </FormItem>
          <el-divider content-position="left">{{ $t('pages.serverAlarmResponseRule') }}</el-divider>
          <!-- 响应规则 -->
          <div class="notify-group">
            <FormItem prop="notifyGroupId" label-width="0">
              <response-content
                id="notifyRefGroupId"
                ref="notifyRefGroup"
                :show-check-rule="false"
                :prop-check-rule="true"
                :show-select="true"
                :read-only="true"
                :editable="editable"
                :prop-rule-id="temp.notifyGroupId"
                :select-style="{ marginTop: '0px' }"
                :ignore-rule-types="ignoreRuleTypes"
                :filter-data="filterResponseContent"
                @getRuleId="getNotifyGroupId"
                @getChecked="getChecked"
                @validate="(val) => { responseValidate = val }"
              />
            </FormItem>
          </div>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="editable" :loading="submitting" type="primary" @click="submit">{{ $t('button.confirm') }}</el-button>
        <el-button @click="visable = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addServerAlarmStrategy, getStrategyByName, updateServerAlarmStrategy } from '@/api/system/deviceManage/serverAlarm';
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent';
import { getAlarmLimitDict } from '@/utils/dictionary'

export default {
  name: 'ServerAlarmDlg',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    editable: { type: Boolean, default: true },
    serverTreeData: { type: Array, required: true },
    selectNodeKey: { type: Array, default() { return [] } }
  },
  data() {
    return {
      visable: false,
      submitting: false,
      dlgStatus: 'create',
      textMap: {
        'create': this.i18nConcatText(this.$t('pages.serverAlarmStg'), 'create'),
        'update': this.i18nConcatText(this.$t('pages.serverAlarmStg'), 'update'),
        'view': this.$t('pages.serverAlarmStgDetail')
      },
      temp: {},
      defaultForm: {
        id: '',
        name: '',
        remark: '',
        active: false,
        effectDevIds: [],
        configs: [
          { checked: false, alarmType: 5, threshold: 5 },
          { checked: false, alarmType: 6, threshold: 5 },
          { checked: false, alarmType: 7, threshold: 5 },
          // cpu
          { checked: false, alarmType: 3, threshold: 90, duration: 10 },
          // 内存
          { checked: false, alarmType: 4, threshold: 90, duration: 10 }
        ],
        alarmIntervalLimit: 10,
        alarmTimesLimit: 3,
        alarmLimit: 0,
        notifyGroupId: undefined
      },
      supportAlarmLimit: 0,
      focusInput: false,
      ignoreRuleTypes: [1, 2, 32, 64, 128, 256],
      rules: {
        effectDevIds: [{ required: true, message: this.$t('components.chooseApplicationObj'), trigger: 'blur' }],
        name: [
          { required: true, message: this.$t('pages.validateStgName'), trigger: 'blur' },
          { validator: this.validateName, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.initSupportAlarmLimit()
  },
  methods: {
    serverForm() {
      return this.$refs['serverForm']
    },
    validateName(rule, value, callback) {
      getStrategyByName({ name: value }).then(res => {
        const data = res.data
        if (data && data.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    handleCreate() {
      this.resetForm()
      this.dlgStatus = 'create'
      this.$nextTick(() => {
        this.temp.effectDevIds = [...this.selectNodeKey]
      })
    },
    handleUpdate(data) {
      this.resetForm()
      this.$nextTick(() => {
        this.dlgStatus = this.editable ? 'update' : 'view'
        this.updateDataToTemp(data, 'configs')
        this.temp.configs.forEach(config => {
          const result = data.configs.find(c => c.alarmType == config.alarmType || c.alarmType == config.includeType)
          if (result) {
            Object.assign(config, result, { checked: true })
          }
        })
      })
    },
    updateDataToTemp(data, ...property) {
      if (data) {
        for (const key in data) {
          if (!property.includes(key)) {
            this.temp[key] = data[key]
          }
        }
      }
    },
    renderContent(h, { node, data, store }) {
      const iconClass = data.id < 0 ? '' : 'server';
      return (
        <div>
          <svg-icon v-show={!!iconClass} icon-class={!iconClass ? '' : iconClass} />
          <span>{data.label}</span>
        </div>
      )
    },
    groupIdSelectChange(selectedKeys, options) {
      const tempLen = this.temp.effectDevIds.length
      const selectLen = selectedKeys.length
      const len = Math.max(tempLen, selectLen)
      let diffVal
      if (tempLen < selectLen) {
        for (let i = 0; i < len; i++) {
          if (this.temp.effectDevIds[i] != selectedKeys[i]) {
            diffVal = options[i]
            break
          }
        }
      }
      if (diffVal) {
        for (let i = len - 1; i >= 0; i--) {
          if (diffVal['id'] > 0 && options[i]['id'] == -1 * diffVal['type']) {
            selectedKeys.splice(i, 1)
            break
          } else if (diffVal['id'] < 0 && (options[i]['type'] == -1 * diffVal['id'] && options[i]['id'] > 0)) {
            selectedKeys.splice(i, 1)
          }
        }
      }
      this.temp.effectDevIds = selectedKeys
      this.serverForm().validateField('effectDevIds')
    },
    ruleInput(value, object, isUpper) {
      if (typeof (value) == 'string') value = value.replace(/[^\d]/g, '')
      if (!value) value = 1
      let val = Number(value)
      if (val == 0) val = 1
      if (!isUpper) {
        object.alarmIntervalLimit = val > 1440 ? 1440 : val
      }
      const time = object.alarmIntervalLimit
      const maxUpper = Math.floor(1440 / time);
      if (object.alarmTimesLimit > maxUpper) object.alarmTimesLimit = maxUpper
      if (object.alarmTimesLimit == 0) object.alarmTimesLimit = 1
    },
    submit() {
      this.serverForm().validate((valid) => {
        if (valid && this.validExtConfig()) {
          if (!this.responseValidate) {
            const element = document.getElementById('notifyRefGroupId')
            element && element.scrollIntoView({ behavior: 'smooth' })
            return
          }
          this.submitting = true
          const data = this.formatConfig()
          const methods = this.dlgStatus === 'create' ? addServerAlarmStrategy(data) : updateServerAlarmStrategy(data)
          methods.then(res => {
            this.visable = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false
            this.$emit('submitFunc')
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    },
    formatConfig() {
      // 仅保存有勾选的检测项
      const data = JSON.parse(JSON.stringify(this.temp))
      data['configs'] = (data.configs || []).filter(item => item.checked)
      data['effectDevInfos'] = (this.$refs['serverTree'].getSelectedNode() || []).map(({ id, type }) => {
        return { id, devType: type || (-1 * id) }
      })
      delete data['effectDevIds']
      return data
    },
    resetForm() {
      this.visable = true
      this.temp = JSON.parse(JSON.stringify(this.defaultForm))
      this.serverForm() && this.serverForm().resetFields()
    },
    getNotifyGroupId(groupId) {
      if (groupId && this.temp) {
        this.temp.notifyGroupId = groupId
      }
      this.serverForm().validateField('notifyGroupId')
    },
    getChecked(checked, data) {
      this.temp.alarmLimit = (checked || []).filter(v => !this.ignoreRuleTypes.some(type => type == v)).reduce((pv, cv) => pv | cv, 0)
    },
    filterResponseContent(datas) {
      return datas.filter(item => (item.alarmLimit & this.supportAlarmLimit) > 0)
    },
    validExtConfig() {
      const checkConfig = this.temp.configs.some(item => item.checked)
      if (!checkConfig) {
        this.$message({
          duration: 2000,
          type: 'error',
          message: this.$t('pages.serverAlarmPrompt4')
        });
      }
      return checkConfig
    },
    initSupportAlarmLimit() {
      this.supportAlarmLimit = getAlarmLimitDict().map(item => item.value).filter(v => !this.ignoreRuleTypes.includes(v)).reduce((pr, cv) => pr | cv, 0)
    },
    selectRule(index) {
      if (!this.editable) { return }
      if (!this.focusInput) {
        this.temp.configs[index].checked = !this.temp.configs[index].checked
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider--horizontal{
  margin: 19px 0;
  .el-divider__text{
    background-color: #e4e7e9;
    font-weight: 800;
  }
}
.notify-group {
  margin-left: 45px;
  >>>.el-form-item__error {
    margin-left: 25px;
  }
}
.rule-attribute {
  padding-left: 6px;
  line-height: 19px;
  font-size: 14px;
}
.rule-attribute:hover {
  cursor: pointer;
}
</style>

