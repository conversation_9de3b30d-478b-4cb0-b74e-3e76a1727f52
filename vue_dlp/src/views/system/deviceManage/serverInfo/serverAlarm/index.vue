<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="tree"
        :data="treeData"
        resizeable
        default-expand-all
        :expand-on-click-node="false"
        :render-content="renderContent"
        @node-click="serverTreeNodeClick"
      />
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <!--        <el-button icon="el-icon-setting" size="mini" @click="createServerOffStg">-->
        <!--          {{ $t('pages.serverOffAlarmSet') }}-->
        <!--        </el-button>-->
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :autoload="false"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <!--    <el-dialog-->
    <!--      v-el-drag-dialog-->
    <!--      :close-on-click-modal="false"-->
    <!--      :modal="false"-->
    <!--      :visible.sync="offlineVisable"-->
    <!--      :title="$t('pages.serverOfflineAlarmConfig')"-->
    <!--      width="750px"-->
    <!--    >-->
    <!--      <Form ref="offline" :model="offlineTemp" label-position="right" label-width="185px">-->
    <!--        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>-->
    <!--        <FormItem label-width="80px">-->
    <!--          {{ $t('pages.serverAlarmPrompt2') }}-->
    <!--        </FormItem>-->
    <!--        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>-->
    <!--        <FormItem :label="$t('pages.require_Msg4')" prop="alarmIntervalLimit" style="margin-left: 22px">-->
    <!--          <el-input v-model="offlineTemp.alarmIntervalLimit" style="width: 70px" @input="alarmIntervalLimitInput"/>-->
    <!--          <span>{{ $t('pages.require_Msg5') }}</span>-->
    <!--        </FormItem>-->
    <!--        <FormItem :label="$t('pages.require_Msg6')" prop="alarmTimesLimit" style="margin-left: 22px">-->
    <!--          <el-input v-model="offlineTemp.alarmTimesLimit" style="width: 70px" @input="alarmTimesLimitInput"/>-->
    <!--          <span>-->
    <!--            {{ $t('pages.require_Msg7') }}-->
    <!--            <el-tooltip class="item" effect="dark" :content="$t('pages.serverAlarmPrompt3')" placement="right">-->
    <!--              <i class="el-icon-info" />-->
    <!--            </el-tooltip>-->
    <!--          </span>-->
    <!--        </FormItem>-->
    <!--        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>-->
    <!--        <FormItem :label="`${$t('pages.notifyObjGroup')}：`">-->
    <!--          <notify-ref-group-->
    <!--            ref="alarmNotifyGroup"-->
    <!--            :clearable="false"-->
    <!--            :show-select="true"-->
    <!--            :read-only="true"-->
    <!--            :select-style="{ marginTop: '0px' }"-->
    <!--            @getGroupId="getNotifyGroupId"-->
    <!--          />-->
    <!--        </FormItem>-->
    <!--        <FormItem prop="notifyGroupId" :rules="[{ required: true, message: $t('pages.chooseNotifyObjGroup') }]" style="margin-left: 30px"/>-->
    <!--      </Form>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button :loading="offlineSubmitting" type="primary" @click="offlineSubmit">{{ $t('button.confirm') }}</el-button>-->
    <!--        <el-button @click="offlineVisable = false">{{ $t('button.cancel') }}</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->
    <server-alarm-dlg ref="serverAlarmDlg" :server-tree-data="treeData" :editable="editable" :select-node-key="selectNodeKey" @submitFunc="handleFilter"/>
  </div>
</template>

<script>
import {
  getServerAlarmTree,
  getServerAlarmPage,
  deleteServerAlarm,
  getOfflineStg, updateOfflineStg
} from '@/api/system/deviceManage/serverAlarm';
import { stgActiveIconFormatter } from '@/utils/formatter';
import { getDictLabel } from '@/utils/dictionary'
import ServerAlarmDlg from './serverAlarmDlg';

export default {
  name: 'ServerAlarm',
  components: { ServerAlarmDlg },
  data() {
    return {
      listable: true,
      treeable: true,
      editable: true,
      showTree: true,
      deleteable: false,
      treeData: [],
      devTreeOrderMap: {},
      offlineVisable: false,
      selectNodeKey: [],
      query: {
        page: 1,
        searchInfo: '',
        devId: null,
        devType: null
      },
      offlineTemp: {},
      dbOfflineConfig: {},
      colModel: [
        { prop: 'name', label: 'stgName', width: '100', iconFormatter: stgActiveIconFormatter, sort: true },
        { prop: 'effectDevIds', label: 'source', width: '130', type: 'button',
          buttons: [
            { formatter: this.effectDevIdsFormatter, click: this.selectDevId }
          ]
        },
        { prop: 'configs', label: 'stgMessage', type: 'button', width: '200',
          buttons: [
            { formatter: this.conditionFormatter, click: this.handleRead }
          ]
        },
        { prop: 'remark', label: 'remark', width: '100', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      typeOptions: [
        { value: 5, label: this.$t('pages.serverProgramSpaceAlarm') },
        { value: 6, label: this.$t('pages.serverDataSpaceAlarm') },
        { value: 7, label: this.$t('pages.serverSpaceAlarm') },
        { value: 3, label: this.$t('pages.serverCpuAlarm') },
        { value: 4, label: this.$t('pages.serverMemoryAlarm') }
        // { typeId: 11, remark: this.$t('pages.serverOfflineAlarm') }
      ],
      offlineSubmitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTable']
    }
  },
  mounted() {
    this.initServerTree().then(res => {
      this.gridTable && this.gridTable.execRowDataApi()
    })
    // this.initOfflineStgInfo()
  },
  methods: {
    serverAlarmDlg() {
      return this.$refs['serverAlarmDlg']
    },
    alarmNotifyGroup() {
      return this.$refs['alarmNotifyGroup']
    },
    effectDevIdsFormatter(row, data) {
      if (!row || !row.effectDevIds) { return '' }
      const formatLabels = []
      const currentSelectDevId = this.$refs['tree'] && this.$refs['tree'].getCurrentKey()
      for (const devId of row.effectDevIds) {
        if (devId == currentSelectDevId) {
          formatLabels.splice(0, 0, this.$t('pages.self'))
        } else if (devId == 0) {
          formatLabels.push(this.$t('pages.serverlog_server_console'))
        } else {
          this.devTreeOrderMap[devId] && formatLabels.push(this.devTreeOrderMap[devId].name)
        }
      }
      return formatLabels.join(', ')
    },
    getServerByRecursion(devId, treeList) {
      for (let i = 0; i < treeList.length; i++) {
        const data = treeList[i]
        if (data.id == devId) {
          return data
        }
        if (data.children && data.children.length > 0) {
          const tempData = this.getServerByRecursion(devId, data.children)
          if (tempData) { return tempData; }
        }
      }
      return null
    },
    renderContent(h, { node, data, store }) {
      const iconClass = data.id < 0 ? '' : 'server';
      return (
        <div>
          <svg-icon v-show={!!iconClass} icon-class={!iconClass ? '' : iconClass} />
          <span>{data.label}</span>
        </div>
      )
    },
    selectDevId(row) {
      const firstDevId = (row.effectDevIds || [])[0]
      if (firstDevId && this.$refs['tree']) {
        const currentNode = this.$refs['tree'].getCurrentNode() || {}
        if (currentNode.id != firstDevId) {
          this.$refs['tree'].setCurrentKey(firstDevId)
          const devInfo = row.effectDevInfos.find(devInfo => devInfo.id == firstDevId)
          devInfo && this.serverTreeNodeClick(Object.assign({}, devInfo, { type: devInfo.devType }))
        }
      }
    },
    serverTreeNodeClick(data, node, el) {
      this.selectNodeKey = [data.id]
      this.query.devType = data.type
      this.query.devId = data.id > 0 ? data.id : undefined
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleRefresh() {
      this.query.devId = null
      this.query.devType = null
      this.query.page = 1
      this.gridTable.execRowDataApi()
      this.$refs.tree.clearSelectedNode()
      this.selectNodeKey = []
    },
    handleCreate() {
      this.editable = true
      this.serverAlarmDlg() && this.serverAlarmDlg().handleCreate()
    },
    handleRead(row) {
      this.editable = false
      this.serverAlarmDlg() && this.serverAlarmDlg().handleUpdate(row)
    },
    handleUpdate(row) {
      this.editable = true
      this.serverAlarmDlg() && this.serverAlarmDlg().handleUpdate(row)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteKeys = this.gridTable.getSelectedKeys() || []
        if (toDeleteKeys.length == 0) { return }
        deleteServerAlarm({ ids: toDeleteKeys.join(',') }).then(respond => {
          this.gridTable.execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      const zeroOrder = { order: -Infinity }
      return getServerAlarmPage(searchQuery).then(response => {
        (response.data.items || []).forEach(item => {
          item['effectDevIds'] = item['effectDevInfos'].map(({ id }) => id).sort((a, b) => {
            return (this.devTreeOrderMap[a] || zeroOrder).order - (this.devTreeOrderMap[b] || zeroOrder).order
          })
        })
        return response
      })
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    selectable(row, data) {
      return !!this.query.devType || !(row['effectDevIds'] || []).some(id => this.devTreeOrderMap[id] || id == 0)
    },
    conditionFormatter(row, data) {
      let str = ''
      if (row.configs) {
        str += `${this.$t('text.open')}：`
        const info = []
        row.configs.map(config => config.alarmType)
          .sort((a, b) => a - b)
          .forEach(alarmType => {
            const label = getDictLabel(this.typeOptions, alarmType)
            label && (info.push(label))
          })
        str += info.join('，')
      }
      return str
    },
    buttonFormatter(row, data) {
      return !this.query.devType && !this.query.devId && (row['effectDevIds'] || []).some(id => this.devTreeOrderMap[id] || id == 0) ? '' : this.$t('text.edit')
    },
    alarmIntervalLimitInput(val) {
      let value = Number(val)
      if (!value || value <= 0) {
        value = 1
      } else if (value > 1440) {
        value = 1440
      }
      const maxUpper = Math.floor(1440 / value)
      this.offlineTemp.alarmIntervalLimit = value
      if (this.offlineTemp.alarmTimesLimit > maxUpper) {
        this.offlineTemp.alarmTimesLimit = maxUpper
      }
    },
    alarmTimesLimitInput(val) {
      let value = Number(val)
      if (Number.isNaN(value) || value < 0) {
        value = 0
      }
      const maxUpper = Math.floor(1440 / this.offlineTemp.alarmIntervalLimit)
      this.offlineTemp.alarmTimesLimit = value > maxUpper ? maxUpper : value
    },
    getNotifyGroupId(groupId) {
      if (groupId && this.offlineTemp) {
        this.offlineTemp.notifyGroupId = groupId
      }
    },
    createServerOffStg() {
      // confirmBox
      this.offlineVisable = true
      const defaultOfflineTemp = {
        alarmIntervalLimit: 10,
        alarmTimesLimit: 0,
        notifyGroupId: undefined
      }
      this.$nextTick(() => {
        this.offlineTemp = Object.assign(defaultOfflineTemp, this.dbOfflineConfig)
        const notifyGroupId = this.dbOfflineConfig.notifyGroupId;
        if (notifyGroupId) {
          this.alarmNotifyGroup() && this.alarmNotifyGroup().setGroupId(notifyGroupId)
        }
      })
    },
    initOfflineStgInfo() {
      getOfflineStg().then(res => {
        if (res.data) {
          Object.assign(this.dbOfflineConfig, res.data)
        }
      })
    },
    offlineSubmit() {
      this.$refs['offline'].validate((vaild) => {
        if (vaild) {
          this.offlineSubmitting = true
          this.offlineTemp.alarmLimit = this.alarmNotifyGroup().getPushLimit()
          updateOfflineStg(this.offlineTemp).then(res => {
            this.dbOfflineConfig = res.data
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.offlineVisable = false
            this.offlineSubmitting = false
          }).catch(() => {
            this.offlineSubmitting = false
          })
        }
      })
    },
    initServerTree() {
      return getServerAlarmTree().then(res => {
        this.treeData.splice(0, this.treeData.length, ...res.data)
        this.computeDevTreeOrderAndName(this.treeData, 1)
      })
    },
    computeDevTreeOrderAndName(treeList, order) {
      for (let i = 0; i < treeList.length; i++) {
        const data = treeList[i]
        this.devTreeOrderMap[data['id']] = { order: order++, name: data['label'] }
        if (data.children && data.children.length > 0) {
          order = this.computeDevTreeOrderAndName(data.children, order)
        }
      }
      return order
    }
  }
}
</script>
