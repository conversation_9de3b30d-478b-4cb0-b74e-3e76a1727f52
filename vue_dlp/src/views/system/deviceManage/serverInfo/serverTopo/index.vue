<template>
  <div v-loading="showLoading" class="app-container" @contextmenu.prevent="">
    <!--图例-->
    <div v-if="!showLoading" class="legend">
      <ul>
        <li v-for="(opt, index) in legendOptions" :key="index" class="ellipsis">
          <img :src="opt.src"><span :title="opt.label">{{ opt.label }}</span>
        </li>
      </ul>
    </div>
    <div ref="main" class="app-container" @click="mainClick"></div>
    <!--右键弹出菜单-->
    <div ref="rightMenu" class="menu" style="display: none;cursor: pointer">
      <ul>
        <!--        <li v-if="currentDevType === 1" value="1" @click="daqServerAdd">{{ $t('pages.addCollectionServer') }}</li>-->
        <li v-if="currentDevType === 6" @click="daqServerUpdate">{{ $t('pages.updateCollectionServer') }}</li>
        <li v-if="currentDevType === 6" @click="daqServerBindTerm">{{ $t('pages.collectionServerBindTerminal') }}</li>
        <li v-if="currentDevType === 6 || currentDevType === 186 " @click="daqServerMgr">{{ $t('pages.deviceMgr') }}</li>
        <!--        <li v-if="currentDevType === 6 && currentGroupId === 0" @click="dbServerAdd">{{ $t('pages.addDBServer') }}</li>-->
        <!--        <li v-if="currentDevType === 6 && currentGroupId === 0" @click="backupServerAdd">{{ $t('pages.addFileServer') }}</li>-->
        <!--        <li v-if="currentDevType === 6 && currentGroupId === 0" @click="detectionServerAdd">{{ $t('pages.addDetectionServer') }}</li>-->
        <!--        <li v-if="currentDevType === 6 && currentGroupId === 0" v-permission="'A4K'" @click="softwareServerAdd">{{ $t('text.addInfo', { info: $t('route.softwareServer') }) }}</li>-->
        <li v-if="currentDevType === 219" @click="dbServerUpdate">{{ $t('pages.updateDBServer') }}</li>
        <!--        <li v-if="currentDevType === 9" @click="backupServerAdd">{{ $t('pages.addFileServer') }}</li>-->
        <li v-if="currentDevType === 9" @click="daqServerMgr">{{ $t('pages.deviceMgr') }}</li>
        <li v-if="currentDevType === 11" @click="backupServerUpdate" >{{ $t('pages.editFileServer') }}</li>
        <li v-if="currentDevType === 12" @click="detectionServerUpdate">{{ $t('pages.updateDetectionServer') }}</li>
        <li v-if="currentDevType === 172" v-permission="'A4K'" @click="softwareServerUpdate">{{ $t('text.editInfo', { info: $t('route.softwareServer') }) }}</li>
        <!--        <li v-if="currentDevType === 6" @click="smartBackupServerAdd">{{ '新增智能备份' }}</li>-->
        <li v-if="currentDevType === 186" @click="smartBackupServerUpdate">{{ '修改智能备份' }}</li>
      </ul>
    </div>
    <edit-daq-server ref="editDaqServer" @submitEnd="submitEnd"></edit-daq-server>
    <edit-backup-server ref="editBackupServer" @submitEnd="submitEnd"></edit-backup-server>
    <server-mgr-dlg v-if="serverMgrIf" ref="serverMgrDlg" @submitEnd="serverMgrSubmitEnd"/>
    <edit-detection-server ref="editDetectionServer" @submitEnd="submitEnd"></edit-detection-server>
    <edit-db-server ref="editDbServer" @submitEnd="submitEnd"></edit-db-server>
    <edit-software-server ref="editSoftwareServer" v-permission="'A4K'" @submitEnd="submitEnd"></edit-software-server>
    <bind-daq-server-dlg ref="bindDAQServerDlg" @submitEnd="submitEnd"/>
    <approval-server ref="approvalServerDlg"/>
    <edit-smart-backup-server ref="editSmartBackupServer" @submitEnd="submitEnd"></edit-smart-backup-server>
  </div>
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import EditDaqServer from '@/views/system/deviceManage/daqServer/editDaqServer'
import ServerMgrDlg from '@/views/system/deviceManage/serverMgrDlg'
import EditBackupServer from '@/views/system/deviceManage/backupServer/editBackupServer'
import EditDetectionServer from '@/views/system/deviceManage/detectionServer/editDetectionServer'
import EditSoftwareServer from '@/views/system/deviceManage/softwareServer/edit'
import EditDbServer from '@/views/system/deviceManage/dbServer/editDbServer'
import BindDaqServerDlg from '@/views/system/terminalManage/terminal/bindDaqServerDlg'
import { getServerPage as getDaqServerPage } from '@/api/system/deviceManage/daqServer'
import { getServerPage as getBackupServerPage } from '@/api/system/deviceManage/backupServer'
import { getServerPage as getDetectionServerPage } from '@/api/system/deviceManage/detectionServer'
import { getServerPage as getSoftwareServerPage } from '@/api/system/deviceManage/softwareServer'
import { getServerPage as getDbServerPage } from '@/api/system/deviceManage/dbServer'
import ApprovalServer from '@/views/system/deviceManage/approvalServer'
import EditSmartBackupServer from '@/views/system/deviceManage/smartBackupServer'
import { getFileServerPage, getServerPage } from '@/api/system/deviceManage/smartBackupServer';

export default {
  name: 'ServerTopo',
  components: {
    EditDaqServer,
    ServerMgrDlg,
    EditBackupServer,
    EditDetectionServer,
    EditSoftwareServer,
    EditDbServer,
    BindDaqServerDlg,
    ApprovalServer,
    EditSmartBackupServer
  },
  props: {
    dataTree: {
      type: Object,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      showLoading: true,
      topoNum: 0,
      contextmenu: false,
      currentDevType: 0,
      currentGroupId: 0,
      currentId: 0,
      devId: 0,
      serverMgrIf: false,
      onlines: {
        0: this.$t('pages.offline'),
        1: this.$t('pages.online')
      },
      adminAuthorized: {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      },
      legendOptions: [
        { label: this.$t('pages.serverStatus', { isOnline: this.$t('pages.online'), isAuthor: this.$t('pages.authorization') }), src: require('@/assets/serverTopo/server_online_reg.png') },
        { label: this.$t('pages.serverStatus', { isOnline: this.$t('pages.online'), isAuthor: this.$t('pages.unauthorized') }), src: require('@/assets/serverTopo/server_online_warn.png') },
        { label: this.$t('pages.serverStatusError'), src: require('@/assets/serverTopo/server_offline_warn.png') },
        { label: this.$t('text.connectNormal'), src: require('@/assets/serverTopo/lineGreen.png') },
        { label: this.$t('text.connectExceptional'), src: require('@/assets/serverTopo/lineRed.png') }
      ]
    }
  },
  computed: {
    theme() {
      return this.$store.getters.theme || this.$store.getters.sysResources.theme
    }
  },
  watch: {
    dataTree(val, oldVal) {
      if (val == null) {
        // 解决当所有服务器都未安装时，此页面一直转圈圈。
        this.showLoading = false
        return
      }
      if (this.topoNum != val.length) {
        this.setOptions();
        this.showLoading = false
      }
    },
    theme() {
      this.setOptions()
    }
  },
  mounted: function() {
    // 解决tab页切换时，此页面一直转圈圈。
    this.showLoading = false
    // this.setOptions()
    this.day_init()
  },
  created() {
  },
  methods: {
    /**
     * 树组件
     */
    // 实现自适应
    day_init() {
      const self = this;
      const todaypieId = this.$refs.main
      if (!todaypieId) {
        return false;
      } else {
        setTimeout(() => {
          window.onresize = function() {
            //   self.chart = echarts.init(self.$refs.myEchart);
            self.chart_today = echarts.init(
              todaypieId
            );
            self.chart_today.resize();
          };
        }, 20);
      }
    },
    /**
     * 遍历树
     * */
    readNodes(nodes) {
      for (const item of nodes) { // js遍历树形数组结构
        if (item.children && item.children.length) {
          if (!this.hasPermission('A4K')) {
            // 未购买软件中心模块时隐藏软件中心服务器
            item.children = item.children.filter(child => child.devType !== 172)
          }
          this.readNodes(item.children)
        }
        if (item.status == 1) {
          if (item.adminAuthorized === 0) {
            // 采集未授权
            item.symbol = 'image://' + require('@/assets/serverTopo/server_online_warn.png');
          } else {
            item.symbol = 'image://' + require('@/assets/serverTopo/server_online_reg.png');
          }
        } else {
          item.symbol = 'image://' + require('@/assets/serverTopo/server_offline_warn.png');
        }
        if (item.lineStatus === 1) {
          // 修改连线颜色
          item.lineStyle = {
            color: '#2E8B57'
          }
        } else {
          item.lineStyle = {
            color: '#ff0000'
          }
        }
      }
    },
    setOptions() {
      const textColor = this.theme === 'default' ? '#fff' : '#333'
      const that = this
      if (this.dataTree) {
        this.readNodes([this.dataTree])
        const defaultOpt = {
          tooltip: {
            show: true,
            trigger: 'item',
            triggerOn: 'mousemove',
            enterable: true, // 鼠标是否可进入提示框浮层中
            formatter: this.formatterHover// 修改鼠标悬停显示的内容
          },
          toolbox: {
            show: true,
            top: 20,
            left: 30
          },
          series: [
            {
              type: 'tree',
              data: [this.dataTree],
              top: '1%',
              left: '10%',
              bottom: '1%',
              right: '20%',
              // edgeShape: 'polyline',
              // layout: 'orthogonal',
              label: {
                position: 'left',
                verticalAlign: 'middle',
                align: 'right',
                color: textColor,
                fontSize: 14
              },
              lineStyle: {
                color: '#2E8B57'
              },
              orient: 'LR',
              leaves: {
                label: {
                  position: 'right',
                  verticalAlign: 'middle',
                  align: 'left'
                }
              },
              symbolSize: [30, 30],
              symbol: function(params, params1) {
                // 自定义图片后，不知道是否还有子节点未展开
                // 通过打印，发现没有展开的节点，和最后一层子节点的collapsed属性都为true
                // 所以判断collapsed为true，并且没有孩子节点的时候，就是还有没展开的子节点
                // 修改label的样式，来判断是否还存在子节点没展开
                // console.log('params11111111111111111', params1)
                if (params1.collapsed == true && params1.data.children) {
                  params1.data.label = {
                    borderColor: '#ff0000', // 边框颜色
                    color: '#ff0000',
                    borderWidth: '2', // 边框宽度
                    rotate: '10', // 倾斜
                    fontSize: '13',
                    fontWeight: 'bold'
                  }
                }
              },
              edgeForkPosition: '72%',
              emphasis: { // 高亮
                focus: 'descendant'
              },
              initialTreeDepth: -1, // 树图初始展开层级-1，展开所有子节点
              roam: true, // 鼠标缩放，拖拽整颗树
              expandAndCollapse: false, // 无关的子树折叠收起
              animationDuration: 550,
              animationDurationUpdate: 750,
              width: '50%'// 组件宽度
            }
          ]
        }
        const chart = this.$refs.main
        if (chart) {
          echarts.init(chart).setOption(defaultOpt) // 将画布添加到页面中
          // 双击节点时，跳转到服务器详细信息中
          // 因为树图默认单击会展开收缩子节点，所以会早上双击时，节点位置移动，达不到双击的效果，所以将树节点点击折叠的属性禁掉了expandAndCollapse: false。
          // 否则改成单击时跳转页面很奇怪，如果用户只想点一下节点就跳转了很奇怪
          echarts.init(chart).on('dblclick', function(params) {
            // 子组件向父组件传值，修改tabs的activeName,从而达到组件切换的效果
            that.$emit('updateActiveName', 'serverInfo', 0, params.data.devId)
          });
          echarts.init(chart).on('click', function(params) {
            that.contextmenuCtr(false)
          });
          // echarts.init(chart).on('contextmenu', function () { return false; });//防止默认菜单弹出（查看图像,图像另存为等）
          echarts.init(chart).on('contextmenu', function(params) {
            that.currentDevType = params.data.devType
            that.devId = params.data.devId
            that.currentId = params.data.id
            console.log('params.data.devId', params)
            console.log('that.currentId', that.currentId)
            that.currentGroupId = params.data.groupId != undefined ? params.data.groupId : 0
            // 开启右键菜单、关闭悬停
            that.contextmenuCtr(true)
            // chart 剩余宽度（surplusWidth）：鼠标点击位置距离右边界宽度
            // chart 剩余高度（surplusHeight）：鼠标点击位置距离下边高度
            const surplusWidth = that.$refs.main.clientWidth - (params.event.offsetX + 15)
            const surplusHeight = that.$refs.main.clientHeight - (params.event.offsetY)
            // 菜单个数：依据设备类型不同，个数不同
            let menuCount = 1;
            if (that.currentDevType === 6) {
              that.currentGroupId === 0 ? menuCount = 6 : menuCount = 3
            }
            // console.log('menuCount', menuCount)
            const menuHeight = 28 * menuCount
            // 剩余宽度（surplusWidth）< 菜单宽度时，菜单放在点击位置的左边显示
            if (surplusWidth < 146) {
              that.$refs.rightMenu.style.left = params.event.offsetX + 15 - 150 + 'px'
            } else {
              that.$refs.rightMenu.style.left = params.event.offsetX + 15 + 'px'
            }
            // 剩余高度（surplusHeight）< 菜单高度时，菜单放在点击位置之上显示
            if (surplusHeight < menuHeight) {
              that.$refs.rightMenu.style.top = params.event.offsetY - menuHeight + 'px'
            } else {
              that.$refs.rightMenu.style.top = params.event.offsetY + 'px'
            }
            // // //让自定义菜单随鼠标的箭头位置移动
            // that.$refs.rightMenu.style.left = params.event.offsetX + 15 + 'px'
            // that.$refs.rightMenu.style.top = params.event.offsetY + 15 + 'px'
          });
          // 通过点击的时候，判断子节点是否展开，修改label的样式  echarts版本太低，不能支持
          // echarts.init(chart).on('click', function(params) {
          //   console.log('2222222222222', params)
          //   // 解决树图点击展开图片不显示问题
          //   echarts.init(chart).resize()
          //   if (params.collapsed == true) {
          //     params.data.label = {
          //       borderColor: '#ff0000', // 边框颜色
          //       color: '#ff0000',
          //       borderWidth: '2', // 边框宽度
          //       rotate: '10', // 倾斜
          //       fontSize: '13',
          //       fontWeight: 'bold'
          //     }
          //   } else {
          //     params.data.label = {
          //       color: '#fff',
          //       borderWidth: '0', // 边框宽度
          //       rotate: '0', // 倾斜
          //       fontSize: '9',
          //       fontWeight: 'normal'
          //     }
          //   }
          // });
          // 解决树图首次加载图片不显示问题
          echarts.init(chart).resize()
        }
      }
    },
    /**
     * 鼠标悬停显示详情
     * @param params
     * @returns {string}
     */
    formatterHover(params) {
      if (this.contextmenu) {
        // 隐藏悬停
        return '';
      }
      // 1：引擎 6:采集 9：审批 11：文件 12：检测 219:数据库
      var deviceType = params.data.devType;
      let serverIp = ' '
      const commlist = params.data.commlist
      if (commlist != undefined) {
        commlist.forEach(function(value) {
          // eslint-disable-next-line no-const-assign
          serverIp = serverIp + `[${value.ip}:${value.port}]`
        })
      }
      let html = `<span class="text">${this.$t('table.deviceNum')}：${params.data.devId}</span>` +
        `<span class="text">${this.$t('table.deviceName')}：${params.data.devName}</span>` +
          `<span class="text">${this.$t('table.versionNumber')}：${params.data.version}</span>` +
      `<span class="text">${this.$t('pages.onlineStatus')}：${this.onlines[params.data.status]}</span>`;
      if (deviceType === 1) {
        html += `<span class="text">${this.$t('pages.onlineTerminals')} / ${this.$t('pages.terminalAll')}：${params.data.devOnlineNum} / ${params.data.devTotalNum}</span>`
        if (params.data.devIp != null) {
          html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        }

        // return `<span class="text">设备编号：${+ '</span>'+
        //   `<span class="text">引擎：${params.data.commlist[0].ip}</span>'
      }
      if (deviceType === 6) {
        if (params.data.adminAuthorized != undefined) {
          html += `<span class="text">${this.$t('table.usbDiskStatus')}：${this.adminAuthorized[params.data.adminAuthorized]}</span>`
        }
        html += `<span class="text">${this.$t('pages.onlineTerminals')} / ${this.$t('pages.terminalAll')}：${params.data.devOnlineNum} / ${params.data.devTotalNum}</span>` +
          `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        // if (params.data.internetIp != undefined) {
        //   html += `<span class="text">${this.$t('pages.internetIPAddress')}：${params.data.internetIp}</span>`
        // }
        // return `<span class="text">采集：${params.data.name}</span>`
      }
      if (deviceType === 9) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        // if (params.data.internetIp != undefined) {
        //   html += `<span class="text">${this.$t('pages.internetIPAddress')}：${params.data.internetIp}</span>`
        // }
        // return `<span class="text">审批：${params.data.id}</span>`
      }
      if (deviceType === 11) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        // if (params.data.internetIp != undefined) {
        //   html += `<span class="text">${this.$t('pages.internetIPAddress')}：${params.data.internetIp}</span>`
        // }
        // return `<span class="text">文件：${params.data.devName}</span>`
        if (params.data.adminAuthorized != undefined) {
          html += `<span class="text">${this.$t('table.usbDiskStatus')}：${this.adminAuthorized[params.data.adminAuthorized]}</span>`
        }
      }
      if (deviceType === 12) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        // if (params.data.internetIp != undefined) {
        //   html += `<span class="text">${this.$t('pages.internetIPAddress')}：${params.data.internetIp}</span>`
        // }
        // return `<span class="text">检测：${params.data.devName}</span>`
        if (params.data.adminAuthorized != undefined) {
          html += `<span class="text">${this.$t('table.usbDiskStatus')}：${this.adminAuthorized[params.data.adminAuthorized]}</span>`
        }
      }
      if (deviceType === 219) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        // if (params.data.internetIp != undefined) {
        //   html += `<span class="text">${this.$t('pages.internetIPAddress')}：${params.data.internetIp}</span>`
        // }
        // return `<span class="text">数据库：${params.data.devName}</span>`
      }
      if (deviceType === 172) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        if (params.data.adminAuthorized != undefined) {
          html += `<span class="text">${this.$t('table.usbDiskStatus')}：${this.adminAuthorized[params.data.adminAuthorized]}</span>`
        }
      }
      if (deviceType === 186) {
        html += `<span class="text">${this.$t('pages.onlineTerminals')} / ${this.$t('pages.terminalAll')}：${params.data.devOnlineNum} / ${params.data.devTotalNum}</span>`
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span>`
        if (params.data.adminAuthorized != undefined) {
          html += `<span class="text">${this.$t('table.usbDiskStatus')}：${this.adminAuthorized[params.data.adminAuthorized]}</span>`
        }
      }
      return html;
    },
    mainClick() {
      // 点击其他位置隐藏菜单、恢复悬停
      this.contextmenuCtr(false)
    },
    contextmenuCtr(startMenu) {
      if (startMenu) {
        // 设备管理组件开启 - 控制组件开关目的是组件里左边树恢复初始状态
        this.serverMgrIf = true
        // 开启右键菜单
        this.contextmenu = true
        this.$refs.rightMenu.style.display = 'block';
        // 去掉悬停
        this.$refs.main.children[1].style.display = 'none'
      } else {
        // 关闭右键菜单
        this.contextmenu = false
        this.$refs.rightMenu.style.display = 'none';
        // 去掉悬停
        // this.$refs.main.children[1].style.display = 'block'
      }
    },
    /**
     * 采集服务器
     */
    submitEnd() {
      this.serverMgrIf = false
    },
    daqServerAdd() {
      this.contextmenuCtr(false)
      this.$refs.editDaqServer.handleCreate()
    },
    daqServerUpdate() {
      this.contextmenuCtr(false)
      const query = { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: '',
        devIds: this.devId.toString()
      }
      const promise = getDaqServerPage(query)
      promise.then(res => {
        res.data.items.forEach(item => {
          if (item.devId === this.devId) {
            this.$refs.editDaqServer.handleUpdate(item)
          }
        })
      })
    },
    // 采集-设备管理
    serverMgrSubmitEnd() {
      this.serverMgrIf = false
    },
    daqServerMgr() {
      this.contextmenuCtr(false)
      this.serverMgrIf = true
      this.$refs.serverMgrDlg.show(this.devId)
    },
    // 文件服务器
    backupServerAdd() {
      this.contextmenuCtr(false)
      this.$refs.editBackupServer.handleCreate()
    },
    backupServerUpdate() {
      this.contextmenuCtr(false)
      const query = { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: '',
        devIds: this.devId.toString()
      }
      const promise = getBackupServerPage(query)
      promise.then(res => {
        res.data.items.forEach(item => {
          if (item.devId === this.devId) {
            this.$refs.editBackupServer.handleUpdate(item)
          }
        })
        // if (res.data.items.length === 0) {
        console.log('this.query', this)
        const devIdArr = this.currentId.split('-');
        const parentDevId = devIdArr[devIdArr.length - 2]
        const query2 = { // 查询条件
          page: 1,
          groupId: undefined,
          devId: parentDevId
        }
        const searchQuery2 = Object.assign({}, query2)
        const promise1 = getFileServerPage(searchQuery2)
        promise1.then(res => {
          console.log('getBackupServerPage1111', res)
          res.data.items.forEach(item => {
            if (item.devId === this.devId) {
              // 若是智能备份的文件服务器，需添加smartServerId参数，以便与普通的文件服务器区分
              if (parentDevId && parentDevId >= 6601 && parentDevId < 6700) {
                item.smartServerId = parentDevId
                this.$refs.editBackupServer.handleSmartUpdate(item)
              } else {
                item.smartServerId = undefined
                this.$refs.editBackupServer.handleUpdate(item)
              }
            }
          })
        })
        // }
      })
      // this.$refs.editFileServer.handleSmartUpdate(row)
    },
    // 检测服务器
    detectionServerAdd() {
      this.contextmenuCtr(false)
      this.$refs.editDetectionServer.handleCreate()
    },
    detectionServerUpdate() {
      this.contextmenuCtr(false)
      const query = { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: '',
        devIds: this.devId.toString()
      }
      const promise = getDetectionServerPage(query)
      promise.then(res => {
        res.data.items.forEach(item => {
          if (item.devId === this.devId) {
            this.$refs.editDetectionServer.handleUpdate(item)
          }
        })
      })
    },
    // 软件中心服务器
    softwareServerAdd() {
      this.contextmenuCtr(false)
      this.$refs.editSoftwareServer.handleCreate()
    },
    softwareServerUpdate() {
      this.contextmenuCtr(false)
      const query = { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: '',
        devIds: this.devId.toString()
      }
      const promise = getSoftwareServerPage(query)
      promise.then(res => {
        res.data.items.forEach(item => {
          if (item.devId === this.devId) {
            this.$refs.editSoftwareServer.handleUpdate(item)
          }
        })
      })
    },
    // 数据库服务器
    dbServerAdd() {
      this.contextmenuCtr(false)
      this.$refs.editDbServer.handleCreate()
    },
    dbServerUpdate() {
      this.contextmenuCtr(false)
      const query = { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: '',
        devIds: this.devId.toString()
      }
      const promise = getDbServerPage(query)
      promise.then(res => {
        // console.log('res', res)
        res.data.items.forEach(item => {
          if (item.devId === this.devId) {
            this.$refs.editDbServer.handleUpdate(item)
          }
        })
      })
    },
    // 智能备份
    smartBackupServerAdd() {
      this.$refs.editSmartBackupServer.handleCreate()
    },
    smartBackupServerUpdate() {
      const query = { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: '',
        devId: this.devId.toString()
      }
      const searchQuery = Object.assign({}, query)
      const promise = getServerPage(searchQuery)
      promise.then(res => {
        // console.log('res', res)
        res.data.items.forEach(item => {
          if (item.devId === this.devId) {
            this.$refs.editSmartBackupServer.handleUpdate(item)
          }
        })
      })
    },
    approvalServerUpdate() {
      this.contextmenuCtr(false)
      this.$refs.approvalServerDlg.handleCreate()
    },
    // dbSubmitEnd() {
    //   this.$refs.serverMgrDlg.show()
    // },
    // 采集服务器绑定终端
    daqServerBindTerm() {
      const query = { // 查询条件
        page: 1,
        groupIds: '',
        searchInfo: '',
        status: null,
        name: '',
        computerName: '',
        searchId: '',
        ip: '',
        mac: '',
        type: null,
        useType: null,
        offlineTime: null,
        serverId: undefined
      }
      this.$refs.bindDAQServerDlg.handleUpdate([], 0, query)
    }
  }
}
</script>

<style lang="scss" scoped>
  .legend{
    width: 200px;
    position: absolute;
    top: 0;
    left: 3px;
    z-index: 1;
    user-select: none;
  }
  .legend>ul{
    margin: 0;
    padding: 0;
  }
  .legend>ul>li{
    list-style: none;
    height: 40px;
    line-height: 40px;
    font-size: 12px;
  }
  .legend>ul>li>img{
    width: 20px;
    margin-right: 10px;
    vertical-align:middle;
  }
  *{padding: 0;margin: 0;}
  .menu{
    /*这个样式不写，右键弹框会一直显示在画布的左下角*/
    position: absolute;
    background: rgba(255, 255, 255, 1);
    border-radius: 5px;
    left: -99999px;
    top: -999999px;
  }
  .menu ul{
    border: 1px solid;
    border-radius: 6px;
    list-style: none;
  }
  .menu ul li{
    padding: 5px 10px;
    color: #000000;
    border-bottom: 1px dashed #a9a9a9;
    font-size: 14px;
  }
  .menu ul li:hover{
    color: #0e7beb;
  }
  .menu ul li:last-child{
    border-bottom: none;
  }
  >>>.text {
    height: 30px;
    line-height: 30px;
    padding-left: 5px;
    display: block;
    font-size: 14px;
  }
</style>
