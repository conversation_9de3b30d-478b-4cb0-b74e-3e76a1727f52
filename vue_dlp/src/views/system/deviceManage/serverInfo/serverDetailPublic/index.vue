<template>
  <div class="serverInfo">
    <!--    v-loading="showLoading"-->
    <!--    app-container-->
    <!--    <div v-if="serverTreeData.length>0" class="tree-container">-->
    <!--      <tree-menu-->
    <!--        ref="groupTree"-->
    <!--        :data="serverTreeData"-->
    <!--        resizeable-->
    <!--        default-expand-all-->
    <!--        :render-content="renderContent"-->
    <!--        @node-click="serverTreeNodeClick"-->
    <!--      />-->
    <!--    </div>-->
    <div class="table-container">
      <el-descriptions class="margin-top" title="计算机信息" size="small" :column="3" border>
        <el-descriptions-item :label="$t('pages.operatingSystem')">
          {{ temp.systemName }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.totalPartitions')">
          {{ temp.totalWpi }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.totalNetworkCards')">
          {{ temp.totalNetwork }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.cpuUsage')">
          {{ temp.cpu }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.memoryUsage')">
          {{ temp.allUseMemory }}
        </el-descriptions-item>
      </el-descriptions>
      <el-row >
        <!--        <el-col :span="12">-->
        <!--          <el-card class="box-card">-->
        <!--            <div slot="header">-->
        <!--              <span>{{ $t('pages.serverBasicInformation') }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info" >-->
        <!--              <span>{{ $t('pages.deviceNum') }}:</span>-->
        <!--              <span>{{ temp.devId }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.devType') }}:</span>-->
        <!--              <span >{{ temp.devTypeName }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.deviceName_1') }}:</span>-->
        <!--              <span >{{ temp.devName }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('table.versionNumber') }}:</span>-->
        <!--              <span >{{ temp.version }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.onlineStatus') }}:</span>-->
        <!--              <span>{{ temp.onlineStatus }}</span>-->
        <!--            </div>-->
        <!--            <div v-if="temp.devType && [1, 6, 186].indexOf(temp.devType) > -1" class="item_info" >-->
        <!--              <span>{{ $t('pages.onlineTerminals') }} /{{ $t('pages.terminalAll') }} :</span>-->
        <!--              <span >{{ temp.allDevOnlineNum }}</span>-->
        <!--            </div>-->
        <!--          </el-card>-->
        <!--        </el-col>-->

        <!--        <el-col :span="12" style="padding-left: 5px;">-->
        <!--          <el-card class="box-card" >-->
        <!--            <div slot="header">-->
        <!--              <span>{{ $t('pages.computerInfo',{content:$t('pages.baseInfo')}) }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.operatingSystem') }}:</span>-->
        <!--              <span >{{ temp.systemName }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.totalPartitions') }}:</span>-->
        <!--              <span >{{ temp.totalWpi }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.totalNetworkCards') }}:</span>-->
        <!--              <span>{{ temp.totalNetwork }}</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.cpuUsage') }}:</span>-->
        <!--              <span >{{ temp.cpu }}%</span>-->
        <!--            </div>-->
        <!--            <div class="item_info">-->
        <!--              <span>{{ $t('pages.memoryUsage') }}:</span>-->
        <!--              <span>{{ temp.allUseMemory }}</span>-->
        <!--            </div>-->
        <!--          </el-card>-->
        <!--        </el-col>-->
      </el-row>
      <!--      <el-collapse v-model="activeNames" accordion :class="{ serverInfoCollapse: serverTreeData.length ===0 }" @change="collapseChange">-->
      <!--        <el-collapse-item :title="$t('pages.computerInfo',{content:$t('pages.diskInformation')})" :name="1" >-->
      <!--        </el-collapse-item>-->
      <!--        <el-collapse-item :title="$t('pages.computerInfo',{content:$t('pages.networkCardInformation')})" :name="2">-->
      <!--        </el-collapse-item>-->
      <!--        <el-collapse-item :title="$t('pages.computerInfo',{content:$t('pages.CPU_utilization')})" :name="3">-->
      <!--        </el-collapse-item>-->
      <!--        <el-collapse-item :title="$t('pages.serviceInfo',{content:$t('pages.processInformation')})" :name="4">-->
      <!--        </el-collapse-item>-->
      <!--      </el-collapse>-->
      <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.computerInfo',{content:$t('pages.diskInformation')})" name="1" >
          <!--          <div class="table-container">-->
          <grid-table
            ref="diskTable"
            :col-model="diskColModel"
            :row-datas="diskRowData"
            :default-sort="diskDefaultSort"
            :multi-select="multiSelect"
            :height="300"
            :show-pager="false"
            :size="'mini'"
          />
          <!--          </div>-->
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.computerInfo',{content:$t('pages.networkCardInformation')})" name="2" >
          <div class="table-container">
            <grid-table
              ref="netCardTable"
              :col-model="netCardColModel"
              :row-datas="netCardRowData"
              :default-sort="netCardDefaultSort"
              :multi-select="multiSelect"
              :height="300"
              :show-pager="false"
              :size="'mini'"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.computerInfo',{content:$t('pages.CPU_utilization')})" name="3" >
          <div class="chart-container">
            <CpuChart ref="cupChartId" :chart-data="chartData" />
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.serviceInfo',{content:$t('pages.processInformation')})" name="4" >
          <div class="table-container">
            <grid-table
              ref="processInfoTable"
              :col-model="processInfoColModel"
              :row-datas="processInfoRowData"
              :default-sort="processInfoDefaultSort"
              :multi-select="multiSelect"
              :height="300"
              :show-pager="false"
              :size="'mini'"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import CpuChart from '@/components/ECharts/CpuChart'

export default {
  name: 'ServerDetail',
  components: { CpuChart },
  props: {
    serverInfoList: {
      type: Array,
      default: function() {
        return []
      }
    },
    serverTreeData: {
      type: Array,
      default: function() {
        return []
      }
    },
    originClass: {
      type: Object,
      default: function() {
        return { color: 'red' }
      }
    },
    devId: {
      type: Number,
      default: function() {
        return 1;
      }
    }
  },
  data() {
    return {
      activeNames: [],
      activeName: '1',
      showLoading: false,
      multiSelect: false,
      colModel: [
        { prop: 'devId', label: 'number', width: '50' },
        { prop: 'devName', label: 'serverName', width: '50' }
      ],
      diskDefaultSort: { prop: 'name', order: 'desc' },
      diskColModel: [
        { prop: 'diskName', label: 'diskName', width: '120', fixed: true, sort: true },
        { prop: 'diskFormate', label: 'diskFormat', width: '120', sort: true },
        { prop: 'totalDiskSize', label: 'diskSize', width: '120', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'useDiskSize', label: 'usedSpace', width: '120', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'freeSize', label: 'remainingSpace', width: '120', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'usedPercent', label: 'diskUtilization', width: '120', sort: true, sortOriginal: true, formatter: this.usedPercentFormatter }
      ],
      diskRowData: [],
      netCardDefaultSort: { prop: 'mac', order: 'desc' },
      netCardColModel: [
        { prop: 'adapterName', label: 'adapterName', width: '200', fixed: true, sort: true },
        { prop: 'mac', label: 'physicalAddress', width: '200', sort: true },
        { prop: 'ips', label: 'networkAddressIPv4', width: '150', sort: true },
        { prop: 'adapterDesc', label: 'adapterDescription', width: '200', sort: true },
        { prop: 'ipv6s', label: 'networkAddressIPv6', width: '200', sort: true }
      ],
      netCardRowData: [],
      processInfoDefaultSort: { prop: 'mac', order: 'desc' },
      processInfoColModel: [
        { prop: 'processName', label: 'processName', width: '200', fixed: true, sort: true },
        { prop: 'processId', label: 'processID', width: '150', sort: true },
        { prop: 'cpu', label: 'cpu', width: '150', sort: true },
        { prop: 'memory', label: 'memory', width: '150', sort: true, formatter: this.sizeFormatter, sortOriginal: true },
        { prop: 'virtualMemory', label: 'virtualMemory', width: '150', sort: true, formatter: this.sizeFormatter, sortOriginal: true }
      ],
      processInfoRowData: [],
      chartData: {
        yAxisData: []
      },
      query2: { // 查询条件
        page: 1,
        devId: undefined,
        searchInfo: ''
      },
      temp: {
        devId: undefined,
        devName: undefined,
        devType: undefined,
        devTypeName: undefined,
        systemName: undefined,
        useMemory: undefined,
        totalMemory: undefined,
        allUseMemory: undefined,
        cpu: undefined,
        onlineStatus: undefined,
        devOnlineNum: undefined,
        devTotalNum: undefined,
        allDevOnlineNum: undefined,
        totalWpi: '',
        totalNetwork: undefined,
        version: undefined
      },
      readDataStatus: true,
      timer: undefined,
      onlineStatusMap: {
        0: this.$t('pages.offline'),
        1: this.$t('pages.online')
      },
      devTypeMap: {
        1: this.$t('pages.server_eng'),
        6: this.$t('pages.server_daq'),
        9: this.$t('pages.server_approval'),
        11: this.$t('pages.BackupServer'),
        12: this.$t('pages.DetectionServer'),
        219: this.$t('pages.database'),
        172: this.$t('route.softwareServer'),
        186: '智能备份'
      }
    }
  },
  computed: {
    serverDiskInfoGridTable() {
      return this.$refs['diskTable']
    },
    serverNetworkInfoGridTable() {
      return this.$refs['netCardTable']
    }
  },
  created() {
    console.log('serverTreeDataserverTreeData1', this.serverTreeData)
  },
  activated() {
    console.log('serverTreeDataserverTreeData', this.serverTreeData)
    clearInterval(this.timer)
  },
  deactivated() {
    clearInterval(this.timer)
  },
  mounted() {
    clearInterval(this.timer)
    this.showLoading = true
    this.timer = setInterval(() => {
      this.sendToServer()
    }, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    clickServer(data) {
      this.showLoading = true
      this.query2.devId = data.id
      this.chartData.yAxisData.splice(0, this.chartData.yAxisData.length)
      // 通过emit将编号传给父组件
      // 用户切换tabs时，继续选中列表选中项
      this.$emit('devId', data.id)
    },
    isPanel(name) {
      return this.activeName == name
    },
    tabClick(val) {
      console.log('tabClick', val)
      this.$refs['cupChartId'].resize()
      if (val.index == '3') {
        console.log('tabClick start', val)
        this.$refs['cupChartId'].resize()
      }
    },
    sendToServer() {
      const that = this
      if (!that.query2.devId) {
        console.log('that.query2.devId', that.query2.devId)
        that.query2.devId = this.devId
      }
      if (that.query2.devId != undefined) {
        that.readDataStatus = false
        if (that.temp.devId != undefined && that.temp.devId != that.query2.devId) {
          that.resetData(that) // 重置数据
        }
        this.$socket.sendToUser(that.query2.devId, '/getServerInfo', that.query2.devId, (respond, handle) => {
          const data = JSON.parse(JSON.stringify(respond.data))
          handle.close()
          that.showLoading = false
          if (respond.data.serverDiskInfoList != undefined && respond.data.serverDiskInfoList != []) {
            that.diskRowData.splice(0)
            data.serverDiskInfoList.forEach((d) => {
              if (d.usedPercent) {
                d.usedPercent = parseFloat(d.usedPercent)
              }
              that.diskRowData.push(d)
            })
          }
          if (respond.data.serverNetworkInfoList != undefined && respond.data.serverNetworkInfoList != []) {
            that.netCardRowData.splice(0)
            data.serverNetworkInfoList.forEach((d) => {
              that.netCardRowData.push(d)
            })
          }
          if (respond.data.serverProcessInfoList != undefined) {
            that.processInfoRowData.splice(0)
            respond.data.serverProcessInfoList.forEach((data) => {
              that.processInfoRowData.push(data)
            })
          }
          if (respond.data.serverBusinessInfo != undefined) {
            const serverBusinessInfo = respond.data.serverBusinessInfo
            that.temp.devOnlineNum = serverBusinessInfo.termOnlineNum
            that.temp.devTotalNum = serverBusinessInfo.termTotalNum
            that.temp.allDevOnlineNum = that.temp.devOnlineNum + '/' + that.temp.devTotalNum
            that.temp.version = serverBusinessInfo.version
          }
          if (respond.data.serverBaseInfo != undefined) {
            const serverBaseInfo = respond.data.serverBaseInfo
            // that.temp.diskNums = respond.data.diskNums
            // that.temp.netCardNums = respond.data.netCardNums
            that.temp.devId = serverBaseInfo.devId
            that.temp.devName = serverBaseInfo.devName
            that.temp.devType = serverBaseInfo.devType
            that.temp.devTypeName = this.devTypeMap[serverBaseInfo.devType]
            that.temp.systemName = serverBaseInfo.systemName
            that.temp.useMemory = serverBaseInfo.useMemory
            that.temp.totalMemory = serverBaseInfo.totalMemory
            if (that.temp.useMemory && that.temp.totalMemory) { // 内存使用
              that.temp.allUseMemory = that.round(that.temp.useMemory / that.temp.totalMemory) + '%(' + this.convertSize(that.temp.useMemory) + '/' + this.convertSize(that.temp.totalMemory) + ')';
              // (that.temp.useMemory / that.temp.totalMemory).toFixed(2) * 100 + '%(' + this.convertSize(that.temp.useMemory) + '/' + this.convertSize(that.temp.totalMemory) + ')'
            }
            that.temp.cpu = serverBaseInfo.cpu
            if (0 === serverBaseInfo.onlineStatus) {
              // 离线时，清除缓存数据
              that.resetData(that)
            }
            that.temp.onlineStatus = this.onlineStatusMap[serverBaseInfo.onlineStatus]
            that.temp.totalWpi = that.diskRowData.length
            that.temp.totalNetwork = that.netCardRowData.length
            that.retFinished = true
            if (that.chartData.yAxisData.length == 100) that.chartData.yAxisData.splice(0, 1)
            that.chartData.yAxisData.push(serverBaseInfo.cpu)
          }
          that.readDataStatus = true
          that.showLoading = false
          that.$emit('serverInfo', that.temp)
        }, (handle) => {
          handle.close()
          that.readDataStatus = true
          that.showLoading = false
          if (that.showLoading) {
            that.showLoading = false
            that.$notify({ title: this.$t('text.fail'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
          }
        })
      }
    },
    sizeFormatter: function(row, data) {
      return this.convertSize(data)
    },
    usedPercentFormatter: function(row, data) {
      return data + '%'
    },
    round: function(value) {
      const result = (value * 100).toString();
      const pointIdx = result.indexOf('.');
      if (pointIdx == -1) {
        return result;
      } else {
        const res = result.substring(0, pointIdx);
        console.log(res)
        return res;
      }
    },
    convertSize: function(size) {
      if (!size) {
        return '0 Bytes';
      }
      var sizeNames = [' Bytes', ' KB', ' MB', ' GB', ' TB', ' PB', ' EB', ' ZB', ' YB'];
      var i = Math.floor(Math.log(size) / Math.log(1024));
      var p = (i > 1) ? 2 : 1;
      if (p == 1) {
        return 1 + sizeNames[1];
      } else {
        return (size / Math.pow(1024, Math.floor(i))).toFixed(p) + sizeNames[i];
      }
    },
    resetData: function(that) {
      that.diskRowData.splice(0, that.diskRowData.length)
      that.netCardRowData.splice(0, that.netCardRowData.length)
      that.processInfoRowData.splice(0, that.processInfoRowData.length)
      // that.temp.devId = undefined
      // that.temp.devName = undefined
      // that.temp.devType = undefined
      // that.temp.devTypeName = undefined
      that.temp.systemName = undefined
      that.temp.useMemory = undefined
      that.temp.totalMemory = undefined
      that.temp.allUseMemory = undefined
      that.temp.cpu = undefined
      that.temp.onlineStatus = undefined
      that.temp.devOnlineNum = undefined
      that.temp.devTotalNum = undefined
      that.temp.allDevOnlineNum = undefined
      that.temp.totalWpi = undefined
      that.temp.totalNetwork = undefined
      that.retFinished = true
      that.chartData.yAxisData.splice(0)
      // if (that.chartData.yAxisData.length == 100) that.chartData.yAxisData.splice(0, 1)
      // that.chartData.yAxisData.push(undefined)
    },
    serverTreeNodeClick(data, node) {
      if (data.id < 0) {
        return
      }
      this.clickServer(data)
    },
    renderContent(h, { node, data, store }) {
      const iconClass = data.id < 0 ? '' : 'server';
      return (
        <div>
          <svg-icon v-show={!!iconClass} icon-class={!iconClass ? '' : iconClass} />
          <span>{data.label}</span>
        </div>
      )
    }
  }
}
</script>

<style lang="scss" scoped>
  .chart-container{
    height: calc(100% - 20px);
    overflow: auto;
  }
  .serverInfo {
    padding-right: 0;
    margin-bottom: 10px;
  }
  .table-container {
    //padding-right: 10px;
    overflow: auto;
  }
  >>> .el-card__header{
    padding: 10px 10px;
  }
  >>> .el-card__body{
    font-size: 14px;
  }
  >>> .text {
    font-size: 14px;
    display: flex;
  }
  >>> .text>span:first-child{
    width: 40px;
  }
  >>> .text>span:last-child{
    flex: 1;
    cursor: pointer;
  }
  >>> .item {
    margin-bottom: 18px;
  }
  >>> .clearfix{
    font-size: 14px;
  }
  >>> .clearfix>span:first-child{
    display: inline-block;
    width: 40px;
  }
  >>> .clearfix:before,
  >>> .clearfix:after {
    display: table;
    content: "";
  }
  >>> .clearfix:after {
    clear: both
  }
  .activeClass{
    color:#409EFF
  }
  .el-collapse {
    margin-top: 10px;
  }
  >>> .el-collapse-item__content {
    padding-bottom: 5px;
  }
  >>> .el-collapse-item__arrow {
    margin: 0px;
  }
  .box-card {
    border: solid 1px #eee;
    padding: 5px 5px 5px;
    height: 242px;
  }
  .item_info {
    width: 400px;
    height: 30px;
    line-height: 30px;
    overflow: hidden;
  }
  .serverInfoCollapse>>> .el-collapse-item__header {
    color: #666;
  }
</style>
