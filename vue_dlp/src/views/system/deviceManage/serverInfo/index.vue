<template>
  <div class="app-container">
    <div class="table-container">
      <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.serverTopologyOverview')" name="topo" >
          <ServerTopo v-if="isTopoUpdate" ref="topoCanvas" :data-tree="dataTree" @updateActiveName="updateActiveName"/>
        </el-tab-pane>
        <el-tab-pane :lazy="true" :label="$t('pages.serverDetails')" name="serverInfo" :disabled="!serverTreeData || serverTreeData.length === 0">
          <server-detail v-if="isServerInfoUpdate" ref="ServerDetail" :server-tree-data="serverTreeData" :server-info-list="serverInfoList" @devId="updateDevId"/>
        </el-tab-pane>
        <!-- 24/11/01 使用v-permission时，目前不会隐藏tab，而是将内部内容隐藏，暂时修改成 v-if  hasPermission -->
        <el-tab-pane v-if="hasPermission('364')" :lazy="true" :label="$t('pages.ServerLog')" name="serverLog">
          <server-log v-if="isServerLog" ref="serverLog" @openConfig="openLogConfig"/>
        </el-tab-pane>
        <el-tab-pane v-if="hasPermission('397')" :lazy="true" :label="$t('pages.serverAlarmSet')" name="serverAlarmSet">
          <server-alarm/>
        </el-tab-pane>
      </el-tabs>
    </div>

    <server-log-config ref="logConfig"/>
  </div>
</template>
<script type="text/jsx">
import ServerTopo from './serverTopo'
import ServerDetail from './serverDetail'
import ServerAlarm from './serverAlarm';
import ServerLog from './serverLog'
import ServerLogConfig from './serverLog/config'
// import Cookies from 'js-cookie'
export default {
  name: 'ServerInfo',
  components: { ServerTopo, ServerAlarm, ServerDetail, ServerLog, ServerLogConfig },
  props: {
  },
  data() {
    return {
      wsMid: '', // websocket 组件声明，控制组件销毁
      activeName: 'topo',
      serverInfoList: [],
      dataTopo: [],
      dataTree: {},
      links: [],
      gather: {},
      serverTreeData: [],
      serverTreeMap: {
        1: { label: this.$t('pages.server_eng'), children: [] },
        6: { label: this.$t('pages.server_daq'), children: [] },
        11: { label: this.$t('pages.BackupServer'), children: [] },
        9: { label: this.$t('pages.server_approval'), children: [] },
        219: { label: this.$t('pages.DBServer'), children: [] },
        12: { label: this.$t('pages.DetectionServer'), children: [] },
        172: { label: this.$t('route.softwareServer'), children: [] },
        186: { label: '智能备份', children: [] }
      },
      otherServerTreeData: { label: this.$t('pages.server_other'), children: [] },
      totalGatherServerNums: undefined,
      onlineGatherServerNums: undefined,
      // isTopoUpdate和isServerInfoUpdate、isServerLog解决tabs页面初始化时，把两个子组件全部初始化，后面点击切换时，数据不会重新加载的问题
      isTopoUpdate: true,
      isServerInfoUpdate: false,
      isServerLog: false,
      devId: undefined, // 服务器详细信息--服务器列表信息中选中服务名称的编号
      number: undefined // 拓扑图双击进入详细页--双击节点的编号
    }
  },
  created() {
    // this.sendToServerList()
    clearInterval(this.timer)
    this.setInterval()
  },
  activated() {
    // console.log('activated', this.timer)
    clearInterval(this.timer)
    if (this.isPanel('topo')) {
      this.setInterval()
    }
  },
  deactivated() {
    // console.log('deactivated', 'deactivated')
    clearInterval(this.timer)
    if (this.wsMid) {
      delete this.$socket.bindCallbackMap[this.wsMid]
      this.$socket.unsubscribe('/getServerInfoList')
    }
  },
  mounted() {
    // console.log('mounted', 'mounted')
    // clearInterval(this.timer)
    // this.setInterval()
  },
  beforeDestroy() {
    // 组件关闭时
    clearInterval(this.timer)
    if (this.wsMid) {
      delete this.$socket.bindCallbackMap[this.wsMid]
      this.$socket.unsubscribe('/getServerInfoList')
    }
  },
  methods: {
    // name取值：topo serverInfo serverLog serverAlarmSet
    isPanel(name) {
      return this.activeName == name
    },
    renderContent(h, { node, data, store }) {
      const iconClass = data.id < 0 ? '' : 'server';
      return (
        <div>
          <svg-icon v-show={!!iconClass} icon-class={!iconClass ? '' : iconClass} />
          <span>{data.label}</span>
        </div>
      )
    },
    setInterval() {
      this.timer = setInterval(() => {
        this.sendToServerList()
      }, 1000)
    },
    updateDevId(data) {
      this.devId = data
    },
    // 拓扑图双击节点时传值
    updateActiveName(data, index, number) {
      clearInterval(this.timer)
      // this.dataTopo.splice(0, this.dataTopo.length)
      this.activeName = data // 拓扑图节点双击后，tabs选中服务器详细信息
      this.isServerInfoUpdate = true
      this.isTopoUpdate = false
      this.$nextTick(() => {
        // 进入详细信息后，选中双击节点的编号
        this.$refs['ServerDetail'].query2.devId = number // 编号
        this.$refs['ServerDetail'].active = index // 索引
      })
    },
    // tab菜单点击事件
    tabClick(pane, event) {
      clearInterval(this.timer)
      if (this.isPanel('topo')) {
        this.setInterval()
        this.isServerInfoUpdate = false
        this.isTopoUpdate = true
        this.isServerLog = false
      } else if (this.isPanel('serverInfo')) {
        // 详情
        this.dataTopo.splice(0, this.dataTopo.length)
        this.isServerInfoUpdate = true
        this.isTopoUpdate = false
        this.isServerLog = false
        this.$nextTick(() => {
          // 服务器详细信息 列表如果有选中，则将选中编号在子组件中通过emit传给父组件
          // 用户切换tabs时，继续选中列表选中项
          this.$refs['ServerDetail'].query2.devId = this.devId
        })
      } else if (this.isPanel('serverLog')) {
        this.isServerInfoUpdate = false
        this.isTopoUpdate = false
        this.isServerLog = true
      } else if (this.isPanel('serverAlarmSet')) {
        this.isServerInfoUpdate = false
        this.isTopoUpdate = false
        this.isServerLog = false
      }
    },
    serverTopoBaseInfoListToServerTreeNode(serverTopoBaseInfoList) {
      // console.log('tree--------', serverTopoBaseInfoList)
      this.otherServerTreeData.children.splice(0)
      for (const key in this.serverTreeMap) {
        this.serverTreeMap[key].children.splice(0)
        this.serverTreeMap[key].id = '-' + key
      }
      serverTopoBaseInfoList.forEach((value, index, array) => {
        const nodeData = { label: value.devName + '（' + value.devId + '）', id: value.devId, devType: value.devType }
        const rootNode = this.serverTreeMap[value.devType]
        if (rootNode) {
          rootNode.children.push(nodeData)
        } else {
          this.otherServerTreeData.children.push(nodeData)
        }
      })
      this.serverTreeData.splice(0)
      if (this.otherServerTreeData.children.length > 0) {
        this.serverTreeData.push(this.otherServerTreeData)
      }
      for (const key in this.serverTreeMap) {
        const rootNode = this.serverTreeMap[key]
        if (rootNode.children.length > 0) {
          this.serverTreeData.push(rootNode)
        }
      }
      if (!this.hasPermission('A4K')) {
        this.serverTreeData = this.serverTreeData.filter(item => item.id !== '-172')
      }
      // console.log('serverTreeData--------', this.serverTreeData)
    },
    // 拓扑图节点值获取
    sendToServerList() {
      // const sid = Cookies.get('sid')
      const that = this
      this.wsMid = this.$socket.sendToUser(1, '/getServerInfoList', 1, (respond, handle) => {
        handle.close()

        const arr = JSON.parse(respond.data.dataTree)
        if (!arr) {
          // 当所有服务器都未审批接入的时候，不显示服务器树。
          that.dataTree = null
          return;
        }
        that.dataTree = arr[0]
        // console.log('that.dataTree', that.dataTree)
        const serverTopoBaseInfoList = respond.data.serverTopoBaseInfoList
        // this.devId = serverTopoBaseInfoList[0].devId // 页面初始化时，选中第一个
        this.devId = 1 // 详情默认显示引擎
        that.serverInfoList.splice(0, that.serverInfoList.length)
        serverTopoBaseInfoList.forEach(function(value, index, array) {
          that.serverInfoList.push({
            devId: value.devId,
            devName: value.devName
          })
        })
        this.serverTopoBaseInfoListToServerTreeNode(serverTopoBaseInfoList)

        const devIdStatusMap = {} // key为设备ID， value为在线状态
        that.dataTopo.splice(0, that.dataTopo.length)

        serverTopoBaseInfoList.forEach(function(value, index, array) {
          let serverIp = ' '
          if (value.commlist != undefined) {
            const commlist = value.commlist;
            commlist.forEach(function(value) {
              // eslint-disable-next-line no-const-assign
              serverIp = serverIp + '[' + value.ip + ':' + value.port + ']'
            })
          }
          const topo = {}
          topo.id = '' + value.devId
          topo.number = value.devId
          topo.devName = value.devName
          topo.serverIp = serverIp // JSON.stringify(value.commlist)
          topo.online = value.devOnlineNum
          topo.total = value.devTotalNum
          topo.devType = value.devType// 'online'
          topo.status = value.status// 'online'
          topo.lineStatus = value.lineStatus
          topo.totalGatherServerNums = value.totalGatherServerNums
          topo.onlineGatherServerNums = value.onlineGatherServerNums
          topo.label = value.devName
          topo.version = value.version
          that.dataTopo.push(topo)
          devIdStatusMap[value.devId] = value.status
        })

        // const serverLinkInfoList = respond.data.serverLinkInfoList
        // that.links.splice(0, that.links.length)
        // serverLinkInfoList.forEach(function(value, index, array) {
        //   const link = {}
        //   link.source = value.source
        //   link.target = value.target
        //   const sourceOnline = devIdStatusMap[value.source]
        //   const targetOnline = devIdStatusMap[value.target]
        //   if (sourceOnline && targetOnline) {
        //     link.style = { lineWidth: 2, stroke: '#03fc67' }
        //   }
        //   that.links.push(link)
        // })

        // this.gather.totalGatherServerNums = respond.data.totalGatherServerNums
        // this.gather.onlineGatherServerNums = respond.data.onlineGatherServerNums
      }, (handle) => {
        handle.close()
        // that.readDataStatus = true
        // if (that.showLoading) {
        //   that.showLoading = false
        //   this.$notify({ title: '错误', message: '请求超时', type: 'error', duration: 2000 })
        // }
      })
    },
    calcFileSuffix(fileInfo) {
      const arr = fileInfo.fileName.split('.')
      if (arr.length > 1) {
        fileInfo.suffix = arr.pop().toLowerCase()
      }
      return fileInfo
    },
    openLogConfig(devId, devName) {
      this.$refs['logConfig'].openConfig(devId, devName)
    },
    handleTreeClick(data, node, el, tree) {
      this.$refs.serverLog.handleTreeClick(data, node, el, tree)
      this.$refs['tree'].clearLoading()
    }
  }
}
</script>
<style scoped>
  .app-container{
    padding: 10px 15px;
  }
  .table-container {
    position: relative;
  }
  .download-btn {
    position: absolute;
    right: 5px;
    top: 5px;
  }
</style>
