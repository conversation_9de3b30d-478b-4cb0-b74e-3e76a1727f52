<template>
  <tree-menu
    ref="tree"
    :data="treeData"
    node-key="id"
    current-node-key="0_0"
    resizeable
    :accordion="false"
    default-expand-all
    expand-on-click-node
    :render-content="renderContent"
    @node-click="handleClick"
  />
</template>

<script type="text/jsx">
import { getServers } from '@/api/system/deviceManage/serverLog'

export default {
  name: 'ServerTree',
  data() {
    return {
      treeData: [],
      defaultTreeData: [
        { label: this.$t('pages.serverlog_server_console'), type: 0, devId: 0, loading: false },
        { label: this.$t('pages.server_eng'), type: 1, devId: 1, loading: false },
        { label: this.$t('pages.server_daq'), type: 6, children: undefined },
        { label: this.$t('pages.BackupServer'), type: 11, children: undefined },
        { label: this.$t('pages.DetectionServer'), type: 12, children: undefined },
        { label: this.$t('route.softwareServer'), type: 172, hide: !this.hasPermission('A4K'), children: undefined },
        { label: this.$t('route.intelligentBackupServer'), type: 186, hide: !this.hasPermission('A4L'), children: undefined }
      ],
      fileTraceServer: { id: '22_201', label: this.$t('route.docTrackServer'), type: 22, hide: !this.hasPermission('A4C'), devId: 201, loading: false },
      reportServer: { id: '187_202', label: this.$t('route.reportServer'), type: 187, hide: !this.hasPermission('A4A'), devId: 202, loading: false }
    }
  },
  created() {
    this.initServerData()
  },
  methods: {
    resetTreeData() {
      this.treeData = []
      this.defaultTreeData.forEach(item => {
        if (item.hide) {
          return
        }
        const _item = { ...item }
        if ('children' in _item) {
          _item.children = []
        }
        _item.id = item.type + '_' + (item.devId || 0)
        this.treeData.push(_item)
      })
    },
    initServerData() {
      this.resetTreeData()
      getServers().then(respond => {
        const servers = respond.data || []
        for (let i = 0; i < servers.length; i++) {
          const server = servers[i]
          if (server.devType === this.fileTraceServer.type && !this.fileTraceServer.hide) {
            this.treeData.push(this.fileTraceServer)
            continue
          }
          if (server.devType === this.reportServer.type && !this.reportServer.hide) {
            this.treeData.push(this.reportServer)
            continue
          }
          for (let j = 0; j < this.treeData.length; j++) {
            const item = this.treeData[j]
            if (!item.hide && server.devType === item.type) {
              if ('children' in item) {
                item.children.push({
                  id: item.type + '_' + server.devId,
                  label: server.name + '(' + server.devId + ')',
                  type: item.type,
                  devId: server.devId,
                  loading: false
                })
              }
              break
            }
          }
        }
      })
    },
    renderContent(h, { node, data, store }) {
      const iconClass = 'children' in data ? 'serverGroup' : 'server';
      return (
        <div>
          <i v-show={data.loading} class='el-icon-loading'/>
          <svg-icon v-show={!data.loading} icon-class={iconClass}/>
          <span>{data.label}</span>
        </div>
      )
    },
    handleClick(data, node, el) {
      if ('devId' in data) {
        this.$emit('click', data, node, el, this)
      }
    },
    getCurrentNode() {
      return new Promise(resolve => {
        const tree = this.$refs['tree'].tree()
        tree.$nextTick(() => {
          resolve(tree.getCurrentNode())
        })
      })
    },
    clearLoading() {
      const clearFunc = function(arr) {
        for (let i = 0; i < arr.length; i++) {
          const item = arr[i]
          if ('children' in item) {
            clearFunc(item.children)
            continue
          }
          if ('loading' in item) {
            item.loading = false
          }
        }
      }
      clearFunc(this.treeData)
    }
  }
}
</script>
