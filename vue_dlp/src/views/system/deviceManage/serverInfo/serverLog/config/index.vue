<template>
  <el-dialog
    ref="dlg"
    v-el-drag-dialog
    :title="dlgTitle"
    width="650px"
    :class="$store.getters.language"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    @opened="handleOpened"
    @close="handleClose"
  >
    <div v-show="loading" class="el-loading-mask">
      <div class="el-loading-spinner">
        <svg viewBox="25 25 50 50" class="circular">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
      </div>
    </div>
    <console-log-config v-if="isConsole" ref="web"/>
    <device-log-config v-else ref="dev" :dev-id="devId"/>
    <div slot="footer" class="dialog-footer">
      <el-button v-show="!loading" :loading="submitting" type="primary" @click="saveConfig">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import ConsoleLogConfig from './console'
import DeviceLogConfig from './device'
import axios from 'axios'

export default {
  name: 'ServerLogConfig',
  components: { ConsoleLogConfig, DeviceLogConfig },
  data() {
    return {
      devId: undefined,
      devName: '',
      visible: false,
      loading: false,
      submitting: false,
      cancelTokenSource: undefined
    }
  },
  computed: {
    dlgTitle() {
      let title = this.$t('route.ServerLog') + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('button.highConfig')
      if (this.devName) {
        title += ' - ' + this.devName
      }
      return title
    },
    isConsole() {
      return this.devId === 0
    }
  },
  methods: {
    configRef() {
      return this.$refs[this.isConsole ? 'web' : 'dev']
    },
    openConfig(devId, devName) {
      this.devId = devId
      this.devName = devName
      this.visible = true
      this.loading = true
      if (this.cancelTokenSource) {
        this.cancelTokenSource.cancel()
      }
      this.cancelTokenSource = axios.CancelToken.source()
      this.$nextTick(() => {
        this.configRef().getConfig(this.cancelTokenSource.token).then(() => {
          this.loading = false
          this.cancelTokenSource = undefined
        }).catch(() => {
          this.cancelTokenSource = undefined
        })
      })
    },
    saveConfig() {
      this.submitting = true
      if (this.cancelTokenSource) {
        this.cancelTokenSource.cancel()
      }
      this.cancelTokenSource = axios.CancelToken.source()
      this.configRef().saveConfig(this.cancelTokenSource.token).then(() => {
        this.submitting = false
        this.visible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.dlgTitle + ', ' + this.$t('text.editSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => { this.submitting = false })
    },
    handleOpened() {
      const el = this.$refs.dlg.$el
      const style = getComputedStyle(el, null)
      if (parseInt(style.zIndex) < 3000) {
        el.style.zIndex = '3000'
      }
    },
    handleClose() {
      if (this.cancelTokenSource) {
        this.cancelTokenSource.cancel()
        this.cancelTokenSource = undefined
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-loading-mask {
    margin: 34px 0 58px 0;
  }
  .en >>>.log-config .log-config-check-group {
    margin-left: 117px;
    >.log-config-item {
      width: 200px;
      .el-checkbox {
        width: 200px;
      }
    }
  }
  >>>.log-config {
    .log-config-label {
      font-size: 14px;
    }
    .log-config-check-all {
      font-size: 0;
      height: 30px;
      line-height: 30px;
      margin-left: 10px;
      .el-checkbox {
        margin-left: 10px;
      }
    }
    .log-config-check-group {
      margin-left: 94px;
      line-height: 30px;
      .log-config-item {
        height: 30px;
      }
    }
    .log-config-item {
      height: 36px;
      line-height: 30px;
      margin-left: 10px;
      .el-checkbox {
        width: 125px;
      }
    }
    .log-config-autoclose {
      margin-top: 10px;
    }
    .log-config-input {
      width: 60px;
      height: 28px;
      line-height: 28px;
      .el-input {
        display: inline-block;
        height: 28px;
        .el-input__inner {
          padding-left: 5px;
          padding-right: 5px;
        }
      }
    }
    .log-config-remark {
      margin-left: 10px;
      color: #2674b2;
      font-size: 12px;
    }
  }
</style>
