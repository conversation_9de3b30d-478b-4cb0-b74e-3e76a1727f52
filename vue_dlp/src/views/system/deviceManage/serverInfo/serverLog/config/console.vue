<template>
  <div class="log-config">
    <el-divider content-position="left">{{ $t('pages.serverlog_config_output') }}</el-divider>
    <div class="log-config-check-all">
      <label class="log-config-label">{{ $t('pages.serverlog_debug_open') }}</label>
      <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAllChange">
        {{ $t('button.selectAll') }}
      </el-checkbox>
    </div>
    <div class="log-config-check-group">
      <div v-for="(item, key) in loggers" :key="key" class="log-config-item">
        <el-checkbox v-model="item.level" true-label="DEBUG" :false-label="item.default">{{ item.label }}</el-checkbox>
      </div>
    </div>
    <!--<div v-show="checkAll || indeterminate" class="log-config-autoclose">
      <el-input-number
        v-model="autoClose"
        class="log-config-input"
        :min="0"
        :max="24"
        :controls="false"
        :step="1"
        step-strictly
      />
      <span class="log-config-label">{{ $t('pages.serverlog_debug_close1') }}</span>
      <span class="log-config-remark">
        <i class="el-icon-info"></i>
        0 {{ $t('pages.serverlog_debug_close2') }}
      </span>
    </div>-->

    <el-divider content-position="left">{{ $t('pages.serverlog_config_cleanup') }}</el-divider>
    <div v-for="(item, key) in clear" :key="key" class="log-config-item">
      <i18n :path="item.label">
        <el-input-number
          slot="num"
          v-model="item.value"
          class="log-config-input"
          :min="item.min"
          :max="item.max"
          :step="item.step"
          step-strictly
          :controls="false"
        />
        <template slot="space" >&nbsp;</template>
      </i18n>
      <!--<span class="log-config-remark">
        <i class="el-icon-info"></i>
        {{ item.tips }}
      </span>-->
      <el-tooltip effect="dark" :placement="item.placement">
        <div slot="content" v-html="item.tips()"/>
        <i class="el-icon-info"/>
      </el-tooltip>
    </div>
    <!--<div class="log-config-remark">
      控制台单个日志文件最大10MB，日志超过10MB打印到新的文件时才会查看文件总大小是否超过限制大小，<br>超过大小后删除当日最早的文件
    </div>-->

    <!--
    <el-divider content-position="left">日志下载地址配置</el-divider>
    <console-address ref="urlCfg"/>
    -->
  </div>
</template>

<script>
// import ConsoleAddress from './address'
import { getConsoleLogConfigs, updateConsoleLogConfigs } from '@/api/system/deviceManage/serverLog'

export default {
  name: 'ConsoleLogConfig',
  // components: { ConsoleAddress },
  data() {
    return {
      checkAll: false,
      indeterminate: false,
      loggers: {
        // HTTP请求日志
        http: {
          label: this.$t('pages.serverlog_logger_http'),
          level: 'WARN',
          default: 'WARN',
          codes: ['log.level.http_log']
        },
        // 通信协议日志
        socket: {
          label: this.$t('pages.serverlog_logger_socket'),
          level: 'INFO',
          default: 'INFO',
          codes: ['log.level.socket_log', 'log.level.web_socket_log']
        },
        // 远程控制日志
        vnc: {
          label: this.$t('pages.serverlog_logger_vnc'),
          level: 'INFO',
          default: 'INFO',
          codes: ['log.level.vnc_log']
        },
        // 文件传输日志
        file: {
          label: this.$t('pages.serverlog_logger_file'),
          level: 'INFO',
          default: 'INFO',
          codes: ['log.level.com.tipray.dlp.ftp', 'log.level.download_log']
        },
        // 数据库日志
        sql: {
          label: this.$t('pages.serverlog_logger_sql'),
          level: 'WARN',
          default: 'WARN',
          codes: ['log.level.com.tipray.dlp.mybatis', 'log.level.shardingsphere-sql']
        },
        // 基础日志
        root: {
          label: this.$t('pages.serverlog_logger_base'),
          level: 'INFO',
          default: 'INFO',
          codes: ['log.level.com.tipray']
        },
        // 训练组件日志
        ccbg: {
          label: this.$t('pages.serverlog_logger_ccbg'),
          level: 'INFO',
          default: 'INFO',
          codes: ['log.level.ccbg']
        }
      },
      autoClose: 0,
      clear: {
        retainedDays: {
          label: 'pages.serverlog_clean_retainedDays',
          min: 2,
          max: 30,
          step: 1,
          value: 10,
          placement: 'right',
          tips: () => this.$t('pages.configDayRange', { min: this.clear.retainedDays.min, max: this.clear.retainedDays.max })
        },
        totalFileSize: {
          label: 'pages.serverlog_clean_totalFileSize',
          min: 10,
          max: 200,
          step: 10,
          value: 100,
          placement: 'bottom',
          tips: () => {
            const opts = { br: '<br/>', min: this.clear.totalFileSize.min, size: this.clear.totalFileSize.value }
            const tip = this.$t('pages.serverlog_clean_totalFileSizeDescription', opts)
            return `<div style="line-height: 24px;">${tip}</div>`
          }
        }
      },
      retainedDays: 10,
      totalFileSize: 100,
      sizeCountType: 1,
      autoCloseCfg: { code: 'log.debug.autoClose', value: '2' },
      retainedDaysCfg: { code: 'log.clear.retainedDays', value: '10' },
      totalFileSizeCfg: { code: 'log.clear.totalFileSize', value: '100' },
      sizeCountTypeCfg: { code: 'log.clear.sizeCountType', value: '1' },
      levelConfigs: []
    }
  },
  watch: {
    loggers: {
      deep: true,
      handler() {
        const loggers = Object.values(this.loggers)
        const checkedCount = loggers.filter(item => item.level.toLowerCase() === 'debug').length
        this.checkAll = checkedCount === loggers.length
        this.indeterminate = checkedCount > 0 && !this.checkAll
      }
    }
  },
  methods: {
    initLogger(logger) {
      for (let i = 0; i < this.levelConfigs.length; i++) {
        if (this.levelConfigs[i].code.toLowerCase() === logger.codes[0]) {
          logger.level = this.levelConfigs[i].value
          break
        }
      }
    },
    getConfig(cancelToken) {
      return getConsoleLogConfigs(cancelToken).then(respond => {
        this.levelConfigs = respond.data.levelConfigs
        this.autoCloseCfg = { ...this.autoCloseCfg, ...respond.data.autoClose }
        this.retainedDaysCfg = { ...this.retainedDaysCfg, ...respond.data.retainedDays }
        this.totalFileSizeCfg = { ...this.totalFileSizeCfg, ...respond.data.totalFileSize }
        this.sizeCountTypeCfg = { ...this.sizeCountTypeCfg, ...respond.data.sizeCountType }
        Object.values(this.loggers).forEach(item => {
          this.initLogger(item)
        })
        // this.autoClose = parseInt(this.autoCloseCfg.value)
        this.clear.retainedDays.value = parseInt(this.retainedDaysCfg.value)
        this.clear.totalFileSize.value = parseInt(this.totalFileSizeCfg.value)
        this.clear.totalFileSize.min = respond.data.maxFileSize
      })
    },
    saveConfig(cancelToken) {
      const configs = []
      Object.values(this.loggers).forEach(item => {
        item.codes.forEach(code => {
          configs.push({ code, value: item.level })
        })
      })
      configs.push({ ...this.autoCloseCfg, value: this.autoClose })
      configs.push({ ...this.retainedDaysCfg, value: this.clear.retainedDays.value })
      configs.push({ ...this.totalFileSizeCfg, value: this.clear.totalFileSize.value })
      configs.push({ ...this.sizeCountTypeCfg })
      return updateConsoleLogConfigs(configs, cancelToken)
    },
    handleCheckAllChange(checkedAll) {
      Object.values(this.loggers).forEach(item => {
        item.level = checkedAll ? 'DEBUG' : item.default
      })
      this.indeterminate = false
    },
    handleClear(data) {

    }
  }
}
</script>

<style lang="scss" scoped>
  .log-config-check-group>.log-config-item {
    display: inline-block;
    width: 160px;
  }
  div.log-config-remark {
    line-height: 24px;
    position: relative;
    bottom: 5px;
  }
</style>
