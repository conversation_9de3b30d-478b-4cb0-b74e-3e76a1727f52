<template>
  <div class="log-config">
    <el-divider content-position="left">{{ $t('pages.serverlog_config_output') }}</el-divider>
    <div v-if="isReport">
      <Form style="margin-left: 10px">
        <FormItem :label="$t('pages.serverlog_debug')">
          <el-switch v-model="level.debug.enable" :active-value="1" :inactive-value="0"></el-switch>
        </FormItem>
      </Form>
    </div>
    <div v-else>
      <div class="log-config-check-all">
        <label class="log-config-label">{{ $t('pages.serverlog_debug_open') }}</label>
        <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAllChange">
          {{ $t('button.selectAll') }}
        </el-checkbox>
      </div>
      <div class="log-config-check-group">
        <div v-for="(item, key) in level" :key="key" class="log-config-item">
          <el-checkbox v-model="item.enable" :true-label="1" :false-label="0">{{ item.label }}</el-checkbox>
          <!--<span v-show="item.enable" class="log-config-autoclose">
            <el-input-number
              v-model="item.autoClose"
              class="log-config-input"
              :min="0"
              :max="24"
              :controls="false"
              :step="1"
              step-strictly
            />
            <span class="log-config-label">{{ $t('pages.serverlog_debug_close1') }}</span>
            <span class="log-config-remark">
              <i class="el-icon-info"></i>
              0 {{ $t('pages.serverlog_debug_close2') }}
            </span>
          </span>-->
        </div>
      </div>
    </div>
    <el-divider content-position="left">{{ $t('pages.serverlog_config_cleanup') }}</el-divider>
    <div v-for="(item, key) in clear" :key="key" class="log-config-item">
      <i18n :path="item.label">
        <el-input-number
          slot="num"
          v-model="item.value"
          class="log-config-input"
          :min="item.min"
          :max="item.max"
          :controls="false"
          :step="1"
          step-strictly
        />
        <template slot="space" >&nbsp;</template>
      </i18n>
      <!--<span class="log-config-remark">
        <i class="el-icon-info"></i>
        {{ item.tips }}
      </span>-->
      <el-tooltip effect="dark" placement="right">
        <div slot="content">{{ item.tips() }}</div>
        <i class="el-icon-info"/>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import { getServerLogConfig, setServerLogConfig, ERR_STATUS } from '@/api/system/deviceManage/serverLog'

export default {
  name: 'DeviceLogConfig',
  props: {
    devId: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      checkAll: false,
      indeterminate: false,
      level: {
        debug: {
          label: this.$t('pages.serverlog_logger_base'),
          enable: 0,
          autoClose: 0
        },
        trcomm: {
          label: this.$t('pages.serverlog_logger_socket'),
          enable: 0,
          autoClose: 0
        }
      },
      // remark: {
      //   debug: '指明细致的事件信息，对调试程序最有用',
      //   info: '指明描述信息，从粗粒度上描述了程序运行过程',
      //   warn: '指明可能潜在的危险状况',
      //   error: '指明错误事件，但程序可能还能继续运行',
      //   fatal: '指明严重错误，程序可能无法继续运行'
      // },
      clear: {
        retainedDays: {
          label: 'pages.serverlog_clean_retainedDays',
          min: 2,
          max: 30,
          value: 7,
          tips: () => this.$t('pages.configDayRange', { min: this.clear.retainedDays.min, max: this.clear.retainedDays.max })
        },
        logFileSize: {
          label: 'pages.serverlog_clean_totalFileSize',
          min: 10,
          max: 200,
          value: 100,
          tips: () => this.$t('pages.serverlog_clean_totalFileSizeDescription2')
        }
      }
    }
  },
  computed: {
    isReport() {
      return this.devId === 202
    }
  },
  watch: {
    level: {
      deep: true,
      handler() {
        const level = Object.values(this.level)
        const checkedCount = level.filter(item => item.enable === 1).length
        this.checkAll = checkedCount === level.length
        this.indeterminate = checkedCount > 0 && !this.checkAll
      }
    }
  },
  methods: {
    resetConfig() {
      this.level.debug.enable = 0
      this.level.debug.autoClose = 0
      this.level.trcomm.enable = 0
      this.level.trcomm.autoClose = 0
      this.clear.retainedDays.value = 7
      this.clear.logFileSize.value = 100
    },
    getConfig(cancelToken) {
      this.resetConfig()
      return getServerLogConfig(this.devId, cancelToken).then(respond => {
        const data = respond.data
        if (data.endFlag === 1) {
          this.level.debug.enable = data.debugEnable
          this.level.debug.autoClose = data.autoCloseDebug
          this.level.trcomm.enable = data.trcommEnable
          this.level.trcomm.autoClose = data.autoCloseTrcomm
          this.clear.retainedDays.value = data.retainedDays
          this.clear.logFileSize.value = data.logFileSize
          return data
        }
        const reason = ERR_STATUS[data.endFlag]
        this.$notify({
          title: this.$t('text.error'),
          message: reason,
          type: 'error',
          duration: 2000
        })
        return Promise.reject(reason)
      })
    },
    saveConfig(cancelToken) {
      const data = {
        debugEnable: this.level.debug.enable,
        autoCloseDebug: this.level.debug.autoClose,
        trcommEnable: this.level.trcomm.enable,
        autoCloseTrcomm: this.level.trcomm.autoClose,
        retainedDays: this.clear.retainedDays.value,
        logFileSize: this.clear.logFileSize.value
      };
      return setServerLogConfig(this.devId, data, cancelToken)
    },
    handleCheckAllChange(checkedAll) {
      const enable = Number(checkedAll)
      Object.values(this.level).forEach(item => {
        item.enable = enable
      })
      this.indeterminate = false
    }
  }
}
</script>
