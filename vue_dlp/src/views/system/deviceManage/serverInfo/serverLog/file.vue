<template>
  <download-executor
    v-if="isEnableDownloadManager()"
    float="right"
    :disabled="disabled"
    :show-zip-mode="showed"
    button-type="primary"
    button-size="mini"
    :need-check-address="devId > 0"
    @download="handleDownload"
  />
  <file-downloader
    v-else
    :disabled="disabled"
    :show-zip-mode="showed"
    :progress-title="$t('pages.serverlog_download_progress')"
    :progress-visible.sync="progressVisible"
    :progress-active="downloadFile.active"
    :progress-error="downloadFile.error"
    :progress-percent="downloadFile.percent"
    :progress-steps="progressSteps"
    :need-check-address="devId > 0"
    show-address-dialog
    @download="handleDownload"
    @cancel-task="handleCancelTask"
  />
</template>

<script>
import DownloadExecutor from '@/components/DownloadManager/executor'
import FileDownloader from '@/components/FileManager/downloader'
import axios from 'axios'
import {
  listFiles,
  extractFile,
  notifyUpload,
  cancelDownload,
  download,
  ERR_STATUS
} from '@/api/system/deviceManage/serverLog'
import { downloadZeroByteFile } from '@/utils/download/index'
import { formatFileSize } from '@/utils'
import { buildDownloadFile, DEFAULT_DOWNLOAD_FILE } from '@/utils/download/helper'

export default {
  name: 'ServerFile',
  components: { DownloadExecutor, FileDownloader },
  props: {
    devId: {
      type: Number,
      default: undefined
    },
    selection: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      mode: 0,
      downloading: false,
      root: '$DEVSERVER_HOME',
      subscribeUrl: undefined,
      currentServer: undefined,
      cancelTokenSource: undefined,
      filePathMd5: undefined,
      guid: undefined,
      timer: undefined,
      downloadFile: { ...DEFAULT_DOWNLOAD_FILE },
      progressVisible: false,
      progressSteps: [],
      defaultSteps: `[
        { "title": "${this.$t('pages.serverlog_download_progress1')}" },
        { "title": "${this.$t('pages.serverlog_download_progress2')}" },
        { "title": "${this.$t('pages.serverlog_download_progress3')}" }
      ]`
    }
  },
  computed: {
    disabled() {
      return !this.selection || this.selection.length === 0
    },
    showed() {
      if (this.disabled || this.selection.length > 1) {
        return false
      }
      return this.selection[0].fileType === 2
    },
    timeout() {
      if (this.downloadFile.percent < 20) {
        return 500
      }
      if (this.downloadFile.percent < 35) {
        return 1000
      }
      if (this.downloadFile.percent < 50) {
        return 2000
      }
      if (this.downloadFile.percent < 65) {
        return 3000
      }
      if (this.downloadFile.percent < 80) {
        return 4000
      }
      return 5000
    }
  },
  methods: {
    handleDownload(zipped) {
      this.mode = zipped ? 1 : 0
      this.extractFile()
    },
    getFileList(data) {
      this.currentServer = data
      this.resetCancelToken()
      return listFiles(data, this.cancelTokenSource.token).then(respond => {
        if (respond.data.endFlag > 1) {
          return Promise.reject(ERR_STATUS[respond.data.endFlag])
        }
        this.cancelTokenSource = undefined
        return respond.data
      }).catch(reason => {
        this.cancelTokenSource = undefined
        return Promise.reject(reason)
      })
    },
    extractFile() {
      this.currentServer.mode = this.showed ? this.mode : 1
      this.currentServer.selection = this.selection.map(item => ({ fileName: item.fileName, fileType: item.fileType }))
      this.resetCancelToken()
      this.progressSteps = JSON.parse(this.defaultSteps)
      this.progressVisible = true
      this.showVirtualPercent()
      const server = this.currentServer.paths[0]
      this.downloadFile = buildDownloadFile(this.selection, this.currentServer.mode > 0, server)
      this.downloadFile.steps = 3
      this.downloadFile.abort = this.handleCancelTask
      extractFile(this.currentServer, this.cancelTokenSource.token).then(respond => {
        if (this.downloadFile.canceled) {
          return
        }
        this.stopVirtualPercent()
        this.cancelTokenSource = undefined
        const extractData = respond.data
        if (this.selection.length > 1) {
          extractData.fileName = this.downloadFile.name
        }
        const flag = extractData.endFlag
        if (flag === 100) { // 就绪
          this.downloadFile.active = 2
          this.progressSteps[2].title += '(' + formatFileSize(extractData.fileSize) + ')'
          if (extractData.fileSize === 0) {
            this.downloadFile.percent = 100
            downloadZeroByteFile(extractData.fileName)
          } else {
            this.downloadFile.percent = 0
            this.downloadRequest(extractData)
          }
          this.progressVisible = false
          return
        }
        if (flag === 1) { // 等待上传
          this.downloadFile.active = 1
          this.progressSteps[1].title += '(' + formatFileSize(extractData.fileSize) + ')'
          if (extractData.fileSize === 0) {
            this.downloadFile.active = 2
            this.downloadFile.percent = 100
            downloadZeroByteFile(extractData.fileName)
            this.progressVisible = false
            return
          }
          this.downloadFile.percent = 0
          this.filePathMd5 = extractData.filePathMd5
          this.guid = extractData.guid
          this.notifyUpload(extractData)
          return
        }
        // 提取错误
        this.downloadFile.error = ERR_STATUS[flag]
      }).catch(() => {
        this.cancelTokenSource = undefined
      })
    },
    notifyUpload(extractData) {
      if (this.subscribeUrl) {
        this.$socket.unsubscribe(this.subscribeUrl)
      }
      this.subscribeUrl = '/topic/getServerUploadProgress/' + extractData.guid
      // 订阅上传进度
      this.$socket.subscribe({
        url: this.subscribeUrl,
        callback: (resp, handle) => {
          if (this.downloadFile.canceled) {
            handle.close()
            this.subscribeUrl = undefined
            return
          }
          const status = resp.data
          if (status < 100) {
            this.downloadFile.percent = Math.max(status, this.downloadFile.percent)
            return;
          }
          handle.close()
          this.subscribeUrl = undefined
          this.downloadFile.percent = 0
          if (status === 100) {
            this.downloadRequest(extractData)
            this.progressVisible = false
          } else {
            this.downloadFile.error = ERR_STATUS[status]
          }
        }
      })
      // 通知上传
      notifyUpload(extractData).then(res => {
        const flag = res.data.endFlag
        if (flag === 100) { // 就绪
          this.downloadFile.canceled = !this.isEnableDownloadManager()
          this.downloadRequest(extractData)
          this.progressVisible = false
          this.$socket.unsubscribe(this.subscribeUrl)
          this.subscribeUrl = undefined
          return
        }
        if (flag !== 1) {
          this.downloadFile.error = ERR_STATUS[flag]
        }
      })
    },
    downloadRequest(extractData) {
      this.downloadFile.active = 2
      download(extractData, this.downloadFile, this.devId)
    },
    handleCancelTask() {
      this.downloadFile.canceled = true
      if (this.cancelTokenSource) {
        this.cancelTokenSource.cancel()
        this.cancelTokenSource = undefined
      }
      if (this.downloadFile.active === 0) {
        this.stopVirtualPercent()
        return
      }
      if (this.downloadFile.active === 1) {
        cancelDownload(this.filePathMd5, this.guid)
        if (this.subscribeUrl) {
          this.$socket.unsubscribe(this.subscribeUrl)
          this.subscribeUrl = undefined
        }
      }
    },
    resetCancelToken() {
      if (this.cancelTokenSource) {
        this.cancelTokenSource.cancel()
      }
      this.cancelTokenSource = axios.CancelToken.source()
    },
    showVirtualPercent() {
      this.stopVirtualPercent()
      this.timer = setTimeout(() => {
        if (this.downloadFile.percent++ < 100) {
          this.showVirtualPercent()
        }
      }, this.timeout)
    },
    stopVirtualPercent() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = undefined
      }
    }
  }
}
</script>
