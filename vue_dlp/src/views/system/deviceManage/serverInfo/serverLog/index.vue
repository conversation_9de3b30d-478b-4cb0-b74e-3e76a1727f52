<template>
  <file-manager size-key="fileSize" type-key="fileType" :selection="selection" @refresh="getFileList">
    <server-tree slot="tree" ref="tree" @click="handleTreeClick"/>
    <template slot="toolbar">
      <el-button class="fl ml10" icon="el-icon-s-tools" size="mini" :disabled="cfgDisabled" @click="openConfig">
        {{ $t('button.highConfig') }}
      </el-button>
      <file-search
        class="fr ml10"
        :tips="$t('pages.serverlog_search_tips')"
        :disabled="tableLoading"
        @search="handleSearch"
      />
      <server-file ref="file" :dev-id="devId" :selection="selection"/>
      <el-breadcrumb v-show="paths.length > 0" separator="/" class="breadcrumb">
        <el-breadcrumb-item v-for="(item, index) in paths" :key="index">
          <a href="javascript:void(0);" :title="item" @click="handlePathsClick(index)">{{ item }}</a>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </template>
    <grid-table
      ref="table"
      v-loading="tableLoading"
      row-key="fileName"
      multi-select
      :show-pager="false"
      :col-model="colModel"
      :row-datas="tableData"
      :default-sort="{ prop: 'fileType', order: 'desc' }"
      @row-dblclick="handleRowDblClick"
      @selectionChangeEnd="handleFilesSelected"
    />
    <server-log-config ref="logConfig"/>
  </file-manager>
</template>

<script>
import FileManager from '@/components/FileManager'
import FileSearch from '@/components/FileManager/search'
import ServerFile from './file'
import ServerTree from './tree'
import ServerLogConfig from './config'
import { formatFileType, getFileIcon } from '@/icons/extension'
import { getServerOnline } from '@/api/system/deviceManage/serverLog'
import { timestampFormatter } from '@/api/dataEncryption/fileTrace/documentTrack'

export default {
  name: 'ServerLogs',
  components: { FileManager, FileSearch, ServerFile, ServerTree, ServerLogConfig },
  data() {
    return {
      tableLoading: false,
      colModel: [
        { prop: 'fileName', label: 'name', width: '200', sort: true, iconFormatter: this.iconClassFormatter },
        { prop: 'fileType', label: 'fileType', width: '150', sort: true, sortOriginal: true, formatter: this.fileTypeFormatter },
        { prop: 'fileSize', label: 'size1', width: '150', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'modifyTime', label: 'updateTime', width: '150', sort: true, formatter: timestampFormatter }
        // { prop: 'createTime', label: 'createTime', width: '150', sort: true, formatter: timestampFormatter }
      ],
      paths: [],
      treeNode: undefined,
      tableData: [],
      tableDataCache: undefined,
      selection: [],
      fileFormat: 0,
      isShow: true
    }
  },
  computed: {
    devId() {
      return this.treeNode && this.treeNode.devId
    },
    devName() {
      return this.treeNode && this.treeNode.label
    },
    cfgDisabled() {
      return this.devId == null || this.tableLoading
    }
  },
  created() {
    this.$nextTick(() => {
      if (this.treeNode == null) {
        this.$refs['tree'].getCurrentNode().then(this.initData)
      }
    })
  },
  methods: {
    openConfig() {
      this.isOnline().then(online => {
        if (online) {
          // this.$emit('openConfig', this.devId, this.devName)
          this.$refs['logConfig'].openConfig(this.devId, this.devName)
        }
      })
    },
    initData(nodeData) {
      if (nodeData) {
        this.treeNode = nodeData
        this.paths = [nodeData.label]
        nodeData.loading = true
        this.getFileList().then(() => {
          nodeData.loading = false
        })
      } else {
        this.paths = []
      }
    },
    buildListFileReqData() {
      return {
        id: this.treeNode.id,
        devType: this.treeNode.type,
        devId: this.treeNode.devId,
        paths: this.paths,
        fileFormat: this.fileFormat
      }
    },
    calcFileSuffix(fileInfo) {
      const arr = fileInfo.fileName.split('.')
      if (arr.length > 1) {
        fileInfo.suffix = arr.pop().toLowerCase()
      }
      return fileInfo
    },
    isOnline() {
      return getServerOnline(this.treeNode.devId, this.treeNode.type).then(online => {
        if (!online) {
          this.$message({
            message: this.$t('pages.serverlog_offline'),
            type: 'info',
            duration: 2000
          })
        }
        return online
      })
    },
    getFileList() {
      this.tableLoading = true
      this.tableData = []
      this.tableDataCache = undefined
      return this.isOnline().then(online => {
        if (!online) {
          this.tableLoading = false
          return
        }
        const reqData = this.buildListFileReqData()
        return this.$refs['file'].getFileList(reqData).then(fileData => {
          let fileRecords = fileData.fileRecords || []
          if (reqData.devId > 0 && reqData.paths.length <= 1 && reqData.devType !== 187) {
            fileRecords = fileRecords.filter(item => item.fileType === 1 && (item.fileName === 'log' || item.fileName === 'dmp' || item.fileName === 'dump'))
          }
          this.tableLoading = false
          this.fileFormat = fileData.fileFormat
          this.tableData = fileRecords.map(this.calcFileSuffix)
        }).catch(reason => {
          this.tableLoading = false
          if (reason.message) {
            return
          }
          this.$notify({
            title: this.$t('text.error'),
            message: reason,
            type: 'error',
            duration: 2000
          })
        })
      })
    },
    handleTreeClick(data, node, el, tree) {
      this.treeNode = data
      this.paths = [data.label]
      this.$refs['tree'].clearLoading()
      data.loading = true
      this.getFileList().then(() => {
        data.loading = false
      })
    },
    handlePathsClick(index) {
      if (index === this.paths.length - 1) {
        return
      }
      this.paths.splice(index + 1)
      this.getFileList()
    },
    handleRowDblClick(row, column, event) {
      if (row.fileType === 2) {
        return
      }
      this.paths.push(row.fileName)
      this.getFileList()
    },
    handleFilesSelected(selection) {
      this.selection = selection || []
    },
    iconClassFormatter(row, data) {
      if (row.fileType === 1) { // 1: 文件夹
        return [{ class: 'dir1', title: this.$t('pages.folder') }]
      }
      // 2: 文件
      return [{ class: getFileIcon(row.suffix), title: row.suffix }]
    },
    fileTypeFormatter(row, data) {
      if (1 === data) {
        return this.$t('pages.serverlog_type_folder')
      }
      return formatFileType(row.fileName)
    },
    fileSizeFormatter(row, data) {
      if (row.fileType === 1) {
        return ''
      }
      const sizeArr = Math.ceil(data / 1024).toString().split('')
      let str = ''
      for (let i = sizeArr.length - 1, j = 1; i >= 0; i--) {
        str = sizeArr[i] + str
        if (i > 0 && (j % 3) === 0) {
          str = ',' + str
        }
        j++
      }
      return str + ' KB'
    },
    handleSearch(data) {
      if (!this.tableDataCache) {
        this.tableDataCache = this.tableData
      }
      this.tableData = this.tableDataCache.filter(item => {
        if (data.name && item.fileName.toLowerCase().indexOf(data.name.toLowerCase()) < 0) {
          return false
        }
        if (data.modified && (item.modifyTime < data.modified[0] || item.modifyTime > data.modified[1])) {
          return false
        }
        if (data.creation && (item.createTime < data.creation[0] || item.createTime > data.creation[1])) {
          return false
        }
        return true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .fl {
    float: left;
  }
  .fr {
    float: right;
  }
  .ml10 {
    margin-left: 10px;
  }
  .breadcrumb {
    height: 29px;
    line-height: 28px;
    padding-left: 10px;
  }
</style>
