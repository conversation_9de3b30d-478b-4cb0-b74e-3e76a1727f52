<template>
  <div>
    <div>
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="textMap[dialogStatus]"
        :visible.sync="dialogFormVisible"
        width="700px"
      >
        <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="130px" style="width: 650px;">
          <el-divider content-position="left">{{ $t('pages.dbUserInfo') }}</el-divider>
          <el-row>
            <el-col :span="11">
              <FormItem :label="$t('pages.userName1')" label-width="80px" prop="username">
                <el-input v-model="temp.username" :maxlength="30" show-word-limit clearable></el-input>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem :label="$t('pages.pwdInfo')" prop="password">
                <el-input v-model="showPassword" type="password" show-word-limit clearable @change="passwordChange"></el-input>
              </FormItem>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <FormItem :label="$t('pages.dataSource')" label-width="80px" prop="dbServerId">
                <el-select v-if="dialogStatus === 'create'" v-model="temp.dbServerId" :placeholder="$t('text.select')">
                  <el-option v-for="item in onlineDbServers" :key="item.devId" :label="item.name" :value="item.devId"></el-option>
                </el-select>
                <el-select v-else v-model="temp.dbServerId" :placeholder="$t('text.select')" disabled>
                  <el-option v-for="item in dbServers" :key="item.devId" :label="item.name" :value="item.devId"></el-option>
                </el-select>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem :label="$t('pages.allowInterviewIpAddress')" prop="hostname" class="input-left">
                <el-input v-model="temp.hostname" :disabled="dialogStatus !== 'create'" :placeholder="$t('pages.allowIpAddressPlaceholder')" :maxlength="50"></el-input>
              </FormItem>
            </el-col>
          </el-row>
          <el-divider content-position="left">{{ $t('pages.grantPermission') }}</el-divider>
          <div style="padding: 0 25px">
            <el-row>
              <el-col :span="24">
                <div style="display: flex; align-items: center">
                  <el-checkbox v-model="temp.isStgDbPermission" @change="stgChange">{{ $t('pages.dbUserStgPermission') }}</el-checkbox>
                  <FormItem label-width="0" prop="stgDbPermissions">
                    <el-select v-model="temp.stgDbPermissions" :disabled="!temp.isStgDbPermission" style="width: 350px" multiple clearable>
                      <el-option v-for="item in permissionTypes" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</el-option>
                    </el-select>
                  </FormItem>
                </div>
              </el-col>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :span="24">
                <FormItem label-width="0">
                  <div style="display: flex; align-items: center">
                    <el-checkbox v-model="temp.isLogDbPermission" @change="logChange">{{ $t('pages.dbUserLogPermission') }}</el-checkbox>
                    <FormItem label-width="0" prop="logDbPermissions">
                      <el-select v-model="temp.logDbPermissions" :disabled="!temp.isLogDbPermission" style="width: 350px" multiple clearable>
                        <el-option v-for="item in permissionTypes" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</el-option>
                      </el-select>
                    </FormItem>
                  </div>
                </FormItem>
              </el-col>
            </el-row>
          </div>

        </Form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
            {{ $t('button.confirm') }}
          </el-button>
          <el-button @click="cancel">
            {{ $t('button.cancel') }}
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { createDbUser, getDatabaseDevs, updateDbUser } from '@/api/system/deviceManage/dbUser';

export default {
  name: 'EditDbUser',
  components: {},
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        username: '',
        dbServerId: null,
        password: '',
        hostname: '',
        isStgDbPermission: false,   //  是否授予策略库权限
        isLogDbPermission: false,   //  是否授予日志权限
        stgDbPermissions: [],       //  策略库权限
        logDbPermissions: [],       //  日志库权限
        oldUsername: '',
        oldPassword: ''
      },
      //  显示的密码
      showPassword: '',
      //  总部连接的数据库
      dbServers: [],
      //  在线的总部连接的数据库
      onlineDbServers: [],
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.$t('pages.updateDbUser'),
        create: this.$t('pages.createDbUser')
      },
      rules: {
        username: [{ required: true, validator: this.usernameValid, trigger: 'blur' }],
        dbServerId: [{ required: true, validator: this.dbServerIdValid, trigger: 'blur' }],
        password: [{ required: true, validator: this.passwordValid, trigger: 'blur' }],
        hostname: [{ required: true, validator: this.hostnameValid, trigger: 'blur' }],
        stgDbPermissions: [{ required: true, validator: this.stgDbPermissionsValid, trigger: 'change' }],
        logDbPermissions: [{ required: true, validator: this.logDbPermissionsValid, trigger: 'change' }]
      },
      //  允许分配的权限,
      //  注意：ALL PRIVILEGES/ALL 表示拥有数据库所有权限(不包含GRANT权限，即授权权限）
      permissionTypes: [
        { value: 'Select', label: 'Select' },
        { value: 'Insert', label: 'Insert' },
        { value: 'Update', label: 'Update' },
        { value: 'Delete', label: 'Delete' }
      ]
    }
  },
  computed: {
    groupTree() {
      return this.$refs['groupTree']
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    resetTemp() {
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
      this.showPassword = ''
    },
    handleDrag() {
    },
    //  加载数据源
    loadDbServer() {
      getDatabaseDevs().then(res => {
        this.dbServers = res.data.totalServers || []
        this.onlineDbServers = res.data.onlineDbServers || []
        if (this.dialogStatus === 'create' && this.onlineDbServers.length > 0) {
          this.temp.dbServerId = this.onlineDbServers[0].devId
        }
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.loadDbServer()
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.loadDbServer()
      this.temp = Object.assign(this.temp, JSON.parse(JSON.stringify(row)))
      this.temp.oldUsername = this.temp.username
      this.temp.oldPassword = this.temp.password
      //  假密码，仅用于显示，无其它意义
      this.showPassword = '*****'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    //  格式化数据
    formatRowData(temp) {
      return JSON.parse(JSON.stringify(temp));
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const rowTemp = this.formatRowData(this.temp);
          createDbUser(rowTemp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = this.formatRowData(this.temp);
          tempData.errorType = 1
          updateDbUser(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    cancel() {
      this.dialogFormVisible = false
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    passwordChange(value) {
      this.temp.password = value
    },
    stgChange(value) {
      if (!value) {
        this.temp.stgDbPermissions.splice(0)
        this.$refs['dataForm'].validateField('stgDbPermissions')
      }
    },
    logChange(value) {
      if (!value) {
        this.temp.logDbPermissions.splice(0)
        this.$refs['dataForm'].validateField('logDbPermissions')
      }
    },
    //  校验规则
    //  校验用户名
    usernameValid(rule, value, callback) {
      //  只能输入字母数字下划线,且长度由4-16位字符组成！
      if (value.length === 0) {
        callback(new Error(this.$t('pages.dbUsernameNotNull') + ''));
      }
      if (!this.validCheckAlphanumericUnderscores(value)) {
        callback(new Error(this.$t('pages.dbUsernameValidMsg1') + ''));
      }
      callback();
    },
    //  校验IP地址
    hostnameValid(rule, value, callback) {
      if (value === '%') {
        callback();
      }
      //  支持IPv4，Ipv6
      const flag = this.validIpv4(value) || this.validIpv6(value);
      if (!flag) {
        callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules') + ''))
        return;
      }
      callback();
    },
    stgDbPermissionsValid(rule, value, callback) {
      if (this.temp.isStgDbPermission && (!value || value.length === 0)) {
        callback(new Error(this.$t('pages.dbUserPermissionNotNull') + ''))
      }
      callback();
    },
    logDbPermissionsValid(rule, value, callback) {
      if (this.temp.isLogDbPermission && (!value || value.length === 0)) {
        callback(new Error(this.$t('pages.dbUserPermissionNotNull') + ''))
      }
      callback();
    },
    dbServerIdValid(rule, value, callback) {
      if (!this.temp.dbServerId) {
        callback(new Error(this.$t('pages.dbUserDataSourceNotNull') + ''))
      }
      callback();
    },
    //  密码校验
    passwordValid(rule, value, callback) {
      if (this.showPassword.length === 0) {
        callback(new Error(this.$t('pages.validateMsg_password') + ''))
      } else if (this.showPassword.length > 50) {
        callback(new Error(this.$t('pages.passwordOutMaxLength', { size: 50 }) + ''))
      }
      callback();
    },
    //  只能输入字母数字下划线,且长度由4-16位字符组成！
    validCheckAlphanumericUnderscores(value) {
      const reg = /^[a-zA-Z0-9_]{1,16}$/
      return reg.test(value);
    },
    //  校验ipv4
    validIpv4(ip) {
      const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
      return reg.test(ip);
    },
    //  校验ipv6
    validIpv6(ip) {
      const reg = /^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;
      return reg.test(ip);
    }
  }
}
</script>
<style scoped lang='scss'>
>>> .input-left .el-input-number .el-input__inner {
  text-align: left;
}
</style>
