<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createDbUser') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteDbUser') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.username" clearable :placeholder="$t('pages.userName1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="userList"
        node-key="id"
        :col-model="colModel"
        :row-datas="rowDatas"
        :default-sort="defaultSort"
        :show-pager="false"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <edit-db-user ref="editDbUser" @submitEnd="handleFilter"></edit-db-user>
    </div>
  </div>
</template>

<script>
import { deleteDbUser, getById, listDb } from '@/api/system/deviceManage/dbUser'
import editDbUser from '@/views/system/deviceManage/dbServer/editDbUser'

export default {
  components: { editDbUser },
  data() {
    return {
      colModel: [
        { prop: 'username', label: 'username', width: '150', fixed: true },
        { prop: 'dbServerId', label: 'dataSource1', width: '100' },
        { prop: 'hostname', label: 'allowInterViewIpAddress', width: '80' },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      defaultSort: {
        prop: 'id',
        order: 'desc'
      },
      rowDatas: [],
      query: { // 查询条件
        page: 1,
        username: '',
        hostname: ''
      },
      deleteable: false,
      cloneable: false,
      dbTypeOptions: [
        { id: 1, label: 'MySQL', defaultPort: 23306 },
        { id: 2, label: 'SqlServer', defaultPort: 1433 },
        { id: 3, label: 'Oracle', defaultPort: 1521 },
        { id: 4, label: 'DM', defaultPort: 5236 },
        { id: 5, label: 'KingbaseES', defaultPort: 54321 }
      ],
      dbRuleOptions: [
        { value: 0, label: this.$t('pages.warehouseByDay') },
        { value: 1, label: this.$t('pages.monthlyDistribution') },
        { value: 2, label: this.$t('pages.byQuarter') },
        { value: 3, label: this.$t('pages.byYear') }
      ],
      accessOptions: [
        { value: 0, label: this.$t('pages.intranetAccess') },
        { value: 1, label: this.$t('pages.internetAccess') }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['userList']
    }
  },
  created() {
    this.handleFilter()
  },
  activated() {
    this.listDbUser()
  },
  methods: {
    init() {
      this.handleFilter()
    },
    listDbUser() {
      listDb(this.query).then(res => {
        res.data.forEach(item => {
          item.id = item.dbServerId + '$' + item.username + '$' + item.hostname + '$'
        })
        this.rowDatas = res.data || []
      })
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
      this.cloneable = rowDatas.length == 1 && rowDatas[0].access
    },
    handleFilter() {
      this.listDbUser()
    },
    handleCreate() {
      this.$refs.editDbUser.handleCreate()
    },
    handleUpdate(row) {
      getById(row.dbServerId, row.username, row.hostname).then(res => {
        if (!res.data) {
          this.$message({
            type: 'error',
            message: this.$t('pages.currentUserNotExitsRefresh'),
            duration: 2000
          })
          return;
        }
        this.$refs.editDbUser.handleUpdate(res.data)
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg')).then(() => {
        const toDeleteDatas = this.gridTable.getSelectedDatas();
        const list = [];
        (toDeleteDatas || []).forEach(item => {
          list.push({ dbServerId: item.dbServerId, username: item.username, hostname: item.hostname });
        })
        deleteDbUser(list).then(respond => {
          this.handleFilter();
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.el-input-number.is-without-controls .el-input__inner{
  text-align: left;
}
</style>
