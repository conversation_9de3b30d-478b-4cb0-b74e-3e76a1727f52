<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.updateDbServerPassword')"
    :visible.sync="dialogFormVisible"
    class="show-detail-panel"
    width="450px"
  >
    <Form ref="dataForm" label-position="right" :rules="rules" :model="temp" label-width="100px">
      <FormItem :label="$t('pages.currentDbPwdVersion') + '：'" label-width="150px">
        {{ dbServerPasswordVer }}
      </FormItem>
      <FormItem :label="$t('pages.databasePassword')" prop="password">
        <el-input v-model="temp.password" type="password" show-password :maxlength="48"/>
      </FormItem>
      <FormItem :label="$t('pages.confirmPassword')" prop="rePassword">
        <el-input v-model="temp.rePassword" type="password" show-password :maxlength="48"/>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="confirm">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="close">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { getMaxDbPassVer, updateDbServerPassword } from '@/api/system/deviceManage/dbServer';
import { aesEncode, formatAesKey } from '@/utils/encrypt';

export default {
  name: 'UpdatePasswordDlg',
  components: {},
  props: {
  },
  data() {
    return {
      dialogFormVisible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        password: '',
        rePassword: ''
      },
      rules: {
        password: [{ required: true, validator: this.passwordValidator, trigger: 'blur' }],
        rePassword: [{ required: true, validator: this.rePasswordValidator, trigger: 'blur' }]
      },
      dbServerPasswordVer: null  //  数据库密码版本
    }
  },
  created() {
  },
  methods: {
    initData() {
      this.submitting = false;
      this.dbServerPasswordVer = null
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    getMaxDbPassVer() {
      getMaxDbPassVer().then(res => {
        this.dbServerPasswordVer = res.data
      })
    },
    show() {
      this.initData();
      this.resetTemp()
      this.getMaxDbPassVer();
      this.dialogFormVisible = true
    },
    passwordValidator(rule, value, callback) {
      //  密码必须有且仅有数字，字母、符号（如!@#$%^&*()_+=|:<>,.?{}[]/），且长度在8-48之间  !@#$%^&*()_+=|:<>,.?{}[]/  排除;分号和\反斜杠,分号和反斜杠会出现问题
      const pwd = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+=|:<>,.?{}\[\]/])[a-zA-Z\d!@#$%^&*()_+=|:<>,.?{}\[\]/]{8,48}$/;
      if (!value) {
        callback(new Error(this.$t('pages.passwordNotNull') + ''))
      } else if (!pwd.test(value)) {
        callback(new Error(this.$t('pages.passwordComplexityTip') + ''))
      }
      callback()
    },
    rePasswordValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.passwordNotNull') + ''))
      } else if (this.temp.password && this.temp.rePassword && this.temp.password !== this.temp.rePassword) {
        callback(new Error(this.$t('pages.repeatPasswordTip') + ''))
      }
      callback()
    },
    confirm() {
      this.$refs.dataForm.validate((valid, error) => {
        if (valid) {
          this.submitting = true;
          //  加密
          const password = aesEncode(this.temp.password, formatAesKey('tr838408', ''))
          if (password) {
            updateDbServerPassword({ password: password }).then(res => {
              this.submitting = false
              if (!res.data.result) {
                this.$message({
                  message: this.$t('pages.dbPasswordUpdateFail'),
                  type: 'error',
                  duration: 2000
                })
              } else {
                this.$message({
                  message: this.$t('pages.dbPasswordUpdateSuccess'),
                  type: 'success',
                  duration: 2000
                })
                this.$emit('submit')
                this.close()
              }
            }).catch(() => {
              this.submitting = false
            })
          }
        }
      })
    },
    close() {
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
      this.dialogFormVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.list-panel {
  width: 165px;
  height: 460px;
  position: relative;
  float: left;
  overflow-y: auto;
  border-right: 1px solid #e6e6e6;
  box-sizing: content-box;
  border-right: 1px dashed #cccccc;
}
.list-item {
  border-bottom: 1px dashed #ccc;
  cursor: pointer;
  padding: 5px 0 5px 5px;
  position: relative;
}
.list-item-summary {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  margin-top: 3px;
  font-size: 12px;
}
.detail-panel {
  padding-right: 10px;
  margin-left: 182px;
  height: 460px;
  overflow-y: auto;
  >>>.doc-info-item {
    font-size: 14px;
  }
  >>>.doc-info-item__label {
    width: 95px;
  }
  >>>.doc-info-item__content {
    width: calc(100% - 105px);
  }
  >>>.base-item-content{
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.show-detail-panel {
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
