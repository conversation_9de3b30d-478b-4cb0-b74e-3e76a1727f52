<template>
  <div>
    <Drawer
      :title="$t('table.detail')"
      :visible.sync="detailVisible"
      status="update"
      :with-footer="false"
      :size="'60%'"
      wrapper-closable
      append-to-body
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
    >
      <div class="app-container show-detail-panel" >
        <div style="flex: 1">
          <el-descriptions class="margin-top" :title="$t('pages.basicInformation')" :column="3" size="small" border>
            <el-descriptions-item :label="$t('pages.serverName')">
              {{ temp.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.deviceNum')">
              {{ temp.devId }}
            </el-descriptions-item>
            <!--            &lt;!&ndash; 数据库服务器尚不支持设置授权状态 &ndash;&gt;-->
            <!--            <el-descriptions-item :label="$t('pages.grantAuthor')">-->
            <!--              {{ statusTableFilter(temp.adminAuthorized) }}-->
            <!--            </el-descriptions-item>-->
            <el-descriptions-item :label="$t('pages.accessMode')">
              {{ accessOptions[temp.access] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.DBType')">
              {{ dbTypeOptions[temp.dbType] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetType')">
              <div slot="label">
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.intranetTypeTip1') }}<br>
                    {{ $t('pages.intranetTypeTip2') }}<br>
                    {{ $t('pages.intranetTypeTip3') }}<br>
                    {{ $t('pages.intranetTypeTip4') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.intranetType') }}
              </div>
              {{ intranetTypeOptions[temp.intranetType] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.installMode')">
              {{ temp.dbMethod ? $t('pages.dbServerInstallType1') : $t('pages.dbServerInstallType2') }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.ipAddr')">
              {{ temp.ip }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.port')">
              {{ temp.port }}
            </el-descriptions-item>
            <!--            服务器状态-->
            <el-descriptions-item :label="$t('pages.versionNumber')">
              {{ temp.version }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              {{ temp.onlineStatus }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.remark')">
              {{ temp.remark }}
            </el-descriptions-item>
          </el-descriptions>
          <server-detail-public v-if="isServerDetail" ref="ServerDetail" :dev-id="temp.devId" @serverInfo="serverInfo"/>
          <!--          <el-descriptions class="margin-top" :title="$t('pages.systemConfig')" :column="2" size="" border>-->
          <!--            <el-descriptions-item :label="$t('pages.ratedConnections')">-->
          <!--              {{ temp.ratedConnections }}-->
          <!--            </el-descriptions-item>-->
          <!--            <el-descriptions-item :label="$t('pages.maximumConnection')">-->
          <!--              {{ temp.maxConnections }}-->
          <!--            </el-descriptions-item>-->
          <!--            <el-descriptions-item :span="2" :label="$t('pages.terminalIPAccessRange')">-->
          <!--              <grid-table-->
          <!--                ref="allowIpSegmentsTable"-->
          <!--                :height="160"-->
          <!--                row-no-label=" "-->
          <!--                :col-model="colModel1"-->
          <!--                :show-pager="false"-->
          <!--                :row-datas="allowIpSegments"-->
          <!--                :default-sort="{ prop: 'name', order: 'desc' }"-->
          <!--                :multi-select="false"-->
          <!--              />-->
          <!--            </el-descriptions-item>-->
          <!--          </el-descriptions>-->
        </div>
        <!--        <div style="flex: 1">-->
        <!--          <div class="el-descriptions__header" style="margin-bottom: 15px;">-->
        <!--            <div class="el-descriptions__title" style="margin-left: 18px">服务器状态</div>-->
        <!--            <div class="el-descriptions__extra"></div>-->
        <!--          </div>-->
        <!--          <server-detail v-if="true" ref="ServerDetail"/>-->
        <!--        </div>-->

      </div>
    </Drawer>
  </div>
</template>
<script>
// import ServerDetail from '@/views/system/deviceManage/serverInfo/serverDetail/index.vue';
import ServerDetailPublic from '@/views/system/deviceManage/serverInfo/serverDetailPublic/index.vue';

export default {
  name: 'DetailDaqServer',
  components: { ServerDetailPublic/*, ServerDetail*/ },
  data() {
    return {
      detailVisible: false,
      submitting: false,                 // 提交数据中...
      isServerDetail: false, // 开启服务器详情组件
      groupTreeSelectData: [],
      treeSelectNode: [],
      defaultTreeSelectNode: [{ id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped') }],
      treeData: [],
      temp: {}, // 表单字段
      defaultTemp: { // 表单字段
        id: undefined,
        dbType: 1,
        rule: 1,
        ip: undefined,
        port: 23306,
        useDefault: true,
        account: undefined,
        password: undefined,
        readOnly: false,
        access: 0,
        remark: '',
        authorizedStatus: 1,   //  授权状态，即开关,0-关闭，1-打开， null：表示该设备数据是旧数据，且尚未连接引擎，此数据不允许修改开关， 数据库服务器尚不支持设置授权状态，固定为1
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        dbMethod: null,        // 数据库安装方式  0：内置数据库，1：外置数据库  若为外置数据库时且内网类型=0时，IP和端口都不可变
        version: '',
        onlineStatus: 0 // 服务器在线状态0:不在线 1：在线
      },
      dbTypeOptions: {
        1: 'MySQL',
        2: 'SqlServer',
        3: 'Oracle',
        4: 'DM',
        5: 'KingbaseES',
        6: 'OceanBase'
      },
      accessOptions: {
        0: this.$t('pages.intranetAccess'),
        1: this.$t('pages.internetAccess')
      },
      intranetTypeOptions: {
        0: this.$t('pages.truthIpAndPort'),
        1: this.$t('pages.typeless')
      }
    }
  },
  computed: {

  },
  watch: {
  },
  activated() {
  },
  created() {
  },
  methods: {
    show(row) {
      this.handleUpdate(row)
      this.detailVisible = true
      // 进入详细信息后，选中双击节点的编号
      const devId = row.devId
      console.log('ServerDetail devId', devId)
      this.isServerDetail = true
      this.$nextTick(() => {
        this.$refs['ServerDetail'].query2.devId = row.devId + ''// 编号
      })
    },
    // 服务器信息
    serverInfo(data) {
      Object.assign(this.temp, {
        onlineStatus: data.onlineStatus
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleUpdate(row) {
      //  若authorizedStatus为undefined或null表示该数据库是系统旧版本升级到5.0版本后尚未接入的数据库服务器，映射表尚未有数据，暂不支持修改内网类型
      this.isAccessed = row.authorizedStatus === undefined || row.authorizedStatus === null
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
      if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
        this.temp.intranetType = 1
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.accessDisabled = !this.$store.getters.isMasterSubAble
    },
    // 格式化授权状态
    statusTableFilter(data) {
      const statusMap = {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      }
      return statusMap[data]
    },
    // 关闭弹窗前，校验数据是否发生变化，并提示
    beforeClose(done) {
      this.isServerDetail = false
      done()
    },
    // 点击确认提交数据
    async handleSaveConfig() {
    }
  }
}
</script>
<style lang="scss" scoped>
.show-detail-panel {
  color: #666;
  display: flex;
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
