<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="false" type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createDevice') }}
        </el-button>

        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delDevice') }}
        </el-button>
        <el-tooltip v-if="false" effect="dark" placement="bottom-start">
          <div slot="content">
            {{ '注意：无法删除已被【服务器接入审批】配置引用的数据库信息，若需删除被引用的数据需前往【服务器接入审批】->【接入设置】中重新设置匹配接入规则' }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <!--        <el-button v-if="$store.getters.isMasterSubAble" icon="el-icon-brush" size="mini" :disabled="!cloneable" @click="handleClone">-->
        <!--          <el-button v-if="$store.getters.isMasterSubAble" icon="el-icon-brush" size="mini" :disabled="!cloneable" @click="handleClone">-->
        <!--          {{ $t('pages.clone') }}-->
        <!--        </el-button>-->
        <el-button v-if="isShowUpdateDbPassword" style="margin-left: 10px;" size="mini" @click="updateDbPassword">
          {{ $t('pages.updateDbServerPassword') }}
        </el-button>
        <el-tooltip v-if="isShowUpdateDbPassword" effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.updatePwdTip1') }}<br>
            {{ $t('pages.updatePwdTip2') }}<br>
            {{ $t('pages.updatePwdTip3') }}<br>
            {{ $t('pages.updatePwdTip4') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.serverName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable :maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.deviceNum')" prop="devId">
                <el-input v-model.number="query.devId" v-trim clearable :maxlength="4" :placeholder="this.$t('pages.serverLibrary_text5')" @input="number('devId')"/>
              </FormItem>
              <FormItem :label="$t('pages.accessMode')">
                <el-select v-model="query.access" clearable :placeholder="$t('text.select')" style="width: 100%">
                  <el-option v-for="item in accessOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.ipAddr')" prop="ip">
                <el-input v-model="query.ip" v-trim :maxlength="64" clearable></el-input>
              </FormItem>
              <FormItem :label="$t('pages.port')" prop="port" class="input-left">
                <el-input v-model.number="query.port" clearable maxlength="5" :placeholder="$t('pages.serverLibrary_text6')" @input="number('port')"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table ref="appList" :col-model="colModel" :row-data-api="rowDataApi" :default-sort="defaultSort" @selectionChangeEnd="selectionChangeEnd" />
      <edit-db-server ref="editDbServer" @submitEnd="handleFilter"></edit-db-server>
      <detail-db-server ref="detailDbServer"></detail-db-server>
    </div>
    <update-password-dlg ref="updatePasswordDlg" @submit="updatePasswordSubmit"/>
  </div>
</template>

<script>
import {
  getServerPage,
  deleteServer,
  isUpdateDbPassword,
  getMasterDbId,
  getUpdateDbPassword
} from '@/api/system/deviceManage/dbServer'
import { getDictLabel } from '@/utils/dictionary'
import editDbServer from '@/views/system/deviceManage/dbServer/editDbServer'
import UpdatePasswordDlg from '@/views/system/deviceManage/dbServer/dlg/updatePasswordDlg';
import { getConfigDbIds } from '@/api/system/deviceManage/serverAccessApproval';
import detailDbServer from '@/views/system/deviceManage/dbServer/detailDbServer'

export default {
  name: 'DbInfo',
  components: { detailDbServer, UpdatePasswordDlg, editDbServer },
  data() {
    return {
      defaultColModel: [
        { prop: 'name', label: 'serverName', width: '150', fixed: true, iconFormatter: this.onlineFormatter, sort: true },
        { prop: 'dbType', label: 'DBType', width: '120', formatter: this.dbTypeFormatter, sort: true },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: true },
        { prop: 'sourceId', label: 'originDeviceNum', width: '120', sort: true },
        // { prop: 'rule', label: 'subDatabaseRules', width: '100', formatter: this.dbRuleFormatter },
        { prop: 'ip', label: 'ip', width: '100', sort: true },
        { prop: 'port', label: 'port', width: '100', sort: true },
        // { prop: 'account', label: '账号', width: '100' },
        { prop: 'access', label: 'accessMode', width: '100', formatter: this.accessFormatter, sort: true },
        { prop: 'dbpassVer', label: 'dbpassVer', width: '130' },
        { prop: 'version', label: 'version', width: '130' },
        // { prop: 'readOnly', label: '是否归档库', width: '80', formatter: this.isTrueFormatter },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'detail', click: this.handleDetail }
          ]
        }
      ],
      colModel: [],
      defaultSort: {
        prop: 'db_id',
        order: 'desc'
      },
      query: { // 查询条件
        page: 1,
        name: '',
        devId: undefined,
        access: undefined,
        ip: '',
        port: undefined
      },
      deleteable: false,
      cloneable: false,
      dbTypeOptions: [
        { id: 1, label: 'MySQL', defaultPort: 23306 },
        { id: 2, label: 'SqlServer', defaultPort: 1433 },
        { id: 3, label: 'Oracle', defaultPort: 1521 },
        { id: 4, label: 'DM', defaultPort: 5236 },
        { id: 5, label: 'KingbaseES', defaultPort: 54321 },
        { id: 6, label: 'OceanBase', defaultPort: 23306 }
      ],
      dbRuleOptions: [
        { value: 0, label: this.$t('pages.warehouseByDay') },
        { value: 1, label: this.$t('pages.monthlyDistribution') },
        { value: 2, label: this.$t('pages.byQuarter') },
        { value: 3, label: this.$t('pages.byYear') }
      ],
      accessOptions: [
        { value: 0, label: this.$t('pages.intranetAccess') },
        { value: 1, label: this.$t('pages.internetAccess') }
      ],
      isShowUpdateDbPassword: false,  //  是否可展示修改密码按钮
      minVersion: '5.0.1.250319.SC',  //  数据库修改密码功能需达到的最低版本号
      masterDbIds: null
    }
  },
  computed: {
    gridTable() {
      return this.$refs['appList']
    }
  },
  created() {
    this.colModel = this.defaultColModel
    this.getUpdateDbPasswordPermission()
    this.getMasterDbId();
    //  支持设备名称查询
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.$nextTick(() => {
      this.handleFilter()
    })
  },
  activated() {
    //  支持设备名称查询
    if (this.$route.query) {
      this.query.name = this.$route.query.name || ''
    }
    this.handleFilter()
    this.getUpdateDbPasswordPermission()
  },
  methods: {
    //  获取是否拥有修改数据库密码权限
    getUpdateDbPasswordPermission() {
      isUpdateDbPassword().then(res => {
        this.isShowUpdateDbPassword = res.data || false
        this.colModel = this.isShowUpdateDbPassword ? this.defaultColModel : this.defaultColModel.filter(col => col.prop !== 'dbpassVer')
      })
    },
    init() {
      this.handleFilter()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (searchQuery.sortName == 'name') {
        searchQuery.sortName = 'dbName'
      } else if (searchQuery.sortName == 'devId') {
        searchQuery.sortName = 'dbId'
      }
      return getServerPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
      this.cloneable = rowDatas.length == 1 && rowDatas[0].access
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.editDbServer.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.editDbServer.handleUpdate(row)
    },
    handleDetail(row) {
      this.$refs.detailDbServer.show(row)
    },
    handleClone() {
      const rowDatas = this.gridTable.getSelectedDatas();
      //  映射设备时，情况账号和密码
      const data = Object.assign({}, rowDatas[0])
      data.account = null
      data.password = null
      this.$refs.editDbServer.handleClone(data)
    },
    async getNotAllowDelDevIds() {
      const res = await getConfigDbIds();
      const dbIds = res.data || []  //  已被服务器接入审批设置引用
      const notAllowDevIds = [] //  不允许被删除的数据库设备Id
      if (dbIds.length) {
        const toDeleteDatas = this.gridTable.getSelectedDatas() || [];
        const needDelDbIds = toDeleteDatas.map(t => t.devId); //   待删除的数据库设备Id
        needDelDbIds.forEach(devId => {
          if (dbIds.includes(devId)) {
            notAllowDevIds.push(devId);
          }
        })
      }
      return notAllowDevIds;
    },
    async handleDelete() {
      const toDeleteDatas = this.gridTable.getSelectedDatas() || []
      //  校验主数据库不允许删除
      if (this.masterDbIds) {
        const toDelMasterDb = toDeleteDatas.find(t => this.masterDbIds.indexOf(t.devId) >= 0)
        if (toDelMasterDb) {
          this.$notify({
            title: this.$t('text.error'),
            message: this.$t('pages.masterDevIdNotAllowDel', { devId: toDelMasterDb.devId }),
            type: 'error',
            duration: 2000
          })
          return;
        }
      }
      const notAllowDelDevIds = await this.getNotAllowDelDevIds();
      if (notAllowDelDevIds.length) {
        this.$message({
          message: this.$t('pages.notDeleteDbNumCited', { dbDevId: notAllowDelDevIds.join(',') }),
          type: 'error',
          duration: 2000
        })
        return;
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds();
        deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
      })
    },
    onlineFormatter(row, data) {
      const online = row.onlineStatus == 1
      const title = (online ? this.$t('pages.online') : this.$t('pages.offline')) + '\r\n' + this.$t('pages.DBServer')
      const style = online ? 'color: green' : 'color: #8a8a8a'
      return { class: 'database', title, style }
    },
    dbTypeFormatter(row, data) {
      for (let i = 0, size = this.dbTypeOptions.length; i < size; i++) {
        const id = this.dbTypeOptions[i].id
        if (row.dbType === id) {
          return this.dbTypeOptions[i].label
        }
      }
      return row.dbType
    },
    dbRuleFormatter(row, data) {
      return getDictLabel(this.dbRuleOptions, row.rule)
    },
    accessFormatter(row, data) {
      return getDictLabel(this.accessOptions, row.access)
    },
    isTrueFormatter(row, data) {
      return !data ? this.$t('text.no') : this.$t('text.yes')
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.devId = undefined
      this.query.access = undefined
      this.query.ip = ''
      this.query.port = undefined
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    },
    updateDbPassword() {
      getUpdateDbPassword().then(res => {
        let msg = null;
        if (res.data === 1) {
          this.$refs.updatePasswordDlg.show()
        } else if (res.data === -1) {
          msg = this.$t('pages.updateDbPwdValidTip1')
        } else if (res.data === -2) {
          msg = this.$t('pages.updateDbPwdValidTip2', { minVersion: this.minVersion })
        }
        if (msg) {
          this.$message({
            message: msg,
            type: 'warning'
          })
        }
      })
    },
    //  数据库密码修改成功回调
    updatePasswordSubmit() {
      this.handleFilter()
    },
    //  获取主数据库编号
    getMasterDbId() {
      getMasterDbId().then(res => {
        this.masterDbIds = res.data || null
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-input-number.is-without-controls .el-input__inner{
  text-align: left;
}
</style>
