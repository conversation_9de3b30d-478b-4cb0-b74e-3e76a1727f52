<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card">
      <el-tab-pane :label="$t('route.DBServer')" name="DbInfo">
        <DBInfo ref="DbInfo"/>
      </el-tab-pane>
      <el-tab-pane v-if="dbUserEnable" :lazy="true" :label="$t('pages.databaseAccount')" name="DbUser">
        <DbUser ref="DbUser"/>
      </el-tab-pane>
      <el-tab-pane v-if="enable" :lazy="true" :label="$t('route.dataBaseBack')" name="ManageLibrary">
        <ManageLibrary ref="ManageLibrary"/>
      </el-tab-pane>
      <el-tab-pane v-if="enable" :lazy="true" :label="$t('route.dataBaseBackLog')" name="DataBaseBackLog">
        <DataBaseBackLog ref="DataBaseBackLog" :note-id="noteId" :create-time="createTime"/>
      </el-tab-pane>
      <el-tab-pane v-if="enable" :lazy="true" :label="$t('route.databaseBackupRecord')" name="DatabaseBackupRecord">
        <DatabaseBackupRecord ref="DatabaseBackupRecord"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DBInfo from '@/views/system/deviceManage/dbServer/dbInfo'
import DbUser from '@/views/system/deviceManage/dbServer/dbUser'
import ManageLibrary from '@/views/system/configManage/manageLibrary'
import DataBaseBackLog from '@/views/system/terminalManage/dataBaseBackLog'
import DatabaseBackupRecord from '@/views/system/terminalManage/databaseBackupRecord'
import { getCurrentDataBaseServerInfo } from '@/api/system/terminalManage/manageLibrary';

export default {
  name: 'DBServer',
  components: { DBInfo, ManageLibrary, DataBaseBackLog, DatabaseBackupRecord, DbUser },
  props: {
    tabName: { type: String, default: 'DbInfo' }
  },
  data() {
    return {
      activeName: this.tabName,
      //  数据库备份，恢复功能支持的操作系统类型  允许填的值为：windows, linux, mac
      supportOsTypes: ['windows', 'linux'],
      supportDbTypes: [1, 5],  //  支持的数据库类型，1：mysql，4：达梦，5：人大金仓
      enable: false,    //  是否支持数据库备份，恢复功能
      noteId: null,
      createTime: null,
      dbUserSupportDbTypes: [1] //  数据库用户功能支持的数据库类型，1：mysql，4：达梦，5：人大金仓
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.activeName = to.query.tabName || vm.activeName
      vm.noteId = to.query.noteId ? parseInt(to.query.noteId) : null
      vm.createTime = to.query.createTime || null
      vm.$router.push({ query: {}})
    })
  },
  computed: {
    dbUserEnable() {
      return false; //  数据库账户创建功能暂时不上
      // return this.$store.getters.masterDbType && this.dbUserSupportDbTypes.includes(this.$store.getters.masterDbType)
    }
  },
  watch: {
    '$store.state.commonData.notice.DBServer'(val) {
      this.toActiveTab()
    },
    activeName(val) {
      this.$refs[this.activeName] && this.$refs[this.activeName].init();
    }
  },
  created() {
    this.getSystemData();
  },
  activated() {
    this.getSystemData();
  },
  methods: {
    toActiveTab() {
      const query = this.$route.query
      this.activeName = query.tabName ? query.tabName : this.activeName
      this.noteId = query.noteId ? parseInt(query.noteId) : null
      this.createTime = query.createTime || null
    },
    //  获取引擎服务器的操作系统类型,和策略库的主数据库类型
    getSystemData() {
      //  获取引擎信息
      if (!this.$store.getters.engineOsType || !this.$store.getters.masterDbType) {
        return this.loadOsTypeAndDbType()
      } else {
        this.enable = this.supportOsTypes.includes(this.$store.getters.engineOsType) && this.supportDbTypes.includes(this.$store.getters.masterDbType)
      }
      //  数据库备份
    },
    loadOsTypeAndDbType() {
      return getCurrentDataBaseServerInfo().then(res => {
        const data = res.data || {}
        const systemName = (data.serverInfo && data.serverInfo.serverBaseInfo && data.serverInfo.serverBaseInfo.systemName || '').trim().toLowerCase().toString()
        const masterDbType = res.data.masterDbType
        let osType = null
        if (systemName.indexOf('windows') > -1) {
          osType = 'windows'
        } else if (systemName.indexOf('linux') > -1) {
          osType = 'linux'
        } else if (systemName.indexOf('mac') > -1) {
          osType = 'mac'
        }
        if (osType != null) {
          // 20240516 支持人大金仓数据库，故enable多判断支持的数据库类型
          this.enable = this.supportOsTypes.includes(osType + '') && this.supportDbTypes.includes(masterDbType)
          this.$store.dispatch('commonData/setEngineOsType', osType)
        }
        if (masterDbType != null) {
          this.$store.dispatch('commonData/setMasterDbType', masterDbType);
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .app-container{
    padding: 10px 15px;
  }
</style>
