<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 550px;">
        <!--<el-tabs ref="tabs" v-model="tabName" :before-leave="changeTab" style="height: 400px;">-->
        <!--  <el-tab-pane :label="$t('pages.basicInformation')" name="serverTab" style="padding: 10px; overflow: auto;">-->
        <!-- 数据库服务器尚不支持设置授权状态 -->
        <FormItem v-if="false" :label="$t('pages.grantAuthor')">
          <el-switch v-model="temp.authorizedStatus" :active-value="1" :inactive-value="0" />
        </FormItem>
        <FormItem :label="$t('pages.serverName')" prop="name">
          <el-input v-model="temp.name" :maxlength="30" clearable></el-input>
        </FormItem>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.accessMode')">
              <el-tooltip effect="dark" placement="top-start" :disabled="!isEnglish() || !accessDisabled">
                <div slot="content">
                  {{ getDictLabel(accessOptions, temp.access) }}
                </div>
                <el-select v-model="temp.access" :disabled="accessDisabled" :placeholder="$t('text.select')">
                  <el-option v-for="item in accessOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-tooltip>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.DBType')">
              <el-select v-model="temp.dbType" disabled :placeholder="$t('text.select')" @change="dbTypeChange">
                <el-option v-for="item in dbTypeOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem label="内网类型">
              <div slot="label">
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.intranetTypeTip1') }}<br>
                    {{ $t('pages.intranetTypeTip2') }}<br>
                    {{ $t('pages.intranetTypeTip3') }}<br>
                    {{ $t('pages.intranetTypeTip4') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.intranetType') }}
              </div>
              <el-select v-model="temp.intranetType" @change="intranetTypeChange">
                <el-option :key="0" :value="0" :disabled="isAccessed" :label="$t('pages.truthIpAndPort')"/>
                <el-option :key="1" :value="1" :label="$t('pages.typeless')"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <!-- 数据库安装类型 -->
            <FormItem :label="$t('pages.installMode') + '：'">
              {{ temp.dbMethod ? $t('pages.dbServerInstallType1') : $t('pages.dbServerInstallType2') }}
            </FormItem>
          </el-col>
        </el-row>

        <FormItem :label="$t('pages.ipAddr')" prop="ip">
          <!-- 内网类型为实际网卡IP和端口时，安装方式为自定义安装时，不允许修改 -->
          <el-select v-if="!temp.intranetType" v-model="temp.ip" :disabled="!temp.onlineStatus || (!!temp.dbMethod)">
            <el-option v-for="(item, index) in ips" :key="index" :value="item" :label="item"/>
          </el-select>
          <el-input v-else v-model="temp.ip" :maxlength="64" clearable></el-input>
        </FormItem>
        <FormItem :label="$t('pages.port')" prop="port" class="input-left">
          <el-input-number v-model="temp.port" :min="1" :max="65535" :step="1" :disabled="!temp.intranetType" step-strictly :controls="false"></el-input-number>
        </FormItem>
        <div v-if="showFileServerAccount && !supportUpdateDbPassword">
          <FormItem :label="$t('pages.loginUser')" prop="account">
            <el-input v-model="temp.account" v-trim maxlength="32" clearable/>
          </FormItem>
          <FormItem :label="$t('pages.loginPwd')" encrypt prop="password">
            <encrypt-input v-model="temp.password" v-trim maxlength="32" clearable/>
          </FormItem>
        </div>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" type="textarea" rows="3" :maxlength="300" show-word-limit/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="connecting" @click="testConnection()">
          {{ $t('button.testConnection') }}
        </el-button>
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createServer, updateServer } from '@/api/system/deviceManage/dbServer';
import { ipValidator, isPort } from '@/utils/validate';
import { cloneDbServer, getByIpPort, getByName, getShardRule, testConnection, getMasterDbType } from '@/api/system/deviceManage/dbServer';
import { getDictLabel } from '@/utils/dictionary';
import { getPropertyByCode } from '@/api/property'
import EncryptInput from '@/components/EncryptInput'
import { getServerInfoByDevId } from '@/api/system/deviceManage/serverAccessApproval';

export default {
  name: 'EditDbServer',
  components: { EncryptInput },
  data() {
    return {
      accessDisabled: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        dbType: 1,
        rule: 1,
        ip: undefined,
        port: 23306,
        useDefault: true,
        account: undefined,
        password: undefined,
        readOnly: false,
        access: 0,
        remark: '',
        authorizedStatus: 1,   //  授权状态，即开关,0-关闭，1-打开， null：表示该设备数据是旧数据，且尚未连接引擎，此数据不允许修改开关， 数据库服务器尚不支持设置授权状态，固定为1
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        dbMethod: null        // 数据库安装方式  0：内置数据库，1：外置数据库  若为外置数据库时且内网类型=0时，IP和端口都不可变
      },
      dbTypeOptions: [
        { id: 1, label: 'MySQL', defaultPort: 23306 },
        { id: 2, label: 'SqlServer', defaultPort: 1433 },
        { id: 3, label: 'Oracle', defaultPort: 1521 },
        { id: 4, label: 'DM', defaultPort: 5236 },
        { id: 5, label: 'KingbaseES', defaultPort: 54321 },
        { id: 6, label: 'OceanBase', defaultPort: 23306 }
      ],
      dbRuleOptions: [
        { value: 0, label: this.$t('pages.warehouseByDay') },
        { value: 1, label: this.$t('pages.monthlyDistribution') },
        { value: 2, label: this.$t('pages.byQuarter') },
        { value: 3, label: this.$t('pages.byYear') }
      ],
      accessOptions: [
        { value: 0, label: this.$t('pages.intranetAccess') },
        { value: 1, label: this.$t('pages.internetAccess') }
      ],
      showFileServerAccount: false,
      dialogFormVisible: false,
      dialogCloneVisible: false,
      dialogStatus: '',
      connecting: false,
      submitting: false,
      textMap: {
        update: this.$t('pages.updateDBServer'),
        create: this.$t('pages.addDBServer'),
        clone: this.$t('pages.cloneDBServer')
      },
      rules: {
        ip: [
          { required: true, message: this.$t('pages.validateIP'), trigger: 'blur' },
          { validator: (rule, value, callback) => ipValidator(rule, value, callback), trigger: 'blur' }
        ],
        port: [{ required: true, validator: this.ipPortValidator, trigger: 'blur' }],
        name: [
          { required: true, message: this.$t('pages.validateMsg_nickName'), trigger: 'blur' }
          // { validator: this.validateName, trigger: 'blur' }   // 设备名称重复限制去除
        ],
        account: [{ required: true, message: this.$t('pages.validateMsg_enterAccount'), trigger: 'blur' }],
        password: [{ required: true, message: this.$t('pages.validateMsg_password'), trigger: 'blur' }]
      },
      editable: true,
      supportDbTypes: [1],  //  主数据库是否支持修改数据库密码， 若支持时，不允许修改登录账号和登录密码  1：mysql，4：达梦，5：人大金仓
      tabName: 'serverTab',
      severNetworkInfoList: [],  //  获取网卡中的信息
      ips: [],  //  网卡中的IPv6
      isAccessed: false   //  该数据库是系统旧版本升级到5.0版本后尚未接入的数据库服务器，映射表尚未有数据，暂不支持修改内网类型
    }
  },
  computed: {
    groupTree() {
      return this.$refs['groupTree']
    },
    //  是否支持数据库修改密码功能
    supportUpdateDbPassword() {
      return this.$store.getters.masterDbType && this.supportDbTypes.includes(this.$store.getters.masterDbType)
    }
  },
  watch: {},
  created() {
    this.resetTemp()
    this.loadShardRule()
    this.loadMasterDbType();
  },
  activated() {
  },
  methods: {
    getDictLabel,
    resetTemp() {
      this.connecting = false
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
      return this.$nextTick().then(() => {
        return getPropertyByCode('server.config').then(res => {
          this.showFileServerAccount = res.data && res.data.value.split(',').indexOf('4') > -1
          this.temp.useDefault = !this.showFileServerAccount
        })
      })
    },
    handleDrag() {
    },
    loadShardRule() {
      getShardRule().then(resp => {
        if (resp.data) {
          this.defaultTemp.rule = resp.data
          this.temp.rule = resp.data
        }
      })
    },
    dbTypeChange(val) {
      let isDefaultPort = false
      let defaultPort
      this.dbTypeOptions.forEach(op => {
        if (val == op.id) defaultPort = op.defaultPort
        if (op.defaultPort == this.temp.port) isDefaultPort = true
      })
      if (isDefaultPort || !this.temp.port) {
        this.temp.port = defaultPort
        this.defaultTemp.port = defaultPort
      }
    },
    testConnection() {
      this.connecting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          if (!this.temp.dbType) {
            // 请选择数据库类型
            this.$notify({ message: this.$t('pages.databaseException1'), type: 'error', duration: 2000 })
            this.connecting = false
            return
          }
          testConnection(tempData).then(resp => {
            this.connecting = false
            if (resp.data === 1) {
              this.$notify({ title: this.$t('text.success'), message: this.$t('pages.DBConnectionSucceeded'), type: 'success', duration: 2000 })
            } else if (resp.data === 2) {
              this.$notify({ title: this.$t('pages.DBConnectionSucceeded'), message: this.$t('pages.DBNotExistTable'), type: 'info', duration: 2000 })
            } else if (resp.data === 3) {
              this.$notify({ message: this.$t('pages.databaseException2'), type: 'error', duration: 2000 })
            } else if (resp.data === 4) {
              this.$notify({ message: this.$t('pages.DBConnectionFail'), type: 'error', duration: 2000 })
            }
          }).catch(() => {
            this.connecting = false
          })
        } else {
          this.connecting = false
        }
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.accessDisabled = !this.$store.getters.isMasterSubAble
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      //  若authorizedStatus为undefined或null表示该数据库是系统旧版本升级到5.0版本后尚未接入的数据库服务器，映射表尚未有数据，暂不支持修改内网类型
      this.isAccessed = row.authorizedStatus === undefined || row.authorizedStatus === null
      this.resetTemp()
      //  若内网类型为0，即Ip地址和端口为实际IP地址和端口时，仅支持选择网卡中的IP信息，且端口不支持修改，为1时，端口和IP地址都可修改
      this.getServerNetworkInfoList(row.devId);
      this.temp = Object.assign({}, row) // copy obj
      //  若内网类型为null，表示该设备属于5.0版本之前升上来但未连接的设备，默认设置成1
      if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
        this.temp.intranetType = 1
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.accessDisabled = !this.$store.getters.isMasterSubAble
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleClone(row) {
      this.temp = Object.assign({}, row)
      Object.assign(this.temp, {
        access: 0,
        remark: this.$t('pages.DBServerMapperBranchServerContent', { devId: this.temp.devId }),
        ip: '',
        name: ''
      })
      this.dialogStatus = 'clone'
      this.dialogFormVisible = true
      this.accessDisabled = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    formatFormData() {
      if (!this.showFileServerAccount) {
        // 不显示时，去除表单数据，避免加密处理错误
        this.temp.account = undefined
        this.temp.password = undefined
      }
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          createServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            // this.gridTable.execRowDataApi()
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          const func = this.dialogStatus === 'clone' ? cloneDbServer : updateServer
          func(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            // this.gridTable.execRowDataApi()
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    /**
     *表单验证
     */
    ipPortValidator(rule, value, callback) {
      if (!isPort(value)) {
        callback(new Error(this.$t('pages.validateMsg_PortError')))
      } else {
        getByIpPort({ ip: this.temp.ip, port: this.temp.port }).then(respond => {
          const bean = respond.data
          if (bean && bean.id !== this.temp.id) {
            callback(new Error(this.$t('pages.validateMsg_SameIPAddressAndPort')))
          } else {
            callback()
          }
        })
      }
    },
    validateName(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const db = respond.data
        if (db && db.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    loadMasterDbType() {
      getMasterDbType().then(res => {
        if (res.data) {
          this.defaultTemp.dbType = res.data
          this.temp.dbType = res.data
          this.dbTypeChange(res.data);
        }
      })
    },
    changeTab(activeName, oldActiveName) {
      return true
    },
    /**
     * 获取设备网卡信息
     * @param devId
     */
    getServerNetworkInfoList(devId) {
      this.ips = []
      getServerInfoByDevId(devId).then(res => {
        if (res.data) {
          this.severNetworkInfoList = res.data.serverNetworkInfoList || []
          this.severNetworkInfoList.forEach(t => {
            t.ips && this.ips.push(...t.ips.split(';'))
            t.ipv6s && this.ips.push(...t.ipv6s.split(';'))
          })
        }
      })
    },
    /**
     * 内网类型发生变更
     * @param value
     */
    intranetTypeChange(value) {
      //  IP有值且不在实际网卡IP中时，自动默认为实际网卡中的IP
      if (this.temp.ip && this.ips.length > 0 && !this.ips.includes(this.temp.ip)) {
        this.temp.ip = this.ips[0]
      }
      this.$refs.dataForm.validateField(['ip', 'port'])
    }
  }
}
</script>
<style scoped lang='scss'>
>>> .input-left .el-input-number .el-input__inner {
  text-align: left;
}
</style>
