<template>
  <div>
    <Form ref="serverForm" :model="temp" label-position="right" label-width="135px" style="width: 640px;">
      <el-divider content-position="left"> {{ $t('pages.DBServerBasicInformation') }} </el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.serverName') + '：'">{{ temp.name }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.deviceNum') + '：'">{{ temp.devId }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetAddress') + '：'">{{ temp.ip }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetPort') + '：'">{{ temp.port }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.accessMode') + '：'">{{ getDictLabel(accessOptions, temp.access) }}</FormItem>
        </el-col>
      </el-row>
      <el-divider content-position="left">{{ $t('pages.DBServerBindCollectionServer') }}</el-divider>
      <FormItem :label="$t('pages.server_daq') " :tooltip-content="$t('pages.bindGatherGroupInfo')" tooltip-placement="bottom-start">
        <el-select v-model="temp.daqIds" multiple :placeholder="$t('text.select')" @change="changeValue($event)">
          <el-option v-for="item in daqServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <FormItem :label="$t('route.intelligentBackupServer')" :tooltip-content="$t('pages.smartBindGatherGroupInfo')" tooltip-placement="bottom-start">
        <el-select v-model="temp.smartIds" multiple :placeholder="$t('text.select')" @change="changeValue1($event)">
          <el-option v-for="item in smartServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <el-divider v-if="$store.getters.isMasterSubAble && newTemp.sourceId" content-position="left">{{ $t('pages.DBServerCloneBranchServer') }}</el-divider>
      <div v-if="$store.getters.isMasterSubAble && newTemp.sourceId">
        <FormItem :label="$t('pages.DBServerBranchServer')+'：'">
          <el-select v-model="newTemp.sourceId" disabled :placeholder="$t('text.select')">
            <el-option v-for="item in dbServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
          </el-select>
        </FormItem>
        <label style="margin: 15px;color: #75b9e2;">
          <i18n path="pages.DBServerCloneServerContent">
            <template slot="devId">{{ newTemp.devId }}</template>
            <template slot="sourceId">{{ newTemp.sourceId }}</template>
            <br slot="br"/>
          </i18n>
        </label>
      </div>
      <!--      <FormItem label-width="40px">
        <el-checkbox v-model="masterSubsectionAble">开启总分数据库服务器映射</el-checkbox>
      </FormItem>
      <el-divider v-if="masterSubsectionAble && temp.access" content-position="left">{{ '映射到总部数据库服务器' }}</el-divider>
      <FormItem v-if="masterSubsectionAble && temp.access" :label="'总部数据库服务器：'">
        <el-select v-model="temp.dbIds" :placeholder="$t('text.select')" style="height: 30px;">
          <el-option-group label="数据库服务器">
            <el-option v-for="item in getDbServer(0)" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
          </el-option-group>
        </el-select>
      </FormItem>
      <el-divider v-if="masterSubsectionAble && !temp.access" content-position="left">{{ '已经映射到当前服务器的分部数据库服务器' }}</el-divider>
      <FormItem v-if="masterSubsectionAble && !temp.access" :label="'分部数据库服务器：'">
        <el-select v-model="temp.dbIds" multiple :placeholder="$t('text.select')">
          <el-option-group label="数据库服务器">
            <el-option v-for="item in getDbServer(1)" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
          </el-option-group>
        </el-select>
      </FormItem>-->
    </Form>
  </div>
</template>

<script>
import { deepMerge, equalsObj } from '@/utils'
import { getDictLabel } from '@/utils/dictionary'
import {
  bindServer,
  getBindServer,
  getDbServerAndDevGroupRelation
} from '@/api/system/deviceManage/dbServer'

export default {
  name: 'DbBindServerFrom',
  components: { },
  props: {
    daqServer: {
      type: Array,
      default: null
    },
    dbServer: {
      type: Array,
      default: null
    },
    smartServer: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      masterSubsectionAble: false,
      masterAble: false,
      temp: {},
      oldTemp: {},
      sourceDB: {},
      noChangeTemp: {},
      accessOptions: [
        { value: 0, label: this.$t('pages.intranetAccess') },
        { value: 1, label: this.$t('pages.internetAccess') }
      ],
      daqGroupNames: '',
      smartGroupNames: '',
      newTemp: {}
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  activated() {
  },
  methods: {
    getDictLabel,
    changeValue() {
      const oldOptions = [...this.temp.daqIds];
      let groupDeleted = false
      let deletedGroupName = ''
      for (let i = 0; i < this.daqGroupNames.length; i++) {
        let deleted = true
        for (let j = 0; j < oldOptions.length; j++) {
          if (this.daqGroupNames[i] == oldOptions[j]) {
            // 选项中存在该分组名，表示改分组没有移除
            deleted = false
          }
        }
        if (deleted) {
          groupDeleted = true
          deletedGroupName = this.daqGroupNames[i]
        }
      }

      if (groupDeleted) {
        const position = this.noChangeTemp.daqIds.indexOf(deletedGroupName);
        this.temp.daqIds.splice(position, 0, deletedGroupName);
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.unableRemoveGatherGroup'),
          type: 'warning',
          duration: 1000
        })
        return
      }
      const storeTemp = []
      for (let k = 0; k < this.temp.daqIds.length; k++) {
        if (!this.daqGroupNames.includes(this.temp.daqIds[k])) {
          storeTemp.push(this.temp.daqIds[k])
        }
      }
      this.newTemp.daqIds = []
      for (let p = 0; p < storeTemp.length; p++) {
        this.newTemp.daqIds[p] = storeTemp[p]
      }
      this.noChangeTemp.daqIds = []
      for (let q = 0; q < this.temp.daqIds.length; q++) {
        this.noChangeTemp.daqIds[q] = this.temp.daqIds[q]
      }
    },
    changeValue1() {
      const oldOptions = [...this.temp.smartIds];
      let groupDeleted = false
      let deletedGroupName = ''
      for (const smartGroupName of this.smartGroupNames) {
        let deleted = true
        for (const opt of oldOptions) {
          if (smartGroupName == opt) {
            // 选项中存在该分组名，表示改分组没有移除
            deleted = false
            break
          }
        }
        if (deleted) { // 该分组名被移除，则记录被移除的分组名称
          groupDeleted = true
          deletedGroupName = smartGroupName
        }
      }

      if (groupDeleted) {
        const position = this.noChangeTemp.smartIds.indexOf(deletedGroupName);
        this.temp.smartIds.splice(position, 0, deletedGroupName);
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.unableRemoveSmartBackupGroup'),
          type: 'warning',
          duration: 1000
        })
        return
      }
      const storeTemp = []
      for (let k = 0; k < this.temp.smartIds.length; k++) {
        if (!this.smartGroupNames.includes(this.temp.smartIds[k])) {
          storeTemp.push(this.temp.smartIds[k])
        }
      }

      this.newTemp.smartIds = []
      for (let p = 0; p < storeTemp.length; p++) {
        this.newTemp.smartIds[p] = storeTemp[p]
      }

      this.noChangeTemp.smartIds = []
      for (let q = 0; q < this.temp.smartIds.length; q++) {
        this.noChangeTemp.smartIds[q] = this.temp.smartIds[q]
      }
    },
    setFormData(data) {
      this.sourceDB = {}
      this.temp = deepMerge({}, data)
      this.oldTemp = deepMerge({}, this.temp)
      this.newTemp = deepMerge({}, this.temp)
      getDbServerAndDevGroupRelation(data.devId).then(resp => {
        if (resp.data) {
          this.daqGroupNames = resp.data.daqGroupNames
          this.smartGroupNames = resp.data.smartGroupNames
          getBindServer(data.devId).then(resp => {
            if (resp.data) {
              this.temp = deepMerge({
                daqIds: resp.data.daqIds,
                smartIds: resp.data.smartIds
              }, resp.data.server)

              this.oldTemp = deepMerge({}, this.temp)
              this.newTemp = deepMerge({}, this.temp)

              let k = this.temp.daqIds.length;
              for (let j = 0; j < this.daqGroupNames.length; j++) {
                this.temp.daqIds[k] = this.daqGroupNames[j]
                k++
              }
              let l = this.temp.smartIds.length;
              for (let m = 0; m < this.smartGroupNames.length; m++) {
                this.temp.smartIds[l] = this.smartGroupNames[m]
                l++
              }
              if (this.dbServer && this.newTemp.sourceId) {
                for (let i = 0; i < this.dbServer.length; i++) {
                  if (this.dbServer[i].devId === this.newTemp.sourceId) {
                    this.sourceDB = this.dbServer[i]
                    break
                  }
                }
              }
              this.noChangeTemp = deepMerge({}, this.temp)
            }
          })
        }
      })
    },
    isFormDataChange() {
      const obj1 = this.getFormData(this.newTemp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData) {
      return {
        devType: tempData.devType,
        devId: tempData.devId,
        daqServers: tempData.daqIds,
        smartServers: tempData.smartIds
      }
    },
    submitData(callback) {
      bindServer(this.getFormData(this.newTemp)).then(() => {
        callback()
        this.oldTemp = deepMerge({}, this.newTemp)
      }).catch((e) => {
        callback({ valid: false })
        console.log('提交失败！{}', e)
      })
    },
    getDbServer(access) {
      const result = []
      if (this.dbServer) {
        this.dbServer.forEach(item => {
          if (access === item.access) {
            result.push(item)
          }
        })
      }
      return result
    },
    toOptionByServer(data) {
      let label = data.name;
      if (data.intranetIpv6) {
        label += ' (' + data.devId + '_' + data.intranetIpv6 + ':' + data.intranetPort + ')'
      } else if (data.internetIpv6) {
        label += ' (' + data.devId + '_' + data.internetIpv6 + ':' + data.internetPort + ')'
      } else if (data.intranetIp) {
        label += ' (' + data.devId + '_' + data.intranetIp + ':' + data.intranetPort + ')'
      } else if (data.internetIp) {
        label += ' (' + data.devId + '_' + data.internetIp + ':' + data.internetPort + ')'
      }
      return label
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
