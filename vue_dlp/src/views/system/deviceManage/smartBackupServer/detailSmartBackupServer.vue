<template>
  <div>
    <Drawer
      :title="$t('table.detail')"
      :visible.sync="detailVisible"
      status="update"
      :with-footer="false"
      :size="'60%'"
      wrapper-closable
      append-to-body
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
    >
      <div class="app-container show-detail-panel" >
        <div style="flex: 1">
          <el-descriptions class="margin-top" :title="$t('pages.basicInformation')" :column="3" size="small" border>
            <el-descriptions-item :label="$t('pages.serverName')">
              {{ temp.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.deviceNum')">
              {{ temp.devId }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.serverSubGroup')">
              {{ groupFormatter(temp.groupId) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.remark')">
              {{ temp.remark }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.grantAuthor')">
              {{ statusTableFilter(temp.adminAuthorized) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetType')">
              <div slot="label">
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.intranetTypeTip1') }}<br>
                    {{ $t('pages.intranetTypeTip2') }}<br>
                    {{ $t('pages.intranetTypeTip3') }}<br>
                    {{ $t('pages.intranetTypeTip4') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.intranetType') }}
              </div>
              {{ intranetTypeOptions[temp.intranetType] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetIPV4Address')">
              {{ temp.intranetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetIPV6Address')">
              {{ temp.intranetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetPort')">
              {{ temp.intranetPort }}
            </el-descriptions-item>
            <!--            <el-descriptions-item :label="$t('pages.enableInternetIP')">-->
            <!--              {{ yesOrNoMap[temp.enableInternet] }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item :label="$t('pages.returnPublicNetwork')">-->
            <!--              {{ yesOrNoMap[temp.enableForceRetIP] }}-->
            <!--            </el-descriptions-item>-->
            <el-descriptions-item :label="$t('pages.internetIPV4Address')">
              {{ temp.internetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV6Address')">
              {{ temp.internetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetPort')">
              {{ temp.internetPort }}
            </el-descriptions-item>
            <!--            服务器状态-->
            <el-descriptions-item :label="$t('pages.versionNumber')">
              {{ temp.version }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              {{ temp.onlineStatus }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              <div slot="label">{{ $t('pages.onlineTerminals') }} /{{ $t('pages.terminalAll') }} :</div>
              {{ temp.allDevOnlineNum }}
            </el-descriptions-item>
          </el-descriptions>
          <div style="margin-bottom: 10px">
            <server-detail-public v-if="isServerDetail" ref="ServerDetail" :dev-id="temp.devId" @serverInfo="serverInfo"/>
          </div>

          <el-descriptions class="margin-top" :title="$t('pages.systemConfig')" :column="3" size="small" border>
            <el-descriptions-item label="是否开启最大连接数">
              {{ yesOrNoMap[temp.isSetMax] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.maximumConnection')">
              {{ temp.isSetMax==1?temp.maxConnections: 0 }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </Drawer>
  </div>
</template>
<script>
import { fileServerDefaultBusses } from '@/views/system/deviceManage/backupServer/backupServerCommonInfo';
import ServerDetailPublic from '@/views/system/deviceManage/serverInfo/serverDetailPublic/index.vue';

export default {
  name: 'DetailSmartBackupServer',
  components: { ServerDetailPublic },
  props: {
    groupTreeSelectData: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      detailVisible: false,
      submitting: false,                 // 提交数据中...
      isServerDetail: false, // 开启服务器详情组件
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        groupId: undefined,
        pwd: '',
        accessPwd: 'dev-server_12345',
        devId: undefined,
        intranetIp: '',
        internetIp: '',
        intranetIpv6: '',
        internetIpv6: '',
        intranetPort: 20381,
        internetPort: undefined,
        remark: '',
        adminAuthorized: 1,
        maxConnections: undefined,
        isSetMax: 0, // isSetMax==1时，maxConnections默认为3000
        encryptProps: ['accessPwd'],
        authorizedStatus: null, //  授权状态  若为null代表mapping表中不存在该设备信息，表示未接入，无法操作主数据库
        intranetType: null,     //  内网类型 0：内网ip,端口与实际一致 1:内网ip,端口与实际不一致 （当为1时，只允许按网卡中的IP进行修改） ,当为0时，端口不支持修改
        sysmgrDbId: null, //  主数据库Id
        extraInfo: {
          sysmgrDbId: null,  //  主数据库Id
          dbMethod: null  //  数据库安装方式  0：内置数据库，1：外置数据库
        },
        onlineStatus: null,    //   服务器在线状态
        allDevOnlineNum: ''
      },
      bussTemp: {},
      batchBussTemp: {}, // 批量配置业务备份路径与文件保留时间存储对象
      checkedModuleIds: [],
      selectedCheckedModuleIds: [], // 用于批量配置时存储选中的业务的bussId
      specialBussIds: [13],
      specialBussDatas: [],
      bussDatas: [],
      selectedDatas: [], // 用于批量配置时存储选中的业务
      defaultBussDatas: fileServerDefaultBusses,
      yesOrNoMap: {
        0: this.$t('text.no'),
        1: this.$t('text.yes')
      },
      osTypeMap: {
        1: 'Windows',
        2: 'Linux'
      },
      unitMap: {
        1: 'GB',
        0: 'MB'
      },
      intranetTypeOptions: {
        0: this.$t('pages.truthIpAndPort'),
        1: this.$t('pages.typeless')
      }
    }
  },
  computed: {

  },
  watch: {
  },
  activated() {
  },
  created() {
  },
  methods: {
    show(row) {
      this.handleUpdate(row)
      this.detailVisible = true
      // 进入详细信息后，选中双击节点的编号
      // const devId = row.devId
      this.isServerDetail = true
      this.$nextTick(() => {
        this.$refs['ServerDetail'].query2.devId = row.devId + ''// 编号
      })
    },
    // 服务器信息
    serverInfo(data) {
      Object.assign(this.temp, { 'onlineStatus': data.onlineStatus })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // 格式化授权状态
    statusTableFilter(data) {
      const statusMap = {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      }
      return statusMap[data]
    },
    // 格式化智能备份组
    groupFormatter: function(groupId) {
      // const groupId = row.groupId;
      if (groupId === 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.groupTreeSelectData, groupId)
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    async handleUpdate(row) {
      this.resetTemp()
      this.sysmgrDbIdIsNull = false
      this.isSetAdminDbAuth = false
      //  若内网类型为0，即Ip地址和端口为实际IP地址和端口时，仅支持选择网卡中的IP信息，且端口不支持修改，为1时，端口和IP地址都可修改
      // this.getServerNetworkInfoList(row.devId);
      // await this.loadDbServer();
      this.temp = Object.assign(this.temp, row) // copy obj
      //  主数据库是否为空
      this.sysmgrDbIdIsNull = row.extraInfo ? !row.extraInfo.sysmgrDbId : true
      //  设置主数据库Id
      this.temp.sysmgrDbId = row.extraInfo ? row.extraInfo.sysmgrDbId || null : null

      if (this.temp.maxConnections && this.temp.maxConnections === -1) {
        this.temp.maxConnections = undefined
      }
      if (this.temp.maxConnections) this.temp.isSetMax = 1

      //  若内网类型为null，表示该设备属于蓝盾版本之前升上来但未连接的设备，默认设置成1
      if (this.temp.intranetType === undefined || this.temp.intranetType === null) {
        this.temp.intranetType = 1
      }

      this.tabName = 'serverTab'
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },
    // 关闭弹窗前，校验数据是否发生变化，并提示
    beforeClose(done) {
      this.isServerDetail = false
      done()
    },
    // 点击确认提交数据
    async handleSaveConfig() {
    }
  }
}
</script>
<style lang="scss" scoped>
.show-detail-panel {
  color: #666;
  display: flex;
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
