<template>
  <div>
    <Form ref="serverForm" :model="temp" label-position="right" label-width="135px" style="width: 640px;">
      <el-divider content-position="left"> {{ $t('pages.intelligentBackupServerMessage') }} </el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.serverName') + '：'">{{ temp.name }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.deviceNum') + '：'">{{ temp.devId }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetAddress') + '：'">{{ temp.intranetIp }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetPort') + '：'">{{ temp.intranetPort }}</FormItem>
        </el-col>
      </el-row>
      <el-divider content-position="left">{{ $t('pages.intelligentBackupServerBindServer') }}</el-divider>
      <FormItem :label="$t('pages.BackupServer')">
        <el-select v-model="temp.backupIds" :disabled="true" multiple :placeholder="$t('text.select')">
          <el-option v-for="item in backupServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
      <FormItem :label="$t('pages.DBServer')">
        <el-select v-model="temp.dbIds" multiple :disabled="temp.dbReadOnly" :placeholder="$t('text.select')">
          <el-option v-for="item in dbServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
        <span v-if="temp.dbReadOnly" style="color: #68a8d0;font-size: 12px;">{{ $t('pages.currentSmartServerUseServer',{content:$t('route.DBServer')}) }}</span>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import { deepMerge, equalsObj } from '@/utils'
import { bindServer, getBindServer } from '@/api/system/deviceManage/smartBackupServer'

export default {
  name: 'SmartBindServerFrom',
  props: {
    dbServer: {
      type: Array,
      default: null
    },
    backupServer: {
      type: Array,
      default: null
    },
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      temp: {},
      oldTemp: {},
      noChangeTemp: {}
    }
  },
  computed: {
  },
  watch: {

  },
  created() {

  },
  activated() {
  },
  methods: {
    setFormData(data) {
      this.resetTemp()
      this.temp = deepMerge({
        dbIds: [],
        backupIds: []
      }, data)
      this.oldTemp = deepMerge({}, this.temp)
      getBindServer(data.devId).then(resp => {
        if (resp.data) {
          this.temp = deepMerge({
            dbIds: resp.data.dbIds,
            backupIds: resp.data.backupIds
          }, resp.data.server)
          this.oldTemp = deepMerge({}, this.temp)
          this.noChangeTemp = deepMerge({}, this.temp)
          if (resp.data.groupBind) {
            if (resp.data.groupBind.dbIds && resp.data.groupBind.dbIds.length > 0) {
              this.temp.oldDbIds = this.temp.dbIds
              this.temp.dbIds = resp.data.groupBind.dbIds
              this.temp.dbReadOnly = true
            }
          }
        }
      })
    },
    isFormDataChange() {
      const obj1 = this.getFormData(this.temp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData) {
      return {
        devId: tempData.devId,
        dbServers: tempData.dbReadOnly ? tempData.oldDbIds : tempData.dbIds,
        backServers: tempData.backupIds
      }
    },
    submitData(callback) {
      if (!this.temp.dbIds) {
        return;
      }
      bindServer(this.getFormData(this.temp)).then(() => {
        callback()
        this.oldTemp = deepMerge({}, this.temp)
      }).catch((e) => {
        callback({ valid: false })
        console.log('提交失败！{}', e)
      })
    },
    resetTemp() {
      this.temp = {
        dbIds: []
      }
    },
    toOptionByServer(data) {
      let label = data.name;
      const { devId, intranetIpv6, internetIpv6, intranetIp, internetIp, intranetPort, internetPort } = data
      const ip = intranetIpv6 || internetIpv6 || intranetIp || internetIp
      if (ip) {
        const port = (intranetIpv6 || intranetIp) ? intranetPort : internetPort
        label += ` (${devId}_${ip}_${port})`
      }
      return label
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
