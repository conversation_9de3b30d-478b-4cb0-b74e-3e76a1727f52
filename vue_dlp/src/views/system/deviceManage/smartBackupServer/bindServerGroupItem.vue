<template>
  <Form ref="serverForm" :model="temp" :rules="rules" label-position="right" label-width="150px" style="width: 640px;">
    <el-divider content-position="left">{{ $t('pages.intelligentBackupServerGroupMessage') }}</el-divider>
    <FormItem :label="$t('table.groupName') + '：'">{{ temp.name }}</FormItem>
    <el-divider content-position="left">{{ $t('pages.intelligentBackupServerGroupBindServer') }}</el-divider>
    <FormItem :label="$t('pages.server_daq')" :tooltip-content="$t('pages.bindGatherGroupInfo')" tooltip-placement="bottom-start">
      <el-select v-model="temp.daqIds" multiple :placeholder="$t('text.select')" @change="changeValue($event)">
        <el-option v-for="item in daqServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
      </el-select>
    </FormItem>
    <FormItem :label="$t('pages.DBServer')">
      <el-select v-model="temp.dbIds" multiple :placeholder="$t('text.select')" @change="changeValue2">
        <el-option v-for="item in dbServer" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
      </el-select>
    </FormItem>
  </Form>
</template>

<script>
import { deepMerge, equalsObj } from '@/utils'
import {
  updateSmartGroupBindServer,
  getSmartGroupRelatedDaqGroup,
  getSmartGroupRelatedServer
} from '@/api/system/deviceManage/smartBackupServer'

export default {
  name: 'SmartBindServerGroupFrom',
  props: {
    daqServer: {
      type: Array,
      default: null
    },
    dbServer: {
      type: Array,
      default: null
    },
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      masterAble: false,
      temp: {},
      oldTemp: {},
      newTemp: {},
      bindDaqGroupNames: [], // 智能备份服务器分组绑定的采集组名称集合
      nochangeTemp: {},
      rules: {}
    }
  },
  methods: {
    changeValue() {
      const oldOptions = [...this.temp.daqIds];
      let groupDeleted = false
      let deletedGroupName = ''
      for (let i = 0; i < this.bindDaqGroupNames.length; i++) {
        let deleted = true
        for (let j = 0; j < oldOptions.length; j++) {
          if (this.bindDaqGroupNames[i] == oldOptions[j]) {
            // 选项中存在该分组名，表示改分组没有移除
            deleted = false
          }
        }
        if (deleted) {
          groupDeleted = true
          deletedGroupName = this.bindDaqGroupNames[i]
        }
      }

      if (groupDeleted) {
        const position = this.nochangeTemp.daqIds.indexOf(deletedGroupName)
        this.temp.daqIds.splice(position, 0, deletedGroupName);
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.unableRemoveGatherGroup'),
          type: 'warning',
          duration: 1000
        })
        return
      }
      const storeTemp = []
      for (let k = 0; k < this.temp.daqIds.length; k++) {
        if (!this.bindDaqGroupNames.includes(this.temp.daqIds[k])) {
          storeTemp.push(this.temp.daqIds[k])
        }
      }
      this.newTemp.daqIds = []
      for (let p = 0; p < storeTemp.length; p++) {
        this.newTemp.daqIds[p] = storeTemp[p]
      }
      this.nochangeTemp.daqIds = []
      for (let q = 0; q < this.temp.daqIds.length; q++) {
        this.nochangeTemp.daqIds[q] = this.temp.daqIds[q]
      }
    },
    changeValue2() {
      this.newTemp.dbIds = []
      for (let p = 0; p < this.temp.dbIds.length; p++) {
        this.newTemp.dbIds[p] = this.temp.dbIds[p]
      }
      this.nochangeTemp.dbIds = []
      for (let q = 0; q < this.temp.dbIds.length; q++) {
        this.nochangeTemp.dbIds[q] = this.temp.dbIds[q]
      }
    },
    setFormData(data) {
      this.resetTemp()
      this.temp = deepMerge({}, data)
      this.oldTemp = deepMerge({}, this.temp)
      this.newTemp = deepMerge({}, this.temp)
      getSmartGroupRelatedDaqGroup(data.id).then(resp => {
        if (resp.data) {
          this.bindDaqGroupNames = resp.data
          getSmartGroupRelatedServer(data.id).then(resp => {
            if (resp.data) {
              this.temp = deepMerge({
                daqIds: resp.data.daqIds,
                dbIds: resp.data.dbIds
              }, this.temp)
              this.oldTemp = deepMerge({}, this.temp)
              this.newTemp = deepMerge({}, this.temp)
              let k = this.temp.daqIds.length
              for (let i = 0; i < this.bindDaqGroupNames.length; i++) {
                this.temp.daqIds[k] = this.bindDaqGroupNames[i]
                k++
              }
              this.nochangeTemp = deepMerge({}, this.temp)
            }
          })
        }
      })
    },
    isFormDataChange() {
      const obj1 = this.getFormData(this.newTemp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData) {
      return {
        groupId: tempData.id,
        devType: 186,
        daqServers: tempData.daqIds,
        dbServers: tempData.dbIds
      }
    },
    submitData(callback) {
      if (!this.newTemp.daqIds) {
        return
      }
      updateSmartGroupBindServer(this.getFormData(this.newTemp)).then(() => {
        callback()
        this.oldTemp = deepMerge({}, this.newTemp)
      }).catch((e) => {
        callback({ valid: false })
      })
    },
    resetTemp() {
      this.temp = {
        daqIds: [],
        dbIds: []
      }
      this.newTemp = {
        daqIds: [],
        dbIds: []
      }
    },
    toOptionByServer(data) {
      let label = data.name;
      const { devId, intranetIpv6, internetIpv6, intranetIp, internetIp, intranetPort, internetPort } = data
      const ip = intranetIpv6 || internetIpv6 || intranetIp || internetIp
      if (ip) {
        const port = (intranetIpv6 || intranetIp) ? intranetPort : internetPort
        label += ` (${devId}_${ip}_${port})`
      }
      return label
    }
  }
}
</script>

<style scoped>

</style>
