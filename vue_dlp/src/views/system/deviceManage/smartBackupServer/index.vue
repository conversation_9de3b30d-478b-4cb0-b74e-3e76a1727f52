<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :default-expand-all="true"
        resizeable
        :render-content="renderContent"
        @node-click="handleNodeClick"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button v-if="false" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('pages.createDevice') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delDevice') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleDeviceMgr">
          {{ $t('pages.deviceMgr') }}
        </el-button>
        <el-button v-if="false" icon="el-icon-plus" size="mini" :disabled="!bindFileServerAble" @click="handleFileServer">
          新增文件服务器
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.serverName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.deviceNum')" prop="devId">
                <el-input v-model.number="query.devId" v-trim clearable :maxlength="4" :placeholder="this.$t('pages.serverLibrary_text5')" @input="number('devId')"/>
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV4Address')" prop="intranetIp">
                <el-input v-model="query.intranetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetIPV6Address')" prop="intranetIpv6">
                <el-input v-model="query.intranetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.intranetPort')" prop="intranetPort">
                <el-input v-model.number="query.intranetPort" v-trim clearable maxlength="5" :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('intranetPort')"/>
              </FormItem>
              <FormItem :label="$t('pages.internetIPV4Address')" prop="internetIp">
                <el-input v-model="query.internetIp" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetIPV6Address')" prop="internetIpv6">
                <el-input v-model="query.internetIpv6" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('pages.internetPort')" prop="internetPort">
                <el-input v-model.number="query.internetPort" v-trim clearable maxlength="5" :placeholder="this.$t('pages.serverLibrary_text6')" @input="number('internetPort')"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="serverTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <edit-smart-backup-server ref="editSmartBackupServer" :group-tree-select-data="getTreeDataExceptRoot()" @submitEnd="handleFilter"/>
    <detail-smart-backup-server ref="detailSmartBackupServer" :group-tree-select-data="getTreeDataExceptRoot()" ></detail-smart-backup-server>
    <server-mgr-dlg ref="serverMgrDlg" :default-expanded-keys="['-7']" @submitEnd="handleFresh"/>
    <edit-file-server ref="editFileServer" @submitEnd="handleFresh"/>
    <file-server ref="fileServer" @submitEnd="handleFresh"/>
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('table.intelligentBackupServerGroup')"
      :add-func="createSmartBackupServerGroup"
      :update-func="updateSmartBackupServerGroup"
      :delete-func="deleteSmartBackupServerGroup"
      :edit-valid-func="getSmartBackupServerGroupByName"
      @addEnd="createNodeEnd"
      @updateEnd="updateNodeEnd"
      @deleteEnd="removeNodeEnd"
    />
  </div>
</template>

<script>
import ServerMgrDlg from '../serverMgrDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import {
  getServerPage,
  deleteServer,
  getSmartBackupServerGroupTree,
  createSmartBackupServerGroup,
  updateSmartBackupServerGroup,
  deleteSmartBackupServerGroup,
  getSmartBackupServerGroupByName, existSmartBackupServer
} from '@/api/system/deviceManage/smartBackupServer'
import EditSmartBackupServer from '@/views/system/deviceManage/smartBackupServer/edit'
import EditFileServer from '@/views/system/deviceManage/backupServer/editBackupServer'
import FileServer from './fileServer'
import { getSmartBackupByFileServerId, updateAuthorizedStatus } from '@/api/system/deviceManage/serverAccessApproval';
import DetailSmartBackupServer from '@/views/system/deviceManage/smartBackupServer/detailSmartBackupServer.vue';

export default {
  name: 'SmartBackupServer',
  components: { DetailSmartBackupServer, EditSmartBackupServer, ServerMgrDlg, EditGroupDlg, EditFileServer, FileServer },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { prop: 'name', label: 'serverName', width: '120', fixed: true, sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: 'custom' },
        { label: '文件服务器', width: '120',
          buttons: [
            { disabledFormatter: (row) => { return !row.fileServerNames }, formatter: (row) => { return row.fileServerNames ? row.fileServerNames : '未绑定' }, click: this.showFileServer }
          ]
        },
        { prop: 'intranetIp', label: 'intranetIPV4Address', width: '140', sort: 'custom' },
        { prop: 'intranetIpv6', label: 'intranetIPV6Address', width: '150', sort: 'custom' },
        { prop: 'intranetPort', label: 'intranetPort', width: '100', sort: 'custom' },
        { prop: 'internetIp', label: 'internetIPV4Address', width: '140', sort: 'custom' },
        { prop: 'internetIpv6', label: 'internetIPV6Address', width: '150', sort: 'custom' },
        { prop: 'internetPort', label: 'internetPort', width: '100', sort: 'custom' },
        { prop: 'version', label: 'version', width: '130' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '260',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'detail', click: this.handleDetail },
            { label: 'grantAuthor', disabledFormatter: (row) => this.grantButtonFormatter(row, 1),
              click: (row) => this.handleAuthorizedStatus(row, 1) },
            { label: 'cancelGrantAuthor', disabledFormatter: (row) => this.grantButtonFormatter(row, 0),
              click: (row) => this.handleAuthorizedStatus(row, 0) }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        groupId: undefined,
        intranetIp: '',
        intranetIpv6: '',
        intranetPort: undefined,
        internetIp: '',
        internetIpv6: '',
        internetPort: undefined,
        devId: undefined
      },
      showTree: true,
      deleteable: false,
      treeData: [{ label: this.$t('table.intelligentBackupServerGroup'), id: 'G0', dataId: '0', children: [] }],
      addBtnAble: false,
      treeNodeType: 'G',
      bindFileServerAble: false,
      smartServer: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverTable']
    },
    filterKey() {
      return this.tempG.id
    },
    groupTree() {
      return this.$refs['groupTree']
    }
  },
  created() {
    this.getGroupTreeNode()
    if (this.$route.query.fileServerId) {
      this.toFileServer(this.$route.query.fileServerId)
    } else if (this.$route.query.name) {
      this.query.name = this.$route.query.name || ''
      this.$nextTick(() => {
        this.handleFilter()
      })
    }
  },
  activated() {
    this.getGroupTreeNode()
    if (this.$route.query.fileServerId) {
      this.toFileServer(this.$route.query.fileServerId)
    } else if (this.$route.query.name) {
      this.query.name = this.$route.query.name || ''
    }
    this.handleFilter()
  },
  methods: {
    createSmartBackupServerGroup,
    updateSmartBackupServerGroup,
    deleteSmartBackupServerGroup,
    getSmartBackupServerGroupByName,
    filterNodeMethod(value, data, node) {
      // 过滤节点及其子节点
      node.visible = node.key != this.filterKey && node.parent.visible
      return node.visible
    },
    parentIdSelectChange(data) {
      if (data && data !== this.tempG.id) {
        this.tempG.parentId = data
      }
    },
    terminalSelect() {
      return this.$refs['terminalGridSelect']
    },
    handleCreate() {
      this.$refs.editSmartBackupServer.handleCreate(this.query.groupId)
    },
    handleUpdate(row) {
      this.$refs.editSmartBackupServer.handleUpdate(row)
    },
    handleDetail(row) {
      this.$refs.detailSmartBackupServer.show(row)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getServerPage(searchQuery)
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
      this.bindFileServerAble = val.length === 1
      this.smartServer = val[0] ? val[0] : undefined;
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.intranetIp = ''
      this.query.intranetIpv6 = ''
      this.query.intranetPort = undefined
      this.query.internetIp = ''
      this.query.internetIpv6 = ''
      this.query.internetPort = undefined
      this.query.devId = undefined
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    },
    handleDeviceMgr() {
      this.$refs['serverMgrDlg'].show()
    },
    handleFresh() {
      this.gridTable.execRowDataApi()
    },
    getGroupTreeNode: function() {
      getSmartBackupServerGroupTree().then((respond) => {
        this.treeData = [{ label: this.$t('table.intelligentBackupServerGroup'), id: 'G0', dataId: '0', children: [] }]
        respond.data.forEach(el => {
          this.treeData[0].children.push(el)
        })
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    getTreeDataExceptRoot() {
      return this.treeData[0].children
    },
    renderContent(h, { node, data, store }) {
      const topShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!topShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={topShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={topShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.handleGroupDelete(data)}/>
          </span>
        </div>
      )
    },
    // 单击树节点的回调函数
    handleNodeClick(data, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      this.query.groupId = checkedNode.data.dataId == '0' ? null : checkedNode.data.dataId
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    handleGroupDelete(data) {
      existSmartBackupServer(data.dataId).then((respond) => {
        if (respond.data == 1) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.serverLibrary_text9'),
            type: 'warning',
            duration: 2000
          })
        } else if (respond.data == 0) {
          this.$refs['editGroupDlg'].handleDelete(data.dataId)
        }
      })
    },
    createNodeEnd(data) {
      this.groupTree.addNode(this.dataToTreeNode(data))
    },
    updateNodeEnd(data) {
      this.groupTree.updateNode(this.dataToTreeNode(data))
    },
    removeNodeEnd(dataId) {
      const nodeData = this.groupTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
      this.query.groupId = undefined
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    groupFormatter(row, data) {
      const groupId = row.groupId;
      if (groupId === 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.treeData, data)
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    showFileServer(row) {
      this.smartServer = row;
      if (row && row.fileServerIds) {
        this.$refs['fileServer'].show(row)
      }
    },
    handleFileServer() {
      this.$refs['editFileServer'].handleSmartCreate(this.smartServer.devId)
    },
    /**
     * 由服务器接入审批-已接入设备 跳转到  智能备份服务器，展示对应的文件服务器
     * @param fileDevId
     * @returns {Promise<void>}
     */
    async toFileServer(fileDevId) {
      if (fileDevId) {
        //  获取绑定该文件服务器的智能备份服务器信息
        const res = await getSmartBackupByFileServerId(fileDevId);
        const server = res.data
        if (server) {
          this.query.name = server.name
          this.handleFilter()
          this.showFileServer(server);
        }
      }
    },
    grantButtonFormatter: function(row, type) {
      return row.adminAuthorized == type
    },
    handleAuthorizedStatus(row, status) {
      updateAuthorizedStatus({ devId: row.devId, devType: 186, authorizedStatus: status }).then(respond => {
        row.authorizedStatus = status
        row.adminAuthorized = status
        this.gridTable.updateRowData(row)
        this.$notify({
          title: this.$t('text.success'),
          message: status ? this.$t('pages.collectService_notifyMsg19') : this.$t('pages.collectService_notifyMsg20'),
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card{
  position: relative;
  >>>.el-card__header{
    padding: 10px 20px;
  }
}
.btn-box{
  position: absolute;
  top: 5px;
  right: 20px;
}

</style>
