<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="绑定的文件服务器"
      :visible.sync="dlgVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <el-button v-if="addAble" size="small" @click="handleCreate()">
        {{ $t('button.insert') }}
      </el-button>
      <el-button size="small" :disabled="!deleteAble" @click="handleDelete()">
        {{ $t('button.delete') }}
      </el-button>
      <grid-table
        ref="fileServerTable"
        :col-model="colModel"
        :height="300"
        :row-data-api="rowDataApi"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        @selectionChangeEnd="handleSelectionChange"
      />
    </el-dialog>
    <edit-file-server ref="editFileServer" @submitEnd="handleFreshAndParent"/>
  </div>
</template>

<script>
import { deleteServer } from '@/api/system/deviceManage/backupServer'
import { getFileServerPage } from '@/api/system/deviceManage/smartBackupServer'
import EditFileServer from '@/views/system/deviceManage/backupServer/editBackupServer'

export default {
  name: 'FileServer',
  components: { EditFileServer },
  props: {
    addAble: { type: Boolean, default: false }
  },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'id', order: 'asc' },
      deleteAble: false,
      dlgVisible: false,
      colModel: [
        { label: 'type', fixedWidth: '50', fixed: true, iconFormatter: this.isBranch },
        { prop: 'name', label: 'serverName', width: '120', fixed: true, sort: 'custom' },
        { prop: 'devId', label: 'deviceNum', width: '100', sort: 'custom' },
        { prop: 'intranetIp', label: this.$t('pages.intranetIPV4Address'), width: '140', sort: 'custom' },
        { prop: 'intranetIpv6', label: this.$t('pages.intranetIPV6Address'), width: '150', sort: 'custom' },
        { prop: 'intranetPort', label: 'intranetPort', width: '100', sort: 'custom' },
        { prop: 'internetIp', label: this.$t('pages.internetIPV4Address'), width: '120', sort: 'custom' },
        { prop: 'internetIpv6', label: this.$t('pages.internetIPV6Address'), width: '150', sort: 'custom' },
        { prop: 'internetPort', label: 'internetPort', width: '100', sort: 'custom' },
        { prop: 'headquartersName', label: 'mappingServer', width: '120', formatter: this.headquartersNameFormatter },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        groupId: undefined,
        intranetIp: '',
        intranetIpv6: '',
        intranetPort: undefined,
        internetIp: '',
        internetIpv6: '',
        internetPort: undefined,
        devId: undefined,
        devIds: undefined
      },
      smartServerId: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['fileServerTable']
    }
  },
  watch: {},
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getFileServerPage(searchQuery)
    },
    handleSelectionChange(val) {
      this.deleteAble = val.length > 0
    },
    handleFresh() {
      this.$nextTick(() => {
        this.gridTable.execRowDataApi()
      })
    },
    handleFreshAndParent() {
      this.$nextTick(() => {
        this.gridTable.execRowDataApi()
        this.$emit('submitEnd')
      })
    },
    handleCreate() {
      this.$refs.editFileServer.handleSmartCreate(this.smartServerId)
    },
    handleUpdate(row) {
      this.$refs.editFileServer.handleSmartUpdate(row)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateFile_4'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteServer({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.$emit('submitEnd')
        })
      }).catch(() => {})
    },
    show(val) {
      this.smartServerId = val.devId
      this.$nextTick(() => {
        this.dlgVisible = true
        this.query.devId = this.smartServerId
        this.handleFresh();
      })
    },
    handleDrag() {},
    isBranch: function(row) {
      const online = row.onlineStatus == 1
      const style = online ? 'color: green' : 'color: #888'
      const labelPre = (online ? this.$t('pages.online') : this.$t('pages.offline')) + '\r\n'
      return row.isBranch ? { class: 'branch', title: labelPre + this.$t('pages.branchServer'), style } : row.isHeadquarters ? { class: 'controller', title: labelPre + this.$t('pages.headquartersServer'), style } : { class: 'server', title: labelPre + this.$t('pages.commonServer'), style }
    },
    headquartersNameFormatter: function(row) {
      return row.headquartersName ? row.headquartersName : this.$t('pages.null')
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
