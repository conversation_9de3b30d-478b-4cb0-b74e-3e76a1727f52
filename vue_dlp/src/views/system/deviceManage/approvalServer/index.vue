<template>
  <div class="app-container">
    <Form class="server-form" label-width="180px" label-position="right">
      <el-divider class="server-label" content-position="left">{{ $t('pages.intranet_config') }}:</el-divider>
      <FormItem :label="$t('pages.intranetIpv4')">
        <el-input v-model="serverList.intranetIp" v-trim style="width:200px" :maxlength="64"/>
      </FormItem>
      <FormItem :label="$t('pages.intranetIpv6')">
        <el-input v-model="serverList.intranetIpv6" v-trim style="width:200px" :maxlength="64"/>
      </FormItem>
      <FormItem :label="$t('pages.intranetSsl')" prop="intranetSsl">
        <el-switch v-model="serverList.intranetSsl" :active-value="1" :inactive-value="0" />
      </FormItem>
      <FormItem :label="$t('pages.intranetPort')">
        <el-input v-model="serverList.intranetPort" v-trim style="width:80px" maxlength="11"/>
        <el-button type="primary" size="mini" :loading="loading1" :disabled="!serverList.intranetIp && !serverList.intranetIpv6 ||!serverList.intranetPort" @click="testAddress(1)">{{ $t('button.test') }}</el-button>
      </FormItem>
      <el-divider class="server-label" content-position="left">{{ $t('pages.internet_config') }}:</el-divider>
      <FormItem :label="$t('pages.internetIpv4')">
        <el-input v-model="serverList.internetIp" v-trim style="width:200px" :maxlength="64"/>
      </FormItem>
      <FormItem :label="$t('pages.internetIpv6')">
        <el-input v-model="serverList.internetIpv6" v-trim style="width:200px" :maxlength="64"/>
      </FormItem>
      <FormItem prop="internetSsl" :label="$t('pages.internetSsl')">
        <el-switch v-model="serverList.internetSsl" :active-value="1" :inactive-value="0" />
      </FormItem>
      <FormItem :label="$t('pages.internetPort')">
        <el-input v-model="serverList.internetPort" v-trim style="width:80px" maxlength="11"/>
        <el-button type="primary" size="mini" :loading="loading2" :disabled="!serverList.internetIp && !serverList.internetIpv6 || !serverList.internetPort" @click="testAddress(2)">{{ $t('button.test') }}</el-button>
      </FormItem>
      <FormItem :label="$t('pages.version')">
        <span v-if="serverList.version">{{ serverList.version }}</span>
        <span v-if="!serverList.version">未知</span>
        <!-- <el-input v-model="serverList.version" v-trim style="width:200px" readonly :maxlength="64"/> -->
      </FormItem>
      <div class="server-footer">
        <div class="btn-group">
          <el-button style="margin-left:0px" type="primary" size="mini" @click="showQrcode"> {{ $t('pages.approvalServerQrcode') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.qrTip') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-button>
          <el-button type="primary" size="mini" :loading="loading" @click="bindBuackupServer">{{ $t('button.assignFileServer') }}</el-button>
          <el-button type="primary" size="mini" :loading="loading" @click="updateAddress">{{ $t('button.save') }}</el-button>
          <el-button type="primary" size="mini" :loading="loading" @click="handleDetail">{{ $t('table.detail') }}</el-button>
        </div>
      </div>
    </Form>
    <detail-approval-server ref="detailApprovalServer"></detail-approval-server>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.approvalServerQrcode')"
      :visible.sync="qrCodeVisible"
      width="600px"
      :lock-scroll="false"
      :destroy-on-close="true"
      @dragDialog="handleDrag"
    >
      <div id="qrcode" ref="qrCodeUrl" style="text-align: center" class="img">
        <vue-qr
          :text="serverJson"
          :correct-level="3"
          :size="380"
          :margin="10"
          color-dark="#000000"
          color-light="white"
          background-color="white"
          background-dimming="white"
          :logo-src="logoSrc"
          :logo-scale=".2"
          :logo-margin="5"
          logo-background-color="white"
        >
        </vue-qr>
        <span style="color: #0e7beb; margin: 5px 0; display: inline-block;">{{ $t('pages.qrTip') }}</span>
        <el-button type="primary" size="medium" style="width:400px;padding:14px" class="nowBtn" @click="downs">{{ $t('pages.DownloadQrcode') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.assignFileServer')"
      :visible.sync="dialogBackupServerVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="serverForm" label-position="right" label-width="100px" style="width: 730px;">
        <grid-select
          ref="backupServerGridSelect"
          :height="400"
          :col-model="backupServerColModel"
          searchable
          :search-prop="{ key: 'searchInfo', label: this.$t('pages.fileServiceName').toLocaleLowerCase() }"
          :select-row-data-api="selectBackupServerRowDataApi"
          :selected-row-data-api="selectedBackupServerTerminalRowDataApi"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="bindBackupServer()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogBackupServerVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAddress, testConnect, updateAddress, allotBackupServer } from '@/api/system/configManage/approvalConfig.js'
import { getServerPage, listSelectedBackupServer } from '@/api/system/deviceManage/backupServer'
import GridSelect from '@/components/GridSelect'
import vueQr from 'vue-qr'
import DetailApprovalServer from '@/views/system/deviceManage/approvalServer/detailApprovalServer.vue';

export default {
  name: 'ApprovalServer',
  components: { DetailApprovalServer, GridSelect, vueQr },
  data() {
    return {
      serverList: {},
      defaultServerList: {
        intranetIp: '', // 内网地址 用于判断二维码信息是否有包含内网地址
        intranetIpv6: '',
        internetIp: '',
        internetIpv6: '',
        intranetPort: '',
        internetPort: '',
        intranetSsl: 0,
        internetSsl: 0
      },
      loading: false,
      loading1: false, // 内网Ipv4测试按钮加载
      loading2: false, // 外网Ipv4测试按钮加载
      dialogBackupServerVisible: false,
      qrCodeVisible: false,
      submitting: false,
      intranetIp: '', // 内网地址 用于判断二维码信息是否有包含内网地址
      intranetIpv6: '',
      internetIp: '',
      internetIpv6: '',
      serverJson: '',
      backupServerColModel: [
        { prop: 'name', label: 'fileServiceName', width: '100', sort: true }
      ],
      // 用于记录未分配之前审批服务器所分配的文件服务器
      selectedBackupServerNames: []
    }
  },
  computed: {
    logoSrc() {
      return this.$store.getters.sysResources.loginLogo
    }
  },
  watch: {
    '$store.state.commonData.notice.selectedDepartment'(val) {
      this.getAddress()
    }
  },
  mounted() {
  },
  created() {
    this.getAddress()
  },
  methods: {
    backupServerSelect() {
      return this.$refs['backupServerGridSelect']
    },
    selectBackupServerRowDataApi(option) {
      return getServerPage(option)
    },
    async selectedBackupServerTerminalRowDataApi(option) {
      const selectedBackupServers = await listSelectedBackupServer(this.serverList.devId)
      this.selectedBackupServerNames = selectedBackupServers.data.map(item => item.name)
      return selectedBackupServers
    },
    bindBackupServer() {
      const selectedDatas = this.backupServerSelect().getSelectedDatas()
      const selectedServerIds = []
      const selectedServerNames = []
      selectedDatas.forEach(data => {
        selectedServerIds.push(data.devId)
        selectedServerNames.push(data.name)
      })
      allotBackupServer({ devId: this.serverList.devId,
        name: this.serverList.name,
        backServerIds: selectedServerIds,
        backupServerNames: this.selectedBackupServerNames,
        backupServerNames_new: selectedServerNames }).then(() => {
        this.dialogBackupServerVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.fileServerAllocationSucceeded'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    testAddress(type) {
      if (type === 1) {
        this.loading1 = true
      } else if (type === 2) {
        this.loading2 = true
      }
      let ip
      let ipv6
      let port
      let ssl
      if (type === 1) {
        ip = this.serverList.intranetIp
        ipv6 = this.serverList.intranetIpv6
        port = this.serverList.intranetPort
        ssl = this.serverList.intranetSsl
      } else if (type === 2) {
        ip = this.serverList.internetIp
        ipv6 = this.serverList.internetIpv6
        port = this.serverList.internetPort
        ssl = this.serverList.internetSsl
      }
      testConnect({ ip, ipv6, port, ssl }).then(res => {
        this.$message({
          message: res.data,
          type: 'success'
        })
        if (type === 1) {
          this.loading1 = false
        } else if (type === 2) {
          this.loading2 = false
        }
      }).catch(err => {
        console.log(err)
        if (type === 1) {
          this.loading1 = false
        } else if (type === 2) {
          this.loading2 = false
        }
      })
    },
    updateAddress() {
      this.loading = true
      updateAddress(this.serverList).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.getAddress()
        this.loading = false
      }).catch(err => {
        console.log(err)
        this.loading = false
      })
    },
    handleDetail() {
      this.$refs.detailApprovalServer.show(this.serverList)
    },
    getAddress() {
      getAddress().then(res => {
        this.serverList = !res.data ? {} : res.data
        this.serverJson = this.serverList.serverJson
        this.internetIp = this.serverList.internetIp
        this.internetIpv6 = this.serverList.internetIpv6
        this.intranetIp = this.serverList.intranetIp
        this.intranetIpv6 = this.serverList.intranetIpv6
        // console.log('serverjson', this.serverJson)
        // this.creatQrCode()
      })
    },
    bindBuackupServer() {
      this.dialogBackupServerVisible = true
      if (this.backupServerSelect()) {
        this.backupServerSelect().clearFilter()
        this.backupServerSelect().reload()
      }
    },
    showQrcode() {
      if (!this.internetIp && !this.intranetIp && !this.intranetIpv6 && !this.internetIpv6) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.approvalServerAddressMsg'),
          type: 'warning',
          duration: 3000
        })
      } else {
        this.qrCodeVisible = true
      }
    },
    downs() {
      const fileName = this.$t('pages.approvalServerQrcode') + '.png'
      const img = document.getElementById('qrcode').getElementsByTagName('img')[0].src

      const aLink = document.createElement('a')
      const blob = this.base64ToBlob(img)
      const evt = document.createEvent('HTMLEvents')
      evt.initEvent('click', true, true) // initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = fileName
      aLink.href = URL.createObjectURL(blob);
      // aLink.dispatchEvent(evt);
      aLink.click()
    },
    // base64转blob
    base64ToBlob(code) {
      const parts = code.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], { type: contentType })
    }
  }
}
</script>

<style lang="scss" scoped>
  .server-form{
    height: 100%;
    padding-left: 50px;
    margin: 20px 0 0 20px;
    overflow: auto;
    font-size: 14px;
    .server-label {
      display: inline-block;
      width: 40%;
    }
    .server-footer {
      margin-bottom: 25px;
    }
    .el-button{
      min-width: 80px;
      margin-left: 40px;
    }
  }
  .btn-group {
    margin-top: 20px;
    .el-button {
      width: auto;
    }
  }
  >>>.el-dialog__body{
    max-height: 520px;
  }
  >>>.el-divider__text {
    font-size: 15px;
    font-weight: 700;
    color: #eee;
    background: #0c161f;
  }
</style>
