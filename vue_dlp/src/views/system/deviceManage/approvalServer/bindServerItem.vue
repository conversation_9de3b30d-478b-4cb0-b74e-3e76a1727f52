<template>
  <div>
    <Form ref="serverForm" :model="temp" label-position="right" label-width="135px" style="width: 640px;">
      <el-divider content-position="left"> {{ $t('pages.approvalServerInfo') }} </el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.serverName') + '：'">{{ temp.name }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.deviceNum') + '：'">{{ temp.devId }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetAddress') + '：'">{{ temp.intranetIp }}</FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.intranetPort') + '：'">{{ temp.intranetPort }}</FormItem>
        </el-col>
      </el-row>
      <el-divider content-position="left">
        {{ $t('pages.approvalServerBindServer') }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.approvalServerBindServerDescription') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </el-divider>
      <FormItem :label="$t('pages.BackupServer') + '：'">
        <el-select v-model="temp.devIds" multiple :placeholder="$t('text.select')">
          <el-option v-for="item in backupServer.filter(server => !server.hidden)" :key="item.devId" :label="toOptionByServer(item)" :value="item.devId"/>
        </el-select>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import { deepMerge, equalsObj } from '@/utils'
import { bindServer, getBindServer } from '@/api/system/configManage/approvalConfig'

export default {
  name: 'ApprovalBindServerFrom',
  props: {
    backupServer: {
      type: Array,
      default: null
    },
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      masterSubsectionAble: false,
      masterAble: false,
      temp: {},
      oldTemp: {}
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  activated() {
  },
  methods: {
    setFormData(data) {
      this.resetTemp()
      this.temp = deepMerge({
        devIds: []
      }, data)
      this.oldTemp = deepMerge({}, this.temp)
      getBindServer(data.devId).then(resp => {
        if (resp.data) {
          this.temp = deepMerge({
            devIds: resp.data.backIds
          }, resp.data.server)
          this.oldTemp = deepMerge({}, this.temp)
        }
      })
    },
    isFormDataChange() {
      const obj1 = this.getFormData(this.temp)
      const obj2 = this.getFormData(this.oldTemp)
      return !equalsObj(obj1, obj2);
    },
    getFormData(tempData) {
      return {
        devType: tempData.devType,
        devId: tempData.devId,
        backServers: tempData.devIds
      }
    },
    submitData(callback) {
      if (!this.temp.devIds) {
        return;
      }
      bindServer(this.getFormData(this.temp)).then(() => {
        callback()
        this.oldTemp = deepMerge({}, this.temp)
      })
    },
    resetTemp() {
      this.temp = {
        devIds: []
      }
    },
    toOptionByServer(data) {
      let label = data.name;
      if (data.intranetIpv6) {
        label += ' (' + data.devId + '_' + data.intranetIpv6 + ':' + data.intranetPort + ')'
      } else if (data.internetIpv6) {
        label += ' (' + data.devId + '_' + data.internetIpv6 + ':' + data.internetPort + ')'
      } else if (data.intranetIp) {
        label += ' (' + data.devId + '_' + data.intranetIp + ':' + data.intranetPort + ')'
      } else if (data.internetIp) {
        label += ' (' + data.devId + '_' + data.internetIp + ':' + data.internetPort + ')'
      }
      return label
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
