<template>
  <div>
    <Drawer
      :title="$t('table.detail')"
      :visible.sync="detailVisible"
      status="update"
      :with-footer="false"
      :size="'60%'"
      wrapper-closable
      append-to-body
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
    >
      <div class="app-container show-detail-panel" >
        <div style="flex: 1">
          <el-descriptions class="margin-top" :title="$t('pages.basicInformation')" :column="3" size="small" border>
            <el-descriptions-item :label="$t('pages.serverName')">
              {{ temp.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.deviceNum')">
              {{ temp.devId }}
            </el-descriptions-item>

            <!--            内外网+端口-->
            <el-descriptions-item :label="$t('pages.intranetIPV4Address')">
              {{ temp.intranetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetIPV6Address')">
              {{ temp.intranetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetSsl')">
              {{ sslOptions[temp.intranetSsl] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.intranetPort')">
              {{ temp.intranetPort }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV4Address')">
              {{ temp.internetIp }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetIPV6Address')">
              {{ temp.internetIpv6 }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetSsl')">
              {{ sslOptions[temp.internetSsl] }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.internetPort')">
              {{ temp.internetPort }}
            </el-descriptions-item>
            <!--            服务器状态-->
            <el-descriptions-item :label="$t('pages.versionNumber')">
              {{ temp.version }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.onlineStatus')">
              {{ temp.onlineStatus }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('pages.remark')">
              {{ temp.remark }}
            </el-descriptions-item>
          </el-descriptions>
          <server-detail-public v-if="isServerDetail" ref="ServerDetail" :dev-id="temp.devId" @serverInfo="serverInfo"/>
        </div>

      </div>
    </Drawer>
  </div>
</template>
<script>
import ServerDetailPublic from '@/views/system/deviceManage/serverInfo/serverDetailPublic/index.vue';

export default {
  name: 'DetailApprovalServer',
  components: { ServerDetailPublic },
  data() {
    return {
      detailVisible: false,
      submitting: false,                 // 提交数据中...
      isServerDetail: false, // 开启服务器详情组件
      groupTreeSelectData: [],
      treeSelectNode: [],
      defaultTreeSelectNode: [{ id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped') }],
      treeData: [],
      temp: {}, // 表单字段
      defaultTemp: {
        devId: '',
        intranetIp: '', // 内网地址 用于判断二维码信息是否有包含内网地址
        intranetIpv6: '',
        internetIp: '',
        internetIpv6: '',
        intranetPort: '',
        internetPort: '',
        intranetSsl: 0,
        internetSsl: 0,
        version: '',
        onlineStatus: ''    //   服务器在线状态
      },
      sslOptions: {
        1: this.$t('text.open'),
        0: this.$t('text.dontOpen1')
      }
    }
  },
  computed: {

  },
  watch: {
  },
  activated() {
  },
  created() {
  },
  methods: {
    show(row) {
      this.handleUpdate(row)
      this.detailVisible = true
      // 进入详细信息后，选中双击节点的编号
      const devId = row.devId
      console.log('ServerDetail devId', devId)
      this.isServerDetail = true
      this.$nextTick(() => {
        this.$refs['ServerDetail'].query2.devId = row.devId + ''// 编号
      })
    },
    // 服务器信息
    serverInfo(data) {
      console.log('serverInfo', data)
      this.temp = Object.assign({}, this.temp, {
        onlineStatus: data.onlineStatus
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      console.log('handleUpdate', this.temp)
    },
    // 格式化授权状态
    statusTableFilter(data) {
      const statusMap = {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      }
      return statusMap[data]
    },
    // 关闭弹窗前，校验数据是否发生变化，并提示
    beforeClose(done) {
      this.isServerDetail = false
      done()
    },
    // 点击确认提交数据
    async handleSaveConfig() {
    }
  }
}
</script>
<style lang="scss" scoped>
.show-detail-panel {
  color: #666;
  display: flex;
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
