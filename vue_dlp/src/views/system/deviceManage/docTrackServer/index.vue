<template>
  <div class="app-container">
    <Form :model="config" class="server-form" label-width="278px" label-position="right">
      <el-divider class="server-label" content-position="left">{{ $t('pages.basicConfig') }}:</el-divider>
      <FormItem :label="$t('pages.deviceNum')">
        <el-input value="201" readonly/>
      </FormItem>
      <FormItem v-if="showAccessPwd" :label="$t('pages.devPassword')" :error="accessPwdError">
        <el-input v-model="server.accessPwd" type="password" :maxlength="20" show-password @blur="handleAccessPwdBlur"/>
      </FormItem>
      <div v-for="(value, key) in config" :key="key">
        <el-divider class="server-label" content-position="left">{{ value.Label }}:</el-divider>
        <FormItem>
          <template slot="label">
            {{ $t('pages.connect_addr') }}<span>({{ $t('pages.IPv4OrDomain') }})</span>
          </template>
          <el-input v-model="value.Ip" v-trim :maxlength="64"/>
        </FormItem>
        <FormItem>
          <template slot="label">
            {{ $t('pages.connect_addr') }}<span>({{ $t('pages.IPv6OrDomain') }})</span>
          </template>
          <el-input v-model="value.Ipv6" v-trim :maxlength="64"/>
        </FormItem>
        <FormItem>
          <template slot="label">
            {{ $t('pages.tcp_port') }}<span>({{ $t('pages.docTrackServer_text1') }})</span>
          </template>
          <el-input-number v-model="value.Port" :min="0" :max="65535" step-strictly :controls="false"/>
          <el-button type="primary" size="mini" :loading="value.Loading" :disabled="!value.Ip && !value.Ipv6 || !value.Port" @click="testTcp(value)">
            {{ $t('button.test') }}
          </el-button>
          <!-- <el-button type="primary" size="mini" :loading="value.Loadingv6" :disabled="!value.Ipv6 || !value.Port" @click="testTcp(value,true)">
            测试IPv6
          </el-button> -->
        </FormItem>
        <FormItem>
          <template slot="label">
            {{ $t('pages.http_port') }}<span>({{ $t('pages.docTrackServer_text2') }})</span>
          </template>
          <el-input-number v-model="value.HttpPort" :min="0" :max="65535" step-strictly :controls="false"/>
          <el-button type="primary" size="mini" :loading="value.HttpLoading" :disabled="!value.Ip && !value.Ipv6 || !value.HttpPort" @click="testHttp(value)">
            {{ $t('button.test') }}
          </el-button>
          <!-- <el-button type="primary" size="mini" :loading="value.HttpLoadingv6" :disabled="!value.Ipv6 || !value.HttpPort" @click="testHttp(value,true)">
            测试IPv6
          </el-button> -->
        </FormItem>
      </div>
      <div class="server-footer">
        <el-button v-if="sysOptsSupported" size="mini" @click="$refs.sysOpts.show()">{{ $t('pages.serverConfig') }}</el-button>
        <el-button type="primary" size="mini" :disabled="saveDisabled" :loading="loading" @click="updateConfig">
          {{ $t('button.save') }}
        </el-button>
      </div>
    </Form>

    <sys-opts ref="sysOpts"/>
  </div>
</template>

<script>
import SysOpts from './sysOpts'
import { getServer, updateServer, testTcp, testHttp, isSysOptsSupported } from '@/api/system/deviceManage/docTrackServer'

export default {
  name: 'DocTrackServer',
  components: { SysOpts },
  data() {
    return {
      server: {},
      defaultServer: {
        devId: 201,
        accessPwd: 'dev-server_12345',
        intranetIp: undefined,
        internetIp: undefined,
        intranetIpv6: undefined,
        internetIpv6: undefined,
        intranetPort: 20281,
        internetPort: undefined,
        httpIntranetIp: undefined,
        httpInternetIp: undefined,
        httpIntranetIpv6: undefined,
        httpInternetIpv6: undefined,
        httpIntranetPort: 20280,
        httpInternetPort: undefined
      },
      config: {
        intranet: {
          Label: this.$t('pages.intranet_config'),
          Ip: undefined,
          Ipv6: undefined,
          Port: 20281,
          Loading: false,
          Loadingv6: false,
          HttpPort: 20280,
          HttpLoading: false,
          HttpLoadingv6: false
        },
        internet: {
          Label: this.$t('pages.internet_config'),
          Ip: undefined,
          Ipv6: undefined,
          Port: undefined,
          Loading: false,
          Loadingv6: false,
          HttpPort: undefined,
          HttpLoading: false,
          HttpLoadingv6: false
        }
      },
      showAccessPwd: false,
      accessPwdError: undefined,
      accessPwdReg: new RegExp('^(?=.*[0-9])(?=.*[a-zA-Z])([a-zA-Z0-9_\\-]{8,20})$'),
      domainReg: new RegExp('^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$'),
      loading: false,
      sysOptsSupported: false
    }
  },
  computed: {
    isEmptyConfig() {
      return Object.values(this.config)
        .map(item => !item.Ip && !item.Ipv6 && !item.Port && !item.HttpPort)
        .reduce((prev, curr) => prev && curr)
    },
    saveDisabled() {
      return !(this.isEmptyConfig ||
        ((this.config.intranet.Ip || this.config.intranet.Ipv6) && this.config.intranet.Port && this.config.intranet.HttpPort) ||
        ((this.config.internet.Ip || this.config.internet.Ipv6) && this.config.internet.Port && this.config.internet.HttpPort))
    }
  },
  created() {
    this.getConfig()
    this.getSysOptsSupported()
  },
  methods: {
    getConfig() {
      getServer().then(respond => {
        this.server = Object.assign({}, this.defaultServer, respond.data)
        const server = Object.assign({}, this.server)
        server.intranetHttpPort = server.httpIntranetPort
        server.internetHttpPort = server.httpInternetPort
        for (const key in server) {
          if (key.startsWith('intranet')) {
            this.config.intranet[key.substring(8)] = server[key]
          } else if (key.startsWith('internet')) {
            this.config.internet[key.substring(8)] = server[key]
          }
        }
      })
    },
    getSysOptsSupported() {
      isSysOptsSupported().then(res => {
        this.sysOptsSupported = !!res.data
      })
    },
    updateConfig() {
      if (this.showAccessPwd && !this.isEmptyConfig && (!this.server || !this.server.accessPwd || !this.accessPwdReg.test(this.server.accessPwd))) {
        this.accessPwdError = this.$t('pages.validateCollect_3')
        return
      }
      if (this.isEmptyConfig && (!this.server || !this.server.id)) {
        this.notifySaveSuccess()
        return
      }
      const server = {}
      for (const key1 in this.config) {
        for (const key2 in this.config[key1]) {
          server[key1 + key2] = this.config[key1][key2]
        }
      }
      for (const key in server) {
        if (this.server.hasOwnProperty(key)) {
          this.server[key] = server[key]
        }
      }
      this.server.httpIntranetIp = server.intranetIp
      this.server.httpInternetIp = server.internetIp
      this.server.httpIntranetIpv6 = server.intranetIpv6
      this.server.httpInternetIpv6 = server.internetIpv6
      this.server.httpIntranetPort = server.intranetHttpPort
      this.server.httpInternetPort = server.internetHttpPort
      this.loading = true
      const tempData = Object.assign({}, this.server, { encryptProps: ['accessPwd'] })
      updateServer(tempData).then(() => {
        this.notifySaveSuccess()
        this.getConfig()
        this.getSysOptsSupported()
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    notifySaveSuccess() {
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.saveSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    testTcp(addr) {
      addr.Loading = true
      testTcp(addr).then(respond => {
        this.handleTestResult(respond.data, true)
        addr.Loading = false
      }).catch(() => {
        addr.Loading = false
      })
    },
    testHttp(addr) {
      const requestObj = Object.assign({}, addr)
      addr.HttpLoading = true
      testHttp(requestObj).then(respond => {
        this.handleTestResult(respond.data, false)
        addr.HttpLoading = false
      }).catch(() => {
        addr.HttpLoading = false
      })
    },
    handleTestResult(result, isTcp) {
      if (result) {
        this.$notify({
          title: this.$t('text.success'),
          message: isTcp ? this.$t('pages.docTrackServer_text4') : this.$t('pages.server_connect_success'),
          type: 'success',
          duration: 2000
        })
      } else {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.server_connect_failure'),
          type: 'error',
          duration: 2000
        })
      }
    },
    handleAccessPwdBlur() {
      if (this.server.accessPwd && !this.accessPwdReg.test(this.server.accessPwd)) {
        this.accessPwdError = this.$t('pages.validateCollect_3')
      } else {
        this.accessPwdError = undefined
      }
    },
    isAddr(addr) {
      // 不校验格式了，域名不好校验
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
  .server-form {
    height: calc(100% - 60px);
    overflow: auto;
    font-size: 14px;
    padding-left: 50px;
    margin: 20px 0 0 20px;
    .server-label {
      display: inline-block;
      width: 40%;
    }
    .server-footer {
      position: absolute;
      left: 572px;
      bottom: 20px;
    }
    >>>.el-divider__text {
      font-size: 15px;
      font-weight: 700;
    }
    /*.el-form-item {*/
    /*  padding-left: 32px;*/
    /*}*/
    >>>.el-form-item__label {
      span {
        font-size: 12px;
        font-weight: 400;
        &:before {
          content: ' ';
        }
      }
    }
    .el-input, .el-input-number {
      width: 200px;
      >>>.el-input__inner {
        position: relative;
        text-align: left;
      }
    }
    .el-button {
      min-width: 90px;
      margin-left: 20px;
    }
  }
</style>
