<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.docTrackServerConfig')"
    :width="dialogWidth"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
  >
    <Form :model="temp" :class="lang" :label-width="`${labelWidth}px`" label-position="right" :extra-width="extraWidth">
      <FormItem class="expired-time-item" label-width="0" style="text-align: center;" :error="expiredTimeNumberError">
        <el-checkbox v-model="temp.expiredEnable" :true-label="1" :false-label="0" class="delete-log" @change="handleEnableChange">
          <i18n path="pages.docTrackServer_text11">
            <el-input
              slot="period"
              v-model="temp.expiredTimeNumber"
              :disabled="disabled"
              class="input-file-size"
              aria-required="true"
              @input="handleInputNumber"
              @blur="handleInputNumberBlur"
            >
              <el-select
                slot="append"
                v-model="temp.expiredTimeUnit"
                :disabled="disabled"
                :placeholder="$t('text.select')"
                @click.native.prevent
              >
                <el-option :label="$t('text.year')" :value="3"/>
                <el-option :label="$t('text.month')" :value="2"/>
                <el-option :label="$t('text.day')" :value="1"/>
              </el-select>
            </el-input>
          </i18n>
          <el-tooltip :content="$t('pages.docTrackServer_text12')" placement="bottom-end">
            <i class="el-icon-info"></i>
          </el-tooltip>
        </el-checkbox>
      </FormItem>
      <FormItem v-if="showWarnMsg" class="warn-msg-item" label-width="0">
        <label>
          {{ $t('pages.cleanLogMgrWarnMsg') }}
        </label>
      </FormItem>
      <FormItem v-show="!disabled" :label="$t('pages.docTrackServer_text5')">
        <el-time-picker v-model="temp.expiredCleanTime" :format="timeFormat" :value-format="timeFormat" :editable="false" :clearable="false"/>
      </FormItem>
      <FormItem v-show="showWaterIdx" :label="$t('pages.docTrackServer_text6')">
        <el-time-picker v-model="temp.waterIdxUpdateTime" :format="timeFormat" :value-format="timeFormat" :editable="false" :clearable="false"/>
      </FormItem>
    </Form>

    <div slot="footer" class="dialog-footer">
      <el-button v-show="showWaterIdx" :loading="submitting" :title="lastRebuildTime" type="primary" @click="rebuildIndex">
        {{ $t('pages.docTrackServer_text7') }}
      </el-button>
      <el-button :loading="submitting" type="primary" @click="updateConfig">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSysOpts, setSysOpts, getLastRebuildTime, rebuildWaterIdx } from '@/api/system/deviceManage/docTrackServer'
import moment from 'moment'
import { onlyInt } from '@/utils/inputLimit'

export default {
  name: 'SysOpts',
  data() {
    return {
      showWaterIdx: false,
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        expiredEnable: 0,
        expiredTimeNumber: 1,
        expiredTimeUnit: 3,
        expiredCleanTime: '00:00',
        waterIdxUpdateTime: '00:00'
      },
      expiredTimeNumberError: undefined,
      lastRebuildTimestamp: undefined,
      timeFormat: 'HH:mm',
      widthOpts: {
        'zh': '580px',
        'tw': '598px',
        'en': '563px'
      }
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    },
    dialogWidth() {
      return this.widthOpts[this.lang]
    },
    labelWidth() {
      return this.showWaterIdx ? 146 : 105
    },
    extraWidth() {
      const extra = { tw: 0, en: 111 }
      if (this.showWaterIdx) {
        extra.tw += 16
        extra.en += 27
      }
      if (navigator.userAgent.includes(' Gecko/20100101 Firefox/')) {
        extra.en += 5
      }
      return extra
    },
    disabled() {
      return !this.temp.expiredEnable
    },
    showWarnMsg() {
      if (this.disabled || this.temp.expiredTimeUnit > 2 || this.temp.expiredTimeNumber == null || this.temp.expiredTimeNumber === '') {
        return false
      }
      return this.temp.expiredTimeUnit == 1 && this.temp.expiredTimeNumber < 180 || this.temp.expiredTimeUnit == 2 && this.temp.expiredTimeNumber < 6
    },
    lastRebuildTime() {
      if (!this.lastRebuildTimestamp || this.lastRebuildTimestamp < 0) {
        return undefined
      }
      const time = moment.unix(this.lastRebuildTimestamp).format('YYYY-MM-DD HH:mm:ss')
      return `${this.$t('pages.docTrackServer_text8')}: ${time}`
    }
  },
  methods: {
    show() {
      this.expiredTimeNumberError = undefined
      this.getConfig()
      this.getRebuildTime()
      this.visible = true
    },
    getConfig() {
      getSysOpts().then(res => {
        const opts = res.data
        if (!opts.expiredEnable && opts.expiredTimeNumber === 0) {
          opts.expiredTimeNumber = ''
        }
        this.temp = { ...this.defaultTemp, ...opts }
      }).catch(() => {
        this.temp = { ...this.defaultTemp }
      })
    },
    updateConfig() {
      if (this.temp.expiredEnable && !this.temp.expiredTimeNumber) {
        this.expiredTimeNumberError = this.$t('text.cantNull')
        return
      }
      setSysOpts(this.temp).then(() => {
        this.notifySuccess(this.$t('text.updateSuccess'))
        this.visible = false
      })
    },
    getRebuildTime() {
      getLastRebuildTime().then(res => {
        this.lastRebuildTimestamp = res.data
      }).catch(() => {
        this.lastRebuildTimestamp = undefined
      })
    },
    rebuildIndex() {
      this.$confirmBox(this.$t('pages.docTrackServer_text10')).then(rebuildWaterIdx).then(() => {
        this.getRebuildTime()
        this.notifySuccess(this.$t('pages.docTrackServer_text9'))
      }).catch(() => {})
    },
    notifySuccess(message) {
      this.$notify({
        title: this.$t('text.success'),
        message,
        type: 'success',
        duration: 2000
      })
    },
    handleEnableChange(value) {
      if (!value && this.expiredTimeNumberError) {
        this.expiredTimeNumberError = undefined
      }
    },
    handleInputNumber(value) {
      if (!value) {
        return
      }
      let number = parseInt(value)
      if (isNaN(number)) {
        number = parseInt(onlyInt(value))
        if (isNaN(number)) {
          this.temp.expiredTimeNumber = undefined
          return
        }
      }
      this.expiredTimeNumberError = undefined
      if (number < 1) {
        this.temp.expiredTimeNumber = 1
        return
      }
      if (number > 999) {
        this.temp.expiredTimeNumber = 999
        return
      }
      this.temp.expiredTimeNumber = number
    },
    handleInputNumberBlur() {
      if (!this.temp.expiredTimeNumber) {
        this.expiredTimeNumberError = this.$t('text.cantNull')
      } else {
        this.expiredTimeNumberError = undefined
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-form {
    /*background: bisque;*/
    .warn-msg-item label {
      font-size: small;
      color: red;
      margin-left: 4px;
    }
    .el-date-editor.el-input.el-date-editor--time {
      width: 160px;
    }
  }
  .expired-time-item {
    >>>.el-form-item__error {
      left: unset;
      right: 165px;
    }
    .delete-log {
      font-weight: 700;
      min-width: 280px;
      >>>.el-checkbox__label{
        line-height: 40px;
      }
    }
    .input-file-size {
      margin-top: 3px;
      width: 160px;
      >>>.el-input-group__append {
        height: 32px;
        width: 90px;
        border: 1px solid #aaaaaa;
        border-left: 0;
      }
      &.is-disabled >>>.el-input-group__append {
        background: #e4e7e9;
      }
    }
  }
  .en.el-form {
    .expired-time-item {
      >>>.el-form-item__error {
        right: 68px;
      }
      .input-file-size>>>.el-input-group__append {
        width: 100px;
      }
    }
    .warn-msg-item label {
      margin-left: 10px;
    }
  }
  >>>.el-input-group__append {
    position: relative;
  }
  .el-select {
    position: absolute;
    top: 10px;
    left: 20px;
    >>>input {
      background: transparent;
    }
    >>>.el-input__inner.is-disabled {
      background: #e4e7e9;
    }
    >>>.el-input__inner {
      border: none;
    }
  }
</style>
