<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.makeInstallPackage')"
      :visible.sync="visible"
      width="650px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px">
        <el-divider content-position="left">{{ $t('pages.basicInformation') }}</el-divider>
        <FormItem :label="$t('table.installPackageName')">
          {{ temp.fileName }}
        </FormItem>
        <FormItem :label="$t('table.installPackageVersion')">
          {{ temp.version }}
        </FormItem>
        <el-divider content-position="left">{{ $t('table.installParam') }}</el-divider>
        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.addServerAddressAndPort', { server: serverTypeName }) }}</span>
          </div>
          <el-checkbox v-model="temp.forceSetServerAdd" :true-label="1" :false-label="0" style="padding-left: 20px">
            {{ $t('pages.builtInServerAddressInfo') }}
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content">
                {{ $t('pages.builtInServerAddressInfoTips', { server: serverTypeName, port: defaultServerPort }) }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
          <div v-if="generalServerList.length > 0">
            <el-row v-for="(item,index) in generalServerList" :key="index">
              <el-col :span="12" >
                <FormItem :label="$t('pages.serverAddress')">
                  <el-input v-model="item.ip" :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.address') })" maxlength="" @blur="setDefaultIpValue(item.ip, item)"></el-input>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.serverPort')">
                  <el-input-number
                    v-model="item.port"
                    :controls="false"
                    :step="1"
                    step-strictly
                    :min="1"
                    :max="65535"
                    :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.port') })"
                    @blur="setDefaultPortValue(item.port, item)"
                  />
                  <i v-show="generalServerList.length > 1" class="el-icon-remove-outline"></i>
                </FormItem>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <span v-if="submitting" style="color: #666; margin-right: 65px;">{{ $t('pages.makingPackage') }}</span>
        <el-button :loading="submitting" type="primary" @click="makeInstallPkg">{{ $t('pages.making') }}</el-button>
        <el-button @click="close">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { makeServerPkg } from '@/api/assets/systemMaintenance/terminalUpgrade';

export default {
  name: 'ServerMakeDlg',
  components: { },
  props: {
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        fileName: null,
        version: null,
        forceSetServerAdd: 1,    //  是否内置服务器地址信息
        serverInfos: []
      },
      generalServerList: [],  //  添加的引擎Ip和端口信息
      serverTypeName: this.$t('pages.server_eng'),   //  引擎服务器
      defaultServerPort: 20180,   //  引擎端口
      rules: {}
    }
  },
  created() {
  },
  methods: {
    restTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    initData() {
      this.restTemp()
      this.generalServerList = []
      this.submitting = false
    },
    show(data) {
      this.initData();
      Object.assign(this.temp, data)
      //  设置默认的引擎IP地址信息
      this.generalServerList.push({ ip: '127.0.0.1', port: this.defaultServerPort })
      this.visible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate();
      })
    },
    close() {
      this.visible = false
    },
    /**
     * 格式化数据
     * @param temp
     * @returns {*}
     */
    formData(temp) {
      const rowData = JSON.parse(JSON.stringify(temp));
      rowData.packageName = rowData.fileName
      if (rowData.forceSetServerAdd) {
        rowData.serverInfos = this.generalServerList
      }
      return rowData;
    },
    /**
     * 制作服务器安装包
     */
    makeInstallPkg() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          const data = this.formData(this.temp);
          makeServerPkg(data).then(res => {
            this.close();
          }).finally(_ => {
            this.submitting = false
          })
        }
      })
    },
    /**
     * 设置采集服务器的默认IP地址
     * @param ip
     * @param item
     */
    setDefaultIpValue(ip, item) {
      if (!ip) {
        item.ip = '127.0.0.1'
      }
    },
    /**
     * 设置采集服务器的默认端口号
     * @param port
     * @param item
     */
    setDefaultPortValue(port, item) {
      if (port === undefined || port === null) {
        item.port = this.defaultServerPort
      }
    }
  }
}
</script>

<style scoped>

</style>
