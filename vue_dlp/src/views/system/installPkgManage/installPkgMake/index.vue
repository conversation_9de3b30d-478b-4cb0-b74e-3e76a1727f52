<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-upload" size="mini" style="text-transform: capitalize" @click="handleCreatePackage">
          {{ '上传终端安装包' }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!packageDeleteable" @click="handleDeletePack">
          {{ $t('button.delete') }}
        </el-button>
        <download-tool :show-type="showType" placement="bottom-start"/>
        <el-button size="mini" @click="handlePlugMakePackage">
          {{ '终端MSI安装包制作' }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.pleasePackageName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="packageList"
        :show-pager="true"
        :multi-select="true"
        :col-model="packageModel"
        :row-data-api="loadPackage"
        :selectable="selectable"
        @selectionChangeEnd="packageSelectChange"
      />
    </div>

    <package-upload v-show="pkgUploadEnable" ref="packageUpload" v-model="pkgVer" :btn-show="false" :tips="pkgVerTips" @upload="handlePkgUpload"/>
    <plug-install-make-dlg ref="plugInstallMake"/>
    <term-make-install-pkg-dlg ref="termMakeInstallPkgDlg"/>
    <upload-term-install-pkg-dlg ref="uploadTermInstallPkgDlg"/>
    <server-make-dlg ref="serverMakeDlg"/>

    <el-dialog
      :title="progressTitle"
      :close-on-click-modal="false"
      :modal="false"
      :show-close="progressShowClose"
      width="600px"
      :visible.sync="progressVisible"
    >
      <el-progress type="line" :text-inside="true" :stroke-width="15" :percentage="percent" style="height: 40px;"/>
    </el-dialog>
  </div>
</template>

<script>
import { osTypeIconFormatter } from '@/utils/formatter'
import { deletePack, getMakePkgPage, isNewInstallPackage } from '@/api/assets/systemMaintenance/terminalUpgrade'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
import PlugInstallMakeDlg from '@/views/system/installPkgManage/installPkgMake/plug/plugInstallMakeDlg';
import ServerMakeDlg from '@/views/system/installPkgManage/installPkgMake/serverMakeDlg';
import TermMakeInstallPkgDlg from '@/views/system/installPkgManage/installPkgMake/dlg/termMakeInstallPkgDlg';
import { checkTermPkgAllowUploadOrMake } from '@/api/system/installPkgManage/installPkg';
import UploadTermInstallPkgDlg from '@/views/system/installPkgManage/installPkgMake/dlg/uploadTermInstallPkgDlg';
import PackageUpload from '@/views/system/terminalManage/terminal/uterm/upload'
import { getUTermVersion } from '@/api/system/terminalManage/uterm';
import { decode } from '@/views/system/terminalManage/terminal/uterm/client';

export default {
  name: 'InstallPkgMake',
  components: { UploadTermInstallPkgDlg, TermMakeInstallPkgDlg, ServerMakeDlg, PlugInstallMakeDlg, DownloadTool, PackageUpload },
  data() {
    return {
      packageModel: [
        { prop: '', label: 'source', width: '50', formatter: this.sourceFormatter },
        { prop: 'fileName', label: 'installPackageName', width: '300', sort: 'custom', iconFormatter: this.pkgIconFormatter },
        { prop: 'version', label: 'installPackageVersion', width: '120', sort: 'custom' },
        { prop: 'createTime', label: 'time', width: '120', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'making', click: this.handleMakePackage },
            { label: '更新', click: this.updateTermInstallPkg, isShow: (row) => { return this.pkgUploadEnable && row.id < 0 && !this.isServerPkg(row) } }
          ]
        }
      ],
      packageDeleteable: false,
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      showType: 2,
      pkgVer: undefined,      //  内置安装包版本号
      pkgUploadEnable: false,  //  终端内置安装包上传是否支持
      percent: 0,
      progressTimer: undefined,
      progressTitle: undefined,
      progressVisible: false,
      progressShowClose: false,
      progressCancelTimer: undefined
    }
  },
  computed: {
    //  安装包上传提示信息
    pkgVerTips() {
      if (this.pkgVer) {
        return this.$t('pages.serverInstallPackageVersion', { info: this.pkgVer })
      }
      return this.$t('pages.serverInstallPackageNotExitsts')
    }
  },
  watch: {
    progressVisible(value) {
      if (!value && this.progressCancelTimer) {
        clearTimeout(this.progressCancelTimer)
        this.progressCancelTimer = undefined
      }
    }
  },
  created() {
    this.getUTermInfo()
  },
  activated() {
    this.getUTermInfo()
  },
  methods: {
    getPackageTable() {
      return this.$refs['packageList']
    },
    isServerPkg(row) {
      // 是否服务端安装包
      return !!row.serverPkg
    },
    loadPackage(option) {
      return getMakePkgPage(option)
    },
    packageSelectChange(val) {
      this.packageDeleteable = val.length > 0
    },
    selectable(row) {
      return row.id > 0
    },
    handleMakePackage(row) {
      const isServerPkg = this.isServerPkg(row)
      if (isServerPkg) {
        this.$refs.serverMakeDlg.show(row);
      } else {
        if (!row.version) {
          this.$message({
            title: this.$t('text.prompt'),
            message: '当前终端安装包版本错误',
            type: 'error',
            duration: 2000
          })
          return;
        }
        checkTermPkgAllowUploadOrMake(row.version).then(res => {
          if (res.data) {
            isNewInstallPackage({ packageId: row.id }).then(resp => {
              // 旧版终端安装包只允许配置单个采集服务器ip、地址
              const oldType = !!resp.data
              this.$refs.termMakeInstallPkgDlg.show({ packageId: row.id, fileName: row.fileName, version: row.version }, oldType)
            })
          } else {
            this.$message({
              title: this.$t('text.prompt'),
              message: '安装包版本过低',
              type: 'error',
              duration: 2000
            })
          }
        })
      }
    },
    /**
     * 使用插件制作
     * @param row
     */
    handlePlugMakePackage(row) {
      this.$refs.plugInstallMake.show(row);
    },
    handleCreatePackage() {
      this.$refs.uploadTermInstallPkgDlg.show()
    },
    handleFilter() {
      this.query.page = 1
      this.getPackageTable().execRowDataApi(this.query)
    },
    handleDeletePack() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.getPackageTable().getSelectedIds()
        deletePack({ ids: toDeleteIds.join(',') }).then(respond => {
          this.getPackageTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    sourceFormatter(row, data) {
      return row.id < 0 ? '内置' : '上传'
    },
    pkgIconFormatter(row, data) {
      if (this.isServerPkg(row)) {
        return [{ class: 'server', title: '服务器安装包' }]
      }
      const icons = osTypeIconFormatter(row, data)
      if (icons) {
        icons.forEach(icon => {
          if (icon.title) {
            icon.title = icon.title.replace(this.$t('table.osType'), '终端安装包')
          }
        })
      }
      return icons
    },
    /**
     * 更新终端内置安装包
     */
    updateTermInstallPkg(row) {
      this.$refs.packageUpload.triggerSubmit()
    },
    /**
     * 获取U盘终端信息
     */
    getUTermInfo() {
      getUTermVersion().then(decode).then(data => {
        this.pkgVer = data.pkgVer
        this.pkgUploadEnable = data.hasGroupPermission
      })
    },
    handlePkgUpload(percent) {
      this.percent = percent
      if (percent === 0) {
        if (this.progressTimer) {
          clearInterval(this.progressTimer)
          this.progressTimer = undefined
        }
        this.progressTitle = this.$t('pages.uploadProgress')
        this.progressShowClose = false
        this.progressVisible = true
        return
      }
      if (percent === 100) {
        this.progressVisible = false
        this.getPackageTable().execRowDataApi(this.query)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
>>>.el-input-number.is-without-controls .el-input__inner {
  text-align: left;
}
>>>.date-picker-disabled input{
  background-color: #e4e7e9 ;
}
</style>
