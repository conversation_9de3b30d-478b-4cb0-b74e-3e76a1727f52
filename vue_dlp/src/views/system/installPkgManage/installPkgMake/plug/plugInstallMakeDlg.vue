<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="'终端MSI安装包制作'"
      :visible.sync="visible"
      width="750px"
      @close="close"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="150px">
        <el-divider content-position="left">
          {{ $t('pages.pluginStatus') }}
        </el-divider>
        <plug-info
          ref="plugInfo"
          :loading="loading"
          :support-min-version="supportMinVersion"
          @plugInfoListener="plugInfoListener"
        />
        <el-divider content-position="left">{{ $t('pages.productionMode') }}</el-divider>
        <FormItem :label="$t('pages.productionMode')">
          <el-radio-group v-model="mode">
            <el-radio :key="1" :value="1" :label="1">{{ `基于${webVersion}版本安装包制作` }}</el-radio>
            <el-radio :key="2" :value="2" :label="2">{{ '指定安装包制作' }}</el-radio>
<!--            <el-radio :key="2" :value="2" :label="2">{{ $t('pages.buildOutPackage') }}</el-radio>-->
          </el-radio-group>
        </FormItem>

        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.addServerAddressAndPort', { server: this.$t('pages.server_daq') }) }}
              <svg-icon style="color: #409EFF" icon-class="add" class-name="add-time" @click="addCollectServer"/>
            </span>
          </div>

          <el-checkbox v-model="forceSetServerAdd" style="padding-left: 20px">
            {{ $t('pages.builtInServerAddressInfo') }}
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content">
                {{ $t('pages.builtInServerAddressInfoTips', { server: $t('route.daqServer'), port: 20181 }) }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>

          <div v-if="collectServerList.length > 0">
            <el-row v-for="(item,index) in collectServerList" :key="index">
              <el-col :span="12" >
                <FormItem :label="$t('pages.serverAddress')">
                  <el-input
                    v-model="item.serverIp"
                    :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.address') })"
                    maxlength=""
                    @blur="setDefaultIpValue(item.serverIp, item)"
                  />
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.serverPort')">
                  <el-input-number
                    v-model="item.serverPort"
                    :controls="false"
                    :step="1"
                    step-strictly
                    :min="1"
                    :max="65535"
                    :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.port') })"
                    style="width: calc(100% - 20px)"
                    @blur="setDefaultPortValue(item.serverPort, item)"
                  />
                  <i v-show="collectServerList.length > 1" class="el-icon-remove-outline" @click="deleteCollectServer(index)"></i>
                </FormItem>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            {{ '安装包信息' }}
          </div>
          <FormItem v-show="mode === 2" :label="$t('pages.originInstallPackagePath')" prop="msiFilePath" label-width="150px">
            <div style="display: flex; align-items: center;">
              <file-scan-plug
                v-show="!temp.msiFilePath"
                ref="fileScanPlug"
                file-ext=".msi"
                type="selectFilePath"
                :only-select-file="true"
                :btn-title="$t('pages.selectFile')"
                :multi-select="false"
                :client="client"
                :plugin-work-ok="pluginWorkOk"
                :plugin-work-ver="pluginWorkVer"
                @selectPathEnd="selectPathEnd"
              />
              <el-tooltip :content="temp.msiFilePath" placement="top" :disabled="!temp.msiFilePath">
                <span v-show="!!temp.msiFilePath" class="path-display">{{ temp.msiFilePath }}</span>
              </el-tooltip>
              <i v-show="!!temp.msiFilePath" class="el-icon-close" style="margin-left: 10px" size="mini" @click="clearMsiFilePath"></i>
            </div>
          </FormItem>
          <FormItem :label="$t('pages.installPackageEndTime')" prop="endTime" label-width="150px">
            <el-date-picker
              v-model="temp.endTime"
              :disabled="!!temp.isPermanent"
              :class="{'date-picker-disabled':temp.isPermanent}"
              :picker-options="setEndTimeRange()"
              type="date"
              value-format="yyyy-MM-dd"
              :placeholder="$t('pages.selectDate')"
              style="width: 300px;"
            />
            <el-checkbox v-model="temp.isPermanent" :true-label="1" :false-label="0" style="padding-left: 20px" @change="isPermanentChange">
              {{ $t('pages.permanentEffective') }}
            </el-checkbox>
          </FormItem>
          <FormItem :label="$t('pages.generateInstallPackagePath')" label-width="150px">
            <div style="display: flex; align-items: center;">
              <!-- 显示当前路径 -->
              <el-tooltip :content="currentInstallPath" placement="top" :disabled="!currentInstallPath">
                <span class="path-display">{{ currentInstallPath || $t('pages.multiLoginAuth_notSelected') }}</span>
              </el-tooltip>
              <!-- 复制路径快捷键 点击后将路径复制到剪切板 -->
              <el-tooltip :content="$t('pages.copyPathToShearPlate')" placement="top">
                <svg-icon icon-class="list" class="copy-path-icon" style="width: 20px; height: 20px" @click="copyPath"></svg-icon>
              </el-tooltip>
              <!-- 自定义选择路径按钮 -->
              <file-scan-plug
                ref="customAppScanPlug"
                file-ext="None"
                type="selectFilePath"
                :only-select-file="false"
                :btn-title="customInstallPath ? $t('pages.reSelect') : $t('pages.selectPath')"
                :multi-select="false"
                :client="client"
                :plugin-work-ok="pluginWorkOk"
                :plugin-work-ver="pluginWorkVer"
                class="customPathStyle"
                @selectPathEnd="selectCustomInstallPathEnd"
              />
            </div>
          </FormItem>
          <FormItem :label="'安装包名称'" prop="installPkgName">
            <el-input v-model="temp.installPkgName" maxlength="20" style="width: 300px"/>
          </FormItem>
          <FormItem v-if="temp.installPkgName && temp.installPkgName.length">
            {{ getInstallPkgName(temp.installPkgName) }}
          </FormItem>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <span v-if="submitting" style="color: #666; margin-right: 65px;">{{ $t('pages.makingPackage') }}</span>
        <el-button :loading="submitting" type="primary" @click="makePackage">{{ $t('pages.making') }}</el-button>
        <el-button @click="close">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script type="text/jsx">
import PlugInfo from '@/components/PlugInfo'
import { parseTime } from '@/utils';
import { getMainCode, recordInstallPackageLog } from '@/api/system/installPkgManage/installPkg';
import { buildDownloadUrl, getWebVersion, makeMsiTerm } from '@/api/system/installPkgManage/msikInstallPkg';
import FileScanPlug from './fileScanPlug'
import { getNowDate } from '@/api/assets/systemMaintenance/terminalUpgrade'

export default {
  name: 'PlugInstallMake',
  components: { PlugInfo, FileScanPlug },
  props: {
    groupTreeData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      mode: 1,  //  制作方式 1-基于系统内部基础包制作，2-指定基础包制作
      temp: {},
      defaultTemp: {
        installPkgName: '',
        type: 1,  //  安装包类型
        isPermanent: 1,  // 是否长期有效
        endTime: '', // 截止有效期
        msiFilePath: '' //  本地文件路径
      },
      rules: {
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        endTime: [{ trigger: 'blur', validator: this.endTimeValidator }],
        msiFilePath: [
          { required: true, validator: this.msiFilePathValidator, trigger: 'blur' }
        ],
        installPkgName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      customInstallPath: '',  //  生成的安装包自定义路径
      installPkgName: '终端安装包', //  安装包名称
      defaultPkgName: '终端安装包', //  安装包默认名称
      supportMinVersion: '1.01.250101SC',  //  支持该功能最低插件版本号
      client: undefined,
      packetEncrypted: true, // 插件报文是否加密
      loading: false,       //  查询信息加载状态
      pluginWorkOk: false, // 插件工作状态是否正常
      pluginWorkVer: undefined, // 当前插件版本
      pluginInterrupted: false, // 插件连接中断
      webVersion: '', //  控制台版本号
      defaultServerPort: 20181,

      forceSetServerAdd: true,  //  是否内置服务器地址
      collectServerList: []    //  采集服务器配置信息
    }
  },
  computed: {
    //  生成的安装包路径
    currentInstallPath() {
      return this.customInstallPath;
    },
    //  生成的安装包全路径
    installFilePath() {
      return this.currentInstallPath ? this.currentInstallPath + '\\' + this.getInstallPkgName(this.temp.installPkgName) : null
    }
  },
  created() {
  },
  methods: {
    /**
     * 获取内置MSI安装包版本
     */
    getWebVersion() {
      getWebVersion().then(res => {
        this.webVersion = res.data
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    initData() {
      this.forceSetServerAdd = true
      this.webVersion = ''
      this.copyVisible = false
      this.submitting = false
      this.mode = 1
      this.loading = false
      this.collectServerList = []
      this.resetTemp()
      this.temp.installPkgName = this.defaultPkgName
    },
    show() {
      this.initData();
      this.getWebVersion()
      this.visible = true
      this.addCollectServer()
      this.$nextTick(() => {
        this.client = this.$refs.plugInfo.startPlug()
        this.$refs.dataForm.clearValidate()
      })
    },
    close() {
      this.$refs.plugInfo.closePlug()
      this.visible = false;
    },
    endTimeValidator(rule, value, callback) {
      if (!this.temp.isPermanent && !value) {
        //  安装包有效期不能为空
        callback(new Error(this.$t('pages.installPackValidityTimeNotNull') + ''))
      }
      callback();
    },
    msiFilePathValidator(rule, value, callback) {
      if (this.mode === 2 && !value) {
        //  请选择一个 MSI 安装包文件路径
        callback(new Error(this.$t('pages.pleaseSelectMsiPackPath') + ''));
      }
      callback();
    },
    /**
     * 设置截止时间的选择范围
     * @returns {{disabledDate: (function(*): boolean)}}
     */
    setEndTimeRange() {
      const dateObj = new Date()
      return {
        disabledDate: time => time.getTime() < dateObj.getTime()
      }
    },
    /**
     * 校验数据
     * @returns {boolean}
     */
    validFormData() {
      //  若勾选了内置服务器地址信息时，所有IP地址和端口必须填写
      let serverInfoFlag = true
      if (this.forceSetServerAdd) {
        for (let i = 0; i < this.collectServerList.length; i++) {
          const t = this.collectServerList[i];
          if (!t.serverIp || !t.serverPort) {
            serverInfoFlag = false
            break;
          }
        }
      }
      if (!serverInfoFlag) {
        this.$message({
          message: '存在采集服务器地址或端口未填写！',
          type: 'warning',
          duration: 2000
        })
        return false;
      }
      //  判断是否设置安装包生成路径
      if (!this.currentInstallPath) {
        this.$message({
          message: '请选择安装包生成路径！',
          type: 'warning',
          duration: 2000
        })
        return false;
      }
      return true;
    },
    /**
     * 制作安装包
     */
    makePackage() {
      this.$refs.dataForm.validate(async(valid) => {
        if (valid) {
          if (!this.validFormData()) {
            return
          }
          //  判断是否有配置主标识码
          const res = await getMainCode();
          const code = res.data
          if (!code) {
            //  安装码未生成，请到【安装码】生成！
            this.$message({
              message: this.$t('pages.installCodeNotGenerateTip'),
              type: 'warning',
              duration: 2000
            })
            return;
          }
          //  再次尝试测试连接插件
          this.$refs.plugInfo.connect().then(res => {
            if (res.code === 1) {
              this.submitting = true
              this.makeMsi(code).then((res) => {
                const { installCode, startTime, endTime } = res.data
                //  记录该安装码的使用日志  type=2：使用内置MSI安装包做为原始基础包制作  type=3:使用外置MSI安装包做为原始基础包制作
                const split = this.temp.msiFilePath.splice('\\') || '外置基础MSI安装包.msi'
                const originFileName = split[split.length - 1]
                recordInstallPackageLog({ fileName: originFileName, type: this.temp.mode === 1 ? 2 : 3, code: installCode, startTime, endTime });
                this.$notify({
                  title: this.$t('text.success'),
                  message: '终端MSI安装包制作成功',
                  type: 'success',
                  duration: 5000
                })
                this.visible = false
              }).catch((reason) => {
                if (reason === 'cancel') {
                  return
                }
                this.$notify({
                  title: this.$t('text.fail'),
                  message: reason,
                  type: 'error',
                  duration: 5000
                })
              }).finally(() => {
                this.submitting = false
              })
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    /**
     * 使用插件制作
     * @param installCode
     * @returns {Promise|Promise<T>}
     */
    makeMsi(installCode) {
      const { startTime, endTime } = this.getCodeTime(this.temp.isPermanent, this.temp.endTime)
      const IPAddressList = this.forceSetServerAdd ? this.collectServerList || [] : []
      if (this.mode === 1) {
        return makeMsiTerm().then((res) => {
          return this.updateTermPkg(res.data.taskId, res.data.token, installCode, startTime, endTime, this.installFilePath, IPAddressList).then(res => {
            return new Promise((resolve, reject) => { resolve({ data: { startTime, endTime, installCode }}) });
          })
        })
      } else {
        return this.updateTermPkgToPath(this.temp.msiFilePath, installCode, startTime, endTime, this.installFilePath, IPAddressList).then(res => {
          return new Promise((resolve, reject) => { resolve({ data: { startTime, endTime, installCode }}) });
        })
      }
    },
    /**
     * 调用插件制作安装包
     * @param taskId        下载安装包的taskId
     * @param token         下载安装包使用的token
     * @param installCode   安装码
     * @param startTime     有效期开始时间，格式：YYYY-MM-DD hh:mm:ss
     * @param endTime       有效期结束时间，格式：YYYY-MM-DD hh:mm:ss
     * @param PackagePath   指定安装包路径(完整路径，例如：D:\test\安装包.msi)
     * @param IPAddressList 采集服务器地址列表 [{"IP":"127.0.0.1","Port": 20181}]
     * @returns {Promise}
     */
    updateTermPkg(taskId, token, installCode, startTime, endTime, PackagePath, IPAddressList) {
      return this.client.geneTermInstallPackage({
        PackageUrl: buildDownloadUrl(taskId),
        token,
        installCode,
        startTime,
        endTime,
        PackagePath,
        IPAddressList
      })
    },
    /**
     * 调用插件制作安装包
     * @param packageFromPath        指定基础安装包所在的本地路径
     * @param installCode   安装码
     * @param startTime     有效期开始时间，格式：YYYY-MM-DD hh:mm:ss
     * @param endTime       有效期结束时间，格式：YYYY-MM-DD hh:mm:ss
     * @param PackagePath   指定安装包路径(完整路径，例如：D:\test\安装包.msi)
     * @param IPAddressList 采集服务器地址列表 [{"IP":"127.0.0.1","Port": 20181}]
     * @returns {Promise}
     */
    updateTermPkgToPath(packageFromPath, installCode, startTime, endTime, PackagePath, IPAddressList) {
      return this.client.geneTermInstallPackage({
        PackageUrl: packageFromPath,
        installCode,
        startTime,
        endTime,
        PackagePath,
        IPAddressList
      })
    },
    /**
     * 获取当前配置的安装码有效期
     */
    getCodeTime(isPermanent, codeEndTime) {
      let startTime = parseTime(new Date(), 'y-m-d')
      let endTime = codeEndTime
      if (isPermanent) {
        startTime = null
        endTime = null
      } else {
        startTime += ' 00:00:00'
        endTime += ' 23:59:59'
      }
      return { startTime, endTime }
    },
    isPermanentChange(data) {
      if (data) {
        this.temp.endTime = null
        this.$refs.dataForm.validateField('endTime')
      }
    },
    /**
     * 选择文件路径
     * @param filePaths
     */
    selectPathEnd(filePaths) {
      if (filePaths && filePaths.length > 0) {
        this.temp.msiFilePath = filePaths[0].FilePath || ''
        this.$nextTick(() => {
          this.$refs.dataForm.validateField('msiFilePath')
        })
      }
    },
    /**
     * 清除路径
     */
    clearMsiFilePath() {
      this.temp.msiFilePath = null
      this.$nextTick(() => {
        this.$refs.dataForm.validateField('msiFilePath')
      })
    },
    selectCustomInstallPathEnd(filePaths) {
      if (filePaths && filePaths.length > 0) {
        this.customInstallPath = filePaths[0].FilePath || ''
      }
    },
    handleInstallPathChange(event) {
      const files = event.target.files;
      if (files && files.length > 0) {
        this.customInstallPath = files[0].path; // 获取选中目录路径（Electron/Node环境）
      }
    },
    hideContextMenu() {
      document.removeEventListener('click', this.hideContextMenu);
    },
    /**
     * copy路径
     * @returns {Promise<void>}
     */
    async copyPath() {
      if (!this.currentInstallPath) {
        this.$message({
          title: this.$t('text.prompt'),
          message: '请选择安装包生成路径！',
          type: 'warning',
          duration: 2000
        })
        return;
      }
      try {
        await navigator.clipboard.writeText(this.currentInstallPath);
        //  路径已复制到剪贴板
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.copiedPathToShearPlate'),
          type: 'warning',
          duration: 2000
        })
      } catch (err) {
        //  复制失败
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.copyFail'),
          type: 'warning',
          duration: 2000
        })
      }
      this.hideContextMenu();
    },
    /**
     * 监听插件状态
     * @param data
     */
    plugInfoListener(data) {
      const { client, pluginWorkOk, pluginWorkVer, pluginInterrupted } = data
      this.pluginWorkOk = pluginWorkOk
      this.pluginWorkVer = pluginWorkVer
      this.pluginInterrupted = pluginInterrupted
      this.client = client
    },
    /**
     * 获取安装包名称
     * @param installPkgName
     * @returns {string|null|*}
     */
    getInstallPkgName(installPkgName) {
      let pkgName = this.defaultPkgName
      if (installPkgName) {
        if (installPkgName.includes('.')) {
          const split = installPkgName.split('.');
          if (split.length > 1 && split[split.length - 1] === 'msi') {
            pkgName = split[0];
          }
        } else {
          pkgName = installPkgName;
        }
      }
      return `${pkgName}_${this.webVersion}_${getNowDate()}.msi`;
    },
    /**
     * 添加采集服务器信息
     */
    addCollectServer() {
      const server = {
        serverIp: '127.0.0.1',
        serverPort: this.defaultServerPort
      }
      this.collectServerList.push(server)
    },
    /**
     * 删除采集服务器信息
     * @param index
     */
    deleteCollectServer(index) {
      this.collectServerList.splice(index, 1)
    },
    /**
     * 设置采集服务器的默认IP地址
     * @param ip
     * @param item
     */
    setDefaultIpValue(ip, item) {
      if (!ip) {
        item.serverIp = '127.0.0.1'
      }
    },
    /**
     * 设置采集服务器的默认端口号
     * @param port
     * @param item
     */
    setDefaultPortValue(port, item) {
      if (port === undefined || port === null) {
        item.serverPort = this.defaultServerPort
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-dialog__body {
    max-height: 660px;
  }
  .usb-term-form {
    padding: 0 20px;
    .plug-cfg-btn {
      display: none;
      margin: 0;
    }
    .usb-strategy-tips {
      line-height: 30px;
      span {
        color: #2674b2;
        strong {
          color: #f56c6c;
        }
      }
    }
    .el-date-editor.el-input {
      width: 100%;
    }
  }
  .btn-gen-lic {
    position: relative;
    float: left;
    .el-icon-info {
      color: #666;
      position: relative;
      top: 3px;
    }
  }
</style>

<style lang="scss">
  .pwd-clr.el-input--suffix {
    &:hover .el-input__inner, .el-input__inner:focus {
      padding-right: 55px;
    }
  }
  .usb-partition-msgbox {
    width: 555px;
  }
  .path-display {
    //flex: 1;
    width: 300px;
    padding: 0 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .customPathStyle {
    margin-left: 5px;
    >>> .el-button {
       margin-bottom: 0!important;
    }
  }
  .copy-path-icon {
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: #409EFF; // Element UI 的蓝色主题色
    }
  }
</style>
