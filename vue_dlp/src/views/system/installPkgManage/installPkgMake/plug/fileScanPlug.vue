<!--
  功能：获取当前操作系统的磁盘信息
  特点：使用外部client实例，外部传入插件连接状态pluginWorkOk，插件版本号pluginWorkVer
-->
<template>
  <div style="display: inline">
    <!--点击后，从插件获取扫描本地的目录/文件-->
    <el-button type="primary" :disabled="plugStatus !== 1" size="mini" @click="uploadDirOrFile">{{ btnTitle }}</el-button>
    <!-- 扫描本地目录/文件信息的窗口 -->
    <el-dialog
      v-el-drag-dialog
      append-to-body
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="localFileVis"
      :title="$t('pages.uploadDirOrFile')"
      width="850px"
    >
      <file-manager :out-class="'file-manager'" :file-path="curFilePath" @refresh="refreshFilePath" @clickPath="curFilePathClick">
        <template slot="tree">
          <!--目录树-->
          <tree-menu
            ref="fileDirTree"
            resizeable
            :data="filePathTree"
            :expand-on-click-node="true"
            :default-expanded-keys="defaultExpandedKeys"
            :render-content="renderTree"
            @node-click="nodeClick"
          />
        </template>
        <template slot="toolbar">
          <!--搜索当前目录的下级目录/文件-->
          <file-search :disabled="disabledSearch" :support-date-range="false" @search="handlerSearch"/>
          <!--当前显示的布局-->
          <el-dropdown class="layout-dropdown" trigger="click" @command="command => layout = command">
            <el-button size="mini">
              {{ $t('pages.layout') }}<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in layoutOpts"
                :key="index"
                :command="item.value"
                :icon="item.icon"
                :class="{ 'highlight-layout': item.value === layout }"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <!--当前目录的下级目录/文件信息-->
        <explorer-layout
          :layout="layout"
          :loading="tableLoading"
          :data="tableDatas"
          :col-model="colModel"
          :default-sort="{ prop: 'type', order: 'asc' }"
          :move-mouse-to-box="true"
          :icon-formatter="iconClassFormatter"
          :show-encrypted-icon="false"
          :show-file-size="false"
          :multi-select="multiSelect"
          :selectable="selectable"
          @selected="selectedFunc"
          @dblclick="rowDblclickFunc"
        />
      </file-manager>
      <div slot="footer" class="dialog-footer">
        <!-- 上传选中的文件/目录， 获取目录下所有文件 + 选中文件的 程序属性-->
        <el-button v-if="type === 'uploadFileProperty'" :loading="uploadLoading" type="primary" @click="upload">{{ $t('button.upload') }}</el-button>
        <el-button v-if="type === 'selectFilePath'" :loading="uploadLoading" type="primary" @click="selectPath">{{ '选择文件路径' }}</el-button>
        <el-button @click="closeDialog">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { compareVersion } from '@/api/system/terminalManage/uterm'
import FileManager from '@/components/FileManager'
import FileSearch from '@/components/FileManager/search'
import ExplorerLayout from '@/views/assets/systemMaintenance/explorer/layout'
import { formatFileType, getFileIcon } from '@/icons/extension'

export default {
  name: 'FileScanPlug',
  components: { ExplorerLayout, FileSearch, FileManager },
  props: {
    multiSelect: {
      type: Boolean,
      default: true
    },
    //
    /**
     * 功能类型，
     * 'uploadFileProperty'    文件上传并获取属性
     * 'selectFilePath'        获取文件路径
     */
    type: {
      type: String,
      default: 'uploadFileProperty'
    },
    //  扫描的文件后缀
    fileExt: {
      type: String,
      default: '.exe'
    },
    //  仅支持选择文件
    onlySelectFile: {
      type: Boolean,
      default: false
    },
    btnTitle: {
      type: String,
      default() { return this.$t('pages.uploadDirOrFile') }
    },
    client: {
      type: Object,
      default() {
        return null;
      }
    },
    //  插件是否处于工作状态, 即插件是否已连接
    pluginWorkOk: {
      type: Boolean,
      default: false
    },
    //  当前工作的插件版本
    pluginWorkVer: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      // 扫描目录/文件信息的窗口显示状态
      localFileVis: false,
      // 当前的目录路径
      curFilePath: [],
      // 当前的目录路径的ID列表
      curFilePathDataIds: [],
      // 根路径的ID
      rootPathId: '0',
      // 目录树的数据
      filePathTree: [],
      // 当前展示的布局类型
      layout: 'detail',
      // 布局类型
      layoutOpts: [
        { value: 'tile', label: this.$t('pages.placeOptions10'), icon: 'el-icon-menu' },
        { value: 'icon', label: this.$t('table.icon'), icon: 'el-icon-picture-outline' },
        { value: 'list', label: this.$t('pages.list'), icon: 'el-icon-s-grid' },
        { value: 'detail', label: this.$t('table.detailInfo'), icon: 'el-icon-notebook-2' }
      ],
      // 这个值目前是取上个DLP的插件版本号，需要判断大于这个版本号的插件版本，才有提取程序属性的功能
      prevMinVersion: '1.01.241121.SC',
      // 表格布局时，是否正获取当前目录的子目录和文件信息
      tableLoading: false,
      // 是否正在提取属性
      uploadLoading: false,
      // 表格布局的列属性
      colModel: [
        { prop: 'label', label: 'name', width: '200', sort: true, sortOriginal: [true, true], sortArr: ['type', 'label'], iconFormatter: this.iconClassFormatter },
        { prop: 'type', label: 'fileType', width: '150', sort: true, sortOriginal: [true, true], sortArr: ['type', 'label'], formatter: this.fileTypeFormatter }
      ],
      // { 目录ID: [文件信息, 文件信息]} 用于缓存已加载过的目录的文件信息，当点击已点击过的目录时，直接使用缓存的文件列表
      relDirIdToFiles: {},
      // 树默认展开节点
      defaultExpandedKeys: [],
      // 当前插件应用的操作系统路径分隔符（目前插件仅有win版本，所以暂时固定）
      filePathSpilt: '\\', // 文件路径分隔符
      // 当前选择的目录和文件
      selection: [],
      // 当前选中目录
      curSelectedNode: undefined,
      // 当前目录的子目录和文件数据列表
      tableDatas: [],
      fileTypeDict: {     //  文件类型字典
        0: this.$t('pages.localDisk'),    //  硬盘
        1: this.$t('pages.folder'),       //  文件夹
        2: this.$t('table.file')          //  文件
      }
    }
  },
  computed: {
    disabledSearch() {
      if (this.tableLoading || this.curFilePath.length === 0) {
        return true
      }
      return this.tableDatas.length === 0
    },
    //  插件当前状态   0 - 未连接插件， 1 - 插件正常运行， 2 - 插件版本低下，需要进行升级
    plugStatus() {
      if (!this.pluginWorkOk) {
        // 插件无法连接（插件未安装/端口配置错误）
        return 0
      } else if (this.prevMinVersion && compareVersion(this.pluginWorkVer, this.prevMinVersion) <= 0) {
        // 插件版本低下，需要进行升级来支持功能
        return 2
      }
      // 插件正常运行
      return 1
    }
  },
  created() {
  },
  methods: {
    compareVersion,
    upload() {
      const filePaths = this.selection.filter(s => s && s['oriData']).map(s => s['oriData']['path']).filter(p => !!p).map(p => { return { FilePath: p } })
      // 获取当前选中的目录/文件，组装参数，向插件进行请求
      // datas为获取的数据，end表明数据是否完全传输（考虑多帧传输的情况， end == 1表明完成传输）
      if (!filePaths || !filePaths.length) {
        this.$message({
          message: '请选择上传的目录或文件',
          type: 'warning',
          duration: 2000
        })
        return;
      }
      this.uploadLoading = true
      this.client.listFileAttribute(filePaths, (datas, end) => {
        datas = datas || []
        this.$emit('uploadEnd', datas.filter(data => data.ErrCode == 0).map(data => {
          const r = this.transferObject(data)
          r['fileDescription'] = r['description']
          r['originalFilename'] = r['originalFileName']
          r['legalCopyright'] = r['copyRight']
          r['softSign'] = r['signature']
          r['fileMd5'] = r['md5']
          r['quicklyMd5'] = r['quickMd5']
          return r
        }))
        if (end == 1) {
          this.uploadLoading = false
          this.localFileVis = false
        }
      }, () => { this.uploadLoading = false })
    },
    selectPath() {
      this.uploadLoading = true
      const filePaths = this.selection.filter(s => s && s['oriData']).map(s => s['oriData']['path']).filter(p => !!p).map(p => { return { FilePath: p } })
      this.$emit('selectPathEnd', filePaths)
      this.uploadLoading = false
      this.localFileVis = false
    },
    renderTree(h, { node, data, store }) {
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
        </div>
      )
    },
    resetParams() {
      this.filePathTree.splice(0, this.filePathTree.length, { id: this.rootPathId, label: this.$t('pages.thisComputer'), haveGot: false, oriData: { path: '' }, children: [] })
      this.defaultExpandedKeys.splice(0)
      this.curFilePath.splice(0, this.curFilePath.length, this.filePathTree[0].label)
      this.curFilePathDataIds.splice(0, this.curFilePathDataIds.length, this.rootPathId)
    },
    uploadDirOrFile() {
      this.localFileVis = true
      this.resetParams()
      if (!this.client) {
        return
      }
      this.$nextTick(() => {
        this.curSelectedNode = this.$refs['fileDirTree'].getNode(this.rootPathId)
        this.queryApi(this.curSelectedNode)
      })
    },
    nodeClick(data, node, el) {
      this.curSelectedNode = node
      this.queryApi(node)
    },
    queryApi(node) {
      if (!node) { return new Promise(resolve => { resolve() }) }
      node.loading = true
      this.tableLoading = true
      const data = node.data
      this.tableDatas.splice(0)
      this.setCurDirPath(node)
      return new Promise((resolve, reject) => {
        // haveGot 表明当前节点是否已获取过，如果获取过，则直接使用 树当前节点的子节点列表 + 缓存的当前目录的文件列表
        if (data.haveGot) {
          node.loading = false
          resolve()
          return
        }
        node.childNodes && node.childNodes.splice(0)
        data.children && data.children.splice(0)
        this.relDirIdToFiles[data.id] = undefined
        // 该函数体为处理 接收插件扫描目录，思路：接收当前协议的数据，将标识为目录的，向树添加节点，如果标识为文件的，向缓存添加数据
        const resolveFunc = (datas, end) => {
          const path = (datas || {})['Path'] || ''
          const objects = (datas || {})['ObjectList'] || []
          const beginI = (node.childNodes || []).length + (this.relDirIdToFiles[data.id] || []).length + 1
          for (let i = 0; i < objects.length; i++) {
            const oriData = this.transferObject(objects[i])
            oriData.path = path + (path ? this.filePathSpilt : '') + oriData['name']
            oriData.fileName = oriData['name']
            delete oriData['name']
            const type = oriData.type
            const node = { id: `${data.id}-${i + beginI}`, label: oriData.fileName, oriData, type, parentId: data.id }
            //  type: 0-硬盘 1-文件夹 2-文件
            if (type == 2) {
              if (!this.relDirIdToFiles[data.id]) { this.relDirIdToFiles[data.id] = [] }
              this.relDirIdToFiles[data.id].push(node)
              continue
            } else if (type == 0) {
              oriData.attributes = 1
            }
            this.$refs['fileDirTree'] && this.$refs['fileDirTree'].addNode(node)
          }
          if (end == 1) {
            data.haveGot = true
            node.loading = false
            node.expanded = true
            resolve()
          }
        };
        const rejectFunc = err => {
          node.loading = false
          this.tableLoading = false
          reject(err)
        };
        this.client.scanDirFileList({ Path: data.oriData.path, FileExt: this.fileExt }, resolveFunc, rejectFunc)
      }).then(() => this.rowDataApi())
    },
    rowDataApi(opt) {
      this.tableLoading = true
      const searchInfo = !opt || !opt.searchInfo ? undefined : opt.searchInfo.toLowerCase()
      const selectedData = (this.curSelectedNode || {})['data'] || {}
      const dirDatas = (selectedData['children'] || []).map(v => {
        if (v.children) {
          const tv = Object.assign({}, v)
          delete tv['children']
          return tv
        }
        return v
      }).filter(v => !searchInfo || v.label.toLowerCase().includes(searchInfo))
      const fileDatas = (this.relDirIdToFiles[selectedData['id']] || []).filter(v => !searchInfo || v.label.toLowerCase().includes(searchInfo))
      this.tableDatas.splice(0, this.tableDatas.length, ...dirDatas, ...fileDatas)
      this.tableLoading = false
    },
    // 刷新当前目录的子目录/文件信息
    refreshFilePath() {
      const ref = this.$refs['fileDirTree']
      if (!ref) { return }
      const node = ref.getNode(this.curSelectedNode)
      if (node) {
        node.data.haveGot = false
        this.queryApi(node)
      }
    },
    curFilePathClick(index) {
      const dataId = this.curFilePathDataIds[index]
      const ref = this.$refs['fileDirTree']
      if (!dataId || !ref) { return }
      const node = ref.getNode(dataId)
      node && this.rowDblclickFunc(node.data)
    },
    selectedFunc(selection) {
      this.selection = selection || []
    },
    rowDblclickFunc(rowData, column, event) {
      const ref = this.$refs['fileDirTree']
      if ((rowData.type <= 1 || rowData.id == this.rootPathId) && ref) {
        ref.setCurrentKey(rowData.id)
        this.defaultExpandedKeys.splice(0, this.defaultExpandedKeys.length, rowData)
        const node = ref.getNode(rowData.id)
        node && this.nodeClick(rowData, node)
      }
    },
    handlerSearch(data) {
      const searchInfo = (data || {})['name']
      this.rowDataApi({ searchInfo })
    },
    iconClassFormatter: function(row) {
      const icons = []
      // 0：硬盘 1：文件夹 2. 文件
      const type = row.type
      let title
      let iconName
      if (type == 2) {
        title = row.label.split('.').pop().toLowerCase()
        iconName = getFileIcon(title)
      } else {
        title = this.fileTypeDict[type]
        iconName = { 0: 'disk1', 1: 'dir1' }[type]
      }
      icons.push({ class: iconName, title: title })
      return icons
    },
    fileTypeFormatter(row, data) {
      return data == 2 ? formatFileType(row.label) : this.fileTypeDict[data]
    },
    // 首字母大写转驼峰
    transferObject(obj) {
      if (!obj) { return {} }
      return Object.entries(obj).reduce((acc, [key, value]) => {
        const newKey = key.charAt(0).toLowerCase() + key.slice(1)
        acc[newKey] = value
        return acc
      }, {})
    },
    // 获取当前目录路径
    setCurDirPath(node) {
      const tempPath = []
      let tempNode = node
      this.curFilePathDataIds.splice(0)
      do {
        tempPath.unshift(tempNode.data.label)
        this.curFilePathDataIds.unshift(tempNode.data.id)
      } while (tempNode.data.id != this.rootPathId && (tempNode = tempNode.parent))
      this.curFilePath.splice(0, this.curFilePath.length, ...tempPath)
    },
    closeDialog() {
      this.localFileVis = false
    },
    /**
     * 是否可选
     * @param item
     */
    selectable(item) {
      if (this.onlySelectFile) {
        return item.type === 2
      }
      return true;
    }
  }
}
</script>

<style lang="scss" scoped>
  .file-manager {
    height: 460px;

    .layout-box {
      color: black;
      background-color: inherit;

      >>>.layout-item:hover {
        background-color: lightgray;
        border: 1px solid lightgray;
      }
      >>>.highlight-item {
        background-color: lightgray;
      }
    }
  }
  .extract-program-tip-font {
    text-decoration: underline;
    font-size: 18px;
    cursor: pointer;
  }
  .highlight-layout {
    background-color: #ecf5ff;
    color: #66b1ff;
  }
</style>
