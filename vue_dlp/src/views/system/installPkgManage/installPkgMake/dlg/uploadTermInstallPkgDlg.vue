<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="'上传终端安装包'"
    :visible.sync="visible"
    width="400px"
    @close="close"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 340px;">
      <el-row>
        <el-col :span="21">
          <FormItem :label="$t('pages.exeName')" prop="fileName">
            <el-input v-model="temp.fileName" readonly/>
          </FormItem>
        </el-col>
        <el-col :span="2">
          <el-upload
            ref="upload"
            name="upload"
            action="1111"
            accept=".exe"
            :on-change="fileChange"
            :show-file-list="false"
            :file-list="fileList"
            :disabled="submitting"
            :auto-upload="false"
          >
            <el-button type="primary" icon="el-icon-upload" :loading="submitting" style="margin: 0 0 0 1px;"></el-button>
          </el-upload>
        </el-col>
      </el-row>
      <el-row v-if="submitting">
        <el-col :span="22">
          <el-progress type="line" :percentage="percentage"/>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
        </el-col>
      </el-row>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="saveFile()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="close">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { uploadSoft } from '@/api/assets/systemMaintenance/terminalUpgrade';
import axios from 'axios';

export default {
  name: 'UploadTermInstallPkgDlg',
  props: {
  },
  data() {
    return {
      visible: false,
      submitting: false,
      percentage: 0,
      source: null,
      fileList: [],
      rules: {
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      temp: {}, // 表单字段
      defaultTemp: {
        bussId: 1,
        id: undefined,
        fileName: ''
      }
    }
  },
  watch: {
    uploadVisible(val) {
      if (!val) {
        this.cancel()
      }
    }
  },
  created() {
  },
  methods: {
    initData() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.fileList.splice(0)
    },
    show() {
      this.initData()
      this.visible = true
    },
    close() {
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
      this.visible = false
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          message: this.$t('pages.upgrade_text2'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      this.temp.fileName = fileName
      this.fileList.splice(0, 1, file)
    },
    saveFile() {
      this.submitting = true
      this.percentage = 0
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const onUploadProgress = (progressEvent) => {
            const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
            this.percentage = parseInt(percent)
          }
          this.source = this.connectionSource()
          const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.temp)
          fd.append('uploadFile', this.fileList[0].raw)
          fd.append('sendToFtp', false)
          // 发起请求
          uploadSoft(fd, onUploadProgress, cacheToken).then(res => {
            this.submitting = false
            this.percentage = 0
            this.uploadVisible = false
            this.getPackageTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.upgrade_text3'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
            this.percentage = 0
          })
        } else {
          this.submitting = false
          this.percentage = 0
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
