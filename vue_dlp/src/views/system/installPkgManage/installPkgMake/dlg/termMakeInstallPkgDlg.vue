<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.makeInstallPackage')"
    :visible.sync="visible"
    width="650px"
    @close="close"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px">
      <el-divider content-position="left">{{ $t('pages.basicInformation') }}</el-divider>
      <FormItem :label="$t('table.installPackageName')">
        {{ temp.fileName }}
      </FormItem>
      <FormItem :label="$t('table.installPackageVersion')">
        {{ temp.version }}
      </FormItem>
      <el-divider content-position="left">{{ $t('table.installParam') }}</el-divider>
      <FormItem :label="$t('pages.productionType')" :tooltip-content="$t('pages.terminalOldInstallPackageOnlyCreateSilent')">
        <el-radio-group v-model="temp.type" style="padding-left: 20px;">
          <el-radio :label="1">{{ $t('pages.silentInstallationPackage') }}</el-radio>
          <el-radio :label="2" :disabled="!oldType">
            {{ $t('pages.commonInstallationPackage') }}
          </el-radio>
        </el-radio-group>
      </FormItem>
      <el-card v-if="temp.type == 1" class="box-card" :body-style="{'padding': ' 5px 10px'}">
        <div slot="header">
          <span>{{ $t('pages.addServerAddressAndPort', { server: serverTypeName }) }}
            <svg-icon v-show="oldType" style="color: #409EFF" icon-class="add" class-name="add-time" @click="addSilentServer"/>
          </span>
        </div>
        <div v-if="silentServerList.length > 0">
          <el-row v-for="(item,index) in silentServerList" :key="index">
            <el-col :span="12" >
              <FormItem :label="$t('pages.serverAddress')">
                <el-input
                  v-model="item.serverIp"
                  :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.address') })"
                  maxlength=""
                  @blur="setDefaultIpValue(item.serverIp, item)"
                />
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.serverPort')">
                <el-input-number
                  v-model="item.serverPort"
                  :controls="false"
                  :step="1"
                  step-strictly
                  :min="1"
                  :max="65535"
                  :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.port') })"
                  style="width: calc(100% - 20px)"
                  @blur="setDefaultPortValue(item.serverPort, item)"
                />
                <i v-show="silentServerList.length > 1" class="el-icon-remove-outline" @click="deleteSilentServer(index)"></i>
              </FormItem>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card v-if="temp.type == 1" class="box-card" :body-style="{'padding': ' 5px 10px'}">
        <div slot="header">
          <span>{{ $t('pages.makePackage_text4') }}</span>
        </div>
        <FormItem :label="$t('pages.makePackage_text5')" prop="userName">
          <el-input v-model="temp.userName" style="width: calc(100% - 20px);" maxlength="30"/>
          <el-tooltip effect="dark" placement="bottom-end">
            <div slot="content">
              {{ $t('pages.makePackage_text6') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.pwdInfo')" prop="userPassword">
          <el-input v-model="temp.userPassword" style="width: calc(100% - 20px);" maxlength=""></el-input>
        </FormItem>
      </el-card>
      <el-card v-if="temp.type == 2" class="box-card" :body-style="{'padding': ' 5px 10px'}">
        <div slot="header">
          <span>{{ $t('pages.addServerAddressAndPort', { server: serverTypeName }) }}
          </span>
        </div>
        <el-checkbox v-model="temp.forceSetServerAdd" :true-label="1" :false-label="0" style="padding-left: 20px">
          {{ $t('pages.builtInServerAddressInfo') }}
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.builtInServerAddressInfoTips', { server: serverTypeName, port: defaultServerPort }) }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-checkbox>
        <div v-if="generalServerList.length > 0">
          <el-row v-for="(item,index) in generalServerList" :key="index">
            <el-col :span="12" >
              <FormItem :label="$t('pages.serverAddress')">
                <el-input
                  v-model="item.serverIp"
                  :disabled="temp.forceSetServerAdd == 0"
                  :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.address') })"
                  maxlength=""
                  @blur="setDefaultIpValue(item.serverIp, item)"
                />
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.serverPort')">
                <el-input-number
                  v-model="item.serverPort"
                  :controls="false"
                  :step="1"
                  step-strictly
                  :min="1"
                  :max="65535"
                  :disabled="temp.forceSetServerAdd == 0"
                  :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.port') })"
                  @blur="setDefaultPortValue(item.serverPort, item)"
                />
                <i v-show="generalServerList.length > 1" class="el-icon-remove-outline" @click="deleteGeneralServer(index)"></i>
              </FormItem>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card v-if="supportEffectTime" class="box-card" :body-style="{'padding': ' 5px 10px'}">
        <div slot="header">
          <span>安装包有效期</span>
        </div>
        <FormItem label="截至有效期">
          <el-date-picker
            v-model="temp.endTime"
            :disabled="!!temp.isPermanent"
            :class="{'date-picker-disabled':temp.isPermanent}"
            :picker-options="setEndTimeRange"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            style="width: 300px;"
          />
          <el-checkbox v-model="temp.isPermanent" :true-label="1" :false-label="0" style="padding-left: 20px">
            {{ $t('pages.permanentEffective') }}
          </el-checkbox>
        </FormItem>
      </el-card>
    </Form>
    <div slot="footer" class="dialog-footer">
      <span v-if="submitting" style="color: #666; margin-right: 65px;">{{ $t('pages.makingPackage') }}</span>
      <el-button :loading="submitting" type="primary" @click="createData()">{{ $t('pages.making') }}</el-button>
      <el-button @click="close">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { makeInstallationPackage } from '@/api/assets/systemMaintenance/terminalUpgrade';
export default {
  name: 'TermMakeInstallPkgDlg',
  props: {},
  data() {
    return {
      submitting: false,
      visible: false,
      temp: {},
      defaultTemp: {
        fileName: '',
        packageId: null,
        type: 1,
        isPermanent: 1,  // 是否长期有效
        endTime: '', // 截止有效期
        forceSetServerAdd: 1,
        serverInfo: [],
        userName: '',
        userPassword: ''
      },

      rules: {
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        serverIp: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        serverPort: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        userPassword: [
          { validator: this.passwordRightValid, trigger: 'blur' }
        ]
      },
      oldType: true,
      silentServerList: [],
      generalServerList: [],
      defaultServerPort: 20181,
      serverTypeName: '',
      //  生效时间支持的最低版本
      effectTimeSupportMinVersion: '5.02.250701.SC'
    }
  },
  computed: {
    //  支持安装包生效时间的配置
    supportEffectTime() {
      //  packageId < 0 代表内置安装包，内置安装包支持配置生效时间
      return this.temp.packageId < 0 || this.temp.version > this.effectTimeSupportMinVersion;
    }
  },
  created() {
  },
  methods: {
    initData() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.defaultServerPort = 20181
      this.serverTypeName = this.$t('pages.server_daq')
      this.silentServerList = [{
        serverIp: '127.0.0.1',
        serverPort: this.defaultServerPort
      }]
      this.generalServerList = [{
        serverIp: '127.0.0.1',
        serverPort: this.defaultServerPort
      }]
    },
    show(temp, oldType) {
      this.initData();
      Object.assign(this.temp, temp)
      this.oldType = oldType
      this.visible = true
    },
    addSilentServer() {
      const server = {
        serverIp: '127.0.0.1',
        serverPort: this.defaultServerPort
      }
      this.silentServerList.push(server)
    },
    deleteSilentServer(index) {
      this.silentServerList.splice(index, 1)
    },
    deleteGeneralServer(index) {
      this.generalServerList.splice(index, 1)
    },
    // 设置截止时间的选择范围
    setEndTimeRange() {
      const dateObj = new Date()
      return {
        disabledDate: time => time.getTime() < dateObj.getTime()
      }
    },
    duplication(data) {
      const serverInfo = [];
      for (const server of data) {
        if (serverInfo.find((item) => item.serverIp == server.serverIp && item.serverPort == server.serverPort)) {
          continue;
        }
        serverInfo.push(server);
      }
      return serverInfo;
    },
    formatterData() {
      if (this.temp.type == 1) {
        this.silentServerList = this.duplication(this.silentServerList)
        this.temp.serverInfo.splice(0, this.temp.serverInfo.length, ...this.silentServerList)
      } else if (this.temp.type == 2) {
        if (this.temp.forceSetServerAdd == 0) {
          const server = { serverIp: '127.0.0.1', serverPort: this.defaultServerPort }
          this.temp.serverInfo.splice(0, this.temp.serverInfo.length, server)
        } else {
          this.generalServerList = this.duplication(this.generalServerList)
          this.temp.serverInfo.splice(0, this.temp.serverInfo.length, ...this.generalServerList)
        }
      }
    },
    createData() {
      this.formatterData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          console.log('temp %o', this.temp)
          makeInstallationPackage(this.temp).then(res => {
            this.submitting = false
            this.visible = false
            this.$notify({
              title: this.$t('text.success'),
              message: '安装包制作成功',
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    close() {
      this.visible = false
    },
    passwordRightValid(rule, value, callback) {
      if (this.temp.userName != '' && this.temp.userPassword == '') {
        callback(new Error(this.$t('pages.makePackage_text8')))
      } else {
        callback()
      }
    },
    /**
     * 设置采集服务器的默认IP地址
     * @param ip
     * @param item
     */
    setDefaultIpValue(ip, item) {
      if (!ip) {
        item.serverIp = '127.0.0.1'
      }
    },
    /**
     * 设置采集服务器的默认端口号
     * @param port
     * @param item
     */
    setDefaultPortValue(port, item) {
      if (port === undefined || port === null) {
        item.serverPort = this.defaultServerPort
      }
    }
  }
}
</script>

<style scoped>

</style>
