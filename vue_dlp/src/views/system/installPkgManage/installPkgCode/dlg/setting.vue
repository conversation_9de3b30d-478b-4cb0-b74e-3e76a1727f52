<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="'高级配置'"
    :visible.sync="visible"
    width="600px"
    @close="close"
  >
    <el-row>
      <el-col :span="24">
        <el-checkbox v-model="termLoginValid">未升级到5.02版本的终端登录服务器时需校验系统接入码</el-checkbox>
      </el-col>
      <el-col :span="24">
        <el-checkbox v-model="termAccessValid">未升级到5.02版本的终端接入时需校验系统接入码</el-checkbox>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="confirm">
        {{ $t('button.save') }}
      </el-button>
      <el-button @click="close">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listByOptIds, saveSysOptions } from '@/api/sysOptions';

export default {
  name: 'Setting',
  props: {},
  data() {
    return {
      visible: false,
      submitting: false,
      termLoginValid: false, // 未升级到5.02版本的终端登录服务器时需校验系统接入码
      termAccessValid: false,  //  未升级到5.02版本的终端接入服务器时需校验系统接入码
      setBeforeValidIsEnable: false  //  终端安装包身份认证功能配置之前是否开启
    }
  },
  computed: {
    //  终端安装包身份认证功能修改之后是否开启
    setAfterValidIsEnable() {
      return this.termAccessValid || this.termLoginValid
    }
  },
  created() {

  },
  methods: {
    initData() {
      this.submitting = false
      this.termLoginValid = false
      this.termAccessValid = false
      this.validIsEnable = false
    },
    show() {
      this.initData()
      this.getConfig()
      this.visible = true
    },
    /**
     * 获取配置信息
     */
    getConfig() {
      listByOptIds({ optIds: '2010,2011' }).then(res => {
        if (res.data && res.data.length) {
          const opt2010 = res.data.find(t => t.optId === 2010);
          if (opt2010) {
            this.termLoginValid = !!opt2010.optVal
          }
          const opt2011 = res.data.find(t => t.optId === 2011);
          if (opt2011) {
            this.termAccessValid = !!opt2011.optVal
          }
          this.setBeforeValidIsEnable = this.termAccessValid || this.termLoginValid
        }
      })
    },
    confirm() {
      if (this.setAfterValidIsEnable !== this.setBeforeValidIsEnable) {
        let msg = '';
        if (this.setBeforeValidIsEnable) {
          msg = '此配置下,【终端安装包身份校验】功能将关闭，确定继续保存？'
        } else {
          msg = '此配置下,【终端安装包身份校验】功能将开启，确定继续保存？'
        }
        this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
          this.saveConfig();
        }).catch((e) => {
        })
      } else {
        this.saveConfig();
      }
    },
    /**
     * 保存配置
     */
    saveConfig() {
      this.submitting = true
      const datas = [{ optId: 2010, optVal: this.termLoginValid ? 1 : 0 }, { optId: 2011, optVal: this.termAccessValid ? 1 : 0 }]
      saveSysOptions(datas).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: '配置更新成功',
          type: 'success',
          duration: 2000
        })
        this.submitting = false
        this.$emit('submit')
        this.close()
      }).catch(_ => {
        this.submitting = false
      })
    },
    close() {
      this.visible = false
    }
  }
}
</script>

<style scoped>

</style>
