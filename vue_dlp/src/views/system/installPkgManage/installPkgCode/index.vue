<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('text.addInfo', { info: $t('route.installPkgCode') }) }}
        </el-button>
        <span style="margin-left: 20px">
          <label>终端安装包身份校验</label>
          <el-tooltip>
            <div slot="content">
              {{ '终端安装包身份校验功能：限制非法终端安装包接入服务器。开启后，终端在接入时，服务器将校验终端的系统接入码，允许携带已启用的系统接入码的终端接入，拒绝系统接入码错误的终端接入(系统接入码是作为连接服务器的身份校验凭证)；' }}<br>
              {{ '注意：开启后，5.02之前版本的终端将无法接入服务器；' }}<br>
              {{ '终端系统接入码来源：通过在【安装包制作】界面制作终端安装包，制作的终端安装包将携带最新系统接入码；' }}<br>
              {{ '适用终端：仅支持Windows终端；' }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-switch v-model="codeEnable" @change="codeEnableChange"></el-switch>
        </span>
        <el-button v-if="codeEnable" type="primary" icon="el-icon-setting" size="mini" @click="handleSetting">
          {{ '高级配置' }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('route.installPkgCode')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="codeList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <!-- 生成系统接入码 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title=" $t('text.addInfo', { info: $t('route.installPkgCode') })"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 500px;">
        <FormItem :label="$t('route.installPkgCode')" prop="code">
          <el-tooltip slot="tooltip" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ '系统接入码：作为终端连接服务器的身份校验凭证，由服务端生成，无法手动修改，保存后有效' }}<br>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-input v-model="temp.code" disabled></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 高级设置 -->
    <setting ref="setting" @submit="settingSubmit"/>
  </div>
</template>

<script>
import {
  getInstallPkgCode,
  addInstallPkgCode,
  updateInstallPkgCode,
  getCodePage,
  validInstallCodeExitsApprovalAccess, changeInstallCodeSwitch
} from '@/api/system/installPkgManage/installPkg'
import { stgActiveIconFormatter } from '@/utils/formatter'
import { listByOptIds } from '@/api/sysOptions';
import Setting from '@/views/system/installPkgManage/installPkgCode/dlg/setting';

export default {
  name: 'InstallPkgCode',
  components: { Setting },
  data() {
    return {
      colModel: [
        { prop: 'code', label: 'installPkgCode', width: '100', sort: true, iconFormatter: stgActiveIconFormatter, formatter: this.codeFormatter },
        { prop: 'createTime', label: 'createTime', width: '100', sort: true },
        { prop: 'makeSize', label: 'makePkgSize', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'disable', isShow: row => row.active && !row.isMain, click: this.disableHandler },
            { label: 'enable', isShow: row => !row.active, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      editable: true,
      deleteable: false,
      temp: { },
      defaultTemp: {
        id: undefined,
        startTime: undefined,
        active: 1,
        code: ''
      },
      dialogFormVisible: false,
      rules: { },
      submitting: false,
      codeEnable: false   //  是否开启系统接入码功能，默认关闭

    }
  },
  computed: {
    gridTable() {
      return this.$refs['codeList']
    }
  },
  created() {
    this.resetTemp()
    this.getCodeEnable()
  },
  activated() {
    this.getCodeEnable()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getCodePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.startTime = new Date()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      if (this.query.parentId) {
        this.temp.parentId = this.query.parentId
      }
      this.dialogFormVisible = true
      getInstallPkgCode().then(resp => {
        this.temp.code = resp.data
      })
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, this.defaultTemp, row, { active: row.active ? 0 : 1 })
      updateInstallPkgCode(this.temp).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: '修改成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          addInstallPkgCode(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    async disableHandler(row) {
      this.$confirmBox('禁用系统接入码后，携带此系统接入码的终端安装包无法使用，是否禁用？', this.$t('text.prompt')).then(async(t) => {
        const codes = [row.encCode]
        const res = await validInstallCodeExitsApprovalAccess(codes)
        //  若终端接入审批表中存在改安装码时，提示禁用后会清除审批接入记录（未审批的，已审批但未再次接入的）
        if (res.data) {
          this.$confirmBox(this.$t('pages.stopInstallCodeTip'), this.$t('text.prompt')).then(() => {
            this.handleUpdate(row)
          });
        } else {
          this.handleUpdate(row)
        }
      }).catch(e => {
      })
    },
    codeFormatter(row, data) {
      if (row.isMain) {
        return row.code + '（最新）'
      }
      return row.code
    },
    /**
     * 获取校验接入码功能是否开启
     */
    getCodeEnable() {
      listByOptIds({ optIds: '2010,2011' }).then(res => {
        if (res.data && res.data.length && res.data.length === 2) {
          this.codeEnable = !(res.data[0].optVal == 0 && res.data[1].optVal == 0)
        }
      })
    },
    /**
     * 校验接入码功能开关改变时
     */
    codeEnableChange(value) {
      changeInstallCodeSwitch({ enable: !value ? 0 : 1 }).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: `终端安装包身份校验功能已${value ? '开启' : '关闭'}`,
          type: 'success',
          duration: 2000
        })
      })
    },
    /**
     * 高级设置
     */
    handleSetting() {
      this.$refs.setting.show()
    },
    /**
     * 高级设置成功回调
     */
    settingSubmit() {
      this.getCodeEnable()
    }
  }
}
</script>
