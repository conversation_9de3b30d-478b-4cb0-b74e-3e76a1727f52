<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createCustomizeReport') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delCustomizeReport') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.customizeReportText1')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="textMap[dialogStatus]"
        :visible.sync="dlgVisible"
        width="750px"
      >
        <Form
          ref="dataForm"
          :model="temp"
          :rules="rules"
          label-position="right"
          label-width="100px"
          style="width: 700px;"
        >
          <FormItem :label="$t('pages.reportTypeName')" prop="businessName">
            <el-input v-model="temp.businessName"/>
          </FormItem>
          <FormItem :label="$t('text.remark')">
            <el-input v-model="temp.remark"/>
          </FormItem>
          <FormItem :label="$t('pages.templateSelect')">
            <el-radio v-model="temp.templateHandleType" :label="1">{{ $t('text.base') }}</el-radio>
            <el-radio v-model="temp.templateHandleType" :label="2">{{ $t('text.customize') }}</el-radio>
          </FormItem>
          <div v-show="temp.templateHandleType === 1" class="reportIndexPanel">
            <FormItem :label="$t('pages.contentSplitChar')">
              <el-radio-group v-model="splitCharRadio" style="margin-left: 0">
                <el-radio v-for="(item, i) in splitChatDict" :key="i" :label="item.value">
                  {{ item.label }}
                  <el-input
                    v-if="item.value === '0'"
                    v-show="splitCharRadio === '0'"
                    v-model="splitChar"
                    maxlength="2"
                    show-word-limit
                    style="width: 70px;margin-left: 16px"
                  />
                </el-radio>
              </el-radio-group>
            </FormItem>
            <el-card
              style="width: calc( 50% - 2.5px); background-color: transparent; float: left; height: 100%"
              class="box-card"
            >
              <div class="clearfix" :style="headerStyle">
                <span>{{ $t('pages.optionalIndex') }}</span>
              </div>
              <tree-menu
                ref="toSelectTree"
                :style="{border: '0 !important'}"
                :data="reportTemplateList"
                :checked-keys="dataIndexListKeys"
                :accordion="false"
                node-key="dataId"
                :height="330"
                multiple
                :render-content="renderContent"
                @check-change="dataIndexChange"
              >
              </tree-menu>
            </el-card>
            <el-card
              class="box-card"
              style="width: calc( 50% - 2.5px); background-color: transparent; float: right; height: 100%; margin-left: 5px"
            >
              <div class="clearfix" :style="headerStyle">
                <span>{{ $t('pages.chooseIndex') }}</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="$t('pages.customizeReportText2')"
                  placement="top-start"
                >
                  <i class="el-icon-info"/>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="previewEffect" placement="top-start">
                  <el-button v-show="dataIndexList.length > 0" type="text" style="margin: 0; padding: 0; float: right">
                    {{ $t('pages.dataContentBrowse') }}
                  </el-button>
                </el-tooltip>
              </div>
              <div class="input-container" style="margin-top: 4px">
                <el-input v-model="queryChoose" :placeholder="$t('components.enterContent')"></el-input>
              </div>
              <div style="height: 296px; overflow-y:auto;">
                <div
                  v-for="(item, index) in dataIndexList"
                  v-show="item.label.includes(queryChoose)"
                  :key="index"
                  style="line-height: 26px; margin: 3px 0; padding: 0 5px; display: flex"
                  draggable="true"
                  @mouseenter="mouseenterEvent"
                  @mouseleave="mouseleaveEvent"
                  @dragstart="handleDragStart($event, item)"
                  @dragover.prevent="handleDragOver($event, item)"
                  @dragenter="handleDragEnter($event, item)"
                  @dragend="handleDragEnd($event, item)"
                ><span style="padding-right: 10px; flex: 1">{{ item.label }}</span>
                  <el-button
                    type="text"
                    style="padding: 0;margin: 0;line-height: 26px"
                    @click="deleteChooseIndex(item)"
                  >{{ $t('button.delete') }}
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>
          <FormItem v-show="temp.templateHandleType == 2" label="数据内容" tooltip-content="自定义推送报表数据内容，占位符将替换为具体的指标数据" prop="templateContent">
            <el-popover
              placement="left"
              width="400"
              trigger="click"
            >
              <tree-menu
                ref="dataIndexTree1"
                :data="reportTemplateList"
                :height="360"
                :accordion="false"
                node-key="dataId"
                is-filter
                @node-click="addDataIndexContent"
              />

              <el-button slot="reference" size="small">添加报表指标数据内容</el-button>
            </el-popover>
            <el-popover
              placement="right"
              width="400"
              trigger="click"
            >
              <tree-menu
                ref="dataIndexTree2"
                :data="reportTemplateList"
                :height="360"
                :accordion="false"
                node-key="dataId"
                :default-expanded-keys="['R1']"
                is-filter
                :render-content="seeTreeIndex"
                @node-click="addDataIndex"
              />
              <el-button slot="reference" size="small" >查看报表指标占位符</el-button>
            </el-popover>
            <el-input v-model="templateContent" type="textarea" :rows="8" resize="none" maxlength="200" show-word-limit @input="inputText" @blur="blurEvent" />
          </FormItem>
        </Form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="submitting" @click="handleSubmit">
            {{ $t('button.confirm') }}
          </el-button>
          <el-button @click="dlgVisible = false">
            {{ $t('button.cancel') }}
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  addReportTemplate, delReportTemplate,
  getReportTemplateByName, getReportTemplateChipTree,
  getReportTemplatePage, updateReportTemplate
} from '@/api/system/messageNotification/customizeReportTemplate';
import { getSplitChar } from '@/utils/messagePush';

export default {
  name: 'CustomizeReport',
  data() {
    return {
      deleteable: false,
      query: {
        searchInfo: undefined,
        templateType: 2
      },
      dialogStatus: 'create',
      textMap: {
        'create': this.$t('pages.createCustomizeReport'),
        'update': this.$t('pages.updateCustomizeReport')
      },
      dlgVisible: false,
      temp: {},
      defaultTemp: {
        businessName: '',
        remark: '',
        templateType: 2,
        templateHandleType: 1,
        templateContent: '',
        businessCode: 'customize_report'
      },
      templateContent: '',
      splitChar: '',
      submitting: false,
      submitting2: false,
      dataIndexListKeys: undefined,
      dataIndexList: [],
      reportTemplateList: [],
      dataIndexTemp: {},
      splitChatDict: {},
      splitCharRadio: ',',
      blurIndex: 0,
      hasEnter: false,
      handleRadio: '1',
      dragging: null,
      queryChoose: '',
      previewEffect: '',
      headerStyle: { color: '#666666', fontWeight: 'bold' },
      colModel: [
        { prop: 'businessName', label: 'customizeReportTypeName', width: '120' },
        { prop: 'dataIndex', label: 'customizeReportDataIndex', width: '200', formatter: this.dataFormatter },
        { prop: 'remark', label: 'remark', width: '120' },
        {
          label: 'operate', type: 'button', width: '120', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      rules: {
        businessName: [
          { required: true, message: this.$t('pages.ReportTemplateValidatorText1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        templateContent: [
          { validator: (rule, value, callback) => {
            if (this.temp.templateHandleType === 2) {
              value = this.temp.templateContent
              if (value) {
                const rep = /\$\{[0-9]+\}/g
                const flag = rep.test(value)
                if (!flag) { callback(new Error(this.$t('pages.customizeReportText4'))) }
                callback()
              }
              callback(new Error(this.$t('pages.customizeReportText3')))
            }
            callback()
          }
          }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs.gridTable
    },
    toSelectTree() {
      return this.$refs.toSelectTree
    }
  },
  created() {
    this.initData()
    this.splitChatDict = getSplitChar()
  },
  methods: {
    resetForm() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.splitCharRadio = ','
      this.splitChar = ''
      this.dataIndexListKeys = ['']
      this.dataIndexList = []
      this.templateContent = ''
      this.$nextTick(() => {
        this.toSelectTree.clearSelectedNodes()
      })
    },
    renderContent(h, { node, data, store }) {
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
        </div>
      )
    },
    seeTreeIndex(h, { node, data, store }) {
      return (<span>{data.dataType === 'R' ? data.label : data.label + '${' + data.dataId + '}'}</span>)
    },
    initData() {
      getReportTemplateChipTree().then(res => {
        this.reportTemplateList = res.data
      })
    },
    mouseenterEvent($event) {
      $event.currentTarget.className = 'mouse-active'
    },
    mouseleaveEvent($event) {
      $event.currentTarget.className = ''
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getReportTemplatePage(searchQuery)
    },
    dataFormatter(row, data) {
      const idList = row.templateChipIds.split(',')
      const arrayList = idList.map(id => {
        for (const template of this.reportTemplateList) {
          const index = template.children.findIndex(item => item.dataId === id)
          if (index >= 0) {
            return template.children[index].label
          }
        }
      })
      return arrayList.join('，') + '。'
    },
    handleCreate() {
      this.dlgVisible = true
      this.dialogStatus = 'create'
      this.resetForm()
    },
    handleDelete() {
      const ids = this.gridTable.getSelectedKeys().join(',')
      delReportTemplate(ids).then(res => {
        if (res) {
          this.$store.dispatch('commonData/setReportTypeChange')
          this.gridTable.execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleUpdate(row, data) {
      this.resetForm()
      this.dlgVisible = true
      this.dialogStatus = 'update'
      this.$nextTick(() => {
        this.temp = JSON.parse(JSON.stringify(row))
        if (row.templateHandleType === 1) {
          this.dataIndexListKeys = row.templateChipIds.split(',')
        } else {
          this.templateContent = row.templateInfo
          this.inputText(this.templateContent)
        }
      })
    },
    handleSubmit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid && this.formValidator()) {
          const data = this.formatToSubmitData()
          this.submitting = true
          if (this.dialogStatus === 'create') {
            addReportTemplate(data).then(res => {
              this.submitting = false
              if (res.data) {
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.saveSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }
              this.dlgVisible = false
              this.$store.dispatch('commonData/setReportTypeChange')
              this.gridTable.execRowDataApi()
            }).catch(() => {
              this.submitting = false
            })
          } else if (this.dialogStatus === 'update') {
            updateReportTemplate(data).then(res => {
              this.submitting = false
              if (res.data) {
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.updateSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }
              this.dlgVisible = false
              this.$store.dispatch('commonData/setReportTypeChange')
              this.gridTable.execRowDataApi()
            }).catch(() => {
              this.submitting = false
            })
          }
        }
      })
    },
    formatToSubmitData() {
      const formData = Object.assign({}, this.temp)
      if (formData.templateHandleType === 1) {
        formData.templateInfo = this.splitCharRadio !== '0' ? this.splitCharRadio : this.splitChar
        const dataIndexList = this.dataIndexList
        formData.templateChipIds = dataIndexList.map(item => item.dataId).join(',')
      } else {
        formData.templateInfo = formData.templateContent
      }
      return formData
    },
    formatBrowse(str) {
      return str.replaceAll('%s', 'XX')
    },
    formatSplitChar(index, arrayList) {
      return index === arrayList.length - 1 ? '。' : '，'
    },
    changeBrowseResult() {
      let str = ''
      const dataIndexList = this.dataIndexList
      for (let i = 0; i < dataIndexList.length; i++) {
        str += this.formatBrowse(dataIndexList[i].oriData.str) + this.formatSplitChar(i, dataIndexList)
      }
      this.previewEffect = str
    },
    handleDragStart(e, items) {
      this.dragging = items
      e.currentTarget.className = ''
    },
    handleDragOver(e, items) {
      e.dataTransfer.dropEffect = 'move'
    },
    deleteChooseIndex(data) {
      for (let i = 0; i < this.dataIndexListKeys.length; i++) {
        if (this.dataIndexListKeys[i] === data.dataId) {
          this.dataIndexListKeys.splice(i, 1)
          break
        }
      }
      for (let i = 0; i < this.dataIndexList.length; i++) {
        if (this.dataIndexList[i].dataId === data.dataId) {
          this.dataIndexList.splice(i, 1)
          break
        }
      }
    },
    handleDragEnter(e, items) {
      e.dataTransfer.effectAllowed = 'move'
      if (this.dragging === items) return
      const newItem = [...this.dataIndexList]
      const src = newItem.indexOf(this.dragging)
      const dst = newItem.indexOf(items)
      newItem.splice(dst, 0, ...newItem.splice(src, 1))
      this.dataIndexList = newItem
      this.changeBrowseResult()
    },
    handleDragEnd(e, items) {
      this.dragging = null
    },
    dataIndexChange(keys, node) {
      const dataIndexList = this.dataIndexList
      for (let i = node.length - 1; i >= 0; i--) {
        if (!node[i].oriData) {
          node.splice(i, 1)
          keys.splice(i, 1)
        }
      }
      this.dataIndexListKeys = keys
      if (dataIndexList && dataIndexList.length > 0) {
        const temp = []
        if (dataIndexList.length < node.length) {
          node.forEach(item => {
            const index = dataIndexList.findIndex(dataIndex => dataIndex.dataId === item.dataId)
            if (index < 0) {
              temp.push(item)
            }
          })
          this.dataIndexList = dataIndexList.concat(temp)
        } else if (dataIndexList.length > node.length) {
          for (let i = dataIndexList.length - 1; i >= 0; i--) {
            const index = node.findIndex(item => item.dataId === dataIndexList[i].dataId)
            if (index < 0) {
              dataIndexList.splice(i, 1)
            }
          }
        }
      } else {
        // 当选中的指标是空时，说明是初始化，update时，初始化需要调整位置
        if (this.dialogStatus === 'update') {
          const templateChipIdList = this.temp.templateChipIds.split(',')
          for (let i = 0; i < templateChipIdList.length; i++) {
            for (let j = 0; j < node.length; j++) {
              if (node[j].dataId === templateChipIdList[i]) {
                if (i === j) {
                  break
                }
                const temp = node[i]
                node[i] = node[j]
                node[j] = temp
              }
            }
          }
        }
        this.dataIndexList = node
      }
      this.changeBrowseResult()
    },
    addDataIndexContent(data, node, el) {
      let insertStr = ''
      if (data.oriData) {
        insertStr = this.formatByPlaceholder(data.oriData)
      } else {
        insertStr = data.children.map(children => this.formatByPlaceholder(children.oriData)).join(',')
      }
      const content = this.templateContent || ''
      const len = content.length
      if (!this.hasEnter) {
        this.blurIndex = len
      }
      this.inputText(content.slice(0, this.blurIndex) + insertStr + content.slice(this.blurIndex))
    },
    addDataIndex(data, node, el) {
      let insertStr = ''
      if (data.oriData) {
        insertStr = '${' + data.dataId + '}'
      }
      const content = this.templateContent || ''
      const len = content.length
      if (!this.hasEnter) {
        this.blurIndex = len
      }
      this.inputText(content.slice(0, this.blurIndex) + insertStr + content.slice(this.blurIndex))
    },
    formatByPlaceholder(data) {
      return data.str.replace('%s', '${' + data.id + '}')
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    formValidator() {
      if (this.temp.templateHandleType === 1) {
        if (!this.dataIndexList || this.dataIndexList.length === 0) {
          this.$message({
            message: this.$t('pages.validatorText1'),
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      return true
    },
    blurEvent(e) {
      this.blurIndex = e.srcElement.selectionStart
      this.hasEnter = true
    },
    nameValidator(rule, value, callback) {
      getReportTemplateByName({ name: this.temp.businessName }).then(respond => {
        const data = respond.data
        if (data && data.id !== this.temp.id) {
          callback(new Error(this.$t('pages.ReportTemplateValidatorText2')))
        } else {
          callback()
        }
      })
    },
    inputText(val) {
      this.templateContent = val
      this.temp.templateContent = val
      this.$refs.dataForm.validateField('templateContent')
    }
  }
}
</script>

<style lang="scss" scoped>
.reportIndexPanel {
  height: 100%;

  .custom-tree-node {
    justify-content: space-between;
    width: 100%;
  }
}

.mouse-active {
  cursor: pointer;
  background-color: rgb(204, 204, 204);
}
</style>
