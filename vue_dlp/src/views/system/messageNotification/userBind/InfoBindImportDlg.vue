<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[type]"
      :visible.sync="dlgVisible"
      width="800px"
    >
      <el-container>
        <el-aside width="210px">
          <tree-menu
            v-show="type == 1"
            ref="groupTree"
            :default-expand-all="true"
            :height="400"
            multiple
            check-strictly
            node-key="dataId"
            :data="roleTreeData"
            @node-click="treeNodeClick"
          />
          <tree-menu
            v-show="type == 2"
            ref="userGroupTree"
            :local-search="false"
            :get-search-list="getSearchListFunction"
            node-key="dataId"
            :height="400"
            multiple
            :data="userGroupTreeData"
            @node-click="userTreeNodeClick"
          />
        </el-aside>
        <el-main>
          <div>
            <div class="toolbar">
              <div style="float: right;">
                <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('components.searchPlaceholder_3')" style="width: 130px;" @keyup.enter.native="handleFilter" />
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <div style="height: 360px;">
              <grid-table
                ref="gridTable"
                :height="360"
                pager-small
                :col-model="colModel"
                :row-data-api="loadAccountList"
              />
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleImport">
          {{ $t('button.confirm2') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { findNode, toNodeValue } from '@/utils/tree';
import { getPage } from '@/api/system/organizational/sysUser';
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department';
import { isSameTimestamp } from '@/utils'
import { getUserPage } from '@/api/system/terminalManage/user';
import { bindCloudInfo } from '@/api/system/messageNotification/messagePush';

export default {
  name: 'InfoBindImportDlg',
  props: {
    type: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      dlgVisible: false,
      textMap: {
        1: this.$t('pages.importGivingUserInfo1'),
        2: this.$t('pages.importGivingUserInfo2')
      },
      submitting: false,
      colModel: [
        { prop: 'account', label: 'account', width: '150' },
        { prop: 'name', label: 'name', width: '150' },
        { prop: 'phone', label: 'phone', width: '150' },
        { prop: 'email', label: 'email', width: '150' }
      ],
      editAbleRoleId: [],
      roleTreeData: [{ id: '', dataId: '0', label: this.$t('pages.sysUserRole'), parentId: '0', children: [], disabled: true }],
      roleSelectNode: [],
      userGroupTreeData: [{ id: '', dataId: '0', label: this.$t('pages.userGroup'), parentId: '0', children: [], disabled: true }],
      rowDatas: [],
      query: {
        searchInfo: '',
        roleId: ''
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTable']
    },
    userTreeNode() {
      return this.$refs['groupTree']
    },
    terminalTreeNode() {
      return this.$refs['userGroupTree']
    }
  },
  watch: {
    '$store.state.commonData.roleTree'() {
      this.roleTreeData[0].children = this.$store.getters.roleTree
      this.changeTreeSelectNode()
    },
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    }
  },
  activated() {
    if (!isSameTimestamp(this, 'Department') || !isSameTimestamp(this, 'Terminal')) {
      this.initGroupTreeNode()
    }
  },
  created() {
    this.loadTreeData()
    this.initGroupTreeNode()
  },
  methods: {
    initShow() {
      this.dlgVisible = true
      this.query = { searchInfo: '', roleId: '', groupIds: '' }
      this.$nextTick(() => {
        this.gridTable.clearRowData()
        if (this.type == 1) {
          this.userTreeNode.clearSelectedNodes()
        } else if (this.type == 2) {
          this.terminalTreeNode.clearSelectedNodes()
        }
        this.gridTable.execRowDataApi()
      })
    },
    loadAccountList(option) {
      const type = this.type
      const param = Object.assign({}, this.query, option)
      if (type == 1) {
        return getPage(param)
      } else if (type == 2) {
        return getUserPage(param)
      }
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: { total: 0, items: null }})
      })
    },
    treeNodeClick(data, checkedNode) {
      if (data.dataId == 0) data.dataId = undefined
      this.handleFilter(Object.assign({}, this.query, { roleId: data.dataId }))
    },
    userTreeNodeClick(data, checkedNode) {
      const groupIds = []
      if (data.id.indexOf('G') > -1) {
        groupIds.push(data.dataId)
      }
      if (data.children && data.children.length > 0) {
        this.getAllChildrenGroupId(data.children, groupIds)
      }
      this.handleFilter(Object.assign({}, this.query, { groupIds: groupIds.join(',') }))
    },
    // 获取所有子部门
    getAllChildrenGroupId(groups, groupIds) {
      groups.forEach(group => {
        if (group.id.indexOf('G') > -1) {
          groupIds.push(group.dataId)
          if (group.children && group.children.length > 0) {
            this.getAllChildrenGroupId(group.children, groupIds)
          }
        }
      })
    },
    initGroupTreeNode: function() {
      getDeptTreeFromCache().then(respond => {
        this.userGroupTreeData[0].children = respond.data
      })
    },
    handleImport() {
      const type = this.type
      const data = this.gridTable.getSelectedKeys()
      const param = {}
      if (type == 1) {
        param.userIds = data
        param.roleIds = this.userTreeNode.getCheckedKeys()
      } else if (type == 2) {
        param.terminalUserIds = data
        param.deptIds = this.terminalTreeNode.getCheckedKeys()
      }
      this.submitting = true
      bindCloudInfo(param).then(res => {
        if (res.data) {
          this.dlgVisible = false
          this.$emit('successImport')
          this.submitting = false
        }
      }).catch(() => {
        this.submitting = false
      })
    },
    handleFilter(option) {
      this.query.page = 1
      this.gridTable.execRowDataApi(Object.assign({}, this.query, option))
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    loadTreeData: function() {
      this.$store.dispatch('commonData/setRoleTree')
    },
    changeTreeSelectNode() {
      // this.roleSelectNode.splice(0)
      this.editAbleRoleId.splice(0)
      if (this.$store.getters.isSuperRole) {
        this.roleSelectNode.splice(0, 0, ...this.roleTreeData[0].children)
      } else {
        const curUserRoleIds = this.$store.getters.roles
        curUserRoleIds.forEach(roleId => {
          const nodeData = findNode(this.roleTreeData, roleId, 'dataId')
          if (nodeData) {
            // this.roleSelectNode.push(nodeData)
            this.editAbleRoleId.splice(0, 0, ...toNodeValue(nodeData, 'dataId'))
          }
        })
      }
    }
  }
}
</script>
