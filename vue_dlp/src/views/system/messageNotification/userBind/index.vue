<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" :disabled="unCloudConnect" size="mini" @click="handleCreate">
          {{ $t('pages.givingUserInfo') }}
        </el-button>
        <el-button icon="el-icon-download" :disabled="!exportable || unCloudConnect" size="mini" @click="handleExport">
          {{ $t('button.export') + $t('pages.wechatBingQRCode') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.userAndPhone')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      ></grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.givingUserInfo')"
      :visible.sync="chooseDlgVisible"
      width="400px"
    >
      <el-radio-group v-model="chooseRadio">
        <el-radio :label="1">{{ $t('pages.givingUserRadio1') }}
          <el-tooltip class="item" effect="dark" placement="right">
            <i class="el-icon-info" />
            <div slot="content">{{ $t('pages.givingUserRadio4') }}</div>
          </el-tooltip>
        </el-radio>
        <el-radio :label="2">{{ $t('pages.givingUserRadio2') }}
          <el-tooltip class="item" effect="dark" placement="right">
            <i class="el-icon-info" />
            <div slot="content">{{ $t('pages.givingUserRadio5') }}</div>
          </el-tooltip>
        </el-radio>
        <el-radio :label="3">{{ $t('pages.givingUserRadio3') }}</el-radio>
      </el-radio-group>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSelect">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="chooseDlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[bindDlg]"
      :visible.sync="dlgVisible"
      width="500px"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="100px" style="width: 450px;">
        <FormItem :label="$t('table.name')" prop="userName">
          <el-input v-model="temp.userName" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('table.phone')" prop="tel">
          <el-input v-model="temp.tel" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('table.email')" prop="mail">
          <el-input v-model="temp.mail" v-trim :maxlength="60"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" :disabled="unCloudConnect" type="primary" @click="handleSure">
          {{ $t('button.confirm2') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :modal="false"
      :title="userStatusTitle"
      :visible.sync="userStatusDlg"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="medium" >
          <el-descriptions-item :label="$t('pages.userName1')">
            {{ rowDetail.userName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operate')">
            <el-button :disabled="unCloudConnect" type="text" size="small" style="float: left; margin: 0" @click="synchronousStatus">{{ $t('pages.synchronizingBindingStatus') }}</el-button>
            <el-tooltip class="item" effect="dark" :content="$t('pages.resetGivingUserInfoText')" placement="right-start">
              <el-button :disabled="unCloudConnect" type="text" size="small" style="margin: 0" @click="resetGiving">{{ $t('pages.resetGivingUserInfo') }}</el-button>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.bindUserStatus')">
            <span v-if="rowDetail.active == 1">{{ $t('pages.bindUserStatusText1') }}</span>
            <p v-if="rowDetail.active == 2" style="margin: 0">{{ $t('pages.bindUserStatusText2') }}</p>
            <p v-if="rowDetail.active == 2 && rowDetail.weChatInfo" style="margin: 0">{{ $t('pages.bindUserStatusText4') + JSON.parse(rowDetail.weChatInfo).bindTime }}</p>
            <p v-if="rowDetail.active == 3" style="margin: 0">{{ $t('pages.bindUserStatusText3') }}</p>
            <p v-if="rowDetail.active == 3 && rowDetail.weChatInfo" style="margin: 0">{{ $t('pages.bindUserStatusText5') + JSON.parse(rowDetail.weChatInfo).unbindTime }}</p>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.email')">
            {{ rowDetail.mail }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.phone')">
            {{ rowDetail.tel }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.bindCode')">
            {{ rowDetail.shortCode }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div style="padding-left: 20px">
        <Form>
          <FormItem v-if="rowDetail.tel" :label="$t('pages.bindMethod') + ': ' " label-width="70px">
            {{ $t('pages.bindUserText1') }}
            <br>
            <span>{{ $t('pages.bindUserText2') }}</span>
            <br>
            <div style="display: flex; justify-content: center">
              <el-image v-if="rowDetail.qrCodeBase64" style="width: 150px; height: 150px; margin-left: -60px;" :src="'data:image/png;base64,' + rowDetail.qrCodeBase64"></el-image>
            </div>
            <span>{{ $t('pages.bindUserText3') }}</span>
            <Form label-position="right">
              <FormItem :label="$t('table.account') + ': '">
                {{ rowDetail.tel }}
              </FormItem>
              <FormItem :label="$t('table.bindCode') + ': '">
                {{ rowDetail.shortCode }}
              </FormItem>
            </Form>
          </FormItem>
          <FormItem v-else :label="$t('pages.bindMethod') + ': ' " label-width="70px">
            {{ $t('pages.bindUserText4') }}
            <br>
            <div style="display: flex; justify-content: center">
              <el-image v-if="rowDetail.qrCodeBase64" style="width: 150px; height: 150px; margin-left: -60px;" :src="'data:image/png;base64,' + rowDetail.qrCodeBase64"></el-image>
            </div>
            <span style="color: red">{{ $t('pages.care') + "：" }}</span>
            <span>{{ $t('pages.bindUserText5') }}</span>
          </FormItem>
        </Form>
      </div>
    </el-dialog>
    <info-bind-import-dlg ref="userInfoBindImport" :type="chooseRadio" @successImport="successImport"></info-bind-import-dlg>
  </div>
</template>

<script>

import {
  bindCloudInfo, exportCloudBindCode, getCloudInfoBind,
  getCloudInfoByName, updateGivingInfo, updateUserStatus
} from '@/api/system/messageNotification/messagePush';
import InfoBindImportDlg from '@/views/system/messageNotification/userBind/InfoBindImportDlg';
import { testCloudConnect } from '@/api/system/deviceManage/cloudServer';

export default {
  name: 'UserBind',
  components: { InfoBindImportDlg },
  data() {
    return {
      colModel: [
        { prop: 'userName', label: 'bindUserName', width: '150', formatter: this.userNameFormatter },
        { prop: 'tel', label: 'phone', width: '120' },
        { prop: 'mail', label: 'email', width: '120' },
        { prop: 'shortCode', label: 'bindCode', width: '120' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '160',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'seeBindInfo', click: this.seeUserStatus }
          ]
        }
      ],
      chooseDlgVisible: false,
      chooseRadio: 1,
      unCloudConnect: false,
      query: {},
      temp: {},
      exportable: false,
      submitting: false,
      userStatusDlg: false,
      userStatusTitle: '',
      textMap: {
        'create': this.$t('pages.givingOtherUser1'),
        'update': this.$t('pages.givingOtherUser2')
      },
      updateTemp: {},
      bindDlg: 'create',
      rowDetail: {},
      rules: {
        userName: [
          { required: true, message: this.$t('pages.validateMsg_user'), trigger: 'blur' },
          { validator: this.validateUniqueName, trigger: 'blur' }
        ],
        tel: [{ type: 'number', validator: this.checkPhone, trigger: 'blur' }],
        mail: [{ validator: this.validateMail, trigger: 'blur' }]
      },
      dlgVisible: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs.gridTable
    }
  },
  watch: {
    '$store.state.commonData.cloudAddress'(val) {
      this.testConnect()
    }
  },
  created() {
    this.testConnect()
  },
  methods: {
    testConnect() {
      testCloudConnect().then(res => {
        this.unCloudConnect = false
      }).catch(reason => {
        this.unCloudConnect = true
      })
    },
    userNameFormatter(row, data) {
      let str = ''
      if (row.userType == 1) {
        str = '（' + this.$t('pages.givingUserRadio1') + '）'
      } else if (row.userType == 2) {
        str = '（' + this.$t('pages.givingUserRadio2') + '）'
      } else {
        str = '（' + this.$t('pages.givingUserRadio3') + '）'
      }
      return data + str
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getCloudInfoBind(searchQuery)
    },
    handleCreate() {
      this.chooseDlgVisible = true
      // this.resetTemp()
    },
    handleUpdate(row) {
      this.dlgVisible = true
      this.bindDlg = 'update'
      this.updateTemp = row
      this.temp = Object.assign({}, row)
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    synchronousStatus() {
      updateUserStatus(this.rowDetail).then(res => {
        this.rowDetail = res.data
        this.gridTable.execRowDataApi()
        this.$notify({ title: this.$t('text.success'), message: this.$t('pages.updateUserStatus'), type: 'success', duration: 2000 })
      })
    },
    seeUserStatus(row) {
      this.rowDetail = row
      this.userStatusDlg = true
      this.userStatusTitle = row.userName + this.$t('pages.bindUserText6')
    },
    handleExport() {
      const selectIds = this.gridTable.getSelectedKeys()
      exportCloudBindCode({ ids: selectIds })
    },
    validateUniqueName(rule, value, callback) {
      if (this.temp.userType == 1 || this.temp.userType == 2) {
        callback()
      }
      getCloudInfoByName({ name: this.temp.userName }).then(res => {
        if (res.data && res.data.id != this.temp.id) {
          callback(new Error(this.$t('pages.bindValidateText')))
        }
        callback()
      })
    },
    validateMail(rule, value, callback) {
      if (value == undefined || value == '') callback()
      const test = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(value);
      if (!test) {
        callback(new Error(this.$t('pages.validateMsg_email2')))
      }
      callback()
    },
    successImport() {
      this.chooseDlgVisible = false
      this.$store.dispatch('commonData/setBindUser')
      this.gridTable.execRowDataApi()
    },
    handleSure() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitting = true
          const bindDlg = this.bindDlg
          if (bindDlg == 'create') {
            bindCloudInfo(this.temp).then(res => {
              if (res.data) {
                this.gridTable.execRowDataApi()
                this.submitNotify()
              }
              this.closeDlg()
            }).catch(() => {
              this.submitting = false
            })
          } else if (bindDlg == 'update') {
            updateGivingInfo(this.temp).then(res => {
              if (res.data) {
                this.gridTable.execRowDataApi()
                this.submitNotify()
              }
              this.closeDlg()
            }).catch(() => {
              this.submitting = false
            })
          }
        }
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetGiving() {
      const t = Object.assign({}, this.rowDetail)
      t.qrCodeBase64 = null
      updateGivingInfo(t).then(res => {
        if (res.data) {
          this.rowDetail = res.data
          if (this.rowDetail.shortCode) {
            this.gridTable.execRowDataApi()
          }
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.againSubmitSuccess'),
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    submitNotify() {
      this.$store.dispatch('commonData/setBindUser')
      this.$notify({
        title: this.$t('text.success'),
        message: this.bindDlg == 'create' ? this.$t('text.submitSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    closeDlg() {
      this.submitting = false
      this.dlgVisible = false
      this.chooseDlgVisible = false
    },
    selectionChangeEnd(selections) {
      if (selections.length > 0) {
        this.exportable = true
      } else {
        this.exportable = false
      }
    },
    handleSelect() {
      const radio = this.chooseRadio
      if (radio == 3) {
        this.dlgVisible = true
        this.bindDlg = 'create'
        this.temp = Object.assign({}, { account: '', userName: '', mail: '', tel: '' })
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      } else {
        this.$refs.userInfoBindImport.initShow()
      }
    },
    checkPhone(rule, value, callback) {
      if (value == undefined || value == '') { callback() }
      const reg = /^1[345789]\d{9}$/
      if (!reg.test(value)) {
        callback(new Error('请输入11位手机号'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-radio{
    display: block;
    margin:5px 0;
  }
  >>>.el-descriptions-item__label {
    text-align: right;
  }
  >>>.el-descriptions-item__content {
    text-align: left;
  }
</style>
