<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <TimeQuery @getTimeParams="getTimeParams"/>
        <span><label>{{ $t('table.sendStatus') }}：</label>
          <el-select v-model="query.statusVo" filterable :placeholder="$t('pages.all')" style="width: 180px;">
            <el-option v-for="(item, i) in statusMap" :key="i" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </span>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <el-button v-permission="'389'" v-loading="resending" icon="el-icon-refresh" :disabled="!retrySend" size="mini" @click="resendData">
          {{ $t('button.resend') }}
        </el-button>
      </div>
      <grid-table ref="pushingRecordList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="handleSelectionChange"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :modal="false"
      :title="$t('pages.msgPushInfo')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions v-if="rowDetail" class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.pushTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.msgPushMethod')">
            {{ pushTypeFormatter(rowDetail, rowDetail.taskClass) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.msgPushContent')">
            {{ rowDetail.taskInfo }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.msgPushObject')">
            {{ rowDetail.receiver }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.sendStatus')">
            {{ sendStatusFormatter(rowDetail, rowDetail.status) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.remark')">
            {{ statusJsonFormatter(rowDetail, rowDetail.statusJson) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="rowDetail['formDTOS'].length > 0" :label="`消息详情`">
            <Form label-position="right" label-width="80px">
              <FormItem v-for="(item, index) in rowDetail['formDTOS']" :key="index" :label="`${item.label}：`">{{ item.value }}</FormItem>
              <FormItem :label="`${$t('table.details')}：`">{{ rowDetail['param']['response']['detail'] }}</FormItem>
            </Form>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 剔除推送记录的预览消息， 交由审计权限的管理员并不一定有报表权限，审计仅提供给运维人员方便查询推送状态，不具备推送消息预览； 后续可针对预览作权限控制，暂不需要-->
    <!--    <preview-push-msg ref="previewPushMsg" :push-types="previewSendData.pushTypes" :preview-data="previewSendData.previewData"/>-->
  </div>
</template>

<script>
import { getLogDetail, getMsgLogPage, resendMsg } from '@/api/system/messageNotification/messagePushLog'
import { getPushTypeDict } from '@/utils/messagePush';

export default {
  name: 'PushingRecord',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'pushTime', width: '120', sort: true },
        // 复杂的格式化方法，直接交由后端进行格式化
        { prop: 'taskInfo', label: 'msgPushContent', width: '150' },
        { prop: 'taskClass', label: 'msgPushMethod', width: '100', formatter: this.pushTypeFormatter },
        { prop: 'receiver', label: 'msgPushObject', width: '150' },
        { prop: 'status', label: 'sendStatus', width: '100', formatter: this.sendStatusFormatter },
        { prop: 'statusJson', label: 'remark', width: '120', formatter: this.statusJsonFormatter },
        {
          label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      retrySend: false,
      query: { page: 1, bizCode: 1001 },
      rowDetail: undefined,
      resending: false,
      dialogFormVisible: false,
      pushTypeDict: getPushTypeDict(),
      previewSendData: {},
      successCode: 200,
      statusMap: [
        { label: this.$t('pages.all'), value: undefined },
        { label: this.$t('pages.sendSuccess'), value: 1 },
        { label: this.$t('pages.sendFail'), value: 0 }
      ]
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getMsgLogPage(searchQuery)
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleView(row) {
      this.rowDetail = Object.assign({}, row, { param: {}, formDTOS: [] })
      this.dialogFormVisible = true
      this.rowDetail['param'] = JSON.parse(this.rowDetail.taskJson).param || {}
      // 如果范围不为公司的话的话，因为涉及到操作员和终端的查询，所以交由请求后端进行处理
      getLogDetail(row.id).then(res => {
        if (res.data) {
          this.rowDetail['formDTOS'] = [...res.data]
        }
      })
    },
    pushTypeFormatter(row, data) {
      if (data.indexOf('MailSendTask') > -1) {
        return this.pushTypeDict[3]
      } else if (data.indexOf('SmsSendTask') > -1) {
        return this.pushTypeDict[1]
      } else if (data.indexOf('WeChatSendTask') > -1) {
        return this.pushTypeDict[2]
      } else if (data.indexOf('ControllerSendTask') > -1) {
        return this.pushTypeDict[4]
      }
      return ''
    },
    sendStatusFormatter(row, data) {
      return data === this.successCode ? this.$t('pages.sendSuccess') : this.$t('pages.sendFail')
    },
    statusJsonFormatter(row, data) {
      return data && row.status !== this.successCode ? JSON.parse(data)['msg'] : ''
    },
    handleFilter() {
      this.query.page = 1
      this.$refs['pushingRecordList'].execRowDataApi(this.query)
    },
    handleSelectionChange(selections) {
      this.retrySend = selections.length > 0
    },
    refresh() {
      this.$refs['pushingRecordList'].execRowDataApi()
    },
    resendData() {
      this.resending = true
      const selectedIds = this.$refs['pushingRecordList'].getSelectedIds();
      resendMsg(selectedIds).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.alreadyAgainSendMsg'),
          type: 'success',
          duration: 2000
        })
        this.$refs['pushingRecordList'].execRowDataApi()
        this.resending = false
      }).catch(res => {
        this.resending = false
      })
    }
  }
}
</script>
