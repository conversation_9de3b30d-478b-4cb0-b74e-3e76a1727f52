<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="true"
    :modal="false"
    :title="$t('pages.browsePushMsg')"
    :visible.sync="visible"
    width="600px"
    @close="visible = false"
  >
    <Form style="width: 500px" label-width="90px" label-position="right">
      <FormItem :label="$t('table.msgPushMethod')">
        <el-radio-group v-model="radio">
          <el-radio v-for="(value, key, index) in pushTypeDict" v-show="pushTypes.includes(key)" :key="index" :label="key">{{ value }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.msgBrowse')">
        <el-tooltip slot="tooltip" effect="dark" placement="bottom-end">
          <div slot="content">
            <span v-if="previewData && previewData.length > 0 && !previewData[0].isLogDetail" style="display: block">{{ $t('pages.previewMsgTipOne') }}</span>
            <span v-if="previewData && previewData.length > 0 && !previewData[0].isLogDetail">{{ $t('pages.previewMsgTipTwo') }}</span>
            <span v-if="previewData && previewData.length > 0 && previewData[0].isLogDetail">{{ $t('pages.previewMsgTipThree') }}</span>
          </div>
          <i class="el-icon-info"/>
        </el-tooltip>
        <el-card style="max-height: 400px">
          <div v-if="radio == 2">
            <div class="card_header">
              <p style="margin: 0">{{ $t('pages.weChatPublicNumName') }}</p>
            </div>
            <div style="margin: 0; display: block; height: 1px; width: 100%; background-color: #adaeaf"></div>
            <div class="content">
              <el-card v-for="(item, index) in previewData" :key="index" class="inner_card">
                <div v-if="templateData" class="preview_msg_template" style="white-space:pre-line;" v-html="formatPreviewValue(templateData, 'A0004', item)">

                </div>
              </el-card>
            </div>
          </div></el-card>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" style="float: left" @click="updateTemplate">
        {{ $t('pages.updateMsgTemplate') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSystemResources } from '@/utils/i18n'
import { getPushTypeDict } from '@/utils/messagePush';
import { getPreviewTemplate, updatePreviewTemplate } from '@/api/system/messageNotification/messagePush';
import { getReportTemplateChipTree } from '@/api/system/messageNotification/customizeReportTemplate';

export default {
  name: 'PreviewPushMsg',
  props: {
    previewData: {
      type: Array,
      default() {
        return []
      }
    },
    reportScope: {
      type: String,
      default: ''
    },
    dateValue: {
      type: String,
      default: ''
    },
    dimBaseName: {
      type: String,
      default: ''
    },
    splitChar: {
      type: String,
      default: '，'
    },
    pushTypes: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      radio: 0,
      pushTypeDict: undefined,
      reportTemplateChip: undefined,
      reportSet: undefined,
      templateData: undefined
    }
  },
  computed: {
    title() {
      return getSystemResources('title')
    }
  },
  watch: {
    '$store.state.commonData.cloudAddress'(val) {
      this.getTemplate()
    }
  },
  created() {
    this.pushTypeDict = getPushTypeDict()
    this.initReportTemplateChip()
    this.getTemplate()
  },
  methods: {
    handleCreate() {
      this.visible = true
      this.pushTypes.sort()
      this.radio = String(this.pushTypes[0])
    },
    initReportTemplateChip() {
      getReportTemplateChipTree().then(res => {
        this.reportTemplateChip = res.data
      })
    },
    formatPreviewValue(previewTemplate, number, param) {
      let str = ''
      if (number == 'A0004') {
        str += previewTemplate[number]
        if (param.isLogDetail) {
          str = str.replace('{{title}}', param.title)
          str = str.replace('{{systemName}}', param.systemName)
          str = str.replace('{{date}}', param.date)
          str = str.replace('{{type}}', param.type)
          str = str.replace('{{content}}', param.content)
          str = str.replace('{{remark}}', '')
        } else {
          str = str.replace('{{title}}', param.label + this.dimBaseName + '已生成')
          str = str.replace('{{systemName}}', this.title)
          str = str.replace('{{date}}', this.dateValue)
          str = str.replace('{{type}}', param.label + '-' + this.reportScope)
          str = str.replace('{{content}}', this.formatResult(param))
          str = str.replace('{{remark}}', '')
        }
      }
      return str
    },
    updateTemplate() {
      updatePreviewTemplate().then(res => {
        if (res.data) {
          this.templateData = {}
          res.data.forEach(item => {
            this.templateData[item.bizCode] = item.preview
          })
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.syncSuccess'),
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    getTemplate() {
      getPreviewTemplate().then(res => {
        this.templateData = {}
        res.data.forEach(item => {
          this.templateData[item.bizCode] = item.preview
        })
      })
    },
    formatResult(data) {
      const dataId = data.dataId
      let str = ''
      if (dataId.startsWith('R')) {
        const index = this.reportTemplateChip.findIndex(item => item.dataId == dataId)
        if (index >= 0) {
          str = this.reportTemplateChip[index].children.map(item => item.oriData.str.replace('%s', 'XX')).join(this.splitChar)
        }
      } else if (dataId.startsWith('C')) {
        const oriData = data.oriData
        const ids = oriData.templateChipIds.split(',')
        str = ids.map(id => {
          for (const chip of this.reportTemplateChip) {
            const index = chip.children.findIndex(item => item.oriData.id == id)
            if (index >= 0) {
              const tempData = chip.children[index].oriData
              return tempData.str.replace('%s', 'XX')
            }
          }
        }).filter(item => item != '').join(this.splitChar)
      }
      return str
    }
  }
}
</script>

<style lang="scss" scoped>
  .card_header {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .content {
    height: 330px;
    overflow-y: auto;
  }
  .inner_card {
    margin: 10px auto;
    width: 350px;
    background-color: white;
  }
  >>>.preview_msg_template > div {
    display: flex;
  }
  >>>.preview_msg_template > div span:first-child {
    width: 80px;
    font-weight: bold;
  }
  >>>.preview_msg_template > div span:last-child {
    flex: 1;
  }
</style>
