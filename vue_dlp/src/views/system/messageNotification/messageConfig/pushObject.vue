<template>
  <LocalZoomIn parent-tag="el-dialog">
    <data-editor
      v-if="editable"
      append-to-body
      :popover-width="500"
      :addable="editable"
      :updateable="editable && updateable"
      :deletable="editable && deleteable"
      :add-func="createReceiver"
      :update-func="updateReceiver"
      :delete-func="deleteReceiver"
      :before-add="beforeAddReceiver"
      :before-update="beforeUpdateReceiver"
      :cancel-func="resetPushObj"
    >
      <Form ref="receiverForm" :model="pushObj" :rules="pushObjRules" label-position="right" label-width="85px" style="margin-left: -10px">
        <FormItem :label="$t('table.msgPushMethod')" prop="pushType">
          <el-select v-model="pushObj.pushType" :disabled="updateObj" class="push-type-class" @change="clearValidate">
            <el-option v-for="(value, key) in pushTypeDict" :key="key" :label="value" :value="Number(key)"/>
          </el-select>
        </FormItem>
        <!--邮件推送-->
        <div v-show="!formSysUserPush" class="mail-class">
          <FormItem :label="$t('table.nickName')" prop="name">
            <el-input v-model="pushObj.name" max-length="32"/>
          </FormItem>
          <FormItem :label="$t('table.account')" prop="receiver">
            <el-input v-model="pushObj.receiver" max-length="64"/>
          </FormItem>
        </div>
        <!--控制台首页面板+控制台告警推送-->
        <FormItem v-if="formSysUserPush" label-width="24px" prop="sysUserIds">
          <el-select
            v-model="pushObj.sysUserIds"
            :disabled="!editable"
            filterable
            clearable
            :multiple="!updateObj"
            :placeholder="$t('pages.alarmSetup_text7')"
            style="width: 100%;"
            @change="sysUserChange"
          >
            <el-option v-for="user in sysUserList" :key="user.id" :label="user.name" :value="user.id" :disabled="updateObj && user.id === 0"/>
          </el-select>
        </FormItem>
      </Form>
    </data-editor>
    <grid-table
      ref="receiverTable"
      auto-height
      :multi-select="editable"
      :show-pager="false"
      :col-model="receiverColModel"
      :row-datas="rowDatas"
      :autoload="false"
      style="margin-bottom: 10px;"
      @selectionChangeEnd="receiverSelectionChange"
    />
  </LocalZoomIn>
</template>

<script>
import { getPushTypeDict } from '@/utils/messagePush'

export default {
  name: 'PushObject',
  props: {
    editable: { type: Boolean, default: true },
    pushTypeDict: { type: Object, default() { return getPushTypeDict() } },
    rowDatas: { type: Array, default() { return [] } }
  },
  data() {
    return {
      receiverColModel: [
        { prop: 'pushType', label: 'msgPushMethod', width: '130', formatter: (row, data) => { return this.pushTypeDict[data] } },
        { prop: 'name', label: 'nickName', width: '130' },
        { prop: 'receiver', label: 'account', width: '130', formatter: this.receiverFormatter }
      ],
      pushObj: {},
      defaultPushObject: {
        pushType: 3,
        receiver: '',
        name: '',
        sysUserIds: [],
        sysSync: false
      },
      pushObjRules: {
        name: [{ validator: this.nameValidate, trigger: 'blur' }],
        receiver: [{ validator: this.receiverValidate, trigger: 'blur' }],
        sysUserIds: [{ validator: this.sysUserIdsValidate, trigger: 'blur' }]
      },
      updateObj: false,
      updateable: false,
      deleteable: false
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    formSysUserPush() {
      const pushType = (this.pushObj || {})['pushType']
      return this.isSysUserPush(pushType)
    }
  },
  created() {
  },
  methods: {
    resetPushObj() {
      this.pushObj = JSON.parse(JSON.stringify(this.defaultPushObject))
      this.clearValidate()
    },
    isSysUserPush(pushType) {
      return pushType == 4 || pushType == 5
    },
    receiverFormatter(row, data) {
      if (this.isSysUserPush(row.pushType)) {
        return (this.sysUserList.find(r => r.id == row.receiver) || {}).account
      }
      return data
    },
    nameValidate(rule, value, callback) {
      if (this.pushObj['pushType'] == 3) {
        if (value === undefined || value === null || value === '') {
          callback(new Error(this.$t('pages.validateMsg_emailName')))
        }
      }
      callback()
    },
    receiverValidate(rule, value, callback) {
      if (this.pushObj['pushType'] == 3) {
        if (value === undefined || value === null || value === '') {
          callback(new Error(this.$t('text.cantNullInfo', { info: this.$t('table.account') })))
        } else if (!(/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/.test(value))) {
          callback(new Error(this.$t('pages.validaEmail')))
        }
      }
      callback()
    },
    sysUserIdsValidate(rule, value, callback) {
      if (this.pushObj['pushType'] == 4 || this.pushObj['pushType'] == 5) {
        if (Array.isArray(value) && (value || []).length === 0) {
          callback(new Error(this.$t('pages.alarmSetup_text7')))
        } else if (typeof value === 'number') {
          const { id, pushType } = this.pushObj
          const exist = this.rowDatas.some(item => item.id != id && pushType == item.pushType && item.receiver == value)
          if (exist) {
            callback(new Error(this.$t('valid.customizeSameName', { obj: this.$t('pages.sysUser') })))
          }
        }
      }
      callback()
    },
    clearValidate() {
      this.$refs['receiverForm'] && this.$refs['receiverForm'].clearValidate()
    },
    sysUserChange(selections) {
      const length = selections.length
      if (length > 1) {
        if (selections[length - 1] == 0 || length == this.sysUserList.length - 1) {
          // 当最后一个选择‘所有管理员’，或者选择了除‘所有管理员’的其他所有选项时，清空所有其他选项，只保留‘所有管理员’
          selections.splice(0, length, 0)
        } else if (selections[0] == 0) {
          // ‘所有管理员’在选项第一个时，去掉该选项
          selections.splice(0, 1)
        }
      }
      this.$refs['receiverForm'].validateField(['sysUserIds'])
    },
    receiverSelectionChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
      this.updateable = rowDatas.length === 1
    },
    createReceiver() {
      let validate
      this.$refs['receiverForm'].validate((valid) => {
        if (valid) {
          // 邮件推送
          const pushType = this.pushObj.pushType
          if (pushType == 3) {
            const exist = this.rowDatas.some(item => item.receiver === this.pushObj['receiver'])
            if (!exist) {
              const rowData = Object.assign({}, this.pushObj)
              rowData.id = new Date().getTime()
              delete rowData['sysUserIds']
              delete rowData['sysSync']
              this.rowDatas.unshift(rowData)
            }
          } else if (this.isSysUserPush(pushType)) {
            const popSet = new Set()
            const panelSet = new Set()
            this.rowDatas.forEach(item => {
              item['pushType'] == 4 && (popSet.add(item['receiver']))
              item['pushType'] == 5 && (panelSet.add(item['receiver']))
            })
            const handler = tempPushType => {
              const tempSet = tempPushType == 4 ? popSet : panelSet
              if (!tempSet.has('0')) {
                if (this.pushObj['sysUserIds'].includes('0')) {
                  // 使用 filter 方法过滤掉 pushType 等于 tempPushType 的元素
                  this.rowDatas = this.rowDatas.filter(item => item.pushType !== tempPushType);
                }
                const filterIds = this.pushObj['sysUserIds'].filter(id => !tempSet.has(String(id)))
                this.rowDatas.unshift(...this.formatSysObject(filterIds, tempPushType))
              }
            }
            if (pushType == 4 || this.pushObj.sysSync) { handler(4) }
            if (pushType == 5 || this.pushObj.sysSync) { handler(5) }
          }
          validate = valid
        }
      })
      return validate
    },
    updateReceiver() {
      let validate
      this.$refs['receiverForm'].validate((valid) => {
        if (valid) {
          const rowData = this.$refs['receiverTable'].getSelectedDatas()[0]
          const pushType = this.pushObj.pushType
          if (pushType == 3) {
            Object.assign(rowData, this.pushObj)
            delete rowData['sysUserIds']
            delete rowData['sysSync']
          } else if (this.isSysUserPush(pushType)) {
            rowData.receiver = this.pushObj['sysUserIds']
            const user = this.sysUserList.find(item => item.id == rowData.receiver)
            rowData['name'] = user.name
            rowData['receiver'] = user.account
          }
        }
        validate = valid
      })
      return validate
    },
    deleteReceiver() {
      const toDeleteIds = this.$refs['receiverTable'].getSelectedIds()
      this.$refs['receiverTable'].deleteRowData(toDeleteIds)
    },
    beforeAddReceiver() {
      this.updateObj = false
      this.resetPushObj()
    },
    beforeUpdateReceiver() {
      this.updateObj = true
      this.resetPushObj()
      this.$nextTick(() => {
        const rowData = this.$refs['receiverTable'].getSelectedDatas()[0]
        Object.assign(this.pushObj, rowData)
        if (rowData.pushType == 4 || rowData.pushType == 5) {
          this.pushObj.sysUserIds = rowData.receiver
        }
      })
    },
    formatSysObject(idList, pushType) {
      idList = idList || []
      // data的id和当前时间戳取大的值+1 作为 id
      let tempId = this.rowDatas.map(data => data.id).reduce((max, cur) => Math.max(max, cur), new Date().getTime()) + 1
      return idList.map(id => {
        const user = this.sysUserList.find(item => item.id === id) || {}
        return { id: tempId++, pushType, name: user.name, receiver: user.id }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .push-type-class /deep/ .el-input__inner {
    height: 30px !important;
  }
</style>
