<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addMsgPush') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delMsgPush') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.msgPushText2')" style="width: 200px;" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="messageTaskList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dlgVisible"
      width="750px"
    >
      <Form ref="dataForm" :model="temp" :rules="formRules" label-position="right" label-width="100px" style="width: 650px;">
        <FormItem :label="$t('pages.taskName')" prop="name">
          <el-input v-model="temp.name" :disabled="unEditable"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :disabled="unEditable"/>
        </FormItem>
        <FormItem :label="$t('table.enable')">
          <el-switch v-model="temp.isPush" :disabled="unEditable"/>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.pushContent') }}</el-divider>
        <FormItem :label="$t('pages.pushTaskMessageType') + '：'">
          <div v-for="(item, i) in msgTypes" :key="i">
            <el-radio v-model="temp.dataType" style="margin-left: 12px" :disabled="unEditable" :label="i + 1">{{ item }}</el-radio>
          </div>
        </FormItem>
        <!--报表推送的内容-->
        <FormItem :label="$t('pages.pushTaskMessageConfig') + '：'">
          <div v-if="temp.dataType == 1">
            <FormItem :label="$t('pages.reportType')" label-width="70px">
              <tree-select
                :data="reportLists"
                node-key="dataId"
                multiple
                clearable
                is-filter
                :width="436"
                :disabled="unEditable"
                :placeholder="$t('pages.selectReportType')"
                :checked-keys="temp.pushContent.reportTypes"
                @change="reportTypeChange"
              >
              </tree-select>
            </FormItem>
            <FormItem :label="$t('pages.reportScope')" label-width="70px">
              <el-row style="line-height: 3px">
                <el-col :span="8">
                  <el-select v-model="temp.pushContent.groupType" :disabled="unEditable" class="unitSelect">
                    <el-option v-for="item in reportScopeList" :key="item.id" :label="item.name" :value="item.id"/>
                  </el-select>
                </el-col>
                <el-col :span="16" style="padding-left: 6px" label-width="70px">
                  <tree-select
                    v-if="temp.pushContent.groupType == 1"
                    ref="deptTree"
                    leaf-key="dept"
                    :placeholder="$t('pages.reportScopeText1')"
                    :local-search="false"
                    :checked-keys="[temp.pushContent.deptGroupId]"
                    :disabled="unEditable"
                    node-key="dataId"
                    is-filter
                    :width="286"
                    @change="deptChange"
                  />
                  <tree-select
                    v-if="temp.pushContent.groupType == 0"
                    ref="userTree"
                    clearable
                    is-filter
                    :disabled="unEditable"
                    :local-search="false"
                    :default-expand-all="false"
                    leaf-key="user"
                    :checked-keys="[temp.pushContent.userGroupId]"
                    :width="286"
                    :placeholder="$t('pages.reportScopeText2')"
                    :rewrite-node-click-fuc="true"
                    :node-click-fuc="userTreeNodeCheckChange"
                  />
                </el-col>
              </el-row>
            </FormItem>
            <FormItem :label="$t('pages.dimBaseType')" label-width="70px">
              <el-row>
                <el-col :span="8">
                  <el-select v-model="temp.pushContent.dimBaseType" :disabled="unEditable" is-filter class="unitSelect" :placeholder="$t('text.select')" @change="formatPushTimePre">
                    <el-option v-for="(item, index) in reportTimeTypesList" :key="index" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-col>
              </el-row>
            </FormItem>
            <FormItem :label="$t('pages.countByObject')" label-width="70px">
              <el-row>
                <el-col :span="8">
                  <el-select v-model="temp.pushContent.countByObject" class="unitSelect" :disabled="unEditable" is-filter :placeholder="$t('text.select')">
                    <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-col>
              </el-row>
            </FormItem>
          </div>
        </FormItem>
        <el-divider content-position="left"> {{ $t('pages.pushTime') }} </el-divider>
        <div v-if="temp.pushContent" style="font-weight: 800;margin-left: 20px;" :style="{ marginLeft: temp.pushContent.dimBaseType != 5 ? '11px' : '20px' } ">
          <span>{{ pushTimePreText }}</span>
          <el-select v-if="temp.pushContent.dimBaseType != 5" v-model="temp.day" style="width: 60px" :disabled="unEditable">
            <el-option v-for="item in 7" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <span v-if="temp.pushContent.dimBaseType != 5">{{ $t('pages.dimFormatText6') }}</span>
          <el-time-picker
            v-model="temp.pushTimeRange"
            style="width: 160px"
            is-range
            :clearable="false"
            format="HH:mm"
            value-format="HH:mm"
            :range-separator="$t('pages.till')"
            :disabled="unEditable"
          >
          </el-time-picker>
          <span>{{ $t('pages.pushReportData') }}</span>
        </div>
        <!--推送方式-->
        <el-divider content-position="left"> {{ $t('pages.pushWay') }} </el-divider>
        <push-object ref="pushObjectRef" :row-datas="temp.receiveObject" :editable="!unEditable" :push-type-dict="pushTypeDict"/>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button v-show="!unEditable" btn-type="primary" btn-style="float: left" :menu-code="'A74'" :link-url="'/system/messageNotify/customizeReport'" :btn-text="$t('button.customizeReportType')"/>
        <!-- 先前预览消息为与云平台进行交互的功能，所以不需要预览推送消息,若需要查看先前代码，需利用svn查看历史记录 -->
        <!--        <el-button :disabled="unCloudConnect || !(temp.pushContent.reportTypes.length > 0)" type="primary" style="float: left" @click="browserPushMsg">-->
        <!--          {{ $t('button.browserPushMsg') }}-->
        <!--        </el-button>-->
        <el-button v-show="!unEditable" :loading="submitting" type="primary" @click="handleSure">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dlgVisible = false; submitting = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <preview-push-msg
      ref="previewPushMsg"
      :preview-data="reportTypeNodeList"
      :report-scope="browserReportScope"
      :date-value="previewDateValue"
      :dim-base-name="dimBaseName"
      :push-types="temp.pushTypes"
    />
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :append-to-body="true"
      :title="$t('text.prompt')"
      :visible.sync="tipVisible"
      width="900px"
      @close="closeTipDlg"
    >
      <p v-t="{path: 'pages.crossPromptText', args: {sum: tipDataArray.length}}"></p>
      <grid-table
        ref="checkTaskList"
        :show-pager="false"
        :multi-select="false"
        :row-data-api="checkTaskListRequest"
        :col-model="crossColModel"
        :height="400"
      ></grid-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="delCrossPushObject">
          {{ $t('pages.deleteCrossText1') }}
        </el-button>
        <el-button type="primary" @click="delCrossReportType">
          {{ $t('pages.deleteCrossText2') }}
        </el-button>
        <el-button @click="closeTipDlg">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18n from '@/lang';
import {
  addMsgPushTask, delMsgPushTask, getMsgPushTaskPage, getTaskByName, updateMsgPushTask
} from '@/api/system/messageNotification/messagePush';
import {
  dataContentFormatter, getTypeOptions, getReportScopeList, getReportTimeTypesList, getPushTypeDict, formatterReportType
} from '@/utils/messagePush';
import PreviewPushMsg from '@/views/system/messageNotification/messageConfig/previewPushMsg';
import { getReportType } from '@/api/system/messageNotification/customizeReportTemplate';
import PushObject from '@/views/system/messageNotification/messageConfig/pushObject'

export default {
  name: 'MessageConfig',
  components: { PushObject, PreviewPushMsg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'msgPushTaskName', width: '130', iconFormatter: this.activeFormatter },
        { prop: 'dataContent', label: 'msgPushContent', width: '150', type: 'button',
          buttons: [{ formatter: this.pushContentFormatter, click: this.handleCheck }] },
        { prop: 'receiveObject', label: 'msgPushObject', width: '150', formatter: this.receiveObjectFormatter },
        { prop: 'executionRules', label: 'pushTime', width: '150', formatter: this.pushTimeFormatter },
        { prop: 'remark', label: 'remark', width: '120' },
        {
          label: 'operate', type: 'button', width: '120', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      crossColModel: [
        { prop: 'crossInfo', label: 'crossProperty', width: '160', formatter: this.crossPropertyFormatter },
        { prop: 'name', label: 'msgPushTaskName', width: '130' },
        { prop: 'dataContent', label: 'msgPushContent', width: '150', formatter: this.pushContentFormatter },
        { prop: 'receiveObject', label: 'msgPushObject', width: '150', formatter: this.receiveObjectFormatter },
        { prop: 'executionRules', label: 'pushTime', width: '150', formatter: this.pushTimeFormatter },
        { prop: 'remark', label: 'remark', width: '120' }
      ],
      query: {
        name: ''
      },
      formRules: {
        name: [{ required: true, message: this.$t('pages.MsgPushValidatorText1'), trigger: 'blur' }, { validator: this.nameValidate, trigger: 'blur' }]
      },
      textMap: {
        'create': i18n.t('pages.addMsgPush'),
        'update': i18n.t('pages.updateMsgPush'),
        'check': i18n.t('pages.checkMsgPushTask')
      },
      temp: {},
      defaultForm: {
        isPush: true,
        name: '',
        remark: '',
        pushObj: {},
        receiveObject: [],
        pushTypes: [],
        dataType: 1,
        dataConfigId: undefined,
        day: 1,
        pushTimeRange: ['08:00', '18:00'],
        pushContent: {
          reportTypes: [],
          groupType: 2,
          dimBaseType: 5,
          userGroupId: '',
          countByObject: 2
        },
        reportConfig: undefined
      },
      pushUserIds: [],
      pushObj: {
        pushType: '3',
        receiver: undefined,
        name: undefined
      },
      previewDateValue: undefined,
      dimBaseName: undefined,
      browserReportScope: undefined,
      reportTypeNodeList: undefined,
      msgTypes: [i18n.t('route.report')],
      receiveObjectTreeData: [],
      submitting: false,
      reportLists: [],
      typeOptions: undefined,
      dialogStatus: 'create',
      pushTimePreText: '',
      tipDataArray: [],
      unEditable: false,
      dlgVisible: false,
      tipVisible: false,
      deleteable: false,
      reportScopeList: undefined,
      pushTypeDict: undefined,
      reportTimeTypesList: undefined
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    messageTaskList() {
      return this.$refs.messageTaskList
    }
  },
  watch: {
    '$store.state.commonData.reportTypeChange'() {
      this.getReportList()
    }
  },
  created() {
    this.reportTimeTypesList = getReportTimeTypesList()
    this.pushTypeDict = Object.fromEntries(Object.entries(getPushTypeDict()).filter(([key, value]) => key >= 3))
    this.typeOptions = getTypeOptions()
    this.reportScopeList = getReportScopeList()
    this.temp = JSON.parse(JSON.stringify(this.defaultForm))
    this.getReportList()
  },
  methods: {
    activeFormatter(row, data) {
      const activeValue = { true: true, false: false, 1: true, 0: false }[row.isPush]
      const activeOptions = { true: i18n.t('text.enable'), false: i18n.t('text.disable2') }
      const label = activeOptions[activeValue]
      return activeValue ? [{ class: 'active', title: label }] : [{ class: 'offline', title: label, style: 'color: #888;' }]
    },
    crossPropertyFormatter(row, data) {
      if (this.tipVisible) {
        const { baseReport, customizeReport, pushObjs } = data
        const reportTypeStr = formatterReportType(baseReport, customizeReport, this.reportLists)
        return reportTypeStr + '，' + this.$t('pages.pushWay') + ': ' + this.receiveObjectFormatter(null, pushObjs)
      }
      return ''
    },
    pushContentFormatter(row, data) {
      return dataContentFormatter(row, row.reportConfig, this.reportLists)
    },
    receiveObjectFormatter(row, data) {
      const pushObjMap = {}
      data.forEach(t => {
        !pushObjMap[t.pushType] && (pushObjMap[t.pushType] = [])
        pushObjMap[t.pushType].push(t.receiver)
      })
      const result = []
      Object.entries(pushObjMap).forEach(([pushType, receivers]) => {
        if (pushType === '4') {
          const receiversSet = new Set(receivers)
          receivers = this.sysUserList.filter(user => receiversSet.has(user.id)).map(user => user.name)
        }
        result.push(`${this.pushTypeDict[pushType]}: ${receivers.join(', ')}`);
      })
      return result.join('; ')
    },
    timeRuleFormatter(row, data) {
      const dimBaseType = row.reportConfig.dimBaseType
      data = JSON.parse(data)
      const day = data.day
      let str = this.changeDimBaseType(dimBaseType)
      if (dimBaseType < 5) {
        str += day + this.$t('pages.day1')
      }
      str += data.range + this.$t('pages.pushReportData')
      return str
    },
    formatPushTimePre(dimBaseType) {
      this.pushTimePreText = this.changeDimBaseType(dimBaseType)
    },
    changeDimBaseType(dimBaseType) {
      let str = ''
      switch (dimBaseType) {
        case 1: str = this.$t('pages.dimFormatText1'); break
        case 2: str = this.$t('pages.dimFormatText2'); break
        case 3: str = this.$t('pages.dimFormatText3'); break
        case 4: str = this.$t('pages.dimFormatText4'); break
        case 5: str = this.$t('pages.dimFormatText5'); break
      }
      return str
    },
    resetForm() {
      this.dlgVisible = true
      this.temp = JSON.parse(JSON.stringify(this.defaultForm))
    },
    handleCreate() {
      this.$nextTick(() => {
        this.resetForm()
        this.formatPushTimePre(this.temp.pushContent.dimBaseType)
        this.unEditable = false
        this.dialogStatus = 'create'
      })
    },
    handleUpdate(row) {
      this.resetForm()
      this.formatUpdateData(row)
      this.unEditable = false
      this.dialogStatus = 'update'
    },
    handleCheck(row, data) {
      this.resetForm()
      this.formatUpdateData(row)
      this.unEditable = true
      this.dialogStatus = 'check'
    },
    pushTimeFormatter(row, data) {
      const timeRules = JSON.parse(data)
      let str = this.changeDimBaseType(timeRules.timeCycle)
      if (timeRules.timeCycle != 5) {
        str += timeRules.day + this.$t('pages.dimFormatText6')
      }
      return str + timeRules.range + this.$t('pages.pushReportData')
    },
    formatUpdateData(row) {
      this.$nextTick(() => {
        const { id, name, isPush, remark, dataType, dataConfigId } = row
        this.temp = Object.assign(JSON.parse(JSON.stringify(this.defaultForm)), { id, name, isPush, remark, dataType, dataConfigId })
        // 初始化时间规则
        const timeRules = JSON.parse(row.executionRules)
        const range = timeRules.range.split('-')
        this.temp.day = timeRules.day
        this.temp.pushTimeRange = [range[0], range[1]]
        // 初始化推送方式
        this.temp.pushTypes = this.numToList(row.pushType, 2).map(item => String(item))
        // 初始化推送对象
        this.temp.receiveObject = [...row.receiveObject]
        if (dataType == 1) {
          const reportJson = row.reportConfig
          const pushContent = this.temp.pushContent
          // 报表类型
          const typeList = []
          const reportType = reportJson.reportType
          const baseReportList = this.reportLists[0].children
          for (let i = 0; i < baseReportList.length; i++) {
            const val = 1 << baseReportList[i].id - 1;
            if ((reportType & val) == val) {
              typeList.push(baseReportList[i].dataId)
            }
          }
          if (reportJson.customizeTypeIds) {
            const tempList = reportJson.customizeTypeIds.split(',')
            tempList.forEach(item => typeList.push('C' + item))
          }
          pushContent.reportTypes = typeList
          // this.reportTypeChange(typeList)
          // 报表范围
          pushContent.groupType = reportJson.groupType

          if (pushContent.groupType == 1) {
            // 部门
            pushContent.deptGroupId = reportJson.objectId
          } else if (pushContent.groupType == 0) {
            // 个人
            pushContent.userGroupId = 'U' + reportJson.objectId
          }
          // 报表日期
          pushContent.dimBaseType = reportJson.dimBaseType
          // 统计类型
          pushContent.countByObject = reportJson.countByObject
          this.formatPushTimePre(reportJson.dimBaseType)
          this.$refs.dataForm.clearValidate()
        }
      })
    },
    handleRefresh() {
      this.messageTaskList.execRowDataApi()
    },
    deptChange(key, data) {
      this.temp.pushContent.deptGroupId = key
    },
    userTreeNodeCheckChange: function(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.temp.pushContent.userGroupId = 'U' + data.dataId
      } else {
        return false
      }
    },
    checkTaskListRequest(option) {
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: this.tipDataArray })
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt'))
        .then(() => {
          const taskIds = this.messageTaskList.getSelectedKeys().join(',')
          delMsgPushTask({ ids: taskIds }).then(res => {
            this.messageTaskList.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {})
    },
    handleFilter() {
      this.query.page = 1
      this.messageTaskList.execRowDataApi(this.query)
    },
    rowDataApi(option) {
      const searchData = Object.assign({}, this.query, option)
      return getMsgPushTaskPage(searchData)
    },
    selectionChangeEnd(rows) {
      this.deleteable = rows && rows.length > 0
    },
    getReportList() {
      getReportType().then(res => {
        this.reportLists = res.data
      })
    },
    handleSure() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (!this.validateForm()) { return }
          this.formatReportType()
          this.submitting = true
          if (this.temp.dataType == 1) {
            const groupType = this.temp.pushContent.groupType
            switch (groupType) {
              case 1:
                this.temp.pushContent.objectId = this.temp.pushContent.deptGroupId;
                break;
              case 0: {
                const indexOf = this.temp.pushContent.userGroupId.indexOf('U');
                let temp = this.temp.pushContent.userGroupId
                if (indexOf >= 0) {
                  temp = temp.substring(indexOf + 1)
                }
                this.temp.pushContent.objectId = temp
              }
                break;
            }
            this.temp.reportConfig = this.temp.pushContent
          }
          const formData = JSON.parse(JSON.stringify(this.temp))
          delete formData['pushContent']
          delete formData['reportConfig']['reportTypes']
          if (this.dialogStatus == 'create') {
            addMsgPushTask(formData).then(res => {
              this.successSubmit(res)
            })
          } else if (this.dialogStatus == 'update') {
            updateMsgPushTask(formData).then(res => {
              this.successSubmit(res)
            })
          }
        }
      })
    },
    successSubmit(res) {
      const data = res.data
      if (Array.isArray(data)) {
        if (data.length > 0) {
          this.tipDataArray = res.data
          this.tipVisible = true;
          this.$nextTick(() => {
            this.$refs.checkTaskList.clearRowData()
            this.$refs.checkTaskList.execRowDataApi()
          })
        }
      } else {
        this.dlgVisible = false
        this.submitting = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.dialogStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.$nextTick(() => {
          this.messageTaskList.execRowDataApi()
        })
      }
    },
    reportTypeChange(keys, nodes) {
      if (nodes) {
        for (let i = nodes.length - 1; i >= 0; i--) {
          if (nodes[i].id == undefined) {
            nodes.splice(i, 1)
            keys.splice(i, 1)
          }
        }
        this.reportTypeNodeList = nodes
        this.temp.pushContent.reportTypes = keys
      }
    },
    closeTipDlg() {
      this.submitting = false
      this.tipVisible = false
    },
    formatReportType() {
      let reportType = 0;
      const reportTypes = this.temp.pushContent.reportTypes
      const customizeType = []
      for (const typeKey of reportTypes) {
        if (typeKey.startsWith('R')) {
          reportType += (1 << (Number(typeKey.substring(1))) - 1)
        } else if (typeKey.startsWith('C')) {
          customizeType.push(Number(typeKey.substring(1)))
        }
      }
      this.temp.pushContent.reportType = reportType
      this.temp.pushContent.customizeTypeIds = customizeType.join(',')
    },
    delCrossPushObject() {
      const tipArray = this.tipDataArray
      tipArray.forEach(tip => {
        tip.receiveObject.forEach(id => {
          const findIndex = this.pushUserIds.indexOf(String(id))
          if (findIndex >= 0) {
            this.pushUserIds.splice(findIndex, 1)
          }
        })
      })
      this.closeTipDlg()
    },
    delCrossReportType() {
      const tipArray = this.tipDataArray
      const pushContent = this.temp.pushContent
      let reportType = pushContent.reportType
      const tempCustomizeReports = new Set(pushContent.reportTypes.filter(item => item.startsWith('C')))
      tipArray.forEach(tip => {
        const { baseReport, customizeReport } = tip.crossInfo
        reportType &= ~baseReport
        if (tempCustomizeReports && customizeReport) {
          customizeReport.forEach(e => tempCustomizeReports.delete('C' + e))
        }
      })
      const typeList = [...tempCustomizeReports]
      const baseReport = this.reportLists[0].children
      for (let i = 0; i < baseReport.length; i++) {
        if (reportType <= 0) break;
        const reportVal = (1 << (baseReport[i].id - 1))
        if ((reportVal & reportType) == reportVal) {
          typeList.push(baseReport[i].dataId)
        }
      }
      pushContent.reportTypes = typeList
      // this.reportTypeChange(typeList)
      this.closeTipDlg()
    },
    nameValidate(rule, value, callback) {
      getTaskByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    validateForm() {
      const data = this.temp
      if (data.receiveObject == undefined || data.receiveObject.length == 0) {
        this.validateErrorMsg(this.$t('pages.MsgPushValidatorText2'))
        return false
      }
      const pushContent = data.pushContent
      // 报表
      if (pushContent.reportTypes == null || pushContent.reportTypes.length == 0) {
        this.validateErrorMsg(this.$t('pages.MsgPushValidatorText5'))
        return false
      }
      if (pushContent.groupType == 1 && !pushContent.deptGroupId) {
        this.validateErrorMsg(this.$t('pages.MsgPushValidatorText7'))
        return false
      } else if (pushContent.groupType == 0 && !pushContent.userGroupId) {
        this.validateErrorMsg(this.$t('pages.MsgPushValidatorText8'))
        return false
      }
      return true
    },
    validateErrorMsg(content) {
      this.$message({
        message: content,
        type: 'error',
        duration: 2000
      })
    }
  }
}
</script>
