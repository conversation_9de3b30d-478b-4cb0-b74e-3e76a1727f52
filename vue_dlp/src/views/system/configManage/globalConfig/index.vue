<template>
  <div class="app-container global-config-container" style="overflow: auto;">
    <div class="config-type">
      <div class="config-all" @click="handleUpdate('basic', 'all')">
        <div class="title">{{ $t('pages.basicConfig') }}</div>
        <div class="description">支持自定义配置，提供个性化服务</div>
      </div>
      <div class="config-items">
        <div class="config-item" @click="handleUpdate('basic', 'nameMode')">
          <svg-icon icon-class="text1"></svg-icon>
          <div class="config-item-text">
            名称显示方式
          </div>
        </div>
        <div class="config-item" @click="handleUpdate('basic', 'log')">
          <svg-icon icon-class="file"></svg-icon>
          <div class="config-item-text">
            日志审计
          </div>
        </div>
        <div class="config-item" @click="handleUpdate('basic', 'time')">
          <svg-icon icon-class="time"></svg-icon>
          <div class="config-item-text">
            时间设置
          </div>
        </div>
      </div>
    </div>
    <div class="config-type">
      <div class="config-all" @click="handleUpdate('consoleAddress', 'all')">
        <div class="title">服务器配置</div>
        <div class="description">支持修改相关服务器的配置</div>
      </div>
      <div class="config-items">
        <div class="config-item">
          <svg-icon icon-class="server" @click="handleUpdate('consoleAddress', 'webServer')"></svg-icon>
          <div class="config-item-text">
            web服务器
            <el-tooltip effect="dark" placement="top-start">
              <div slot="content">
                {{ $t('pages.globalConfig_configInstruction') }}
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局配置弹窗 -->
    <tr-dialog
      type="drawer"
      :title="$t('pages.basicConfig')"
      :visible.sync="basicVisible"
      status="update"
      wrapper-closable
      append-to-body
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
    >
      <!-- 基础配置 -->
      <Form
        ref="globalForm"
        class="global-config"
        :model="dataList"
        label-width="60px"
        label-position="right"
      >
        <div v-show="configShow('nameMode')" class="config-form-item">
          <label>{{ $t('pages.operatorDisplayMode') }}：</label>
          <FormItem >
            <el-radio-group v-model="dataList.operatorDisplayMode.value">
              <el-radio :label="1">{{ $t('pages.accountDisplay') }}</el-radio>
              <el-radio :label="2">{{ $t('pages.nickNameDisplay') }}</el-radio>
              <el-radio :label="3">{{ $t('pages.accountNickNameDisplay') }}</el-radio>
              <el-radio :label="4">{{ $t('pages.nickNameAccountDisplay') }}</el-radio>
            </el-radio-group>
          </FormItem>
        </div>

        <div v-show="configShow('nameMode')" class="config-form-item">
          <label>{{ $t('pages.terminalDisplayMode') }}：</label>
          <FormItem >
            <el-radio-group v-model="dataList.termDisplayMode.value">
              <el-radio :label="1">{{ $t('pages.terminalName') }}（{{ $t('pages.terminalCode') }}）</el-radio>
              <el-radio :label="2">{{ $t('pages.terminalName') }}</el-radio>
            </el-radio-group>
          </FormItem>
        </div>

        <div v-show="configShow('nameMode')" class="config-form-item">
          <label>{{ $t('pages.termTreeNodeDisplayMode') }}：</label>
          <FormItem >
            <el-radio-group v-model="dataList.termTreeNodeDisplayMode.value">
              <el-row>
                <el-radio :label="1">{{ $t('pages.terminalName') }}（{{ $t('pages.terminalCode') }}）</el-radio>
                <el-radio :label="2">{{ $t('pages.terminalName') }}</el-radio>
              </el-row>
              <el-row>
                <el-radio :label="3">{{ $t('pages.terminalName') }}（{{ $t('pages.curOperatorAccount') }}）</el-radio>
                <el-radio :label="4">{{ $t('pages.terminalName') }}（{{ $t('pages.curOperatorName') }}）</el-radio>
              </el-row>
            </el-radio-group>
          </FormItem>
        </div>

        <div v-show="configShow('nameMode')" class="config-form-item">
          <label>{{ $t('pages.termNameSyncMode') }}：
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                <i18n path="pages.termNameSyncTips">
                  <br slot="br"/>
                </i18n>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </label>
          <FormItem>
            <el-checkbox-group v-model="dataList.termNameSyncMode.checkedValue" @change="termNameSyncModeChange">
              <el-checkbox :label="1">{{ $t('pages.computerNameMode') }}</el-checkbox>
              <el-checkbox :label="2" :disabled="dataList.termNameSyncMode.checkedValue.indexOf(1)==-1">{{ $t('pages.termNameMode') }}</el-checkbox>
            </el-checkbox-group>
          </FormItem>
        </div>

        <div v-show="configShow('time')" class="config-form-item">
          <label>{{ $t('pages.recordIntervalConfig') }}：
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                {{ $t('pages.recordIntervalConfigTip') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </label>
          <FormItem :label="dataList.recordInterval.label" label-width="145px" :extra-width="{en: 95}">
            <el-input-number v-model="dataList.recordInterval.value" :min="dataList.recordInterval.min" :max="60" :controls="false" :precision="0" style="width:105px"/> <span>{{ $t('text.secondLower') }}</span>
            <div v-if="!dataList.recordInterval.value" class="error_style">{{ $t('pages.screenSpacingError') }}</div>
          </FormItem>
        </div>

        <div v-show="configShow('log')" class="config-form-item">
          <label v-if="isSuperRole" style="margin-bottom: 10px;">{{ $t('pages.auditLogMgr') }}：
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">{{ $t('pages.auditLogMgr_Msg') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </label>
          <FormItem v-if="isSuperRole" label-width="60px">
            <el-checkbox v-model="dataList.opt2001.switch" :true-label="1" :false-label="0" class="delete-log">
              <i18n path="pages.periodDeleteLog">
                <el-input
                  slot="period"
                  v-model.number="dataList.opt2001.saveTimeNumber"
                  :disabled="!dataList.opt2001.switch"
                  class="input-file-size"
                  maxlength="3"
                  aria-required="true"
                  @blur="opt2001SaveTimeNumberChange"
                >
                  <el-select
                    slot="append"
                    v-model="dataList.opt2001.saveTimeUnit"
                    :disabled="!dataList.opt2001.switch"
                    :placeholder="$t('text.select')"
                    :style="$store.getters.language === 'en'? 'width: 101px':'width: 70px'"
                    @click.native.prevent="() => {}"
                    @change="opt2001SaveTimeNumberChange"
                  >
                    <el-option :label="$t('text.year')" :value="3"/>
                    <el-option :label="$t('text.month')" :value="2"/>
                    <el-option :label="$t('text.day')" :value="1"/>
                  </el-select>
                </el-input>
              </i18n>
            </el-checkbox>
            <br>
            <label v-if="dataList.opt2001.switch && dataList.opt2001.showMsg" style="font-size: small;color: red;">{{ $t('pages.cleanLogMgrWarnMsg') }}</label>
          </FormItem>
        </div>
        <div v-show="configShow('log')" v-permission="'137'" class="config-form-item">
          <label>
            {{ $t('pages.logViewVideoSet') }}：
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">{{ $t('pages.logViewVideoSetTip') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </label>
          <FormItem :style="{ height: !dataList.logVideoTimeRange.beforeTime ? '50px': '' }">
            <i18n path="pages.getLogBefore">
              <template slot="time">
                <el-input-number
                  v-model="dataList.logVideoTimeRange.beforeTime"
                  :min="dataList.logVideoTimeRange.min"
                  :max="dataList.logVideoTimeRange.max"
                  :controls="false"
                  :precision="0"
                  style="width: 60px; margin: 0 4px;"
                />
              </template>
              <template slot="radio">
              </template>
            </i18n>
            <div v-if="!dataList.logVideoTimeRange.beforeTime" style="position: absolute; left: 100px" class="error_style">{{ $t('pages.timeNotNull') }}</div>
            <el-radio-group v-model="dataList.logVideoTimeRange.afterRadio" style="display: inline-block; margin-left: 30px; margin-bottom: 6px">
              <el-radio :label="1">{{ $t('pages.curVideoDeadline') }}</el-radio>
              <el-radio :label="2">
                <i18n path="pages.getLogAfter">
                  <span slot="afterTime">
                    <el-input-number
                      v-model="dataList.logVideoTimeRange.afterTime"
                      :min="dataList.logVideoTimeRange.min"
                      :max="dataList.logVideoTimeRange.max"
                      :controls="false"
                      :precision="0"
                      :disabled="dataList.logVideoTimeRange.afterRadio === 1"
                      style="width: 60px; margin: 0 4px"
                    />
                  </span>
                </i18n>
                <div v-if="dataList.logVideoTimeRange.afterRadio === 2 && !dataList.logVideoTimeRange.afterTime" style="position: absolute; left: 100px" class="error_style">{{ $t('pages.timeNotNull') }}</div>
              </el-radio>
            </el-radio-group>
          </FormItem>
        </div>

        <div v-show="configShow('time')" class="config-form-item">
          <label>{{ $t('pages.overTimeConfig') }}
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">{{ $t('pages.overTimeConfigTips') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </label>
          <FormItem :label="dataList.alarmEndFlagTimedOut.label" label-width="145px">
            <el-input-number
              v-model="dataList.alarmEndFlagTimedOut.value"
              :min="dataList.alarmEndFlagTimedOut.min"
              :max="dataList.alarmEndFlagTimedOut.max"
              :controls="false"
              :precision="0"
              style="width:105px"
            /> <span>{{ $t('pages.minuteLower') }}</span>
            <div v-if="!dataList.alarmEndFlagTimedOut.value" class="error_style">{{ $t('pages.overTimeError') }}</div>
          </FormItem>
        </div>
        <div v-if="isModule" v-show="configShow('time')" class="config-form-item">
          <label>{{ $t('pages.taskStatusReportConfig') }}</label>
          <FormItem :label="dataList.taskStatusReport.label" label-width="145px">
            <el-input-number
              v-model="dataList.taskStatusReport.value"
              :min="dataList.taskStatusReport.min"
              :max="dataList.taskStatusReport.max"
              :controls="false"
              :precision="0"
              style="width:105px"
            /> <span>{{ $t('pages.minuteLower') }}</span>
            <div v-if="!dataList.taskStatusReport.value" class="error_style">{{ $t('pages.intervalTimeError') }}</div>
          </FormItem>
        </div>
      </Form>
    </tr-dialog>

    <tr-dialog
      type="drawer"
      :title="$t('pages.ConsoleWebServerConfig')"
      :visible.sync="consoleAddressVisible"
      status="update"
      wrapper-closable
      :submitting.sync="submitting"
      :update-data="handleSaveConfig"
      :before-close="beforeClose"
      destroy-on-close
    >
      <console-address v-show="configShow('webServer')" ref="consoleAddress" :get-config-func="getConsoleAddressConfig" hide-save-button/>
    </tr-dialog>

    <differ-interval-time-dlg ref="differIntervalTimeDlg" @submit="updateTimeSubmit"/>
  </div>
</template>

<script>
import ConsoleAddress from '@/components/DownloadManager/setting/consoleAddress'
import { getConfig, updateConfig, validateGlobalConfigTime } from '@/api/system/configManage/globalConfig'
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig'
import DifferIntervalTimeDlg from './differIntervalTimeDlg';

export default {
  name: 'GlobalConfig',
  components: { DifferIntervalTimeDlg, ConsoleAddress },
  data() {
    return {
      basicVisible: false,               // 基础配置弹窗是否显示
      consoleAddressVisible: false,      // 服务器配置弹窗是否显示
      configType: '',                    // 当前修改的配置类型
      showType: '',
      submitting: false,                 // 提交数据中...
      dataList: {},                      //
      defaultDataList: {
        operatorDisplayMode: { label: this.$t('pages.operatorDisplayMode'), value: 1 },
        recordInterval: { label: this.$t('pages.screenRecordingInterval'), value: '', min: 1, max: 60 },
        termDisplayMode: { label: this.$t('pages.terminalDisplayMode'), value: 1 },
        termTreeNodeDisplayMode: { label: this.$t('pages.termTreeNodeDisplayMode'), value: 1 },
        // userMultiLogin: { isProp: true, label: this.$t('pages.multipointLogin'), value: '0' },
        // singleFileSize: { label: this.$t('pages.singleFileSize'), value: 1024 },
        // fileCount: { label: this.$t('pages.maximumSingleDownload_1'), value: 1, min: 1, max: 100 },
        // fileSumSize: { label: this.$t('pages.fileSumSize'), value: 10 * 1024 * 1024 },
        // downloadConcurrency: { label: this.$t('pages.concurrentNumber'), value: 1, min: 1, max: 10 },
        consoleIntranetIP: { label: this.$t('pages.consoleIntranetIP'), value: '', isAddr: true, isProp: true },
        consoleIntranetIpv6: { label: this.$t('pages.consoleIntranetIPv6'), value: '', isAddr: true, isProp: true },
        consoleIntranetPort: { label: this.$t('pages.consoleIntranetPort'), value: '', isAddr: true, isProp: true },
        consoleExtranetEnable: { label: this.$t('pages.enableExtranetMapping'), value: '0', isAddr: true, isProp: true },
        consoleExtranetIP: { label: this.$t('pages.consoleExtranetIPv4'), value: '', isAddr: true, isProp: true },
        consoleExtranetIpv6: { label: this.$t('pages.consoleExtranetIPv6'), value: '', isAddr: true, isProp: true },
        consoleExtranetPort: { label: this.$t('pages.consoleExtranetPort'), value: '', isAddr: true, isProp: true },
        // 定期删除审计日志
        opt2001: { isOpt: true, label: this.$t('pages.regularDeleteAuditLog'), saveTimeNumber: 1, saveTimeUnit: 3, switch: 0, showMsg: false },
        alarmEndFlagTimedOut: { label: this.$t('pages.overTimeTips'), value: 35, min: 10, max: 180 },
        // 日志审计查看录屏时间设置
        logVideoTimeRange: { label: this.$t('pages.viewLogTime'), value: '', min: 1, max: 60, beforeTime: 2, afterTime: 2, afterRadio: 2 },
        // 终端名称同步方式
        termNameSyncMode: { label: this.$t('pages.termNameSyncMode'), value: 0, checkedValue: [] },
        taskStatusReport: { label: this.$t('pages.taskStatusReport'), value: 5, min: 1, max: 60 }
      },
      oldDataString: null,     // 修改前的数据，用于校验配置是否有修改
      isModule: false          // 是否有 任务分发状态上报配置 的销售模块
    }
  },
  computed: {
    // 超管或根三员
    isSuperRole() {
      return this.$store.getters.isSuperRole || this.$store.getters.isSuperThreeUser
    }
  },
  watch: {
  },
  activated() {
  },
  created() {
    this.listModule()
    this.getConfig()
  },
  methods: {
    // 根据 type 显示对应的配置项
    configShow(type) {
      return this.showType === 'all' || type === this.showType
    },
    // 重置 dataList
    resetTemp() {
      this.dataList = JSON.parse(JSON.stringify(this.defaultDataList))
    },
    listModule() {
      // 获取开通的销售模块
      listSaleModuleId().then(resp => {
        const saleModule = resp.data
        // 任务分发状态上报配置
        if (saleModule && saleModule.indexOf(13) > -1) {
          this.isModule = true
        }
      })
    },
    // 获取配置，并更新 dataList 的值
    getConfig() {
      this.resetTemp()
      getConfig().then(res => {
        const data = res.data
        data.forEach(item => {
          const { key, type } = item
          let { value } = item
          if (this.dataList.hasOwnProperty(key)) {
            if (key === 'logVideoTimeRange' && value) {
              // 日志审计查看录屏时间设置
              Object.assign(this.dataList[key], JSON.parse(value))
            } else if (key === 'termNameSyncMode') {
              // 终端名称同步方式
              this.dataList[key].checkedValue = this.numToList(value, 2)
            } else if (key === 'opt2001' && value) {
              // 定期删除审计日志
              Object.assign(this.dataList[key], JSON.parse(value))
            } else {
              // 其余配置
              value = type === 'Integer' ? parseInt(value) : value
              Object.assign(this.dataList[key], { type, value })
            }
          }
        })
      })
    },
    // 获取控制台web服务器的配置
    getConsoleAddressConfig() {
      return new Promise((resolve, reject) => {
        const config = {}
        Object.entries(this.dataList).forEach(([key, data]) => {
          if (data.isAddr) {
            config[key] = data.value
          }
        })
        resolve(config)
      })
    },
    // 获取修改后的服务器数据，并更新到 dataList
    updateConsoleAddressData() {
      const addressData = this.$refs['consoleAddress'].getFormData()
      addressData.forEach(data => {
        const { key, value } = data
        Object.assign(this.dataList[key], { value })
      })
    },
    // 打开配置弹窗
    handleUpdate(type, show) {
      this.configType = type
      this.showType = show
      this.showDrawer(true)
      this.oldDataString = JSON.stringify(this.dataList)
    },
    // 打开/关闭弹窗
    showDrawer(open) {
      if (this.configType === 'basic') {
        this.basicVisible = open
      } else if (this.configType === 'consoleAddress') {
        this.consoleAddressVisible = open
      }
    },
    // 关闭弹窗前，校验数据是否发生变化，并提示
    beforeClose(done) {
      // 将服务器配置更新到 dataList
      if (this.configType === 'consoleAddress') {
        this.updateConsoleAddressData()
      }

      // 校验数据是否修改
      if (JSON.stringify(this.dataList) !== this.oldDataString) {
        this.$confirmBox('配置未保存，是否确定离开？', this.$t('text.prompt')).then(() => {
          this.dataList = JSON.parse(this.oldDataString)
          done()
        }).catch(() => {})
        return
      }
      done()
    },
    // 点击确认提交数据
    async handleSaveConfig() {
      if (this.configType === 'basic') {
        if (!await this.validateRecordInterval()) return
        this.validateConfigAndUpdate();
      } else if (this.configType === 'consoleAddress') {
        // 将服务器配置更新到 dataList
        this.updateConsoleAddressData()
        this.updateConfig()
      }
    },
    // 校验录屏时间间隔设置是否正常, 返回 true 验证通过
    async validateRecordInterval() {
      const recordInterval = this.dataList.recordInterval.value
      if (!recordInterval) {
        return false
      }
      const res = await validateGlobalConfigTime(recordInterval);
      const { responseTimeTotal, responseTime, stgIds } = res.data
      //  违规响应规则库有配置违规响应规则，且录屏时长小于录屏间隙时
      if ((responseTimeTotal > 0 && responseTime < recordInterval) || stgIds.length) {
        // 弹窗进行重新配置
        this.$refs.differIntervalTimeDlg.show(Object.assign({ intervalTime: recordInterval }, res.data));
        return false;
      }
      return true
    },
    // 校验基础配置的数据并提交
    validateConfigAndUpdate() {
      this.opt2001SaveTimeNumberChange()
      const { switch: optSwitch, showMsg, saveTimeNumber, saveTimeUnit } = this.dataList.opt2001
      if (optSwitch && showMsg && this.isSuperRole) {
        // 只有超管和根三员才能配置修改此值
        const saveTime = Number.parseInt(saveTimeNumber)
        const timeStr = saveTime + (saveTimeUnit === 1 ? ' 天' : ' 月')
        this.$confirmBox('', {
          title: this.$t('text.prompt'),
          message: '国家法律法规要求日志留存时间不少于六个月，您确定要定期删除 ' + timeStr + '前的审计日志吗？',
          type: 'warning'
        }).then(() => {
          this.updateConfig()
        })
      } else {
        this.updateConfig()
      }
    },
    // 校验表单数据并提交
    updateConfig() {
      this.formValidate().then((valid) => {
        if (!valid) return
        // 获取变化的数据
        const formData = this.formatFormData(this.dataList)
        if (formData.length == 0) {
          this.showDrawer(false)
          return
        }
        this.submitting = true
        updateConfig(formData).then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
          this.showDrawer(false)
          this.getConfig()
          this.submitting = false
        }).catch(err => {
          console.error(err)
          this.submitting = false
        })
      }).catch(e => {
        console.error(e);
      })
    },
    // 校验数据，返回表单的验证结果，值为 false 则校验不通过
    formValidate() {
      if (this.configType === 'basic') {
        return this.$refs['globalForm'].validate().then(valid => {
          const { alarmEndFlagTimedOut, taskStatusReport, recordInterval, logVideoTimeRange } = this.dataList
          const { beforeTime, afterRadio, afterTime } = logVideoTimeRange
          // 非空校验
          const emptyValide = !alarmEndFlagTimedOut.value || !taskStatusReport.value || !recordInterval.value
          const logVideoTimeValide = !beforeTime || (afterRadio === 2 && !afterTime)
          const validResult = !valid || emptyValide || logVideoTimeValide

          return !validResult
        }).catch(e => {
          console.error(e);
        })
      } else if (this.configType === 'consoleAddress') {
        return this.$refs['consoleAddress'].validate()
      }
    },
    // 格式化表单数据，并过滤未修改的数据
    formatFormData(data) {
      const oldData = JSON.parse(this.oldDataString)
      const formData = []
      // 添加基础配置
      Object.entries(data).forEach(([key, data]) => {
        // 未修改的配置不添加到 formData
        if (JSON.stringify(oldData[key]) == JSON.stringify(data)) return

        const { value, type, isProp, isAddr } = data
        // web服务器配置
        if (isAddr) {
          formData.push({ key, value, isProp })
        } else if (key === 'opt2001') {
          const { switch: optSwitch, saveTimeNumber, saveTimeUnit, isOpt } = data
          const value = JSON.stringify({ switch: optSwitch, saveTimeNumber, saveTimeUnit })
          formData.push({ key, value, isOpt })
        } else if (key === 'logVideoTimeRange') {
          const { beforeTime, afterTime, afterRadio, isProp } = data
          const value = JSON.stringify({ beforeTime, afterTime, afterRadio })
          formData.push({ key, value, isProp: !!isProp })
        } else if (key === 'termNameSyncMode') {
          const checkedValue = data.checkedValue
          formData.push({ key, value: this.getSum(checkedValue) })
        } else {
          formData.push({ key, value, type, isProp: !!isProp })
        }
      })
      // 添加资源包内容
      // if (this.fileContent) {
      //   formData.push({ key: 'resourceBundleContent', value: this.fileContent })
      // }
      return formData
    },
    // 终端名称同步方式 勾选状态变化
    termNameSyncModeChange(val) {
      if (val.indexOf(1) == -1 && val.indexOf(2) > -1) {
        val.splice(0)
      }
    },
    // 定期删除日志的配置变化
    opt2001SaveTimeNumberChange() {
      this.dataList.opt2001.showMsg = false
      const { saveTimeNumber, saveTimeUnit } = this.dataList.opt2001
      const time = Number.parseInt(saveTimeNumber)
      if (!time || !Number.isInteger(time) || time < 1) {
        // 1：日，默认180日  2：月，默认6月  3：年，默认1年
        const saveTimeOption = { 1: 180, 2: 6, 3: 1 }
        this.dataList.opt2001.saveTimeNumber = saveTimeOption[saveTimeUnit]
      } else if ((saveTimeUnit === 1 && time < 180) || (saveTimeUnit === 2 && time < 6)) {
        // 当选择 日且时间小于180 或 选择 月且时间小于6 时，显示提示信息
        this.dataList.opt2001.showMsg = true
      }
    },
    updateTimeSubmit() {
      this.validateConfigAndUpdate();
    }
  }
}
</script>

<style lang="scss" scoped>
  .config-type {
    margin: 10px 20px;
    display: flex;
    flex-direction: row;
  }

  .config-all {
    width: 300px;
    height: 120px;
    padding: 10px;
    border-radius: 5px 0 0 5px;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .config-all:hover {
    transform: translateY(-2px);
  }
  .config-all .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
    text-align: center;
  }
  .config-all .description {
    padding: 0 10px;
    font-size: 13px;
    text-align: center;
  }

  .config-items {
    border: 1px solid #aaa;
    border-radius: 0 5px 5px 0;
    display: flex;
    flex: 1;
  }
  .config-item {
    width: 160px;
    height: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    cursor: pointer;
    .svg-icon {
      margin: 0 auto 5px;
      font-size: 50px;
    }
    &:hover .svg-icon {
      margin: 0 auto;
      font-size: 55px;
    }
    .config-item-text {
      height: 20px;
    }
  }

  .config-form-item {
    padding: 5px;
    margin-bottom: 5px;
    border: 1px solid #ccc;
    >>>.el-select .el-input__inner {
      height: 30px !important;
    }
  }

  .global-config {
    padding: 10px;
    .el-form-item{
      // margin-bottom: 15px;
      label{
        text-align: left;
        min-width: 140px;
      }
      .delete-log {
        min-width: 280px;
        >>>.el-checkbox__label{
          line-height: 40px;
        }
      }
    }
    label{
      display: inline-block;
      /*text-align: right;*/
      /*width: 135px;*/
      font-size: 15px;
    }
  }
  .addr-config {
    margin: 30px;
  }
  .group >>>label{
    width: 40px !important;
  }
  .input-file-size {
    width: 160px;
    vertical-align: middle;
  }
  .el-select {
    >>>input:not(:nth-child(1)) {
      border: none;
    }
  }
  .error_style {
    line-height: 20px; color: #F56C6C; font-size: 12px
  }
</style>
