<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.differIntervalTitle')"
    :visible.sync="visible"
    width="600px"
    @close="close"
  >
    <div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="0"
        style="width: 550px; margin: 0 15px;"
      >
        <el-row>
          <el-col v-if="initTemp.responseTimeTotal > 0 && initTemp.intervalTime > initTemp.responseTime" :span="24" style="margin-bottom: 10px">
            <el-divider content-position="left">{{ $t('route.alarmSetup') }}</el-divider>
            <FormItem prop="responseTimeValue">
              <i18n path="pages.differIntervalTimeTip1">
                <el-button slot="link" type="text" @click="toLink('/system/baseData/alarmSetup')">【{{ $t('route.alarmSetup') }}】</el-button>
                <span slot="responseTime">{{ initTemp.responseTime }}</span>
                <el-input-number slot="responseTimeValue" v-model="temp.responseTimeValue" :min="initTemp.intervalTime" :max="3600" :controls="false" :precision="0" style="width:105px; margin-right: 5px"/>秒。
              </i18n>
            </FormItem>
          </el-col>
          <el-col v-if="initTemp.stgIds && initTemp.stgIds.length" :span="24" style="margin-bottom: 10px">
            <el-divider content-position="left">{{ $t('route.processMonitor') }}</el-divider>
            <FormItem prop="stgTimeValue">
              <i18n path="pages.differIntervalTimeTip2">
                <el-button slot="link" type="text" @click="toLink('/behaviorManage/monitor/processMonitor')">【{{ $t('route.processMonitor') }}】</el-button>
                <el-input-number slot="stgTimeValue" v-model="temp.stgTimeValue" :min="initTemp.intervalTime" :max="600" :controls="false" :precision="0" style="width:105px; margin-right: 5px"/>秒。
              </i18n>
            </FormItem>
          </el-col>
        </el-row>
      </Form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="confirm">
        {{ '修改' }}
      </el-button>
      <el-button @click="close()">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateRecordTime } from '@/api/system/configManage/globalConfig';

export default {
  name: 'DifferIntervalTimeDlg',
  components: {},
  props: {},
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {
      },
      defaultTemp: {
        responseTimeValue: 0,   //  违规响应规则的录屏时长
        stgTimeValue: 0         //  程序动作监控的录屏时长
      },
      initTemp: {
      },
      defaultInitTemp: {
        intervalTime: 0,    //  改变后的【全局配置】的录屏间隔
        responseTime: 0,    //  【违规响应规则库】的录屏时长
        responseTimeTotal: 0,   //  【违规响应规则库】中违规响应规则数量
        stgIds: []          // 【程序动作监控策略的配置】的时长低于此时间间隔时的策略Id
      },
      rules: {
        responseTimeValue: [
          { required: true, message: this.$t('pages.required'), trigger: 'blur' }
        ],
        stgTimeValue: [
          { required: true, message: this.$t('pages.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.submitting = false;
      this.initTemp = JSON.parse(JSON.stringify(this.defaultInitTemp));
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
    },
    /**
     * 展示
     */
    show(data) {
      this.initData();
      this.initTemp = Object.assign(this.initTemp, data);
      if (this.initTemp.responseTime) {
        this.temp.responseTimeValue = this.initTemp.intervalTime
      }
      if (this.initTemp.stgIds.length) {
        this.temp.stgTimeValue = this.initTemp.intervalTime
      }
      this.visible = true;
    },
    /**
     * 确认
     */
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          updateRecordTime(this.initTemp.intervalTime, this.temp.responseTimeValue, this.temp.stgTimeValue).then(res => {
            this.$emit('submit');
            this.close();
          })
        }
      })
    },
    /**
     * 关闭
     */
    close() {
      this.visible = false;
    },
    /**
     * 跳转
     */
    toLink(path) {
      const map = {
        '/system/baseData/alarmSetup': 'A5C',
        '/behaviorManage/monitor/processMonitor': 'C34'
      };
      if (this.hasPermission(map[path])) {
        this.$router.push(path);
      }
    }
  }
}
</script>

<style scoped>

</style>
