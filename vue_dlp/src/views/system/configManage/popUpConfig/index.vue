<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('route.popUpConfig')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    :width="$store.getters.language === 'en' ? '700px' : '600px'"
    @dragDialog="handleDrag"
  >
    <Form
      ref="popUpForm"
      class="popup-config"
      :rules="rules"
      :model="popUpForm"
      label-position="right"
      label-width="100px"
    >
      <!-- <el-checkbox v-model="popUpForm.showAlarm" :true-label="1" :false-label="0" style="margin-left: 46px">{{ $t('pages.msgFormTypeConfig') }}</el-checkbox> -->
      <!-- <FormItem :label=" $t('pages.configMethod')">
        <el-radio-group v-model="popUpForm.configMethod" :disabled="popUpForm.showAlarm == 0" @change="((value)=>handleConfigMethodChange(value))">
          <el-radio :label="1">{{ $t('pages.configMethod1') }}</el-radio>
          <el-radio :label="2">{{ $t('pages.configMethod2') }}</el-radio>
        </el-radio-group>
      </FormItem> -->
      <FormItem :label="$t('pages.popUpMode')">
        <el-radio-group v-model="popUpForm.msgFormType">
          <el-radio :label="0">{{ $t('pages.directPopUpWindow') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.msgFormType5') }}</el-radio>
          <el-radio :label="2">{{ $t('pages.msgFormType6') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.popUpPosition')">
        <el-radio-group v-model="popUpForm.msgFormPosition" :disabled="popUpForm.msgFormType === 2">
          <el-radio :label="0">{{ $t('pages.lowerRightCorner') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.preciseMiddle') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem style="margin-left: 10px;">
        <el-checkbox v-model="popUpForm.isScroll" :true-label="1" :false-label="0" :disabled="popUpForm.msgFormType === 2">{{ $t('pages.latestMessages') }}</el-checkbox>
      </FormItem>
      <FormItem prop="msgFormClose" style="margin-left: 10px;">
        <el-checkbox v-model="autoShutdown" :disabled="popUpForm.msgFormType === 2" @change="checkChange">
          <i18n :class="{ disabled: !autoShutdown }" path="pages.autoOffWindow">
            <el-input-number
              slot="info"
              v-model="popUpForm.msgFormClose"
              :controls="false"
              :min="1"
              :max="86400"
              :disabled="popUpForm.msgFormType === 2"
              style="width:100px"
            />
          </i18n>
        </el-checkbox>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateConfig()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPopUpConfig, updatePopUpConfig } from '@/api/system/configManage/popUpConfig'
export default {
  name: 'PopUpConfig',
  data() {
    return {
      popUpForm: {
        showAlarm: 1,
        isScroll: 0,
        configMethod: 1, // 终端弹窗配置方式： 1. 默认配置 2. 自定义配置
        msgFormPosition: 0,
        msgFormType: 0,
        msgFormClose: undefined
      },
      submitting: false,
      autoShutdown: false,
      dialogVisible: false
    }
  },
  computed: {
    rules() {
      return {
        msgFormClose: [
          { required: this.autoShutdown, message: this.$t('pages.validateMsg_number5'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // this.getPopUpConfig()
  },
  methods: {
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.getPopUpConfig()
      })
    },
    getPopUpConfig() {
      getPopUpConfig().then(res => {
        this.popUpForm = res.data
        if (res.data.msgFormClose === 0) {
          this.autoShutdown = false
          this.popUpForm.msgFormClose = undefined
        } else {
          this.autoShutdown = true
        }
      })
    },
    updateConfig() {
      this.$refs['popUpForm'].validate(valid => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.popUpForm)
          if (!this.autoShutdown) {
            tempData.msgFormClose = 0
          }
          updatePopUpConfig(tempData).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogVisible = false
            this.$emit('submit', true)
            this.submitting = false
          }).catch(err => {
            console.log(err)
            this.submitting = false
          })
        }
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    checkChange(value) {
      if (!value) {
        this.popUpForm.msgFormClose = undefined
      }
    },
    handleConfigMethodChange(value) {
      if (value == 1) {
        this.popUpForm.msgFormType = 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .popup-config{
    height: calc(100% - 60px);
    overflow: auto;
    >>>.el-form-item__label{
      // color: #ddd;
      font-size: 15px;
    }
  }
  >>>.el-radio__input.is-disabled+span.el-radio__label{
    color: #848484;
  }
  >>>.el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner {
    background-color: #999a9b;
    border-color: #b4b5b6;
  }
  >>>.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
    background-color: #dbdde3;
  }
  span.disabled{
    color: #848484;
  }
</style>
