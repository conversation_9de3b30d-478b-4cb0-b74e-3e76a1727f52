<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <span style="color: #4ea2e4;font-size: 16px;">{{ $t('pages.ruleLibraryUpgradeInfo') }}</span>
        
        <el-button size="mini" style="float: right;" @click="upgradeRuleLibrary">
          {{ $t('button.modifyRuleLibraryVersion') }}
        </el-button>
        <!-- <span style="color: #4ea2e4;font-size: 16px;float: right;margin-right: 100px;">{{ $t('pages.currentUpdatePackageVersion') }} : {{ currentRuleLibraryVersionInfo.version }}</span> -->
        <span style="color: #4ea2e4;font-size: 16px;float: right;margin-right: 30px;">{{ $t('pages.currentRuleLibraryVersion') }} : {{ currentRuleLibraryVersionInfo.ruleLibraryVersion }}</span>

      </div>
      <grid-table
        ref="urlList"
        :col-model="templateInfoModel"
        :row-data-api="rowDataApi"
        :row-datas="tableData"
        :multi-select="false"
      />
    </div>
    <import-dlg
      ref="importDlgPackage"
      :title="$t('pages.ruleLibraryUpgradePackage')"
      :show-import-type="false"
      accept=".tip"
      :show-import-way="false"
      :show-import-style="false"
      :upload-func="uploadUpdate"
      @success="importEndFunc"
    />
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.ruleLibraryVersionDetails')" 
      :visible.sync="dialogTableVisible"
      :close-on-click-modal="false"
      :modal="false"
    >
      <div>
        {{ $t('table.standardLib') }}:&ensp;<label style="font-size: 16px;">{{ versionDetailsInfo.standardLib }}</label><br/>
        {{ $t('table.fullPackageVersion') }}:&ensp;<label style="font-size: 16px;">{{ versionDetailsInfo.fullVersion }}</label><br/>
        {{ $t('table.updatePackageVersion') }}:&ensp;<label style="font-size: 16px;">{{ versionDetailsInfo.version }}</label><br/>
        {{ $t('table.updatePackagePreVersion') }}:&ensp;<label style="font-size: 16px;">{{ versionDetailsInfo.preVersion }}</label><br/><br/>
      </div>
      <fieldset>
        <legend>{{ $t('table.ruleLibraryUpdateContent') }}</legend>
        <div>
          <el-input
            v-model="versionDetailsInfo.remark"
            type="textarea"
            :rows="13"
            readonly
          >
          </el-input>
        </div> 
      </fieldset>
    </el-dialog>
  </div>
</template>

<script>

import {
  selectCurrentVersionInfo, getLibraryInfoList
} from '@/api/system/configManage/cloudRuleLibraryInfo'
import request from '@/utils/request'
import ImportDlg from '@/views/common/import'

export default {
  name: 'CloudRuleConfig',
  components: { ImportDlg },
  data() {
    return {
      templateInfoModel: [
        { prop: 'ruleLibraryVersion', label: 'ruleLibraryVersion', width: '115', fixed: true },
        { prop: 'modules', label: 'modules', width: '115' },
        { prop: 'importTypeName', label: 'ruleLibraryImportType', width: '115' },
        { prop: 'modifyDate', label: 'modifyDate', width: '115' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '130',
          buttons: [
            { label: 'viewVersionDetails', click: this.viewVersionDetailsInfo }
          ]
        }
      ],
      ruleLibraryVersionModel: [
        { prop: 'preVersion', label: 'fileDescription', width: '100' }
      ],
      query: { // 查询条件
        page: 1
      },
      currentRuleLibraryVersionInfo: {},
      option: '', // 用来存储查找条件，便于方法的二次调用
      tableData: [],
      dialogTableVisible: false,
      versionDetailsInfo: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['ruleList']
    }
  },
  watch: {
    
  },
  created() {
    this.getTemplateInfo()
  },
  methods: {
    viewVersionDetailsInfo: function(row) {
      this.versionDetailsInfo.standardLib = row.standardLib
      this.versionDetailsInfo.fullVersion = row.fullVersion
      this.versionDetailsInfo.version = row.version
      this.versionDetailsInfo.preVersion = row.preVersion
      if (row.remark == undefined || row.remark == null || row.remark == '') {
        this.versionDetailsInfo.remark = this.$t('pages.currentNotVersionInfo')
      } else {
        this.versionDetailsInfo.remark = row.remark
      }
      this.dialogTableVisible = true
    },
    uploadUpdate(data) {
      return request.post('/cloudRuleLibraryInfo/upgradeRuleLibrary', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc() {
      this.tableData = []
      const searchQuery = Object.assign({}, this.query, this.option)
      getLibraryInfoList(searchQuery).then(resp => {
        this.tableData = resp.data.data
      })
      this.getTemplateInfo()
    },
    rowDataApi(option) {
      this.option = option
      const searchQuery = Object.assign({}, this.query, option)
      return getLibraryInfoList(searchQuery)
    },
    getTemplateInfo() {
      selectCurrentVersionInfo().then(resp => {
        this.currentRuleLibraryVersionInfo = resp.data
      })
    },
    upgradeRuleLibrary() {
      this.$refs.importDlgPackage.show()
    }
  }
}
</script>
<style lang="scss" scoped>
  .upload-demo>>>.el-upload-list{
    min-height: 20px;
    max-height: 188px;
    overflow: hidden;
    margin-top: 2px;
    .el-upload-list__item:hover {
      background-color: #dbdbdb;
    }
  }
</style>
