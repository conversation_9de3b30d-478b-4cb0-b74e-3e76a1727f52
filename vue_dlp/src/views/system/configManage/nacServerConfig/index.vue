<template>
  <div class="app-container">
    <div style="height: calc(100% - 60px); overflow: auto;">
      <Form
        ref="temp"
        class="sensitive-config"
        :model="temp"
        :rules="rules"
        label-position="left"
      >
        <FormItem prop="consoleIntranetPort">
          <el-checkbox v-model="temp.consoleIntranetPortEdit" style="margin-left:10px" >{{ $t('pages.dlpConsoleIntranetPort') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.dlpConsoleTip1') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
          <el-input-number v-model="temp.consoleIntranetPort" style="width:120px" :min="1" :max="65535" :controls="false" :disabled="!temp.consoleIntranetPortEdit" />
        </FormItem>
        <FormItem prop="consoleDlpPort">
          <el-checkbox v-model="temp.consoleDlpPortEdit" style="margin-left:10px" >{{ $t('pages.dlpConsoleServerPort') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.dlpConsoleTip1') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
          <el-input-number v-model="temp.consoleDlpPort" style="width:120px" :min="1" :max="65535" :controls="false" :disabled="!temp.consoleDlpPortEdit"/>
        </FormItem>
      </Form>
    </div>
    <el-button type="primary" size="mini" style="position: absolute; left: 500px; bottom: 30px;" :loading="submitting" @click="updateConfig">{{ $t('button.save') }}</el-button>
  </div>
</template>

<script>
import { getConsolePort, updateConsolePort } from '@/api/system/deviceManage/nacServer';

export default {
  name: 'NacServerConfig',
  data() {
    return {
      temp: {
        consoleIntranetPortEdit: false,
        consoleIntranetPort: undefined,
        consoleDlpPortEdit: false,
        consoleDlpPort: undefined
      },
      submitting: false
    }
  },
  computed: {
    rules() {
      return {
      }
    }
  },
  mounted() {
    this.initConfig()
  },
  methods: {
    initConfig() {
      return getConsolePort().then(res => res.data).then(configs => {
        console.log('configs', configs)
        for (const key in this.temp) {
          if (!this.temp.hasOwnProperty(key)) {
            continue
          }
          if (configs.hasOwnProperty(key)) {
            this.temp[key] = configs[key]
            console.log('config', configs[key])
          } else if (key === 'consoleExtranetEnable') {
            this.temp[key] = '0'
          } else {
            this.temp[key] = ''
          }
        }
      })
    },
    getFormData() {
      return [{
        'code': 'console.intranet.port',
        'value': this.temp.consoleIntranetPort,
        'editable': this.temp.consoleIntranetPortEdit
      }, {
        'code': 'console.dlp.port',
        'value': this.temp.consoleDlpPort,
        'editable': this.temp.consoleDlpPortEdit
      }]
    },
    updateConfig() {
      this.$refs['temp'].validate(valid => {
        if (valid) {
          this.submitting = true
          const data = this.getFormData()
          updateConsolePort(data).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.initConfig()
            this.submitting = false
          }).catch(err => {
            console.log(err)
            this.submitting = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .sensitive-config{
    margin: 20px 0 0 30px;
    width: 525px;
    >>>.el-form-item__label{
      color: #fff;
      font-size: 15px;
    }
    .outsend{
      display: flex;
      flex-wrap:wrap;
      div{
        width:200px;
      }
      div:nth-child(2n){
        width:140px;
      }
    }
  }
  .el-form-item{
    margin-bottom: 15px;
  }

</style>
