<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="receiverGroupTree"
        resizeable
        :default-expand-all="true"
        :data="ldMenuTreeData"
        @node-click="menuTreeCheckChange"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" @click="handleLdImport">
          {{ $t('button.import') }}
        </el-button>
      </div>
      <el-row style="line-height: 30px;color: #bbb;font-size: 14px;">
        <el-col :span="24">{{ `${$t('layout.moduleName')}：${nodeData.label}` }}</el-col>
        <el-col :span="24">{{ `${$t('table.suffixes')}：${nodeData.suffix}` }}</el-col>
        <el-col :span="24">{{ `${$t('pages.fileContent')}：${nodeData.remark}` }}</el-col>
        <el-col v-if="nodeData.effectInfo" :span="24">{{ $t('pages.achieveEffect') }}：</el-col>
        <el-col v-for="(msg, i) in nodeData.effectInfo" :key="'e' + i" :span="24" style="padding-left: 20px;">{{ i + 1 }}、{{ $t(msg) }}</el-col>
        <el-col v-if="nodeData.notice" :span="24">{{ $t('pages.beCareful') }}：</el-col>
        <el-col v-for="(msg, i) in nodeData.notice" :key="'n' + i" :span="24" style="padding-left: 20px;">{{ i + 1 }}、{{ $t(msg) }}</el-col>
      </el-row>
    </div>
    <import-org
      ref="importOrgDlg"
      :title="$t('pages.ldOrganizationalStructure')"
      accept=".base"
      tip="部门名称、操作员账号、终端GUID"
      :show-import-type="false"
      :show-import-way="true"
      :upload-func="uploadOrgFunc"
    />
    <import-stg
      ref="importProcessDlg"
      :title="nodeData.label"
      :accept="nodeData.suffix"
      :os-able="nodeData.params.osAble"
      :data-type="nodeData.params.dataType"
      :term-able="nodeData.params.termAble"
      :user-able="nodeData.params.userAble"
      :auto-name="nodeData.params.autoName"
      :import-type2="nodeData.params.importType2"
    />
  </div>
</template>

<script>
import { getPropertyPage, updateProperty } from '@/api/property';
import { uploadFile } from '@/api/grantAuth';
import ImportOrg from '@/views/common/importOrg'
import ImportStg from '@/views/common/importStg'
import request from '@/utils/request'

export default {
  name: 'LvDun',
  components: { ImportStg, ImportOrg },
  props: {
    likeCode: {
      type: String,
      default: null
    },
    exceptCode: {
      type: String,
      default: 'log.level'
    },
    isDlg: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ldMenuTreeData: [
        { id: '0', label: this.$t('pages.module'), parentId: '', children: [
          { id: '1', label: this.$t('route.structure'), suffix: '.base', parentId: '0', params: {},
            remark: this.$t('pages.ldConfigRemark1'),
            effectInfo: ['pages.ldConfigEffectInfo1', 'pages.ldConfigEffectInfo2'],
            notice: [
              'pages.ldConfigNotice1',
              'pages.ldConfigNotice2',
              'pages.ldConfigNotice3'
            ]
          },
          { id: '2', label: this.$t('route.processStgLib'), suffix: '.tip', parentId: '0',
            params: {
              dataType: '1', termAble: false, userAble: false, autoName: false, osAble: true, importType2: { name: this.$t('pages.importAndAdd'), tipMsg: this.$t('pages.importAndAdd_Msg') }
            },
            remark: this.$t('pages.ldConfigRemark2'),
            effectInfo: ['pages.ldConfigEffectInfo3', 'pages.ldConfigEffectInfo4']
          },
          { id: '3', label: this.$t('pages.processStgMsg2'), suffix: '.tipa', parentId: '0',
            params: {
              dataType: '1', termAble: false, userAble: true, autoName: true, osAble: true
            },
            remark: this.$t('pages.ldConfigRemark3'),
            effectInfo: ['pages.ldConfigEffectInfo5', 'pages.ldConfigEffectInfo6']
          },
          { id: '4', label: this.$t('route.translucentEncStrategy'), suffix: '.ldt', parentId: '0',
            params: {
              dataType: '2', termAble: false, userAble: true, autoName: true, osAble: false
            },
            remark: this.$t('pages.ldConfigRemark4'),
            effectInfo: ['pages.ldConfigEffectInfo7', 'pages.ldConfigEffectInfo8']
          },
          { id: '5', label: this.$t('route.contentStg'), suffix: '.ldi', parentId: '0',
            params: {
              dataType: '11', termAble: true, userAble: true, autoName: true, osAble: false
            },
            remark: this.$t('pages.ldConfigRemark5'),
            effectInfo: ['pages.ldConfigEffectInfo9', 'pages.ldConfigEffectInfo10']
          }
        ] }
      ],
      query: { // 查询条件
        page: 1,
        code: '',
        exceptCode: ''
      },
      temp: {},
      fileTemp: {
        fileName: ''
      },
      showTree: true,
      nodeData: { params: {}},
      rule: undefined,
      dialogVisible: false,
      dialogFileVisible: false,
      dialogFormVisible: false,
      submitting: false,
      activeTab: 'configTab',
      rules: {
        value: [{ required: true, message: this.$t('layout.enterConfigValue'), trigger: 'blur' }],
        remark: [{ required: true, message: this.$t('layout.enterRemark'), trigger: 'blur' }]
      },
      fileRules: {
        fileName: [
          { required: true, message: this.$t('pages.signatureData_text2'), trigger: 'blur' },
          { validator: this.fileNameValid, trigger: 'blur' }
        ]
      },
      showHidden: false,
      fileList: []
    };
  },
  computed: {
    gridTable() {
      return this.$refs['propList']
    }
  },
  beforeRouteEnter(to, from, next) {
    const isPageLoaded = window.getMenuAccessStatus('LvDun')
    const isRefresh = from.path == '/redirect/config/LD'
    // 通过 搜索、已访问过、右键菜单刷新
    if (to.params.search || isPageLoaded || isRefresh) {
      next()
    } else {
      next('/404')
    }
  },
  created() {
    this.setMenuAccessStatus('LvDun')
    document.addEventListener('keyup', this.handleKeyUp)
    this.nodeData = this.ldMenuTreeData[0].children[0]
  },
  methods: {
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    menuTreeCheckChange(tabName, checkedNode) {
      checkedNode.expanded = true
      const nodeId = Number.parseInt(checkedNode.key)
      if (nodeId) {
        this.nodeData = checkedNode.data
      }
    },
    parseRule(rule) {
      if (typeof rule !== 'string' || rule.trim().length === 0) {
        return {}
      }
      try {
        return JSON.parse(rule)
      } catch (e) {
        return {}
      }
    },
    notifySuccess(msg) {
      this.$notify({
        title: this.$t('text.success'),
        message: msg,
        type: 'success',
        duration: 2000
      })
    },
    handleKeyUp(e) {
      if (e.ctrlKey && e.altKey && e.shiftKey) {
        if (e.key === 's' || e.key === 'S') {
          this.dialogVisible = true
        }
        e.preventDefault()
      }
      if (e.ctrlKey && e.altKey && e.shiftKey) {
        if (e.key === 'k' || e.key === 'K') {
          this.showHidden = true
        }
        e.preventDefault()
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleExport() {
      this.$refs.mainKeyFile.handleExport()
    },
    handleLdImport() {
      if (this.nodeData.id === '1') {
        this.$refs.importOrgDlg.show()
      } else {
        this.$refs.importProcessDlg.show()
      }
    },
    uploadOrgFunc(data) {
      return request.post('/department/importOrg', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    handleFileImport() {
      this.fileTemp = {
        fileName: ''
      }
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles()
      })
      this.dialogFileVisible = true
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (this.exceptCode) {
        searchQuery.exceptCode = this.exceptCode
      }
      if (this.likeCode) {
        searchQuery.code = this.likeCode
      }
      return getPropertyPage(searchQuery)
    },
    handleUpdate(row) {
      this.submitting = false
      this.temp = Object.assign({}, row)
      this.rule = this.parseRule(this.temp.rule)
      if ((this.temp.type === 'enum' && this.rule.multiple)) {
        this.temp.value = this.temp.value.split(',')
      } else if (this.temp.type === 'datetime' && this.rule.type && this.rule.type.endsWith('range')) {
        this.temp.value = this.temp.value.split('~')
      }
      this.dialogFormVisible = true
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
    },
    updateFile() {
      this.submitting = true
      this.$refs['fileDataForm'].validate((valid) => {
        if (valid) {
          const fd = new FormData()
          // const file = this.$refs['upload']
          const uploadFiles = this.$refs['upload'].uploadFiles
          fd.append('uploadFile', uploadFiles[uploadFiles.length - 1].raw) // 传文件
          uploadFile(fd).then(res => {
            this.dialogFileVisible = false
            this.submitting = false
            this.$message({
              message: this.$t('text.uploadSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = {
            code: this.temp.code,
            value: Array.isArray(this.temp.value) ? this.temp.value.join(this.temp.type === 'enum' ? ',' : '~') : this.temp.value,
            remark: this.temp.remark
          }
          updateProperty(tempData).then(_ => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.notifySuccess(this.$t('text.updateSuccess'))
          }).catch(_ => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    beforeUpload(file, fileList) {
      this.fileTemp.fileName = file.name
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    fileNameValid(rule, value, callback) {
      if (this.fileTemp.fileName != 'ArResRights.ini') {
        callback(new Error(this.$t('layout.ArResRights')))
      } else {
        callback()
      }
    }
  }
}
</script>
