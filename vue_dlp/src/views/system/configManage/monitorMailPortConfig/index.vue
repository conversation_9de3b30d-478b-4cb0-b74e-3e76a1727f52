<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addMonitorMailPort') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteMonitorMailPort') }}
        </el-button>
        <div style="margin: 7px 20px 0px 0px; float: right; color: #68a8d0; ">
          <span>{{ $t('pages.monitorMailPortMsg') }}</span>
        </div>
      </div>
      <grid-table
        ref="portTable"
        :multi-select="true"
        :col-model="colModel"
        :row-datas="portList"
        :show-pager="false"
        @selectionChangeEnd="portSelectionChangeEnd"
      />
    </div>
    <!--邮件监控端口配置-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t(textMap[dialogStatus]) }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.onlySSL') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">
        <FormItem :label="$t('table.protocol')">
          <el-select v-model="temp.protocol">
            <el-option v-for="(value, key) in protocolOptions" :key="key" :label="value" :value="Number.parseInt(key)" />
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.monitorMailPort')" prop="port">
          <el-input v-model.number="temp.port" maxlength="5" />
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchList, createData, updateData, deleteData } from '@/api/system/configManage/monitorMailPortConfig'

export default {
  name: 'MonitorMailPortConfig',
  data() {
    return {
      colModel: [
        { prop: 'port', label: 'monitorMailPort', width: '150', fixed: true, sort: true },
        { prop: 'protocol', label: 'protocol', width: '150', sort: true, formatter: this.protocolFormatter },
        { prop: 'remark', label: 'remark', width: '200' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      deleteable: false,
      temp: {},
      defaultTemp: {
        id: undefined,
        protocol: 1,
        port: null,
        remark: ''
      },
      dialogFormVisible: false,
      rules: {
        port: [
          { required: true, message: this.$t('pages.validateMsg_port'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      configKey: 'monitorMailPort',
      configTemp: {},
      textMap: {
        update: this.i18nConcatText(this.$t('table.monitorMailPort'), 'update'),
        create: this.i18nConcatText(this.$t('table.monitorMailPort'), 'create')
      },
      dialogStatus: '',
      protocolOptions: {
        1: this.$t('pages.mail_protocol1'),
        2: this.$t('pages.mail_protocol2'),
        3: this.$t('pages.mail_protocol3')
      },
      portList: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['portTable']
    }
  },
  watch: {
    dialogFormVisible: function(val) {
      if (!val) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        })
      }
    }
  },
  created() {
    this.resetTemp()
    this.getPortList()
  },
  methods: {
    getPortList() {
      fetchList().then(respond => {
        this.portList = respond.data
      })
    },
    portSelectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    protocolChange(val) {
      // 切换协议，端口恢复默认值，SMTP-25，POP3-110 ，IMAP-143
      if (val == 2) { // POP3
        this.temp.port = 110
      } else if (val == 3) { // IMAP
        this.temp.port = 143
      } else { // SMTP以及其他
        this.temp.port = 25
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        // const ports = this.gridTable.getDatas()
        // const portArray = []
        // // 删除邮件监控端口
        // ports.forEach((item, index) => {
        //   if (toDeletedIds.indexOf(item.id) < 0) {
        //     portArray.push(item)
        //   }
        // })
        // this.configTemp.value = JSON.stringify(portArray)
        deleteData({ ids: toDeleteIds.join(',') }).then(respond => {
          this.submitting = false
          this.gridTable.deleteRowData(toDeleteIds)
          // 删除邮件监控端口
          const deleteKeys = new Set(toDeleteIds);
          // 使用filter一次性过滤
          this.portList = this.portList.filter(({ id }) =>
            !deleteKeys.has(id)
          );
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }).catch(reason => {
          this.submitting = false
        })
      }).catch(() => {})
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = new Date().getTime()
          const ports = this.gridTable.getDatas()
          ports.push(this.temp)
          // this.configTemp.value = JSON.stringify(ports)
          createData(this.temp).then(respond => {
            this.submitting = false
            this.portList.push(this.temp)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // const ports = this.gridTable.getDatas()
          // ports.forEach(item => {
          //   if (item.id == this.temp.id) {
          //     item.port = this.temp.port
          //     item.protocol = this.temp.protocol
          //     item.remark = this.temp.remark
          //   }
          // })
          // this.configTemp.value = JSON.stringify(ports)
          updateData(this.temp).then(respond => {
            this.submitting = false
            const index = this.portList.findIndex(item => item.id === this.temp.id)
            this.portList.splice(index, 1, Object.assign({}, this.temp))
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    portValidator(rule, value, callback) {
      console.log(111, isNaN(Number(value)))
      console.log(222, Number(value) != parseInt(value))
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      }
      if (value < 1 || value > 65535) {
        callback(new Error(this.$t('pages.serverLibrary_text6')))
      } else {
        const index = this.portList.findIndex(item => {
          return item.id != this.temp.id && item.port == this.temp.port
        })
        if (index > -1) {
          callback(new Error(this.$t('pages.globalConfig_Msg11')))
        } else {
          callback()
        }
      }
    },
    protocolFormatter(row, data) {
      return this.protocolOptions[data]
    }
  }
}
</script>
