<template>
  <div class="app-container">
    <Form style="width: 400px" inline>
      <FormItem label="系统名称">
        <el-input v-model="data.title"></el-input>
      </FormItem>
      <FormItem label="版权">
        <el-input v-model="data.copyright"></el-input>
      </FormItem>
      <FormItem label="公司网址">
        <el-input v-model="data.website"></el-input>
      </FormItem>
      <FormItem label="主题">
        <el-input v-model="data.theme"></el-input>
      </FormItem>
      <FormItem label="登录页图标">
        <el-upload
          ref="upload"
          name="uploadFile"
          action="aaaaaa"
          accept=".properties"
          :show-file-list="false"
          :before-upload="beforeUpload"
        >
          <el-button type="primary" size="mini">上传 properties 文件</el-button>
        </el-upload>
      </FormItem>
      <FormItem label="导航栏图标">
        <el-upload
          ref="upload"
          name="uploadFile"
          action="aaaaaa"
          accept=".properties"
          :show-file-list="false"
          :before-upload="beforeUpload"
        >
          <el-button type="primary" size="mini">上传 properties 文件</el-button>
        </el-upload>
      </FormItem>
      <FormItem label="登录页图标">
        <el-upload
          ref="upload"
          name="uploadFile"
          action="aaaaaa"
          accept=".properties"
          :show-file-list="false"
          :before-upload="beforeUpload"
        >
          <el-button type="primary" size="mini">上传 properties 文件</el-button>
        </el-upload>
      </FormItem>
    </Form>
    <el-row>
      <el-col :span="3">
        <upload-dir ref="uploadDir" :file-suffix="['png']" text="上传资源目录" :popover-height="235" style="padding-top: 2px" @changeFile="changeFile" />
        <div>
          <p v-for="item in resourceList" :key="item.name"> {{ item.name }}</p>
        </div>
      </el-col>
      <el-col :span="6">
        <el-upload
          ref="upload"
          name="uploadFile"
          action="aaaaaa"
          accept=".properties"
          :show-file-list="false"
          :before-upload="beforeUpload"
        >
          <el-button type="primary" size="mini">上传 properties 文件</el-button>
        </el-upload>
        <p v-if="properties">{{ properties.name }}</p>
      </el-col>
      <el-col :span="3">
        <el-button type="primary" size="mini" @click="build">开始制作</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import UploadDir from '@/components/UploadDir'
import { encodeResourceBundle } from '@/api/system/configManage/globalConfig'
import { readAsDataURL, readAsText } from '@/utils/blob'

function readProperties(fileContent) {
  // 定义正则表达式匹配键值对
  const regex = /^\s*([^#\s][^=]*)\s*=\s*(.*?)\s*(?=#|$)/gm;

  // 存储解析后的键值对
  const keyValuePairs = {};

  // 使用正则表达式提取键值对
  let match;
  while ((match = regex.exec(fileContent)) !== null) {
    const key = match[1].trim();
    const value = match[2].trim();
    keyValuePairs[key] = value;
  }

  return keyValuePairs;
}

export default {
  name: 'ResourceBuilder',
  components: { UploadDir },
  data() {
    return {
      resourceList: [],
      properties: null,
      data: {
        title: '天锐数据泄露防护系统',
        copyright: 'Copyright©2006-2023 厦门天锐科技股份有限公司',
        website: '',
        theme: '',
        loginLogo: '',
        topLogo: '',
        favicon: ''
      }
    }
  },
  methods: {
    changeFile(files) {
      this.resourceList = files
      console.log(files)
    },
    beforeUpload(file) {
      console.log(file)
      this.properties = file
      return false
    },
    async build() {
      const data = await readAsText(this.properties)
      const json = readProperties(data)
      console.log(json)
      const resourceMap = {}
      for (const file of this.resourceList) {
        resourceMap[file.webkitRelativePath] = file
      }
      console.log(resourceMap)
      for (const key in json) {
        if (resourceMap[json[key]]) {
          json[key] = await readAsDataURL(resourceMap[json[key]])
        }
      }
      json.builder = true
      console.log(json)
      await encodeResourceBundle(json)
      this.$message({
        message: '制作完成',
        type: 'success'
      })
      // const url = window.URL.createObjectURL(new Blob([response.data]));
      // const link = document.createElement('a');
      // link.href = url;
      // link.setAttribute('download', 'web.source'); // 设置下载文件的文件名
      // document.body.appendChild(link);
      // link.click();
    }
  }
}
</script>
