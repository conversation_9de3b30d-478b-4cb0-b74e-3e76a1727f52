<template>
  <el-dialog
    ref="sourceForm"
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="true"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dialogFormVisible"
    width="750px"
    @closed="submitting=false"
    @dragDialog="handleDrag"
  >
    <div slot="title" class="el-dialog__title">
      {{ textMap[dialogStatus] }}
      <el-tooltip v-show="temp.sourceType === 7" effect="light" placement="bottom-start">
        <span slot="content">
          <div class="sample-title" style="font-weight: bolder;font-size: 16px">{{ $t('pages.ldapDepartmentExample') }}</div>
          <img class="sample-image" :src="require('@/assets/ldap/ldap.jpg')" :alt="$t('pages.ldapDepartmentExample')">
        </span>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <Form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="130px"
      style="margin-left: 20px"
    >
      <!--数据源类型下拉框-->
      <el-row>
        <el-col :span="24">
          <FormItem :label="$t('pages.dataSourceType')" prop="sourceType">
            <el-select v-model="temp.sourceType" :placeholder="$t('pages.pleaseSelectContent',{content:$t('pages.dataSourceType')})" style="width:500px" :disabled="!formable" @change="changeType">
              <el-option v-for="item in sourceTypes" :key="item.id" :label="item.label" :value="item.id"/>
            </el-select>
          </FormItem>
        </el-col>
      </el-row>
      <el-row v-if="isShowItem === true && temp.sourceType !== 2">
        <el-col :span="24">
          <FormItem :label="$t('table.name')" prop="name">
            <el-input
              v-model="temp.name"
              v-trim
              style="width:500px"
              maxlength="60"
              @input.native="wipeReplaceChar($event)"
            />
          </FormItem>
        </el-col>
      </el-row>
      <template v-if="isShowItem === true">
        <!--数据源Form信息项-->
        <el-row v-for="(item, key) in sourceItems[temp.sourceType]" :key="key" >
          <el-col :span="24">
            <span v-if="temp.sourceType === 2 && item.id==='password'" style="margin-left:100px;color: #0c60a5">{{ $t('pages.adDomain_text3') }}</span>
            <el-divider v-if="temp.sourceType === 3 && item.id==='loginUrl'" content-position="left">{{ $t('pages.dataSourceSystemConfig',{ type: 'IDM'}) }}：</el-divider>
            <el-divider v-if="temp.sourceType === 3 && item.id==='esbUrl'" content-position="left">{{ $t('pages.dataSourceSystemConfig',{ type: 'ESB'}) }}：</el-divider>
            <!-- 从sourceItems里获取的信息项，遍历展示 -->
            <FormItem
              v-if="!item.other"
              :label="item.label"
              :prop="`config.${item.id}`"
              :rules="rulesSelect(item)"
            >
              <!--              <el-input-->
              <!--                v-model="temp.config[item.id]"-->
              <!--                clearable-->
              <!--                maxlength="800"-->
              <!--                :type="item.type"-->
              <!--                :placeholder="item.placeholder"-->
              <!--                :show-password="item.showPassword"-->
              <!--                style="width:500px"-->
              <!--                @input.native="wipeReplaceChar($event)"-->
              <!--                @blur="blurChar($event, item.id)"-->
              <!--              />-->
              <encrypt-input
                v-model="temp.config[item.id]"
                clearable
                maxlength="800"
                :type="item.type === 'password' ? 'password' : 'text'"
                :placeholder="item.placeholder"
                :show-password="item.showPassword"
                style="width:500px"
                @input.native="wipeReplaceChar($event)"
                @blur.capture.native="blurChar($event, item.id)"
              />
            </FormItem>
            <!--AD域名 同步时间间隔-->
            <FormItem v-if="item.id === 'synCycle' && temp.sourceType === 2" :label="$t('pages.synCycle')" prop="synCycle">
              <el-input-number v-model="temp.config.synCycle" style="width: 40%" :min="5" :max="99999"/>
              {{ $t('pages.adDomain_text4') }}
            </FormItem>
            <!--企业微信 域名归属认证-->
            <FormItem v-else-if="item.id === 'fileName' && temp.sourceType === 4" :label="$t('pages.dataSourceDomainNameAuthentication')">
              <el-input v-model="temp.config.fileName" disabled style="width:400px" :title="$t('pages.dataSourceDomainNameAuthenticationMsg')" :placeholder="$t('pages.dataSourceDomainNameAuthenticationMsg')"/>
              <el-upload
                ref="upload"
                style="margin-left: 420px;margin-top: -33px"
                class="upload-demo"
                name="uploadFile"
                action="aaaaaa"
                accept=".txt"
                :limit="1"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleUploadFileChange"
              >
                <el-button size="small" type="primary">{{ $t('pages.uploadFile1') }}</el-button>
              </el-upload>
            </FormItem>
            <!--LDAP objectClass配置-->
            <span v-else-if="item.id === 'otherConfig' && temp.sourceType===7">
              <FormItem style="margin-left: -100px;width: 700px">
                <i18n path="pages.ldapSyncInfo">
                  <el-select slot="info" v-model="temp.config.groupClass" :placeholder="$t('pages.pleaseSelectOrManuallyEnter')" filterable allow-create clearable style="width:200px" @input="temp.config.groupClass = temp.config.groupClass.replace(/[\r\n\s\t]/g, '')">
                    <el-option v-for="option in groupClassOptions" :key="option.id" :label="option.label" :value="option.id"></el-option>
                  </el-select>
                  <template slot="type">{{ $t('pages.dept') }}</template>
                </i18n>
                <el-tooltip effect="light" placement="bottom-start">
                  <span slot="content">
                    <div class="sample-title" style="font-weight: bolder;font-size: 16px">{{ $t('pages.ldapObjectClassExampleInfo', { info: $t('pages.dept')}) }}</div>
                    <img class="sample-image" :src="require('@/assets/ldap/dept.jpg')" :alt="$t('pages.ldapObjectClassExampleInfo', { info: $t('pages.dept')})">
                  </span>
                  <i class="el-icon-info" />
                </el-tooltip>
              </FormItem>
              <FormItem style="margin-left: -100px;width: 700px">
                <i18n path="pages.ldapSyncInfo">
                  <el-select slot="info" v-model="temp.config.userClass" :placeholder="$t('pages.pleaseSelectOrManuallyEnter')" filterable allow-create clearable style="width:200px" @input="temp.config.userClass = temp.config.userClass.replace(/[\r\n\s\t]/g, '')">
                    <el-option v-for="option in userClassOptions" :key="option.id" :label="option.label" :value="option.id"></el-option>
                  </el-select>
                  <template slot="type">{{ $t('pages.user') }}</template>
                </i18n>
                <el-tooltip effect="light" placement="bottom-start">
                  <span slot="content">
                    <div class="sample-title" style="font-weight: bolder;font-size: 16px">{{ $t('pages.ldapObjectClassExampleInfo', { info: $t('pages.user')}) }}</div>
                    <img class="sample-image" :src="require('@/assets/ldap/user.jpg')" :alt="$t('pages.ldapObjectClassExampleInfo', { info: $t('pages.user')})">
                  </span>
                  <i class="el-icon-info" />
                </el-tooltip>
              </FormItem>
            </span>
            <FormItem v-show="item.id === 'redirectUrl'" :label="$t('pages.dataSourceRedirectUrl')" prop="prefixPath">
              <el-input v-model="temp.prefixPath" :placeholder="$t('pages.syncRedirectUrlTips')" style="width:500px" @input="saveRedirectUrl">
                <template slot="append">{{ urlMap[temp.sourceType] }}</template>
              </el-input>
              <el-button style="position: absolute;margin-left: 6px;margin-top: 4px" icon="el-icon-document-copy" @click="copyContent(temp.config.redirectUrl)"/>
            </FormItem>
          </el-col>
        </el-row>
      </template>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-show="isShowItem === true" type="primary" :loading="testConnectting" @click="testConnect()">
        {{ $t('pages.testConnect') }}
      </el-button>
      <el-button v-show="isShowItem === true" type="primary" :loading="submitting" @click="saveSourceConfig()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import elDragDialog from '@/directive/el-dragDialog'
import {
  addSyncSource,
  testConnect,
  updateSyncSource,
  uploadFile,
  getSyncSourceByName,
  validConfig
} from '@/api/system/configManage/syncConfig';
import { sourceItems, syncSourceList } from './sourceItems'
import { aesDecode, aesEncode, formatAesKey } from '@/utils/encrypt'
import { copy } from '@/api/dataEncryption/fileTrace/documentTrack';
import EncryptInput from '@/components/EncryptInput'

export default {
  name: 'SourceConfigForm',
  components: { EncryptInput },
  directives: { elDragDialog },
  props: {
    hiddenSource: { // 隐藏的数据源
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      temp: {},
      defaultTemp: {
        syncId: undefined,
        sourceType: undefined,
        name: undefined,
        config: {},
        prefixPath: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      testConnectting: false,
      formable: true,
      sourceItems: sourceItems,
      rules: {
        name: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        sourceType: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        prefixPath: [
          { validator: this.redirectUrlValidator, trigger: 'blur' }
        ]
      },
      textMap: {
        update: this.i18nConcatText(this.$t('text.dataSource'), 'update'),
        create: this.i18nConcatText(this.$t('text.dataSource'), 'create')
      },
      // 是否显示配置项
      isShowItem: false,
      groupClassOptions: [
        { id: 'organizationalUnit', label: 'organizationalUnit' },
        { id: 'organization', label: 'organization' }
      ],
      userClassOptions: [
        { id: 'posixAccount', label: 'posixAccount' },
        { id: 'inetOrgPerson', label: 'inetOrgPerson' },
        { id: 'organizationalPerson', label: 'organizationalPerson' },
        { id: 'person', label: 'person' },
        { id: 'user', label: 'user' }
      ],
      encryptFieldMap: {
        2: ['password'], 3: ['appKey', 'encodeParam', 'esbSystemKey'], 4: ['secretKey'], 5: ['secretKey'], 7: ['password'], 8: ['secretKey'], 9: ['clientSecret']
      },
      encryptTemp: {},
      urlMap: {
        4: '/dlp/syncRedirect/wecom/auth',
        5: '/dlp/syncRedirect/dingtalk/auth',
        8: '/dlp/syncRedirect/feishu/auth'
      }
    }
  },
  computed: {
    sourceTypes() {
      const sourceList = syncSourceList('1');
      for (let i = 0; i < sourceList.length; i++) {
        const item = sourceList[i]
        if (this.hiddenSource.indexOf(item.id) > -1) {
          sourceList.splice(i, 1)
        }
      }
      return sourceList
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show(row) {
      this.resetTemp()
      this.dialogFormVisible = true;
      this.dialogStatus = 'create'
      this.formable = true;
      this.isShowItem = false;
      if (row) {
        this.temp = Object.assign({}, this.defaultTemp, row, {
          config: JSON.parse(row.config)
        })
        this.formatRedirectUrl(this.temp.config.redirectUrl)
        const encryptFields = this.encryptFieldMap[this.temp.sourceType] || []
        if (encryptFields.length > 0) {
          encryptFields.forEach(field => {
            if (this.temp.config[field]) {
              this.encryptTemp[field] = this.temp.config[field]
              this.temp.config[field] = '        '
            }
          })
        }
        this.dialogStatus = 'update'
        this.formable = false;
        this.isShowItem = true;
      }
      // 选择后先清空验证文字
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.encryptTemp = {}
      this.temp.prefixPath = undefined;
    },
    formatRedirectUrl(url) {
      if (url) {
        try {
          const parsedUrl = new URL(url);
          // 分别获取协议、主机（含域名和端口）、路径
          const protocol = parsedUrl.protocol; // "http:"
          const domainAndPort = parsedUrl.host; // "study.tipray.com:28080"
          this.temp.prefixPath = protocol + '//' + domainAndPort;
        } catch (e) {
          this.temp.prefixPath = 'http://example.com';
        }
      } else {
        this.temp.prefixPath = undefined;
      }
    },
    saveRedirectUrl(value) {
      if (value) {
        this.temp.config.redirectUrl = value + (this.urlMap[this.temp.sourceType] ? this.urlMap[this.temp.sourceType] : '');
      } else {
        this.temp.config.redirectUrl = undefined;
      }
    },
    changeType(value) {
      // 赋值name
      // this.temp.name = syncSources[value];
      this.isShowItem = false
      // 此处判断是否数据源只能配置一种，如果是，则不允许配置
      validConfig(value).then(res => {
        if (res.data === false) {
          this.isShowItem = false
          // this.isShowItem = true // 改成true可正常录入
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.syncDataSourceHasAlreadyConfigTips'),
            type: 'warning',
            duration: 2000
          })
        } else {
          this.isShowItem = true
        }
      }).catch(e => {
        this.isShowItem = false
      })
      // 选择后先清空验证文字
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
      // 进行部分初始化工作
      if ('create' === this.dialogStatus) {
        this.temp.config = {};
        if (value === 2) {
          const config = {
            host: '',
            port: 389,
            account: 'Administrator',
            password: '',
            synCycle: 30,
            synType: 0,
            // 用户同步字段
            synUserAttrRef: {
              account: 'userPrincipalName'
            }
          }
          this.temp = Object.assign({}, this.temp, {
            config: config
          })
        } else if (value === 4) {
          this.temp.config.type = 1;
        } else if (value === 5) {
          this.temp.config.type = 2;
        } else if (value === 7) {
          const config = {
            ip: '',
            port: 389,
            userName: 'cn=Manager,dc=test,dc=com',
            password: '',
            groupClass: '',
            userClass: ''
          }
          this.temp = Object.assign({}, this.temp, {
            config: config
          })
        } else if (value === 8) {
          this.temp.config.type = 3;
        }
      }
      // this.temp.sourceType = value;
    },
    handleDrag() {
    },
    validateFormData() {
      if (this.temp.sourceType === 7) {
        if (this.temp.config.groupClass.trim() === '' || this.temp.config.userClass.trim() === '') {
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.ldapObjectClassNullTips'), type: 'error', duration: 2000 })
          return false;
        }
      }
      return true
    },
    saveSourceConfig() {
      this.submitting = true
      if (this.validateFormData()) {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            const postData = this.formatFormData()
            const syncId = postData.syncId;
            if (null === syncId || undefined === syncId) {
              addSyncSource(postData).then(res => {
                this.dialogFormVisible = false
                this.$emit('submitEnd', res)
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(e => {
                this.$emit('submitEnd', e)
                this.submitting = false;
              })
            } else {
              updateSyncSource(postData).then(res => {
                this.dialogFormVisible = false
                this.$emit('submitEnd', res)
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.updateSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(e => {
                this.$emit('submitEnd', e)
                this.submitting = false;
              })
            }
          } else {
            this.submitting = false;
          }
        })
      } else {
        this.submitting = false;
      }
    },
    formatFormData() {
      this.saveRedirectUrl(this.temp.prefixPath)
      // 格式化同步数据源
      const temp = JSON.parse(JSON.stringify(this.temp))
      const fields = this.encryptFieldMap[this.temp.sourceType]
      fields.forEach(field => {
        if (!temp.config[field] || !temp.config[field].trim()) {
          temp.config[field] = this.encryptTemp[field]
        }
      })
      if (temp.sourceType === 7 && temp.config.port) {
        // 端口port更改为整型
        temp.config.port = parseInt(temp.config.port)
      }
      const data = Object.assign({}, this.defaultTemp, temp, { config: JSON.stringify(temp.config) })
      this.encryptDecryptConfig(data, true)
      return data
    },
    testConnect() {
      const postData = this.formatFormData()
      this.testConnectting = true;
      testConnect(postData).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.connectSuccess'),
          type: 'success',
          duration: 2000
        })
        this.testConnectting = false;
      }).catch(e => {
        console.log(e)
        this.testConnectting = false;
      })
    },
    handleUploadFileChange: async function(file, fileList) {
      this.submitting = true
      const fd = new FormData()
      fd.append('file', file.raw)// 传文件
      uploadFile(fd).then(res => {
        this.submitting = false
        this.temp.config.fileName = res.data.fileName
        this.temp.config.fileMd5 = res.data.fileMd5
        // this.temp = Object.assign({}, this.temp)
        this.$refs['upload'] && this.$refs['upload'][0].clearFiles();
      }).catch(res => {
        this.submitting = false
        this.$refs['upload'] && this.$refs['upload'][0].clearFiles()
      })
      return false // 屏蔽了action的默认上传
    },
    rulesSelect(item) {
      if (item.id === 'account' && item.required) {
        return [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' },
          { type: 'email', message: this.$t('pages.adDomain_text9'), trigger: 'blur' },
          { validator: this.accountValidator, trigger: 'blur' }];
      } else if (item.required) {
        return { required: true, message: this.$t('text.cantNull'), trigger: 'blur' };
      } else {
        return '';
      }
    },
    encryptDecryptConfig(row, encrypt) {
      const data = JSON.parse(row.config)
      const field = this.encryptFieldMap[row.sourceType] || []
      if (field.length > 0) {
        for (const attribute of field) {
          if (data[attribute]) {
            if (encrypt) {
              data[attribute] = aesEncode(data[attribute], formatAesKey('tr838408', row.sourceType));
            } else {
              data[attribute] = aesDecode(data[attribute], formatAesKey('tr838408', row.sourceType));
            }
          }
        }
        row.config = JSON.stringify(data)
      }
    },
    // 擦拭掉前置的空格
    wipeReplaceChar($event) {
      const val = $event.target.value
      if (typeof val === 'string') {
        $event.target.value = $event.target.value.replace(/^\s+/g, '')
      }
    },
    // 兼容 v-trim，且防止点击输入框后，因为输入框全替换成 '          ',再没有更改输入框的情况下，输入框会清空的问题
    // 前置空格的输入在wipeReplaceChar处理
    blurChar($event, id) {
      const val = $event.target.value
      if (typeof val === 'string' && val.trim().length > 0) {
        $event.target.value = $event.target.value.replace(/\s+$/g, '')
        this.temp.config[id] = $event.target.value
      }
    },
    copyContent(content) {
      copy(content, r => {
        this.$message({
          message: this.$t('pages.copy_success'),
          type: 'success',
          duration: 1000
        });
      })
    },
    redirectUrlValidator(rule, value, callback) {
      const regex = /^(https?:\/\/)(?:(?:[a-z0-9\-]+\.)*[a-z0-9\-]+\.[a-z]{2,}|(?:\d{1,3}\.){3}\d{1,3})(?::\d+)?$/i;
      if (value && !regex.test(value)) {
        callback(new Error(this.$t('pages.urlValidity')));
      } else {
        callback();
      }
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getSyncSourceByName({ name: value }).then(respond => {
          const data = respond.data
          if (data && data.syncId !== this.temp.syncId) {
            callback(new Error(this.$t('pages.nameValidator')))
          } else {
            callback()
          }
        }).catch(() => { this.submitting = false })
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
