import i18n from '@/lang';

/**
 * 数据源名称字典
 * @param sourceType 数据源类型
 */
export const sourceNames = {
  1: i18n.t('pages.local'),
  3: i18n.t('pages.dataSourceIDM'),
  2: i18n.t('pages.dataSourceAdDomain'),
  4: i18n.t('pages.dataSourceQYWX'),
  5: i18n.t('pages.dataSourceDD'),
  7: 'LDAP',
  8: i18n.t('pages.feiShu'),
  9: i18n.t('pages.dataSourceCRPIDM')
}

/**
 * {xxx}数据源字典
 * @param sourceType 数据源类型
 */
export const syncSources = {
  1: i18n.t('pages.dataSourceName', { type: i18n.t('pages.local') }),
  3: i18n.t('pages.dataSourceName', { type: i18n.t('pages.dataSourceIDM') }),
  2: i18n.t('pages.dataSourceName', { type: i18n.t('pages.dataSourceAdDomain') }),
  4: i18n.t('pages.dataSourceName', { type: i18n.t('pages.dataSourceQYWX') }),
  5: i18n.t('pages.dataSourceName', { type: i18n.t('pages.dataSourceDD') }),
  7: i18n.t('pages.dataSourceName', { type: 'LDAP' }),
  8: i18n.t('pages.dataSourceName', { type: i18n.t('pages.feiShu') }),
  9: i18n.t('pages.dataSourceName', { type: i18n.t('pages.dataSourceCRPIDM') })
}

/**
 * 将数据源字典对象转换成数组<id,label>
 * <p>转换前 { 3: i18n.t('pages.dataSourceName', { type: i18n.t('pages.dataSourceIDM') }) }</p>
 * <p>转换后 [ id: 3, label: this.$t('pages.dataSourceName', { type: this.$t('pages.dataSourceIDM') }) ]</p>
 */
export function syncSourceList(...filter) {
  const arr = [];
  const keys = Object.keys(syncSources);
  keys.map(key => {
    if (filter.includes(key)) {
      return;
    }
    arr.push({
      id: Number(key),
      label: syncSources[key]
    })
  })
  return arr;
}

/**
 * 数据源配置
 * @param sourceType 数据源类型
 * 2024.4.7 caitw: 密钥隐藏不可见
 */
export const sourceItems = {
  // AD域
  2: [
    { id: 'host', label: i18n.t('pages.adDomain_host'), required: true },
    { id: 'account', label: i18n.t('pages.adDomain_account'), required: true },
    { id: 'password', label: i18n.t('pages.adDomain_password'), type: 'password', showPassword: true, required: true },
    { id: 'synCycle', label: i18n.t('table.synCycle'), other: true }
  ],
  // IDM
  3: [
    // IDM系统配置：
    { id: 'loginUrl', label: i18n.t('pages.dataSourceLoginUrl'), required: true },
    { id: 'appUser', label: i18n.t('pages.dataSourceAppUser'), required: true },
    { id: 'appKey', label: i18n.t('pages.dataSourceAppKey'), required: true, type: 'password', showPassword: true },
    { id: 'encodeParam', label: i18n.t('pages.dataSourceEncodeParam'), required: true, type: 'password', showPassword: true },
    // ESB系统配置：
    { id: 'esbUrl', label: i18n.t('pages.dataSourceLoginUrl'), required: true },
    { id: 'esbSystemCode', label: i18n.t('pages.dataSourceEsbSystemCode'), required: true },
    { id: 'esbSystemKey', label: i18n.t('pages.dataSourceEsbSystemKey'), required: true, type: 'password', showPassword: true },
    { id: 'syncGroupApi', label: i18n.t('pages.dataSourceSyncApi', { type: i18n.t('pages.dept') }), required: true },
    { id: 'syncUserApi', label: i18n.t('pages.dataSourceSyncApi', { type: i18n.t('pages.user1') }), required: true }
  ],
  // 企业微信
  4: [
    { id: 'corpId', label: i18n.t('pages.dataSourceCorpId'), required: true },
    { id: 'appId', label: i18n.t('pages.dataSourceAppId'), required: true },
    { id: 'redirectUrl', label: i18n.t('pages.dataSourceRedirectUrl'), required: true, other: true },
    { id: 'secretKey', label: i18n.t('pages.dataSourceSecretKey'), required: true, type: 'password', showPassword: true },
    { id: 'fileName', label: i18n.t('pages.dataSourceDomainNameAuthentication'), other: true }
  ],
  // 钉钉
  5: [
    { id: 'corpId', label: i18n.t('pages.dataSourceAppId'), required: true },
    { id: 'appId', label: i18n.t('pages.dataSourceAppIdKey'), required: true },
    { id: 'redirectUrl', label: i18n.t('pages.dataSourceRedirectUrl'), required: true, other: true },
    { id: 'secretKey', label: i18n.t('pages.dataSourceAppSecretKey'), required: true, type: 'password', showPassword: true }
  ],
  // LDAP
  7: [
    { id: 'ip', label: i18n.t('pages.serverIp'), required: true },
    { id: 'port', label: i18n.t('pages.serverPort'), placeholder: i18n.t('pages.defaultPortInfo', { info: 389 }), required: true },
    { id: 'userName', label: i18n.t('pages.sysUserAccount'), placeholder: i18n.t('pages.exampleInfo', { info: 'cn=Manager,dc=test,dc=com' }), required: true },
    { id: 'password', label: i18n.t('pages.sysUserPassword'), type: 'password', showPassword: true, required: true },
    { id: 'otherConfig', label: i18n.t('pages.otherConfig'), other: true }
  ],
  // 飞书
  8: [
    { id: 'appId', label: i18n.t('pages.dataSourceAppId'), required: true },
    { id: 'secretKey', label: i18n.t('pages.dataSourceAppSecretKey'), required: true, type: 'password', showPassword: true },
    { id: 'redirectUrl', label: i18n.t('pages.dataSourceRedirectUrl'), required: true, other: true }
  ],
  // 华润医药
  9: [
    { id: 'clientId', label: i18n.t('pages.dataSourceAppId'), required: true },
    { id: 'clientSecret', label: i18n.t('pages.dataSourceAppSecretKey'), required: true, type: 'password', showPassword: true },
    { id: 'srcHost', label: i18n.t('table.dataSource'), placeholder: i18n.t('pages.exampleInfo', { info: 'user-center.dev.crpharm.com' }), required: true },
    { id: 'buCode', label: i18n.t('pages.CRPIDMBuCode'), placeholder: i18n.t('pages.exampleInfo', { info: 'CR008' }), required: true },
    { id: 'apiAddress', label: i18n.t('pages.CRPIDMApiAddress'), placeholder: i18n.t('pages.exampleInfo', { info: 'https://gateway-out.dev.crpharm.com' }), required: true }
  ]
}

/**
 * 同步字段属性配置
 * <p>（各个数据源接口的字段）</>
 * @param sourceType 数据源类型
 */
export const sourceSyncFieldItems = {
  // IDM
  3: {
    account: [
      { id: 'alias', label: 'alias', msg: i18n.t('pages.idmAlias') },
      { id: 'userId', label: 'userId', msg: i18n.t('pages.idmUserId') },
      { id: 'idcardnumber', label: 'idcardnumber', msg: i18n.t('pages.idmIdcardnumber') },
      { id: 'personid', label: 'personid', msg: i18n.t('pages.idmPersonid') },
      { id: 'usernumber', label: 'usernumber', msg: i18n.t('pages.idmUsernumber') },
      { id: 'userCn', label: 'userCn', msg: i18n.t('pages.idmUserCn') },
      { id: 'securemobile', label: 'securemobile', msg: i18n.t('pages.idmSecuremobile') },
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.idmMobile') },
      { id: 'mail', label: 'mail', msg: i18n.t('pages.idmMail') }
    ],
    name: [
      { id: 'userCn', label: 'userCn', msg: i18n.t('pages.idmUserCn') },
      { id: 'alias', label: 'alias', msg: i18n.t('pages.idmAlias') }
    ],
    phone: [
      { id: 'securemobile', label: 'securemobile', msg: i18n.t('pages.idmSecuremobile') },
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.idmMobile') }
    ],
    email: [
      { id: 'mail', label: 'mail', msg: i18n.t('pages.idmMail') }
    ]
  },
  // 企业微信
  4: {
    account: [
      { id: 'userid', label: 'userid', msg: i18n.t('pages.qywxUserid') },
      { id: 'name', label: 'name', msg: i18n.t('pages.qywxName') },
      { id: 'alias', label: 'alias', msg: i18n.t('pages.qywxAlias') },
      { id: 'email', label: 'email', msg: i18n.t('pages.qywxEmail') },
      { id: 'biz_mail', label: 'biz_mail', msg: i18n.t('pages.qywxBizMail') },
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.qywxMobile') }
    ],
    name: [
      { id: 'name', label: 'name', msg: i18n.t('pages.qywxName') },
      { id: 'alias', label: 'alias', msg: i18n.t('pages.qywxAlias') }
    ],
    phone: [
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.qywxMobile') }
    ],
    email: [
      { id: 'biz_mail', label: 'biz_mail', msg: i18n.t('pages.qywxBizMail') },
      { id: 'email', label: 'email', msg: i18n.t('pages.qywxEmail') }
    ]
  },
  // 钉钉
  5: {
    account: [
      { id: 'userid', label: 'userid', msg: i18n.t('pages.ddUserid') },
      { id: 'unionid', label: 'unionid', msg: i18n.t('pages.ddUnionid') },
      { id: 'job_number', label: 'job_number', msg: i18n.t('pages.ddJobNumber') },
      { id: 'name', label: 'name', msg: i18n.t('pages.ddName') },
      { id: 'email', label: 'email', msg: i18n.t('pages.ddEmail') },
      { id: 'org_email', label: 'org_email', msg: i18n.t('pages.ddOrgEmail') },
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.ddMobile') }
    ],
    name: [
      { id: 'name', label: 'name', msg: i18n.t('pages.ddName') }
    ],
    phone: [
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.ddMobile') }
    ],
    email: [
      { id: 'email', label: 'email', msg: i18n.t('pages.ddEmail') },
      { id: 'org_email', label: 'org_email', msg: i18n.t('pages.ddOrgEmail') }
    ]
  },
  // LDAP
  7: {
    account: [
      { id: 'entryUUID', label: 'entryUUID', msg: i18n.t('pages.ldapEntryuuid') },
      { id: 'uid', label: 'uid', msg: i18n.t('pages.ldapUid') },
      { id: 'cn', label: 'cn', msg: i18n.t('pages.ldapCn') },
      { id: 'telephoneNumber', label: 'telephoneNumber', msg: i18n.t('pages.ldapTelephoneNumber') },
      { id: 'mail', label: 'mail', msg: i18n.t('pages.ldapMail') }
    ],
    name: [
      { id: 'cn', label: 'cn', msg: i18n.t('pages.ldapCn') }
    ],
    phone: [
      { id: 'telephoneNumber', label: 'telephoneNumber', msg: i18n.t('pages.ldapTelephoneNumber') }
    ],
    email: [
      { id: 'mail', label: 'mail', msg: i18n.t('pages.ldapMail') }
    ]
  },
  // 飞书
  8: {
    account: [
      { id: 'user_id', label: 'user_id', msg: i18n.t('pages.feiShuUserId') },
      { id: 'open_id', label: 'open_id', msg: i18n.t('pages.feiShuOpenId') },
      { id: 'union_id', label: 'union_id', msg: i18n.t('pages.feiShuUnionId') },
      { id: 'employee_no', label: 'employee_no', msg: i18n.t('pages.ddJobNumber') },
      { id: 'name', label: 'name', msg: i18n.t('pages.ddName') },
      { id: 'email', label: 'email', msg: i18n.t('pages.ddEmail') },
      { id: 'enterprise_email', label: 'enterprise_email', msg: i18n.t('pages.feiShuEnterpriseEmail') },
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.ddMobile') }
    ],
    name: [
      { id: 'name', label: 'name', msg: i18n.t('pages.ddName') },
      { id: 'en_name', label: 'en_name', msg: i18n.t('pages.enName') },
      { id: 'nickname', label: 'nickname', msg: i18n.t('pages.byname') }
    ],
    phone: [
      { id: 'mobile', label: 'mobile', msg: i18n.t('pages.ddMobile') }
    ],
    email: [
      { id: 'email', label: 'email', msg: i18n.t('pages.ddEmail') },
      { id: 'enterprise_email', label: 'enterprise_email', msg: i18n.t('pages.feiShuEnterpriseEmail') }
    ]
  },
  // 华润医药
  9: {
    account: [
      { id: 'employeeCode', label: 'employeeCode', msg: i18n.t('pages.CRPIDMEmployeeCode') },
      { id: 'userName', label: 'userName', msg: i18n.t('pages.CRPIDMUserName') },
      { id: 'email', label: 'email', msg: i18n.t('pages.ddEmail') },
      { id: 'mobil', label: 'mobil', msg: i18n.t('pages.ddMobile') }
    ],
    name: [
      { id: 'name', label: 'name', msg: i18n.t('pages.ddName') }
    ],
    phone: [
      { id: 'mobil', label: 'mobil', msg: i18n.t('pages.ddMobile') }
    ],
    email: [
      { id: 'email', label: 'email', msg: i18n.t('pages.ddEmail') }
    ]
  }
}
