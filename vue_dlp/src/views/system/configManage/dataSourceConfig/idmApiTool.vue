<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <div>
          {{ $t('table.type') }}：
          <el-select v-model="query.type" style="width: 228px;">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" clearable></el-option>
          </el-select>
          <span v-if="1 === query.type">
            {{ $t('table.deptName') }}：
            <el-input v-model="query.orgShortName" clearable :placeholder="$t('pages.validateMsg_enterName')" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchOrgId') }}：
            <el-input v-model="query.orgId" clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.idmSearchOrgId')})" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchParentOrgId') }}：
            <el-input v-model="query.parentOrgId" clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.idmSearchParentOrgId')})" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchOrgState') }}：
            <el-select v-model="query.orgState" clearable style="width: 200px;">
              <el-option v-for="option in statusOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
            </el-select>
            <br/>
            {{ $t('pages.idmSearchFilterExpression') }}：
            <el-tooltip effect="light" placement="bottom-start">
              <span slot="content">
                {{ $t('pages.idmSearchFilterExpressionTip', { info: '($(orgName=测试部)(orgId=100001))'}) }}
              </span>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-input v-model="query.filterExpression" clearable :placeholder="$t('pages.exampleInfo', { info: '(orgName=测试部)'})" style="width: 400px;" @keyup.enter.native="handleSearch" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">
              {{ $t('table.search') }}
            </el-button>
          </span>
          <span v-else-if="2 === query.type">
            {{ $t('table.userName1') }}：
            <el-input v-model="query.userCn" clearable :placeholder="$t('pages.validateMsg_enterName')" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchOrgId') }}：
            <el-input v-model="query.userOrgId" clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.idmSearchOrgId')})" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchAlias') }}：
            <el-input v-model="query.alias" clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.idmSearchAlias')})" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchSecuremobile') }}：
            <el-input v-model="query.securemobile" clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.idmSearchSecuremobile')})" style="width: 200px;" @keyup.enter.native="handleSearch" />
            <br/>
            {{ $t('pages.idmSearchMail') }}：
            <el-input v-model="query.mail" clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.idmSearchMail')})" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchUserStatus') }}：
            <el-select v-model="query.userstatus" clearable style="width: 200px;">
              <el-option v-for="option in statusOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
            </el-select>
            {{ $t('pages.idmSearchHrUserStatus') }}：
            <el-input v-model="query.hruserstatus" clearable :placeholder="$t('pages.idmSearchHrUserStatusTip')" style="width: 200px;" @keyup.enter.native="handleSearch" />
            {{ $t('pages.idmSearchFilterExpression') }}：
            <el-tooltip effect="light" placement="bottom-start">
              <span slot="content">
                {{ $t('pages.idmSearchFilterExpressionTip', { info: '($(userOrgId=测试部)(securemobile=13900000000))'}) }}
              </span>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-input v-model="query.filterExpression" clearable :placeholder="$t('pages.exampleInfo', { info: '(userCn=张三)'})" style="width: 400px;" @keyup.enter.native="handleSearch" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">
              {{ $t('table.search') }}
            </el-button>
          </span>

        </div>
      </div>
      <grid-table
        ref="gridTableList"
        :autoload="false"
        :show-pager="true"
        :multi-select="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="i18nConcatText('JSON', 'details')"
      :visible.sync="dialogFormVisible"
      width="900px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item v-for="(key, index) in Object.keys(temp)" :key="index" extra="aaaa" :label="key" >
            <span :title="temp[key]">{{ temp[key] }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getIdmData } from '@/api/system/configManage/syncConfig'

export default {
  name: 'IdmApiTool',
  props: {
  },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        type: 1
      },
      temp: {},
      queryDatas: [],
      dialogFormVisible: false,
      loading: false,
      colModel: [],
      colModel1: [
        { prop: 'orgShortName', label: 'deptName', width: '180', fixed: 'left', fixedWidth: '180' },
        { prop: 'orgId', label: this.$t('pages.idmSearchOrgId'), width: '80' },
        { prop: 'parentOrgId', label: this.$t('pages.idmSearchParentOrgId'), width: '80' },
        { prop: 'orgnamefullpath', label: this.$t('pages.idmSearchOrgNameFullPath'), width: '180' },
        { prop: 'orgnofullpath', label: this.$t('pages.idmSearchOrgIdFullPath'), width: '180' },
        { prop: 'orgstatus', label: this.$t('pages.idmSearchOrgState'), width: '50', formatter: this.isEnableFormatter },
        { prop: 'createtime', label: 'createTime', width: '100' },
        { prop: 'modifytime', label: 'modifyTime', width: '100' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'detail', click: this.handleDetail }
          ]
        }
      ],
      colModel2: [
        { prop: 'userCn', label: 'userName1', width: '120', fixed: 'left', fixedWidth: '120' },
        { prop: 'userId', label: this.$t('pages.idmSearchUserId'), width: '100' },
        { prop: 'alias', label: this.$t('pages.idmSearchAlias'), width: '100' },
        { prop: 'securemobile', label: this.$t('pages.idmSearchSecuremobile'), width: '100' },
        { prop: 'mail', label: this.$t('pages.idmSearchMail'), width: '100' },
        { prop: 'userOrgId', label: this.$t('pages.idmSearchOrgId'), width: '100' },
        { prop: 'userOrgName', label: 'deptName', width: '100' },
        { prop: 'userstatus', label: this.$t('pages.idmSearchUserStatus'), width: '50', formatter: this.isEnableFormatter },
        { prop: 'hruserstatus', label: this.$t('pages.idmSearchHrUserStatus'), width: '100' },
        { prop: 'createtime', label: 'createTime', width: '100' },
        { prop: 'modifytime', label: 'modifyTime', width: '100' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'detail', click: this.handleDetail }
          ]
        }
      ],
      typeOptions: [
        { label: this.$t('table.dept'), value: 1 },
        { label: this.$t('table.user'), value: 2 }
      ],
      statusOptions: [
        { label: this.$t('text.enable'), value: '1' },
        { label: this.$t('text.disable'), value: '0' }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTableList']
    }
  },
  created() {
    this.colModel = this.colModel1;
  },
  methods: {
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option);
      this.formatModel();
      return getIdmData(searchQuery)
    },
    handleSearch() {
      this.formatModel();
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    isEnableFormatter(row, data) {
      return '1' === data ? this.$t('text.enable') : this.$t('text.disable');
    },
    handleDetail(row) {
      this.dialogFormVisible = true;
      this.temp = row;
    },
    formatModel() {
      const data = this.query;
      if (1 === data.type) {
        this.colModel = this.colModel1;
      } else if (2 === data.type) {
        this.colModel = this.colModel2;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-descriptions-item__label {
    min-width: 150px;
  }
  >>>.el-descriptions-item__content {
    min-width: 100px;
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
