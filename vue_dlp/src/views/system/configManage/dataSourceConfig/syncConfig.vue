<template>
  <Form ref="dataForm" class="global-config" :model="temp" :rules="rules" label-width="200px" :extra-width="{en: 60}" label-position="right">
    <div class="block">
      <label class="class-label">{{ $t('pages.dataSourceLoginParamsConfig') }}：</label>
      <FormItem :label=" $t('pages.dataSourceTerminalScanLogin')">
        <el-checkbox-group v-model="qrAble" @change="qrAbleChange">
          <el-checkbox :label="4">{{ $t('pages.syncQrAbleEnterpriseWechat') }}</el-checkbox>
          <i v-if="showErrorMsg4" class="el-icon-info error-msg-li" @click="changeToSource(4)" v-html="getErrorMsg(4)"/>
          <br/>
          <el-checkbox :label="5">{{ $t('pages.syncQrAbleDingTalk') }}</el-checkbox>
          <i v-if="showErrorMsg5" class="el-icon-info error-msg-li" @click="changeToSource(5)" v-html="getErrorMsg(5)"/>
          <br/>
          <el-checkbox :label="8">{{ $t('pages.syncQrAbleFeiShu') }}</el-checkbox>
          <i v-if="showErrorMsg8" class="el-icon-info error-msg-li" @click="changeToSource(8)" v-html="getErrorMsg(8)"/>
          <span style="font-size: 12px;color:#afafaf;line-height: 0">
            <i18n path="pages.dataSourceTerminalScanLoginDescription">
              <template slot="space">&nbsp;</template>
              <br slot="br"/>
            </i18n>
          </span>
        </el-checkbox-group>
      </FormItem>
      <FormItem :label="$t('pages.dataSourceTerminalLoginDefaultMode')">
        <el-select v-model="temp.defaultLoginType" :placeholder="$t('pages.dataSourceLoginType')" style="width: 250px;" @change="showErrorMsg">
          <el-option v-for="item in loginTypes" :key="item.id" :label="item.label" :value="item.id"/>
        </el-select>
        <i v-if="defaultLoginErrorMsg" class="el-icon-info error-msg-li">{{ defaultLoginErrorMsg }}</i>
      </FormItem>
      <FormItem v-if="!(hiddenSource.indexOf(3) > -1 && hiddenSource.indexOf(2) > -1)" :label="$t('pages.dataSourceLoginAuthenticationSwitch')">
        <el-checkbox-group v-model="loginAble" @change="showErrorMsg">
          <!--          <el-checkbox v-if="hiddenSource.indexOf(3) < 0" :label="3">{{ $t('pages.dataSourceOpenTerminalLoginAndAuthentication',{ type: $t('pages.dataSourceIDMSystemUser')}) }}</el-checkbox>-->
          <!--          <i v-if="showErrorMsg3 && hiddenSource.indexOf(3) < 0" class="el-icon-info error-msg-li" @click="changeToSource(3)" v-html="getErrorMsg(3)"/>-->
          <!--          <br v-if="hiddenSource.indexOf(3) < 0"/>-->
          <el-checkbox :label="2">{{ $t('pages.dataSourceOpenTerminalLoginAndAuthentication',{ type: $t('pages.dataSourceAdDomainAcount')}) }}</el-checkbox>
          <i v-if="showErrorMsg2" class="el-icon-info error-msg-li" @click="changeToSource(2)" v-html="getErrorMsg(2)"/>
        </el-checkbox-group>
      </FormItem>
      <FormItem :label="$t('pages.otherConfig')">
        <el-checkbox-group v-model="temp.userCancelAutoLogin">
          <el-checkbox :label="2" :true-label="1" :false-label="0">{{ $t('pages.userCancelAutoLogin') }}</el-checkbox>
        </el-checkbox-group>
        <template v-if="!isArmMode">
          <el-checkbox v-model="temp.extApiPort.enableExtPort" @change="changeEnableExtPortFuc">{{ $t('pages.syncExtApiPortTips') }}</el-checkbox>
          <div v-if="temp.extApiPort.enableExtPort" style="margin-left: 18px">
            {{ $t('pages.syncExtApiHttpPort') }}<el-input-number ref="inputField" v-model="temp.extApiPort.httpPort" style="width:120px;margin-left: 10px" :step="1" step-strictly :min="1" :max="65535" :controls="false" @blur="inputBlur(1)"/>
            <span style="margin-left: 18px;color:red">{{ httpPortErrorMsg }}</span>
            <br/>
            {{ $t('pages.syncExtApiHttpsPort') }}<el-input-number v-model="temp.extApiPort.httpsPort" style="width:120px" :step="1" step-strictly :min="1" :max="65535" :controls="false" @blur="inputBlur(2)"/>
            <span style="margin-left: 18px;color:red">{{ httpsPortErrorMsg }}</span>
          </div>
        </template>
      </FormItem>
      <label class="class-label">{{ $t('pages.dataSourceSyncParamConfig') }}：</label>
      <FormItem :label="$t('pages.dataSourceSync')">
        <el-select v-model="temp.sourceType" clearable :placeholder="$t('pages.dataSourceSyncSelect')" style="width: 250px" @change="syncSourceChange">
          <el-option v-for="item in getSyncSources()" :key="item.id" :label="item.label" :value="item.id"/>
        </el-select>
        <el-button type="primary" size="small" :disabled="disabledSyncNow" :loading="syncDoing" @click="doSync">
          {{ $t('pages.handleSync') }}
        </el-button>
        <i v-if="showSyncErrorMsg" class="el-icon-info error-msg-li" @click="changeToSource(temp.sourceType)" v-html="getErrorMsg(temp.sourceType)"/>
        <i v-if="!showSyncErrorMsg && temp.sourceType === 3 && !syncAllVisible && !disabledSyncNow" class="el-icon-setting" :title="$t('pages.fullSynchronizationConfiguration')" style="color: #349edb;cursor: pointer;" @click="showSyncAll"/>
        <el-checkbox v-if="syncAllVisible && !disabledSyncNow" v-model="isSyncAll" :true-label="1" :false-label="0">{{ $t('pages.dataSourceFullSync') }}</el-checkbox>
      </FormItem>
      <FormItem v-show="[3].includes(temp.sourceType) /*&& syncAllVisible && !disabledSyncNow*/" :label="$t('pages.enableTimeFullSynchronization')">
        <el-switch v-model="temp.syncTimeAble" :active-value="1" :inactive-value="0" style="margin-left: 10px" @change="timeSyncChange"></el-switch>
      </FormItem>
      <FormItem v-show="[3].includes(temp.sourceType) /*&& syncAllVisible && !disabledSyncNow */&& 1 === temp.syncTimeAble" :label="$t('pages.timingFullSynchronizationConfiguration')">
        <i18n path="pages.fullSyncSelectedTimeInfo">
          <el-select slot="day" v-model="temp.selectedDay" :style="selectedDayStyle" size="mini" filterable multiple clearable style="width: 250px" @change="rateChange">
            <el-option v-for="item in dayOptions" :key="item.id" :label="item.label" :value="item.id"/>
          </el-select>
          <el-time-picker
            slot="time"
            v-model="temp.selectedTime"
            size="mini"
            :placeholder="$t('pages.manageLibrary_encryptionKey_text2')"
            range-separator=":"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 130px"
            :clearable="false"
          />
        </i18n>
      </FormItem>
      <FormItem v-show="![2].includes(temp.sourceType)" :label="$t('pages.dataSourceSyncRange')">
        <el-checkbox v-model="temp.syncGroup" :true-label="1" :false-label="0">{{ $t('pages.dataSourceSyncDepartment') }}</el-checkbox>
        <el-checkbox v-model="temp.syncUser" :true-label="1" :false-label="0">{{ $t('pages.dataSourceSyncUser') }}</el-checkbox>
      </FormItem>
      <FormItem v-show="[2].includes(temp.sourceType)" :label="$t('pages.dataSourceSyncRange')">
        <el-checkbox v-model="temp.syncGroupUser" :true-label="1" :false-label="0">{{ $t('pages.dataSourceSyncDeptUser') }}</el-checkbox>
      </FormItem>
      <FormItem v-show="![2].includes(temp.sourceType)" :label="$t('pages.synCycle')">
        <el-input-number v-model="temp.syncCycle" :min="5" :max="99999" :disabled="!temp.syncGroup && !temp.syncUser" style="width: 250px"/>
        {{ $t('pages.adDomain_text4') }}
      </FormItem>
      <FormItem v-show="![2].includes(temp.sourceType)" :label="$t('pages.synchronousAlarmSettings')">
        <el-button icon="el-icon-setting" type="primary" size="small" @click="handleSetAlarm">
          {{ $t('pages.syncAlarmConfig') }}
        </el-button>
      </FormItem>
      <FormItem v-show="false" :label="$t('pages.repeatNameDealType')">
        <el-tooltip slot="tooltip" effect="dark" placement="bottom">
          <div slot="content">
            <i18n path="pages.repeatNameDealTypeContent">
              <template slot="tip">{{ $t('pages.deptName')+ '、'+ $t('pages.dataSourceUserAccount') }}</template>
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <el-radio-group v-model="temp.conflict" :disabled="!temp.syncGroup && !temp.syncUser">
          <el-radio :label="0">{{ $t('pages.dataSourceConflictType1') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.dataSourceConflictType2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <sync-filter ref="syncFilter"/>
      <sync-alarm ref="syncAlarm"/>
      <switch-data-source ref="switchDataSource" @switchEndFuc="switchEndFuc"/>
    </div>
    <el-button type="primary" size="mini" style="width:80px; position: absolute; bottom: 30px; left: 500px;" :disabled="disabled" :loading="submitting" @click="submit">{{ $t('button.save') }}</el-button>
  </Form>
</template>

<script>
import {
  listSources, getSyncConfig, updateSyncConfig, doSyncNow, getCheckSwitchSource, checkPortExists
} from '@/api/system/configManage/syncConfig'
import { list as listAdDomains } from '@/api/system/configManage/adDomain'
import SyncFilter from './syncFilter';
import SyncAlarm from './syncAlarm';
import SwitchDataSource from './switchDataSource';
import { getArmMode } from '@/api/system/register/reg';
import { syncSourceList, sourceNames } from './sourceItems'

export default {
  name: 'SyncConfig',
  components: { SyncFilter, SwitchDataSource, SyncAlarm },
  props: {
    showInCreate: { // 是否创建时就显示
      type: Boolean,
      default: false
    },
    hiddenSource: { // 隐藏的数据源
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      submitting: false,
      sourceMap: {},
      syncDoing: false,
      mapperConfigAble: false,
      isSyncAll: 0,
      syncAllVisible: false,
      showErrorMsg2: false,
      showErrorMsg3: false,
      showErrorMsg4: false,
      showErrorMsg5: false,
      showErrorMsg8: false,
      defaultLoginErrorMsg: undefined,
      showSyncErrorMsg: false,
      disabledSyncNow: false,
      oldSyncSource: 1,
      loginTypes: [
        { id: 1, label: this.$t('pages.dataSourceLoginType1') },
        { id: 2, label: this.$t('pages.dataSourceLoginType2'), sourceType: 4 },
        { id: 3, label: this.$t('pages.dataSourceLoginType3'), sourceType: 5 },
        { id: 4, label: this.$t('pages.dataSourceLoginType4'), sourceType: 8 }
      ],
      dayOptions: [
        { id: 0, label: this.$t('pages.everyDay') },
        { id: 1, label: this.$t('pages.monday') },
        { id: 2, label: this.$t('pages.tuesday') },
        { id: 3, label: this.$t('pages.wednesday') },
        { id: 4, label: this.$t('pages.Thursday') },
        { id: 5, label: this.$t('pages.friday') },
        { id: 6, label: this.$t('pages.saturday') },
        { id: 7, label: this.$t('pages.sunday') }
      ],
      loginAble: [],
      qrAble: [],
      temp: { },
      defaultTemp: {
        defaultLoginType: 1,
        syncId: undefined,
        sourceType: undefined,
        syncGroup: 1,
        syncUser: 1,
        syncCycle: 30,
        conflict: 0,
        loginAble: 0,
        qrAble: 0,
        // 定时全量同步开关
        syncTimeAble: 0,
        // ad域同步数据范围两个整合一起(同步组织架构+同步用户)
        syncGroupUser: 1,
        // 操作员取消自动登录开关
        userCancelAutoLogin: 0,
        // 开启独立端口
        extApiPort: {
          enableExtPort: false,
          httpPort: undefined,
          httpsPort: undefined
        }
      },
      rules: {
        httpPort: [
          { required: true, message: this.$t('pages.validateMsg_port'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ]
      },
      httpPortErrorMsg: '',
      httpsPortErrorMsg: '',
      isArmMode: false
    }
  },
  computed: {
    selectedDayStyle() {
      const selectedDay = this.temp.selectedDay ? this.temp.selectedDay : [];
      let toSelectWidth = 120;
      for (let i = 0, len = selectedDay.length; i < len; i++) {
        if (i > 0) {
          toSelectWidth += 70
        }
      }
      return { width: toSelectWidth + 'px' }
    },
    disabled() {
      // 扫码登录端口判断
      const extPortErrorMsgFlag = this.temp.extApiPort.enableExtPort ? !!(this.httpPortErrorMsg || this.httpsPortErrorMsg) : false;
      return !!(this.showErrorMsg2 || this.showErrorMsg4 || this.showErrorMsg5 || this.showErrorMsg8 || this.defaultLoginErrorMsg || extPortErrorMsgFlag || this.showSyncErrorMsg);
    }
  },
  created() {
    if (this.showInCreate) {
      this.show()
    } else {
      this.resetTemp()
    }
    this.initArmMode();
  },
  methods: {
    show() {
      this.resetTemp()
      this.getSyncConfig()
      this.listDataSources()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    submit() {
      this.submitting = true;
      this.updateConfig()
    },
    resetTemp() {
      this.syncAllVisible = false
      this.temp = Object.assign({}, this.defaultTemp)
      this.loginAble = []
      this.qrAble = []
    },
    getSyncSources() {
      const sourceList = syncSourceList('1');
      if (!this.hiddenSource || !this.hiddenSource.length) {
        return sourceList
      }
      for (let i = 0; i < sourceList.length; i++) {
        const item = sourceList[i]
        if (this.hiddenSource.indexOf(item.id) > -1) {
          sourceList.splice(i, 1)
        }
      }
      return sourceList
    },
    qrAbleChange() {
      this.showErrorMsg()
    },
    getErrorMsg(sourceType) {
      return this.$t('pages.dataSourceErrorMsg1', { type: sourceNames[sourceType] });
    },
    changeToSource(sourceType) {
      this.$emit('toSource', sourceType)
    },
    getQrErrorMsg(sourceType) {
      return this.$t('pages.dataSourceErrorMsg2', { type: sourceNames[sourceType] });
    },
    setErrorMsgAble(checkedVal, sourceType) {
      this['showErrorMsg' + sourceType] = !this.sourceMap[sourceType] && checkedVal && checkedVal.indexOf(sourceType) > -1
    },
    showErrorMsg() {
      this.setErrorMsgAble(this.loginAble, 2)
      this.setErrorMsgAble(this.loginAble, 3)
      this.setErrorMsgAble(this.loginAble, 7)
      this.setErrorMsgAble(this.loginAble, 8)
      this.setErrorMsgAble(this.qrAble, 4)
      this.setErrorMsgAble(this.qrAble, 5)
      this.setErrorMsgAble(this.qrAble, 8)
      this.defaultLoginErrorMsg = null
      this.loginTypes.forEach(item => {
        if (item.sourceType && this.temp.defaultLoginType === item.id && this.qrAble.indexOf(item.sourceType) < 0) {
          this.defaultLoginErrorMsg = this.getQrErrorMsg(item.sourceType)
        }
      })
      // const extPortErrorMsgFlag = this.temp.extApiPort.enableExtPort ? !!(this.httpPortErrorMsg || this.httpsPortErrorMsg) : false;
      // this.disabled = !!(this.showErrorMsg2 || this.showErrorMsg4 || this.showErrorMsg5 || this.defaultLoginErrorMsg || extPortErrorMsgFlag);
    },
    getSyncConfig() {
      getSyncConfig().then(res => {
        if (res.data) {
          this.temp = Object.assign({}, this.defaultTemp, res.data, { syncCycle: res.data.syncCycle / 60 })
          if (this.temp.sourceType < 1 || this.hiddenSource.indexOf(this.temp.sourceType) > -1) {
            this.temp.sourceType = null
          }
          this.oldSyncSource = this.temp.sourceType
          this.numToArr(this.temp.loginAble).forEach(val => {
            this.loginAble.push(Math.log(val) / Math.log(2))
          })
          this.numToArr(this.temp.qrAble).forEach(val => {
            this.qrAble.push(Math.log(val) / Math.log(2))
          })
          // AD域特殊，处理一下syncGroupUser（同步组织架构以及人员）
          if (this.temp.sourceType === 2) {
            if (this.temp.syncGroup && this.temp.syncUser) {
              this.temp.syncGroupUser = 1;
            } else {
              this.temp.syncGroupUser = 0;
            }
          }
        }
      })
    },
    async listDataSources() {
      this.sourceMap = {}
      await listSources().then(resp => {
        resp.data.forEach(source => {
          const config = source.config ? JSON.parse(source.config) : {}
          if (source.sourceType === 3) { // IDM
            if (!config.loginUrl && !config.esbUrl) {
              // 如果登录和同步的url未配置，则认为未配置数据源
              return
            }
          } else if (source.sourceType === 4 || source.sourceType === 5) { // 企业微信
            if (!config.corpId) {
              // 如果企业微信和钉钉的企业ID未配置，则认为未配置数据源
              return
            }
          } else if (source.sourceType === 7) { // LDAP
            if (!config.userName) {
              return
            }
          }
          if (!this.sourceMap[source.sourceType]) {
            this.sourceMap[source.sourceType] = []
          }
          this.sourceMap[source.sourceType].push(source);
        })
      })
      await listAdDomains().then(resp => {
        if (resp.data.length > 0) {
          this.sourceMap[2] = resp.data
        }
      })
      this.showErrorMsg()
      this.syncSourceChange()
    },
    syncSourceChange() {
      this.syncAllVisible = false
      this.showSyncErrorMsg = this.temp.sourceType && !this.sourceMap[this.temp.sourceType]
      this.disabledSyncNow = this.showSyncErrorMsg || !this.temp.sourceType || this.temp.sourceType !== this.oldSyncSource
    },
    /**
     * 切换数据源完成后执行的方法
     */
    switchEndFuc() {
      this.disabledSyncNow = false;
    },
    doSync() { // 立即同步
      this.syncDoing = true
      doSyncNow(this.temp.sourceType, this.isSyncAll).then(resp => {
        if (resp.data && 'error' === resp.data.status) {
          this.$notify({ type: 'error', title: this.$t('text.error'), message: resp.data.message, duration: 2000 })
        } else {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.dataSourceSyncMsg1'),
            type: 'success',
            duration: 2000
          })
        }
        this.syncDoing = false
      }).catch(e => {
        this.syncDoing = false
      })
    },
    showSyncAll() {
      this.syncAllVisible = !this.syncAllVisible
      if (!this.syncAllVisible) {
        // 如果不显示，则默认增量更新
        this.isSyncAll = 0
      }
    },
    formatFormData() {
      // 格式化同步数据源
      this.temp.loginAble = 0
      this.loginAble.forEach(st => {
        this.temp.loginAble += Math.pow(2, st)
      })
      this.temp.qrAble = 0
      this.qrAble.forEach(st => {
        this.temp.qrAble += Math.pow(2, st)
      })
      // AD域特殊，处理一下syncGroupUser（同步组织架构以及人员）
      if (this.temp.sourceType === 2) {
        this.temp.syncGroup = this.temp.syncGroupUser;
        this.temp.syncUser = this.temp.syncGroupUser;
      }
      return Object.assign({}, this.defaultTemp, this.temp, {
        config: JSON.stringify(this.temp.config),
        syncCycle: this.temp.syncCycle * 60
      })
    },
    validateFormData() {
      if (3 === this.temp.sourceType) {
        if (1 === this.temp.syncTimeAble) {
          if (this.temp.selectedDay === undefined || this.temp.selectedDay.length === 0 || !this.temp.selectedTime) {
            this.$notify({ title: this.$t('text.error'), message: this.$t('text.cantNullInfo', { info: this.$t('pages.timingFullSynchronizationConfiguration') }), type: 'error', duration: 2000 })
            return false;
          }
        }
      }
      return true
    },
    updateConfig() {
      // 此处判断是否是切换数据源
      // 思路：
      // 1.若本来已经有同步数据源了，查询同步库看是否已经有不同于当前保存的数据源，然后提供警告
      // 2.警告后，若还是需切换的，则弹出页面（将下载的数据与本地user库的数据列出来比对按照account，并保存在临时表sync_group_info_temp、sync_user_info_temp）
      // 3.比对结果展示页面（列出处理措施，例如1.覆盖本地数据 2.同名数据）
      // 4.处理完成后覆盖同步表
      this.switchDataSource();
    },
    saveConfig() {
      if (this.validateFormData()) {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            const postData = this.formatFormData()
            if (3 !== postData.sourceType) {
              postData.syncTimeAble = 0;
            }
            updateSyncConfig(postData).then(res => {
              this.oldSyncSource = postData.sourceType
              this.syncSourceChange()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
              this.submitting = false;
            }).catch(e => {
              this.submitting = false;
            })
          } else {
            this.submitting = false;
          }
        })
      } else {
        this.submitting = false;
      }
    },
    handleSetDeptFilter() {
      this.$refs['syncFilter'].show(this.temp)
    },
    handleSetAlarm() {
      this.$refs['syncAlarm'].show(this.temp.sourceType)
    },
    switchDataSource() {
      const source = this.temp.sourceType;
      // ad域暂不处理
      if (2 === source) {
        this.saveConfig();
        return;
      }
      // 如果同步勾选项为空，则可以保存
      if (!source) {
        this.saveConfig();
        return
      }
      getCheckSwitchSource(source).then(res => {
        const data = res.data;
        if (data) {
          this.showSwitchDataSource();
          this.submitting = false;
        } else {
          this.saveConfig();
        }
      }).catch(e => {
        this.submitting = false;
      })
    },
    // 显示切换数据源组件
    showSwitchDataSource() {
      this.$refs['switchDataSource'].show(this.temp.sourceType);
    },
    timeSyncChange(value) {
      if (!value) {
        // this.$nextTick(() => {
        //   this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
        // })
      }
    },
    // 处理“每天”
    rateChange(data) {
      if (data.indexOf(0) > -1 || data.length === 7) {
        data.splice(0, data.length, 0)
      }
    },
    changeEnableExtPortFuc(val) {
      this.temp.extApiPort.enableExtPort = val;
      this.$nextTick(() => {
        this.$refs.inputField.focus();
      });
    },
    async inputBlur(type) {
      let msg = '';
      if (type === 1) {
        this.httpPortErrorMsg = '';
        msg = await this.portValidator(type, this.temp.extApiPort.httpPort);
        this.httpPortErrorMsg = msg;
      } else {
        this.httpsPortErrorMsg = '';
        msg = await this.portValidator(type, this.temp.extApiPort.httpsPort);
        this.httpsPortErrorMsg = msg;
      }
      // http和https端口不可一致
      const samePortMsg = this.$t('pages.syncSamePortMsg');
      // http和https端口不能同时为空
      const nullPortMsg = this.$t('pages.syncNullPortMsg');
      if (this.temp.extApiPort.httpPort === this.temp.extApiPort.httpsPort) {
        if (1 === type && '' === this.httpPortErrorMsg) {
          this.httpPortErrorMsg = samePortMsg;
          this.httpsPortErrorMsg = ''
        }
        if (2 === type && '' === this.httpsPortErrorMsg) {
          this.httpsPortErrorMsg = samePortMsg;
          this.httpPortErrorMsg = ''
        }
        if (!this.temp.extApiPort.httpPort && !this.temp.extApiPort.httpsPort) {
          this.httpPortErrorMsg = nullPortMsg;
          this.httpsPortErrorMsg = ''
        }
      } else {
        if (this.httpPortErrorMsg !== this.httpsPortErrorMsg) {
          if ((this.httpPortErrorMsg === samePortMsg || this.httpPortErrorMsg === nullPortMsg) && '' === this.httpsPortErrorMsg) {
            this.httpPortErrorMsg = '';
          }
          if ((this.httpsPortErrorMsg === samePortMsg || this.httpsPortErrorMsg === nullPortMsg) && '' === this.httpPortErrorMsg) {
            this.httpsPortErrorMsg = '';
          }
        }
      }
    },
    async portValidator(type, port) {
      let msg = '';
      if (isNaN(Number(port))) {
        msg = '';
      } else if (Number(port) !== parseInt(port)) {
        msg = this.$t('pages.serverLibrary_text5')
      } else {
        if (port < 1 || port > 65535) {
          msg = this.$t('pages.serverLibrary_text6')
        } else {
          // 后端请求判断
          await checkPortExists(type, port).then(resp => {
            if (resp.data) {
              msg = this.$t('pages.validateMsg_PortUsed', { port: port });
            } else {
              msg = '';
            }
          }).catch(e => {
            console.log(e);
            msg = this.$t('pages.validateMsg_CheckError');
          })
        }
      }
      return msg;
    },
    async initArmMode() {
      await getArmMode().then(res => {
        const data = res.data
        this.isArmMode = data === true || data === 'true'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.el-tabs{
  height: 100%;
  overflow: auto;
}
.global-config{
  padding: 30px;
  .el-form-item__label{
    color: #fff;
  }
  .error-msg-li{
    margin-left: 10px;
    color: #c74242;
    font-size: 12px;
    cursor: pointer;
  }
}
.block{
  .el-form-item{
    margin-bottom: 10px;
    label{
      text-align: left;
      // width: 160px;
    }
  }
  label{
    display: inline-block;
    /*text-align: right;*/
    /*width: 135px;*/
    font-size: 15px;
  }
  .class-label {
    line-height: 50px;
    font-size: 18px;
  }
}
.group >>>label{
  width: 40px !important;
}
>>>.el-divider__text {
  font-size: 15px;
  font-weight: 700;
  color: #eee;
  background: #0c161f;
}
</style>
