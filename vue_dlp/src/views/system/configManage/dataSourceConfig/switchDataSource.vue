<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.changeToDataSource', { info: syncSources[selectSource]})"
    :visible.sync="dialogFormVisible"
    :append-to-body="false"
    :fullscreen="true"
  >
    <div style="float: left">
      <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
        {{ $t('button.refresh') }}
      </el-button>
      <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleClear">
        {{ $t('pages.clearTemporaryTableData') }}
      </el-button>
      <el-button type="primary" icon="el-icon-download" size="mini" @click="handleDownload">
        {{ $t('pages.syncGetThirdPartyData') }}
      </el-button>
      <el-progress v-if="showProgress" :percentage="percent" style="float: right; margin-left: 20px; width: 300px" :text-inside="true" :stroke-width="26" ></el-progress>
    </div>
    <div style="float: right">
      {{ $t('table.account') }}：
      <el-input
        v-model="query.account"
        clearable
        :placeholder="$t('text.pleaseEnterInfo', { info: $t('table.account')})"
        style="width: 200px;"
        maxlength="64"
        @keyup.enter.native="handleSearch"
      ></el-input>
      {{ $t('table.userName1') }}：
      <el-input
        v-model="query.name"
        clearable
        :placeholder="$t('pages.validateMsg_enterName')"
        style="width: 200px;"
        maxlength="300"
        @keyup.enter.native="handleSearch"
      ></el-input>
      <span>
        {{ $t('table.treatmentMeasure') }}：
        <el-select v-model="query.measure" style="width: 150px;">
          <el-option v-for="item in measureOptions" :key="item.value" :label="item.label" :value="item.value" clearable></el-option>
        </el-select>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">
          {{ $t('table.search') }}
        </el-button>
      </span>
    </div>
    <Form
      ref="dataForm"
      :model="temp"
      label-position="right"
      label-width="130px"
    >
    </Form>
    <grid-table ref="gridTableList" style="min-height: 200px;height: calc(100% - 71px);" :multi-select="false" :show-pager="true" row-key="userId" :col-model="colModel" :row-data-api="rowDataApi" />
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="submit()">
        {{ $t('pages.confirmAndTakeEffect') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSyncUserTemp, downloadSyncTemp, getPercent, updateIgnoreData, cancelIgnoreData, updateSwitchSyncTempData, clearSyncTemp } from '@/api/system/configManage/syncConfig'
import { syncSources } from './sourceItems'
export default {
  name: 'SwitchDataSource',
  props: {
  },
  data() {
    return {
      temp: {},
      defaultTemp: {
      },
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        sourceType: undefined
      },
      selectSource: [],
      dialogFormVisible: false,
      submitting: false,
      syncSources: syncSources,
      colModel: [
        { prop: 'account', label: 'account', width: '150', fixed: 'left', fixedWidth: '150' },
        { prop: 'sourceType', label: this.$t('pages.syncThirdPartyDataInfo', { info: this.$t('pages.dataSourceType') }), width: '100', formatter: this.sourceTypeFormatter },
        { prop: 'sourceName', label: this.$t('pages.syncDataSourceName'), width: '80' },
        { prop: 'name', label: this.$t('pages.syncThirdPartyDataInfo', { info: this.$t('pages.userName') }), width: '100' },
        { prop: 'groupName', label: this.$t('pages.syncThirdPartyDataInfo', { info: this.$t('pages.groupType') }), width: '150' },
        { prop: 'phone', label: this.$t('pages.syncThirdPartyDataInfo', { info: this.$t('table.phone') }), width: '110' },
        { prop: 'email', label: this.$t('pages.syncThirdPartyDataInfo', { info: this.$t('table.email') }), width: '100' },
        { prop: 'localSource', label: this.$t('pages.syncLocalDataInfo', { info: this.$t('pages.dataSourceType') }), width: '100', formatter: this.sourceTypeFormatter },
        { prop: 'localName', label: this.$t('pages.syncLocalDataInfo', { info: this.$t('pages.userName') }), width: '100' },
        { prop: 'localGroupName', label: this.$t('pages.syncLocalDataInfo', { info: this.$t('pages.groupType') }), width: '150' },
        { prop: 'localPhone', label: this.$t('pages.syncLocalDataInfo', { info: this.$t('table.phone') }), width: '110' },
        { prop: 'localEmail', label: this.$t('pages.syncLocalDataInfo', { info: this.$t('table.email') }), width: '100' },
        { prop: 'measure', label: 'treatmentMeasure', width: '80', fixed: 'right', fixedWidth: '80' },
        { prop: 'details', label: 'details', width: '200', fixed: 'right', fixedWidth: '200' },
        { label: this.$t('pages.ifIgnore'), type: 'button', fixed: 'right', fixedWidth: '80',
          buttons: [
            { label: this.$t('pages.ignore'), click: this.handleIgnore, formatter: this.operateFormatter }
          ]
        }
      ],
      measureOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.cover'), value: 1 },
        { label: this.$t('text.add'), value: 2 }
      ],
      timer: undefined,
      percent: 0,
      showProgress: false,
      // 是否手动获取过第三方数据了 {false：否 true 是}
      downloadSyncFinished: false
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
  },
  methods: {
    show(selectSource) {
      this.$confirmBox(this.$t('pages.syncSwitchDataRequireFirstMsg'), this.$t('text.warning')).then(() => {
        this.dialogFormVisible = true;
        this.selectSource = selectSource;
        this.handleRefresh()
        this.downloadSyncFinished = false;
      }).catch(() => {})
    },
    rowDataApi(option) {
      this.query.page = option.page;
      const searchQuery = Object.assign({}, this.query, option);
      searchQuery.sourceType = this.selectSource;
      return getSyncUserTemp(searchQuery)
    },
    submit() {
      if (this.$refs['gridTableList'].total === 0) {
        // 如果是请求完还是没有数据的话，提示：此次获取的数据无更新，是否要直接切换数据源？
        if (this.downloadSyncFinished) {
          this.$confirmBox(this.$t('pages.syncSwitchDataEmpty'), this.$t('text.warning')).then(() => {
            this.updateSwitchSyncTemp()
          }).catch(() => {})
        } else {
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.syncSwitchEmptyMsg'), type: 'error', duration: 2000 })
        }
        return;
      }
      const h = this.$createElement
      this.$confirmBox(h('div', [h('p', this.$t('pages.syncSwitchConfirmCurrentDataMsg1')), h('p', this.$t('pages.syncSwitchConfirmCurrentDataMsg2'))]), this.$t('text.warning')).then(() => {
        this.updateSwitchSyncTemp()
      }).catch(() => {})
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    getConfig() {
      const ids = [];
      this.selectSource.map((k, v) => {
        ids.push(k.id)
      })
      this.temp.ids = ids.join(',');
      console.log(this.selectSource);
      this.temp.synType = this.selectSource[0].synType;
      this.temp.synUserAttrRef = this.selectSource[0].synUserAttrRef ? JSON.parse(this.selectSource[0].synUserAttrRef) : this.defaultTemp.synUserAttrRef;
    },
    validateFormData() {
      return true
    },
    formatFormData() {
      // 格式化同步数据源
      return Object.assign({}, this.defaultTemp, this.temp, {
        synUserAttrRef: JSON.stringify(this.temp.synUserAttrRef)
      })
    },
    updateSwitchSyncTemp() {
      this.submitting = true
      if (this.validateFormData()) {
        updateSwitchSyncTempData(this.selectSource).then(res => {
          this.submitting = false
          this.dialogFormVisible = false
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
          this.$emit('submitEnd', res)
          this.$emit('switchEndFuc')
        }).catch(e => {
          this.submitting = false
          this.$emit('submitEnd', e)
        })
      }
    },
    sourceTypeFormatter(row, data) {
      return this.syncSources[data];
    },
    handleRefresh() {
      this.$refs['gridTableList'].execRowDataApi(this.query)
    },
    handleClear() {
      this.$confirmBox(this.$t('pages.syncSwitchClearTempTable'), this.$t('text.prompt')).then(() => {
        clearSyncTemp().then(res => {
          this.query.page = 1;
          this.handleRefresh()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.operateSuccess'),
            type: 'success',
            duration: 2000
          })
          this.$emit('submitEnd', res)
        }).catch(e => {
          this.$emit('submitEnd', e)
        })
      }).catch(() => {})
    },
    handleDownload() {
      this.$confirmBox(this.$t('pages.syncSwitchManualGetDataMsg'), this.$t('text.prompt')).then(() => {
        // this.handleRefresh()
        this.downloadSyncTempData();
      }).catch(() => {})
    },
    async createdPercent() {
      this.timer = setInterval(async() => {
        this.percent = await this.updateProgress()
      }, 1000)
    },
    destroyPercent() {
      clearInterval(this.timer)
      this.showProgress = false;
      this.percent = 0;
    },
    async updateProgress() {
      // 发送请求，获取最新的进度值
      let num = 0;
      await getPercent().then(async res => {
        num = Number(res.data);
        if (num === -1 || num >= 100) {
          clearInterval(this.timer)
          this.percent = 99;
          const sleep = (delay) => new Promise((resolve) => setTimeout(resolve, delay))
          await sleep(2000)
          this.percent = 100;
          this.destroyPercent();
          this.handleRefresh()
          this.$emit('submitEnd', res)
          this.downloadSyncFinished = true;
        }
      }).catch(e => {
        this.percent = 0;
        this.$emit('submitEnd', e)
        this.destroyPercent();
        this.downloadSyncFinished = false;
      })
      return num;
    },
    downloadSyncTempData() {
      this.downloadSyncFinished = false;
      this.showProgress = true;
      this.percent = 0;
      this.createdPercent();
      downloadSyncTemp(this.selectSource).then(res => {
      }).catch(e => {
        this.destroyPercent();
        this.$emit('submitEnd', e)
        this.downloadSyncFinished = false;
      })
    },
    handleIgnore(row) {
      const h = this.$createElement
      let arr = [];
      let isIgnore = false;
      if (row.isMultiGroup === 2) {
        isIgnore = false;
        arr = [h('p', this.$t('pages.syncIfCancelIgnore'))];
      } else {
        isIgnore = true;
        arr = [h('p', this.$t('pages.syncIfIgnoreThisDataMsg1')), h('p', this.$t('pages.syncIfIgnoreThisDataMsg2'))]
      }
      this.$confirmBox(h('div', arr), this.$t('text.warning')).then(() => {
        (isIgnore ? updateIgnoreData : cancelIgnoreData)(row.syncId, row.userId).then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: isIgnore ? this.$t('pages.ignoreSuccess') : this.$t('pages.cancelIgnoreSuccess'),
            type: 'success',
            duration: 2000
          })
          this.handleRefresh()
        })
      }).catch(() => {})
    },
    handleSearch() {
      this.query.page = 1
      this.$refs['gridTableList'].execRowDataApi(this.query)
    },
    operateFormatter(row) {
      let msg;
      switch (row.isMultiGroup) {
        case 0 : msg = this.$t('pages.ignore'); break;
        case 1 : msg = this.$t('pages.ignore'); break;
        case 2 : msg = this.$t('pages.cancelIgnore'); break;
        default : msg = this.$t('pages.ignore'); break;
      }
      return msg;
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container .toolbar+.tableBox {
  height: calc(100% - 120px);
}
>>>.el-dialog__body {
  height: 600px;
}
//>>>.el-table__header .cell{
//  white-space: pre-line;
//}
</style>
