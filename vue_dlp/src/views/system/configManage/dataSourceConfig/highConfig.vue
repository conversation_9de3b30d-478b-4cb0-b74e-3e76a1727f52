<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="true"
    :modal="false"
    :title="$t('button.highConfig')"
    :visible.sync="dialogFormVisible"
    width="600px"
  >
    <Form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="150px"
    >
      <div v-if="selectSource.sourceType !== 2">
        <el-divider content-position="left">{{ $t('pages.syncUserFieldConfig') }}</el-divider>
        <FormItem :label="$t('pages.syncFieldUserAccount')" prop="account">
          <el-select v-model="temp.account" style="width: 280px" filterable allow-create clearable >
            <el-option v-for="item in sourceSyncFieldItemsOptions.account" :key="item.id" :label="item.label" :value="item.id">
              <span>{{ item.label }}</span>
              <span style="margin-left: 20px; color: #8492a6; font-size: 13px">{{ item.msg }}</span>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.syncFieldUserName')" prop="name">
          <el-select v-model="temp.name" style="width: 280px" filterable allow-create clearable >
            <el-option v-for="item in sourceSyncFieldItemsOptions.name" :key="item.id" :label="item.label" :value="item.id">
              <span>{{ item.label }}</span>
              <span style="margin-left: 20px; color: #8492a6; font-size: 13px">{{ item.msg }}</span>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.syncFieldUserPhone')" prop="phone">
          <el-select v-model="temp.phone" style="width: 280px" filterable allow-create clearable >
            <el-option v-for="item in sourceSyncFieldItemsOptions.phone" :key="item.id" :label="item.label" :value="item.id">
              <span>{{ item.label }}</span>
              <span style="margin-left: 20px; color: #8492a6; font-size: 13px">{{ item.msg }}</span>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.syncFieldUserEmail')" prop="email">
          <el-select v-model="temp.email" style="width: 280px" filterable allow-create clearable >
            <el-option v-for="item in sourceSyncFieldItemsOptions.email" :key="item.id" :label="item.label" :value="item.id">
              <span>{{ item.label }}</span>
              <span style="margin-left: 20px; color: #8492a6; font-size: 13px">{{ item.msg }}</span>
            </el-option>
          </el-select>
        </FormItem>
      </div>
      <!--<FormItem label="是否启用（active）" prop="active">
        <el-select v-model="temp.active" style="width: 280px" filterable allow-create clearable >
          <el-option v-for="item in accountOptions" :key="item.id" :label="item.label" :value="item.id">
            <span>{{ item.label }}</span>
            <span style="margin-left: 20px; color: #8492a6; font-size: 13px">{{ item.msg }}</span>
          </el-option>
        </el-select>
      </FormItem>
      <FormItem label="是否删除（deleted）" prop="deleted">
        <el-select v-model="temp.deleted" style="width: 280px" filterable allow-create clearable >
          <el-option v-for="item in accountOptions" :key="item.id" :label="item.label" :value="item.id">
            <span>{{ item.label }}</span>
            <span style="margin-left: 20px; color: #8492a6; font-size: 13px">{{ item.msg }}</span>
          </el-option>
        </el-select>
      </FormItem>-->

      <div v-if="selectSource.sourceType === 2">
        <div>
          <i18n path="pages.synGroupTypeMsg">
            <el-select slot="synType" v-model="temp.synType" style="width: 300px;" default-first-option :placeholder="$t('pages.validateMsg_synType')">
              <el-option v-for="item in synTypes" :key="item.id" :label="item.label" :value="item.id">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
          </i18n>
          <el-tooltip effect="light" placement="bottom-start">
            <span slot="content">
              <div class="sample-title" style="font-weight: bolder;font-size: 16px">{{ this.$t('pages.adDomain_image', { field: 'distinguishedName(OU、DC)'}) }}</div>
              <img class="sample-image" :src="require('@/assets/addomain/adDN.png')" :alt="$t('pages.exampleDiagram')">
              <div class="sample-title" style="font-weight: bolder;font-size: 16px">{{ this.$t('pages.adDomain_image', { field: this.$t('pages.adDomain_user_org')}) }}</div>
              <img class="sample-image" :src="require('@/assets/addomain/adOrg.png')" :alt="$t('pages.exampleDiagram')">
            </span>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
        <div>
          <i18n path="pages.synUserAccountMsg">
            <el-select slot="account" v-model="temp.synUserAttrRef.account" style="width: 300px;" default-first-option :placeholder="$t('pages.validateMsg_synType')">
              <el-option v-for="item in synUserAccountTypes" :key="item.id" :label="item.label" :value="item.id">
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
              </el-option>
            </el-select>
          </i18n>
          <el-tooltip effect="light" placement="bottom-start">
            <span slot="content">
              <div class="sample-title" style="font-weight: bolder;font-size: 16px">{{ this.$t('pages.adDomain_image', { field: this.$t('pages.adDomain_user_account')}) }}</div>
              <img class="sample-image" :src="require('@/assets/addomain/adAccount.png')" :alt="$t('pages.exampleDiagram')">
            </span>
            <i class="el-icon-info" />
          </el-tooltip>
          <template v-if="temp.synUserAttrRef.account === 'userPrincipalName'">
            <el-checkbox v-model="temp.synUserAttrRef.keepDomainName" style="margin: 5px 5px 5px 60px;" :true-label="1" :false-label="0">{{ $t('pages.adDomain_keepDomainName_message') }}</el-checkbox>
            <el-tooltip effect="light" placement="bottom-start">
              <span slot="content">
                <i18n path="pages.adDomain_keepDomainName_tips">
                  <span slot="space" style="margin-left:12px" />
                  <br slot="br"/>
                </i18n>
              </span>
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
        </div>
      </div>
      <span v-if="selectSource.sourceType === 4" style="color: #0c60a5; font-size: 12px">{{ $t('pages.syncQywxHighConfigTips') }}</span>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="submit()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSyncFieldData, updateHighConfig } from '@/api/system/configManage/syncConfig'
import { updateHighConfig as updateAdHighConfig } from '@/api/system/configManage/adDomain'
import { sourceSyncFieldItems } from './sourceItems'

export default {
  name: 'HighConfig',
  components: { },
  props: {
  },
  data() {
    return {
      temp: {},
      defaultTemp: {
        synType: 0,
        // // 用户同步字段
        synUserAttrRef: {
          account: 'userPrincipalName',
          keepDomainName: 0
        }
      },
      sourceType: 0,
      rules: {
        account: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      submitting: false,
      synTypes: [
        { id: 0, label: 'distinguishedName(OU、DC)', value: this.$t('text.default') },
        { id: 1, label: 'company、department' }
      ],
      synUserAccountTypes: [
        { id: 'userPrincipalName', label: 'userPrincipalName', value: this.$t('text.default') },
        { id: 'sAMAccountName', label: 'sAMAccountName' }
      ],
      selectSource: [],
      sourceSyncFieldItemsOptions: []
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show(selectSource) {
      this.dialogFormVisible = true;
      this.selectSource = selectSource;
      this.sourceType = this.selectSource.sourceType;
      this.sourceSyncFieldItemsOptions = sourceSyncFieldItems[this.sourceType];
      if (selectSource.sourceType === 2) {
        this.initAdConfig(selectSource);
      } else {
        this.initSyncField();
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    initAdConfig(selectSource) {
      const config = JSON.parse(selectSource.config)
      config.synUserAttrRef = (undefined === config.synUserAttrRef || '' === config.synUserAttrRef) ? { account: 'userPrincipalName', keepDomainName: 0 } : JSON.parse(config.synUserAttrRef)
      if (!config.synUserAttrRef.keepDomainName) {
        config.synUserAttrRef.keepDomainName = 0;
      }
      this.temp = Object.assign({}, config)
    },
    submit() {
      this.updateConfig()
    },
    // 初始化同步字段（根据数据源类型）
    initSyncField() {
      const sourceType = this.selectSource.sourceType
      getSyncFieldData(sourceType).then(res => {
        // this.temp = Object.assign({}, res.data)
        if (!res.data.account) {
          // 如果account无值，则默认第一个选项
          res.data.account = this.sourceSyncFieldItemsOptions.account[0].id;
        }
        this.temp = Object.assign({}, res.data)
      }).catch(e => {
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    validateFormData() {
      if (this.sourceType !== 2) {
        let validate
        this.$refs['dataForm'].validate(async(valid) => {
          validate = valid
        })
        if (!validate) {
          return false;
        }
      }
      return true
    },
    updateConfig() {
      if (this.validateFormData()) {
        this.submitting = true
        const postData = Object.assign({}, this.temp)
        postData.sourceType = this.sourceType;
        if (this.sourceType === 2) {
          postData.synUserAttrRef = JSON.stringify(postData.synUserAttrRef)
          postData.ids = postData.id;
        }
        (this.sourceType === 2 ? updateAdHighConfig : updateHighConfig)(postData).then(res => {
          this.$parent.handleRefresh();
          this.submitting = false
          this.dialogFormVisible = false
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(e => {
          this.submitting = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container .toolbar+.tableBox {
  height: calc(100% - 120px);
}
</style>
