<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button icon="el-icon-s-tools" :disabled="!showBatchProces" size="mini" @click="handleAutoConflict">
          {{ $t('pages.dataSourceBatchProcessing') }}
        </el-button>
        <el-button icon="el-icon-s-tools" size="mini" @click="handleAutoShow">
          {{ $t('pages.autoHandleConfig') }}
        </el-button>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.dataSourceConflictDescription">
              <template slot="space">&nbsp;</template>
              <br slot="br"/>
            </i18n>
          </div>
          <i>&nbsp;{{ $t('pages.functionDescription') }}&nbsp;<i class="el-icon-info" /></i>
        </el-tooltip>
        <div class="searchCon">
          <span>
            {{ $t('pages.handlingSituation') }}：
            <el-select v-model="query.processed" style="width: 150px;" clearable>
              <el-option v-for="item in processedOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            {{ $t('table.type') }}：
            <el-select v-model="query.dataType" style="width: 150px;" clearable>
              <el-option v-for="item in dataTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            {{ $t('table.name') }}：
            <el-input
              v-model="query.searchInfo"
              v-trim
              clearable
              :placeholder="$t('text.pleaseEnterInfo', { info: $t('table.userOrTerminalName') })"
              style="width: 200px;"
              @keyup.enter.native="handleFilter"
            ></el-input>
            {{ $t('table.accountOrNumber') }}：
            <el-input
              v-model="query.accountOrNumber"
              v-trim
              clearable
              :placeholder="$t('text.pleaseEnterInfo', { info: $t('table.accountOrNumber2') })"
              style="width: 200px;"
              @keyup.enter.native="handleFilter"
            ></el-input>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </span>
        </div>
      </div>
      <grid-table ref="gridTableList" :show-pager="true" row-key="entityId" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.dataSourceSyncConflict')"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="120px"
        style="width: 500px;margin-left: 20px"
      >
        <FormItem :label="$t('pages.dataSourceType')" prop="sourceType">
          {{ syncSources[temp.sourceType] }}
        </FormItem>
        <FormItem :label="$t('pages.type')" prop="type">
          {{ dataType[temp.type] }}
        </FormItem>
        <FormItem v-if="1 === temp.type" :label="$t('table.userAccount')" prop="account">
          {{ temp.account }}
        </FormItem>
        <FormItem v-if="1 === temp.type" :label="$t('table.phoneNumber')" prop="phone">
          {{ temp.phone }}
        </FormItem>
        <FormItem v-if="2 === temp.type" :label="$t('table.terminalCode')" prop="id">
          {{ temp.id }}
        </FormItem>
        <FormItem :label="2 === temp.type ? $t('table.terminalName') : $t('table.userName1')" prop="name">
          {{ temp.name }}
        </FormItem>
        <template v-if="1 === temp.type && !!temp.multiGroupId" >
          <FormItem :label="$t('pages.dataSourceCurrentDept')" prop="groupName">
            {{ groupTreeListFormatter(temp) }}
          </FormItem>
          <FormItem :label="$t('pages.groupOptional')" prop="selectData">
            <el-select v-model="selectData" clearable :placeholder="$t('pages.pleaseSelectContent',{content:$t('pages.dept')})" >
              <el-option v-for="item in temp.groupTreeList" :key="item.id" :label="item.label" :value="item.id"/>
            </el-select>
          </FormItem>
        </template>
        <template v-if="1 === temp.type && 2 === temp.isMultiGroup" >
          <FormItem :label="$t('form.status')">
            {{ $t('pages.syncIgnoreData') }}
          </FormItem>
        </template>
        <template v-if="1 === temp.type && 3 === temp.isMultiGroup" >
          <FormItem :label="$t('form.status')">
            {{ $t('pages.samePhoneNumberData') }}
          </FormItem>
        </template>
        <template v-else-if="2 === temp.type" >
          <FormItem :label="$t('pages.groupOptional')" prop="selectData">
            <tree-select
              ref="parentDept"
              leaf-key="dept"
              :local-search="false"
              :checked-keys="['G'+temp.groupId]"
              is-filter
              :width="380"
              @change="parentIdSelectChange1"
            />
          </FormItem>
        </template>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="updateData()">
          {{ 2 === temp.isMultiGroup && !temp.multiGroupId ? $t('pages.cancelIgnore') : $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.autoHandleConfig')"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <Form
        ref="configForm"
        :model="configForm"
        label-width="180px"
        label-position="right"
      >
        <el-row>
          <el-col :span="24">
            <el-checkbox v-model="configConfict.userAuto" :true-label="1" :false-label="0">{{ $t('pages.autoHandleConfigUser') }}</el-checkbox>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="handleConfig">{{ $t('button.save') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.batchHandleExceptionData')"
      :visible.sync="dialogVisible2"
      width="800px"
    >
      <Form
        ref="configBatchForm"
        label-width="180px"
        label-position="right"
      >
        <el-row>
          <el-col :span="24">
            <p v-if="userData && userData.length>0&& userData.filter(p => (0 === p.isMultiGroup && !!p.multiGroupId || 1 === p.isMultiGroup || 2 === p.isMultiGroup && !!p.multiGroupId || 3 === p.isMultiGroup && !!p.multiGroupId)).length > 0 ">{{ $t('pages.batchHandleExceptionDataUser') }}
              <grid-table ref="gridTableListUser" :show-pager="false" style="height: 120px" :multi-select="false" :col-model="colModelUserTerminal" :row-datas="userData.filter(p => (0 === p.isMultiGroup && !!p.multiGroupId || 1 === p.isMultiGroup || 2 === p.isMultiGroup && !!p.multiGroupId || 3 === p.isMultiGroup && !!p.multiGroupId))" />
            </p>
            <p v-if="userData && userData.length>0&& userData.filter(p => 2 === p.isMultiGroup && !p.multiGroupId).length > 0 ">{{ $t('pages.syncUserCancelIgnore') }}
              <grid-table ref="gridTableListUser" :show-pager="false" style="height: 120px" :multi-select="false" :col-model="colModelUserTerminal" :row-datas="userData.filter(p => 2 === p.isMultiGroup && !p.multiGroupId)" />
            </p>
            <p v-if="userData && userData.length>0&& userData.filter(p => 3 === p.isMultiGroup && !p.multiGroupId).length > 0 ">{{ $t('pages.syncUserResumeSynchronization') }}
              <grid-table ref="gridTableListUser" :show-pager="false" style="height: 120px" :multi-select="false" :col-model="colModelUserTerminal" :row-datas="userData.filter(p => 3 === p.isMultiGroup && !p.multiGroupId)" />
            </p>
            <p v-if="terminalData && terminalData.length>0">
              <i18n path="pages.batchHandleExceptionDataTerminal">
                <tree-select
                  slot="group"
                  ref="parentDept"
                  style="display: inline-block"
                  leaf-key="dept"
                  :local-search="false"
                  :checked-keys="['G'+batchGroupId]"
                  is-filter
                  :width="380"
                  @change="parentIdSelectChange3"
                />
              </i18n>
              <grid-table ref="gridTableListTerminal" :show-pager="false" style="height: 120px" :multi-select="false" :col-model="colModelUserTerminal" :row-datas="terminalData" />
            </p>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="handleBatchConfig">{{ $t('button.save') }}</el-button>
        <el-button @click="dialogVisible2 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSyncConflict, updateSyncConflict, batchUpdateConflictData, autoConflictConfig, getAutoConflictConfig } from '@/api/system/configManage/syncConfig'
import { syncSources } from './sourceItems'
export default {
  name: 'SyncConflict',
  data() {
    return {
      colModel: [
        { prop: 'sourceType', label: this.$t('pages.dataSourceType'), width: '100', formatter: this.sourceTypeFormatter },
        { prop: 'sourceName', label: this.$t('pages.syncDataSourceName'), width: '100' },
        { prop: 'type', label: 'type', width: '100', formatter: this.typeFormatter },
        { prop: 'name', label: 'name', width: '150' },
        { label: 'accountOrNumber', width: '150', formatter: this.accountNumberFormatter },
        { prop: 'details', label: 'details', width: '450' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '150',
          buttons: [
            { label: this.$t('pages.deal'), click: this.handleUpdate, formatter: this.dealFormatter }
          ]
        }
      ],
      colModelUserTerminal: [
        { prop: 'sourceType', label: this.$t('pages.dataSourceType'), width: '100', sort: true, formatter: this.sourceTypeFormatter },
        { prop: 'type', label: 'type', width: '100', sort: true, formatter: this.typeFormatter },
        { prop: 'name', label: 'name', width: '150', sort: true },
        { prop: 'details', label: 'details', width: '350', sort: true }
      ],
      rules: {
        groupId: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        type: undefined,
        sourceType: '',
        account: '',
        name: '',
        groupId: ''
      },
      selectData: '',
      dialogFormVisible: false,
      submitting: false,
      loading: false,
      syncSources: syncSources,
      dataType: {
        1: this.$t('pages.user'),
        2: this.$t('pages.terminal')
      },
      dataTypeOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.user'), value: 1 },
        { label: this.$t('pages.terminal'), value: 2 }
      ],
      processedOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.processedModifiable'), value: false },
        { label: this.$t('pages.undeal'), value: true }
      ],
      dialogVisible: false,
      dialogVisible2: false,
      configForm: {},
      configTerminalForm: {},
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        dataType: undefined,
        accountOrNumber: '',
        processed: undefined
      },
      showBatchProces: false,
      configConfict: {
        userAuto: 0,
        terminalAuto: 0,
        terminalGroupId: undefined
      },
      userData: [],
      terminalData: [],
      batchGroupId: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTableList']
    }
  },
  created() {
    // this.resetTemp()
  },
  methods: {
    handleDrag() {
    },
    rowDataApi(option) {
      this.getAutoConfig();
      const searchQuery = Object.assign({}, this.query, option);
      return getSyncConflict(searchQuery)
    },
    handleRefresh() {
      this.gridTable.execRowDataApi()
    },
    handleUpdate: function(row) {
      this.dialogFormVisible = true
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.selectData = this.temp.groupId.indexOf(',') > -1
        ? this.temp.groupTreeList[0].id
        : 'default' === this.groupTreeListFormatter(this.temp)
          ? null
          : this.temp.groupId;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          const type = tempData.type;
          if (1 === type) {
            tempData.groupId = this.selectData;
            if (!tempData.groupId) {
              this.$message({
                title: this.$t('text.prompt'),
                message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.groupOptional') }),
                type: 'error',
                duration: 2000
              })
              this.submitting = false;
              return;
            }
          }
          updateSyncConflict(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.handleRefresh();
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    selectionChangeEnd(rowDatas) {
      this.showBatchProces = rowDatas && rowDatas.length > 0
    },
    completedFormatter(row, data) {
      return true === data ? this.$t('text.yes') : this.$t('text.no');
    },
    sourceTypeFormatter(row, data) {
      return this.syncSources[data];
    },
    typeFormatter(row, data) {
      return this.dataType[data];
    },
    accountNumberFormatter(row, data) {
      if (1 === row.type) {
        return row.account;
      } else if (2 === row.type) {
        return row.id;
      }
    },
    groupFormatter(row) {
      return row.groupName ? row.groupName.split(',')[0] : null;
    },
    multiGroupFormatter(row) {
      return row.groupName;
    },
    handleAutoConflict() {
      const selectedIds = this.gridTable.getSelectedKeys();
      if (undefined === selectedIds || 0 === selectedIds.length) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.pleaseTickContent', { content: this.$t('pages.dataSourceNeedBatchData') }),
          type: 'error',
          duration: 2000
        })
      } else {
        this.dialogVisible2 = true;
        const selectedDatas = this.gridTable.getSelectedDatas();
        const userData = [];
        const terminalData = [];
        selectedDatas.map(p => {
          if (1 === p.type) {
            userData.push(p);
          } else if (2 === p.type) {
            terminalData.push(p)
          }
        })
        this.userData = userData;
        this.terminalData = terminalData;
      }
    },
    handleAutoShow() {
      this.getAutoConfig();
      this.dialogVisible = true;
    },
    handleConfig() {
      this.submitting = true;
      const configConfict = this.configConfict;
      if (configConfict.terminalAuto && configConfict.terminalAuto === 1 && undefined === configConfict.terminalGroupId) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.group') }),
          type: 'error',
          duration: 2000
        })
        this.submitting = false;
        return false;
      }
      autoConflictConfig(configConfict).then(res => {
        this.dialogVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.handleRefresh()
        this.submitting = false
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    handleBatchConfig() {
      this.submitting = true;
      const data = {
        userData: this.userData,
        terminalIds: Array.from(this.terminalData, ({ id }) => id),
        batchGroupId: this.batchGroupId
      }
      if (data.terminalIds && data.terminalIds.length > 0 && undefined === data.batchGroupId) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.group') }),
          type: 'error',
          duration: 2000
        })
        this.submitting = false;
        return false;
      }
      batchUpdateConflictData(data).then(res => {
        this.dialogVisible2 = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.handleRefresh()
        this.submitting = false
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    // 获取自动配置参数
    getAutoConfig() {
      getAutoConflictConfig().then(res => {
        this.configConfict.userAuto = res.data.userAuto ? res.data.userAuto : 0;
        this.configConfict.terminalAuto = res.data.terminalAuto ? res.data.terminalAuto : 0;
        this.configConfict.terminalGroupId = res.data.terminalGroupId;
      }).catch(err => {
        console.log(err)
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    parentIdSelectChange1(key, data) {
      if (undefined === data) {
        return;
      }
      data = data[0] || data
      this.temp.groupId = data.dataId
      this.temp.groupName = data.label;
    },
    parentIdSelectChange2(key, data) {
      if (undefined === data) {
        return;
      }
      data = data[0] || data
      this.configConfict.terminalGroupId = data.dataId
    },
    parentIdSelectChange3(key, data) {
      if (undefined === data) {
        return;
      }
      data = data[0] || data
      this.batchGroupId = data.dataId
    },
    dealFormatter(row) {
      return row.isMultiGroup ? this.$t('pages.deal') : this.$t('pages.processedModifiable');
    },
    groupTreeListFormatter(row) {
      if (!row.multiGroupId) {
        return;
      }
      if (row.isMultiGroup) {
        return row.groupName.split(',')[0];
      }
      let name = 'default';
      row.groupTreeList.map(p => {
        if (p.id === row.groupId) {
          name = p.label;
        }
      });
      return name;
    }
  }
}
</script>

<style lang="scss" scoped>
  .table-container .toolbar+.tableBox {
    height: calc(100% - 120px);
  }
  >>>.el-form-item__content{
    left: 20px;
  }
</style>
