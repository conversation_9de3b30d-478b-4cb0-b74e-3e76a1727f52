<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <div class="searchCon">
          <TimeQuery @getTimeParams="getTimeParams"/>
          <span>
            {{ $t('pages.dataSourceType') }}：
            <el-select v-model="query.sourceType" style="width: 150px;" clearable>
              <el-option v-for="item in dataSourceTypes" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
            {{ $t('pages.syncStatus') }}：
            <el-select v-model="query.status" style="width: 150px;" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">
              {{ $t('table.search') }}
            </el-button>
          </span>
        </div>
      </div>
      <grid-table ref="gridTableList" :multi-select="false" :show-pager="true" row-key="userId" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.logDetail')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.syncTime')" :label-style="{width: lang === 'en'? '150px' : '88px'}">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.syncStatus')" :label-style="{width: lang === 'en'? '200px' : '120px'}">
            {{ statusFormatter(rowDetail, rowDetail.status) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.dataSourceType')" :label-style="{width: lang === 'en'? '150px' : '88px'}">
            {{ sourceTypeFormatter(rowDetail, rowDetail.sourceType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.syncDataSourceName')" :label-style="{width: lang === 'en'? '190px' : '120px'}">
            {{ rowDetail.sourceName }}
          </el-descriptions-item>
        </el-descriptions>
        <grid-table ref="gridTableLogList" :show-pager="false" style="margin-top: -1px;height: 220px" :multi-select="false" :default-sort="defaultSort" :col-model="colLogModel" :row-datas="syncLogDatas" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSyncStatusPage, getDetails } from '@/api/system/configManage/syncConfig'
import { syncSources, syncSourceList } from './sourceItems'

export default {
  name: 'SyncLog',
  props: {
    hiddenSource: { // 隐藏的数据源
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'syncTime', width: '120', sort: true },
        { prop: 'sourceType', label: this.$t('pages.dataSourceType'), width: '120', formatter: this.sourceTypeFormatter },
        { prop: 'sourceName', label: this.$t('pages.syncDataSourceName'), width: '120' },
        { prop: 'status', label: this.$t('pages.syncStatus'), width: '120', formatter: this.statusFormatter },
        // { prop: 'logType', label: '日志类型', width: '80', formatter: this.logTypeFormatter },
        // { prop: 'details', label: '内容', width: '550' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'detail', click: this.handleUpdate }
          ]
        }
      ],
      colLogModel: [
        { prop: 'createTime', label: 'operateTime', width: '120', sort: true },
        { prop: 'logType', label: this.$t('pages.logType'), width: '70', formatter: this.logTypeFormatter },
        { prop: 'details', label: this.$t('pages.syncContent'), width: '330' }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        type: undefined,
        sourceType: '',
        account: '',
        name: '',
        groupId: ''
      },
      rowDetail: {},
      selectData: '',
      dialogFormVisible: false,
      submitting: false,
      loading: false,
      statusOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.synchronizing'), value: 1 },
        { label: this.$t('pages.syncCompleted'), value: 2 },
        { label: this.$t('pages.syncFailed'), value: 3 }
      ],
      logTypeOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.normalLog'), value: 1 },
        { label: this.$t('pages.exceptionLog'), value: 2 }
      ],
      // 查询条件
      query: {
        page: 1,
        createDate: '',
        startDate: '',
        searchInfo: ''
      },
      showBatchProces: false,
      syncLogDatas: [],
      defaultSort: { prop: 'createTime', order: 'asc' }
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    },
    gridTable() {
      return this.$refs['gridTableList']
    },
    dataSourceTypes() {
      const sourceList = syncSourceList('1', '2');
      for (let i = 0; i < sourceList.length; i++) {
        const item = sourceList[i]
        if (this.hiddenSource.indexOf(item.id) > -1) {
          sourceList.splice(i, 1)
        }
      }
      return sourceList
    }
  },
  created() {
    // this.resetTemp()
  },
  methods: {
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option);
      return getSyncStatusPage(searchQuery)
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleSearch() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleUpdate: function(row) {
      this.rowDetail = JSON.parse(JSON.stringify(row)) // copy obj
      this.dialogFormVisible = true
      this.getSyncLogDatas(this.rowDetail.id);
    },
    getSyncLogDatas(taskId) {
      getDetails(taskId).then(res => {
        this.syncLogDatas = res.data;
      }).catch(e => {
      })
    },
    selectionChangeEnd(rowDatas) {
      this.showBatchProces = rowDatas && rowDatas.length > 0
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    sourceTypeFormatter(row, data) {
      return syncSources[data];
    },
    statusFormatter(row, data) {
      let res = null;
      this.statusOptions.map((item) => {
        if (item.value === data) {
          res = item.label;
          return res;
        }
      })
      return res;
    },
    logTypeFormatter(row, data) {
      let res = null;
      this.logTypeOptions.map((item) => {
        if (item.value === data) {
          res = item.label;
          return res;
        }
      })
      return res;
    }
  }
}
</script>

<style lang="scss" scoped>
  .table-container .toolbar+.tableBox {
    height: calc(100% - 120px);
  }
</style>
