<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.dataSourceSetOutSyncOrg')"
    :visible.sync="visible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <tree-select-panel
      v-show="[2].includes(sourceType)"
      ref="adTree"
      :loading="loading"
      lazy
      :load="loadAdTree"
      :selected-data="selectedData"
      check-strictly
      :include-child="false"
      :to-select-title="$t('pages.to_select')"
      :selected-title="$t('pages.selected')"
      height="390px"
    />
    <tree-select-panel
      v-show="[3,4,7,8,9].includes(sourceType)"
      ref="filterTree"
      :loading="loading"
      :data="treeData"
      :selected-data="selectedFilterData"
      :selected-button="selectedButtons"
      default-selected-root-id="T2"
      check-strictly
      include-child
      :selected-node-filter="selectedNodeFilter"
      :to-select-title="$t('pages.to_select')"
      :selected-title="$t('pages.selected')"
      height="390px"
      @check="nodeCheck"
    />
    <tree-select-panel
      v-show="5 === sourceType"
      ref="ddTree"
      :loading="loading"
      lazy
      :load="loadTree"
      :selected-data="selectedFilterData"
      :selected-button="selectedButtons"
      default-selected-root-id="T2"
      check-strictly
      include-child
      :default-expanded-keys="defaultExpandedKeys"
      :selected-node-filter="selectedNodeFilter"
      :to-select-title="$t('pages.to_select')"
      :selected-title="$t('pages.selected')"
      height="390px"
      @check="nodeCheck"
    />
    <ol v-if="sourceType != 2" style="color: rgb(43, 122, 172);margin: 0 0;padding-left: 20px;">
      <li>{{ $t('pages.designateDeptTip') }}</li>
      <li>{{ $t('pages.cascadeDeptTip') }}</li>
    </ol>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" :disabled="loading" @click="handleSaveSyncGroupFilter()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { getSyncGroupTree, getAllSyncGroupTree, saveSyncGroupFilter, getSyncGroupFilter, getSyncGroupFilterNode } from '@/api/system/configManage/syncConfig'
import { getAdDomainFilterTree, saveAdDomainFilter } from '@/api/system/configManage/adDomain'
import TreeSelectPanel from '@/components/TreeSelectPanel/index_old'

export default {
  name: 'SyncFilter',
  components: { TreeSelectPanel },
  props: {
    // syncId: {
    //   type: Number,
    //   default: -1
    // }
  },
  data() {
    return {
      visible: false,
      loading: false,
      // [已选组织]的"级联""指定"树
      selectedFilterData: [
        { id: 'T1', label: this.$t('pages.cascadeDept'), type: 3, children: [] },
        { id: 'T2', label: this.$t('pages.optionedDept'), type: 3, children: [] }
      ],
      // 默认第展开的数据
      defaultExpandedKeys: [],
      submitting: false,
      // [待选组织]的树
      treeData: [],
      // 数据源类型 3：IDM 4：企业微信 5：钉钉
      sourceType: '',
      // 级联的节点集合(dataId)
      cascadePermissionIds: [],
      // 已选组织]的"级联""指定"树的按钮
      selectedButtons: [
        {
          label: this.$t('pages.cascade'),
          show: (node, data) => {
            return data.id.indexOf('G' + data.dataId) >= 0 && this.cascadePermissionIds.indexOf(data.dataId) < 0
          },
          onclick: (node, data, conponent) => this.cascadePermissionNode(node, data, conponent, this)
        },
        {
          label: this.$t('pages.optioned'),
          show: (node, data) => {
            return data.id.indexOf('G' + data.dataId) >= 0 && this.cascadePermissionIds.indexOf(data.dataId) >= 0
          },
          onclick: (node, data, conponent) => this.assignPermissionNode(node, data, conponent, this)
        },
        {
          label: this.$t('pages.delete'),
          show: (node, data) => { return ['T1', 'T2'].indexOf(data.id) < 0 },
          onclick: (node, data, conponent) => {
            if (this.cascadePermissionIds.indexOf(data.dataId) >= 0) { // 说明删除的是级联节点
              this.setSelectedCascadeNode(data, false)
            }
            conponent.removeSelectedNode(data)
          }
        }
      ],
      // [已选组织]的“指定部门id”
      assignDeptId: [],
      // [已选组织]的“级联部门id”
      cascadeDeptId: [],
      deletedParam: {
        dept: 0,
        user: 0
      },
      // sync_source数据源id
      syncId: undefined,
      // AD原功能相关字段
      firstLoad: true,
      currentId: null,
      resultMap: {
        0: this.$t('pages.resultMap0'),
        2: this.$t('pages.resultMap2'),
        3: this.$t('pages.resultMap3')
      },
      selectedData: [],
      sourceData: []
    }
  },
  created() {

  },
  updated() {

  },
  methods: {
    show(data) {
      console.log('data:', data);
      this.sourceData = data;
      if (2 === data.sourceType) {
        this.visible = true;
        this.sourceType = data.sourceType;
        this.syncId = data.syncId;

        this.firstLoad = true
        this.currentId = data.syncId
        if (this.$refs.adTree !== undefined) {
          this.$refs.adTree.clearSelectedNode()
          this.$refs.adTree.reloadToSelectTree(false)
          this.$refs.adTree.clearFilter()
        }
      } else {
        this.visible = true;
        this.sourceType = data.sourceType;
        this.syncId = data.syncId;
        this.selectedFilterData[0].children = [];
        this.selectedFilterData[1].children = [];
        this.treeData = [];
        this.assignDeptId = [];
        this.cascadeDeptId = [];
        this.cascadePermissionIds = [];
        this.initTreeData(data.sourceType);
      }
    },
    handleDrag() {

    },
    // 初始化树
    initTreeData(sourceType) {
      // AD域/IDM/企业微信
      this.loadFilterTreeData(sourceType);
      // 初始化钉钉懒加载树
      if (5 === this.sourceType) {
        this.loadFilterSelectIds(sourceType);
        this.showSelectDataParentNode(sourceType);
        this.refTree() && this.refTree().reloadToSelectTree(true)
      }
    },
    // 获取AD域/IDM/企业微信部门树
    loadFilterTreeData(sourceType) {
      if ([5].includes(sourceType)) {
        return;
      }
      this.loading = true;
      getAllSyncGroupTree(this.sourceType, this.syncId).then(res => {
        this.treeData = res.data
        this.$nextTick(() => {
          // 加载树后再加载已选择节点数据
          this.loadSelectData(sourceType, this.syncId);
          this.loading = false;
        });
      }).catch(e => { console.log(e) })
    },
    // 保存过滤树
    handleSaveSyncGroupFilter() {
      this.submitting = true
      if (2 === this.sourceType) {
        this.saveAdDomainFilter();
        return;
      }
      const params = {
        sourceType: this.sourceType,
        syncId: this.syncId,
        deletedParam: this.deletedParam,
        selectedFilterData: this.selectedFilterData
      };
      saveSyncGroupFilter(params).then(respond => {
        this.submitting = false
        this.visible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.adDomain_text8'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    // 当前树ref
    refTree() {
      if (5 === this.sourceType) {
        return this.$refs['ddTree'];
      } else {
        return this.$refs['filterTree'];
      }
    },
    // 加载已选择的节点数据（将已选节点在[待选组织]勾选上并设置级联节点）
    loadSelectData(sourceType, syncId) {
      getSyncGroupFilter(sourceType, syncId).then(res => {
        const data = res.data;
        const cascadeIds = data.cascadeDeptId;
        const assignIds = data.assignDeptId;
        const allIds = cascadeIds.concat(assignIds);
        this.deletedParam = data.deletedParam;
        // 将已选节点都勾选上
        allIds.forEach(id => {
          const node = this.refTree().getToSelectNode('G' + id);
          if (node) {
            this.refTree().setSelectedNode(node.data)
          }
        })
        // 设置级联节点
        cascadeIds.forEach(id => {
          const node = this.refTree().getToSelectNode('G' + id)
          if (node) {
            this.setSelectedCascadeNode(node.data, true)
            this.cascadePermissionNode(node, node.data, this.refTree(), this)
          }
        })
      })
    },
    // 指定转成级联
    cascadePermissionNode(node, data, conponent, that) {
      const cascadeNodeDatas = that.selectedFilterData[0].children // 级联数据
      const assignNodeDatas = that.selectedFilterData[1].children // 指定数据

      for (let i = 0; i < cascadeNodeDatas.length; i++) {
        const nodeData = cascadeNodeDatas[i]
        if (nodeData.dataCode.indexOf(data.dataCode) === 0) { // 说明当前级联节点是已级联节点的父节点，则抛弃已接连节点
          conponent.setChildNodesDisabled(data, false)
          this.setSelectedCascadeNode(nodeData, false)
          i--
        } else if (data.dataCode.indexOf(nodeData.dataCode) === 0) { // 说明当前级联节点是已级联节点的子节点，则抛弃当前节点
          return
        }
      }
      this.$nextTick(() => {
        this.setSelectedCascadeNode(data, true)
        conponent.setChildNodesDisabled(data, true)
        // 级联节点和指定节点合并起来（为了让左侧勾选的节点不会被取消），然后再过滤节点
        const selectNodeDotas = JSON.parse(JSON.stringify(cascadeNodeDatas.concat(assignNodeDatas)))
        this.refTree().selectTreeDataChangeEnd(selectNodeDotas)
      })
    },
    // 级联转成指定
    assignPermissionNode(node, data, conponent, that) {
      that.setSelectedCascadeNode(data, false)
      that.refTree().setSelectedNode(data)
      this.$nextTick(() => {
        conponent.setChildNodesDisabled(data, false)
      })
    },
    // 级联节点的选中与取消选中
    setSelectedCascadeNode(nodeData, isSelect) {
      const cascadeNodeDatas = this.selectedFilterData[0].children
      if (isSelect === undefined || isSelect) { // 将传入节点设为选中
        const targetNodeData = Object.assign({}, nodeData)
        targetNodeData.children = []
        cascadeNodeDatas.push(targetNodeData)
        this.cascadePermissionIds.push(targetNodeData.dataId)
      } else { // 传入节点取消选中
        let nodeIndex = -1;
        cascadeNodeDatas.forEach(p => {
          // 判断当前节点未选中时，是否与级联部门里的数据相匹配，若匹配，则记录匹配位置，后续进行删除节点
          if (p.dataId === nodeData.dataId) {
            nodeIndex = cascadeNodeDatas.indexOf(p);
          }
        })
        if (nodeIndex > -1) {
          // 若级联部门中存在该节点，则删除节点
          cascadeNodeDatas.splice(nodeIndex, 1)
        }
        const index = this.cascadePermissionIds.indexOf(nodeData.dataId)
        if (index > -1) {
          // 若级联部门id存在该id，则删除该id
          this.cascadePermissionIds.splice(index, 1)
        }
      }
    },
    // 选中节点过滤
    selectedNodeFilter(nodeDatas, isLeafNode) {
      const that = this
      const resultNodeDatas = []
      nodeDatas.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) >= 0 && that.cascadePermissionIds.indexOf(nodeData.dataId) < 0) { // 不是级联节点时，才添加到选中节点中
          resultNodeDatas.push(nodeData)
          if (nodeData.children && nodeData.children.length > 0) {
            nodeData.children = that.selectedNodeFilter(nodeData.children)
          }
        }
      })
      return resultNodeDatas
    },
    // 节点勾选事件（勾选与取消勾选）
    nodeCheck(nodeData, checkedInfo) {
      const isChecked = checkedInfo.checkedNodes.indexOf(nodeData) > -1
      if (!isChecked) {
        // 取消勾选时自身也取消勾选
        this.refTree().refactorSelectedTreeData(nodeData, false, checkedInfo)
        // 取消级联节点
        this.setSelectedCascadeNode(nodeData, isChecked)
      }
    },
    // 获取钉钉部门树
    loadTree(node, resolve) {
      if (5 !== this.sourceType) {
        return;
      }
      this.loading = true;
      const deptId = node.data ? node.data.dataId ? node.data.dataId : 0 : 0;
      (0 === deptId ? getAllSyncGroupTree : getSyncGroupTree)(this.sourceType, this.syncId, deptId).then(res => {
        node.data.children = res.data;
        resolve(res.data);
        // 如果当前node节点的id存在[已选组织]中，则勾选该节点
        // 级联部门+指定部门 的 id都勾选
        if (this.assignDeptId.concat(this.cascadeDeptId).includes(node.data.dataId)) {
          if (node) {
            this.refTree().setSelectedNode(node.data)
          }
        }
        // 级联部门id
        if (this.cascadeDeptId.includes(node.data.dataId)) {
          this.setSelectedCascadeNode(node.data, true)
          this.cascadePermissionNode(node, node.data, this.refTree(), this)
        }
        this.refTree().refactorSelectedTreeData(node, true);
        this.loading = false;
      }).catch(e => { console.log(e) })
    },
    // 加载过滤树，并赋值级联id和部门id
    loadFilterSelectIds(sourceType) {
      getSyncGroupFilter(sourceType, this.syncId).then(res => {
        this.assignDeptId = res.data.assignDeptId;
        this.cascadeDeptId = res.data.cascadeDeptId;
        this.deletedParam = res.data.deletedParam;
      })
    },
    // 加载当前过滤配置中所有上级节点，并展开
    showSelectDataParentNode(sourceType) {
      getSyncGroupFilterNode(sourceType, this.syncId).then(res => {
        const arr = [];
        res.data.forEach(id => {
          const gid = 'G' + id;
          arr.push(gid);
        })
        this.defaultExpandedKeys = arr;
      })
    },
    getSelectNode() {
      // const domainId = this.gridTable.getSelectedIds()[0]
      getAdDomainFilterTree({ domainId: this.syncId }).then(res => {
        res.data.forEach(item => {
          this.$refs.adTree.setSelectedNode(item)
        })
      })
    },
    loadAdTree(node, resolve) {
      this.currentId = this.syncId
      const parse = JSON.parse(this.sourceData.config);
      const parentId = node.id !== 0 && node.data.id ? node.data.id : parse.host
      let list = []
      let curFrameNo = 0
      this.loading = true
      this.$socket.sendToUser('', '/getAdDomainDeptTree', {
        parentId: parentId
      }, (respond, handle) => {
        this.loading = false
        // 判断当前选择的域服务是不是请求的域服务器
        if (this.currentId === this.syncId) {
          if (respond.data && respond.data.result === 1) {
            if (this.firstLoad) {
              list = respond.data.list
            } else {
              list = list.concat(respond.data.list)
            }
            resolve(list)
            // 组织结构数据过大的话，会分多次返回，当前帧数等于总帧时，表示加载完成了，取消订阅
            curFrameNo++
            if (curFrameNo === respond.data.totalFrameNum) {
              handle.close()
              if (this.firstLoad) {
                this.getSelectNode()
              }
              this.firstLoad = false
            }
          } else {
            this.$message({
              message: this.resultMap[respond.data.result],
              type: 'error',
              duration: 2000
            })
            resolve([])
            // 获取数据失败后也要取消订阅
            handle.close()
          }
        } else {
          handle.close()
        }
      }, () => {
        this.loading = false
      })
    },
    saveAdDomainFilter() {
      const domainId = this.sourceData.syncId;
      const filterDept = []
      this.selectedData.forEach(item => {
        const data = {
          adDomainId: domainId,
          groupName: item.label,
          groupGuid: item.dataId,
          adsPath: item.id
        }
        filterDept.push(data)
      })
      const obj = {
        adDomainId: domainId,
        list: filterDept
      }
      this.submitting = true
      saveAdDomainFilter(obj).then(respond => {
        this.submitting = false
        this.visible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.adDomain_text8'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
