<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.synchronousAlarmSettings')"
    :visible.sync="visible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div>
      <Form
        ref="dataForm"
        :model="temp"
        :hide-required-asterisk="true"
        label-position="right"
        label-width="150px"
      >
        <el-row>
          <el-col :span="24">
            <el-divider content-position="left">{{ $t('pages.alarmConditions') }}</el-divider>
            <FormItem>
              <label style="margin-left: -60px">
                <el-checkbox v-model="temp.alarmFailed">
                  <i18n path="pages.alarmFailedNum">
                    <el-input slot="info" v-model="temp.alarmFailedNum" type="Number" maxlength="3" min="1" style="width: 80px;" @input="numberLimit(temp.alarmFailedNum, temp, 'alarmFailedNum', 1, 999)" ></el-input>
                  </i18n>
                </el-checkbox>
              </label>
            </FormItem>
            <FormItem>
              <label style="margin-left: -60px">
                <el-checkbox v-model="temp.alarmError">
                  <i18n path="pages.alarmErrorNum">
                    <el-input slot="info" v-model="temp.alarmErrorNum" maxlength="3" min="1" type="Number" style="width: 80px;" @input="numberLimit(temp.alarmErrorNum, temp, 'alarmErrorNum', 1, 999)" ></el-input>
                  </i18n>
                </el-checkbox>
              </label>
            </FormItem>
          </el-col>
          <el-col :span="24">
            <el-divider content-position="left">{{ $t('pages.alarmTrigger') }}</el-divider>
            <FormItem>
              <label style="margin-left: -60px">
                <i18n path="pages.alarmTriggerFrequencyInfo">
                  <el-input slot="info" v-model="temp.alarmTriggerFrequency" type="Number" maxlength="4" min="1" style="width: 80px;" @input="numberLimit(temp.alarmTriggerFrequency, temp, 'alarmTriggerFrequency', 1, 1440)"></el-input>
                </i18n>
              </label>
            </FormItem>
            <FormItem>
              <label style="margin-left: -102px">
                <i18n path="pages.alarmTriggerUpperInfo">
                  <el-input slot="info" v-model="temp.alarmTriggerUpper" type="Number" maxlength="4" min="0" style="width: 80px;" @input="numberLimit(temp.alarmTriggerUpper, temp, 'alarmTriggerUpper', 1, 1440)"></el-input>
                </i18n>
              </label>
            </FormItem>
          </el-col>
          <el-col :span="24">
            <el-divider content-position="left" >{{ $t('pages.alarmMode') }}</el-divider>
          </el-col>
          <el-col v-for="(item, index) in options" :key="index" :span="24">
            <!-- 配置项选择框 -->
            <el-checkbox v-model="item.checked" :label="item.value" style="margin-bottom:10px;margin-left: 89px" @change="handleCheckChange">{{ item.label }}</el-checkbox>
            <!-- 控制台告警附属功能 -->
            <Form v-if="item.value === 4 && checked.includes(4)" ref="sysUserForm" :model="temp" style="margin: 0 0 10px 89px;" >
              <FormItem ref="sysUser" prop="sysUserIds" :rules="[{ required:true, message: $t('pages.alarmSetup_text7'), trigger: 'change'}]">
                <el-select v-model="temp.sysUserIds" filterable multiple :placeholder="$t('pages.alarmSetup_text7')" @change="sysUserChange">
                  <el-option
                    v-for="user in sysUserList"
                    :key="user.id"
                    :label="user.name"
                    :value="user.id"
                  >
                  </el-option>
                </el-select>
              </FormItem>
            </Form>
            <!-- 邮件告警附属功能 -->
            <div style="margin-left: 89px">
              <data-editor
                v-if="item.value === 8 && checked.includes(8)"
                append-to-body
                :formable="true"
                :popover-width="500"
                :updateable="updateable"
                :deletable="deleteable"
                :add-func="createEmail"
                :update-func="updateEmail"
                :delete-func="deleteEmail"
                :cancel-func="cancelEmail"
                :before-update="beforeUpdateEmail"
              >
                <Form ref="emailForm" :model="emailTemp" :rules="emailRules" label-position="right" label-width="85px" style="margin-left: -10px">
                  <FormItem :label="$t('table.emailName')" prop="name">
                    <el-input v-model="emailTemp.name" maxlength="32"/>
                  </FormItem>
                  <FormItem :label="$t('table.emailAddress')" prop="address">
                    <el-input v-model="emailTemp.address" maxlength="60"/>
                  </FormItem>
                  <FormItem :label="$t('table.remark')" prop="remark">
                    <el-input v-model="emailTemp.remark" type="textarea" show-word-limit maxlength="100"/>
                  </FormItem>
                </Form>
                <empty-mail-server-prompt slot="attach-btn"/>
              </data-editor>
              <grid-table
                v-if="item.value === 8 && checked.includes(8)"
                ref="emailTable"
                :height="150"
                :multi-select="true"
                :show-pager="false"
                :col-model="emailColModel"
                :row-datas="temp.emails"
                :autoload="false"
                style="margin-bottom: 10px;"
                @selectionChangeEnd="handleSelectionChange"
              />
            </div>
          </el-col>
        </el-row>
      </Form>
      <mail-import-table-dlg ref="mailImportTableDlg" @submitEnd="importEnd"/>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" :disabled="submitting" @click="handleSaveSyncAlarm()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>

</template>

<script>
import { getAlarmLimitDict } from '@/utils/dictionary'
import { getSyncAlarmConfig, updateSyncAlarmConfig } from '@/api/system/configManage/syncConfig'
import MailImportTableDlg from '@/views/system/baseData/groupImportList/mailImportTableDlg';
import EmptyMailServerPrompt from '@/views/system/deviceManage/mailServer/prompt'

export default {
  name: 'SyncAlarm',
  components: { EmptyMailServerPrompt, MailImportTableDlg },
  provide() {
    return {
      showImportAccDlg: () => this.$refs['mailImportTableDlg'] && this.$refs['mailImportTableDlg'].show(),
      importDlgBtnName: this.$t('pages.emailLibImport')
    }
  },
  data() {
    return {
      submitting: false,
      visible: false,
      options: getAlarmLimitDict().map(item => { item.checked = false; return item }).filter(item => { return [4, 8].includes(item.value) }),
      temp: {
      },
      defaultTemp: {
        id: undefined,
        // 4：同步告警
        bizType: 4,
        alarmFailed: false,
        alarmError: false,
        alarmFailedNum: 1,
        alarmErrorNum: 1,
        alarmTriggerFrequency: 10,
        alarmTriggerUpper: 3,
        alarmPop: false,
        alarmMail: false,
        // 控制台告警配置的管理员id
        sysUserIds: [],
        // 邮件告警配置的邮箱信息
        emails: []
      },
      defaultEmailTemp: {
        id: null,
        name: '',           // 邮箱名
        address: '',        // 邮箱地址
        remark: ''          // 备注
      },
      emailTemp: {},        // 新增邮箱信息的temp
      emailRules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }],
        address: [
          { required: true, type: 'email', message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.emailAddressCheck, trigger: 'blur' }
        ]
      },
      emailColModel: [
        { prop: 'name', label: 'emailName', width: '100', sort: true },
        { prop: 'address', label: 'emailAddress', width: '120', sort: true },
        { prop: 'remark', label: 'remark', width: '100', sort: true }
      ],
      deleteable: false,    // 邮箱删除按钮是否可用
      updateable: false,    // 邮箱修改按钮是否可用
      checked: []           // 响应规则勾选的配置选项
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  methods: {
    show(sourceType) {
      this.visible = true;
      this.initData()
    },
    initData() {
      getSyncAlarmConfig().then(res => {
        this.temp = Object.assign({}, this.defaultTemp, res.data)
        const alarmPop = this.temp.alarmPop;
        const alarmMail = this.temp.alarmMail;
        this.options.map(item => {
          if (item.value === 4 && alarmPop || item.value === 8 && alarmMail) {
            item.checked = true;
            this.handleCheckChangeValue(true, item.value)
            return item;
          } else {
            item.checked = false;
            return item;
          }
        })
        // if (alarmMode) {
        //   this.options.map(item => {
        //     if (item.value === alarmMode) {
        //       item.checked = true;
        //       this.handleCheckChangeValue(true, item.value)
        //       return item;
        //     } if (alarmMode === 12) {
        //       item.checked = true;
        //       this.handleCheckChangeValue(true, item.value)
        //       return item;
        //     } else {
        //       item.checked = false;
        //       return item;
        //     }
        //   })
        // }
      }).catch(e => { console.log(e) })
    },
    handleSaveSyncAlarm() {
      // let num = 0;
      // this.options.map(item => {
      //   if (item.checked) {
      //     num += Number(item.value);
      //   }
      // })
      // const postData = this.temp;
      // postData.alarmMode = num;
      const postData = this.temp;
      this.options.map(item => {
        if (item.checked) {
          if (item.value === 4) {
            postData.alarmPop = true;
          } else if (item.value === 8) {
            postData.alarmMail = true;
          }
        } else {
          if (item.value === 4) {
            postData.alarmPop = false;
          } else if (item.value === 8) {
            postData.alarmMail = false;
          }
        }
      })
      if (postData.alarmPop && (!postData.sysUserIds || postData.sysUserIds.length === 0)) {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.alarmSetup_text7'), type: 'error', duration: 2000 })
        return;
      }
      if (postData.alarmMail && (!postData.emails || postData.emails.length === 0)) {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.alarmSetup_text8'), type: 'error', duration: 2000 })
        return;
      }
      updateSyncAlarmConfig(postData).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.visible = false;
      }).catch(e => { console.log(e) })
    },
    // 响应规则各个选项勾选的方法，
    handleCheckChange(val, $event) {
      this.handleCheckChangeValue(val, $event.target._value);
    },
    handleCheckChangeValue(val, value) {
      // 勾选项的值
      const valueIndex = this.checked.indexOf(value)
      if (val) {
        valueIndex === -1 && this.checked.push(value)
        // 勾选 截屏、录屏 选项时，需要同时勾选 记录告警日志
        if ((value === 128 || value === 256) && !this.checked.includes(1)) {
          this.checked.push(1)
        }
      } else {
        valueIndex !== -1 && this.checked.splice(valueIndex, 1)
        // 取消勾选 记录告警日志 时，需要同时取消勾选 截屏、录屏
        if (value === 1) {
          this.checked = this.checked.filter(val => val !== 128 && val !== 256)
        }
      }
      if (value === 8) {
        this.refreshEmailList()
      }
    },
    handleDrag() {

    },
    // 刷新邮箱列表
    refreshEmailList() {
      this.$nextTick(() => {
        if (this.temp.emails.length > 0) {
          this.temp.emails = [...this.temp.emails.map((email, index) => { email.id = index + 1; return email })]
        }
      })
    },
    sysUserChange(selections) {
      const length = selections.length
      if (length > 1) {
        if (selections[length - 1] === 0 || length === this.sysUserList.length - 1) {
          // 当最后一个选择‘所有管理员’，或者选择了除‘所有管理员’的其他所有选项时，清空所有其他选项，只保留‘所有管理员’
          selections.splice(0, length, 0)
        } else if (selections[0] === 0) {
          // ‘所有管理员’在选项第一个时，去掉该选项
          selections.splice(0, 1)
        }
      }
    },
    resetEmailTemp() {
      this.emailTemp = JSON.parse(JSON.stringify(this.defaultEmailTemp))
    },
    emailTable() {
      return this.$refs['emailTable'][0]
    },
    emailForm() {
      return this.$refs['emailForm'][0]
    },
    // 新增邮箱数据
    createEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          rowData.id = new Date().getTime()
          this.temp.emails.unshift(rowData)
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 修改邮箱数据
    updateEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          for (let i = 0, size = this.temp.emails.length; i < size; i++) {
            const data = this.temp.emails[i]
            if (rowData.id === data.id) {
              this.temp.emails.splice(i, 1, rowData)
              break
            }
          }
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 删除邮箱数据
    deleteEmail() {
      const toDeleteIds = this.emailTable().getSelectedIds()
      this.temp.emails.splice(0, this.temp.emails.length, ...this.emailTable().deleteRowData(toDeleteIds))
      this.cancelEmail()
    },
    // 重置邮箱表单
    cancelEmail() {
      this.emailTable() && this.emailTable().setCurrentRow()
      this.emailForm() && this.emailForm().clearValidate()
      this.resetEmailTemp()
    },
    // 邮箱列表数据勾选
    handleSelectionChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.updateable = true
        // this.beforeUpdateEmail();
      } else {
        this.updateable = false
        this.cancelEmail()
      }
    },
    // 修改邮箱数据前，更新emailTemp数据
    beforeUpdateEmail() {
      this.emailTemp = Object.assign(this.emailTemp, this.emailTable().getSelectedDatas()[0])
    },
    numberLimit(value, item, type, min, max) {
      if (type === 'alarmTriggerUpper') {
        max = parseInt(1440 / item.alarmTriggerFrequency)
        item.alarmTriggerUpper = !value ? min : value > max ? max : value < min ? min : parseInt(value)
      } else if (type === 'alarmTriggerFrequency') {
        item.alarmTriggerFrequency = value > max ? max : value < min ? min : parseInt(value)
        const alarmMaxTimes = parseInt(1440 / item.alarmTriggerFrequency)
        item.alarmTriggerUpper = item.alarmTriggerUpper > alarmMaxTimes ? alarmMaxTimes : item.alarmTriggerUpper < 0 ? 0 : parseInt(item.alarmTriggerUpper)
      } else {
        if (max) {
          item[type] = value > max ? max : value < min ? min : parseInt(value)
        } else {
          item[type] = value < min ? min : parseInt(value)
        }
      }
    },
    emailAddressCheck(rule, value, callback) {
      const email = (this.temp.emails || []).find(email => email.address === value)
      if (email && email.id !== this.emailTemp.id) {
        callback(new Error(this.$t('pages.effectiveContent_text31')))
        return
      }
      callback()
    },
    importEnd(datas) {
      if (datas) {
        datas.forEach(item => {
          const { name, address, remark } = item
          const emails = this.temp.emails || []
          const time = new Date().getTime()
          const id = emails.length === 0 || time > emails[0].id ? time : emails[0].id + 1
          this.emailAddressCheck(null, address, info => {
            if (!info) {
              this.temp.emails.unshift({ id, name, address, remark })
            }
          })
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
>>>.el-divider__text.is-left {
  color: black;
}
</style>
