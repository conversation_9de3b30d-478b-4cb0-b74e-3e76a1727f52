<template>
  <div class="app-container global-config-container" style="overflow: auto;">
    <el-tabs ref="tabs" v-model="tabName" type="card" @tab-click="tabClick">
      <el-tab-pane :lazy="true" :label="$t('pages.basicConfig')" name="syncTab">
        <sync-config ref="syncTabConfig" :show-in-create="true" :hidden-source="hiddenSource" @toSource="changeToSourceTab"/>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.dataSourceConfiguration')" name="sourceTab">
        <source-config ref="sourceTabConfig" :hidden-source="hiddenSource"/>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.dataSourceSyncConflict')" name="syncConflictTab" style="height: 100%;">
        <sync-conflict ref="syncConflict" />
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.synchronizeLogs')" name="logTab" style="height: 100%;">
        <sync-log :hidden-source="hiddenSource"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SyncConfig from './syncConfig'
import SyncConflict from './syncConflict'
import SourceConfig from './sourceConfig'
import SyncLog from './syncLog';
import { getHidden3DataSource } from '@/api/property'

export default {
  name: 'DataSourceConfig',
  components: { SyncConfig, SyncConflict, SourceConfig, SyncLog },
  data() {
    return {
      tabName: 'syncTab',
      hiddenSource: [],
      showIDM: true,
      disabled: false
    }
  },
  watch: {
    '$store.state.commonData.notice.dataSourceConfig'(val) {
      this.tabName = 'syncConflictTab'
    }
  },
  created() {
    this.hiddenSource.splice(0)
    getHidden3DataSource().then(resp => {
      this.showIDM = resp.data.indexOf(3) < 0
      this.hiddenSource.splice(0, 0, ...resp.data)
    })
    this.changeToConflict();
  },
  methods: {
    tabClick(pane, event) {
      const dom = this.$refs[this.tabName + 'Config']
      if (dom && dom.show) {
        dom.show()
      }
    },
    changeToSourceTab(sourceType) {
      this.tabName = 'sourceTab';
    },
    changeToConflict() {
      if (this.$route.query && this.$route.query.tabName) {
        this.tabName = 'syncConflictTab'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-tabs{
    height: 100%;
    overflow: auto;
  }
  .global-config-container .el-tab-pane{
    height: calc(100% - 40px);
    overflow: auto;
  }
  .global-config-container .save{
    width:80px;
    position: absolute;
    bottom: 30px;
    right: 30px;
  }
  .app-container{
    padding: 10px 15px;
  }
</style>
