<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button icon="el-icon-setting" :disabled="!filterAble" size="mini" @click="handleSetDeptFilter">
          {{ $t('pages.handleSetDeptFilter') }}
        </el-button>
        <el-button icon="el-icon-setting" :disabled="!highConfigAble" size="mini" @click="handleSetHighConfig">
          {{ $t('button.highConfig') }}
        </el-button>
        <!--<el-tooltip effect="dark" content="支持[AD域]过滤" placement="top-start">
        </el-tooltip>-->
      </div>
      <grid-table ref="gridTableList" :row-key="getRowKey" :after-load="afterLoad" :show-pager="false" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <source-config-form ref="sourceConfigForm" :hidden-source="hiddenSource" @submitEnd="submitEndFun" />
    <sync-filter ref="syncFilter"/>
    <high-config ref="highConfig"/>
  </div>

</template>

<script>
import { updateSyncSource, testConnect, listSourceAndAd, deleteSourceAndAdByIds } from '@/api/system/configManage/syncConfig'
import GridTable from '@/components/GridTable'
import SourceConfigForm from '@/views/system/configManage/dataSourceConfig/sourceConfigForm';
import { sourceItems, syncSources } from './sourceItems'
import SyncFilter from './syncFilter';
import HighConfig from './highConfig';
import { aesDecode, aesEncode, formatAesKey } from '@/utils/encrypt';

export default {
  name: 'SourceConfig',
  components: { GridTable, SourceConfigForm, SyncFilter, HighConfig },
  directives: { },
  props: {
    hiddenSource: { // 隐藏的数据源
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      deleteable: false,
      dialogFormVisible: false,
      dialogStatus: '',
      colModel: [
        { prop: 'sourceType', label: this.$t('pages.dataSourceType'), width: '120', formatter: this.dataSourceFormatter, sort: true },
        { prop: 'name', label: this.$t('table.name'), width: '100' },
        { prop: 'config', label: this.$t('pages.configurationDetails'), width: '500', formatter: this.configFormatter },
        // { prop: 'synCycle', label: '创建时间', width: '150' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      temp: {},
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      rules: {
        host: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ]
      },
      filterAble: false,
      highConfigAble: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTableList']
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show() {
      this.resetTemp()
    },
    submit() {
      this.updateConfig()
    },
    submitEndFun() {
      this.gridTable.execRowDataApi()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp, { sourceType: this.sourceType })
    },
    handleDrag() {
    },
    formatFormData() {
      // 格式化同步数据源
      return Object.assign({}, this.defaultTemp, this.temp, {
        config: JSON.stringify(this.temp.config)
      })
    },
    validateFormData() {
      return true
    },
    updateConfig() {
      if (this.validateFormData()) {
        const postData = this.formatFormData()
        updateSyncSource(postData).then(res => {
          this.temp.syncId = res.data.syncId;
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
          this.$emit('submitEnd', res)
        }).catch(e => {
          this.$emit('submitEnd', e)
        })
      }
    },
    testConnect() {
      if (this.validateFormData()) {
        const postData = this.formatFormData()
        testConnect(postData).then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.connectSuccess'),
            type: 'success',
            duration: 2000
          })
          this.$emit('submitEnd', res)
        }).catch(e => {
          this.$emit('submitEnd', e)
        })
      }
    },
    handleCreate() {
      this.resetTemp()
      // this.dialogFormVisible = true
      // this.dialogStatus = 'create'
      this.$refs['sourceConfigForm'].show();
    },
    handleUpdate(row) {
      this.resetTemp()
      this.$refs['sourceConfigForm'].show(row);
    },
    handleDelete() {
      const datas = this.gridTable.getSelectedDatas();
      const params = [];
      datas.map(p => {
        const param = {
          syncId: p.syncId,
          sourceType: p.sourceType,
          name: p.name
        }
        params.push(param);
      })
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteSourceAndAdByIds(params).then(respond => {
          this.handleRefresh();
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    afterLoad(rows) {
      this.canSyn = (rows && rows.length > 0)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return listSourceAndAd(searchQuery).then(res => {
        res.data && res.data.forEach(item => { this.encryptDecryptConfig(item, false) })
        return res
      })
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0;
      // 增加synType同步类型判断，[过滤设置]暂时只支持 0:域组织架构
      this.filterAble = (rowDatas.length === 1)
      this.highConfigAble = (rowDatas.length === 1)
    },
    handleRefresh() {
      this.gridTable.execRowDataApi()
    },
    // 配置详情里的内容做中文转换
    configFormatter(row, data) {
      const config = JSON.parse(data)
      const sourceItem = sourceItems[row.sourceType];
      const configTmp = {};
      sourceItem.map(p => {
        if (null != config.hasOwnProperty(p.id)) {
          if ('password' === p.type) {
            configTmp[p.label] = '******'
          } else {
            configTmp[p.label] = config[p.id]
          }
        }
      })
      return JSON.stringify(configTmp);
    },
    handleSetDeptFilter() {
      const selectServer = this.gridTable.getSelectedDatas()[0];
      // AD域中只支持distinguishedName
      if (2 === selectServer.sourceType) {
        const config = JSON.parse(selectServer.config);
        if (config) {
          const synType = config.synType;
          if (1 === synType) {
            this.$notify({ title: this.$t('text.error'), message: this.$t('pages.syncAdOrgFilterTips'), type: 'error', duration: 2000 })
            return;
          }
        }
      }
      // const currentId = selectServer.syncId;
      // const sourceType = selectServer.sourceType;
      this.$refs['syncFilter'].show(selectServer);
    },
    handleSetHighConfig() {
      const selectServer = this.gridTable.getSelectedDatas()[0];
      console.log('select:', selectServer);
      this.$refs['highConfig'].show(selectServer);
    },
    dataSourceFormatter(row, data) {
      return syncSources[data];
    },
    encryptDecryptConfig(row, encrypt) {
      const data = JSON.parse(row.config)
      const field = []
      switch (row.sourceType) {
        case 2: field.push('password'); break
        case 3: field.push('appKey', 'encodeParam', 'esbSystemKey'); break
        case 4: field.push('secretKey'); break
        case 5: field.push('secretKey'); break
        case 7: field.push('password'); break
        case 8: field.push('secretKey'); break
        case 9: field.push('clientSecret'); break
      }
      if (field.length > 0) {
        for (const attribute of field) {
          if (data[attribute]) {
            if (encrypt) {
              data[attribute] = aesEncode(data[attribute], formatAesKey('tr838408', row.sourceType));
            } else {
              data[attribute] = aesDecode(data[attribute], formatAesKey('tr838408', row.sourceType));
            }
          }
        }
        row.config = JSON.stringify(data)
      }
    },
    getRowKey(row) {
      return row.syncId + '_' + row.sourceType
    }
  }
}
</script>

<style lang="scss" scoped>
.el-tabs{
  height: 100%;
  overflow: auto;
}
.global-config-container .el-tab-pane{
  height: calc(100% - 40px);
  overflow: auto;
}
.global-config-container .save{
  width:80px;
  position: absolute;
  bottom: 30px;
  right: 30px;
}
.global-config{
  padding: 30px;
  .el-form-item__label{
    color: #fff;
  }
}
.block{
  .el-form-item{
    margin-bottom: 5px;
    label{
      text-align: left;
      width: 160px;
    }
  }
  label{
    display: inline-block;
    /*text-align: right;*/
    /*width: 135px;*/
    font-size: 15px;
  }
}
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .addr {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}
.group >>>label{
  width: 40px !important;
}
//>>>.el-divider__text {
//  font-size: 15px;
//  font-weight: 700;
//  color: #eee;
//  background: #0c161f;
//}
</style>
