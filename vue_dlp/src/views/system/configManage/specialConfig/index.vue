<template>
  <div :class="isDlg ? '' : 'app-container'">
    <div class="table-container">
      <div v-if="!likeCode" class="toolbar" >
        <el-button type="primary" size="mini" @click="handleFileImport">
          {{ $t('layout.importAuthorizationFile') }}
        </el-button>
        <common-downloader
          :steps="3"
          :name="$t('table.pkey')"
          :button-name="$t('pages.exportSysKey')"
          button-size="mini"
          button-icon=""
          @download="handleExport"
        />
        <div class="searchCon">
          <el-input v-model="query.code" v-trim clearable :placeholder="$t('table.configItem')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="propList"
        :multi-select="false"
        :show-pager="false"
        row-key="code"
        :col-model="colModel"
        :height="isDlg ? 350 : null"
        :row-data-api="rowDataApi"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('layout.updateSetting')"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="50px" :extra-width="{ en: 90 }" style="width: 450px;">
        <FormItem :label="$t('table.configItem')" prop="code">
          <el-input v-model="temp.code" :maxlength="60" disabled />
        </FormItem>
        <FormItem :label="$t('table.configValue')" prop="value">
          <el-switch
            v-if="temp.code === 'supportCustom'"
            v-model="temp.value"
            :active-value="1"
            :inactive-value="0"
          />
          <template v-else-if="temp.code === 'auditing.query.range'">
            <el-radio v-model="temp.value.radio" label="30">{{ $t('pages.oneMonth') }}</el-radio>
            <br>
            <el-radio v-model="temp.value.radio" label="1">{{ $t('pages.specifiedDays') }}</el-radio>
            <el-input-number
              v-model="temp.value.input"
              :disabled="temp.value.radio !== '1'"
              :min="1"
              :max="31"
              step-strictly
              controls-position="right"
              :style="{ float: 'right', width: ($store.getters.language === 'en' ? 164 : 290) + 'px' }"
            />
          </template>
          <el-switch
            v-else-if="temp.type === 'boolean'"
            v-model="temp.value"
            :active-value="rule.truthy == null ? 'true' : rule.truthy"
            :inactive-value="rule.falsy == null ? 'false' : rule.falsy"
          />
          <el-input-number
            v-else-if="temp.type === 'number' || temp.type ==='Integer'"
            v-model="temp.value"
            :min="rule.min || 0"
            :max="rule.max || Number.MAX_SAFE_INTEGER"
            :step="rule.step || 1"
            :step-strictly="rule.strict"
            :precision="rule.precision || 0"
            controls-position="right"
          />
          <el-input
            v-else-if="temp.type === 'string'"
            v-model="temp.value"
            :type="rule.type || 'text'"
            :minlength="rule.minlength || 0"
            :maxlength="rule.maxlength"
            :show-password="rule.type === 'password'"
            :rows="3"
            show-word-limit
          />
          <el-checkbox-group v-else-if="temp.type === 'enum' && rule.multiple" v-model="temp.value">
            <el-checkbox v-for="(label, key) in (rule.items || {})" :key="key" :label="key">{{ label }}</el-checkbox>
          </el-checkbox-group>
          <el-radio-group v-else-if="temp.type === 'enum' && !rule.multiple" v-model="temp.value">
            <el-radio v-for="(label, key) in (rule.items || {})" :key="key" :label="key">{{ label }}</el-radio>
          </el-radio-group>
          <div v-else-if="temp.type === 'datetime'">
            <el-time-picker
              v-if="rule.type === 'time' || rule.type === 'timerange'"
              v-model="temp.value"
              range-separator="~"
              :is-range="rule.type === 'timerange'"
              :clearable="false"
              :value-format="rule.format"
            />
            <el-date-picker
              v-else
              v-model="temp.value"
              range-separator="~"
              :type="rule.type"
              :clearable="false"
              :value-format="rule.format"
            />
          </div>
          <el-input v-else v-model="temp.value" :maxlength="20" show-word-limit/>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" :rows="3" resize="none" :maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateAndConfirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('layout.importAuthorizationFile2')"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFileVisible"
      width="500px"
    >
      <Form ref="fileDataForm" :rules="fileRules" :model="fileTemp" label-position="right" label-width="90px" style="width: 450px;">
        <el-row>
          <el-col :span="18">
            <FormItem :label="$t('table.file')" prop="fileName">
              <el-input v-model="fileTemp.fileName" readonly :placeholder="$t('layout.importAuthorizationFile3')"/>
            </FormItem>
          </el-col>
          <el-col :span="4">
            <el-upload
              ref="upload"
              accept=".ini,.ldk"
              class="upload-demo"
              name="uploadFile"
              action="aaaaaa"
              :show-file-list="false"
              :on-change="beforeUpload"
              :file-list="fileList"
              :auto-upload="false"
            >
              <el-button size="small" type="primary">{{ $t('button.upload') }}</el-button>
            </el-upload>
          </el-col>
        </el-row>
      </Form>
      <p style="color: rgb(43, 122, 172);font-size: small;margin-left: 50px;">
        <strong>{{ $t('text.prompt') }}：</strong>
        {{ $t('layout.stgIssue_Msg2') }}
      </p>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateFile">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFileVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :title="titleMap[titleStatus]"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFileDetailVisible"
      width="500px"
    >
      <p style="white-space: pre-wrap; font-size: 14px; line-height: normal;">{{ content }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="titleStatus && !nothingChange" :loading="submitting" type="primary" @click="grantAuth">
          {{ $t('table.grantAuthor') }}
        </el-button>
        <el-button v-if="titleStatus && !nothingChange" @click="dialogFileDetailVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
        <el-button v-if="!titleStatus || nothingChange" @click="dialogFileDetailVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
    <main-key-file ref="mainKeyFile"/>
    <import-org
      ref="importOrgDlg"
      :title="$t('pages.ldOrganizationalStructure')"
      accept=".base"
      :tip="$t('layout.stgIssue_Msg3')"
      :show-import-type="false"
      :show-import-way="true"
      :upload-func="uploadOrgFunc"
    />
  </div>
</template>

<script>
import { formatMenuCode } from '@/router'
import { getPropertyPage, updateProperty } from '@/api/property';
import { uploadFile, grantAuthIni, grantAuthLdk } from '@/api/grantAuth';
import MainKeyFile from '@/layout/components/MainKeyFile'
import ImportOrg from '@/views/common/importOrg'
import request from '@/utils/request'
import CommonDownloader from '@/components/DownloadManager/common'
import {
  changeSyncTime, enableSync
} from '@/api/dataEncryption/encryption/dlpCloudOutFile'
export default {
  name: 'SpecialConfig',
  components: { MainKeyFile, ImportOrg, CommonDownloader },
  props: {
    likeCode: {
      type: String,
      default: null
    },
    exceptCode: {
      type: String,
      default: 'log.level'
    },
    exceptCode1: {
      type: String,
      default: 'sys.conf'
    },
    isDlg: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'code', label: 'configItem', width: '150', sort: true },
        { prop: 'value', label: 'configValue', width: '200' },
        { prop: 'remark', label: 'remark', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'edit', isShow: this.editShowFilter, formatter: this.editFormatter, click: this.handleUpdate },
            { label: 'goTo', formatter: this.sysConfFormatter, click: this.skipHandler }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        code: '',
        showAsTree: true,
        exceptCode: '',
        exceptCode1: ''
      },
      temp: {},
      fileTemp: {
        fileName: ''
      },
      textTemp: {
        type: '',
        OfflineLockScreen: '',
        CompetitorsRights: '',
        CompetitorsDeadline: '',
        CompetitorsRightsEx: '',
        ResRights: '',
        TranEncrypt: '',
        moduleName: '',
        authNum: undefined,
        oldAuthNum: undefined
      },
      rule: undefined,
      dialogVisible: false,
      dialogFileVisible: false,
      dialogFileDetailVisible: false,
      dialogFormVisible: false,
      submitting: false,
      activeTab: 'configTab',
      rules: {
        // value: [{ required: true, message: '请输入配置值', trigger: 'blur' }],
        // remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
      },
      fileRules: {
        fileName: [
          { required: true, message: this.$t('pages.signatureData_text2'), trigger: 'blur' }
        ]
      },
      showHidden: false,
      fileList: [],
      isArray: false,
      moduleFileName: '',
      uploadVisible: false,
      fileName: '',
      fileSubmitting: false,
      fileSaved: false,
      title: '',
      content: '',
      grantAuthTypeOptions: {
        1: this.$t('text.open'),
        2: this.$t('pages.change'),
        3: this.$t('text.close')
      },
      menuOptions: {
        OfflineLockScreen: this.$t('pages.OfflineLockScreen'),
        CompetitorsRights: this.$t('pages.CompetitorsRights'),
        CompetitorsDeadline: this.$t('pages.EncryDeadline'),
        ResRights: this.$t('pages.ResRights'),
        TranEncrypt: this.$t('pages.TranEncrypt'),
        EncryAuthorize: this.$t('pages.EncryAuthorize'),
        EncryDeadline: this.$t('pages.EncryDeadline'),
        CADRights: this.$t('pages.CADRights'),
        RemoteHelp: this.$t('pages.vnc_Content16'),
        RemoteCmd: this.$t('pages.cmdCommand_Msg36'),
        EncryAuthorizeFile: this.$t('pages.interface_auth_type1'),
        EncryAuthorizeSvr: this.$t('pages.interface_auth_type2'),
        EncryAuthorizeAnyData: this.$t('pages.interface_auth_type3')
      },
      titleMap: {
        0: this.$t('pages.errorMessage'),
        1: this.$t('pages.sysAuthorizationInfo'),
        2: this.$t('pages.sysAuthorizationChangeDetails'),
        3: this.$t('pages.CADAuthorizationInfo'),
        4: this.$t('pages.CADAuthorizationChange')
      },
      titleStatus: 0,
      nothingChange: false,
      noSearchRouterMap: {                                                    // 无法通过搜索跳转的路由 map
        122: 'SeamlessReplace',
        123: 'InterfaceAuth',
        129: 'Scheduled',
        131: 'SignatureData',
        132: 'ProcessInject',
        133: 'LvDun',
        134: 'AuthorizeUninstall',
        126: 'LangExport',
        G22: 'SensitiveConfig',
        ResourceBuilder: 'ResourceBuilder'
      },
      freshMenu: true,
      adminLogData: '',
      globalConfigCode: ['supportCustom', 'userValidTime', 'fileSumSize']
    };
  },
  computed: {
    gridTable() {
      return this.$refs['propList']
    }
  },
  beforeRouteEnter(to, from, next) {
    const isPageLoaded = window.getMenuAccessStatus('SpecialConfig')
    const isRefresh = from.path == '/redirect/config/specialConfig'
    // 通过 搜索、已访问过、右键菜单刷新
    if (to.params.search || isPageLoaded || isRefresh) {
      next()
    } else {
      next('/404')
    }
  },
  created() {
    this.setMenuAccessStatus('SpecialConfig')
    document.addEventListener('keyup', this.handleKeyUp)
  },
  methods: {
    parseRule(rule) {
      if (typeof rule !== 'string' || rule.trim().length === 0) {
        return {}
      }
      try {
        return JSON.parse(rule)
      } catch (e) {
        console.log(e)
        return {}
      }
    },
    notifySuccess(msg) {
      this.$notify({
        title: this.$t('text.success'),
        message: msg,
        type: 'success',
        duration: 2000
      })
    },
    notifyFail(msg) {
      this.$notify({
        title: this.$t('text.fail'),
        message: msg,
        type: 'error',
        duration: 2000
      })
    },
    handleKeyUp(e) {
      if (e.ctrlKey && e.altKey && e.shiftKey) {
        if (e.key === 's' || e.key === 'S') {
          this.dialogVisible = true
        }
        e.preventDefault()
      }
      if (e.ctrlKey && e.altKey && e.shiftKey) {
        if (e.key === 'k' || e.key === 'K') {
          this.showHidden = true
        }
        e.preventDefault()
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleExport(file) {
      this.$refs.mainKeyFile.handleExport(file)
    },
    handleLdOrgImport() {
      this.$refs.importOrgDlg.show()
    },
    uploadOrgFunc(data) {
      return request.post('/department/importOrg', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    handleFileImport() {
      this.fileTemp = {
        fileName: ''
      }
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles()
        this.$refs['fileDataForm'].clearValidate()
      })
      this.submitting = false
      this.dialogFileVisible = true
    },
    handleCADFileImport() {
      this.moduleFileName = ''
      this.uploadVisible = true
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (this.exceptCode) {
        searchQuery.exceptCode = this.exceptCode
      }
      if (this.likeCode) {
        searchQuery.code = this.likeCode
      }
      // 授权后，界面未重新加载，不查询sys.conf开头的授权信息
      if (!this.freshMenu) {
        searchQuery.exceptCode1 = this.exceptCode1
      }
      return getPropertyPage(searchQuery);
    },
    handleUpdate(row) {
      this.submitting = false
      this.temp = Object.assign({}, row)
      if (row.code === 'hidden.menu' && this.temp.rule.trim().length > 0) {
        this.temp.rule = this.formatMenu(this.temp.rule)
      } else if (row.code === 'fileSumSize') {
        // 最小值1M，单步1M
        this.temp.rule = JSON.stringify({ step: 1048576, min: 1048576, max: 2147483648 })
      }
      this.rule = this.parseRule(this.temp.rule)
      if (row.code === 'supportCustom') {
        this.temp.value = parseInt(row.value)
      } else if (row.code === 'auditing.query.range') {
        const value = (row.value || '1,30').toString().split(',')
        this.temp.value = { input: value[0], radio: value[1] || '1' }
      } else if ((this.temp.type === 'enum' && this.rule.multiple)) {
        this.temp.value = this.temp.value === '' ? [] : this.temp.value.split(',')
      } else if (this.temp.type === 'datetime' && this.rule.type && this.rule.type.endsWith('range')) {
        this.temp.value = this.temp.value.split('~')
      }
      //  判断items是否为数组
      this.isArray = Array.isArray(this.rule.items)
      this.dialogFormVisible = true
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
    },
    formatMenu(data) {
      return data.replace(this.$t('pages.sensitiveOutgoingSendConfig'), this.$t('route.configwjwf'))
    },
    updateFile() {
      this.submitting = true
      this.$refs['fileDataForm'].validate((valid) => {
        if (valid) {
          const fd = new FormData()
          // const file = this.$refs['upload']
          const uploadFiles = this.$refs['upload'].uploadFiles
          fd.append('uploadFile', uploadFiles[uploadFiles.length - 1].raw)
          uploadFile(fd).then(res => {
            this.submitting = false
            this.titleStatus = 0
            this.content = ''
            this.nothingChange = false
            const result = res.data
            this.adminLogData = JSON.stringify(result)
            this.dialogFileVisible = false
            const type = result.type
            if (type === 999) {
              // 授权文件上传失败
              this.content = this.$t('pages.authorizationUploadFailed')
            } else if (type === 99) {
              // 该文件为非法文件
              this.content = this.$t('pages.illegalFile')
            } else if (type === 1) {
              // 格式化授权信息变更描述
              // const { opeType, OfflineLockScreen, CompetitorsRights, CompetitorsDeadline, ResRights, TranEncrypt, EncryDeadline, EncryAuthorize } = result
              this.titleStatus = result.opeType === 1 ? 1 : 2
              const OfflineLockScreen = result.OfflineLockScreen ? this.grantAuthTypeOptions[result.OfflineLockScreen] + this.menuOptions.OfflineLockScreen + '\n' : ''
              const CompetitorsDeadline = result.CompetitorsRights == 3 ? '' : ',' + this.menuOptions.CompetitorsDeadline + result.CompetitorsDeadline
              const CompetitorsRights = result.CompetitorsRights ? this.grantAuthTypeOptions[result.CompetitorsRights] + this.menuOptions.CompetitorsRights + CompetitorsDeadline + '\n' : ''
              const ResRights = result.ResRights ? this.grantAuthTypeOptions[result.ResRights] + this.menuOptions.ResRights + '\n' : ''
              const TranEncrypt = result.TranEncrypt ? this.grantAuthTypeOptions[result.TranEncrypt] + this.menuOptions.TranEncrypt + '\n' : ''

              // 加解密授权接口有三种权限
              let EncryAuthorizeChange = ''
              if (result.EncryAuthorize || result.EncryAuthorize_AnyData || result.EncryAuthorize_svr) {
                if (result.EncryAuthorize == 3 && result.EncryAuthorize_AnyData == 3 && result.EncryAuthorize_svr == 3) {
                  EncryAuthorizeChange = this.grantAuthTypeOptions[3] + this.menuOptions.EncryAuthorize
                } else {
                  EncryAuthorizeChange = this.grantAuthTypeOptions[2] + this.menuOptions.EncryAuthorize + ':\n  '
                  // 文件版本授权
                  if (result.EncryAuthorize) {
                    const encryDeadline = result.EncryDeadline ? result.EncryDeadline : this.$t('pages.permanentEffect')
                    const EncryDeadline = result.EncryAuthorize == 3 ? '' : ',' + this.menuOptions.EncryDeadline + encryDeadline
                    EncryAuthorizeChange = EncryAuthorizeChange + this.grantAuthTypeOptions[result.EncryAuthorize] + this.menuOptions.EncryAuthorizeFile + this.$t('pages.grantAuthor') + EncryDeadline + '\n  '
                  }
                  // 服务版本授权
                  if (result.EncryAuthorize_svr) {
                    const encryDeadline = result.EncryDeadline_svr ? result.EncryDeadline_svr : this.$t('pages.permanentEffect')
                    const EncryDeadline = result.EncryAuthorize_svr == 3 ? '' : ',' + this.menuOptions.EncryDeadline + encryDeadline
                    EncryAuthorizeChange = EncryAuthorizeChange + this.grantAuthTypeOptions[result.EncryAuthorize_svr] + this.menuOptions.EncryAuthorizeSvr + this.$t('pages.grantAuthor') + EncryDeadline + '\n  '
                  }
                  // 爱数版本授权
                  if (result.EncryAuthorize_AnyData) {
                    const encryDeadline = result.EncryDeadline_AnyData ? result.EncryDeadline_AnyData : this.$t('pages.permanentEffect')
                    const EncryDeadline = result.EncryAuthorize_AnyData == 3 ? '' : ',' + this.menuOptions.EncryDeadline + encryDeadline
                    EncryAuthorizeChange = EncryAuthorizeChange + this.grantAuthTypeOptions[result.EncryAuthorize_AnyData] + this.menuOptions.EncryAuthorizeAnyData + this.$t('pages.grantAuthor') + EncryDeadline + '\n'
                  }
                }
              }

              const RemoteHelp = result.RemoteHelp ? this.grantAuthTypeOptions[result.RemoteHelp] + this.menuOptions.RemoteHelp + '\n' : ''
              const RemoteCmd = result.RemoteCmd ? this.grantAuthTypeOptions[result.RemoteCmd] + this.menuOptions.RemoteCmd + '\n' : ''

              this.content = OfflineLockScreen + CompetitorsRights + ResRights + RemoteHelp + RemoteCmd + TranEncrypt + EncryAuthorizeChange
              if (!this.content) {
                this.nothingChange = true
                // 系统授权无任何变更信息
                this.content = this.$t('pages.sysAdvancedConf_Msg1')
              }
            } else if (type === 2) {
              const { authNum, oldAuthNum, opeType } = result
              this.titleStatus = opeType === 1 ? 3 : 4
              if (!authNum) {
                // 关闭移动端CAD权限模块
                this.content = this.$t('pages.sysAdvancedConf_Msg2')
              } else if (opeType === 1) {
                // 开启移动端CAD权限模块，授权点数为{num}
                this.content = this.$t('pages.sysAdvancedConf_Msg3', { num: authNum })
              } else {
                // 移动端CAD权限模块点数由{oldAuthNum}变更为{authNum}
                this.content = this.$t('pages.sysAdvancedConf_Msg4', { oldAuthNum, authNum })
              }
            }
            this.dialogFileDetailVisible = true
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    grantAuth() {
      this.submitting = true
      this.freshMenu = false
      if (this.titleStatus === 1 || this.titleStatus === 2) {
        grantAuthIni({ adminLogData: this.adminLogData }).then(res => {
          this.dialogFileDetailVisible = false
          this.submitting = false
          // this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.authorizeSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      } else {
        grantAuthLdk({ fileName: this.fileTemp.fileName, adminLogData: this.adminLogData }).then(res => {
          this.$socket.subscribeToAjax(res, 'notifyModuleChange/result', (respond, handle) => {
            // 得到异步结果
            handle.close()
            this.dialogFileDetailVisible = false
            this.submitting = false
            // this.handleFilter()
            const data = respond.data
            if (data.code === 1) {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.authorizeSuccess'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: this.$t('text.fail'),
                message: data.msg,
                type: 'error',
                duration: 2000
              })
            }
          })
        })
      }
    },
    updateAndConfirm() {
      if ('log.delete.able' === this.temp.code && this.temp.value && this.temp.value.length > 0) {
        this.$confirmBox('', {
          title: this.$t('text.prompt'),
          message: '国家法律法规要求日志留存时间不少于六个月，您确定允许删除日志记录吗？',
          type: 'warning'
        }).then(() => {
          this.updateData()
        })
      } else {
        this.updateData()
      }
    },
    isValidTime(timeString) {
      // 正则表达式匹配HH:mm:ss格式
      const regex = /^([01]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;
      if (!regex.test(timeString)) {
        return false;
      }
      // 分割字符串获取小时、分钟、秒
      const [hours, minutes, seconds] = timeString.split(':').map(str => parseInt(str, 10));
      // 检查小时、分钟、秒是否在有效范围内
      if (isNaN(hours) || isNaN(minutes) || isNaN(seconds) ||
         hours < 0 || hours > 23 ||
        minutes < 0 || minutes > 59 ||
        seconds < 0 || seconds > 59) {
        return false;
      }
      return true;
    },
    updateData() {
      if (this.temp.code === 'config.out.file.sync') {
        const syncTime = this.temp.value
        if (syncTime && !this.isValidTime(syncTime)) {
          this.$message({
            message: '请输入正确的时间格式',
            type: 'error',
            duration: 2000
          })
          return
        }
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let value = this.temp.value
          if (this.temp.code === 'auditing.query.range') {
            value = this.temp.value.input + ',' + this.temp.value.radio
          } else if (Array.isArray(this.temp.value)) {
            value = this.temp.value.join(this.temp.type === 'enum' ? ',' : '~')
          }
          const tempData = {
            code: this.temp.code,
            value,
            remark: this.temp.remark,
            source: this.globalConfigCode.indexOf(this.temp.code) >= 0 ? 2 : null // 部分配置来自global_config,2:global_bal,1:property
          }
          updateProperty(tempData).then(_ => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.notifySuccess(this.$t('text.updateSuccess'))
            // 属性修改后，需要实时刷新浏览器缓存数据时，执行后续操作
            this.$store.dispatch('user/loadIsMasterSub')
            this.$store.dispatch('permission/addEnableConfig')
            // 云平台外发文件管理需要定时从云平台同步外发文件数据，且支持用于修改定时同步的执行时间，因此对于config.out.file.sync，在更新成功后，需要额外去通知后台更新定时执行时间
            if (tempData.code === 'config.out.file.sync') {
              changeSyncTime(tempData.value).then(resp => {})
            }
            if (this.temp.code === 'enable.out.file.sync') {
              // 修改云平台外发文件管理是否开启定时同步后，需要通知后台去相应的开启或关闭
              enableSync(tempData.value).then(resp => {})
            }
          }).catch(_ => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    beforeUpload(file, fileList) {
      this.fileTemp.fileName = file.name
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    fileNameValid(rule, value, callback) {
      if (this.fileTemp.fileName != 'ArResRights.ini') {
        callback(new Error(this.$t('layout.ArResRights')))
      } else {
        callback()
      }
    },
    skipHandler(row) {
      if (this.noSearchRouterMap[row.value]) {
        // 无法通过搜索跳转的路由，使用 路由name + 参数 进行跳转
        this.$router.push({
          name: this.noSearchRouterMap[row.value],
          params: { search: true }
        })
      } else {
        formatMenuCode(this.$store.getters.permission_routes, row, 'value')
        if (row.editUrl) {
          this.$router.push({ path: row.editUrl })
        } else {
          // 排查问题提示
          this.$message({
            message: '未配置跳转路由！',
            type: 'error',
            duration: 2000
          })
        }
      }
    },
    editShowFilter(data) {
      return data.code.substring(0, 8) !== 'sys.conf' && (!data.children || data.children.length === 0)
    },
    editFormatter: function(data) {
      if (data.code.substring(0, 8) === 'sys.conf' || data.editable == '0') {
        return ''
      }
      return this.$t('table.edit')
    },
    sysConfFormatter: function(data) {
      if (data.code.substring(0, 8) !== 'sys.conf' || !this.hasPermission(data.value)) {
        return ''
      }
      return this.$t('table.goTo')
    }
  }
}
</script>
