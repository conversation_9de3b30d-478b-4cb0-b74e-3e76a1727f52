<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <label v-if="noOutSend" style="color:#fd2222;font-size:13px;;margin-left:80px">{{ $t('pages.emailClosed') }}</label>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <approve-outgoing-email-setting-dlg ref="stgDlg" @submitEnd="submitEnd"/>
  </div>
</template>

<script>
import { enableStgBtn, objectFormatter, entityLink, refreshPage, enableStgDelete } from '@/utils'
import { deleteData, getPage } from '@/api/system/configManage/approvalOutgoingEmailSetting';
import ApproveOutgoingEmailSettingDlg from './dlg/editDlg'
import { getApprovalConfig } from '@/api/system/configManage/approvalConfig.js'

export default {
  name: 'ApproveOutgoingEmailSetting',
  components: { ApproveOutgoingEmailSettingDlg },
  props: {
  },
  data() {
    return {
      colModel: [
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'stgInfo', label: 'stgMessage', width: '100', formatter: this.stgInfoFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'details', isShow: (data) => { return this.query.objectType && !this.showDetails(data) }, click: this.handleShow },
            { label: 'edit', isShow: (data) => { return this.query.objectType && this.showDetails(data) }, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      checkedEntityNode: {},
      showTree: true,
      treeable: true,
      deleteable: false,
      noOutSend: false // 是否关闭了邮件外发
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTable']
    }
  },
  created() {
    getApprovalConfig().then(res => {
      if (res.data.ldFiledecryptSendmail == true) {
        this.noOutSend = true
      } else {
        this.noOutSend = false
      }
    })
  },
  methods: {
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    selectable(row, index) {
      return this.query.objectType && this.showDetails(row)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    /**
     * 刷新列表
     */
    refresh() {
      this.query.entityType = null
      this.query.entityId = null
      return refreshPage(this)
    },
    /**
     * 列表数据获取
     * @param option
     * @returns {AxiosPromise}
     */
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      //  若未选中操作员分组树的节点时，进行分页查询，若有选中时，查找指定生效对象记录
      if (searchQuery.objectType == 4) {
        searchQuery.deptId = searchQuery.objectId;
      } else if (searchQuery.objectType == 2) {
        searchQuery.userId = searchQuery.objectId;
      }
      return getPage(searchQuery);
    },
    /**
     * 分页查询加载之后
     * @param rowData
     * @param grid
     */
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    /**
     * 创建配置信息
     */
    handleCreate() {
      this.$refs['stgDlg'].handleCreate(this.checkedEntityNode)
    },
    /**
     * 更新配置信息
     */
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    /**
     * 查询配置信息
     */
    handleShow(row) {
      this.$refs['stgDlg'].handleShow(row)
    },
    /**
     * 删除配置信息
     */
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteDatas = this.gridTable.getSelectedDatas() || []
        const deptIds = []
        const userIds = []
        toDeleteDatas.forEach(item => {
          item.deptId && deptIds.push(item.deptId)
          item.userId && userIds.push(item.userId)
        })
        deleteData({ deptId: deptIds.join(','), userId: userIds.join(',') }).then(respond => {
          this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    /**
     * 是否可查看详情
     */
    showDetails(row) {
      //  是否为继承上级的数据
      return (this.query.objectType == 2 && this.query.objectId === row.userId) || (this.query.objectType == 4 && this.query.objectId === row.deptId)
    },
    /**
     * 操作员分组树选中事件
     */
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    /**
     * 查询
     */
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    /**
     * 配置信息格式化
     * @param row
     */
    stgInfoFormatter(row) {
      let result = ''
      result += (row.isInherit && (!row.objectGroupIds || !row.objectGroupIds.includes(0)) ? this.$t('pages.extendsParentDeptInfo') : this.$t('pages.notExtendsParentDeptInfo')) + '；'
      //  接收人邮箱信息
      if (row.mailId) {
        result += this.$t('pages.configuredReceiverEmailInfo') + '；'
      }
      //  邮箱模板信息
      if (row.templateId) {
        result += this.$t('pages.configuredTemplateEmailInfo') + '；'
      }
      //  发送邮箱信息
      if (row.sysMailId) {
        result += this.$t('pages.configuredSendEmailInfo') + '；'
      }
      return result;
    },
    /**
     * 添加成功
     */
    submitEnd(data, operator) {
      this.handleFilter()
    }
  }
}
</script>
<style lang='scss' scoped>
</style>

