<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
      @close="cancel"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-width="80px">
        <FormItem :label="$t('table.templateName')" prop="textName">
          <el-input v-model="temp.textName" maxlength="100" show-word-limit/>
        </FormItem>
        <FormItem v-if="groupAddAble" :label="$t('form.group')" prop="groupId">
          <tree-select
            ref="groupTree"
            node-key="dataId"
            :data="mailTree"
            :checked-keys="groupCheckedKeys"
            :icon-option="iconOption"
            :width="296"
            style="width: calc(100% - 38px); display: inline-block;"
            @change="mailTreeSelectChange"
          />
          <el-button style="margin: 2px 0 0;" :title="$t('pages.addType')" class="editBtn" @click="handleEmailGroupCreate"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <FormItem v-else :label="$t('form.group')" prop="groupId">
          <tree-select
            ref="groupTree"
            node-key="dataId"
            :data="mailTree"
            :checked-keys="groupCheckedKeys"
            :icon-option="iconOption"
            :width="296"
            @change="mailTreeSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('table.mailTitle')" prop="textTitle">
          <el-input v-model="temp.textTitle" maxlength="100" show-word-limit/>
        </FormItem>
        <FormItem :label="$t('table.emailContent')" prop="textContent">
          <el-input v-model="temp.textContent" type="textarea" :rows="3" maxlength="500" show-word-limit/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      append-to-body
      :title="$t('pages.group')"
      :group-tree-data="mailTree"
      :add-func="createTemplateEmailInfoGroup"
      :update-func="updateTemplateEmailInfoGroup"
      :delete-func="deleteTemplateEmailInfoGroup"
      :edit-valid-func="getTemplateEmailInfoGroupByName"
      @addEnd="createEmailGroupSuccess"
    />
  </div>
</template>

<script>
import {
  addTemplateEmailInfo,
  updateTemplateEmailInfo,
  getTemplateEmailInfoGroupTree,
  createTemplateEmailInfoGroup,
  updateTemplateEmailInfoGroup,
  deleteTemplateEmailInfoGroup,
  getTemplateEmailInfoGroupByName,
  getTemplateEmailInfoByTemplateName
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import EditGroupDlg from '@/views/common/editGroupDlg'
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'EmailTemplateInfoSettingDlg',
  components: { EditGroupDlg },
  props: {
    //  分组是否支持添加
    groupAddAble: { type: Boolean, default: true }
  },
  data() {
    return {
      title: '',
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        templateId: null, //  模版ID
        textName: '',  //  模版名称 (不能重复)
        textTitle: '',  //  邮件标题
        groupId: null, //  所属分组ID
        textContent: ''  //  邮件内容
      },
      rules: {
        textName: [{ required: true, message: this.$t('pages.pleaseModuleName'), trigger: 'blur' }, { validator: this.textNameValidator, trigger: 'blur' }],
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'blur' }]
      },
      mailTree: [], //  邮件模板信息树
      operator: '',  //  操作 create、update
      groupCheckedKeys: [],  //  选中的分组
      iconOption: {
        typeKey: 'oriData',
        1: ''
      }
    }
  },
  methods: {
    createTemplateEmailInfoGroup,
    updateTemplateEmailInfoGroup,
    deleteTemplateEmailInfoGroup,
    getTemplateEmailInfoGroupByName,
    /**
     * 初始化数据
     */
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
      this.groupCheckedKeys.splice(0)
    },
    /**
     * 加载邮件模板信息分组数据
     */
    loadMailTree() {
      return getTemplateEmailInfoGroupTree().then(res => {
        this.mailTree = res.data || []
        return res;
      })
    },
    /**
     * 校验模板名称
     * @param rule
     * @param value
     * @param callback
     */
    textNameValidator(rule, value, callback) {
      //  校验模板名称是否重复，如果重复则不允许添加
      getTemplateEmailInfoByTemplateName({ textName: value }).then(respond => {
        const data = respond.data
        if (data && data.templateId != this.temp.templateId) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    /**
     * 格式化数据
     * @param temp
     * @returns {any}
     */
    formatRow(temp) {
      return JSON.parse(JSON.stringify(temp));
    },
    /**
     * 确认
     * 添加成功后，加入到邮件模板信息库中，也加入到列表中
     */
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitting = true
          const rowData = this.formatRow(this.temp);
          this.formatGroupName(rowData)
          const fun = this.operator === 'create' ? addTemplateEmailInfo : updateTemplateEmailInfo;
          fun(rowData).then(res => {
            this.submitting = false
            this.$emit('submit', res.data, this.operator);
            this.close()
          }).catch(t => { this.submitting = false })
        }
      });
    },
    /**
     * 取消
     */
    cancel() {
      this.close()
    },
    /**
     * 关闭窗口
     */
    close() {
      this.$refs.dataForm.clearValidate()
      this.visible = false;
    },
    /**
     * 新增操作
     * @param groupId
     */
    async handleCreate(groupId) {
      await this.loadMailTree();
      this.visible = true
      this.initData()
      if (groupId) {
        this.temp.groupId = groupId
        this.$nextTick(() => {
          this.groupCheckedKeys.push(groupId + '')
        })
      }
      this.operator = 'create'
      this.title = this.$t('pages.addEmailTemplateInfo')
    },
    /**
     * 更新操作
     * @param row
     */
    handleUpdate(row) {
      this.visible = true
      this.initData()
      this.temp = Object.assign(this.temp, row);
      this.operator = 'update'
      this.title = this.$t('pages.updateEmailTemplateInfo')
      this.loadMailTree().then(res => {
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      })
    },
    /**
     * 新增邮箱分组
     */
    handleEmailGroupCreate() {
      this.$refs['editGroupDlg'].handleCreate()
    },
    /**
     * 邮箱分组选中事件
     * @param data
     * @param node
     * @param vm
     */
    mailTreeSelectChange(data, node, vm) {
      if (data) {
        this.temp.groupId = data
        this.groupCheckedKeys.splice(0)
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      }
    },
    /**
     * 新增邮箱分组成功
     * @param tempData
     */
    createEmailGroupSuccess(tempData) {
      this.loadMailTree().then(res => {
        this.temp.groupId = tempData.id
        this.groupCheckedKeys.splice(0)
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      })
    },
    formatGroupName(rowData) {
      rowData.groupName = findNodeLabel(this.mailTree, rowData.groupId, 'dataId')
    }
  }
}
</script>

<style scoped>

</style>
