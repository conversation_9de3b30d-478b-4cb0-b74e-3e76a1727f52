<template>
  <div style="margin: 10px">
    <div v-if="formable">
      <el-button size="small" @click="handleCreate">
        {{ $t('button.insert') }}
      </el-button>
      <el-button size="small" @click="emailInfoHandle">
        {{ $t('pages.fromEmailTemplateImport') }}
      </el-button>
      <el-button size="small" :disabled="!deletedAble" @click="handleDelete">
        {{ $t('button.delete') }}
      </el-button>
    </div>
    <grid-table
      ref="gridTable"
      row-key="templateId"
      :height="230"
      :multi-select="true"
      :show-pager="false"
      :col-model="templateColModel"
      :row-datas="temp.templateList"
      @selectionChangeEnd="templateSelectionChange"
    />
    <email-template-info-setting-dlg ref="templateEmailInfoSettingDlg" @submit="submit"/>
    <email-template-info-import-dlg ref="templateEmailInfoImportDlg" :append-to-body="true" @submit="emailImportSubmit"/>
  </div>
</template>

<script>
import EmailTemplateInfoSettingDlg from './editDlg'
import EmailTemplateInfoImportDlg from './emailTemplateInfoImportDlg';
import {
  getTemplateEmailInfoByIds,
  getTemplateEmailInfoGroupTree
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'EmailTemplateInfoSetting',
  components: { EmailTemplateInfoImportDlg, EmailTemplateInfoSettingDlg },
  props: {
    temp: { type: Object, default() { return {} } },
    formable: { type: Boolean, default: true }  //  是否可编辑
  },
  data() {
    return {
      deletedAble: false,
      templateColModel: [
        { prop: 'textName', label: 'templateName', width: '100' },
        { prop: 'groupId', label: 'groupName', width: '100', formatter: this.groupFormatter },
        { prop: 'textTitle', label: 'mailTitle', width: '100' },
        { prop: 'textContent', label: 'emailContent', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', isShow: () => { return this.formable }, click: this.handleUpdate }
          ]
        }
      ],
      mailTree: []  //  分组树
    }
  },
  created() {
  },
  methods: {
    /**
     * 首位
     */
    show() {
      this.loadMailTree()
    },
    /**
     * 加载邮件模板信息分组数据
     */
    loadMailTree() {
      getTemplateEmailInfoGroupTree().then(res => {
        this.mailTree = res.data || []
      })
    },
    /**
     * 新增邮件模板信息
     */
    handleCreate() {
      this.$refs['templateEmailInfoSettingDlg'].handleCreate()
    },
    /**
     * 更新邮件模板信息
     * @param row
     */
    handleUpdate(row) {
      this.$refs['templateEmailInfoSettingDlg'].handleUpdate(row);
    },
    /**
     * 删除选中的邮件模板信息
     */
    handleDelete() {
      const ids = this.$refs.gridTable.getSelectedKeys() || []
      if (ids.length > 0) {
        ids.forEach(id => {
          const index = this.temp.templateList.findIndex(data => data.templateId === id);
          index > -1 && this.temp.templateList.splice(index, 1)
        })
      }
    },
    /**
     * 从邮件模板信息库中导入
     */
    emailInfoHandle() {
      this.$refs.templateEmailInfoImportDlg.show();
    },
    /**
     * 选中邮件模板信息列表事件
     * @param rowDatas
     */
    templateSelectionChange(rowDatas) {
      this.deletedAble = rowDatas.length > 0
    },
    /**
     * 新增单条邮件模板信息成功
     */
    submit(data, operator) {
      if (operator === 'create') {
        this.temp.templateList.push(data)
      } else {
        this.temp.templateList = this.temp.templateList.map(t => {
          return t.templateId === data.templateId ? data : t;
        })
      }
      this.loadMailTree()
    },
    /**
     * 从邮箱导入信息的回调方法
     */
    emailImportSubmit(datas) {
      this.loadMailTree()
      //  过滤重复的数据
      if (datas) {
        const oldLen = datas.length
        datas = datas.filter(data => this.temp.templateList.findIndex(t => t.templateId === data.templateId) === -1);
        if (datas.length < oldLen) {
          this.$message({
            message: this.$t('pages.filterDuplicateData'),
            type: 'info',
            duration: 2000
          })
        }
        datas && datas.forEach(data => this.temp.templateList.push(data));
      }
      //  重新加载templateList中的数据，确保显示的数据是最新的
      if (this.temp.templateList) {
        const templateIds = []
        this.temp.templateList.forEach(item => {
          templateIds.push(item.templateId);
        })
        if (templateIds.length) {
          getTemplateEmailInfoByIds({ ids: templateIds.join(','), limit: templateIds.length, page: 1 }).then(res => {
            this.temp.templateList = res.data || []
          })
        }
      }
    },
    /**
     * 获取预想分组名称
     * @param row
     * @param data
     * @returns {VueI18n.TranslateResult|节点数据}
     */
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return findNodeLabel(this.mailTree, row.groupId, 'dataId')
    }
  }
}
</script>

<style scoped>

</style>
