<template>
  <div style="margin: 10px">
    <div v-if="formable">
      <el-button size="small" @click="handleCreate">
        {{ $t('button.insert') }}
      </el-button>
      <el-button size="small" @click="emailInfoHandle">
        {{ $t('pages.fromReceiverEmailImport') }}
      </el-button>
      <el-button size="small" :disabled="!deletedAble" @click="handleDelete">
        {{ $t('button.delete') }}
      </el-button>
    </div>
    <grid-table
      ref="gridTable"
      row-key="mailId"
      :height="230"
      :multi-select="true"
      :show-pager="false"
      :col-model="receiverColModel"
      :row-datas="temp.receiverList"
      @selectionChangeEnd="receiverSelectionChange"
    />
    <receiver-email-info-setting-dlg ref="receiverEmailInfoSettingDlg" @submit="submit"/>
    <receiver-email-info-import-dlg ref="receiverEmailInfoImportDlg" :append-to-body="true" @submit="emailImportSubmit"/>
  </div>
</template>

<script>
import ReceiverEmailInfoSettingDlg from './editDlg'
import ReceiverEmailInfoImportDlg from './receiverEmailInfoImportDlg';
import {
  getReceiverEmailInfoByIds,
  getReceiverEmailInfoGroupTree
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'ReceiverEmailInfoSetting',
  components: { ReceiverEmailInfoImportDlg, ReceiverEmailInfoSettingDlg },
  props: {
    temp: { type: Object, default() { return {} } },
    formable: { type: Boolean, default: true }  //  是否可编辑
  },
  data() {
    return {
      deletedAble: false,
      receiverColModel: [
        { prop: 'mailAddress', label: 'emailAddress', width: '100' },
        { prop: 'groupId', label: 'groupName', width: '100', formatter: this.groupFormatter },
        { prop: 'receivePersonName', label: 'receiver', width: '100' },
        { prop: 'receiveDeptName', label: 'companyName', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', isShow: () => { return this.formable }, click: this.handleUpdate }
          ]
        }
      ],
      mailTree: []  //  分组树
    }
  },
  created() {
  },
  methods: {
    /**
     * 首位
     */
    show() {
      this.loadMailTree()
    },
    /**
     * 加载邮箱信息分组数据
     */
    loadMailTree() {
      getReceiverEmailInfoGroupTree().then(res => {
        this.mailTree = res.data || []
      })
    },
    /**
     * 新增邮箱信息
     */
    handleCreate() {
      this.$refs['receiverEmailInfoSettingDlg'].handleCreate()
    },
    /**
     * 更新邮箱信息
     * @param row
     */
    handleUpdate(row) {
      this.$refs['receiverEmailInfoSettingDlg'].handleUpdate(row);
    },
    /**
     * 删除选中的邮箱信息
     */
    handleDelete() {
      const ids = this.$refs.gridTable.getSelectedKeys() || []
      if (ids.length > 0) {
        ids.forEach(id => {
          const index = this.temp.receiverList.findIndex(data => data.mailId === id);
          index > -1 && this.temp.receiverList.splice(index, 1)
        })
      }
    },
    /**
     * 从邮箱信息库中导入
     */
    emailInfoHandle() {
      this.$refs.receiverEmailInfoImportDlg.show();
    },
    /**
     * 选中邮箱信息列表事件
     * @param rowDatas
     */
    receiverSelectionChange(rowDatas) {
      this.deletedAble = rowDatas.length > 0
    },
    /**
     * 新增单条邮箱信息成功
     */
    submit(data, operator) {
      if (operator === 'create') {
        this.temp.receiverList.push(data)
      } else {
        this.temp.receiverList = this.temp.receiverList.map(t => {
          return t.mailId === data.mailId ? data : t;
        })
      }
      this.loadMailTree()
    },
    /**
     * 从邮箱导入信息的回调方法
     */
    emailImportSubmit(datas) {
      this.loadMailTree()
      //  过滤重复的数据
      if (datas) {
        const oldLen = datas.length
        datas = datas.filter(data => this.temp.receiverList.findIndex(t => t.mailId === data.mailId) === -1);
        if (datas.length < oldLen) {
          this.$message({
            message: this.$t('pages.filterDuplicateData') + '',
            type: 'info',
            duration: 2000
          })
        }
        datas && datas.forEach(data => this.temp.receiverList.push(data));
      }
      //  重新加载receiverList中的数据，确保显示的数据是最新的
      if (this.temp.receiverList) {
        const emailIds = []
        this.temp.receiverList.forEach(item => {
          emailIds.push(item.mailId);
        })
        if (emailIds.length) {
          getReceiverEmailInfoByIds({ ids: emailIds.join(','), limit: emailIds.length, page: 1 }).then(res => {
            this.temp.receiverList = res.data || []
          })
        }
      }
    },
    /**
     * 获取预想分组名称
     * @param row
     * @param data
     * @returns {VueI18n.TranslateResult|节点数据}
     */
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return findNodeLabel(this.mailTree, row.groupId, 'dataId')
    }
  }
}
</script>

<style scoped>

</style>
