<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
      @close="cancel"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-width="80px">
        <FormItem :label="$t('table.emailAddress')" prop="mailAddress">
          <el-input v-model="temp.mailAddress" maxlength="100" show-word-limit/>
        </FormItem>
        <FormItem v-if="groupAddAble" :label="$t('form.group')" prop="groupId">
          <tree-select
            ref="groupTree"
            node-key="dataId"
            :data="mailTree"
            :icon-option="iconOption"
            :checked-keys="groupCheckedKeys"
            :width="296"
            style="width: calc(100% - 38px); display: inline-block;"
            @change="mailTreeSelectChange"
          />
          <el-button style="margin: 2px 0 0;" :title="$t('pages.addType')" class="editBtn" @click="handleEmailGroupCreate"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <FormItem v-else :label="$t('form.group')" prop="groupId">
          <tree-select
            ref="groupTree"
            node-key="dataId"
            :data="mailTree"
            :icon-option="iconOption"
            :checked-keys="groupCheckedKeys"
            :width="296"
            @change="mailTreeSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('table.receiver')" prop="receivePersonName">
          <el-input v-model="temp.receivePersonName" maxlength="100" show-word-limit/>
        </FormItem>
        <FormItem :label="$t('table.companyName')" prop="receiveDeptName" >
          <el-input v-model="temp.receiveDeptName" maxlength="100" show-word-limit/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      append-to-body
      :title="$t('pages.group')"
      :group-tree-data="mailTree"
      :add-func="createReceiverEmailInfoGroup"
      :update-func="updateReceiverEmailInfoGroup"
      :delete-func="deleteReceiverEmailInfoGroup"
      :edit-valid-func="getReceiverEmailInfoGroupByName"
      @addEnd="createEmailGroupSuccess"
    />
  </div>
</template>

<script>
import {
  addReceiverEmailInfo,
  updateReceiverEmailInfo,
  getReceiverEmailInfoGroupTree,
  getReceiverEmailInfoByEmailAddress,
  createReceiverEmailInfoGroup,
  updateReceiverEmailInfoGroup,
  deleteReceiverEmailInfoGroup,
  getReceiverEmailInfoGroupByName
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import EditGroupDlg from '@/views/common/editGroupDlg'
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'ReceiverEmailInfoSettingDlg',
  components: { EditGroupDlg },
  props: {
    //  分组是否支持添加
    groupAddAble: { type: Boolean, default: true }
  },
  data() {
    return {
      title: '',
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        mailId: null,
        receivePersonName: '',  //  接收人
        receiveDeptName: '',  //  接收人单位
        groupId: null, //  所属分组ID
        mailAddress: ''  //  邮箱地址 可重复
      },
      rules: {
        receivePersonName: [{ required: true, message: this.$t('pages.pleaseReceiver'), trigger: 'blur' }],
        mailAddress: [{ required: true, validator: this.mailAddressValidator, trigger: 'blur' }],
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'blur' }]
      },
      mailTree: [], //  邮箱信息树
      operator: '',  //  操作 create、update
      groupCheckedKeys: [],  //  选中的分组
      iconOption: {
        typeKey: 'oriData',
        1: ''
      }
    }
  },
  methods: {
    createReceiverEmailInfoGroup,
    updateReceiverEmailInfoGroup,
    deleteReceiverEmailInfoGroup,
    getReceiverEmailInfoGroupByName,
    /**
     * 初始化数据
     */
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
      this.groupCheckedKeys.splice(0)
    },
    /**
     * 加载邮箱信息分组数据
     */
    loadMailTree() {
      return getReceiverEmailInfoGroupTree().then(res => {
        this.mailTree = res.data || []
        return res;
      })
    },
    /**
     * 校验邮箱地址
     * @param rule
     * @param value
     * @param callback
     */
    mailAddressValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/
      if (!value || value === '') {
        callback(new Error(this.$t('pages.pleaseEnterEmailAccount')))
      } else if (!(reg.test(value))) {
        callback(new Error(this.$t('pages.validaEmail')))
      } else {
        //  校验邮箱地址是否重复，如果重复则不允许添加
        // getReceiverEmailInfoByEmailAddress({ mailAddress: value }).then(respond => {
        //   const data = respond.data
        //   if (data && data.mailId != this.temp.mailId) {
        //     callback(new Error(this.$t('valid.sameName')))
        //   } else {
        //     callback()
        //   }
        // })
        callback();
      }
    },
    /**
     * 格式化数据
     * @param temp
     * @returns {any}
     */
    formatRow(temp) {
      return JSON.parse(JSON.stringify(temp));
    },
    async validEmailAddressIsRepeat() {
      let flag = true;
      await getReceiverEmailInfoByEmailAddress({ mailAddress: this.temp.mailAddress }).then(respond => {
        const data = respond.data
        if (data && data.mailId != this.temp.mailId) {
          flag = false;
        }
      })
      return flag;
    },
    /**
     * 确认
     * 添加成功后，加入到邮箱信息库中，也加入到列表中
     */
    confirm() {
      this.$refs.dataForm.validate(async(valid) => {
        if (valid) {
          if (this.operator === 'create' && !await this.validEmailAddressIsRepeat()) {
            //  邮箱地址已存在，是否继续添加？
            this.$confirmBox(this.$t('pages.receiverEmailAddressRepeatMessage'), this.$t('text.prompt')).then(() => {
              this.save();
            }).catch(() => {})
          } else {
            this.save();
          }
        }
      });
    },
    save() {
      this.submitting = true
      const rowData = this.formatRow(this.temp);
      this.formatGroupName(rowData)
      const fun = this.operator === 'create' ? addReceiverEmailInfo : updateReceiverEmailInfo;
      fun(rowData).then(res => {
        this.submitting = false
        this.$emit('submit', res.data, this.operator);
        this.close()
      }).catch(t => {
        this.submitting = false
      })
    },
    /**
     * 取消
     */
    cancel() {
      this.close()
    },
    /**
     * 关闭窗口
     */
    close() {
      this.$refs.dataForm.clearValidate()
      this.visible = false;
    },
    /**
     * 新增操作
     * @param row
     */
    async handleCreate(groupId) {
      await this.loadMailTree();
      this.visible = true
      this.initData()
      if (groupId) {
        this.temp.groupId = groupId
        this.$nextTick(() => {
          this.groupCheckedKeys.push(groupId + '')
        })
      }
      this.operator = 'create'
      this.title = this.$t('pages.addReceiverEmailInfo')
    },
    /**
     * 更新操作
     * @param row
     */
    handleUpdate(row) {
      this.visible = true
      this.initData()
      this.temp = Object.assign(this.temp, row);
      this.operator = 'update'
      this.title = this.$t('pages.updateReceiverEmailInfo')
      this.loadMailTree().then(res => {
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      })
    },
    /**
     * 新增邮箱分组
     */
    handleEmailGroupCreate() {
      this.$refs['editGroupDlg'].handleCreate()
    },
    /**
     * 邮箱分组选中事件
     * @param data
     * @param node
     * @param vm
     */
    mailTreeSelectChange(data, node, vm) {
      if (data) {
        this.temp.groupId = data
        this.groupCheckedKeys.splice(0)
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      }
    },
    /**
     * 新增邮箱分组成功
     * @param tempData
     */
    createEmailGroupSuccess(tempData) {
      this.loadMailTree().then(res => {
        this.temp.groupId = tempData.id
        this.groupCheckedKeys.splice(0)
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      });
    },
    formatGroupName(rowData) {
      rowData.groupName = findNodeLabel(this.mailTree, rowData.groupId, 'dataId')
    }
  }
}
</script>

<style scoped>

</style>
