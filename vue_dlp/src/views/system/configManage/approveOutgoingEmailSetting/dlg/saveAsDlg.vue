<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('components.saveAs')"
    :visible.sync="visible"
    width="600px"
  >
    <p style="padding-left: 10px;">{{ $t('components.confirmSaveAs') }}</p>
    <Form ref="copyForm4StgDialog" :model="temp" label-position="right" label-width="80px" style="width: 560px">
      <el-row>
        <el-col :span="6">
          <el-select v-model="objectType">
            <el-option :value="2" :label="$t('components.userG')"/>
          </el-select>
        </el-col>
        <el-col :span="18">
          <tree-select
            ref="objectTree"
            node-key="id"
            is-filter
            check-strictly
            multiple
            :local-search="false"
            :leaf-key="'user'"
            :width="400"
            :checked-keys="objectCheckedKeys"
            @change="objectIdSelectChange"
          />
        </el-col>
      </el-row>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="cancel">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'SaveAsDlg',
  components: {},
  props: {
    //  保存数据的方法
    saveData: { type: Function, default() { return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) }) } },
    //  Form表单数据格式化
    formatFormData: { type: Function, default() { return null } }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      objectType: 2,
      temp: {},
      objectCheckedKeys: []  //  选中的生效对象
    }
  },
  methods: {
    show(row) {
      this.objectCheckedKeys = []
      Object.assign(this.temp, row);
      this.visible = true;
    },
    /**
     * 校验数据是否符合要求
     */
    validData() {
      let result = true;
      if (!this.temp.objectIds.length && !this.temp.objectGroupIds.length) {
        this.$message({
          message: this.$t('pages.effectObjectIsNotNull') + '',
          type: 'error',
          duration: 2000
        })
        result = false;
      }
      return result;
    },
    /**
     * 确认
     */
    confirm() {
      if (!this.validData()) {
        return
      }
      this.submitting = true;
      const rowData = this.formatFormData ? this.formatFormData(this.temp) : this.temp;
      this.saveData(rowData).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.$emit('submitEnd', res.data)
        this.submitting = false;
        this.cancel()
      }).catch(t => { this.submitting = false })
    },
    /**
     * 取消
     */
    cancel() {
      this.visible = false
    },
    /**
     * 操作员选中事件
     * @param datas
     * @param node
     * @param vm
     */
    objectIdSelectChange(datas, nodes, vm) {
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      nodes && Array.isArray(nodes) && nodes.forEach(node => {
        if (node.dataType === 'G') {
          this.temp.objectGroupIds.push(node.dataId);
        } else if (node.id && node.id.substr(0, 1) === 'U') {
          this.temp.objectIds.push(node.dataId);
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
