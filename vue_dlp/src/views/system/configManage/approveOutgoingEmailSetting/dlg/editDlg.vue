<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="800px"
      @close="cancel"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-width="80px">
        <FormItem :label="$t('pages.effectiveObject')">
          <el-row>
            <el-col :span="6">
              <el-select v-model="objectType" :disabled="!formable || operator === 'update'">
                <el-option :value="2" :label="$t('components.userG')"/>
              </el-select>
            </el-col>
            <el-col :span="18">
              <tree-select
                ref="objectTree"
                node-key="id"
                is-filter
                check-strictly
                multiple
                :local-search="false"
                :leaf-key="'user'"
                :width="400"
                :disabled="!formable || operator === 'update'"
                :checked-keys="objectCheckedKeys"
                @change="objectIdSelectChange"
              />
            </el-col>
          </el-row>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="temp.isInherit" :disabled="!formable || isInheritDisabled" :label="$t('pages.extendsParentDeptInfo')"></el-checkbox>
        </FormItem>
        <el-tabs v-model="tabName" type="card" style="margin-top: 10px" @tab-click="tableNameHandleClick">
          <el-tab-pane :label="$t('pages.receiverEmailInfoSetting')" name="receiverEmailInfoSetting">
            <receiver-email-info-setting ref="receiverEmailInfoSetting" :temp="temp" :formable="formable" />
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.emailTemplateInfoSetting')" name="emailTemplateInfoSetting">
            <email-template-info-setting ref="emailTemplateInfoSetting" :temp="temp" :formable="formable"/>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.sendEmailInfoSetting')" name="sendEmailInfoSetting">
            <send-email-info-setting ref="sendEmailInfoSetting" :temp="temp" :formable="formable"/>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable && operator === 'update'" type="primary" :loading="submitting" @click="saveAs">
          {{ $t('components.saveAs') }}
        </el-button>
        <el-button v-if="formable" type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <save-as-dlg ref="saveAsDlg" :format-form-data="formatRow" :save-data="addData" @submitEnd="saveAsSubmit"/>
  </div>
</template>

<script>
import ReceiverEmailInfoSetting from './receiverEmailInfoSetting/index'
import EmailTemplateInfoSetting from './emailTemplateInfoSetting/index'
import SendEmailInfoSetting from './sendEmailInfoSetting/index';
import {
  addData,
  updateData,
  getReceiverEmailInfoByIds,
  getTemplateEmailInfoByIds,
  getSendEmailInfoByIds
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import SaveAsDlg from '@/views/system/configManage/approveOutgoingEmailSetting/dlg/saveAsDlg';

export default {
  name: 'ApproveOutgoingEmailSettingDlg',
  components: { SaveAsDlg, SendEmailInfoSetting, ReceiverEmailInfoSetting, EmailTemplateInfoSetting },
  props: {
  },
  data() {
    return {
      title: '',
      visible: false,
      submitting: false,
      objectType: 2,
      temp: {},
      defaultTemp: {
        objectType: 2,
        objectIds: [],
        objectGroupIds: [],
        isInherit: true,  //  是否继承上级部门分配的信息 域值[false,true]  默认为true，默认会继承上级部门的策略
        receiverList: [], //  接收人邮箱信息设置
        templateList: [], //  邮件模板信息设置
        sendList: [],  //  发送邮箱信息设置
        mailId: '', //  接收人邮箱信息设置Ids
        templateId: '', //  邮件模板信息设置Ids
        sysMailId: ''  //  发送邮箱信息设置Ids
      },
      objectCheckedKeys: [],  //  选中的生效对象
      rules: {},
      tabName: 'receiverEmailInfoSetting',
      operator: '',  //  操作 create、update
      formable: true,  //  支持修改
      isInheritDisabled: false  //  继承上级部门按钮是否置灰
    }
  },
  methods: {
    addData,
    /**
     * 初始化数据
     */
    initData() {
      this.isInheritDisabled = false;
      this.objectCheckedKeys = []
      this.tabName = 'receiverEmailInfoSetting'
      this.temp = Object.assign({}, JSON.parse(JSON.stringify(this.defaultTemp)));
      this.formable = true
    },
    /**
     * 校验数据是否符合要求
     */
    validData() {
      let result = true;
      if (!this.temp.objectIds.length && !this.temp.objectGroupIds.length) {
        this.$message({
          message: this.$t('pages.effectObjectIsNotNull') + '',
          type: 'error',
          duration: 2000
        })
        result = false;
      }
      return result;
    },
    /**
     * 格式化数据
     * @param temp
     * @returns {any}
     */
    formatRow(temp) {
      const rowData = JSON.parse(JSON.stringify(temp));
      //  格式化生效对象
      rowData.deptId = rowData.objectGroupIds.length ? rowData.objectGroupIds.join(',') : ''
      rowData.userId = rowData.objectIds.length ? rowData.objectIds.join(',') : ''
      delete rowData.objectGroupIds
      delete rowData.objectIds
      //  格式化接收人邮箱信息设置
      const receiverIds = []
      rowData.receiverList && rowData.receiverList.forEach(data => receiverIds.push(data.mailId));
      rowData.mailId = receiverIds.length ? receiverIds.join(',') : null
      //  格式化邮件模板信息设置
      const templateIds = []
      rowData.templateList && rowData.templateList.forEach(data => templateIds.push(data.templateId));
      rowData.templateId = templateIds.length ? templateIds.join(',') : null
      //  格式化发送邮箱信息设置
      const sendIds = []
      rowData.sendList && rowData.sendList.forEach(data => sendIds.push(data.mailId));
      rowData.sysMailId = sendIds.length ? sendIds.join(',') : null
      return rowData;
    },
    /**
     * 保存策略
     */
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid && this.validData()) {
          this.submitting = true;
          const operatorFunction = this.operator === 'create' ? addData : updateData;
          const rowData = this.formatRow(this.temp)
          operatorFunction(rowData).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.operator === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.$emit('submitEnd', res.data, this.operator)
            this.submitting = false;
            this.close()
          }).catch(t => { this.submitting = false })
        }
      })
    },
    /**
     * 关闭/取消
     */
    cancel() {
      this.close()
    },
    close() {
      this.$refs.dataForm.clearValidate();
      this.visible = false;
    },
    /**
     * 另存为
     */
    saveAs() {
      const rowTemp = JSON.parse(JSON.stringify(this.temp));
      rowTemp.objectIds.splice(0)
      rowTemp.objectGroupIds.splice(0);
      this.$refs.saveAsDlg.show(rowTemp);
    },
    /**
     * 新增策略
     * objectId: 指定的生效对象
     */
    handleCreate(checkedEntityNode) {
      this.initData()
      this.operator = 'create'
      this.title = this.$t('pages.addDecryptApprovingEmailOutgoingConfig')
      this.visible = true
      this.tableNameHandleClick(this.tabName)
      if (checkedEntityNode.id) {
        this.objectCheckedKeys.splice(0, this.objectCheckedKeys.length, checkedEntityNode.id)
        if (checkedEntityNode.dataType === 'G') {
          this.temp.objectGroupIds.push(checkedEntityNode.dataId)
        } else if ((checkedEntityNode.id && checkedEntityNode.id.substr(0, 1) === 'U')) {
          this.temp.objectIds.push(checkedEntityNode.dataId)
        }
      }
    },
    /**
     * 修改策略
     */
    handleUpdate(row) {
      this.initData()
      this.operator = 'update'
      this.title = this.$t('pages.updateDecryptApprovingEmailOutgoingConfig')
      Object.assign(this.temp, row)
      this.temp.objectIds = this.temp.userId ? (this.temp.userId + '').split(',') : []
      this.temp.objectGroupIds = this.temp.deptId !== undefined && this.temp.deptId !== null ? (this.temp.deptId + '').split(',') : []
      //  设置
      this.temp.objectGroupIds && this.temp.objectGroupIds.forEach(id => {
        this.objectCheckedKeys.push('G' + id)
      })
      this.temp.objectIds && this.temp.objectIds.forEach(id => {
        this.objectCheckedKeys.push('U' + id)
      })

      //  加载接收人邮箱信息设置
      if (this.temp.mailId) {
        getReceiverEmailInfoByIds({ ids: this.temp.mailId }).then(res => {
          this.temp.receiverList = res.data || []
        })
      }
      //  加载邮件模板信息设置
      if (this.temp.templateId) {
        getTemplateEmailInfoByIds({ ids: this.temp.templateId }).then(res => {
          this.temp.templateList = res.data || []
        })
      }
      //  加载发送邮箱信息设置
      if (this.temp.sysMailId) {
        getSendEmailInfoByIds({ ids: this.temp.sysMailId }).then(res => {
          this.temp.sendList = res.data || []
        })
      }
      //  根节点特殊处理,根节点不展示”继承上级部门“，且不允许配置”继承上级部门信息“。
      this.isInheritDisabled = row.objectGroupIds && row.objectGroupIds.includes(0)
      if (this.isInheritDisabled) {
        this.temp.isInherit = false;
      }

      this.visible = true
      this.tableNameHandleClick(this.tabName)
    },
    /**
     * 查看详情
     */
    handleShow(row) {
      this.handleUpdate(row);
      this.title = this.$t('pages.detailsDecryptApprovingEmailOutgoingConfig')
      this.formable = false
    },
    /**
     * 操作员选中事件
     * @param datas
     * @param node
     * @param vm
     */
    objectIdSelectChange(datas, nodes, vm) {
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      nodes && Array.isArray(nodes) && nodes.forEach(node => {
        if (node.dataType === 'G') {
          this.temp.objectGroupIds.push(node.dataId);
        } else if (node.id && node.id.substr(0, 1) === 'U') {
          this.temp.objectIds.push(node.dataId);
        }
      })
    },
    /**
     * 标签改变事件
     * @param name
     */
    tableNameHandleClick(name) {
      this.$nextTick(() => {
        this.$refs[this.tabName] && this.$refs[this.tabName].show();
      })
    },
    /**
     * 另存为成功回调方法
     * @param data
     */
    saveAsSubmit(data) {
      this.close()
    }
  }
}
</script>

<style scoped>

</style>
