<template>
  <div style="margin: 10px">
    <div v-if="formable">
      <el-button size="small" @click="handleCreate">
        {{ $t('button.insert') }}
      </el-button>
      <el-button size="small" @click="emailInfoHandle">
        {{ $t('pages.fromSendEmailImport') }}
      </el-button>
      <el-button size="small" :disabled="!deletedAble" @click="handleDelete">
        {{ $t('button.delete') }}
      </el-button>
    </div>
    <grid-table
      ref="gridTable"
      row-key="mailId"
      :height="230"
      :multi-select="true"
      :show-pager="false"
      :col-model="sendColModel"
      :row-datas="temp.sendList"
      @selectionChangeEnd="sendSelectionChange"
    />
    <send-email-info-setting-dlg ref="sendEmailInfoSettingDlg" @submit="submit"/>
    <send-email-info-import-dlg ref="sendEmailInfoImportDlg" :append-to-body="true" @submit="emailImportSubmit"/>
  </div>
</template>

<script>
import SendEmailInfoSettingDlg from './editDlg'
import SendEmailInfoImportDlg from './sendEmailInfoImportDlg';
import {
  getSendEmailInfoByIds,
  getSendEmailInfoGroupTree
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'SendEmailInfoSetting',
  components: { SendEmailInfoImportDlg, SendEmailInfoSettingDlg },
  props: {
    temp: { type: Object, default() { return {} } },
    formable: { type: Boolean, default: true }  //  是否可编辑
  },
  data() {
    return {
      deletedAble: false,
      sendColModel: [
        { prop: 'mailAccount', label: 'emailAccount', width: '100' },
        { prop: 'groupId', label: 'groupName', width: '100', formatter: this.groupFormatter },
        { prop: 'smtpHost', label: 'emailServer', width: '100' },
        { prop: 'smtpPort', label: 'port', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', isShow: () => { return this.formable }, click: this.handleUpdate }
          ]
        }
      ],
      mailTree: []  //  分组树
    }
  },
  created() {
  },
  methods: {
    /**
     * 首位
     */
    show() {
      this.loadMailTree()
    },
    /**
     * 加载发送邮箱信息分组数据
     */
    loadMailTree() {
      getSendEmailInfoGroupTree().then(res => {
        this.mailTree = res.data || []
      })
    },
    /**
     * 新增发送邮箱信息
     */
    handleCreate() {
      this.$refs['sendEmailInfoSettingDlg'].handleCreate()
    },
    /**
     * 更新发送邮箱信息
     * @param row
     */
    handleUpdate(row) {
      this.$refs['sendEmailInfoSettingDlg'].handleUpdate(row);
    },
    /**
     * 删除选中的发送邮箱信息
     */
    handleDelete() {
      const ids = this.$refs.gridTable.getSelectedKeys() || [];
      if (ids.length > 0) {
        ids.forEach(id => {
          const index = this.temp.sendList.findIndex(data => data.mailId === id);
          index > -1 && this.temp.sendList.splice(index, 1)
        })
      }
    },
    /**
     * 从发送邮箱信息库中导入
     */
    emailInfoHandle() {
      this.$refs.sendEmailInfoImportDlg.show();
    },
    /**
     * 选中发送邮箱信息列表事件
     * @param rowDatas
     */
    sendSelectionChange(rowDatas) {
      this.deletedAble = rowDatas.length > 0
    },
    /**
     * 新增单条发送邮箱信息成功
     */
    submit(data, operator) {
      if (operator === 'create') {
        this.temp.sendList.push(data)
      } else {
        this.temp.sendList = this.temp.sendList.map(t => {
          return t.mailId === data.mailId ? data : t;
        })
      }
      this.loadMailTree()
    },
    /**
     * 从发送邮箱导入信息的回调方法
     */
    emailImportSubmit(datas) {
      this.loadMailTree()
      //  过滤重复的数据
      if (datas) {
        const oldLen = datas.length
        datas = datas.filter(data => this.temp.sendList.findIndex(t => t.mailId === data.mailId) === -1);
        if (datas.length < oldLen) {
          this.$message({
            message: this.$t('pages.filterDuplicateData') + '',
            type: 'info',
            duration: 2000
          })
        }
        datas && datas.forEach(data => this.temp.sendList.push(data));
      }
      //  重新加载sendList中的数据，确保显示的数据是最新的
      if (this.temp.sendList) {
        const emailIds = []
        this.temp.sendList.forEach(item => {
          emailIds.push(item.mailId);
        })
        if (emailIds.length) {
          getSendEmailInfoByIds({ ids: emailIds.join(','), limit: emailIds.length, page: 1 }).then(res => {
            this.temp.sendList = res.data || []
          })
        }
      }
    },
    /**
     * 获取预想分组名称
     * @param row
     * @param data
     * @returns {VueI18n.TranslateResult|节点数据}
     */
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return findNodeLabel(this.mailTree, row.groupId, 'dataId')
    }
  }
}
</script>

<style scoped>

</style>
