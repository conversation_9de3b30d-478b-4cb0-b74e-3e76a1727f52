<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
      @close="cancel"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-width="110px">
        <FormItem :label="$t('table.emailAccount')" prop="mailAccount">
          <el-autocomplete
            v-model="temp.mailAccount"
            class="inline-input"
            style="width: 100%"
            :fetch-suggestions="accountSearch"
            :placeholder="$t('pages.pleaseEnterEmailAccount')"
            suffix-icon="el-icon-message"
            :maxlength="100"
            @select="accountSelect"
          ></el-autocomplete>
        </FormItem>
        <FormItem v-if="groupAddAble" :label="$t('form.group')" prop="groupId">
          <tree-select
            ref="groupTree"
            node-key="dataId"
            :data="mailTree"
            :checked-keys="groupCheckedKeys"
            :icon-option="iconOption"
            :width="296"
            style="width: calc(100% - 38px); display: inline-block;"
            @change="mailTreeSelectChange"
          />
          <el-button style="margin: 2px 0 0;" :title="$t('pages.addType')" class="editBtn" @click="handleEmailGroupCreate"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <FormItem v-else :label="$t('form.group')" prop="groupId">
          <tree-select
            ref="groupTree"
            node-key="dataId"
            :data="mailTree"
            :checked-keys="groupCheckedKeys"
            :icon-option="iconOption"
            :width="296"
            @change="mailTreeSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('table.emailAuthCode')" encrypt prop="mailPwd" maxlength="100" show-word-limit>
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.mailServerContent_1') }}<br/>{{ $t('pages.mailServerContent_2') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
          <encrypt-input v-model="temp.mailPwd" maxlength="20" show-password non-blank/>
        </FormItem>
        <el-row>
          <el-col>
            <FormItem :label="$t('table.emailServer')" prop="smtpHost">
              <el-input v-model="temp.smtpHost" maxlength="50" show-word-limit/>
            </FormItem>
          </el-col>
          <el-col>
            <FormItem :label="$t('table.port')" prop="smtpPort">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.useSSL') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-input v-model="temp.smtpPort"/>
            </FormItem>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="check()">
          {{ $t('button.test') }}
        </el-button>
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      append-to-body
      :title="$t('pages.group')"
      :group-tree-data="mailTree"
      :add-func="createSendEmailInfoGroup"
      :update-func="updateSendEmailInfoGroup"
      :delete-func="deleteSendEmailInfoGroup"
      :edit-valid-func="getSendEmailInfoGroupByName"
      @addEnd="createEmailGroupSuccess"
    />
  </div>
</template>

<script>
import {
  addSendEmailInfo,
  updateSendEmailInfo,
  getSendEmailInfoGroupTree,
  createSendEmailInfoGroup,
  updateSendEmailInfoGroup,
  deleteSendEmailInfoGroup,
  getSendEmailInfoGroupByName,
  getSendEmailInfoByEmailAccount,
  checkConnect
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import EditGroupDlg from '@/views/common/editGroupDlg'
import { isIPv4 } from '@/utils/validate';
import EncryptInput from '@/components/EncryptInput'
import { getMailServerDict } from '@/utils/dictionary';
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'SendEmailInfoSettingDlg',
  components: { EditGroupDlg, EncryptInput },
  props: {
    //  分组是否支持添加
    groupAddAble: { type: Boolean, default: true }
  },
  data() {
    return {
      title: '',
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        mailId: null,
        mailAccount: '',  //  邮箱账号
        mailPwd: '',  //  邮箱授权码
        groupId: null, //  所属分组ID
        smtpHost: '',  //  邮箱服务器
        smtpPort: ''  //   端口
      },
      rules: {
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'blur' }],
        mailAccount: [{ required: true, type: 'email', validator: this.mailAccountValidator, trigger: 'blur' }],
        mailPwd: [{ required: true, message: this.$t('pages.pleaseEmailAuthCode'), trigger: 'blur' }],
        smtpHost: [{ required: true, message: this.$t('pages.pleaseEmailServer'), trigger: 'blur' }],
        smtpPort: [{ required: true, validator: this.validatePort, trigger: 'blur' }]
      },
      mailTree: [], //  邮箱信息树
      operator: '',  //  操作 create、update
      groupCheckedKeys: [],  //  选中的分组
      iconOption: {
        typeKey: 'oriData',
        1: ''
      }
    }
  },
  methods: {
    createSendEmailInfoGroup,
    updateSendEmailInfoGroup,
    deleteSendEmailInfoGroup,
    getSendEmailInfoGroupByName,
    /**
     * 初始化数据
     */
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp));
      this.groupCheckedKeys.splice(0)
    },
    /**
     * 加载发送邮箱信息分组数据
     */
    loadMailTree() {
      return getSendEmailInfoGroupTree().then(res => {
        this.mailTree = res.data || []
        return res;
      })
    },
    /**
     * 校验邮箱账号
     * @param rule
     * @param value
     * @param callback
     */
    mailAccountValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/
      if (!value || value === '') {
        callback(new Error(this.$t('pages.pleaseEnterEmailAccount') + ''))
      } else if (!(reg.test(value))) {
        callback(new Error(this.$t('pages.validaEmail')))
      } else {
        //  校验邮箱账号是否重复，如果重复则不允许添加
        getSendEmailInfoByEmailAccount({ mailAccount: value }).then(respond => {
          const data = respond.data
          if (data && data.mailId != this.temp.mailId) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    /**
     * 校验邮箱服务器是否合法
     * @param rule
     * @param value
     * @param callback
     */
    validateHost(rule, value, callback) {
      let msg = ''
      if (value) {
        if (isIPv4(value)) {
          msg = undefined
        } else {
          msg = this.$t('pages.serverLibrary_text4')
        }
      } else {
        msg = this.$t('pages.validateIP')
      }
      if (msg) callback(msg)
      else callback()
    },
    /**
     * 校验端口号是否合法
     */
    validatePort(rule, value, callback) {
      let msg = null
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (!value) {
        msg = this.$t('pages.validateMsg_enterPort')
      } else if (!number.test(value)) {
        msg = this.$t('pages.validateNaturalTree')
      } else if (value < 0 || value > 65535) {
        msg = this.$t('pages.validatePortRange')
      }
      if (msg) callback(msg)
      else callback()
    },
    /**
     * 格式化数据
     * @param temp
     * @returns {any}
     */
    formatRow(temp) {
      return JSON.parse(JSON.stringify(temp));
    },
    /**
     * 测试连接
     */
    check() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.temp)
          checkConnect(tempData).then(respond => {
            this.submitting = false
            if (respond.data && respond.data == 'true') {
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.connectSuccess'), type: 'success', duration: 2000 })
            } else {
              this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.dataBaseBackupInfoException4'), type: 'error', duration: 2000 })
            }
          }).catch(r => { this.submitting = false })
        }
      })
    },
    /**
     * 确认
     * 添加成功后，加入到发送邮箱信息库中，也加入到列表中
     */
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          checkConnect(this.temp).then(res => {
            if (res.data && res.data == 'true') {
              const rowData = this.formatRow(this.temp);
              this.formatGroupName(rowData)
              const fun = this.operator === 'create' ? addSendEmailInfo : updateSendEmailInfo;
              fun(rowData).then(res => {
                this.submitting = false;
                this.$emit('submit', res.data, this.operator);
                this.close()
              })
            } else {
              this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.dataBaseBackupInfoException4'), type: 'error', duration: 2000 })
              this.submitting = false;
            }
          }).catch(r => { this.submitting = false })
        }
      });
    },
    /**
     * 取消
     */
    cancel() {
      this.close()
    },
    /**
     * 关闭窗口
     */
    close() {
      this.$refs.dataForm.clearValidate()
      this.visible = false;
    },
    /**
     * 新增操作
     * @param row
     */
    async handleCreate(groupId) {
      await this.loadMailTree();
      this.visible = true
      this.initData()
      if (groupId) {
        this.temp.groupId = groupId
        this.$nextTick(() => {
          this.groupCheckedKeys.push(groupId + '')
        })
      }
      this.operator = 'create'
      this.title = this.$t('pages.addSendEmailInfo')
    },
    /**
     * 更新操作
     * @param row
     */
    handleUpdate(row) {
      this.visible = true
      this.initData()
      this.temp = Object.assign(this.temp, row);
      this.operator = 'update'
      this.title = this.$t('pages.updateSendEmailInfo')
      this.loadMailTree().then(res => {
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      })
    },
    /**
     * 新增发送邮箱分组
     */
    handleEmailGroupCreate() {
      this.$refs['editGroupDlg'].handleCreate()
    },
    /**
     * 发送邮箱分组选中事件
     * @param data
     * @param node
     * @param vm
     */
    mailTreeSelectChange(data, node, vm) {
      if (data) {
        this.temp.groupId = data
        this.groupCheckedKeys.splice(0)
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      }
    },
    /**
     * 新增发送邮箱分组成功
     * @param tempData
     */
    createEmailGroupSuccess(tempData) {
      this.loadMailTree().then(res => {
        this.temp.groupId = tempData.id
        this.groupCheckedKeys.splice(0)
        if (this.temp.groupId) {
          this.groupCheckedKeys.push(this.temp.groupId + '')
        }
      })
    },
    /**
     * 邮箱账号查询
     * @param queryString
     * @param cb
     */
    accountSearch(queryString, cb) {
      if (queryString) {
        this.mailDictSearch(queryString, cb)
      } else {
        cb([])
      }
    },
    /**
     * 邮箱账号字典查询
     * @param queryString
     * @param cb
     * @returns {*[]}
     */
    mailDictSearch(queryString, cb) {
      const result = []
      if (queryString) {
        const index = queryString.indexOf('@')
        const suffix = index < 0 ? '' : queryString.substring(index)
        const address = index < 0 ? queryString : queryString.substring(0, index)
        getMailServerDict().forEach(dict => {
          if (dict.suffix.startsWith(suffix)) {
            result.push(Object.assign({ value: address + dict.suffix }, dict))
          }
        })
      }
      if (cb) {
        cb(result)
      } else {
        return result
      }
    },
    /**
     * 选中邮箱地址后
     * @param item
     */
    accountSelect(item) {
      this.temp.smtpHost = item.sendHost
      //  默认使用SSL
      this.temp.smtpPort = item.sendSslPort
      this.$refs['dataForm'].validateField('mailAccount')
    },
    formatGroupName(rowData) {
      rowData.groupName = findNodeLabel(this.mailTree, rowData.groupId, 'dataId')
    }
  }
}
</script>

<style scoped>

</style>
