<template>
  <div>
    <el-container>
      <el-aside width="210px">
        <tree-menu
          ref="groupTree"
          :data="treeData"
          :height="height + 40"
          :multiple="false"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultExpandedKeys"
          :render-content="renderContent"
          @node-click="treeNodeClick"
        />
      </el-aside>
      <el-main>
        <div class="toolbar">
          <div>
            <el-button type="primary" icon="el-icon-plus" :disabled="!query.groupIds" size="mini" @click="handleEmailCreate">
              {{ $t('button.add') }}
            </el-button>
            <el-button :disabled="!deleteabel" icon="el-icon-delete" size="mini" @click="handleEmailDelete">
              {{ $t('button.delete') }}
            </el-button>
            <div style="float: right">
              <el-input v-model="query.mailAccountLike" v-trim clearable :placeholder="'邮箱账号'" style="width: 225px;" @keyup.enter.native="handleFilter"/>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </div>
        </div>
        <grid-table
          ref="InfoList"
          row-key="mailId"
          :height="height"
          :is-saved-selected="true"
          saved-selected-prop="mailAccount"
          :col-model="colModel"
          :multi-select="multiple"
          :default-sort="{ prop: 'mailId' }"
          :row-data-api="rowDataApi"
          :checked-row-keys="checkedRowKeys"
          pager-small
          @selectionChangeEnd="selectionChange"
        />
      </el-main>
    </el-container>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeData"
      :add-func="createSendEmailInfoGroup"
      :update-func="updateSendEmailInfoGroup"
      :delete-func="deleteSendEmailInfoGroup"
      :edit-valid-func="getSendEmailInfoGroupByName"
      @addEnd="createGroup"
      @updateEnd="updateGroup"
      @deleteEnd="removeGroupEnd"
    />
    <send-email-info-setting-dlg ref="sendEmailInfoSettingDlg" :group-add-able="false" @submit="submit"/>

  </div>
</template>

<script>
import {
  createSendEmailInfoGroup,
  updateSendEmailInfoGroup,
  deleteSendEmailInfoGroup,
  getSendEmailInfoGroupByName,
  deleteSendEmailInfo,
  getSendEmailInfoPage
} from '@/api/system/configManage/approvalOutgoingEmailSetting';
import { findNodeLabel } from '@/utils/tree'
import EditGroupDlg from '@/views/common/editGroupDlg'
import SendEmailInfoSettingDlg from './editDlg'

export default {
  name: 'SendEmailInfoSelectTable',
  components: { EditGroupDlg, SendEmailInfoSettingDlg },
  props: {
    height: { type: Number, default: 420 },
    multiple: { type: Boolean, default: true },
    //  查询所有发送邮箱分组树的方法
    libTreeNode: {
      type: Function,
      default: null
    },
    //  分页查询发送邮箱信息的方法
    listPage: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'mailAccount', label: 'emailAccount', width: '100' },
        { prop: 'groupId', label: 'groupName', width: '100', formatter: this.groupFormatter },
        { prop: 'smtpHost', label: 'emailServer', width: '100' },
        { prop: 'smtpPort', label: 'port', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleEmailUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        mailAccountLike: '',
        groupId: undefined,
        groupIds: ''
      },
      suffix: '',
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.sendEmailInfoLibrary'), parentId: '', children: [] }],
      defaultExpandedKeys: ['G0'],
      dataLoadPromise: undefined, // 设置一个函数先执行完后在执行别的操作
      checkedRowKeys: [], // 表格勾选
      datas: [], // 左侧树复选框选中后，获取对应列表值
      deleteabel: false //  发送邮箱是否可删除
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    emailTable: function() {
      return this.$refs['InfoList']
    }
  },
  created() {
  },
  activated() {
    this.show()
  },
  methods: {
    createSendEmailInfoGroup,
    updateSendEmailInfoGroup,
    deleteSendEmailInfoGroup(data) {
      data.groupName = this.groupFormatter({ groupId: data.id }, undefined)
      return deleteSendEmailInfoGroup(data)
    },
    getSendEmailInfoGroupByName,
    async show() {
      this.deleteabel = false
      this.query.mailAccountLike = null
      this.query.groupId = undefined
      this.query.groupIds = ''
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNodes()
        setTimeout(() => {
          this.emailTable && this.emailTable.clearSelection()
        }, 500)
      })
    },
    refreshTableData() {
      this.emailTable.execRowDataApi(this.query)
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleEmailGroupCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleEmailGroupUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    /**
     * 新增发送邮箱信息分组
     * @param data
     */
    handleEmailGroupCreate(data) {
      this.$refs.editGroupDlg.handleCreate(data.dataId)
    },
    /**
     * 修改发送邮箱信息分组
     * @param data
     */
    handleEmailGroupUpdate(data) {
      this.$refs.editGroupDlg.handleUpdate({
        id: data.dataId,
        name: data.label,
        groupId: parseInt(data.dataId),
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    /**
     * 去除分组节点前缀
     * @param data
     * @returns {number}
     */
    replaceTreeNodeType: function(data) {
      return Number.parseInt((data + '').replace('G', ''))
    },
    /**
     * 删除发送邮箱信息分组
     * @param data
     */
    removeNode(data) {
      const ids = []
      this.getDataIds(data, ids);
      //  检查该分组下是否含有信息
      getSendEmailInfoPage({ groupIds: ids.join(',') }).then(res => {
        if (res.data.total) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.sendEmailInfNotAllowDeleted'),
            type: 'warning',
            duration: 2000
          })
          return
        }
        this.$refs.editGroupDlg.handleDelete(data.dataId)
      });
    },
    /**
     * 获取节点包括其子节点的dataId
     * @param data
     */
    getDataIds(data, ids) {
      if (data) {
        ids.push(data.dataId);
        data.children && data.children.forEach(t => {
          this.getDataIds(t, ids);
        })
      }
    },
    /**
     * 新增发送邮箱分组节点后，给分组树新增此节点
     * @param data
     */
    createGroup(data) {
      this.groupTree.addNode(this.dataToTreeNode(data))
    },
    /**
     * 更新发送邮箱分组节点后，给分组树更新此节点
     * @param data
     */
    updateGroup(data) {
      this.groupTree.updateNode(this.dataToTreeNode(data))
    },
    /**
     * 构建树节点
     * @param data
     * @returns {{dataId: string, id: string, label, type: string, parentId: string}}
     */
    dataToTreeNode(data) {
      return {
        id: 'G' + data.id,
        type: 'G',
        dataId: data.id + '',
        label: data.name,
        parentId: 'G' + data.parentId
      }
    },
    /**
     * 删除发送邮箱分组节点后，给分组树删除此节点
     * @param dataId
     */
    removeGroupEnd(dataId) {
      const nodeData = this.groupTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    /**
     * 左侧树节点点击时右侧列表刷新
     * @param data
     * @param node
     * @param element
     */
    treeNodeClick: function(data, node, element) {
      if (data.dataId === '0') {
        this.query.groupId = ''
        this.query.groupIds = ''
      } else {
        this.query.groupId = undefined
        this.query.groupIds = this.getGroupIds(data).join(',')
      }
      this.handleFilter()
    },
    /**
     * 获取传入的树节点及其子节点的 dataId
     */
    getGroupIds(data) {
      const groups = Array.isArray(data) ? data : [data]
      const ids = []
      while (groups.length > 0) {
        const group = groups.shift()
        if (group.children) {
          groups.push(...group.children)
        }
        ids.push(group.dataId)
      }
      return ids
    },
    /**
     * 新增发送邮箱信息
     */
    handleEmailCreate() {
      this.$refs['sendEmailInfoSettingDlg'].handleCreate(this.query.groupIds.split(',')[0])
    },
    /**
     * 修改发送邮箱信息
     */
    handleEmailUpdate(row) {
      this.$refs['sendEmailInfoSettingDlg'].handleUpdate(row)
    },
    /**
     * 删除发送邮箱信息
     */
    handleEmailDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const ids = this.emailTable.getSelectedKeys();
        const toDeleteDatas = this.emailTable.getSelectedDatas();
        // 用于记录管理员日志
        const mailAccount = toDeleteDatas.map(item => item.mailAccount).join(',')
        if (ids) {
          deleteSendEmailInfo({ ids: ids.join(','), mailAccount }).then(res => {
            this.handleFilter()
            this.emailTable && this.emailTable.clearSelection()
          })
        }
      })
    },
    /**
     * 表格查询功能
     * */
    handleFilter() {
      this.query.page = 1
      this.emailTable && this.emailTable.execRowDataApi(this.query)
    },
    /**
     * 获取表格数据
     * */
    rowDataApi: function(option) {
      const newOption = Object.assign(this.query, option)
      return this.listPage(newOption)
    },
    /**
     * 获取左侧树节点信息
     */
    loadGroupTree: function() {
      this.libTreeNode().then(respond => {
        this.treeData[0].children = respond.data
      })
    },
    /**
     * 获取预想分组名称
     * @param row
     * @param data
     * @returns {VueI18n.TranslateResult|节点数据}
     */
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return findNodeLabel(this.treeData, row.groupId, 'dataId')
    },
    /**
     * 发送邮箱列表数据选中
     * @param datas
     */
    selectionChange(datas) {
      this.deleteabel = datas && datas.length > 0
    },
    /**
     *  新增发送邮箱信息成功后事件，
     */
    submit(data) {
      //  刷新列表
      this.handleFilter()
    },
    /**
     * 获取列表中选中的数据
     */
    getSelectedDatas() {
      return this.emailTable && this.emailTable.getSelectedDatas()
    },
    /**
     * 清空选中状态
     * @returns {*}
     */
    clearSelection() {
      return this.emailTable && this.emailTable.clearSelection()
    }
  }
}
</script>
