<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.sendEmailInfoLibrary')"
      :visible.sync="dialogVisible"
      width="850px"
      @close="cancel"
    >
      <send-email-info-select-table
        ref="emailSelectTable"
        :height="400"
        :multiple="multiple"
        :lib-tree-node="getSendEmailInfoGroupTree"
        :list-page="getSendEmailInfoPage"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmImport()">
          {{ $t('pages.selectedSendEmailInfo') }}
        </el-button>
        <el-button @click="cancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import SendEmailInfoSelectTable from './sendEmailInfoSelectTable'
import { getSendEmailInfoGroupTree, getSendEmailInfoPage } from '@/api/system/configManage/approvalOutgoingEmailSetting';

export default {
  name: 'SendEmailInfoImportDlg',
  components: { SendEmailInfoSelectTable },
  props: {
    multiple: { type: Boolean, default: true }, // 能否多选
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      dialogVisible: false,
      fileSuffixTree: [],
      closeNum: 0 //  统计关闭次数，确保关闭弹窗时，submit仅执行一次
    }
  },
  created() {
  },
  methods: {
    getSendEmailInfoGroupTree,
    getSendEmailInfoPage,
    /**
     * 展示窗口
     */
    show() {
      this.closeNum = 0;
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.emailSelectTable.show()
      })
    },
    /**
     * 隐藏窗口
     */
    hide() {
      this.$refs.emailSelectTable && this.$refs.emailSelectTable.clearSelection()
      this.dialogVisible = false
    },
    /**
     * 取消
     */
    cancel() {
      if (!this.closeNum++) {
        this.$emit('submit', null)
      }
      this.hide()
    },
    confirmImport() {
      const datas = this.$refs.emailSelectTable.getSelectedDatas() || [];
      if (datas == null || datas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.selectSendEmailInfoWarnMsg'),
          type: 'warning',
          duration: 2000
        })
        return
      }
      this.$emit('submit', datas)
      this.closeNum++
      this.hide()
    }
  }
}
</script>
