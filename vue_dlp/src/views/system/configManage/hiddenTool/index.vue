<template>
  <div class="table-container" style="padding: 20px">
    <el-tabs v-model="name">
      <el-tab-pane :label="$t('pages.langPackImportExport')" name="langExport">
        <LangExport />
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.resourceBundleUpload')" name="resourceBundleUpload">
        <ResourceUpload />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import LangExport from '../langExport'
import ResourceUpload from '../resourceUpload'
export default {
  name: 'HiddenTool',
  components: { LangExport, ResourceUpload },
  data() {
    return {
      name: 'langExport',
      data: [
        {
          name: '资源包制作',
          router: '/config/resourceBuilder'
        },
        {
          name: '语言包导入导出',
          router: '/config/langExport'
        }
      ],
      colModel: [
        { prop: 'name', label: 'name', width: '150' },
        {
          label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'goTo', click: this.skipHandler }
          ]
        }
      ]
    }
  },
  methods: {
    skipHandler(row) {
      this.$router.push(row.router)
    }
  }
}
</script>
