<template>
  <div class="app-container">
    <Form
      ref="dataForm"
      :hide-required-asterisk="true"
      class="module-form"
    >
      <el-card v-for="(m, i) in modulesData" :key="i" class="box-card">
        <div slot="header">
          <span>{{ m.label }}</span>
        </div>
        <div style="line-height: 22px;">
          <el-checkbox-group v-model="m.checked">
            <el-row>
              <el-col v-for="(item, index) in m.options" :key="index" :span="6">
                <el-checkbox :label="item.code" :disabled="!editable">
                  {{ item.remark }}
                  <span v-if="item.code === 2001">
                    删除<el-input v-model="item.days" :disabled="!editable" maxlength="5" style="width:75px;margin: 5px;"></el-input>天前的审计相关日志
                  </span>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
      </el-card>
      <div class="save-btn-container">
        <el-button v-show="editable" :loading="submitting" type="primary" size="mini" @click="saveData()">{{ $t('button.save') }}</el-button>
        <el-button v-show="editable" size="mini" @click="cancelSave()">{{ $t('button.cancel') }}</el-button>
        <el-button v-show="!editable" type="primary" size="mini" @click="editable = true">{{ $t('pages.setting') }}</el-button>
      </div>
    </Form>
  </div>
</template>

<script>
import { updateOption, listByOptIds } from '@/api/system/configManage/otherConfig'

export default {
  name: 'OtherSystemConfig',
  components: {},
  data() {
    return {
      modulesData: [ // 测试数据
        {
          label: '日志相关设置',
          checked: [],
          options: [
            { code: 2001, remark: '定期', days: 365 }
          ]
        }
      ],
      tempChecked: [],
      query: { // 查询条件
        entityType: undefined,
        entityId: undefined
      },
      editable: false,
      submitting: false
    }
  },
  computed: {
  },
  watch: {
    editable(val) {
      if (val) {
        this.tempChecked.length = 0
        this.modulesData.forEach(data => {
          this.tempChecked.push(data.checked)
        })
      }
    }
  },
  created() {
    this.loadOptions()
  },
  methods: {
    loadOptions() {
      listByOptIds().then(respond => {
        const optMap = {}
        respond.data.forEach(data => { optMap[data.optId] = data })
        this.modulesData.forEach(data => {
          data.options.forEach(option => {
            const opt = optMap[option.code]
            if (opt.optVal) {
              data.checked.push(option.code)
              if (opt.optId === 2001) {
                option.days = opt.optVal
              }
            }
          })
        })
      })
    },
    saveData() {
      const optList = []
      this.modulesData.forEach(data => {
        data.options.forEach(option => {
          const isChecked = data.checked.indexOf(option.code) >= 0
          const opt = { optId: option.code, optVal: isChecked ? 1 : 0 }
          if (isChecked && opt.optId === 2001) {
            opt.optVal = option.days
          }
          optList.push(opt)
        })
      })
      this.submitting = true
      updateOption(optList).then(respond => {
        this.submitting = false
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
        this.editable = false
      }).catch(res => {
        this.submitting = false
      })
    },
    cancelSave() {
      this.editable = false
      this.modulesData.forEach((data, index) => {
        data.checked = this.tempChecked[index]
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    height: 100%;
    overflow: auto;
    .el-col-6{
      min-width: 300px;
    }
  }
  .save-btn-container{
    padding: 10px 50px 0;
  }
</style>
