<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          新增模板
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          删除模板
        </el-button>
        <!--<el-button size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable placeholder="邮件主题" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="emailTemplateList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 700px; margin-left:20px;">
        <FormItem label="模板类型" prop="type">
          <el-select v-model="temp.type" :placeholder="$t('text.select')">
            <el-option v-for="(value, key) in typeOptions" :key="key" :label="value" :value="key" />
          </el-select>
        </FormItem>
        <FormItem label="邮件主题" prop="subject">
          <el-input v-model="temp.subject" maxlength="60"/>
        </FormItem>
        <FormItem label="邮件内容" prop="content">
          <tinymce v-model="temp.content" :timestamp="timestamp"></tinymce>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTemplateBySubject, createTemplate, updateTemplate, deleteTemplate, getTemplatePage } from '@/api/system/configManage/emailConfig'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'EmailConfig',
  components: { Tinymce },
  data() {
    return {
      colModel: [
        { prop: 'type', label: '模板类型', width: '150', fixed: true, formatter: this.typeFormatter },
        { prop: 'subject', label: '邮件主题', width: '150' },
        { prop: 'content', label: '邮件正文', width: '200' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      timestamp: 0,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        type: '1',
        subject: '',
        content: ''
      },
      rules: {
        subject: [
          { required: true, message: '请输入邮件主题', trigger: 'blur' },
          { validator: this.subjectValidator, trigger: 'blur' }
        ],
        content: [{ required: true, message: '邮件正文不能为空', trigger: 'blur' }]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: '修改邮件模板',
        create: '新增邮件模板'
      },
      typeOptions: { 1: '空间预警', 2: '违规报警', 3: '忘记密码' },
      multipleSelection: [], // 选中行数组集合
      downloadLoading: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['emailTemplateList']
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTemplatePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.timestamp = (new Date()).valueOf()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.dialogStatus = 'create'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.type = this.temp.type.toString()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {},
    handleExport() {},
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createTemplate(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateTemplate(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.updateRowData(this.temp)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteTemplate({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    typeFormatter: function(row, data) {
      return this.typeOptions[data]
    },
    subjectValidator(rule, value, callback) {
      getTemplateBySubject({ subject: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error('系统已存在相同主题的邮件模板，请修改!'))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
