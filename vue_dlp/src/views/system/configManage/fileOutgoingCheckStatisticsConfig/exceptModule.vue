<template>
  <div style="width: 100%;">
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="titleMap[exceptDetail.bussType]"
      :modal="false"
      :visible.sync="exceptVisible"
      width="740px"
      @dragDialog="handleDrag"
    >
      <div style="margin-left: 5px;">
        <el-button size="small" :disabled="!formable" @click="handleCreateExcept()">{{ $t('button.add') }}</el-button>
        <el-button size="small" :disabled="editButton || !formable" @click="handleUpdateExcept()">{{ $t('button.edit') }}</el-button>
        <el-button size="small" :disabled="delButton || !formable" @click="handleDeleteExcept()">{{ $t('button.delete') }}</el-button>
      </div>
      <grid-table
        ref="exceptListTable"
        :height="250"
        :multi-select="true"
        :show-pager="false"
        :col-model="exceptColModel"
        :row-datas="rowDatas"
        style="padding: 5px;"
        @selectionChangeEnd="exceptSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ props.detail.split(',').join(', ') }}</span>
          </div>
        </template>
      </grid-table>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="!formable" type="primary" @click="handleCreateExceData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="exceptVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="textMap[textStatus]"
      :modal="false"
      :visible.sync="exceptDetailVisible"
      width="740px"
      @dragDialog="handleDrag"
    >
      <Form ref="exceptForm" :model="exceptDetail" :rules="exceptRules" label-position="right" label-width="120px" style="width: 650px;">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.bizType')" prop="bussType">
              <el-select v-model="exceptDetail.bussType" :placeholder="$t('text.select')" :disabled="true" style="width: 220px;">
                <el-option
                  v-for="(item) in bussTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </FormItem>
          </el-col>

          <el-col :span="12">
            <FormItem :label="$t('table.excepeOperation')">
              <el-select v-model="exceptDetail.opeType" style="width: 200px;">
                <el-option
                  v-for="item in opeTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <div>
          <!-- ADB -->
          <!-- <div v-if="exceptDetail.bussType === 1020" style="margin-top: 10px;">
            <el-divider content-position="left">{{ $t('table.excepeData') }}</el-divider>
            <div style="margin-left: 5px;">
              <el-button size="small" @click="handleCreateAdb()">{{ $t('button.add') }}</el-button>
              <el-button size="small" :disabled="adbDelButton" @click="handleDeleteAdb()">{{ $t('button.delete') }}</el-button>
            </div>
            <grid-table
              ref="adbListTable"
              :height="150"
              :multi-select="true"
              :show-pager="false"
              :col-model="adbColModel"
              :row-datas="exceptDetail.exceptData"
              style="padding: 5px;"
              @selectionChangeEnd="adbExceptSelectionChange"
            ></grid-table>
          </div> -->

          <!-- 蓝牙 -->
          <div v-if="exceptDetail.bussType === 1014" style="margin-top: 10px;">
            <FormItem :label="$t('pages.fileOutgoing_Msg80')">
              <tag :list="blueToothMacAddress" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: calc(100% - 52px);" @tagChange="blueToothChange"/>
              <el-tooltip class="item" effect="dark" :content="$t('pages.fileOutgoing_Msg106')" placement="top">
                <el-button type="primary" size="mini" @click="handleBlueToothImport()">
                  <svg-icon icon-class="import" />
                </el-button>
              </el-tooltip>
            </FormItem>
            <!-- 原本蓝牙还有设备名、匹配模式，但是目前只用到了蓝牙地址且固定为完全匹配，因此改为使用tag, 但是仍保留表格形式代码备用 -->
            <!-- <el-divider content-position="left">{{ $t('table.excepeData') }}</el-divider>
            <div style="margin-left: 5px;">
              <el-button size="small" @click="handleCreateBlueTooth()">{{ $t('button.add') }}</el-button>
              <el-button size="small" :disabled="blueToothDelButton" @click="handleDeleteBlueTooth()">{{ $t('button.delete') }}</el-button>
            </div>
            <grid-table
              ref="blueToothListTable"
              :height="150"
              :multi-select="true"
              :show-pager="false"
              :col-model="blueToothColModel"
              :row-datas="exceptDetail.exceptData"
              style="padding: 5px;"
              @selectionChangeEnd="blueToolthExceptSelectionChange"
            ></grid-table> -->
          </div>

          <!-- FTP or 远程桌面 -->
          <div v-if="exceptDetail.bussType === 1017 || exceptDetail.bussType === 1024" style="margin-top: 10px;">
            <el-divider content-position="left">{{ $t('table.excepeData') }}</el-divider>
            <div style="margin-left: 5px;">
              <el-button size="small" @click="handleCreateIp()">{{ $t('button.add') }}</el-button>
              <el-button size="small" :disabled="ipDelButton" @click="handleDeleteIp()">{{ $t('button.delete') }}</el-button>
            </div>
            <grid-table
              ref="ipListTable"
              :height="150"
              :multi-select="true"
              :show-pager="false"
              :col-model="ipColModel"
              :row-datas="exceptDetail.exceptData"
              style="padding: 5px;"
              @selectionChangeEnd="ipExceptSelectionChange"
            ></grid-table>
          </div>

          <FormItem v-if="exceptDetail.bussType === 1002" :label="$t('table.pnpDeviceId')">
            <tag :list="exceptDetail.exceptData" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: calc(100% - 52px);"/>
            <el-tooltip class="item" effect="dark" :content="$t('pages.fileOutgoing_Msg76')" placement="top">
              <el-button type="primary" size="mini" @click="handleUsbLibImport()">
                <svg-icon icon-class="import" />
              </el-button>
            </el-tooltip>
          </FormItem>
          <FormItem v-if="exceptDetail.bussType === 1020" :label="$t('table.serialNumber')">
            <tag :list="exceptDetail.exceptData" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: 525px;"/>
          </FormItem>
          <FormItem v-if="exceptDetail.bussType === 1006" :label="$t('table.websiteUrl')">
            <tag :list="exceptDetail.exceptData" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: calc(100% - 52px);"/>
            <el-tooltip class="item" effect="dark" :content="$t('pages.effectiveContent_urlImport')" placement="top">
              <el-button type="primary" size="mini" @click="handleUrlLibImport()">
                <svg-icon icon-class="import" />
              </el-button>
            </el-tooltip>
          </FormItem>
          <FormItem v-if="exceptDetail.bussType === 1012" :label="$t('pages.exceptTypeOptions1')">
            <!-- <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
              <div slot="content">
                <i18n path="pages.mailLibrary_address_description">
                  <br slot="br"/>
                </i18n>
              </div>
              <i class="el-icon-info"/>
            </el-tooltip> -->
            <tag :list="exceptDetail.exceptData" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: calc(100% - 52px);" @tagChange="mailChange"/>
            <el-tooltip class="item" effect="dark" :content="$t('pages.emailLibImport')" placement="top">
              <el-button type="primary" size="mini" @click="handleMailLibImport()">
                <svg-icon icon-class="import" />
              </el-button>
            </el-tooltip>
          </FormItem>
          <FormItem v-if="exceptDetail.bussType === 1016" :label="$t('table.processName')">
            <!-- <tag :list="exceptDetail.exceptData" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: 525px"/> -->
            <el-select
              ref="netDiskTree"
              v-model="exceptDetail.exceptData"
              popper-append-to-body
              clearable
              multiple
              size="mini"
              :placeholder="$t('text.select') + $t('table.netDiskType')"
              class="im-tree-select"
            >
              <el-option v-for="item in netDiskTreeData" :key="item.id" :label="item.label" :value="item.id">
                <span>{{ item.label }}</span>
              </el-option>
            </el-select>
          </FormItem>
          <FormItem v-if="exceptDetail.bussType === 1004" :label="$t('table.processName')">
            <!-- <tag :list="exceptDetail.exceptData" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: 525px;"/> -->
            <el-select
              ref="fileSendProcTree"
              v-model="exceptDetail.exceptData"
              popper-append-to-body
              clearable
              multiple
              size="mini"
              :placeholder="$t('text.select') + $t('pages.controlImTool')"
              class="im-tree-select"
            >
              <el-option v-for="item in toolTreeData" :key="item.id" :label="item.label" :value="item.id">
                <span>{{ item.label }}</span>
              </el-option>
            </el-select>
          </FormItem>
          <FormItem v-if="exceptDetail.bussType === 1011" :label="$t('table.netShareAddress')">
            <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
              <div slot="content">
                <i18n :path="$t('pages.fileOutgoing_Msg77')">
                </i18n>
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
            <tag :list="exceptDetail.exceptData" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: 525px;" />
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="textStatus==='create'?createExceptData():updateExceptData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="exceptDetailVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- ADB例外数据 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="$t('button.insert') + $t('pages.fileOutgoing_Msg78')"
      :modal="false"
      :visible.sync="adbExceptVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="adbExceptForm" :model="adbExceptDetail" :rules="adbExceptRules" label-position="right" label-width="120px" style="width: 450px;">
        <FormItem :label="$t('table.devManufacturer')">
          <tag :list="adbExceptDetail.manufacturer" border class="input-with-button" style="min-height: 30px; background-color:#f5f5f5;width: 325px;"/>
        </FormItem>
        <FormItem :label="$t('table.matchKind')">
          <el-select v-model="adbExceptDetail.matchType" style="width: 325px;">
            <el-option
              v-for="(item) in matchTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleCreateAdbExceData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="adbExceptVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 蓝牙例外数据 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="$t('button.insert') + $t('pages.fileOutgoing_Msg79')"
      :modal="false"
      :visible.sync="blueToothExceptVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="blueToothExceptForm" :model="blueToothExceptDetail" :rules="blueToothExceptRules" label-position="right" label-width="120px" style="width: 450px;">
        <FormItem :label="$t('table.deviceName')">
          <el-input v-model="blueToothExceptDetail.deviceName" v-trim maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.matchKind')">
          <el-select v-model="blueToothExceptDetail.matchType" style="width: 325px;">
            <el-option
              v-for="(item) in matchTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.fileOutgoing_Msg80')" prop="macAddress">
          <el-input v-model="blueToothExceptDetail.macAddress" v-trim maxlength="30"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleCreateBlueToothExceData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="blueToothExceptVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- FTP 远程桌面 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="getIpTitle(exceptDetail.bussType)"
      :modal="false"
      :visible.sync="ipExceptVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="ipExceptForm" :model="ipExceptDetail" :rules="ipExceptRules" label-position="right" label-width="120px" style="width: 450px;">
        <FormItem :label="$t('table.startIP')" prop="startIP">
          <el-input v-model="ipExceptDetail.startIP" v-trim maxlength="128"></el-input>
        </FormItem>
        <FormItem :label="$t('table.endIP')" prop="endIP">
          <el-input v-model="ipExceptDetail.endIP" v-trim maxlength="128"></el-input>
        </FormItem>
        <FormItem :label="$t('table.domain')" prop="domain">
          <el-input v-model="ipExceptDetail.domain" v-trim maxlength="128"></el-input>
        </FormItem>
        <FormItem :label="$t('table.matchKind')">
          <el-select v-model="ipExceptDetail.limitType" style="width: 325px;" @change="handleLimitTypeChange">
            <el-option
              v-for="(item) in limitTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleCreateIpExceData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="ipExceptVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <usb-select-dlg ref="usbSelectDlg" @select="selectUSBEnd"/>
    <mail-import-table-dlg ref="mailImportTableDlg" @getMailAddress="getMailAddress"></mail-import-table-dlg>
    <url-import-table-dlg ref="urlImportTableDlg" @getUrlAddress="getUrlAddress"></url-import-table-dlg>
    <blue-tooth-dlg ref="blueToothDlg" @submitEnd="blueSubmitEnd"></blue-tooth-dlg>
  </div>
</template>
<script>
import UsbSelectDlg from '@/views/behaviorManage/hardware/usbFileConfig/usbSelectDlg'
import BlueToothDlg from './blueToothDlg.vue'
import MailImportTableDlg from '@/views/system/baseData/mailLibrary/mailImportTableDlg.vue'
import UrlImportTableDlg from '@/views/system/baseData/urlLibrary/urlImportTableDlg.vue' 
import { isIPv4, isIPv6 } from '@/utils/validate'
export default {
  name: 'ExceptModule',
  components: { UsbSelectDlg, MailImportTableDlg, UrlImportTableDlg, BlueToothDlg },
  props: {
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    temp: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      textMap: {
        create: this.i18nConcatText(this.$t('table.excepeData'), 'create'),
        update: this.i18nConcatText(this.$t('table.excepeData'), 'update')
      },
      textStatus: undefined,
      titleMap: {
        1012: this.$t('pages.fileOutgoing_Msg94') + this.$t('pages.fileOutgoing_Msg75'),
        1004: this.$t('pages.fileOutgoing_Msg92') + this.$t('pages.fileOutgoing_Msg75'),
        1001: this.$t('pages.fileOutgoing_Msg93') + this.$t('pages.fileOutgoing_Msg75'),
        1014: this.$t('table.bluetoothSendFileSumAll') + this.$t('pages.fileOutgoing_Msg75'),
        1006: this.$t('table.webUploadFileSumAll') + this.$t('pages.fileOutgoing_Msg75'),
        1002: this.$t('pages.stgMessage1') + this.$t('pages.fileOutgoing_Msg75'),
        1011: this.$t('pages.fileOutgoing_Msg91') + this.$t('pages.fileOutgoing_Msg75'),
        1016: this.$t('table.webDiskUploadFileSumAll') + this.$t('pages.fileOutgoing_Msg75'),
        1017: this.$t('table.ftpUploadFileSumAll') + this.$t('pages.fileOutgoing_Msg75'),
        1020: this.$t('table.adbSendFileSumAll') + this.$t('pages.fileOutgoing_Msg75'),
        1024: this.$t('table.remoteOutgoingFileSumAll') + this.$t('pages.fileOutgoing_Msg75')
      },
      ipTitleMap: {
        1017: this.ipStatus === 'create' ? this.$t('button.add') + this.$t('pages.fileOutgoing_Msg81') : this.$t('text.edit') + this.$t('pages.fileOutgoing_Msg81'),
        1024: this.ipStatus === 'create' ? this.$t('button.add') + this.$t('pages.fileOutgoing_Msg82') : this.$t('text.edit') + this.$t('pages.fileOutgoing_Msg82')
      },
      ipStatus: 'create',
      nameMap: {
        1002: this.$t('pages.deviceCode'),
        1004: this.$t('table.processName'),
        1006: this.$t('table.websiteUrl'),
        1011: this.$t('table.netShareAddress'),
        1012: this.$t('pages.idmSearchMail'),
        1016: this.$t('table.processName'),
        1020: this.$t('table.serialNumber')
      },
      fieldNameMap: {
        1002: 'usbSerialNum',
        1004: 'processName',
        1006: 'url',
        1011: 'IP',
        1012: 'address',
        1016: 'processName',
        1020: 'serialNum'
      },
      bussTypeOptions: [ 
        { name: 'isUsbDev', value: 1002, label: this.$t('pages.stgMessage1') },
        { name: 'isChatFile', value: 1004, label: this.$t('pages.fileOutgoing_Msg92') },
        { name: 'isBrowerSendFileOption', value: 1006, label: this.$t('table.webUploadFileSumAll') },
        { name: 'isEmailAttach', value: 1012, label: this.$t('pages.fileOutgoing_Msg94') },
        { name: 'isBlueFile', value: 1014, label: this.$t('table.bluetoothSendFileSumAll') },
        { name: 'isNetDisk', value: 1016, label: this.$t('table.webDiskUploadFileSumAll') },
        { name: 'isFtpFileAware', value: 1017, label: this.$t('table.ftpUploadFileSumAll') },
        { name: 'isRemoteDesktop', value: 1024, label: this.$t('table.remoteOutgoingFileSumAll') },
        { name: 'isRemoteDir', value: 1011, label: this.$t('pages.fileOutgoing_Msg91') },
        { name: 'isADBFile', value: 1020, label: this.$t('table.adbSendFileSumAll') }
      ],
      opeTypeOptions: [
        { value: 1, label: this.$t('pages.opeTypeOptions1') },
        { value: 2, label: this.$t('pages.noAlarm') },
        { value: 4, label: this.$t('pages.opeTypeOptions2') }
      ],
      exceptColModel: [
        { prop: 'bussType', label: 'bizType', width: '120', formatter: this.bussTypeFormatter },
        { prop: 'names', label: 'excepeData', width: '200', formatter: this.exceptNameFormatter },
        { prop: 'opeType', label: 'excepeOperation', width: '100', formatter: this.opeTypeFormatter }
      ],
      adbColModel: [
        { prop: 'manufacturer', label: 'devManufacturer', width: '120' },
        { prop: 'matchType', label: 'matchKind', width: '120', formatter: this.matchTypeFormatter }
      ],
      blueToothColModel: [
        { prop: 'deviceName', label: 'deviceName', width: '120' },
        { prop: 'matchType', label: 'matchKind', width: '120', formatter: this.matchTypeFormatter },
        { prop: 'macAddress', label: this.$t('pages.fileOutgoing_Msg80'), width: '120' }
        // { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
        //   buttons: [
        //     { label: 'edit', click: this.handleUpdateBlueTooth }
        //   ]
        // }
      ],
      ipColModel: [
        { prop: 'startIP', label: 'startIP', width: '120' },
        { prop: 'endIP', label: 'endIP', width: '120' },
        { prop: 'domain', label: 'domain', width: '120' },
        { prop: 'limitType', label: 'limitType', width: '100', formatter: this.limitTypeFormatter }
      ],
      exceptRules: {

      },
      adbExceptRules: {

      },
      blueToothExceptRules: {
        macAddress: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.macAddressValidator, trigger: 'blur' }
        ]
      },
      ipExceptRules: {
        startIP: [
          { validator: this.startIpValidator, trigger: 'blur' }
        ],
        endIP: [
          { validator: this.endIpValidator, trigger: 'blur' }
        ],
        domain: [
          { validator: this.domainValidator, trigger: 'blur' }
        ]
      },
      toolTreeData: [
        { id: 'qq.exe', label: 'QQ' },
        { id: 'QIDIAN.EXE', label: this.$t('pages.enterpriseQQ') }, // 新版企业QQ的进程名； 旧版进程名： QQEIM.EXE 上传到后端时，如果存在，添加一下
        { id: 'WeChat.exe', label: this.$t('pages.wechat') },
        { id: 'WXWork.exe', label: this.$t('pages.enterpriseWeChat') },
        { id: 'DingTalk.exe', label: this.$t('pages.dingTalk') },
        { id: 'FeiQ.exe', label: this.$t('pages.feiQ') },
        { id: 'AliIM.exe', label: this.$t('pages.aliTalk') },
        { id: 'FeiShu.exe', label: this.$t('pages.feiShu') },
        { id: 'skype.exe', label: 'Skype' },
        { id: 'tim.exe', label: this.$t('pages.tim') },
        { id: 'SHIYELINE.EXE', label: this.$t('pages.shiyeLine') },
        { id: 'EMOBILE10.EXE', label: this.$t('pages.emobile10') }
      ],
      netDiskTreeData: [
        { id: 'BAIDUNETDISK', label: this.$t('pages.softBaiDuNetDisk') },
        { id: 'WxDrive', label: this.$t('pages.QYWXMicroDisk') }
      ],
      bussType: '',
      exceptVisible: false,
      exceptDetailVisible: false,
      ipExceptVisible: false,
      rowDatas: [],
      exceptDetail: {},
      editButton: true,
      delButton: true,
      adbDelButton: true,
      blueToothDelButton: true,
      ipDelButton: true,
      defaultExceptDetail: {
        id: undefined,
        opeType: 1,
        bussType: undefined,
        names: '',
        exceptData: []
      },
      adbExceptVisible: false,
      adbExceptDetail: {
        manufacturer: [],
        matchType: 0
      },
      blueToothExceptVisible: false,
      blueToothMacAddress: [],
      blueToothExceptDetail: {
        deviceName: '',
        matchType: 1,
        macAddress: ''
      },
      ipExceptDetail: {
        startIP: '',
        endIP: '',
        domain: '',
        limitType: 0
      },
      matchTypeOption: [
        { value: 0, label: this.$t('pages.matchTypeOptions1') },
        { value: 1, label: this.$t('pages.matchTypeOptions2') }
      ],
      limitTypeOption: [
        { value: 0, label: this.$t('pages.fileOutgoing_Msg83') },
        { value: 1, label: this.$t('pages.fileOutgoing_Msg84') },
        { value: 2, label: this.$t('table.domain') }
      ]
    }
  },
  computed: {

  },
  watch: {
  },
  activated() {

  },
  created() {
    
  },
  methods: {
    mailChange() {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const newMap = new Map()
      //  自动添加前缀
      var list = []
      list = [...this.exceptDetail.exceptData]
      list.forEach(item => {
        const len = item.split('#')
        if (reg.test(item) || reg1.test(item) || len.length === 2) {
          newMap[item] = ''
        }
      })
      this.exceptDetail.exceptData = Object.keys(newMap) || []
    },
    blueToothChange() {
      const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      const list = []
      this.blueToothMacAddress.forEach(item => {
        if (macRegex.test(item)) {
          list.push(item)
        }
      })
      this.blueToothMacAddress = [...list]
      const tempMacAddRess = [...this.blueToothMacAddress]
      this.exceptDetail.exceptData = []
      tempMacAddRess.forEach((address, index) => {
        const obj = {
          id: index,
          deviceName: '',
          matchType: 1,
          macAddress: address
        }
        this.exceptDetail.exceptData.push(obj)
      })
    },
    shareIIpChange() {
      const newMap = new Map()
      //  自动添加前缀
      var list = []
      list = [...this.exceptDetail.exceptData]
      list.forEach(item => {
        if (isIPv4(item) || isIPv6(item)) {
          newMap[item] = ''
        }
      })
      this.exceptDetail.exceptData = Object.keys(newMap) || []
    },
    getIpTitle(bussType) {
      const titles = {
        1017: this.ipStatus === 'create'
          ? this.$t('text.add') + this.$t('pages.fileOutgoing_Msg81')
          : this.$t('text.edit') + this.$t('pages.fileOutgoing_Msg81'),
        1024: this.ipStatus === 'create'
          ? this.$t('text.add') + this.$t('pages.fileOutgoing_Msg82')
          : this.$t('text.edit') + this.$t('pages.fileOutgoing_Msg82')
      }
      return titles[bussType] || ''
    },
    handleDeleteAdb() {
      this.$refs.adbListTable.getSelectedDatas().forEach(item => {
        this.exceptDetail.exceptData.splice(this.rowDatas.indexOf(item), 1)
      })
    },
    handleCreateAdb() {
      this.adbExceptVisible = true
      this.adbExceptDetail = {
        manufacturer: [],
        matchType: 0
      }
    },
    handleCreateBlueTooth() {
      this.blueToothExceptVisible = true
      this.blueToothExceptDetail = {
        deviceName: '',
        matchType: 1,
        macAddress: ''
      }
    },
    handleCreateIp() {
      this.ipStatus = 'create'
      this.ipExceptVisible = true
      this.ipExceptDetail = {
        startIP: '',
        endIP: '',
        domain: '',
        limitType: 0
      }
      this.$nextTick(() => {
        this.$refs.ipExceptForm.clearValidate()
      })
    },
    handleDeleteIp() {
      this.$refs.ipListTable.getSelectedDatas().forEach(item => {
        this.exceptDetail.exceptData.splice(this.rowDatas.indexOf(item), 1)
      })
    },
    handleUpdateBlueTooth(row) {

    },
    handleDeleteBlueTooth() {
      this.$refs.blueToothListTable.getSelectedDatas().forEach(item => {
        this.exceptDetail.exceptData.splice(this.rowDatas.indexOf(item), 1)
      })
    },
    handleCreateBlueToothExceData() {
      this.$refs['blueToothExceptForm'].validate((valid) => {
        if (valid) {
          var distinctFlag = false
          this.exceptDetail.exceptData.forEach(item => {
            if (item.macAddress === this.blueToothExceptDetail.macAddress) {
              distinctFlag = true
            }
          })
          if (distinctFlag) {
            this.$message({
              message: this.$t('pages.fileOutgoing_Msg85'),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.exceptDetail.exceptData.push({
            id: new Date().getTime(),
            deviceName: this.blueToothExceptDetail.deviceName,
            matchType: this.blueToothExceptDetail.matchType,
            macAddress: this.blueToothExceptDetail.macAddress
          })
          this.blueToothExceptVisible = false
        }
      })
    },
    handleCreateIpExceData() {
      this.$refs['ipExceptForm'].validate((valid) => {
        if (valid) {
          var distinctFlag = false
          this.exceptDetail.exceptData.forEach(item => {
            if (this.ipExceptDetail.limitType === 0 || this.ipExceptDetail.limitType === 1) {
              if (item.startIP === this.ipExceptDetail.startIP && item.endIP === this.ipExceptDetail.endIP) {
                distinctFlag = true
              }
            } else {
              if (item.domain === this.ipExceptDetail.domain) {
                distinctFlag = true
              }
            }
          })
          if (distinctFlag) {
            this.$message({
              message: this.$t('pages.fileOutgoing_Msg86'),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.exceptDetail.exceptData.push({
            id: new Date().getTime(),
            startIP: this.ipExceptDetail.limitType != 2 ? this.ipExceptDetail.startIP : '',
            endIP: this.ipExceptDetail.limitType != 2 ? this.ipExceptDetail.endIP : '',
            domain: this.ipExceptDetail.limitType == 2 ? this.ipExceptDetail.domain : '',
            limitType: this.ipExceptDetail.limitType
          })
          this.ipExceptVisible = false
        }
      })
    },
    handleCreateAdbExceData() {
      if (this.adbExceptDetail.manufacturer.length === 0) {
        this.$message({
          message: this.$t('pages.fileOutgoing_Msg87'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const manufacturer = []
      this.exceptDetail.exceptData.forEach(item => {
        manufacturer.push(item.manufacturer)
      })
      this.adbExceptDetail.manufacturer.forEach((item, index) => {
        if (manufacturer.indexOf(item) === -1) {
          this.exceptDetail.exceptData.push({
            id: new Date().getTime() + '' + index,
            manufacturer: item,
            matchType: this.adbExceptDetail.matchType
          })
        }
      })
      this.adbExceptVisible = false
    },
    getMailAddress(data) {
      data.forEach(item => {
        if (this.exceptDetail.exceptData.indexOf(item) < 0) {
          this.exceptDetail.exceptData.push(item)
        }
      })
    },
    getUrlAddress(data) {
      data.forEach(item => {
        if (this.exceptDetail.exceptData.indexOf(item) < 0) {
          this.exceptDetail.exceptData.push(item)
        }
      })
    },
    selectUSBEnd(array) {
      array.forEach(item => {
        if (this.exceptDetail.exceptData.indexOf(item.usbCode) < 0) {
          this.exceptDetail.exceptData.push(item.usbCode)
        }
      })
    },
    blueSubmitEnd(blueTooths) {
      blueTooths.forEach(item => {
        if (this.blueToothMacAddress.indexOf(item.blueAddress) < 0) {
          this.blueToothMacAddress.push(item.blueAddress)
        }
      })
      this.blueToothChange()
    },
    handleBlueToothImport() {
      // 从终端读取
      this.$refs.blueToothDlg.show()
    },
    handleUsbLibImport() {  // 从usb导入
      this.$refs.usbSelectDlg.show()
    },
    handleMailLibImport() { // 从邮箱库导入
      this.$refs.mailImportTableDlg.show()
    },
    handleUrlLibImport() { // 网址库导入
      this.$refs.urlImportTableDlg.show()
    },
    handleCreateExceData() {
      var obj = {
        id: this.bussType,
        data: this.rowDatas
      }
      this.$emit('handExceptData', obj);
      this.exceptVisible = false
    },
    createObjExceptData() { 
      const exceptData = []
      var key = ''
      this.rowDatas.forEach(item => {
        item.names.forEach(row => {
          if (this.bussType === 1014) {
            key = 'macAddress'
            exceptData.push(row.macAddress)
          }
        })
      })
      const filterExceptData = []
      this.exceptDetail.exceptData.forEach(item => {
        if (exceptData.indexOf(item[key]) < 0) {
          filterExceptData.push(item)
        }
      })
      var opeTypeFlag = false
      this.rowDatas.forEach(item => {
        if (item.opeType === this.exceptDetail.opeType) {
          opeTypeFlag = true
          filterExceptData.forEach(row => { item.names.push(row) })
          item.names.forEach((row, index) => { row.id = index })
        }
      })
      if (!opeTypeFlag && filterExceptData.length > 0) {
        this.rowDatas.push({
          opeType: this.exceptDetail.opeType,
          names: [...filterExceptData],
          id: new Date().getTime(),
          bussType: this.bussType
        })
      }
      this.exceptDetailVisible = false
    },
    createIpExceptData() { 
      const ipExceptData = []
      const domainExceptData = []
      this.rowDatas.forEach(item => {
        item.names.forEach(row => {
          if (row.limitType != 2) {
            ipExceptData.push({
              startIP: row.startIP,
              endIP: row.endIP
            })
          } else {
            domainExceptData.push({
              domain: row.domain
            })
          }
        })
      })
      const filterExceptData = []
      this.exceptDetail.exceptData.forEach(item => {
        if (item.limitType != 2) {
          var ipDistinctFlag = false
          ipExceptData.forEach(row => {
            if (row.startIP === item.startIP && row.endIP === item.endIP) {
              ipDistinctFlag = true
            }
          })
          if (!ipDistinctFlag) {
            filterExceptData.push({
              startIP: item.startIP,
              endIP: item.endIP,
              domain: '',
              limitType: item.limitType
            })
          }
        } else {
          var domainDistinctFlag = false
          domainExceptData.forEach(row => {
            if (row.domain === item.domain) {
              domainDistinctFlag = true
            }
          })
          if (!domainDistinctFlag) {
            filterExceptData.push({
              startIP: '',
              endIP: '',
              domain: item.domain,
              limitType: item.limitType
            })
          }
        }
      })
      var opeTypeFlag = false
      this.rowDatas.forEach(item => {
        if (item.opeType === this.exceptDetail.opeType) {
          opeTypeFlag = true
          filterExceptData.forEach(row => { item.names.push(row) })
          item.names.forEach((row, index) => { row.id = index })
        }
      })
      if (!opeTypeFlag && filterExceptData.length > 0) {
        this.rowDatas.push({
          opeType: this.exceptDetail.opeType,
          names: [...filterExceptData],
          id: new Date().getTime(),
          bussType: this.bussType
        })
      }
      this.exceptDetailVisible = false
    },
    createExceptData() {
      this.$refs['exceptForm'].validate((valid) => {
        if (valid) {
          if (this.exceptDetail.exceptData.length === 0) {
            this.$message({
              message: this.nameMap[this.bussType] ? this.nameMap[this.bussType] + this.$t('text.cantNull') : this.$t('table.excepeData') + this.$t('text.cantNull'),
              type: 'error',
              duration: 2000
            })
            return
          }
          if (this.bussType === 1014) {
            // ADB的特殊处理
            this.createObjExceptData()
            return
          }
          if (this.bussType === 1017 || this.bussType === 1024) {
            this.createIpExceptData()
            return
          }
          const exceptData = []
          // 拿到已存在的例外数据
          this.rowDatas.forEach((item, index) => {
            const names = item.names.split(',')
            names.forEach(name => {
              exceptData.push(name)
            })
          })
          // 过滤重复数据
          const filterExceptData = []
          this.exceptDetail.exceptData.forEach(item => {
            if (exceptData.indexOf(item) < 0) {
              filterExceptData.push(item)
            }
          })
          var opeTypeFlag = false
          this.rowDatas.forEach((item, index) => {
            if (item.opeType === this.exceptDetail.opeType) {
              opeTypeFlag = true
              const dataText = filterExceptData.join(',')
              item.names += dataText ? `,${dataText}` : ''
            }
          })
          if (!opeTypeFlag && filterExceptData.length > 0) {
            this.rowDatas.push({
              opeType: this.exceptDetail.opeType,
              names: filterExceptData.join(','),
              id: new Date().getTime(),
              bussType: this.bussType
            })
          }
          this.exceptDetailVisible = false
        }
      })
    },
    updateExceptData() {
      this.$refs['exceptForm'].validate((valid) => {
        if (valid) {
          if (this.exceptDetail.exceptData.length === 0) {
            this.$message({
              message: this.nameMap[this.bussType] + this.$t('text.cantNull'),
              type: 'error',
              duration: 2000
            })
            return
          }
          // 先将自身移除
          this.rowDatas = this.rowDatas.filter(item => item.id != this.exceptDetail.id)
          if (this.bussType === 1014) {
            // ADB的特殊处理
            this.createObjExceptData()
            return
          }
          if (this.bussType === 1017 || this.bussType === 1024) {
            this.createIpExceptData()
            return
          }
          const exceptData = []
          // 拿到已存在的例外数据
          this.rowDatas.forEach((item, index) => {
            const names = item.names.split(',')
            names.forEach(name => {
              exceptData.push(name)
            })
          })
          // 过滤重复数据
          const filterExceptData = []
          this.exceptDetail.exceptData.forEach(item => {
            if (exceptData.indexOf(item) < 0) {
              filterExceptData.push(item)
            }
          })
          // 修改
          var opeTypeFlag = false
          const dataText = filterExceptData.join(',')
          this.rowDatas.forEach(item => {
            if (item.opeType === this.exceptDetail.opeType) {
              item.names += dataText ? `,${dataText}` : ''
              opeTypeFlag = true
            }
          })
          if (!opeTypeFlag && dataText) {
            this.rowDatas.push({
              opeType: this.exceptDetail.opeType,
              names: dataText,
              id: new Date().getTime(),
              bussType: this.bussType
            })
          }
          this.exceptDetailVisible = false
        }
      })
    },
    handleDrag() {

    },
    exceptSelectionChange(rowDatas) {
      rowDatas.length == 1 ? this.editButton = false : this.editButton = true
      rowDatas.length != 0 ? this.delButton = false : this.delButton = true
    },
    adbExceptSelectionChange(rowDatas) {
      rowDatas.length != 0 ? this.adbDelButton = false : this.adbDelButton = true
    },
    blueToolthExceptSelectionChange(rowDatas) {
      rowDatas.length != 0 ? this.blueToothDelButton = false : this.blueToothDelButton = true
    },
    ipExceptSelectionChange(rowDatas) {
      rowDatas.length != 0 ? this.ipDelButton = false : this.ipDelButton = true
    },
    handleDeleteExcept() {
      this.$refs.exceptListTable.getSelectedDatas().forEach(item => {
        this.rowDatas.splice(this.rowDatas.indexOf(item), 1)
      })
    },
    macAddressValidator(rule, value, callback) {
      const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      if (!macRegex.test(value)) {
        callback(new Error(this.$t('pages.fileOutgoing_Msg88')));
      } else {
        callback();
      }
    },
    startIpValidator(rule, value, callback) {
      if (this.ipExceptDetail.limitType === 0 || this.ipExceptDetail.limitType === 1) {
        if (!value) { 
          callback(new Error(this.$t('pages.fileOutgoing_Msg89')));
        } else if (this.ipExceptDetail.limitType === 0 && !isIPv4(value)) {
          callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')));
        } else if (this.ipExceptDetail.limitType === 1 && !isIPv6(value)) {
          callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')));
        } else {
          callback();
        }
      } else {
        callback()
      }
    },
    endIpValidator(rule, value, callback) { 
      if (this.ipExceptDetail.limitType === 0 || this.ipExceptDetail.limitType === 1) {
        if (!value) { 
          callback(new Error(this.$t('pages.fileOutgoing_Msg90')));
        } else if (this.ipExceptDetail.limitType === 0 && !isIPv4(value)) {
          callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')));
        } else if (this.ipExceptDetail.limitType === 1 && !isIPv6(value)) {
          callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')));
        } else {
          callback();
        }
      } else {
        callback()
      }
    },
    domainValidator(rule, value, callback) { 
      if (this.ipExceptDetail.limitType === 2 && !value) {
        callback(new Error(this.$t('table.domain') + this.$t('text.cantNull')));
      } else {
        callback();
      }
    },
    handleLimitTypeChange() {
      this.$refs['ipExceptForm'].clearValidate()
    },
    matchTypeFormatter: function(row, data) {
      if (data === 0) {
        return this.$t('pages.blueTooth_blueTooth_Msg12')
      } else {
        return this.$t('pages.blueTooth_blueTooth_Msg11')
      }
    },
    limitTypeFormatter: function(row, data) {
      if (data === 0) {
        return this.$t('table.IPv4Addr')
      } else if (data === 1) {
        return this.$t('table.IPv6Addr')
      } else {
        return this.$t('table.domain')
      }
    },
    resetData() {
      this.rowDatas = []
      this.exceptDetail = {}
      this.editButton = true
      this.delButton = true
    },
    handleUpdateExcept() {
      this.exceptDetail = Object.assign({}, this.$refs.exceptListTable.getSelectedDatas()[0])
      if (this.bussType === 1014 || this.bussType === 1017 || this.bussType === 1024) {
        // 蓝牙、FTP、远程桌面的特殊处理
        this.exceptDetail.names.length > 0 ? this.$set(this.exceptDetail, 'exceptData', this.exceptDetail.names) : this.$set(this.exceptDetail, 'exceptData', [])
        if (this.bussType === 1014) {
          // 蓝牙特殊点，只匹配蓝牙地址且默认为完全匹配因此就不提供选择了
          this.blueToothMacAddress = []
          this.exceptDetail.names.forEach(item => {
            this.blueToothMacAddress.push(item.macAddress)
          })
        }
      } else {
        // 这边得用$set进行赋值，不然绑定exceptDetail.exceptData对象的时候，可能会由于第一句代码导致exceptData属性缺失，导致双向绑定失效
        this.exceptDetail.names.length > 0 ? this.$set(this.exceptDetail, 'exceptData', this.exceptDetail.names.split(',')) : this.$set(this.exceptDetail, 'exceptData', [])
      }
      this.textStatus = 'update'
      this.exceptDetailVisible = true
    },
    handleCreateExcept() {
      this.textStatus = 'create'
      this.exceptDetail.opeType = 1
      this.exceptDetail.id = undefined
      this.exceptDetail.names = ''
      this.exceptDetail.exceptData = []
      if (this.bussType === 1014) {
        this.blueToothMacAddress = []
      }
      this.$nextTick(() => {
        this.$refs.exceptForm.clearValidate()
      })
      this.exceptDetailVisible = true
    },

    bussTypeFormatter: function(row, data) {
      let label = ''
      this.bussTypeOptions.forEach(option => {
        if (option.value === data) {
          label = option.label
        }
      })
      return label
    },
    opeTypeFormatter: function(row, data) {
      var label = ''
      this.opeTypeOptions.forEach(item => {
        if (item.value === data) {
          label = item.label
        } 
      })
      return label
    },
    exceptNameFormatter: function(row, data) {
      if (this.bussType === 1014) { 
        const rows = []
        data.forEach(item => { 
          var msg = '{' + this.$t('pages.fileOutgoing_Msg80') + ': ' + item.macAddress
          msg += ', ' + this.$t('table.matchKind') + ': '  
          item.matchType === 0 ? msg += this.$t('pages.matchTypeOptions1') : msg += this.$t('pages.matchTypeOptions2')
          msg += '}'
          rows.push(msg)
        })
        return rows.join(',')
      } else if (this.bussType === 1017 || this.bussType === 1024) {
        const rows = []
        data.forEach(item => { 
          if (item.limitType != 2) {
            let msg = '{' + this.$t('table.startIP') + ': ' + item.startIP + ', ' + this.$t('table.endIP') + ': ' + item.endIP + ', '
            msg += this.$t('table.matchKind') + ': ' + this.$t('table.domain') + ': \'\'' + ', ' + this.$t('table.matchKind') + ': '
            if (item.limitType === 0) {
              msg += this.$t('pages.fileOutgoing_Msg83')
            } else if (item.limitType === 1) {
              msg += this.$t('pages.fileOutgoing_Msg84')
            } else {
              msg += this.$t('table.domain')
            }
            msg += '}'
            rows.push(msg)
          } else {
            let msg = '{' + this.$t('table.startIP') + ': \'\', ' + this.$t('table.endIP') + ': \'\', ' 
            msg += this.$t('table.domain') + ': ' + item.domain + ', ' + this.$t('table.matchKind') + ': '
            if (item.limitType === 0) {
              msg += this.$t('pages.fileOutgoing_Msg83')
            } else if (item.limitType === 1) {
              msg += this.$t('pages.fileOutgoing_Msg84')
            } else {
              msg += this.$t('table.domain')
            }
            msg += '}'
            rows.push(msg)
          }
        })
        return rows.join(',')
      } else if (this.bussType === 1016) {
        // 网盘例外，网盘进程名转为具体的名称
        const rows = []
        data.split(',').forEach(item => {
          if (item === 'BAIDUNETDISK') {
            rows.push(this.$t('pages.softBaiDuNetDisk'))
          } else {
            rows.push(this.$t('pages.QYWXMicroDisk'))
          }
        })
        return rows.join(',')
      } else if (this.bussType === 1004) {
        const processNameMap = {
          'qq.exe': this.$t('pages.qq'),
          'QIDIAN.EXE': this.$t('pages.enterpriseQQ'),
          'WeChat.exe': this.$t('pages.wechat'),
          'WXWork.exe': this.$t('pages.enterpriseWeChat'),
          'DingTalk.exe': this.$t('pages.dingTalk'),
          'FeiQ.exe': this.$t('pages.feiQ'),
          'AliIM.exe': this.$t('pages.aliTalk'),
          'FeiShu.exe': this.$t('pages.feiShu'),
          'skype.exe': this.$t('pages.skype'),
          'tim.exe': this.$t('pages.tim'),
          'SHIYELINE.EXE': this.$t('pages.shiyeLine'),
          'EMOBILE10.EXE': this.$t('pages.emobile10')
        };
        const rows = data.split(',').map(item => processNameMap[item] || item);
        return rows.join(',');
      }
      return data
    },
    formatterObjRows(bussType, row) {
      const noCheckRows = []
      const noAlarmRows = []
      const noBlockRows = []
      const rows = []
      row.forEach(item => { 
        const obj = {}
        if (bussType === 1014) {
          obj.deviceName = item.deviceName
          obj.matchType = item.matchType
          obj.macAddress = item.macAddress
        } else if (bussType === 1017 || bussType === 1024) { 
          obj.startIP = item.startIP
          obj.endIP = item.endIP
          obj.domain = item.domain
          obj.limitType = item.limitType
        }
        if (item.opeType === 1) {
          noCheckRows.push(obj)
        } else if (item.opeType === 2) {
          noAlarmRows.push(obj)
        } else {
          noBlockRows.push(obj)
        }
      })
      if (noCheckRows.length > 0) {
        const obj = {
          id: new Date().getTime(),
          opeType: 1,
          bussType: bussType,
          names: [...noCheckRows],
          exceptData: []
        }
        rows.push(obj)
      }
      if (noAlarmRows.length > 0) {
        const obj = {
          id: new Date().getTime(),
          opeType: 2,
          bussType: bussType,
          names: [...noAlarmRows],
          exceptData: []
        }
        rows.push(obj)
      }
      if (noBlockRows.length > 0) {
        const obj = {
          id: new Date().getTime(),
          opeType: 4,
          bussType: bussType,
          names: [...noBlockRows],
          exceptData: []
        }
        rows.push(obj)
      }     
      rows.forEach((item, index) => {
        item.id = index
      })
      return rows
    },
    show(bussType, row) {
      this.resetData()
      this.exceptDetail = Object.assign({}, this.defaultExceptDetail)
      this.exceptVisible = true
      this.exceptDetail.bussType = bussType
      this.bussType = bussType
      if (bussType === 1014 || bussType === 1017 || bussType === 1024) {
        const rows = this.formatterObjRows(bussType, row)
        this.rowDatas = [...rows]
        return
      }
      const fieldName = this.fieldNameMap[bussType]
      const noCheckRows = []
      const noAlarmRows = []
      const noBlockRows = []
      row.forEach(item => { 
        if (item.opeType === 1) {
          noCheckRows.push(item[fieldName])
        } else if (item.opeType === 2) {
          noAlarmRows.push(item[fieldName])
        } else {
          noBlockRows.push(item[fieldName])
        }
      })
      const rows = this.formatterRows(noCheckRows, noAlarmRows, noBlockRows, bussType)
      this.rowDatas = [...rows]
    },
    formatterRows(noCheckRows, noAlarmRows, noBlockRows, bussType) {
      const rows = []
      if (noCheckRows.length > 0) {
        const obj = {
          id: new Date().getTime(),
          opeType: 1,
          bussType: bussType,
          names: noCheckRows.join(','),
          exceptData: []
        }
        rows.push(obj)
      }
      if (noAlarmRows.length > 0) {
        const obj = {
          id: new Date().getTime(),
          opeType: 2,
          bussType: bussType,
          names: noAlarmRows.join(','),
          exceptData: []
        }
        rows.push(obj)
      }
      if (noBlockRows.length > 0) {
        const obj = {
          id: new Date().getTime(),
          opeType: 4,
          bussType: bussType,
          names: noBlockRows.join(','),
          exceptData: []
        }
        rows.push(obj)
      }     
      rows.forEach((item, index) => {
        item.id = index
      })
      return rows
    }
  }
}
</script>
