<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend :scope="1"/>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>

      </div>
      <grid-table
        ref="checkStatisticsConfigList"
        row-key="recordId"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <!-- 策略弹窗 -->
    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :title="textMap[dialogStatus]"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="740px"
      @dragDialog="handleDrag"
      @closed="dialogStatus=''"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="150px"
        style="width: 670px;"
      >
        <stg-target-form-item
          ref="formItem"
          label-width="100"
          :stg-code="248"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <!-- <StgBaseFormItem
          :stg-code="248"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        /> -->
        <FormItem label-width="100" :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem label-width="100" :label="$t('table.remark')" >
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" label-width="100" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <!-- 统计规则 -->
        <el-divider content-position="left">{{ $t('pages.fileOutgoing_Msg27') }}</el-divider>
        <!-- 文件数量 -->
        <FormItem :label="$t('pages.fileOutgoing_Msg1')" :tooltip-content="$t('pages.fileOutgoing_Msg40')" tooltip-placement="bottom-start">
          <el-checkbox v-model="temp.countsChecked" :disabled="!formable" style="margin-left: 5px;" @change="fileNumChange()">
          </el-checkbox>
          <i18n path="pages.fileOutgoing_Msg15" >
            <template slot="num">
              <el-input-number v-model="temp.maxCounts" :min="1" :max="99999" :controls="false" :precision="0" :disabled="temp.countsAble || !formable" style="width: 75px; margin: 0 4px" @blur="fileNumBlur()"></el-input-number>
            </template>
          </i18n>
        </FormItem>
        <!-- 文件大小 -->
        <FormItem :label="$t('pages.fileOutgoing_Msg5')" :tooltip-content="$t('pages.fileOutgoing_Msg46')" tooltip-placement="bottom-start">
          <el-checkbox v-model="temp.maxFileSizeChecked" :disabled="!formable" style="margin-left: 5px;" @change="maxFileSizeChange()">
          </el-checkbox>
          <i18n path="pages.fileOutgoing_Msg6" style="margin-left: -28px;">
            <template slot="num">
              <el-input-number v-model="temp.maxFileSize" :min="1" :max="99999" :controls="false" :precision="0" :disabled="temp.maxFileSizeAble || !formable" style="width: 75px; margin: 0 4px" @blur="maxFileSizeBlur()"></el-input-number>
            </template>
            <template slot="size">
              <el-select v-model="temp.sizeUnit" style="width: 60px;margin-top: -5px;margin-right: 5px;" :disabled="temp.maxFileSizeAble || !formable">
                <el-option
                  v-for="item in units"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
          </i18n>
          <br>
          <el-checkbox v-model="temp.maxFileSizeExChecked" :disabled="!formable" style="margin-top: 10px;margin-left: 5px;" @change="maxFileSizeExChange()">
          </el-checkbox>
          <i18n path="pages.fileOutgoing_Msg20" >
            <template slot="num">
              <el-input-number v-model="temp.maxFileSizeEx" :min="1" :max="99999" :controls="false" :precision="0" :disabled="temp.maxFileSizeExAble || !formable" style="width: 75px; margin: 0 4px" @blur="maxFileSizeExBlur()"></el-input-number>
            </template>
            <template slot="size">
              <el-select v-model="temp.sizeUnitEx" style="width: 60px;margin-top: -5px;margin-right: 5px;" :disabled="temp.maxFileSizeExAble || !formable">
                <el-option
                  v-for="item in units"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
          </i18n>
        </FormItem>
        <!-- 时间跨度 -->
        <FormItem :label="$t('pages.fileOutgoing_Msg3')" style="margin-top: 10px;">
          <el-tooltip slot="tooltip" effect="dark" placement="left" >
            <div slot="content">
              {{ $t('pages.fileOutgoing_Msg41') }}
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <el-checkbox v-model="temp.timeSpan" :disabled="!formable" style="margin-left: 5px;" @change="timeSpanChange()">
          </el-checkbox>
          <i18n path="pages.fileOutgoing_Msg4" >
            <template slot="num">
              <el-input-number v-model="temp.days" :min="0" :max="365" :controls="false" :precision="0" :disabled="temp.daysAble || !formable" style="width: 75px; margin: 0 4px" @blur="daysBlur()"></el-input-number>
            </template>
            <template slot="num1">
              <el-input-number v-model="temp.hours" :min="0" :max="23" :controls="false" :precision="0" :disabled="temp.hoursAble || !formable" style="width: 75px; margin: 0 4px" @blur="hoursBlur()"></el-input-number>
            </template>
            <template slot="num2">
              <el-input-number v-model="temp.minutes" :min="0" :max="59" :controls="false" :precision="0" :disabled="temp.minutesAble || !formable" style="width: 75px; margin: 0 4px" @blur="minutesBlur()"></el-input-number>
            </template>
          </i18n>
        </FormItem>
        <!-- 管控统计文件类型 -->
        <FormItem :label="$t('pages.fileOutgoing_Msg47')" prop="ctrlFileType">
          <el-row style="margin-left: 5px;">
            <el-col :span="18">
              <el-checkbox-group v-model="temp.ctrlFileType" style="display: inline-block;" :disabled="!formable">
                <el-checkbox :label="1">{{ $t('pages.fileOutgoing_Msg48') }}</el-checkbox>
                <el-checkbox :label="2">{{ $t('pages.fileOutgoing_Msg49') }}</el-checkbox>
              </el-checkbox-group>
            </el-col>
          </el-row>
        </FormItem>
        <!-- 管控文件后缀 -->
        <FormItem :label="$t('pages.fileOutgoing_Msg50')" :tooltip-content="$t('pages.fileOutgoing_Msg53')" tooltip-placement="bottom-start">
          <tag :list="temp.fileSuffixList" border :disabled="!formable" class="input-with-button" style="width: calc(100% - 94px); min-height: 30px; background-color:#f5f5f5;margin-left: 5px;" @tagChange="suffixChange('fileSuffix')"/>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" :disabled="!formable" size="mini" @click="handleFileSuffixImport()">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>

        <!-- 执行规则 -->
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>

        <FormItem label-width="40px" prop="action">
          <span>{{ $t('pages.fileOutgoing_Msg61') }}</span>
        </FormItem>

        <!-- 响应规则 -->
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <!-- 外发设置 -->
        <el-row style="margin-left: 30px;margin-top: 10px;">
          <el-col :span="21">
            <el-checkbox-group v-model="temp.approvalType" style="display: inline-block;" :disabled="!formable" @change="handleApprovalTypeChange">
              <el-checkbox :label="1">{{ $t('pages.fileOutgoing_Msg59') }}</el-checkbox>
              <el-checkbox :label="2" :disabled="temp.approvalType.indexOf(1) == -1">{{ $t('pages.burnMsg7') }}</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>

        <ResponseContent
          :select-style="{ 'margin-top': '5px', 'margin-left': '13px', 'margin-top': '20px' }"
          :status="dialogStatus"
          :show-select="true"
          :rule-type-name="ruleTypeName"
          :editable="formable"
          read-only
          :prop-check-rule="!!temp.isAlarm"
          :show-check-rule="true"
          :prop-rule-id="temp.propRuleId"
          @ruleIsCheck="getRuleIsCheck"
          @getRuleId="getRuleId"
        />
        <el-row style="margin-left: 30px;margin-top: 10px;">
          <el-col :span="21">
            <el-checkbox v-model="temp.secondAlarmCondition" :disabled="temp.isAlarm === 0 || !formable" :true-label="1" :false-label="0">{{ $t('pages.fileOutgoing_Msg70') }}</el-checkbox>
            <el-button :disabled="temp.secondAlarmCondition === 0" icon="el-icon-setting" :title="$t('pages.fileOutgoing_Msg58')" @click="handleAgainResponse"></el-button>
            <!-- <i class="el-icon-info" :title="$t('pages.fileOutgoing_Msg62')"/> -->
          </el-col>
        </el-row>
        <!-- 业务模块 -->
        <el-divider content-position="left">{{ $t('pages.fileOutgoing_Msg105') }}</el-divider>
        <FormItem label-width="40px" prop="basicModuleTemp">
          <div style="margin-left: -9px;">
            <el-button v-if="formable" type="text" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
            <el-button v-if="formable" type="text" @click="selectAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
            <el-row style="margin-top: 5px;">
              <el-col :span="12">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isFtpFileAware" :disabled="!formable || configDisabled(1017)" :true-label="1017" :false-label="0">
                    {{ $t('table.ftpUploadFileSumAll') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="ftpDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1017)"></el-button>
                </div>
              </el-col>
              <el-col :span="12">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isUsbDev" :disabled="!formable || configDisabled(1002)" :true-label="1002" :false-label="0">
                    {{ $t('pages.stgMessage1') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="usbFileDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1002)"></el-button>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isRemoteDir" :disabled="!formable || configDisabled(1011)" :true-label="1011" :false-label="0">
                    {{ $t('pages.stgLabelRemoteShareFile') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="shareDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1011)"></el-button>
                </div>
              </el-col>
              <el-col :span="12">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isChatFile" :disabled="!formable || configDisabled(1004)" :true-label="1004" :false-label="0">
                    {{ $t('pages.fileOutgoing_Msg92') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="imFileDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1004)"></el-button>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-checkbox v-model="basicModuleTemp.isCDBurn" :disabled="!formable || configDisabled(1001)" :true-label="1001" :false-label="0">
                  {{ $t('pages.fileOutgoing_Msg93') }}
                </el-checkbox>
              </el-col> 
              <el-col :span="12">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isBrowerSendFileOption" :disabled="!formable || configDisabled(1006)" :true-label="1006" :false-label="0">
                    {{ $t('table.webUploadFileSumAll') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="browerFileDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1006)"></el-button>
                </div>
              </el-col>
            </el-row>
            <el-row >
              <el-col :span="12" style="margin-top: -10px;">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isEmailAttach" :disabled="!formable || configDisabled(1012)" :true-label="1012" :false-label="0">
                    {{ $t('pages.fileOutgoing_Msg94') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="emailAttachDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1012)"></el-button>
                </div>
              </el-col>
              <el-col :span="12" style="margin-top: -10px;">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isBlueFile" :disabled="!formable || configDisabled(1014)" :true-label="1014" :false-label="0">
                    {{ $t('table.bluetoothSendFileSumAll') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="blueToothDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1014)"></el-button>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isADBFile" :disabled="!formable || configDisabled(1020)" :true-label="1020" :false-label="0">
                    {{ $t('table.adbSendFileSumAll') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="adbDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1020)"></el-button>
                </div>
              </el-col>
              <el-col :span="12">
                <el-checkbox v-model="basicModuleTemp.isMTP" :disabled="!formable || configDisabled(1021)" :true-label="1021" :false-label="0">
                  {{ $t('table.mtpSendFileSumAll') }}
                </el-checkbox>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" style="margin-top: -10px;">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isNetDisk" :disabled="!formable || configDisabled(1016)" :true-label="1016" :false-label="0">
                    {{ $t('table.webDiskUploadFileSumAll') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="netDiskDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1016)"></el-button>
                </div>
              </el-col>
              <el-col :span="12" style="margin-top: -10px;">
                <div style="display: flex;align-items: center;">
                  <el-checkbox v-model="basicModuleTemp.isRemoteDesktop" :disabled="!formable || configDisabled(1024)" :true-label="1024" :false-label="0">
                    {{ $t('table.remoteOutgoingFileSumAll') }}
                  </el-checkbox>
                  <el-button icon="el-icon-setting" style="border: none;" :disabled="desktopDisabled" :title="$t('pages.fileOutgoing_Msg74')" @click="handleCreateExcept(1024)"></el-button>
                </div>
              </el-col>
            </el-row>
          </div>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="221"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="$t('pages.fileOutgoing_Msg58')"
      :modal="false"
      :visible.sync="againResponseVisible"
      :width="width1"
      @dragDialog="handleDrag"
    >
      <Form
        ref="responseForm"
        :model="temp"
        label-position="right"
        :label-width="width3"
        :style="{ width: width2 }"
      >
        <FormItem :label="$t('pages.fileOutgoing_Msg36')">
          <el-tooltip slot="tooltip" effect="dark" placement="left" >
            <div slot="content">
              {{ $t('pages.fileOutgoing_Msg42') }}
              <br>
              {{ $t('pages.fileOutgoing_Msg43') }}
              {{ $t('pages.fileOutgoing_Msg44') }}
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <!-- 响应二次触发条件 -->
          <el-radio-group v-model="temp1.alarmCondition" style="margin-left: -10px;" @change="handleAgainTiggerChange()">
            <el-radio :label="4" :disabled="!formable">{{ $t('pages.fileOutgoing_Msg37') }}</el-radio><br>
            <el-radio :label="2" :disabled="!formable" style="margin-top: 15px;margin-left: 9px;">
              <i18n path="pages.fileOutgoing_Msg38" >
                <template slot="num">
                  <el-input-number v-model="temp1.overFileCounts" :min="1" :max="9999" :controls="false" :precision="0" :disabled="temp1.overFileCountsAble || !formable" style="width: 75px; margin: 0 4px" @blur="overFileCountsBlur()"></el-input-number>
                </template>
              </i18n>
            </el-radio>
          </el-radio-group>
        </FormItem>
        <!-- 响应规则 -->
        <el-divider content-position="left">{{ $t('pages.fileOutgoing_Msg71') }}</el-divider>
        <!-- 外发设置 -->
        <el-row style="margin-left: 30px;margin-top: 10px;">
          <el-col :span="21">
            <el-checkbox-group v-model="temp1.approvalTypeEx" style="display: inline-block;" @change="handleApprovalTypeExChange">
              <el-checkbox :label="1" :disabled="!formable || temp.approvalType.length != 0">{{ $t('pages.fileOutgoing_Msg59') }}</el-checkbox>
              <el-checkbox :label="2" :disabled="!formable || temp.approvalType.length != 0 || temp1.approvalTypeEx.indexOf(1) == -1">{{ $t('pages.burnMsg7') }}</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="confirmAlarmCondition()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="againResponseVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
    <except-module ref="exceptModule" :formable="formable" @handExceptData="handExceptData"></except-module>
  </div>
</template>
<script>
import { getStrategyPage, deleteStrategy, getDataByName, createData, updateData } from '@/api/system/configManage/fileOutgoingCheckStatistics'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { stgActiveIconFormatter/*, timeDateInfoFormatter*/ } from '@/utils/formatter'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import exceptModule from './exceptModule'
import { getFileOutgoingCheckCount } from '@/utils/dictionary'

export default {
  name: 'FileOutgoingCheckStatisticsSet',
  components: { ResponseContent, FileSuffixLibImport, exceptModule },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      showTree: true,
      treeable: true,
      deleteable: false,
      submitting: false,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', iconFormatter: stgActiveIconFormatter },
        // { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: {
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      units: [
        {
          value: 1,
          label: 'MB'
        }, 
        {
          value: 2,
          label: 'GB'
        }
      ],
      dialogFormVisible: false,
      againResponseVisible: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.FileOutgoingCheckStatisticsStrategy'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.FileOutgoingCheckStatisticsStrategy'), 'create')
      },
      dialogStatus: '',
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        basicModuleTemp: [
          { validator: this.moduleValidator, trigger: 'change' }
        ],
        ctrlFileType: [
          { required: true, message: this.$t('pages.required1'), trigger: 'change' }
        ]
      },
      ruleTypeName: this.$t('pages.alarmSetup'),
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        // fileBackUpLimit: 20,
        entityType: '',
        entityId: undefined,
        countsChecked: false,
        maxCounts: undefined,
        countsAble: true,
        maxFileSize: undefined,
        maxFileSizeEx: undefined,
        maxFileSizeAble: true,
        maxFileSizeExAble: true,
        maxFileSizeChecked: false,
        maxFileSizeExChecked: false,
        timeSpan: false,
        days: undefined,
        hours: undefined,
        minutes: undefined,
        daysAble: true,
        hoursAble: true,
        minutesAble: true,
        sizeUnit: 1,
        sizeUnitEx: 1,
        ctrlFileType: [],
        fileSuffixList: [],
        approvalType: [],
        approvalTypeEx: [],
        propRuleId: undefined,
        isAlarm: 0,
        alarmCondition: 1,
        secondAlarmCondition: 0,
        overFileCounts: undefined,
        overFileCountsAble: true
      },
      temp1: {
        alarmCondition: 2,
        overFileCounts: undefined,
        overFileCountsAble: true,
        approvalTypeEx: []
      },
      basicModuleTemp: {
        'isEmailAttach': 0, // 1012
        'isChatFile': 0,  // 1014
        'isCDBurn': 0, // 1001
        'isBlueFile': 0, // 1014
        'isBrowerSendFileOption': 0, // 1006
        'isFtpFileAware': 0, // 1017
        'isRemoteDir': 0, // 1011
        'isUsbDev': 0, // 1002
        'isMTP': 0, // 1021
        'isNetDisk': 0, // 1016
        'isADBFile': 0, // 1020
        'isRemoteDesktop': 0 // 1024
      },
      exceptKeys: {
        1002: 'usb',
        1004: 'chatToolUpLoadRec',
        1006: 'browserUpLoadRec',
        1011: 'netShare',
        1012: 'mailRec',
        1014: 'blueToothDevices',
        1016: 'netDiskUpLoadRec',
        1017: 'ftpServerIP',
        1020: 'adbSerialNum',
        1024: 'mstscServerIP'
      },
      fieldNameMap: {
        1002: 'usbSerialNum',
        1004: 'processName',
        1006: 'url',
        1011: 'IP',
        1012: 'address',
        1016: 'processName',
        1020: 'serialNum'
      },
      moduleIds: [],
      exceptData: [],
      width1: '',
      width2: '',
      width3: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['checkStatisticsConfigList']
    },
    // 计算得到 销售模块是否禁用 的 map， 31: 网页浏览管控, 34: 邮件管控, 36: 即时通讯管控, 19: 共享管控, 17: 便携设备连接管控, 32: 网络管控, 16: 存储设备管控（U盘管控）, 18: 刻录管控
    moduleDisabled() {
      // 默认值 true 禁用
      const moduleMap = { 31: true, 34: true, 36: true, 19: true, 16: true, 18: true, 17: true, 32: true }
      this.moduleIds.forEach(id => {
        if (moduleMap[id]) {
          // 开通的销售模块设置为 不禁用
          moduleMap[id] = false
        }
      })
      return moduleMap
    },
    emailAttachDisabled() {
      return this.basicModuleTemp.isEmailAttach === 0;
    },
    blueToothDisabled() {
      return this.basicModuleTemp.isBlueFile === 0;
    },
    adbDisabled() {
      return this.basicModuleTemp.isADBFile === 0;
    },

    desktopDisabled() {
      return this.basicModuleTemp.isRemoteDesktop === 0;
    },
    netDiskDisabled() {
      return this.basicModuleTemp.isNetDisk === 0;
    },
    imFileDisabled() {
      return this.basicModuleTemp.isChatFile === 0;
    },
    shareDisabled() {
      return this.basicModuleTemp.isRemoteDir === 0;
    },
    browerFileDisabled() {
      return this.basicModuleTemp.isBrowerSendFileOption === 0;
    },
    usbFileDisabled() {
      return this.basicModuleTemp.isUsbDev === 0;
    },
    ftpDisabled() {
      return this.basicModuleTemp.isFtpFileAware === 0;
    },
    // 各个配置 对应的 销售模块 map
    configType() {
      return {
        // 1006: 网页上传文件; 31: 网页浏览管控
        1006: 31,
        // 1012: 邮件附件内容; 34: 邮件管控
        1012: 34,
        // 1004: 即时通讯发送文件; 36: 即时通讯管控
        1004: 36,
        // 1011: 远程共享文件外发; 19: 共享管控
        1011: 19,
        // 1014: 蓝牙外发文件 1020: ADB外发文件 1021: MTP外发文件;  17: 便携设备连接管控
        1014: 17, 1020: 17, 1021: 17,
        // 1016: 网盘上传文件 1017: FTP上传文件; 1024: 远程桌面; 32: 网络管控
        1016: 32, 1017: 32, 1024: 32,
        // 1002: USB文件外发;  16: 存储设备管控（U盘管控）;
        1002: 16,
        // 1001: 光盘刻录文件;  18: 刻录管控
        1001: 18
      }
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    // 是否隐藏启用字段和策略来源对象
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.getLanguage()
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    tabClick(pane, event) {
    },
    getLanguage() {
      const currentLang = this.$store.getters.language
      if (currentLang != undefined || currentLang != null || currentLang != '') {
        if (currentLang == 'zh' || currentLang == 'tw') {
          this.width1 = '540px'
          this.width2 = '440px'
          this.width3 = '130px'
        } else {
          this.width1 = '730px'
          this.width2 = '630px'
          this.width3 = '200px'
        }
      }
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    handleAgainResponse() {
      this.temp.alarmCondition == 5 ? this.temp1.alarmCondition = 4 : this.temp1.alarmCondition = 2
      if (this.temp.alarmCondition == 1) {
        // 第一次新增时，默认二次响应规则设为持续响应
        this.temp1.alarmCondition = 4
      }
      this.temp1.overFileCounts = this.temp.overFileCounts
      if (this.temp1.overFileCounts === 0) {
        this.temp1.overFileCounts = undefined
      }
      // 二次响应的文件外发设置需要根据外层的外发设置进行处理
      if (this.temp.approvalType.length > 0) {
        this.temp1.approvalTypeEx = [...this.temp.approvalType]
        this.temp.approvalTypeEx = []
      } else {
        this.temp1.approvalTypeEx = [...this.temp.approvalTypeEx]
      }
      this.temp1.overFileCountsAble = this.temp.overFileCountsAble
      this.againResponseVisible = true
    },
    fileNumChange() {
      if (this.temp.countsChecked) {
        this.temp.countsAble = false
        this.temp.maxCounts = 20
      } else {
        this.temp.maxCounts = undefined
        this.temp.countsAble = true
      }
    },
    fileNumBlur() {
      if (!this.temp.maxCounts) {
        this.temp.maxCounts = 20
      }
    },
    maxFileSizeChange() {
      if (this.temp.maxFileSizeChecked) {
        this.temp.maxFileSize = 100
        this.temp.maxFileSizeAble = false
      } else {
        this.temp.maxFileSize = undefined
        this.temp.maxFileSizeAble = true
      }
    },
    maxFileSizeBlur() {
      if (!this.temp.maxFileSize) {
        this.temp.maxFileSize = 100
      }
    },
    maxFileSizeExChange() {
      if (this.temp.maxFileSizeExChecked) {
        this.temp.maxFileSizeEx = 1000
        this.temp.maxFileSizeExAble = false
      } else {
        this.temp.maxFileSizeEx = undefined
        this.temp.maxFileSizeExAble = true
      }
    },
    maxFileSizeExBlur() {
      if (!this.temp.maxFileSizeEx) {
        this.temp.maxFileSizeEx = 1000
      }
    },
    timeSpanChange() {
      if (this.temp.timeSpan) {
        this.temp.days = 1
        this.temp.hours = 0
        this.temp.minutes = 0
        this.temp.daysAble = false
        this.temp.hoursAble = false
        this.temp.minutesAble = false
      } else {
        this.temp.days = undefined
        this.temp.hours = undefined
        this.temp.minutes = undefined
        this.temp.daysAble = true
        this.temp.hoursAble = true
        this.temp.minutesAble = true
      }
    },
    suffixChange(value) {
      const newMap = new Map()
      //  自动添加前缀
      var list = []
      list = this.temp.fileSuffixList
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.temp.fileSuffixList = Object.keys(newMap) || []
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      this.temp.fileSuffixList = [...new Set(this.temp.fileSuffixList.concat(new_suffix))]
    },
    handleEmailExcept() {
      this.$refs.exceptModule.show(1012, 'create')
    },
    createData() {
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      const obj = this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createData(obj).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      const obj = this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateData(obj).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatFormData() {
      var obj = Object.assign({}, this.temp)
      // 未勾选表示不限制，设为0
      this.temp.countsChecked ? obj.maxCounts = this.temp.maxCounts : obj.maxCounts = 0
      this.temp.maxFileSizeChecked ? obj.maxFileSize = this.temp.maxFileSize : obj.maxFileSize = 0
      if (this.temp.sizeUnit === 2) {
        obj.maxFileSize = obj.maxFileSize * 1024
      }
      this.temp.maxFileSizeExChecked ? obj.maxFileSizeEx = this.temp.maxFileSizeEx : obj.maxFileSizeEx = 0
      if (this.temp.sizeUnitEx === 2) {
        obj.maxFileSizeEx = obj.maxFileSizeEx * 1024
      }
      // maxDay 与 maxDayEx共用一个字段, 转换为秒
      this.temp.timeSpan ? obj.maxDay = this.temp.days * 86400 + this.temp.hours * 3600 + this.temp.minutes * 60 : obj.maxDay = 0
      obj.maxDayEx = obj.maxDay
      obj.ctrlFileType = 0
      this.temp.ctrlFileType.forEach(item => {
        obj.ctrlFileType += item
      })
      this.temp.fileSuffixList.length == 0 ? obj.fileExt = '*.*' : obj.fileExt = this.temp.fileSuffixList.join('|')
      if (obj.fileExt != '*.*' && !obj.fileExt.endsWith('|')) {
        // 终端要求如果是具体的后缀，则每个后缀需要跟着一个|
        obj.fileExt = obj.fileExt + '|'
      }
      // 外发设置
      obj.approvalType = 0
      this.temp.approvalType.forEach(item => {
        obj.approvalType += item
      })
      // 响应规则设置
      obj.isAlarm = this.temp.isAlarm
      this.temp.isAlarm == 1 ? obj.alarmStdId = this.temp.propRuleId : obj.alarmStdId = 0
      // 二次响应规则设置
      obj.alarmCondition = 0
      if (this.temp.isAlarm === 1) {
        obj.alarmCondition = 1
      }
      if (this.temp.secondAlarmCondition === 1) {
        obj.alarmCondition += this.temp1.alarmCondition
      }
      this.temp1.alarmCondition == 2 && this.temp.secondAlarmCondition === 1 ? obj.overFileCounts = this.temp1.overFileCounts : obj.overFileCounts = 0
      obj.approvalTypeEx = 0
      if (this.temp.secondAlarmCondition === 1) {
        if (this.temp.approvalType.length > 0) {
          // 外层有勾选，必须按照外层勾选来
          obj.approvalTypeEx = obj.approvalType
        } else {
          // 外层没有勾选，则按照内层我外发配置来
          this.temp1.approvalTypeEx.forEach(item => {
            obj.approvalTypeEx += item
          })
        }
      }
      const checkModule = Object.values(this.basicModuleTemp).filter(value => value !== 0)
      const basicModule = checkModule.join(',');
      obj.businessTye = this.getBusstype(basicModule)
      const exceptKeyData = []
      checkModule.forEach(module => {
        const exceptKey = this.exceptKeys[module]
        if (exceptKey) {
          exceptKeyData.push(exceptKey)
        }
      })
      if (exceptKeyData.length === 0) {
        obj.exceptDetails = null
        return obj
      }
      this.exceptData = this.exceptData.filter(row => exceptKeyData.includes(row.id) > -1)
      const formattedExceptDetails = {}
      this.exceptData.forEach(item => {
        if (item.id && item.data && item.data.length > 0) {
          formattedExceptDetails[item.id] = item.data
        }
      })
      // 判断是否为空对象
      if (Object.keys(formattedExceptDetails).length !== 0) {
        obj.exceptDetails = formattedExceptDetails
      } else {
        obj.exceptDetails = null
      }
      return obj
    },
    getBusstype(data) {
      const modules = data.split(',');
      const moduleMap = {
        '1017': 1,
        '1002': 2,
        '1011': 4,
        '1004': 8,
        '1001': 16,
        '1006': 32,
        '1012': 64,
        '1014': 128,
        '1020': 256,
        '1021': 512,
        '1016': 1024,
        '1024': 2048
      };
      let bussType = 0;
      modules.forEach(module => {
        bussType += moduleMap[module] || 0;
      });
      return bussType;
    },
    getRuleId(value) {
      this.temp.propRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
      if (value === 0) {
        this.temp.secondAlarmCondition = 0
      }
    },
    handleAgainTiggerChange() {
      if (this.temp1.alarmCondition == 4) {
        this.temp1.overFileCounts = undefined
        this.temp1.overFileCountsAble = true
      } else {
        this.temp1.overFileCounts = 10
        this.temp1.overFileCountsAble = false
      }
    },
    overFileCountsBlur() {
      if (!this.temp1.overFileCounts) {
        this.temp1.overFileCounts = 10
      }
    },
    confirmAlarmCondition() {
      this.temp.alarmCondition = this.temp1.alarmCondition + 1
      this.temp.overFileCounts = this.temp1.overFileCounts
      this.temp.overFileCountsAble = this.temp1.overFileCountsAble
      this.temp.approvalTypeEx = this.temp1.approvalTypeEx
      this.againResponseVisible = false
    },
    handleApprovalTypeChange(value) {
      if (this.temp.approvalType.indexOf(1) == -1) {
        this.temp.approvalType = []
        this.temp1.approvalTypeEx = []
      }
    },
    handleApprovalTypeExChange(value) {
      if (this.temp1.approvalTypeEx.indexOf(1) == -1) {
        this.temp1.approvalTypeEx = []
      }
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      // 更新 store 中的销售模块id
      this.$store.dispatch('commonData/setSaleModuleIds')
      const moduleIds = [...this.$store.getters.saleModuleIds]
      if (this.moduleIds.toString() != moduleIds.toString()) {
        this.moduleIds.splice(0, this.moduleIds.length, ...moduleIds)
      }
      this.temp = Object.assign({}, this.defaultTemp)
      this.basicModuleTemp = {
        'isEmailAttach': 0, // 1012
        'isChatFile': 0,  // 1004
        'isCDBurn': 0, // 1001
        'isBlueFile': 0, // 1014
        'isBrowerSendFileOption': 0, // 1006
        'isFtpFileAware': 0, // 1017
        'isRemoteDir': 0, // 1011
        'isUsbDev': 0, // 1002
        'isMTP': 0, // 1021
        'isNetDisk': 0, // 1016
        'isADBFile': 0, // 1020
        'isRemoteDesktop': 0 // 1024
      }
      this.exceptData = []
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    strategyFormatter: function(row, data) {
      var msg = ''
      data.maxCounts == 0 ? msg += this.$t('pages.fileOutgoing_Msg64') : msg += this.$t('pages.fileOutgoing_Msg1') + '：' + data.maxCounts
      msg += '，'
      if (data.maxFileSize === 0) {
        msg += this.$t('pages.fileOutgoing_Msg65')
      } else {
        if (data.sizeUnit === 2) {
          const fileSize = data.maxFileSize / 1024
          msg += this.$t('pages.fileOutgoing_Msg23') + '：' + fileSize + 'GB'
        } else {
          msg += this.$t('pages.fileOutgoing_Msg23') + '：' + data.maxFileSize + 'MB'
        }
      }
      msg += '，'
      if (data.maxFileSizeEx === 0) {
        msg += this.$t('pages.fileOutgoing_Msg66')
      } else {
        if (data.sizeUnitEx === 2) {
          const fileSizeEx = data.maxFileSizeEx / 1024
          msg += this.$t('pages.fileOutgoing_Msg24') + '：' + fileSizeEx + 'GB'
        } else {
          msg += this.$t('pages.fileOutgoing_Msg24') + '：' + data.maxFileSizeEx + 'MB'
        }
      }
      msg += '，'
      if (data.maxDay == 0) {
        msg += this.$t('pages.fileOutgoing_Msg67')
      } else {
        var days = Math.floor(data.maxDay / (60 * 60 * 24));
        var hours = Math.floor((data.maxDay % (60 * 60 * 24)) / (60 * 60));
        var minutes = Math.floor((data.maxDay % (60 * 60)) / 60);
        msg += this.$t('pages.fileOutgoing_Msg3') + '：' + this.$t('pages.fileOutgoing_Msg4', { num: days, num1: hours, num2: minutes })
      }
      if (data.ctrlFileType != 0) {
        msg += '，' + this.$t('pages.fileOutgoing_Msg47') + '：['
      }
      if (data.ctrlFileType == 3) {
        msg += this.$t('pages.fileOutgoing_Msg48') + '、' + this.$t('pages.fileOutgoing_Msg49')
      } else if (data.ctrlFileType == 1) {
        msg += this.$t('pages.fileOutgoing_Msg48')
      } else if (data.ctrlFileType == 2) {
        msg += this.$t('pages.fileOutgoing_Msg49')
      }
      if (data.ctrlFileType != 0) {
        msg += ']'
      }
      msg += '，' + this.$t('pages.fileOutgoing_Msg50') + '：'
      data.fileExt == '*.*' ? msg += this.$t('pages.collectAll') : msg += '[' + data.fileExt.split('|').join(' ') + ']'
      if (data.approvalType != 0) {
        msg += '，' + this.$t('pages.fileOutgoing_Msg56') + '['
      }
      if (data.approvalType == 3) {
        msg += this.$t('pages.fileOutgoing_Msg59') + '、' + this.$t('pages.burnMsg7')
      } else if (data.approvalType == 1) {
        msg += this.$t('pages.fileOutgoing_Msg59')
      }
      if (data.approvalType != 0) {
        msg += ']'
      }
      if (data.isAlarm == 1) {
        msg += '，' + this.$t('pages.triggerViolationResponseRules')
      }
      if (data.alarmCondition != 0 & data.alarmCondition != 1) {
        msg += '，' + this.$t('pages.fileOutgoing_Msg70')
        msg += '，' + this.$t('pages.fileOutgoing_Msg68') + '：['
        data.alarmCondition == 3 ? msg += this.$t('pages.fileOutgoing_Msg38', { num: data.overFileCounts }) : msg += this.$t('pages.fileOutgoing_Msg37')
        msg += ']'
        if (data.approvalTypeEx != 0) {
          msg += '，' + this.$t('pages.fileOutgoing_Msg71') + '-' + this.$t('pages.fileOutgoing_Msg56') + '['
        }
        if (data.approvalTypeEx == 3) {
          msg += this.$t('pages.fileOutgoing_Msg59') + '、' + this.$t('pages.burnMsg7')
        } else if (data.approvalTypeEx == 1) {
          msg += this.$t('pages.fileOutgoing_Msg59')
        }
        if (data.approvalTypeEx != 0) {
          msg += ']'
        }
      }
      msg += '，' + this.$t('pages.busynessType') + '：[ '
      const bussType = this.numToList(data.businessTye, 12)
      bussType.indexOf(16) > -1 ? msg += this.$t('route.burnConfig') + ' ' : msg += ''
      bussType.indexOf(2) > -1 ? msg += this.$t('route.usbFileConfig') + ' ' : msg += ''
      bussType.indexOf(8) > -1 ? msg += this.$t('route.ImFile') + ' ' : msg += ''
      bussType.indexOf(32) > -1 ? msg += this.$t('route.BrowserFileStrategy') + ' ' : msg += ''
      bussType.indexOf(4) > -1 ? msg += this.$t('route.ShareConfig') + ' ' : msg += ''
      bussType.indexOf(64) > -1 ? msg += this.$t('route.EmailAttachFile') + ' ' : msg += ''
      bussType.indexOf(128) > -1 ? msg += this.$t('route.BlueTooth') + ' ' : msg += ''
      bussType.indexOf(1024) > -1 ? msg += this.$t('route.NetDisk') + ' ' : msg += ''
      bussType.indexOf(1) > -1 ? msg += this.$t('route.FtpControlConfig') + ' ' : msg += ''
      bussType.indexOf(256) > -1 ? msg += this.$t('route.AdbLimit') + ' ' : msg += ''
      bussType.indexOf(512) > -1 ? msg += this.$t('route.MtpConfig') + ' ' : msg += ''
      bussType.indexOf(2048) > -1 ? msg += this.$t('pages.remoteDesktopLogin') + ' ' : msg += ''
      msg += ']'
      return msg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    moduleValidator(rule, value, callback) {
      const module = Object.values(this.basicModuleTemp).filter(value => value !== 0).join(',');
      if (module == '') {
        callback(new Error(this.$t('pages.fileOutgoing_Msg63')))
      } else {
        callback()
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    refresh() {
      return refreshPage(this)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.temp.ctrlFileType = [1, 2]
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    handleDrag() {

    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleUpdate: function(row) {
      this.resetTemp()
      var data = Object.assign({}, row)
      this.formatRow(data)
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(data))) // copy obj
      this.temp.alarmCondition == 5 ? this.temp1.alarmCondition = 4 : this.temp1.alarmCondition = 2
      if (this.temp.overFileCounts) {
        this.temp1.overFileCounts = this.temp.overFileCounts
      }
      this.temp1.approvalTypeEx = this.temp.approvalTypeEx
      if (row.exceptDetails) {
        if (!this.formable) {
          // 如果是策略总览，由于策略json的例外数据格式与弹窗所需要的例外数据格式不一样，需要先做转换
          const result = row.exceptDetails.reduce((acc, item) => {
            // 获取value中的第一个属性名（如mailRec/usb等）
            const key = Object.keys(item.value)[0];
            // 将对应的数组赋值给结果对象
            acc[key] = item.value[key];
            return acc;
          }, {});
          this.exceptData = Object.keys(result).map(key => ({
            id: key,
            data: [...result[key]]
          }));
        } else {
          // 不是策略总览，例外数据格式在后端接口就已经格式化完成了
          this.exceptData = Object.keys(row.exceptDetails).map(key => ({
            id: key,
            data: [...row.exceptDetails[key]]
          }));
        }
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    formatRow(row) {
      row.ctrlFileType = this.numToList(row.ctrlFileType, 2)
      row.approvalType = this.numToList(row.approvalType, 2)
      row.approvalTypeEx = this.numToList(row.approvalTypeEx, 2)
      row.propRuleId = row.alarmStdId
      if (row.fileExt.includes('*')) {
        row.fileSuffixList = []
      } else {
        const list = row.fileExt.split('|')
        const suffixList = []
        list.forEach(item => {
          if (item != null && item != '' && item != undefined) {
            suffixList.push(item)
          }
        })
        row.fileSuffixList = suffixList
      }
      if (row.alarmCondition != 0 && row.alarmCondition != 1) {
        row.secondAlarmCondition = 1
      } else {
        row.secondAlarmCondition = 0
      }

      if (row.alarmCondition === 5) {
        row.overFileCountsAble = true
        row.overFileCounts = undefined
      } else if (row.alarmCondition === 3) {
        row.overFileCountsAble = false
      }

      if (row.maxCounts == 0) {
        row.countsChecked = false
        row.countsAble = true
        row.maxCounts = undefined
      } else {
        row.countsChecked = true
        row.countsAble = false
      }
      if (row.maxFileSize == 0) {
        row.maxFileSizeChecked = false
        row.maxFileSizeAble = true
        row.maxFileSize = undefined
      } else {
        row.maxFileSizeChecked = true
        row.maxFileSizeAble = false
      }
      if (row.maxFileSizeEx == 0) {
        row.maxFileSizeExChecked = false
        row.maxFileSizeExAble = true
        row.maxFileSizeEx = undefined
      } else {
        row.maxFileSizeExChecked = true
        row.maxFileSizeExAble = false
      }
      if (row.sizeUnit === 2) {
        if (row.maxFileSize) {
          row.maxFileSize = row.maxFileSize / 1024
        } else {
          row.sizeUnit = 1
        }
      }
      if (row.sizeUnitEx === 2) {
        if (row.maxFileSizeEx) {
          row.maxFileSizeEx = row.maxFileSizeEx / 1024
        } else {
          row.sizeUnitEx = 1
        }
      }
      if (row.maxDay == 0) {
        row.timeSpan = false
        row.days = undefined
        row.hours = undefined
        row.minutes = undefined
        row.daysAble = true
        row.hoursAble = true
        row.minutesAble = true
      } else {
        row.timeSpan = true
        row.days = Math.floor(row.maxDay / (60 * 60 * 24));
        row.hours = Math.floor((row.maxDay % (60 * 60 * 24)) / (60 * 60));
        row.minutes = Math.floor((row.maxDay % (60 * 60)) / 60);
        row.daysAble = false
        row.hoursAble = false
        row.minutesAble = false
      }
      const businsessType = this.numToList(row.businessTye, 12)
      businsessType.forEach(item => {
        item == 16 ? this.basicModuleTemp.isCDBurn = 1001 : 0
        item == 2 ? this.basicModuleTemp.isUsbDev = 1002 : 0
        item == 8 ? this.basicModuleTemp.isChatFile = 1004 : 0
        item == 32 ? this.basicModuleTemp.isBrowerSendFileOption = 1006 : 0
        item == 4 ? this.basicModuleTemp.isRemoteDir = 1011 : 0
        item == 64 ? this.basicModuleTemp.isEmailAttach = 1012 : 0
        item == 128 ? this.basicModuleTemp.isBlueFile = 1014 : 0
        item == 1024 ? this.basicModuleTemp.isNetDisk = 1016 : 0
        item == 1 ? this.basicModuleTemp.isFtpFileAware = 1017 : 0
        item == 256 ? this.basicModuleTemp.isADBFile = 1020 : 0
        item == 512 ? this.basicModuleTemp.isMTP = 1021 : 0
        item == 2048 ? this.basicModuleTemp.isRemoteDesktop = 1024 : 0
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    selectionChangeEnd(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    isNoValue(value) {
      return value == 0 || value == '' || value == null || value == undefined
    },
    daysBlur() {
      const { days, hours, minutes } = this.temp
      if (days == '' || days == null || days == undefined) {
        if (this.isNoValue(hours) && this.isNoValue(minutes)) {
          this.temp.days = 1
        } else {
          this.temp.days = 0
        }
      } else {
        if (this.temp.days == 365) {
          this.temp.hours = 0
          this.temp.minutes = 0
        }
      }
    },
    hoursBlur() {
      const { days, hours, minutes } = this.temp
      if (hours == '' || hours == null || hours == undefined) {
        if (this.isNoValue(days) && this.isNoValue(minutes)) {
          this.temp.hours = 1
        } else {
          this.temp.hours = 0
        }
      } else {
        if (days == 365) {
          this.temp.hours = 0
        } else if (hours == 24) {
          this.temp.days = days + 1
          this.temp.hours = 0
        }
      }
    },
    minutesBlur() {
      const { days, hours, minutes } = this.temp
      if (minutes == '' || minutes == null || minutes == undefined) {
        if (this.isNoValue(days) && this.isNoValue(hours)) {
          this.temp.minutes = 1
        } else {
          this.temp.minutes = 0
        }
      } else {
        if (days == 365) {
          this.temp.minutes = 0
        } else if (minutes == 60) {
          if (hours == 23) {
            this.temp.days++
            this.temp.hours = 0
            this.temp.minutes = 0
          } else {
            this.temp.hours++
            this.temp.minutes = 0
          }
        }
      }
    },
    getExceptData() {
      return this.exceptData
    },
    handObjExceptData(data) {
      const key = this.exceptKeys[data.id]
      this.exceptData = this.exceptData.filter(item => item.id != key)
      const objs = []
      data.data.forEach(item => {
        item.names.forEach(row => {
          if (data.id === 1014) {
            objs.push({
              deviceName: row.deviceName,
              matchType: row.matchType,
              macAddress: row.macAddress,
              opeType: item.opeType
            })
          } else if (data.id === 1017 || data.id === 1024) {
            objs.push({
              startIP: row.startIP,
              endIP: row.endIP,
              domain: row.domain,
              limitType: row.limitType,
              opeType: item.opeType
            })
          }
        })
      })
      if (objs.length === 0) {
        return
      }
      const obj = {
        id: key,
        data: objs
      }
      this.exceptData.push(obj)
    },
    handExceptData(data) {
      if (data.id === 1014 || data.id === 1017 || data.id === 1024) {
        this.handObjExceptData(data)
        return
      }
      const key = this.exceptKeys[data.id]
      const fieldkey = this.fieldNameMap[data.id]
      this.exceptData = this.exceptData.filter(item => item.id != key)
      const objs = []
      data.data.forEach(item => {
        const names = item.names.split(',')
        names.forEach(name => {
          const obj = {
            [fieldkey]: name,
            opeType: item.opeType
          }
          objs.push(obj)
        })
      })
      if (objs.length === 0) {
        return
      }
      const obj = {
        id: key,
        data: objs
      }
      this.exceptData.push(obj)
    },
    handleCreateExcept(bussType) {
      const key = this.exceptKeys[bussType]
      var row = []
      this.exceptData.forEach(item => {
        if (item.id === key) {
          row = [...item.data]
        }
      })
      this.$refs.exceptModule.show(bussType, row)
    },
    handleChatExcept() {
      this.$refs.exceptModule.show(1004)
    },
    // 未开通销售模块的配置则禁用
    configDisabled(id) {
      const type = this.configType[id]
      return this.moduleDisabled[type]
    },
    // 设置勾选状态
    listModule() {
      const selectOpt = { ...getFileOutgoingCheckCount() }
      for (const key in selectOpt) {
        if (Object.hasOwnProperty.call(selectOpt, key)) {
          const id = selectOpt[key]
          // 配置未勾选 或 未开通销售模块的配置，则将值设为 0
          if (!this.temp[key] || this.configDisabled(id)) {
            selectOpt[key] = 0
          }
        }
      }
      Object.assign(this.basicModuleTemp, selectOpt)
    },
    /**
    * 全选/取消全选的方法
    * @param boolean true为全选，false为取消全选
    */
    selectAll(boolean) {
      // 获取配置的字典，默认为开启
      const selectOpt = getFileOutgoingCheckCount()
      if (!boolean) {
        // 取消全选
        for (const key in selectOpt) {
          if (Object.hasOwnProperty.call(selectOpt, key)) {
            selectOpt[key] = 0
          }
        }
      } else {
        // 全选，需要剔除 未开通的销售模块 对应的配置
        for (const key in selectOpt) {
          if (Object.hasOwnProperty.call(selectOpt, key)) {
            const id = selectOpt[key]
            // 未开通销售模块的配置不允许勾选
            if (this.configDisabled(id)) {
              selectOpt[key] = 0
            }
          }
        }
      }
      Object.assign(this.basicModuleTemp, selectOpt)
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
