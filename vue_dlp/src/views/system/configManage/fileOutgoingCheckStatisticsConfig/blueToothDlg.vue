<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('text.insertInfo', { info: $t('pages.bluetooth') })"
    :visible.sync="bluetoothAddVisible"
    width="980px"
    @dragDialog="handleDrag"
  >
    <div class="tree-container" style="height: 450px;">
      <strategy-target-tree ref="bluetoothTargetTree" :showed-tree="['terminal']" @data-change="bluetoothTargetNodeChange" />
    </div>
    <div class="table-container-dialog">
      <grid-table
        ref="bluetoothSelectList"
        row-key="id"
        :height="400"
        :show-pager="false"
        :col-model="colModel"
        :multi-select="true"
        :row-datas="scanBluetoothDevs"
        :loading="loading"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="createBlueData()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="bluetoothAddVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'BluetoothDlg',
  data() {
    return {
      colModel: [
        { prop: 'blueName', label: this.$t('pages.blueTooth_name'), width: '150', sort: true },
        { prop: 'blueAddress', label: this.$t('pages.blueTooth_blueAddress'), width: '150', sort: true }
      ],
      bluetoothAddVisible: false,
      loading: false,
      scanBluetoothDevs: [] // 扫描出来的蓝牙信息
    }
  },
  computed: {
  },
  methods: {
    handleDrag() {},
    show() {
      this.scanBluetoothDevs.splice(0)
      this.$refs.bluetoothTargetTree && this.$refs.bluetoothTargetTree.clearFilter()
      this.$refs.bluetoothTargetTree && this.$refs.bluetoothTargetTree.setCurrentKey()
      this.bluetoothAddVisible = true
    },
    bluetoothTargetNodeChange: function(tabName, data) {
      this.loading = false
      if (data) {
        if (data.type == 1) {
          this.loading = true
          this.termId = data.dataId
          this.scanBluetoothDevs.splice(0)
          this.$socket.sendToUser(this.termId, '/listBluetooth', this.termId, (respond, handle) => {
            this.loading = false
            handle.close()
            // 判断终端id是否等于当前请求的终端id，防止用户连续点击不同终端导致数据加载出错
            if (respond.data) {
              respond.data.forEach((item, index) => {
                const p = {
                  id: Date.now() + index,
                  blueName: item.blueName,
                  blueAddress: item.blueAddress
                }
                this.scanBluetoothDevs.push(p)
              })
            }
          }, (handle) => {
            this.loading = false
            handle.close()
            this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
          })
        }
      }
    },
    createBlueData() {
      const datas = this.$refs.bluetoothSelectList.getSelectedDatas()
      if (datas.length === 0) {
        this.$message({
          message: this.$t('pages.blueTooth_blueTooth_Msg14'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.bluetoothAddVisible = false
      this.$emit('submitEnd', datas)
    }
  }
}
</script>
