<template>
  <div class="app-container">
    <div class="table-container">
      <el-row class="space-row">
        <el-col :span="8">
          <label>总空间：</label>
          <span>{{ systemSpace.totalSpace }}</span>
        </el-col>
        <el-col :span="16">
          <label>平台运行日志文件占用空间：</label>
          <span>{{ systemSpace.applogSpace }}</span>
        </el-col>
        <el-col :span="8">
          <label>已用空间：</label>
          <span>{{ systemSpace.usedSpace }}</span>
        </el-col>
        <el-col :span="16">
          <label>操作日志备份文件占用空间：</label>
          <span>{{ systemSpace.opelogSpace }}</span>
        </el-col>
        <el-col :span="8">
          <label>空间使用率：</label>
          <span>{{ systemSpace.usePercent }}</span>
        </el-col>
        <el-col :span="16">
          <label>事件日志备份文件占用空间：</label>
          <span>{{ systemSpace.issuelogSpace }}</span>
        </el-col>
        <el-col :span="8">
          <label>数据库占用空间：</label>
          <span>{{ systemSpace.dbSpace }}</span>
        </el-col>
      </el-row>

      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <!--<el-button size="mini">
          导入导出
        </el-button>-->
      </div>
      <grid-table ref="spaceConfigList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px" style="width: 500px; margin-left:20px;">
        <FormItem label="空间使用率达" prop="usePercent">
          <el-input v-model="temp.usePercent" maxlength="2" class="num-input" />
          <span style="font-weight: 700;">%，执行如下预警：</span>
        </FormItem>
        <el-card class="box-card">
          <FormItem prop="boxWarn" class="box-card-item">
            <el-checkbox v-model="temp.boxWarn">弹窗警告</el-checkbox>
          </FormItem>
          <FormItem prop="emailNotice" class="box-card-item">
            <el-checkbox v-model="temp.emailNotice">邮件通知：</el-checkbox>
          </FormItem>
          <FormItem label="通知邮箱：" prop="email" style="padding-left: 4px;">
            <el-input v-model="temp.email" :disabled="!temp.emailNotice" :maxlength="100"/>
          </FormItem>
          <FormItem label="邮件模板：" prop="emailTemplateId" style="padding-left: 4px;">
            <el-select v-model="temp.emailTemplateId" :disabled="!temp.emailNotice" :placeholder="$t('text.select')">
              <el-option
                v-for="item in emailTemplateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </FormItem>

          <FormItem prop="repeatEmailDay" label-width="35px">
            <span>每隔</span>
            <el-input v-model="temp.repeatEmailDay" maxlength="2" class="num-input" :disabled="!temp.emailNotice" />
            <span>天，重复发送邮件</span>
          </FormItem>
          <FormItem prop="delLogDay" label-width="0">
            <el-row>
              <el-col :span="1">
                <el-checkbox v-model="temp.delLog"></el-checkbox>
              </el-col>
              <el-col :span="15" style="margin-left: 6px;">
                <span>删除</span>
                <el-input v-model="temp.delLogDay" class="num-input" :disabled="!temp.delLog" maxlength="3"/>
                <span>天前平台的运行日志文件</span>
              </el-col>
            </el-row>
          </FormItem>

        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getConfigByUsePercent, createConfig, updateConfig, deleteConfig, getConfigPage, getSystemSpace } from '@/api/system/configManage/spaceConfig'
import { getEmailTemplateOptions } from '@/api/system/configManage/emailConfig'

export default {
  name: 'SpaceConfig',
  data() {
    return {
      colModel: [
        { prop: 'usePercent', label: '条件', width: '150', fixed: true, formatter: this.usePercentFormatter },
        { prop: '', label: '预警', width: '150', formatter: this.optionsFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        groupId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        usePercent: '',
        boxWarn: false,
        emailNotice: false,
        email: '',
        emailTemplateId: undefined,
        repeatEmailDay: '',
        delLog: false,
        delLogDay: ''
      },
      systemSpace: {},
      defaultSystemSpace: { // 表单字段
        totalSpace: 0,
        usedSpace: 0,
        usePercent: 0,
        dbSpace: 0,
        applogSpace: 0,
        opelogSpace: 0,
        issuelogSpace: 0
      },
      rules: {
        usePercent: [
          { required: true, message: '请输入正整数', trigger: 'blur' },
          { validator: this.usePercentValidator, trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: this.$t('pages.validaEmail'), trigger: 'blur' }
        ],
        repeatEmailDay: [
          { validator: this.repeatEmailDayValidator, trigger: 'blur' }
        ],
        delLogDay: [
          { validator: this.delLogDayValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: '修改空间预警',
        create: '新增空间预警'
      },
      treeSelectNode: [],
      emailTemplateOptions: [],
      downloadLoading: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['spaceConfigList']
    }
  },
  created() {
    this.resetTemp()
    this.resetSystemSpace()
    this.getEmailTemplateOptions()
  },
  methods: {
    rowDataApi: function(option) {
      return getConfigPage(option)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    resetSystemSpace() {
      getSystemSpace().then(respond => {
        console.log(respond.data)
      })
    },
    getEmailTemplateOptions() {
      getEmailTemplateOptions().then(respond => {
        this.emailTemplateOptions = respond.data
      })
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      // this.changeTreeSelectNode()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      // this.changeTreeSelectNode()
      this.temp = Object.assign({}, this.temp, row) // copy obj
      if (this.temp.email) {
        this.temp.emailNotice = true
      }
      if (this.temp.delLogDay) {
        this.temp.delLog = true
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {},
    handleExport() {},
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.temp.emailNotice) {
            this.temp.email = ''
            this.temp.emailTemplateId = ''
            this.temp.repeatEmailDay = ''
          }
          if (!this.temp.delLog) {
            this.temp.delLogDay = ''
          }
          createConfig(this.temp).then(response => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.temp.emailNotice) {
            this.temp.email = ''
            this.temp.emailTemplateId = ''
            this.temp.repeatEmailDay = ''
          }
          if (!this.temp.delLog) {
            this.temp.delLogDay = ''
          }
          const tempData = Object.assign({}, this.temp)
          updateConfig(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.updateRowData(this.temp)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteConfig({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    usePercentFormatter: function(row, data) {
      return '空间使用率达' + data + '%'
    },
    optionsFormatter: function(row, data) {
      let warn = ''
      if (row.boxWarn) {
        warn += '弹窗警告; '
      }
      if (row.email) {
        warn += '邮件通知' + row.email + '; '
      }
      if (row.delLogDay) {
        warn += '删除' + row.delLogDay + '天前平台的运行日志文件'
      }
      return warn
    },
    numberValidator(rule, value, callback) {
      const numReg = /^[0-9]+$/
      const number = new RegExp(numReg)
      if (!number.test(value)) {
        callback(new Error('请输入正确的数字'))
      } else if (value == '0' || value == '00') {
        callback(new Error('请输入正整数'))
      } else {
        return true
      }
    },
    usePercentValidator(rule, value, callback) {
      if (!this.numberValidator(rule, value, callback)) return
      getConfigByUsePercent({ usePercent: value }).then(response => {
        const config = response.data
        if (config && config.id !== this.temp.id) {
          callback(new Error('空间使用率配置重复，请修改!'))
        } else {
          callback()
        }
      })
    },
    repeatEmailDayValidator(rule, value, callback) {
      if (this.temp.emailNotice && value) {
        this.numberValidator(rule, value, callback) && callback()
      } else {
        callback()
      }
    },
    delLogDayValidator(rule, value, callback) {
      if (this.temp.delLog) {
        this.numberValidator(rule, value, callback) && callback()
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card-item{
    >>>.el-form-item__content {
      margin-left: 0px !important;
    }
  }
  .space-row{
    margin-bottom: 10px;
    .el-col{
      margin-bottom: 10px;
      label{
        font-weight: normal;
      }
    }
  }
  .table-container .toolbar+.tableBox {
    height: calc(100% - 160px);
  }

  .num-input{
    width: 50px;
    >>>.el-input__inner{
      padding: 0 5px;
      text-align: center;
    }
  }
</style>
