<template>
  <div class="app-container approval-config">
    <Form
      ref="processForm"
      class="process-form"
      :rules="processRules"
      :model="processForm"
      label-position="left"
      :label-width="$store.getters.language === 'en' ? '440px' : '250px'"
    >
      <FormItem :label="$t('pages.applyMostOnce')" prop="ldMaxApplyRecordsOnce" >
        <el-input-number v-model="processForm.ldMaxApplyRecordsOnce" :min="1" :max="100" :controls="false"/>
      </FormItem>
      <FormItem :label="$t('pages.maximumOfflineTimeForOfflineApproval')" prop="ldLongestOfflinePeriod">
        <el-input-number v-model="processForm.ldLongestOfflinePeriod" :min="1" :max="maxOfflineDay" :controls="false"/>
      </FormItem>
      <FormItem :label="$t('pages.terminalTimingQuery')" prop="ldQueryTaskInterval">
        <el-input-number v-model="processForm.ldQueryTaskInterval" :min="1" :max="600" :controls="false"/>
      </FormItem>
      <FormItem :label="$t('pages.maximumUploadSingleFile')" prop="ldMaxSingleFileSize" >
        <el-input-number v-model="processForm.ldMaxSingleFileSize" style="width:120px" :min="1" :max="20480" :controls="false" :disabled="uploadCheck"/>
        <el-checkbox v-model="uploadCheck" style="margin-left:10px" @change="checkChange">{{ $t('pages.noUpload') }}</el-checkbox>
      </FormItem>
      <FormItem :label="$t('pages.mobileDownloadMaxSize')" prop="mobileDownloadMaxSize" >
        <el-input-number v-model="processForm.mobileDownloadMaxSize" style="width:120px" :min="1" :max="20480" :controls="false" :disabled="downloadCheck"/>
        <el-checkbox v-model="downloadCheck" style="margin-left:10px">{{ $t('pages.ImFile_text13') }}</el-checkbox>
      </FormItem>
      <div>
        <el-checkbox v-model="offlineVerifyCodeFlowConfig.offlineApproval"><label>{{ $t('pages.allowOfflineApproval') }}</label></el-checkbox>
        <label class="el-checkbox__label" style=" font-size: 14px;margin-left:-7px">{{ $t('pages.allowTimes') }}:</label>
        <el-date-picker
          v-model="timeValue"
          :readonly="!offlineVerifyCodeFlowConfig.offlineApproval"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="$t('pages.startDate')"
          :end-placeholder="$t('pages.endDate')"
          format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00','23:59:59']"
          size="large"
          style="width:400px"
        >
        </el-date-picker>
      </div>
      <div class="check-group">
        <el-checkbox v-model="processForm.ldApplyAfterFileUpload">{{ $t('pages.uploadTheFileBeforeSending') }}</el-checkbox>
        <el-checkbox v-model="processForm.ldApplyReasonRequired">{{ $t('pages.applicationReasonIsRequired') }}</el-checkbox>
        <el-checkbox v-model="processForm.ldReceiverRequired" >{{ $t('pages.receiverInformationIsRequired') }}</el-checkbox>
        <el-checkbox v-model="processForm.ldApplyHideVerifycodeFlow" >{{ $t('pages.terminalApplicationProcess') }}</el-checkbox>
        <el-checkbox v-model="processForm.ldApplyFileOverMax" :disabled="uploadCheck">{{ $t('pages.applicationForDocumentsNotAllowed') }}</el-checkbox>
        <el-checkbox v-model="processForm.ldApplyIncludeDifferentSecretFolder" v-permission="'E22'" >{{ $t('pages.decryptionApplication') }}</el-checkbox>
        <el-checkbox v-model="processForm.trusteeMenuSwitch" true-label="1" false-label="0" >{{ $t('pages.trusteeApplication') }}</el-checkbox>
        <el-checkbox v-if="isEncModule" v-model="processForm.ldFiledecryptSendmail">{{ $t('pages.ldFiledecryptSendmail') }}</el-checkbox>
        <el-checkbox v-if="isEncModule" v-model="processForm.ldApplyJurisdiction" >{{ $t('pages.ldApplyJurisdiction') }}</el-checkbox>
        <el-checkbox v-if="isEncModule" v-model="processForm.ldApprovalJurisdiction" >{{ $t('pages.ldApprovalJurisdiction') }}</el-checkbox>
      </div>

    </Form>
    <el-button type="primary" size="mini" style="position: absolute; left: 500px; bottom: 30px;" :loading="submitting" @click="updateConfig">{{ $t('button.save') }}</el-button>
  </div>
</template>

<script>
import { getApprovalConfig, updateApprovalConfig } from '@/api/system/configManage/approvalConfig.js'
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig'
import { getPropertyValueByCode } from '@/api/property'
import moment from 'moment'
export default {
  name: 'ApprovalConfig',
  data() {
    return {
      processForm: {},
      offlineVerifyCodeFlowConfig: {
        'offlineApproval': false,
        'startTime': new Date(moment().startOf('day')),
        'endTime': new Date(moment().endOf('day'))
      },
      submitting: false,
      uploadCheck: false,
      downloadCheck: false,
      isEncModule: false,
      maxOfflineDay: 720,
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.offlineVerifyCodeFlowConfig.endTime
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.offlineVerifyCodeFlowConfig.startTime
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime()
            )
          }
        }
      },
      timeValue: [new Date(moment().startOf('day')), new Date(moment().endOf('day'))],
      processRules: {
        ldMaxApplyRecordsOnce: [
          { required: true, message: this.$t('pages.validateMsg_number1'), trigger: 'blur' }
        ],
        ldMaxSingleFileSize: [
          { required: !this.uploadCheck, message: this.$t('pages.validateMsg_number2'), trigger: 'blur' }
        ],
        ldLongestOfflinePeriod: [
          { required: true, message: this.$t('pages.offline_terminal_text12', { min: 1, max: this.maxOfflineDay }), trigger: 'blur' }
        ],
        ldQueryTaskInterval: [
          { required: true, message: this.$t('pages.validateMsg_number4'), trigger: 'blur' }
        ],
        mobileDownloadMaxSize: [
          { required: !this.downloadCheck, message: this.$t('pages.validateMsg_number2'), trigger: 'blur' }
        ]
      }
    }
  },
  activated() {
    this.getMaxOfflineDay()
  },
  created() {
    this.listModule()
    this.getMaxOfflineDay()
    getApprovalConfig().then(res => {
      this.processForm = res.data
      this.uploadCheck = res.data.ldMaxSingleFileSize === -1
      this.downloadCheck = res.data.mobileDownloadMaxSize === 0
      if (this.processForm.offlineVerifyCodeFlowConfig && this.processForm.offlineVerifyCodeFlowConfig != '') {
        this.offlineVerifyCodeFlowConfig = JSON.parse(this.processForm.offlineVerifyCodeFlowConfig)
        this.$nextTick(() => {
          this.timeValue = [new Date(this.offlineVerifyCodeFlowConfig.startTime), new Date(this.offlineVerifyCodeFlowConfig.endTime)]
        })
      }
    })
  },
  methods: {
    getMaxOfflineDay() {
      getPropertyValueByCode('max.offline.day').then(res => {
        this.maxOfflineDay = (!res.data ? 720 : Number.parseInt(res.data))
        this.processRules.ldLongestOfflinePeriod[0].message = this.$t('pages.offline_terminal_text12', { min: 1, max: this.maxOfflineDay })
      })
    },
    listModule() {
      // 获取开通的销售模块
      listSaleModuleId().then(resp => {
        if (resp.data && resp.data.length > 0 && resp.data.indexOf(51) > -1) {
          this.isEncModule = true
        }
      })
    },
    checkChange(value) {
      this.processForm.ldApplyFileOverMax = value && false
    },
    updateConfig() {
      this.$refs['processForm'].validate(valid => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.processForm)
          tempData.ldMaxSingleFileSize = this.uploadCheck ? -1 : tempData.ldMaxSingleFileSize
          tempData.mobileDownloadMaxSize = this.downloadCheck ? 0 : tempData.mobileDownloadMaxSize
          if (!this.offlineVerifyCodeFlowConfig.offlineApproval) {
            if (!this.timeValue) {
              this.offlineVerifyCodeFlowConfig.startTime = new Date(moment().startOf('day'))
              this.offlineVerifyCodeFlowConfig.endTime = new Date(moment().endOf('day'))
            }
          } else {
            if (!this.timeValue) {
              this.$message({
                message: this.$t('pages.timeNotNull'),
                type: 'error'
              })
              this.submitting = false
              return
            }
            this.offlineVerifyCodeFlowConfig.startTime = moment(this.timeValue[0]).format('YYYY-MM-DD HH:mm:ss')
            this.offlineVerifyCodeFlowConfig.endTime = moment(this.timeValue[1]).format('YYYY-MM-DD HH:mm:ss')
            // if (new Date(this.offlineVerifyCodeFlowConfig.endTime).getTime() < new Date().getTime()) {
            //   this.$message({
            //     message: this.$t('pages.approvalProcess_Msg71'),
            //     type: 'error'
            //   })
            //   this.submitting = false
            //   return
            // }
          }

          tempData.offlineVerifyCodeFlowConfig = JSON.stringify(this.offlineVerifyCodeFlowConfig)
          updateApprovalConfig(tempData).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false
          }).catch(err => {
            console.log(err)
            this.submitting = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.approval-config{
  .process-form{
    height: calc(100% - 60px);
    overflow: auto;
    margin: 10px 0 0 40px;
    .el-form-item{
      margin-bottom: 15px;
    }
    .el-input-number{
      width: 200px;
    }
    .check-group{
      margin-top: 20px;
      .el-checkbox{
        // display: block;
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        float: left;
        clear: both;
      }
    }
  }
}

</style>
