<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.termGuid" v-trim clearable :placeholder="$t('table.terminalGuid')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :multi-select="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="450px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px">
        <FormItem :label="$t('table.terminalGuid')" prop="termGuid">
          <el-input v-model="temp.termGuid" v-trim :maxlength="50" clearable/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createData,
  deleteData,
  getByGuid,
  getPage,
  updateData
} from '@/api/system/configManage/termGuidFilter'

export default {
  name: 'TermGuidBlacklist',
  data() {
    return {
      colModel: [
        { prop: 'termGuid', label: 'terminalGuid', width: '150', sort: true },
        { prop: 'createTime', label: 'createTime', width: '100' },
        { prop: 'modifyTime', label: 'modifyTime', width: '100' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      textMap: {
        update: this.i18nConcatText(this.$t('route.termGuidBlacklist'), 'update'),
        create: this.i18nConcatText(this.$t('route.termGuidBlacklist'), 'create')
      },
      deleteable: false,
      temp: {},
      defaultTemp: {
        id: undefined,
        termGuid: ''
      },
      query: {
        page: 1,
        termGuid: ''
      },
      rules: {
        termGuid: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.termGuidValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      submitting: false,
      dialogStatus: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTable']
    }
  },
  watch: {
  },
  created() {
    this.resetQuery()
    this.resetTemp()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery);
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleDrag() {

    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    resetQuery() {
      this.query.page = 1
      this.query.termGuid = ''
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createData(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateData(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.editSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const selectedIds = this.gridTable.getSelectedIds()
        const toDeleteIds = selectedIds.join(',')
        deleteData({ ids: toDeleteIds }).then(respond => {
          this.submitting = false
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }).catch(reason => { this.submitting = false })
      }).catch(() => { this.submitting = false })
    },
    termGuidValidator(rule, value, callback) {
      if (value) {
        getByGuid({ guid: value }).then(respond => {
          const termGuidFilter = respond.data
          if (termGuidFilter && this.temp.id != termGuidFilter.id) {
            callback(new Error(this.$t('valid.customizeSameName', { obj: this.$t('table.terminalGuid') })))
          } else {
            callback()
          }
        })
      } else {
        callback(new Error(this.$t('components.required')))
      }
    }
  }
}
</script>
