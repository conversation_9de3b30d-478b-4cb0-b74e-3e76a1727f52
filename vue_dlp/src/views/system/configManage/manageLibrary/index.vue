<template>
  <div class="app-container">
    <Form
      ref="dataForm"
      :rules="rules"
      :model="form.local"
      :hide-required-asterisk="true"
      label-width="130px"
      style="width: 1250px; margin-left: 30px"
    >
      <!-- 总开关 -->
      <el-card class="box-card" style="background-color: transparent;padding-top: 10px" :body-style="{ padding: '10px 40px' }">
        <div slot="header" class="title">
        </div>
        <div>
          <FormItem :label="$t('pages.manageLibrary_openBackup')">
            <el-switch v-model="able" style="margin-left: 10px" @change="ableChange"></el-switch>
          </FormItem>
          <!-- 时-->
          <FormItem :label="$t('pages.manageLibrary_backupTime')">
            <div style="display: flex">
              <el-time-picker
                v-model="form.time"
                :placeholder="$t('pages.manageLibrary_encryptionKey_text2')"
                range-separator=":"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 200px"
                :disabled="!able"
                :clearable="false"
              />
              <span style="color: #68a8d0; margin-left: 10px;">{{ $t('pages.manageLibrary_reminder') }}</span>
            </div>
          </FormItem>
        </div>
      </el-card>

      <!-- 本地备份 -->
      <el-card class="box-card" style="background-color: transparent;">
        <div slot="header" class="title">
        </div>
        <!-- 云备份未上线，本地备份启用默认为1，按钮隐藏 -->
        <FormItem v-if="false" :label="$t('pages.manageLibrary_backupLocal')">
          <el-switch v-model="form.local.enable" :active-value="1" :inactive-value="0" style="margin-left: 10px" @change="changeLocalOnlineEnable"></el-switch>
        </FormItem>
        <FormItem :label="$t('pages.manageLibrary_backupPathType')" style="margin-right: 10px;" >
          <div style="display: flex">
            <el-select v-model="form.local.type" class="input-css" @change="typeChange">
              <el-option :key="1" :value="1" :label="$t('pages.manageLibrary_localDiskToEngine')"/>
              <el-option v-if="isSupportShare" :key="2" :value="2" :label="$t('pages.manageLibrary_shareDisk')"/>
            </el-select>
            <div v-if="form.local.type !== null && form.local.type === 2" style="margin-left: 10px">
              <i class="el-icon-warning" style="color: #68a8d0"/>
              <span style="color: #68a8d0">{{ $t('pages.manageLibrary_useShareTypeProcessMessage') }}</span>
            </div>
          </div>
        </FormItem>

        <!-- 本地磁盘 -->
        <div>
          <FormItem v-show="form.local.type === 2" prop="lpRemoteName" :label="$t('pages.manageLibrary_ipAddress')">
            <el-input v-model="form.local.lpRemoteName" style="width: 200px" clearable maxlength @change="testChange"></el-input>
          </FormItem>
          <FormItem v-show="form.local.type === 2" prop="lpUserName" :label="$t('pages.manageLibrary_username')">
            <el-input v-model="form.local.lpUserName" style="width: 200px" clearable maxlength @change="testChange"></el-input>
          </FormItem>
          <FormItem v-if="form.local.type === 2" prop="lpPassword" :label="$t('pages.manageLibrary_loginPassword')">
            <el-input v-model="form.local.lpPassword" type="password" style="width: 200px" maxlength @input.native="inputPwd($event)" @change="testChange"></el-input>
          </FormItem>

          <div style="display: flex; width: 100%">
            <FormItem v-show="isWindows && form.local.type && form.local.type === 1" :label="$t('pages.manageLibrary_localDisk')" prop="devId" style="margin-right: 10px">
              <el-select v-model="form.local.devId" class="input-css" @change="getDiskContent">
                <el-option v-for="item in serverDiskInfoList" :key="item.devId" :value="item.devId" :label="item.diskName"/>
              </el-select>
            </FormItem>
            <FormItem key="path" :label="form.local.type === 1 ? $t('pages.manageLibrary_localBackupAddress') : $t('pages.manageLibrary_shareDiskAddress')" prop="path" style="width: 100%">
              <el-tooltip
                slot="tooltip"
                effect="dark"
                :content="(form.local.type === 1 ? $t('pages.manageLibrary_localBackupAddress') : $t('pages.manageLibrary_shareDiskAddress')) + '：' + localPathTooltip"
                placement="top-start"
              >
                <i class="el-icon-question" style="color: #409EFF"/>
              </el-tooltip>
              <div style="display: flex">
                <el-input v-model="form.local.path" style="width: 75%" :disabled="(form.local.type === 1 && isWindows && form.local.devId == null && form.local.devId === '')" clearable maxlength @change="testChange"></el-input>
                <el-button type="primary" size="mini" style="width: auto; margin-left: 1px;" :loading="onceClickLoading" @click="handlerOnceClick">{{ $t('pages.manageLibrary_onceClickBackup') }}</el-button>
              </div>
            </FormItem>
          </div>

          <FormItem v-if="form.local.type === 1 && form.local.devId !== null && form.local.devId !== ''">
            <span>
              {{ devContent }}
            </span>
          </FormItem>
          <FormItem v-if="form.local.type === 2" prop="testConnection">
            <span slot="label">
              <el-tooltip class="item" effect="dark" :content="$t('pages.manageLibrary_testConnectionHelpMessage')" placement="top-start">
                <i class="el-icon-question" style="color: #409EFF"/>
              </el-tooltip>
            </span>
            <el-button
              id="test"
              type="primary"
              size="mini"
              :disabled="isAlloweTestArr.length !== 3"
              :loading="testLoading"
              @click="test"
            >{{ $t('pages.manageLibrary_testConnection') }}</el-button>
          </FormItem>
        </div>
      </el-card>

      <!-- 云备份  云平台功能隐藏-->
      <el-card v-if="false" class="box-card" style="background-color: transparent;">
        <div slot="header">
          <span slot="label" class="title">
            <label>{{ $t('pages.manageLibrary_onlineBackup') }}</label>
            <el-tooltip class="item" effect="dark" :content="$t('pages.manageLibrary_onlineBackup_text')" placement="top-start">
              <i class="el-icon-info"/>
            </el-tooltip>
          </span>
        </div>
        <!-- 云备份开关 -->
        <FormItem :label="$t('pages.manageLibrary_backupToOnline')">
          <el-switch v-model="onLineSwitchFlag" style="margin-left: 10px" @change="changeLocalOnlineEnable"></el-switch>
        </FormItem>

        <div v-if="form.online.enable === 1" style="display: flex">
          <!-- 备份频率 -->
          <FormItem v-if="form.online.enable === 1" :label="$t('pages.manageLibrary_backupFrequency')">
            <el-select v-model="form.online.frequency" :disabled="!able" @change="frequencyChange">
              <el-option key="day" value="day" :label="$t('pages.manageLibrary_onceDay')"/>
              <el-option key="week" value="week" :label="$t('pages.manageLibrary_onceWeek')"/>
              <el-option key="month" value="month" :label="$t('pages.manageLibrary_onceMonth')"/>
              <el-option key="customize" value="customize" :label="$t('pages.manageLibrary_customize')"/>
            </el-select>
          </FormItem>
          <!-- 周-->
          <FormItem v-if="form.online.frequency && form.online.frequency === 'week'" :label="$t('pages.manageLibrary_backupTime')">
            <el-select v-model="form.online.backupTime.week" :disabled="!able" :placeholder="$t('pages.manageLibrary_encryptionKey_text3')">
              <el-option :key="1" :value="1" :label="$t('pages.monday')"/>
              <el-option :key="2" :value="2" :label="$t('pages.tuesday')"/>
              <el-option :key="3" :value="3" :label="$t('pages.wednesday')"/>
              <el-option :key="4" :value="4" :label="$t('pages.Thursday')"/>
              <el-option :key="5" :value="5" :label="$t('pages.friday')"/>
              <el-option :key="6" :value="6" :label="$t('pages.saturday')"/>
              <el-option :key="7" :value="7" :label="$t('pages.sunday')"/>
            </el-select>
          </FormItem>

          <!-- 月-->
          <FormItem v-if="form.online.frequency && form.online.frequency === 'month'" :label="$t('pages.manageLibrary_backupTime')">
            <el-select v-model="form.online.backupTime.month" :disabled="!able" :placeholder="$t('pages.manageLibrary_encryptionKey_text3')">
              <el-option :key="1" :value="1" :label="$t('pages.manageLibrary_month1')"/>
              <el-option :key="2" :value="2" :label="$t('pages.manageLibrary_month2')"/>
              <el-option :key="3" :value="3" :label="$t('pages.manageLibrary_month3')"/>
            </el-select>
          </FormItem>

          <!-- 自定义-->
          <FormItem v-if="form.online.frequency && form.online.frequency === 'customize'" :label="$t('pages.manageLibrary_betweenDay')">
            <el-input-number v-model="form.online.backupTime.interval" :disabled="!able" style="width: 200px" :min="1" :max="99999" @blur="intervalBlur(form.online.backupTime.interval)"></el-input-number>
          </FormItem>
        </div>

        <!-- 备份文件保留 -->
        <FormItem v-if="form.online.enable === 1" :label="$t('pages.manageLibrary_backupMaxNumber')" :tooltip-content="$t('pages.manageLibrary_backupMaxNumber_text1')" tooltip-placement="top-start">
          <el-input-number v-model="form.online.retainCount" style="width: 200px" :min="1" :max="999999999" :label="$t('pages.manageLibrary_pleaseEnter')" />
          <span>{{ $t('pages.manageLibrary_backupMaxNumber1') }}</span>
        </FormItem>

      </el-card>
      <FormItem style="width: 70%">
        <el-button id="save" type="primary" style="float: right" size="mini" :loading="saveSubmitting" @click="save">{{ $t('pages.manageLibrary_save') }}</el-button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import {
  getCurrentDataBaseServerInfo, manageDataBaseBack, testConnection
} from '@/api/system/terminalManage/manageLibrary';
import { aesDecode, aesEncode, formatAesKey } from '@/utils/encrypt'
import { isIPv4, isIPv6 } from '@/utils/validate'

export default {
  name: 'ManageLibrary',
  data() {
    const ipValidator = (rule, value, callback) => {
      if (this.form.local.type === 1) {
        callback();
        return;
      }
      this.deleteShareTestConnection('lpRemoteName')
      this.form.local.lpRemoteName = this.form.local.lpRemoteName ? this.form.local.lpRemoteName.trim() : ''
      if (this.form.local.lpRemoteName === null || this.form.local.lpRemoteName === '') {
        callback(new Error(this.$t('pages.manageLibrary_ipAddressNotNull')));
      } else {
        //  支持IPv4，Ipv6
        const flag = isIPv4(this.form.local.lpRemoteName) || isIPv6(this.form.local.lpRemoteName);
        //  当前仅支持ipv4
        // const flag = isIPv4(this.form.local.lpRemoteName)
        if (!flag) {
          callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')))
          return;
        }
        if (this.form.local.type === 2) {
          this.addShareTestConnection('lpRemoteName')
        }
        callback()
      }
    }
    const devIdValidator = (rule, value, callback) => {
      if (this.isWindows && this.form.local.type && this.form.local.type === 1) {
        if (this.form.local.devId === null || this.form.local.devId === '') {
          callback(new Error(this.$t('pages.manageLibrary_diskNotNull')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const pathValidator = (rule, value, callback) => {
      if (this.form.local.type === 1) {
        this.localPathValidator(value, callback);
      }
      if (this.form.local.type === 2) {
        this.sharePathValidator(value, callback);
      }
    }
    const testConnectionValidator = (rule, value, callback) => {
      if (this.form.local.enable === 1 && this.form.local.type === 2 && this.testFlag && this.isAlloweTestArr.length === 3) {
        callback(new Error(this.$t('pages.manageLibrary_testConnectionIsSuccess') + ''))
      } else {
        callback()
      }
    }
    const lpUserNameValidator = (ru7le, value, callback) => {
      this.deleteShareTestConnection('lpUserName');
      if (this.form.local.type === 2 && !value) {
        callback(this.$t('pages.manageLibrary_usernameValidMessage') + '');
      } else {
        this.addShareTestConnection('lpUserName')
        callback();
      }
    }
    return {
      number: 1,
      defaultBackupTime: {
        week: 1,   //  该周的周几,  1-7
        month: 1,  //  该月的什么时候, 【1】月初[1号]、【2】月中[15号]、【3】月末，引擎根据当月情况判断因为可能出现[28、29、30、31这几种月末情]。
        interval: 1  //  间隔多少天
      },
      form: {
        notice: null, //  通知类型，1：更新配置信息， 2：立即备份
        enable: 0,  //  总开关
        time: '03:00',   //  备份时间点,  HH：mm
        encryptionKey: '',
        encryptionKeyType: 2,
        online: {
          enable: 0,
          retainCount: 1, // 备份保留数量
          frequency: 'day',   //  备份频率   day||week||month||customize
          backupTime: {
            week: 1,   //  该周的周几,  1-7
            month: 1,  //  该月的什么时候, 【1】月初[1号]、【2】月中[15号]、【3】月末，引擎根据当月情况判断因为可能出现[28、29、30、31这几种月末情]。
            interval: 1  //  间隔多少天
          }
        },
        local: {
          enable: 1,
          path: '',
          type: 1,  // 备份类型 1：本地磁盘，2：window共享目录，3：linux
          lpRemoteName: '', //  共享目录所在机子的IP地址  \\**************[注]备份地址的开头需与lpRemoteName一样
          lpPassword: '', //  共享目录所在机子的用户名
          lpUserName: '',  //  共享目录所在机子的登录密码
          devId: ''
        }
      },
      defaultForm: {
        enable: 0,  //  总开关
        time: '03:00',   //  备份时间点,  HH：mm
        encryptionKey: '',
        encryptionKeyType: 2,
        online: {
          enable: 0,
          retainCount: 1, // 备份保留数量
          frequency: 'day',   //  备份频率   day||week||month||customize
          backupTime: {
            week: 1,   //  该周的周几,  1-7
            month: 1,  //  该月的什么时候, 【1】月初[1号]、【2】月中[15号]、【3】月末，引擎根据当月情况判断因为可能出现[28、29、30、31这几种月末情]。
            interval: 1  //  间隔多少天
          }
        },
        local: {
          enable: 1,
          path: '',
          type: 1,  // 备份类型 1：本地磁盘，2：window共享目录，3：linux
          lpRemoteName: '', //  共享目录所在机子的IP地址  \\**************[注]备份地址的开头需与lpRemoteName一样
          lpPassword: '', //  共享目录所在机子的用户名
          lpUserName: '',  //  共享目录所在机子的登录密码
          devId: ''
        }
      },
      able: false,  //  总开关
      onLineSwitchFlag: false,
      rules: {
        lpRemoteName: [
          { validator: ipValidator, trigger: 'blur' }
        ],
        lpUserName: [
          { required: true, validator: lpUserNameValidator, trigger: 'blur' }
        ],
        devId: [{ validator: devIdValidator, trigger: 'change' }],
        path: [
          { validator: pathValidator, trigger: 'blur' }
        ],
        testConnection: [
          { validator: testConnectionValidator, trigger: 'blur' }
        ]
      },
      serverDiskInfoList: [],    //  磁盘数组
      devContent: '',
      isWindows: false,    //  数据库是否部署在windows上
      isLinux: false,
      submitting: false,
      saveSubmitting: false,
      testLoading: false,
      isAlloweTestArr: [], //  表示是否允许测试连接  共享文件夹测试连接共需要测试ip，用户名，路径
      encryptionKeyTypeTemp: null, //  暂存数据库备份的加密密钥方式
      encryptionKeyTypeError: '',
      testFlag: true,   //  本地路径类型切换到共享磁盘时，锁定保存按钮, false时表示共享磁盘测试连接成功
      onceClickFlag: false,     //  表示执行立即备份
      onceClickLoading: false,   //  一键备份加载
      supportShare: ['windows'], //  当前仅windows支持共享目录，若linux需要支持则添加linux，可填值为：windows/linux/mac
      hasChangePwd: false,
      encryptPwd: ''
    }
  },
  computed: {
    //  是否支持共享文件夹
    isSupportShare() {
      const osType = this.$store.getters.engineOsType || ''
      return this.supportShare.includes(osType)
    },
    localPathTooltip() {
      let content = ''
      if (this.form.local.type && this.form.local.type === 1) {
        if (this.isWindows) {
          content = this.$t('pages.manageLibrary_localPathTooltip1') || ''
        } else if (this.isLinux) {
          content = this.$t('pages.manageLibrary_localPathTooltip2') || ''
        }
      } else {
        content = this.$t('pages.manageLibrary_helpMessage1') || ''
      }
      return content;
    }
  },
  watch: {
    //  云平台未启用，本地备份默认开启
    'form.local.enable'(val) {
      if (val === 0) {
        this.form.local.enable = 1
      }
    },
    'onLineSwitchFlag'(val) {
      this.form.online.enable = val ? 1 : 0
      if (!val) {
        this.form.encryptionKeyType = 1
        this.form.encryptionKey = null
      }
    },
    'form.local.devId'(val) {
      if (this.isWindows && val !== null && val !== undefined) {
        this.getDiskContent()
      }
    }
  },
  created() {
    //  配置操作系统类型
    const osType = this.$store.getters.engineOsType || ''
    if (osType === 'windows') {
      this.isWindows = true
      this.isLinux = false
    } else if (osType === 'linux') {
      this.isWindows = false
      this.isLinux = true
    }
  },
  methods: {
    init() {
      //  获取管理库备份配置信息
      this.getServerInfo()
    },
    //  根据管理库终端id查询管理库信息
    getServerInfo() {
      const _this = this
      // 重置输入的状态
      this.hasChangePwd = false
      //  获取操作系统类型和磁盘信息
      getCurrentDataBaseServerInfo().then(res => {
        // const array = [];
        const serverInfo = res.data.serverInfo
        if (serverInfo) {
          let systemName = serverInfo.serverBaseInfo ? serverInfo.serverBaseInfo.systemName || '' : '';

          //  如果systemName 为空，表示后台的操作系统类型没有缓存成功，前端通过socket请求操作系统类型
          if (systemName === '' || serverInfo.serverDiskInfoList === undefined || serverInfo.serverDiskInfoList === null) {
            const data = JSON.parse(localStorage.getItem('backUp_serverBaseInfo'));
            if (data != null && data.serverInfo && data.dataBaseBack) {
              _this.setSeverInfoData(data.serverInfo, data.dataBaseBack)
              const count = localStorage.getItem('backUp_usedCacheCount');
              if (count == null) {
                localStorage.setItem('backUp_usedCacheCount', 0)
              } else if (count <= 10) {
                localStorage.setItem('backUp_usedCacheCount', count + 1)
              } else {
                localStorage.removeItem('backUp_serverBaseInfo');
              }
              return
            }
            serverInfo.serverBaseInfo && serverInfo.serverBaseInfo.devId &&
            this.$socket.sendToUser(serverInfo.serverBaseInfo.devId, '/getServerInfo', serverInfo.serverBaseInfo.devId, (respond, handle) => {
              const data = JSON.parse(JSON.stringify(respond.data))
              systemName = data.serverBaseInfo && data.serverBaseInfo.systemName || ''
              _this.setSeverInfoData(data, res.data.dataBaseBack)
              if (data.serverBaseInfo && data.serverBaseInfo.systemName && data.serverBaseInfo.systemName !== '' && res.data.dataBaseBack) {
                localStorage.setItem('backUp_serverBaseInfo', JSON.stringify(data))
                localStorage.removeItem('backUp_usedCacheCount')
              }
            });
          } else {
            _this.setSeverInfoData(serverInfo, res.data.dataBaseBack)
            localStorage.setItem('backUp_serverBaseInfo', JSON.stringify(res.data))
            localStorage.removeItem('backUp_usedCacheCount')
          }
        }
      });
    },
    //  设置基本信息
    setSeverInfoData(serverInfo, dataBaseBack) {
      const array = [];
      //  判断操作系统类型
      (serverInfo.serverDiskInfoList || []).forEach(item => {
        item.keyId = item.diskName && item.diskName.length > 4 ? item.diskName.substr(item.diskName.length - 3, 1) : '';
        array.push(item);
      })
      this.serverDiskInfoList = array

      if (dataBaseBack) {
        this.setForm(dataBaseBack);
        this.form.online.backupTime.interval = this.form.online.backupTime.interval === undefined || this.form.online.backupTime.interval == null ? 1 : this.form.online.backupTime.interval;
        //  后台数据库存储
        this.able = this.form.enable && this.form.enable === 1
        this.onLineSwitchFlag = this.form.online.enable === 1
        //  去除自定义密钥
        if (this.form.encryptionKeyType !== 3) {
          this.form.encryptionKey = ''
        }
        this.encryptionKeyTypeTemp = this.form.encryptionKeyType
        if (this.isWindows) {
          //  当备份路径类型不为共享磁盘时
          if (this.form.local.type !== 2) {
            //  获取盘符编号，即c盘或d盘等等
            const cp = this.form.local.path.substr(0, 1)
            const array = this.serverDiskInfoList.filter(item => { return cp && item.keyId == cp }) || []
            this.form.local.devId = array.length > 0 ? array[0].devId : null
            this.form.local.path = this.form.local.path.substr(4)
          }
        }
      }
      if (this.form.local.type === 2) {
        this.validTestField()
      }
    },
    setForm(newForm) {
      newForm.online = newForm.online || this.defaultForm.online
      this.form.enable = newForm.enable || 0
      this.form.online.frequency = newForm.online.frequency || this.defaultForm.online.frequency;
      this.form.local.enable = newForm.local.enable || 0
      //  当返回的备份类型为共享文件夹，但系统不支持共享文件夹备份时，清空数据，备份类型改成本地磁盘/1
      if (!this.isSupportShare && newForm.local.type === 2) {
        newForm.local.path = ''
        newForm.local.type = 1
        newForm.local.lpRemoteName = ''
        newForm.local.lpUserName = ''
        newForm.local.lpPassword = ''
      }
      this.form.local.path = newForm.local.path || ''
      this.form.local.type = newForm.local.type || 1
      this.form.local.lpRemoteName = newForm.local.lpRemoteName || ''
      this.form.local.lpUserName = newForm.local.lpUserName || ''
      this.form.local.lpPassword = newForm.local.lpPassword || ''
      this.form.online.retainCount = newForm.online.retainCount || 1;
      this.form.time = newForm.time || this.defaultForm.time
      this.form.online.backupTime.week = (newForm.online.backupTime && newForm.online.backupTime.week) || this.defaultBackupTime.week
      this.form.online.backupTime.interval = (newForm.online.backupTime && newForm.online.backupTime.interval) || this.defaultBackupTime.interval
      this.form.online.backupTime.month = (newForm.online.backupTime && newForm.online.backupTime.month) || this.defaultBackupTime.month
      this.form.online.enable = newForm.online.enable || 0
      this.form.encryptionKeyType = newForm.encryptionKeyType || 2
      this.form.encryptionKey = newForm.encryptionKey || null
      // 保存加密的值 如果输入框未输入 则lpPassword直接使用该值
      this.encryptPwd = this.form.local.lpPassword
      this.form.local.lpPassword = aesDecode(this.encryptPwd, formatAesKey('tr838408', '')) ? '     ' : ''
    },
    //  根据devId获取磁盘信息
    getDiskNode(devId) {
      let temp = null
      for (let i = 0; i < this.serverDiskInfoList.length; i++) {
        if (this.serverDiskInfoList[i].devId === devId) {
          temp = this.serverDiskInfoList[i];
          break;
        }
      }
      return temp
    },
    //  获取当前磁盘信息
    getDiskContent() {
      const temp = this.getDiskNode(this.form.local.devId)
      if (temp != null) {
        const totalDiskSize = this.getFixed((temp.totalDiskSize || 0) / 1024 / 1024 / 1024) + 'GB';
        const useDiskSize = this.getFixed((temp.useDiskSize || 0) / 1024 / 1024 / 1024) + 'GB';
        const freeSize = this.getFixed((temp.freeSize || 0) / 1024 / 1024 / 1024) + 'GB';
        const usedPercent = (temp.usedPercent || 0) + '%'
        const prama = { 1: totalDiskSize, 2: useDiskSize, 3: freeSize, 4: usedPercent }
        this.devContent = (this.$t('pages.manageLibrary_diskInformation', prama) || '')
      } else {
        this.devContent = this.$t('pages.manageLibrarySave_text3');
      }
    },
    getFixed(fractionDigits) {
      if (fractionDigits === 0) {
        return 0;
      }
      return fractionDigits.toFixed(2);
    },
    //  获取完整本地备份地址, path 为路径下的
    getBackPath(path) {
      if (this.isWindows) {
        const temp = this.getDiskNode(this.form.local.devId);
        const diskName = temp && temp.diskName || ''
        if (diskName.length >= 4) {
          path = path.startsWith('\\') || path.startsWith('/') ? path.substr(1) : path;
          path = path.replaceAll('/', '\\');
          return diskName.substr(diskName.length - 3, 1) + ':\\\\' + path
        }
        return null;
      } else {
        path = path.replaceAll('\\', '/');
        return path;
      }
    },
    //  本地磁盘路径校验
    localPathValidator(value, callback) {
      //  校验路径不能为空字符串，
      //  windows下，文件夹名称不能有空格，文件夹名称只能是数字，字母，中文，反斜杠；
      const windowsPattern = /(\\{0,1}[0-9a-zA-Z\u4e00-\u9fa5_]+)*$/g;
      //  linux下，文件夹名称不能有空格，文件夹名称只能是数字，字母，斜杠
      const linuxPattern = /^\/(\w+\/?)+$/;
      const pattern = this.isWindows ? windowsPattern : this.isLinux ? linuxPattern : windowsPattern;
      this.form.local.path = this.form.local.path || ''
      this.form.local.path = this.form.local.path.trim()
      if (this.form.local.path === '') {
        //  windows下支持为空
        callback(new Error(this.$t('pages.databaseBackupRecord_backupAddressNotNull') + ''))
      } else {
        let flag = false;
        if (this.form.local.path !== '') {
          const flagArray = this.form.local.path.match(pattern) || []
          for (let i = 0; i < flagArray.length; i++) {
            if (flagArray[i] === this.form.local.path) {
              flag = true;
              break;
            }
          }
        }
        if (!flag) {
          callback(new Error(this.$t('pages.manageLibrary_addressNotRules') + ''))
        } else {
          callback();
        }
      }
    },
    //  共享盘符
    sharePathValidator(value, callback) {
      this.deleteShareTestConnection('path')
      //  windows下，文件夹名称不能有空格，文件夹名称只能是数字，字母，中文,反斜杠；
      const windowsPattern = /(([0-9a-zA-Z\u4e00-\u9fa5_]{1})||([0-9a-zA-Z\u4e00-\u9fa5_]+\\{0,1}[0-9a-zA-Z\u4e00-\u9fa5_]+))*$/g;
      this.form.local.path = this.form.local.path || ''
      this.form.local.path = this.form.local.path.trim()
      if (this.form.local.path === '') {
        callback(new Error(this.$t('pages.manageLibrary_addressNotNull')))
      } else {
        let flag = false;
        if (this.form.local.path !== '') {
          const flagArray = this.form.local.path.match(windowsPattern) || []
          for (let i = 0; i < flagArray.length; i++) {
            if (flagArray[i] === this.form.local.path) {
              flag = true;
              break;
            }
          }
        }
        if (!flag) {
          callback(new Error(this.$t('pages.manageLibrary_addressNotRules')))
        } else {
          this.addShareTestConnection('path')
          callback();
        }
      }
    },
    //  定时备份保存
    async save() {
      //  校验path是否符合规则, 和自定义密钥的长度是否为16
      let passFlag = false
      if (this.able) {
        if (this.form.local.enable === 0 && this.form.online.enable === 0) {
          this.$message({
            message: this.$t('pages.manageLibrary_enableLocalBack'),
            type: 'warning',
            duration: 2000
          })
          return
        }
        await this.$refs['dataForm'].validate((valid, err) => {
          passFlag = !valid
        })
        if (passFlag) {
          return;
        }
      }
      this.$confirmBox(
        this.$t('text.prompt'),
        {
          title: this.$t('text.prompt'),
          message: this.$t('pages.manageLibrarySaveConfirmMessage'),
          type: 'warning'
        }
      ).then(() => {
        this.saveSubmitting = true
        const form = JSON.parse(JSON.stringify(this.form))
        //  如果type为windows，添加盘符
        form.local.path = this.form.local.type === 1 ? this.getBackPath(this.form.local.path) : form.local.path
        form.enable = this.able ? 1 : 0
        this.clearNotUsedFormData(form)
        this.encryptionKeyTypeTemp = form.encryptionKeyType ? form.encryptionKeyType : null;
        form.notice = 1
        form.local = this.encryptPassword(form.local, 'lpPassword')
        manageDataBaseBack(form).then(res => {
          this.$message({
            message: this.$t('pages.manageLibrary_saveSuccess'),
            type: 'success'
          })
          this.saveSubmitting = false
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        }).catch(() => {
          this.encryptionKeyTypeTemp = null;
          this.saveSubmitting = false;
        });
      }).catch(() => {
      })
    },
    //  清空无用数据
    clearNotUsedFormData(form) {
      form.time = this.form.time;
      form.local.devId = null;
      if (form.online.enable) {
        const frequency = form.online.frequency
        form.online.backupTime = Object.assign({}, this.defaultBackupTime)
        switch (frequency) {
          case 'week': form.online.backupTime.week = this.form.online.backupTime.week; break;
          case 'month': form.online.backupTime.month = this.form.online.backupTime.month; break;
          case 'customize': form.online.backupTime.interval = this.form.online.backupTime.interval; break;
        }
      } else {
        form.online.frequency = null
        form.online.retainCount = null
        form.online.backupTime = null
      }
    },
    frequencyChange(data) {
      switch (data) {
        case 'week': this.form.online.backupTime.week = 1; break;
        case 'month': this.form.online.backupTime.month = 1; break;
        case 'customize': this.form.online.backupTime.interval = 1; break;
        default:
      }
    },
    intervalBlur(val) {
      this.form.online.backupTime.interval = val === undefined || val === null ? this.defaultForm.online.backupTime.interval : this.form.online.backupTime.interval
    },
    typeChange(val) {
      this.form.local.path = ''
      this.testFlag = true
      this.isAlloweTestArr = []
      //  若为共享文件夹
      if (this.form.local.type === 2) {
        this.validTestField()
      } else {
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate();
        })
      }
    },
    //  若为类型为共享文件夹，校验ip地址、用户名、共享网络路径
    validTestField() {
      const field = []
      if (this.form.local.lpRemoteName) {
        field.push('lpRemoteName')
      }
      if (this.form.local.lpUserName) {
        field.push('lpUserName')
      }
      if (this.form.local.path) {
        field.push('path')
      }
      if (field.length > 0) {
        this.$nextTick(() => {
          this.$refs['dataForm'].validateField(field);
        })
      }
    },
    //  测试连接
    test() {
      const _this = this
      this.testLoading = true;
      const tempData = this.encryptPassword(this.form.local, 'lpPassword')
      testConnection(tempData).then(res => {
        _this.$message({
          message: this.$t('pages.manageLibrary_testConnectionSuccess'),
          type: 'success',
          duration: 2000
        })
        this.testLoading = false;
        this.testFlag = false
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate();
        })
      }).catch(() => { this.testLoading = false; this.testFlag = true; })
    },
    testChange() {
      if (this.form.local.type === 2) {
        this.testFlag = true;
      }
    },
    //  立即备份，  立即备份本地或云平台必须开启
    async handlerOnceClick() {
      if (this.form.local.enable === 0 && this.form.online.enable === 0) {
        this.$message({
          message: this.$t('pages.manageLibrary_enableLocalBack'),
          type: 'warning',
          duration: 2000
        })
        return
      }
      this.onceClickFlag = true

      this.$refs['dataForm'].validate((valid, err) => {
        if (valid) {
          this.onceClickLoading = true
          const form = JSON.parse(JSON.stringify(this.form))
          //  如果type为windows，添加盘符
          form.local.path = this.form.local.type === 1 ? this.getBackPath(this.form.local.path) : form.local.path
          form.enable = this.able ? 1 : 0
          this.clearNotUsedFormData(form)
          this.encryptionKeyTypeTemp = form.encryptionKeyType ? form.encryptionKeyType : null;
          form.notice = 2
          form.local = this.encryptPassword(form.local, 'lpPassword')
          manageDataBaseBack(form).then(res => {
            this.$message({
              message: this.$t('pages.manageLibrary_onceClickBackupSuccess'),
              type: 'success',
              duration: 2000
            })
            this.onceClickFlag = false
            this.onceClickLoading = false
          }).catch(e => {
            this.onceClickFlag = false
            this.onceClickLoading = false
          })
        } else {
          this.onceClickFlag = false
        }
      })
    },
    ableChange(value) {
      //  关闭定时备份时，清空校验
      if (!value) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
        })
      } else {
        //  当前不开启云备份功能，定时备份开时，本地备份自动开
        this.form.local.enable = 1
      }
    },
    //  当同时关闭本地备份和云备份时，关闭定时备份开关
    changeLocalOnlineEnable(value) {
      if (!this.form.local.enable && !this.onLineSwitchFlag) {
        this.able = false
      }
    },
    deleteShareTestConnection(value) {
      if (value) {
        const index = this.isAlloweTestArr.indexOf(value);
        if (index > -1) {
          this.isAlloweTestArr.splice(index, 1);
        }
      }
    },
    addShareTestConnection(value) {
      if (value) {
        const index = this.isAlloweTestArr.indexOf(value);
        if (index === -1) {
          this.isAlloweTestArr.push(value);
        }
      }
    },
    encryptPassword(obj, pwdField) {
      const password = this.hasChangePwd ? aesEncode(obj[pwdField], formatAesKey('tr838408', '')) : this.encryptPwd
      const tempData = JSON.parse(JSON.stringify(obj))
      tempData[pwdField] = password
      return tempData
    },
    inputPwd($event) {
      if (!this.hasChangePwd) {
        $event.target.value = $event.data || ''
      }
      this.hasChangePwd = true
      this.form.local.lpPassword = $event.target.value
    }
  }
}
</script>
<style lang="scss" scoped>
  .app-container{
    overflow: auto;
  }
  >>>.el-card__body {
    padding: 10px 40px;
  }
  >>>.el-form-item{
    margin: 10px 0 15px 0;
  }
  >>>.el-form-item__label{
    line-height: 30px;
    color: #ccc;
  }
  >>>.el-row, >>>.el-form-item__content{
    line-height: 30px;
  }
  .save-btn-container{
    width: 700px;
    margin-top: 10px;
    text-align: right;
  }
  .red{
    color: red;
  }
  .title {
    font-size: 15px;
  }
  ::v-deep .input-css .el-input__inner {
    height: auto !important;
  }
  >>>.el-input--suffix {
    width: 200px;
  }
</style>
