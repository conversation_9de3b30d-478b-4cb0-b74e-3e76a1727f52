<template>
  <div class="app-container">
    <el-select v-model="lang">
      <el-option value="zh" label="zh"></el-option>
      <el-option value="tw" label="tw"></el-option>
      <el-option value="en" label="en"></el-option>
    </el-select>
    <el-button type="primary" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
    <el-upload
      ref="upload"
      name="uploadFile"
      action="aaaaaa"
      accept=".xlsx"
      style="display: inline-block;"
      :show-file-list="false"
      :before-upload="beforeUpload"
    >
      <el-button type="primary" size="mini">{{ $t('button.import') }}</el-button>
    </el-upload>
  </div>
</template>

<script>
import { exportI18nData, importAndConvert } from '@/api/i18n'
export default {
  name: 'LangExport',
  data() {
    return {
      lang: 'zh'
    }
  },
  methods: {
    beforeUpload(file) {
      const formData = new FormData();
      formData.append('file', file);
      importAndConvert(formData)
        .then(response => {
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', this.lang + '.json'); // 设置下载文件的文件名
          document.body.appendChild(link);
          link.click();
        })
      return false;
    },
    async handleExport() {
      const response = await exportI18nData(this.lang)
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', this.lang + '.xlsx'); // 设置下载文件的文件名
      document.body.appendChild(link);
      link.click();
    }
  }
}
</script>
