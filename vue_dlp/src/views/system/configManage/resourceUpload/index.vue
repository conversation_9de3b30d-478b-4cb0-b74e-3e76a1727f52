<template>
  <div style="padding:20px">
    <el-upload
      name="processName"
      action="1111"
      accept=".source"
      :limit="1"
      :show-file-list="false"
      :before-upload="handleBeforeUpload"
    >
      <label style="font-size: 15px;">{{ $t('pages.uploadResourceFileTip') }}:</label>
      <el-button type="primary" icon="el-icon-upload" size="small" style="margin-left: 1px"></el-button>
      <el-button type="primary" icon="el-icon-save" size="small" style="margin-left: 1px" :disabled="!fileContent" @click.stop="save">{{ $t('button.save') }}</el-button>
      <el-button
        v-if="hasResourceBundle"
        icon="el-icon-delete"
        size="small"
        type="error"
        @click.prevent="handleDeleteResourceBundle"
      > {{ $t('pages.deleteResourceBundle') }}</el-button>
    </el-upload>
    <div style="color: rgb(113, 175, 239); font-size: 15px;margin-top:10px">{{ $t('pages.needChangeResourceTip') }}</div>
    <div v-show="fileContent || hasResourceBundle" style="margin-top: 10px;">
      <grid-table
        ref="resourceTable"
        :col-model="[
          { prop: 'name', label: $t('pages.resourceKey'), width: '60' },
          { prop: 'value', label: $t('pages.resourceValue'), width: '150', formatter: resourceValueFormatter }
        ]"
        :height="400"
        :multi-select="false"
        :row-data-api="resourceRowDataApi"
        :show-pager="false"
        style="width: 800px;"
      />
    </div>
  </div>
</template>

<script>
import {
  resolveResourceBundle,
  getResourceBundle,
  deleteResourceBundle,
  uploadResourceBundleAndReplace
} from '@/api/system/configManage/globalConfig'
import { readAsDataURL } from '@/utils/blob'
export default {
  name: 'ResourceUpload',
  data() {
    return {
      resourceDataMap: {
        copyright_zh: this.$t('pages.copyright_zh'),
        copyright_en: this.$t('pages.copyright_en'),
        copyright_tw: this.$t('pages.copyright_tw'),
        favicon: this.$t('text.favicon'),
        loginLogo: this.$t('pages.loginLogo'),
        title_zh: this.$t('pages.systemName_zh'),
        title_en: this.$t('pages.systemName_en'),
        title_tw: this.$t('pages.systemName_tw'),
        website: this.$t('pages.corpWebsite'),
        topLogo: this.$t('pages.topLogo'),
        theme: this.$t('pages.theme')
      },
      fileContent: '',
      resourceData: {},
      hasResourceBundle: false
    }
  },
  watch: {
    resourceData() {
      this.$refs.resourceTable.execRowDataApi()
    }
  },
  created() {
    this.getResourceBundle()
  },
  methods: {
    async save() {
      await uploadResourceBundleAndReplace(this.fileContent)
      await this.getResourceBundle()
      this.$message({
        message: this.$t('text.saveSuccess'),
        type: 'success'
      })
    },
    handleDeleteResourceBundle(event) {
      this.$confirmBox(this.$t('pages.deleteConfirm'), this.$t('text.prompt'), {
        confirmButtonText: this.$t('button.confirm'),
        cancelButtonText: this.$t('button.cancel'),
        type: 'warning'
      }).then(async() => {
        const resp = await deleteResourceBundle()
        if (resp.data) {
          this.$message({
            type: 'success',
            message: this.$t('text.deleteSuccess')
          });
          this.fileContent = ''
          this.getResourceBundle()
        }
      })
      event.stopPropagation()
      return false
    },
    async getResourceBundle() {
      const resp = await getResourceBundle()
      this.hasResourceBundle = resp.data.status
      if (this.hasResourceBundle) {
        this.resourceData = resp.data.data
      }
    },
    handleBeforeUpload(file) {
      readAsDataURL(file)
        .then(async data => {
          this.fileContent = data.split(',')[1]
          if (!this.fileContent) {
            this.$message({
              message: this.$t('pages.resourceResolveFail'),
              type: 'error'
            })
            return
          }
          const resp = await resolveResourceBundle(this.fileContent)
          if (!resp.data.status) {
            this.$message({
              message: this.$t('pages.resourceResolveFail'),
              type: 'error'
            })
            return
          }
          this.resourceData = resp.data.data
        })
      return false
    },
    resourceValueFormatter(data) {
      if (data.value.indexOf && data.value.indexOf(';base64') > -1) {
        return `<img style="width:64px;height:64px" src='${data.value}'>`
      }
      return data.value
    },
    async resourceRowDataApi() {
      const orderKey = {
      }
      orderKey[this.$t('pages.systemName_zh')] = 1
      orderKey[this.$t('pages.systemShortName_zh')] = 2
      orderKey[this.$t('pages.systemName_tw')] = 3
      orderKey[this.$t('pages.systemShortName_tw')] = 4
      orderKey[this.$t('pages.systemName_en')] = 5
      orderKey[this.$t('pages.systemShortName_en')] = 6
      orderKey[this.$t('pages.copyright_zh')] = 7
      orderKey[this.$t('pages.copyright_tw')] = 8
      orderKey[this.$t('pages.copyright_en')] = 9
      orderKey[this.$t('pages.corpWebsite')] = 10
      orderKey[this.$t('pages.loginLogo')] = 11
      orderKey[this.$t('pages.topLogo')] = 12
      orderKey[this.$t('text.favicon')] = 13
      console.log(orderKey)
      const data = []
      for (const key in this.resourceData) {
        if (this.resourceData[key] instanceof Object) {
          for (const skey in this.resourceData[key]) {
            data.push({
              name: this.resourceDataMap[key + '_' + skey],
              value: this.resourceData[key][skey]
            })
          }
        } else {
          const item = {
            name: this.resourceDataMap[key],
            value: this.resourceData[key]
          }
          if (!item.value || !item.name) {
            continue
          }
          data.push(item)
        }
      }
      data.sort((a, b) => {
        return orderKey[a.name] - orderKey[b.name]
      })
      console.log(data)
      return {
        data: {
          items: data
        }
      }
    }
  }
}
</script>
