<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ i18nConcatText(this.$t('pages.application'), 'create') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ i18nConcatText(this.$t('pages.application'), 'delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.appName" clearable :placeholder="$t('text.pleaseEnterInfo', {info: $t('pages.appIdOrName')} )" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="codeList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <Drawer
      :close-on-click-modal="false"
      :modal="false"
      :title="createSuccess ? i18nConcatText(this.$t('pages.application'), 'update') : i18nConcatText(this.$t('pages.application'), 'create')"
      :visible.sync="dialogFormVisible"
      width="700px"
      :before-close="beforeClose"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="100px">
        <el-divider v-if="createSuccess" content-position="left">{{ $t('pages.baseInfo') }}</el-divider>
        <FormItem :label="$t('pages.applyName')" prop="appName">
          <el-input v-model="temp.appName" v-trim maxlength="60"></el-input>
        </FormItem>
        <FormItem :label="$t('components.enable')">
          <el-switch v-model="temp.active" :active-value="1" :inactive-value="0"/>
        </FormItem>
        <FormItem :label="$t('pages.applyRemark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" show-word-limit maxlength="50"/>
        </FormItem>
        <el-divider v-if="createSuccess" content-position="left">{{ $t('pages.encryptionConfiguration') }}</el-divider>
        <FormItem :label="$t('pages.compressionAlgorithm')" prop="compressMode">
          <el-select v-model="temp.compressMode" default-first-option style="width: 300px;">
            <el-option :label="$t('pages.uncompressed')" :value="0"></el-option>
            <el-option label="Gzip" :value="1"></el-option>
            <!--            <el-option label="Deflate" :value="2"></el-option>-->
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.encryptionAlgorithm')" prop="encryMode">
          <el-select v-model="temp.encryMode" default-first-option style="width: 300px;">
            <el-option :label="$t('pages.notEncrypted')" :value="0"></el-option>
            <el-option label="SM4" :value="1"></el-option>
            <!--            <el-option label="AES" :value="2"></el-option>
            <el-option label="3DES" :value="3"></el-option>-->
          </el-select>
        </FormItem>
        <FormItem v-if="createSuccess && 0 !== temp.encryMode" :label="$t('pages.manageLibrary_encryptionKey')" prop="appSecret">
          <el-tooltip slot="tooltip" class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.encryptionKeyTips') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-input v-model="temp.encryKey" :placeholder="$t('pages.encryptionKeyPlaceholder')" disabled style="width: 95%;"></el-input>
          <el-button style="position: absolute;margin-left: 6px;margin-top: 4px" icon="el-icon-document-copy" @click="copyContent(temp.encryKey)"/>
        </FormItem>
        <template v-if="createSuccess" >
          <el-divider content-position="left">{{ $t('pages.syncExtAppId') }}</el-divider>
          <FormItem label="App ID" prop="appId">
            <el-tooltip slot="tooltip" class="item" effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.syncExtAppIdTips') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-input v-model="temp.appId" disabled style="width: 95%;"></el-input>
            <el-button style="position: absolute;margin-left: 6px;margin-top: 4px" icon="el-icon-document-copy" @click="copyContent(temp.appId)"/>
          </FormItem>
          <FormItem label="App Secret" prop="appSecret">
            <el-tooltip slot="tooltip" class="item" effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.syncExtAppSecret') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-input v-model="temp.appSecret" disabled style="width: 95%;"></el-input>
            <el-button style="position: absolute;margin-left: 6px;margin-top: 4px" icon="el-icon-document-copy" @click="copyContent(temp.appSecret)"/>
          </FormItem>
        </template>
        <el-divider content-position="left">{{ $t('pages.serviceSwitch') }}</el-divider>
        <FormItem label="" label-width="30px" prop="bussIdStr">
          <el-button type="text" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
          <el-button type="text" @click="selectAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
          <el-button type="text" @click="expandAll(true)">展开</el-button>
          <el-button type="text" @click="expandAll(false)">收起</el-button>
          <el-checkbox-group v-model="temp.bussIdStr">
            <el-checkbox
              v-for="group in groupIdNameList"
              :key="group.id"
              :label="group.id"
              :indeterminate="getProperty(groupOptionSelect, false, group.id, 'half')"
              :style="detailOptionShowMap[group.id] ? { display: 'block' } : { 'min-width': '250px' }"
              @change="(checked)=> {selectAll(checked, group.id)}"
            >
              {{ group.name + '(' + getProperty(groupOptionSelect, 0, group.id, 'select') + '/' + getProperty(groupOptionSelect, 0, group.id, 'total') + ')' }}
              <i :class="detailOptionShowMap[group.id]?'el-icon-caret-bottom':'el-icon-caret-right'" @click.stop.prevent="expandAll(group.id)"/>
              <div v-show="detailOptionShowMap[group.id]" style="margin-left: 20px;width: 100%;">
                <el-checkbox v-for="(itemLabel, itemVal) in bussTypeOptionMap[group.id]" :key="itemVal" :label="Number.parseInt(itemVal)" style="width: 200px;" @change="()=>{selectGroupCheckBox(group.id)}">{{ itemLabel }}</el-checkbox>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createSuccess ? updateData() : createData()">
          {{ createSuccess ? $t('button.confirm') : $t('pages.create') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </Drawer>
  </div>
</template>

<script>
import { addSyncExtApp, updateSyncExtApp, deleteSyncExtApp, getSyncExtAppPage, getSyncExtAppBussIdMap } from '@/api/system/configManage/syncExtAppConfig'
import { copy } from '@/api/dataEncryption/fileTrace/documentTrack';

export default {
  name: 'SyncExtAppConfig',
  data() {
    return {
      colModel: [
        { prop: 'appId', label: this.$t('pages.dataSourceAppId'), width: '80', sort: true, iconFormatter: this.activeFormatter },
        { prop: 'appName', label: this.$t('pages.applyName'), width: '100', sort: true },
        { prop: 'appSecret', label: this.$t('pages.dataSourceAppSecretKey'), width: '200', formatter: this.keysFormatter },
        { prop: 'encryKey', label: this.$t('pages.manageLibrary_encryptionKey'), width: '120', formatter: this.keysFormatter },
        { prop: 'appInformation', label: this.$t('pages.applyInformation'), width: '150' },
        { prop: 'createTime', label: 'createTime', width: '120', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '80',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        appName: ''
      },
      deleteable: false,
      temp: {},
      defaultTemp: {
        id: undefined,
        appName: undefined,
        remark: undefined,
        appId: undefined,
        appSecret: undefined,
        encryKey: undefined,
        compressMode: 1,
        encryMode: 1,
        bussIdStr: [],
        active: 0
      },
      dialogFormVisible: false,
      rules: {
        appName: [{ required: true, message: this.$t('text.cantNullInfo', { info: this.$t('pages.applyName') }), trigger: 'blur' }],
        bussIdStr: [{ required: true, message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.serviceSwitch') }), trigger: 'blur,change' }]
      },
      submitting: false,
      createSuccess: false,
      bussTypeOptions: [],
      bussTypeOptionMap: {},
      detailOptionShowMap: {},
      groupOptionSelect: {},
      activeOptions: { 1: this.$t('text.enable'), 0: this.$t('text.disable') },
      groupIdNameList: [
        { id: '1', name: '同步' + this.$t('pages.basicData') },
        { id: '2', name: '同步' + this.$t('route.alarmDetailLog') },
        { id: '3', name: '同步' + this.$t('route.SystemLog') },
        { id: '4', name: '同步' + this.$t('route.HardwareLog') },
        { id: '5', name: '同步' + this.$t('route.terminalSystem') },
        { id: '6', name: '同步' + this.$t('route.ApplicationLog') },
        { id: '7', name: '同步' + this.$t('route.networkAuditing') },
        { id: '8', name: '同步' + this.$t('route.encryptionLog') },
        { id: '9', name: '同步' + this.$t('route.contentLog') },
        { id: '10', name: '同步' + this.$t('route.approvalMgrLog') },
        { id: '11', name: this.$t('pages.remoteControlTerm') }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['codeList']
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.initBussTypeOptions()
  },
  methods: {
    getProperty(obj, defaultVal, prop1, prop2) {
      if (obj && obj[prop1]) {
        if (prop2) {
          return this.getProperty(obj[prop1], defaultVal, prop2)
        }
        return obj[prop1]
      }
      return defaultVal
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getSyncExtAppPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    initBussTypeOptions() {
      getSyncExtAppBussIdMap().then(resp => {
        this.bussTypeOptionMap = resp.data;
        for (const key in resp.data) {
          const keyData = resp.data[key]
          for (const subKey in keyData) {
            this.bussTypeOptions.push({
              value: Number.parseInt(subKey),
              label: keyData[subKey]
            })
          }
        }
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.detailOptionShowMap = {}
      this.groupOptionSelect = {}
      this.createSuccess = false;
    },
    handleDrag() {
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.createSuccess = false;
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        this.selectAll(false)
      })
    },
    formatReqData() {
      const data = Object.assign({}, this.temp);
      data.bussIdStr = ''
      if (this.temp.bussIdStr && this.temp.bussIdStr.length > 0) {
        var maxBussId = 0;
        for (let i = 0; i < this.bussTypeOptions.length; i++) {
          maxBussId = Math.max(maxBussId, this.bussTypeOptions[i].value);
        }
        for (let i = 0; i < maxBussId; i++) {
          data.bussIdStr += this.temp.bussIdStr.includes(i + 1) ? 1 : 0;
        }
      }
      return data;
    },
    formatResData(data) {
      const bussIdStr = data.bussIdStr;
      if (typeof bussIdStr === 'object' && bussIdStr.length > 0) {
        return data;
      } else if (typeof bussIdStr === 'string' && bussIdStr !== '') {
        data.bussIdStr = this.bussTypeOptions
          // 位移与bussId按位与，得出对应业务类型
          .filter((t) => bussIdStr.substring(t.value - 1, t.value) === '1')
          .map(item => item.value);
      } else {
        data.bussIdStr = this.bussTypeOptions.map(item => item.value);
      }
      return data;
    },
    createData() {
      this.createSuccess = !!this.temp.id;
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const postData = this.formatReqData();
          addSyncExtApp(postData).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.temp = this.formatResData(respond.data);
            this.createSuccess = true;
          }).catch(reason => {
            this.submitting = false
          });
        } else {
          this.submitting = false
        }
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      row = this.formatResData(row);
      this.temp = Object.assign({}, this.defaultTemp, row)
      this.createSuccess = true;
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        for (const groupId in this.bussTypeOptionMap) {
          this.selectGroupCheckBox(groupId)
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const postData = this.formatReqData();
          updateSyncExtApp(postData).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.temp = this.formatResData(respond.data);
            this.dialogFormVisible = false;
          }).catch(reason => {
            this.submitting = false
          });
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteSyncExtApp({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
      })
    },
    beforeClose() {
      this.dialogFormVisible = false
    },
    expandAll(data) {
      if (typeof data === 'boolean') {
        this.groupIdNameList.forEach(item => {
          this.$set(this.detailOptionShowMap, item.id, data)
        })
      } else {
        this.$set(this.detailOptionShowMap, data, !this.detailOptionShowMap[data])
      }
    },
    selectAll(isSelected, groupId) {
      if (groupId && typeof groupId === 'string') {
        // 说明是选择某一个分组
        const optMap = this.bussTypeOptionMap[groupId]
        for (const key in optMap) {
          const bussId = Number.parseInt(key)
          if (isSelected) {
            if (!this.temp.bussIdStr.includes(bussId)) {
              this.temp.bussIdStr.push(bussId)
            }
          } else {
            const index = this.temp.bussIdStr.indexOf(bussId)
            if (index >= 0) {
              this.temp.bussIdStr.splice(index, 1)
            }
          }
        }
        this.selectGroupCheckBox(groupId, isSelected)
      } else {
        if (isSelected) { // 说明是选择所有分组
          this.temp.bussIdStr = this.bussTypeOptions.map(item => item.value)
        } else { // 说明是全不选
          this.temp.bussIdStr.splice(0)
        }
        for (const groupId in this.bussTypeOptionMap) {
          this.selectGroupCheckBox(groupId, isSelected)
        }
      }
    },
    getSelectSizeArray(groupId) {
      const optMap = this.bussTypeOptionMap[groupId];
      const bussIds = Object.keys(optMap)
      const total = bussIds.length;
      const selectSize = bussIds.filter(item => this.temp.bussIdStr.includes(Number.parseInt(item))).length;
      return [total, selectSize];
    },
    selectGroupCheckBox(groupId, isSelected) {
      let total = 0
      let selectSize = 0
      if (typeof isSelected === 'boolean') {
        const index = this.temp.bussIdStr.indexOf(groupId)
        if (isSelected && index < 0) {
          this.temp.bussIdStr.push(groupId)
        } else if (!isSelected && index >= 0) {
          this.temp.bussIdStr.splice(index, 1)
        }
        const subItemSizeArr = this.getSelectSizeArray(groupId)
        total = subItemSizeArr[0]
        selectSize = subItemSizeArr[1]
      } else {
        // 如果未传isSelected参数，那么根据子选项选中情况，决定是否选中
        const subItemSizeArr = this.getSelectSizeArray(groupId)
        total = subItemSizeArr[0]
        selectSize = subItemSizeArr[1]
        this.selectGroupCheckBox(groupId, subItemSizeArr[0] === subItemSizeArr[1])
      }
      this.$set(this.groupOptionSelect, groupId, { total: total, select: selectSize, half: selectSize < total && selectSize > 0 })
    },
    copyContent(content) {
      copy(content, r => {
        this.$message({
          message: this.$t('pages.copy_success'),
          type: 'success',
          duration: 1000
        });
      })
    },
    keysFormatter(row, data) {
      if (data) {
        const num = Math.ceil(data.length / 3);
        let starNum = '';
        for (let i = 0; i < num; i++) {
          starNum += '*';
        }
        return data.substring(0, num) + starNum + data.substring(data.length - num, data.length);
      }
      return this.$t('pages.notEncrypted');
    },
    activeFormatter: function(row, data) {
      const label = this.activeOptions[row.active]
      return row.active ? { class: 'active', title: label } : { class: 'offline', title: label, style: 'color: #888;' }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider__text{
  background-color: #ffffff;
  font-weight: 800;
  color: #303133;
}
</style>

