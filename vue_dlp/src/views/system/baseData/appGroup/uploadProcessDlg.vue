<template>
  <!-- 正在加载文件弹窗 -->
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible="visible"
    :title="'提示'"
    width="400px"
    :show-close="false"
  >
    <span style="color: #4ea2e4">{{ `${loading ? '正在上传数据' : '上传完成'}，已上传${fileCount}条数据...` }}</span><br>
    <el-progress :percentage="percentage"></el-progress>
    <div slot="footer" class="dialog-footer">
      <!-- 上传选中的文件/目录， 获取目录下所有文件 + 选中文件的 程序属性-->
      <el-button v-if="loading" :loading="breakUploadLoading" @click="breakUpload">{{ '中断上传' }}</el-button>
      <el-button v-if="!delayClose && !loading" @click="finish">{{ '完成' }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'UploadProcessDlg',
  props: {
    //  是否正在加载中
    loading: {
      type: Boolean,
      default: false
    },
    //  加载的文件个数
    fileCount: {
      type: Number,
      default: 0
    },
    //  是否延迟关闭，是：延迟自动关闭，否：手动点击关闭
    delayClose: {
      type: Boolean,
      default: false
    },
    //  延迟时间，单位：毫秒，默认1.5秒
    delayTime: {
      type: Number,
      default: 1500
    },
    //  中断之前的方法
    breakUploadBefore: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      percentage: 0,
      visible: false,
      loadingChange: [],   //  记录Loading的变化，当里面满足[1,2]时，表示可完成
      timeInterval: null,
      breakUploadLoading: false
    }
  },
  watch: {
    loading(value) {
      if (value) {
        this.loadingChange.push(1);
        this.percentage = 0
        this.visible = true
        this.timeIntervalAddNum()
      } else {
        this.loadingChange.push(2);
        if (this.loadingChange.includes(1) && this.loadingChange.includes(2)) {
          this.percentage = 100
          //  延迟关闭1秒
          setTimeout(() => this.finish(), this.delayTime);
        }
      }
    },
    fileCount(value) {
      const num = this.getRandomInt(1, 5);
      this.addPercentage(num)
    }
  },
  created() {
  },
  deactivated() {
    this.closeTimeInterval()
  },
  methods: {
    initData() {
      this.breakUploadLoading = false
      this.closeTimeInterval()
      this.percentage = 0
      this.loadingChange = []
    },
    //  中断上传
    breakUpload() {
      if (this.breakUploadBefore) {
        this.breakUploadBefore()
        this.breakUploadLoading = true
        this.breakUploadFun();
      } else {
        this.breakUploadFun();
      }
    },
    breakUploadFun() {
      this.visible = false
      this.initData()
      this.$emit('breakUpload')
    },
    finish() {
      console.log('finish')
      this.visible = false
      this.initData()
      this.$emit('finish')
    },
    /**
     * 添加进度条
     * @param value
     */
    addPercentage(value) {
      value = !value ? 0 : value;
      this.percentage = Math.min(this.percentage + value, 98)
    },
    /**
     * 生成 [min, max) 之间的随机整数
     * @param min
     * @param max
     * @returns {*}
     */
    getRandomInt(min, max) {
      min = Math.ceil(min);
      max = Math.floor(max);
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    /**
     * 每隔一定时间添加进度条
     */
    timeIntervalAddNum() {
      //  每隔5秒加1
      this.timeInterval = setInterval(() => {
        let num = this.getRandomInt(1, 2);
        const tempTotal = this.percentage + num
        if (tempTotal > 50) {
          num += this.getRandomInt(1, 5);
        }
        this.addPercentage(num)
        if (tempTotal > 50) {
          this.closeTimeInterval()
        }
      }, 5000)
    },
    closeTimeInterval() {
      if (this.timeInterval) {
        clearInterval(this.timeInterval)
        this.timeInterval = null
      }
    }
  }
}
</script>

<style scoped>

</style>
