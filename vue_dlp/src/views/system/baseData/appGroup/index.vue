<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="typeTree"
        resizeable
        :default-expand-all="true"
        :data="treeData"
        :is-filter="true"
        :render-content="renderContent"
        @node-click="treeNodeClick"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" :disabled="selectTreeId==null||selectTreeId==0" size="mini" @click="handleCreate">
          {{ $t('pages.addApp') }}
        </el-button>
        <el-button size="mini" @click="handleRemove">
          {{ $t('pages.deleteApp') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.processName" v-trim clearable :placeholder="$t('table.processName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover placement="bottom" width="350" trigger="click">
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('table.processName')">
                <el-input v-model="query.processName" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.productName')">
                <el-input v-model="query.productName" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.productVersion')">
                <el-input v-model="query.productVersion" v-trim clearable maxlength="50"/>
              </FormItem>
              <FormItem :label="$t('table.companyName')">
                <el-input v-model="query.companyName" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.softSign')">
                <el-input v-model="query.softSign" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem>
                <el-tooltip class="item" effect="dark" :content="$t('pages.appGroupNoFilterTypeMsg')" placement="top-start">
                  <el-checkbox v-model="query.noFilterType" :true-label="1" :false-label="0">{{ $t('pages.appGroupNoFilterType') }}</el-checkbox>
                </el-tooltip>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="softTable"
        :show-pager="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible1"
      width="800px"
      @dragDialog="handleDrag"
      @close="handleClose"
    >
      <Form
        ref="dataForm2"
        :rules="rules"
        :model="temp1"
        label-position="right"
        label-width="80px"
      >
        <div class="toolbar">
          <div style="display: inline-block; max-width: 300px;">
            <upload-dir v-if="!isIE() && !plugIsInWork" ref="uploadDir" :popover-height="235" :loading="fileSubmitting" style="display: inline-block;" @changeFile="changeFile" />
            <el-upload
              v-show="!plugIsInWork"
              ref="upload"
              name="uploadFile"
              action="aaaaaa"
              accept=".exe"
              :disabled="fileSubmitting"
              :show-file-list="false"
              :before-upload="beforeUpload"
              style="display: inline-block;"
            >
              <el-button type="primary" :loading="fileSubmitting" size="mini" style="margin: 0 0 5px 0">{{ $t('pages.uploadFile') }}</el-button>
            </el-upload>
            <!-- 打开窗口时，在进行心跳检测 -->
            <app-scan-plug ref="appScanPlug" :heartbeat-enable="dialogFormVisible1" @plugIsInWork="(inWork) => plugIsInWork = inWork" @uploadEnd="appendFile"/>
          </div>
          <el-button :loading="fileSelecting" type="primary" size="mini" style="margin: 0" @click="showTermAppSelectDlg">
            {{ $t('pages.selectTerminalSoft') }}
          </el-button>
          <el-row v-if="fileSubmitting">
            <el-col :span="22">
              <el-progress type="line" :percentage="percentage"/>
            </el-col>
            <el-col :span="2">
              <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
            </el-col>
          </el-row>
        </div>
        <grid-table
          ref="fileList"
          :height="300"
          :multi-select="true"
          :show-pager="false"
          :col-model="colModel2"
          :row-datas="fileList"
          row-key="fileMd5"
          :checked-row-keys="checkedRowKeys"
        />
        <FormItem :label="$t('pages.groupType')" prop="processType" style="margin-top:5px;">
          <tree-select
            ref="treeSelect"
            :data="formTreeData"
            node-key="dataId"
            :checked-keys="checkedKeys"
            :filter-key="filterParentKey"
            class="input-with-button"
            @change="parentIdChange"
          />
          <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate(1)">
            <svg-icon icon-class="add" />
          </el-button>
        </FormItem>

      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData1">{{ $t('button.confirm') }}</el-button>
        <el-button @click="handleClose">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <edit-group-dlg
      ref="editGroupDlg"
      show-parent
      :title="$t('pages.appGroup')"
      :group-tree-data="editTreeData"
      :add-func="createData"
      :update-func="updateData"
      :delete-func="deleteData"
      :move-func="updateSoft"
      :edit-valid-func="getGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />
    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="$t('pages.applicationProgram')"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteData"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
    />

    <batch-edit-page-dlg
      ref="batchEditDlg"
      :height="390"
      :title="updateGroupForm ? i18nConcatText(this.$t('pages.programGroup'), 'update') : i18nConcatText(this.$t('pages.program'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')">
        <tree-select
          ref="batchTreeSelect"
          :data="treeData"
          :checked-keys="checkedKeys2"
          is-filter
          :width="200"
          style="display: inline-block;"
          @change="parentIdObjChange"
        />
        <el-button class="editBtn" style="float: none; margin-bottom: 0;" @click="handleTypeCreate(2)">
          <svg-icon icon-class="add" />
        </el-button>
      </FormItem>
    </batch-edit-page-dlg>
    <terminal-soft ref="termAppSelectDlg" @select="selectTermFile"/>
  </div>
</template>
<script>
import { getTypeTree } from '@/api/behaviorManage/application/appVersion'
import {
  createData, updateData, deleteData, updateSoft, addSoft, deleteSoft,
  countChildByGroupId, getSoftwarePage, getGroupByName, deleteGroupAndData,
  moveGroupToOther, batchUpdateGroup, batchUpdateAllGroup, changeFiles, loopUploadFiles
} from '@/api/behaviorManage/application/appGroup'
import UploadDir from '@/components/UploadDir'
import EditGroupDlg from '@/views/common/editGroupDlg'
import axios from 'axios'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import AppScanPlug from '@/views/system/baseData/appGroup/appScanPlug'
import TerminalSoft from '@/views/dataEncryption/encryption/processStgLib/TerminalSoft'

export default {
  name: 'AppGroup',
  components: { TerminalSoft, AppScanPlug, BatchEditPageDlg, DeleteGroupDlg, UploadDir, EditGroupDlg },
  data() {
    return {
      tempFile: null,
      checkedKeys: [],
      formTreeData: [],
      colModel: [
        { prop: 'processName', label: 'processName', width: '110', sort: 'custom' },
        { prop: 'name', label: 'sourceGroup', width: '110', sort: 'custom', formatter: this.classNameFormat },
        { prop: 'productName', label: 'productName', width: '110', sort: 'custom' },
        { prop: 'productVersion', label: 'productVersion', width: '100', sort: 'custom' },
        { prop: 'fileDescription', label: 'fileDescription', width: '90', sort: 'custom' },
        { prop: 'companyName', label: 'companyName', width: '100', sort: 'custom' },
        { prop: 'originalFilename', label: 'originalFilename', width: '120', sort: 'custom' },
        { prop: 'softSign', label: 'softSign', width: '100', sort: 'custom' },
        { prop: 'fileMd5', label: 'fileMd5', width: '100', sort: 'custom' }
      ],
      colModel2: [
        { prop: 'processName', label: 'processName', width: '110', sort: true },
        { prop: 'productName', label: 'productName', width: '110', sort: true },
        { prop: 'productVersion', label: 'productVersion', width: '110', sort: true },
        { prop: 'fileDescription', label: 'fileDescription', width: '90', sort: true },
        { prop: 'companyName', label: 'companyName', width: '100', sort: true },
        { prop: 'originalFilename', label: 'originalFilename', width: '120', sort: true },
        { prop: 'softSign', label: 'softSign', width: '100', sort: true },
        { prop: 'fileMd5', label: 'fileMd5', width: '100', sort: true }
      ],
      filterParentKey: '',
      fileList: [],
      checkedRowKeys: [],
      deleteable: false,
      keyword: '',
      temp: { // 表单字段
        id: undefined,
        name: '',
        parentId: 0,
        parentName: ''
      },
      temp1: { // 表单字段
        id: undefined,
        processName: '',
        originalFilename: '',
        fileMd5: '',
        productName: '',
        productVersion: '',
        fileDescription: '',
        companyName: '',
        legalCopyright: '',
        quicklyMD5: '',
        internalName: '',
        remarks: '',
        classId: 0
      },
      query: {
        page: 1,
        classId: null,
        processName: '',
        productName: '',
        companyName: '',
        productVersion: '',
        softSign: '',
        noFilterType: 0
      },
      showTree: true,
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.appLibrary'), parentId: '', children: [] }],
      editTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.appLibrary'), parentId: '', children: [] }],
      selectTreeId: null,
      dialogFormVisible1: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('table.typeId'), 'update'),
        create: this.i18nConcatText(this.$t('table.typeId'), 'create'),
        moving: this.$t('button.move'),
        update1: this.i18nConcatText(this.$t('pages.program'), 'update'),
        create1: this.i18nConcatText(this.$t('pages.program'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.appGroup'), 'delete'),
        verify: this.$t('text.verifyIdentity')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required'), trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ],
        parentId: [{ required: true, message: this.$t('pages.required'), trigger: 'blur' }]
      },
      submitting: false,
      fileSubmitting: false,
      loadText: '',
      percentage: 0,
      fileLimitSize: 1024,
      checkedKeys2: [],
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      groupFlag: null,
      plugIsInWork: false,
      fileSelecting: false,
      saveUploadFileCount: 0
    }
  },
  computed: {
    typeTree: function() {
      return this.$refs['typeTree']
    },
    softTable: function() {
      return this.$refs['softTable']
    }
  },
  created() {
    this.loadTypeTree()
  },
  activated() {
    this.loadTypeTree()
    this.softTable.execRowDataApi()
  },
  methods: {
    createData,
    updateData,
    deleteData,
    updateSoft,
    getGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    tree() {
      return this.$refs['typeTree'].$refs['tree']
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'exe') {
        loopUploadFiles([file], this)
      } else {
        this.$message({
          message: this.$t('pages.processStgLib_Msg55'),
          type: 'error',
          duration: 2000
        })
      }

      return false // 屏蔽了action的默认上传
    },
    changeFile(files) {
      changeFiles(files, this)
    },
    appendFile(softs) {
      this.saveUploadFileCount += softs.length
      const checkedKeys = this.$refs.fileList.getSelectedKeys()
      softs.forEach(item => {
        const index = this.fileList.findIndex(item2 => {
          return item2.fileMd5 == item.fileMd5
        })
        if (index == -1) {
          this.fileList.push(item)
          checkedKeys.push(item.fileMd5)
        }
      })
      return this.$nextTick().then(() => {
        this.checkedRowKeys = [...checkedKeys]
      })
    },
    parentIdChange(data) {
      this.temp.parentId = data.replace('G', '')
      this.checkedKeys.splice(0, this.checkedKeys.length, data)
      this.temp.parentName = ''
      if (this.$refs.parentSelectTree && this.$refs.parentSelectTree.$refs) {
        this.temp.parentName = this.$refs.parentSelectTree.$refs.tree.getNode(data).label
      }
    },
    append(data) {
    },
    selectFirstNode: function() {
      this.tree().setCurrentKey('G0')
      this.selectTreeId = '0'
    },
    // 更新软件类别树的数据
    loadTypeTree: function() {
      return getTypeTree().then(respond => {
        const treeData = this.treeData[0].children
        const editTreeData = this.editTreeData[0].children
        const unGroupData = { id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped'), parentId: '0' }
        treeData.splice(0, treeData.length, unGroupData)
        if (respond.data) {
          treeData.push(...respond.data)
          editTreeData.splice(0, editTreeData.length, ...respond.data)
        }
        this.formTreeData = treeData
        this.$nextTick(() => {
          if (this.selectTreeId == null) {
            // 选中第一个节点
            this.selectFirstNode()
            // 然后刷新右边表格数据
            this.handleFilter()
          } else {
            this.tree().setCurrentKey('G' + this.selectTreeId)
            this.handleFilter()
          }
        })
      })
    },
    loadTypeTreeExceptRoot() {
      return this.treeData[0].children
    },
    // 树节点的点击方法
    treeNodeClick: function(data, node, element) {
      const selectNode = this.tree().getCurrentNode()
      this.selectTreeId = selectNode.dataId
      this.query.classId = this.selectTreeId
      const requestParam = Object.assign(this.query, { page: 1 })
      this.softTable.execRowDataApi(requestParam)
    },
    renderContent(h, { node, data, store }) {
      const unGroup = data.dataId != '-1'
      const iconShow = data.dataId != '0' && unGroup
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={unGroup} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleCreateNode(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleUpdateNode(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        return new Promise((resolve, reject) => {
          resolve({
            code: 20000,
            data: {
              total: 0,
              items: [] // 数组元素格式为{username:'zhangsan',id:1,...}
            }
          })
        })
      }
      this.query.classId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      return getSoftwarePage(newOption)
    },
    handleFilter() {
      this.query.page = 1
      this.softTable.execRowDataApi(this.query)
    },
    resetQuery() {
      this.query = {
        page: 1,
        classId: null,
        processName: '',
        productName: '',
        companyName: '',
        productVersion: '',
        softSign: '',
        noFilterType: 0
      }
    },
    handleDrag() {
    },
    handleClose() {
      this.checkedKeys.splice(0)
      this.$refs.appScanPlug.closePlug()
      this.dialogFormVisible1 = false
    },
    handleCreate() {
      this.fileList.splice(0)
      const selectNode = this.tree().getCurrentNode()
      if (selectNode != null) {
        this.temp.parentId = selectNode.dataId
      } else {
        this.temp.parentId = '0'
      }
      // 因为存在多层级分组，二三级分组变更时，没有修改到这边的数据，因此每次都初始化树节点
      this.formTreeData = JSON.parse(JSON.stringify(this.loadTypeTreeExceptRoot()))
      this.dialogStatus = 'create1'
      this.dialogFormVisible1 = true
      this.$nextTick(() => {
        if (selectNode != null) {
          this.checkedKeys = [selectNode.dataId]
        } else {
          this.checkedKeys = [0]
        }
        this.$refs['dataForm2'].clearValidate()
        this.$refs.appScanPlug.startPlug()
      })
    },
    handleCreateNode(nodeData) {
      this.$refs['editGroupDlg'].handleCreate(nodeData.dataId)
    },
    handleUpdateNode: function(nodeData) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: nodeData.dataId,
        name: nodeData.label,
        parentId: nodeData.parentId.replace('G', '')
      })
    },
    handleMoving() {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.softTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys2 = []
      const selectedData = this.softTable.getSelectedDatas()
      const total = this.softTable.getTotal()
      this.$refs['batchEditDlg'].show(selectedData, total, this.query)
    },
    handleRemove() {
      this.updateGroupForm = false
      const selectedData = this.softTable.getSelectedDatas()
      const total = this.softTable.getTotal()
      this.$refs['batchEditDlg'].show(selectedData, total, this.query)
    },
    handleImport() {
    },
    handleExport() {
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    dataToTreeNode(data) {
      return {
        id: 'G' + data.id,
        type: 'G',
        dataId: data.id.toString(),
        label: data.name,
        parentId: 'G' + data.parentId
      }
    },
    createNode(data) {
      this.typeTree.addNode(this.dataToTreeNode(data))
      this.loadTypeTree().then(res => {
        if (this.groupFlag !== null) {
          if (this.groupFlag === 1) {
            this.checkedKeys = []
            this.checkedKeys.push(data.id + '')
          }
          if (this.groupFlag === 2) {
            this.checkedKeys2 = []
            this.checkedKeys2.push('G' + data.id)
          }
          this.groupFlag = null
        }
      })
    },
    updateNode(data) {
      this.typeTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.typeTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.typeTree.removeNode([nodeData.id])
      }
    },
    moveGroupEnd() {
      this.softTable.execRowDataApi()
    },
    removeNode(nodeData) {
      countChildByGroupId(nodeData.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ classId: this.query.classId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(nodeData.dataId)
      })
    },
    createData1() {
      const datas = this.$refs.fileList.getSelectedDatas()
      if (datas.length == 0) {
        this.$message({
          message: this.$t('pages.appGroup_text7'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.submitting = true
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          datas.forEach(item => {
            item.classId = this.temp.parentId
          })
          addSoft(datas).then((res) => {
            this.submitting = false
            this.dialogFormVisible1 = false
            this.$refs.appScanPlug.closePlug()
            if (res.data == 'success') {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: this.$t('text.warning'),
                message: res.data,
                type: 'error',
                duration: 5000
              })
            }
            this.loadTypeTree()
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.delControlProcess'), this.$t('text.prompt')).then(() => {
        const idArr = []
        this.$refs.strategyTable.selectedData.forEach(function(item, index) {
          idArr.push(item.id)
        })
        if (idArr.length > 0) {
          const obj = {
            ids: idArr.join(',')
          }
          deleteData(obj).then(() => {
            this.loadTypeTree()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      }).catch(() => {})
    },
    classNameFormat(row, data) {
      if (!row.classId || row.classId == 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.treeData, row.classId)
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    groupNameValidator(rule, value, callback) {
      getGroupByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.typeTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.typeTree.setCurrentKey(nodeData.id)
        this.treeNodeClick()
      }
    },
    refreshTableData() {
      this.resetQuery()
      this.softTable.execRowDataApi(this.query)
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData instanceof Array) {
        nodeData = nodeData[0]
      }
      if (nodeData) {
        if (nodeData.dataId === '-1') {
          this.updateGroupId = '0'
          return
        }
        this.updateGroupId = nodeData.dataId
      }
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteAppData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.softTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteAppData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteSoft(params).then(respond => {
          this.softTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    //  新增分组
    handleTypeCreate(val) {
      this.groupFlag = val
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    showTermAppSelectDlg() {
      this.$refs['termAppSelectDlg'].show()
    },
    selectTermFile(data) {
      this.fileSelecting = true
      // 如果传输完毕
      if (data.allNum == data.hasLoadNum) {
        this.fileSelecting = false
      }
      if (data && data.softList && data.softList.length > 0) {
        this.appendFile(data.softList)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .toolbar>div {
    vertical-align: top;
  }
</style>
