<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="$t('table.appGroup')"
    :remark="$t('pages.appGroup_text13')"
    :visible.sync="dlgVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div class="toolbar">
      <Form label-position="right" :model="query" label-width="100px" :extra-width="{en: 0}" style="width: 350px; display: inline-block;">
        <FormItem :label="$t('pages.groupType')" style="margin: 0;">
          <tree-select ref="groupTree" :data="treeData" :checked-keys="[query.classId]" :width="200" @change="treeNodeClick" />
        </FormItem>
      </Form>
      <div style="float: right;">
        <el-input v-model="query.processName" v-trim clearable :placeholder="$t('pages.processName1')" style="width: 200px;"/>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="appInfoList"
      :height="420"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :default-sort="{ prop: 'processName' }"
      :page-sizes="[ 20, 50, 100, 500, 1000 ]"
    />
    <div slot="footer" class="dialog-footer">
      <link-button btn-type="primary" btn-style="float: left" :menu-code="'A58'" :link-url="'/system/baseData/appGroup'" :btn-text="$t('pages.maintainInfo', { info : $t('pages.appLibrary') })"/>
      <el-button type="primary" :loading="submitting" @click="handleSelect()">
        {{ $t('pages.addSelectedApp') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTypeTree } from '@/api/behaviorManage/application/appVersion'
import { getSoftwarePage } from '@/api/behaviorManage/application/appGroup'

export default {
  name: 'AppSelectDlg',
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    columnShow: {
      type: Array,
      default() { return ['processName', 'name', 'productName', 'productVersion', 'fileDescription', 'companyName', 'originalFilename', 'softSign'] }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName', width: '110', sort: 'custom' },
        { prop: 'name', label: 'className', width: '110', formatter: this.classNameFormat },
        { prop: 'productName', label: 'productName', width: '110', sort: 'custom' },
        { prop: 'productVersion', label: 'productVersion', width: '100', sort: 'custom' },
        { prop: 'fileDescription', label: 'fileDescription', width: '90' },
        { prop: 'companyName', label: 'companyName', width: '90' },
        { prop: 'originalFilename', label: 'originalFilename', width: '100' },
        { prop: 'softSign', label: 'softSign', width: '90' }
      ],
      query: { // 查询条件
        page: 1,
        processName: '',
        typeId: undefined,
        osType: this.osType
      },
      dlgVisible: false,
      submitting: false,
      selectTreeId: null,
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.appLibrary'), parentId: '', children: [] }]
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    }
  },
  created() {
    this.colModel = this.colModel.filter(item => { return this.columnShow.includes(item.prop) })
  },
  activated() {
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.selectTreeId = null
      this.query.typeId = undefined
      this.query.processName = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNode()
      })
    },
    treeNodeClick: function(data, node, element) {
      this.selectTreeId = node.dataId
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        this.selectTreeId = 0
      }
      option.classId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      return getSoftwarePage(newOption)
    },
    loadGroupTree: function() {
      getTypeTree().then(respond => {
        this.treeData[0].children = respond.data
        this.$nextTick(() => {

        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      const datas = this.softTable.getSelectedDatas() || []
      if (datas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.waterMark_Msg47'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$emit('select', datas)
      this.dlgVisible = false
    }
  }
}
</script>
