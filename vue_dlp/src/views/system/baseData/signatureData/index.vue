<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.signatureData_useMsg') }}
          </div>
          <i>{{ $t('pages.signatureData_text1') }}：{{ buildTime }}<i class="el-icon-info" /></i>
        </el-tooltip>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateMsg_processName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="tableList" :multi-select="false" :col-model="colModel" :row-data-api="rowDataApi"/>
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.import') + $t('pages.signatureFile')"
      :visible.sync="dialogFormVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px">
        <el-row>
          <el-col :span="21">
            <FormItem :label="$t('pages.signatureFile')" prop="fileName">
              <el-input v-model="temp.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="upload"
              action="1111"
              accept=".json"
              :on-change="fileChange"
              :show-file-list="false"
              :file-list="fileList"
              :disabled="fileSubmitting"
              :auto-upload="false"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting" style="margin: 0 0 0 1px;"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <div style="margin-top: 15px;">
          <label>{{ $t('pages.signatureData_text1') }}：</label>{{ buildTime }}
        </div>
        <div style="line-height: 30px;font-size: 14px;color: red;">
          <span>{{ $t('pages.signatureData_useMsg') }}</span>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPage, createData, getBuildTime } from '@/api/system/baseData/signatureData'

export default {
  name: 'SignatureData',
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName1', width: '150', fixed: true, sort: 'custom' },
        { prop: 'architecture', label: 'processBit', width: '150', sort: 'custom', formatter: this.processBitFormatter },
        { prop: 'beginVersion', label: 'versionNumber', width: '150', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      fileList: [],
      fileSubmitting: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        fileName: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      rules: {
        fileName: [{ required: true, trigger: 'blur', message: this.$t('pages.signatureData_text2') }]
      },
      buildTime: '无'
    }
  },
  computed: {
    gridTable() {
      return this.$refs['tableList']
    }
  },
  watch: {
  },
  beforeRouteEnter(to, from, next) {
    const isPageLoaded = window.getMenuAccessStatus('SignatureData')
    const isRefresh = from.path == '/redirect/config/signatureData'
    // 通过 搜索、已访问过、右键菜单刷新 
    if (to.params.search || isPageLoaded || isRefresh) {
      next()
    } else {
      next('/404')
    }
  },
  created() {
    this.setMenuAccessStatus('SignatureData')
    this.resetTemp()
    this.getBuildTime()
  },
  methods: {
    getBuildTime() {
      getBuildTime().then(res => {
        if (res.data) {
          this.buildTime = res.data.value
        }
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.fileList.splice(0)
    },
    handleDrag() {
    },
    handleImport() {
      this.dialogFormVisible = true
      this.resetTemp();
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.signatureData_text3'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      this.temp.fileName = file.name
      this.fileList.splice(0, 1, file)
    },
    saveData() {
      this.submitting = true
      this.fileSubmitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const fd = this.toFormData(this.temp)
          fd.append('file', this.fileList[0].raw)
          createData(fd).then(respond => {
            this.submitting = false
            this.fileSubmitting = false
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.importSuccess'),
              type: 'success',
              duration: 2000
            })
            this.getBuildTime()
            this.handleFilter()
          }).catch(res => {
            this.submitting = false
            this.fileSubmitting = false
          })
        } else {
          this.submitting = false
          this.fileSubmitting = false
        }
      })
    },
    processBitFormatter(row, data) {
      return { 1: 'x32', 2: 'x64' }[data]
    }
  }
}
</script>
