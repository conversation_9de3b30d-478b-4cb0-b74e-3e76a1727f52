<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="title"
      :visible.sync="appVisible"
      width="850px"
      @dragDialog="handleDrag"
    >
      <el-container>
        <el-aside width="210px">
          <tree-menu
            ref="appTypeTree"
            :data="appTypeTreeData"
            :height="400"
            :multiple="addGroupBtn"
            :default-expand-all="true"
            :render-content="renderContent"
            @node-click="appTypeTreeNodeCheckChange"
          />
        </el-aside>
        <el-main>
          <div>
            <div class="toolbar">
              <el-button v-if="showByOsType" type="primary" icon="el-icon-plus" size="mini" :disabled="!appAddBtnAble" @click="handleBatchCreate()">
                {{ $t('button.add') }}
              </el-button>
              <el-button icon="el-icon-delete" size="mini" :disabled="!appDeleteable" @click="deleteApp">
                {{ $t('button.delete') }}
              </el-button>
              <slot name="export" :selected-ids="getSelectedIds()" :query="appQuery"/>
              <el-tooltip v-if="installNotRepeatAddition && isMerge" :content="'当存在大量需合并的数据时耗时较长，请耐心等待'" effect="dark" placement="top">
                <el-button :loading="mergeLoading" size="mini" @click="mergeApp">
                  {{ '合并' }}
                </el-button>
              </el-tooltip>
              <div style="float: right;">
                <el-input v-model="appQuery.searchInfo" clearable :placeholder="$t('pages.processName1')" style="width: 130px;" @keyup.enter.native="handleAppFilter" />
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleAppFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <div style="height: 360px;">
              <grid-table
                ref="appInfoList"
                :height="360"
                pager-small
                :col-model="colModel"
                :row-data-api="loadAppList"
                :after-load="afterLoad"
                is-saved-selected
                saved-selected-prop="processName"
                @selectionChangeEnd="appSelectionChangeEnd"
              />
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <slot name="extra"></slot>
        <el-tooltip :content="$t('pages.appGroup_text14')" effect="dark" placement="top">
          <el-button v-if="addGroupBtn" type="primary" :loading="submitting" @click="handleCheckType()">
            {{ $t('pages.addType') }}
          </el-button>
        </el-tooltip>
        <el-button type="primary" :loading="submitting" @click="handleCheckApp()">
          {{ $t('pages.addApp') }}
        </el-button>
        <el-button @click="cancelCheckApp()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <app-group-dlg
      ref="appGroupDlg"
      :os-type="osType"
      :append-to-body="appendToBody"
      :create="createGroup"
      :update="updateGroup"
      :get-by-name="getGroupByName"
      @submitEnd="groupSubmitEnd"
    />
    <app-add-dlg
      ref="appAddDlg"
      :os-type="osType"
      :disable-product-md5="disableProductMd5"
      :support-md5="supportMd5"
      :append-to-body="appendToBody"
      :type-tree-data="appTypeRealTreeData"
      :create="create"
      :update="update"
      :create-group="createGroup"
      :update-group="updateGroup"
      :get-group-by-name="getGroupByName"
      :install-not-repeat-addition="installNotRepeatAddition"
      @submitEnd="addSubmitEnd"
    />
    <app-batch-add-dlg
      ref="batchUpload"
      :os-type="osType"
      :disable-product-md5="disableProductMd5"
      :support-md5="supportMd5"
      :append-to-body="appendToBody"
      :create="batchCreate"
      :update="batchUpdate"
      :create-group="createGroup"
      :update-group="updateGroup"
      :get-group-by-name="getGroupByName"
      :type-tree-data="appTypeRealTreeData"
      :support-select-terminal-soft="supportSelectTerminalSoft"
      :install-not-repeat-addition="installNotRepeatAddition"
      @submitEnd="batchSubmitEnd"
    />
    <!-- 删除弹窗 -->
    <delete-relation-stg-dlg
      ref="deleteRelationStg"
      :data="deleteDlgData"
      :col-model="deleteColModel"
      :delete="deleteData"
      :row-no-label="$t('table.keyId')"
      @deleteSuccessAfter="deleteSuccessAfter"
    />

    <re-process-all-merge-dlg
      v-if="installNotRepeatAddition"
      ref="reProcessAllMergeDlg"
      :type-tree-data="appTypeRealTreeData"
      @submit="allMergeSubmit"
      @closeFuc="closeFuc"
    />
  </div>
</template>

<script>
import { enableStgDelete } from '@/utils'
import { findNode } from '@/utils/tree'
import AppAddDlg from './appAddDlg'
import AppGroupDlg from './appGroupDlg'
import AppBatchAddDlg from './appBatchAddDlg'
import DeleteRelationStgDlg from '@/views/common/deleteRelationStgDlg'
import { isMergeInSoftLibrary } from '@/api/system/baseData/softPacket';
import ReProcessAllMergeDlg from './reProcessAllMergeDlg';

export default {
  name: 'AppImportTable',
  components: { ReProcessAllMergeDlg, AppAddDlg, AppGroupDlg, AppBatchAddDlg, DeleteRelationStgDlg },
  props: {
    showByOsType: { type: Boolean, default: true },
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    title: { type: String, default() { return this.$t('pages.importApp') } },
    typeTreeData: { type: Array, default() { return null } },
    groupRootName: { type: String, default() { return this.$t('pages.appLibrary') } },
    list: { // 查询APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    disableProductMd5: { // 是否禁用“产品名称”防伪冒
      type: Boolean,
      default: true
    },
    countByGroup: { // 统计APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    create: { // 添加APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    batchCreate: { // 批量添加APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    batchUpdate: { // 批量修改APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    update: { // 修改APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    delete: { // 删除APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    importFunc: { // 导入APP
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    createGroup: { // 添加APP分组
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    updateGroup: { // 修改APP分组
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    deleteGroup: { // 删除APP分组
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getGroupByName: { // 根据分组名称获取分组
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    supportMd5: { // 是否支持指纹防伪冒（某些功能终端还不支持，所以加个开关）
      type: Boolean,
      default: true
    },
    // 是否支持从终端目录中获取
    supportSelectTerminalSoft: {
      type: Boolean,
      default: false
    },
    addGroupBtn: { // 能否显示添加分组按钮
      type: Boolean,
      default: true
    },
    //  删除数据弹窗
    deleteColModel: {
      type: Array,
      default() {
        return []
      }
    },
    //  查询该数据id集所引用的策略数据
    getRelationStgByIds: {
      type: Function,
      default: null
    },
    //  软件安装/卸载限制 不支持添加重复进程信息
    installNotRepeatAddition: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName', width: '150', sort: 'custom' },
        { prop: 'typeId', label: 'typeId', width: '100', sort: 'custom', formatter: this.typeIdFormatter },
        { prop: 'checkMd5', hidden: () => !this.supportMd5, label: 'checkMd5', width: '120', sort: 'custom', formatter: this.md5LevelFormatter },
        /* { prop: 'productVersion', label: '版本号', width: '150' },
        { prop: 'originalFilename', label: '原始文件名', width: '150' },
        { prop: 'companyName', label: '公司名称', width: '150' },
        { prop: 'internalName', label: '内部名称', width: '150' },
        { prop: 'productName', label: 'productName', width: '150' },*/
        { prop: 'fileDescription', label: 'exeDesc', width: '150', formatter: this.fileDescriptionFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      appQuery: { // 查询条件
        page: 1,
        searchInfo: '',
        typeId: undefined,
        osType: this.osType
      },
      appDeleteable: false,
      appAddBtnAble: false,
      appVisible: false,
      submitting: false,
      outerShow: false,
      innerAdd: true,
      treeNodeType: '',
      appTypeTreeData: [{ id: 'G0', dataId: '0', label: this.groupRootName, parentId: '', children: [] }],
      appTypeRealTreeData: [],
      checkedRowKeys: [], // 策略选中的程序信息
      copyCheckedRowKeys: [], // 备份策略选中的程序信息
      removeCheckedRowKeys: [], // 记录从策略里移除的数据
      tempRowDatas: [], // 用于存储上一次操作中，选中的表格数据
      checkedPageRowKeys: [], // 当前页面，在策略里存在并且选中的数据
      deleteDlgData: [],  //   存放删除弹窗中列表的数据

      isMerge: false,  //  软件安装/卸载限制 程序库是否存在需要合并的数据
      mergeLoading: false
    }
  },
  computed: {

  },
  watch: {
    typeTreeData(val) {
      this.loadAppTypeTree(val)
    }
  },
  created() {
  },
  activated() {
    this.getIsMergeInSoftLibrary();
  },
  methods: {
    //  查询程序库是否存在需要合并的数据
    getIsMergeInSoftLibrary() {
      if (this.installNotRepeatAddition) {
        isMergeInSoftLibrary().then(res => {
          this.isMerge = res.data || false
        })
      }
    },
    show(typeIds, processIds, queryTypeId) {
      this.mergeLoading = false;
      this.getIsMergeInSoftLibrary();
      this.innerAdd = true
      this.appQuery.typeId = queryTypeId
      this.appQuery.searchInfo = ''
      this.appQuery.page = 1
      this.appVisible = true
      this.loadAppTypeTree()
      this.resetRowKeys()
      this.$refs.appTypeTree && this.$refs.appTypeTree.clearFilter()
      this.$refs.appTypeTree && this.$refs.appTypeTree.setCurrent(queryTypeId)
      this.$nextTick(() => {
        this.appGridTable().clearSaveSelection()
        this.appGridTable().execRowDataApi(this.appQuery)
        this.appTypeTree().clearSelectedNodes()
        if (this.addGroupBtn) {
          if (typeIds) {
            this.appTypeTree().setCheckedKeys(typeIds)
          }
          if (processIds) {
            this.setCheckedRowKeys(processIds)
            // 这里不需要勾选，表格勾选数据在afterLoad方法，即表格数据加载后执行
            // this.appGridTable().checkSelectedRows(processIds)
          }
        }
      })
    },
    resetRowKeys() {
      this.checkedRowKeys = []
      this.checkedPageRowKeys = []
      this.copyCheckedRowKeys = []
      this.removeCheckedRowKeys = []
      this.tempRowDatas = []
    },
    setCheckedRowKeys(data) {
      this.checkedRowKeys = [...data]
      this.copyCheckedRowKeys = [...data]
    },
    getRemoveCheckedRowKeys() {
      return this.removeCheckedRowKeys
    },
    afterLoad() {
      if (this.addGroupBtn) {
        const checkedRowKeysSet = new Set(this.checkedRowKeys)
        // 用于存储当前页需要进行勾选的行数据
        const checkPageDataIdsSet = new Set()
        // 获取表格当前页，在策略里存在的数据
        this.appGridTable().getDatas()
          .filter(data => checkedRowKeysSet.has(data.id))
          .forEach(data => {
            checkPageDataIdsSet.add(data.id)
          })
        // 记录表格当前页，在策略里存在的数据
        this.checkedPageRowKeys = Array.from(checkPageDataIdsSet)
        // 获取整个表格勾选的数据
        this.appGridTable().getSelectedDatas().forEach(item => {
          checkPageDataIdsSet.add(item.id)
        })
        this.$nextTick(() => {
          const checkPageDataIds = Array.from(checkPageDataIdsSet)
          if (checkPageDataIds.length > 0) {
            // 在表格当前页，将选中的数据勾选上
            this.appGridTable().checkSelectedRows(checkPageDataIds)
          }
        })
      }
    },
    appGridTable() {
      return this.$refs['appInfoList']
    },
    appTypeTree() {
      return this.$refs['appTypeTree']
    },
    loadAppList(option) {
      this.appQuery.osType = this.osType
      const optionTemp = Object.assign(this.appQuery, option)
      optionTemp.osType = this.osType
      return this.list(optionTemp)
    },
    selectionChangeEnd(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    getRemoveApp(rowDatas) {
      // 整个表格勾选的数据的 map
      const rowDataIdsMap = rowDatas.reduce((map, item) => { map[item.id] = item.id; return map; }, {})
      // 策略选中的程序信息的 map
      const copyCheckedRowKeysMap = this.copyCheckedRowKeys.reduce((map, item) => { map[item] = item; return map; }, {})
      // tempRowDats 存储了表格上一次勾选状态变更时所勾选的数据, unCheckIds 为 被取消勾选 且 存在于策略里 的数据的 id
      const unCheckIds = this.tempRowDatas.filter(item => !rowDataIdsMap[item.id] && !!copyCheckedRowKeysMap[item.id]).map(item => item.id)
      // 合并、去重
      this.removeCheckedRowKeys = Array.from(new Set(this.removeCheckedRowKeys.concat(unCheckIds)))
    },
    removeStrategyApp(rowDatas) {
      const checkedPageRowKeysSet = new Set(this.checkedPageRowKeys)
      // 用来存储当前页还有哪些策略程序被勾选
      const checkedIdsSet = new Set(
        rowDatas.filter(item => checkedPageRowKeysSet.has(item.id)).map(item => item.id)
      );

      // 收集取消勾选的数据
      const toRemove = new Set(
        this.checkedPageRowKeys.filter(item => !checkedIdsSet.has(item))
      );

      // 从checkedRowKeys数组里移除取消勾选的数据
      this.checkedRowKeys = this.checkedRowKeys.filter(data => !toRemove.has(data));
    },
    appSelectionChangeEnd(rowDatas) {
      if (this.addGroupBtn) {
        // 获取被移除的程序（需要获取原先策略里存在的程序，哪些被取消勾选了）
        this.getRemoveApp(rowDatas)
        this.tempRowDatas = [...rowDatas]
        // 策略里的程序被移除时，更新存储策略程序的数组
        this.removeStrategyApp(rowDatas)
      }
      this.appDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTree(data) {
      data = data || this.typeTreeData
      if (data) {
        const treeData = [{ id: 'G0', dataId: '0', label: this.groupRootName, parentId: '', children: data }]
        this.appTypeTreeData = treeData
        this.appTypeRealTreeData = data
        if (data.length > 0) {
          this.treeNodeType = data[0].type
        }
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleAppTypeCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleAppTypeUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    appTypeTreeNodeCheckChange(tabName, checkedNode) {
      checkedNode.expanded = true
      this.appAddBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      if (checkedNode) {
        this.appQuery.typeId = checkedNode.data.dataId
      } else {
        this.appQuery.typeId = undefined
      }
      this.appQuery.page = 1
      this.appGridTable().execRowDataApi(this.appQuery)
    },
    handleAppFilter() {
      this.appQuery.page = 1
      this.appGridTable().execRowDataApi(this.appQuery)
      this.$emit('search')
    },
    showCreate() {
      this.outerShow = true
      this.$refs.appAddDlg.show()
    },
    showBatchCreate(innerAdd, batchAdd) {
      this.outerShow = true
      this.innerAdd = !!innerAdd
      this.$refs.batchUpload.show(null, !!batchAdd)
    },
    handleCreate() {
      this.outerShow = false
      this.$refs.appAddDlg.show({ typeId: this.appQuery.typeId })
    },
    handleBatchCreate() {
      this.outerShow = false
      this.$refs.batchUpload.show({ typeId: this.appQuery.typeId })
    },
    handleUpdate(row) {
      this.outerShow = false
      this.$refs.appAddDlg.show(row)
    },
    handleAppTypeCreate(data) {
      this.outerShow = false
      this.$refs.appGroupDlg.show()
    },
    handleAppTypeUpdate(data) {
      this.outerShow = false
      this.$refs.appGroupDlg.show({
        id: data.dataId,
        name: data.label
      })
    },
    removeNode(data) {
      this.countByGroup(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.appGroup_text17'), type: 'warning', duration: 2000 })
          return
        }
        this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
          this.deleteGroup({ id: data.dataId }).then(respond => {
            this.appTypeTree().removeNode([data.id])
            const toDeleteItems = [{ id: data.dataId, itemType: 2 }]
            this.$emit('submitEnd', toDeleteItems, 0)
            this.$emit('updateGroup')
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id.toString(),
        label: data.name,
        parentId: 'G0'
      }
    },
    deleteData(data) {
      return (data && data.ids !== '') ? this.delete({ ids: data.ids }) : new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
    },
    deleteSuccessAfter(ids) {
      this.appGridTable().clearSelection()
      this.appGridTable().deleteRowData(ids)
      const toDeleteItems = ids.map(id => { return { id, itemType: 1 } })
      this.$emit('submitEnd', toDeleteItems, 0)
    },
    deleteApp() {
      const toDeleteIds = this.appGridTable().getSelectedIds()
      if (this.getRelationStgByIds) {
        this.getRelationStgByIds({ ids: toDeleteIds.join(',') }).then(res => {
          if (res.data && res.data.length > 0) {
            this.deleteDlgData = res.data
            this.$refs['deleteRelationStg'].show()
          } else {
            this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
              this.delete({ ids: toDeleteIds.join(',') }).then(respond => {
                this.appGridTable().deleteRowData(toDeleteIds)
                this.appGridTable().clearSelection()
                const toDeleteItems = toDeleteIds.map(id => { return { id, itemType: 1 } })
                this.$emit('submitEnd', toDeleteItems, 0)
                this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
              })
            }).catch(() => {})
          }
        })
        return;
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        this.delete({ ids: toDeleteIds.join(',') }).then(respond => {
          this.appGridTable().deleteRowData(toDeleteIds)
          this.appGridTable().clearSelection()
          const toDeleteItems = toDeleteIds.map(id => { return { id, itemType: 1 } })
          this.$emit('submitEnd', toDeleteItems, 0)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    cancelCheckApp() {
      this.appVisible = false
    },
    handleCheckApp() {
      const rows = this.appGridTable().getSelectedDatas()
      if (rows.length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text15'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const selectRows = this.appGridTable().getSelectedDatas()
      const selectIdSet = new Set(selectRows.map(item => item.id))
      this.removeCheckedRowKeys = this.removeCheckedRowKeys.filter(item =>
        !selectIdSet.has(item)
      )
      if (rows && rows.length > 0) {
        this.formatTypeName(rows)
        this.$emit('submitEnd', rows, 1)
      }
      this.appVisible = false
    },
    handleCheckType() {
      // 过滤掉根节点
      const nodes = this.appTypeTree().getCheckedNodes().filter(node => node.id != 'G0')
      if (nodes.length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text16'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (nodes && nodes.length > 0) {
        this.$emit('submitEnd', nodes, 2)
      }
      this.appVisible = false
    },
    addSubmitEnd(data, dlgStatus) {
      if (this.outerShow) {
        // 修改程序或者是导致并直接引用程序时
        this.formatTypeName(data)
        this.$emit('submitEnd', [data], 1)
      } else {
        if (dlgStatus == 'update') {
          // 当是修改应用程序时，因为防伪冒算法可能也会改变，所以要回调
          this.formatTypeName(data)
          this.$emit('submitEnd', [data], 3)
        }
        this.appGridTable().execRowDataApi()
      }
      const msg = dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess')
      this.$notify({ title: this.$t('text.success'), message: msg, type: 'success', duration: 2000 })
      this.getIsMergeInSoftLibrary()
      this.outerShow = false
    },
    batchSubmitEnd(data, dlgStatus) {
      if (this.innerAdd && data[0]) {
        data.forEach(v => { v.innerAdd = true })
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
      }
      if (this.outerShow) {
        this.formatTypeName(data)
        this.$emit('submitEnd', data, 1)
      } else {
        this.$emit('refreshList')
        this.appGridTable().execRowDataApi()
      }
      this.outerShow = false
      this.innerAdd = true
    },
    importSubmitEnd(data) {
      if (this.outerShow) {
        this.formatTypeName(data)
        this.$emit('submitEnd', data, 1)
      } else {
        this.appGridTable().execRowDataApi()
      }
      this.$notify({ title: this.$t('text.success'), message: this.$t('text.importSuccess'), type: 'success', duration: 2000 })
      this.outerShow = false
    },
    groupSubmitEnd(data, dlgStatus) {
      if (dlgStatus === 'create') {
        this.appTypeTree().addNode(this.dataToTreeNode(data))
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
      } else {
        this.appTypeTree().updateNode(this.dataToTreeNode(data))
        this.$emit('submitEnd', this.dataToTreeNode(data), 4)
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
      }
      this.$emit('updateGroup')
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    formatTypeName(rows) { // 为行数据格式化分类名称
      const rowList = Array.isArray(rows) ? rows : [rows]
      const typeIdNameMap = {}
      rowList.forEach(v => {
        if (v.typeId && (v.typeName === undefined || v.typeName === null || v.typeName === '')) {
          let typeName = typeIdNameMap[v.typeId]
          if (typeName === undefined || typeName === null) {
            const node = findNode(this.appTypeTreeData, v.typeId, 'dataId')
            typeName = node ? node.label : ''
            typeIdNameMap[v.typeId] = typeName
          }
          v.typeName = typeName
        }
      })
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      } else if (data === 0) {
        return this.$t('text.disable2')
      } else {
        return this.$t('text.enable')
      }
    },
    fileDescriptionFormatter(row, data) {
      if (row.remark) {
        return row.remark
      } else {
        return data
      }
    },
    typeIdFormatter(row, data) {
      let msg = ''
      if (this.appTypeRealTreeData) {
        this.appTypeRealTreeData.some(node => {
          if (node.dataId == data) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    getSelectedIds() {
      return this.appGridTable() && this.appGridTable().getSelectedIds() || []
    },
    /**
     * 合并数据
     */
    mergeApp() {
      this.mergeLoading = true
      this.$refs.reProcessAllMergeDlg.show()
    },
    /**
     * 全部合并-回调
     */
    allMergeSubmit() {
      this.mergeLoading = false;
      this.getIsMergeInSoftLibrary()
      this.handleAppFilter();
    },
    closeFuc() {
      this.mergeLoading = false
    }
  }
}
</script>
