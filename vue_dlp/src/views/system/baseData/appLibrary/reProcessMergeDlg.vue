<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      :width="status === 1 ? '1000px' : '750px'"
    >
      <div>
        <!-- 库中的进程信息先合并 -->
        <div v-if="status === 1">
          <div style="margin-bottom: 10px; color: #409EFF"><label>库中存在相同的进程信息，请进行合并</label></div>
          <el-timeline>
            <el-timeline-item
              v-for="(process, index) in processList"
              :key="index"
              :color="process.color"
              :type="process.type"
              size="large"
              :timestamp="process.processName"
              placement="top"
            >
              <el-card>
                <div style="margin-bottom: 5px">
                  {{ process.type !== 'info' ? '合并结果：' : '待合并信息：' }}
                </div>
                <grid-table
                  :ref="'processList' + process.processName"
                  :max-height="200"
                  auto-height
                  :multi-select="false"
                  :show-pager="false"
                  default-expand-all
                  row-key="dataId"
                  :col-model="mergeColModel"
                  :row-datas="process.children"
                  style="margin-bottom: 5px;"
                />
                <el-button type="primary" size="medium" style="float:right;" :disabled="process.type !== 'info'" @click="merge(process.processName)">合并</el-button>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <!-- 待添加进程合并入库 -->
      <div v-if="status === 2">
        <div style="margin-bottom: 5px;">
          <label style="color: #409eff;">以下进程信息与库中的进程存在指纹冲突，请进行选择。</label>
        </div>
        <el-container>
          <el-aside width="220px">
            <tree-menu
              ref="groupTree"
              row-key="dataId"
              :data="allProcessNameTreeData"
              :height="380"
              :multiple="true"
              :expand-on-click-node="false"
              @node-click="processTreeNodeClick"
            />
          </el-aside>
          <el-main>
            <label>待添加进程信息：</label>
            <grid-table
              ref="processList"
              :height="100"
              :multi-select="false"
              :show-pager="false"
              default-expand-all
              row-key="id"
              :col-model="toAddColModel"
              :row-datas="toAddProcessList"
              style="margin-bottom: 10px;"
            />
            <label>库中已存在的进程信息：</label>
            <grid-table
              ref="processList"
              :height="150"
              :multi-select="false"
              :show-pager="false"
              default-expand-all
              row-key="id"
              :col-model="colModel"
              :row-datas="exitsProcessList"
              style="margin-bottom: 15px;"
            />
            <el-row>
              <el-col :span="24">
                <label>请选择添加的方式：</label>
              </el-col>
              <el-col :span="24">
                <el-radio-group v-model="checkBoxMerge" @change="checkBoxMergeChange">
                  <el-radio :key="1" :value="1" :label="1">库中进程信息</el-radio>
                  <el-radio :key="2" :value="2" :label="2">进程合并入库，以待添加进程防伪冒级别为准</el-radio>
                </el-radio-group>
              </el-col>
            </el-row>
          </el-main>
        </el-container>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="status === 1" type="primary" :loading="submitting" :disabled="allIsMerge" @click="merge(null)">
          {{ '一键合并' }}
        </el-button>
        <el-button v-if="status === 1 && isNext" :disabled="!allIsMerge" type="primary" @click="next">
          {{ '下一步' }}
        </el-button>
        <el-button v-if="status === 2" type="primary" @click="batchSelectMode">
          {{ '批量选择添加方式' }}
        </el-button>
        <el-button v-if="status === 2" type="primary" @click="confirm">
          {{ '确认' }}
        </el-button>
        <el-button @click="cancel">
          {{ status === 1 ? $t('button.close') : $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <diff-check-md5-process-dlg ref="diffCheckMd5ProcessDlg" @submit="diffCheckSubmit"/>

    <!--选择添加方式弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="false"
      :modal="false"
      :title="'选择添加方式'"
      :visible.sync="dlgVisible"
      width="500px"
    >
      <el-row>
        <el-col :span="24">
          <el-radio-group v-model="checkBoxMergeDlg" @change="checkBoxMergeChange">
            <el-radio :key="1" :value="1" :label="1">库中进程信息</el-radio>
            <el-radio :key="2" :value="2" :label="2">进程合并入库，以待添加进程防伪冒级别为准</el-radio>
          </el-radio-group>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dlgConfirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dlgCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createProcessVersion } from '@/utils/fingerprint'
import DiffCheckMd5ProcessDlg from './diffCheckMd5ProcessDlg';
import {
  batchUpdateSoftPacket,
  listSoftProcessByProcessName,
  mergeProcess
} from '@/api/system/baseData/softPacket';

export default {
  name: 'ReProcessMergeDlg',
  components: { DiffCheckMd5ProcessDlg },
  data() {
    return {
      status: 1,  //  页面状态，1-合并进程，2-选中进程
      visible: false,
      submitting: false,
      title: '',
      operatorStatus: null,  //  操作方式：修改（update）
      mergeColModel: [
        { prop: 'processName', label: 'processName', width: '120', formatter: this.nameFormatter },
        { prop: 'productVersion', label: 'productVersion', width: '80' },
        { prop: 'processVersion', label: 'processVersion', width: '80' },
        { prop: 'checkMd5', label: 'counterfeitingLevel', width: '100', formatter: this.getCheckMd5Label }
      ],
      colModel: [
        { prop: 'processName', label: 'processName', width: '120', formatter: this.nameFormatter },
        { prop: 'processVersion', label: 'productVersion', width: '80' },
        { prop: 'checkMd5', label: 'counterfeitingLevel', width: '100', formatter: this.getCheckMd5Label }
      ],
      toAddColModel: [
        { prop: 'processName', label: 'processName', width: '120', formatter: this.nameFormatter },
        { prop: 'componentVersion', label: 'componentVersion', width: '80' },
        { prop: 'checkMd5', label: 'counterfeitingLevel', width: '100', formatter: this.checkMd5Formatter }
      ],
      toBeAddedProcessList: [], //  待添加的进程数据（最开始）
      checkMd5: null, //  待添加的进程配置的防伪冒级别
      typeId: null,   //  程序类别
      processList: [],  //  未合并的进程信息
      mergeProcessMap: new Map(),  //  已一键合并后的数据, key:进程名称，value：不同版本的进程信息
      allIsMerge: false,  //  所有进程是否都已合并
      isNext: false,  //  是否有下一步

      notExitsProcessDatas: [],  //  待添加进程不在库中的进程信息
      exitsProcessDatas: [],  // 待添加进程已存在与库中的进程信息
      mergeNotExitsVersionProcessDatas: [], //  待合并的进程（库中存在防伪冒程序数据，但不存在的相同版本号的指纹）

      //  以下为status = 2页面涉及到的字段
      processNameTreeData: [],  //  存储 待添加进程设置了防伪冒且在库中存在冲突的进程信息
      allProcessNameTreeData: [{ id: 'G0', dataId: '0', label: '全部', parentId: '', children: [] }],
      mergeExitsProcessMap: new Map(),  //  待合并的进程信息对应的库中的进程
      toAddProcessList: [], //  待添加进程信息列表
      exitsProcessList: [],  //  已在库中的进程信息列表
      checkBoxMerge: 1, //  当前待添加进程的选中的方式
      currentProcessKey: null,  //  当前展示的进程信息processName
      checkBoxMergeMap: {},  //  各个待添加进程的选中方式 key: processName, value：取值[1,2]

      //  以下为弹窗界面涉及到的字段
      dlgVisible: false,
      checkBoxMergeDlg: 1
    }
  },
  computed: {
    diffCheckMd5ProcessDlg() {
      return this.$refs.diffCheckMd5ProcessDlg;
    }
  },
  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.isNext = false;
      this.status = 1;
      this.processList = [];
      this.mergeProcessMap = new Map()
      this.mergeExitsProcessMap = new Map();
      this.allIsMerge = false;
      this.submitting = false;
      this.toAddProcessList = []
      this.exitsProcessList = []
      this.checkBoxMergeMap = {}
      this.processNameTreeData = []
      this.notExitsProcessDatas = []
      this.exitsProcessDatas = []
      this.mergeNotExitsVersionProcessDatas = []
      this.operatorStatus = null
      this.currentProcessKey = null
      this.checkBoxMerge = 1
    },
    /**
     * 库中进程信息合并-再进行待添加进程合并
     * @param toBeAddedProcessList    待添加的进程特征数据（指纹数据）
     * @param checkMd5                待添加的进程的防伪冒级别  0-未设置防伪冒 1-程序指纹  3-进程特征 4-产品名称
     * @param typeId                  程序类别
     * @param processes               待添加进程名称在库中需要合并的进程信息（程序数据）
     * @param operatorStatus          操作方式：修改（ update ）
     *
     * 说明：
     * - 程序数据：程序数据的checkMd5值代表 是否开启防伪冒，值仅有 0 和 1
     * - 指纹数据：指纹数据的checkMd5值代表 防伪冒级别，值有 1-程序指纹  3-进程特征 4-产品名称
     */
    show(toBeAddedProcessList, checkMd5, typeId, processes, operatorStatus) {
      this.initData()
      this.operatorStatus = operatorStatus;
      this.title = this.$t('pages.duplicateProcessMerging')
      this.checkMd5 = checkMd5
      this.typeId = typeId;
      this.toBeAddedProcessList = this.formatToBeAddedProcessList(toBeAddedProcessList || []);
      this.processList = this.formatProcess(processes);
      //  按进程名称进行分类
      this.visible = true;
    },
    /**
     *  待添加进程合并
     * @param toBeAddedProcessList    待添加的进程特征数据（指纹数据）
     * @param checkMd5                待添加的进程的防伪冒级别 0-未设置防伪冒 1-程序指纹  3-进程特征 4-产品名称
     * @param typeId                  程序类别
     * @param exitsProcesses          待添加的进程已在库中的进程信息（例如：待添加进程设置为开启防伪冒，则返回开启防伪冒的该进程信息）（程序数据）
     */
    async showMerge(toBeAddedProcessList, checkMd5, typeId, exitsProcesses) {
      this.initData()
      this.title = this.$t('pages.processChoose')
      this.status = 2;
      this.checkMd5 = checkMd5
      this.typeId = typeId;
      this.toBeAddedProcessList = this.formatToBeAddedProcessList(toBeAddedProcessList || []);
      this.analyzeProcess(this.toBeAddedProcessList, exitsProcesses)
      if (!this.processNameTreeData.length && !this.mergeNotExitsVersionProcessDatas.length) {
        this.$emit('submit', this.notExitsProcessDatas, exitsProcesses);
      } else if (this.mergeNotExitsVersionProcessDatas.length && !this.processNameTreeData.length) {
        await this.confirm()
      } else {
        this.visible = true;
        this.processTreeNodeClick(this.processNameTreeData[0])
      }
    },
    /**
     * 解析进程信息
     * @param toBeAddedProcessList    待添加进程指纹信息（指纹数据）
     * @param exitsProcesses          待添加的进程已在库中的进程信息（例如：待添加进程设置为开启防伪冒，则返回开启防伪冒的该进程信息）（程序数据）
     */
    analyzeProcess(toBeAddedProcessList, exitsProcesses) {
      const exitsProcessList = [] //  保存已存在库中的进程信息
      const mergeProcessList = [] //  待合并的进程信息(进程防伪冒存在冲突）
      const mergeNotExitsVersionProcessList = [] //  待合并的进程（库中存在防伪冒程序数据，但不存在的相同版本号的指纹）
      this.mergeExitsProcessMap = new Map()  //  待合并的进程信息对应的库中的进程
      const notExitsProcessList = []  //  不在库中的进程信息
      toBeAddedProcessList.forEach(process => {
        const exitsProcess = exitsProcesses.find(t => t.processName === process.processName);
        if (exitsProcess) {
          if (this.checkMd5) {
            //  若已在库中时添加
            if (exitsProcess.relaFingerPrint.filter(t => t.checkMd5 === this.checkMd5 && process.fileMd5 === t.fileMd5).length) {
              if (!exitsProcessList.filter(t => exitsProcess.processName === t.processName).length) {
                exitsProcessList.push(exitsProcess)
              }
            } else {    //  库中不存在时（即库中不存在版本号相同进程防伪冒相同的数据时）
              //  若库中不存在此版本的进程时
              if (!exitsProcess.relaFingerPrint.filter(t => process.fileMd5 === t.fileMd5).length) {
                mergeNotExitsVersionProcessList.push(process)
              } else {
                mergeProcessList.push(process);
              }
              this.mergeExitsProcessMap.set(process.processName, exitsProcess);
            }
          } else if (!exitsProcessList.filter(t => exitsProcess.processName === t.processName).length) {
            exitsProcessList.push(exitsProcess);
          }
        } else {
          notExitsProcessList.push(process);
        }
      })
      this.notExitsProcessDatas = notExitsProcessList;
      this.exitsProcessDatas = exitsProcessList;
      this.mergeNotExitsVersionProcessDatas = mergeNotExitsVersionProcessList;
      this.buildMergeToStoreData(mergeProcessList);
    },
    /**
     * 格式化成指纹数据
     */
    formatToBeAddedProcessList(toBeAddedProcessList) {
      const list = []
      toBeAddedProcessList && toBeAddedProcessList.forEach(data => {
        list.push(this.toFingerprintData(data));
      })
      return list;
    },
    /**
     * 格式化数据
     * @param processes
     * @returns {*[]}
     */
    formatProcess(processes) {
      const list = []
      let i = 1;
      for (const key in processes) {
        list.push({
          dataId: 'G' + i++,
          processName: key,
          type: 'info',
          color: 'hsv',
          children: processes[key]
        });
        processes[key].forEach(data => {
          data.dataId = 'T' + data.id
          if (data.checkMd5) {
            data.itemType = 2;
            data.children = data.relaFingerPrint;
            data.children && data.children.forEach(t => {
              t.dataId = t.id
            })
          }
        })
      }
      return list;
    },
    /**
     * 下一步，待添加进程与库中进程进行合并
     */
    next() {
      this.status = 2;
      this.title = this.$t('pages.processChoose')
      this.processTreeNodeClick(this.processNameTreeData[0])
    },
    /**
     * 解析进程数据，判断是否需要跳转到：待添加进程合并到库中的界面
     * @return true: 有下一步； false: 没有下一步
     */
    async analyzeProcessData() {
      //  是否有下一步
      let isNext = false;
      if (this.status === 1) {
        const names = this.toBeAddedProcessList.map(data => data.processName);
        const res = await listSoftProcessByProcessName({
          processNames: names,
          checkMd5: this.checkMd5 ? 1 : 0,
          typeId: this.typeId
        });
        const exitsProcessMap = res.data.exitsProcessMap
        const exitsDatas = [] //  已存在的进程信息
        if (exitsProcessMap) {
          for (const key in exitsProcessMap) {
            if (exitsProcessMap.hasOwnProperty(key)) {
              exitsDatas.push(exitsProcessMap[key])
            }
          }
        }
        this.analyzeProcess([...this.toBeAddedProcessList], exitsDatas)
        if (!this.processNameTreeData.length && !this.mergeNotExitsVersionProcessDatas.length) {
          this.$emit('submit', this.notExitsProcessDatas, this.exitsProcessDatas);
          this.visible = false
        } else if (this.mergeNotExitsVersionProcessDatas.length && !this.processNameTreeData.length) {
          this.status = 2;
          await this.confirm()
        } else if (this.checkMd5) { //  必须开启防伪冒才支持选择
          isNext = true
        }
      }
      return isNext;
    },
    /**
     * 构建待添加进程合并到库中的进程数据
     * - 仅显示与库中存在指纹冲突的进程信息（即：版本号相同，防伪冒级别不同）
     * - 若待添加进程的版本号在库中不存在时，可直接添加
     * @param mergeProcessList  (指纹数据)
     */
    buildMergeToStoreData(mergeProcessList) {
      this.checkBoxMergeMap = {}
      let i = 1;
      const list = []
      mergeProcessList.forEach(data => {
        data.checkMd5 = this.checkMd5
        //  设置初始导入方式
        this.checkBoxMergeMap[data.processName] = 1;
        //  保存每个待添加进程的每个防伪码信息
        let process = list.find(t => t.label === data.processName) || null
        if (!process) {
          process = { id: i, dataId: i, label: data.processName, processName: data.processName, addDatas: [] }
          list.push(process);
        }
        process.addDatas.push(data);
        i++;
      })
      this.processNameTreeData = list;
      this.allProcessNameTreeData[0].children = this.processNameTreeData
    },
    /**
     * 确认
     */
    async confirm() {
      //  统计各项进程合并入库后的结果-> 统计出 已在库中的信息，需要合并的信息
      const exitsProcessList = []
      const mergeProcessList = []
      for (const key in this.checkBoxMergeMap) {
        if (this.checkBoxMergeMap.hasOwnProperty(key)) {
          const value = this.checkBoxMergeMap[key]
          const exitsProcess = this.mergeExitsProcessMap.get(key);
          //  选择已在库中的进程信息
          if (value === 1) {
            exitsProcessList.push(exitsProcess);
          } else if (value === 2) {
            //  将库中的进程指纹替换成待添加进程，或额外添加进程指纹
            const addProcess = this.processNameTreeData.find(t => t.processName === key);
            if (addProcess) {
              addProcess.addDatas && addProcess.addDatas.forEach(addData => {
                exitsProcess.relaFingerPrint = exitsProcess.relaFingerPrint.map(t => {
                  if (t.fileMd5 === addData.fileMd5) {
                    Object.assign(t, addData, { id: t.id, checkMd5: this.checkMd5 });
                  }
                  return t;
                })
              })
            }
            mergeProcessList.push(exitsProcess);
          }
        }
      }
      //  （库中存在防伪冒程序数据，但不存在的相同版本号的）指纹数据 合并到库中
      this.mergeNotExitsVersionProcessDatas.length && this.mergeNotExitsVersionProcessDatas.forEach(data => {
        const mergeProcess = mergeProcessList.find(t => t.processName === data.processName)
        data.checkMd5 = this.checkMd5
        if (mergeProcess) {
          mergeProcess.relaFingerPrint.push(data);
        } else {
          const exitsProcess = this.mergeExitsProcessMap.get(data.processName);
          exitsProcess.relaFingerPrint.push(data);
          mergeProcessList.push(exitsProcess);
        }
      })

      //  将数据进行合并
      const res = await batchUpdateSoftPacket(mergeProcessList)
      res.data && res.data.forEach(data => exitsProcessList.push(data));
      exitsProcessList && exitsProcessList.forEach(t => {
        if (!this.exitsProcessDatas.find(n => n.id === t.id)) {
          this.exitsProcessDatas.push(t);
        }
      })
      this.$emit('submit', this.notExitsProcessDatas, this.exitsProcessDatas)
      this.visible = false;
    },
    /**
     * 取消
     */
    cancel() {
      if (this.operatorStatus === 'update' && (this.allIsMerge || this.status === 2)) {
        this.$emit('submit', null, null, 'cancel')
      }
      this.visible = false;
    },
    nameFormatter(row, data) {
      if (row.itemType == 2) {
        const label = row.processName
        return `<span class='el-icon el-icon-folder'></span> ${label}`
      } else {
        return row.processName
      }
    },
    /**
     * 获取防伪冒级别描述
     */
    getCheckMd5Label(row, type) {
      let result = '';
      if (type !== undefined && type != null && (!row.itemType || row.itemType !== 2)) {
        switch (type + '') {
          case '1': result = this.$t('table.programFinger'); break;
          case '3': result = this.$t('table.processCharacter'); break;
          case '4': result = this.$t('table.productName'); break;
          default: result = this.$t('pages.notSettingAntiCounterfeitTower')
        }
      }
      return result;
    },
    /**
     * 待添加进程的防伪冒级别格式化
     */
    checkMd5Formatter(row, data) {
      let result = '';
      const type = this.checkMd5
      switch (type + '') {
        case '1': result = this.$t('table.programFinger'); break;
        case '3': result = this.$t('table.processCharacter'); break;
        case '4': result = this.$t('table.productName'); break;
      }
      return result;
    },
    /**
     * 返回进程库中合并时所需要的进程信息
     * @param processName 进程名称
     * @param processList 所有待合并的进程信息
     * @returns {{diffCheckMd5es: *[], diffCheckMd5List: *[]}}
     *  - diffCheckMd5es: 存储存在冲突的进程特征信息（存在多条不同防伪冒级别的进程信息）
     *  - diffCheckMd5List：保存不同版本的进程信息
     *  - processArr: 最多保留2条进程信息，分表代表防伪冒和非防伪冒进程信息，用于构建进程信息
     */
    getDiffCheckData(processName, processList) {
      //  查找待合并进程
      const processMerge = processList.find(t => t.processName === processName);
      //  归类，相同版本的进程信息归一类,  防伪冒级别（checkMd5) 1-程序指纹  3-进程特征 4-产品名称
      //  key: fileMd5，value: 不同防伪冒级别的进程信息
      const map = new Map();
      //  保存不同版本的进程信息
      const diffCheckMd5List = []
      const processArr = []
      processMerge.children.forEach(processM => {
        //  有配置进程防伪冒
        if (processM.checkMd5) {
          processM.children && processM.children.forEach(process => {
            if (!map.get(process.fileMd5)) {
              map.set(process.fileMd5, [])
            }
            //  相同防伪冒级别的不重复添加
            if (!map.get(process.fileMd5).filter(t => t.checkMd5 === process.checkMd5).length) {
              diffCheckMd5List.push(process)
              map.get(process.fileMd5).push(process);
            }
          })
          if (!processArr.filter(t => t.checkMd5).length) {
            const t = JSON.parse(JSON.stringify(processM))
            t.relaFingerPrint = []
            processArr.push(t)
          }
        } else {
          //  未配置进程防伪冒
          if (!diffCheckMd5List.filter(t => !t.checkMd5).length) {
            diffCheckMd5List.push(processM)
          }
          if (!processArr.filter(t => !t.checkMd5).length) {
            processArr.push(JSON.parse(JSON.stringify(processM)))
          }
        }
      });
      //  获取存在多条不同防伪冒级别的进程信息
      const diffCheckMd5es = [];
      for (const [, value] of map) {
        if (value.length > 1) {
          diffCheckMd5es.push({ processName, processVersion: value[0].componentVersion, fileMd5: value[0].fileMd5, children: value });
        }
      }
      return { diffCheckMd5es, diffCheckMd5List, processArr }
    },
    /**
     * 合并进程
     * @param processName
     */
    merge(processName) {
      //  一键合并
      if (processName == null) {
        const processNameList = this.processList.map(t => t.processName);
        const map = new Map();
        processNameList.forEach(name => {
          const { diffCheckMd5es, diffCheckMd5List, processArr } = this.getDiffCheckData(name, this.processList);
          if (diffCheckMd5es.length) {
            map.set(name, { diffCheckMd5es, diffCheckMd5List, processArr });
          } else {
            this.setProcessMergeResult(name, diffCheckMd5List, processArr);
          }
        })
        if (map.size) {
          this.diffCheckMd5ProcessDlg.show(map);
        }
      } else {
        const { diffCheckMd5es, diffCheckMd5List, processArr } = this.getDiffCheckData(processName, this.processList);
        //  若存在多条不同防伪冒级别
        if (diffCheckMd5es.length) {
          const map = new Map();
          map.set(processName, { diffCheckMd5es, diffCheckMd5List, processArr })
          this.diffCheckMd5ProcessDlg.show(map)
          return;
        }
        //  该进程的合并状态设置为完成
        this.setProcessMergeResult(processName, diffCheckMd5List, processArr);
      }
    },
    /**
     * 设置进程合并结果
     * @param processName         进程名称
     * @param diffCheckMd5List    存储存在冲突的进程特征信息（存在多条不同防伪冒级别的进程信息）
     * @param processArr          最多保留2条进程信息，分表代表防伪冒和非防伪冒进程信息，用于构建进程信息
     */
    setProcessMergeResult(processName, diffCheckMd5List, processArr) {
      processArr = [...processArr]
      const processMerge = this.processList.find(t => t.processName === processName);
      //  重新构建结构，未展开时，最多可展示2条数据，一个代表非防伪冒、一个代表防伪冒
      //  合并数据
      const map = {};
      processArr.forEach(data => {
        if (data.checkMd5) {
          data.relaFingerPrint = diffCheckMd5List.filter(t => !!t.checkMd5 === !!data.checkMd5) || []
        }
      })
      map[processName] = processArr;
      mergeProcess({ processNames: [processName], typeId: this.typeId, checkMd5: this.checkMd5 ? 1 : 0, mergeProcessMap: map }).then(async res => {
        this.mergeProcessMap.set(processName, diffCheckMd5List);
        if (res.data.mergeClash) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.processMergeMsg1'),
            type: 'warning',
            duration: 2000
          })
          const data = res.data.exitsProcessMap[processName]
          processMerge.children = data ? this.buildProcessMergeResult(processName, [data]) : []
        } else {
          processMerge.children = this.buildProcessMergeResult(processName, res.data.mergeProcessMap[processName])
        }
        processMerge.type = 'success'
        this.allIsMerge = this.mergeProcessMap.size === this.processList.length;
        //  若全部合并完成，构建待添加数据
        if (this.allIsMerge) {
          this.isNext = await this.analyzeProcessData()
        }
      });
    },
    /**
     * 构建进程合并后的结果
     * @param processName
     * @param diffCheckMd5List
     * @returns {*}
     */
    buildProcessMergeResult(processName, diffCheckMd5List) {
      diffCheckMd5List.forEach(t => {
        t.dataId = 'T' + t.id
        if (t.checkMd5) {
          t.children = t.relaFingerPrint
          t.children && t.children.forEach(d => {
            d.dataId = d.id
          })
        }
      })
      return diffCheckMd5List;
    },
    /**
     * 不同防伪冒级别选择的回调
     * @param map  返回该进程待添加的防伪冒进程信息
     */
    diffCheckSubmit(map) {
      //  该进程的合并状态设置为完成
      map.forEach((data, processName) => {
        const { diffCheckMd5List, processArr } = data;
        this.setProcessMergeResult(processName, diffCheckMd5List, processArr);
      })
    },
    /**
     * 选中进程名称时，展示待添加进程的信息和库中的进程信息
     * @param data
     * @param node
     * @param element
     */
    processTreeNodeClick(data, node, element) {
      if (data.dataId !== '0') {
        this.toAddProcessList = data.addDatas || []
        this.exitsProcessList = []
        this.currentProcessKey = data.label
        this.checkBoxMerge = this.checkBoxMergeMap[this.currentProcessKey];
        const exitsProcess = this.mergeExitsProcessMap.get(data.label);
        if (exitsProcess) {
          exitsProcess.itemType = 2;
          exitsProcess.children = exitsProcess.relaFingerPrint
          this.exitsProcessList.push(exitsProcess);
        }
      }
    },
    /**
     * 选中添加方式
     * @param data
     */
    checkBoxMergeChange(data) {
      this.checkBoxMergeMap[this.currentProcessKey] = data;
    },
    /**
     * 把程序数据装成指纹数据
     * @param appData
     * @returns {{propertyMd5: (string|*), quicklyMd5, productMd5: *, processName, propertyMark: (string|*), remark: string, id, componentVersion, fileMd5, processVersion: (*), checkMd5}}
     */
    toFingerprintData(appData) {
      return {
        id: appData.id,
        processName: appData.processName,
        processVersion: createProcessVersion(appData.processName, appData.productVersion),
        componentVersion: appData.productVersion ? appData.productVersion : appData.componentVersion,
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: appData.checkMd5,
        propertyMark: appData.propertyMark,
        productMd5: appData.productMd5,
        remark: ''
      };
    },
    /**
     * 批量选择添加方式
     */
    batchSelectMode() {
      const list = this.$refs.groupTree.getCheckedNodes() || []
      if (!list.length) {
        this.$notify({
          title: this.$t('text.prompt'),
          message: '未选择待添加进程',
          type: 'warning',
          duration: 2000
        })
        return
      }
      this.checkBoxMergeDlg = 1
      this.dlgVisible = true;
    },
    /**
     * 批量选择添加方式-确认
     */
    dlgConfirm() {
      const list = this.$refs.groupTree.getCheckedNodes();
      list.length && list.forEach(t => {
        //  过滤掉“全部”节点
        if (t.dataId !== '0') {
          this.checkBoxMergeMap[t.processName] = this.checkBoxMergeDlg
        }
      })
      this.processTreeNodeClick(this.processNameTreeData.find(t => t.processName === this.currentProcessKey))
      this.dlgVisible = false;
    },
    dlgCancel() {
      this.dlgVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.el-timeline {
  padding: 0 0 0 1px;
  >>>.el-timeline-item__tail {
    border-left: 2px solid #909399;
  }
  >>>.el-timeline-item__timestamp {
    color: black;
  }
}

</style>
