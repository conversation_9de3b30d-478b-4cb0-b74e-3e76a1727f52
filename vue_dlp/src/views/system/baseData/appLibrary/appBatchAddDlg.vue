<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="appendToBody"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addApp')"
      :visible.sync="dialogFormVisible1"
      width="800px"
      @dragDialog="handleDrag"
      @close="handleClose"
    >
      <Form ref="dataForm2" :rules="rules" :model="tempF" label-position="right" label-width="140px">
        <div class="toolbar">
          <div style="display: inline-block; max-width: 210px;">
            <upload-dir v-if="!isIE() && osType==1 && !plugIsInWork" ref="uploadDir" :popover-height="235" :loading="fileSubmitting" style="display: inline-block;" @changeFile="changeFile" />
            <el-upload
              v-show="!plugIsInWork"
              ref="upload"
              name="uploadFile"
              action="aaaaaa"
              :accept="osType==1 ?'.exe':null"
              :disabled="fileSubmitting"
              :show-file-list="false"
              :before-upload="beforeUpload"
              style="display: inline-block;"
            >
              <el-button type="primary" :loading="fileSubmitting" size="mini" style="margin-top: 0; margin-bottom: 5px;">{{ $t('pages.uploadFile') }}</el-button>
            </el-upload>
            <!-- 打开窗口时，在进行心跳检测 -->
            <app-scan-plug ref="appScanPlug" :heartbeat-enable="dialogFormVisible1" @plugIsInWork="(inWork) => plugIsInWork = inWork" @uploadEnd="appendFile"/>
          </div>
          <el-button v-if="osType==1" type="primary" size="mini" @click="showAppSelectDlg">
            {{ $t('pages.software_Msg16') }}
          </el-button>
          <el-button v-if="supportSelectTerminalSoft" :loading="fileSelecting" type="primary" size="mini" @click="showTermAppSelectDlg">
            {{ $t('pages.selectTerminalSoft') }}
          </el-button>
          <el-row v-if="fileSubmitting">
            <el-col :span="22">
              <el-progress type="line" :percentage="percentage"/>
            </el-col>
            <el-col :span="2">
              <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
            </el-col>
          </el-row>
        </div>
        <grid-table
          ref="fileList"
          :height="250"
          :multi-select="true"
          :show-pager="false"
          row-key="fileMd5"
          :col-model="colModel"
          :row-datas="fileList"
          style="margin-bottom: 5px;"
        />
        <FormItem v-if="supportGroup" :label="$t('pages.appType')" label-width="80px" :extra-width="{en:60}" prop="typeId">
          <tree-select
            ref="typeTree"
            v-model="tempF.typeId"
            :data="typeTreeData"
            node-key="dataId"
            :checked-keys="checkTypeIds"
            :width="296"
            style="display: inline-block;"
            class="input-with-button"
            @change="parentTypeSelectChange"
          />
          <el-button :title="$t('pages.addType')" class="editBtn" @click="handleAppTypeCreate"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <open-finger-form-item v-if="supportMd5" :temp="tempF" :disable-product-md5="disableProductMd5" :valid-md5="false"/>
        <!-- 自定义插槽 -->
        <slot></slot>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData1">{{ $t('button.confirm') }}</el-button>
        <el-button @click="handleClose">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <app-group-dlg
      ref="appGroupDlg"
      :os-type="osType"
      :append-to-body="appendToBody"
      :create="createGroup"
      :update="updateGroup"
      :get-by-name="getGroupByName"
      @submitEnd="groupSubmitEnd"
    />
    <app-select-dlg ref="appSelectDlg" :append-to-body="appendToBody" @select="appendFile"/>
    <terminal-soft ref="termAppSelectDlg" @select="selectTermFile"/>

    <re-process-merge-dlg v-if="installNotRepeatAddition" ref="reProcessMergeDlg" @submit="reProcessMergeSubmit"/>

  </div>
</template>
<script>
import { changeFiles, loopUploadFiles } from '@/api/behaviorManage/application/appGroup'
import { canSetPropertyMd5, createProcessVersion } from '@/utils/fingerprint'
import UploadDir from '@/components/UploadDir'
import axios from 'axios'
import AppGroupDlg from './appGroupDlg'
import AppSelectDlg from './appSelectDlg'
import OpenFingerFormItem from './openFingerFormItem'
import TerminalSoft from '@/views/dataEncryption/encryption/processStgLib/TerminalSoft'
import ReProcessMergeDlg from './reProcessMergeDlg';
import { listSoftProcessByProcessName } from '@/api/system/baseData/softPacket';
import AppScanPlug from '@/views/system/baseData/appGroup/appScanPlug'

export default {
  name: 'BatchUpload',
  components: { AppScanPlug, ReProcessMergeDlg, TerminalSoft, AppSelectDlg, UploadDir, OpenFingerFormItem, AppGroupDlg },
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    typeTreeData: {
      type: Array,
      default() {
        return []
      }
    },
    disableProductMd5: { // 是否禁用“产品名称”防伪冒
      type: Boolean,
      default: true
    },
    addType: {
      type: Function,
      default: null
    },
    create: { // 保存导入数据方法
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    update: { // 修改导入数据方法
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    createGroup: { // 添加APP分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    updateGroup: { // 修改APP分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getGroupByName: { // 根据分组名称获取分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    supportMd5: { // // 是否支持指纹防伪冒（某些功能终端还不支持，所以加个开关）
      type: Boolean,
      default: true
    },
    // 是否支持分组
    supportGroup: {
      type: Boolean,
      default: true
    },
    // 是否支持从终端目录中获取
    supportSelectTerminalSoft: {
      type: Boolean,
      default: false
    },
    //  软件安装/卸载限制 不支持添加重复进程信息
    installNotRepeatAddition: {
      type: Boolean,
      default: false
    },
    // 单次最大允许添加的进程数量
    maxSize: {
      type: Number,
      default: 1000
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName', width: '120', sort: true },
        { prop: 'productName', label: 'productName', width: '100', sort: true },
        { prop: 'productVersion', label: 'productVersion', width: '100', sort: true },
        { prop: 'fileDescription', label: 'fileDescription', width: '100', sort: true },
        { prop: 'companyName', label: 'companyName', width: '100', sort: true },
        { prop: 'originalFilename', label: 'originalFilename', width: '120', sort: true },
        { prop: 'softSign', label: 'softSign', width: '100', sort: true }
      ],
      tempF: {},
      defaultTempF: { // 表单字段
        osType: this.osType,
        typeId: null,
        checkMd5: 0,
        md5Level: 3,
        fingerPrintIds: [],
        processList: [],
        useSelf: 0, // 是否使用进程本身指纹数据
        fingerprintGroup: 0, // 进程生成的指纹归属类别
        fingerprintData: null // 指纹
      },
      fileList: [],
      temp1: { // 表单字段
        id: undefined,
        processName: '',
        originalFilename: '',
        fileMd5: '',
        productName: '',
        productVersion: '',
        fileDescription: '',
        companyName: '',
        legalCopyright: '',
        quicklyMD5: '',
        internalName: '',
        remarks: '',
        classId: 0
      },
      dialogFormVisible1: false,
      dialogStatus: '',
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        typeId: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        encOpenSfx: [{ validator: this.suffixValidator, trigger: 'blur' }]
      },
      submitting: false,
      fileSubmitting: false,
      fileSelecting: false,
      fileLimitSize: 1024,
      percentage: 0,
      source: null,
      treeNodeType: 'G',
      checkTypeIds: [],
      batchAdd: false,
      plugIsInWork: false
    }
  },
  computed: {
    softTable: function() {
      return this.$refs['softTable']
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    groupSubmitEnd(data, dlgStatus) {
      const nodeData = {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id.toString(),
        label: data.name,
        parentId: '0'
      }
      this.typeTreeData.push(nodeData)
      this.$nextTick(() => {
        this.checkTypeIds = []
        this.checkTypeIds.push(nodeData.dataId)
      })
      this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
    },
    resetTemp() {
      this.tempF = Object.assign({}, this.defaultTempF)
      this.tempF.osType = this.osType
      this.fileList.splice(0)
      this.batchAdd = false
    },
    showAppSelectDlg() {
      this.$refs['appSelectDlg'].show()
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      // const fileName = file.name
      // const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      loopUploadFiles([file], this)
      return false // 屏蔽了action的默认上传
    },
    changeFile(files) {
      changeFiles(files, this)
    },
    appendFile(softs) {
      let isOverMaxSize = false
      for (let i = 0; i < softs.length; i++) {
        const item = softs[i]
        const index = this.fileList.findIndex(item2 => {
          return item2.fileMd5 == item.fileMd5
        })
        if (this.maxSize && this.fileList.length >= this.maxSize) {
          isOverMaxSize = true
          break
        }
        if (index == -1) {
          this.fileList.push(item)
        }
      }
      if (isOverMaxSize) {
        this.$message({
          // 添加的数据超过{limitSize}条，已过滤超过部分
          message: this.$t('pages.listLimitNumMsg', { limitSize: this.maxSize })
        })
      }
      return this.$nextTick().then(() => {
        this.$refs.fileList.toggleAllSelection()
      })
    },
    handleDrag() {
      this.$emit('dlgClose')
    },
    handleClose() {
      this.$refs.appScanPlug.closePlug()
      this.dialogFormVisible1 = false
    },
    show(rowData, batchAdd) {
      this.resetTemp()
      this.batchAdd = batchAdd
      this.dialogStatus = 'create1'
      this.dialogFormVisible1 = true
      this.tempF.typeId = null
      this.checkTypeIds.splice(0)
      this.$nextTick(() => {
        if (rowData && rowData.typeId) {
          this.tempF.typeId = rowData.typeId
          this.checkTypeIds.push(rowData.typeId)
          this.$refs.typeTree.checkSelectedNode(this.checkTypeIds)
        }
        this.$refs['dataForm2'].clearValidate()
        this.$refs.appScanPlug.startPlug()
      })
    },
    parentTypeSelectChange(data) {
      this.tempF.typeId = data
      this.$refs['dataForm2'].clearValidate()
    },
    handleAppTypeCreate() {
      this.$refs['appGroupDlg'].show()
    },
    createData1() {
      this.submitting = true
      const datas = this.$refs.fileList.getSelectedDatas()
      if (!datas.length) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text7'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      this.saveProcessHandler(datas);
    },
    saveProcessHandler(datas) {
      let flag = false
      for (let i = 0; i < datas.length; i++) {
        const process = datas[i]
        if (this.tempF.checkMd5 == 1 && this.tempF.md5Level == 3 && !canSetPropertyMd5(process)) {
          flag = true
          break
        }
        if (this.tempF.checkMd5 == 1 && this.tempF.md5Level == 4 && (process.productName == '' || process.productName == null)) {
          flag = true
          break
        }
      }
      if (flag) {
        this.$confirmBox(this.tempF.md5Level == 4 ? this.$t('pages.appGroup_text26') : this.$t('pages.appGroup_text6'), this.$t('text.prompt')).then(() => {
          this.saveProcess(datas)
        }).catch(() => {
          this.submitting = false
        })
      } else {
        this.saveProcess(datas)
      }
    },
    saveProcess(datas) {
      this.submitting = true
      this.$refs['dataForm2'].validate(async(valid) => {
        if (valid) {
          //  软件安装/卸载限制-进程名称限制
          if (this.installNotRepeatAddition) {
            const names = datas.map(data => data.processName).filter((item, index, arr) => { return arr.indexOf(item, 0) === index });
            //  根据进程信息，分类ID，返回存在相同的进程信息，例如：进程信息不是防伪冒，则返回库中不是防伪冒的进程信息；当进程信息是防伪冒时,返回库中的防伪冒进程信息
            //  checkMd5仅传0根1
            const res = await listSoftProcessByProcessName({ processNames: names, checkMd5: this.tempF.checkMd5 ? 1 : 0, typeId: this.tempF.typeId });
            //  mergeProcessMap：库中需要合并的数据
            const mergeProcessMap = res.data.mergeProcessMap
            // exitsProcessMap： 返回已在存在与库中的进程信息，
            //  key：进程名称
            //  value：进程信息（仅确保 待添加为防伪冒进程时，库中存在则返回防伪冒进程，不保证防伪冒级别相同，有返回表示库中不存在多条相同配置的进程信息（相同配置：进程名称相同，是否开启防伪冒配置相同））
            const exitsProcessMap = res.data.exitsProcessMap
            //  待添加的进程名称在库中是否存在需要合并的数据
            if (mergeProcessMap || exitsProcessMap) {
              //  将程序数据转换成指纹数据
              const tzDatas = datas.map(t => t.relaFingerPrint && t.relaFingerPrint.length ? t.relaFingerPrint[0] : t);
              if (mergeProcessMap) {
                this.$refs.reProcessMergeDlg.show(tzDatas, this.tempF.checkMd5 ? this.tempF.md5Level : 0, this.tempF.typeId, mergeProcessMap);
              } else {
                //  校验待添加的进程和库中的进程是否存在进程指纹冲突（冲突：进程版本号相同，防伪冒级别不同），若存在时需要进行合并
                const reNames = []
                const exitsDatas = [] //  已存在的进程信息
                if (exitsProcessMap) {
                  for (const key in exitsProcessMap) {
                    if (exitsProcessMap.hasOwnProperty(key)) {
                      reNames.push(key);
                      exitsDatas.push(exitsProcessMap[key])
                    }
                  }
                }
                //  所有待添加进程都不在库中,直接保存
                if (reNames.length === 0) {
                  this.save(datas, exitsDatas)
                } else if (!this.batchAdd && !this.tempF.checkMd5 && reNames.length === names.length) {
                  this.$notify({ title: this.$t('text.prompt'), message: this.$t('pages.processMergeMsg2'), type: 'warning', duration: 2000 })
                } else {
                  this.$refs.reProcessMergeDlg.showMerge(tzDatas, this.tempF.checkMd5 ? this.tempF.md5Level : 0, this.tempF.typeId, exitsDatas)
                }
              }
              this.submitting = false
              return
            }
          }
          this.save(datas)
        } else {
          this.submitting = false
        }
      })
    },
    /**
     * 去重
     * 当存在相同的进程时，若开启防伪冒，相同版本去重，不同版本进行合并；若未开启防伪冒时，相同进程去重
     * @param datas
     * @returns {*[]}
     */
    handlerDatas(datas) {
      const list = []
      datas.forEach(data => {
        const process = list.find(t => t.processName === data.processName)
        if (process) {
          //  若开启防伪冒
          if (this.tempF.checkMd5) {
            if (!process.relaFingerPrint.find(t => t.fileMd5 === data.fileMd5)) {
              process.relaFingerPrint.push(data.relaFingerPrint[0]);
            }
          }
        } else {
          list.push(data);
        }
      })
      return list;
    },
    // 把程序数据装成指纹数据
    toFingerprintData(appData) {
      let md5Level = this.tempF.md5Level
      if (!canSetPropertyMd5(appData)) {
        md5Level = 1
      }
      const obj = {
        processName: appData.processName,
        processVersion: createProcessVersion(appData.processName, appData.productVersion),
        componentVersion: appData.productVersion,
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: md5Level,
        propertyMark: appData.propertyMark,
        productMd5: appData.productMd5,
        remark: ''
      }
      return obj
    },
    /**
     * 保存进程
     * @param datas 待添加到库中进程
     * @param exitsDatas  待添加且库中已存在的进程信息
     */
    save(datas, exitsDatas) {
      if (this.installNotRepeatAddition && !datas.length) {
        this.$emit('submitEnd', exitsDatas, 'create', null)
        this.submitting = false
        this.dialogFormVisible1 = false
        this.$refs.appScanPlug.closePlug()
        return
      }
      datas.forEach(item => {
        item.relaFingerPrint = [this.toFingerprintData(item)]
      })
      if (this.installNotRepeatAddition) {
        datas = this.handlerDatas(datas);
      }
      this.tempF.processList = datas
      this.create(this.tempF).then(respond => {
        const list = respond.data || []
        this.installNotRepeatAddition && exitsDatas && list.push(...exitsDatas)
        this.$emit('submitEnd', list, 'create', null)
        this.submitting = false
        this.dialogFormVisible1 = false
        // this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
      }).catch(() => {
        this.submitting = false
      })
    },
    showTermAppSelectDlg() {
      this.$refs['termAppSelectDlg'].show()
    },
    selectTermFile(data) {
      this.fileSelecting = true
      // 如果传输完毕
      if (data.allNum == data.hasLoadNum) {
        this.fileSelecting = false
      }
      if (data && data.softList && data.softList.length > 0) {
        this.appendFile(data.softList, false)
      }
    },
    suffixValidator(rule, value, callback) {
      if (value == '*.*') {
        callback(new Error(this.$t('pages.appGroup_text5')))
      } else {
        callback()
      }
    },
    /**
     *  重复进程选中后
     * @param notExitsProcessDatas  待添加进程不在库中的进程信息
     * @param exitsProcessDatas     待添加进程已存在与库中的进程信息
     */
    reProcessMergeSubmit(notExitsProcessDatas, exitsProcessDatas) {
      this.save(notExitsProcessDatas, exitsProcessDatas)
    }
  }
}
</script>
<style lang="scss" scoped>
.toolbar>div {
  vertical-align: top;
}
</style>
