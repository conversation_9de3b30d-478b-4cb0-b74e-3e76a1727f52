<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="titleMap[dlgStatus]"
    :visible.sync="dlgVisible"
    width="400px"
    @dragDialog="handleDrag"
  >
    <Form ref="dataFrom" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
      <FormItem :label="$t('pages.groupName1')" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="30"/>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="dlgStatus==='create'?createNode():updateNode()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'AppGroupDlg',
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    create: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    update: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getByName: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    }
  },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        parentId: 0,
        name: '',
        osType: this.osType
      },
      submitting: false,
      dlgVisible: false,
      dlgStatus: '',
      titleMap: {
        update: this.i18nConcatText(this.$t('pages.appType'), 'update'),
        create: this.i18nConcatText(this.$t('pages.appType'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    show(rowData) {
      this.dlgVisible = true
      this.submitting = false
      if (rowData && rowData.id) {
        this.dlgStatus = 'update'
        this.temp = JSON.parse(JSON.stringify(rowData))
      } else {
        this.dlgStatus = 'create'
        this.temp = Object.assign({}, this.defaultTemp)
        this.temp.osType = this.osType
      }
      this.$nextTick(() => {
        this.$refs['dataFrom'].clearValidate()
      })
    },
    createNode() {
      this.submitting = true
      this.$refs['dataFrom'].validate((valid) => {
        if (valid) {
          this.submitting = true
          this.create(this.temp).then(resp => {
            this.submitting = false
            this.dlgVisible = false
            this.$emit('submitEnd', resp.data, this.dlgStatus)
          }).catch(res => { this.submitting = false })
        } else { this.submitting = false }
      })
    },
    updateNode() {
      this.submitting = true
      this.$refs['dataFrom'].validate((valid) => {
        if (valid) {
          this.update(this.temp).then(resp => {
            this.submitting = false
            this.dlgVisible = false
            this.$emit('submitEnd', resp.data, this.dlgStatus)
          }).catch(res => { this.submitting = false })
        } else { this.submitting = false }
      })
    },
    handleDrag() {
    },
    groupNameValidator(rule, value, callback) {
      this.getByName({ name: value, osType: this.osType }).then(respond => {
        const data = respond.data
        if (data && data.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
