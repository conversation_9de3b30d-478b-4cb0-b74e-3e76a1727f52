<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="titleMap[formStatus]"
      :visible.sync="dialogVisible"
      :width="width + 'px'"
      @dragDialog="handleDrag"
    >
      <Form ref="appDataForm" :rules="rules" :model="appTemp" label-position="right" label-width="100px" :style="{ width: (width - 50) + 'px' }">
        <FormItem v-if="supportGroup" :label="$t('pages.appType')" prop="typeId">
          <tree-select
            ref="softTypeTree"
            v-model="appTemp.typeId"
            :data="treeSelectNode"
            node-key="dataId"
            :width="335"
            :checked-keys="checkedTypeIds"
            class="input-with-button"
            @change="appTypeNodeSelectChange"
          />
          <el-button :title="$t('pages.addType')" class="editBtn" @click="handleAppTypeCreate"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <upload-process ref="upload" :app-temp="appTemp" :can-upload="canUpload" :after-upload="afterUpload"/>
        <batch-fingerprint v-if="supportMd5" ref="batckFinger" :disable-product-md5="disableProductMd5" :temp="appTemp" :os-type="osType"/>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="formStatus==='create'?createApp():updateApp()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <app-group-dlg
      ref="appGroupDlg"
      :os-type="osType"
      :append-to-body="appendToBody"
      :create="createGroup"
      :update="updateGroup"
      :get-by-name="getGroupByName"
      @submitEnd="groupSubmitEnd"
    />
    <re-process-merge-dlg v-if="installNotRepeatAddition" ref="reProcessMergeDlg" @submit="reProcessMergeSubmit"/>
  </div>
</template>

<script>
import BatchFingerprint from '@/views/dataEncryption/encryption/processStgLib/OpenFingerprint/batchFingerprint'
import UploadProcess from '@/views/behaviorManage/application/appBlock/uploadProcess'
import AppGroupDlg from './appGroupDlg'
import ReProcessMergeDlg from './reProcessMergeDlg';
import { listSoftProcessByProcessName, deleteSoftPacket } from '@/api/system/baseData/softPacket';

export default {
  name: 'AppAddDlg',
  components: { AppGroupDlg, BatchFingerprint, UploadProcess, ReProcessMergeDlg },
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    // 窗体宽度
    width: { type: Number, default: 600 },
    appendToBody: { type: Boolean, default: false },
    typeTreeData: {
      type: Array,
      default() {
        return null
      }
    },
    disableProductMd5: {
      type: Boolean,
      default: true
    },
    create: { // 添加APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    update: { // 修改APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    createGroup: { // 添加APP分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    updateGroup: { // 修改APP分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getGroupByName: { // 根据分组名称获取分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    supportMd5: { // 是否支持指纹防伪冒（某些功能终端还不支持，所以加个开关）
      type: Boolean,
      default: true
    },
    // 是否支持分组
    supportGroup: {
      type: Boolean,
      default: true
    },
    //  软件安装/卸载限制 不支持添加重复进程信息
    installNotRepeatAddition: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      appTemp: {},
      defaultTemp: { // 表单字段
        id: null,
        osType: this.osType,
        typeId: null,
        typeName: null,
        processName: '',
        productName: '',
        productVersion: '',
        originalFilename: '',
        fileDesc: '',
        companyName: '',
        internalName: '',
        legalCopyright: '',
        fileMd5: '',
        quicklyMd5: '',
        softSign: '',
        checkMd5: 0,
        md5Level: 3,
        useSelf: 0,
        fingerprintGroup: 0,
        relaFingerPrint: []
      },
      submitting: false,
      canUpload: true,
      dialogVisible: false,
      formStatus: '',
      titleMap: {
        update: this.i18nConcatText(this.$t('pages.applicationProgram'), 'update'),
        create: this.i18nConcatText(this.$t('pages.applicationProgram'), 'create')
      },
      treeNodeType: '',
      rules: {
        processName: [{ required: true, validator: this.processNameValidator, trigger: 'blur' }],
        typeId: [{ required: true, validator: this.typeIdValidator, trigger: 'blur' }]
      },
      treeSelectNode: [],
      checkedTypeIds: [],
      appendData: null
    }
  },
  computed: {

  },
  watch: {
    typeTreeData(val) {
      this.loadAppTypeTree()
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    append(data) {
      this.appendData = data
      return this
    },
    validProcess() {
      if (this.appTemp.checkMd5 == 1 && (!this.appTemp.relaFingerPrint || this.appTemp.relaFingerPrint.length == 0)) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text1'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.appTemp.relaFingerPrint && this.appTemp.relaFingerPrint.length > 0) {
        // 验证是否存在进程名称不一样的指纹
        // 这个场景主要是防止用户已经上传了指纹信息，但是又把主进程名称改掉的问题
        const difPros = []
        this.appTemp.relaFingerPrint.forEach(item => {
          if (item.processName.toLowerCase() != this.appTemp.processName.toLowerCase()) {
            difPros.push(item)
          }
        })
        if (difPros.length > 0) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.appGroup_text2'),
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      return true
    },
    show(rowData) {
      this.dialogVisible = true
      this.loadAppTypeTree()
      this.checkedTypeIds.splice(0)
      if (rowData && rowData.id) {
        this.canUpload = false
        this.formStatus = 'update'
        this.appTemp = JSON.parse(JSON.stringify(rowData))
      } else {
        this.canUpload = true
        this.formStatus = 'create'
        this.appTemp = Object.assign({}, this.defaultTemp)
        this.appTemp.osType = this.osType
      }
      this.$nextTick(() => {
        if (rowData && rowData.typeId) {
          this.appTemp.typeId = rowData.typeId
          this.appTemp.typeName = rowData.typeName
          this.checkedTypeIds.splice(0, 1, rowData.typeId)
          this.$refs.softTypeTree.checkSelectedNode(this.checkedTypeIds)
        }
        if (!this.canUpload) {
          this.$refs.upload.cancel()
          this.$refs.upload.resetUploadComponent()
        }
        this.$refs['appDataForm'].clearValidate()
        // 表格的防伪冒算法列是否隐藏需要重新加载
        if (this.$refs.batckFinger) {
          this.$refs.batckFinger.colModel[3].hidden = this.appTemp.osType != 1
        }
      })
    },
    afterUpload(appData) {
      // changeMd5Level(this.appTemp)
      const obj = {
        processName: appData.processName,
        processVersion: appData.productVersion ? appData.productVersion : appData.processName,
        componentVersion: '',
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: appData.propertyMd5 ? 3 : 1,
        propertyMark: appData.propertyMark,
        remark: ''
      }
      if (this.appTemp.osType != 1) {
        // 非window终端只支持全量md5防伪冒验证方式
        obj.checkMd5 = 2
      }
      this.appTemp.relaFingerPrint.splice(0, this.appTemp.relaFingerPrint.length, obj)
      this.$refs.appDataForm.clearValidate({ processName: this.appTemp.processName })
    },
    loadAppTypeTree: function() {
      if (this.typeTreeData) {
        this.treeSelectNode = this.typeTreeData
        if (this.typeTreeData.length > 0) {
          this.treeNodeType = this.typeTreeData[0].type
        }
      }
    },
    appTypeNodeSelectChange: function(data, options) {
      this.appTemp.typeId = data
      if (options instanceof Array) {
        this.appTemp.typeName = options.length > 0 ? options[0].label : ''
      } else {
        this.appTemp.typeName = options.label
      }
      this.$refs.appDataForm.clearValidate({ typeId: this.appTemp.typeId })
    },
    handleAppTypeCreate(data) {
      this.$refs['appGroupDlg'].show()
    },
    createApp() {
      this.submitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid) {
          if (!this.validProcess()) {
            this.submitting = false
            return
          }
          this.create(this.appTemp).then(res => {
            this.$emit('submitEnd', res.data, this.formStatus, this.appendData)
            this.submitting = false
            this.dialogVisible = false
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateApp() {
      this.submitting = true
      this.$refs['appDataForm'].validate(async(valid) => {
        if (valid) {
          if (!this.validProcess()) {
            this.submitting = false
            return
          }
          //  软件安装/卸载限制-不支持相同进程名称时,校验库中是否已存在该进程信息
          if (this.installNotRepeatAddition) {
            const res = await listSoftProcessByProcessName({
              processNames: [this.appTemp.processName],
              checkMd5: this.appTemp.checkMd5 ? 1 : 0,
              typeId: this.appTemp.typeId
            });
            const { mergeProcessMap, exitsProcessMap } = res.data
            const temp = this.appTemp.relaFingerPrint[0];
            const datas = [temp]
            //  库中存在需要合并的数据时，优先处理
            if (mergeProcessMap) {
              this.$refs.reProcessMergeDlg.show(datas, this.appTemp.checkMd5 ? temp.checkMd5 || 0 : 0, this.appTemp.typeId, mergeProcessMap, 'update');
              this.submitting = false
              return;
            } else {
              if (exitsProcessMap && exitsProcessMap[temp.processName].id !== this.appTemp.id) {
                const exitsDatas = [] //  已存在的进程信息
                if (exitsProcessMap) {
                  for (const key in exitsProcessMap) {
                    if (exitsProcessMap.hasOwnProperty(key)) {
                      exitsDatas.push(exitsProcessMap[key])
                    }
                  }
                }
                //   若不为进程防伪冒时，删除当前进程
                if (!this.appTemp.checkMd5) {
                  this.$notify({ title: this.$t('text.prompt'), message: '进程已存在未开启进程防伪冒的信息', type: 'warning', duration: 2000 })
                } else {
                  //  修改后的进程与库中可能存在进程特征冲突
                  this.$refs.reProcessMergeDlg.showMerge(datas, temp.checkMd5 || 0, this.appTemp.typeId, exitsDatas)
                }
                this.submitting = false
                return;
              }
            }
          }

          const tempData = Object.assign({}, this.appTemp)
          this.update(tempData).then(res => {
            this.$emit('submitEnd', tempData, this.formStatus, this.appendData)
            this.submitting = false
            this.dialogVisible = false
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    groupSubmitEnd(data, dlgStatus) {
      const nodeData = {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id.toString(),
        label: data.name,
        parentId: '0'
      }
      this.treeSelectNode.push(nodeData)
      this.$nextTick(() => {
        this.checkedTypeIds = []
        this.checkedTypeIds.push(nodeData.dataId)
      })
      this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    typeIdValidator(rule, value, callback) {
      if (this.appTemp.typeId) {
        callback()
      } else {
        callback(new Error(this.$t('pages.appGroup_text3')))
      }
    },
    processNameValidator(rule, value, callback) {
      if (this.appTemp.processName) {
        callback()
      } else {
        callback(new Error(this.$t('pages.appGroup_text4')))
      }
    },
    /**
     *  重复进程选中后
     * @param notExitsProcessDatas  待添加进程不在库中的进程信息
     * @param exitsProcessDatas     待添加进程已存在与库中的进程信息
     * @param operator               'cancel':取消回调
     */
    reProcessMergeSubmit(notExitsProcessDatas, exitsProcessDatas, operator) {
      this.dialogVisible = false
      if (!operator) {
        deleteSoftPacket({ ids: this.appTemp.id + '' }).then(res => {
          this.$emit('submitEnd', null, '');
        })
      } else {
        this.$emit('submitEnd', null, '');
      }
    }
  }
}
</script>
