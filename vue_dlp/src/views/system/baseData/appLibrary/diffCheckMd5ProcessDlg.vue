<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="false"
    :modal="false"
    :title="title"
    :visible.sync="visible"
    width="600px"
  >
    <el-container>
      <el-aside v-if="processNameTreeData.length > 1" width="200px">
        <tree-menu
          ref="groupTree"
          :data="processNameTreeData"
          :height="400"
          :multiple="false"
          :expand-on-click-node="false"
          @node-click="processTreeNodeClick"
        />
      </el-aside>
      <el-main>
        <el-row>
          <el-col v-for="(process, index) in diffCheckMd5es" :key="index" :span="24">
            <div style="padding: 5px 10px 10px 10px">
              <label>{{ `${process.processName}(${process.processVersion})` }}</label>
              <el-radio-group v-model="diffCheckMd5Map[process.processName][process.fileMd5]">
                <el-radio v-for="(item, index1) in process.children" :key="index1" :label="item.checkMd5">{{ getCheckMd5Label(item.checkMd5) }}</el-radio>
              </el-radio-group>
            </div>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="confirm">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="cancel">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'DiffCheckMd5ProcessDlg',
  data() {
    return {
      title: this.$t('pages.differentLevel'),
      submitting: false,
      visible: false,
      diffCheckMd5es: [],
      diffCheckMd5Map: {}, // 保存存在冲突的版本号进程信息 [processName][fileMd5] = [{ processName: '', ...},{}] key1： 进程名称， key2：进程fileMd5，value：防伪冒级别（checkMd5) 1-程序指纹  3-进程特征 4-产品名称
      diffCheckMd5List: [], //  临时保存-该进程的所有版本信息不同防伪冒级别的进程信息
      processNameTreeData: [], // 用于存储各个进程的名称， 当存在多个进程需要合并时
      mergeMap: new Map() //  存储所有进程信息
    }
  },
  created() {

  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.diffCheckMd5es = []
      this.diffCheckMd5Map = {}
      this.diffCheckMd5List = []
      this.processNameTreeData = []
      this.mergeMap = new Map();
      this.submitting = false;
    },
    /**
     * 设置当前进程的合并信息
     * @param diffCheckMd5es   待选择防伪冒级别的进程信息
     * @param diffCheckMd5List   该进程的所有版本信息不同防伪冒级别的进程信息
     */
    setCurrentProcessMergeData(diffCheckMd5es, diffCheckMd5List) {
      this.diffCheckMd5List = diffCheckMd5List;
      this.diffCheckMd5es = diffCheckMd5es;
    },
    /**
     * 展示
     * @param map   key：进程名称， value：{ diffCheckMd5es，diffCheckMd5List }
     * @param diffCheckMd5es   待选择防伪冒级别的进程信息
     * @param diffCheckMd5List   该进程的所有版本信息不同防伪冒级别的进程信息，结构包含 未设置进程防伪冒，设置防伪冒（进程特征，程序指纹，产品名称）
     */
    show(map) {
      this.initData();
      this.mergeMap = map;
      let i = 1;
      map.forEach((value, processName) => {
        this.processNameTreeData.push({ id: i, dataId: i + '', label: processName, parentId: '' });
        i++;
        this.$set(this.diffCheckMd5Map, processName, {});
        value.diffCheckMd5es.forEach(process => {
          this.$set(this.diffCheckMd5Map[processName], process.fileMd5, process.children[0].checkMd5);
        })
      })
      if (this.processNameTreeData.length) {
        const { diffCheckMd5es, diffCheckMd5List } = this.mergeMap.get(this.processNameTreeData[0].label)
        this.setCurrentProcessMergeData(diffCheckMd5es, diffCheckMd5List);
        this.visible = true;
      }
    },
    /**
     * 确认
     */
    confirm() {
      this.submitting = true;
      const map = new Map();
      this.mergeMap.forEach((value, processName) => {
        //  过滤掉与选中的防伪冒级别不相同的进程信息，与展示的进程版本号不相同的数据会保留，非防伪冒进程最多仅有一条
        const list = value.diffCheckMd5List.filter(t => t.checkMd5 === 0 || !this.diffCheckMd5Map[processName][t.fileMd5] || t.checkMd5 === this.diffCheckMd5Map[processName][t.fileMd5])
        map.set(processName, { diffCheckMd5List: list, processArr: this.mergeMap.get(processName).processArr });
      })
      this.$emit('submit', map);
      this.submitting = false;
      this.visible = false;
    },
    /**
     * 取消
     */
    cancel() {
      this.$emit('closeFuc')
      this.visible = false;
    },
    /**
     * 获取防伪冒级别描述
     */
    getCheckMd5Label(type) {
      let result = '';
      if (type) {
        switch (type) {
          case 1: result = this.$t('table.programFinger'); break;
          case 3: result = this.$t('table.processCharacter'); break;
          case 4: result = this.$t('table.productName'); break;
        }
      }
      return result;
    },
    /**
     * 选中进程名称时，展示该进程的防伪冒进程信息
     * @param data
     * @param node
     * @param element
     */
    processTreeNodeClick(data, node, element) {
      const { diffCheckMd5es, diffCheckMd5List } = this.mergeMap.get(data.label)
      this.setCurrentProcessMergeData(diffCheckMd5es, diffCheckMd5List);
    }
  }
}
</script>

<style scoped>

</style>
