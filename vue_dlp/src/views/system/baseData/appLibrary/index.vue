<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu ref="appTypeTree" :default-expand-all="true" :data="appTypeTreeData" :render-content="renderContent" @node-click="appTypeTreeNodeCheckChange" />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <!--<el-button size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.appGroup_text23')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="appInfoList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" style="width: 500px; margin-left: 25px;">
        <FormItem :label="$t('pages.appType')" prop="typeId">
          <tree-select :data="treeSelectNode" node-key="dataId" :checked-keys="[temp.typeId]" @change="appTypeNodeSelectChange" />
        </FormItem>
        <FormItem :label="$t('pages.exeDesc')" prop="exeDesc">
          <el-input v-model="temp.exeDesc" :maxlength="50"/>
        </FormItem>
        <el-row>
          <el-col :span="22">
            <FormItem :label="$t('pages.exeName')" prop="exeName">
              <el-input v-model="temp.exeName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="uploadProcess"
              name="processFile"
              action="1111"
              accept=".exe"
              :limit="1"
              :show-file-list="false"
              :disabled="fileSubmitting"
              :before-upload="getFileName"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <open-finger-form-item :temp="temp"/>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogAppTypeFormVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="appTypeForm" :rules="grouprules" :model="tempG" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.groupName1')" prop="name">
          <el-input v-model="tempG.name" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='createType'?createNode():updateNode()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogAppTypeFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeNode, createAppType, updateAppType, deleteAppType, getAppTypeByName, getAppInfoList, createAppInfo, updateAppInfo, deleteAppInfo, countInfoByGroupId } from '@/api/system/baseData/appLibrary'
import OpenFingerFormItem from './openFingerFormItem'
import { upload } from '@/api/behaviorManage/application/appGroup'

export default {
  name: 'AppLibrary',
  components: { OpenFingerFormItem },
  data() {
    return {
      colModel: [
        { prop: 'exeName', label: 'exeName', width: '150', fixed: true },
        { prop: 'originalFileName', label: 'originalFileName', width: '150' },
        { prop: 'exeDesc', label: 'exeDesc', width: '200' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        typeId: undefined
      },
      rules: {
        exeDesc: [
          { required: true, message: this.$t('pages.appGroup_text24'), trigger: 'blur' }
        ],
        exeName: [
          { required: true, message: this.$t('pages.appGroup_text25'), trigger: 'blur' }
        ]
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        exeName: '',
        exeDesc: '',
        originalFileName: '',
        typeId: undefined,
        checkMd5: 0,
        md5Level: 3,
        useSelf: 1,
        fingerPrintIds: [],
        fingerprintGroup: 0,
        fingerprintData: null
      },
      tempG: {},
      defaultTempG: { // 表单字段
        id: undefined,
        parentId: 0,
        name: ''
      },
      grouprules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.appTypeNameValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogAppTypeFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.applicationProgram'), 'update'),
        create: this.i18nConcatText(this.$t('pages.applicationProgram'), 'create'),
        createType: this.i18nConcatText(this.$t('pages.appType'), 'create'),
        updateType: this.i18nConcatText(this.$t('pages.appType'), 'update')
      },
      multipleSelection: [], // 选中行数组集合
      downloadLoading: false,
      treeNodeType: '',
      appTypeTreeData: [{ id: '0', dataId: '0', label: this.$t('pages.appLibrary'), parentId: '', children: [] }],
      treeSelectNode: [],
      fileSubmitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['appInfoList']
    },
    appTypeTree: function() {
      return this.$refs['appTypeTree']
    }
  },
  created() {
    this.resetTemp()
    this.loadUrlTree()
  },
  methods: {
    rowDataApi: function(option) {
      return getAppInfoList(option)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    appTypeTreeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      if (checkedNode) {
        this.query.typeId = checkedNode.data.dataId
      } else {
        this.query.typeId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    loadUrlTree: function() {
      getTreeNode().then(respond => {
        this.appTypeTreeData[0].children = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.appTypeTreeData[0].children
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id.toString(),
        label: data.name,
        parentId: '0'
      }
    },
    createNode() {
      this.submitting = true
      this.$refs['appTypeForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.appTypeTemp)
          createAppType(tempData).then(respond => {
            this.submitting = false
            this.dialogAppTypeFormVisible = false
            tempData.id = respond.data.id
            this.appTypeTree.addNode(this.dataToTreeNode(tempData))
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateNode() {
      this.submitting = true
      this.$refs['appTypeForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.tempG)
          updateAppType(tempData).then(respond => {
            this.submitting = false
            this.dialogAppTypeFormVisible = false
            this.appTypeTree.updateNode(this.dataToTreeNode(tempData))
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    removeNode(data) {
      countInfoByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.appGroup_text17'), type: 'warning', duration: 2000 })
          return
        }
        this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
          deleteAppType({ id: data.dataId }).then(respond => {
            this.appTypeTree.removeNode([data.id])
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.tempG = Object.assign({}, this.defaultTempG)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    appTypeNodeSelectChange: function(data) {
      this.temp.typeId = data
    },
    handleAppTypeCreate(data) {
      this.resetTemp()
      // this.tempG.parentId = data.dataId
      this.dialogStatus = 'createType'
      this.dialogAppTypeFormVisible = true
      this.$nextTick(() => {
        this.$refs['appTypeForm'].clearValidate()
      })
    },
    handleAppTypeUpdate: function(data) {
      this.resetTemp()
      this.tempG.id = data.dataId
      this.tempG.name = data.label
      // this.tempG.parentId = this.replaceTreeNodeType(data.parentId)
      this.dialogStatus = 'updateType'
      this.dialogAppTypeFormVisible = true
      this.$nextTick(() => {
        this.$refs['appTypeForm'].clearValidate()
      })
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.typeId = this.query.typeId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    getFileName(file) {
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'exe') {
        this.fileSubmitting = true
        this.submitting = true
        const fd = new FormData()
        fd.append('uploadFile', file)// 传文件
        upload(fd).then(res => {
          this.fileSubmitting = false
          this.submitting = false
          if (res.data.length > 0) {
            this.temp.fingerprintData = res.data[0]
            this.temp.exeName = res.data[0].processName
            this.temp.originalFileName = res.data[0].originalFilename
          }
        }).catch(res => {
          this.fileSubmitting = false
          this.submitting = false
        })
      }
      return false // 屏蔽了action的默认上传
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createAppInfo(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateAppInfo(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.updateRowData(this.temp)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteAppInfo({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    appTypeNameValidator(rule, value, callback) {
      getAppTypeByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleAppTypeCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleAppTypeUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    pushChildDataIds: function(toDeleteIds, data) {
      data.children.forEach(child => {
        toDeleteIds.push(child.dataId)
        if (child.children && child.children.length > 0) {
          this.pushChildDataIds(toDeleteIds, child)
        }
      })
    }
  }
}
</script>
