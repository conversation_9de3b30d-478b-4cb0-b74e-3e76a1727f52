<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="1000px"
    >
      <div>
        <!-- 库中的进程信息先合并 -->
        <div>
          <div style="margin-bottom: 10px; color: #409EFF"><label>库中存在相同的进程信息，请进行合并</label></div>
          <el-timeline>
            <el-timeline-item
              v-for="(process, index) in processList"
              :key="index"
              :color="process.color"
              :type="process.type"
              size="large"
              :timestamp="process.processName"
              placement="top"
            >
              <el-card>
                <div v-for="(typeId, index1) in process.children.keys()" :key="'children' + index1">
                  <div>
                    <div style="margin-left: -5px; font-weight: bolder">{{ '程序类别：' + getTypeLabel(typeId) }}</div>
                    <div style="margin-bottom: 5px">
                      {{ process.type !== 'info' ? '合并结果：' : '待合并信息：' }}
                    </div>
                    <grid-table
                      :ref="'processList' + index1 + process.processName"
                      :max-height="200"
                      auto-height
                      :multi-select="false"
                      :show-pager="false"
                      default-expand-all
                      row-key="dataId"
                      :col-model="mergeColModel"
                      :row-datas="process.children.get(typeId)"
                      style="margin-bottom: 5px;"
                    />
                    <div style="width: 100%; float: right">
                      <el-button type="primary" size="medium" style="float:right;" :disabled="process.type !== 'info' || process.successStatus.includes(typeId)" @click="merge(process.processName, typeId)">合并</el-button>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="merge(null)">
          {{ '一键合并' }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <diff-check-md5-process-dlg ref="diffCheckMd5ProcessDlg" @submit="diffCheckSubmit" @closeFuc="closeFuc"/>
  </div>
</template>

<script>
import DiffCheckMd5ProcessDlg from './diffCheckMd5ProcessDlg';
import {
  mergeProcess,
  batchMergeProcess,
  getAllMergeProcess
} from '@/api/system/baseData/softPacket';

export default {
  name: 'ReProcessAllMergeDlg',
  components: { DiffCheckMd5ProcessDlg },
  props: {
    //  程序库类别
    typeTreeData: { type: Array, default() { return null } }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      title: '',
      operatorStatus: null,  //  操作方式：修改（update）
      mergeColModel: [
        { prop: 'processName', label: 'processName', width: '120', formatter: this.nameFormatter },
        { prop: 'productVersion', label: 'productVersion', width: '80' },
        { prop: 'processVersion', label: 'processVersion', width: '80' },
        { prop: 'checkMd5', label: 'counterfeitingLevel', width: '100', formatter: this.getCheckMd5Label }
      ],
      processList: [],  //  未合并的进程信息（不同分类下允许存在相同进程）
      mergeProcessMap: new Map(),  //  合并完成情况, key:程序Id-进程名称，value：固定值：1
      allIsMerge: false,  //  所有进程是否都已合并
      allMergeList: [],  //  一键合并时，存储格式化后的合并数据
      needMergeNumber: 0  //  需完成合并的个数，达到自动退出
    }
  },
  computed: {
    diffCheckMd5ProcessDlg() {
      return this.$refs.diffCheckMd5ProcessDlg;
    }
  },
  watch: {
    allIsMerge(val) {
      if (val) {
        this.$emit('submit')
        this.visible = false;
      }
    }
  },
  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.submitting = false;
      this.needMergeNumber = 0
      this.allMergeList = []
      this.allIsMerge = false
      this.mergeProcessMap = new Map();
    },
    /**
     * 合并库中所有数据
     */
    show() {
      this.initData();
      this.status = 1;
      this.title = '合并库中数据'
      getAllMergeProcess().then(res => {
        if (res.data) {
          if (res.data.mergeProcessMap) {
            this.processList = this.formatProcess(res.data.mergeProcessMap);
            this.visible = true;
          } else if (res.data.directMerge) {
            this.$notify({
              title: this.$t('text.success'),
              message: '合并成功',
              type: 'success',
              duration: 2000
            })
            this.$emit('submit')
          }
        }
      })
    },
    /**
     * 格式化数据
     * @param processes
     * @returns {*[]}
     */
    formatProcess(processes) {
      const list = []
      let i = 1;
      for (const key in processes) {
        const processArr = processes[key];
        //  按照程序类型Id区分
        const map = new Map();
        processArr.forEach(t => {
          if (!map.get(t.typeId)) {
            map.set(t.typeId, []);
          }
          map.get(t.typeId).push(t);
        })
        list.push({
          dataId: 'G' + i++,
          processName: key,
          type: 'info',
          color: 'hsv',
          children: map,
          successStatus: [],
          successNum: 0 //  完成数量
        });
        this.needMergeNumber += map.size;

        //    格式化列表中每个数据
        processes[key].forEach(data => {
          data.dataId = 'T' + data.id
          if (data.checkMd5) {
            data.itemType = 2;
            data.children = data.relaFingerPrint;
            data.children && data.children.forEach(t => {
              t.dataId = t.id
            })
          }
        })
      }
      return list;
    },
    /**
     * 取消
     */
    cancel() {
      this.visible = false;
      this.$emit('closeFuc')
    },
    nameFormatter(row, data) {
      if (row.itemType == 2) {
        const label = row.processName
        return `<span class='el-icon el-icon-folder'></span> ${label}`
      } else {
        return row.processName
      }
    },
    /**
     * 获取防伪冒级别描述
     */
    getCheckMd5Label(row, type) {
      let result = '';
      if (type !== undefined && type != null && (!row.itemType || row.itemType !== 2)) {
        switch (type + '') {
          case '1': result = this.$t('table.programFinger'); break;
          case '3': result = this.$t('table.processCharacter'); break;
          case '4': result = this.$t('table.productName'); break;
          default: result = this.$t('pages.notSettingAntiCounterfeitTower')
        }
      }
      return result;
    },
    /**
     * 待添加进程的防伪冒级别格式化
     */
    checkMd5Formatter(row, data) {
      let result = '';
      const type = this.checkMd5
      switch (type + '') {
        case '1': result = this.$t('table.programFinger'); break;
        case '3': result = this.$t('table.processCharacter'); break;
        case '4': result = this.$t('table.productName'); break;
      }
      return result;
    },
    /**
     * 保存不同版本的进程信息（不考虑未开启防伪冒的进程，因为在查询合并数据时已直接合并了，不存在未开启防伪冒进程数据）
     * @param processName 进程名称
     * @param typeId      程序类别
     * @param list 所有待合并的进程信息
     * @returns {{diffCheckMd5es: *[], diffCheckMd5List: *[]}}
     *  - diffCheckMd5es: 存储存在冲突的进程特征信息（存在多条不同防伪冒级别的进程信息）
     *  - diffCheckMd5List：保存不同版本的进程信息
     */
    getDiffCheckData(processName, typeId, list) {
      //  归类，相同版本的进程信息归一类,  防伪冒级别（checkMd5) 1-程序指纹  3-进程特征 4-产品名称
      //  key: fileMd5，value: 不同防伪冒级别的进程信息
      const map = new Map();
      //  保存不同版本的进程信息
      const diffCheckMd5List = []
      let t = null
      list && list.forEach(processM => {
        //  有配置进程防伪冒
        if (processM.checkMd5) {
          processM.children && processM.children.forEach(process => {
            if (!map.get(process.fileMd5)) {
              map.set(process.fileMd5, [])
            }
            //  相同防伪冒级别的不重复添加
            if (!map.get(process.fileMd5).filter(t => t.checkMd5 === process.checkMd5).length) {
              diffCheckMd5List.push(process)
              map.get(process.fileMd5).push(process);
            }
          })
          if (!t) {
            t = processM;
            t.relaFingerPrint = []
          }
        }
      });
      //  获取存在多条不同防伪冒级别的进程信息
      const diffCheckMd5es = [];
      for (const [, value] of map) {
        if (value.length > 1) {
          diffCheckMd5es.push({ processName: this.buildKey(processName, typeId), processVersion: value[0].componentVersion, fileMd5: value[0].fileMd5, children: value });
        }
      }
      return { diffCheckMd5es, diffCheckMd5List, processArr: [t] }
    },
    /**
     * 合并进程
     * @param processName
     * @param typeId
     */
    merge(processName, typeId) {
      this.submitting = true;
      //  一键合并
      if (processName == null) {
        this.allMergeList = []
        const map = new Map();
        //  存储上传服务端的合并数据
        const allMergeList = []
        this.processList.forEach(process => {
          const name = process.processName
          process.children.keys().forEach(typeId => {
            const { diffCheckMd5es, diffCheckMd5List, processArr } = this.getDiffCheckData(name, typeId, process.children.get(typeId));
            //  同一版本的相同进程若存在多条不同防伪冒级别，提供选择
            if (diffCheckMd5es.length) {
              map.set(this.buildKey(name, typeId), { processName: name, typeId, diffCheckMd5es, diffCheckMd5List, processArr });
            } else {
              allMergeList.push(this.formatProcessMergeResult(name, typeId, diffCheckMd5List, processArr));
              // this.setProcessMergeResult(name, typeId, diffCheckMd5List, processArr);
            }
          })
        })
        if (map.size) {
          this.allMergeList = allMergeList;
          this.diffCheckMd5ProcessDlg.show(map);
        } else {
          this.submitting = false;
        }
      } else {
        const process = this.processList.find(t => t.processName === processName);
        const { diffCheckMd5es, diffCheckMd5List, processArr } = this.getDiffCheckData(processName, typeId, process.children.get(typeId));
        //  同一版本的相同进程若存在多条不同防伪冒级别，提供选择
        if (diffCheckMd5es.length) {
          const map = new Map();
          map.set(this.buildKey(processName, typeId), { processName, typeId, diffCheckMd5es, diffCheckMd5List, processArr });
          this.diffCheckMd5ProcessDlg.show(map)
          return;
        }
        //  该进程的合并状态设置为完成
        this.setProcessMergeResult(processName, typeId, diffCheckMd5List, processArr);
        this.submitting = false;
      }
    },
    /**
     * 构建Key
     * @param processName
     * @param typeId
     * @returns {string}
     */
    buildKey(processName, typeId) {
      const label = this.getTypeLabel(typeId);
      return `${processName}(${label || typeId})`
    },
    /**
     * 根据程序Id获取程序名称
     * @param typeId
     * @returns {null|*}
     */
    getTypeLabel(typeId) {
      const type = this.typeTreeData.find(t => t.dataId == typeId);
      if (type) {
        return type.label;
      }
      return null;
    },
    /**
     * 解析Key
     * @param key
     */
    analyze(key) {
      const list = key.split('(');
      let processName = ''
      for (let i = 0; i < list.length - 1; i++) {
        processName += list[i];
      }
      return processName;
    },
    /**
     * 格式化成合并接口数据
     * @param processName
     * @param typeId
     * @param diffCheckMd5List
     * @param processArr
     * @returns {{mergeProcessMap: {}, processNames: *[], typeId, checkMd5: (number)}}
     */
    formatProcessMergeResult(processName, typeId, diffCheckMd5List, processArr) {
      const data = processArr[0]
      const map = {};
      if (data.checkMd5) {
        data.relaFingerPrint = diffCheckMd5List.filter(t => !!t.checkMd5 === !!data.checkMd5) || []
      }
      map[processName] = processArr;
      return {
        processNames: [processName],
        typeId: typeId,
        checkMd5: data.checkMd5 ? 1 : 0,
        mergeProcessMap: map
      }
    },
    /**
     * 设置进程合并结果
     * @param processName         进程名称
     * @param typeId              程序类别
     * @param diffCheckMd5List    存储存在冲突的进程特征信息（存在多条不同防伪冒级别的进程信息）
     * @param processArr          仅保留1条进程信息，代表防伪冒进程信息，用于构建进程信息
     */
    async setProcessMergeResult(processName, typeId, diffCheckMd5List, processArr) {
      const processMerge = this.processList.find(t => t.processName === processName);
      await mergeProcess(this.formatProcessMergeResult(processName, typeId, diffCheckMd5List, processArr)).then(async res => {
        this.mergeProcessMap.set(typeId + processName, 1);
        if (res.data.mergeClash) {
          const data = res.data.exitsProcessMap[processName]
          processMerge.children.set(typeId, data ? this.buildProcessMergeResult(processName, [data]) : []);
        } else {
          processMerge.children.set(typeId, this.buildProcessMergeResult(processName, res.data.mergeProcessMap[processName]));
        }
        //  设置完成状态
        this.setProcessSuccessStatus(processMerge, typeId);
      });
    },
    /**
     * 设置进程完成状态
     * @param processMerge
     * @param typeId
     */
    setProcessSuccessStatus(processMerge, typeId) {
      processMerge.successNum++
      processMerge.successStatus.push(typeId)
      processMerge.type = processMerge.successNum === processMerge.children.size ? 'success' : processMerge.type
      this.allIsMerge = this.mergeProcessMap.size === this.needMergeNumber;
    },
    /**
     * 构建进程合并后的结果
     * @param processName
     * @param diffCheckMd5List
     * @returns {*}
     */
    buildProcessMergeResult(processName, diffCheckMd5List) {
      diffCheckMd5List.forEach(t => {
        t.dataId = 'T' + t.id
        if (t.checkMd5) {
          t.children = t.relaFingerPrint
          t.children && t.children.forEach(d => {
            d.dataId = d.id
          })
        }
      })
      return diffCheckMd5List;
    },
    /**
     * 不同防伪冒级别选择的回调
     * @param map  返回该进程待添加的防伪冒进程信息
     */
    diffCheckSubmit(map) {
      //  该进程的合并状态设置为完成
      map.forEach((data, key) => {
        const { diffCheckMd5List, processArr } = data;
        const processName = this.analyze(key);
        this.allMergeList.push(this.formatProcessMergeResult(processName, processArr[0].typeId, diffCheckMd5List, processArr))
      })
      if (this.allMergeList.length) {
        batchMergeProcess(this.allMergeList).then(res => {
          if (res.data) {
            res.data.forEach(item => {
              const processName = item.processNames[0]
              const processMerge = this.processList.find(t => t.processName === processName);
              this.mergeProcessMap.set(item.typeId + processName, 1);
              if (item.mergeClash) {
                const data = item.exitsProcessMap[processName]
                processMerge.children.set(item.typeId, data ? this.buildProcessMergeResult(processName, [data]) : []);
              } else {
                processMerge.children.set(item.typeId, this.buildProcessMergeResult(processName, item.mergeProcessMap[processName]));
              }
              //  设置完成状态
              this.setProcessSuccessStatus(processMerge, item.typeId);
            })
          }
        })
      }
      this.submitting = false;
    },
    closeFuc() {
      this.submitting = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.el-timeline {
  padding: 0 0 0 1px;
  >>>.el-timeline-item__tail {
    border-left: 2px solid #909399;
  }
  >>>.el-timeline-item__timestamp {
    color: black;
  }
}

</style>
