<template>
  <div>
    <FormItem label-width="0px">
      <el-checkbox v-model="temp.checkMd5" :disabled="!canSetPropertyMd5() && !canSetQuicklyMd5() && validMd5" :true-label="1" :false-label="0">
        {{ $t('pages.supportMd5') }}
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content" style="width: 500px;">{{ $t('pages.appGroup_text18') }}<br/></div>
          <i class="el-icon-info" />
        </el-tooltip>
      </el-checkbox>
    </FormItem>
    <FormItem v-if="temp.checkMd5 == 1 && temp.osType == 1" :label="$t('pages.checkMd5Label')" prop="md5Level">
      <el-radio-group v-model="temp.md5Level">
        <el-radio :disabled="!canSetPropertyMd5() && validMd5" :label="3">{{ $t('pages.md5LevelMap3') }}
          <el-tooltip class="item" effect="dark" content="" placement="bottom">
            <div slot="content">{{ $t('pages.appGroup_text20') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
        <el-radio :disabled="!canSetQuicklyMd5() && validMd5" :label="1">{{ $t('pages.md5LevelMap1') }}
          <el-tooltip class="item" effect="dark" content="" placement="bottom">
            <div slot="content">{{ $t('pages.appGroup_text21') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
        <el-radio v-if="!disableProductMd5" :disabled="(temp.productMd5=='' || temp.productMd5==null) && validMd5" :label="4">
          {{ $t('pages.md5LevelMap4') }}
        </el-radio>
        <!--<el-radio :label="2">超级严格</el-radio>-->
      </el-radio-group>
    </FormItem>
  </div>
</template>

<script>

import { canSetPropertyMd5, canSetQuicklyMd5 } from '@/utils/fingerprint'

export default {
  name: 'OpenFingerFormItem',
  components: { },
  directives: { },
  props: {
    validMd5: { // 是否验证可以设置指纹算法
      type: Boolean,
      default: true
    },
    disableProductMd5: { // 是否禁用“产品名称”防伪冒
      type: Boolean,
      default: true
    },
    temp: {
      type: Object,
      default: function() {
        return {
          checkMd5: 0,
          md5Level: 3 // 进程验证指纹的级别
        }
      }
    }
  },
  data() {
    return { }
  },
  created() {
  },
  activated() {
  },
  methods: {
    canSetPropertyMd5() {
      return canSetPropertyMd5(this.temp)
    },
    canSetQuicklyMd5() {
      return canSetQuicklyMd5(this.temp)
    }
  }
}
</script>
