<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="$t('pages.appLibrary')"
    :remark="$t('pages.appGroup_text13')"
    :visible.sync="dlgVisible"
    width="850px"
    @dragDialog="handleDrag"
  >
    <div class="toolbar">
      <app-select-table ref="appSelectTable" :height="400" :multiple="multiple" :multiple-group="multipleGroup" @single-select="singleSelectApp"/>
      <slot name="additionalConfig"/>
    </div>
    <div slot="footer" class="dialog-footer">
      <link-button btn-type="primary" btn-style="float: left" :menu-code="'A58'" :link-url="'/system/baseData/appGroup'" :btn-text="$t('pages.maintainInfo', { info : $t('pages.appLibrary') })" :before-click="beforeClick"/>
      <el-button v-if="multiple" type="primary" :loading="submitting" @click="handleSelect()">
        <slot name="btnText">
          {{ $t('pages.addSelectedApp') }}
        </slot>
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import AppSelectTable from '@/views/system/baseData/appLibrary/appSelectTable'

export default {
  name: 'AppSelectDlg',
  components: { AppSelectTable },
  props: {
    appendToBody: { type: Boolean, default: false },
    multipleGroup: { type: Boolean, default: true },
    addType: { type: String, default: '' }, // 用于程序版本限制区分导入的是起始版本还是截止版本
    multiple: { type: Boolean, default: true },
    singleImport: { type: Boolean, default: false }, //  只能导入单个程序
    validateFunc: { type: Function, default: () => { return true } }
  },
  data() {
    return {
      dlgVisible: false,
      submitting: false
    }
  },
  computed: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.$nextTick(() => {
        this.$refs.appSelectTable.show()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      this.$refs.appSelectTable.getSelectedDatas().then(resp => {
        if (resp.length === 0) {
          this.$message({
            message: this.$t('pages.processStgLib_addSelectedProcessNameHint'),
            type: 'error',
            duration: 2000
          })
          return
        }
        if (this.singleImport && resp.length > 1) {
          this.$message({
            message: '只能选中一个程序',
            type: 'error',
            duration: 2000
          })
          return
        }
        if (this.validateFunc && !this.validateFunc()) {
          return;
        }
        if (this.addType) {
          const data = { type: this.addType, info: resp }
          this.$emit('select', data)
          this.dlgVisible = false
        } else {
          this.$emit('select', resp)
          this.dlgVisible = false
        }
      })
    },
    beforeClick() {
      this.dlgVisible = false
      this.$emit('toAppLibraryFlag')
    },
    singleSelectApp(data) {
      if (this.addType) {
        this.$emit('select', { type: this.addType, info: data })
        this.dlgVisible = false
      } else {
        this.$emit('select', data)
        this.dlgVisible = false
      }
    }
  }
}
</script>
