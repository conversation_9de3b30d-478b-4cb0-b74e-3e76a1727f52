<template>
  <el-container>
    <el-aside width="210px">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :height="height + 40"
        :multiple="multipleGroup"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="treeNodeClick"
      />
    </el-aside>
    <el-main>
      <div class="toolbar">
        <el-row>
          <el-col :span="9"><label style="font-size: 15px;"></label></el-col>
          <el-col :span="15">
            <div style="float: right;">
              <el-input v-model="query.processName" v-trim clearable :placeholder="$t('table.processName')" style="width: 225px;" @keyup.enter.native="handleFilter"/>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <grid-table
        ref="appInfoList"
        :height="height"
        :col-model="colModel"
        :multi-select="multiple"
        :row-data-api="rowDataApi"
        is-saved-selected
        saved-selected-prop="processName"
        :default-sort="{ prop: 'processName' }"
        :page-sizes="[ 20, 50, 100, 500, 1000 ]"
        pager-small
      />
    </el-main>
  </el-container>
</template>

<script>
import { getTypeTree } from '@/api/behaviorManage/application/appVersion'
import { getSoftwarePage, listSoftwareInfo } from '@/api/behaviorManage/application/appGroup'

export default {
  name: 'AppSelectTable',
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    height: { type: Number, default: 420 },
    multipleGroup: { type: Boolean, default: true },
    //  列表是否支持多选
    multiple: {
      type: Boolean, default: true
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName', width: '110', sort: 'custom' },
        { prop: 'classId', label: 'sourceGroup', width: '110', sort: 'custom', formatter: this.classNameFormat },
        { prop: 'productName', label: 'productName', width: '110', sort: 'custom' },
        { prop: 'productVersion', label: 'productVersion', width: '110', sort: 'custom' },
        { label: 'remark', width: '200', formatter: this.otherInfoFormat },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: this.multiple,
          buttons: [
            { label: 'addSelectedApp', click: this.singleSelectApp }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        processName: '',
        classId: undefined,
        typeId: undefined,
        osType: this.osType
      },
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.appLibrary'), parentId: '', children: [] }],
      defaultExpandedKeys: ['G0']
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    }
  },
  created() {
    this.show()
  },
  activated() {
  },
  methods: {
    show() {
      this.query.typeId = undefined
      this.query.processName = null
      this.query.classId = undefined
      this.loadGroupTree()
      this.$nextTick(() => {
        this.softTable && this.softTable.clearSaveSelection()
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNodes()
      })
    },
    treeNodeClick: function(data, node, element) {
      this.query.classId = data.dataId;
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      const newOption = Object.assign(this.query, option, { queryPropertyMd5: true })
      return getSoftwarePage(newOption)
    },
    loadGroupTree: function() {
      getTypeTree().then(respond => {
        const treeData = JSON.parse(JSON.stringify(this.treeData))
        treeData[0].children = []
        treeData[0].children.push({ id: 'G-1', dataId: '-1', label: this.$t('pages.ungrouped'), parentId: '0' })
        respond.data && respond.data.forEach(item => {
          treeData[0].children.push(item)
        })
        if (JSON.stringify(treeData) != JSON.stringify(this.treeData)) {
          this.treeData = treeData
        }
      })
    },
    async getSelectedDatas() {
      const datas = this.softTable.getSelectedDatas()
      if (this.multipleGroup) { // 说明支持选择类型
        const groupNodes = this.$refs['groupTree'].getCheckedNodes()
        const groupIds = []
        groupNodes.forEach(data => groupIds.push(data.dataId))
        if (groupIds.length > 0) {
          await listSoftwareInfo({ classIds: groupIds.join(','), queryPropertyMd5: true }).then(resp => {
            datas.splice(0, 0, ...resp.data.items)
          })
        }
      }
      return datas
    },
    otherInfoFormat(row, data) {
      let msg = ''
      if (row.fileDescription) {
        msg += msg.length > 0 ? ';  ' : ''
        msg += this.$t('table.fileDescription') + ' : ' + row.fileDescription
      }
      if (row.companyName) {
        msg += msg.length > 0 ? ';  ' : ''
        msg += this.$t('table.companyName') + ' : ' + row.companyName
      }
      if (row.originalFilename) {
        msg += msg.length > 0 ? ';  ' : ''
        msg += this.$t('table.originalFilename') + ' : ' + row.originalFilename
      }
      if (row.softSign) {
        msg += msg.length > 0 ? ';  ' : ''
        msg += this.$t('table.softSign') + ' : ' + row.softSign
      }
      return msg
    },
    classNameFormat(row, data) {
      if (!row.classId || row.classId == 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.treeData, row.classId)
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    singleSelectApp(row) {
      this.$emit('single-select', row);
    }
  }
}
</script>
