<template>
  <div :style="outerStyle">
    <common-import-table
      ref="importTable"
      :max-height="210"
      auto-height
      :formable="formable"
      :delete-disabled="!deleteable"
      :col-model="urlColModel"
      :row-data-api="loadUrlTableApi"
      :row-no-label="$t('table.keyId')"
      :search-info-prompt-message="$t('pages.url_text1')"
      :elg-title="$t('pages.importUrlLibrary')"
      :group-root-name="$t('pages.urlLibrary')"
      :import-confirm-button-name="$t('pages.addUrl')"
      :import-search-info="$t('pages.websiteNameOrURL')"
      :group-title="$t('pages.url_group_title')"
      :text-map="urlTextMap"
      :import-load-group-tree="getTreeNode"
      :import-row-data-api="getInfoList"
      :import-col-model="urlColModel"
      :create-group="createUrlGroup"
      :update-group="updateUrlGroup"
      :delete-group="deleteUrlGroup"
      :count-by-group="countUrlByGroupId"
      :get-group-by-name="getUrlGroupByName"
      :delete-lib-data="deleteUrl"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteUrlGroup"
      :move-group-to-other="moveGroupToOther"
      :get-list-by-ids="getByIds"
      :get-list-by-group-ids="listUrlByGroupId"
      :handle-form-func="handleFormFunc"
      @resetForm="resetForm"
      @change="changeRowDatas"
      @selectionChangeEnd="selectionChangeEnd"
      @changeGroupAfter="changeGroupAfter"
    >
      <template slot="form-content">
        <Form
          ref="urlDataForm"
          :rules="urlRules"
          :model="urlTemp"
          label-position="right"
          label-width="100px"
          style="width: 500px; margin-left:20px;"
        >
          <FormItem :label="$t('pages.websiteName')" prop="name">
            <el-input v-model="urlTemp.name" v-trim :maxlength="60"/>
          </FormItem>
          <FormItem :label="$t('pages.websiteUrl')" prop="address">
            <el-input v-model="urlTemp.address" v-trim :maxlength="60" :placeholder="$t('pages.urlLibrary_text1')"/>
          </FormItem>
          <FormItem :label="$t('pages.groupType')" prop="groupId">
            <el-row>
              <el-col :span="!isImport ? 21 : 24">
                <el-select v-model="urlTemp.groupId" filterable :placeholder="$t('text.select')">
                  <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
                </el-select>
              </el-col>
              <el-col v-show="!isImport" style="padding-top:1px" :span="3">
                <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                  <svg-icon icon-class="add" />
                </el-button>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem :label="$t('pages.remark')" prop="remark">
            <el-input v-model="urlTemp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit/>
          </FormItem>
        </Form>
      </template>
    </common-import-table>
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      append-to-body
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getUrlGroupByName"
      :add-func="createUrlGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import {
  countUrlByGroupId, createUrl, createUrlGroup, deleteGroupAndData, deleteUrl, deleteUrlGroup,
  getByIds, getTreeNode, getUrlByAdress, getUrlGroupByName, getUrlList,
  listUrlByGroupId, moveGroupToOther, updateUrl, updateUrlGroup
} from '@/api/system/baseData/urlLibrary'
import CommonImportTable from '@/views/system/baseData/groupImportList/commonImportTable'
import EditGroupDlg from '@/views/common/editGroupDlg'
import { findNodeLabel } from '@/utils/tree'

export default {
  name: 'UrlImportCombination',
  components: { EditGroupDlg, CommonImportTable },
  props: {
    value: { type: Array, default: null },
    formable: { type: Boolean, default: true }, // 能否提交表单
    outerStyle: { type: Object, default: null }
  },
  data() {
    return {
      urlColModel: [
        { prop: 'name', label: 'websiteName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'websiteUrl', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      isImport: false,
      deleteable: false,
      rowDatas: [],
      checkKeys: [],
      // 受控网页表单属性
      urlTemp: {},
      urlTextMap: {
        update: this.i18nConcatText(this.$t('table.websiteUrl'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('table.websiteUrl'), 'create')
      },
      treeSelectNode: [],
      urlRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_enterUrl'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    rowDatas: {
      handler(values) {
        this.$refs['importTable'] && this.$refs['importTable'].handleSearch()
        this.checkKeys = [...(values || []).map(data => data.id)]
        this.$emit('input', this.checkKeys)
        this.$emit('change', this.checkKeys, values)
      },
      deep: true
    },
    value: {
      handler(val) {
        if (val == this.checkKeys) { return }
        this.queryByIds((val || []).join(','))
      },
      immediate: true
    }
  },
  created() {
    this.listTreeNode()
  },
  methods: {
    getByIds,
    getTreeNode,
    createUrlGroup,
    updateUrlGroup,
    deleteUrlGroup,
    getUrlGroupByName,
    countUrlByGroupId,
    deleteUrl,
    deleteGroupAndData,
    moveGroupToOther,
    listUrlByGroupId,
    addressValidator(rule, value, callback) {
      getUrlByAdress({ url: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.urlTemp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    groupFormatter(row, data) {
      row.groupName = row.groupName || this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    //  导入方法的row-data-api
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getUrlList(searchQuery)
    },
    loadUrlTableApi(opt) {
      const urls = this.rowDatas || []
      const { searchInfo } = opt
      return new Promise(resolve => {
        resolve({
          code: 20000,
          data: searchInfo && searchInfo.length > 0 ? urls.filter(item => {
            return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1) ||
              (item.address && item.address.toLowerCase().indexOf(searchInfo) !== -1) ||
              (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo) !== -1)
          }) : urls
        })
      })
    },
    handleUpdate(row, data) {
      this.$refs['importTable'] && this.$refs['importTable'].handleUpdate()
      this.urlTemp = Object.assign({}, this.urlTemp, row)
      this.urlTemp.groupId = this.urlTemp.groupId ? this.urlTemp.groupId + '' : '';
    },
    listTreeNode() {
      getTreeNode().then(res => {
        this.treeSelectNode = res.data || []
      })
    },
    handleFormFunc(status) {
      return new Promise((resolve, reject) => {
        this.$refs['urlDataForm'].validate(valid => {
          if (!valid) {
            reject(valid)
            return
          }
          this.urlTemp.groupName = findNodeLabel(this.treeSelectNode, this.urlTemp.groupId, 'dataId')
          const requestFunc = status === 'create' ? createUrl : updateUrl
          const ids = this.rowDatas.map(data => data.id)
          requestFunc(this.urlTemp).then(res => {
            if (res.data) {
              if (!this.isImport) {
                status === 'create' && ids.push(res.data.id)
                getByIds({ ids: ids.join(',') }).then(res => {
                  this.rowDatas.splice(0, this.rowDatas.length, ...res.data)
                })
              } else {
                if (this.$refs['importTable']) {
                  const importTableDlg = this.$refs['importTable'].$refs['importTableDlg']
                  importTableDlg && importTableDlg.justRefreshTableData()
                  if (status === 'update' && this.rowDatas.some(item => item.id == this.urlTemp.id)) {
                    getByIds({ ids: this.rowDatas.map(data => data.id).join(',') }).then(res => {
                      this.rowDatas.splice(0, this.rowDatas.length, ...res.data)
                    })
                  }
                }
              }
            }
            resolve({ code: 20000, data: this.rowDatas })
          }).catch(error => {
            reject(error)
          })
        })
      })
    },
    resetForm(isImport, groupId) {
      this.urlTemp = { name: '', address: '', groupId: groupId, remark: '' }
      this.isImport = isImport
      this.$refs['urlDataForm'].clearValidate()
    },
    handleTypeCreate() {
      this.listTreeNode()
      this.$refs['createGroupDlg'].handleCreate();
    },
    createGroupAddEnd(row) {
      this.listTreeNode();
      this.urlTemp.groupId = row.id + ''
    },
    changeRowDatas(rowDatas) {
      if (rowDatas && Array.isArray(rowDatas)) {
        this.rowDatas.splice(0, this.rowDatas.length, ...rowDatas)
      }
    },
    queryByIds(ids) {
      getByIds({ ids }).then(res => {
        this.rowDatas.splice(0, this.rowDatas.length, ...res.data)
      })
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = (rowDatas || []).length > 0
    },
    changeGroupAfter() {
      this.listTreeNode()
    }
  }
}
</script>

<style scoped>

</style>
