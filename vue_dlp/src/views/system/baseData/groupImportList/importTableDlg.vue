<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="elgTitle"
      :visible.sync="visible"
      width="850px"
    >
      <span v-if="promptMessage != null" style="color:blue;">{{ promptMessage }}</span>
      <el-container style="margin-top: 5px">
        <el-aside v-if="treeAble" width="210px">
          <tree-menu
            ref="groupTree"
            :height="groupTreeHeight"
            node-key="dataId"
            :multiple="treeMultiple"
            :default-expand-all="true"
            :data="allGroupTreeData"
            :render-content="renderContent"
            @node-click="groupTreeNodeCheckChange"
          />
        </el-aside>
        <el-main>
          <div>
            <div class="toolbar">
              <el-button v-if="addAndEditAble" type="primary" icon="el-icon-plus" size="mini" @click="handleCreate()">
                {{ $t('button.add') }}
              </el-button>
              <el-button v-if="addAndEditAble" icon="el-icon-delete" size="mini" :disabled="!deleteAble" @click="handleDelete">
                {{ $t('button.delete') }}
              </el-button>
              <!-- <div style="float: right; margin-top: -5px;"> -->
              <div :class="addAndEditClass">
                <slot name="search-left"/>
                <el-tooltip class="item" effect="dark" :content="searchInfoName" placement="top-start">
                  <el-input v-model="query.searchInfo" v-trim clearable :placeholder="searchInfoName" style="width: 150px;" @keyup.enter.native="handleSearch" />
                </el-tooltip>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <div v-if="checkAllAble" class="toolbar">
              <el-checkbox v-model="checkAll" @change="handleCheckAllChange">{{ $t('pages.checkAllPagesData') }}</el-checkbox>
            </div>
            <div :style="{height:gridTableHeight}">
              <grid-table
                ref="infoList"
                :height="gridTableHeight"
                pager-small
                :row-no-label="rowNoLabel"
                :col-model="colModel"
                :row-data-api="loadList"
                :after-load="afterLoad"
                :is-saved-selected="!checkAllAble"
                :selected-data-label-formatter="selectedDataLabelFormatter"
                @selectionChangeEnd="selectionChangeEnd"
                @select="selectRemoteData"
                @select-all="selectAllRemoteData"
              />
              <div v-if="checkTrustSoftAble">
                <Form ref="checkTypeForm" :rules="checkTypeRules" :model="checkTypeForm" label-position="right" label-width="80px">
                  <FormItem :label="$t('table.flagList')" prop="checkType">
                    <el-select v-model="checkTypeForm.checkType" multiple clearable style="margin-top: 5px;width: 500px;">
                      <el-option v-for="item in verifyModelTreeData" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                  </FormItem>
                </Form>
              </div>
            </div>
          </div>
          <slot/>
        </el-main>
      </el-container>
      <div v-if="showOpt" style="display: flex; margin-top: 20px; margin-left: 30px">
        <label>允许操作类型</label>
        <el-checkbox-group v-model="optTypeList" style="margin-left: 10px; margin-top: -2px">
          <el-checkbox v-for="(item, index) in optTypeOptions" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <div v-if="showRule">
        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <el-radio-group v-model="labelDec" style="margin-left: 0">
          <el-radio :label="0">{{ $t('pages.allMatch1') }}</el-radio>
          <el-radio :label="1">
            <i18n path="pages.labelPermissionConfig">
              <span slot="n">
                <el-input-number
                  v-model="hitLabelContentCount"
                  :controls="false"
                  :step="1"
                  :precision="0"
                  :min="1"
                  :max="999"
                  :disabled="labelDec === 0"
                  step-strictly
                  style="width: 60px; margin: 0 4px"
                />
              </span>
            </i18n>
            <div v-if="labelDec === 1 && !hitLabelContentCount" style="position: absolute; left: 100px" class="error_style">{{ $t('text.cantNull') }}</div>
          </el-radio>
        </el-radio-group>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <el-radio-group v-model="hitRule" style="margin-left: 0">
          <el-radio :label="0">{{ $t('pages.executeInRule') }}</el-radio>
          <br>
          <el-radio :label="1">{{ $t('pages.executeOutRule') }}</el-radio>
        </el-radio-group>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <el-checkbox-group v-model="labelResList" style="display: inline-block" @change="handleLabelTypeChange">
          <el-checkbox :label="3">{{ $t('pages.burnMsg5') }}</el-checkbox>
          <el-checkbox :disabled="labelResList.indexOf(3) == -1" :label="12">{{ $t('pages.burnMsg7') }}</el-checkbox>
        </el-checkbox-group>
        <el-checkbox v-model="labelInfoAlarm" :true-label="1" :false-label="0" style="margin-left: 30px">{{ $t('pages.alarmOfOutgoing') }}</el-checkbox>
      </div>
      <div slot="footer" class="dialog-footer">
        <link-button
          v-show="!addAndEditAble"
          btn-type="primary"
          btn-style="float: left; height: 30px; padding: 0 10px;"
          :menu-code="'A5P'"
          :link-url="'/system/baseData/printerLibrary'"
          :btn-text="$t('pages.maintainPrinter')"
          :before-click="beforeClick"
        />
        <slot name="footer-slot"/>
        <el-tooltip v-if="checkTrustSoftAble" class="item" effect="dark" :content="$t('pages.outgoingProcess_text10')" placement="top-end">
          <el-button type="primary" :loading="submitting" @click="confirmCheckTrustSoftAble">
            {{ confirmButtonName }}
          </el-button>
        </el-tooltip>
        <el-button v-else type="primary" :loading="submitting" @click="confirm">
          {{ confirmButtonName }}
        </el-button>
        <el-button @click="cancel">
          {{ cancelButtonName }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 删除弹窗 -->
    <delete-relation-stg-dlg
      ref="deleteRelationStg"
      :append-to-body="appendToBody"
      :data="deleteDlgData"
      :col-model="deleteColModel"
      :delete="deleteData"
      :row-no-label="$t('table.keyId')"
      @deleteSuccessAfter="deleteSuccessAfter"
    />

    <!-- 修改分组 -->
    <edit-group-dlg
      ref="editGroupDlg"
      :append-to-body="appendToBody"
      :title="groupTitle"
      :group-label="groupLabel"
      :rules-group-label="rulesGroupLabel"
      :group-tree-data="groupTreeData"
      :extra-param="invokeGroupParam"
      :add-func="createGroup"
      :update-func="updateGroup"
      :delete-func="deleteGroup"
      :move-func="moveGroup"
      :edit-valid-func="getGroupByName"
      @addEnd="editGroupAddEnd"
      @updateEnd="editGroupUpdateEnd"
      @deleteEnd="editGroupDeleteEnd"
      @moveEnd="moveGroupEnd"
    />

    <!-- 删除分组 -->
    <delete-group-dlg
      v-if="deleteElgAble"
      ref="deleteGroupDlg"
      :append-to-body="appendToBody"
      :prompt-message="deleteGroupPromptMessage"
      :title="deleteTitle"
      :dlg-title="delGroupElgTitle"
      :select-tree-data="groupTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteGroupFunc"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="editGroupDeleteEnd"
      @deleteEnd="editGroupDeleteEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>
  </div>
</template>

<script>
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg';
import DeleteRelationStgDlg from '@/views/common/deleteRelationStgDlg'

export default {
  name: 'ImportTableDlg',
  components: { DeleteGroupDlg, EditGroupDlg, DeleteRelationStgDlg },
  props: {
    treeAble: {
      type: Boolean,
      default: true
    },
    //  是否启用全选按钮
    checkAllAble: { type: Boolean, default: false },
    // 是否显示表格的新增与修改按钮
    addAndEditAble: { type: Boolean, default: true },
    // 是否开启分组数的编辑功能
    groupEdit: { type: Boolean, default: true },
    //  弹窗名称
    elgTitle: { type: String, default() { return this.$t('pages.importData') } },
    //  左边类型库名称
    groupRootName: { type: String, default() { return this.$t('pages.typeLibrary') } },
    //  父组件已存在的数据(父组件已存在的列表数据
    exitsListData: { type: Array, default() { return [] } },
    // 处理分组额外参数
    invokeGroupParam: { type: Object, default() { return {} } },
    // 搜索额外传输
    invokeQuery: { type: Object, default() { return {} } },
    list: { //  查询右边列表数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    loadGroupTree: { //  加载类型数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    handleCreateGroup: { //  创建分组节点,父级自定义弹窗
      type: Function,
      default: null
    },
    handleDeleteGroup: { //  删除分组节点,父级自定义弹窗
      type: Function,
      default: null
    },
    handleUpdateGroup: { //  修改分组节点，父级自定义弹窗
      type: Function,
      default: null
    },
    handleCreateElg: { //  添加列表数据弹窗
      type: Function,
      default: null
    },
    countByGroup: { // 统计分组数量，判断分组是否存在
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    create: { // 添加列表数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    batchCreate: { // 批量添加列表数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    update: { // 修改列表数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    delete: { // 删除列表数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    importFunc: { // 导入列表数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    createGroup: { // 添加分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    updateGroup: { // 修改分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    deleteGroup: { // 删除分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    moveGroup: {  //  移动分组的数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getGroupByName: { // 根据分组名称获取分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    //  左边树是否多选
    treeMultiple: {
      type: Boolean,
      default: true
    },
    //  列表分页
    colModel: {
      type: Array,
      default: function() {
        return []
      }
    },
    //  行号
    rowNoLabel: {
      type: String,
      default: null
    },
    // 已选择列表展示的 label 的格式化方法
    selectedDataLabelFormatter: {
      type: Function,
      default: null
    },
    // 查询条件名称
    searchInfoName: {
      type: String,
      default: function() {
        return this.$t('pages.searchKeyword')
      }
    },
    //  确认按钮名称
    confirmButtonName: {
      type: String,
      default: function() {
        return this.$t('button.insert')
      }
    },
    //  取消按钮名称
    cancelButtonName: {
      type: String,
      default: function() {
        return this.$t('button.cancel')
      }
    },
    //  提示信息
    promptMessage: {
      type: String,
      default: null
    },
    //  未选中数据弹窗的提示信息
    notSelectedPromptMessage: {
      type: String,
      default: function() {
        return this.$t('pages.notSelectedPromptMessage');
      }
    },
    //  删除分组弹窗标题
    delGroupElgTitle: {
      type: String,
      default: function() {
        return this.$t('pages.data')
      }
    },
    deleteGroupAndData: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    deleteGroupFunc: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    moveGroupToOther: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    //  根据分组id获取ids
    getListByGroupIds: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    deleteElgAble: {
      type: Boolean,
      default: false
    },
    //  分组的id名称
    groupNodeKey: {
      type: String,
      default: function() {
        return 'groupId'
      }
    },
    //  树的根节点dataId的值
    treeRootDataId: {
      type: String,
      default: '0'
    },
    //  未分组节点的dataId，兼容 服务器白名单设置页面
    notGroupDataId: {
      type: String,
      default: null
    },
    //  分组标题
    groupTitle: {
      type: String,
      default: ''
    },
    //  输入分组的名称
    groupLabel: {
      type: String,
      default() {
        return this.$t('form.groupName')
      }
    },
    //  分组提示语言
    rulesGroupLabel: {
      type: String,
      default() {
        return this.$t('valid.requireGroupName')
      }
    },
    //  删除分组弹窗的提示
    deleteGroupPromptMessage: {
      type: String,
      default: ''
    },
    deleteTitle: {
      type: String,
      default: '删除分组'
    },
    //  删除数据弹窗
    deleteColModel: {
      type: Array,
      default() {
        return []
      }
    },
    gridTableHeight: {
      type: Number,
      default: 360
    },
    // 云平台外发文件信任软件管理需要支持从软件白名单库导入，信任软件需要选择是否需要校验方式，此参数用来控制校验参数是否显示
    checkTrustSoftAble: {
      type: Boolean,
      default: false
    },
    //  查询该数据id集所引用的策略数据
    getRelationStgByIds: {
      type: Function,
      default: null
    },
    // Dialog 自身是否插入至 body 元素上
    appendToBody: {
      type: Boolean,
      default: false
    },
    groupTreeHeight: {
      type: Number,
      default: 400
    },
    // 是否显示允许操作类型配置，默认不显示
    showOpt: {
      type: Boolean,
      default: false
    },
    // 允许操作类型
    optTypeOptions: {
      type: Array,
      default() {
        return []
      }
    },
    // 外发标签权限管控策略
    showRule: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteAble: false,
      visible: false,
      submitting: false,
      treeNodeType: 'G',
      allGroupTreeData: [],
      defaultGroupTreeData: [{ id: '0', dataId: this.treeRootDataId, label: this.groupRootName, parentId: '', children: [] }],
      groupTreeData: [],
      selectedGroupId: null,          // 当前展示的分组的id
      deleteDlgData: [],              // 存放删除弹窗中列表的数据
      checkAll: false,                // 勾选所有数据
      backupSelectedIds: [],          // 备份当前选中的id
      backupSelectedDatas: [],
      backupUnSelectedIds: [],        // 备份当前未选中的id
      tempOption: {},
      addAndEditClass: '',             // 表格查询按钮格式
      optTypeList: [],
      labelDec: 0,
      hitLabelContentCount: 1,
      hitRule: 0,
      labelResList: [],
      labelInfoAlarm: 0,
      valiPassFlag: true,
      // 云服务外发文件从软件白名单库导入时，需要选择软件校验类型
      checkTypeForm: {
        checkType: []
      },                   
      verifyModelTreeData: [
        { label: this.$t('table.szSoftName'), value: 1 },
        { label: this.$t('table.szSoftSig'), value: 2 },
        { label: this.$t('table.szSoftMd5'), value: 4 }
      ],
      checkTypeRules: {
        checkType: [
          { required: true, message: this.$t('components.required'), trigger: 'change' }
        ]
      }
    }
  },
  computed: {
  },
  watch: {
    groupTreeData(val) {
      this.loadAllGroupTree()
    }
  },
  created() {
    if (this.addAndEditAble) {
      this.addAndEditClass = 'showBtn'
    } else {
      this.addAndEditClass = 'noShowBtn'
    }
  },
  activated() {
    if (this.visible) {
      this.show()
    }
  },
  methods: {
    resetData() {
      this.optTypeList = []
      this.labelDec = 0
      this.hitLabelContentCount = 1
      this.hitRule = 0
      this.labelResList = []
      this.labelInfoAlarm = 0
      this.valiPassFlag = true
    },
    gridTable() {
      return this.$refs['infoList']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    show() {
      if (this.checkAllAble) {
        this.checkAll = false
        this.backupSelectedIds = [] // 备份当前选中的id
        this.backupSelectedDatas = []
        this.backupUnSelectedIds = [] // 备份当前未选中的id
      } else {
        this.gridTable() && this.gridTable().clearSelection()
      }
      if (this.checkTrustSoftAble) {
        // 云服务外发文件调用，见验证方式清空
        this.checkTypeForm = {
          checkType: []
        }
        this.$nextTick(() => {
          this.$refs.checkTypeForm.clearValidate()
        })
      }
      this.query[this.groupNodeKey] = undefined
      this.query.searchInfo = ''
      this.query.page = 1
      this.loadGroupTreeData()
      this.selectedGroupId = null
      this.optTypeList = []
      this.labelResList = []
      this.groupTree() && this.groupTree().clearFilter()
      this.groupTree() && this.groupTree().setCurrent(null)
      this.groupTree() && this.groupTree().clearSelectedNodes()
      this.resetData()
      this.visible = true
    },
    beforeClick() {
      this.visible = false
    },
    loadList: function(option) {
      const optionTemp = Object.assign(this.query, option)
      this.tempOption = option
      return this.list(optionTemp)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteAble = rowDatas && rowDatas.length > 0
    },
    loadAllGroupTree: function() {
      if (this.groupTreeData) {
        const treeData = JSON.parse(JSON.stringify(this.defaultGroupTreeData))
        treeData[0].label = this.groupRootName
        treeData[0].children = this.groupTreeData
        this.allGroupTreeData = treeData
        if (this.groupTreeData.length > 0) {
          this.treeNodeType = this.groupTreeData[0].group
        }
      }
    },
    renderContent(h, { node, data, store }) {
      if (this.groupEdit) {
        const iconShow = data.dataId != this.treeRootDataId
        //  表示根节点下的未分组节点
        const flag = this.notGroupDataId == null ? true : data.dataId != this.notGroupDataId
        return (
          <div class='custom-tree-node'>
            <span>{data.label}</span>
            <span class='el-ic'>
              <svg-icon v-show={!iconShow && flag} icon-class='add' title={this.$t('button.insert')} class='icon-space'
                on-click={r => this.handleGroupCreate(data)}/>
              <svg-icon v-show={iconShow && flag} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
                on-click={r => this.handleGroupUpdate(data)}/>
              <svg-icon v-show={iconShow && flag} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
                on-click={r => this.handleGroupDelete(data)}/>
            </span>
          </div>
        )
      } else {
        return (
          <div class='custom-tree-node'>
            <span>{data.label}</span>
          </div>
        )
      }
    },
    groupTreeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      if (checkedNode) {
        this.query[this.groupNodeKey] = checkedNode.data.dataId
      } else {
        this.query[this.groupNodeKey] = undefined
      }
      this.query.page = 1
      this.selectedGroupId = checkedNode.data.dataId || null

      if (this.query[this.groupNodeKey] == this.treeRootDataId) {
        this.query[this.groupNodeKey] = undefined
        this.selectedGroupId = null
      }
      this.$emit('groupChange', checkedNode.data.dataId)
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
    },
    handleSearch() {
      this.query.page = 1
      this.gridTable() && this.gridTable().execRowDataApi(Object.assign({}, this.query, this.invokeQuery))
    },
    handleCreate() {
      if (this.handleCreateElg != null) {
        this.handleCreateElg(this.selectedGroupId, true)
      }
    },
    handleAppTypeCreate(data) {
    },
    dataToTreeNode(data) {
      return {
        id: (this.treeNodeType || 'G') + data.id,
        type: this.treeNodeType || 'G',
        dataId: data.id.toString(),
        label: data.name,
        parentId: this.treeRootDataId
      }
    },
    deleteData(data) {
      return (data && data.ids !== '') ? this.delete({ ids: data.ids }) : new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
    },
    deleteSuccessAfter(ids) {
      this.gridTable().clearSelection()
      this.gridTable().deleteRowData(ids)
      this.$emit('submitDeleteEnd', ids)
    },
    handleDelete() {
      const toDeleteIds = this.gridTable().getSelectedIds()
      if (this.getRelationStgByIds) {
        this.getRelationStgByIds({ ids: toDeleteIds.join(',') }).then(res => {
          if (res.data && res.data.length > 0) {
            this.deleteDlgData = res.data
            this.$refs['deleteRelationStg'].show()
          } else {
            this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
              this.delete({ ids: toDeleteIds.join(',') }).then(respond => {
                this.gridTable().deleteRowData(toDeleteIds)
                this.gridTable().clearSelection()
                this.$emit('submitDeleteEnd', toDeleteIds)
                this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
              })
            }).catch(() => {})
          }
        })
        return;
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        this.delete({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable().deleteRowData(toDeleteIds)
          this.gridTable().clearSelection()
          this.$emit('submitDeleteEnd', toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    async confirmCheckTrustSoftAble() {
      if (this.checkTrustSoftAble) {
        this.$refs.checkTypeForm.validate((valid) => {
          if (valid) {
            this.confirm()
          }
        })
      } else {
        this.confirm()
      }
    },
    // 提交勾选的数据到父组件，这边遵循的原则是：分组+列表勾选的数据。分组勾选则视为分组下的所有数据都添加到父组件列表。
    async confirm() {
      // 选中的分组
      const groupList = this.groupTree() && this.groupTree().getCheckedNodes() || []
      // 选中的列表数据
      let list = []
      // 有勾选全部数据的多选框
      if (this.checkAllAble) {
        if (this.checkAll) {
          // 获取全部数据
          list = await this.getListAll()
          // 过滤掉未勾选的选的数据
          if (this.backupUnSelectedIds.length > 0) {
            const unSelectedIdMap = this.backupUnSelectedIds.reduce((result, cur) => { result[cur] = true; return result }, {})
            list = list.filter(item => !unSelectedIdMap[item.id])
          }
        } else {
          // 未勾选全选，直接获取表格勾选的数据
          list = this.gridTable().getSelectedDatas() || []
        }
      } else {
        // 没有勾选全部数据的多选框，说明使用内置的翻页勾选功能，直接获取表格勾选的数据
        list = this.gridTable().getSelectedDatas() || []
      }
      // 未勾选分组，且未勾选列表数据
      if (groupList.length === 0 && list.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.notSelectedPromptMessage,
          type: 'error',
          duration: 2000
        })
        return
      }
      if (this.labelDec === 1 && !this.hitLabelContentCount) {
        return
      }
      // 文档分级分类-手动打标签需校验允许操作类型
      if (this.showOpt) {
        if (!(this.optTypeList && this.optTypeList.length > 0)) {
          this.$message({
            title: this.$t('text.prompt'),
            message: '请选择允许操作类型',
            type: 'warning',
            duration: 3000
          })
          return
        }
      }
      // 勾选了分组
      if (groupList.length > 0) {
        // 勾选列表数据
        if (list.length > 0) {
          this.importTableSubmitEnd(3, groupList, list);
        } else {
          this.importTableSubmitEnd(2, groupList);
        }
      } else {
        // 未勾选分组
        this.importTableSubmitEnd(1, null, list);
      }
    },
    editGroupAddEnd(data) {
      //  分组内容发生改变时，父组件的分组也要发送编号
      this.$emit('changeGroupAfter')
      this.groupTree().addNode(this.dataToTreeNode(data))
    },
    editGroupUpdateEnd(data) {
      //  分组内容发生改变时，触发
      this.$emit('changeGroupAfter')
      this.groupTree().updateNode(this.dataToTreeNode(data))
    },
    editGroupDeleteEnd(dataId) {
      //  分组内容发生改变时，触发
      this.$emit('changeGroupAfter')
      // 移除节点
      this.groupTree().removeNode([dataId])
    },
    moveGroupEnd() {
      this.gridTable() && this.gridTable().execRowDataApi()
    },
    //  删除类型节点
    handleGroupDelete(data) {
      if (this.handleDeleteGroup != null) {
        //  节点数据
        this.handleDeleteGroup(data);
        return;
      }
      this.countByGroup(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.loadAllGroupTree();
          if (this.deleteElgAble) {
            this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.selectedGroupId })
          } else {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.deleteTypeFailPromptMessage'),
              type: 'warning',
              duration: 3000
            })
          }
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleGroupCreate(data) {
      if (this.handleCreateGroup != null) {
        this.handleCreateGroup(data);
        return;
      }
      this.$refs['editGroupDlg'].handleCreate(data.parentId || this.treeRootDataId)
    },
    handleGroupUpdate: function(data) {
      if (this.handleUpdateGroup != null) {
        this.handleUpdateGroup(data);
        return;
      }
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '') || ''
      }
      //  如果treeNodeType为空时， 可使用正则表达式解析分组id
      const valid = '^[0-9][0-9]*$';
      let i = 0;
      const str = data.split('');
      for (; i < str.length; i++) {
        if (valid.match(str[i])) {
          break;
        }
      }
      return data.substr(i);
    },
    refreshTableData() {
      this.selectedGroupId = null
      this.gridTable().clearRowData()
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
    },
    justRefreshTableData() {
      this.gridTable().clearRowData()
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
    },
    //  deleteGroupDlg的回调函数，指定的分组id
    arriveTreeNodeId(groupId) {
      this.loadGroupTreeData(groupId)
    },
    //  加载类型数据
    loadGroupTreeData(groupId) {
      this.loadGroupTree().then(respond => {
        this.groupTreeData = respond.data
        this.loadAllGroupTree()
        const _this = this
        this.$nextTick(() => {
          _this.query[this.groupNodeKey] = groupId || null
          _this.gridTable() && _this.gridTable().execRowDataApi(this.query)
        })
      })
    },
    cancel() {
      this.elgCancelAfter();
      this.visible = false
    },
    elgCancelAfter() {
      this.$emit('elgCancelAfter');
    },
    handleLabelTypeChange() {
      if (this.labelResList.indexOf(3) == -1 && this.labelResList.indexOf(12) > -1) {
        this.labelResList = []
      }
    },
    async importTableSubmitEnd(type, typeList, list) {
      if (this.checkTrustSoftAble) {
        // 如果是云服务外发文件的信任软件从软件白名单导入的，多一个通知验证结果的
        this.$emit('trustSoftTypeCheck', this.checkTypeForm.checkType)
      }
      const exitsIds = this.exitsListData.map(data => data.id)
      //  只添加列表中勾选的数据
      if (type === 1) {
        const newIds = this.getNewIds(list, exitsIds)
        if (this.showRule && this.labelDec === 1 && this.hitLabelContentCount > list.length) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.conditionTips1'),
            type: 'warning',
            duration: 3000
          })
          this.valiPassFlag = false
          return
        } else {
          this.valiPassFlag = true
        }
        //  返回需要查询的ids，将查询的结果返回给前端
        this.$emit('submitEnd', newIds, list, this.optTypeList, this.labelDec, this.hitRule, this.labelResList, this.hitLabelContentCount, this.labelInfoAlarm)
      //  添加分组数据
      } else if (type === 2) {
        await this.getListDataByGroupIds(typeList).then(res => {
          const newIds = this.getNewIds(res.data || [], exitsIds);
          const groupDataIds = res.data.map(data => data.id)
          if (this.showRule && this.labelDec === 1 && this.hitLabelContentCount > groupDataIds.length) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.conditionTips1'),
              type: 'warning',
              duration: 3000
            })
            this.valiPassFlag = false
            return
          } else {
            this.valiPassFlag = true
          }
          this.$emit('submitEnd', newIds, res.data, this.optTypeList, this.labelDec, this.hitRule, this.labelResList, this.hitLabelContentCount, this.labelInfoAlarm)
        });
      // 添加分组和列表勾选的数据
      } else if (type === 3) {
        await this.getListDataByGroupIds(typeList).then(res => {
          const newData = [...(res.data || []), ...list]
          const newIds = this.getNewIds(newData, exitsIds);
          if (this.showRule && this.labelDec === 1 && this.hitLabelContentCount > newData.length) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.conditionTips1'),
              type: 'warning',
              duration: 3000
            })
            this.valiPassFlag = false
            return
          } else {
            this.valiPassFlag = true
          }
          this.$emit('submitEnd', newIds, newData, this.optTypeList, this.labelDec, this.hitRule, this.labelResList, this.hitLabelContentCount, this.labelInfoAlarm)
        })
      }
      if (this.valiPassFlag) {
        this.visible = false
      }
    },
    // 返回需要查询的数据ids，参数1：需要添加的数据； 参数2：列表中已存在的数据id
    getNewIds(newList, exitIds) {
      newList = newList || []
      const newIds = newList.map(data => data.id)
      // 过滤掉重复的id
      return Array.from(new Set([...newIds, ...exitIds]))
    },
    // 从后台获取勾选的分组下的所有数据
    getListDataByGroupIds(groupData) {
      // 获取勾选的分组的id字符串，过滤掉 id < 0 的数据
      const groupIds = (groupData || []).map(data => data.dataId).filter(id => id >= 0).toString()
      if (groupIds) {
        return this.getListByGroupIds({ ...this.invokeGroupParam, groupIds })
      }
      return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    //  查询所有数据
    async getListAll() {
      const optionTemp = Object.assign(this.query, this.tempOption)
      optionTemp.page = null
      optionTemp.limit = null
      const res = await this.list(optionTemp);
      return res.data.items || []
    },
    handleCheckAllChange() {
      this.backupSelectedIds.splice(0) // 备份已选中的行的id
      this.backupSelectedDatas.splice(0)
      this.backupUnSelectedIds.splice(0)
      if (this.checkAll) {
        this.gridTable().toggleAllSelection()
      } else {
        if (this.gridTable().getSelectedKeys().length > 0) {
          this.gridTable().clearSelection()
        }
      }
    },
    afterLoad(rowData, table) {
      if (this.checkAllAble) {
        this.$nextTick(() => {
          // 选中当前页面所有的终端,处理未选中的数据
          if (rowData.length > 0 && this.checkAll) {
            const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
            rowData.forEach(r => {
              if (!backupUnSelectedIdsSet.has(r['id'])) {
                this.gridTable().toggleRowSelection(r, true)
              }
            })
          } else { // 未选中当前页面所有的终端,处理已选中的数据
            const backupSelectedIdsSet = new Set(this.backupSelectedIds)
            rowData.forEach(r => {
              if (backupSelectedIdsSet.has(r['id'])) {
                this.gridTable().toggleRowSelection(r, true)
              }
            })
          }
        })
      }
    },
    selectRemoteData(selection, row) {
      if (this.checkAllAble) {
        this.$nextTick(() => {
          const id = row['id']
          const selectedIds = this.allRemoteTable().getSelectedIds()
          const isSelect = selectedIds.indexOf(id) >= 0
          // 选中全部的情况下，缓存取消勾选的数据
          if (this.checkAll) {
            // 当前 row 在缓存的未勾选的数据ids中的位置
            const index = this.backupUnSelectedIds.indexOf(id)
            if (isSelect) {
              // 如果是勾选，则移除
              index >= 0 && this.backupUnSelectedIds.splice(index, 1)
            } else {
              // 如果是取消勾选，则添加
              index == -1 && this.backupUnSelectedIds.push(id)
            }
          } else {
            // 未选中的情况下，缓存已勾选的数据
            // 当前 row 在缓存的勾选的数据ids中的位置
            const index = this.backupSelectedIds.indexOf(id)
            if (isSelect) {
              // 如果是勾选，且未添加到数组，则添加
              if (index == -1) {
                this.backupSelectedIds.push(id)
                this.backupSelectedDatas.push(row)
              }
            } else {
              // 如果是取消勾选，且已添加到数组，则移除
              if (index >= 0) {
                this.backupSelectedIds.splice(index, 1)
                this.backupSelectedDatas.splice(index, 1)
              }
            }
          }
        })
      }
    },
    selectAllRemoteData(selection) {
      if (this.checkAllAble) {
        this.$nextTick(() => {
          const rowDatas = this.gridTable().rowData
          // 当前页面选中数据的长度为0，证明是取消全选
          const isUnselectAll = this.gridTable().getSelectedIds().length === 0
          // 选中全部的情况下，缓存取消勾选的数据
          if (this.checkAll) {
            const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
            rowDatas.forEach(r => {
              const id = r['id']
              const isExist = backupUnSelectedIdsSet.has(id)
              // 取消全选且未缓存，则添加
              if (isUnselectAll && !isExist) {
                backupUnSelectedIdsSet.add(id)
              }
              // 全选且已缓存，则删除
              if (!isUnselectAll && isExist) {
                backupUnSelectedIdsSet.delete(id)
              }
            })
            // 根据 Set 更新 this.backupUnSelectedIds
            this.backupUnSelectedIds = Array.from(backupUnSelectedIdsSet)
          } else {
            // 未选中全部的情况下，缓存已勾选的数据
            const backupSelectedMap = new Map()
            this.backupSelectedIds.forEach((id, i) => { backupSelectedMap.set(id, this.backupSelectedDatas[i]) })
            rowDatas.forEach(r => {
              const id = r['id']
              const isExist = backupSelectedMap.has(id)
              // 全选且已未缓存，则添加
              if (!isUnselectAll && !isExist) {
                backupSelectedMap.set(id, r)
              }
              // 取消全选且已缓存，则删除
              if (isUnselectAll && isExist) {
                backupSelectedMap.delete(id)
              }
            })
            // 根据 Map 更新 this.backupSelectedIds, this.backupSelectedDatas
            this.backupSelectedIds = Array.from(backupSelectedMap.keys())
            this.backupSelectedDatas = Array.from(backupSelectedMap.values())
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .showBtn {
    float: right;
  }
  .noShowBtn {
    float: right;
    margin-top: -5px;
  }
  .error_style {
    line-height: 20px; color: #F56C6C; font-size: 12px
  }
</style>
