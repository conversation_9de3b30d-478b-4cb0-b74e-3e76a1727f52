<template>
  <div>
    <import-table
      ref="importTable"
      :formable="formable"
      :add-disabled="addDisabled"
      :import-disabled="importDisabled"
      :delete-disabled="deleteDisabled"
      :input-disabled="inputDisabled"
      :grid-table-height="gridTableHeight"
      :search-info-prompt-message="searchInfoPromptMessage"
      :col-model="colModel"
      :row-no-label="rowNoLabel"
      :row-datas="rowDatas"
      :handle-create="handleCreate"
      :handle-import="handleImport"
      :handle-delete="handleDelete"
      :handle-search="handleSearch"
      :selectable="selectable"
      :auto-height="autoHeight"
      :max-height="maxHeight"
      @selectionChangeEnd="selectionChangeEnd"
    />
    <import-table-dlg
      ref="importTableDlg"
      append-to-body
      :exits-list-data="rowDatas"
      :elg-title="elgTitle"
      :group-root-name="groupRootName"
      :search-info-name="importSearchInfo"
      :confirm-button-name="importConfirmButtonName"
      :prompt-message="importPromptMsg"
      :group-title="groupTitle"
      :col-model="importColModel"
      :list="importRowDataApi"
      :load-group-tree="importLoadGroupTree"
      :create-group="createGroup"
      :update-group="updateGroup"
      :delete-group="deleteGroup"
      :count-by-group="countByGroup"
      :get-group-by-name="getGroupByName"
      :delete="deleteLibData"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteGroupFunc"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleCreate"
      :get-list-by-group-ids="getListByGroupIds"
      @submitEnd="importSubmitEnd"
      @submitDeleteEnd="importSubmitDeleteEnd"
      @changeGroupAfter="changeGroupAfter"
    />
    <el-dialog
      v-el-drag-dialog
      append-to-body
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dlgStatus]"
      :visible.sync="dlgVis"
      width="600px"
    >
      <slot name="form-content"/>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgLoading" @click="dlgSubmit">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dlgVis = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import { selectable } from '@/utils'
export default {
  // 通用的具有导入功能的表格，封装表格以及导入窗口
  name: 'CommonImportTable',
  components: { ImportTableDlg, ImportTable },
  props: {
    formable: { type: Boolean, default: true },
    addDisabled: { type: Boolean, default: false },
    importDisabled: { type: Boolean, default: false },
    deleteDisabled: { type: Boolean, default: true },
    inputDisabled: { type: Boolean, default: false },
    gridTableHeight: {  //  列表高度
      type: Number,
      default: 200
    },
    textMap: {
      type: Object,
      required: true
    },
    searchInfoPromptMessage: {  //  输入框的提示信息
      type: String,
      default: function() {
        return this.$t('pages.searchKeyword')
      }
    },
    colModel: {
      type: Array,
      default: function() { return [] }
    },
    importColModel: {
      type: Array,
      default: function() { return [] }
    },
    //  行号
    rowNoLabel: {
      type: String,
      default: null
    },
    rowDataApi: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => {
          resolve({ code: 20000, data: [] })
        })
      }
    },
    selectable: {
      type: Function,
      default: selectable
    },
    autoHeight: { // autoHeight要与maxHeight搭配使用
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200
    },
    elgTitle: { type: String, default() { return this.$t('pages.importData') } },
    //  左边类型库名称
    groupRootName: { type: String, default() { return this.$t('pages.typeLibrary') } },
    importSearchInfo: { type: String, default() { return this.$t('pages.searchKeyword') } },
    //  确认按钮名称
    importConfirmButtonName: { type: String, default: function() { return this.$t('button.insert') } },
    importPromptMsg: { type: String, default: null },
    groupTitle: { type: String, default: null },
    importRowDataApi: { //  查询右边列表数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    importLoadGroupTree: { //  加载类型数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    createGroup: { // 添加分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    updateGroup: { // 修改分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    deleteGroup: { // 删除分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    countByGroup: { // 统计分组数量，判断分组是否存在
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getGroupByName: { // 根据分组名称获取分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    deleteLibData: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    deleteGroupAndData: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    deleteGroupFunc: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    moveGroupToOther: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    getListByIds: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    getListByGroupIds: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    handleFormFunc: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    }
  },
  data() {
    return {
      dlgStatus: 'create',
      isImport: false,
      dlgVis: false,
      dlgLoading: false,
      rowDatas: [],
      tableSearchInfo: ''
    }
  },
  methods: {
    importDlgRef() {
      return this.$refs['importTableDlg']
    },
    importTable() {
      return this.$refs['importTable']
    },
    loadTableData() {
      this.handleSearch()
    },
    initShowDlg(groupId, importDlg) {
      this.dlgVis = true
      this.$emit('resetForm', importDlg, groupId)
    },
    handleCreate(groupId, importDlg) {
      this.dlgStatus = 'create'
      this.initShowDlg(groupId, importDlg)
    },
    handleUpdate() {
      this.dlgStatus = 'update'
      this.initShowDlg()
    },
    dlgSubmit() {
      this.dlgLoading = true
      this.handleFormFunc(this.dlgStatus).then(res => {
        this.dlgLoading = false
        this.dlgVis = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.dlgLoading = false
      })
    },
    handleSearch(searchInfo) {
      searchInfo = (!searchInfo && searchInfo !== '' ? this.tableSearchInfo : searchInfo).trim().toLowerCase()
      this.tableSearchInfo = searchInfo
      this.rowDataApi({ searchInfo }).then(res => {
        if (res.data) {
          this.rowDatas = [...res.data]
        }
      })
    },
    handleImport() {
      this.importDlgRef() && this.importDlgRef().show()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const deleteIds = this.importTable().getSelectedIds() || []
        for (let i = this.rowDatas.length - 1; i >= 0; i--) {
          if (deleteIds.includes(this.rowDatas[i].id)) {
            this.rowDatas.splice(i, 1)
          }
        }
        this.$emit('change', this.rowDatas)
      })
    },
    importSubmitEnd(needIds) {
      this.getListByIds({ ids: needIds.join(',') }).then(res => {
        res.data && this.$emit('change', res.data)
      })
    },
    importSubmitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      const rowDatas = this.rowDatas.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
      this.$emit('change', rowDatas)
    },
    changeGroupAfter() {
      this.$emit('changeGroupAfter')
    },
    selectionChangeEnd(rowDatas) {
      this.$emit('selectionChangeEnd', rowDatas)
    }
  }
}
</script>

<style scoped>

</style>
