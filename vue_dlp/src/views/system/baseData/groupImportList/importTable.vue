<template>
  <LocalZoomIn
    :height="normalHeight"
    :zoom-in-width="700"
    parent-tag="el-dialog"
    :useable="autoHeight"
  >
    <div class="toolbar">
      <el-button v-if="formable" :disabled="addDisabled" icon="el-icon-plus" size="small" @click="handleAddFun">
        {{ $t('button.add') }}
      </el-button>
      <el-button v-if="formable && showImport" :disabled="importDisabled" icon="el-icon-plus" size="small" @click="handleImportFun">
        {{ $t('button.import') }}
      </el-button>
      <el-button v-if="formable" icon="el-icon-delete" size="small" :disabled="deleteDisabled" @click="handleDeleteFun">
        {{ $t('button.delete') }}
      </el-button>
      <el-button v-if="formable && showBatchUpdate" icon="el-icon-delete" size="small" :disabled="deleteDisabled" @click="handleBatchUpdateFun">
        批量修改操作类型
      </el-button>
      <div v-if="formable && showSearch" style="float: right;">
        <el-tooltip placement="top">
          <div slot="content">{{ searchInfoPromptMessage }}</div>
          <el-input v-model="searchInfo" v-trim clearable style="width: 130px;" :disabled="inputDisabled" maxlength @keyup.enter.native="handleSearchFun" />
        </el-tooltip>
        <el-button type="primary" icon="el-icon-search" size="mini" :disabled="inputDisabled" @click="handleSearchFun">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <!-- 弹窗列表 -->
    <grid-table
      ref="table"
      :col-model="colModel1"
      :row-datas="rowDatas"
      :multi-select="true"
      :auto-height="autoHeight"
      :row-no-label="rowNoLabel"
      :max-height="'calc(100% - 30px)'"
      :height="tableHeight"
      :show-pager="false"
      :selectable="handleSelectableFun"
      @selectionChangeEnd="urlSelectionChangeEnd"
    />
  </LocalZoomIn>
</template>

<script>

export default {
  name: 'ImportTable',
  props: {
    formable: { type: Boolean, default: true },
    addDisabled: { type: Boolean, default: false },
    importDisabled: { type: Boolean, default: false },
    deleteDisabled: { type: Boolean, default: true },
    inputDisabled: { type: Boolean, default: false },
    gridTableHeight: {  //  列表高度
      type: Number,
      default: 200
    },
    searchInfoPromptMessage: {  //  输入框的提示信息
      type: String,
      default: function() {
        return this.$t('pages.searchKeyword')
      }
    },
    colModel: {
      type: Array,
      default: function() { return [] }
    },
    //  行号
    rowNoLabel: {
      type: String,
      default: null
    },
    rowDatas: {
      type: Array,
      default: function() { return [] }
    },
    handleCreate: {
      type: Function,
      default: null
    },
    handleImport: {
      type: Function,
      default: null
    },
    handleDelete: {
      type: Function,
      default: null
    },
    handleBatchUpdate: {
      type: Function,
      default: null
    },
    handleSearch: {
      type: Function,
      default: null
    },
    selectable: {
      type: Function,
      default: null
    },
    autoHeight: { // autoHeight要与maxHeight搭配使用
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200
    },
    showImport: { // 管控是否显示导入按钮（标签外发权限管控）
      type: Boolean,
      default: true
    },
    showBatchUpdate: { // 手动打标签策略配置，管控是否显示批量修改按钮
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchInfo: '',
      colModel1: [],
      tableHeight: 0
    }
  },
  computed: {
    // 未放大情况下的最大高度，列表最大高度+工具栏高度
    normalHeight() {
      return this.maxHeight + 40
    }
  },
  created() {
    //  关闭操作项
    this.colModel1 = this.formable ? this.colModel : this.colModel.filter(item => { return item.label !== 'operate'; })
    this.tableHeight = this.autoHeight ? 0 : this.gridTableHeight
  },
  methods: {
    handleAddFun() {
      if (this.handleCreate != null) {
        this.handleCreate();
      }
    },
    handleImportFun() {
      if (this.handleImport != null) {
        this.handleImport();
      }
    },
    handleDeleteFun() {
      if (this.handleDelete != null) {
        this.handleDelete();
      }
    },
    handleBatchUpdateFun() {
      if (this.handleBatchUpdate != null) {
        this.handleBatchUpdate();
      }
    },
    handleSearchFun() {
      if (this.handleSearch != null) {
        this.handleSearch(this.searchInfo);
      }
    },
    handleSelectableFun(row, index) {
      if (this.selectable != null) {
        return this.selectable(row, index)
      }
      return false
    },
    urlSelectionChangeEnd(rowDatas) {
      this.$emit('selectionChangeEnd', rowDatas)
    },
    getSelectedIds() {
      return this.$refs['table'].getSelectedIds();
    },
    getSelectedDatas() {
      this.$refs['table'].deleteTableData()
      return this.$refs['table'].getSelectedDatas();
    },
    getSearchInfo() {
      return this.searchInfo
    },
    getIdsByList(array, nodeKey) {
      nodeKey = nodeKey || 'id'
      const arr = [];
      (array || []).forEach(item => {
        arr.push(item[nodeKey])
      })
      return arr;
    },
    //  删除列表数据，方便父组件调用
    deleteTableData(sourceData, needDeleteIds) {
      sourceData = JSON.parse(JSON.stringify(sourceData))
      needDeleteIds = needDeleteIds || []
      const temps = [];
      let item = null
      for (let i = 0; i < sourceData.length; i++) {
        item = sourceData[i]
        if (needDeleteIds.indexOf(parseInt(item.id)) > -1) {
          temps.push(item);
        }
      }
      temps.forEach(item => {
        const index = sourceData.indexOf(item);
        sourceData.splice(index, 1)
      })
      return sourceData;
    },
    //  排序
    tableSort(option) {
      this.$refs['table'].execRowDataApi(option)
    },
    //  清空查询
    clearSearchInfo() {
      this.searchInfo = ''
    }
  }
}
</script>
