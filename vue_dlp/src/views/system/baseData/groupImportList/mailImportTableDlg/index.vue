<template>
  <div>
    <import-table-dlg
      ref="emailImportTable"
      :append-to-body="appendToBody"
      :elg-title="$t('pages.importMailLibrary')"
      :group-root-name="$t('pages.mailLibrary')"
      :search-info-name="$t('pages.emailCopy_nameOrAddress')"
      :confirm-button-name="$t('pages.addMail')"
      :prompt-message="$t('pages.mailImportText1')"
      :group-title="$t('pages.mailGroup')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createMailLibGroup"
      :update-group="updateMailLibGroup"
      :delete-group="deleteMailLibGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getMailLibGroupByName"
      :delete="deleteMailLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteMailLibGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleEmailCreate"
      :get-list-by-group-ids="getMailLibraryByGroupIds"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
    >
      <template slot="footer-slot">
        <link-button
          btn-type="primary"
          btn-style="float: left"
          :menu-code="'A52'"
          :link-url="'/system/baseData/mailLibrary'"
          :btn-text="$t('pages.maintainMail')"
          :before-click="linkBeforeClick"
        /></template>
    </import-table-dlg>
    <el-dialog
      v-el-drag-dialog
      append-to-body
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="emailTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('form.emailName')" prop="name">
          <el-input v-model="emailTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.mailLibrary_text3')" prop="address">
          <el-input v-model="emailTemp.address" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="emailTemp.groupId" :placeholder="$t('text.select')" @change="emailGroupChange">
            <el-option v-for="item in groupTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('form.unit')" prop="unit">
          <el-input v-model="emailTemp.unit" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('form.office')" prop="office">
          <el-input v-model="emailTemp.office" :maxlength="60"/>
        </FormItem>
        <!-- <FormItem label="状态" prop="remark">
          <el-select v-model="temp.state">
            <el-option v-for="item in stateOptions" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}</el-option>
          </el-select>
        </FormItem> -->
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="emailTemp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import {
  countChildByGroupId,
  createMailLib,
  createMailLibGroup,
  deleteGroupAndData,
  deleteMailLib,
  deleteMailLibGroup,
  getGroupTreeNode,
  getMailLibByAddress,
  getMailLibGroupByName,
  getMailLibPage,
  getMailLibraryByGroupIds,
  getMailLibraryByIds,
  moveGroup,
  moveGroupToOther,
  updateMailLib,
  updateMailLibGroup
} from '@/api/system/baseData/mailLibrary'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg';
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'MailImportTableDlg',
  components: { ImportTableDlg },
  props: {
    appendToBody: {
      type: Boolean,
      default: true
    },
    /** 提交导入申请后，返回到父组件的信息是否需要 信息库的ID， true返回的是ID列表，false返回邮箱信息 */
    isNeedIds: {
      type: Boolean,
      default: false
    },
    /** 当idNeedIds == true时， 该属性会过滤邮箱地址包括通配符的邮箱 */
    filterWildcardChar: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      importColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailUpdate }
          ]
        }
      ],
      dialogFormVisible: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mailBox'), 'update'),
        create: this.i18nConcatText(this.$t('pages.mailBox'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'blur' }]
      },
      emailTemp: {},
      defaultEmailTemp: { // 表单字段
        id: undefined,
        name: '',
        address: '',
        office: '',
        unit: '',
        state: 0,
        groupId: undefined,
        type: 0
      },
      groupTreeData: undefined,
      dialogStatus: 'create',
      submitting: false,
      linkSkip: false
    }
  },
  activated() {
    if (this.linkSkip) {
      this.dlgRef().show()
      this.linkSkip = false
    }
  },
  methods: {
    countChildByGroupId,
    createMailLib,
    createMailLibGroup,
    deleteGroupAndData,
    deleteMailLib,
    deleteMailLibGroup,
    getGroupTreeNode,
    getMailLibByAddress,
    getMailLibGroupByName,
    getMailLibPage,
    getMailLibraryByGroupIds,
    getMailLibraryByIds,
    moveGroup,
    moveGroupToOther,
    updateMailLib,
    updateMailLibGroup,
    dlgRef() {
      return this.$refs['emailImportTable']
    },
    addressValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const len = value.split('#')
      if (!(reg.test(value) || reg1.test(value) || len.length === 2)) {
        callback(new Error(this.$t('pages.validateMsg_email2')))
      }
      getMailLibByAddress({ address: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.emailTemp.id) {
          callback(new Error(this.$t('pages.validateMsg_email3')))
        } else {
          callback()
        }
      })
    },
    initForm() {
      this.emailTemp = JSON.parse(JSON.stringify(this.defaultEmailTemp))
      this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
    },
    initGroupTree() {
      this.dlgRef() && (this.groupTreeData = this.dlgRef().groupTreeData)
    },
    getInfoList(option) {
      const searchQuery = Object.assign({}, option)
      return getMailLibPage(searchQuery)
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.groupTreeData, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      if (treeDatas && treeDatas.length > 0) {
        for (let i = 0, size = treeDatas.length; i < size; i++) {
          const treeData = treeDatas[i]
          if (treeData.dataId == dataId) {
            return treeData.label
          } else if (treeData.children) {
            const result = this.getGroupNameByDataId(treeData.children, dataId)
            if (result) return result
          }
        }
      }
      return ''
    },
    show() {
      this.dlgRef().show()
      if (!this.groupTreeData) {
        getGroupTreeNode().then(res => {
          if (res.data) {
            this.groupTreeData = res.data || []
          }
        })
      }
    },
    handleEmailCreate(selectedGroupId) {
      this.initGroupTree()
      this.initForm()
      selectedGroupId && (this.emailTemp.groupId = selectedGroupId + '')
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    handleEmailUpdate(row) {
      this.initForm()
      this.emailTemp = JSON.parse(JSON.stringify(row))
      this.emailTemp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },
    getNeedAddIds(needIds) {
      if (!needIds || needIds.length === 0) { return }
      if (this.isNeedIds) {
        this.$emit('submitEnd', needIds)
      } else {
        getMailLibraryByIds({ ids: needIds.join(',') }).then(res => {
          let datas = res.data
          if (this.filterWildcardChar) {
            const beforeSize = datas.length
            datas = datas.filter(item => item.address && !item.address.includes('#'))
            if (beforeSize !== datas.length) {
              this.$notify({
                title: this.$t('text.warning'),
                message: this.$t('pages.mailImportWarnTip1'),
                type: 'warning',
                duration: 3000
              })
            }
          }
          this.$emit('submitEnd', datas)
        })
      }
    },
    submitDeleteEnd(ids) {
      this.$emit('submitDeleteEnd', ids)
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.emailTemp.groupName = findNodeLabel(this.groupTreeData, this.emailTemp.groupId, 'dataId')
          createMailLib(this.emailTemp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dlgRef() && this.dlgRef().$refs['infoList'].execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.emailTemp)
          tempData.groupName_new = findNodeLabel(this.groupTreeData, this.emailTemp.groupId, 'dataId')
          updateMailLib(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dlgRef() && this.dlgRef().$refs['infoList'].execRowDataApi()
            this.$emit('updateEmail', tempData)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    emailGroupChange() {
      this.$refs['dataForm'] && this.$refs['dataForm'].validateField('groupId')
    },
    linkBeforeClick() {
      this.linkSkip = true
      this.dlgRef().visible = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
