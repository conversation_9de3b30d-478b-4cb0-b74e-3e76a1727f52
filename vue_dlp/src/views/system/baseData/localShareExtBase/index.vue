<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate2()">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'541'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'542'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.fileSuffix_text1')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="suffixTable"
        :show-pager="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
      >
        <div class="toolbar">
          <FormItem v-if="dialogStatus==='batchCreate'" :label="$t('table.suffixes')" prop="suffix">
            <el-input v-model="temp.suffix" type="textarea" :onkeyup="(function() { temp.suffix = temp.suffix.replace(/\s+/g,'') })()" :maxlength="1000" show-word-limit :placeholder="$t('pages.fileSuffix_text13')"></el-input>
          </FormItem>
          <FormItem v-else :label="$t('table.suffixes')" prop="suffix">
            <el-input v-model="temp.suffix" :onkeyup="(function() { temp.suffix = temp.suffix.replace(/[\s|]+/g,'') })()" :maxlength="60" show-word-limit :placeholder="$t('pages.fileSuffix_text11')"></el-input>
          </FormItem>
          <FormItem :label="$t('table.remark')" prop="name">
            <el-input v-model="temp.remark" :maxlength="60" type="textarea" show-word-limit></el-input>
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='update'?updateData() : createData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <export-dlg ref="exportDlg" :export-func="exportFunc"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      template="localShareExt"
      :show-import-type="false"
      :file-name="title"
      :tip="$t('route.localShareExtBase')"
      :show-import-way="true"
      :upload-func="upload"
      @success="importEndFunc"
    />

  </div>
</template>

<script>
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import {
  getLocalShareExtBasePage, createLocalShareExtBase, updateLocalShareExtBase, deleteLocalShareExtBase, getLocalShareExtBaseBySuffix, exportExcel
} from '@/api/system/baseData/localShareExtBase'
import { enableStgBtn, initTimestamp, selectable } from '@/utils'
import request from '@/utils/request'

export default {
  name: 'LocalShareExtBase',
  components: { ExportDlg, ImportDlg },
  data() {
    return {
      deleteable: false,
      dialogFormVisible: false,
      submitting: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('table.localShareExtBase'), 'update'),
        batchCreate: this.i18nConcatText(this.$t('table.localShareExtBase'), 'batchAdd'),
        delete: this.i18nConcatText(this.$t('table.localShareExtBase'), 'delete')
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        suffix: '',
        remark: ''
      },
      rules: {
        suffix: [
          { required: true, message: this.$t('pages.fileSuffix_text4'), trigger: 'blur' },
          { validator: this.suffixValidator, trigger: 'change' }
        ]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      colModel: [
        { prop: 'suffix', label: 'suffixes', width: '110', sort: 'custom', formatter: (row) => { return row.suffix.indexOf('.') === 0 ? row.suffix : '.' + row.suffix } },
        { prop: 'remark', label: 'remark', width: '110', sort: 'custom' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      title: this.$t('route.localShareExtBase'),
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      existSuffixes: []   // 已存在的文件后缀
    }
  },
  computed: {
    gridTable() {
      return this.$refs['suffixTable']
    }
  },
  watch: {
    dialogFormVisible: function(val) {
      if (!val) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        })
      }
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
  },
  activated() {
    this.gridTable.execRowDataApi()
  },
  methods: {
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getLocalShareExtBasePage(searchQuery)
    },
    handleCreate2() {
      this.dialogStatus = 'batchCreate'
      this.existSuffixes.splice(0)
      this.handleCreate()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormVisible = true
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteLocalShareExtBase({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    createData() {
      this.$refs['dataForm'].validate(async valid => {
        if (valid) {
          if (this.existSuffixes.length > 0) {
            this.$confirmBox(this.$t('pages.localShareExtBase_text', { suffixes: this.existSuffixes.join(',') }), this.$t('text.prompt')).then(() => {
              this.submitting = true
              createLocalShareExtBase(this.temp, true).then((res) => {
                this.submitting = false
                this.dialogFormVisible = false
                this.gridTable.execRowDataApi(this.query)
                if (res.data == 'success') {
                  this.$notify({
                    title: this.$t('text.success'),
                    message: this.$t('text.createSuccess'),
                    type: 'success',
                    duration: 2000
                  })
                } else {
                  this.$notify({
                    title: this.$t('text.warning'),
                    message: res.data,
                    type: 'error',
                    duration: 5000
                  })
                }
              })
            })
          } else {
            this.submitting = true
            createLocalShareExtBase(this.temp, false).then((res) => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi(this.query)
              if (res.data == 'success') {
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              } else {
                this.$notify({
                  title: this.$t('text.warning'),
                  message: res.data,
                  type: 'error',
                  duration: 5000
                })
              }
            })
          }
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(async valid => {
        if (valid) {
          updateLocalShareExtBase(this.temp).then((res) => {
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            if (res.data == 'success') {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.editSuccess'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: this.$t('text.warning'),
                message: res.data,
                type: 'error',
                duration: 5000
              })
            }
          })
        }
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({ ids: formData.dataIds.join(',') }, opts)
      }
    },
    upload(data) {
      return request.post('/localShareExtBase/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc() {
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    suffixValidator: function(rule, value, callback) {
      value = value || '';
      if (value === '') {
        callback();
        return
      }
      const suffixes = value.split('|')
      suffixes.forEach(suffix => {
        if (!suffix.startsWith('.')) {
          suffix = '.' + suffix
        }
        if (suffix.length > 60) {
          callback(new Error(this.$t('pages.suffixOver60')))
        }
      })
      getLocalShareExtBaseBySuffix(suffixes).then(respond => {
        const strategy = respond.data
        if (strategy.length === 0) {
          callback()
        } else if (this.dialogStatus === 'update' && strategy[0].id === this.temp.id) {
          callback()
        } else if (this.dialogStatus === 'update' && strategy[0].id !== this.temp.id) {
          const errorStr = strategy.map(item => {
            return item.suffix
          }).join(',')
          callback(new Error(this.$t('pages.alreadyExistsInfo', { info: errorStr })))
        }
        this.existSuffixes.splice(0)
        this.existSuffixes = strategy.map(item => { return item.suffix })
        callback()
      })
    },
    handleDrag() {
    }
  }
}

</script>
