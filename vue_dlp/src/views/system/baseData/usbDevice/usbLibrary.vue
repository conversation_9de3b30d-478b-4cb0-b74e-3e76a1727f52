<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="usbGroupTree"
        resizeable
        :default-expand-all="true"
        :data="usbGroupTreeData"
        :render-content="renderContent"
        @node-click="usbGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar currentRow">
        <el-button type="primary" size="mini" @click="toggleTreeMenu" >
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button v-if="false" type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>

        <el-button v-permission="'381'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'382'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <el-button v-if="hasPermission('382')" :loading="plugLoading" size="mini" @click="plugGetInfo">
          {{ $t('pages.addedUsbInfo') }}
        </el-button>

        <el-button v-permission="'C71'" icon="el-icon-setting" size="mini" @click="()=>{this.$router.push('/behaviorManage/StorageDevice/Driver')}">
          {{ $t('pages.deviceAuthor') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.usbName" v-trim clearable :placeholder="$t('pages.deviceName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('pages.deviceName')">
                <el-input v-model="query.usbName" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('pages.deviceCode')">
                <el-input v-model="query.usbCode" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('pages.devType')">
                <el-select v-model="query.usbType" clearable style="width: 100%;">
                  <el-option :label="$t('pages.all')" :value="null"></el-option>
                  <el-option v-for="item in usbTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.manufacturer')">
                <el-input v-model="query.manufacturer" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.vid')">
                <el-input v-model="query.vid" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.pid')">
                <el-input v-model="query.pid" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.deviceSerialNumber')">
                <el-input v-model="query.srcPnpDeviceId" v-trim clearable maxlength="100"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table ref="urlList" :col-model="colModel" :row-data-api="rowDataApi" :show-pager="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" style="width: 500px; margin-left:20px;">
        <FormItem :label="$t('pages.deviceName')" prop="usbName">
          <el-input v-model="temp.usbName" v-trim maxlength="50" show-word-limit/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.deviceCode')" prop="usbCode">
          <el-input v-model="temp.usbCode" disabled maxlength="50" />
        </FormItem>
        <FormItem :label="$t('pages.devType')" prop="usbType">
          <el-select v-model="temp.usbType" disabled>
            <el-option v-for="item in usbTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.driverType')" prop="driverType">
          <el-select v-model="temp.driverType" disabled>
            <el-option v-for="item in driverTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.vid')">
          <el-input v-model="temp.vid" maxlength="150" disabled/>
        </FormItem>
        <FormItem :label="$t('pages.manufacturer')">
          <el-input v-model="temp.manufacturer" maxlength="50" disabled/>
        </FormItem>
        <FormItem :label="$t('table.pid')">
          <el-input v-model="temp.pid" maxlength="150" disabled/>
        </FormItem>
        <FormItem :label="$t('table.deviceSerialNumber')">
          <el-input v-model="temp.srcPnpDeviceId" maxlength="150" disabled/>
        </FormItem>
        <FormItem :label="$t('pages.usbSize')" prop="usbSize">
          <el-input-number v-model="temp.usbSize" :controls="false" :min="0" :max="99999999" :step="1" step-strictly disabled/>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.memo" v-trim type="textarea" maxlength="100" show-word-limit resize="none" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.usbGroup')"
      :group-tree-data="treeSelectNode"
      :add-func="addUsbGroup"
      :update-func="updateUsbGroup"
      :delete-func="deleteUsbGroup"
      :move-func="moveUsbGroup"
      :edit-valid-func="getGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="$t('pages.usbDevice_text8')"
      :select-tree-data="treeSelectNode"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteUsbGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="$t('pages.usbDevice_delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :delete-filter-name="$t('pages.usbDevice_text8')"
      :edit-type="1"
      @submitEnd="deleteData"
    />

    <plug-port-dlg ref="plugPortDlg" :prompt-message="$t('pages.usbGetPlugInfoMessage')" @connectionSuccess="plugConnectionSuccess"></plug-port-dlg>
    <plug-update-dlg ref="plugUpdateDlg" :re-operator="$t('pages.addedUsbInfo')"/>

    <plug-usb-select ref="plugUsbSelect" :group-tree-data="groupTreeData" :group-tree-id="query.groupId" @successSubmit="successSubmit" @refresh="refresh" @closeSubmit="closeSubmit"/>

    <import-dlg
      ref="importDlg"
      :title="title"
      :tip="$t('table.pnpDeviceId')"
      template="usb"
      :show-import-type="false"
      :show-import-way="true"
      :file-name="title"
      :upload-func="importExcel"
      @success="importEndFunc"
    />
    <export-dlg ref="exportDlg" :group-tree-data="usbGroupTreeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>
  </div>
</template>
<script>
import {
  addUsbDevice,
  addUsbGroup,
  countChildByGroupId,
  deleteGroupAndData,
  deleteUsbDevice,
  deleteUsbGroup,
  exportExcel,
  findUsbDeviceList,
  findUsbGroupTree,
  getGroupByName,
  importExcel,
  moveGroupToOther,
  moveUsbGroup,
  updateUsbDevice,
  updateUsbGroup
} from '@/api/behaviorManage/hardware/usbConfig'
import { getUsbTypeDict } from '@/utils/dictionary'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg';
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import moment from 'moment';
import ImportDlg from '@/views/common/import'
import ExportDlg from '@/views/common/export'
import { Level, UTermPluginClient } from '@/views/system/terminalManage/terminal/uterm/client';
import PlugPortDlg from '@/views/loginAuth/plugPortDlg'
import PlugUsbSelect from '@/views/system/baseData/usbDevice/plugUsbSelect';
import PlugUpdateDlg from '@/views/loginAuth/plugUpdateDlg'

export default {
  name: 'UsbLibrary',
  components: { PlugUsbSelect, BatchEditPageDlg, DeleteGroupDlg, EditGroupDlg, ImportDlg, ExportDlg, PlugPortDlg, PlugUpdateDlg },
  props: {
    groupTreeData: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'usbName', label: 'volumeName', width: '100', sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '80', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'usbCode', label: 'pnpDeviceId', width: '80', sort: 'custom' },
        { prop: 'usbType', label: 'devType', width: '80', sort: 'custom', formatter: this.usbTypeOptionFormatter },
        { prop: 'driverType', label: 'driverType', width: '80', sort: 'custom', formatter: this.driverTypeOptionFormatter },
        { prop: 'vid', label: 'vid', width: '80', sort: 'custom' },
        { prop: 'manufacturer', label: 'manufacturer', width: '80', sort: 'custom' },
        { prop: 'pid', label: 'pid', width: '80', sort: 'custom' },
        { prop: 'srcPnpDeviceId', label: 'deviceSerialNumber', width: '100', sort: 'custom' },
        { prop: 'usbSize', label: 'usbSize', width: '120', sort: 'custom' },
        { prop: 'memo', label: 'remark', width: '80', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        usbName: '',
        usbCode: '',
        manufacturer: '',
        usbType: null,
        groupId: undefined,
        vid: null,
        pid: null,
        srcPnpDeviceId: null
      },
      usbTypeOptions: getUsbTypeDict(),
      driverTypeOptions: [
        { value: 1, label: this.$t('pages.driverType1') },
        { value: 2, label: this.$t('pages.driverType2') }
      ],
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        usbName: '',
        usbCode: '',
        manufacturer: '',
        usbType: 0,
        driverType: 1,
        usbSize: undefined,
        memo: '',
        srcPnpDeviceId: null,
        pid: null,
        vid: null
      },
      rules: {
        usbName: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        usbCode: [
          { required: true, message: this.$t('pages.usbDevice_text2'), trigger: 'blur' }
        ],
        usbGroupId: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        // usbSize: [
        //   { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        // ],
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.urlGroupNameValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.$t('pages.usbDevice_update'),
        create: this.$t('pages.usbDevice_create'),
        delete: this.$t('pages.usbDevice_deleteGroup')
      },
      treeNodeType: 'G',
      usbGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.usbDevice'), parentId: '', children: this.groupTreeData }],
      treeSelectNode: [],
      driverTypeMap: {
        1: this.$t('pages.driverType1'),
        2: this.$t('pages.driverType2')
      },
      title: this.$t('pages.usbInfo'),
      plugLoading: false,
      uPlugin: null
    }
  },
  computed: {
    gridTable() {
      return this.$refs['urlList']
    },
    usbGroupTree: function() {
      return this.$refs['usbGroupTree']
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.loadReceiverTree()
  },
  methods: {
    addUsbGroup,
    updateUsbGroup,
    deleteUsbGroup,
    getGroupByName,
    moveUsbGroup,
    deleteGroupAndData,
    moveGroupToOther,
    importExcel,
    getFileName() { return `${this.$t('route.' + this.$route.meta.title)}-${moment().format('YYYY-MM-DD HH-mm-ss')}.xls`; },
    rowDataApi: function(option) {
      option = Object.assign(this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return findUsbDeviceList(option)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    usbGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId + ''
      } else {
        this.query.groupId = undefined
      }
      this.resetQuery()
      this.gridTable.execRowDataApi()
    },
    loadReceiverTree: function() {
      findUsbGroupTree().then(respond => {
        this.groupTreeData.splice(0, this.groupTreeData.length, ...respond.data)
        this.usbGroupTreeData[0].children = respond.data
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.usbGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.usbGroupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.usbGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.usbGroupTree.findNode(this.usbGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.usbGroupTree.removeNode([nodeData.id])
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    resetQuery() {
      this.query.page = 1
      this.query.usbName = ''
      this.query.usbCode = ''
      this.query.manufacturer = ''
      this.query.usbType = null
      this.query.vid = null
      this.query.pid = null
      this.query.srcPnpDeviceId = null
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleRefresh() {
      this.resetQuery()
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleMoving() {
      this.treeSelectNode = this.loadTypeTreeExceptRoot()
      this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data != null && respond.data != '') {
          this.treeSelectNode = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = this.query.groupId + ''
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport(file) {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          addUsbDevice(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateUsbDevice(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    urlGroupNameValidator(rule, value, callback) {
      getGroupByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleGroupCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleGroupUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.usbGroupTreeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    driverTypeOptionFormatter(row, data) {
      return this.driverTypeMap[row.driverType]
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.usbGroupTree.findNode(this.usbGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.usbGroupTree.setCurrentKey(nodeData)
        this.usbGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    loadTypeTreeExceptRoot() {
      return this.usbGroupTreeData[0].children
    },
    refreshTableData() {
      this.query.groupId = undefined
      this.gridTable.execRowDataApi(this.query)
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteUsbDevice(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    importEndFunc() {
      this.loadReceiverTree()
      this.handleFilter()
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    //  通过U盘插件获取U盘信息
    async plugGetInfo() {
      this.plugLoading = true
      if (!await this.checkPlugConnectionSuccess()) {
        this.plugLoading = false
        return;
      }
      await this.showPlugUsb()
      this.plugLoading = false
    },
    //  检测插件连接是否正常
    async checkPlugConnectionSuccess() {
      let flag = true;
      this.uPlugin = new UTermPluginClient(this, UTermPluginClient.getPort(), Level.DEBUG, true)
      try {
        await this.uPlugin.connectedPromise
      } catch (e) {
        console.log('plug connection error log %o', e)
        this.$refs['plugPortDlg'].show()
        flag = false;
      }
      return flag;
    },
    //  插件连接成功
    plugConnectionSuccess(plug) {
      this.uPlugin = new UTermPluginClient(this, UTermPluginClient.getPort(), Level.DEBUG, true)
      this.showPlugUsb()
    },
    //  获取插件版本号
    async getPlusVersion() {
      const res = await this.uPlugin.getPlugInfo();
      //  当前插件版本号
      const plugVersion = res.PlugVersion || ''
      //  去除版本号的逗号
      const version = plugVersion.split('.')
      let pVersion = ''
      for (let i = 0, len = version.length; i < len; i++) {
        pVersion += version[i];
      }
      return pVersion;
    },
    //  展示通过插件获取到的Usb信息
    async showPlugUsb() {
      //  校验插件版本号,最低版本支持101240827SC
      const pVersion = await this.getPlusVersion();
      if (pVersion < '101240827SC') {
        this.$refs['plugUpdateDlg'].show()
        return;
      }
      this.uPlugin.listUsbDisk().then(datas => {
        datas = datas || []
        //  提示未发现U盘信息
        if (datas.length === 0) {
          this.$message({
            message: this.$t('pages.notUsbInfoMsg'),
            type: 'warning',
            duration: 3000
          })
          return;
        }
        this.$refs.plugUsbSelect.show(this.plugUsbInfoFormat(datas))
      }).catch((err) => {
        console.log('showPlugUsb error log %o', err)
      })
    },
    //  将从插件中获取的USB信息转换成后端的USB信息格式
    plugUsbInfoFormat(datas) {
      const list = []
      if (datas) {
        datas.forEach(data => {
          list.push({
            usbName: data.volumeName,
            usbCode: data.pnpDeviceId,
            usbType: data.deviceType,
            driverType: data.driverType,
            vid: data.vid,
            pid: data.pid,
            manufacturer: data.manufacturer,
            srcPnpDeviceId: data.srcPnpDeviceId,
            usbSize: data.devSize
          })
        })
      }
      return list;
    },
    /**
     * u盘信息添加成功后的回调
     */
    successSubmit() {
      //  刷新列表
      this.handleFilter()
    },
    /**
     * 刷新新增U盘信息列表
     */
    async refresh() {
      //  尝试测试插件是否正常，正常获取插件列表信息
      if (await this.checkPlugConnectionSuccess()) {
        await this.showPlugUsb()
      }
    },
    closeSubmit() {
      this.loadReceiverTree()
    }
  }
}
</script>
<style lang='scss' scoped>
.currentRow{
  margin-top: 5px;
}
>>>.el-input-number.is-without-controls .el-input__inner{
  text-align: left;
}
</style>
