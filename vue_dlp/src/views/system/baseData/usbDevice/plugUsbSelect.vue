<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.usbDevice_create')"
      :remark="$t('pages.usbDevice_text7')"
      :visible.sync="dlgVisible"
      width="800px"
      @close="cancel"
    >
      <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
        <svg-icon icon-class="refresh" />
      </el-button>
      <grid-table
        ref="usbInfoList"
        row-key="dataId"
        :height="350"
        :col-model="colModel"
        :row-datas="rowDatas"
        :show-pager="false"
        saved-selected-prop="usbCode"
        :default-sort="{ prop: 'usbCode' }"
      />
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" style="padding-top: 5px">
        <!-- 为设备指定分组 -->
        <FormItem :label="$t('pages.appointDevGroup')" prop="groupId">
          <el-select v-model="temp.groupId" filterable style="width: 150px; margin-right: 5px; float: left" :placeholder="$t('text.select')">
            <el-option v-for="item in groupTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
          <el-button class="editBtn" @click="handleTypeCreate">
            <svg-icon icon-class="add" />
          </el-button>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('pages.addSelectedDevice') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="groupTreeData"
      :edit-valid-func="getGroupByName"
      :add-func="addUsbGroup"
      @addEnd="createNode"
    />
  </div>
</template>

<script>
import { getUsbTypeDict } from '@/utils/dictionary';
import { batchAddUsbDevice, findUsbGroupTree, listDeviceByCode, getGroupByName, addUsbGroup } from '@/api/behaviorManage/hardware/usbConfig'
import EditGroupDlg from '@/views/common/editGroupDlg'
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'PlugUsbSelect',
  components: { EditGroupDlg },
  props: {
    appendToBody: { type: Boolean, default: false },
    groupTreeId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      dlgVisible: false,
      submitting: false,
      colModel: [
        { prop: 'usbName', label: 'volumeName', width: '100', type: 'input', editMode: true, maxlength: 50 },
        { prop: 'usbCode', label: 'pnpDeviceId', width: '100', sort: true },
        { prop: 'usbType', label: 'devType', width: '80', formatter: this.usbTypeOptionFormatter },
        { prop: 'driverType', label: 'driverType', width: '80', formatter: this.driverTypeOptionFormatter },
        { prop: 'vid', label: 'vid', width: '80' },
        { prop: 'manufacturer', label: 'manufacturer', width: '80' },
        { prop: 'pid', label: 'pid', width: '80' },
        { prop: 'srcPnpDeviceId', label: 'deviceSerialNumber', width: '100' },
        { prop: 'usbSize', label: 'usbSize', width: '120' },
        { prop: 'memo', label: 'remark', width: '80', type: 'input', editMode: true, maxlength: 100 }
      ],
      rowDatas: [],
      usbTypeOptions: getUsbTypeDict(),
      driverTypeMap: {
        1: this.$t('pages.driverType1'),
        2: this.$t('pages.driverType2')
      },
      rules: {
        groupId: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      temp: {
        groupId: null //  指定部门
      },
      groupTreeData: [],
      count: 1  //  用于限制重复返回closeSubmit
    }
  },
  created() {
  },
  methods: {
    getGroupByName,
    addUsbGroup,
    /**
     * 加载USB分组
     */
    loadUsbGroupTree() {
      return findUsbGroupTree().then(respond => {
        this.groupTreeData.splice(0, this.groupTreeData.length, ...respond.data)
        return respond;
      })
    },
    async show(datas) {
      this.count = 1
      await this.loadUsbGroupTree();
      let i = 1;
      //  设置dataId
      datas.forEach(item => {
        item.dataId = i++;
      })
      this.rowDatas = datas || []
      //  不允许默认为根节点
      if (this.groupTreeId != '0') {
        //  检查groupId是否存在分组树中
        const label = findNodeLabel(this.groupTreeData, this.groupTreeId, 'dataId') || null
        if (label) {
          this.temp.groupId = this.groupTreeId
        }
      }
      this.dlgVisible = true
    },
    //  提交
    confirm() {
      const selectDatas = this.$refs.usbInfoList.getSelectedDatas() || []
      // 未选择USB信息时，提示：请选择需要导入的USB
      if (selectDatas.length === 0) {
        this.$message({
          message: this.$t('pages.pleaseChooseImportUsbInfo'),
          type: 'warning',
          duration: 3000
        })
        return
      }
      //  存在未设置设备昵称的USB设备时，提示请设置设备昵称
      if (selectDatas.filter(item => item.usbName === undefined || item.usbName === null || item.usbName === '').length > 0) {
        this.$message({
          message: this.$t('pages.usbPlugImportNotSetNameMsg'),
          type: 'warning',
          duration: 3000
        })
        return
      }
      //  校验选中的设备中是否存在设备编码为空的数据
      if (selectDatas.filter(item => !item.usbCode).length > 0) {
        this.$message({
          message: this.$t('pages.usbPlugImportUsbCodeIsNullMsg'),
          type: 'warning',
          duration: 3000
        })
        return
      }

      this.$refs.dataForm.validate(async(valid) => {
        if (valid) {
          //  校验保存的usb设备编码中是否存在已有的设备编码
          const usbCodes = []
          const sameUsbCodeMap = new Map()
          selectDatas.forEach(data => {
            //  设置部门分组
            data.groupId = this.temp.groupId
            if (data.usbCode) {
              usbCodes.push(data.usbCode);
              const upperCaseCode = data.usbCode.toUpperCase()
              if (!sameUsbCodeMap.get(upperCaseCode)) {
                sameUsbCodeMap.set(upperCaseCode, [])
              }
              sameUsbCodeMap.get(upperCaseCode).push(data.usbName);
            }
          })
          let sameMsg = ''
          //  校验是否存在相同设备编码的USB设备 ('xx设备昵称1'、'xxx设备昵称2'存在相同的编码，默认保存第一条，确定是否继续？
          sameUsbCodeMap.forEach((value, key) => {
            if (value.length > 1) {
              sameMsg += this.$t('pages.usbSamePlugValidMsg1', { usbNames: value.join('、') }) + '，'
            }
          })
          if (sameMsg) {
            sameMsg = this.$t('pages.usbSamePlugValidMsg2', { msg: sameMsg })
            this.$confirmBox(sameMsg, this.$t('text.prompt')).then(() => {
              this.addUsbDevice(usbCodes, selectDatas);
            }).catch(() => {})
          } else {
            await this.addUsbDevice(usbCodes, selectDatas);
          }
        }
      })
    },
    /**
     * 添加usb设备库
     */
    async addUsbDevice(usbCodes, selectDatas) {
      if (usbCodes.length > 0) {
        const res = await listDeviceByCode(usbCodes)
        //  若导入的usbCode已存在，提示继续添加将覆盖已存在的设备
        if (res.data && res.data.length > 0) {
          const exitsCodes = []
          res.data.forEach(code => {
            exitsCodes.push(code.usbCode)
          })
          const message = this.$t('pages.usbPlugRepeatImportMsg', { usbCodes: exitsCodes.join(',') })
          this.$confirmBox(message, this.$t('text.prompt')).then(() => {
            batchAddUsbDevice(selectDatas).then(res => {
              this.$emit('successSubmit')
              this.dlgVisible = false
            })
          }).catch(() => {})
          return;
        }
      }
      batchAddUsbDevice(selectDatas).then(res => {
        this.$emit('successSubmit')
        this.dlgVisible = false
      })
    },
    cancel() {
      if (this.count) {
        this.count--
        this.$emit('closeSubmit')
      }
      this.dlgVisible = false
    },
    //  设备类型-格式处理
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    //  磁盘类型-格式处理
    driverTypeOptionFormatter(row, data) {
      return this.driverTypeMap[row.driverType]
    },
    /**
     * 刷新列表
     */
    refresh() {
      this.$emit('refresh')
    },
    handleTypeCreate() {
      this.loadUsbGroupTree()
      this.$refs['createGroupDlg'].handleCreate();
    },
    createNode(data) {
      this.groupTreeData.push(this.dataToTreeNode(data))
      this.temp.groupId = data.id
    },
    dataToTreeNode(data) {
      return {
        id: 'G' + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name
      }
    }
  }
}
</script>

<style scoped>
.editBtn{
  height: 30px;
  padding: 0 10px !important;
  float: left;
}
</style>
