<template>
  <div class="app-container">

    <div class="table-container">
      <div class="toolbar currentRow">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button icon="el-icon-s-check" size="mini" :disabled="!approvable" @click="handleApproval">
          {{ $t('pages.approval') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.usbSearchInfo')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="approvalUsbList" is-saved-selected saved-selected-prop="volumeName" :col-model="colModel" :row-data-api="rowDataApi" :show-pager="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.usbDevice_text1')"
      :visible.sync="approvalFormVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="approvalForm" :rules="rules" :model="tempApproval" label-position="right" label-width="100px" style="width: 400px;">
        <FormItem :label="$t('pages.approvalOpinion')">
          <el-radio-group v-model="tempApproval.type" @change="changeType()">
            <el-radio :label="1">{{ $t('pages.accessAccept') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.accessReject') }}</el-radio>
          </el-radio-group>
        </FormItem>

        <!-- 设备名称 -->
        <FormItem v-if="!multiChecked && tempApproval.type == 1" :label="$t('table.deviceName')" prop="volumeName">
          <el-input v-model="tempApproval.volumeName" :maxlength="60"></el-input>
        </FormItem>

        <!-- 批量审批时,控制是否修改设备名称 -->
        <FormItem v-if="multiChecked && tempApproval.type == 1" :label="$t('pages.edit_volumeName')">
          <el-radio-group v-model="tempApproval.edit">
            <el-radio :label="1">{{ $t('pages.edit') }}</el-radio>
            <el-radio :label="0" style="margin-left: 28px;">{{ $t('pages.no_edit') }}</el-radio>
          </el-radio-group>
        </FormItem>

        <!-- 批量审批时,修改设备名称 -->
        <FormItem v-if="multiChecked && tempApproval.type==1 && tempApproval.edit == 1" :label="$t('pages.original_volumeName')" prop="usbApprocalId">
          <el-select ref="volumeNameSelect" v-model="tempApproval.usbApprocalId" class="input-with-button" filterable :placeholder="$t('text.select')" @change="changeVolumeName($event)">
            <el-option v-for="item in checkedTableData" :key="item.id" :label="item.volumeName" :value="item.id"/>
          </el-select>
          <el-button :title="$t('pages.edit_volumeName')" class="editBtn" :disabled="editVolumeNameBtn" @click="editVolumeName()"><svg-icon icon-class="edit" /></el-button>
        </FormItem>

        <FormItem v-if="tempApproval.type==1" :label="$t('pages.groupType')" prop="usbGroupId">
          <el-select v-model="tempApproval.usbGroupId" class="input-with-button" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in groupTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
          <el-button :title="$t('pages.addType')" class="editBtn" @click="handleGroupCreate({dataId: 0})"><svg-icon icon-class="add" /></el-button>
        </FormItem>
      </Form>
      <p class="tip">{{ $t('pages.usbDevice_text9') }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveApproval">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="approvalFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量审批时，修改设备名称弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.edit_volumeName')"
      :visible.sync="editVolumeNameVisible"
      width="400px"
    >
      <Form ref="editVolumeNameFrom" :rules="editVolumeNameRules" :model="editVolumeNameTemp" label-position="right" label-width="100px" style="width: 300px;">
        <FormItem :label="$t('pages.original_volumeName')" prop="originalVolumeName">
          <el-input v-model="editVolumeNameTemp.originalVolumeName" disabled :maxlength="60"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.end_volumeName')" prop="endVolumeName">
          <el-input v-model="editVolumeNameTemp.endVolumeName" :maxlength="60"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveVolumeName()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="editVolumeNameVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.usbGroup')"
      :group-tree-data="groupTreeData"
      :add-func="addUsbGroup"
      :edit-valid-func="getGroupByName"
      @addEnd="createNode"
    />
  </div>
</template>
<script>
import {
  addUsbGroup, getGroupByName, getApprovalList, saveApproval, validUsbCode
} from '@/api/behaviorManage/hardware/usbConfig'
import { getUsbTypeDict } from '@/utils/dictionary'
import EditGroupDlg from '@/views/common/editGroupDlg'

export default {
  name: 'UsbApprovalAccess',
  components: { EditGroupDlg },
  props: {
    curActiveTab: {
      type: String,
      default: ''
    },
    groupTreeData: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'volumeName', label: 'volumeName', width: '30', sort: 'custom' },
        { prop: 'terminalName', label: 'terminalName2', width: '30' },
        { prop: 'pnpDeviceId', label: 'pnpDeviceId', width: '30', sort: 'custom' },
        { prop: 'devType', label: 'devType', width: '30', sort: 'custom', formatter: this.usbTypeOptionFormatter },
        { prop: 'driverType', label: 'driverType', width: '30', sort: 'custom', formatter: this.driverTypeOptionFormatter },
        { prop: 'vid', label: 'vid', width: '20', sort: 'custom' },
        { prop: 'model', label: 'manufacturer', width: '30', sort: 'custom' },
        { prop: 'srcPnpDeviceId', label: 'deviceSerialNumber', width: '30', sort: 'custom' },
        { prop: 'pid', label: 'pid', width: '20', sort: 'custom' },
        { prop: 'devSize', label: 'usbSize', width: '20', sort: 'custom' },
        // { prop: 'limitType', label: 'rwPermission', width: '20', formatter: this.limitTypeOptionFormatter },
        { prop: 'reason', label: 'reason', width: '30', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      usbTypeOptions: getUsbTypeDict(),
      showTree: true,
      temp: {},
      tempApproval: {},
      defaulttempApproval: {
        ids: '',
        idArr: [],
        usbName: [],
        codes: [],
        type: 1,
        usbGroupId: null,
        volumeName: '',
        edit: 0,
        usbApprocalId: '',
        tempVolumeName: []
      },
      editVolumeNameTemp: {
        id: '',
        originalVolumeName: '',
        endVolumeName: ''
      },
      rules: {
        usbGroupId: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'change' }
        ],
        volumeName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'change' }
        ]
      },
      editVolumeNameRules: {
        endVolumeName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      approvalFormVisible: false,
      submitting: false,
      treeNodeType: '',
      driverTypeMap: {
        1: this.$t('pages.driverType1'),
        2: this.$t('pages.driverType2')
      },
      limitTypeMap: {
        0: this.$t('pages.readAndWrite'),
        1: this.$t('pages.onlyRead')
      },
      approvable: false,
      checkedTableData: [],
      multiChecked: false,
      editVolumeNameVisible: false,
      editVolumeNameBtn: true,
      usbApprovalObject: {
        id: '',
        volumeName: '',
        status: false
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['approvalUsbList']
    },
    usbGroupTree: function() {
      return this.$refs['usbGroupTree']
    }
  },
  watch: {
    '$store.state.commonData.notice.usbApproval'(val) {
      if (this.curActiveTab === 'UsbApprovalAccess') {
        this.handleRefresh()
      } else {
        this.$emit('changeTabName')
        this.$nextTick(() => {
          this.handleRefresh()
        })
      }
      this.$router.push({ query: {}})
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    addUsbGroup,
    getGroupByName,
    rowDataApi: function(option) {
      option = Object.assign(this.query, option)
      return getApprovalList(option)
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    saveVolumeName() {
      if (this.editVolumeNameTemp.endVolumeName == '' || this.editVolumeNameTemp.endVolumeName == undefined || this.editVolumeNameTemp.endVolumeName == null) {
        this.$notify({
          title: this.$t('text.fail'),
          message: '最终设备名称不能为空',
          type: 'error',
          duration: 2000
        })
        return
      }
      let pos = -1
      this.tempApproval.tempVolumeName.forEach((item, index) => {
        if (item.id == this.editVolumeNameTemp.id) {
          pos = index
        }
      })
      this.tempApproval.tempVolumeName[pos].volumeName = this.editVolumeNameTemp.endVolumeName
      this.tempApproval.tempVolumeName[pos].status = true
      this.editVolumeNameVisible = false
    },
    // 批量审批时，打开编辑设备名称弹窗
    editVolumeName() {
      this.editVolumeNameTemp = {
        id: '',
        originalVolumeName: '',
        endVolumeName: ''
      }
      this.$nextTick(() => {
        this.$refs['editVolumeNameFrom'].clearValidate()
      })
      this.checkedTableData.forEach(item => {
        if (item.id == this.tempApproval.usbApprocalId) {
          this.editVolumeNameTemp.originalVolumeName = item.volumeName
        }
      })
      this.tempApproval.tempVolumeName.forEach(data => {
        if (data.id == this.tempApproval.usbApprocalId && data.status) {
          this.editVolumeNameTemp.endVolumeName = data.volumeName
        }
      })
      this.editVolumeNameTemp.id = this.tempApproval.usbApprocalId
      this.editVolumeNameVisible = true
    },
    // 方法在表格多选框改变时执行
    selectionChangeEnd: function(rowDatas) {
      console.log('rowDatas', rowDatas)
      this.approvable = rowDatas && rowDatas.length > 0
      this.checkedTableData = rowDatas
      this.multiChecked = rowDatas.length > 1
    },
    dataToTreeNode(data) {
      if (this.groupTreeData.length > 0) {
        this.treeNodeType = this.groupTreeData[0].type
      }
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: '0'
      }
    },
    createNode(data) {
      this.tempApproval.usbGroupId = data.id || null
      this.groupTreeData.push(this.dataToTreeNode(data))
    },
    saveDevice() {
      saveApproval(this.tempApproval).then(respond => {
        this.submitting = false
        this.approvalFormVisible = false
        this.gridTable.selectedDatasDelete()
        this.gridTable.execRowDataApi()
        const msg = this.tempApproval.type == 1 ? this.$t('pages.accessAccept1') : this.$t('pages.accessReject1')
        this.$notify({
          title: this.$t('text.success'),
          message: msg,
          type: 'success',
          duration: 2000
        })
      }).catch(res => {
        this.submitting = false
      })
    },
    saveApproval() {
      this.submitting = true
      const rows = this.$refs['approvalUsbList'].getSelectedDatas()
      if (rows.length == 0) {
        this.$message({
          message: this.$t('pages.usbDevice_text3'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      // 验证usb编码是否符合格式
      for (const item of rows) {
        if ((item.pnpDeviceId == '' || item.pnpDeviceId == this.$t('pages.unknown')) && this.tempApproval.type == 1) {
          this.$message({
            message: this.$t('pages.usbDevice_text4'),
            type: 'error',
            duration: 2000
          })
          this.submitting = false
          return
        }
      }
      this.$refs['approvalForm'].validate((valid) => {
        if (valid) {
          this.tempApproval.idArr.splice(0)
          this.tempApproval.usbName.splice(0)
          this.tempApproval.codes.splice(0)
          rows.forEach(item => {
            this.tempApproval.idArr.push(item.id)
            this.tempApproval.usbName.push(item.volumeName + '(' + item.pnpDeviceId + ')')
            this.tempApproval.codes.push(item.pnpDeviceId)
          })
          this.tempApproval.ids = this.tempApproval.idArr.join(',')
          if (this.tempApproval.type == 0) {
            // 不通过审批
            this.saveDevice()
          } else {
            validUsbCode({ usbCode: this.tempApproval.codes.join(',') }).then(res => { // validUsbCode方法用来判断审批的usb编码是否已经存在
              if (res.data != null && res.data.length > 0 && this.tempApproval.type == 1) {
                this.$confirmBox(this.$t('pages.usbDevice_text5', { info: res.data.join('、') }), this.$t('text.prompt')).then(() => {
                  this.saveDevice()
                }).catch(() => {
                  this.submitting = false
                })
              } else {
                this.saveDevice()
              }
            }).catch(() => {
              this.submitting = false
            })
          }
        } else {
          this.submitting = false
        }
      })
    },
    resetQuery() {
      this.query.page = 1
      this.query.usbName = ''
      this.query.usbCode = ''
      this.query.manufacturer = ''
      this.query.usbType = null
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.tempApproval = JSON.parse(JSON.stringify(this.defaulttempApproval))
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(data.dataId)
    },
    initVolueName() {
      const rows = this.$refs['approvalUsbList'].getSelectedDatas()
      if (rows.length == 1) {
        this.tempApproval.volumeName = rows[0].volumeName
      }
    },
    changeVolumeName(event) { // 批量审批，设备名称改变时
      if (this.tempApproval.usbApprocalId != '' || this.tempApproval.usbApprocalId != undefined || this.tempApproval.usbApprocalId != null) {
        this.editVolumeNameBtn = false
      }
    },
    changeType() { // 审批意见改变时
      if (this.tempApproval.volumeName == '' || this.tempApproval.volumeName == undefined || this.tempApproval.volumeName == null) {
        this.initVolueName()
      }
    },
    handleApproval() {  // 打开审批页面
      this.resetTemp()
      // 初始化设备名称
      this.initVolueName()
      this.approvalFormVisible = true
      this.$nextTick(() => {
        // this.$refs['approvalUsbList'].execRowDataApi()
        this.$refs['approvalForm'].clearValidate()
      })
      this.editVolumeNameBtn = true
      this.checkedTableData.forEach((item, i) => {
        this.usbApprovalObject = {
          id: '',
          volumeName: '',
          status: false
        }
        this.usbApprovalObject.id = item.id
        this.usbApprovalObject.volumeName = item.volumeName
        this.tempApproval.tempVolumeName[i] = this.usbApprovalObject
      })
    },
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    driverTypeOptionFormatter(row, data) {
      return this.driverTypeMap[row.driverType]
    },
    limitTypeOptionFormatter(row, data) {
      return this.limitTypeMap[row.limitType]
    },
    // 父组件调用
    checkedUsbGroup() {
      if (!!this.tempApproval.usbGroupId && this.groupTreeData.findIndex(data => data.dataId === this.tempApproval.usbGroupId) === -1) {
        this.tempApproval.usbGroupId = null
      }
    }
  }
}
</script>
<style lang='scss' scoped>
  .currentRow{
    margin-top: 5px;
  }
  .tip {
    width: 400px;
    margin: 13px auto 0;
    color: #409eff;
    font-size: 13px;
    text-align: left;
  }
</style>
