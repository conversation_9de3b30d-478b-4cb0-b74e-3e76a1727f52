<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.usbDevice')" name="UsbLib">
        <Usb-Lib
          ref="UsbLib"
          :limit-type="1"
          :group-tree-data="groupTreeData"
          @submitGroupEnd="submitGroupEnd"
        />
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission('121')" :lazy="true" :label="$t('pages.UsbApprovalAccess')" name="UsbApprovalAccess">
        <Usb-Approval-Access
          ref="UsbApprovalAccess"
          :cur-active-tab="activeName"
          :limit-type="2"
          :group-tree-data="groupTreeData"
          @submitGroupEnd="submitGroupEnd"
          @changeTabName="activeName = 'UsbApprovalAccess'"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import UsbLib from './usbLibrary'
import UsbApprovalAccess from './usbApprovalAccess'
import { findUsbGroupTree } from '@/api/behaviorManage/hardware/usbConfig'

export default {
  name: 'UsbLibrary',
  components: { UsbLib, UsbApprovalAccess },
  data() {
    return {
      activeName: 'UsbLib',
      groupTreeData: []
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.activeName = to.query.tabName || vm.activeName
      vm.$router.push({ query: {}})
    })
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.loadUsbGroupTree()
  },
  activated() {
    this.loadUsbGroupTree()
  },
  methods: {
    loadUsbGroupTree() {
      findUsbGroupTree().then(respond => {
        this.groupTreeData.splice(0, this.groupTreeData.length, ...respond.data)
      })
    },
    submitGroupEnd(fromPage, status, node) {
      if (fromPage === 'lib') {
        this.$refs['UsbApprovalAccess'].approvalFormVisible = false
        // this.$refs['UsbApprovalAccess'].loadUsbTree()
      } else {
        // if (status === 'add') {
        //   this.$refs['UsbLib'].usbGroupTree.addNode(node)
        // } else {
        //   this.$refs['UsbLib'].usbGroupTree.updateNode(node)
        // }
      }
    },
    tabClick(pane, event) {
      if (this.activeName === 'UsbApprovalAccess') {
        this.$refs['UsbApprovalAccess'].checkedUsbGroup()
      } else {
        this.$refs.UsbLib.loadReceiverTree()
        this.$refs.UsbLib.handleRefresh()
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.module-form{
  margin-left: 210px;
  height: 100%;
  overflow: auto;
  .el-tabs{
    height: calc(100% - 40px);
  }
  .el-tab-pane{
    padding: 0 10px 10px;
  }
}
.app-container .tree-container.hidden+.module-form{
  margin-left: 0;
}
</style>
