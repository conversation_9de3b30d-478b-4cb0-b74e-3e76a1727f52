<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.usbDevice_create')"
      :remark="$t('pages.usbDevice_text7')"
      :visible.sync="dlgVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="toolbar">
        <el-row>
          <el-col :span="2" style="font-weight: 700;padding-top: 7px;">{{ $t('pages.groupType') }}</el-col>
          <el-col :span="6">
            <tree-select ref="groupTree" v-model="query.groupId" :data="treeData" :width="200" @change="treeNodeClick" />
          </el-col>
          <el-col :span="16">
            <div style="float: right;">
              <el-input v-model="query.usbName" v-trim clearable :placeholder="$t('pages.deviceName_1')" style="width: 200px;"/>
              <el-button type="primary" icon="el-icon-search" size="mini" style="margin: 0 0 5px 0" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
              <el-button type="primary" icon="el-icon-setting" size="mini" style="margin: 0 0 5px 0" :disabled="!updateable" @click="handleSetting">{{ $t('pages.setTime') }}</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <grid-table
        ref="appInfoList"
        :height="420"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        is-saved-selected
        saved-selected-prop="usbName"
        :selected-data-label-formatter="labelFormatter"
        :selected-data-title-formatter="labelFormatter"
        :default-sort="{ prop: 'usbName,id', order: 'ascending' }"
        :page-sizes="[ 20, 50, 100, 500, 1000 ]"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :menu-code="'A5B'" :link-url="'/system/baseData/usbDevice'" :btn-text="$t('pages.maintainUsb')"/>
        <el-button type="primary" :loading="submitting" @click="handleSelect()">
          {{ $t('pages.addSelectedDevice') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-time-dlg ref="editTimeDlg" :show-name="showName" @update="updateData" />
  </div>
</template>

<script>
import { findUsbGroupTree, findUsbDeviceList } from '@/api/behaviorManage/hardware/usbConfig'
import { getUsbTypeDict } from '@/utils/dictionary'
import editTimeDlg from '@/views/behaviorManage/hardware/driver/editTimeDlg'

export default {
  name: 'UsbSelectDlg',
  components: { editTimeDlg },
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'usbName', label: 'volumeName', width: '100', sort: 'custom' },
        { prop: 'groupName', label: 'sourceGroup', width: '100', sort: 'custom' },
        { prop: 'usbCode', label: 'pnpDeviceId', width: '120', sort: 'custom' },
        { prop: 'usbType', label: 'devType', width: '100', sort: 'custom', formatter: this.usbTypeOptionFormatter },
        { prop: 'exceptionTime', label: 'exceptionTime', width: '120' },
        { prop: 'rwPermission', label: 'rwPermission', width: '120', formatter: this.rwPermissionTypeOptionFormatter },
        { prop: 'vid', label: 'vid', width: '90', sort: 'custom' },
        { prop: 'manufacturer', label: 'manufacturer', width: '100', sort: 'custom' },
        { prop: 'pid', label: 'pid', width: '90', sort: 'custom' },
        { prop: 'srcPnpDeviceId', label: 'deviceSerialNumber', width: '120', sort: 'custom' },
        { prop: 'usbSize', label: 'usbSize', width: '120', sort: 'custom' },
        { prop: '', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '70',
          buttons: [
            { label: 'edit', click: this.handleConfig }
          ]
        }
      ],
      usbTypeOptions: getUsbTypeDict(),
      rwPermissionTypeOptions: [{ value: 0, label: this.$t('pages.readAndWrite') }, { value: 1, label: this.$t('pages.onlyRead') }, { value: 2, label: this.$t('pages.forbid') }],
      query: { // 查询条件
        page: 1,
        usbName: '',
        groupId: 0
      },
      dlgVisible: false,
      submitting: false,
      selectTreeId: null,
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.usbDevice'), parentId: '', children: [] }],
      showName: true,
      updateable: false,
      selectDatas: [] // 保存列表已选中的数据
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.selectDatas = []
      this.selectTreeId = null
      this.query.groupId = 0
      this.query.usbName = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.softTable && this.softTable.clearSaveSelection()
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNode()
      })
    },
    treeNodeClick(data, node) {
      this.selectTreeId = node.dataId
      this.handleFilter()
    },
    afterLoad(rowData, grid) {
      if (rowData && rowData.length > 0) {
        rowData.forEach(row => {
          if (row.rwPermission == undefined) {
            row.rwPermission = 0
          }
        })
      }
      // 放行时间/读写权限未存表，处理因翻页导致已设置的放行时间被清除问题
      if (rowData && this.selectDatas && this.selectDatas.length > 0) {
        // 得到已勾选数据中有配置放行时间/读写权限的数据，用于翻页后重新赋值
        const selectData = this.selectDatas.filter(data => data.exceptionTime || data.rwPermission == 0 || data.rwPermission == 1)
        if (selectData && selectData.length > 0) {
          selectData.forEach(data => {
            const replaceIndex = rowData.findIndex(item => item.id == data.id)
            if (replaceIndex > -1) {
              rowData.splice(replaceIndex, 1, data)
            }
          })
        }
      }
    },
    labelFormatter(row, prop) {
      return `${row.usbName} - [${row.usbCode}]`
    },
    selectionChangeEnd(rowDatas) {
      this.updateable = rowDatas && rowDatas.length > 0
      if (!(rowDatas && rowDatas.length > 0)) return
      this.selectDatas.splice(0, this.selectDatas.length, ...rowDatas)
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    handleSetting() {
      this.showName = false
      this.$refs.editTimeDlg.show()
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        this.selectTreeId = 0
      }
      this.query.groupId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      newOption.groupId = this.selectTreeId
      return findUsbDeviceList(newOption)
    },
    loadGroupTree: function() {
      findUsbGroupTree().then(respond => {
        this.treeData[0].children = respond.data
        this.$nextTick(() => {

        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      if (!(this.softTable.getSelectedDatas() && this.softTable.getSelectedDatas().length > 0)) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.selectTips'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$emit('select', this.softTable.getSelectedDatas())
      this.dlgVisible = false
    },
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    rwPermissionTypeOptionFormatter(row, data) {
      let msg = ''
      this.rwPermissionTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    handleConfig(row) {
      if (row.exceptionTime) {
        row.startTime = row.exceptionTime.split('~')[0]
        row.endTime = row.exceptionTime.split('~')[1]
      }
      if (row.rwPermission == undefined) {
        row.rwPermission = 0
      }
      this.showName = true
      this.$refs.editTimeDlg.show(row)
    },
    updateData(newData) {
      if (newData.rwPermission != undefined || (newData.startTime && newData.endTime)) {
        if (this.showName) {
          newData.exceptionTime = newData.startTime || newData.endTime ? newData.startTime + '~' + newData.endTime : ''
          this.softTable.updateRowData(newData)
        } else {
          const datas = this.softTable.getSelectedDatas()
          datas.forEach((data) => {
            data.startTime = newData.startTime
            data.endTime = newData.endTime
            data.exceptionTime = newData.startTime || newData.endTime ? newData.startTime + '~' + newData.endTime : ''
            if (newData.checkRw == 1) {
              data.rwPermission = newData.rwPermission
            }
            this.softTable.updateRowData(data)
          })
        }
        const index = this.selectDatas.findIndex(item => item.id == newData.id)
        if (index == -1) {
          this.selectDatas.push(newData)
        }
      }
    }
  }
}
</script>
