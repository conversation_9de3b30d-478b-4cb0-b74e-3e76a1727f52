<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'259'" icon="el-icon-setting" size="mini" @click="handleSetting">{{ $t('button.highConfig') }}</el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleConfig">{{ $t('pages.alarmSetup_text10') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('table.ruleName')">
                <el-input v-model="query.name" v-trim clearable :maxlength="300"/>
              </FormItem>
              <FormItem :label="$t('table.treatmentMeasure')">
                <el-select v-model="query.alarmLimits" multiple clearable collapse-tags style="width: 100%">
                  <el-option v-for="item in alarmLimitOps" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table ref="ruleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogVisible"
      :width="$store.getters.language === 'en' ? '680px' : '600px'"
      @closed="dialogStatus = ''"
    >
      <Form ref="ruleForm" :model="temp" :rules="rules" label-position="right" label-width="80px">
        <FormItem :label=" $t('table.ruleName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"></el-input>
        </FormItem>
      </Form>
      <ResponseContent
        ref="responseContent"
        :status="dialogStatus"
        :show-data="temp"
        @getChecked="getChecked"
      />
      <p v-show="temp.alarmLimit===0" style="font-size:12px;color:red;margin:5px 0 0 18px">{{ $t('pages.alarmSetup_text2') }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="handleCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="$t('pages.alarmSetup_delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :delete-filter-name="$t('table.violationName')"
      :edit-type="2"
      @submitEnd="deleteData"
    />

    <config-alarm ref="configAlarm"/>
    <pop-up-config ref="termConfigDlg" @submit="submit"></pop-up-config>
  </div>
</template>

<script>
import { getRulePage, createRule, updateRule, deleteRule, validRuleId, getRuleByName } from '@/api/system/baseData/alarmSetup'
import { getAlarmLimitDict } from '@/utils/dictionary'
import { isSameTimestamp, initTimestamp } from '@/utils'
import { getConfig } from '@/api/system/configManage/globalConfig'
import ResponseContent from './responseContent'
import ConfigAlarm from './configDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import PopUpConfig from '@/views/system/configManage/popUpConfig'

export default {
  name: 'AlarmSetup',
  components: { ConfigAlarm, ResponseContent, BatchEditPageDlg, PopUpConfig },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName1', width: '100', sort: 'custom', fixed: true },
        { prop: 'alarmLimit', label: 'treatmentMeasure', width: '200', formatter: this.alarmLimitFormatter },
        { prop: 'msgFormType', label: 'alarmPara', width: '150', formatter: this.termAlarmFormatter },
        { prop: 'sysUsers', label: 'alarmAdmin', width: '150', formatter: this.sysUserFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        alarmLimits: [],
        alarmLimitSum: undefined
      },
      temp: {},
      defaultTemp: {
        id: '',
        name: '',
        alarmLimit: 3,
        msgFormClose: 30,
        msgFormPosition: 0,
        msgFormType: 0,
        sysUserIds: [],
        emails: [],
        sms: [],
        wechatUserIds: [],
        userOrGroupIds: [],
        appUserAndUserTags: [],
        configMethod: null
      },
      // 保存旧的响应措施
      oldAlarmLimit: 0,
      checkedArr: [],
      configForm: {},
      dataList: {
        screenshotInterval: { label: this.$t('pages.screenCaptureInterval'), value: '', min: 1, max: 60 },
        screenshotNum: { label: this.$t('pages.numberOfScreenshots'), value: '', min: 1, max: 10 },
        recordTime: { label: this.$t('pages.screenRecordingDuration'), value: '', min: 1, max: 3600 }
      },
      rules: {
        name: [
          { required: true, trigger: 'blur', validator: this.ruleNameValidator }
        ]
      },
      deleteable: false,
      dialogVisible: false,
      dialogConfigVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.alarmSetup_update'),
        create: this.$t('pages.alarmSetup_create')
      },
      checked: [],
      submitting: false,
      alarmLimitOps: []
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    screenData() {
      const data = {}
      const keys = ['screenshotInterval', 'screenshotNum', 'recordTime']
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        data[key] = this.dataList[key]
      }
      return data
    },
    ruleTable() {
      return this.$refs['ruleList']
    }
  },
  watch: {
    'query.alarmLimits'(val) {
      this.query.alarmLimitSum = val.reduce((prev, cur) => prev + cur, 0)
    }
  },
  created() {
    initTimestamp(this)
    this.getConfig()
    this.alarmLimitOps = getAlarmLimitDict()
  },
  activated() {
    if (!isSameTimestamp(this, 'Administrator')) {
      this.ruleTable.execRowDataApi(this.query)
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.ruleTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    // 表单验证
    checkValid() {
      let validation2
      let validation4
      let validation8
      let validation16
      // 第三方渠道
      let validation2048
      // 终端弹窗告警部分
      if (this.checked.includes(2)) {
        this.$refs['responseContent'].$refs['dataForm'][0].validate((valid) => { validation2 = !!valid })
      } else {
        validation2 = true
      }
      // 控制台告警部分
      if (this.checked.includes(4)) {
        this.$refs['responseContent'].$refs['sysUserForm'][0].validate((valid) => { validation4 = !!valid })
      } else {
        validation4 = true
      }
      // 邮件告警部分
      if (this.checked.includes(8) && this.temp.emails.length == 0) {
        this.$message({
          message: this.$t('pages.alarmSetup_text8'),
          type: 'error'
        })
      } else {
        validation8 = true
      }
      // 短信告警部分
      if (this.checked.includes(16) && this.temp.sms.length == 0) {
        this.$message({
          message: this.$t('pages.alarmSetup_text9'),
          type: 'error'
        })
      } else {
        validation16 = true
      }
      // 钉钉告警部分
      if (this.checked.includes(validation2048)) {
        this.$refs['responseContent'].$refs['operatorForm'][0].validate((valid) => { validation2048 = !!valid })
      } else {
        validation2048 = true
      }
      return validation2 && validation4 && validation8 && validation16 && validation2048
    },
    async validLockScreen(data) {
      var flag = true
      await validRuleId({ id: data.id }).then(respond => {
        if (respond.data.length && ((data.alarmLimit & 32) == 32 || (data.alarmLimit & 2) != 2)) {
          flag = false
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.alarmSetupValidLockScreen'),
            type: 'warning',
            duration: 5000
          })
        }
      })
      return flag
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.resetTemp()
        this.$refs['ruleForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      const temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row)))
      this.oldAlarmLimit = temp.alarmLimit
      temp.msgFormClose = row.msgFormClose || undefined
      temp.sysUserIds = temp.sysUserIds ? (temp.sysUserIds.includes(0) ? [0] : temp.sysUserIds) : []
      temp.wechatUserIds = temp.wechatUserIds.map(id => String(id))
      temp.userOrGroupIds = temp.userOrGroupIds ? (temp.userOrGroupIds.includes(2048) ? [0] : temp.userOrGroupIds) : []
      this.dialogVisible = true
      this.$nextTick(() => {
        this.temp = temp
        this.$refs['ruleForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid && this.checkValid() && this.temp.alarmLimit !== 0) {
          const data = this.formatData(this.temp)
          createRule(data).then(res => {
            this.submitting = false
            this.dialogVisible = false
            this.ruleTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['ruleForm'].validate(async(valid) => {
        var flag = true
        await this.validLockScreen(this.temp).then(res => {
          flag = res
        })
        if (valid && this.checkValid() && this.temp.alarmLimit !== 0 && flag) {
          const data = this.formatData(this.temp)
          data['oldAlarmLimit'] = this.oldAlarmLimit
          updateRule(data).then(res => {
            this.submitting = false
            this.dialogVisible = false
            this.ruleTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    getConfig() {
      getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'screenshotInterval') {
            this.dataList[item.key].value = item.value
          } else if (item.key === 'screenshotNum') {
            this.dataList[item.key].value = item.value
          } else if (item.key === 'recordTime') {
            this.dataList[item.key].value = item.value
          }
        }
      })
    },
    formatData(data) {
      const temp = { ...data }
      temp.msgFormClose = temp.msgFormClose || 0
      // temp.sysUserIds = temp.sysUserIds.includes(0) ? this.sysUserList.map(item => item.id) : temp.sysUserIds
      temp.terminalAlarmParam = this.termAlarmFormatter(temp);
      temp.alarmLimitDescribe = this.alarmLimitFormatter(temp);
      return temp
    },
    handleDelete() {
      const selectedData = this.ruleTable.getSelectedDatas()
      const total = this.ruleTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleCancel() {
      this.dialogVisible = false
    },
    handleSetting() {
      this.$refs['configAlarm'].show()
    },
    handleConfig() {
      this.$refs['termConfigDlg'].show()
    },
    getChecked(alarmLimit, obj) {
      this.checked = alarmLimit
      const alarmLimitSum = alarmLimit.reduce((total, curr) => {
        return total + curr
      }, 0)
      this.temp = Object.assign(this.temp, obj, { name: this.temp.name, alarmLimit: alarmLimitSum })
    },
    submit(data) {
      if (data) {
        this.handleFilter()
      }
    },
    alarmLimitFormatter(row, data) {
      const result = []
      const alarmLimitOps = getAlarmLimitDict()
      alarmLimitOps.forEach(op => {
        const isCheck = row.alarmLimit & op.value;
        if (isCheck !== 0) {
          result.push(op.label)
        }
      })
      return result.join('，')
    },
    termAlarmFormatter(row, data) {
      if ((row.alarmLimit & 2) === 0) {
        return ''
      }
      const msgFormType = { 0: this.$t('pages.msgFormType1'), 1: this.$t('pages.msgFormType2'), 2: this.$t('pages.msgFormType3') }
      const msgFormPosition = { 0: this.$t('pages.msgFormPosition1'), 1: this.$t('pages.msgFormPosition2') }
      let result = msgFormType[row.msgFormType]
      if (row.msgFormType !== 2) {
        result += '，' + msgFormPosition[row.msgFormPosition] + '，'
        if (row.msgFormClose === 0) {
          result += this.$t('pages.msgFormClose1')
        } else {
          result += this.$t('pages.msgFormClose2', { second: row.msgFormClose })
        }
      }
      return result
    },
    sysUserFormatter(row, data) {
      if ((row.alarmLimit & 4) === 0) {
        return ''
      }
      return row.sysUserIds && row.sysUserIds[0] === 0 ? this.$t('pages.allSysUsers') : row.sysUsers
    },
    handleDrag() {
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.ruleTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    ruleNameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const info = respond.data
          if (info && info.id != this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.alarmLimits = []
    }
  }
}
</script>

<style lang="scss">
.alarm-confirmBox{
  .el-message-box__status{
    position: absolute;
    top:23px
  }
}
</style>
