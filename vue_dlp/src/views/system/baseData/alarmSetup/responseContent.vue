<!--
  /**
  * 违规响应规则的配置组件，用于配置违规响应规则及策略中展示、选择已配置的违规响应规则
  * 2023.5.16 去掉短信告警，因终端触发的告警总数较多，去掉短信告警实现，如果需要查看相关实现，在23.5.16之前的svn版本查看
  * @date
  * 调用示例：
    <ResponseContent
      :status="status"                    // 标识当前是新增或修改/查看的状态
      :show-data="showData"               // 外部传入的规则数据
      :show-select="true"                 // 是否显示规则下拉框
      :editable="editable"                // 下拉选择框是否可编辑
      :read-only="true"                   // 规则内容只读模式
      :show-check-rule="showCheckRule"    // 是否显示规则下拉框前的多选框
      :prop-check-rule="propCheckRule"    // 是否允许选择规则,
      :prop-rule-id="propRuleId"          // 传入选择的规则id
      :is-respond-rule="isRespondRule"    // 是否内容策略响应规则的配置
      :select-style="selectStyle"         // 下拉选择框的样式
      :filter-data="filterData"           // 过滤函数
      :check-empty-rule="checkEmptyRule"  // 是否校验空规则,默认true校验，false不校验允许为空
      :ignore-rule-types="ignoreRuleTypes"// 忽略展示的响应规则类型
      @getRuleId="getRuleId"              // 当前选择的规则id
      @getChecked="getChecked"            // 当前已勾选的响应动作数组
    />

  */
-->

<template>
  <div>
    <!-- 下拉选择框，用于策略配置时，选择规则 -->
    <div v-if="showSelect" :style="Object.assign({'margin-bottom': errorTipShow ? '20px' : '10px', 'position': 'relative' }, selectStyle)">
      <i :class="isShow?'el-icon-caret-bottom':'el-icon-caret-right'" @click="()=>{isShow = !isShow}"></i>
      <el-checkbox v-show="showCheckRule" v-model="checkRule" :disabled="!editable" @change="handleCheck">{{ ruleTypeName }}</el-checkbox>
      <span v-show="showRuleName">{{ ruleTypeName }}</span>
      <div :class="{'select-con': true, 'mb10': errorTipShow }">
        <el-select
          ref="ruleSelect"
          v-model="ruleId"
          :placeholder="$t('text.select') "
          size="mini"
          :disabled="!editable || !checkRule"
          :clearable="clearable"
          @visible-change="visibleChange"
          @change="handleChange"
        >
          <el-option v-for="item in allRules" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
        <slot name="errorTip"><span v-show="errorTipShow" class="error-tip">{{ $t('pages.alarmSetup_text5') }}</span></slot>
      </div>
      <link-button v-if="editable" btn-class="editBtn" btn-style="float: none; margin-bottom: 0;" :menu-code="'A5C'" :link-url="'/system/baseData/alarmSetup'" />
      <slot name="tail"></slot>
    </div>
    <!-- 响应规则的详细配置信息 -->
    <div style="position:relative">
      <el-row v-show="showItems" style="margin:13px 0 0 30px;">
        <el-col v-for="(item, index) in options.filter(v => !ignoreRuleTypes.some(i => v.value == i))" :key="index" :span="24">
          <!-- 配置项选择框 -->
          <el-checkbox v-model="item.checked" :label="item.value" style="margin-bottom:10px" :disabled="readOnly" @change="handleCheckChange">{{ item.label }}</el-checkbox>

          <!-- 终端弹窗告警附属功能 -->
          <Form v-if="item.value == 2 && checked.includes(2)" v-show="showSelect" ref="dataForm" :model="temp" label-position="right" label-width="90px">
            <!-- <FormItem :label=" $t('pages.configMethod')">
              <el-radio-group v-model="temp.configMethod" :disabled="true">
                <el-radio :label="1">{{ $t('pages.configMethod1') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.configMethod2') }}</el-radio>
              </el-radio-group>
            </FormItem> -->
            <FormItem :label=" $t('pages.msgFormType')">
              <el-radio-group v-model="temp.msgFormType" :disabled="true">
                <el-radio :label="0">{{ $t('pages.msgFormType4') }}</el-radio>
                <el-radio :label="1">{{ $t('pages.msgFormType5') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.msgFormType6') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem :label=" $t('pages.msgFormPosition')">
              <el-radio-group v-model="temp.msgFormPosition" :disabled="true">
                <el-radio :label="0">{{ $t('pages.msgFormPosition3') }}</el-radio>
                <el-radio :label="1">{{ $t('pages.msgFormPosition4') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem label-width="25px">
              <el-checkbox v-model="autoClose" :disabled="true" @change="checkChange">
                <i18n :class="{ disabled: true }" path="pages.autoOffWindow">
                  <el-input-number
                    slot="info"
                    v-model="temp.msgFormClose"
                    :controls="false"
                    :disabled="true"
                    :min="1"
                    :max="86400"
                    style="width: 80px;"
                    @blur="msgFormCloseBlur"
                  />
                </i18n>
              </el-checkbox>
            </FormItem>
            <!--<FormItem v-show="isRespondRule" label-width="26px">
              <el-checkbox :label="0" :disabled="readOnly" @change="(val) => { temp.showSensContent = val ? 1 : 0 }">显示违规内容</el-checkbox>
            </FormItem>-->
          </Form>
          <!-- 控制台告警附属功能 -->
          <Form v-if="item.value == 4 && checked.includes(4)" ref="sysUserForm" :model="temp" style="margin:0 0 10px 20px" :disabled="readOnly">
            <FormItem ref="sysUser" prop="sysUserIds" :rules="[{ required:true, message: $t('pages.alarmSetup_text7'), trigger: 'change'}]">
              <el-select v-model="temp.sysUserIds" filterable multiple :placeholder="$t('pages.alarmSetup_text7')" @change="sysUserChange">
                <el-option v-for="user in sysUserList" :key="user.id" :label="user.name" :value="user.id"/>
              </el-select>
            </FormItem>
          </Form>
          <!-- 微信公众号告警 -->
          <Form v-if="item.value == 512 && checked.includes(512)" ref="wechatAlarmForm" label-width="25px">
            <FormItem
              ref="wechatAlarm"
              prop="wechatUserIds"
              :rules="[{trigger: 'change', validator: validWeChat}]"
            >
              <wechat-user-select
                ref="wechatUserSelect"
                :editable="!readOnly"
                :check-keys="temp.wechatUserIds"
                :wechat-tree-data="wechatTreeData"
                :width="500"
                @getChangeValue="getSelectWechatUser"
                @getTreeData="getWechatTreeData"
              />
            </FormItem>
          </Form>
          <!-- 短信告警 因终端触发的告警总数较多，去掉短信告警实现 v-if 恒为false -->
          <data-editor
            v-if="false && item.value == 16 && checked.includes(16)"
            append-to-body
            :formable="!readOnly"
            :popover-width="500"
            :updateable="smsUpdateable"
            :deletable="smsDeleteable"
            :add-func="createSMS"
            :update-func="updateSMS"
            :delete-func="deleteSMS"
            :cancel-func="cancelSMS"
            :before-update="beforeUpdateSMS"
          >
            <Form ref="smsForm" :model="smsTemp" :rules="smsRules" label-position="right" label-width="85px" style="margin-left: -10px">
              <FormItem :label="$t('table.smsName')" prop="name">
                <el-input v-model="smsTemp.name"/>
              </FormItem>
              <FormItem :label="$t('table.smsNumber')" prop="number">
                <el-input v-model="smsTemp.number"/>
              </FormItem>
              <FormItem :label="$t('table.remark')" prop="remark">
                <el-input v-model="smsTemp.remark"/>
              </FormItem>
            </Form>
          </data-editor>
          <grid-table
            v-if="false && item.value == 16 && checked.includes(16)"
            ref="smsTable"
            :height="150"
            :multi-select="true"
            :show-pager="false"
            :selectable="() => !readOnly"
            :col-model="smsColModel"
            :row-datas="temp.sms"
            :autoload="false"
            style="margin-bottom: 10px;"
            @selectionChangeEnd="smsSelectionChange"
          />
          <!-- 邮件告警附属功能 -->
          <empty-mail-server-prompt v-if="item.value == 8 && checked.includes(8)"/>
          <data-editor
            v-if="item.value == 8 && checked.includes(8)"
            append-to-body
            :formable="!readOnly"
            :popover-width="500"
            :updateable="updateable"
            :deletable="deleteable"
            :add-func="createEmail"
            :update-func="updateEmail"
            :delete-func="deleteEmail"
            :cancel-func="cancelEmail"
            :before-update="beforeUpdateEmail"
          >
            <Form ref="emailForm" :model="emailTemp" :rules="emailRules" label-position="right" label-width="85px" style="margin-left: -10px">
              <FormItem :label="$t('table.emailName')" prop="name">
                <el-input v-model="emailTemp.name" v-trim maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.emailAddress')" prop="address">
                <el-input v-model="emailTemp.address" maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.remark')" prop="remark">
                <el-input v-model="emailTemp.remark" type="textarea" show-word-limit maxlength="100"/>
              </FormItem>
            </Form>
          </data-editor>
          <grid-table
            v-if="item.value == 8 && checked.includes(8)"
            ref="emailTable"
            :height="150"
            :multi-select="true"
            :show-pager="false"
            :selectable="() => !readOnly"
            :col-model="emailColModel"
            :row-datas="temp.emails"
            :autoload="false"
            style="margin-bottom: 10px;"
            @selectionChangeEnd="handleSelectionChange"
          />
          <!-- <Form v-if="item.value === 2048 && checked.includes(2048)" ref="operatorForm" :model="temp" style="margin:0 0 10px 20px" :disabled="readOnly">
            <tree-select-panel
              ref="userSelectTree"
              leaf-key="user"
              :check-strictly="false"
              :local-search="false"
              :default-checked-keys="defaultCheckedKeys"
              :include-child="false"
              :selected-node-filter="selectedBindedNodeFilter"
              :to-select-title="$t('pages.selectableBindUser')"
              :selected-title="$t('pages.selectedBindUser')"
              height="350px"
              @checkedData="checkedUserData"
            >
            </tree-select-panel>
          </Form> -->

          <div v-if="item.value == 2048 && checked.includes(2048)">
            <el-button size="small" @click="handleImportAppUser">导入人员</el-button>
            <el-button size="small" @click="handleImportAppUserTag">导入标签</el-button>
            <el-button size="small" :disabled="!appUserDeletable" @click="handleDeleteAppUserAndUserTag">{{ $t('button.delete') }}</el-button>
            <grid-table
              ref="appTable"
              :height="150"
              :multi-select="true"
              :show-pager="false"
              :selectable="() => !readOnly"
              :col-model="appColModel"
              :row-datas="temp.appUserAndUserTags"
              :autoload="false"
              style="margin-bottom: 10px;"
              @selectionChangeEnd="handleAppUserOrUserTagDelete"
            />
          </div>

        </el-col>
      </el-row>
    </div>
    <mail-import-table-dlg ref="mailImportTableDlg" @submitEnd="importEnd"/>
    <app-user-import-dlg ref="appUserImportTableDlg" @submitEnd="importAppUser"/>
    <app-user-tag-import-dlg ref="appUserTagImportTableDlg" @submitEnd="importAppUserTag" />
  </div>
</template>

<script>
import { getPopUpConfig } from '@/api/system/configManage/popUpConfig'
import { getAlarmLimitDict } from '@/utils/dictionary'
import { initTimestamp } from '@/utils'
import MailImportTableDlg from '@/views/system/baseData/groupImportList/mailImportTableDlg';
import EmptyMailServerPrompt from '@/views/system/deviceManage/mailServer/prompt'
// import TreeSelectPanel from '@/components/TreeSelectPanel'
// import WechatUserSelect from '@/components/WechatUserSelect';
import AppUserImportDlg from './appUserImportDlg.vue';
import AppUserTagImportDlg from './appUserTagImportDlg.vue';

const channelMap = {
  1: '邮件',
  2: '钉钉'
};

const typeMap = {
  1: '人员',
  2: '标签'
}

export default {
  name: 'ResponseContent',
  components: { EmptyMailServerPrompt, MailImportTableDlg, /* TreeSelectPanel, */ AppUserImportDlg, AppUserTagImportDlg /* WechatUserSelect*/ },
  props: {
    // 标识当前是新增或修改/查看的状态
    status: {
      type: String,
      default() {
        return ''
      }
    },
    // 外部传入的规则数据
    showData: {
      type: Object,
      default() {
        return {}
      }
    },
    // 是否显示规则下拉选择框
    showSelect: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 下拉选择框是否可编辑
    editable: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 规则内容只读模式
    readOnly: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 是否显示规则名
    showRuleName: {
      type: Boolean,
      default: false
    },
    // 规则名
    ruleTypeName: {
      type: String,
      default() {
        return this.$t('pages.alarmSetup')
      }
    },
    // 是否显示规则下拉框前的多选框
    showCheckRule: {
      type: Boolean,
      default: true
    },
    // 当不显示规则下拉框前的多选框时，通过该值控制是否允许选择规则
    propCheckRule: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 传入选择的规则id
    propRuleId: {
      type: Number,
      default() {
        return undefined
      }
    },
    // 是否内容策略响应规则的配置
    isRespondRule: {
      type: Boolean,
      default() {
        return false
      }
    },
    selectStyle: {
      type: Object,
      default() {
        return {}
      }
    },
    filterData: {
      type: Function,
      default(item) {
        return item
      }
    },
    checkEmptyRule: {
      type: Boolean,
      default() {
        return true
      }
    },
    ignoreRuleTypes: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否允许可清空 选择违规响应规则的选择框内容
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      emailColModel: [
        { prop: 'name', label: 'emailName', width: '100' },
        { prop: 'address', label: 'emailAddress', width: '120' },
        { prop: 'remark', label: 'remark', width: '100' }
      ],
      appColModel: [
        { prop: 'name', label: '姓名/标签', width: '100' },
        { prop: 'type', label: '类型', width: '80', formatter: this.formatterType },
        { prop: 'channel', label: '推送渠道', width: '150', formatter: this.formatterChannel }
      ],
      smsColModel: [
        { prop: 'name', label: 'smsName', width: '100' },
        { prop: 'number', label: 'smsNumber', width: '120' },
        { prop: 'remark', label: 'remark', width: '100' }
      ],
      emailRules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }],
        address: [
          { required: true, type: 'email', message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.emailAddressCheck, trigger: 'blur' }
        ]
      },
      // smsRules: {
      //   name: [{ required: true, message: this.$t('pages.validateMsg_smsName'), trigger: 'blur' }],
      //   number: [{ required: true, type: 'number', trigger: 'blur', validator: this.checkPhone }]
      // },
      channelMap,
      typeMap,
      options: getAlarmLimitDict().map(item => { item.checked = false; return item }), // 获取响应规则选项，并添加checked属性
      autoClose: true,      // 终端弹窗告警-自动关闭弹窗配置的checkbox的值
      temp: {},
      defaultTemp: {
        msgFormClose: '',   // 终端弹窗是否自动关闭
        msgFormPosition: 0, // 终端弹窗位置
        msgFormType: 0,     // 终端弹窗方式
        // showSensContent: 0,
        sysUserIds: [],     // 控制台告警通知的管理员id
        userOrGroupIds: [], // 第三方平台推送的用户或组id
        emails: [],         // 邮件告警配置的邮箱信息
        appUserAndUserTags: [], // 用户或标签人员
        sms: [],
        wechatUserIds: [],  // 微信公众号绑定用户信息
        configMethod: 1     // 终端弹窗配置方式： 1. 默认配置 2. 自定义配置
      },
      defaultConfig: {
        configMethod: 0,
        msgFormClose: 30,
        msgFormPosition: 0,
        msgFormType: 0
      },
      emailTemp: {},        // 新增邮箱信息的temp
      smsTemp: {},          // 新增短信信息的temp
      defaultEmailTemp: {
        id: null,
        name: '',           // 邮箱名
        address: '',        // 邮箱地址
        remark: ''          // 备注
      },
      defaultSMSTemp: {
        id: null,
        name: '',           // 短信昵称
        number: '',         // 手机号码
        remark: ''          // 备注
      },
      deleteable: false,    // 邮箱删除按钮是否可用
      updateable: false,    // 邮箱修改按钮是否可用
      smsDeleteable: false, // 短信删除按钮是否可用
      smsUpdateable: false, // 短信修改按钮是否可用
      appUserDeletable: false, // 第三方删除按钮

      checked: [],          // 响应规则勾选的配置选项
      checkRule: false,     // 规则下拉框前的多选框是否勾选
      ruleId: undefined,
      wechatTreeData: [],   // 微信公众号的树节点数据
      isShow: false,        // 是否显示规则配置项（下拉选择框前的三角号）
      defaultCheckedKeys: []       // 是否显示规则配置项（下拉选择框前的三角号）
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    // 显示规则配置项
    showItems() {
      // 显示下拉选择框时，根据isShow判断是否显示规则配置项；不显示下拉框时，默认显示规则配置项
      return this.showSelect ? this.isShow : true
    },
    // 过滤后的响应规则数据
    resData() {
      return this.filterData(this.$store.getters.alarmRules)
    },
    // 响应规则下拉框中的选项options
    allRules() {
      return this.resData.map(item => {
        return {
          label: item.name,
          value: item.id
        }
      })
    },
    // 需要配置规则却未配置时，显示提示消息
    errorTipShow() {
      if (this.checkEmptyRule) {
        const validate = this.checkRule && !this.ruleId
        this.$emit('validate', !validate)
        return validate
      } else {
        this.$emit('validate', true)
        return false
      }
    }
  },
  watch: {
    checked() {
      this.options.forEach(item => {
        this.$set(item, 'checked', this.checked.includes(item.value))
      })
      this.$emit('getChecked', this.checked, this.temp)
    },
    temp: {
      handler: function() {
        this.$emit('getChecked', this.checked, this.temp)
      },
      deep: true
    },
    allRules: {
      handler: function() {
        if (this.ruleId) {
          const index = this.allRules.findIndex(item => item.value === this.ruleId)
          if (index < 0) {
            this.ruleId = undefined
            this.resetTemp()
          } else {
            this.handleChange(this.ruleId)
          }
        }
      },
      deep: true
    },
    showData(val) {
      if (this.ruleId == val.id) {
        this.init()
      } else {
        this.ruleId = val.id
      }
    },
    propCheckRule(val) {
      this.checkRule = val
    },
    propRuleId(val) {
      this.ruleId = val
    },
    ruleId(val) {
      this.init()
    }
  },
  created() {
    initTimestamp(this)
    this.checkRule = this.propCheckRule
    this.ruleId = this.propRuleId
  },
  activated() {
    initTimestamp(this)
  },
  mounted() {
  },
  provide() {
    return {
      showImportAccDlg: () => this.$refs['mailImportTableDlg'] && this.$refs['mailImportTableDlg'].show(),
      importDlgBtnName: this.$t('pages.emailLibImport')
    }
  },
  methods: {
    cancelSelectCheckbox(value) {
      this.checkRule = value
    },
    init() {
      this.resetEmailTemp()
      this.resetSMSTemp()
      this.getPopUpConfig()
      // 新增响应规则
      if (this.status === 'create') {
        if (this.showSelect) {
          this.resetTemp()
        } else {
          this.resetTemp({ alarmLimit: 3, ...this.defaultConfig })
        }
        this.ruleId && this.handleChange(this.ruleId)
      } else {
        this.handleChange(this.ruleId)
      }
    },
    checkPhone(rule, value, callback) {
      const reg = /^1[345789]\d{9}$/
      if (value == '' || !reg.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_sms')))
      } else {
        callback()
      }
    },
    emailAddressCheck(rule, value, callback) {
      const email = (this.temp.emails || []).find(email => email.address === value)
      if (email && email.id !== this.emailTemp.id) {
        callback(new Error(this.$t('pages.effectiveContent_text31')))
        return
      }
      callback()
    },
    validWeChat(rule, value, callback) {
      this.$nextTick(() => {
        const temp = this.temp
        if (temp && temp.wechatUserIds && temp.wechatUserIds.length > 0) {
          callback()
        } else {
          callback(new Error('请选择微信用户'))
        }
      })
    },
    getSelectWechatUser(users) {
      this.temp.wechatUserIds = users
    },
    // 获取响应规则已勾选的响应动作数组
    getAlarmLimitChecked(alarmLimit) {
      // 23.5.16 去掉短信告警 短信告警code为16
      return [1, 2, 4, 8, 32, 64, 128, 256, 512, 2048].filter(val => alarmLimit & val)
    },
    // 获取终端弹窗告警的默认配置
    getPopUpConfig() {
      getPopUpConfig().then(res => {
        const { configMethod, msgFormClose, msgFormPosition, msgFormType } = res.data
        Object.assign(this.defaultConfig, { configMethod, msgFormClose, msgFormPosition, msgFormType })
        this.setDefaultConfig()
      })
    },
    // 设置弹窗告警的默认配置
    setDefaultConfig() {
      Object.assign(this.temp, this.defaultConfig)
      this.autoClose = !!this.temp.msgFormClose
    },
    resetTemp(option = {}) {
      this.checked.splice(0)
      if (option.alarmLimit) {
        this.checked.push(...this.getAlarmLimitChecked(option.alarmLimit))
        delete option.alarmLimit
      }
      this.temp = Object.assign({}, JSON.parse(JSON.stringify(this.defaultTemp)), option)
      this.$nextTick(() => {
        if (option.wechatUserIds) {
          this.temp.wechatUserIds.splice(0, this.temp.wechatUserIds.length, ...option.wechatUserIds)
        }
      })
      // 显示违规内容？
      // if (this.temp.showSensContent > 0) {
      //   this.checked.push(0)
      // }
      this.autoClose = !!this.temp.msgFormClose
      this.refreshEmailList()
      this.refreshSMSList()
      this.refreshUserOrGroupId(option)
      this.refreshAppUserAndUserTagList()
      this.refresh
    },
    resetEmailTemp() {
      this.emailTemp = JSON.parse(JSON.stringify(this.defaultEmailTemp))
    },
    resetSMSTemp() {
      this.smsTemp = JSON.parse(JSON.stringify(this.defaultSMSTemp))
    },
    emailTable() {
      return this.$refs['emailTable'][0]
    },
    emailForm() {
      return this.$refs['emailForm'][0]
    },
    smsForm() {
      return this.$refs['smsForm'][0]
    },
    smsTable() {
      return this.$refs['smsTable'][0]
    },
    appTable() {
      return this.$refs['appTable'][0]
    },
    // 刷新邮箱列表
    refreshEmailList() {
      this.$nextTick(() => {
        if (this.temp.emails.length > 0) {
          this.temp.emails = [...this.temp.emails.map((email, index) => { email.id = index + 1; return email })]
        }
      })
    },
    // 刷新短信列表
    refreshSMSList() {
      this.$nextTick(() => {
        if (this.temp.sms.length > 0) {
          this.temp.sms = [...this.temp.sms.map((sms, index) => { sms.id = index + 1; return sms })]
        }
      })
    },
    // 刷新第三方推送用户
    refreshUserOrGroupId(option) {
      this.$nextTick(() => {
        if (option.userOrGroupIds) {
          this.temp.userOrGroupIds.splice(0, this.temp.userOrGroupIds.length, ...option.userOrGroupIds)
          this.defaultCheckedKeys = this.temp.userOrGroupIds
        } else {
          this.defaultCheckedKeys = []
        }
      })
    },
    refreshAppUserAndUserTagList(option) {
      this.$nextTick(() => {
        if (this.temp.appUserAndUserTags.length > 0) {
          // id字段是业务字段，所以这里使用了 _id 作为刷新的字段
          this.temp.appUserAndUserTags = [...this.temp.appUserAndUserTags.map((userOrTag, index) => { userOrTag._id = index + 1; return userOrTag })]
        }
      })
    },
    // 自动关闭弹窗的勾选状态变化
    checkChange(value) {
      if (value && !this.temp.msgFormClose) {
        this.temp.msgFormClose = 30
      } else {
        this.temp.msgFormClose = undefined
      }
    },
    // 自动关闭弹窗时长输入框失去焦点
    msgFormCloseBlur(val) {
      if (!this.temp.msgFormClose) {
        this.temp.msgFormClose = 1
      }
    },
    // select 下拉框出现/隐藏时触发
    visibleChange(data) {
      if (data) {
        // 下拉框出现时，添加滚轮事件
        this.addWheelEvent()
      } else {
        // 下拉框隐藏时，移除滚轮事件
        this.removeWheelEvent()
      }
    },
    // 添加滚轮事件
    addWheelEvent() {
      document.addEventListener('wheel', this.wheelEvent);
    },
    // 移除滚轮事件
    removeWheelEvent() {
      document.removeEventListener('wheel', this.wheelEvent);
    },
    // 滚轮事件
    wheelEvent(event) {
      if (event.target.className.indexOf('el-select-dropdown__item') == -1) {
        // 隐藏 select 的下拉框
        this.$refs.ruleSelect.blur()
      }
    },
    // 选择的响应规则变更时，对勾选状态进行更新
    handleChange(value) {
      const chooseRule = value ? JSON.parse(JSON.stringify(this.resData.find(item => item.id === value) || {})) : {}
      const resetRuleId = Object.keys(chooseRule).length === 0
      chooseRule.msgFormClose = chooseRule.msgFormClose || undefined
      chooseRule.sysUserIds = chooseRule.sysUserIds ? (chooseRule.sysUserIds.includes(0) ? [0] : chooseRule.sysUserIds) : []
      this.resetTemp(chooseRule)
      this.refreshEmailList()
      this.refreshSMSList()
      this.refreshAppUserAndUserTagList()
      resetRuleId && (this.ruleId = undefined)
      this.$emit('getRuleId', this.ruleId || null)
    },
    // 响应规则前面的复选框是否选中
    handleCheck(value) {
      if (value) {
        this.$emit('ruleIsCheck', 1)
      } else {
        this.ruleId = undefined
        this.resetTemp()
        this.$emit('getRuleId', undefined)
        this.$emit('ruleIsCheck', 0)
      }
    },
    // 响应规则各个选项勾选的方法，
    handleCheckChange(val, $event) {
      // 勾选项的值
      const value = $event.target._value
      const valueIndex = this.checked.indexOf(value)
      if (val) {
        valueIndex == -1 && this.checked.push(value)
        // 勾选 截屏、录屏 选项时，需要同时勾选 记录告警日志
        if ((value === 128 || value === 256) && !this.checked.includes(1)) {
          this.checked.push(1)
        }
      } else {
        valueIndex != -1 && this.checked.splice(valueIndex, 1)
        // 取消勾选 记录告警日志 时，需要同时取消勾选 截屏、录屏
        if (value === 1) {
          this.checked = this.checked.filter(val => val != 128 && val != 256)
        }
      }
      if (value == 8) {
        this.refreshEmailList()
      }
      if (value == 16) {
        this.refreshSMSList()
      }
      if (value == 2048) {
        this.refreshAppUserAndUserTagList()
      }
    },
    handleConfigMethodChange(val) {
      // 弹窗告警默认配置
      if (val === 1) {
        this.setDefaultConfig()
      }
    },
    sysUserChange(selections) {
      const length = selections.length
      if (length > 1) {
        if (selections[length - 1] == 0 || length == this.sysUserList.length - 1) {
          // 当最后一个选择‘所有管理员’，或者选择了除‘所有管理员’的其他所有选项时，清空所有其他选项，只保留‘所有管理员’
          selections.splice(0, length, 0)
        } else if (selections[0] == 0) {
          // ‘所有管理员’在选项第一个时，去掉该选项
          selections.splice(0, 1)
        }
      }
    },
    // 新增邮箱数据
    createEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          rowData.id = new Date().getTime()
          const emails = this.temp.emails
          // 防止重复
          if (emails.length > 0 && rowData.id < emails[0].id) {
            rowData.id = emails[0].id + 1
          }
          this.temp.emails.unshift(rowData)
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 修改邮箱数据前，更新emailTemp数据
    beforeUpdateEmail() {
      Object.assign(this.emailTemp, this.emailTable().getSelectedDatas()[0])
    },
    // 修改邮箱数据
    updateEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          for (let i = 0, size = this.temp.emails.length; i < size; i++) {
            const data = this.temp.emails[i]
            if (rowData.id === data.id) {
              this.temp.emails.splice(i, 1, rowData)
              break
            }
          }
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 删除邮箱数据
    deleteEmail() {
      const toDeleteIds = this.emailTable().getSelectedIds()
      this.temp.emails.splice(0, this.temp.emails.length, ...this.emailTable().deleteRowData(toDeleteIds))
      this.cancelEmail()
    },
    // 重置邮箱表单
    cancelEmail() {
      this.emailTable() && this.emailTable().setCurrentRow()
      this.emailForm() && this.emailForm().clearValidate()
      this.resetEmailTemp()
    },
    // 邮箱列表数据勾选
    handleSelectionChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.updateable = true
      } else {
        this.updateable = false
        this.cancelEmail()
      }
    },
    handleAppUserOrUserTagDelete(rowDatas) {
      this.appUserDeletable = rowDatas && rowDatas.length > 0
    },
    // 新增短信数据
    createSMS() {
      let validate
      this.smsForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.smsTemp)
          rowData.id = new Date().getTime()
          this.temp.sms.unshift(rowData)
          this.cancelSMS()
          validate = valid
        }
      })
      return validate
    },
    // 修改短信数据前，更新smsTemp数据
    beforeUpdateSMS() {
      Object.assign(this.smsTemp, this.smsTable().getSelectedDatas()[0])
    },
    // 修改短信数据
    updateSMS() {
      let validate
      this.smsForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.smsTemp)
          for (let i = 0, size = this.temp.sms.length; i < size; i++) {
            const data = this.temp.sms[i]
            if (rowData.id === data.id) {
              this.temp.emails.splice(i, 1, rowData)
              break
            }
          }
          this.cancelSMS()
          validate = valid
        }
      })
      return validate
    },
    // 删除短信数据
    deleteSMS() {
      const toDeleteIds = this.smsTable().getSelectedIds()
      this.temp.sms.splice(0, this.temp.sms.length, ...this.smsTable().deleteRowData(toDeleteIds))
      this.cancelSMS()
    },
    // 重置短信表单
    cancelSMS() {
      this.smsTable() && this.smsTable().setCurrentRow()
      this.smsForm() && this.smsForm().clearValidate()
      this.resetSMSTemp()
    },
    // 短信列表数据勾选
    smsSelectionChange(rowDatas) {
      this.smsDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.smsUpdateable = true
      } else {
        this.smsUpdateable = false
        this.cancelSMS()
      }
    },
    getWechatTreeData(data) {
      this.wechatTreeData = data
    },
    // 导入第三方推送人员
    handleImportAppUser() {
      this.$refs['appUserImportTableDlg'].show();
    },
    // 导入第三方推送标签
    handleImportAppUserTag() {
      this.$refs['appUserTagImportTableDlg'].show();
    },
    // 删除第三方人员或标签
    handleDeleteAppUserAndUserTag() {
      const toDeleteIds = this.appTable().getSelectedIds()
      this.temp.appUserAndUserTags.splice(0, this.temp.appUserAndUserTags.length, ...this.appTable().deleteRowData(toDeleteIds))
    },

    selectedBindedNodeFilter(nodeDatas) {
      return nodeDatas
    },
    checkedUserData(checkedData) {
      this.temp.userOrGroupIds = []
      checkedData.forEach(data => {
        this.temp.userOrGroupIds.push(data.id)
      });
    },
    importEnd(datas) {
      if (datas) {
        datas.forEach(item => {
          const { name, address, remark } = item
          const emails = this.temp.emails || []
          const time = new Date().getTime()
          const id = emails.length === 0 || time > emails[0].id ? time : emails[0].id + 1
          this.emailAddressCheck(null, address, info => {
            if (!info) {
              this.temp.emails.unshift({ id, name, address, remark })
            }
          })
        })
      }
    },
    checkAppUserOrUserTagExist(id, type) {
      const arr = this.temp.appUserAndUserTags || []
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        if (item.id === id && item.type === type) {
          return true;
        }
      }
      return false;
    },
    importAppUser(datas) {
      if (!this.temp.appUserAndUserTags) {
        this.temp.appUserAndUserTags = []
      }
      // 类型： 1-按人员导入
      const type = 1;
      const { appUserList, channel } = datas;
      appUserList.forEach(item => {
        const { id, realname } = item
        const obj = { id, type, name: realname, channel }
        // 检查是否列表已经有导入过，有导入过则不导入
        if (!this.checkAppUserOrUserTagExist(id, type)) {
          this.temp.appUserAndUserTags.unshift(obj)
        }
      })
    },
    importAppUserTag(datas) {
      if (!this.temp.appUserAndUserTags) {
        this.temp.appUserAndUserTags = []
      }
      // 类型： 1-按标签导入
      const type = 2;
      const { channel, tags } = datas;
      tags.forEach(item => {
        const obj = { id: item, type, name: item, channel }
        // 检查是否列表已经有导入过，有导入过则不导入
        if (!this.checkAppUserOrUserTagExist(item, type)) {
          this.temp.appUserAndUserTags.unshift(obj)
        }
      })
    },
    formatterChannel(row, data) {
      return data.map(channel => this.channelMap[channel]).join(', ');
    },
    formatterType(row, data) {
      return this.typeMap[data]
    }
  }
}
</script>

<style lang="scss" scoped>
span.disabled{
  color: #888;
}
.add-icon{
  margin-top: -17px;
  width: 30px;
  height: 30px;
  i{
    font-size: 15px;
    cursor: pointer;
  }
}
.select-con {
  display: inline-block;
  position: relative;
  .el-select {
    width: 180px;
    margin-left: 5px;
    display: inline-block;
  }
}
.mb10 {
  margin-bottom: 10px;
}
.error-tip {
  width: 100%;
  line-height: initial;
  left: 8px;
  bottom: -18px;
  position: absolute;
  color: red;
}
</style>
