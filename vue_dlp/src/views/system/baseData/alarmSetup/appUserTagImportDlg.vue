<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="false"
      :title="elgTitle"
      :visible.sync="visible"
      width="650px"
    >
      <div>
        <span style="color:blue;">标签下所有用户（包括后续新增）均会收到推送</span>
      </div>
      <div>
        <el-divider content-position="left">选择推送渠道</el-divider>
        <el-select v-model="form.channel" multiple clearable style="margin-top: 10px;width: 300px;">
          <el-option v-for="item in channelOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </div>
      <div>
        <el-divider content-position="left">选择推送标签</el-divider>
        <el-select
          v-model="form.tags"
          multiple
          filterable
          default-first-option
          placeholder="请选择标签"
          style="width: 50%"
        >
          <el-option
            v-for="item in tagOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          查看相关用户
        </el-button>
      </div>
      <div>
        <el-divider content-position="left">标签用户预览</el-divider>

        <!-- <div class="toolbar">
          <div class="search-div">
            <el-input v-model="query.keyword" v-trim clearable placeholder="请输入姓名或邮箱" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div> -->
        <grid-table
          ref="appUserGridTable"
          :height="260"
          pager-small
          :multi-select="false"
          :autoload="false"
          :col-model="colModel"
          :row-data-api="loadList"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getAllTags, getPageByTag } from '@/api/system/baseData/appUser';

export default {
  name: 'AppUserTagImportDlg',
  data() {
    return {
      colModel: [
        { prop: 'realname', label: '姓名', width: '100', sort: 'custom', fixed: true },
        { prop: 'tag', label: '标签', width: '200', formatter: this.formatterTag },
        { prop: 'mail', label: '邮箱', width: '100' },
        { prop: 'dingtalkUserId', label: '钉钉用户ID', width: '100' }
      ],
      prompt: '提示',
      visible: false,
      elgTitle: '导入标签',
      submitting: false,
      query: {
        page: 1,
        tagList: []
      },
      channelOptions: [
        { label: '邮件', value: 1 },
        { label: '钉钉', value: 2 }
      ],
      tagOptions: [],
      form: {
        channel: [],
        tags: []
      },
      defaultForm: {
        channel: [],
        tags: []
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['appUserGridTable']
    }
  },
  methods: {
    show() {
      this.visible = true
      this.getTagOptions()
    },
    reset() {
      // 清空表单数据
      this.form = Object.assign({}, this.defaultForm)
      // 清空列表选择
      // this.gridTable.clearSelection()
      this.gridTable.clearRowData()
      this.gridTable.clearPageData()
    },
    loadList: function(option) {
      const searchQuery = Object.assign({}, this.query, option, { tagList: this.form.tags })
      console.log('searchQuery', searchQuery)
      return getPageByTag(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    getTagOptions() {
      getAllTags().then(res => {
        this.tagOptions = res.data
      })
    },
    formatterTag(row, data) {
      if (!data) return ''
      return data.split(',').map(tag => `<el-tag>${tag}</el-tag>`).join(' ')
    },
    confirm() {
      console.log('data===', this.form)
      if (!this.form.channel || this.form.channel.length === 0) {
        this.$message({
          message: '请选择推送渠道',
          type: 'error',
          duration: 3000
        })
        return;
      }
      if (!this.form.tags || this.form.tags.length === 0) {
        this.$message({
          message: '请选择标签',
          type: 'error',
          duration: 3000
        })
        return;
      }
      this.visible = false
      this.$emit('submitEnd', this.form)
      this.reset()
    }
  }
}
</script>
<style lang="scss">
  .toolbar .search-div {
    float: right;
    padding: 8px 0;
  }
</style>
