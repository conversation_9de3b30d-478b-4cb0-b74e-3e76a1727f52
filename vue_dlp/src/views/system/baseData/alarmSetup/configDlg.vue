<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.advancedConfiguration')"
    :visible.sync="dialogVisible"
    width="600px"
  >
    <Form
      ref="configForm"
      :model="configForm"
      label-width="120px"
      label-position="right"
      :extra-width="{en: 85}"
    >
      <el-row>
        <el-col :span="isEnglish() ? 24 : 12">
          <FormItem :label="dataList.screenshotInterval.label">
            <el-input-number v-model="dataList.screenshotInterval.value" :min="dataList.screenshotInterval.min" :max="dataList.screenshotInterval.max" :controls="false" :precision="0" style="width: 70px;"/>
            <span>{{ $t('text.secondLower') }}</span>
          </FormItem>
        </el-col>
        <el-col :span="isEnglish() ? 24 : 12">
          <FormItem :label="dataList.screenshotNum.label">
            <el-input-number v-model="dataList.screenshotNum.value" :min="dataList.screenshotNum.min" :max="dataList.screenshotNum.max" :controls="false" :precision="0" style="width: 70px;"/>
            <span>{{ $t('pages.piece') }}</span>
          </FormItem>
        </el-col>
        <el-col :span="24">
          <FormItem>
            <span style="color: #2b7aac;font-size: small;">
              {{ this.$t('pages.alarmSetupTriggeredScreenshotDescript',{
                second: dataList.screenshotInterval.value,
                num: dataList.screenshotNum.value })
              }}
            </span>
          </FormItem>
        </el-col>
        <el-col :span="24">
          <FormItem :label="dataList.recordTime.label">
            <el-input-number v-model="dataList.recordTime.value" :min="dataList.recordTime.min" :max="dataList.recordTime.max" :controls="false" :precision="0" style="width: 70px;"/>
            <span>{{ $t('text.secondLower') }}</span>
            <span v-if="dataList.recordTime.value < recordInterval" style="color: red">
              <i18n path="pages.recordTimeMsg2">
                <span slot="currentRecordInterval">{{ recordInterval }}</span>
              </i18n>
            </span>
          </FormItem>
        </el-col>
        <el-col :span="24">
          <FormItem>
            <i18n style="color: #2b7aac;font-size: small;" path="pages.alarmSetupTriggeredRecordDescript">
              <template slot="second">{{ recordInterval }}</template>
              <el-tooltip slot="info" class="item" effect="dark" placement="top">
                <div slot="content">
                  {{ $t('pages.screenIntervalTip', { second: recordInterval }) }}
                  <i18n v-permission="'A68'" path="pages.clickTip">
                    <a slot="link" style="color:#148ff1;" @click="()=>{this.$router.push('/system/configManage/globalConfig')}">{{ $t('route.globalConfig') }}</a>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <template slot="num">{{ dataList.recordTime.value }}</template>
            </i18n>
          </FormItem>
        </el-col>
      </el-row>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="handleConfig">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateConfig } from '@/api/system/baseData/alarmSetup'
import { getConfig } from '@/api/system/configManage/globalConfig'

export default {
  name: 'ConfigAlarm',
  data() {
    return {
      dialogVisible: false,
      configForm: {},
      dataList: {
        screenshotInterval: { label: this.$t('pages.screenCaptureInterval'), value: '', min: 1, max: 60 },
        screenshotNum: { label: this.$t('pages.numberOfScreenshots'), value: '', min: 1, max: 10 },
        recordTime: { label: this.$t('pages.screenRecordingDuration'), value: '', min: 1, max: 3600 }
      },
      recordInterval: 0,
      submitting: false
    }
  },
  computed: {
    screenData() {
      const data = {}
      const keys = ['screenshotInterval', 'screenshotNum', 'recordTime']
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        data[key] = this.dataList[key]
      }
      return data
    }
  },
  watch: {

  },
  created() {
  },
  methods: {
    show() {
      this.resetTemp()
      this.getConfig()
      this.dialogVisible = true
    },
    resetTemp() {
    },
    handleDrag() {
    },
    handleConfig() {
      if (this.dataList.recordTime.value < this.recordInterval) {
        return;
      }
      this.submitting = true
      const formData = []
      for (const key in this.dataList) {
        formData.push({ key: key, value: this.dataList[key].value, isProp: !!this.dataList[key].isProp })
      }
      updateConfig(formData).then(res => {
        this.dialogVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.getConfig()
        this.submitting = false
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    getConfig() {
      getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'screenshotInterval') {
            this.dataList[item.key].value = item.value
          } else if (item.key === 'screenshotNum') {
            this.dataList[item.key].value = item.value
          } else if (item.key === 'recordTime') {
            this.dataList[item.key].value = item.value
          } else if (item.key === 'recordInterval') {
            this.recordInterval = item.value
          }
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-form-item{
    .el-form-item__label{
      color: #ccc;
      line-height: 30px;
    }
    .el-form-item__content{
      line-height: 30px;
      .el-input__inner{
        height: 30px;
        line-height: 30px;
      }
    }
  }
  .weak,.medium,.strong {
  display: inline-block;
  height: 5px;
  width: 60px;
  margin-left: 3px;
  margin-top: 2px;
  font-size: 2px;
  text-align: center;
  }
  .btn {
    border: none;
  }
  .ul {
    list-style-type: none;
    margin-left: -30px;
  }
</style>
