<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="elgTitle"
      :visible.sync="visible"
      width="650px"
    >
      <div>
        <el-divider content-position="left">选择推送渠道</el-divider>
        <el-select v-model="form.channel" multiple clearable style="margin-top: 10px;width: 300px;">
          <el-option v-for="item in channelOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </div>
      <div style="height: 350px;">
        <el-divider content-position="left">选择推送人员</el-divider>
        <div class="toolbar">
          <div class="search-div">
            <el-input v-model="query.keyword" v-trim clearable placeholder="请输入姓名或邮箱" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table
          ref="appUserGridTable"
          :height="gridTableHeight"
          pager-small
          :col-model="colModel"
          :row-data-api="loadList"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getPage } from '@/api/system/baseData/appUser';

export default {
  name: 'AppUserImportDlg',
  props: {
    appendToBody: {
      type: Boolean,
      default: true
    },
    gridTableHeight: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'realname', label: '姓名', width: '100' },
        { prop: 'mail', label: '邮箱', width: '180' },
        { prop: 'dingtalkUserId', label: '钉钉用户id', width: '100' }
      ],
      visible: false,
      elgTitle: '导入推送用户',
      submitting: false,
      query: {
        page: 1,
        keyword: ''
      },
      channelOptions: [
        { label: '邮件', value: 1 },
        { label: '钉钉', value: 2 }
      ],
      form: {
        channel: [],
        appUserList: []
      },
      defaultForm: {
        channel: [],
        appUserList: []
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['appUserGridTable']
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    reset() {
      // 清空表单数据
      this.form = Object.assign({}, this.defaultForm)
      // 清空列表选择
      this.gridTable.clearSelection()
    },
    loadList: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    confirm() {
      console.log('channel', this.form.channel)
      this.form.appUserList = this.gridTable.getSelectedDatas();
      if (!this.form.channel || this.form.channel.length === 0) {
        this.$message({
          message: '请选择推送渠道',
          type: 'error',
          duration: 3000
        })
        return;
      }
      if (!this.form.appUserList || this.form.appUserList.length === 0) {
        this.$message({
          message: '请选择推送人员',
          type: 'error',
          duration: 3000
        })
        return;
      }
      this.visible = false
      this.$emit('submitEnd', this.form)
      this.reset()
    }
  }
}
</script>
<style lang="scss">
  .toolbar .search-div {
    float: right;
    padding: 8px 0;
  }
</style>
