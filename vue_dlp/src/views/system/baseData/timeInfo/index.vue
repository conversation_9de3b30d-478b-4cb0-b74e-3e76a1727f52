<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.timeInfo_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="timeInfoList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="(row) => { return row.id > 1 }" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 550px;">
        <el-row>
          <el-col :span="23">
            <FormItem :label="$t('pages.timeInfoName')" prop="name">
              <el-input v-model="temp.name" v-trim :maxlength="60"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <div v-for="(day, dayIndex) in daysMap" :key="dayIndex">
          <el-row>
            <el-col :span="23">
              <FormItem :label="day.name" :prop="day.eName">
                <el-input v-model="temp[day.eName]" :disabled="!timeEditable || currentDay == day.eName" :title="temp[day.eName]" :maxlength="90"></el-input>
              </FormItem>
            </el-col>
            <el-col v-if="timeEditable" :span="1">
              <svg-icon v-show="currentDay != day.eName" icon-class="add" class-name="add-time" @click="showItems(day.eName)" />
              <svg-icon v-show="currentDay == day.eName" icon-class="active" class-name="add-time" @click="saveItems(day.eName)" />
            </el-col>
          </el-row>
          <div v-show="currentDay == day.eName" class="edit-item">
            <FormItem label-width="0">
              <el-row v-for="(value, index) in timeList" :key="index">
                <el-col :span="22">
                  <el-time-picker
                    v-model="timeList[index]"
                    is-range
                    :editable="true"
                    value-format="HH:mm:ss"
                    range-separator=" -- "
                    :start-placeholder="$t('pages.startTime')"
                    :end-placeholder="$t('pages.endTime')"
                    :placeholder="$t('pages.timeInfo_text3')"
                    style="width: 350px;margin-left: 10px;"
                  >
                  </el-time-picker>
                </el-col>
                <el-col :span="1" style="float:right;width: 32px;cursor: pointer;color: #68a8d0;">
                  <i v-show="editabled" class="el-icon-circle-plus-outline" @click="addTimeItem(index + 1)"></i>
                  <i class="el-icon-remove-outline" @click="deleteTimeItem(index)"></i>
                </el-col>
              </el-row>
            </FormItem>
            <span style="padding-left: 10px; color: #0c60a5;">{{ $t('pages.timeInfo_text2') }}</span>
          </div>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="$t('pages.timeInfo_delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi1"
      :delete-filter-name="$t('pages.timeInfo')"
      :edit-type="2"
      @submitEnd="deleteData"
    />
  </div>
</template>

<script>
import {
  getTimeInfoPage,
  getTimeInfoByName,
  createTimeInfo,
  updateTimeInfo,
  deleteTimeInfo,
  getTimeInfoPageFilterAllDay
} from '@/api/system/baseData/timeInfo'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import { parseTime } from '@/utils'

export default {
  name: 'TimeInfo',
  components: { BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'timeName', width: '150', fixed: true, sort: true },
        { prop: 'sun', label: 'sun', width: '150' },
        { prop: 'mon', label: 'mon', width: '150' },
        { prop: 'tue', label: 'tue', width: '150' },
        { prop: 'wed', label: 'wed', width: '150' },
        { prop: 'thu', label: 'thu', width: '150' },
        { prop: 'fri', label: 'fri', width: '150' },
        { prop: 'sat', label: 'sat', width: '150' },
        { label: 'operate', type: 'button', width: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate, formatter: this.buttonFormatter }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        sun: '00:00:00-23:59:59',
        mon: '00:00:00-23:59:59',
        tue: '00:00:00-23:59:59',
        wed: '00:00:00-23:59:59',
        thu: '00:00:00-23:59:59',
        fri: '00:00:00-23:59:59',
        sat: '00:00:00-23:59:59'
      },
      daysMap: [
        { name: this.$t('table.sun'), eName: 'sun' },
        { name: this.$t('table.mon'), eName: 'mon' },
        { name: this.$t('table.tue'), eName: 'tue' },
        { name: this.$t('table.wed'), eName: 'wed' },
        { name: this.$t('table.thu'), eName: 'thu' },
        { name: this.$t('table.fri'), eName: 'fri' },
        { name: this.$t('table.sat'), eName: 'sat' }
      ],
      currentDay: '', //  当前天
      timeList: [], // 当前展开天配置的时间段
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      editabled: true,
      timeEditable: true,
      textMap: {
        update: this.$t('pages.timeInfo_update'),
        create: this.$t('pages.timeInfo_create')
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        sun: [{ trigger: 'blur', validator: this.timesValidator }],
        mon: [{ trigger: 'blur', validator: this.timesValidator }],
        tue: [{ trigger: 'blur', validator: this.timesValidator }],
        wed: [{ trigger: 'blur', validator: this.timesValidator }],
        thu: [{ trigger: 'blur', validator: this.timesValidator }],
        fri: [{ trigger: 'blur', validator: this.timesValidator }],
        sat: [{ trigger: 'blur', validator: this.timesValidator }]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['timeInfoList']
    }
  },
  watch: {
    'timeList.length'(val) {
      this.editabled = val < 5
    }
  },
  created() {
    this.resetTemp()
    this.disOperation()
  },
  deactivated() {
    // 更新策略生效时间选项
    this.$store.dispatch('commonData/setTimeOptions')
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTimeInfoPage(searchQuery)
    },
    rowDataApi1: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTimeInfoPageFilterAllDay(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.timeEditable = true
      this.temp = Object.assign({}, this.defaultTemp)
    },
    disOperation() {

    },
    resetCurrentDay() {
      this.currentDay = ''
    },
    showItems(type) {
      this.saveItems()
      const timeArr = this.temp[type].split(';')
      this.timeList.splice(0)
      timeArr.forEach(el => {
        const strArr = el.split('-')
        this.timeList.push(strArr)
      })
      this.currentDay = type
    },
    deleteTimeItem(index) {
      this.timeList.splice(index, 1)
      if (this.timeList.length === 0) {
        this.addTimeItem(0)
      }
    },
    addTimeItem(index) {
      this.timeList.splice(index, 0, ['00:00:00', '23:59:59'])
    },
    saveItems(type) {
      type = type || this.currentDay
      if (!type) return // 没有已展开的item，则直接返回
      let time = ''
      this.timeList.forEach(el => {
        if (!el) return
        const str = el.join('-')
        if (time.indexOf(str) == -1) {
          time = time ? time + ';' + str : str
        }
      })
      this.temp[type] = time || '00:00:00-23:59:59'
      this.resetCurrentDay()
      this.timeList.splice(0)
      this.$refs['dataForm'].validateField(type)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.resetCurrentDay()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetCurrentDay()
      this.temp = Object.assign({}, row) // copy obj
      // this.timeEditable = this.temp.id !== 1 // 默认全天候数据禁止修改时间
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.saveItems()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createTimeInfo(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.saveItems()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          console.log('this.temp', this.temp)
          const tempData = Object.assign({}, this.temp)
          updateTimeInfo(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.updateRowData(respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getTimeInfoByName({ name: value }).then(respond => {
          const timeInfo = respond.data
          if (timeInfo && timeInfo.id != this.temp.id) {
            callback(new Error(this.$t('pages.nameValidator')))
          } else {
            callback()
          }
        }).catch(() => { this.submitting = false })
      }
    },
    timesValidator(rule, value, callback) {
      const ranges = value.split(';')
      const dateStr = parseTime(new Date(), 'y-m-d')
      for (let i = 0; i < ranges.length; i++) {
        const times = ranges[i].split('-')
        const date1 = dateStr + ' ' + times[0]
        const date2 = dateStr + ' ' + times[1]
        // ie浏览器不支持 new Date('xxxx-xx-xx xx:xx:xx')，修改为 new Date('xxxx/xx/xx xx:xx:xx')
        const nDate1 = parseTime(new Date(date1.replace(/-/g, '/')), 'y-m-d h:i:s')
        const nDate2 = parseTime(new Date(date2.replace(/-/g, '/')), 'y-m-d h:i:s')
        if (date1 != nDate1 || date2 != nDate2) {
          callback(new Error(this.$t('pages.validateMsg_time1')))
          break
        } else if (new Date(date1) > new Date(date2)) {
          callback(new Error(this.$t('pages.validateMsg_time2')))
          break
        }
      }
      callback()
    },
    buttonFormatter(row) {
      if (row.id === 1) {
        return ''
      } else {
        return this.$t('button.edit')
      }
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteTimeInfo(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    }
  }
}
</script>

<style lang="scss" scoped>
  .add-time{
    margin: 9px 0 0 10px;
    cursor: pointer;
  }
  .edit-item{
    max-height: 200px;
    width: 427px;
    padding: 5px 7px;
    margin: 0px 45px 5px 100px;
    border: 1px solid #aaa;
    >>>.el-form-item{
      max-height: 160px;
      overflow-y: auto;
    }
  }
</style>
