<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="1000px"
    >
      <el-container>
        <el-aside width="60%">
          <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" class="water-mark-form">
            <FormItem :label="$t('pages.templateName')" prop="name">
              <el-input v-model="temp.name" v-trim maxlength="20" />
            </FormItem>
            <FormItem v-if="groupIds != null" :label="$t('pages.templateGroup')" prop="groupId">
              <el-row>
                <el-col :span="12">
                  <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')" >
                    <el-option v-for="item in groupIds" :key="item.id" :label="item.label" :value="item.dataId"/>
                  </el-select>
                </el-col>
                <el-col v-show="addGroupAble" :span="2">
                  <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleGroup">
                    <svg-icon icon-class="add" />
                  </el-button>
                </el-col>
              </el-row>
            </FormItem>
            <el-card class="box-card">
              <el-checkbox v-model="temp.isWaterMark" :false-label="0" :true-label="1" class="title">{{ $t('pages.waterMarkLib_text14') }}</el-checkbox>
              <FormItem :label="$t('pages.waterMark')">
                <keyword-item :disabled="temp.isWaterMark===0" :data="temp.strategyOptionList"/>
              </FormItem>
              <FormItem :label="$t('pages.itemRowData1')" prop="keyword">
                <el-input v-model="temp.keyword" :disabled="temp.isWaterMark===0" size="mini" maxlength="20" />
              </FormItem>

              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.strFont')" prop="strFont">
                    <el-select v-model="temp.strFont" :disabled="temp.isWaterMark===0" :placeholder="$t('text.select')">
                      <el-option v-for="item in fontOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="temp.waterMarkForm===2?$t('pages.dotSize'):$t('pages.fontType')" prop="dwWordType">
                    <el-select v-model="temp.dwWordType" :disabled="temp.isWaterMark===0" :placeholder="$t('text.select')">
                      <el-option v-for="item in wordTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.fontSize')" prop="fontHeight">
                    <el-input-number v-model="temp.fontHeight" :controls="false" :disabled="temp.isWaterMark===0" :min="1" :max="200" size="mini" />
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.color')" prop="color">
                    <el-color-picker v-model="temp.color" :disabled="temp.isWaterMark===0" size="mini" @change="colorChange"></el-color-picker>
                  </FormItem>
                </el-col>
              </el-row>
              <el-row >
                <el-col :span="12">
                  <FormItem :label="$t('pages.iplace')" prop="iplace">
                    <el-select v-model="temp.iplace" :disabled="temp.isWaterMark===0" :placeholder="$t('text.select')">
                      <el-option v-for="item in placeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.fgorbg')" prop="fgorbg">
                    <el-select v-model="temp.fgorbg" :disabled="temp.isWaterMark===0" :placeholder="$t('text.select')" style="width: calc(100% - 20px);">
                      <el-option :key="1" :label="$t('pages.fgorbg1')" :value="1" />
                      <el-option :key="2" :label="$t('pages.fgorbg2')" :value="2" />
                    </el-select>
                    <el-tooltip class="item" effect="dark" placement="bottom-start">
                      <div slot="content">
                        <i18n path="pages.fgorbgTip">
                          <br slot="br" />
                          <template slot="space">&nbsp;</template>
                        </i18n>
                      </div>
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </FormItem>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.dwEscapement')" prop="dwEscapement">
                    <el-input-number v-model="temp.dwEscapement" :controls="false" :disabled="temp.isWaterMark===0" :min="0" :max="360" size="mini" />
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.waterMarkForm')" prop="waterMarkForm">
                    <el-select v-model="temp.waterMarkForm" :disabled="temp.isWaterMark===0" :placeholder="$t('text.select')" @change="changeValue">
                      <el-option :key="0" :label="$t('pages.waterMarkForm1')" :value="0" />
                      <el-option :key="1" :label="$t('pages.waterMarkForm2')" :value="1" />
                      <el-option :key="2" :label="$t('pages.waterMarkForm3')" :value="2" />
                    </el-select>
                  </FormItem>
                </el-col>
              </el-row>

            </el-card>
            <el-card class="box-card">
              <el-checkbox v-model="temp.isImageMark" :false-label="0" :true-label="1">{{ $t('pages.imageParams') }}</el-checkbox>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.imagePlace')" prop="imagePlace">
                    <el-select v-model="temp.imagePlace" :disabled="temp.isImageMark===0" :placeholder="$t('text.select')">
                      <el-option v-for="item in placeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.picSize')" prop="imageZoom">
                    <el-slider v-model="temp.imageZoom" :step="10" :disabled="temp.isImageMark===0" :min="20" :max="500" :format-tooltip="(value) => {return value + '%'}" class="slider" />
                  </FormItem>
                </el-col>
              </el-row>
              <el-upload
                ref="upload"
                :disabled="temp.isImageMark===0"
                class="upload-demo"
                name="uploadFile"
                action="1111"
                accept=".jpg,.jpeg,.bmp"
                list-type="picture"
                :on-change="fileChange"
                :on-remove="handleRemove"
                :file-list="fileList"
                :auto-upload="false"
              >
                <el-button size="small" :disabled="temp.isImageMark===0" type="primary">{{ $t('pages.clickUpload') }}</el-button>
                <div slot="tip" class="el-upload__tip">{{ $t('pages.waterMarkLib_text16') }}</div>
              </el-upload>
            </el-card>
            <el-card class="box-card">
              <FormItem :label="$t('pages.alphaValueShow')" prop="alphaValue">
                <el-slider v-model="temp.alphaValue" :step="5" :min="0" :max="255" :format-tooltip="(value) => {return parseInt(value*100/255) + '%'}" class="slider" />
              </FormItem>

              <FormItem v-show="temp.imagePlace == 15 || temp.iplace == 15" :label="$t('pages.watermarkSpace')">
                <el-row>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.tbDistance')" label-width="60px" prop="tbDistance">
                      <el-input-number v-model="temp.tbDistance" :controls="false" :min="5" :max="200" size="mini" class="distance-input"/>(mm)
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.lrDistance')" label-width="60px" prop="lrDistance">
                      <el-input-number v-model="temp.lrDistance" :controls="false" :min="5" :max="200" size="mini" class="distance-input"/>(mm)
                    </FormItem>
                  </el-col>
                </el-row>
              </FormItem>

              <FormItem :label="$t('pages.pageMargin')">
                <el-row>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.topMargin')" label-width="60px" prop="topMargin">
                      <el-input-number v-model="temp.topMargin" :controls="false" :min="1" :max="200" size="mini" class="distance-input"/>(mm)
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.bottomMargin')" label-width="60px" prop="bottomMargin">
                      <el-input-number v-model="temp.bottomMargin" :controls="false" :min="1" :max="200" size="mini" class="distance-input"/>(mm)
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.leftMargin')" label-width="60px" prop="leftMargin">
                      <el-input-number v-model="temp.leftMargin" :controls="false" :min="1" :max="200" size="mini" class="distance-input"/>(mm)
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.rightMargin')" label-width="60px" prop="rightMargin">
                      <el-input-number v-model="temp.rightMargin" :controls="false" :min="1" :max="200" size="mini" class="distance-input"/>(mm)
                    </FormItem>
                  </el-col>
                </el-row>
              </FormItem>

              <FormItem :label="$t('pages.templateRemark')" prop="remark">
                <el-input v-model="remark" type="textarea" class="template-remark" disabled :autosize="{ minRows: 2, maxRows: 4}" />
              </FormItem>
            </el-card>
          </Form>
        </el-aside>
        <el-main class="">
          <label>{{ $t('pages.preview') }}</label>
          <el-tooltip class="item" effect="dark" placement="bottom-start" :content="$t('pages.waterMarkLib_text27')">
            <i class="el-icon-info" />
          </el-tooltip>
          <div class="preview-container" :style="containerStyle">
            <div class="pic-container" :style="picStyle">
              <div :id="viewId" ref="preview" class="watermark-preview" :style="viewStyle">
                <div :id="maskId" :class="maskClass" :style="maskStyle" v-html="maskContent"></div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="saveAsAble" type="primary" @click="handleCopy">{{ $t('components.saveAs' ) }}</el-button>
        <el-button :loading="submitting" type="primary" @click="editData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getGroupByName, getLibByName } from '@/api/system/baseData/waterMarkLib'
import { toRGB } from '@/utils'
import { watermark, options, waterFormType, wordType, fgorbgType, printPlaceOptions } from '@/utils/waterMark'
import KeywordItem from '@/views/system/baseData/waterMarkLib/KeywordItem'
import { getDictLabel } from '@/utils/dictionary';

export default {
  name: 'PrintWaterMarkDlg',
  components: { KeywordItem },
  props: {
    fontOptions: {
      type: Array,
      default() {
        return [
          { label: this.$t('pages.fontOptions1'), value: this.$t('pages.fontOptions1') },
          { label: this.$t('pages.fontOptions2'), value: this.$t('pages.fontOptions2') },
          { label: this.$t('pages.fontOptions3'), value: this.$t('pages.fontOptions3') },
          { label: this.$t('pages.fontOptions4'), value: this.$t('pages.fontOptions4') }
        ]
      }
    },
    groupIds: {
      type: Array,
      default() {
        return null
      }
    },
    //  显示另存为按钮
    saveAsAble: {
      type: Boolean,
      default: true
    },
    //  创建分组
    handleGroupCreate: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      stgTypeNumber: 2,
      containerStyle: {},
      picStyle: {},
      viewId: 'watermarkPreview2',
      viewStyle: {},
      maskId: 'maskDiv2',
      maskClass: 'mask-div',
      maskStyle: '',
      maskContent: '',
      placeOptions: printPlaceOptions,
      fileList: [],
      remark: '', // 策略的备注信息，为了让remark不被监听到，所以独立出来
      temp: {},
      defaultTemp: {
        id: undefined,
        name: '',
        isWaterMark: 1,
        isImageMark: 0,
        color: '#008080',
        colorR: '',
        colorG: '',
        colorB: '',
        strFont: '宋体',
        dwEscapement: 45,
        iplace: 1,
        keyword: '',
        isUsedTime: 0,
        isUsedName: 1,
        dwWordType: 1,
        fgorbg: 1,
        fontHeight: 16,
        rowCount: 2,
        columnCount: 2,
        topMargin: 10,
        bottomMargin: 10,
        leftMargin: 10,
        rightMargin: 10,
        tbDistance: 10,
        lrDistance: 10,
        alphaValue: 102,
        imageMd5: '',
        waterMarkForm: 0, // 文字水印, 二维码, 点阵
        imagePlace: 1,
        imageZoom: 100,
        optionValue: null,
        printOptionList: [],
        printOption: null,
        markDistance: 30,
        strategyOptionList: [],
        strategyOption: '',
        groupId: null
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        groupId: [{ required: true, message: this.$t('pages.required1'), trigger: 'change' }],
        keyword: [{ validator: this.keywordValid, trigger: 'blur' }],
        color: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        dwEscapement: [{ required: true, type: 'number', message: this.$t('pages.required1'), trigger: 'blur' }],
        fontHeight: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        topMargin: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        bottomMargin: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        leftMargin: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        rightMargin: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        tbDistance: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        lrDistance: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }]
      },
      mockData: {
        user: 'User10001',
        ip: '127.0.0.1',
        mac: '00-0C-29-BB-26-2F',
        term: 'WIN-520L'
      },
      //  是否展示新增分组按钮
      addGroupAble: false
    }
  },
  computed: {
    gridTable() {
      return this.$parent.gridTable
    },
    wordType() {
      return wordType
      // return { 0: this.$t('pages.wordType1'), 1: this.$t('pages.wordType2') }
    },
    textMap() {
      return {
        update: this.$t('pages.waterMarkLib_update2'),
        create: this.$t('pages.waterMarkLib_create2')
      }
    },
    // 水印内容map
    optionsMap() {
      return options.reduce((map, option) => {
        map[option.value] = option.label
        return map
      }, {})
    },
    placeType() {
      const obj = {}
      this.placeOptions.forEach(item => {
        obj[item.value] = item.label
      })
      return obj
    },
    fgorbgType() {
      return fgorbgType
      // return {
      //   1: this.$t('pages.fgorbg1'),
      //   2: this.$t('pages.fgorbg2')
      // }
    },
    waterFormType() {
      return waterFormType
      // return {
      //   0: this.$t('pages.waterMarkForm1'),
      //   1: this.$t('pages.waterMarkForm2'),
      //   2: this.$t('pages.waterMarkForm3')
      // }
    },
    placeCssType() {
      return {
        1: 'top center',
        2: 'center center',
        3: 'bottom center',
        13: 'center left',
        14: 'center right',
        6: 'top left',
        7: 'top right',
        8: 'bottom left',
        9: 'bottom right',
        15: ''
      }
    },
    wordTypeOptions: function() { // 要根据水印形式来确定下拉框内容
      if (this.temp.waterMarkForm === 2) {
        const arr = []
        for (let i = 1; i <= 10; i++) {
          arr.push({ label: this.$t('pages.fontSize1', { size: i }), value: i })
        }
        return arr
      } else {
        return [
          { label: this.$t('pages.wordType3'), value: 0 },
          { label: this.$t('pages.wordType4'), value: 1 }
        ]
      }
    }
  },
  watch: {
    temp: {
      handler(newValue, oldValue) {
        const { isWaterMark, isImageMark, strategyOptionList, waterMarkForm, dwWordType, strFont,
          fontHeight, color, iplace, fgorbg, dwEscapement, alphaValue, imagePlace, imageZoom } = this.temp
        // 水印类型：1.文字+图片 2.文字 3.图片
        const type = (isWaterMark && isImageMark) ? 1 : isWaterMark ? 2 : isImageMark ? 3 : ''
        const watermarkType = {
          1: `${this.$t('pages.waterMarkType3')}`,
          2: `${this.$t('pages.waterMarkType1')}`,
          3: `${this.$t('pages.waterMarkType2')}`
        }[type]
        // 水印 文字内容 (选项拼接在一起)
        const watermarkContent = isWaterMark === 1 ? strategyOptionList.reduce((content, option) => {
          const optionLabel = this.optionsMap[option.dataId]
          return content ? `${content}、${optionLabel}` : optionLabel
        }, '') : ''
        // i18n字体
        const i18nStrFont = getDictLabel(this.fontOptions, strFont)
        // 圆点大小
        const dotSizeTemplate = waterMarkForm === 2 ? this.$t('pages.dotSizeTemplate', { size: dwWordType }) : ''
        // 字体
        const fontTemplate = waterMarkForm === 2 ? '' : this.$t('pages.fontTemplate', { font: `${i18nStrFont} ${fontHeight} ${this.wordType[dwWordType]}` })
        // 文字水印详情 (文字内容、圆点/字体、颜色、文字位置、图层位置、倾斜度、水印形式、透明度)
        const textTemplate = isWaterMark ? this.$t('pages.textTemplate', {
          watermarkContent, dotSizeTemplate, fontTemplate, color, textPlace: this.placeType[iplace],
          layerPosition: this.fgorbgType[fgorbg], degree: dwEscapement,
          waterMarkForm: this.waterFormType[waterMarkForm], alphaValue: parseInt((alphaValue) * 100 / 255)
        }) : ''
        // 图片水印
        const imageTemplate = isImageMark ? this.$t('pages.imageTemplate', { imagePosition: this.placeType[imagePlace], imageZoom }) : ''
        // 模板描述
        this.remark = watermarkType ? this.$t('pages.watermarkTemplate', { watermarkType, textTemplate, imageTemplate }) : ''
        watermark(this)
      },
      immediate: true,
      deep: true
    },
    dialogFormVisible(val) {
      if (val) {
        this.$emit('dialogShow')
      }
    }
  },
  created() {
  },
  methods: {
    nameValidator(rule, value, callback) {
      getLibByName({ name: value, type: this.getDataType() }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.waterMarkLib_text29')))
        } else {
          callback()
        }
      }).catch(() => {
        this.submitting = false
      })
    },
    groupIdValidator(rule, value, callback) {
      if (this.groupIds != null) {
        if (value == null || value === '') {
          callback(new Error(this.$t('pages.waterMarkLib_text33')))
        } else {
          getGroupByName({ name: value }).then(res => {
            if (res.data == null) {
              callback(new Error(this.$t('pages.waterMarkLib_text34')))
            }
          }).catch(() => {
            this.submitting = false
          })
        }
      }
    },
    keywordValid(rule, value, callback) {
      let flag = false  // 水印内容是否包含了自定义内容
      this.temp.strategyOptionList.forEach(item => {
        if (item.dataId == '00') {
          flag = true
        }
      })
      if (this.temp.isWaterMark && !this.temp.keyword && flag) {
        callback(new Error(this.$t('pages.waterMarkLib_text23')))
      } else {
        callback()
      }
    },
    getDataType() {
      return 2
    },
    hide() {
      this.dialogFormVisible = false
    },
    colorChange(val) {
      if (val == null) {
        this.temp.color = '#008080'
      }
    },
    changeValue: function(value) {
      if (value === 2) {
        this.temp.dwWordType = 3
      } else {
        this.temp.dwWordType = 0
      }
    },
    afterLoad: function(rowData, grid) {
      var selectMap = {}
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (selectMap[item.id] != null) {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.remark = ''
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      watermark(this)
    },
    fileChange(file, fileList) {
      // 图片显示前做一下判断
      const IMG_ALLOWD = ['jpg', 'bmp', 'jpeg']
      const imgType = file.raw.type.split('/')[1]
      const imgSize = file.size / 1024
      // 判断图片格式
      if (IMG_ALLOWD.indexOf(imgType) === -1) {
        this.$message({
          message: this.$t('pages.waterMarkLib_text18'),
          type: 'error',
          duration: 2000
        })
        if (this.fileList.length === 0) {
          this.fileList = []
        } else {
          this.fileList = fileList.slice(0, 1)
        }
        return
      } else if (imgSize >= 200) {
        this.$message({
          message: this.$t('pages.waterMarkLib_text19'),
          type: 'error',
          duration: 2000
        })
        if (this.fileList.length === 0) {
          this.fileList = []
        } else {
          this.fileList = fileList.slice(0, 1)
        }
        return
      } else {
        this.fileList = fileList.slice(-1)
      }
      watermark(this)
    },
    handleCreate(groupId, submitFunc, addGroupAble) {
      this.addGroupAble = addGroupAble || false
      this.submitFunc = submitFunc
      this.resetTemp()
      this.temp.groupId = groupId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      if (this.$refs['upload']) {
        this.$refs['upload'].clearFiles()
        this.fileList.splice(0)
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        watermark(this)
      })
    },
    handleUpdate(row, submitFunc, addGroupAble) {
      this.addGroupAble = addGroupAble || false
      this.submitFunc = submitFunc
      this.resetTemp()
      this.remark = row.remark
      this.temp = Object.assign(this.temp, row.info) // copy obj
      this.temp.groupId = row.groupId
      this.temp.printOptionList = this.numToArr(this.temp.printOption)
      // 因为打印水印的倾斜度的单位是1，而预览组件的单位是0.1，所以要除以10
      this.temp.dwEscapement = this.temp.dwEscapement / 10
      this.temp.id = row.id
      this.versionDeal(row.info)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      if (this.$refs['upload']) {
        this.$refs['upload'].clearFiles()
        this.fileList.splice(0)
      }
      this.$nextTick(() => {
        if (this.temp.isImageMark === 1) {
          this.fileList = [{ name: this.temp.imageName, url: process.env.VUE_APP_BASE_API + this.temp.path }]
        }
        this.$refs['dataForm'].clearValidate()
        watermark(this)
      })
    },
    versionDeal(rowInfo) {
      // 兼容旧策略应该使用旧策略信息的json，原因在于：旧策略应该没有strategyOption字段，Object.assign会将defaultTemp进行写入，
      // 判断是否为新策略 this.temp.strategyOption === '' 会造成旧策略判断为新策略
      // 版本数据处理，有些旧的数据需要进行格式化，兼容一下
      if (rowInfo.strategyOption || rowInfo.strategyOption === '') {
        // strategyOption的每两个字符表示一个文字项设置
        for (let i = 0; i < this.temp.strategyOption.length; i = i + 2) {
          let code = this.temp.strategyOption[i] + this.temp.strategyOption[i + 1]
          // 打印水印与屏幕水印不太一样，03表示的是终端昵称，04表示的是操作员名称。需要对调一下。
          const oldCode = code
          if (oldCode == '03') {
            code = '04'
          }
          if (oldCode == '04') {
            code = '03'
          }
          if (this.optionsMap[code]) {
            this.temp.strategyOptionList.push({ label: this.optionsMap[code], id: i, dataId: code })
          }
        }
      } else {
        // strategyOption为空说明是旧数据，需要进行兼容处理
        if (this.temp.isUsedTime == 0) {
          // 添加时间
          this.temp.strategyOptionList.push({ label: this.optionsMap['05'], id: 1, dataId: '05' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 2, dataId: '02' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['06'], id: 3, dataId: '06' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 4, dataId: '01' })
        }
        if (this.temp.isUsedName == 0) {
          // 添加操作员账号
          this.temp.strategyOptionList.push({ label: this.optionsMap['03'], id: 5, dataId: '03' })
          if (this.temp.printOptionList.indexOf(1) != -1) {
            // 如果下个文字项是终端，则要显示为一行
            this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 6, dataId: '02' })
          } else {
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 7, dataId: '01' })
          }
        }
        if (this.temp.printOptionList.indexOf(1) != -1) {
          // 添加终端名称
          this.temp.strategyOptionList.push({ label: this.optionsMap['04'], id: 8, dataId: '04' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 9, dataId: '01' })
        }
        if (this.temp.strategyOptionList && this.temp.strategyOptionList[this.temp.strategyOptionList.length - 1].dataId == '01') {
          // 如果最后一个文字项是回车符，则去掉
          this.temp.strategyOptionList.pop()
        }
      }
    },
    validataData() {
      if (this.temp.isImageMark === 1) {
        const uploadFiles = this.$refs['upload'].uploadFiles
        if (uploadFiles.length === 0) {
          this.$message({ message: this.$t('pages.waterMarkLib_text20'), type: 'error', duration: 2000 })
          return false
        }
      }
      if (this.temp.isImageMark === 0 && this.temp.isWaterMark === 0) {
        this.$message({ message: this.$t('pages.waterMarkLib_text21'), type: 'error', duration: 2000 })
        return false
      }
      if (this.temp.isWaterMark && this.temp.strategyOptionList.length == 0) {
        this.$message({
          showClose: true,
          message: this.$t('pages.waterMarkLib_text22'),
          type: 'error'
        })
        return false
      }
      this.temp.remark = this.remark
      return true
    },
    formatFormData(deleteGuid) {
      const data = JSON.parse(JSON.stringify(this.temp))
      data.dwEscapement = data.dwEscapement * 10  // 打印水印的倾斜度单位是0.1度，范围0~3600，页面显示还是0~360，但是入库要乘10
      if (data.color != '') {
        const colors = toRGB(data.color)
        data.colorR = colors[0]
        data.colorG = colors[1]
        data.colorB = colors[2]
      }
      data.printOptionList.splice(0)
      data.strategyOption = data.strategyOptionList.map(item => {
        // 为了兼容旧终端，一些旧的参数也要根据strategyOption设置一下
        if (item.dataId == '03') {
          // 操作员
          data.isUsedName = 0
        }
        if (item.dataId == '04' && data.printOptionList.indexOf(1) == -1) {
          // 终端
          data.printOptionList.push(1)
        }

        // 打印水印与屏幕水印不太一样，03表示的是终端昵称，04表示的是操作员名称。需要对调一下。
        const code = item.dataId
        if (code == '03') {
          item.dataId = '04'
        }
        if (code == '04') {
          item.dataId = '03'
        }

        if (item.dataId == '05' || item.dataId == '06') {
          // 时间
          data.isUsedTime = 0
        }

        if (item.dataId == '09' && data.printOptionList.indexOf(2) == -1) {
          // 计算机名称
          data.printOptionList.push(2)
        }
        if (item.dataId == '0A' && data.printOptionList.indexOf(4) == -1) {
          // 计算机用户名称
          data.printOptionList.push(4)
        }
        if (item.dataId == '07' && data.printOptionList.indexOf(8) == -1) {
          // ip
          data.printOptionList.push(8)
        }
        if (item.dataId == '08' && data.printOptionList.indexOf(16) == -1) {
          // mac
          data.printOptionList.push(16)
        }
        if (item.dataId == '0C' && data.printOptionList.indexOf(64) == -1) {
          // 操作员编号
          data.printOptionList.push(64)
        }
        if (item.dataId == '0D' && data.printOptionList.indexOf(128) == -1) {
          // 终端编号
          data.printOptionList.push(128)
        }

        return item.dataId
      }).join('')
      data.printOption = this.getSum(data.printOptionList)
      data.strategyOptionList = null
      const groupId = data.groupId
      this.$delete(data, 'groupId')
      deleteGuid === true && (delete data.guid)
      const tempData = {
        type: this.getDataType(),
        id: data.id,
        groupId: groupId,
        name: data.name,
        remark: this.remark,
        json: JSON.stringify(data)
      }
      if (data.isImageMark === 1) {
        const uploadFiles = this.$refs['upload'].uploadFiles
        tempData['uploadFile'] = uploadFiles[0].raw // 传文件
      }
      return tempData
    },
    editData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const flag = this.validataData()
          if (!flag) {
            this.submitting = false
            return
          }
          const formData = this.toFormData(this.formatFormData())
          this.submitFunc(formData).then(() => {
            this.submitting = false
            this.hide()
            this.$emit('reloadTable')
            const message = { create: this.$t('text.createSuccess'), update: this.$t('text.updateSuccess') }[this.dialogStatus]
            this.$notify({ title: this.$t('text.success'), message: message, type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleCopy() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const flag = this.validataData()
          if (!flag) {
            this.submitting = false
            return
          }
          const formData = this.formatFormData(true)
          this.$parent.handleCopy(formData)
        } else {
          this.submitting = false
        }
      })
    },
    handleGroup() {
      if (this.handleGroupCreate) {
        this.handleGroupCreate()
      }
    },
    setGroupId(groupId) {
      if (groupId) {
        this.temp.groupId = String(groupId)
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-dialog__body{
    .el-tabs{
      border: 0;
    }
    .el-tabs__item{
      color: #666;
      &.is-active {
        color: #409EFF;
      }
    }
    .distance-input{
      width: 60px;
      margin-right: 3px;
    }
    .water-mark-form{
      overflow: auto;
      height: 460px;
      padding: 0 5px;
    }
    .el-main{
      padding: 10px 20px 0 20px;
    }
  }
  >>>.el-input-number .el-input__inner{
    text-align: left;
  }
  .template-remark >>>.el-textarea__inner {
    cursor: default;
  }
  .slider {
    width: 95%;
    margin: 0 auto;
    >>>.el-slider__runway {
      margin: 12px 0;
      background-color: #fff;
    }
  }
</style>
