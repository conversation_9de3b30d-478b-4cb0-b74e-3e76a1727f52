<template>
  <div>
    <el-row>
      <el-col :span="11">
        <grid-table
          ref="gridTable"
          :height="200"
          :show-pager="false"
          :multi-select="true"
          :default-sort="{ prop: 'id', order: 'ascending' }"
          :col-model="itemColModel"
          :row-datas="itemRowData"
          @row-dblclick="dbClick"
        />
      </el-col>
      <el-col :span="2" style="padding-left: 5px;">
        <el-button class="el-icon-right" :disabled="disabled" type="primary" @click="addData"></el-button><br>
        <el-button class="el-icon-delete" :disabled="disabled" type="primary" @click="removeAll"></el-button>
      </el-col>
      <el-col :span="11">
        <div class="tree-container" style="width: 100%; height: 200px;">
          <tree-menu
            ref="selectTree"
            :is-filter="false"
            :default-expand-all="true"
            :data="data"
            :draggable="true"
            :allow-drag="() => true"
            :allow-drop="allowDrop"
            :render-content="renderContent"
            @node-drop="handleDrop"
          />
        </div>
      </el-col>
    </el-row>

  </div>
</template>

<script type="text/jsx">
import { options } from '@/utils/waterMark'

export default {
  name: 'KeywordItem',
  props: {
    data: {
      type: Array,
      default() {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    maxLength: {
      type: Number,
      default() {
        return 50
      }
    },
    keywordFilter: { // 过滤函数，过滤掉itemRowData中不显示的数据
      type: Function,
      default: null
    }
  },
  data() {
    return {
      itemColModel: [
        { prop: 'label', label: 'options', width: '40' }
      ],
      itemRowData: [],
      lastSelectNode: null
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.itemRowData.splice(0)
    options.forEach((item, index) => {
      this.itemRowData.push({ id: index, label: item.label, dataId: item.value })
    })
    if (typeof this.keywordFilter == 'function') {
      this.itemRowData = this.keywordFilter(this.itemRowData)
    }
  },
  methods: {
    renderContent(h, { node, data, store }) {
      const isshow = !this.disabled
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={isshow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    removeNode(data) {
      this.data.forEach((item, index) => {
        if (item.id == data.id) {
          this.data.splice(index, 1)
        }
      })
      this.$emit('change', this.data)
    },
    removeAll() {
      this.$confirmBox(this.$t('pages.waterMarkLib_text6'), this.$t('text.prompt')).then(() => {
        this.clear()
      }).catch(() => {})
    },
    clear() {
      this.data.splice(0)
      this.$emit('change', this.data)
    },
    allowDrop(draggingNode, dropNode, type) {
      // 禁止把一个节点拉倒另一个节点下面
      return type != 'inner'
    },
    addData() {
      // 得到表格选中的记录
      const rows = this.$refs.gridTable.getSelectedDatas()
      if (rows.length == 0) {
        return
      }
      if (this.data.length >= this.maxLength) {
        this.$message({
          showClose: true,
          message: this.$t('pages.waterMarkLib_text36', { number: this.maxLength }),
          type: 'error'
        })
        return
      }
      this.selectItem(rows)
      this.$refs.gridTable.clearSelection()
    },
    dbClick(row) {
      if (this.disabled) {
        return
      }
      if (this.data.length >= this.maxLength) {
        this.$message({
          showClose: true,
          message: this.$t('pages.waterMarkLib_text36', { number: this.maxLength }),
          type: 'error'
        })
        return
      }
      this.selectItem(row)
    },
    selectItem(rowDatas) { // 添加选中Item，不会清除历史选中
      if (!Array.isArray(rowDatas)) {
        rowDatas = [rowDatas]
      }
      const datas = JSON.parse(JSON.stringify(rowDatas))
      // 重新设置表格的记录的id，防止添加到右边的时候出现ID冲突
      datas.forEach((item, index) => {
        item.id = new Date().getTime() + '' + index
      })
      const node = this.$refs['selectTree'].$refs.tree.getCurrentNode()
      if (node) {
        const index = this.data.findIndex(item => {
          return item.id == node.id
        })
        if (index < 0) {
          this.data.push(...datas)
        } else {
          this.data.splice(index + 1, 0, ...datas)
        }
        this.$refs['selectTree'].$refs.tree.setCurrentKey()
      } else {
        this.data.push(...datas)
      }
      this.$emit('change', this.data)
    },
    // 选中的item根据itemRowData最终转换为label，例如：“自定义内容，空格，操作员账号，空格，终端名称”
    getSelectedLabel() {
      const label = []
      if (this.data) {
        this.data.forEach(item => {
          if (item && item.label) {
            label.push(item.label)
          }
        })
      }
      return label.join('，')
    },
    // 选中的item根据itemRowData最终转换为code，例如：040203、0402030200
    getSelectedCode() {
      let code = ''
      if (this.data) {
        this.data.forEach(item => {
          if (item && item.dataId) {
            code += item.dataId
          }
        })
      }
      return code
    },
    // 根据code从itemRowData转换为选中的item
    selectItemByCode(code) {
      const rowData = []
      for (let i = 0; i < code.length; i += 2) {
        const c = code.substring(i, i + 2)
        for (let j = 0; j < this.itemRowData.length; j++) {
          const item = this.itemRowData[j]
          if (item.dataId === c) {
            rowData.push(item)
            break
          }
        }
      }
      this.selectItem(rowData)
    },
    // 拖拽成功完成时触发的事件	共四个参数，依次为：被拖拽节点对应的 Node、结束拖拽时最后进入的节点、被拖拽节点的放置位置（before、after、inner）、event
    handleDrop(draggingNode, dropNode, dropType, ev) {
      this.$emit('node-drop', draggingNode, dropNode, dropType, ev)
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
