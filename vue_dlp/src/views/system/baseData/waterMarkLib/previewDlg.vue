<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="`${$t('pages.overview')}（` + temp.name +'）'"
      :visible.sync="dialogFormVisible"
      width="500px"
      @closed="$emit('closed')"
    >
      <el-container style="height: 460px">
        <el-main>
          <div class="preview-container" :style="containerStyle">
            <div class="pic-container" :style="picStyle">
              <div :id="viewId" ref="preview" class="watermark-preview" :style="viewStyle">
                <div :id="maskId" :class="maskClass" :style="maskStyle" v-html="maskContent"></div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <office-water-mark-dlg ref="officeWatermarkDlg"/>
  </div>
</template>

<script>
import { watermark, options } from '@/utils/waterMark'
import OfficeWaterMarkDlg from './officeWaterMarkDlg'
export default {
  name: 'PreviewDlg',
  components: { OfficeWaterMarkDlg },
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      stgTypeNumber: 2,
      containerStyle: {},
      picStyle: {},
      viewId: 'watermarkPreview' + new Date().getTime(),
      viewStyle: {},
      maskId: 'maskDiv' + new Date().getTime(),
      maskClass: 'mask-div',
      maskStyle: '',
      maskContent: '',
      temp: {},
      defaultTemp: {
        id: null,
        name: '',
        active: false,
        entityType: '',
        entityId: null,
        isWaterMark: 1,
        isImageMark: 0,
        color: '#008080',
        colorR: '',
        colorG: '',
        colorB: '',
        strFont: this.$t('pages.fontOptions4'),
        dwEscapement: 0,
        iplace: 1,
        keyword: '',
        isUsedTime: 0,
        isUsedName: 1,
        dwWordType: 1,
        fgorbg: 1,
        fontHeight: null,
        rowCount: 2,
        columnCount: 2,
        topMargin: 3,
        bottomMargin: 3,
        leftMargin: 3,
        rightMargin: 3,
        tbDistance: 10,
        lrDistance: 10,
        alphaValue: 100,
        iwaterType: 1,
        imageMd5: '',
        waterMarkForm: 0, // 文字水印, 二维码, 点阵
        imagePlace: 1,
        imageZoom: 100,
        imageInfo: '',
        stgTypeNumber: this.stgTypeNumber,
        optionValue: null,
        optionValueList: [],
        markDistance: 20,
        waterType: null,
        printOption: null,
        printOptionList: [],
        strategyOption: '',
        strategyOptionList: []
      },
      dialogFormVisible: false
    }
  },
  computed: {
    placeCssType() {
      const office = {
        6: 'top left', 1: 'top center', 7: 'top right',
        13: 'center left', 2: 'center center', 14: 'center right',
        8: 'bottom left', 3: 'bottom center', 9: 'bottom right', 15: ''
      }
      const print = {
        6: 'top left', 1: 'top center', 7: 'top right',
        13: 'center left', 2: 'center center', 14: 'center right',
        8: 'bottom left', 3: 'bottom center', 9: 'bottom right', 15: ''
      }
      // 屏幕水印没有图片，所以只区分office和打印
      return this.stgTypeNumber == 151 ? office : print
    },
    optionsMap() {
      const map = {}
      options.forEach(item => {
        map[item.value] = item.label
      })
      return map
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.activeTab = 'first'
      this.remark = ''
    },
    versionDeal() {
      // 版本数据处理，有些旧的数据需要进行格式化，兼容一下
      if (this.temp.strategyOption) {
        // strategyOption的每两个字符表示一个文字项设置
        for (let i = 0; i < this.temp.strategyOption.length; i = i + 2) {
          let code = this.temp.strategyOption[i] + this.temp.strategyOption[i + 1]
          if (this.stgTypeNumber == 2) {
            // 打印水印与屏幕水印不太一样，03表示的是终端昵称，04表示的是操作员名称。需要对调一下
            const oldCode = code
            if (oldCode == '03') {
              code = '04'
            }
            if (oldCode == '04') {
              code = '03'
            }
          }

          if (this.optionsMap[code]) {
            this.temp.strategyOptionList.push({ label: this.optionsMap[code], id: i, dataId: code })
          }
        }
      } else {
        // strategyOption为空说明是旧数据，需要进行兼容处理
        if (this.stgTypeNumber == 44) {
          if (this.temp.isUsedTime == 0) {
            // 添加时间
            this.temp.strategyOptionList.push({ label: this.optionsMap['05'], id: 1, dataId: '05' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 2, dataId: '02' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['06'], id: 3, dataId: '06' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 4, dataId: '01' })
          }
          if (this.temp.isUsedName == 0) {
            // 添加终端名称
            this.temp.strategyOptionList.push({ label: this.optionsMap['04'], id: 5, dataId: '04' })
            if (this.temp.optionValueList.indexOf(4) != -1) {
              // 如果下个文字项是操作员，则要显示为一行
              this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 6, dataId: '02' })
            } else {
              this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 7, dataId: '01' })
            }
          }
          if (this.temp.optionValueList.indexOf(4) != -1) {
            // 添加操作员账号
            this.temp.strategyOptionList.push({ label: this.optionsMap['03'], id: 8, dataId: '03' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 9, dataId: '01' })
          }
          if (this.temp.optionValueList.indexOf(1) != -1) {
            // 添加ip
            this.temp.strategyOptionList.push({ label: this.optionsMap['07'], id: 10, dataId: '07' })
            if (this.temp.optionValueList.indexOf(2) != -1) {
              // 如果下个文字项是Mac，则要显示为一行
              this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 11, dataId: '02' })
            } else {
              this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 12, dataId: '01' })
            }
          }
          if (this.temp.optionValueList.indexOf(2) != -1) {
            // 添加mac
            this.temp.strategyOptionList.push({ label: this.optionsMap['08'], id: 13, dataId: '08' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 14, dataId: '01' })
          }
          if (this.temp.strategyOptionList && this.temp.strategyOptionList[this.temp.strategyOptionList.length - 1].dataId == '01') {
            // 如果最后一个文字项是回车符，则去掉
            this.temp.strategyOptionList.pop()
          }
        } else {
          if (this.temp.isUsedTime == 0) {
            // 添加时间
            this.temp.strategyOptionList.push({ label: this.optionsMap['05'], id: 1, dataId: '05' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 2, dataId: '02' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['06'], id: 3, dataId: '06' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 4, dataId: '01' })
          }
          if (this.temp.isUsedName == 0) {
            // 添加操作员账号
            this.temp.strategyOptionList.push({ label: this.optionsMap['03'], id: 5, dataId: '03' })
            if (this.temp.printOptionList.indexOf(1) != -1) {
              // 如果下个文字项是终端，则要显示为一行
              this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 6, dataId: '02' })
            } else {
              this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 7, dataId: '01' })
            }
          }
          if (this.temp.printOptionList.indexOf(1) != -1) {
            // 添加终端名称
            this.temp.strategyOptionList.push({ label: this.optionsMap['04'], id: 8, dataId: '04' })
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 9, dataId: '01' })
          }
          if (this.temp.strategyOptionList && this.temp.strategyOptionList[this.temp.strategyOptionList.length - 1].dataId == '01') {
            // 如果最后一个文字项是回车符，则去掉
            this.temp.strategyOptionList.pop()
          }
        }
      }
    },
    handleUpdate(row, stgType) { // stgType:水印类型：2：打印水印 44：屏幕水印 151：office文档水印
      this.stgTypeNumber = stgType
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      this.temp.optionValueList = this.numToList(this.temp.optionValue, 3)
      this.temp.printOptionList = this.numToList(this.temp.printOption, 3)
      if (this.stgTypeNumber == 44) {
        // 因为屏幕水印的枚举值不一样，1代表的是点阵，但是预览组件2代表的是点阵，所以这边转换一下
        if (this.temp.waterMarkForm == 1) {
          this.temp.waterMarkForm = 2
        }
        // 屏幕水印的透明度和预览组件的透明度是相反的，要翻转一下
        this.temp.alphaValue = 255 - this.temp.alphaValue
      } else if (this.stgTypeNumber == 2) {
        // 因为打印水印的倾斜度的单位是0.1，所以要除以10
        this.temp.dwEscapement = this.temp.dwEscapement / 10
      }
      this.versionDeal()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        if (this.temp.isImageMark === 1) {
          this.fileList = [{ name: this.temp.imageName, url: process.env.VUE_APP_BASE_API + this.temp.path }]
        }
        if (this.stgTypeNumber == 151) {
          this.temp.dwWordType = 0
          this.temp.iplace = null
          this.$refs.officeWatermarkDlg.previewWatermark(this)
        } else {
          watermark(this)
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-dialog__body{
    .el-tabs{
      border: 0;
    }
    .el-tabs__item{
      color: #666;
      &.is-active {
        color: #409EFF;
      }
    }
    .el-main{
      padding: 10px 20px 0 20px;
    }
  }
</style>
