<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="1000px"
    >
      <el-container>
        <el-aside width="60%">
          <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" class="water-mark-form">
            <FormItem :label="$t('pages.templateName')" prop="name">
              <el-input v-model="temp.name" maxlength="20" @blur="temp.name=$event.target.value.trim()"/>
            </FormItem>
            <FormItem v-if="groupIds != null" :label="$t('pages.templateGroup')" prop="groupId">
              <el-row>
                <el-col :span="12">
                  <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')" >
                    <el-option v-for="item in groupIds" :key="item.id" :label="item.label" :value="item.dataId"/>
                  </el-select>
                </el-col>
                <el-col v-show="addGroupAble" :span="2">
                  <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleGroup">
                    <svg-icon icon-class="add" />
                  </el-button>
                </el-col>
              </el-row>
            </FormItem>
            <el-card class="box-card">
              <FormItem :label="$t('pages.waterMark')">
                <keyword-item :data="temp.strategyOptionList"/>
              </FormItem>
              <!-- <FormItem label="截屏水印">
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    截屏水印仅支持桌面水印
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-switch v-model="temp.capture" :active-value="1" :inactive-value="0"></el-switch>
              </FormItem> -->
              <FormItem :label="$t('pages.itemRowData1')" prop="keyword">
                <el-input v-model="temp.keyword" type="textarea" :rows="1" size="mini" maxlength="20" style="width: 100%"/>
              </FormItem>

              <!--<FormItem label="水印内容" prop="keyword">
                <el-row>
                  <el-col :span="6">
                    <el-checkbox v-model="temp.isUsedTime" :false-label="1" :true-label="0">时间</el-checkbox>
                  </el-col>
                  <el-col :span="8">
                    <el-checkbox v-model="temp.isUsedName" :false-label="1" :true-label="0">终端名称</el-checkbox>
                  </el-col>
                  <el-col :span="10">
                    <el-checkbox-group v-model="temp.optionValueList">
                      <el-checkbox :label="4">操作员账号</el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="6">
                    <el-checkbox-group v-model="temp.optionValueList">
                      <el-checkbox :label="1">IP地址</el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                  <el-col :span="8">
                    <el-checkbox-group v-model="temp.optionValueList">
                      <el-checkbox :label="2">MAC地址</el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                  <el-col :span="10">
                    自设内容：
                    <el-input v-model="temp.keyword" style="width: 50%" size="mini" maxlength="20" />
                  </el-col>
                </el-row>
              </FormItem>-->
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.strFont')" prop="strFont">
                    <el-select v-model="temp.strFont" :placeholder="$t('text.select')">
                      <el-option v-for="item in fontOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="temp.waterMarkForm===2?$t('pages.dotSize'):$t('pages.fontType')" prop="dwWordType">
                    <el-select v-model="temp.dwWordType" :placeholder="$t('text.select')">
                      <el-option v-for="item in wordTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.fontSize')" prop="fontHeight">
                    <el-input-number v-model="temp.fontHeight" :controls="false" :min="1" :max="200" size="mini"/>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.color')" prop="color">
                    <el-color-picker v-model="temp.color" size="mini" @change="colorChange"></el-color-picker>
                  </FormItem>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.dwEscapement')" prop="dwEscapement">
                    <el-input-number v-model="temp.dwEscapement" :controls="false" :min="0" :max="360" size="mini"/>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.waterMarkForm')" prop="waterMarkForm">
                    <el-select v-model="temp.waterMarkForm" :placeholder="$t('text.select')" @change="changeValue">
                      <el-option :key="0" :label="$t('pages.waterMarkForm1')" :value="0" />
                      <el-option :key="2" :label="$t('pages.waterMarkForm3')" :value="2" />
                    </el-select>
                  </FormItem>
                </el-col>
              </el-row>
              <FormItem :label="$t('pages.alphaValueShow')" prop="alphaValue">
                <el-slider v-model="temp.alphaValue" :step="5" :min="0" :max="255" :format-tooltip="(value) => {return parseInt(value*100/255) + '%'}" class="slider" />
              </FormItem>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.watermarkSpace')" prop="markDistance">
                    <el-input-number v-model="temp.markDistance" :controls="false" :min="10" :max="255" size="mini"/>
                  </FormItem>
                </el-col>
              </el-row>
              <FormItem :label="$t('pages.showCondition')">
                <template #tooltip>
                  <el-tooltip effect="dark" placement="top">
                    <div slot="content">
                      <i18n path="pages.screenShowConditionTip">
                        <br slot="br"/>
                      </i18n>
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </template>
                <el-row>
                  <el-col :span="9" style="height: 30px">
                    <el-checkbox v-model="temp.capture" :true-label="1" :false-label="0" @change="screenShotChange">
                      {{ $t('pages.showWatermarkOnlyWhenScreenshot') }}
                    </el-checkbox>
                  </el-col>
                  <!-- 先判断是否存在文件加密模块-->
                  <el-col v-permission="'124 & E22'" :span="15" style="height: 30px">
                    <el-checkbox v-model="temp.encryptCapture" :true-label="1" :false-label="0" :disabled="temp.capture == 0">
                      {{ $t('pages.encryptScreenShotWatermark') }}
                    </el-checkbox>
                  </el-col>
                  <el-col :span="9" style="height: 30px">
                    <el-checkbox v-model="temp.offlineMark" :true-label="1" :false-label="0">
                      {{ $t('pages.showWatermarkOnlyWhenOffline') }}
                    </el-checkbox>
                  </el-col>
                  <el-col :span="15" style="height: 30px">
                    <el-checkbox v-model="temp.encryptCtrlProcess" :true-label="1" :false-label="0">
                      {{ $t('pages.encryptCtrlProcessWatermark') }}
                    </el-checkbox>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem :label="$t('pages.templateRemark')" prop="remark">
                <el-input v-model="remark" class="template-remark" disabled type="textarea" :rows="4" />
              </FormItem>
            </el-card>
          </Form>
        </el-aside>
        <el-main>
          <label>{{ $t('pages.preview') }}</label>
          <el-tooltip class="item" effect="dark" placement="bottom-start" :content="$t('pages.waterMarkLib_text27')">
            <i class="el-icon-info" />
          </el-tooltip>
          <div class="preview-container" :style="containerStyle">
            <div class="pic-container" :style="picStyle">
              <div :id="viewId" ref="preview" class="watermark-preview" :style="viewStyle">
                <div :id="maskId" :class="maskClass" :style="maskStyle" v-html="maskContent"></div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="saveAsAble" type="primary" @click="handleCopy">{{ $t('components.saveAs' ) }}</el-button>
        <el-button :loading="submitting" type="primary" @click="editData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getGroupByName, getLibByName } from '@/api/system/baseData/waterMarkLib'
import { toRGB } from '@/utils'
import { watermark, options, waterFormType, wordType, fgorbgType } from '@/utils/waterMark'
import KeywordItem from '@/views/system/baseData/waterMarkLib/KeywordItem'
import { getDictLabel } from '@/utils/dictionary';

export default {
  name: 'ScreenWaterMarkDlg',
  components: { KeywordItem },
  props: {
    fontOptions: {
      type: Array,
      default() {
        return [
          { label: this.$t('pages.fontOptions1'), value: this.$t('pages.fontOptions1') },
          { label: this.$t('pages.fontOptions2'), value: this.$t('pages.fontOptions2') },
          { label: this.$t('pages.fontOptions3'), value: this.$t('pages.fontOptions3') },
          { label: this.$t('pages.fontOptions4'), value: this.$t('pages.fontOptions4') }
        ]
      }
    },
    groupIds: {
      type: Array,
      default() {
        return null
      }
    },
    //  显示另存为按钮
    saveAsAble: {
      type: Boolean,
      default: true
    },
    //  创建分组
    handleGroupCreate: {
      type: Function,
      default: null
    }
  },
  data() {
    // var checkWaterMark = (rule, value, callback) => {
    //   if (this.temp.isUsedName === 1 && this.temp.isUsedTime === 1 && this.temp.keyword === '' && this.temp.optionValueList.length == 0) {
    //     return callback(new Error('必须选择一个文字内容'))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      stgTypeNumber: 44,
      containerStyle: {},
      picStyle: {},
      viewId: 'watermarkPreview1',
      viewStyle: {},
      maskId: 'maskDiv1',
      maskClass: 'mask-div',
      maskStyle: '',
      maskContent: '',
      placeOptions: [
        { label: this.$t('pages.placeOptions1'), value: 1 },
        { label: this.$t('pages.placeOptions2'), value: 2 },
        { label: this.$t('pages.placeOptions8'), value: 3 },
        { label: this.$t('pages.placeOptions9'), value: 4 },
        { label: this.$t('pages.placeOptions3'), value: 5 },
        { label: this.$t('pages.placeOptions4'), value: 6 },
        { label: this.$t('pages.placeOptions5'), value: 7 },
        { label: this.$t('pages.placeOptions6'), value: 8 },
        { label: this.$t('pages.placeOptions7'), value: 9 },
        { label: this.$t('pages.placeOptions10'), value: 15 }
      ],
      dotMatrix: ['begin', 0, 0, 0, 0, 0, 1, 2, 3],
      fileList: [],
      remark: '', // 策略的备注信息，为了让remark不被监听到，所以独立出来
      temp: {},
      defaultTemp: {
        id: undefined,
        name: '',
        isWaterMark: 1,
        color: '#008080',
        colorR: '',
        colorG: '',
        colorB: '',
        strFont: this.$t('pages.fontOptions4'),
        dwEscapement: 45,
        keyword: '',
        isUsedTime: 0,
        isUsedName: 1,
        dwWordType: 1,
        fontHeight: 16,
        alphaValue: 102,
        waterMarkForm: 0, // 文字水印, 二维码, 点阵
        optionValue: null,
        optionValueList: [],
        markDistance: 30,
        strategyOptionList: [],
        strategyOption: '',
        capture: 0,
        encryptCapture: 0,
        offlineMark: 0,
        encryptCtrlProcess: 0,
        groupId: null
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      submitFunc: undefined,
      rules: {
        name: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }, { validator: this.nameValidator, trigger: 'blur' }],
        groupId: [{ required: true, message: this.$t('pages.required1'), trigger: 'change' }],
        keyword: [{ validator: this.keywordValid, trigger: 'blur' }],
        color: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        dwEscapement: [{ required: true, type: 'number', message: this.$t('pages.required1'), trigger: 'blur' }],
        fontHeight: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        markDistance: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }]
      },
      //  是否展示新增分组按钮
      addGroupAble: false
    }
  },
  computed: {
    gridTable() {
      return this.$parent.gridTable
    },
    wordType() {
      return wordType
      // return { 0: this.$t('pages.wordType1'), 1: this.$t('pages.wordType2') }
    },
    textMap() {
      return {
        update: this.$t('pages.waterMarkLib_update3'),
        create: this.$t('pages.waterMarkLib_create3')
      }
    },
    // 水印内容map
    optionsMap() {
      return options.reduce((map, option) => {
        map[option.value] = option.label
        return map
      }, {})
    },
    placeType() {
      const obj = {}
      this.placeOptions.forEach(item => {
        obj[item.value] = item.label
      })
      return obj
    },
    fgorbgType() {
      return fgorbgType
      // return {
      //   1: this.$t('pages.fgorbg1'),
      //   2: this.$t('pages.fgorbg2')
      // }
    },
    waterFormType() {
      return JSON.parse(JSON.stringify(waterFormType, [0, 2]))
      // return {
      //   0: this.$t('pages.waterMarkForm1'),
      //   2: this.$t('pages.waterMarkForm3')
      // }
    },
    placeCssType() {
      return {
        1: 'top center',
        2: 'center center',
        3: 'center left',
        4: 'center right',
        5: 'bottom center',
        6: 'top left',
        7: 'top right',
        8: 'bottom left',
        9: 'bottom right',
        15: ''
      }
    },
    wordTypeOptions: function() { // 要根据水印形式来确定下拉框内容
      if (this.temp.waterMarkForm === 2) {
        const arr = []
        for (let i = 1; i <= 10; i++) {
          arr.push({ label: this.$t('pages.fontSize1', { size: i }), value: i })
        }
        return arr
      } else {
        return [
          { label: this.$t('pages.wordType3'), value: 0 },
          { label: this.$t('pages.wordType4'), value: 1 }
        ]
      }
    }
  },
  watch: {
    temp: {
      handler(newValue, oldValue) {
        const { strategyOptionList = [], waterMarkForm, dwWordType, strFont,
          fontHeight, color, dwEscapement, alphaValue, markDistance } = this.temp
        // 水印 文字内容 (选项拼接在一起)
        const watermarkContent = strategyOptionList.reduce((content, option) => {
          const optionLabel = this.optionsMap[option.dataId]
          return content ? `${content}、${optionLabel}` : optionLabel
        }, '')
        // 圆点大小
        const dotSizeTemplate = waterMarkForm === 2 ? this.$t('pages.dotSizeTemplate', { size: dwWordType }) : ''
        // i18n字体
        const i18nStrFont = getDictLabel(this.fontOptions, strFont)
        // 字体
        const fontTemplate = waterMarkForm === 2 ? '' : this.$t('pages.fontTemplate', { font: `${i18nStrFont} ${fontHeight} ${this.wordType[dwWordType]}` })
        // 截屏时才显示水印
        const showWatermark = this.temp.capture === 1
          ? this.temp.encryptCapture == 1 ? `${this.$t('pages.encryptScreenShotWatermark')}；` : `${this.$t('pages.showWatermarkOnlyWhenScreenshot')}；`
          : ''
        // 离线时才显示水印
        const showOfflineWatermark = this.temp.offlineMark === 1 ? `${this.$t('pages.showWatermarkOnlyWhenOffline')}；` : ''
        const showEncryptCtrlProcess = this.temp.encryptCtrlProcess === 1 ? `${this.$t('pages.encryptCtrlProcessWatermark')}；` : ''
        // 文字水印详情 (文字内容、圆点/字体、[截屏时才显示水印]、颜色、倾斜度、水印形式、透明度、水印间隔)
        this.remark = this.$t('pages.screenTextTemplate', {
          watermarkContent, dotSizeTemplate, fontTemplate, showWatermark, color, degree: dwEscapement,
          waterMarkForm: this.waterFormType[waterMarkForm], alphaValue: parseInt((alphaValue) * 100 / 255), markDistance, showOfflineWatermark, showEncryptCtrlProcess
        })
        watermark(this)
      },
      immediate: true,
      deep: true
    },
    dialogFormVisible(val) {
      if (val) {
        this.$emit('dialogShow')
      }
    }
  },
  methods: {
    nameValidator(rule, value, callback) {
      getLibByName({ name: value, type: this.getDataType() }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.waterMarkLib_text29')))
        } else {
          callback()
        }
      }).catch(() => {
        this.submitting = false
      })
    },
    groupIdValidator(rule, value, callback) {
      if (this.groupIds != null) {
        if (value == null || value === '') {
          callback(new Error(this.$t('pages.waterMarkLib_text33')))
        } else {
          getGroupByName({ name: value }).then(res => {
            if (res.data == null) {
              callback(new Error(this.$t('pages.waterMarkLib_text34')))
            } else {
              callback()
            }
          }).catch(() => {
            this.submitting = false
          })
        }
      }
    },
    keywordValid(rule, value, callback) {
      let flag = false  // 水印内容是否包含了自定义内容
      this.temp.strategyOptionList.forEach(item => {
        if (item.dataId == '00') {
          flag = true
        }
      })
      if (!this.temp.keyword && flag) {
        callback(new Error(this.$t('pages.waterMarkLib_text23')))
      } else {
        callback()
      }
    },
    getDataType() {
      return 1
    },
    hide() {
      this.dialogFormVisible = false
    },
    colorChange(val) {
      if (val == null) {
        this.temp.color = '#008080'
      }
    },
    changeValue: function(value) {
      if (value === 2) {
        this.temp.dwWordType = 3
      } else {
        this.temp.dwWordType = 0
      }
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.remark = ''
    },
    handleCreate(groupId, submitFunc, addGroupAble) {
      this.addGroupAble = addGroupAble || false
      this.submitFunc = submitFunc
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.temp.groupId = groupId || null
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        watermark(this)
      })
    },
    versionDeal() {
      // 版本数据处理，有些旧的数据需要进行格式化，兼容一下
      if (this.temp.strategyOption) {
        // strategyOption的每两个字符表示一个文字项设置
        for (let i = 0; i < this.temp.strategyOption.length; i = i + 2) {
          const code = this.temp.strategyOption[i] + this.temp.strategyOption[i + 1]
          if (this.optionsMap[code]) {
            this.temp.strategyOptionList.push({ label: this.optionsMap[code], id: i, dataId: code })
          }
        }
      } else {
        // strategyOption为空说明是旧数据，需要进行兼容处理
        if (this.temp.isUsedTime == 0) {
          // 添加时间
          this.temp.strategyOptionList.push({ label: this.optionsMap['05'], id: 1, dataId: '05' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 2, dataId: '02' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['06'], id: 3, dataId: '06' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 4, dataId: '01' })
        }
        if (this.temp.isUsedName == 0) {
          // 添加终端名称
          this.temp.strategyOptionList.push({ label: this.optionsMap['04'], id: 5, dataId: '04' })
          if (this.temp.optionValueList.indexOf(4) != -1) {
            // 如果下个文字项是操作员，则要显示为一行
            this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 6, dataId: '02' })
          } else {
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 7, dataId: '01' })
          }
        }
        if (this.temp.optionValueList.indexOf(4) != -1) {
          // 添加操作员账号
          this.temp.strategyOptionList.push({ label: this.optionsMap['03'], id: 8, dataId: '03' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 9, dataId: '01' })
        }
        if (this.temp.optionValueList.indexOf(1) != -1) {
          // 添加ip
          this.temp.strategyOptionList.push({ label: this.optionsMap['07'], id: 10, dataId: '07' })
          if (this.temp.optionValueList.indexOf(2) != -1) {
            // 如果下个文字项是Mac，则要显示为一行
            this.temp.strategyOptionList.push({ label: this.optionsMap['02'], id: 11, dataId: '02' })
          } else {
            this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 12, dataId: '01' })
          }
        }
        if (this.temp.optionValueList.indexOf(2) != -1) {
          // 添加mac
          this.temp.strategyOptionList.push({ label: this.optionsMap['08'], id: 13, dataId: '08' })
          this.temp.strategyOptionList.push({ label: this.optionsMap['01'], id: 14, dataId: '01' })
        }
        if (this.temp.strategyOptionList && this.temp.strategyOptionList[this.temp.strategyOptionList.length - 1].dataId == '01') {
          // 如果最后一个文字项是回车符，则去掉
          this.temp.strategyOptionList.pop()
        }
      }
    },
    handleUpdate(row, submitFunc, addGroupAble) {
      this.addGroupAble = addGroupAble || false
      this.submitFunc = submitFunc
      this.resetTemp()
      this.remark = row.remark
      this.temp = Object.assign(this.temp, row.info) // copy obj
      this.temp.groupId = row.groupId
      this.temp.id = row.id
      this.temp.optionValueList = this.numToList(this.temp.optionValue, 3)
      // 屏幕水印的透明度和预览组件的透明度是相反的，要翻转一下
      this.temp.alphaValue = 255 - this.temp.alphaValue
      // 因为屏幕水印的枚举值不一样，1代表的是点阵，但是预览组件2代表的是点阵，所以这边转换一下
      if (this.temp.waterMarkForm == 1) {
        this.temp.waterMarkForm = 2
      }
      // 版本兼容
      this.versionDeal()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        watermark(this)
      })
    },
    screenShotChange(val) {
      this.temp.capture = val
      if (this.temp.capture == 0) {
        this.temp.encryptCapture = 0
      }
    },
    validataData() {
      if (this.temp.strategyOptionList.length == 0) {
        this.$message({
          showClose: true,
          message: this.$t('pages.waterMarkLib_text30'),
          type: 'error'
        })
        return false
      }
      return true
    },
    formatFormData(deleteGuid) {
      const data = JSON.parse(JSON.stringify(this.temp))
      data.remark = this.remark
      if (data.color != '') {
        const colors = toRGB(data.color)
        data.colorR = colors[0]
        data.colorG = colors[1]
        data.colorB = colors[2]
      }
      // 历史问题，屏幕水印的透明度和打印水印相反，值越大越明显，所以要转一下
      data.alphaValue = 255 - data.alphaValue
      if (data.waterMarkForm == 2) {
        data.waterMarkForm = 1
      }
      data.optionValueList.splice(0)
      data.strategyOption = data.strategyOptionList.map(item => {
        // 为了兼容旧终端，一些旧的参数也要根据strategyOption设置一下
        if (item.dataId == '04') {
          // 终端
          data.isUsedName = 0
        }
        if (item.dataId == '03' && data.optionValueList.indexOf(4) == -1) {
          // 操作员
          data.optionValueList.push(4)
        }
        if (item.dataId == '05' || item.dataId == '06') {
          // 时间
          data.isUsedTime = 0
        }
        if (item.dataId == '07' && data.optionValueList.indexOf(1) == -1) {
          // IP
          data.optionValueList.push(1)
        }
        if (item.dataId == '08' && data.optionValueList.indexOf(2) == -1) {
          // IP
          data.optionValueList.push(2)
        }
        return item.dataId
      }).join('')
      data.strategyOptionList = null
      if (data.capture === 1) {
        if (data.encryptCapture === 1) {
          data.optionValueList.push(512)
        } else {
          data.optionValueList.push(128)
        }
      }
      if (data.encryptCtrlProcess === 1) {
        data.optionValueList.push(256)
      }
      if (data.offlineMark === 1) {
        data.optionValueList.push(4096)
      }
      data.optionValue = this.getSum(data.optionValueList)
      const groupId = data.groupId
      this.$delete(data, 'groupId')
      deleteGuid === true && (delete data.guid)
      const tempData = {
        type: this.getDataType(),
        id: data.id,
        groupId: groupId,
        name: data.name,
        remark: this.remark,
        json: JSON.stringify(data)
      }
      return tempData
    },
    editData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const flag = this.validataData()
          if (!flag) {
            this.submitting = false
            return
          }
          const formData = this.toFormData(this.formatFormData())
          this.submitFunc(formData).then(() => {
            this.submitting = false
            this.hide()
            this.$emit('reloadTable')
            const message = { create: this.$t('text.createSuccess'), update: this.$t('text.updateSuccess') }[this.dialogStatus]
            this.$notify({ title: this.$t('text.success'), message: message, type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleCopy() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const flag = this.validataData()
          if (!flag) {
            this.submitting = false
            return
          }
          const formData = this.formatFormData(true)
          this.$parent.handleCopy(formData)
        } else {
          this.submitting = false
        }
      })
    },
    handleGroup() {
      if (this.handleGroupCreate) {
        this.handleGroupCreate()
      }
    },
    setGroupId(groupId) {
      if (groupId) {
        this.temp.groupId = String(groupId)
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-dialog__body{
    .el-tabs{
      border: 0;
    }
    .el-tabs__item{
      color: #666;
      &.is-active {
        color: #409EFF;
      }
    }
    .water-mark-form{
      overflow: auto;
      height: 460px;
    }
    .el-main{
      padding: 10px 20px 0 20px;
    }
  }
  >>>.el-input-number .el-input__inner{
    text-align: left;
  }
  .template-remark >>>.el-textarea__inner {
    cursor: default;
  }
  .slider {
    width: 95%;
    margin: 0 auto;
    >>>.el-slider__runway {
      margin: 12px 0;
      background-color: #fff;
    }
  }
</style>
