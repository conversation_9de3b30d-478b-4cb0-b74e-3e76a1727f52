<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('components.saveAs')"
      :visible.sync="dialogFormVisible"
      width="1000px"
    >
      <Form>
        <FormItem :label="$t('pages.templateName')" prop="name">
          <el-input v-model="temp.name" v-trim maxlength="20"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="editData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLibByName } from '@/api/system/baseData/waterMarkLib'
// import { createLib } from '@/api/system/baseData/waterMarkLib'

export default {
  name: 'CopyWaterMark',
  props: {
    dataType: {
      type: Number,
      default: 1
    },
    dlgRef: { // 新增界面组件的ref
      type: String,
      default: ''
    }
  },
  data() {
    return {
      temp: {},
      dialogFormVisible: false,
      submitting: false
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
    getDlg() {
      return this.$parent.$refs[this.dlgRef]
    },
    nameValidator(rule, value, callback) {
      getLibByName({ name: value, type: this.dataType }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.waterMarkLib_text29')))
        } else {
          callback()
        }
      }).catch(() => {
        this.submitting = false
      })
    },
    handleCopy(row, submitFunc, formatFormData) {
      this.submitFunc = submitFunc
      this.temp = Object.assign({}, row)
      this.dialogFormVisible = true
    },
    editData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitFunc(this.temp)
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>
