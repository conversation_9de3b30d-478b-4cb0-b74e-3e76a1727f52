<template>
  <div class="table-container">
    <grid-table ref="waterMarkList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="(rowDatas) => {this.$emit('selectChange', rowDatas)}"/>
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? $t('text.editInfo', { info: $t('pages.waterMarkGroup')}) : $t('text.deleteInfo', { info: $t('pages.waterMarkLib3_waterMark')})"
      :col-model="colModel"
      :delete-filter-name="$t('pages.waterMarkLib3_waterMark')"
      :edit-type="updateGroupForm ? 0 : 1"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="groupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>
  </div>
</template>

<script>
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import { getLibPage, createLib, updateLib, deleteLib, batchUpdateGroup, batchUpdateAllGroup } from '@/api/system/baseData/waterMarkLib'
import { formatWatermarkDetail } from '@/utils/waterMark';
export default {
  name: 'WaterMarkLibList',
  components: { BatchEditPageDlg },
  props: {
    dlgRef: { // 新增界面组件的ref
      type: String,
      default: ''
    },
    type: { // 数据类型：1、屏幕水印；2、打印水印；3、office文档水印
      type: Number,
      default: 1
    },
    groupTreeData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'templateName', width: '100', sort: 'custom', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '100', formatter: this.groupFormatter },
        { prop: 'remark', label: 'templateRemark', width: '500', formatter: this.templateDetailFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: {},
      submitting: false,
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined // 批量修改的分组id
    }
  },
  computed: {
    gridTable() {
      return this.$refs['waterMarkList']
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    getDlg() {
      return this.$parent.$refs[this.dlgRef]
    },
    rowDataApi(option) {
      option = Object.assign(this.query, option)
      option.type = this.type
      return getLibPage(option)
    },
    handleCreate() {
      this.getDlg().handleCreate(this.query.groupId, this.createData)
    },
    handleUpdate(row) {
      const rowData = JSON.parse(JSON.stringify(row))
      this.getDlg().handleUpdate(rowData, this.updateData)
    },
    searchData(query) {
      this.query = query
      this.loadGridTableData()
    },
    loadGridTableData() {
      this.gridTable.execRowDataApi(this.query)
    },
    handleMove() {
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    createData(formData, callBack) {
      return createLib(formData)
    },
    updateData(formData, callBack) {
      return updateLib(formData)
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.groupTreeData, data)
    },
    templateDetailFormatter(row, data) {
      return formatWatermarkDetail(row.info, row.type) || data
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleDrag() {
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.loadGridTableData()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteLib(params).then(respond => {
          this.loadGridTableData()
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    }
  }
}
</script>
