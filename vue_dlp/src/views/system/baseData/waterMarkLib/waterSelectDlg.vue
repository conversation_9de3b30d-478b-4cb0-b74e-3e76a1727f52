<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="title"
      :remark="$t('pages.waterMarkLib_text31')"
      :visible.sync="dlgVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="toolbar">
        <el-row>
          <el-col :span="1" style="font-weight: 700;padding-top: 7px;">{{ $t('form.group') }}</el-col>
          <el-col :span="7">
            <tree-select ref="groupTree" v-model="query.groupId" :data="treeData" :width="200" @change="treeNodeClick" />
          </el-col>
          <el-col :span="16">
            <div style="float: right;">
              <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.waterMarkLib_text31')" style="width: 200px;"/>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <grid-table
        ref="appInfoList"
        :height="420"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :default-sort="{ prop: 'name' }"
        :page-sizes="[ 20, 50, 100, 500, 1000 ]"
      />
      <div slot="footer" class="dialog-footer">
        <link-button
          btn-type="primary"
          btn-style="float: left"
          :menu-code="'A5A'"
          :link-url="{ path: '/system/baseData/waterMarkLib', query: { tabName: tabName } }"
          :btn-text="$t('pages.maintainWaterMarkLib')"
        />
        <el-button type="primary" :loading="submitting" @click="handleSelect()">
          {{ $t('pages.addWaterMark') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <preview-dlg ref="previewWaterDlg"/>
  </div>
</template>

<script>
import PreviewDlg from '@/views/system/baseData/waterMarkLib/previewDlg'
import { getGroupTreeNode, getLibPage } from '@/api/system/baseData/waterMarkLib'
import { findNode } from '@/utils/tree'

export default {
  name: 'WaterSelectDlg',
  components: { PreviewDlg },
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      stgTypeNumber: 151, // 策略类型编号 2打印水印策略 44屏幕水印策略 151office文档水印
      colModel: [
        { prop: 'name', label: 'templateName', width: '100', fixed: true },
        { prop: 'groupId', label: 'group', width: '100', formatter: this.groupFormatter },
        { prop: 'remark', label: 'templateRemark', width: '400' },
        { label: '', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [{ label: 'overview', click: (row) => { this.$refs['previewWaterDlg'].handleUpdate(row.info, this.stgTypeNumber) } }]
        }
      ],
      query: { // 查询条件
        page: 1,
        usbName: '',
        groupId: 0
      },
      dlgVisible: false,
      submitting: false,
      selectTreeId: null,
      title: this.$t('pages.waterMarkLib'),
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.waterMarkLib'), parentId: '', children: [] }]
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    },
    tabName() {
      // 策略类型编号 2打印水印策略 44屏幕水印策略 151 office文档水印
      const tab = { 2: 'printTab', 44: 'screenTab', 151: 'officeTab' }
      return tab[this.stgTypeNumber]
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    show(type) { // 数据类型：1、屏幕水印；2、打印水印；3、office文档水印
      if (type === 1) {
        this.title = this.$t('pages.waterMarkLib1')
        this.stgTypeNumber = 44
      } else if (type === 2) {
        this.title = this.$t('pages.waterMarkLib2')
        this.stgTypeNumber = 2
      } else if (type === 3) {
        this.title = this.$t('pages.waterMarkLib3')
        this.stgTypeNumber = 151
      }
      this.dlgVisible = true
      this.selectTreeId = null
      this.query.groupId = 0
      this.query.usbName = null
      this.query.type = type
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNode()
      })
    },
    treeNodeClick(data, node) {
      this.selectTreeId = node.dataId
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        this.selectTreeId = 0
      }
      this.query.groupId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      return getLibPage(newOption)
    },
    loadGroupTree: function() {
      getGroupTreeNode().then(respond => {
        this.treeData[0].children = respond.data
        this.$nextTick(() => {

        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      this.$emit('select', this.softTable.getSelectedDatas())
      this.dlgVisible = false
    },
    groupFormatter(row, data) {
      const nodeData = findNode(this.treeData, data, 'dataId')
      return nodeData ? nodeData.label : ''
    }
  }
}
</script>
