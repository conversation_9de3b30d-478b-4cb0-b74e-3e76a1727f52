<template>
  <div class="table-container">
    <grid-table ref="otherConfigList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="(rowDatas) => {this.$emit('selectChange', rowDatas)}"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 700px;">
        <FormItem :label="$t('components.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('components.enable')">
          <el-switch v-model="temp.active"></el-switch>
        </FormItem>
        <FormItem :label="$t('pages.readTimes')" label-width="175">
          <el-row>
            <el-col :span="4">
              <el-input v-model="temp.readTimes" :disabled="notReadTimes" maxlength="5"></el-input>
            </el-col>
            <el-col :span="8" style="padding-left: 5px;">
              <el-checkbox v-model="notReadTimes" :label="$t('pages.noLimit')" @change="changeNotReadTimes"></el-checkbox>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.readDays')" label-width="175">
          <el-row>
            <el-col :span="4">
              <el-input v-model="temp.readDays" :disabled="notReadDays" maxlength="5"></el-input>
            </el-col>
            <el-col :span="8" style="padding-left: 5px;">
              <el-checkbox v-model="notReadDays" :label="$t('pages.noLimit')" @change="changeNotReadDays"></el-checkbox>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.suffix')" label-width="175">
          <el-row>
            <el-col :span="5">
              <el-select v-model="temp.suffix">
                <el-option value="*.*" :label="$t('pages.allType')"></el-option>
                <el-option value=".ldm" label=".ldm"></el-option>
                <el-option value=".exe" label=".exe"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </FormItem>
        <el-checkbox-group v-model="enableCodes" style="border-top: 2px solid #aaa;padding-top: 10px;margin-top: 10px;">
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="1">{{ $t('pages.enableCodes1') }}</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="2">{{ $t('pages.enableCodes2') }}</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="4">{{ $t('pages.enableCodes4') }}</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="8">{{ $t('pages.enableCodes8') }}</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
        </el-checkbox-group>
        <el-checkbox-group v-model="pwdCodes" style="border-top: 2px solid #aaa;padding-top: 10px;margin-top: 10px;">
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="1">{{ $t('pages.pwdCodes1') }}</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="2">{{ $t('pages.pwdCodes2') }}</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
        </el-checkbox-group>
        <el-checkbox-group v-model="disableCodes" style="border-top: 2px solid #aaa;padding-top: 10px;margin-top: 10px;">
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="1">{{ $t('pages.disableCodes1') }}</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="2">{{ $t('pages.disableCodes2') }}</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="4">{{ $t('pages.disableCodes4') }}</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="8">{{ $t('pages.disableCodes8') }}</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem label-width="1">
            <el-checkbox :label="16">{{ $t('pages.disableCodes16') }}</el-checkbox>
          </FormItem>
        </el-checkbox-group>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getConfigPage, createConfig, updateConfig, deleteConfig, getConfigByName } from '@/api/dataEncryption/encryption/fileOutgoing'
import { buttonFormatter } from '@/utils'
export default {
  name: 'PrintWaterMarkLib',
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'templateName', width: '150', fixed: true },
        { prop: 'remark', label: 'templateRemark', width: '450' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', formatter: (row) => buttonFormatter(row, this), click: this.handleUpdate }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        readTimes: 0,
        readDays: 0,
        suffix: '*.*',
        enableCode: undefined,
        disableCode: undefined,
        pwdCode: undefined
      },
      notReadTimes: true,
      notReadDays: true,
      enableCodes: [],
      disableCodes: [],
      pwdCodes: [],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      isUpdateFormMode: false,
      processEditable: false,
      processDeleteable: false,
      textMap: {
        update: this.$t('pages.updateStg'),
        create: this.$t('pages.createStg')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['otherConfigList']
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    rowDataApi: function(option) {
      return getConfigPage(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.enableCodes.splice(0)
      this.disableCodes.splice(0)
      this.pwdCodes.splice(0)
      this.notReadTimes = true
      this.notReadDays = true
    },
    changeNotReadTimes(value) {
      if (value) this.temp.readTimes = 0
    },
    changeNotReadDays(value) {
      if (value) this.temp.readDays = 0
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row))
      if (this.temp.enableCode) this.enableCodes = this.numToList(this.temp.enableCode, 4)
      if (this.temp.disableCode) this.disableCodes = this.numToList(this.temp.disableCode, 5)
      if (this.temp.pwdCode) this.pwdCodes = this.numToList(this.temp.pwdCode, 2)
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteConfig({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    formatFrom() {
      this.temp.enableCode = this.getSum(this.enableCodes)
      this.temp.disableCode = this.getSum(this.disableCodes)
      this.temp.pwdCode = this.getSum(this.pwdCodes)
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFrom()
          createConfig(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFrom()
          const tempData = Object.assign({}, this.temp)
          updateConfig(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.updateRowData(this.temp)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      getConfigByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    processNameFormatter(row, data) {
      const result = ''
      return result
    },
    readTimesFormatter(row, data) {
      return data == 0 ? this.$t('pages.noLimit') : data
    },
    suffixFormatter(row, data) {
      return data === '*.*' ? this.$t('pages.allType') : data
    },
    enableCodeFormatter(row, data) {
      let result = ''
      const codes = this.numToList(data, 4)
      if (codes.indexOf(1) >= 0) result += `${this.$t('pages.enableCodeFormatter1')}；`
      if (codes.indexOf(2) >= 0) result += `${this.$t('pages.enableCodeFormatter2')}；`
      if (codes.indexOf(4) >= 0) result += `${this.$t('pages.enableCodeFormatter3')}；`
      if (codes.indexOf(8) >= 0) result += `${this.$t('pages.enableCodeFormatter4')}；`
      return result
    },
    disableCodeFormatter(row, data) {
      let result = ''
      const codes = this.numToList(data, 5)
      if (codes.indexOf(1) >= 0) result += `${this.$t('pages.disableCodeFormatter1')}；`
      if (codes.indexOf(2) >= 0) result += `${this.$t('pages.disableCodeFormatter2')}；`
      if (codes.indexOf(4) >= 0) result += `${this.$t('pages.disableCodeFormatter3')}；`
      if (codes.indexOf(8) >= 0) result += `${this.$t('pages.disableCodeFormatter4')}；`
      if (codes.indexOf(16) >= 0) result += `${this.$t('pages.disableCodeFormatter5')}；`
      return result
    },
    pwdCodeFormatter(row, data) {
      let result = ''
      const codes = this.numToList(data, 2)
      if (codes.indexOf(1) >= 0) result += `${this.$t('pages.pwdCodeFormatter1')}；`
      if (codes.indexOf(2) >= 0) result += `${this.$t('pages.pwdCodeFormatter2')}；`
      return result
    }
  }
}
</script>
