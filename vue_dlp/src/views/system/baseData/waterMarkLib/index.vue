<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu ref="groupTree" resizeable :default-expand-all="true" :data="groupTreeData" :render-content="renderContent" @node-click="groupTreeNodeClickEnd" />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" @click="getTab().handleMove()">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="getTab().handleCreate()">{{ $t('pages.addTemplate') }}</el-button>
        <el-button icon="el-icon-delete" size="mini" @click="getTab().handleDelete()">{{ $t('pages.delTemplate') }}</el-button>
        <el-button icon="el-icon-search" size="mini" @click="dotMatrixSearch">{{ $t('pages.dotMatrixSearch') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.waterMarkLib_text1')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <el-tabs ref="tabs" v-model="tabName" type="card" style="height: 41px;" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.screenTab')" name="screenTab"/>
        <el-tab-pane :label="$t('pages.printTab')" name="printTab"/>
        <el-tab-pane :label="$t('pages.officeTab')" name="officeTab"/>
      </el-tabs>
      <water-mark-lib-list
        ref="waterMarkList"
        :type="waterMarkMap[tabName].type"
        :dlg-ref="waterMarkMap[tabName].dlgRef"
        :group-tree-data="groupTreeData"
        @selectChange="selectRowChangeFunc"
      />
    </div>
    <screen-water-mark-dlg ref="screenDlg" :font-options="fontOptions" @dialogShow="() => { dialogShow('screenTab') }" @reloadTable="reloadData"/>
    <print-water-mark-dlg ref="printDlg" :font-options="fontOptions" @dialogShow="() => { dialogShow('printTab') }" @reloadTable="reloadData"/>
    <office-water-mark-dlg ref="officeDlg" :font-options="fontOptions" @dialogShow="() => { dialogShow('officeTab') }" @reloadTable="reloadData"/>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.waterMarkLib_text2')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div style="position: relative;">
        <img :src="require('@/assets/encode.jpg')" alt="encode" style="width: 100%;">
        <label class="img-label title">{{ $t('pages.patternCodeTable') }}</label>
        <label class="img-label first">{{ $t('pages.initialPattern') }}</label>
        <div v-if="dotMatrix.length > 1" class="dot-matrix">
          <label>{{ $t('pages.dotMatrix') }}</label>
          <div class="dot-matrix-image">
            <div v-for="(group, i) in 9" :key="i" class="dot-group">
              <div v-for="(dot, j) in 9" :key="j" :class="{dot, visible: dotMatrixOption[dotMatrix[i]].indexOf(dot) > -1}"></div>
            </div>
          </div>
        </div>
      </div>
      <Form label-width="90px" style="width: 750px;" :extra-width="{tw: 15, en: 130}">
        <FormItem :label="$t('pages.dotMatrixCode')">
          <el-input v-model="encode" clearable class="input-with-button" maxlength="8" style="width: calc(100% - 94px);" @keyup.enter.native="handleSearch"></el-input>
          <el-button icon="el-icon-search" size="mini" style="width: 90px;" @click="handleSearch">{{ $t('button.search') }}</el-button>
        </FormItem>
      </Form>
      <grid-table
        ref="terminalTable"
        :row-datas="terminalDatas"
        :col-model="terminalColModel"
        :height="120"
        :multi-select="false"
        :show-pager="false"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('components.saveAs')"
      :visible.sync="dialogCopyVisible"
      width="400px"
    >
      <Form ref="copyForm" :model="markLib" :rules="libRules" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.templateName')" prop="name">
          <el-input v-model="markLib.name" v-trim maxlength="20"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="editData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogCopyVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.waterMarkGroup')"
      :group-tree-data="formTreeData"
      :add-func="createGroup"
      :update-func="updateGroup"
      :delete-func="deleteGroup"
      :move-func="moveGroup"
      :edit-valid-func="getGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

  </div>
</template>

<script type="text/jsx">
import WaterMarkLibList from './waterMark'
import ScreenWaterMarkDlg from './screenWaterMarkDlg'
import PrintWaterMarkDlg from './printWaterMarkDlg'
import OfficeWaterMarkDlg from './officeWaterMarkDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import {
  getGroupTreeNode, createGroup, updateGroup, deleteGroup, getGroupByName,
  countLibByGroupId, moveGroup, deleteGroupAndData, moveGroupToOther
} from '@/api/system/baseData/waterMarkLib'
import { getTerminalPageByWaterCode } from '@/api/system/terminalManage/terminal'
import { getDeptPage } from '@/api/system/terminalManage/department'
import { getTypeFont } from '@/utils/dictionary'
import { getLibByName } from '@/api/system/baseData/waterMarkLib'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg';

export default {
  name: 'WaterMarkLib',
  components: { DeleteGroupDlg, OfficeWaterMarkDlg, ScreenWaterMarkDlg, PrintWaterMarkDlg, WaterMarkLibList, EditGroupDlg },
  data() {
    return {
      tabName: 'screenTab',
      terminalColModel: [
        { label: 'encode', width: '90', formatter: this.encodeFormatter },
        { prop: 'id', label: 'terminalCode', width: '110', sort: true },
        { prop: 'groupIds', label: 'sourceGroup', width: '100', formatter: this.groupFormatter },
        { prop: 'name', label: 'terminalName', width: '100', sort: true },
        { prop: 'computerName', label: 'computerName', width: '115', sort: true },
        { prop: 'mainIp', label: 'IP', width: '100', sort: true },
        { prop: 'mainMac', label: 'macAddr', width: '100' }
      ],
      terminalDatas: [],
      deptList: {},
      showTree: true,
      addBtnAble: false,
      deleteAble: false,
      groupTreeData: [{ label: this.$t('pages.waterMarkLib'), id: 'G0', dataId: '0', children: [] }],
      query: { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: ''
      },
      encode: '',
      dotMatrix: ['begin'],
      dotMatrixOption: {
        begin: [1, 3, 7, 9],
        0: [2, 5, 8],
        1: [3, 5, 7],
        2: [4, 5, 6],
        3: [1, 5, 9],
        4: [1, 7, 8],
        5: [1, 3, 4],
        6: [2, 3, 9],
        7: [6, 7, 9],
        8: [2, 4, 6],
        9: [2, 6, 8],
        A: [4, 6, 8],
        B: [2, 4, 8],
        C: [5, 6, 7],
        D: [1, 5, 8],
        E: [3, 4, 5],
        F: [2, 5, 9]
      },
      dialogVisible: false,
      dialogCopyVisible: false,
      markLib: {
        name: ''
      },
      textMap: {
        delete: this.$t('pages.waterMarkLib_delete_group')
      },
      submitting: false,
      formMode: 'create',
      treeNodeType: 'G',
      formTreeData: [],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        parentId: 0,
        dataId: undefined
      },
      libRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.libNameValidator, trigger: 'blur' }
        ]
      },
      fontOptions: getTypeFont(),
      waterMarkRef: 'screenDlg',
      waterMarkType: 1,
      waterMarkMap: {
        screenTab: { type: 1, dlgRef: 'screenDlg' },
        printTab: { type: 2, dlgRef: 'printDlg' },
        officeTab: { type: 3, dlgRef: 'officeDlg' }
      },
      title: this.$t('pages.waterMarkLib3_waterMark')
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    }
  },
  watch: {
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.query.tabName) {
        vm.tabName = to.query.tabName
        vm.$router.push({ query: {}})
        delete to.query.tabName
        vm.tabClick()
      }
    })
  },
  created() {
    this.loadGroupTree()
    this.getDeptList()
    this.resetTemp()
    // getFontOptions().then(res => {
    //   // 系统安装的字体
    //   this.fontOptions = res.data
    //   console.log('res',res.data)
    // })
  },
  activated() {
    this.loadGroupTree()
    this.getDeptList()
  },
  methods: {
    createGroup,
    updateGroup,
    deleteGroup,
    getGroupByName,
    moveGroup,
    deleteGroupAndData,
    moveGroupToOther,
    handleCopy(row) {
      this.markLib = Object.assign({}, row)
      this.dialogCopyVisible = true
    },
    dialogShow(tabName) {
      this.waterMarkType = this.waterMarkMap[tabName].type
      this.waterMarkRef = this.waterMarkMap[tabName].dlgRef
    },
    reloadData() {
      this.getTab().loadGridTableData()
    },
    editData() {
      this.submitting = true
      this.$refs['copyForm'].validate((valid) => {
        if (valid) {
          const formData = this.toFormData(this.markLib)
          this.getTab().createData(formData).then(res => {
            this.submitting = false
            this.dialogCopyVisible = false
            this.$refs[this.waterMarkRef].hide()
            this.reloadData()
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    libNameValidator(rule, value, callback) {
      getLibByName({ name: value, type: this.waterMarkType }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.waterMarkLib_text29')))
        } else {
          callback()
        }
      }).catch(() => {
        this.submitting = false
      })
    },
    getDeptList() {
      const query = { page: 1 }
      getDeptPage(query).then(res => {
        const data = res.data.items
        data.forEach(el => {
          this.deptList[el.id] = el
        })
      })
    },
    getTab() {
      return this.$refs['waterMarkList']
    },
    tabClick(pane, event) {
      setTimeout(() => {
        this.handleFilter()
      }, 0)
    },
    loadGroupTree: function() {
      getGroupTreeNode().then(respond => {
        this.groupTreeData[0].children = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    selectRowChangeFunc(rowDatas) {
      this.deleteAble = false
      if (rowDatas && rowDatas.length > 0) {
        this.deleteAble = true
      }
    },
    loadTypeTreeExceptRoot() {
      return this.groupTreeData[0].children
    },
    groupTreeNodeClickEnd: function(checkedData, checkedNode) {
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.addBtnAble = !!checkedData && checkedData.dataId > 0
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.getTab().searchData(this.query)
    },
    dotMatrixSearch() {
      this.dialogVisible = true
      this.encode = ''
      this.dotMatrix.splice(1)
      this.terminalDatas.splice(0)
    },
    handleSearch() {
      this.dotMatrix.splice(1)
      this.terminalDatas.splice(0)
      const reg = /^[A-Fa-f0-9]+$/
      if (!reg.test(this.encode)) {
        this.$message({
          message: this.$t('pages.waterMarkLib_text4'),
          type: 'error'
        })
        return
      }
      // const id = parseInt(this.encode, 16)
      const id = this.encode
      const query = { searchIdCode: id }
      getTerminalPageByWaterCode(query).then(res => {
        this.terminalDatas = res.data.items
      })

      let encode = this.encode.toUpperCase()
      const n = 8 - encode.length
      const arr = new Array(n)
      arr.push(encode)
      encode = arr.join('0')
      this.dotMatrix.push(...encode.split(''))
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId !== '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleGroupCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleGroupUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.handleGroupDelete(data)} />
          </span>
        </div>
      )
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    treeNodeIdToDataId: function(nodeId) {
      if (this.treeNodeType) {
        return nodeId.replace(this.treeNodeType, '')
      }
      return nodeId
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.treeNodeIdToDataId(data.parentId)
      })
    },
    handleGroupDelete: function(data) {
      countLibByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleMoving() {
      this.formTreeData = this.loadTypeTreeExceptRoot()
      this.$refs['editGroupDlg'].handleMove(this.getTab().gridTable.getSelectedIds())
    },
    createNode(data) {
      this.groupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.groupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.groupTree.findNode(this.groupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.getTab().searchData(this.query)
    },
    groupNameValidator(rule, value, callback) {
      getGroupByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    encodeFormatter(row, data) {
      let encode = row.id.toString(16).toUpperCase()
      const n = 8 - encode.length
      const arr = new Array(n)
      arr.push(encode)
      encode = arr.join('0')
      return encode
    },
    groupFormatter(row, data) {
      const groupNames = []
      if (data && data.length > 0) {
        data.forEach(groupId => {
          if (this.deptList[groupId] != null) {
            groupNames.push(this.deptList[groupId].name)
          }
        })
      }
      return groupNames.join(',')
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.groupTree.findNode(this.groupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.setCurrentKey(nodeData.id)
        this.groupTreeNodeClickEnd(nodeData, {})
      }
    },
    refreshTableData() {
      this.query.groupId = undefined
      this.getTab().searchData(this.query)
    }
  }
}
</script>

<style lang='scss' scoped>
.module-form{
  margin-left: 210px;
  height: 100%;
  overflow: auto;
  .el-tabs{
    height: calc(100% - 40px);
  }
  .el-tab-pane{
    padding: 0 10px 10px;
  }
}
.app-container .tree-container.hidden+.module-form{
  margin-left: 0;
}
.img-label {
  position: absolute;
  color: #026cac;
  display: block;
  text-align: center;
}
.title {
  width: 180px;
  left: 0;
  top: 8px;
  font-size: 17px;
}
.first {
  width: 100px;
  left: 40px;
  top: 122px;
  font-size: 15px;
}
.dot-matrix{
  width: 120px;
  height: 130px;
  text-align: center;
  position: absolute;
  left: 25px;
  bottom: 20px;
  label{
    color: #026cac;
    white-space: nowrap;
  }
  .dot-matrix-image{
    width: 120px;
    height: 120px;
    margin: 3px 0 0 3px;
    .dot-group{
      width: 36px;
      height: 36px;
      margin: 2px;
      float: left;
      border: 1px solid transparent;
      box-sizing: border-box;
      position: relative;
    }
    .dot{
      float: left;
      position: relative;
      visibility: hidden;
      border: 2px solid #3a7797;
      border-radius: 2px;
      margin: 3px;
    }
    .visible{
      visibility: visible;
    }
  }
}
</style>
