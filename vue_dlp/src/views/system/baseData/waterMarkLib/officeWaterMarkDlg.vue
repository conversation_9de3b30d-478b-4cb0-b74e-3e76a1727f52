<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @closed="closed"
    >
      <el-container>
        <el-aside width="60%">
          <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" class="water-mark-form">
            <FormItem :label="$t('pages.templateName')" prop="name" label-width="90px">
              <el-input v-model="temp.name" v-trim maxlength="20" />
            </FormItem>
            <el-card class="box-card">
              <el-checkbox v-model="temp.isWordMark" :false-label="0" :true-label="1" class="title">{{ $t('pages.waterMarkLib_text14') }}</el-checkbox>
              <FormItem :label="$t('pages.waterMark')" prop="codeInfo">
                <keyword-item
                  ref="keywordInfo"
                  :disabled="temp.isWordMark===0"
                  :max-length="20"
                  :keyword-filter="filterKeywordItem"
                  @change="changeTempTimestamp"
                  @node-drop="changeTempTimestamp"
                />
              </FormItem>
              <FormItem v-show="keywordVisible" :label="$t('pages.itemRowData1')" prop="keyword">
                <el-select
                  ref="keywordSelect"
                  v-model="temp.keyword"
                  :disabled="temp.isWordMark===0"
                  filterable
                  clearable
                  allow-create
                  default-first-option
                  class="fixed-height"
                  :placeholder="$t('pages.waterMarkLib_text15')"
                  @change="(value) => {temp.keyword = value}"
                >
                  <el-option :value="$t('pages.keyword1')"></el-option>
                  <el-option :value="$t('pages.keyword2')"></el-option>
                  <el-option :value="$t('pages.keyword3')"></el-option>
                  <el-option :value="$t('pages.keyword4')"></el-option>
                  <el-option :value="$t('pages.keyword5')"></el-option>
                  <el-option :value="$t('pages.keyword6')"></el-option>
                  <el-option :value="$t('pages.keyword7')"></el-option>
                </el-select>
              </FormItem>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.strFont')" prop="strFont">
                    <el-select v-model="temp.strFont" :disabled="temp.isWordMark===0" :placeholder="$t('text.select')">
                      <el-option v-for="item in fontOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.fontSize')" prop="fontSize" style="margin-bottom: 7px;">
                    <el-input-number v-model="temp.fontSize" :controls="false" :disabled="temp.isWordMark===0" :min="1" :max="200" size="mini" />
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.fontRotation')">
                    <el-select v-model="temp.fontRotation" :disabled="temp.isWordMark===0" :placeholder="$t('text.select')">
                      <el-option v-for="item in rotationOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.color')" prop="color">
                    <el-color-picker v-model="color" :disabled="temp.isWordMark===0" size="mini" @change="colorChange"/>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.iplace')">
                    <el-select v-model="temp.fontLayout" :disabled="temp.isWordMark === 0" class="fixed-height">
                      <el-option v-for="(value, key) in fontPlaceOpts" :key="key" :label="value" :value="Number(key)"/>
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12"><div style="height: 37px"/></el-col>
                <!--指定位置的水印位置参数-->
                <div v-show="temp.fontLayout === 0" class="specified-position-param">
                  <el-col :span="12">
                    <FormItem :label="$t('pages.fontShapeLeft')">
                      <el-select
                        v-model="temp.fontShapeLeft"
                        :disabled="temp.isWordMark===0"
                        :placeholder="$t('text.select')"
                        class="fixed-height"
                        @change="changeTempTimestamp"
                      >
                        <el-option v-for="item in shapeLeftOptions" :key="item.value" :label="item.label" :value="item.value" />
                      </el-select>
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem label-width="0">
                      <el-input
                        v-model="temp.fontShapeLeftSize"
                        class="size-input"
                        :disabled="temp.isWordMark===0 || temp.fontShapeLeft !== 0"
                        @change="shapeSizeChange('fontShapeLeftSize')"
                      /> cm
                    </FormItem>
                  </el-col>

                  <el-col :span="12">
                    <FormItem :label="$t('pages.fontShapeTop')">
                      <el-select
                        v-model="temp.fontShapeTop"
                        :disabled="temp.isWordMark===0"
                        :placeholder="$t('text.select')"
                        class="fixed-height"
                        @change="changeTempTimestamp"
                      >
                        <el-option v-for="item in shapeTopOptions" :key="item.value" :label="item.label" :value="item.value" />
                      </el-select>
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem label-width="0">
                      <el-input
                        v-model="temp.fontShapeTopSize"
                        class="size-input"
                        :disabled="temp.isWordMark===0 || temp.fontShapeTop !== 0"
                        @change="shapeSizeChange('fontShapeTopSize')"
                      /> cm
                    </FormItem>
                  </el-col>
                </div>
                <!--平铺位置的水印位置参数-->
                <div v-show="temp.fontLayout === 1" class="tiled-param">
                  <el-col :span="12">
                    <FormItem :label="$t('pages.horizontalSpacing')" prop="lrDistance">
                      <div style="display: flex">
                        <el-input-number
                          v-model="temp.lrDistance"
                          :controls="false"
                          :disabled="temp.isWordMark===0"
                          :min="5"
                          :max="200"
                          style="flex: 1"
                        />
                        <span style="width: 20px; text-align: center">pt</span>
                      </div>
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.verticalSpacing')" prop="tbDistance">
                      <div style="display: flex">
                        <el-input-number
                          v-model="temp.tbDistance"
                          :controls="false"
                          :disabled="temp.isWordMark===0"
                          :min="5"
                          :max="200"
                          style="flex: 1"
                        />
                        <span style="width: 20px; text-align: center">pt</span>
                      </div>
                    </FormItem>
                  </el-col>
                </div>
                <el-col :span="24">
                  <FormItem :label="$t('pages.alphaValueShow')" prop="alphaValue">
                    <el-slider v-model="alphaValueShow" :step="1" :min="0" :max="100" :format-tooltip="(value) => {return value + '%'}" class="slider" @change="alphaValueChange" />
                  </FormItem>
                </el-col>
              </el-row>
            </el-card>
            <el-card class="box-card">
              <el-checkbox v-model="temp.isImageMark" :false-label="0" :true-label="1" class="title">{{ $t('pages.imageParams') }}</el-checkbox>
              <el-upload
                ref="upload"
                :disabled="temp.isImageMark===0"
                class="upload-demo"
                name="uploadFile"
                action="1111"
                accept=".jpg,.jpeg,.bmp"
                list-type="picture"
                :on-change="fileChange"
                :on-remove="handleRemove"
                :file-list="fileList"
                :auto-upload="false"
              >
                <el-button size="small" :disabled="temp.isImageMark===0" type="primary">{{ $t('pages.clickUpload') }}</el-button>
                <div slot="tip" class="el-upload__tip">{{ $t('pages.waterMarkLib_text16') }}</div>
              </el-upload>
              <el-row >
                <el-col :span="12">
                  <FormItem :label="$t('pages.picSize')" prop="picSize">
                    <el-slider v-model="temp.picSize" :step="10" :disabled="temp.isImageMark===0" :min="20" :max="500" :format-tooltip="(value) => {return value + '%'}" class="slider" />
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.picTurbid')">
                    <el-checkbox v-model="temp.picTurbid" :false-label="1" :true-label="0" :disabled="temp.isImageMark===0">{{ $t('pages.turbidity') }}</el-checkbox>
                  </FormItem>
                </el-col>

                <el-col :span="12">
                  <FormItem :label="$t('pages.imagePlace')">
                    <el-select v-model="temp.imageLayout" :disabled="temp.isImageMark === 0" class="fixed-height">
                      <el-option v-for="(value, key) in fontPlaceOpts" :key="key" :label="value" :value="Number(key)"/>
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12"><div style="height: 37px"/></el-col>
                <!--指定位置的水印位置参数(图片)-->
                <div v-show="temp.imageLayout === 0" class="specified-position-param" >
                  <el-col :span="12">
                    <FormItem :label="$t('pages.fontShapeLeft')">
                      <el-select
                        v-model="temp.picShapeLeft"
                        :disabled="temp.isImageMark===0"
                        :placeholder="$t('text.select')"
                        class="fixed-height"
                        @change="changeTempTimestamp"
                      >
                        <el-option v-for="item in shapeLeftOptions" :key="item.value" :label="item.label" :value="item.value" />
                      </el-select>
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem label-width="0">
                      <el-input
                        v-model="temp.picShapeLeftSize"
                        class="size-input"
                        :disabled="temp.isImageMark===0 || temp.picShapeLeft !== 0"
                        @change="shapeSizeChange('picShapeLeftSize')"
                      /> cm
                    </FormItem>
                  </el-col>

                  <el-col :span="12">
                    <FormItem :label="$t('pages.fontShapeTop')">
                      <el-select
                        v-model="temp.picShapeTop"
                        :disabled="temp.isImageMark===0"
                        :placeholder="$t('text.select')"
                        class="fixed-height"
                        @change="changeTempTimestamp"
                      >
                        <el-option v-for="item in shapeTopOptions" :key="item.value" :label="item.label" :value="item.value" />
                      </el-select>
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem label-width="0">
                      <el-input
                        v-model="temp.picShapeTopSize"
                        class="size-input"
                        :disabled="temp.isImageMark===0 || temp.picShapeTop !== 0"
                        @change="shapeSizeChange('picShapeTopSize')"
                      /> cm
                    </FormItem>
                  </el-col>
                </div>
                <!--平铺位置的水印位置参数（图片）-->
                <div v-show="temp.imageLayout === 1" class="tiled-param" >
                  <el-col :span="12">
                    <FormItem :label="$t('pages.horizontalSpacing')" prop="imageLrDistance">
                      <div style="display: flex">
                        <el-input-number
                          v-model="temp.imageLrDistance"
                          :controls="false"
                          :disabled="temp.isImageMark===0"
                          :min="5"
                          :max="200"
                          style="flex: 1"
                        />
                        <span style="width: 20px; text-align: center">pt</span>
                      </div>
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                    <FormItem :label="$t('pages.verticalSpacing')" prop="imageTbDistance">
                      <div style="display: flex">
                        <el-input-number
                          v-model="temp.imageTbDistance"
                          :controls="false"
                          :disabled="temp.isImageMark===0"
                          :min="5"
                          :max="200"
                          style="flex: 1"
                        />
                        <span style="width: 20px; text-align: center">pt</span>
                      </div>
                    </FormItem>
                  </el-col>
                </div>
              </el-row>
            </el-card>
            <el-card class="box-card">
              <FormItem :label="$t('pages.templateRemark')" prop="remark">
                <el-input v-model="remark" type="textarea" class="template-remark" disabled :autosize="{ minRows: 3, maxRows: 4}" />
              </FormItem>
            </el-card>
          </Form>
        </el-aside>
        <el-main>
          <label>{{ $t('pages.preview') }}</label>
          <el-tooltip class="item" effect="dark" placement="bottom-start" :content="$t('pages.waterMarkLib_text17')">
            <i class="el-icon-info" />
          </el-tooltip>
          <div class="preview-container" :style="containerStyle">
            <div class="pic-container" :style="picStyle">
              <div :id="viewId" ref="preview" class="watermark-preview" :style="viewStyle">
                <div :id="maskId" :class="maskClass" :style="maskStyle" v-html="maskContent"></div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleCopy">{{ $t('components.saveAs') }}</el-button>
        <el-button :loading="submitting" type="primary" @click.stop="editData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { rotationOptions, shapeLeftOptions, shapeTopOptions, watermark } from '@/utils/waterMark'
import { getLibByName } from '@/api/system/baseData/waterMarkLib'
import KeywordItem from '@/views/system/baseData/waterMarkLib/KeywordItem'
import { formatWatermarkDetail, filterKeywordItem } from '@/utils/waterMark';

export default {
  name: 'OfficeWaterMarkDlg',
  components: { KeywordItem },
  props: {
    fontOptions: {
      type: Array,
      default() {
        return [
          { label: this.$t('pages.fontOptions1'), value: this.$t('pages.fontOptions1') },
          { label: this.$t('pages.fontOptions2'), value: this.$t('pages.fontOptions2') },
          { label: this.$t('pages.fontOptions3'), value: this.$t('pages.fontOptions3') },
          { label: this.$t('pages.fontOptions4'), value: this.$t('pages.fontOptions4') }
        ]
      }
    }
  },
  data() {
    return {
      previewTxt: '',
      containerStyle: {},
      picStyle: {},
      viewId: 'watermarkPreview',
      viewStyle: {},
      maskId: 'maskDiv',
      maskClass: 'mask-div',
      maskStyle: '',
      maskContent: '',
      stgTypeNumber: 151, // 水印类型： office文档水印
      rotationOptions,
      shapeLeftOptions,
      shapeTopOptions,
      fontPlaceOpts: {
        0: this.$t('pages.wordPlace1'),
        1: this.$t('pages.wordPlace2')
      },
      placeOptions: [
        // 1-上中,2-中间，3-下中，6-左上，7-右上，8-左下，9-右下，13-左中，14-右中
        { label: this.$t('pages.placeOptions1'), value: 1, left: -999995, top: -999999 },
        { label: this.$t('pages.placeOptions2'), value: 2, left: -999995, top: -999995 },
        { label: this.$t('pages.placeOptions3'), value: 3, left: -999995, top: -999997 },
        { label: this.$t('pages.placeOptions4'), value: 6, left: -999998, top: -999999 },
        { label: this.$t('pages.placeOptions5'), value: 7, left: -999996, top: -999999 },
        { label: this.$t('pages.placeOptions6'), value: 8, left: -999998, top: -999997 },
        { label: this.$t('pages.placeOptions7'), value: 9, left: -999996, top: -999997 },
        { label: this.$t('pages.placeOptions8'), value: 13, left: -999998, top: -999995 },
        { label: this.$t('pages.placeOptions9'), value: 14, left: -999996, top: -999995 }
      ],
      fileList: [],
      remark: '', // 策略的备注信息，为了让remark不被监听到，所以独立出来
      groupId: undefined,
      markWord: undefined,
      color: '#008080',
      alphaValueShow: 50,
      temp: {},
      defaultTemp: {
        id: '',
        name: '',
        waterType: 0,
        extendContent: '',
        keyword: '',
        isWordMark: 1,
        isImageMark: 0,
        color: '#008080',
        strFont: this.$t('pages.fontOptions4'),
        fontSize: 16,
        fontRotation: 0,
        picSize: 100,
        picTurbid: 0,
        alphaValue: 50,
        imageMd5: '',
        imagePlace: 1,
        timestamp: 0,
        // 文字是否平铺 0 - 非平铺，1 - 平铺
        fontLayout: 0,
        lrDistance: 10,
        tbDistance: 10,
        // 图片是否平铺 0 - 非平铺, 1 - 平铺
        imageLayout: 0,
        imageLrDistance: 10,
        imageTbDistance: 10,
        fontShapeLeft: -999995,
        fontShapeTop: -999999,
        fontShapeLeftSize: 0,
        fontShapeTopSize: 0,
        picShapeLeft: -999995,
        picShapeTop: -999999,
        picShapeLeftSize: 0,
        picShapeTopSize: 0
      },
      keywordVisible: true,
      dialogFormVisible: false,
      dialogStatus: '',
      initializing: false,
      submitting: false,
      keywordRef: undefined,
      rules: {
        name: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }, { validator: this.nameValidator, trigger: 'blur' }],
        color: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        keyword: [{ validator: this.keywordValid, trigger: 'blur' }],
        fontSize: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        codeInfo: [{ validator: this.codeInfoValid, trigger: 'blur' }],
        lrDistance: [{ validator: this.fontDistanceRequire, trigger: 'blur' }],
        tbDistance: [{ validator: this.fontDistanceRequire, trigger: 'blur' }],
        imageLrDistance: [{ validator: this.imageDistanceRequire, trigger: 'blur' }],
        imageTbDistance: [{ validator: this.imageDistanceRequire, trigger: 'blur' }]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$parent.gridTable
    },
    textMap() {
      return {
        update: this.$t('pages.waterMarkLib_update1'),
        create: this.$t('pages.waterMarkLib_create1')
      }
    },
    placeType() {
      const obj = {}
      this.placeOptions.forEach(item => {
        obj[item.value] = item.label
      })
      return obj
    },
    placeCssType() {
      return {
        1: 'top center',
        6: 'top left',
        7: 'top right',
        13: 'center left',
        2: 'center center',
        14: 'center right',
        3: 'bottom center',
        8: 'bottom left',
        9: 'bottom right',
        15: ''
      }
    }
  },
  watch: {
    temp: {
      handler(newValue, oldValue) {
        // 正在初始化的渲染交给初始化的方法，防止渲染次数过多造成的卡顿
        if (this.initializing) { return }
        // 模板描述
        this.remark = formatWatermarkDetail(newValue, 3)

        this.watermark()
        this.keywordVisible = this.getExtendContent().indexOf('00') >= 0
      },
      deep: true
    },
    dialogFormVisible(val) {
      if (val) {
        this.$emit('dialogShow')
        this.$nextTick(() => {
          const ref = this.$refs['keywordSelect']
          if (ref && this.keywordRef != ref) {
            this.keywordRef = ref
            const input = ref.$el.querySelector('.el-input__inner')
            // 添加原生属性限制输入长度
            input && input.setAttribute('maxlength', 20)
          }
        })
      }
    }
  },
  created() {
  },
  methods: {
    // 文字参数的平铺间距检验
    fontDistanceRequire(rule, value, callback) {
      if (this.temp.fontLayout === 1 && !value) {
        callback(new Error(this.$t('pages.required1')))
      }
      callback()
    },
    // 图片参数的平铺间距检验
    imageDistanceRequire(rule, value, callback) {
      if (this.temp.imageLayout === 1 && !value) {
        callback(new Error(this.$t('pages.required1')))
      }
      callback()
    },
    closed() {
      this.resetTemp()
      this.$emit('closed')
    },
    getDataType() {
      return 3
    },
    hide() {
      this.dialogFormVisible = false
      this.fileList.splice(0)
      if (this.$refs['upload']) {
        this.$refs['upload'].clearFiles()
      }
    },
    numberLimit(value, type, min, max) {
      this.temp[type] = value > max ? max : value < min ? min : parseInt(value)
    },
    resetTemp() {
      this.initializing = true
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.alphaValueShow = 100 - this.temp.alphaValue
      this.remark = ''
      this.color = '#008080'
      this.fileList.splice(0)
      if (this.$refs['keywordInfo']) this.$refs['keywordInfo'].clear()
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.watermark()
    },
    // filterKeywordItem 移至 utils/waterMark.js
    filterKeywordItem,
    colorChange() {
      this.color = this.color || '#008080'
      this.temp.color = parseInt('0x' + this.color.substring(1))
      this.changeTempTimestamp()
    },
    changeTempTimestamp() {
      this.temp.timestamp = new Date().getTime()
    },
    alphaValueChange(val) {
      this.temp.alphaValue = 100 - val
    },
    shapeSizeChange(key) {
      const val = parseInt(this.temp[key])
      if (Number.isNaN(val)) {
        this.temp[key] = ''
      } else {
        this.temp[key] = Math.min(Math.max(val, 1), 10)
      }
    },
    getShapePlace(left, top) {
      for (let i = 0; i < this.placeOptions.length; i++) {
        const op = this.placeOptions[i]
        if (op.left === left && op.top === top) {
          return op.value
        }
      }
      return null
    },
    fileChange(file, fileList) {
      // 图片显示前做一下判断
      const IMG_ALLOWD = ['jpg', 'bmp', 'jpeg']
      const imgType = file.raw.type.split('/')[1]
      const imgSize = file.size / 1024
      // 判断图片格式
      if (IMG_ALLOWD.indexOf(imgType) === -1) {
        this.$message({
          message: this.$t('pages.waterMarkLib_text18'),
          type: 'error',
          duration: 2000
        })
        if (this.fileList.length === 0) {
          this.fileList = []
        } else {
          this.fileList = fileList.slice(0, 1)
        }
        return
      } else if (imgSize >= 200) {
        this.$message({
          message: this.$t('pages.waterMarkLib_text19'),
          type: 'error',
          duration: 2000
        })
        if (this.fileList.length === 0) {
          this.fileList = []
        } else {
          this.fileList = fileList.slice(0, 1)
        }
        return
      } else {
        this.fileList = fileList.slice(-1)
      }
      this.watermark()
    },
    handleCreate(groupId, submitFunc) {
      this.submitFunc = submitFunc
      this.resetTemp()
      this.initializing = false
      this.groupId = groupId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.watermark()
      })
    },
    handleUpdate(row, submitFunc) {
      this.submitFunc = submitFunc
      this.resetTemp()
      this.groupId = row.groupId
      this.remark = row.remark
      this.temp = Object.assign(this.temp, row.info, { id: row.id }) // copy obj
      const { alphaValue, extendContent, color, isImageMark, imageName, path } = this.temp
      this.alphaValueShow = 100 - alphaValue
      this.toShowShapeAlign()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        if (extendContent) {
          this.$refs['keywordInfo'].selectItemByCode(extendContent)
        }
        if (color) {
          let tempColor = color.toString(16)
          tempColor = tempColor.length < 6 ? new Array(6 - tempColor.length + 1).join('0') + tempColor : tempColor
          this.color = '#' + tempColor
        }
        if (isImageMark === 1) {
          this.fileList = [{ name: imageName, url: process.env.VUE_APP_BASE_API + path }]
        }
        this.initializing = false
        this.watermark()
      })
    },
    validataData() {
      if (this.temp.isImageMark === 1) {
        const uploadFiles = this.$refs['upload'].uploadFiles
        if (uploadFiles.length === 0) {
          this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.waterMarkLib_text20'), type: 'error', duration: 2000 })
          return false
        }
      }
      if (this.temp.isImageMark === 0 && this.temp.isWordMark === 0) {
        this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.waterMarkLib_text21'), type: 'error', duration: 2000 })
        return false
      }
      this.temp.remark = this.remark
      return true
    },
    formatFormData(deleteGuid) {
      // const data = Object.assign(this.temp)
      const data = JSON.parse(JSON.stringify(this.temp))
      // data.extendContent = this.$refs['keywordInfo'].getSelectedCode()
      data.extendContent = this.getExtendContent()
      if (!this.keywordVisible && data.keyword) {
        data.keyword = ''
      }
      data.color = parseInt('0x' + this.color.substring(1))
      data.waterType = 0
      if (data.isWordMark) {
        data.waterType += 1
      }
      if (data.isImageMark) {
        data.waterType += 2
      }
      this.toSubmitShapeAlign(data)
      deleteGuid === true && (delete data.guid)
      const tempData = {
        type: this.getDataType(),
        id: data.id,
        groupId: this.groupId,
        name: data.name,
        remark: this.remark,
        json: JSON.stringify(data)
      }
      if (data.isImageMark === 1) {
        const uploadFiles = this.$refs['upload'].uploadFiles
        tempData['uploadFile'] = uploadFiles[0].raw // 传文件
      }
      return tempData
    },
    editData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const flag = this.validataData()
          if (!flag) {
            this.submitting = false
            return
          }
          const formData = this.toFormData(this.formatFormData())
          this.submitFunc(formData).then(() => {
            this.submitting = false
            this.hide()
            this.$emit('reloadTable')
            const message = { create: this.$t('text.createSuccess'), update: this.$t('text.updateSuccess') }[this.dialogStatus]
            this.$notify({ title: this.$t('text.success'), message: message, type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    },
    codeInfoValid(rule, value, callback) {
      const code = this.$refs['keywordInfo'].getSelectedCode()
      if (this.temp.isWordMark && (!code || code.length === 0)) {
        callback(new Error(this.$t('pages.waterMarkLib_text22')))
      } else {
        callback()
      }
    },
    keywordValid(rule, value, callback) {
      // 水印内容是否包含了自定义内容
      // const flag = this.$refs['keywordInfo'].getSelectedCode().indexOf('00') > -1
      if (this.temp.isWordMark && !this.temp.keyword && this.keywordVisible) {
        callback(new Error(this.$t('pages.waterMarkLib_text23')))
      } else {
        callback()
      }
    },
    watermark() {
      if (this.$refs['keywordInfo']) {
        const contentCode = this.$refs['keywordInfo'].getSelectedCode()
        this.temp.extendContent = contentCode
        this.previewWatermark(this)
      }
    },
    previewWatermark(vm) {
      vm.contentCode = vm.temp.extendContent
      vm.temp.dwEscapement = vm.temp.fontRotation > 0 ? 45 : 0
      vm.temp.imageZoom = vm.temp.picSize
      vm.topMargin = 3
      vm.bottomMargin = 3
      vm.leftMargin = 3
      vm.rightMargin = 3
      if (vm.temp.isWordMark) {
        vm.iplace = vm.temp.fontLayout === 1 ? 15 : this.getShapePlace(vm.temp.fontShapeLeft, vm.temp.fontShapeTop)
        vm.temp.fontShapeLeftSize = vm.temp.fontShapeLeft >= 0 ? vm.temp.fontShapeLeftSize : ''
        vm.temp.fontShapeTopSize = vm.temp.fontShapeTop >= 0 ? vm.temp.fontShapeTopSize : ''
      }
      if (vm.temp.isImageMark) {
        vm.temp.imagePlace = vm.temp.imageLayout === 1 ? 15 : this.getShapePlace(vm.temp.picShapeLeft, vm.temp.picShapeTop)
        vm.temp.picShapeLeftSize = vm.temp.picShapeLeft >= 0 ? vm.temp.picShapeLeftSize : ''
        vm.temp.picShapeTopSize = vm.temp.picShapeTop >= 0 ? vm.temp.picShapeTopSize : ''
      }
      vm.alphaPercent = vm.temp.alphaValue / 100
      watermark(vm)
    },
    // 数据库中字体或图片的水平对齐和垂直对齐方式，转换为界面显示数据
    toShowShapeAlign() {
      this.temp.fontShapeLeftSize = this.temp.fontShapeLeft > 0 ? this.temp.fontShapeLeft : ''
      this.temp.fontShapeLeft = this.temp.fontShapeLeft > 0 ? 0 : this.temp.fontShapeLeft

      this.temp.fontShapeTopSize = this.temp.fontShapeTop > 0 ? this.temp.fontShapeTop : ''
      this.temp.fontShapeTop = this.temp.fontShapeTop > 0 ? 0 : this.temp.fontShapeTop

      this.temp.picShapeLeftSize = this.temp.picShapeLeft > 0 ? this.temp.picShapeLeft : ''
      this.temp.picShapeLeft = this.temp.picShapeLeft > 0 ? 0 : this.temp.picShapeLeft

      this.temp.picShapeTopSize = this.temp.picShapeTop > 0 ? this.temp.picShapeTop : ''
      this.temp.picShapeTop = this.temp.picShapeTop > 0 ? 0 : this.temp.picShapeTop
    },
    // 界面中显示的字体或图片的水平对齐和垂直对齐方式，转换为数据库数据
    toSubmitShapeAlign(data) {
      data.fontShapeLeft = this.temp.fontShapeLeft == 0 ? Math.max(this.temp.fontShapeLeftSize, 0) : this.temp.fontShapeLeft
      data.fontShapeTop = this.temp.fontShapeTop == 0 ? Math.max(this.temp.fontShapeTopSize, 0) : this.temp.fontShapeTop
      data.picShapeLeft = this.temp.picShapeLeft == 0 ? Math.max(this.temp.picShapeLeftSize, 0) : this.temp.picShapeLeft
      data.picShapeTop = this.temp.picShapeTop == 0 ? Math.max(this.temp.picShapeTopSize, 0) : this.temp.picShapeTop
    },
    getExtendContent() {
      if (this.$refs['keywordInfo']) {
        const contentCode = this.$refs['keywordInfo'].getSelectedCode()
        if (contentCode !== undefined) {
          return contentCode
        }
      }
      return this.temp.extendContent || ''
    },
    handleCopy() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const flag = this.validataData()
          if (!flag) {
            this.submitting = false
            return
          }
          const formData = this.formatFormData(true)
          this.$parent.handleCopy(formData)
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      getLibByName({ name: value, type: this.getDataType() }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.waterMarkLib_text29')))
        } else {
          callback()
        }
      }).catch(() => {
        this.submitting = false
      })
    }
  }
}
</script>

<style lang='scss' scoped>
.el-dialog__body{
  .el-tabs{
    border: 0;
  }
  .el-tabs__item{
    color: #666;
    &.is-active {
      color: #409EFF;
    }
  }
  .distance-input{
    width: 90px;
    margin-right: 3px;
  }
  .water-mark-form{
    overflow: auto;
    height: 460px;
    padding: 0 5px;
  }
  .el-main{
    padding: 10px 20px 0 20px;
  }
}
.title >>>.el-checkbox__label{
  line-height: 30px;
  font-size: 16px;
}
>>>.el-height-select .el-input__inner{
  height: 30px !important;
}
.fixed-height >>>.el-input__inner {
  height: 30px !important;
}
.template-remark >>>.el-textarea__inner {
  cursor: default;
}
.slider {
  width: 95%;
  margin: 0 auto;
  >>>.el-slider__runway {
    margin: 12px 0;
    background-color: #fff;
  }
}
.size-input {
  width: 60px;
  >>>.el-input__inner {
    padding: 0 5px;
    text-align: center;
  }
}
</style>
