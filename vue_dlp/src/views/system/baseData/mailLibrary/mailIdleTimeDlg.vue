<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="$t('pages.mailLibrary')"
    :remark="$t('pages.mailLibrary_text7')"
    :visible.sync="dlgVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div class="toolbar">
      <el-row>
        <el-col :span="2" style="font-weight: 700;padding-top: 7px;">{{ $t('table.sourceGroup') }}</el-col>
        <el-col :span="6">
          <tree-select ref="groupTree" v-model="query.groupId" :data="treeData" :width="200" @change="treeNodeClick" />
        </el-col>
        <el-col :span="16">
          <div style="float: right;">
            <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.mailLibrary_text1')" style="width: 200px;"/>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
            <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
              {{ $t('table.delete') }}
            </el-button>
            <el-button icon="el-icon-finished" size="mini" :disabled="!deleteable" @click="handleRetain">
              {{ $t('button.retain') }}
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <grid-table
      ref="appInfoList"
      :height="420"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :default-sort="{ prop: 'name' }"
      @selectionChangeEnd="handleSelectionChange"
    />
    <div slot="footer" class="dialog-footer">
      <el-popover placement="top" width="350" trigger="click" @show="getSetting">
        <Form ref="idleTimeForm" label-position="right" :model="temp">
          <FormItem prop="idleTime" label-width="0">
            {{ $t('pages.idleMailMessage1') }}<el-input-number v-model="temp.idleTime" style="width: 20%" :controls="false" :min="1" :max="999999" :step="1" step-strictly size="mini"/>
            {{ $t('pages.idleMailMessage2') }}
          </FormItem>
        </Form>
        <div style="text-align: right; margin-top: 10px">
          <el-button type="primary" size="mini" @click="saveSetting">{{ $t('button.save') }}</el-button>
        </div>
        <el-button slot="reference" type="primary" class="search-icon-left" size="mini">闲置设置</el-button>
      </el-popover>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getGroupTreeNode, getIdleTimePage, getIdleTimeSetting, saveIdleTimeSetting, flushMailUsedTime } from '@/api/system/baseData/mailLibrary'
import { findNode } from '@/utils/tree'

export default {
  name: 'MailIdleTimeDlg',
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'emailName', width: '30', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '30', formatter: this.groupFormatter },
        { prop: 'lastUsedTime', label: 'lastUsedTime', width: '40' },
        { prop: 'idleTime', label: 'idleTime', width: '30' },
        { prop: 'address', label: 'email', width: '30' },
        { prop: 'unit', label: 'unit', width: '30' },
        { prop: 'office', label: 'office', width: '30' },
        { prop: 'remark', label: 'remark', width: '30' }
      ],
      deleteable: false,
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: 0
      },
      dlgVisible: false,
      submitting: false,
      selectTreeId: null,
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.mailLibrary'), parentId: '', children: [] }],
      temp: {
        idleTime: 365
      }
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.selectTreeId = null
      this.query.groupId = 0
      this.query.searchInfo = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNode()
      })
    },
    handleSelectionChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    treeNodeClick(data, node) {
      this.query.groupId = node.dataId
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      const newOption = Object.assign({}, this.query, option)
      return getIdleTimePage(newOption)
    },
    loadGroupTree: function() {
      getGroupTreeNode().then(respond => {
        this.treeData[0].children = respond.data
        this.$nextTick(() => {

        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
    },
    groupFormatter(row, data) {
      const nodeData = findNode(this.treeData, data, 'dataId')
      return nodeData ? nodeData.label : ''
    },
    handleDelete() {
      this.$parent.updateGroupForm = false
      this.$parent.idleMailDelete = true
      const selectedData = this.softTable.getSelectedDatas()
      const total = this.softTable.getTotal()
      this.$parent.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleRetain() {
      this.$confirmBox(this.$t('pages.confirmRetainMail'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.softTable.getSelectedIds()
        flushMailUsedTime({ ids: toDeleteIds.join(',') }).then(respond => {
          this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.retainSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    getSetting() {
      getIdleTimeSetting().then((res) => {
        this.temp.idleTime = res.data.idleTime
      })
    },
    saveSetting() {
      this.submitting = true
      this.$refs.idleTimeForm.validate((valid) => {
        if (valid) {
          if (this.temp.idleTime === undefined) {
            this.temp.idleTime = 1
          }
          saveIdleTimeSetting(this.temp).then(() => {
            this.submitting = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.handleFilter()
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>
