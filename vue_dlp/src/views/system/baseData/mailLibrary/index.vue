<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="receiverGroupTree"
        resizeable
        :default-expand-all="true"
        :data="receiverGroupTreeData"
        :render-content="renderContent"
        @node-click="receiverGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'232'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'240'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <el-button v-permission="'482'" size="mini" @click="handleIdleTimeDlp">
          {{ $t('pages.idleTimeMailLib') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.mailLibrary_text1')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="receiverList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('form.emailName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.mailLibrary_text3')" prop="address">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.mailLibrary_address_description">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <el-input v-model="temp.address" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="temp.groupId" :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('form.unit')" prop="unit">
          <el-input v-model="temp.unit" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('form.office')" prop="office">
          <el-input v-model="temp.office" :maxlength="60"/>
        </FormItem>
        <!-- <FormItem label="状态" prop="remark">
          <el-select v-model="temp.state">
            <el-option v-for="item in stateOptions" :key="item.id" :value="item.id" :label="item.name">{{ item.name }}</el-option>
          </el-select>
        </FormItem> -->
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteMailLibGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <export-dlg ref="exportDlg" :group-tree-data="receiverGroupTreeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      :tip="$t('pages.mailLibrary_text3')"
      template="mail"
      :show-import-type="false"
      :show-import-way="true"
      :file-name="title"
      @success="importEndFunc"
    />
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.mailGroup')"
      :group-tree-data="formTreeData"
      :add-func="createMailLibGroup"
      :update-func="updateMailLibGroup"
      :delete-func="deleteMailLibGroup"
      :move-func="moveGroup"
      :edit-valid-func="getMailLibGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? i18nConcatText(this.$t('pages.mailGroup'), 'update') : i18nConcatText(this.$t('pages.mailBox'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApiForDelete"
      :delete-filter-name="$t('table.email')"
      :edit-type="updateGroupForm ? 0 : 1"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="receiverGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>
    <mail-idle-time-dlg ref="mailIdleTimeDlg"/>
  </div>
</template>

<script>
import {
  getGroupTreeNode, createMailLibGroup, updateMailLibGroup, deleteMailLibGroup, deleteGroupAndData,
  moveGroupToOther, getMailLibGroupByName, countChildByGroupId, getMailLibPage, createMailLib,
  updateMailLib, deleteMailLib, getMailLibByAddress, exportExcel, moveGroup,
  batchUpdateGroup, batchUpdateAllGroup, getIdleTimePage
} from '@/api/system/baseData/mailLibrary'
import { findNodeLabel } from '@/utils/tree'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import MailIdleTimeDlg from './mailIdleTimeDlg'

export default {
  name: 'MailLibrary',
  components: { BatchEditPageDlg, DeleteGroupDlg, EditGroupDlg, ImportDlg, ExportDlg, MailIdleTimeDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'emailName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'address', label: 'email', width: '150', sort: 'custom', formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: 'custom' },
        { prop: 'office', label: 'office', width: '150', sort: 'custom' },
        // { prop: 'state', label: 'status', width: '150', formatter: this.stateFormatter },
        { prop: 'remark', label: 'remark', width: '200', sort: 'custom' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      stateOptions: [{ 'id': 0, 'name': this.$t('text.normal') }, { 'id': 1, 'name': this.$t('text.disable') }],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        address: '',
        office: '',
        unit: '',
        state: 0,
        groupId: undefined,
        type: 0
      },
      formTreeData: [],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mailBox'), 'update'),
        create: this.i18nConcatText(this.$t('pages.mailBox'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.mailGroup'), 'delete')
      },
      treeNodeType: 'G',
      receiverGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.mailLibrary'), parentId: '', children: [] }],
      treeSelectNode: [],
      title: this.$t('pages.mailBox'),
      checkedKeys: [],
      updateGroupForm: false,
      idleMailDelete: false, // 闲置邮箱删除
      updateGroupId: undefined, // 批量修改的分组id
      dlgName: null
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      const dlgName = to.query.dlgName
      if (dlgName) {
        vm.handleIdleTimeDlp()
      }
      vm.$router.push({ query: {}})
    })
  },
  computed: {
    gridTable() {
      return this.$refs['receiverList']
    },
    receiverGroupTree: function() {
      return this.$refs['receiverGroupTree']
    }
  },
  watch: {
    '$store.state.commonData.notice.mailIdleTime'(val) {
      this.handleIdleTimeDlp()
    }
  },
  activated() {
    this.loadReceiverTree()
    this.gridTable && this.gridTable.execRowDataApi(this.query)
  },
  created() {
    this.resetTemp()
    this.loadReceiverTree()
  },
  methods: {
    createMailLibGroup,
    updateMailLibGroup,
    deleteMailLibGroup,
    getMailLibGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getMailLibPage(searchQuery)
    },
    rowDataApiForDelete: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (this.idleMailDelete) {
        return getIdleTimePage(searchQuery)
      } else {
        return getMailLibPage(searchQuery)
      }
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    receiverGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    loadReceiverTree: function() {
      return getGroupTreeNode().then(respond => {
        this.receiverGroupTreeData[0].children = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.receiverGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.receiverGroupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.receiverGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.receiverGroupTree.findNode(this.receiverGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.receiverGroupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    loadTypeTreeExceptRoot() {
      // if (this.query.groupId) return this.receiverGroupTreeData[0].children.filter(item => item.dataId != this.query.groupId)
      return this.receiverGroupTreeData[0].children
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleMoving(idList) {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      if (row.address.indexOf('$') === 0) {
        row.address = row.address.replace('$', '#').concat('#')
      }
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleIdleTimeDlp() {
      this.$refs.mailIdleTimeDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    importEndFunc(groupId) {
      this.loadReceiverTree().then(() => {
        this.$nextTick(() => {
          groupId = groupId ? String(groupId) : '0'
          const node = this.dataToTreeNode({ id: groupId })
          this.receiverGroupTree.selectCurrentNode(node.id)
        })
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createMailLib(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateMailLib(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      this.idleMailDelete = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    addressFormatter(row, data) {
      const index = data.indexOf('$')
      if (index === 0) {
        return data.replace('$', '#').concat('#')
      }
      return data
    },
    addressValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const len = value.split('#')
      if (!(reg.test(value) || reg1.test(value) || len.length === 2)) {
        callback(new Error(this.$t('pages.validateMsg_email2')))
      }
      getMailLibByAddress({ address: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('pages.validateMsg_email3')))
        } else {
          callback()
        }
      })
    },
    stateFormatter(row, data) {
      for (let i = 0, size = this.stateOptions.length; i < size; i++) {
        if (this.stateOptions[i].id === data) {
          return this.stateOptions[i].name
        }
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.receiverGroupTreeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.receiverGroupTree.findNode(this.receiverGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.receiverGroupTree.setCurrentKey(nodeData.id)
        this.receiverGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      const paramTemp = Object.assign({}, params);
      if (this.idleMailDelete) {
        paramTemp.deletedIdleMail = true
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteMailLib(paramTemp).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (this.idleMailDelete) {
            this.$refs.mailIdleTimeDlg.handleFilter()
          }
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    }
  }
}
</script>
