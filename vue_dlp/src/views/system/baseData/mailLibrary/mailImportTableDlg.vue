<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[emailDialogStatus]"
      :visible.sync="emailDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="emailForm"
        :rules="emailRules"
        :model="emailTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('form.emailName')" prop="name">
          <el-input v-model="emailTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.mailLibrary_text3')" prop="address" style="white-space:nowrap; ">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.mailLibrary_address_description">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <el-input v-model="emailTemp.address" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="24">
              <el-select v-model="emailTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('form.unit')" prop="unit">
          <el-input v-model="emailTemp.unit" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('form.office')" prop="office">
          <el-input v-model="emailTemp.office" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="emailTemp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="emailDialogStatus === 'emailCreate' ? createEmail() : updateEmail()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="emailDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-table-dlg
      ref="emailImportTable"
      :elg-title="$t('pages.importMailLibrary')"
      :group-root-name="$t('pages.mailLibrary')"
      :search-info-name="$t('pages.emailCopy_nameOrAddress')"
      :confirm-button-name="$t('pages.addMail')"
      :prompt-message="$t('pages.mailImportText1')"
      :group-title="$t('pages.mailGroup')"
      :col-model="mailImportColModel"
      :list="getMailInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createMailLibGroup"
      :update-group="updateMailLibGroup"
      :delete-group="deleteMailLibGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getMailLibGroupByName"
      :delete="deleteMailLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteMailLibGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleEmailCreate"
      :get-list-by-group-ids="getMailLibraryByGroupIds"
      @submitEnd="getMailNeedAddIds"
      @submitDeleteEnd="submitMailDeleteEnd"
      @elgCancelAfter="elgMailCancelAfter"
      @changeGroupAfter="changeMailGroupAfter"
    />
  </div>
</template>
<script> 
import {
  countChildByGroupId,
  createMailLib,
  createMailLibGroup,
  deleteGroupAndData,
  deleteMailLib,
  deleteMailLibGroup,
  getGroupTreeNode,
  getMailLibByAddress,
  getMailLibGroupByName,
  getMailLibPage,
  getMailLibraryByGroupIds,
  moveGroupToOther,
  updateMailLib,
  updateMailLibGroup
} from '@/api/system/baseData/mailLibrary'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import { findNodeLabel } from '@/utils/tree';
export default {
  name: 'MailImportTableDlg',
  components: { ImportTableDlg },
  props: {
    /**
     * 组件在哪个模块引用
     */
    moduleName: {
      type: String,
      default: function() {
        return 'fileOutgoingCheckStatistics'
      }
    }
  },
  data() {
    return { 
      mailImportColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailUpdate }
          ]
        }
      ],
      dlgSubmitting: false,
      emailRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'change' }
        ]
      },
      textMap: {
        emailCreate: this.i18nConcatText(this.$t('pages.mailInfo'), 'create'),
        emailUpdate: this.i18nConcatText(this.$t('pages.mailInfo'), 'update')
      },
      mailQuery: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      emailTemp: {},
      emailDefaultTemp: { // 表单字段
        id: undefined,
        name: '',
        address: '',
        office: '',
        unit: '',
        state: 0,
        groupId: undefined,
        type: 0
      },
      emailDialogFormVisible: false,
      emailDialogStatus: '',
      mailGroupSearchId: undefined,
      treeSelectNode: []
    }
  },
  created() { 
    this.loadGroupTree()
  },
  activated() {
    this.loadGroupTree()
  },
  methods: { 
    deleteMailLibGroup,
    createMailLibGroup,
    updateMailLibGroup,
    getGroupTreeNode,
    countChildByGroupId,
    getMailLibGroupByName,
    deleteMailLib,
    deleteGroupAndData,
    moveGroupToOther,
    getMailLibraryByGroupIds,
    handleDrag() {

    },
    createEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          this.emailTemp.groupName = findNodeLabel(this.treeSelectNode, this.emailTemp.groupId, 'dataId')
          createMailLib(this.emailTemp).then(respond => {
            this.dlgSubmitting = false
            this.emailDialogFormVisible = false
            this.$refs['emailImportTable'].refreshTableData()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    updateEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.emailTemp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.emailTemp.groupId, 'dataId')
          updateMailLib(tempData).then(respond => {
            this.dlgSubmitting = false
            this.emailDialogFormVisible = false
            this.$refs['emailImportTable'].refreshTableData()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    loadGroupTree: function() {
      return getGroupTreeNode().then(res => {
        this.treeSelectNode = res.data
      })
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleEmailUpdate(row) {
      this.emailDialogStatus = 'emailUpdate'
      this.emailTemp = Object.assign({}, row)
      this.emailTemp.groupId = this.emailTemp.groupId ? this.emailTemp.groupId + '' : '';
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    getMailNeedAddIds(needIds) {
      // 这里主要是为了文件外发量统计配置邮件外发例外的时候使用，这里只需要邮箱地址，因此如果该组件需要在其它地方引用自行修改逻辑
      if (this.moduleName === 'fileOutgoingCheckStatistics') {
        if (needIds.length === 0) {
          this.$emit('getMailAddress', [])
        } else {
          getMailLibPage({ ids: needIds.join(',') }).then(res => {
            const addressList = []
            res.data.items.forEach(item => {
              addressList.push(item.address)
            })
            this.$emit('getMailAddress', addressList)
          })
        }
      }
    },
    submitMailDeleteEnd() {
      this.$refs['emailImportTable'].refreshTableData()
    },
    elgMailCancelAfter() {
      this.loadGroupTree();
    },
    changeMailGroupAfter() {
      this.loadGroupTree()
    },
    handleEmailCreate(selectedGroupId) {
      this.emailDialogStatus = 'emailCreate'
      this.emailTemp = Object.assign({}, this.emailDefaultTemp)
      // 如果传参没有selectedGroupId，则获取左侧邮箱信息库选择的分组id
      this.emailTemp.groupId = selectedGroupId ? selectedGroupId + '' : this.mailGroupSearchId
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    addressValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const len = value.split('#')
      if (!(reg.test(value) || reg1.test(value) || len.length === 2)) {
        callback(new Error(this.$t('pages.validateMsg_email2')))
      }
      getMailLibByAddress({ address: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.emailTemp.id) {
          callback(new Error(this.$t('pages.validateMsg_email3')))
        } else {
          callback()
        }
      })
    },
    getMailInfoList(option) {
      const searchQuery = Object.assign({}, this.mailQuery, option)
      this.mailGroupSearchId = searchQuery.groupId;
      return getMailLibPage(searchQuery)
    },
    show() {
      this.$refs.emailImportTable.show()
    }
  }
}
</script>
