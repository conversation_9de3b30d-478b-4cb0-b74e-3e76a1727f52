<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :default-expand-all="true"
        resizeable
        :render-content="renderContent"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" :disabled="!addBtnAble" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'251'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'252'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <el-button icon="el-icon-edit" size="mini" @click="handleEditBrowser">
          {{ $t('pages.editBrowserProcess') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder=" $t('pages.serverNameOrDomain')" style="width: 218px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="100px">
              <FormItem :label="$t('pages.serverName')">
                <el-input v-model="query.name" v-trim clearable :maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.whiteListMode')" prop="ssl">
                <el-select v-model="query.useNewVersion" clearable style="width: 100%">
                  <el-option :label="$t('pages.serverLibrayWhiteListMode1')" :value="1"></el-option>
                  <el-option :label="$t('pages.serverLibrayWhiteListMode2')" :value="2"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.ipType')" prop="ipType">
                <el-select v-model="query.ipType" clearable style="width: 100%;">
                  <el-option v-for="(item, key) in ipTypeOptionLists" :key="key" :label="item.label" :value="parseInt(item.value)"></el-option>
                </el-select>
              </FormItem>

              <FormItem :label="$t('table.domain')" prop="internetIp">
                <el-input v-model="query.domain" v-trim clearable maxlength="64" />
              </FormItem>
              <FormItem :label="$t('table.port')" prop="internetPort">
                <el-input v-model.number="query.port" clearable maxlength="5" :placeholder="$t('pages.serverLibrary_text6')" @input="number('port')"/>
              </FormItem>
              <FormItem :label="$t('table.browserProcess')" prop="processes">
                <el-input v-model="query.processes" clearable maxlength="128"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table ref="serverList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px" style="width: 700px;">
        <FormItem :label="$t('pages.serverName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.groupType')">
          <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in groupTreeSelectData" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem>
          <el-switch v-model="temp.useNewVersion" :active-text="$t('pages.useNewVersion')" :active-value="0" :inactive-value="1" @change="versionChange"/>
        </FormItem>
        <FormItem v-if="temp.limitNet!==1" :label="$t('pages.ipType')" prop="ipType">
          <el-select v-model="temp.ipType" style="width: 150px;" @change="ipTypeChange">
            <el-option v-for="(item, key) in ipTypeOptionLists" :key="key" :label="item.label" :value="parseInt(item.value)"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-if="temp.useNewVersion==1 || temp.useNewVersion==2" :label="$t('pages.whiteListMode')">
          <el-select v-model="temp.limitNet" style="width: 150px;" @change="limitNetTypeChange">
            <el-option v-for="(value, key) in netOptions" :key="key" :label="value" :value="parseInt(key)"></el-option>
          </el-select>
        </FormItem>
        <div v-if="temp.ipType==0 || temp.ipType == 2">
          <div v-if="temp.useNewVersion == 0">
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.beginIp')" prop="beginIp">
                  <el-input v-model="temp.beginIp" @blur="inputBlur('endIp')"></el-input>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.endIp')" prop="endIp">
                  <el-input v-model="temp.endIp" @blur="inputBlur('beginIp')"></el-input>
                </FormItem>
              </el-col>
            </el-row>
          </div>
          <div v-if="temp.useNewVersion == 1 || temp.useNewVersion == 2">
            <FormItem :tooltip-content="$t('pages.ipOrDomainConfigTip', { ipOrDomain: $t('pages.serverIp') })" :label="$t('pages.serverIp')" prop="ip">
              <el-input v-model="temp.ip" v-trim></el-input>
            </FormItem>
          </div>
        </div>
        <div v-if="temp.ipType==1">
          <FormItem :tooltip-content="$t('pages.ipOrDomainConfigTip', { ipOrDomain: temp.limitNet==1 ? $t('pages.serverAddress') : $t('pages.domainName') })" :label="temp.limitNet==1 ? $t('pages.serverAddress') : $t('pages.domainName')" prop="domainName">
            <el-input v-model="temp.domainName" v-trim :maxlength="64"></el-input>
          </FormItem>
        </div>
        <div v-if="temp.useNewVersion == 0">
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.beginPort')" prop="beginPort">
                <el-input v-model="temp.beginPort" maxlength="5" @blur="inputBlur('endPort')" @input="handleBeginInput"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.endPort')" prop="endPort">
                <el-input v-model="temp.endPort" maxlength="5" @blur="inputBlur('beginPort')" @input="handleEndInput"></el-input>
              </FormItem>
            </el-col>
          </el-row>
        </div>
        <div v-if="temp.useNewVersion == 1 || temp.useNewVersion == 2">
          <FormItem :label="$t('pages.portMode')">
            <el-radio-group v-model="temp.portMode" @change="portModeChange">
              <el-radio v-for="(value, key) in portModeOptions" :key="key" :label="parseInt(key)">{{ value }}</el-radio>
            </el-radio-group>
          </FormItem>
          <FormItem v-if="temp.portMode === 1" :label="$t('pages.serverPort')" prop="port">
            <el-input v-model="temp.port" maxlength="5" @input="handleInput"></el-input>
          </FormItem>
          <el-row v-if="temp.portMode === 2">
            <el-col :span="12">
              <FormItem :label="$t('pages.beginPort')" prop="beginPort">
                <el-input v-model="temp.beginPort" maxlength="5" @blur="inputBlur('endPort')" @input="handleBeginInput"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.endPort')" prop="endPort">
                <el-input v-model="temp.endPort" maxlength="5" @blur="inputBlur('beginPort')" @input="handleEndInput"></el-input>
              </FormItem>
            </el-col>
          </el-row>
        </div>
        <FormItem :label="$t('pages.serverProcess_Msg10')" prop="encDecType">
          <el-radio-group v-model="temp.encDecType" @change="encAndDecChange">
            <el-radio :label="1">{{ $t('pages.serverProcess_Msg20') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.serverProcess_Msg19') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <div>
          <FormItem :label="$t('pages.uploadType')" prop="uploadType">
            <el-select v-model="temp.uploadType" style="width: 150px;" :disabled="temp.encDecType === 1">
              <el-option
                v-for="(value, key) in loadTypeOptions"
                v-show="key!=2"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              />
            </el-select>
          </FormItem>
          <el-row>
            <el-col :span="20">
              <FormItem :label="$t('pages.uploadFileExt')" prop="uploadFileExt">
                <el-input v-model="temp.uploadFileExt" v-trim :disabled="temp.allOfUpload || temp.encDecType === 1" maxlength="400"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="3" style="margin: 5px 0 0 5px;">
              <el-checkbox v-model="temp.allOfUpload" :disabled="temp.allOfUploadAble" @change="uploadFileExtChange">{{ $t('pages.allType') }}</el-checkbox>
            </el-col>
          </el-row>
          <FormItem :label="$t('pages.downloadType')" prop="downloadType">
            <el-select v-model="temp.downloadType" style="width: 150px;" :disabled="temp.encDecType === 1">
              <el-option
                v-for="(value, key) in loadTypeOptions"
                v-show="key!=1"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              />
            </el-select>
          </FormItem>
          <el-row>
            <el-col :span="20">
              <FormItem :label="$t('pages.downloadFileExt')" prop="downloadFileExt">
                <el-input v-model="temp.downloadFileExt" v-trim :disabled="temp.allOfDownload || temp.encDecType === 1" maxlength="400"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="3" style="margin: 5px 0 0 5px;">
              <el-checkbox v-model="temp.allOfDownload" :disabled="temp.allOfDownloadAble" @change="downloadFileExtChange">{{ $t('pages.allType') }}</el-checkbox>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="20">
            <FormItem :label="$t('pages.processName')" prop="processName">
              <el-input v-if="temp.useNewVersion==2" v-model="temp.processName" v-trim type="textarea" maxlength="450" rows="3" ></el-input>
              <el-input v-if="temp.useNewVersion==1" v-model="temp.processName" v-trim type="textarea" maxlength="450" rows="3" ></el-input>
              <el-input v-if="temp.useNewVersion==0" v-model="temp.processName" v-trim disabled type="textarea" maxlength="450" rows="3" ></el-input>
            </FormItem>
          </el-col>
          <el-col v-if="temp.useNewVersion==1 || temp.useNewVersion==2" :span="3" style="margin: 5px 0 0 5px;">
            <el-button icon="el-icon-add" size="small" @click="dialogBrowserVisible=true">{{ $t('button.choose') }}</el-button>
          </el-col>
        </el-row>
        <el-row>
          <span style="color:red;margin-left: 110px;display: inline-block; width: 590px">{{ tipMsg }}</span>
        </el-row>
        <el-row v-if="(temp.useNewVersion == 1 || temp.useNewVersion == 2) && temp.limitNet == 0">
          <el-col :span="9">
            <FormItem :label="$t('pages.upLoadLimitSpeed')" prop="upLoadLimitSpeed">
              <el-input-number v-model="temp.upLoadLimitSpeed" :min="-1" :max="1000000000" :controls="false"/>
            </FormItem>
          </el-col>
          <el-col :span="3" style="padding-top: 8px; padding-left: 5px;">
            <span>(B/S)</span>
            <el-tooltip class="item" effect="dark" placement="bottom-start">
              <div slot="content">{{ $t('pages.serverLibrary_text1') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-col>
        </el-row>
        <!--<FormItem prop="waitAccessLogin">
          <el-checkbox v-model="temp.waitAccessLogin" :false-label="0" :true-label="1" >验证服务器</el-checkbox>
          <el-tooltip class="item" effect="dark" placement="right">
            <div slot="content">验证服务器需要结合应用安全接入系统才可实现</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textTitle"
      :visible.sync="dialogBrowserVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <grid-table ref="browserList" :height="200" :col-model="browserColModel" :show-pager="false" :row-datas="browserDatas" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addBrowser()">
          {{ $t('button.insert') }}
        </el-button>
        <el-button @click="dialogBrowserVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <export-dlg ref="exportDlg" :group-tree-data="treeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>
    <import-dlg ref="importDlg" :title="title" template="server" :show-import-type="false" :file-name="title" :tip="tip" :show-import-way="true" :upload-func="upload" @success="importEndFunc"/>
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.serverGroup')"
      :group-tree-data="formTreeData"
      :add-func="createWhiteListServerGroup"
      :update-func="updateWhiteListServerGroup"
      :delete-func="deleteWhiteListServerGroup"
      :move-func="moveGroup"
      :edit-valid-func="getGroupByName"
      @addEnd="createGroupData"
      @updateEnd="updateGroupData"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteWhiteListServerGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="batchEditDlgName"
      :height="326"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :delete-filter-name="$t('pages.service')"
      :edit-type="batchEditType"
      @submitEnd="batchEditFunc"
      @close="() => hasShowEditBrowser = false"
    >
      <FormItem v-if="updateGroupForm && !hasShowEditBrowser" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="treeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
      <template slot="form-bottom">
        <div v-if="hasShowEditBrowser">
          <FormItem :label="$t('table.operate')" label-width="52px">
            <el-radio-group v-model="editBrowserOpt">
              <el-radio :label="1">{{ this.$t('pages.editBrowserProcessWithCreate') }}</el-radio>
              <el-radio :label="0">{{ this.$t('pages.editBrowserProcessWithDelete') }}</el-radio>
            </el-radio-group>
          </FormItem>
          <div style="display: flex">
            <div style="flex: 1">
              <el-input v-model="editBrowserProcesses" v-trim type="textarea" maxlength="450" rows="2"></el-input>
            </div>
            <div style="margin-left: 5px">
              <el-button icon="el-icon-add" size="small" @click="dialogBrowserVisible=true">{{ $t('button.choose') }}</el-button>
            </div>
          </div>
          <div v-if="editBrowserProcesses.includes('*.*')" style="color: #F56C6C; font-size: 12px; line-height: 1; margin-top: 2px">{{ $t('pages.serverLibrary_text8') }}</div>
        </div>
      </template>
    </batch-edit-page-dlg>
  </div>
</template>

<script>
import {
  getWhiteListServerPage, getWhiteListServerByName, createWhiteListServer, updateWhiteListServer, deleteWhiteListServer,
  getSysBrowserPage, createWhiteListServerGroup, updateWhiteListServerGroup, deleteWhiteListServerGroup,
  listGroupTree, countChildByGroupId, getGroupByName, moveGroup, exportExcel,
  deleteGroupAndData, moveGroupToOther, batchUpdateGroup, batchUpdateAllGroup, editBrowserProcess
} from '@/api/system/baseData/serverLibrary'
import { findNodeLabel } from '@/utils/tree'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import EditGroupDlg from '@/views/common/editGroupDlg'
import request from '@/utils/request'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import { isIPv4, isIPv6 } from '@/utils/validate'
import { getPropertyByCode } from '@/api/property'

export default {
  name: 'ServerLibrary',
  components: { BatchEditPageDlg, DeleteGroupDlg, ExportDlg, ImportDlg, EditGroupDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'serverName', width: '150', sort: 'custom', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.serverGroupNameFormatter },
        { prop: 'useNewVersion', label: 'useNewVersion', width: '150', sort: 'custom', formatter: this.useNewVersionFormatter },
        { label: 'whiteListMode', width: '150', formatter: this.modelFormatter },
        { prop: 'ipType', label: 'ipType', width: '150', sort: 'custom', formatter: this.ipTypeFormatter },
        { prop: 'beginIp', label: 'domain', width: '150', sort: 'custom', formatter: this.domainFormatter },
        { prop: 'beginPort', label: 'port', width: '150', sort: 'custom', formatter: this.portFormatter },
        { prop: 'uploadType', label: 'uploadType', width: '150', sort: 'custom', formatter: this.loadTypeFormatter },
        { prop: 'uploadFileExt', label: 'uploadFileExt', width: '150', sort: 'custom' },
        { prop: 'downloadType', label: 'downloadType', width: '150', sort: 'custom', formatter: this.loadTypeFormatter },
        { prop: 'downloadFileExt', label: 'downloadFileExt', width: '150', sort: 'custom' },
        { prop: 'processName', label: 'processName', width: '150' },
        { prop: 'upLoadLimitSpeed', label: 'upLoadLimitSpeed', width: '150', sort: 'custom' },
        /* { prop: 'waitAccessLogin', label: 'waitAccessLogin', width: '150', formatter: this.waitAccessLoginFormatter }, */
        { label: 'operate', type: 'button', width: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      browserColModel: [
        { prop: 'name', label: 'browserName', width: '150', sort: true },
        { prop: 'processName', label: 'browserProcess', width: '150', sort: true }
      ],
      query: { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: '',
        name: '',
        useNewVersion: undefined,
        ipType: undefined,
        domain: '',
        port: undefined,
        processes: undefined
      },
      deleteable: false,
      showTree: true,
      temp: {},
      tip: this.$t('pages.serverName'),
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        groupId: undefined,
        ipType: 1, // 0:ipv4地址 1:域名 2: ipv6地址
        beginIp: '',
        endIp: '',
        ip: '',
        domainName: '',
        beginPort: 80,
        endPort: 80,
        portMode: 1,
        port: 80,
        uploadType: 0,
        uploadFileExt: '*.*',
        downloadType: 0,
        downloadFileExt: '*.*',
        processName: '',
        waitAccessLogin: 0,
        upLoadLimitSpeed: 0,
        bufferTime: 2000,
        useNewVersion: 1, // 0:旧服务器白名单 1:新服务器白名单-限制上网模式 2:新服务器白名单-允许上网模式
        encDecType: 1, // 0 - 自定义加解密策略， 1 - 默认加解密策略
        limitNet: 0, // 0: '限制上网模式' 1: '允许上网模式'
        allOfDownload: true,
        allOfUpload: true,
        allOfUploadAble: true,
        allOfDownloadAble: true
      },
      oldProcessName: '',
      oldIpType: 1,
      editBrowserOpt: 1,
      hasShowEditBrowser: false,
      editBrowserProcesses: '',
      useNewVersionOptions: { 0: this.$t('text.yes'), 1: this.$t('text.no'), 2: this.$t('text.no') },
      ipTypeOptions: { 0: this.$t('table.IPv4Addr'), 1: this.$t('pages.domain'), 2: this.$t('table.IPv6Addr') },
      ipTypeOptionLists: [{ label: this.$t('pages.domain'), value: 1 }, { label: this.$t('table.IPv4Addr'), value: 0 }, { label: this.$t('table.IPv6Addr'), value: 2 }],

      netOptions: { 0: this.$t('pages.serverLibrayWhiteListMode1'), 1: this.$t('pages.serverLibrayWhiteListMode2') },
      portModeOptions: { 1: this.$t('pages.portModeOptions1'), 2: this.$t('pages.portModeOptions2'), 3: this.$t('pages.portModeOptions3') },
      loadTypeOptions: { 0: this.$t('pages.null'), 1: this.$t('pages.loadTypeOptions2'), 2: this.$t('pages.loadTypeOptions3') },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      addBtnAble: false,
      formTreeData: [],
      browserDatas: [],
      allowInternetBrowserDatas: [],
      allBrowserList: [],
      textMap: {
        update: this.i18nConcatText(this.$t('pages.serverInformation'), 'update'),
        create: this.i18nConcatText(this.$t('pages.serverInformation'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.serverInformationGroup'), 'delete')
      },
      treeData: [
        {
          id: 'G0',
          label: this.$t('pages.serverLibrary'),
          dataId: '0',
          children: [{ label: this.$t('pages.ungrouped'), dataId: '-1', id: 'G-1' }]
        }
      ],
      defaultExpandedKeys: ['0'],
      groupTreeSelectData: [{ label: this.$t('pages.ungrouped'), dataId: '-1', id: 'G-1' }],
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        beginIp: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        endIp: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        ip: [
          { required: true, message: this.$t('pages.validateMsg_Ip'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        domainName: [{ required: true, message: this.$t('pages.validateMsg_domainName'), trigger: 'blur' }],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        port: [
          { required: true, message: this.$t('pages.validateMsg_port'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        uploadFileExt: [{ required: true, message: this.$t('pages.validateMsg_uploadFileExt'), trigger: 'blur' }],
        downloadFileExt: [{ required: true, message: this.$t('pages.validateMsg_downloadFileExt'), trigger: 'blur' }],
        processName: [
          { required: true, message: this.$t('pages.validateMsg_processName'), trigger: ['blur', 'change'] },
          { validator: this.processNameValidator, trigger: ['blur', 'change'] }
        ],
        upLoadLimitSpeed: [{ required: true, message: this.$t('pages.validateMsg_upLoadLimitSpeed'), trigger: 'blur' }]
      },
      editable: true,
      dialogBrowserVisible: false,
      textTitle: this.$t('pages.serverLibrary_text2'),
      treeNodeType: '',
      title: this.$t('pages.serverInformation'),
      tipMsg: '',
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      propertyStg: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverList']
    },
    groupTree() {
      return this.$refs['groupTree']
    },
    batchEditDlgName() {
      if (this.hasShowEditBrowser) {
        return this.$t('pages.editBrowserProcess')
      }
      return this.updateGroupForm ? this.i18nConcatText(this.$t('pages.serverSubGroup'), 'update') : this.i18nConcatText(this.$t('pages.service'), 'delete')
    },
    batchEditType() {
      return this.hasShowEditBrowser || this.updateGroupForm ? 0 : 1
    }
  },
  watch: {
    'temp.limitNet'(newVal, oldVal) {
      this.browserDatas = newVal == 1 ? [...this.allowInternetBrowserDatas] : [...this.allBrowserList]
      const tipList = []
      if (newVal == 1) {
        const processName = this.browserDatas.map(item => item.processName)
        this.temp.processName.split('|').forEach(item => {
          if (!processName.includes(item) && item != '360chromeie.exe' && item != '') {
            tipList.push(item)
          }
        })
        if (tipList.length > 0) {
          this.tipMsg = this.$t('pages.serverLibrayWhiteListMsg', { process: tipList.join('|') })
        } else {
          this.tipMsg = ''
        }
      } else {
        this.tipMsg = ''
        // this.browserDatas = this.browserDatas.filter(item => item.processName != 'TSBrowser.exe')
        // const processName = this.browserDatas.map(item => item.processName)
        // this.temp.processName.split('|').forEach(item => {
        //   if (!processName.includes(item) && item != '') {
        //     tipList.push(item)
        //   }
        // })
        // if (tipList.length > 0) {
        //   this.tipMsg = this.$t('pages.serverLibrayWhiteListMsg', { process: tipList.join('|') })
        // } else {
        //   this.tipMsg = ''
        // }
      }
    },
    'temp.processName'(newVal, oldVal) {
      this.oldProcessName = oldVal
      if (this.temp.limitNet == 1) {
        const tipList = []
        const processName = this.browserDatas.map(item => item.processName)
        newVal.split('|').forEach(item => {
          if (!processName.includes(item) && item != '360chromeie.exe' && item != '') {
            tipList.push(item)
          }
        })
        if (tipList.length > 0) {
          this.tipMsg = this.$t('pages.serverLibrayWhiteListMsg', { process: tipList.join('|') })
        } else {
          this.tipMsg = ''
        }
      } else {
        this.tipMsg = ''
      }
    }
  },
  activated() {
    this.getGroupTreeNode()
  },
  created() {
    this.resetTemp()
    this.getGroupTreeNode()
    this.getSysBrowserPage()
  },
  methods: {
    createWhiteListServerGroup,
    updateWhiteListServerGroup,
    deleteWhiteListServerGroup,
    getGroupByName,
    moveGroup,
    deleteGroupAndData,
    moveGroupToOther,
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    versionChange(val) {
      this.temp.limitNet = 0
      if (val) {
        this.temp.processName = this.oldProcessName
      } else {
        this.temp.processName = '*.*'
        this.temp.upLoadLimitSpeed = 0
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    ipTypeChange(val) {
      this.$refs['dataForm'].clearValidate()
      this.temp.ip = ''
      this.temp.beginIp = ''
      this.temp.endIp = ''
      this.temp.domainName = ''
    },
    limitNetTypeChange(val) {
      this.$refs['dataForm'].clearValidate()
      if (val == 0) {
        this.temp.ipType = this.oldIpType
      } else if (val == 1) {
        this.temp.ipType = 1
      }
    },
    portModeChange(val) {
      this.$refs['dataForm'].clearValidate()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getWhiteListServerPage(searchQuery)
    },
    browserTable() {
      return this.$refs['browserList']
    },
    getSysBrowserPage() {
      const searchQuery = Object.assign({}, { page: 1 })
      getSysBrowserPage(searchQuery).then(res => {
        this.allBrowserList = [...res.data.items]
        this.browserDatas = [...this.allBrowserList]
        this.allowInternetBrowserDatas = this.browserDatas.filter(item => !!item.processName).filter(item =>
          item.processName == 'iexplore.exe' || item.processName == 'chrome.exe' || item.processName == 'QQBrowser.exe' ||
          item.processName == 'firefox.exe' || item.processName == 'TSBrowser.exe' || item.processName.startsWith('360') ||
          item.processName == 'msedge.exe' || item == '360chromeie.exe'
        )
      })
    },
    browserRowDataApi: function(option) {
      const searchQuery = Object.assign({}, { page: 1 }, option)
      return getSysBrowserPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? (formData.groupId == 0 ? null : formData.groupId == -1 ? 0 : formData.groupId) : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.editable = true
      this.allOfUpload = true
      this.allOfDownload = true
      this.oldProcessName = ''
      this.oldIpType = 1
    },
    renderContent(h, { node, data, store }) {
      const topShow = data.dataId == '0'
      const unGroupShow = data.dataId == '-1'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <svg-icon v-show={topShow && !unGroupShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' style='margin-left:5px;' on-click={r => this.handleGroupCreate(data)} />
          <span class='el-ic'>
            <svg-icon v-show={!topShow && !unGroupShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleGroupUpdate(data)} />
            <svg-icon v-show={!topShow && !unGroupShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    getGroupTreeNode: function() {
      return listGroupTree().then(respond => {
        this.groupTreeSelectData = [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }]
        this.treeData = [
          {
            id: 'G0',
            label: this.$t('pages.serverLibrary'),
            dataId: '0',
            children: [{ label: this.$t('pages.ungrouped'), id: 'G-1', dataId: '-1' }]
          }]
        respond.data.forEach(el => {
          this.groupTreeSelectData.push(el)
          this.treeData[0].children.push(el)
          if (respond.data.length > 0) {
            this.treeNodeType = respond.data[0].type
          }
        })
      })
    },
    loadTypeTreeExceptRoot() {
      return this.treeData[0].children
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    downloadFileExtChange(val) {
      this.temp.downloadFileExt = val ? '*.*' : this.temp.downloadFileExt
      this.$refs.dataForm.clearValidate('downloadFileExt')
    },
    uploadFileExtChange(val) {
      this.temp.uploadFileExt = val ? '*.*' : this.temp.uploadFileExt
      this.$refs.dataForm.clearValidate('uploadFileExt')
    },
    addBrowser() {
      const selectedDatas = this.browserTable().getSelectedDatas()
      const processName = this.hasShowEditBrowser ? this.editBrowserProcesses : this.temp.processName
      // 字符串转成数组，并去除空值
      const processArr = processName ? processName.split('|').filter(el => el && el.trim()) : []
      const processNames = selectedDatas.map(data => data.processName)
      if ((processNames.indexOf('360ChromeX.exe') > -1 || processNames.indexOf('360chrome.exe') > -1 || processNames.indexOf('360SE.exe') > -1 ||
      processArr.indexOf('360ChromeX.exe') > -1 || processArr.indexOf('360chrome.exe') > -1 || processArr.indexOf('360SE.exe') > -1) && processArr.indexOf('360chromeie.exe') == -1) {
        processArr.push('360chromeie.exe')
      }
      // 添加选中的数据，并使用Set过滤重复数据
      if (this.hasShowEditBrowser) {
        this.editBrowserProcesses = Array.from(new Set([...processArr, ...selectedDatas.map(data => data.processName)])).join('|')
      } else {
        this.temp.processName = Array.from(new Set([...processArr, ...selectedDatas.map(data => data.processName)])).join('|')
      }
      this.browserTable().clearSelection()
      this.dialogBrowserVisible = false
    },
    changeTreeSelectNode() {
      this.groupTreeSelectData = this.treeData[0].children
      this.groupTreeSelectData.forEach(node => {
        node.dataId += ''
      })
    },
    encAndDecChange() {
      if (this.temp.encDecType === 0) {
        this.temp.uploadType = 0
        this.temp.uploadFileExt = '*.*'
        this.temp.downloadType = 0
        this.temp.downloadFileExt = '*.*'
        this.temp.allOfUpload = true
        this.temp.allOfDownload = true
        this.temp.allOfUploadAble = false
        this.temp.allOfDownloadAble = false
      } else {
        this.temp.uploadType = this.propertyStg.uploadType
        this.temp.uploadFileExt = this.propertyStg.uploadFileExt
        if (this.propertyStg.uploadFileExt === '*.*') {
          this.temp.allOfUpload = true
        } else {
          this.temp.allOfUpload = false
        }
        this.temp.downloadType = this.propertyStg.downloadType
        this.temp.downloadFileExt = this.propertyStg.downloadFileExt
        if (this.propertyStg.downloadFileExt === '*.*') {
          this.temp.allOfDownload = true
        } else {
          this.temp.allOfDownload = false
        }
        this.temp.allOfUploadAble = true
        this.temp.allOfDownloadAble = true
      }
    },
    async handleCreate() {
      const resp = await getPropertyByCode('server.encrypt-decrypt.stg')
      this.propertyStg = Object.assign({}, JSON.parse(resp.data.value))
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.temp.uploadType = this.propertyStg.uploadType
      this.temp.uploadFileExt = this.propertyStg.uploadFileExt
      if (this.propertyStg.uploadFileExt === '*.*') {
        this.temp.allOfUpload = true
      } else {
        this.temp.allOfUpload = false
      }
      this.temp.downloadType = this.propertyStg.downloadType
      this.temp.downloadFileExt = this.propertyStg.downloadFileExt
      if (this.propertyStg.downloadFileExt === '*.*') {
        this.temp.allOfDownload = true
      } else {
        this.temp.allOfDownload = false
      }
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleUpdate(row) {
      const resp = await getPropertyByCode('server.encrypt-decrypt.stg')
      this.propertyStg = Object.assign({}, JSON.parse(resp.data.value))
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = Object.assign({}, this.temp, row)
      this.oldIpType = row.ipType
      this.temp.groupId = this.temp.groupId == 0 ? '-1' : this.temp.groupId + ''
      this.temp.allOfUpload = this.temp.uploadFileExt === '*.*'
      this.temp.allOfDownload = this.temp.downloadFileExt === '*.*'
      if (this.temp.useNewVersion == 1) {
        this.temp.limitNet = 0
        if (row.beginPort === row.endPort) {
          this.temp.portMode = 1
          this.temp.port = this.temp.beginPort
        } else if (row.beginPort === 1 && row.endPort === 65535) {
          this.temp.portMode = 3
        } else {
          this.temp.portMode = 2
        }
        if (this.temp.ipType == 1) {
          this.temp.domainName = this.temp.beginIp
        } else {
          this.temp.ip = this.temp.beginIp
        }
      } else if (this.temp.useNewVersion == 2) {
        this.temp.limitNet = 1
        if (row.beginPort === row.endPort) {
          this.temp.portMode = 1
          this.temp.port = this.temp.beginPort
        } else if (row.beginPort === 1 && row.endPort === 65535) {
          this.temp.portMode = 3
        } else {
          this.temp.portMode = 2
        }
        if (this.temp.ipType == 1) {
          this.temp.domainName = this.temp.beginIp
        } else {
          this.temp.ip = this.temp.beginIp
        }
      } else {
        if (this.temp.ipType == 1) {
          this.temp.domainName = this.temp.beginIp
          this.temp.beginIp = this.temp.endIp = ''
        }
      }
      if (row.encDecType === 1) {
        this.temp.allOfUploadAble = true
        this.temp.allOfDownloadAble = true
      } else {
        this.temp.allOfUploadAble = false
        this.temp.allOfDownloadAble = false
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.editable = false
      this.$nextTick(() => {
        this.oldProcessName = row.processName
        this.$refs['dataForm'].clearValidate()
      })
    },
    async createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatterData()
          const tempData = Object.assign({}, this.temp)
          tempData.groupId = tempData.groupId == '-1' ? 0 : tempData.groupId
          tempData.groupName = findNodeLabel(this.treeSelectNode, tempData.groupId, 'dataId')
          createWhiteListServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatterData()
          const tempData = Object.assign({}, this.temp)
          tempData.groupId = tempData.groupId == '-1' ? 0 : tempData.groupId
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, tempData.groupId, 'dataId')
          updateWhiteListServer(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createGroupData(data) {
      this.groupTree.addNode(this.dataToTreeNode(data))
    },
    updateGroupData(data) {
      this.groupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.groupTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
      this.getGroupTreeNode()
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleMoving() {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleEditBrowser() {
      this.hasShowEditBrowser = true
      this.editBrowserOpt = 1
      this.editBrowserProcesses = ''
      // 变更limitNet值，以使监听后，可选浏览器进程为所有可支持的进程名
      this.temp.limitNet = null
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleNodeClick(data, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!data && data.dataId != '0'
      if (data) {
        this.query.groupId = data.dataId == '0' ? null : data.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    upload(data) {
      return request.post('/serverLibrary/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc(groupId) {
      this.getGroupTreeNode().then(() => {
        this.$nextTick(() => {
          groupId = groupId ? String(groupId) : '0'
          const node = this.dataToTreeNode({ id: groupId })
          this.groupTree.selectCurrentNode(node.id)
        })
      })
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    formatterData() {
      if (this.temp.useNewVersion === 0) {
        this.temp.limitNet = null
      }
      if (this.temp.limitNet === 1) {
        this.temp.useNewVersion = 2
      } else if (this.temp.limitNet === 0) {
        this.temp.useNewVersion = 1
      }
      if (this.temp.useNewVersion === 1 || this.temp.useNewVersion === 2) {
        this.temp.beginIp = this.temp.endIp = this.temp.ip
        if (this.temp.portMode === 1) {
          this.temp.beginPort = this.temp.endPort = this.temp.port
        } else if (this.temp.portMode === 3) {
          this.temp.beginPort = 1
          this.temp.endPort = 65535
        }
      }
      if (this.temp.ipType == 1) {
        this.temp.beginIp = this.temp.endIp = this.temp.domainName
      }
      if (this.temp.limitNet == 1 && this.temp.upLoadLimitSpeed != -1) {
        this.temp.upLoadLimitSpeed = -1
      }
      if (this.temp.encDecType === 1) {
        // 使用默认加解密策略
        this.temp.uploadType = this.propertyStg.uploadType
        this.temp.uploadFileExt = this.propertyStg.uploadFileExt
        this.temp.downloadType = this.propertyStg.downloadType
        this.temp.downloadFileExt = this.propertyStg.downloadFileExt
      }
    },
    useNewVersionFormatter: function(row, data) {
      return this.useNewVersionOptions[data]
    },
    modelFormatter: function(row, data) {
      if (row.useNewVersion === 1) {
        return this.$t('pages.serverLibrayWhiteListMode1')
      } else if (row.useNewVersion === 2) {
        return this.$t('pages.serverLibrayWhiteListMode2')
      }
    },
    serverGroupNameFormatter: function(row, data) {
      const groupId = row.groupId;
      if (groupId === 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.treeData, data)
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    ipTypeFormatter: function(row, data) {
      return this.ipTypeOptions[data]
    },
    loadTypeFormatter: function(row, data) {
      return this.loadTypeOptions[data]
    },
    /* waitAccessLoginFormatter: function(row, data) {
      if (data) {
        return this.$t('text.yes')
      } else {
        return this.$t('text.no')
      }
    }, */
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getWhiteListServerByName({ name: value }).then(respond => {
          const timeInfo = respond.data
          if (timeInfo && timeInfo.id != this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    ipValidator(rule, value, callback) {
      if (this.temp.ipType == 0) { // ipv4
        if (value && isIPv4(value)) {
          if (this.temp.useNewVersion === 0 && this.temp.beginIp && this.temp.endIp) {
            const temp1 = this.temp.beginIp.split('.')
            const temp2 = this.temp.endIp.split('.')
            let flag = false
            for (var i = 0; i < 4; i++) {
              if (temp1[i] - temp2[i] == 0) {
                continue
              } else if (temp1[i] - temp2[i] > 0) {
                flag = true
              }
              break
            }
            if (flag) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      } else {   // ipv6
        if (value && isIPv6(value)) {
          if (this.temp.useNewVersion === 0 && this.temp.beginIp && this.temp.endIp) {
            const fullbeginIpv6 = this.getFullIPv6(this.temp.beginIp)
            const fullendIpv6 = this.getFullIPv6(this.temp.endIp)
            if (fullbeginIpv6 > fullendIpv6) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    },
    portValidator(rule, value, callback) {
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      }
      if (value < 1 || value > 65535) {
        callback(new Error(this.$t('pages.serverLibrary_text6')))
      } else {
        const beginPort = Number(this.temp.beginPort)
        const endPort = Number(this.temp.endPort)
        if (beginPort && endPort) {
          if (beginPort > endPort) {
            callback(new Error(this.$t('pages.serverLibrary_text7')))
          } else {
            this.$refs['dataForm'].clearValidate(['beginPort', 'endPort'])
            callback()
          }
        } else {
          callback()
        }
      }
    },
    processNameValidator(rule, value, callback) {
      if (this.temp.processName && this.temp.processName.length > 450) {
        callback(this.$t('pages.broseProcessNameTooLong'))
      }
      if (this.temp.useNewVersion == 1 || this.temp.useNewVersion == 2) {
        value.indexOf('*.*') > -1 ? callback(this.$t('pages.serverLibrary_text8')) : callback()
      }
      callback()
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.groupTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.setCurrentKey(nodeData.dataId)
        this.handleNodeClick(nodeData, {})
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    batchEditFunc(params, callback) {
      if (this.hasShowEditBrowser) {
        this.editBrowserProcessData(params, callback)
      } else if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteWhiteListServer(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    editBrowserProcessData(params, callback) {
      if (this.editBrowserProcesses.includes('*.*')) {
        callback('cancel')
        return
      }
      const reqParams = Object.assign({}, params, { editOpt: this.editBrowserOpt, processes: this.editBrowserProcesses })
      if (!this.editBrowserProcesses || this.editBrowserProcesses.length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.noInputBrowserProcess'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      editBrowserProcess(reqParams).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.operateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback('cancel') })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    handleInput(value) {
      if (isNaN(Number(value)) || Number(value) !== parseInt(value)) {
        this.temp.port = undefined
      } else if (value.includes('.')) {
        this.temp.port = value.split('.')[0]
      } else {
        this.temp.port = value
      }
    },
    handleBeginInput(value) {
      if (isNaN(Number(value)) || Number(value) !== parseInt(value)) {
        this.temp.beginPort = undefined
      } else if (value.includes('.')) {
        this.temp.beginPort = value.split('.')[0]
      } else {
        this.temp.beginPort = value
      }
    },
    handleEndInput(value) {
      if (isNaN(Number(value)) || Number(value) !== parseInt(value)) {
        this.temp.endPort = undefined
      } else if (value.includes('.')) {
        this.temp.endPort = value.split('.')[0]
      } else {
        this.temp.endPort = value
      }
    },
    inputBlur(validateProp, value) {
      this.$refs['dataForm'].validateField(validateProp)
    },
    domainFormatter(row, data) {
      if (row.ipType === 1) { // IP地址类型为域名
        return row.beginIp
      } else if (row.ipType === 0) { // IP地址类型为IPV4地址
        if (row.beginIp === row.endIp) {
          return row.beginIp
        } else {
          return `${row.beginIp} ~ ${row.endIp}`
        }
      } else if (row.ipType === 2) { // IP地址类型为IPV6地址
        if (row.beginIp === row.endIp) {
          return row.beginIp
        } else {
          return `${row.beginIp} ~ ${row.endIp}`
        }
      }
    },
    portFormatter(row, data) {
      if (row.beginPort === row.endPort) {
        return row.beginPort
      } else {
        return `${row.beginPort} ~ ${row.endPort}`
      }
    },
    resetQuery() {
      this.query.page = 1
      this.query.searchInfo = ''
      this.query.name = ''
      this.query.useNewVersion = undefined
      this.query.ipType = undefined
      this.query.domain = ''
      this.query.port = undefined
      this.query.processes = undefined
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    }
  }
}
</script>
