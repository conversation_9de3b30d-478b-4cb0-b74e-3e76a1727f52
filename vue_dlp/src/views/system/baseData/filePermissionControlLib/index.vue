<template>
  <div class="app-container">
    <div class="table-container">
      <el-tabs ref="tabs" v-model="tabName" type="card">
        <el-tab-pane :label="$t('table.processConfig')" name="processTab">
          <basic-tab-content
            :type="0"
            :root-node-label="this.$t('table.processGroup')"
            :placeholder="this.$t('table.processName')"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('table.pathConfig')" name="pathTab">
          <basic-tab-content
            :type="1"
            :root-node-label="this.$t('table.pathGroup')"
            :placeholder="this.$t('table.path')"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('table.suffixConfig')" name="suffixTab">
          <basic-tab-content
            :type="2"
            :root-node-label="this.$t('table.suffixGroup')"
            :placeholder="this.$t('table.fileSuffix')"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script type="text/jsx">

import BasicTabContent from '@/views/system/baseData/filePermissionControlLib/basicTabContent.vue';

export default {
  name: 'FilePermissionControlLib',
  components: { BasicTabContent },
  data() {
    return {
      tabName: 'processTab'
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
    const tabName = this.$route.query.tabName
    if (tabName) {
      this.tabName = tabName
    }
    // 清除路由查询参数
    this.$router.replace({ query: null })
  },
  methods: {

  }
}
</script>

