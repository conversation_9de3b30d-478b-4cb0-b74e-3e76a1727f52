<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="treeMenu"
        resizeable
        :default-expand-all="true"
        :data="groupTreeData"
        :render-content="renderContent"
        @node-click="nodeClick"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-if="type === 0" icon="el-icon-upload2" size="mini" @click="handleProcessImport">
          {{ $t('button.import') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.value"
            v-trim
            clearable
            :placeholder="placeholder"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <edit-group-dlg
      ref="editGroupDlg"
      :title="rootNodeLabel"
      :add-func="createGroup"
      :update-func="updateGroup"
      :delete-func="deleteGroup"
      :edit-valid-func="getGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
    />

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="90px"
      >
        <FormItem :label="$t('table.sourceGroup')" prop="groupId">
          <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in formTreeData" :key="item.id" :label="item.label" :value="Number(item.dataId)"/>
          </el-select>
        </FormItem>

        <FormItem v-if="type === 0 && dialogStatus === 'create'" :label="placeholder" :tooltip-content="$t('pages.onlySupportFullMatch')" tooltip-placement="top-end" prop="value">
          <div style="display: flex;align-items: center;">
            <tag
              :list="processList"
              trim-able
              border
              input-length="15"
              :limit-size="addMaxNum"
              style="display: inline-block;width: calc(100% - 88px);background-color:#f5f5f5; min-height: 30px;"
              @tagChange="tagChange"
            />
            <el-upload
              ref="upload"
              name="uploadFile"
              action="aaaaaa"
              accept=".exe"
              :disabled="fileSubmitting"
              :show-file-list="false"
              :before-upload="beforeUpload"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting" size="mini" style="width: 42px;height: 30px;padding: 0;margin: 0 2px 1px 2px"/>
            </el-upload>
            <el-button size="mini" style="width: 42px;height: 30px;padding: 0;margin: 0" @click="handleProcessClear">
              {{ $t('button.clear') }}
            </el-button>
          </div>
        </FormItem>

        <FormItem v-if="type === 1 && dialogStatus === 'create'" :label="placeholder" :tooltip-content="$t('pages.supportWildcardCharacter')" tooltip-placement="top-end" prop="value">
          <tag
            :list="pathList"
            trim-able
            border
            input-length="240"
            :limit-size="addMaxNum"
            style="background-color:#f5f5f5; min-height: 30px;"
            @tagChange="tagChange"
          />
        </FormItem>

        <FormItem v-if="type === 2 && dialogStatus === 'create'" :label="placeholder" :tooltip-content="$t('pages.supportWildcardCharacter')" tooltip-placement="top-end" prop="value">
          <div style="display: flex;align-items: center;">
            <tag
              :list="suffixList"
              trim-able
              border
              input-length="10"
              :limit-size="addMaxNum"
              style="width: calc(100% - 80px);background-color:#f5f5f5; min-height: 30px;"
              @tagChange="tagChange"
            />
            <el-button type="primary" size="mini" style="margin: 0 0 0 2px!important" @click="handleFileSuffixImport()">
              <svg-icon icon-class="import" />
            </el-button>
            <el-button size="small" style="height: 30px;margin-bottom: 0" @click="handleClear">
              {{ $t('button.clear') }}
            </el-button>
          </div>
        </FormItem>

        <FormItem v-if="dialogStatus === 'update'" :label="placeholder" :tooltip-content="type === 0 ? $t('pages.onlySupportFullMatch') : $t('pages.supportWildcardCharacter')" tooltip-placement="top-end" prop="value">
          <el-input v-model="temp.value" v-trim :maxlength="type === 0 ? 15 : type === 1 ? 240 : 10" clearable/>
        </FormItem>
      </Form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>

    <app-select-dlg ref="appSelectDlg" :validate-func="validateFunc" :append-to-body="true" @select="importProcess">
      <template slot="additionalConfig">
        <div style="float: left;margin-left: 216px;">
          <label>{{ $t('pages.processToGroup') }}:</label>
          <el-select v-model="temp.groupId" filterable style="width: 250px" :placeholder="$t('text.select')">
            <el-option v-for="item in formTreeData" :key="item.id" :label="item.label" :value="Number(item.dataId)"/>
          </el-select>
        </div>
      </template>
      <template slot="btnText">
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            {{ $t('pages.autoDuplicateProcess') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        {{ $t('button.import') }}
      </template>
    </app-select-dlg>
  </div>
</template>

<script>
import {
  createGroup,
  updateGroup,
  deleteGroup,
  getGroupByName,
  groupIdExistReference,
  listGroupTreeNode,
  getPage,
  getByValues,
  batchInsert,
  update,
  batchDelete,
  getByTypeAndGroupId
} from '@/api/system/baseData/filePermissionControlLib';
import EditGroupDlg from '@/views/common/editGroupDlg'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'

export default {
  name: 'BasicTabContent',
  components: { EditGroupDlg, FileSuffixLibImport, AppSelectDlg },
  props: {
    type: { type: Number, default: null }, // 类型，0-进程，1-路径，2-后缀
    rootNodeLabel: { type: String, default: '' },
    placeholder: { type: String, default: '' }
  },
  data() {
    return {
      colModel: [
        { prop: 'groupId', label: 'sourceGroup', width: '100', formatter: this.groupFormatter },
        { prop: 'value', label: this.placeholder, width: '200' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      groupTreeData: [{ id: 'G0', dataId: '0', label: this.rootNodeLabel, parentId: '', type: 'G', children: [] }],
      formTreeData: [],
      query: { // 查询条件
        page: 1,
        groupId: undefined,
        value: undefined
      },
      textMap: {
        update: this.i18nConcatText(this.placeholder, 'update'),
        create: this.i18nConcatText(this.placeholder, 'create')
      },
      showTree: true,
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        type: undefined,
        groupId: undefined,
        value: undefined
      },
      addMaxNum: 50,
      processList: [],
      pathList: [],
      suffixList: [],
      addBtnAble: false,
      dialogStatus: '',
      dialogFormVisible: false,
      submitting: false,
      rules: {
        value: [
          { required: true, trigger: 'blur', validator: this.valueValidator }
        ],
        groupId: [{ required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }]
      },
      fileLimitSize: 1024,
      fileSubmitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTable']
    },
    treeMenu() {
      return this.$refs['treeMenu']
    }
  },
  created() {
    this.resetTemp()
    this.loadGroupTreeData()
  },
  activated() {
    this.loadGroupTreeData()
  },
  methods: {
    createGroup(data) {
      const groupData = Object.assign(data, { type: this.type })
      return createGroup(groupData)
    },
    updateGroup(data) {
      const groupData = Object.assign(data, { type: this.type })
      return updateGroup(groupData)
    },
    deleteGroup,
    getGroupByName(data) {
      const searchQuery = Object.assign(data, { type: this.type })
      return getGroupByName(searchQuery)
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.processList = []
      this.pathList = []
      this.suffixList = []
    },
    loadGroupTreeData() {
      listGroupTreeNode(this.type).then(respond => {
        this.groupTreeData[0].children = respond.data
        this.formTreeData = this.groupTreeData[0].children
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option, { type: this.type })
      return getPage(searchQuery)
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    groupFormatter(row, data) {
      const nodeData = this.treeMenu.findNode(this.groupTreeData, data, 'dataId')
      return nodeData.label
    },
    nodeClick(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData && checkedData.dataId != '0') {
        this.query.groupId = Number(checkedData.dataId)
      } else {
        this.query.groupId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: data.parentId.replace('G', '')
      })
    },
    removeNode(data) {
      groupIdExistReference(data.dataId).then(respond => {
        const dataReference = respond.data.dataReference
        const stgReference = respond.data.stgReference
        if (dataReference > 0) {
          this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.groupExistDataNotAllowDelete'), type: 'warning', duration: 2000 })
          return
        }
        if (stgReference.length > 0) {
          this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.timeInfo_text4') + stgReference.join(','), type: 'warning', duration: 2000 })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    createNode(data) {
      this.treeMenu.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.treeMenu.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.treeMenu.findNode(this.groupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.treeMenu.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    dataToTreeNode(data) {
      return {
        id: 'G' + data.id,
        type: 'G',
        dataId: data.id + '',
        label: data.name,
        parentId: 'G0'
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row))
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const datas = []
          if (this.type === 0) {
            this.processList.forEach(process => {
              const data = { type: this.type, groupId: this.temp.groupId, value: process }
              datas.push(data)
            })
          } else if (this.type === 1) {
            this.pathList.forEach(path => {
              const data = { type: this.type, groupId: this.temp.groupId, value: path }
              datas.push(data)
            })
          } else if (this.type === 2) {
            this.suffixList.forEach(suffix => {
              const data = { type: this.type, groupId: this.temp.groupId, value: suffix }
              datas.push(data)
            })
          }
          batchInsert(datas).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.type = this.type
          update(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const selectedIds = this.gridTable.getSelectedIds()
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        batchDelete({ ids: selectedIds.join(',') }).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(e => { })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    valueValidator(rule, value, callback) {
      if (this.dialogStatus === 'create') {
        if ((this.type === 0 && this.processList.length === 0) || (this.type === 1 && this.pathList.length === 0) || (this.type === 2 && this.suffixList.length === 0)) {
          callback(new Error(this.$t('text.pleaseEnterInfo', { info: this.placeholder })))
        } else {
          let validValues = []
          let flag = false
          if (this.type === 0) {
            validValues = this.processList
            for (let i = 0; i < validValues.length; i++) {
              const process = validValues[i]
              if (process.includes('*.*')) {
                flag = true
                break
              }
            }
          } else if (this.type === 1) {
            validValues = this.pathList
          } else {
            validValues = this.suffixList
          }
          if (flag) {
            callback(new Error(this.$t('pages.enDeFileScan_Msg9')))
          }
          getByValues({ type: this.type, groupId: this.temp.groupId, values: validValues }).then(respond => {
            const bean = respond.data
            if (bean && bean.id != this.temp.id) {
              callback(new Error(bean.value + ' ' + this.$t('pages.alreadyExistsInfo', { info: this.placeholder })))
            } else {
              callback()
            }
          })
        }
      } else {
        if (!value) {
          callback(new Error(this.$t('text.pleaseEnterInfo', { info: this.placeholder })))
        } else {
          if (this.type === 0) {
            if (value.includes('*.*')) {
              callback(new Error(this.$t('pages.enDeFileScan_Msg9')))
            }
          }
          getByValues({ type: this.type, groupId: this.temp.groupId, values: [value] }).then(respond => {
            const bean = respond.data
            if (bean && bean.id != this.temp.id) {
              callback(new Error(bean.value + ' ' + this.$t('pages.alreadyExistsInfo', { info: this.placeholder })))
            } else {
              callback()
            }
          })
        }
      }
    },
    handleFileSuffixImport() { // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    handleProcessImport() { // 从应用程序库导入
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.$refs.appSelectDlg.show()
    },
    importFileSuffix(suffix) {
      let flag = false
      const newSuffix = suffix.split('|').map(item => {
        if (item.length > 10) {
          flag = true
          return item.substr(0, 10)
        } else {
          return item
        }
      })
      let unionSuffix = [...new Set(this.suffixList.concat(newSuffix))]
      if (unionSuffix.length > this.addMaxNum) {
        unionSuffix = unionSuffix.slice(0, this.addMaxNum)
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.$t('pages.fileSuffixOutnumberErrorMsg2', { number: this.addMaxNum }),
          type: 'warning',
          duration: 2000
        })
      }
      if (flag) {
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.$t('text.cantTooLong', { info: this.placeholder, size: 10 }),
          type: 'warning',
          duration: 2000
        })
      }
      this.suffixList = unionSuffix
      this.validValue()
    },
    async importProcess(processList) {
      const uniqueList = processList.reduce((acc, current) => {
        const exists = acc.some(item => item['processName'] === current['processName']);
        if (!exists) {
          acc.push(current);
        }
        return acc;
      }, []);
      this.submitting = true
      const selectedList = uniqueList.map(item => {
        if (item.processName.length > 15) {
          return item.processName.substr(0, 15)
        } else {
          return item.processName
        }
      })
      const resp = await getByTypeAndGroupId({ type: this.type, groupId: this.temp.groupId })
      const existValues = resp.data
      let list = selectedList.filter(item => !existValues.includes(item))
      if (list.length > this.addMaxNum) {
        list = list.slice(0, this.addMaxNum)
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.$t('pages.procOutnumberErrorMsg2', { number: this.addMaxNum }),
          type: 'warning',
          duration: 2000
        })
      }
      const datas = []
      list.forEach(process => {
        const data = { type: this.type, groupId: this.temp.groupId, value: process }
        datas.push(data)
      })
      batchInsert(datas).then(respond => {
        this.submitting = false
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      let fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'exe') {
        this.fileSubmitting = true
        let flag = false
        if (fileName.length > 15) {
          flag = true
          fileName = fileName.substr(0, 15)
        }
        let unionList = [...new Set(this.processList.concat(fileName))]
        if (unionList.length > this.addMaxNum) {
          unionList = unionList.slice(0, this.addMaxNum)
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.procOutnumberErrorMsg2', { number: this.addMaxNum }),
            type: 'warning',
            duration: 2000
          })
        }
        if (flag) {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('text.cantTooLong', { info: this.placeholder, size: 15 }),
            type: 'warning',
            duration: 2000
          })
        }
        this.processList = unionList
        this.fileSubmitting = false
        this.validValue()
      } else {
        this.$message({
          message: this.$t('pages.processStgLib_Msg55'),
          type: 'error',
          duration: 2000
        })
      }
      return false // 屏蔽了action的默认上传
    },
    handleClear() {
      this.suffixList.splice(0)
      this.validValue()
    },
    handleProcessClear() {
      this.processList.splice(0)
      this.validValue()
    },
    tagChange() {
      this.validValue()
    },
    validValue() {
      this.$refs.dataForm.validateField('value')
    },
    validateFunc() {
      if (this.temp.groupId === undefined) {
        this.$message({
          message: this.$t('pages.validaGroup'),
          type: 'error',
          duration: 2000
        })
        return false
      } else {
        return true;
      }
    }
  }
}
</script>

<style scoped>

</style>
