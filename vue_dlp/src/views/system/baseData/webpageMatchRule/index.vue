<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="webpageMatchRuleTree"
        resizeable
        :default-expand-all="true"
        :data="webpageMatchRuleGroupTreeData"
        :render-content="renderContent"
        @node-click="webpageMatchRuleGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'392'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'393'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.name"
            v-trim
            clearable
            :placeholder="$t('table.name')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="85px">
              <FormItem :label="$t('table.name')" prop="name">
                <el-input v-model="query.name" v-trim clearable :maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.ipOrDomain')" prop="host">
                <el-input v-model="query.host" v-trim clearable :maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.beginPort')" prop="beginPort">
                <el-input v-model.number="query.beginPort" v-trim clearable :placeholder="$t('pages.serverLibrary_text6')" :maxlength="5" @input="number1('beginPort')"/>
              </FormItem>
              <FormItem :label="$t('table.endPort')" prop="endPort">
                <el-input v-model.number="query.endPort" v-trim :maxlength="5" clearable :placeholder="$t('pages.serverLibrary_text6')" @input="number1('endPort')"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>

      <grid-table
        ref="webpageMatchRuleList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.name')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"/>
        </FormItem>

        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>

        <FormItem :label="$t('table.ipOrDomain')" prop="host">
          <el-input v-model="temp.host" v-trim :maxlength="60"/>
        </FormItem>

        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.beginPort')" prop="beginPort">
              <el-input v-model.number="temp.beginPort" v-trim :maxlength="5" @keyup.native="number('beginPort')"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.endPort')" prop="endPort">
              <el-input v-model.number="temp.endPort" v-trim :maxlength="5" @keyup.native="number('endPort')"/>
            </FormItem>
          </el-col>
        </el-row>

        <!--        <FormItem :label="$t('table.matchKind')" prop="matchKind">-->
        <!--          <el-select v-model="temp.matchKind">-->
        <!--             <el-option v-for="(value, key) in matchKindOptions" :key="value" :label="key" :value="value" ></el-option>-->
        <!--          </el-select>  根据终端调整，匹配模式写死为模糊匹配-->
        <!--        </FormItem>-->
      </Form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.webpageMatchRuleGroup')"
      :group-tree-data="formTreeData"
      :add-func="createWebpageMatchRuleGroup"
      :update-func="updateWebpageMatchRuleGroup"
      :delete-func="deleteWebpageMatchRuleGroup"
      :move-func="moveGroup"
      :edit-valid-func="getWebpageMatchRuleGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="$t('table.webpageMatchRule')"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteWebpageMatchRuleGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? i18nConcatText(this.$t('table.webpageMatchRule'), 'update') : i18nConcatText(this.$t('table.webpageMatchRule'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :delete-filter-name="$t('table.webpageMatchRule')"
      :edit-type="updateGroupForm ? 0 : 1"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="webpageMatchRuleGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>

    <export-dlg ref="exportDlg" :group-tree-data="webpageMatchRuleGroupTreeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>

    <import-dlg
      ref="importDlg"
      :title="title"
      :tip="$t('table.webpageMatchRule')"
      template="webpageMatchRule"
      :show-import-type="false"
      :show-import-way="true"
      :file-name="title"
      :upload-func="upload"
      @success="importEndFunc"
    />
  </div>
</template>

<script>
import {
  getTreeNode, getWebpageMatchRuleList, createWebpageMatchRuleGroup,
  updateWebpageMatchRuleGroup, deleteWebpageMatchRuleGroup,
  moveGroup, getWebpageMatchRuleGroupByName, deleteGroupAndData,
  moveGroupToOther, batchUpdateAllGroup, batchUpdateGroup,
  createWebpageMatchRule, updateWebpageMatchRule, deleteWebpageMatchRule,
  exportExcel, countWebpageMatchRuleByGroupId, getByName
} from '@/api/system/baseData/webpageMatchRule';
import { findNodeLabel } from '@/utils/tree';
import EditGroupDlg from '@/views/common/editGroupDlg';
import DeleteGroupDlg from '@/views/common/deleteGroupDlg';
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import request from '@/utils/request';
export default {
  name: 'WebpageMatchRule',
  components: { EditGroupDlg, DeleteGroupDlg, BatchEditPageDlg, ExportDlg, ImportDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'name', width: '150', sort: true, fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'host', label: 'ipOrDomain', width: '150', sort: true },
        { prop: 'beginPort', label: 'beginPort', width: '150', sort: true },
        { prop: 'endPort', label: 'endPort', width: '150', sort: true },
        // { prop: 'matchKind', label: 'matchKind', width: '150', formatter: this.matchKindFormatter },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      // matchKindOptions: {
      //   // [this.$t('pages.blueTooth_blueTooth_Msg11')]: 0,
      //   // [this.$t('pages.leftLikeMatch')]: 1,
      //   // [this.$t('pages.rightLikeMatch')]: 2,
      //   // [this.$t('pages.blueTooth_blueTooth_Msg12')]: 3
      // },
      webpageMatchRuleGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('route.webpageMatchRuleLib'), parentId: '', children: [] }],
      formTreeData: [],
      treeNodeType: 'G',
      query: { // 查询条件
        page: 1,
        name: '',
        host: '',
        beginPort: undefined,
        endPort: undefined,
        groupId: ''
      },
      showTree: true,
      deleteable: false,
      textMap: {
        update: this.i18nConcatText(this.$t('table.webpageMatchRule'), 'update'),
        create: this.i18nConcatText(this.$t('table.webpageMatchRule'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.webpageMatchRuleGroup'), 'delete')
      },
      title: this.$t('table.webpageMatchRule'),
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        groupId: '',
        host: '',
        beginPort: undefined,
        endPort: undefined,
        matchKind: 3 // 默认模糊匹配
      },
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      addBtnAble: false,
      treeSelectNode: [],
      dialogStatus: '',
      dialogFormVisible: false,
      submitting: false,
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        groupId: [{ required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }],
        host: [{ required: true, message: this.$t('pages.ipOrDomainNotEmpty'), trigger: 'blur' }],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['webpageMatchRuleList']
    },
    webpageMatchRuleTree: function() {
      return this.$refs['webpageMatchRuleTree']
    }
  },
  created() {
    this.resetTemp()
    this.loadWebpageMatchRuleGroupTree()
  },
  activated() {
    this.loadWebpageMatchRuleGroupTree()
  },
  methods: {
    createWebpageMatchRuleGroup,
    updateWebpageMatchRuleGroup,
    deleteWebpageMatchRuleGroup,
    getWebpageMatchRuleGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleWebpageMatchRuleGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleWebpageMatchRuleGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    loadWebpageMatchRuleGroupTree: function() {
      getTreeNode().then(respond => {
        this.webpageMatchRuleGroupTreeData[0].children = respond.data
        this.formTreeData = this.loadTypeTreeExceptRoot()
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
        this.$nextTick(() => {
          this.webpageMatchRuleTree.$refs['tree'].setCurrentKey('0')
          this.query.groupId = '0'
          this.gridTable.execRowDataApi()
        })
      })
    },
    loadTypeTreeExceptRoot() {
      return this.webpageMatchRuleGroupTreeData[0].children
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getWebpageMatchRuleList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.webpageMatchRuleGroupTreeData, data)
    },
    // matchKindFormatter(row) {
    //   if (row.matchKind === 0) {
    //     return this.$t('pages.blueTooth_blueTooth_Msg11')
    //   } else if (row.matchKind === 1) {
    //     return this.$t('pages.leftLikeMatch')
    //   } else if (row.matchKind === 2) {
    //     return this.$t('pages.rightLikeMatch')
    //   } else if (row.matchKind === 3) {
    //     return this.$t('pages.blueTooth_blueTooth_Msg12')
    //   }
    //   return '';
    // },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    webpageMatchRuleGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleWebpageMatchRuleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    handleWebpageMatchRuleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    removeNode(data) {
      countWebpageMatchRuleByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.webpageMatchRuleTree.findNode(this.webpageMatchRuleGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.webpageMatchRuleTree.setCurrentKey(nodeData.id)
        this.webpageMatchRuleGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.webpageMatchRuleTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.webpageMatchRuleTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.webpageMatchRuleTree.findNode(this.webpageMatchRuleGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.webpageMatchRuleTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    handleMoving() {
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteWebpageMatchRule(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.webpageMatchRuleGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createWebpageMatchRule(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateWebpageMatchRule(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null
        }, opts)
      }
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    importEndFunc() {
      this.loadWebpageMatchRuleGroupTree()
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    upload(data) {
      return request.post('/webpageMatchRule/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    portValidator(rule, value, callback) {
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      } else {
        if (value < 1 || value > 65535) {
          callback(new Error(this.$t('pages.serverLibrary_text6')))
        } else {
          const beginPort = Number(this.temp.beginPort)
          const endPort = Number(this.temp.endPort)
          if (beginPort && endPort) {
            if (beginPort > endPort) {
              callback(new Error(this.$t('pages.serverLibrary_text7')))
            } else {
              this.$refs['dataForm'].clearValidate(['beginPort', 'endPort'])
              callback()
            }
          } else {
            callback()
          }
        }
      }
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getByName({ name: value }).then(respond => {
          const webpageMatchRule = respond.data
          if (webpageMatchRule && webpageMatchRule.id != this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    number(field) {
      const val = this.temp[field]
      if (isNaN(val)) {
        this.temp[field] = val.replace(/[^\d]/g, '')
      }
    },
    number1(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.host = ''
      this.query.beginPort = undefined
      this.query.endPort = undefined
    }
  }
}
</script>

<style scoped>

</style>
