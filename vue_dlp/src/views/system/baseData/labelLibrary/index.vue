<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="labelInfoGroupTree"
        resizeable
        :default-expand-all="true"
        :data="labelInfoGroupTreeData"
        :render-content="renderContent"
        @node-click="labelInfoGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <!-- <el-button v-permission="'232'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'240'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button> -->
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('table.labelName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="labelInfoList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.labelName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60" @input="handleInput" @change="handelChange"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="temp.groupId" :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteLabelLibGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <export-dlg ref="exportDlg" :group-tree-data="labelInfoGroupTreeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      :tip="$t('pages.mailLibrary_text3')"
      template="mail.xls"
      :show-import-type="false"
      :show-import-way="true"
      :file-name="title + '.xls'"
      @success="importEndFunc"
    />
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.labelGroup')"
      :group-tree-data="formTreeData"
      :add-func="createLabelLibGroup"
      :update-func="updateLabelLibGroup"
      :delete-func="deleteLabelLibGroup"
      :move-func="moveGroup"
      :edit-valid-func="getLabelLibGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? i18nConcatText($t('pages.labelGroup'), 'update') : i18nConcatText($t('pages.labelInfo'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApiForDelete"
      :delete-filter-name="$t('table.label')"
      :edit-type="updateGroupForm ? 0 : 1"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="labelInfoGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>
  </div>
</template>

<script>
import {
  getGroupTreeNode, createLabelLibGroup, updateLabelLibGroup, deleteLabelLibGroup, deleteGroupAndData,
  moveGroupToOther, getLabelLibGroupByName, countChildByGroupId, getLabelLibPage, createLabelLib,
  updateLabelLib, deleteLabelLib, exportExcel, moveGroup, batchUpdateGroup, batchUpdateAllGroup, countByLabel
} from '@/api/system/baseData/labelLibrary'
import { findNodeLabel } from '@/utils/tree'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'LabelLibrary',
  components: { BatchEditPageDlg, DeleteGroupDlg, EditGroupDlg, ImportDlg, ExportDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'labelName', width: '150', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'remark', label: 'remark', width: '200' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        groupId: undefined,
        type: 0
      },
      formTreeData: [],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_labelName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.labelInfo'), 'update'),
        create: this.i18nConcatText(this.$t('pages.labelInfo'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.labelGroup'), 'delete')
      },
      treeNodeType: 'G',
      labelInfoGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.labelLibrary'), parentId: '', children: [] }],
      treeSelectNode: [],
      title: this.$t('pages.mailBox'),
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined // 批量修改的分组id
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.$router.push({ query: {}})
    })
  },
  computed: {
    gridTable() {
      return this.$refs['labelInfoList']
    },
    labelInfoGroupTree: function() {
      return this.$refs['labelInfoGroupTree']
    }
  },
  watch: {
  },
  activated() {
    this.loadReceiverTree()
    this.gridTable && this.gridTable.execRowDataApi(this.query)
  },
  created() {
    this.resetTemp()
    this.loadReceiverTree()
  },
  methods: {
    createLabelLibGroup,
    updateLabelLibGroup,
    deleteLabelLibGroup,
    getLabelLibGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLabelLibPage(searchQuery)
    },
    rowDataApiForDelete: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getLabelLibPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    labelInfoGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    loadReceiverTree: function() {
      return getGroupTreeNode().then(respond => {
        this.labelInfoGroupTreeData[0].children = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.labelInfoGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.labelInfoGroupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.labelInfoGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.labelInfoGroupTree.findNode(this.labelInfoGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.labelInfoGroupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    loadTypeTreeExceptRoot() {
      // if (this.query.groupId) return this.labelInfoGroupTreeData[0].children.filter(item => item.dataId != this.query.groupId)
      return this.labelInfoGroupTreeData[0].children
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleMoving(idList) {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    importEndFunc(groupId) {
      this.loadReceiverTree().then(() => {
        this.$nextTick(() => {
          groupId = groupId ? String(groupId) : '0'
          const node = this.dataToTreeNode({ id: groupId })
          this.labelInfoGroupTree.selectCurrentNode(node.id)
        })
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createLabelLib(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateLabelLib(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.labelInfoGroupTreeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.labelInfoGroupTree.findNode(this.labelInfoGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.labelInfoGroupTree.setCurrentKey(nodeData.id)
        this.labelInfoGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      const paramTemp = Object.assign({}, params);
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteLabelLib(paramTemp).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          // 通知应用到该配置的标签树重新请求，更新数据
          this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    handleInput(value) {
      // 正则表达式匹配所有标点符号（这里使用了 Unicode 属性 \p{P} 来匹配标点）
      // const punctuationRegex = /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/ig
      const punctuationRegex = /[|,'，]/g
      if (punctuationRegex.test(value)) {
        // 如果输入不是纯标点符号，则清除非标点符号的内容
        this.temp.name = value.replace(/[|,'，]/g, '')
      } else {
        // 如果是纯标点符号，则直接赋值（可选，根据需求决定是否需要）
        this.temp.name = value;
      }
    },
    handelChange(value) {
      // 正则表达式匹配所有标点符号（这里使用了 Unicode 属性 \p{P} 来匹配标点）
      const punctuationRegex = /[|,'，]/g
      if (punctuationRegex.test(value)) {
        // 如果输入不是纯标点符号，则清除非标点符号的内容
        this.temp.name = value.replace(/[|,'，]/g, '')
      } else {
        // 如果是纯标点符号，则直接赋值（可选，根据需求决定是否需要）
        this.temp.name = value;
      }
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback()
      }
      if (value && value == this.$t('report.tagReportMsg33')) {
        callback(new Error('不允许存在无标签内容'))
      }
      countByLabel({ name: value }).then(respond => {
        const data = respond.data
        if (data && data.id !== this.temp.id) {
          callback(new Error(this.$t('pages.labelLibrary_confirmMsg')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
