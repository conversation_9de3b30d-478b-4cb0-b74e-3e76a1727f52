<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="120px">
              <FormItem :label="$t('table.backupRuleName')" prop="name">
                <el-input v-model="query.name" v-trim clearable :maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('pages.backupRule_text3')" prop="fileExtSetup">
                <el-select v-model="query.fileExtSetup" clearable style="width: 100%">
                  <el-option :label="$t('pages.backupRule5')" :value="2"></el-option>
                  <el-option :label="$t('pages.backupRule1')" :value="1"></el-option>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table ref="ruleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="dialogStatus = ''"
    >
      <Form ref="ruleForm" :model="temp" :rules="rules" label-position="right" label-width="80px">
        <FormItem :label=" $t('table.ruleName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"></el-input>
        </FormItem>
        <el-card class="box-card" :body-style="{'padding': ' 5px 20px'}" style="margin-top: 15px">
          <div slot="header">
            <span>{{ $t('pages.backupRule_text3') }}</span>
            <el-button slot="reference" size="small" @click="handleFileSuffixImport()">
              {{ $t('button.FileSuffixLibImport') }}
            </el-button>
            <el-button size="small" @click="handleClear()">{{ $t('button.clear') }}</el-button>
            <el-button size="small" @click="handleSort()">{{ $t('button.sort') }}</el-button>
          </div>
          <el-radio-group v-model="temp.fileExtSetup">
            <el-radio :label="2">{{ $t('pages.backupRuleContent2') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.backupRuleContent1') }}</el-radio>
          </el-radio-group>
          <el-checkbox v-model="noSuffixChecked" class="checkbox-interval">
            {{ temp.fileExtSetup === 1 ? $t('pages.backupRuleContent6') : $t('pages.backupRuleContent5') }}
          </el-checkbox>
          <FormItem prop="fileExt" label-width="0px">
            <tag v-model="temp.fileExt" :list="temp.fileExt" style="margin-left: -5px" :limit-size="1000" @tagChange="filterFileExtChange"/>
          </FormItem>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="handleCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="$t('pages.backupRule_delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :delete-filter-name="$t('pages.burnMsg18')"
      :edit-type="2"
      @submitEnd="deleteData"
    />
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import { getRulePage, createRule, updateRule, deleteRule, getBackUpRuleByName } from '@/api/system/baseData/backupRule'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'BackupRule',
  components: { BatchEditPageDlg, FileSuffixLibImport },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'backupRuleName', width: '100', sort: true, fixed: true },
        { prop: 'fileExtSetup', label: this.$t('pages.backupRule_text2'), width: '200', sort: true, formatter: (row) => { return row.fileExtSetup === 1 ? this.$t('pages.backupRule1') : this.$t('pages.backupRule5') } },
        { prop: 'fileExt', label: 'suffixes', width: '200', sort: true, formatter: (row) => { return row.fileExt.replace('#NoSuffix', this.$t('pages.notSuffix')) } },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        fileExtSetup: undefined
      },
      temp: {},
      defaultTemp: {
        id: '',
        name: '',
        fileExtSetup: 2,
        fileExt: []
      },
      checkedArr: [],
      configForm: {},
      dataList: {
        screenshotInterval: { label: this.$t('pages.screenCaptureInterval'), value: '', min: 1, max: 60 },
        screenshotNum: { label: this.$t('pages.numberOfScreenshots'), value: '', min: 1, max: 10 },
        recordTime: { label: this.$t('pages.screenRecordingDuration'), value: '', min: 1, max: 3600 }
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.backupRuleValidator }]
      },
      deleteable: false,
      dialogVisible: false,
      dialogConfigVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.backupRule_update'),
        create: this.$t('pages.backupRule_create')
      },
      checked: [],
      submitting: false,
      noSuffixChecked: false
    }
  },
  computed: {
    ruleTable() {
      return this.$refs['ruleList']
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.ruleTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.noSuffixChecked = false
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.resetTemp()
        this.$refs['ruleForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      const temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row)))
      temp.fileExt = temp.fileExt.length > 0 ? temp.fileExt.split('|') : []
      if (temp.fileExt.includes('#NoSuffix')) {
        const index = temp.fileExt.indexOf('#NoSuffix')
        temp.fileExt.splice(index, 1)
        this.noSuffixChecked = true
      } else {
        this.noSuffixChecked = false
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.temp = temp
        this.$refs['ruleForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          const data = this.formatData(this.temp)
          // 目前备份过滤规则只能配置文件后缀，所以限制文件后缀不能为空
          if (data.fileExt.length > 0) {
            if (data.fileExt.length > 2048) {
              this.$message({
                title: this.$t('text.prompt'),
                message: this.$t('pages.fileSuffix_text15'),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
              return
            }
            createRule(data).then(res => {
              this.submitting = false
              this.dialogVisible = false
              this.ruleTable.execRowDataApi(this.query)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.$t('pages.backupRuleContent4'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['ruleForm'].validate(async(valid) => {
        if (valid) {
          const data = this.formatData(this.temp)
          if (data.fileExt.length > 0) {
            if (data.fileExt.length > 2048) {
              this.$message({
                title: this.$t('text.prompt'),
                message: this.$t('pages.fileSuffix_text15'),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
              return
            }
            updateRule(data).then(res => {
              this.submitting = false
              this.dialogVisible = false
              this.ruleTable.execRowDataApi(this.query)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.$t('pages.backupRuleContent4'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    formatData(data) {
      const temp = JSON.parse(JSON.stringify(data))
      if (this.noSuffixChecked) {
        temp.fileExt.push('#NoSuffix')
      }
      temp.fileExt = temp.fileExt.join('|')
      return temp
    },
    handleDelete() {
      const selectedData = this.ruleTable.getSelectedDatas()
      const total = this.ruleTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleCancel() {
      this.dialogVisible = false
    },
    handleDrag() {
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.ruleTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      this.temp.fileExt = [...new Set(this.temp.fileExt.concat(new_suffix))]
    },
    filterFileExtChange(list) {
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.temp.fileExt = Object.keys(newMap) || [];
    },
    handleClear() {
      this.temp.fileExt = []
    },
    handleSort() {
      this.temp.fileExt = this.temp.fileExt.sort()
    },
    backupRuleValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getBackUpRuleByName({ name: value }).then(respond => {
          const info = respond.data
          if (info && info.id != this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = ''
      this.query.fileExtSetup = undefined
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-confirmBox{
  .el-message-box__status{
    position: absolute;
    top:23px
  }
}
.checkbox-interval {
  margin: 10px 0;
  >>>.el-checkbox__inner{
    margin-right: 4px;
  }
}
</style>
