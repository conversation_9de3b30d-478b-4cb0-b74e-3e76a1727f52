<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="600px"
    :title="$t('button.highConfig')"
  >
    <div :style="Object.assign({'position': 'relative' })">
      <i :class="isShow?'el-icon-caret-bottom':'el-icon-caret-right'" @click="()=>{isShow = !isShow}"></i>
      <el-checkbox :disabled="!formable" :value="checkRule" :true-label="1" :false-label="0" @change="handleCheck">{{ ruleTypeName }}</el-checkbox>
      <el-select
        v-model="backupRuleId"
        :placeholder="$t('text.select') "
        style="display:inline-block;width:180px;margin-left:5px"
        size="mini"
        :disabled="!formable || checkRule == 0"
        clearable
        @change="handleChange"
      >
        <el-option v-for="item in backupRules" :key="item.value" :label="item.label" :value="item.value"/>
      </el-select>
      <link-button v-if="formable" v-permission="'A5I'" btn-class="editBtn" btn-style="float: none; margin-bottom: 0;" :menu-code="'A5C'" :link-url="'/system/baseData/backupRule'" />
      <!-- <el-tooltip :content="$t('pages.backupRuleContent3')" placement="top">
        <i class="el-icon-info"></i>
      </el-tooltip> -->
      <slot name="tail"><span v-show="showSlot" class="error-tip">{{ $t('pages.backupRule_text1') }}</span></slot>
      <div style="margin: 5px 17px; color: #2b7aac;">{{ $t('pages.backupRuleContent3') }}</div>
    </div>
    <div v-show="isShow" style="position:relative">
      <div style="margin:0 0 0 15px;">
        <!-- <el-checkbox :value="option.fileExtSetup != null" style="margin-bottom:10px" disabled>过滤指定后缀文件</el-checkbox> -->
        <Form v-if="option.fileExtSetup != null" label-position="left">
          <FormItem :label="(option.fileExtSetup === 1 ? $t('pages.backupRuleContent1') : $t('pages.backupRuleContent2')) + '：'" label-width="150px">
            <span>{{ option.fileExt.replace('#NoSuffix', $t('pages.backupRuleContent7')) }}</span>
          </FormItem>
        </Form>
      </div>
    </div>

    <div v-if="formable" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleConfirm">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'BackupRuleContent',
  components: { /* WechatUserSelect*/ },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    // 多选框的label
    ruleTypeName: {
      type: String,
      default() {
        return this.$t('pages.burnMsg18')
      }
    },
    // 是否显示规则下拉框前的多选框
    showCheckRule: {
      type: Boolean,
      default: true
    },
    // 传入选择的规则id
    propRuleId: {
      type: Number,
      default() {
        return undefined
      }
    },
    // 当不显示规则下拉框前的多选框时，通过该值控制是否允许选择规则
    propCheckRule: {
      type: Number,
      default() {
        return 0
      }
    },
    filterData: {
      type: Function,
      default(item) {
        return item
      }
    }
    // 是否内容策略响应规则的配置
    // isRespondRule: {
    //   type: Boolean,
    //   default() {
    //     return false
    //   }
    // }
  },
  data() {
    return {
      dialogVisible: false,
      isShow: false,        // 是否显示规则配置项（下拉选择框前的三角号）
      // checked: [],          // 响应规则勾选的配置选项
      checkRule: false,     // 是否配置备份过滤规则
      backupRuleId: undefined,
      option: {},
      showSlot: false
    }
  },
  computed: {
    // 过滤后的响应规则数据
    resData() {
      return this.filterData(this.$store.getters.backupRules)
    },
    // 响应规则下拉框中的选项options
    backupRules() {
      return this.resData.map(item => {
        return {
          label: item.name,
          value: item.id
        }
      })
    }
  },
  watch: {
    backupRules: {
      handler: function() {
        if (this.backupRuleId) {
          const index = this.backupRules.findIndex(item => item.value === this.backupRuleId)
          if (index < 0) {
            this.backupRuleId = undefined
          }
          this.resetTemp()
        }
      },
      deep: true
    },
    propRuleId(val) {
      this.backupRuleId = val
      this.resetTemp()
    },
    propCheckRule(val) {
      this.checkRule = val
    }
  },
  created() {
    this.checkRule = this.propCheckRule
    this.backupRuleId = this.propRuleId
  },
  methods: {
    show() {
      this.checkRule = this.propCheckRule
      this.backupRuleId = this.propRuleId
      this.resetTemp()
      this.isShow = false
      this.showSlot = false
      this.dialogVisible = true
    },
    resetTemp() {
      const options = this.resData.filter(item => { return item.id === this.backupRuleId }) // 获取响应规则选项，并添加checked属性
      if (options.length > 0) {
        this.option = options[0]
        this.showSlot = false
      } else {
        this.option = {}
      }
    },
    // 选择的响应规则变更时，对勾选状态进行更新
    handleChange(value) {
      this.resetTemp()
    },
    // 响应规则前面的复选框是否选中
    handleCheck(value) {
      this.checkRule = !this.checkRule
      if (!value) {
        this.showSlot = false
      }
      this.backupRuleId = undefined
      this.resetTemp()
    },
    handleConfirm() {
      const valid = this.checkRule && !this.backupRuleId
      if (valid) {
        this.showSlot = true
      } else {
        this.dialogVisible = false
        this.$emit('setBackupRule', this.backupRuleId, this.checkRule ? 1 : 0)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .error-tip {
    line-height: initial;
    margin: 6px 0 0 20px;
    position: absolute;
    color:red;
  }
</style>
