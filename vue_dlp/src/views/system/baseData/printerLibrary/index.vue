<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="printerGroupTree"
        resizeable
        :default-expand-all="true"
        :data="printerGroupTreeData"
        :render-content="renderContent"
        @node-click="printerGroupTreeNodeCheckChange"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate2">
          {{ $t('pages.importFromTerm') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'531'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'532'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.printerNameOrDriverName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="printerList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="textMap[dialogStatus]"
        :visible.sync="dialogFormVisible"
        width="600px"
        @dragDialog="handleDrag"
      >
        <Form
          ref="dataForm"
          :rules="printerRules"
          :model="temp"
          label-position="right"
          label-width="100px"
          style="width: 500px; margin-left:20px;"
        >
          <FormItem :label="$t('pages.printName')" prop="printName">
            <el-input v-model="temp.printName" v-trim :maxlength="60"/>
          </FormItem>
          <FormItem :label="$t('table.driverName')" prop="driverName">
            <el-input v-model="temp.driverName" v-trim :maxlength="60"/>
          </FormItem>
          <FormItem :label="$t('pages.groupType')" prop="groupId">
            <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')">
              <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
            </el-select>
          </FormItem>
        </Form>
        <!-- <div style="margin-top: 15px;color: #3296FA;">
          <span>{{ $t('pages.printer_Msg6') }}</span><br><br>
          <span>{{ $t('pages.printer_Msg7') }}</span>
        </div> -->
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
            {{ $t('button.confirm') }}
          </el-button>
          <el-button @click="dialogFormVisible = false">
            {{ $t('button.cancel') }}
          </el-button>
        </div>
      </el-dialog>

      <!--从终端新增打印机-->
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="i18nConcatText(this.$t('table.printer'), 'create')"
        :visible.sync="dialogFormVisible2"
        width="800px"
        @dragDialog="handleDrag"
      >
        <div class="tree-container" style="height: 450px;">
          <strategy-target-tree ref="printerTargetTree" :showed-tree="['terminal']" @data-change="printerTargetNodeChange" />
        </div>
        <div class="table-container-dialog">
          <grid-table
            ref="printerSelectList"
            :height="400"
            :show-pager="false"
            :col-model="colModel2"
            :multi-select="true"
            :row-datas="allPrinters"
          />
          <div style="display: flex;align-items: center;margin-top: 10px;">
            <span>{{ $t('pages.groupType') }}：</span>
            <el-select v-model="printerGroupId" filterable :placeholder="$t('text.select')" style="width: 200px;margin-left: 6px;">
              <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="Number(item.dataId)"/>
            </el-select>
            <el-button :title="$t('pages.addType')" class="editBtn" style="margin-left: 5px;margin-top: 5px;" @click="handlePrinterGroupCreate"><svg-icon icon-class="add" /></el-button>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="submitting" type="primary" @click="createData2()">{{ $t('button.confirm') }}</el-button>
          <el-button @click="dialogFormVisible2 = false">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>
      <export-dlg ref="exportDlg" :group-tree-data="printerGroupTreeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>
      <import-dlg
        ref="importDlg"
        :title="title"
        :tip="$t('pages.printerInformation')"
        template="printer"
        :show-import-type="false"
        :show-import-way="true"
        :file-name="title"
        :upload-func="upload"
        @success="importEndFunc"
      />
      <edit-group-dlg
        ref="editGroupDlg"
        :title="$t('pages.printerGroup')"
        :group-tree-data="formTreeData"
        :add-func="createPrinterGroup"
        :update-func="updatePrinterGroup"
        :delete-func="deletePrinterGroup"
        :move-func="moveGroup"
        :edit-valid-func="getPrinterGroupByName"
        @addEnd="createNode"
        @updateEnd="updateNode"
        @deleteEnd="removeGroupEnd"
        @moveEnd="moveGroupEnd"
      />
      <delete-group-dlg
        ref="deleteGroupDlg"
        :title="textMap.delete"
        :dlg-title="title"
        :select-tree-data="formTreeData"
        :delete-group-and-data="deleteGroupAndData"
        :delete-func="deletePrinterGroup"
        :move-group-to-other="moveGroupToOther"
        @removeFunc="removeGroupEnd"
        @deleteEnd="removeGroupEnd"
        @refreshTableData="refreshTableData"
      ></delete-group-dlg>

      <batch-edit-page-dlg
        ref="batchDeleteDlg"
        :title="updateGroupForm ? i18nConcatText(this.$t('pages.printerGroup'), 'update') : i18nConcatText(this.$t('table.printer'), 'delete')"
        :edit-type="updateGroupForm ? 0 : 1"
        :delete-filter-name="$t('table.printer')"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @submitEnd="batchEditFunc"
      >
        <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
          <tree-select :data="printerGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
        </FormItem>
      </batch-edit-page-dlg>
    </div>

  </div>
</template>

<script>
import {
  getPrinterList,
  getTreeNode,
  createPrinterGroup,
  updatePrinterGroup,
  deletePrinterGroup,
  getPrinterGroupByName,
  moveGroup,
  countPrinterByGroupId,
  deleteGroupAndData,
  moveGroupToOther,
  createPrinter,
  updatePrinter,
  deletePrinter,
  batchUpdateGroup,
  batchUpdateAllGroup,
  exportExcel,
  getPrinterByName,
  createOrUpdatePrinter
} from '@/api/system/baseData/printerLibrary'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg';
import ExportDlg from '@/views/common/export'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import ImportDlg from '@/views/common/import'
import { findNodeLabel } from '@/utils/tree'
import request from '@/utils/request';
export default {
  name: 'PrinterLibrary',
  components: { EditGroupDlg, DeleteGroupDlg, BatchEditPageDlg, ExportDlg, ImportDlg },
  data() {
    return {
      colModel: [
        { prop: 'printName', label: 'printName', width: '150', sort: 'custom' },
        { prop: 'driverName', label: 'driverName', width: '150', sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.groupFormatter },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '150',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'computer', label: 'source', sort: true, width: '1' },
        { prop: 'printerName', label: 'printName', sort: true, width: '1' },
        { prop: 'driverName', label: 'driverName', sort: true, width: '1' }
      ],
      printerRules: {
        printName: [
          // { required: true, message: this.$t('pages.printer_Msg1'), trigger: 'blur' },
          { trigger: 'blur', validator: this.printNameValidator }
        ],
        driverName: [
          // { required: false, trigger: 'blur', validator: this.driverNameValidator }
          { validator: this.driverNameValidator, trigger: 'blur' }
        ],
        groupId: [
          // { required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }
        ]
      },
      printerGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.printerLibrary'), parentId: '', children: [] }],
      formTreeData: [],
      printerLibTreeData: [],
      treeNodeType: 'G',
      showTree: true,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        printName: '',
        driverName: '',
        groupId: ''
      },
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: ''
      },
      deleteable: false,
      addBtnAble: false,
      textMap: {
        update: this.i18nConcatText(this.$t('table.printer'), 'update'),
        create: this.i18nConcatText(this.$t('table.printer'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.printerGroup'), 'delete')
      },
      title: this.$t('pages.printerInformation'),
      treeSelectNode: [],
      dialogFormVisible: false,
      dialogFormVisible2: false,
      dialogStatus: '',
      submitting: false,
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      termPrinters: [], // 终端的打印机
      printerGroupId: undefined
    }
  },
  computed: {
    printerGroupTree: function() {
      return this.$refs['printerGroupTree']
    },
    gridTable() {
      return this.$refs['printerList']
    },
    allPrinters() {
      const printers = [...this.termPrinters]
      return printers.map((item, index) => { item.id = index + 2; return item })
    }
  },
  activated() {
    this.loadPrinterTree()
    this.gridTable && this.gridTable.execRowDataApi(this.query)
  },
  created() {
    this.loadPrinterTree()
  },
  methods: {
    createPrinterGroup,
    updatePrinterGroup,
    deletePrinterGroup,
    getPrinterGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    handleDrag() {

    },
    importEndFunc() {
      this.loadPrinterTree()
      this.handleFilter()
    },
    upload(data) {
      return request.post('/printer/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    createNode(data) {
      this.printerGroupTree.addNode(this.dataToTreeNode(data))
      this.printerGroupId = Number.parseInt(data.id)
    },
    updateNode(data) {
      this.printerGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.printerGroupTree.findNode(this.printerGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.printerGroupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.$refs.printerList.execRowDataApi()
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.printerGroupTreeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          if ((tempData.printName == '' || tempData.printName == null || tempData.printName == undefined) && (tempData.driverName == '' || tempData.driverName == null || tempData.driverName == undefined)) {
            this.$message({
              type: 'error',
              duration: 2000,
              message: this.$t('pages.printer_Msg')
            })
            this.submitting = false
            return
          }
          updatePrinter(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$refs.printerList.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          if ((this.temp.printName == '' || this.temp.printName == null || this.temp.printName == undefined) && (this.temp.driverName == '' || this.temp.driverName == null || this.temp.driverName == undefined)) {
            this.$message({
              type: 'error',
              duration: 2000,
              message: this.$t('pages.printer_Msg')
            })
            this.submitting = false
            return
          }
          createPrinter(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$refs.printerList.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    printNameValidator(rule, value, callback) {
      if ((this.temp.driverName == '' || this.temp.driverName == null || this.temp.driverName == undefined) && 
          (value == '' || value == null || value == undefined)) {
        callback(new Error(this.$t('pages.printer_Msg')))
      } else {
        var obj = {
          printName: this.temp.printName,
          driverName: ''
        }
        if (this.temp.driverName != '') {
          obj.driverName = this.temp.driverName
        }
        getPrinterByName(obj).then(res => {
          const stg = res.data
          if (stg && stg.id != this.temp.id) {
            // 存在
            callback(new Error(this.$t('pages.printer_Msg3')))
          } else {
            // 不存在
            this.$refs.dataForm.clearValidate()
            callback()
          }
        })
      }
    },
    driverNameValidator(rule, value, callback) {
      if ((this.temp.printName == '' || this.temp.printName == null || this.temp.printName == undefined) && 
          (value == '' || value == null || value == undefined)) {
        callback(new Error(this.$t('pages.printer_Msg')))
      } else {
        var obj = {
          printName: this.temp.printName,
          driverName: ''
        }
        if (this.temp.driverName != '') {
          obj.driverName = this.temp.driverName
        }
        getPrinterByName(obj).then(res => {
          const stg = res.data
          if (stg && stg.id != this.temp.id) {
            // 存在
            callback(new Error(this.$t('pages.printer_Msg3')))
          } else {
            // 不存在
            this.$refs.dataForm.clearValidate()
            callback()
          }
        })
      }
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPrinterList(searchQuery)
    },
    handleUpdate(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.printerList.execRowDataApi(this.query)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.$refs.printerList.getSelectedIds())
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        params.filterUsedPrinter = params.filterUsed
        deletePrinter(params).then(respond => {
          this.$refs.printerList.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.$refs.printerList.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.$refs.printerList.getSelectedDatas()
      const total = this.$refs.printerList.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleCreate2() {
      this.changeTreeSelectNode()
      this.printerGroupId = Number(this.query.groupId)
      this.dialogFormVisible2 = true
      this.$refs.printerTargetTree && this.$refs.printerTargetTree.clearFilter()
      this.$nextTick(() => {
        this.$refs.printerSelectList.clearSelection()
      })
    },
    handleMoving() {
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.$refs.printerList.getSelectedDatas()
      const total = this.$refs.printerList.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.printerGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handlePrinterGroupCreate(data) {
      console.log('groupId: ' + JSON.stringify(this.printerGroupId))
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handlePrinterGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    removeNode(data) {
      countPrinterByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.printerGroupTree.findNode(this.printerGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.printerGroupTree.setCurrentKey(nodeData.id)
        this.printerGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.$refs.printerList.clearRowData()
      this.$refs.printerList.execRowDataApi()
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handlePrinterGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handlePrinterGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    printerGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.$refs.printerList.execRowDataApi(this.query)
    },
    loadPrinterTree: function(groupId) {
      return getTreeNode().then(respond => {
        this.printerGroupTreeData[0].children = respond.data
        this.formTreeData = this.loadTypeTreeExceptRoot()
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
        this.$nextTick(() => {
          this.printerGroupTree.selectCurrentNode(this.treeNodeType + (groupId || '0'))
        })
      })
    },
    loadTypeTreeExceptRoot() {
      return this.printerGroupTreeData[0].children
    },
    printerTargetNodeChange: function(tabName, data) {
      if (data) {
        if (data.type == 1) {
          const termId = data.dataId
          const termName = data.label
          this.termPrinters.splice(0)
          this.$socket.sendToUser(termId, '/getPrinter', termId, (respond, handle) => {
            handle.close()
            // 判断终端id是否等于当前请求的终端id，防止用户连续点击不同终端导致数据加载出错
            if (respond.data && respond.data.termId == termId && respond.data.printList) {
              respond.data.printList.forEach(item => {
                const p = {
                  computer: termName,
                  ctrlCode: 1,
                  printerName: item.printName,
                  driverName: item.driverName
                }
                this.termPrinters.push(p)
              })
            }
          }, (handle) => {
            handle.close()
            this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
          })
        }
      }
    },
    async createData2() {
      this.submitting = true
      const datas = this.$refs.printerSelectList.getSelectedDatas()
      if (datas.length === 0) {
        this.submitting = false
        this.$message({
          message: this.$t('pages.PrintSet_Msg10'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (this.printerGroupId == undefined) {
        this.submitting = false
        this.$message({
          message: this.$t('pages.printer_Msg5'),
          type: 'error',
          duration: 2000
        })
        return
      }
      for (let i = 0; i < datas.length; i++) {
        const printerObj = {
          printName: datas[i].printerName,
          driverName: datas[i].driverName,
          groupId: this.printerGroupId
        }
        await createOrUpdatePrinter(printerObj)
      }
      this.submitting = false
      this.dialogFormVisible2 = false
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.insertSuccess'),
        type: 'success',
        duration: 2000
      })
      this.gridTable.execRowDataApi()
    }
  }
}
</script>
