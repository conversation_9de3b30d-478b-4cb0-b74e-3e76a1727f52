<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
      </div>
      <grid-table
        ref="strategyTypeTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left: 20px;"
      >
        <FormItem :label="$t('pages.number')" prop="number">
          <el-input v-model="temp.number" :maxlength="9" @keyup.native="matchNum('number')"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.chineseName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="20"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.stgKeyValue')" prop="strategyKey">
          <el-input v-model="temp.strategyKey" :maxlength="30" @keyup.native="matchKey('strategyKey')"></el-input>
        </FormItem>
        <FormItem :label="$t('table.dataType')" prop="type">
          <el-select v-model="temp.type" :placeholder="$t('text.select')">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.stgType')" prop="groupType">
          <el-select v-model="temp.groupType" :placeholder="$t('text.select')">
            <el-option
              v-for="item in groupTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.isActive')" prop="active">
          <el-switch
            v-model="temp.active"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
        </FormItem>
        <FormItem :label="$t('pages.effectiveScope')" prop="scopeArr">
          <el-checkbox-group v-model="temp.scopeArr">
            <el-checkbox :label="1">{{ $t('pages.terminal') }}</el-checkbox>
            <el-checkbox :label="2">{{ $t('pages.terminalUser') }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="$t('pages.sortNo')" prop="sortNumber">
          <el-input v-model="temp.sortNumber" maxlength="9" @keyup.native="matchNum('sortNumber')"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {
  getPage, createData, updateData, deleteData
} from '@/api/system/baseData/strategyType'

export default {
  name: 'StrategyType',
  data() {
    return {
      colModel: [
        { prop: 'number', label: 'number', width: '100' },
        { prop: 'name', label: this.$t('pages.chineseName'), width: '200' },
        { prop: 'strategyKey', label: this.$t('pages.stgKeyValue'), width: '200' },
        { prop: 'type', label: 'dataType', width: '100', formatter: this.typeFormatter },
        { prop: 'groupType', label: 'stgType', width: '100', formatter: this.groupTypeFormatter },
        { prop: 'active', label: 'isActive', width: '100', formatter: (row, data) => { return row.active == 1 ? this.$t('pages.takeEffect') : this.$t('pages.inoperative') } },
        { prop: 'usedScope', label: this.$t('pages.effectiveScope'), width: '200', formatter: this.usedScopeFormatter },
        {
          prop: 'createdTime',
          label: 'operate',
          type: 'button',
          fixed: 'right',
          fixedWidth: '100',
          buttons: [{
            label: 'edit', click: this.handleUpdate
          }
          ]
        }
      ],
      deleteable: false,
      keyword: '',
      typeOptions: [
        { label: this.$t('pages.businessStrategy'), value: 1 },
        { label: this.$t('pages.basicData'), value: 2 }
      ],
      groupTypeOptions: [
        { label: this.$t('pages.dataSecurityStrategy'), value: 1 },
        { label: this.$t('pages.behavioralStrategy'), value: 2 },
        { label: this.$t('pages.contentStg'), value: 3 }
      ],
      temp: { // 表单字段
        id: undefined,
        number: undefined,
        name: '',
        strategyKey: '',
        type: 1,
        groupType: 1,
        active: 1,
        usedScope: 3,
        scopeArr: [1, 2],
        sortNumber: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      query: {
        keyword: ''
      },
      textMap: {
        update: this.i18nConcatText(this.$t('pages.stgTypeConfig'), 'update'),
        create: this.i18nConcatText(this.$t('pages.stgTypeConfig'), 'create')
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        number: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        sortNumber: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        strategyKey: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        scopeArr: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs.strategyTypeTable
    }
  },
  methods: {
    rowDataApi: function(option) {
      const objeTemp = Object.assign({}, this.query, option)
      return getPage(objeTemp)
    },
    handleFilter() {
      this.gridTable.execRowDataApi()
    },
    handleDrag() {
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        number: undefined,
        name: '',
        strategyKey: '',
        type: 1,
        groupType: 1,
        active: 1,
        usedScope: 3,
        scopeArr: [1, 2],
        sortNumber: undefined
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.temp = Object.assign(this.temp, row) // copy obj
      this.temp.scopeArr = []
      let a = this.temp.usedScope
      if ((a - 2) >= 0) {
        a = a - 2
        this.temp.scopeArr.push(2)
      }
      if ((a - 1) >= 0) {
        a = a - 1
        this.temp.scopeArr.push(1)
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
    },
    handleExport() {
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.usedScope = 0
          this.temp.scopeArr.forEach(item => {
            this.temp.usedScope += item
          })
          createData(this.temp).then(() => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.usedScope = 0
          this.temp.scopeArr.forEach(item => {
            this.temp.usedScope += item
          })
          updateData(this.temp).then(() => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    typeFormatter(row, data) {
      const map = {
        1: this.$t('pages.businessStrategy'),
        2: this.$t('pages.basicData')
      }
      return map[data]
    },
    groupTypeFormatter(row, data) {
      const map = {
        1: this.$t('pages.dataSecurityStrategy'),
        2: this.$t('pages.behavioralStrategy'),
        3: this.$t('pages.contentStg')
      }
      if (row.type == 1) {
        return map[data]
      } else {
        return ''
      }
    },
    usedScopeFormatter(row, data) {
      const map = {
        1: this.$t('pages.terminal'),
        2: this.$t('pages.terminalUser'),
        3: this.$t('pages.terminal') + '、' + this.$t('pages.terminalUser')
      }
      return map[data]
    },
    matchNum(field) {
      if (isNaN(this.temp[field])) {
        this.temp[field] = this.temp[field].replace(/[^\d]/g, '')
      }
    },
    matchKey(field) {
      if (isNaN(this.temp[field])) {
        this.temp[field] = this.temp[field].replace(/[^\w_]/g, '')
      }
    }
  }
}
</script>
