<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">
          {{ $t('button.add') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearabl e placeholder="请输入机器人名称" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="appRobotList" :col-model="colModel" :row-data-api="rowDataApi" :multi-select="false" />
    </div>
    <!-- 编辑/添加弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dialogStatus==='create'?'添加机器人':'编辑机器人'"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <Form ref="appRobotForm" :model="form" :rules="rules" label-position="right" label-width="100px">
        <input value="" class="autocomplete" />
        <input type="password" value="" class="autocomplete">
        <FormItem label="机器人名称" prop="name">
          <el-input v-model="form.name" v-trim :maxlength="64"></el-input>
        </FormItem>
        <!--
        <FormItem label="类型" prop="type">
          <el-select v-model="form.type">
            <el-option label="钉钉" :value="1" />
            <el-option label="企业微信" :value="2" />
          </el-select>
        </FormItem>
        -->

        <FormItem label="应用ID" prop="appId">
          <el-input v-model="form.appId" v-trim :maxlength="128"></el-input>
        </FormItem>
        <FormItem label="应用KEY" prop="appKey">
          <el-input v-model="form.appKey" v-trim type="text" :maxlength="128"></el-input>
        </FormItem>
        <FormItem label="应用密钥" prop="appSecret">
          <el-input v-model="form.appSecret" v-trim type="password" :maxlength="256"></el-input>
        </FormItem>
        <FormItem label="状态" prop="status">
          <el-select v-model="form.status">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPage, addAppRobot, updateAppRobot, deleteAppRobot } from '@/api/system/baseData/appRobot';

export default {
  name: 'AppUser',
  data() {
    return {
      colModel: [
        { prop: 'name', label: '机器人名称', width: '150', sort: 'custom', fixed: true },
        { prop: 'appId', label: '应用ID', width: '300' },
        { prop: 'status', label: '状态', width: '100', formatter: this.formatterStatus },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'delete', click: this.handleDelete }
          ]
        }
      ],
      submitting: false,
      dialogVisible: false,
      dialogStatus: 'create',
      query: {
        page: 1,
        name: ''
      },
      form: {
        id: null,
        name: '',
        type: 1,
        appId: '',
        appKey: '',
        appSecret: '',
        status: 1
      },
      rules: {
        name: [{ required: true, message: '请输入机器人名称', trigger: 'blur' }],
        appId: [{ required: true, message: '请输入应用ID', trigger: 'blur' }],
        appKey: [{ required: true, message: '请输入应用KEY', trigger: 'blur' }],
        appSecret: [{ required: true, message: '请输入应用密钥', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleFilter() {
      this.query.page = 1
      this.$refs['appRobotList'].execRowDataApi(this.query)
    },
    handleAdd() {
      this.dialogStatus = 'create'
      this.resetForm()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['appRobotForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.resetForm()
      this.dialogVisible = true
      this.form = JSON.parse(JSON.stringify(row))
      this.$nextTick(() => {
        this.$refs['appRobotForm'].clearValidate()
      })
    },
    resetForm() {
      this.form = {
        id: null,
        name: '',
        type: 1,
        appId: '',
        appKey: '',
        appSecret: '',
        status: 1 }
    },
    handleDelete(row) {
      this.$confirm('确定要删除该机器人吗？', '提示', { type: 'warning' }).then(() => {
        // 执行删除
        deleteAppRobot(row.id).then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.$refs['appRobotList'].execRowDataApi();
          this.refreshTagOptions()
        })
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    createData() {
      this.submitting = true
      this.$refs['appRobotForm'].validate(valid => {
        if (valid) {
          addAppRobot(this.form).then(res => {
            this.submitting = false
            this.dialogVisible = false
            this.$refs['appRobotList'].execRowDataApi()
            this.refreshTagOptions()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['appRobotForm'].validate(valid => {
        if (valid) {
          const tempData = Object.assign({}, this.form)
          updateAppRobot(tempData).then(res => {
            this.submitting = false
            this.dialogVisible = false
            this.$refs['appRobotList'].execRowDataApi()
            this.refreshTagOptions()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatterStatus(row, data) {
      return data === 1 ? '启用' : '禁用';
    },
    refreshTagOptions() {
      this.$emit('refreshTagOption')
    }
  }
}
</script>

<style scoped>
</style>
