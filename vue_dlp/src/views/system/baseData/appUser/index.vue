<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane label="第三方人员管理" name="appUser">
        <app-user ref="appUser" />
      </el-tab-pane>
      <el-tab-pane :lazy="true" label="第三方机器人管理" name="appRobot">
        <app-robot ref="appRobot" @refreshTagOption="refreshTagOptions"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import AppUser from './appUser'
import AppRobot from './appRobot'

export default {
  name: 'AppUserRobot',
  components: { AppUser, AppRobot },
  data() {
    return {
      activeName: 'appUser'
    }
  },
  // beforeRouteEnter(to, from, next) {
  //   next(vm => {
  //     vm.activeName = to.query.tabName || vm.activeName
  //     vm.$router.push({ query: {}})
  //   })
  // },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    tabClick(pane, event) {
      // if (this.activeName === 'UsbApprovalAccess') {
      //   this.$refs['UsbApprovalAccess'].checkedUsbGroup()
      // } else {
      //   this.$refs.UsbLib.loadReceiverTree()
      //   this.$refs.UsbLib.handleRefresh()
      // }
    },
    refreshTagOptions() {
      this.$refs['appUser'].getRobotOptions()
    }
  }
}
</script>

<style lang='scss' scoped>
.module-form{
  margin-left: 210px;
  height: 100%;
  overflow: auto;
  .el-tabs{
    height: calc(100% - 40px);
  }
  .el-tab-pane{
    padding: 0 10px 10px;
  }
}
.app-container .tree-container.hidden+.module-form{
  margin-left: 0;
}
</style>
