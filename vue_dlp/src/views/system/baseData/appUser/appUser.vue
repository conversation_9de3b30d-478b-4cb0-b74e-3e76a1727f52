<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">
          {{ $t('button.add') }}
        </el-button>
        <!--<el-button type="success" icon="el-icon-plus" size="mini" @click="handleQuickAdd">快速添加</el-button>-->
        <el-button :disabled="!deleteable" icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.keyword" v-trim clearable placeholder="请输入姓名或邮箱" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="appUserList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <!-- 编辑/添加弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dialogStatus==='create'?'添加人员':'编辑人员'"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <Form ref="appUserForm" :model="form" :rules="rules" label-position="right" label-width="100px">
        <FormItem label="姓名" prop="realname">
          <el-input v-model="form.realname" v-trim :maxlength="64"></el-input>
        </FormItem>
        <FormItem label="标签" prop="tag">
          <el-select
            v-model="form.tag"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="item in tagOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </FormItem>
        <FormItem label="邮箱" prop="mail">
          <el-input v-model="form.mail" v-trim :maxlength="128"></el-input>
        </FormItem>
        <FormItem label="钉钉机器人" prop="dingtalkRobotId">
          <el-select v-model="form.dingtalkRobotId">
            <el-option v-for="item in robotOptions" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </FormItem>
        <el-row>
          <el-col :span="10">
            <FormItem label="钉钉用户ID" prop="dingtalkUserId">
              <el-input v-model="form.dingtalkUserId" v-trim :maxlength="64"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="6" :offset="1">
            <el-input v-model="mobile" v-trim :maxlength="20" clearable placeholder="请输入手机号"></el-input>
          </el-col>
          <el-col :span="1">
            <div style="margin-top: 5px; margin-left:5px;">
              <el-tooltip class="item" placement="top">
                <div slot="content">手机号仅用于获取钉钉用户ID</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="2">
            <el-button @click="getByMobile">获取钉钉用户ID</el-button>
          </el-col>
        </el-row>
        <FormItem label="状态" prop="status">
          <el-select v-model="form.status">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <!-- 快速添加弹窗（可后续扩展为批量导入等） -->
    <el-dialog title="快速添加" :visible.sync="quickDialogVisible" width="400px">
      <div>（此处可扩展为批量导入、粘贴等功能）</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="quickDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPage, addAppUser, updateAppUser, deleteAppUser, getAllTags, getDingTalkUserIdByMobile } from '@/api/system/baseData/appUser';
import { listForOptions } from '@/api/system/baseData/appRobot';

export default {
  name: 'AppUser',
  data() {
    return {
      colModel: [
        { prop: 'realname', label: '姓名', width: '100', sort: 'custom', fixed: true },
        { prop: 'tag', label: '标签', width: '200', formatter: this.formatterTag },
        { prop: 'mail', label: '邮箱', width: '100' },
        { prop: 'dingtalkUserId', label: '钉钉用户ID', width: '100' },
        { prop: 'dingtalkRobotName', label: '钉钉机器人', width: '150' },
        { prop: 'status', label: '状态', width: '150', formatter: this.formatterStatus },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'delete', click: this.handleDelete }
          ]
        }
      ],
      robotOptions: [],
      dialogVisible: false,
      quickDialogVisible: false,
      dialogStatus: 'create',
      query: {
        page: 1,
        keyword: ''
      },
      tagOptions: [],
      deleteable: false,
      form: {
        id: null,
        realname: '',
        tag: [],
        mail: '',
        dingtalkUserId: '',
        dingtalkRobotId: '',
        status: 1
      },
      mobile: '',
      rules: {
        realname: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getRobotOptions()
    this.getTagOptions()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.$refs['appUserList'].execRowDataApi(this.query)
    },
    handleAdd() {
      this.dialogStatus = 'create'
      this.resetForm()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['appUserForm'].clearValidate()
        this.mobile = ''
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.resetForm()
      this.dialogVisible = true
      const tempData = JSON.parse(JSON.stringify(row))
      const tags = tempData.tag
      const tagArr = []
      if (tags) {
        tags.split(',').forEach(tag => {
          tagArr.push(tag)
        })
      }
      this.form = { ...tempData, tag: tagArr }
      this.$nextTick(() => {
        this.$refs['appUserForm'].clearValidate()
        this.mobile = ''
      })
    },
    resetForm() {
      this.form = {
        id: null,
        realname: '',
        tag: [],
        mail: '',
        dingtalkUserId: '',
        dingtalkRobotId: '',
        status: 1
      }
    },
    handleDelete(row) {
      const ids = row.id || this.$refs['appUserList'].getSelectedDatas().map(data => data.id).join(',')
      this.$confirm('确定要删除人员吗？', '提示', { type: 'warning' }).then(() => {
        // 执行删除
        deleteAppUser(ids).then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.$refs['appUserList'].execRowDataApi();
          this.refreshTag()
        })
      })
    },
    getRobotOptions() {
      listForOptions().then(res => {
        this.robotOptions = res.data
      })
    },
    getTagOptions() {
      getAllTags().then(res => {
        this.tagOptions = res.data
      })
    },
    // handleSave() {
    //   this.$refs.appUserForm.validate(valid => {
    //     if (!valid) return
    //     // 新标签自动加入tagOptions
    //     this.form.tag.forEach(t => {
    //       if (t && !this.tagOptions.includes(t)) this.tagOptions.push(t)
    //     })
    //     // 保存时tag转为字符串
    //     const saveData = { ...this.form, tag: this.form.tag.join(',') }
    //     if (this.dialogStatus === 'create') {
    //       saveData.id = Date.now()
    //       this.userList.push(saveData)
    //       this.$message.success('添加成功')
    //     } else {
    //       const idx = this.userList.findIndex(item => item.id === this.form.id)
    //       if (idx !== -1) this.userList.splice(idx, 1, saveData)
    //       this.$message.success('编辑成功')
    //     }
    //     this.dialogVisible = false
    //   })
    // },
    createData() {
      this.submitting = true
      this.$refs['appUserForm'].validate(valid => {
        if (valid) {
          const validateResult = this.validate()
          if (!validateResult) {
            return
          }
          // 保存时tag转为字符串
          const saveData = { ...this.form, tag: this.form.tag.join(',') }
          addAppUser(saveData).then(res => {
            this.submitting = false
            this.dialogVisible = false
            this.$refs['appUserList'].execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.refreshTag()
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['appUserForm'].validate(valid => {
        if (valid) {
          const updateData = { ...this.form, tag: this.form.tag.join(',') }
          updateAppUser(updateData).then(res => {
            this.submitting = false
            this.dialogVisible = false
            this.$refs['appUserList'].execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.refreshTag()
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    validate() {
      // 校验业务
      if (this.form.dingtalkRobotId && !this.form.dingtalkUserId) {
        this.$message({
          message: '请输入钉钉用户ID',
          type: 'error',
          duration: 2000
        })
        return false;
      }
      if (!this.form.dingtalkRobotId && this.form.dingtalkUserId) {
        this.$message({
          message: '请选择钉钉机器人',
          type: 'error',
          duration: 2000
        })
        return false;
      }
      if (!this.form.mail && !this.form.dingtalkUserId) {
        this.$message({
          message: '邮件或钉钉至少配置一种',
          type: 'error',
          duration: 2000
        })
        return false;
      }
      return true;
    },
    // 刷新标签数据
    refreshTag() {
      this.getTagOptions()
    },
    // 刷新机器人数据
    refreshRobot() {},
    handleQuickAdd() {
      this.quickDialogVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    formatterStatus(row, data) {
      return data === 1 ? '启用' : '禁用';
    },
    formatterTag(row, data) {
      if (!data) return ''
      return data.split(',').map(tag => `<el-tag>${tag}</el-tag>`).join(' ')
    },
    getByMobile() {
      if (!this.mobile) {
        this.$message({
          message: '请输入手机号',
          type: 'error',
          duration: 2000
        })
        return
      }
      if (!this.form.dingtalkRobotId) {
        this.$message({
          message: '请选择钉钉机器人',
          type: 'error',
          duration: 2000
        })
        return;
      }
      const param = { 'robotId': this.form.dingtalkRobotId, 'mobile': this.mobile }
      getDingTalkUserIdByMobile(param).then(res => {
        this.form.dingtalkUserId = res.data
      })
    }
  }
}
</script>

<style scoped>
</style>
