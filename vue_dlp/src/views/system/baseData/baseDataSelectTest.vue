<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="() => { this.$refs.mailSelectDlg.show() }">
          {{ $t('pages.selectionBox', { info: $t('pages.mailLibrary' )}) }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="() => { this.$refs.urlSelectDlg.show() }">
          {{ $t('pages.selectionBox', { info: $t('pages.urlLibrary' )}) }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="() => { this.$refs.usbSelectDlg.show() }">
          {{ $t('pages.selectionBox', { info: $t('pages.usbDevice' )}) }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="() => { this.$refs.appSelectDlg.show() }">
          {{ $t('pages.selectionBox', { info: $t('pages.appLibrary' )}) }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="() => { this.$refs.waterSelectDlg.show(1) }">
          {{ $t('pages.selectionBox', { info: $t('pages.waterMarkLib1' )}) }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="() => { this.$refs.waterSelectDlg.show(2) }">
          {{ $t('pages.selectionBox', { info: $t('pages.waterMarkLib2' )}) }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="() => { this.$refs.waterSelectDlg.show(3) }">
          {{ $t('pages.selectionBox', { info: $t('pages.waterMarkLib3' )}) }}
        </el-button>
      </div>
    </div>

    <mail-select-dlg ref="mailSelectDlg"/>
    <usb-select-dlg ref="usbSelectDlg"/>
    <app-select-dlg ref="appSelectDlg"/>
    <url-select-dlg ref="urlSelectDlg"/>
    <water-select-dlg ref="waterSelectDlg"/>
  </div>
</template>

<script>
import MailSelectDlg from '@/views/system/baseData/mailLibrary/mailSelectDlg';
import UsbSelectDlg from '@/views/system/baseData/usbDevice/usbSelectDlg';
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import UrlSelectDlg from '@/views/system/baseData/urlLibrary/urlSelectDlg';
import WaterSelectDlg from '@/views/system/baseData/waterMarkLib/waterSelectDlg';

export default {
  name: 'BaseDataSelectTest',
  components: { WaterSelectDlg, UrlSelectDlg, AppSelectDlg, UsbSelectDlg, MailSelectDlg },
  data() {
    return {

    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  methods: {

  }
}
</script>
