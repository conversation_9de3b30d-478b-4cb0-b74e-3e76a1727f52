<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="receiverGroupTree"
        resizeable
        :default-expand-all="true"
        :data="receiverGroupTreeData"
        @node-click="receiverGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <!-- <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button> -->
        <el-button type="primary" size="mini" @click="handleLevel">
          {{ $t('table.alarmLevel') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.alarmTypeDesc"
            v-trim
            clearable
            :placeholder="$t('pages.templateType')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.templateNameOrContent')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="receiverList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="false"
        :row-datas="tableData"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <!-- 新增修改对话框 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 700px; margin-left:20px;"
      >
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.templateType')" prop="alarmType">
              <span>
                {{ temp.templateType }}
              </span>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.groupType')" prop="groupId">
              <el-select v-model="temp.groupId" filterable :disabled="true" :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="parseInt(item.dataId)"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.templateLang')" prop="groupId">
              <el-select v-model="langName" :placeholder="$t('text.select')" @change="changeLang()">
                <el-option v-for="item in langList" :key="item.code" :label="item.name" :value="item.name"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col v-if="temp.sstType == 0 && temp.tableType == 1" :span="12">
            <FormItem :label="$t('pages.level')" prop="alarmLevel">
              <el-select v-model="temp.alarmLevel" style="width: 150px;">
                <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem label-width="0">
          <fieldset>
            <el-dropdown placement="bottom-start" @command="insertParamName">
              <el-button>
                {{ $t('pages.insertVariable') }}<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <span v-if="errorMsg" style="color: red;font-size: smaller;margin-left: 20px;">{{ errorMsg }}</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in paramList" :key="item.id" :command="item.paramName">
                  {{ item.paramName }}
                  <span v-if="item.remark">({{ item.remark }})</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <legend>{{ $t('pages.modifyTemplateContent') }}</legend>
            <el-input
              ref="contentArea"
              v-model="alarmTemplatefind.content"
              type="textarea"
              resize="none"
              :rows="13"
              :maxlength="maxLength"
              show-word-limit
              @change="() => { errorMsg = '' }"
            >
            </el-input>
          </fieldset>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleResetTemplate">
          {{ $t('button.reset') }}
        </el-button>
        <el-button type="primary" :loading="submitting" @click="handleSaveTemplate">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 告警级别名称对话框 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="levelFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('table.alarmLevel') }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.alarmLevelNotice">
              <br slot="br"/>
              <span slot="sysMgr">{{ $t('pages.sysManageLibrary') }}</span>
              <span slot="termMgr">{{ $t('pages.terminalManageLibrary') }}</span>
              <span slot="termOp">{{ $t('pages.terminalBehaviorLibrary') }}</span>
              <span slot="netOp">{{ $t('pages.networkBehaviorLibrary') }}</span>
              <span slot="encData">{{ $t('pages.dataSecurityLibrary') }}</span>
              <span slot="labelData">{{ $t('pages.labelAlarmLibrary') }}</span>
              <span slot="numData">{{ $t('pages.outFileNumAlarmLibrary') }}</span>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 700px; margin-left:20px;"
      >
        <FormItem :label="$t('pages.language')" label-width="45px" :extra-width="{ en: 35 }" style="margin-top: 0px;margin-bottom: 10px;">
          <el-select v-model="i18nTabName" style="width: 110px;" @change="reloadAllTable">
            <el-option value="zh_CN" :label="$t('pages.zh_CN')"></el-option>
            <el-option value="zh_TW" :label="$t('pages.zh_TW')"></el-option>
            <el-option value="en_US" :label="$t('pages.English')"></el-option>
          </el-select>
          <span v-if="errorMsg" style="color: red;font-size: smaller;margin-left: 20px;">{{ errorMsg }}</span>
        </FormItem>
        <div class="table-container">
          <grid-table
            ref="encTable"
            :height="182"
            :show-pager="false"
            :col-model="levelColModel"
            :multi-select="false"
            :row-data-api="() => { return getRows(i18nTabName) }"
          />
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="handleSaveLevel">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="levelFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :deleteable="false"
      :select-tree-data="formTreeData"
      :delete-func="deleteAlarmTemplateGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.alaramTemplateGroup')"
      :group-tree-data="formTreeData"
      :add-func="createAlarmTemplateGroup"
      :update-func="updateAlarmTemplateGroup"
      :delete-func="deleteAlarmTemplateGroup"
      :move-func="moveGroup"
      :edit-valid-func="getAlarmTemplateGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? i18nConcatText(this.$t('pages.templateGroup'), 'update') : i18nConcatText(this.$t('pages.mailInfo'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')">
        <tree-select :data="receiverGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>
  </div>
</template>

<script>
// import { Boot } from '@wangeditor/editor'
// import MarcoMenu from './MarcoMenu.js'
// const menu1Conf = {
//   key: 'marco-menu',
//   factory() {
//     return new MarcoMenu()
//   }
// }
// Boot.registerMenu(menu1Conf)

import { exportExcel } from '@/api/system/baseData/mailLibrary'
import {
  batchUpdateAllGroup,
  batchUpdateGroup,
  countChildByGroupId,
  createAlarmTemplate,
  createAlarmTemplateGroup,
  deleteAlarmTemplate,
  deleteAlarmTemplateGroup,
  deleteGroupAndData,
  getAlarmTemplateGroupByName,
  getAlarmTemplatePage,
  getDetailAndLanguage,
  getDetailList,
  getGroupTreeNode,
  getInitTemplate,
  getParamList,
  moveGroup,
  moveGroupToOther,
  saveTemplate,
  updateAlarmTemplate,
  updateAlarmTemplateGroup
} from '@/api/system/baseData/alarmTemplate'
import { getAlarmLevelList, getAlarmLevelDict, updateAlarmLevel } from '@/api/system/baseData/alarmLevel'
import { getDictLabel } from '@/utils/dictionary'
import { findNodeLabel } from '@/utils/tree'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

function getTextFromHtml(html) {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText || '';
}

export default {
  name: 'AlarmTemplate',
  components: { BatchEditPageDlg, DeleteGroupDlg, EditGroupDlg },
  data() {
    return {
      editor: null,
      contentDialogFormVisible: false,
      errorMsg: '',
      toolbarConfig: {
        insertKeys: {
          index: 0,
          keys: ['marco-menu']
        }
      },
      editorConfig: { placeholder: this.$t('pages.editorPlaceholder') },
      mode: 'default', // or 'simple'
      colModel: [
        { prop: 'templateType', label: this.$t('pages.templateType'), width: '100', sort: 'custom', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '100', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'alarmLevel', label: 'alarmLevel', width: '100', sort: 'custom', formatter: this.alarmLevelFormatter },
        { prop: 'remark', label: 'alarmContent', width: '200', sort: 'custom', formatter: this.contentFormatter },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '80',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      detailColModel: [
        { prop: 'lang', label: this.$t('pages.templateLang'), width: '100', fixed: true, formatter: this.langFormatter },
        { prop: 'content', label: this.$t('pages.templateContent'), width: '200', fixed: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdateDetail }
          ]
        }
      ],
      stateOptions: [{ 'id': 0, 'name': this.$t('text.normal') }, { 'id': 1, 'name': this.$t('text.disable') }],
      query: { // 查询条件
        page: 1,
        alarmTypeDesc: '',
        searchInfo: '',
        groupId: undefined
      },
      langList: [
        { name: '中文（简体）', code: 'zh_CN' },
        { name: '中文（繁體）', code: 'zh_TW' },
        { name: 'English', code: 'en_US' }
      ],
      updateTemplateId: -1,
      updateTemplateTableType: 1,
      templateLanguageContent: '',
      alarmTemplatefind: {},
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      langName: '中文（简体）', // 修改告警模板时，设置语言选择框的初始值
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        groupId: undefined,
        alarmType: undefined,
        sstType: undefined,
        content: '',
        lang: '',
        alarmLevel: 1
      },
      // levelOptions: {
      //   1: this.$t('text.prompt'),
      //   2: this.$t('text.warning')
      //   // 3: '告警'
      // },
      alarmLevelOptions: [],
      sensitiveLevelOptions: [
        { label: this.$t('pages.severityOptions1'), value: 1 },
        { label: this.$t('pages.severityOptions2'), value: 2 },
        { label: this.$t('pages.severityOptions3'), value: 3 },
        { label: this.$t('pages.severityOptions4'), value: 4 }
      ],
      formTreeData: [],
      rules: {
        // name: [
        //   { required: true, message: this.$t('pages.inputTemplateName'), trigger: 'blur' }
        // ],
        // alarmType: [
        //   { required: true, message: this.$t('pages.selectTemplateType'), trigger: 'blur' }
        // ],
        // lang: [
        //   { required: true, message: this.$t('pages.selectTemplateLang'), trigger: 'blur' }
        // ],
        // content: [
        //   { validator: this.templateContentValidator, trigger: 'blur' }
        // ]
      },
      dialogFormVisible: false,
      levelFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.$t('pages.updateTemplate'),
        update1: this.$t('pages.updateTemplate1'),
        create: this.$t('pages.createTemplate'),
        delete: this.$t('pages.deleteTemplate')
      },
      treeNodeType: 'G',
      receiverGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('route.alarmTemplate'), parentId: '', children: [] }],
      treeSelectNode: [],
      title: this.$t('pages.alarmTemplate'),
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      paramList: [], // 模板参数列表
      currentEditTemplate: {},
      maxLength: 1000, // 最大长度,数据库的i18n_dict设置模板字段最长为1000字符
      tableData: [],
      option: '',
      tempTemplateContent: '',
      levelColModel: [
        { prop: 'level', label: 'level', fixedWidth: '180' },
        { prop: 'levelName', label: 'levelName', type: 'input', editMode: true, change: () => { this.errorMsg = '' }, clearable: false, maxlength: 30, width: '150' }
      ],
      levelRowData: [],
      i18nTabName: 'zh_CN'
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    },
    gridTable() {
      return this.$refs['receiverList']
    },
    receiverGroupTree: function() {
      return this.$refs['receiverGroupTree']
    }
  },
  activated() {
    // this.loadReceiverTree()
  },
  created() {
    this.resetTemp()
    this.loadReceiverTree()
    this.loadAlarmLevelDict()
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    createAlarmTemplateGroup,
    updateAlarmTemplateGroup,
    deleteAlarmTemplateGroup,
    getAlarmTemplateGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    rowDataApi: function(option) {
      this.option = option
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.sortName == 'id' && (searchQuery.sortName = 'modifyTime')
      return getAlarmTemplatePage(searchQuery)
    },
    detailRowDataApi: function() {
      return getDetailList(this.updateTemplateId)
    },
    loadAlarmLevelDict() {
      getAlarmLevelDict().then(res => {
        this.alarmLevelOptions = res.data.map(item => {
          return { value: item.level, label: item.levelName }
        })
      })
    },
    templateContentValidator(rule, value, callback) {
      if (!getTextFromHtml(value)) {
        callback(new Error(this.$t('pages.inputTemplateConent')))
      }
      callback()
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    receiverGroupTreeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      if (checkedNode) {
        this.query.groupId = checkedNode.data.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    async loadReceiverTree() {
      await getGroupTreeNode().then(respond => {
        if (respond.data) {
          this.receiverGroupTreeData[0].children = [...respond.data]
        }
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    getLanguage() {
      const currentLang = this.$store.getters.language
      if (currentLang != undefined || currentLang != null || currentLang != '') {
        if (currentLang == 'zh') {
          this.langName = '中文（简体）'
        } else if (currentLang == 'tw') {
          this.langName = '中文（繁體）'
        } else {
          this.langName = 'English'
        }
      }
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.receiverGroupTreeData[0].children.filter(v => v.id != '-1')
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.receiverGroupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.receiverGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.receiverGroupTree.findNode(this.receiverGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.receiverGroupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    loadTypeTreeExceptRoot() {
      return this.receiverGroupTreeData[0].children
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.submitting = false
      this.errorMsg = false
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    langFormatter(row) {
      if (!row.lang) {
        return ''
      }
      return this.langList.find(v => v.code == row.lang).name
    },
    handleMoving(idList) {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleLevel() {
      this.errorMsg = ''
      this.submitting = false
      this.levelFormVisible = true
      getAlarmLevelList().then(respond => {
        this.levelRowData.splice(0)
        respond.data.forEach(data => {
          this.levelRowData.push(Object.assign({}, data))
        })
        this.reloadAllTable()
      })
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = (this.query.groupId || '') + '';
      if (this.temp.groupId == '0') {
        this.temp.groupId = ''
      }
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleResetTemplate() {
      this.errorMsg = ''
      this.alarmTemplatefind.content = this.tempTemplateContent
    },
    handleUpdate: function(row) {
      this.updateTemplateId = row.id
      this.updateTemplateTableType = row.tableType
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      // this.temp.groupId = (this.temp.groupId || '') + ''
      this.dialogStatus = row.tableType == 2 ? 'update1' : 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.alarmTemplatefind = {}
      this.getLanguage()
      getDetailAndLanguage(row.id, this.langName, row.tableType).then(resp => {
        this.alarmTemplatefind = resp.data
        this.currentEditTemplate = Object.assign({}, this.alarmTemplatefind)
        this.resetAlarmTemplate().then(resp => {
          this.tempTemplateContent = this.currentEditTemplate.content
        })
      })
      getParamList(row.alarmType, row.sstType, row.bizType).then(resp => {
        this.paramList = this.removeDuplicateParamName(resp.data)
      })
    },
    changeLang() {
      // 语言选择框的值发生变化时
      getDetailAndLanguage(this.updateTemplateId, this.langName, this.updateTemplateTableType).then(resp => {
        this.alarmTemplatefind = resp.data
        this.currentEditTemplate = Object.assign({}, this.alarmTemplatefind)
        this.resetAlarmTemplate().then(resp => {
          this.tempTemplateContent = this.currentEditTemplate.content
        })
      })
    },
    removeDuplicateParamName(list) {
      const arr = []
      list && list.forEach(item => {
        if (arr.findIndex(t => { return t.paramName === item.paramName }) === -1) {
          arr.push(item);
        }
      })
      return arr;
    },
    handleUpdateDetail(row) {
      this.currentEditTemplate = Object.assign({}, row)
      getParamList(this.temp.alarmType, this.temp.sstType).then(resp => {
        this.paramList = this.removeDuplicateParamName(resp.data)
      })
      this.currentEditTemplate.templateTypeKey = this.temp.templateTypeKey
      this.contentDialogFormVisible = true
    },
    insertParamName(cmd) {
      const that = this
      function insertTextAtCursor(textarea, text) {
        var startPos = textarea.selectionStart;
        var endPos = textarea.selectionEnd;

        var textBefore = textarea.value.substring(0, startPos);
        var textAfter = textarea.value.substring(endPos, textarea.value.length);

        textarea.value = textBefore + text + textAfter;
        that.alarmTemplatefind.content = textBefore + text + textAfter;

        // 重新设置光标位置
        textarea.selectionStart = startPos + text.length;
        textarea.selectionEnd = startPos + text.length;

        // 让 textarea 获取焦点
        textarea.focus();
      }

      insertTextAtCursor(this.$refs.contentArea.$el.querySelector('textarea'), cmd)
    },
    async resetAlarmTemplate() {
      const key = this.currentEditTemplate.contentKey
      const arr = key.replace('{{', '').replace('}}', '').split('.')
      return await getInitTemplate(this.currentEditTemplate.lang, arr[arr.length - 1]).then(resp => {
        this.currentEditTemplate.content = resp.data
      })
    },
    async handleSaveTemplate() {
      this.submitting = true
      if (!this.alarmTemplatefind.content || !this.alarmTemplatefind.content.trim()) {
        this.submitting = false
        this.errorMsg = this.$t('pages.templateDoNotEmpty')
        return;
      } else if (this.alarmTemplatefind.content.length > this.maxLength) {
        this.submitting = false
        return;
      }
      this.alarmTemplatefind.alarmLevel = this.temp.alarmLevel
      this.alarmTemplatefind.id = this.temp.id
      this.alarmTemplatefind.groupId = this.temp.groupId
      await saveTemplate(this.alarmTemplatefind)
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.saveSuccess'),
        type: 'success',
        duration: 2000
      })
      this.submitting = false
      this.tableData = []
      const searchQuery = Object.assign({}, this.query, this.option)
      getAlarmTemplatePage(searchQuery).then(resp => {
        this.tableData = resp.data.data
        this.dialogFormVisible = false
        this.contentDialogFormVisible = false
      })
    },
    exportFunc(formData, opts) {
      return exportExcel({
        ids: formData.type === 1 ? formData.dataIds.join(',') : null,
        groupId: formData.type === 2 ? formData.groupId : null
      }, opts)
    },
    importEndFunc() {
      this.loadReceiverTree()
      this.handleFilter()
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createAlarmTemplate(this.temp).then(respond => {
            this.submitting = false
            if (!respond.data) {
              this.$message({
                message: this.$t('pages.existSameTemplate'),
                type: 'error'
              })
              return
            }
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateAlarmTemplate(tempData).then(respond => {
            this.submitting = false
            if (!respond.data) {
              this.$message({
                message: this.$t('pages.existSameTemplate'),
                type: 'error'
              })
              return
            }
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    stateFormatter(row, data) {
      for (let i = 0, size = this.stateOptions.length; i < size; i++) {
        if (this.stateOptions[i].id === data) {
          return this.stateOptions[i].name
        }
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic' v-show={data.dataId != '-1'}>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    groupFormatter(row, data) {
      // if (!data) {
      //   return this.$t('pages.ungrouped')
      // }
      return this.getGroupNameByDataId(this.receiverGroupTreeData, data)
    },
    alarmLevelFormatter(row, data) {
      // 去掉非普通告警的模版
      if (row.tableType != '1' || row.sstType != '0') {
        return ''
      }
      return getDictLabel(this.alarmLevelOptions, data)
    },
    contentFormatter(row, data) {
      if ('zh' === this.lang) {
        return row.zhTemplate
      } else if ('tw' === this.lang) {
        return row.twTemplate
      } else if ('en' === this.lang) {
        return row.enTemplate
      }
      return row.content
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.receiverGroupTree.findNode(this.receiverGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.receiverGroupTree.selectCurrentNode(nodeData.id)
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteAlarmTemplate(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    getRows(lang) {
      const rowDatas = this.levelRowData.filter(item => item.language == lang)
      console.log('getRows.................', rowDatas)
      // const rowDatas = this.levelRowData
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: { total: rowDatas.length, items: rowDatas }})
      })
    },
    reloadAllTable() {
      if (this.$refs.encTable) {
        this.$refs.encTable.execRowDataApi()
      }
    },
    handleSaveLevel() {
      this.submitting = true
      console.log('this.levelRowData', this.levelRowData)
      if (this.levelRowData) {
        for (let i = 0; i < this.levelRowData.length; i++) {
          if (!this.levelRowData[i].levelName) {
            this.errorMsg = this.$t('text.cantNullInfo', { info: this.$t('table.levelName') })
            this.submitting = false
            return;
          }
        }
      }
      updateAlarmLevel(this.levelRowData).then(res => {
        this.submitting = false
        this.levelFormVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.loadAlarmLevelDict()
        this.$store.dispatch('commonData/changeNotice', 'refreshAlarm')
      }).catch(e => {
        this.submitting = false
      })
    }
  }
}
</script>
