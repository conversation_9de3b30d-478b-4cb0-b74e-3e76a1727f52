<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="$t('pages.mailLibrary')"
    :remark="$t('pages.mailLibrary_text7')"
    :visible.sync="dlgVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div class="toolbar">
      <el-row>
        <el-col :span="1" style="font-weight: 700;padding-top: 7px;">{{ $t('table.groupId') }}</el-col>
        <el-col :span="7">
          <tree-select ref="groupTree" v-model="query.groupId" :data="treeData" :width="200" @change="treeNodeClick" />
        </el-col>
        <el-col :span="16">
          <div style="float: right;">
            <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.mailLibrary_text1')" style="width: 200px;"/>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <grid-table
      ref="appInfoList"
      :height="420"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :default-sort="{ prop: 'name' }"
      :page-sizes="[ 20, 50, 100, 500, 1000 ]"
    />
    <div slot="footer" class="dialog-footer">
      <link-button btn-type="primary" btn-style="float: left" :menu-code="'A52'" :link-url="'/system/baseData/mailLibrary'" :btn-text="$t('pages.maintainMail')"/>
      <el-button type="primary" :loading="submitting" @click="handleSelect()">
        {{ $t('pages.select_mail') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getGroupTreeNode, getMailLibPage } from '@/api/system/baseData/mailLibrary'
import { findNode } from '@/utils/tree'

export default {
  name: 'MailSelectDlg',
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'userName', width: '30', fixed: true },
        { prop: 'groupId', label: 'groupId', width: '30', formatter: this.groupFormatter },
        { prop: 'address', label: 'email', width: '30' },
        { prop: 'unit', label: 'unit', width: '30' },
        { prop: 'office', label: 'office', width: '30' },
        { prop: 'remark', label: 'remark', width: '30' }
      ],
      query: { // 查询条件
        page: 1,
        usbName: '',
        groupId: 0
      },
      dlgVisible: false,
      submitting: false,
      selectTreeId: null,
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.mailLibrary'), parentId: '', children: [] }]
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.selectTreeId = null
      this.query.groupId = 0
      this.query.usbName = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNode()
      })
    },
    treeNodeClick(data, node) {
      this.selectTreeId = node.dataId
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        this.selectTreeId = 0
      }
      this.query.groupId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      return getMailLibPage(newOption)
    },
    loadGroupTree: function() {
      getGroupTreeNode().then(respond => {
        this.treeData[0].children = respond.data
        this.$nextTick(() => {

        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      this.$emit('select', this.softTable.getSelectedDatas())
      this.dlgVisible = false
    },
    groupFormatter(row, data) {
      const nodeData = findNode(this.treeData, data, 'dataId')
      return nodeData ? nodeData.label : ''
    }
  }
}
</script>
