<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.importFileSuffixDefault2')"
    :visible.sync="dlgVisible"
    width="850px"
    @dragDialog="handleDrag"
  >
    <file-suffix-select-table ref="fileSuffixSelectTable" :height="340" :lib-tree-node="getLibTreeNode" :lib-file-suffix-page="getLibFileSuffixPage"/>
    <Form label-width="120px" :extra-width="{en: 90}" :model="importType">
      <FormItem :label="$t('pages.repeatNameDealType')">
        <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.repeatNameDealTypeContent">
              <template slot="tip">{{ $t('pages.fileSuffixTip') }}</template>
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <el-radio-group v-model="importType.repeat">
          <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.importGroupDisposal')">
        <el-radio-group v-model="importType.groupType" style="display: flex; flex-direction: column;">
          <el-radio :label="0">
            <span>{{ $t('pages.importGroupDisposalType1', { type: $t('pages.suffixes'), target: $t('pages.customLibrary')}) }}</span>
          </el-radio>
          <div style="display: flex">
            <el-radio :label="1" style="margin-right: 10px;">
              <i18n path="pages.importGroupDisposalType2">
                <template slot="type">{{ $t('pages.suffixes') }}</template>
              </i18n>
            </el-radio>
            <tree-select
              :data="selectTreeData[0].children"
              :checked-keys="[importType.parentId]"
              node-key="dataId"
              :disabled="importType.groupType === 0"
              @change="parentIdChange"
            />
          </div>
        </el-radio-group>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="handleSelect()">
        {{ $t('button.import') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import FileSuffixSelectTable from './fileSuffixSelectTable'
import { getLibTreeNode, getLibFileSuffixPage } from '@/api/system/baseData/fileSuffix'

export default {
  name: 'FileSuffixSelectDlg',
  components: { FileSuffixSelectTable },
  props: {
    formTreeData: { type: Array, default() { return [] } }
  },
  data() {
    return {
      dlgVisible: false,
      submitting: false,
      importType: {},
      defaultImportType: {
        repeat: 1,
        groupType: 0,
        parentId: undefined
      },
      groupTreeDataJSON: '' // 保存groupTreeData数据的JSON字符串，用于监听数据是否发生变化。（对象层级较深时，监听不到数据的变化）
    }
  },
  computed: {
    selectTreeData() {
      return JSON.parse(this.groupTreeDataJSON)
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
    this.resetTemp()
  },
  methods: {
    getLibTreeNode,
    getLibFileSuffixPage,
    resetTemp() {
      this.importType = Object.assign({}, this.defaultImportType)
      this.groupTreeDataJSON = JSON.stringify(this.formTreeData)
    },
    show() {
      this.resetTemp()
      this.dlgVisible = true
      this.$nextTick(() => {
        this.$refs.fileSuffixSelectTable.show()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      const targetGroupId = this.importType.parentId;
      const groupType = this.importType.groupType
      const selectedDatas = this.$refs.fileSuffixSelectTable.getSelectedDatas()
      if (selectedDatas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.fileSuffix_text14'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (groupType == 1 && (targetGroupId == null || targetGroupId === '')) {
        this.$message({
          message: this.$t('pages.validaGroup'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      this.$emit('select', selectedDatas, this.importType)
      this.dlgVisible = false
    },
    parentIdChange(data) {
      this.importType.parentId = data
    }
  }
}
</script>

<style lang='scss' scoped>
>>>.el-dialog__body {
    max-height: none;
    padding: 20px 19px 15px;
    color: #666;
    overflow: auto;
}
</style>
