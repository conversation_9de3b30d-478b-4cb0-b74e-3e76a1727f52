<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="typeTree"
        resizeable
        :default-expand-all="true"
        :data="treeData"
        :is-filter="true"
        :render-content="renderContent"
        @node-click="treeNodeClick"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" :disabled="selectTreeId==null" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button size="mini" icon="el-icon-delete" @click="handleRemove">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'332'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-dropdown v-permission="'357'" style="padding-left: 10px;" @command="handleMoreClick">
          <el-button size="mini">
            {{ $t('button.import') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1" icon="el-icon-reading">{{ $t('pages.importFromFile') }}</el-dropdown-item>
            <el-dropdown-item :command="2" icon="el-icon-reading">{{ $t('pages.importFileSuffixDefault') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.fileSuffix_text1')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="suffixTable"
        :show-pager="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible1"
      width="500px"
    >
      <Form
        ref="dataForm2"
        :rules="rules"
        :model="temp1"
        label-position="right"
        label-width="80px"
      >
        <div class="toolbar">
          <FormItem v-if="dialogStatus==='batchCreate'" :label="$t('table.suffixes')" prop="suffix">
            <el-input v-model="temp1.suffix" type="textarea" :onkeyup="(function() { temp1.suffix = temp1.suffix.replace(/\s+/g,'') })()" :maxlength="1000" show-word-limit :placeholder="$t('pages.fileSuffix_text13')"></el-input>
          </FormItem>
          <FormItem v-else :label="$t('table.suffixes')" prop="suffix">
            <el-input v-model="temp1.suffix" :onkeyup="(function() { temp1.suffix = temp1.suffix.replace(/[\s|]+/g,'') })()" :maxlength="60" show-word-limit :placeholder="$t('pages.fileSuffix_text11')"></el-input>
          </FormItem>
          <FormItem :label="$t('pages.groupType')" prop="parentId" style="margin-top:5px;">
            <tree-select
              :data="formTreeData"
              node-key="dataId"
              :checked-keys="checkedKeys"
              @change="parentIdChange"
            />
          </FormItem>
          <FormItem :label="$t('table.remark')" prop="name">
            <el-input v-model="temp1.name" :maxlength="60" type="textarea" show-word-limit></el-input>
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='update1'?updateData1() : createData1()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible1 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <export-dlg ref="exportDlg" :group-tree-data="treeData" :export-func="exportFunc" :group-tree-id="selectTreeId"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      template="suffix"
      :show-import-type="false"
      :file-name="title"
      :tip="$t('pages.fileSuffixTip')"
      :show-import-way="true"
      :upload-func="upload"
      @success="importEndFunc"
    />

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="$t('pages.suffixes')"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteData"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
    ></delete-group-dlg>

    <edit-group-dlg
      ref="editGroupDlg"
      show-parent
      :title="$t('pages.fileSuffix')"
      :group-tree-data="treeData"
      :add-func="createData"
      :update-func="updateData"
      :delete-func="deleteData"
      :move-func="updateSuffixGroup"
      :edit-valid-func="getGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? $t('text.editInfo', { info: $t('pages.fileSuffixTipGroup')}) : $t('text.deleteInfo', { info: $t('pages.fileSuffixTip')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="treeData" :checked-keys="checkedKeys2" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>

    <file-suffix-select-dlg ref="defaultSelectDlg" :form-tree-data="treeData" @select="appendDataTable"/>
  </div>
</template>

<script>
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import request from '@/utils/request'
import FileSuffixSelectDlg from './fileSuffixSelectDlg.vue'
import { findNodeLabel } from '@/utils/tree'
import {
  createData, updateData, deleteData, updateSuffixGroup, deleteGroupAndData, moveGroupToOther,
  batchAddFileSuffix, updateFileSuffix, deleteFileSuffix, getGroupByName, getFileSuffixPage, getFileSuffixBySuffix,
  countChildByGroupId, getTreeNode, exportExcel, addFromFileSuffixLib, batchUpdateGroup, batchUpdateAllGroup
} from '@/api/system/baseData/fileSuffix'

export default {
  name: 'FileSuffixLib',
  components: { EditGroupDlg, DeleteGroupDlg, ExportDlg, ImportDlg, FileSuffixSelectDlg, BatchEditPageDlg },
  data() {
    return {
      showTree: true,
      deleteable: false,
      selectTreeId: null,
      checkedKeys: [],
      checkedKeys2: [],
      treeNodeType: 'G',
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.fileSuffix'), parentId: '', children: [] }],
      formTreeData: [],
      submitting: false,
      dialogFormVisible1: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('table.typeId'), 'update'),
        create: this.i18nConcatText(this.$t('table.typeId'), 'create'),
        update1: this.i18nConcatText(this.$t('pages.fileSuffixTip'), 'update'),
        create1: this.i18nConcatText(this.$t('pages.fileSuffixTip'), 'create'),
        batchCreate: this.i18nConcatText(this.$t('pages.fileSuffixTip'), 'batchAdd'),
        delete: this.i18nConcatText(this.$t('pages.fileSuffixTipGroup'), 'delete')
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        parentId: '0',
        parentName: ''
      },
      temp1: {},
      defaultTemp1: { // 表单字段
        id: undefined,
        name: '',
        suffix: '',
        groupId: 0,
        base64Data: ''
      },
      rules: {
        suffix: [
          { required: true, message: this.$t('pages.fileSuffix_text4'), trigger: 'blur' },
          { validator: this.suffixValidator, trigger: 'change' }
        ]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      colModel: [
        { prop: 'suffix', label: 'suffixes', width: '110', sort: 'custom', formatter: (row) => { return row.suffix.indexOf('.') === 0 ? row.suffix : '.' + row.suffix } },
        { prop: 'groupId', label: 'sourceGroup', width: '110', formatter: this.groupFormatter },
        { prop: 'name', label: 'remark', width: '110' },
        // { prop: 'icon', label: '图标', width: '30', iconFormatter: this.iconClassFormatter },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      title: this.$t('pages.suffixes'),
      updateGroupForm: false,
      updateGroupId: undefined // 批量修改的分组id
    }
  },
  computed: {
    gridTable() {
      return this.$refs['suffixTable']
    },
    typeTree: function() {
      return this.$refs['typeTree']
    }
  },
  created() {
    this.resetTemp()
    this.loadGroupTree()
  },
  activated() {
    this.loadGroupTree()
    this.gridTable.execRowDataApi()
  },
  methods: {
    createData,
    updateData,
    deleteData,
    getGroupByName,
    updateSuffixGroup,
    deleteGroupAndData,
    moveGroupToOther,
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: this.treeNodeType + data.parentId
      }
    },
    createNode(data) {
      this.typeTree.addNode(this.dataToTreeNode(data))
      this.loadGroupTree()
    },
    updateNode(data) {
      this.typeTree.updateNode(this.dataToTreeNode(data))
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp1 = Object.assign({}, this.defaultTemp1)
    },
    removeGroupEnd(dataId) {
      const nodeData = this.typeTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.typeTree.removeNode([nodeData.id])
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        return new Promise((resolve, reject) => {
          resolve({
            code: 20000,
            data: {
              total: 0,
              items: [] // 数组元素格式为{username:'zhangsan',id:1,...}
            }
          })
        })
      }
      this.query.groupId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getFileSuffixPage(newOption)
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'batchCreate'
      const selectNode = this.typeTree.$refs['tree'].getCurrentNode()
      if (selectNode != null) {
        this.temp.parentId = selectNode.dataId
      } else {
        this.temp.parentId = '0'
      }
      this.formTreeData = JSON.parse(JSON.stringify(this.loadTypeTreeExceptRoot()))
      this.dialogFormVisible1 = true
      this.$nextTick(() => {
        if (selectNode != null) {
          this.checkedKeys = [selectNode.dataId]
        } else {
          this.checkedKeys = [0]
        }
        this.$refs['dataForm2'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp1 = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.parentId = '' + row.groupId
      this.formTreeData = JSON.parse(JSON.stringify(this.loadTypeTreeExceptRoot()))
      this.dialogStatus = 'update1'
      this.dialogFormVisible1 = true
      this.$nextTick(() => {
        this.checkedKeys = [row.groupId]
        this.$refs['dataForm2'].clearValidate()
      })
    },
    handleCreateNode(nodeData) {
      this.$refs['editGroupDlg'].handleCreate(nodeData.dataId)
    },
    handleUpdateNode: function(nodeData) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: nodeData.dataId,
        name: nodeData.label,
        parentId: nodeData.parentId.replace('G', '')
      })
    },
    handleMoving() {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys2 = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleRemove() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    removeNode(nodeData) {
      countChildByGroupId(nodeData.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(nodeData.dataId)
      })
    },
    createData1() {
      this.submitting = true
      this.$refs['dataForm2'].validate(async valid => {
        if (valid) {
          this.temp1.groupId = this.temp.parentId
          this.temp1.groupName = findNodeLabel(this.formTreeData, this.temp1.groupId, 'dataId')
          batchAddFileSuffix(this.temp1).then((res) => {
            this.dialogFormVisible1 = false
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            if (res.data == 'success') {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: this.$t('text.warning'),
                message: res.data,
                type: 'error',
                duration: 5000
              })
            }
            this.loadGroupTree()
          }).catch(e => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData1() {
      this.submitting = true
      this.$refs['dataForm2'].validate(async valid => {
        if (valid) {
          this.temp1.groupId = this.temp.parentId
          this.temp1.groupName_new = findNodeLabel(this.formTreeData, this.temp1.groupId, 'dataId')
          updateFileSuffix(this.temp1).then((res) => {
            this.dialogFormVisible1 = false
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            if (res.data == 'success') {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.editSuccess'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: this.$t('text.warning'),
                message: res.data,
                type: 'error',
                duration: 5000
              })
            }
            this.loadGroupTree()
          }).catch(e => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    loadTypeTreeExceptRoot() {
      return this.treeData[0].children
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleCreateNode(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleUpdateNode(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    resetQuery() {
      this.query = { // 查询条件
        page: 1,
        groupId: null
      }
    },
    treeNodeClick: function(data, node, element) {
      const selectNode = this.typeTree.$refs['tree'].getCurrentNode()
      this.selectTreeId = selectNode.dataId
      this.resetQuery()
      const options = Object.assign(this.query, { page: 1, groupId: this.selectTreeId })
      console.log('options', options)
      this.gridTable.execRowDataApi(options)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    parentIdChange(data) {
      this.temp.parentId = data
      this.checkedKeys.splice(0, this.checkedKeys.length, data)
      this.temp.parentName = ''
      if (this.$refs.parentSelectTree && this.$refs.parentSelectTree.$refs) {
        this.temp.parentName = this.$refs.parentSelectTree.$refs.tree.getNode(data).label
      }
    },
    selectFirstNode: function() {
      this.typeTree.$refs['tree'].setCurrentKey(this.treeNodeType + '0')
      this.selectTreeId = '0'
    },
    loadGroupTree: function() {
      getTreeNode().then(respond => {
        this.treeData[0].children = respond.data
        this.formTreeData = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
        this.$nextTick(() => {
          if (this.selectTreeId == null) {
            // 选中第一个节点
            this.selectFirstNode()
            // 然后刷新右边表格数据
            this.handleFilter()
          } else {
            this.typeTree.$refs['tree'].setCurrentKey(this.treeNodeType + this.selectTreeId)
            this.handleFilter()
          }
        })
      })
    },
    refreshTableData() {
      this.resetQuery()
      this.gridTable.execRowDataApi(this.query)
    },
    suffixValidator: function(rule, value, callback) {
      value = value || '';
      if (value === '') {
        callback();
        return
      }
      const suffixes = value.split('|')
      suffixes.forEach(suffix => {
        if (!suffix.startsWith('.')) {
          suffix = '.' + suffix
        }
        if (suffix.length > 60) {
          callback(new Error(this.$t('pages.suffixOver60')))
        }
      })
      getFileSuffixBySuffix(suffixes).then(respond => {
        const strategy = respond.data
        if (strategy.length === 0) {
          callback()
        } else if (this.dialogStatus === 'update1' && strategy[0].id === this.temp1.id) {
          callback()
        } else {
          const errorStr = strategy.map(item => {
            return item.suffix
          }).join(',')
          callback(new Error(this.$t('pages.alreadyExistsInfo', { info: errorStr })))
        }
      })
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.typeTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.typeTree.setCurrentKey(nodeData.id)
        this.treeNodeClick()
      }
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    upload(data) {
      return request.post('/fileSuffix/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc(groupId) {
      this.selectTreeId = groupId ? String(groupId) : null
      this.loadGroupTree()
    },
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return this.getGroupNameByDataId(this.treeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleDrag() {
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveSuffixData(params, callback)
      } else {
        this.deleteSuffixData(params, callback)
      }
    },
    deleteSuffixData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteFileSuffix(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    moveSuffixData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    handleMoreClick(type) {
      switch (type) {
        case 1:
          this.handleImport()
          break
        case 2:
          this.$refs.defaultSelectDlg.show()
          break
        default:
          break
      }
    },
    appendDataTable(datas, type) {
      if (!datas || datas.length === 0) return
      var ids = datas.map(t => { return t.id })
      const param = { ids: ids.join(','), importWay: type.repeat }
      if (type.groupType === 1) {
        param.groupId = type.parentId
      }
      addFromFileSuffixLib(param).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.importSuccess'),
          type: 'success',
          duration: 2000
        })
        this.importEndFunc()
      })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    }
  }
}

</script>
