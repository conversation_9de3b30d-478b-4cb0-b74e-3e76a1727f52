<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.importFileSuffixLib')"
      :visible.sync="dialogVisible"
      width="850px"
    >
      <file-suffix-select-table ref="fileSuffixSelectTable" :height="400" :multiple="multiple" :lib-tree-node="getTreeNode" :lib-file-suffix-page="getFileSuffixPage"/>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :menu-code="'A5G'" :link-url="'/system/baseData/fileSuffixLib'" :btn-text="$t('pages.maintainFileSuffixLib')" :click-func="'clickLink'" @clickLink="clickLink('/system/baseData/fileSuffixLib')"/>
        <el-button type="primary" @click="confirmImport()">
          {{ $t('pages.addSelectedSuffix') }}
        </el-button>
        <el-button @click="hide()">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getTreeNode, getFileSuffixPage } from '@/api/system/baseData/fileSuffix'
import FileSuffixSelectTable from './fileSuffixSelectTable'

export default {
  name: 'FileSuffixImportDlg',
  components: { FileSuffixSelectTable },
  props: {
    multiple: { type: Boolean, default: true }, // 能否多选
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      dialogVisible: false,
      fileSuffixTree: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs.fileSuffixSelectTable.checkedRowKeys = []
      }
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    getTreeNode,
    getFileSuffixPage,
    show(suffix) {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.fileSuffixSelectTable.show(suffix)
      })
    },
    hide() {
      this.dialogVisible = false
    },
    renderContent(h, { node, data, store }) {
      if (data.type !== 'fileSuffix') {
        return (
          <div class='custom-tree-node'>
            <span style='width: 22px'>
              <svg-icon icon-class='dir1' />
            </span>
            <span>{data.label}</span>
          </div>
        )
      } else {
        return (
          <div class='custom-tree-node'>
            <span style='width: 20px;display:flex;justify-content: center;'>
              <img src={ data.oriData.base64Data == null || data.oriData.base64Data == '' ? this.imgIconMap['txt'] : data.oriData.base64Data } style='display: flex;height: 14px'/>
            </span>
            <span style='margin-left:2px'>{data.label} (.{data.oriData.suffix}) </span>
          </div>
        )
      }
    },
    confirmImport() {
      const selectedDatas = this.$refs.fileSuffixSelectTable.getSelectedDatas()
      if (selectedDatas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.fileSuffix_text14'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const result = selectedDatas.map(data => {
        const suffix = data.suffix
        return suffix.indexOf('.') === 0 ? suffix : `.${suffix}`
      }).join('|')
      this.$emit('importfilesuffix', result)
      this.hide()
    },
    clickLink() {
      this.dialogVisible = false
      this.$router.push('/system/baseData/fileSuffixLib')
    }
  }
}
</script>
