<template>
  <el-container>
    <el-aside width="210px">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :height="height + 40"
        :multiple="multiple"
        :default-expanded-keys="defaultExpandedKeys"
        :expand-on-click-node="false"
        @node-click="treeNodeClick"
        @check-change="checkChange"
      />
    </el-aside>
    <el-main>
      <div class="toolbar">
        <div style="float: right;">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.nameOrSuffix')" style="width: 225px;" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="suffixTable"
        :height="height"
        :is-saved-selected="true"
        saved-selected-prop="suffix"
        :col-model="colModel"
        :multi-select="multiple"
        :row-data-api="rowDataApi"
        :checked-row-keys="checkedRowKeys"
        pager-small
        :after-load="afterLoad"
        @select="select"
        @currentChange="currentChange"
      />
    </el-main>
  </el-container>
</template>

<script>
import { findNodeLabel } from '@/utils/tree'
import { getIdsBySuffixList } from '@/api/system/baseData/fileSuffix'

export default {
  name: 'FileSuffixSelectTable',
  props: {
    height: { type: Number, default: 420 },
    multiple: { type: Boolean, default: true },
    libTreeNode: {
      type: Function,
      default: null
    },
    libFileSuffixPage: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'suffix', label: 'fileSuffix', width: '110', sort: 'custom', formatter: this.suffixFormatter },
        { prop: 'groupId', label: 'groupName', width: '110', formatter: this.groupFormatter },
        { prop: 'name', label: 'remark', width: '110' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined,
        groupIds: ''
      },
      suffix: '',
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.fileSuffix'), parentId: '', children: [] }],
      defaultExpandedKeys: ['G0'],
      currentData: null,
      dataLoadPromise: undefined,     // 设置一个函数先执行完后在执行别的操作
      checkedRowKeys: [],             // 表格勾选
      checkedGroupDatas: [],          // 勾选的分组下的数据
      uncheckRowKeySet: new Set(),    // 由勾选变成未勾选的 key
      currentDataAll: null            // 当前表格中所有的数据
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    suffixTable: function() {
      return this.$refs['suffixTable']
    }
  },
  created() {
    this.show()
  },
  activated() {
    this.show()
  },
  methods: {
    async show(suffix) {
      this.suffix = suffix
      this.init()
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNodes()
        setTimeout(() => {
          this.clearSelection()
        }, 500)
      })
    },
    init() {
      this.query.searchInfo = null
      this.query.groupId = undefined
      this.query.groupIds = ''
      this.currentData = null
      this.checkedRowKeys = []
      this.uncheckRowKeySet = new Set()
    },
    clearSelection() {
      this.$refs.suffixTable && this.$refs.suffixTable.clearSelection()
    },
    /**
     * 左侧树节点点击时右侧列表刷新
     * @param data
     * @param node
     * @param element
     */
    treeNodeClick: function(data, node, element) {
      if (data.dataId === '0') {
        this.query.groupId = data.dataId
        this.query.groupIds = ''
      } else {
        this.query.groupId = undefined
        this.query.groupIds = this.getGroupIds(data).join(',')
      }
      this.handleFilter()
    },
    /**
     * 获取传入的树节点及其子节点的 dataId
     */
    getGroupIds(data) {
      const groups = Array.isArray(data) ? data : [data]
      const ids = []
      while (groups.length > 0) {
        const group = groups.shift()
        if (group.children) {
          groups.push(...group.children)
        }
        ids.push(group.dataId)
      }
      return ids
    },
    /**
     * 表格查询功能
     * */
    handleFilter() {
      this.query.page = 1
      this.suffixTable && this.suffixTable.execRowDataApi(this.query)
    },
    /**
     * 获取表格数据
     * */
    rowDataApi: function(option) {
      const newOption = Object.assign(this.query, option)
      return this.libFileSuffixPage(newOption)
    },

    /**
     * 获取左侧树节点信息
     */
    loadGroupTree: function() {
      this.libTreeNode().then(respond => {
        this.treeData[0].children = respond.data
      })
    },
    select(selection, row) {
      const isSelect = selection.includes(row)
      if (!isSelect) {
        this.uncheckRowKeySet.add(row.id)
      } else {
        this.uncheckRowKeySet.delete(row.id)
      }
    },
    currentChange: function(currentRow) {
      this.currentData = currentRow
    },
    /**
     * 表格刷新后调用方法
     * */
    afterLoad(rowData, grid) {
      this.currentDataAll = rowData
      this.selectTable()
    },
    /**
     * 左侧树选中状态发生变化时
     * */
    checkChange(val) {
      if (val.length > 0) {
        const groupIds = val.map(key => key.replace('G', '')).join(',')
        this.libFileSuffixPage({ groupIds }).then(resp => {
          this.checkedGroupDatas = resp.data.items
          this.checkedGroupDatas.forEach(data => {
            if (this.uncheckRowKeySet.has(data.id)) {
              this.uncheckRowKeySet.delete(data.id)
            }
          })
          this.selectTable()
        })
      } else {
        this.checkedGroupDatas.splice(0)
        this.uncheckRowKeySet = new Set()
      }
    },
    /**
     *
     * */
    selectTable() {
      const selectedKeys = this.getSelectedDatas().map(data => data.id)
      if (this.suffix) {
        getIdsBySuffixList(this.suffix).then(resp => {
          setTimeout(() => {
            this.checkedRowKeys = Array.from(new Set([...selectedKeys, ...resp.data]))
            this.suffix = ''
          }, 500)
        })
      } else {
        this.checkedRowKeys = selectedKeys
      }
    },
    /**
     * 获取勾选的列表数据
     */
    getSelectedDatas() {
      let selectedDatas = []
      if (this.multiple) {
        const uniqueSet = new Set()
        const checkedRowDatas = this.suffixTable.getSelectedDatas()
        // 勾选的数据由 勾选分组的数据 + 勾选列表的数据，再剔除取消勾选的数据
        selectedDatas = checkedRowDatas.concat(this.checkedGroupDatas).filter(data => {
          const isSelected = !this.uncheckRowKeySet.has(data.id)
          const isUnique = !uniqueSet.has(data.id)
          isSelected && isUnique && uniqueSet.add(data.id)
          return isSelected && isUnique
        })
      } else if (this.currentData != null) {
        selectedDatas = [this.currentData]
      }
      return selectedDatas
    },
    suffixFormatter(row) {
      return row.suffix.indexOf('.') === 0 ? row.suffix : `.${row.suffix}`
    },
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return findNodeLabel(this.treeData, data, 'dataId')
    }
  }
}
</script>
