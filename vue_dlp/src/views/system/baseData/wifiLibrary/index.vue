<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="wifiGroupTree"
        resizeable
        :default-expand-all="true"
        :data="wifiGroupTreeData"
        :render-content="renderContent"
        @node-click="wifiGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'371'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'372'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.wifiLibrary_text1')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="wifiList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.wifiName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60" />
        </FormItem>
        <FormItem :label="$t('table.macAddr')" prop="macAddress">
          <el-input v-model="temp.macAddress" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteWifiGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <export-dlg ref="exportDlg" :group-tree-data="wifiGroupTreeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      template="wifi"
      :show-import-type="false"
      :file-name="title"
      :tip="$t('pages.wifiInfo')"
      :show-import-way="true"
      :upload-func="upload"
      @success="importEndFunc"
    />

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.wifiGroup')"
      :group-tree-data="formTreeData"
      :add-func="createWifiGroup"
      :update-func="updateWifiGroup"
      :delete-func="deleteWifiGroup"
      :move-func="moveGroup"
      :edit-valid-func="getWifiGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? i18nConcatText(this.$t('pages.wifiGroup'), 'update') : i18nConcatText(this.$t('pages.wifiInfo'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="wifiGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>
  </div>
</template>

<script>
import {
  getGroupTreeNode, createWifiGroup, updateWifiGroup, deleteWifiGroup, deleteGroupAndData,
  moveGroupToOther, getWifiGroupByName, countChildByGroupId, getWifiPage, createWifi,
  updateWifi, deleteWifi, getWifiByVO, exportExcel, moveGroup,
  batchUpdateGroup, batchUpdateAllGroup
} from '@/api/system/baseData/wifiLib'
import { findNodeLabel } from '@/utils/tree'
import request from '@/utils/request'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'WifiLibrary',
  components: { BatchEditPageDlg, DeleteGroupDlg, EditGroupDlg, ImportDlg, ExportDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'wifiName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', sort: 'custom', width: '150', formatter: this.groupFormatter },
        { prop: 'macAddress', label: 'macAddr', sort: 'custom', width: '150' },
        { prop: 'remark', label: 'remark', sort: 'custom', width: '200' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate, isShow: () => this.showFormatter }
          ]
        }
      ],
      stateOptions: [{ 'id': 0, 'name': this.$t('text.normal') }, { 'id': 1, 'name': this.$t('text.disable') }],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        dataSource: undefined,
        groupId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        macAddress: '',
        remark: '',
        groupId: undefined,
        dataSource: 1
      },
      formTreeData: [],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_wifiName'), trigger: 'blur' },
          { validator: this.sameValidator, trigger: 'blur' }
        ],
        macAddress: [
          { required: true, message: this.$t('pages.wifiBlock_text3'), trigger: 'blur' },
          { validator: this.macValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.wifiInfo'), 'update'),
        create: this.i18nConcatText(this.$t('pages.wifiInfo'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.wifiInfo'), 'delete')
      },
      treeNodeType: 'G',
      wifiGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.wifiLibrary'), parentId: '', children: [] }],
      treeSelectNode: [],
      title: this.$t('pages.wifiInfo'),
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      validateFlag: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['wifiList']
    },
    wifiGroupTree: function() {
      return this.$refs['wifiGroupTree']
    }
  },
  activated() {
    this.loadWifiGroupTree()
  },
  created() {
    this.resetTemp()
    this.loadWifiGroupTree()
  },
  methods: {
    createWifiGroup,
    updateWifiGroup,
    deleteWifiGroup,
    getWifiGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getWifiPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    wifiGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    loadWifiGroupTree: function() {
      return getGroupTreeNode().then(respond => {
        this.wifiGroupTreeData[0].children = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.wifiGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    upload(data) {
      return request.post('/wifi/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    createNode(data) {
      this.wifiGroupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.wifiGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.wifiGroupTree.findNode(this.wifiGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.wifiGroupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.wifiGroupTree.findNode(this.wifiGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.wifiGroupTree.setCurrentKey(nodeData)
        this.wifiGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    loadTypeTreeExceptRoot() {
      return this.wifiGroupTreeData[0].children
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleMoving(idList) {
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.validateFlag = true
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    showFormatter(data, btn) {
      return data.dataSource
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    importEndFunc(groupId) {
      this.loadWifiGroupTree().then(() => {
        groupId = groupId ? String(groupId) : 0
        this.$nextTick(() => {
          const node = this.dataToTreeNode({ id: groupId })
          this.wifiGroupTree.selectCurrentNode(node.id)
        })
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createWifi(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateWifi(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    // addressFormatter(row, data) {
    //   const index = data.indexOf('$')
    //   if (index === 0) {
    //     return data.replace('$', '#').concat('#')
    //   }
    //   return data
    // },
    // stateFormatter(row, data) {
    //   for (let i = 0, size = this.stateOptions.length; i < size; i++) {
    //     if (this.stateOptions[i].id === data) {
    //       return this.stateOptions[i].name
    //     }
    //   }
    // },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    sameValidator(rule, value, callback) {
      if (this.temp.macAddress && this.temp.name) {
        const vo = {
          macAddress: this.temp.macAddress,
          name: this.temp.name
        }
        getWifiByVO(vo).then(res => {
          if (res.data && res.data.id !== this.temp.id) {
            callback(new Error(this.$t('pages.wifiLib_text1')))
          } else {
            callback()
          }
        })
      } else {
        callback()
      }
    },
    macValidator(rule, value, callback) {
      const re = /^([A-Fa-f0-9]{2}[-]){5}[A-Fa-f0-9]{2}$/
      if (!re.test(value)) {
        callback(new Error(this.$t('pages.ipMac_Msg7')))
      } else {
        this.sameValidator(rule, value, callback)
      }
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.wifiGroupTreeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteWifi(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    }
  }
}
</script>
