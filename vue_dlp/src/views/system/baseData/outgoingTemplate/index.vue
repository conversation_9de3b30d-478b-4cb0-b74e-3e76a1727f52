<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder=" $t('pages.templateName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="serverList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="170px"
        style="width: 700px;"
      >
        <FormItem :label="$t('pages.templateName')" prop="name" label-width="80px">
          <el-col :span="12">
            <el-input v-model="temp.name" v-trim style="width: 90%" :maxlength="20"></el-input>
          </el-col>
        </FormItem>
        <el-card class="box-card">
          <div slot="header">
            <el-checkbox v-model="temp.canUpdate" :true-label="1" :false-label="0">{{ $t('pages.outgoingTemplate_text1') }}</el-checkbox>
          </div>
          <div style="margin-top: -20px">
            <el-divider content-position="left">{{ $t('pages.ctrlValueList') }}</el-divider>
          </div>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :label="4" @change="ctrlChange">{{ $t('pages.ctrlValueList1') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :disabled="temp.ctrlValueList.indexOf(4)!=-1" :label="1">{{ $t('pages.ctrlValueList2') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :label="2">{{ $t('pages.ctrlValueList3') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :label="8" :disabled="temp.noLimitUse == 1">{{ $t('pages.ctrlValueList4') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :label="32">{{ $t('pages.ctrlValueList5') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :label="64">{{ $t('pages.ctrlValueList6') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-checkbox v-model="temp.ctrlValueList" :label="16">{{ $t('pages.ctrlValueList7') }}</el-checkbox>
          </FormItem>

          <FormItem :label=" $t('pages.readTimes') " prop="openTimes">
            <el-col :span="12">
              <el-input-number
                v-model.number="temp.openTimes"
                style="width: 90%"
                :controls="false"
                step-strictly
                :step="1"
                :disabled="temp.noLimitOpen==1"
                :min="1"
                :max="65535"
              />
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.noLimitOpen" :true-label="1" :false-label="0" @change="changeLimitOpen">
                {{ $t('pages.noLimit') }}
              </el-checkbox>
            </el-col>
          </FormItem>
          <FormItem :label="$t('pages.readDays')" prop="openTimes">
            <el-col :span="12">
              <el-input-number
                v-model.number="temp.useDays"
                style="width: 90%"
                :controls="false"
                step-strictly
                :step="1"
                :disabled="temp.noLimitUse==1"
                :min="1"
                :max="65535"
              />
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.noLimitUse" :true-label="1" :false-label="0" @change="changeLimitUse">{{ $t('pages.noLimit') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem :label="$t('pages.suffix')" prop="makeType">
            <el-col :span="12">
              <el-select v-model="temp.makeType" style="width: 90%">
                <el-option :label="$t('pages.makeType1')" :value="0"></el-option>
                <el-option :label="$t('pages.makeType2')" :value="1"></el-option>
                <el-option :label="$t('pages.makeType3')" :value="2"></el-option>
              </el-select>
            </el-col>
          </FormItem>
          <div>
            <el-divider content-position="left">{{ $t('pages.accessOptions') }}</el-divider>
          </div>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.validateValueList" :label="65536">{{ $t('pages.validateValue1') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.validateValueList" :label="131072">{{ $t('pages.validateValue2') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.validateValueList" :label="262144">{{ $t('pages.validateValue3') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.validateValueList" :label="524288">{{ $t('pages.validateValue4') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.validateValueList" :label="1048576">{{ $t('pages.validateValue5') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.validateValueList" :label="2097152">{{ $t('pages.validateValue6') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.validateValueList" :label="4194304">{{ $t('pages.validateValue7') }}</el-checkbox>
            </el-col>
          </FormItem>
        </el-card>
        <el-card class="box-card">
          <div slot="header">
            <span>{{ $t('pages.watermarkSetting') }}</span>
          </div>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :label="128">
                {{ $t('pages.watermarkText1') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.watermarkText2') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="temp.ctrlValueList" :label="256" :disabled="temp.validateValueList.indexOf(131072)==-1">
                {{ $t('pages.watermarkText3') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.watermarkText4') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </el-col>
          </FormItem>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <batch-edit-page-dlg ref="batchDeleteDlg" :title="i18nConcatText(this.$t('pages.outgoingTemplate'), 'delete')" :col-model="colModel" :row-data-api="rowDataApi" @submitEnd="deleteData"/>
  </div>
</template>

<script>
import {
  getPage, getByName, createData, updateData, deleteData
} from '@/api/system/baseData/outgoingTemplate'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'OutgoingTemplate',
  components: { BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'templateName', width: '100' },
        { prop: 'remark', label: 'templateRemark', width: '300' },
        {
          label: 'operate', type: 'button', width: '50', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false,
      temp: {
        id: undefined,
        name: '',
        ctrlValue: 0,
        ctrlValueList: [],
        validateValue: 0,
        validateValueList: [],
        noLimitOpen: 1,
        openTimes: 65535,
        noLimitUse: 1,
        useDays: 65535,
        makeType: 0,
        canUpdate: 0,
        remark: ''
      },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        ctrlValue: 0,
        ctrlValueList: [],
        validateValue: 0,
        validateValueList: [],
        noLimitOpen: 1,
        openTimes: 65535,
        noLimitUse: 1,
        useDays: 65535,
        makeType: 0,
        canUpdate: 0,
        remark: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.outgoingTemplate'), 'update'),
        create: this.i18nConcatText(this.$t('pages.outgoingTemplate'), 'create')
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['serverList']
    }
  },
  watch: {
    'temp.validateValueList'(newVal, oldVal) {
      if (newVal.indexOf(131072) == -1 && this.temp.ctrlValueList.indexOf(256) != -1) {
        this.temp.ctrlValueList.splice(this.temp.ctrlValueList.indexOf(256), 1)
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    changeLimitUse(val) {
      if (val == 1) {
        this.temp.useDays = 65535
      } else {
        this.temp.useDays = 0
      }
      if (val == 1 && this.temp.ctrlValueList.indexOf(8) != -1) {
        this.temp.ctrlValueList.splice(this.temp.ctrlValueList.indexOf(8), 1)
      }
    },
    changeLimitOpen(val) {
      if (val == 1) {
        this.temp.openTimes = 65535
      } else {
        this.temp.openTimes = 0
      }
    },
    ctrlChange(val) {
      if (this.temp.ctrlValueList.indexOf(4) != -1 && this.temp.ctrlValueList.indexOf(1) == -1) {
        this.temp.ctrlValueList.push(1)
      }
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    browserTable() {
      return this.$refs['browserList']
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.$set(this.temp, 'ctrlValueList', this.numToList(this.temp.ctrlValue, 9))
      this.$set(this.temp, 'validateValueList', this.numToList(this.temp.validateValue, 23))
      if (this.temp.openTimes == 0) {  // 如果次数为0，表示不限制
        // 界面输入框置灰，显示65535
        this.temp.openTimes = 65535
        this.temp.noLimitOpen = 1
      }
      if (this.temp.useDays == 0) {  // 如果次数为0，表示不限制
        // 界面输入框置灰，显示65535
        this.temp.useDays = 65535
        this.temp.noLimitUse = 1
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.formatterData(tempData)
          createData(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.formatterData(tempData)
          updateData(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.updateRowData(respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    formatterData(tempObj) {
      if (tempObj.noLimitOpen == 1) {  // 不限制次数
        tempObj.openTimes = 0
      }
      if (tempObj.noLimitUse == 1) {  // 不限制天数
        tempObj.useDays = 0
      }
      if (tempObj.validateValueList.indexOf(131072) == -1 && tempObj.ctrlValueList.indexOf(256) != -1) {
        tempObj.ctrlValueList.splice(tempObj.ctrlValueList.indexOf(256), 1)
      }
      if (tempObj.ctrlValueList.indexOf(8) != -1 && tempObj.noLimitUse == 1) {
        tempObj.ctrlValueList.splice(tempObj.ctrlValueList.indexOf(8), 1)
      }
      tempObj.ctrlValue = this.getSum(tempObj.ctrlValueList)
      tempObj.validateValue = this.getSum(tempObj.validateValueList)
      tempObj.remark = this.remarkFormatter(tempObj)
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getByName({ name: value }).then(respond => {
          const timeInfo = respond.data
          if (timeInfo && timeInfo.id != this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    remarkFormatter(row) {
      let msg = ''
      msg += row.canUpdate == 1 ? `${this.$t('pages.outgoingTemplate_text1')}，` : `${this.$t('pages.outgoingTemplate_text2')}，`
      msg += row.ctrlValue & 1 ? `${this.$t('pages.ctrlValueList2')}，` : `${this.$t('pages.ctrlValueList8')}，`
      msg += row.ctrlValue & 2 ? `${this.$t('pages.ctrlValueList3')}，` : `${this.$t('pages.ctrlValueList9')}，`
      msg += row.ctrlValue & 4 ? `${this.$t('pages.ctrlValueList1')}，` : `${this.$t('pages.ctrlValueList10')}，`
      msg += row.ctrlValue & 8 ? `${this.$t('pages.ctrlValueList4')}，` : `${this.$t('pages.ctrlValueList11')}，`
      msg += row.ctrlValue & 16 ? `${this.$t('pages.ctrlValueList7')}，` : `${this.$t('pages.ctrlValueList12')}，`
      msg += row.ctrlValue & 32 ? `${this.$t('pages.ctrlValueList5')}，` : `${this.$t('pages.ctrlValueList13')}，`
      msg += row.ctrlValue & 64 ? `${this.$t('pages.ctrlValueList6')}，` : `${this.$t('pages.ctrlValueList14')}，`
      msg += row.ctrlValue & 128 ? `${this.$t('pages.ctrlValueList15')}，` : `${this.$t('pages.ctrlValueList16')}，`
      msg += row.ctrlValue & 256 ? `${this.$t('pages.ctrlValueList17')}，` : `${this.$t('pages.ctrlValueList18')}，`
      msg += row.openTimes == 0 ? `${this.$t('pages.openTimes')}，` : `${this.$t('pages.openTimes1', { times: row.openTimes })}，`
      msg += row.useDays == 0 ? `${this.$t('pages.useDays')}，` : `${this.$t('pages.useDays1', { days: row.useDays })}，`
      msg += (row.makeType == 0 ? `${this.$t('pages.makeType1')}，` : row.makeType == 1 ? `${this.$t('pages.makeType2')}，` : `${this.$t('pages.makeType3')}，`)
      msg += row.validateValue & 65536 ? `${this.$t('pages.validateValue1')}，` : `${this.$t('pages.validateValue8')}，`
      msg += row.validateValue & 131072 ? `${this.$t('pages.validateValue2')}，` : `${this.$t('pages.validateValue9')}，`
      msg += row.validateValue & 262144 ? `${this.$t('pages.validateValue3')}，` : `${this.$t('pages.validateValue10')}，`
      msg += row.validateValue & 524288 ? `${this.$t('pages.validateValue4')}，` : `${this.$t('pages.validateValue11')}，`
      msg += row.validateValue & 1048576 ? `${this.$t('pages.validateValue5')}，` : `${this.$t('pages.validateValue12')}，`
      msg += row.validateValue & 2097152 ? `${this.$t('pages.validateValue6')}，` : `${this.$t('pages.validateValue13')}，`
      msg += row.validateValue & 4194304 ? `${this.$t('pages.validateValue7')}，` : `${this.$t('pages.validateValue14')}，`
      return msg
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteData(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    }
  }
}
</script>
