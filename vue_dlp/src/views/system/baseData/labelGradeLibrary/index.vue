<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <span>
          <label>启用等级：</label>
          <el-select v-model="query.enableGrade" style="width: 150px">
            <el-option v-for="(label, value) in gradeOptions" :key="value" :label="label" :value="Number(value)"></el-option>
          </el-select>
        </span>
        <el-button type="primary" size="mini" @click="saveEnableGrade">
          {{ $t('button.save') }}
        </el-button>
        <!-- <el-button icon="el-icon-error" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.merger') }}
          <el-tooltip effect="dark" placement="bottom-end">
            <div slot="content">{{ $t('pages.documentGradeTips') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button> -->
        <!-- <el-button v-permission="'232'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'240'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button> -->
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('table.gradeName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="labelInfoList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.labelGrade')" prop="grade">
          <el-input-number v-model="temp.grade" :controls="false" :min="0" :max="99" :step="1" step-strictly disabled/>
        </FormItem>
        <div v-for="item in langDataList" :key="item.lang">
          <FormItem :label="item.label" :prop="item.prop">
            <el-input v-model="item.content" :maxlength="99" :disabled="temp.grade == 0" @input="item.content=item.content.replace(/[|,'，]/g, '')"></el-input>
          </FormItem>
        </div>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="合并文档等级"
      :visible.sync="dialogDeleteFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="gradeDataForm"
        :rules="gradeRules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.labelGrade')" prop="grade">
          <tree-select
            ref="gradeTreeSelect"
            v-model="temp.gradeId"
            :data="realLabelGradeTree"
            :width="200"
            style="width: 200px;"
            @change="handleCheckChange"
          />
          <!-- :checked-keys="[temp.gradeId]" -->
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveGrade()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogDeleteFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  add, update, deleteGrade, getPage, countByGrade, getAllList, getMaxGrade, getEnableGrade, saveEnableGrade
} from '@/api/system/baseData/labelGradeLibrary'
import { mapGetters } from 'vuex'

export default {
  name: 'LabelGradeLibrary',
  components: { },
  data() {
    return {
      colModel: [
        { prop: 'grade', label: 'labelGrade', width: '150', sort: true },
        { prop: 'content', label: 'gradeName', width: '150', formatter: this.gradeNameFormatter },
        { prop: 'remark', label: 'remark', width: '200' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined,
        enableGrade: null
      },
      showTree: true,
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        langKey: '',
        grade: undefined,
        content: '',
        remark: '',
        type: 0,
        gradeId: null,
        active: 0,
        selectGradeLangKey: null
      },
      langDataList: [],
      gradeOptions: {},
      rules: {
        grade: [
          { required: true, validator: this.gradeValidator, trigger: 'blur' }
        ],
        content: [
          { required: true, validator: this.contentValidator, trigger: 'blur' }
        ]
      },
      gradeRules: {
        grade: [
          { required: true, message: '请选择文档等级', trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogDeleteFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('table.labelGrade'), 'update'),
        create: this.i18nConcatText(this.$t('table.labelGrade'), 'create')
      },
      treeSelectNode: [],
      labelGradeTree: [],
      realLabelGradeTree: [],
      showDialog: true
    }
  },
  computed: {
    gridTable() {
      return this.$refs['labelInfoList']
    },
    ...mapGetters([
      'languageOption'
    ])
  },
  watch: {
  },
  async created() {
    this.loadLangDataList()
    this.loadGradeList()
    this.resetTemp()
    await this.getEnableGrade()
  },
  methods: {
    loadLangDataList() {
      if (this.languageOption && this.languageOption.length > 0) {
        const langDataList = []
        const length = this.languageOption.length
        for (let i = length - 1; i >= 0; i--) {
          const lang = this.languageOption[i]
          const data = { label: lang.label, lang: lang.lang, content: '', prop: lang.lang == 'zh' ? 'content' : '' } // , prop2: lang.lang == 'zh' ? 'content' : ''
          langDataList.push(data)
        }
        this.langDataList.splice(0, this.langDataList.length, ...langDataList)
      }
    },
    getEnableGrade() {
      getEnableGrade().then(res => {
        this.query.enableGrade = parseInt(res.data)
      })
    },
    loadGradeList() {
      getAllList().then(res => {
        const data = res.data
        if (data && data.length == 1) {
          this.showDialog = false
        }
        if (data) {
          const gradeOptions = {}
          data.forEach(item => {
            const index = item.content.indexOf('zh:')
            item.label = index > -1 ? item.content.slice(index + 3, item.content.length) : item.content
            gradeOptions[item.id] = item.label + '(等级' + item.grade + ')'
          })
          this.gradeOptions = gradeOptions
          this.labelGradeTree = data
        }
      })
    },
    handleCheckChange(data, node, vm) {
      this.temp.gradeId = node.grade
      this.temp.selectGradeLangKey = node.langKey
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.loadLangDataList()
    },
    resetGrade() {
      this.$nextTick(() => {
        this.temp.gradeId = null
        this.temp.selectGradeLangKey = null
        this.$refs['gradeTreeSelect'].clearSelectedNode()
        this.$refs['gradeTreeSelect'].clearValidate()
      })
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    async handleCreate() {
      this.resetTemp()
      await getMaxGrade().then(res => {
        const maxGrade = res.data
        this.temp.grade = Number(maxGrade) + 1
      })
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    saveEnableGrade() {
      saveEnableGrade({ gradeId: this.query.enableGrade }).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: '保存成功',
          type: 'success',
          duration: 2000
        })
        // 通知应用到该配置的标签树重新请求，更新数据
        this.$store.dispatch('commonData/changeNotice', 'updateFileGrade')
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      const selectDatas = this.gridTable.getSelectedDatas()
      const selectLangKey = selectDatas.map(data => data.langKey).join(',')
      if (toDeleteIds && toDeleteIds.length > 0) {
        if (this.showDialog) {
          // 当前等级数大于1条时，删除需指定合并至哪一等级
          this.dialogDeleteFormVisible = true
          this.$nextTick(() => {
            this.temp.gradeId = null
            this.$refs['gradeTreeSelect'].clearSelectedNode()
          })
        } else {
          this.$confirmBox(this.$t('pages.confirmMsg'), this.$t('text.prompt')).then(() => {
            deleteGrade({ ids: toDeleteIds.join(','), langKey: selectLangKey }).then(respond => {
              this.gridTable.deleteRowData(toDeleteIds)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.collectService_notifyMsg8'),
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {})
        }
      }
    },
    saveGrade() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      const selectDatas = this.gridTable.getSelectedDatas()
      const selectLangKey = selectDatas.map(data => data.langKey).join(',')
      this.$confirmBox(this.$t('pages.confirmMsg1'), this.$t('text.prompt')).then(() => {
        deleteGrade({ ids: toDeleteIds.join(','), grade: this.temp.gradeId, langKey: selectLangKey, targetLangKey: this.temp.selectGradeLangKey }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.loadGradeList()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.collectService_notifyMsg8'),
            type: 'success',
            duration: 2000
          })
          // 通知应用到该配置的标签树重新请求，更新数据
          this.$store.dispatch('commonData/changeNotice', 'updateFileGrade')
          this.dialogDeleteFormVisible = false
        })
      }).catch(() => {})
    },
    handleUpdate(row) {
      this.resetTemp()
      this.formatRowData(row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.temp = Object.assign({}, row)
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    formatRowData(data) {
      const content = data.content.split('|')
      const langMap = {}
      content.forEach(item => {
        const lang = item.split(':')
        langMap[lang[0]] = lang[1]
      });
      this.langDataList.forEach(item => {
        const key = item.lang
        if (langMap[key]) {
          item.content = langMap[key]
        }
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
      if (this.deleteable) {
        const selectIds = this.gridTable.getSelectedIds()
        this.realLabelGradeTree = this.labelGradeTree.filter(node => selectIds.indexOf(node.id) == -1)
      }
    },
    createData() {
      this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          add(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.loadGradeList()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateFileGrade')
          }).catch(res => {
            this.submitting = false
          })
        }
      })
    },
    updateData() {
      this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          update(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.loadGradeList()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateFileGrade')
          }).catch(res => {
            this.submitting = false
          })
        }
      })
    },
    formatFormData() {
      if (this.langDataList && this.langDataList.length > 0) {
        const langKey = '{{label.grade.' + this.temp.grade + '}}'
        let content = ''
        const length = this.langDataList.length
        for (let i = length - 1; i >= 0; i--) {
          const item = this.langDataList[i]
          if (item.content) {
            content += item.lang + ':' + item.content + (i == 0 ? '' : '|')
          }
        }
        content.slice(0, -2)
        this.temp.content = content
        this.temp.langKey = langKey
      }
    },
    contentValidator(rule, value, callback) {
      if (this.langDataList[0] && this.langDataList[0].content) {
        callback()
      } else {
        callback(new Error(this.$t('pages.labelGradeTips')))
      }
    },
    gradeValidator(rule, value, callback) {
      if (!this.temp.id && !this.temp.grade) {
        callback(new Error('请配置文档等级'))
      }
      countByGrade({ id: this.temp.id, grade: this.temp.grade }).then(respond => {
        const data = respond.data
        if (data > 0) {
          callback(new Error(this.$t('pages.labelGradeTips1')))
        } else {
          callback()
        }
      })
    },
    gradeNameFormatter(row, data) {
      const nameList = []
      if (data) {
        const names = data.split('|')
        names.forEach(n => {
          const zhIndex = n.indexOf('zh:')
          const twIndex = n.indexOf('tw:')
          const enIndex = n.indexOf('en:')
          if (twIndex > -1) {
            nameList.push(n.slice(twIndex + 3, n.length))
          }
          if (zhIndex > -1) {
            nameList.push(n.slice(zhIndex + 3, n.length))
          }
          if (enIndex > -1) {
            nameList.push(n.slice(enIndex + 3, n.length))
          }
        })
      }
      return nameList.reverse().join(',')
    }
    // exportFunc(formData, opts) {
    //   if (formData.type == 3) {
    //     const q = Object.assign({}, this.query)
    //     return exportExcel(q, opts)
    //   } else {
    //     return exportExcel({
    //       ids: formData.type === 1 ? formData.dataIds.join(',') : null,
    //       groupId: formData.type === 2 ? formData.groupId : null,
    //       groupName: formData.type === 2 ? formData.groupName : null
    //     }, opts)
    //   }
    // },
    // importEndFunc(groupId) {
    //   this.loadReceiverTree().then(() => {
    //     this.$nextTick(() => {
    //       groupId = groupId ? String(groupId) : '0'
    //       const node = this.dataToTreeNode({ id: groupId })
    //       this.labelInfoGroupTree.selectCurrentNode(node.id)
    //     })
    //   })
    // }
  }
}
</script>

<style lang='scss' scoped>
  /deep/ .el-input-number.is-without-controls .el-input__inner {
    text-align: left;
  }
</style>
