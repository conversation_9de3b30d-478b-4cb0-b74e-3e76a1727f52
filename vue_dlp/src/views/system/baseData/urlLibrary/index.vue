<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="urlGroupTree"
        resizeable
        :default-expand-all="true"
        :data="urlGroupTreeData"
        :render-content="renderContent"
        @node-click="urlGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-permission="'249'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <!-- <el-button v-permission="'250'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button> -->
        <!--<el-button icon="el-icon-download" size="mini" @click="handleImport">
          内置数据导入
        </el-button>-->
        <el-dropdown v-permission="'250'" style="padding-left: 10px;" @command="handleImportControl">
          <el-button size="mini">
            {{ $t('button.import') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1" icon="el-icon-reading">{{ $t('pages.importFromFile') }}</el-dropdown-item>
            <el-dropdown-item :command="2" icon="el-icon-reading">{{ $t('pages.importFromBuiltInURLLibrary') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.websiteNameOrURL')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="urlList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="urlRules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('pages.websiteName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.websiteUrl')" prop="address">
          <el-input v-model="temp.address" v-trim :maxlength="60" :placeholder="$t('pages.urlLibrary_text1')"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="temp.groupId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <export-dlg ref="exportDlg" :group-tree-data="urlGroupTreeData" :export-func="exportFunc" :group-tree-id="query.groupId"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      template="url"
      :show-import-type="false"
      :file-name="title"
      :tip="tip"
      :show-import-way="true"
      :upload-func="upload"
      @success="importEndFunc"
    />
    <url-lib-import-dlg ref="urlLibImportDlg" :group-tree-data="formTreeData" @submitEnd="importEndFunc"/>
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.urlGroup')"
      :group-tree-data="formTreeData"
      :add-func="createUrlGroup"
      :update-func="updateUrlGroup"
      :delete-func="deleteUrlGroup"
      :move-func="moveGroup"
      :edit-valid-func="getUrlGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteUrlGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? i18nConcatText(this.$t('pages.urlGroup'), 'update') : i18nConcatText(this.$t('table.websiteUrl'), 'delete')"
      :edit-type="updateGroupForm ? 0 : 1"
      :delete-filter-name="$t('table.websiteUrl')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="urlGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>
  </div>
</template>

<script>
import {
  getTreeNode, createUrlGroup, updateUrlGroup, deleteUrlGroup, getUrlGroupByName, getUrlList,
  createUrl, updateUrl, deleteUrl, countUrlByGroupId, getUrlByAdress, getUrlLibTree,
  moveGroup, exportExcel, deleteGroupAndData, moveGroupToOther, batchUpdateGroup, batchUpdateAllGroup
} from '@/api/system/baseData/urlLibrary'
import { findNodeLabel } from '@/utils/tree'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import EditGroupDlg from '@/views/common/editGroupDlg'
import request from '@/utils/request'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg';
import UrlLibImportDlg from '@/views/system/baseData/urlLibrary/UrlLibImportDlg';
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'UrlLibrary',
  components: { BatchEditPageDlg, DeleteGroupDlg, ExportDlg, ImportDlg, EditGroupDlg, UrlLibImportDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'websiteName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter, sort: 'custom' },
        { prop: 'address', label: this.$t('pages.websiteUrl'), width: '150', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '200', sort: 'custom' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: ''
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        remark: '',
        address: '',
        groupId: ''
      },
      tip: this.$t('table.websiteUrl'),
      formTreeData: [],
      urlLibTreeData: [],
      urlRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_enterUrl'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('table.websiteUrl'), 'update'),
        create: this.i18nConcatText(this.$t('table.websiteUrl'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.urlGroup'), 'delete')
      },
      treeNodeType: 'G',
      urlGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.urlLibrary'), parentId: '', children: [] }],
      treeSelectNode: [],
      title: this.$t('pages.websiteInformation'),
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined // 批量修改的分组id
    }
  },
  computed: {
    gridTable() {
      return this.$refs['urlList']
    },
    urlGroupTree: function() {
      return this.$refs['urlGroupTree']
    }
  },
  activated() {
    this.loadUrlTree()
    this.loadUrlLibTree()
  },
  created() {
    this.resetTemp()
    this.loadUrlTree()
    this.loadUrlLibTree()
  },
  methods: {
    createUrlGroup,
    updateUrlGroup,
    deleteUrlGroup,
    getUrlGroupByName,
    moveGroup,
    deleteGroupAndData,
    moveGroupToOther,
    loadUrlLibTree() {
      getUrlLibTree().then(res => {
        this.urlLibTreeData = res.data
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getUrlList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    urlGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    loadUrlTree: function(groupId) {
      return getTreeNode().then(respond => {
        this.urlGroupTreeData[0].children = respond.data
        this.formTreeData = this.loadTypeTreeExceptRoot()
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
        this.$nextTick(() => {
          this.urlGroupTree.selectCurrentNode(this.treeNodeType + (groupId || '0'))
        })
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.urlGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    loadTypeTreeExceptRoot() {
      return this.urlGroupTreeData[0].children
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query)
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          groupName: formData.type === 2 ? formData.groupName : null
        }, opts)
      }
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.urlGroupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.urlGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.urlGroupTree.findNode(this.urlGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.urlGroupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    removeNode(data) {
      countUrlByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleUrlGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleUrlGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleMoving() {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImportControl(type) {
      if (type === 1) {
        this.handleImport();
      } else {
        this.handleUrlDefaultImport();
      }
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleUrlDefaultImport() {
      this.$refs.urlLibImportDlg.show()
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createUrl(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateUrl(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    addressValidator(rule, value, callback) {
      getUrlByAdress({ url: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleUrlGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleUrlGroupUpdate(data)}/>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    pushChildDataIds: function(toDeleteIds, data) {
      data.children.forEach(child => {
        toDeleteIds.push(child.dataId)
        if (child.children && child.children.length > 0) {
          this.pushChildDataIds(toDeleteIds, child)
        }
      })
    },
    upload(data) {
      return request.post('/url/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc(groupId) {
      this.loadUrlTree(groupId)
      this.handleFilter()
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.urlGroupTreeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.urlGroupTree.findNode(this.urlGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.urlGroupTree.setCurrentKey(nodeData.id)
        this.urlGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        params.filterUsedUrl = params.filterUsed
        deleteUrl(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    }
  }
}
</script>
