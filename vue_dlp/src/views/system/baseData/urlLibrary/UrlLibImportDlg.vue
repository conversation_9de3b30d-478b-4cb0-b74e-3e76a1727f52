<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.builtInWebsite')"
    :visible.sync="dlgVisible"
    width="850px"
  >
    <div>
      <div class="tree-container">
        <tree-menu
          ref="urlGroupLibTree"
          multiple
          :height="340"
          :data="urlGroupTreeData"
          :default-expanded-keys="defaultExpandedKeys"
          @node-click="urlGroupCheckChange"
          @check="urlGroupCkeckFunc"
        />
      </div>
      <div class="table-container">
        <div class="toolbar">
          <div class="searchCon">
            <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.websiteNameOrURL')" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <div class="">
          <grid-table
            ref="urlList"
            :col-model="colModel"
            :height="300"
            pager-small
            :default-sort="{ prop: 'name', order: 'ascending' }"
            :row-data-api="rowDataApi"
            is-saved-selected
            :selected-data-label-formatter="(row, prop) => `${row.name} (${row.address})`"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
      </div>
      <div>
        <Form ref="dataForm" label-width="120px" :extra-width="{en: 90}" :model="temp">
          <FormItem :label="$t('pages.repeatNameDealType')" prop="objectType">
            <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
              <div slot="content">
                <i18n path="pages.repeatNameDealTypeContent">
                  <template slot="tip">{{ $t('table.websiteUrl') }}</template>
                  <br slot="br"/>
                </i18n>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-radio-group v-model="temp.importType">
              <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
              <!--              <el-radio :label="2">{{ $t('pages.importAndRename') }}</el-radio>-->
            </el-radio-group>
          </FormItem>
          <FormItem :label="$t('pages.importGroupDisposal')" prop="objectType">
            <el-radio-group v-model="temp.groupType" style="display: flex; flex-direction: column;">
              <el-radio :label="1">
                <span>{{ $t('pages.importGroupDisposalType1', { type: $t('table.websiteUrl'), target: $t('pages.urlLibrary')}) }}</span>
              </el-radio>
              <div style="display: flex">
                <el-radio :label="0" style="margin-right: 10px;">
                  <i18n path="pages.importGroupDisposalType2">
                    <template slot="type">{{ $t('pages.websiteInformation') }}</template>
                  </i18n>
                </el-radio>
                <tree-select
                  v-model="temp.targetGroupId"
                  :checked-keys="[temp.targetGroupId]"
                  :disabled="temp.groupType == 1"
                  :data="groupTreeData"
                  node-key="dataId"
                  :width="296"
                  @change="groupSelectChange"
                />
              </div>
            </el-radio-group>
          </FormItem>
        </Form>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="importUrl()">
        {{ $t('button.import') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { findNodeLabel } from '@/utils/tree'
import {
  getUrlLibList,
  getUrlLibTree,
  addFromUrlLib,
  getImportProgress
} from '@/api/system/baseData/urlLibrary'

export default {
  name: 'UrlLibImportDlg',
  props: {
    groupTreeData: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      dlgVisible: false, // 列表页是否显示
      submitting: false,
      defaultExpandedKeys: ['G0'],
      urlGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.builtInWebsiteInformation'), parentId: '', children: [] }],
      colModel: [
        { prop: 'name', label: 'websiteName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter, sort: 'custom' },
        { prop: 'address', label: 'websiteUrl', width: '150', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '200' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: null,
        groupIds: ''
      },
      temp: {},
      defaultTemp: {
        importType: 1,
        groupType: 1,
        targetGroupId: undefined
      }
    }
  },
  computed: {
    urlGroupLibTree: function() {
      return this.$refs['urlGroupLibTree']
    },
    gridTable() {
      return this.$refs['urlList']
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    show() {
      this.resetTemp()
      console.log('this.temp', this.temp)
      this.loadUrlGroupTreeData()
      this.dlgVisible = true
      this.$nextTick(() => {
        this.handleFilter()
        this.urlGroupLibTree && this.urlGroupLibTree.clearFilter()
        this.urlGroupLibTree && this.urlGroupLibTree.clearSelectedNodes()
      })
    },
    hide() {
      this.dlgVisible = false
    },
    // 获取模板库的树数据
    loadUrlGroupTreeData: function() {
      if (this.urlGroupTreeData[0].children.length === 0) {
        getUrlLibTree().then(respond => {
          this.urlGroupTreeData[0].children = respond.data
        })
      }
    },
    urlGroupCkeckFunc: function(nodeData, checkedInfo) {
      // this.importAbleChange(checkedInfo)
    },
    urlGroupCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      // this.addBtnAble = !!checkedNode && checkedNode.data.dataId !== '0'
      const checkNodeIds = []
      this.listNodeIdAndChildNodeIds(checkNodeIds, checkedNode.data)
      this.query.groupIds = checkNodeIds.join(',')
      // if (checkedNode) {
      //   this.query.groupId = checkedNode.data.dataId
      // } else {
      //   this.query.groupId = undefined
      // }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    listNodeIdAndChildNodeIds(checkNodeIds, checkedData) {
      checkNodeIds.push(checkedData.dataId)
      if (checkedData.children) {
        checkedData.children.forEach(data => {
          this.listNodeIdAndChildNodeIds(checkNodeIds, data)
        })
      }
    },
    // 列表
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getUrlLibList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    groupFormatter(row, data) {
      return findNodeLabel(this.urlGroupTreeData, data, 'dataId')
    },
    // 表单
    groupSelectChange(data) {
      this.temp.targetGroupId = data
    },
    // 功能
    async importUrl() {
      this.submitting = true
      const nodes = this.$refs.urlGroupLibTree.getCheckedNodes()
      const tableDatas = this.$refs.urlList.getSelectedDatas()
      const targetGroupId = this.temp.targetGroupId;
      const groupType = this.temp.groupType
      if (nodes.length == 0 && tableDatas.length == 0) {
        this.$message({
          message: this.$t('pages.urlLibrary_text3'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      if (groupType == 0 && (typeof targetGroupId == 'undefined' || targetGroupId === '')) {
        this.$message({
          message: this.$t('pages.validaGroup'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      const groupIds = []
      const urlIds = []
      nodes.forEach(item => {
        if (item.type == 'G') {
          groupIds.push(item.dataId)
        }
      })

      if (groupIds.length != 0) {
        const resp = await getUrlLibList({
          page: 1,
          size: 999,
          groupIds: groupIds.join(',')
        })
        tableDatas.push(...resp.data.items)
      }

      tableDatas.forEach(item => {
        urlIds.push(item.id)
      })
      const param = {
        groupIds: groupIds.join(','),
        urlIds: urlIds.join(','),
        importWay: this.temp.importType,
        targetGroupId: this.temp.targetGroupId
      }
      getImportProgress().then(resp => {
        if (resp.data.percent == 0 || resp.data.percent >= 100) {
          this.$confirmBox(this.$t('pages.confirmToImportTheSelected', { info: this.$t('table.websiteUrl'), target: this.$t('pages.urlLibrary') }), this.$t('text.prompt')).then(() => {
            addFromUrlLib(param).then(respond => {
              this.submitting = false
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.importSuccess'),
                type: 'success',
                duration: 2000
              })
              this.hide()
              this.$emit('submitEnd')
            }).catch(res => {
              this.submitting = false
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.importExecutingMsg', { percent: resp.data.percent }),
            type: 'info',
            duration: 2000
          })
        }
      })
    }
  }
}
</script>
