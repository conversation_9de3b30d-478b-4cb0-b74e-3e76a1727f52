<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="$t('pages.urlLibrary')"
    :remark="$t('pages.urlLibrary_text5')"
    :visible.sync="dlgVisible"
    width="850px"
    @dragDialog="handleDrag"
  >
    <el-container>
      <el-aside width="210px">
        <tree-menu
          ref="groupTree"
          :height="400"
          :data="treeData"
          :multiple="multipleGroup"
          :default-expanded-keys="defaultExpandedKeys"
          @node-click="treeNodeClick"
        />
      </el-aside>
      <el-main>
        <div class="toolbar">
          <el-row>
            <el-col :span="9"><label style="font-size: 15px;"></label></el-col>
            <el-col :span="15">
              <div style="float: right;">
                <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.websiteName')" style="width: 200px;"/>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
        <grid-table
          ref="appInfoList"
          :height="360"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          is-saved-selected
          :selected-data-label-formatter="(row, prop) => `${row.name} (${row.address})`"
          :default-sort="{ prop: 'name' }"
          pager-small
          :page-sizes="[ 20, 50, 100, 500, 1000 ]"
        />
      </el-main>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <link-button btn-type="primary" btn-style="float: left" :menu-code="'A53'" :link-url="'/system/baseData/urlLibrary'" :btn-text="$t('pages.maintainWeb')"/>
      <el-button type="primary" :loading="submitting" @click="handleSelect()">
        {{ $t('pages.addWebsite') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTreeNode, getUrlList, listUrlByGroupId } from '@/api/system/baseData/urlLibrary'
import { findNode } from '@/utils/tree'

export default {
  name: 'UrlSelectDlg',
  props: {
    appendToBody: { type: Boolean, default: false },
    multipleGroup: { type: Boolean, default: true }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'websiteName', width: '150', sort: 'custom', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'address', label: 'websiteUrl', width: '150', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '200', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        usbName: '',
        groupId: 0
      },
      dlgVisible: false,
      submitting: false,
      selectTreeId: null,
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.urlLibrary'), parentId: '', children: [] }],
      defaultExpandedKeys: ['G0']
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.selectTreeId = null
      this.query.groupId = 0
      this.query.usbName = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.softTable && this.softTable.clearSaveSelection()
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNode()
      })
    },
    treeNodeClick(data, node) {
      this.selectTreeId = data.dataId
      this.query.groupId = data.dataId;
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        this.selectTreeId = 0
      }
      this.query.groupId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      return getUrlList(newOption)
    },
    loadGroupTree: function() {
      getTreeNode().then(respond => {
        this.treeData[0].children = respond.data
        this.$nextTick(() => {

        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      this.getSelectedDatas().then(resp => {
        if (!resp || resp.length == 0) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.url_text2'),
            type: 'error',
            duration: 2000
          })
          return
        }
        this.$emit('select', resp)
        this.dlgVisible = false
      })
    },
    async getSelectedDatas() {
      const datas = this.softTable.getSelectedDatas()
      if (this.multipleGroup) { // 说明支持选择类型
        const groupNodes = this.$refs['groupTree'].getCheckedNodes()
        const groupIds = []
        groupNodes.forEach(data => groupIds.push(data.dataId))
        if (groupIds.length > 0) {
          await listUrlByGroupId({ groupIds: groupIds.join(',') }).then(resp => {
            datas.splice(0, 0, ...resp.data)
          })
        }
      }
      return datas
    },
    groupFormatter(row, data) {
      const nodeData = findNode(this.treeData, data, 'dataId')
      return nodeData ? nodeData.label : ''
    }
  }
}
</script>
