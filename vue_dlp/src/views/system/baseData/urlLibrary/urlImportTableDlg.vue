<template>
  <div>
    <!-- 网址的新增修改-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[urlDialogStatus]"
      :visible.sync="urlDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="urlDataForm"
        :rules="urlRules"
        :model="urlTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('pages.websiteName')" prop="name">
          <el-input v-model="urlTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.websiteUrl')" prop="address">
          <el-input v-model="urlTemp.address" v-trim :maxlength="60" :placeholder="$t('pages.urlLibrary_text1')"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="24">
              <el-select v-model="urlTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in urlTreeNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="urlTemp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="urlDialogStatus === 'createUrl' ? createUrlData() : updateUrlData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="urlDialogFormVisible = false;">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-table-dlg
      ref="urlImportTable"
      :elg-title="$t('pages.importUrlLibrary')"
      :group-root-name="$t('pages.urlLibrary')"
      :search-info-name="$t('pages.websiteNameOrURL')"
      :confirm-button-name="$t('pages.addUrl')"
      :group-title="$t('pages.url_group_title')"
      :col-model="importColModel"
      :list="getUrlList"
      :load-group-tree="getTreeNode"
      :create-group="createUrlGroup"
      :update-group="updateUrlGroup"
      :delete-group="deleteUrlGroup"
      :count-by-group="countUrlByGroupId"
      :get-group-by-name="getUrlGroupByName"
      :delete="deleteUrl"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteUrlGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleCreateElg"
      :get-list-by-group-ids="listUrlByGroupId"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="importDeleteEnd"
      @changeGroupAfter="changeGroupAfter"
    />
  </div>
</template>
<script> 
import {
  countUrlByGroupId, createUrl, createUrlGroup, deleteGroupAndData, deleteUrl, deleteUrlGroup,
  getTreeNode, getUrlByAdress, getUrlGroupByName, getUrlList,
  listUrlByGroupId, moveGroupToOther, updateUrl, updateUrlGroup
} from '@/api/system/baseData/urlLibrary'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import { findNodeLabel } from '@/utils/tree';
export default {
  name: 'UrlImportTableDlg',
  components: { ImportTableDlg },
  props: {
    /**
     * 组件在哪个模块引用
     */
    moduleName: {
      type: String,
      default: function() {
        return 'fileOutgoingCheckStatistics'
      }
    }
  },
  data() {
    return { 
      importColModel: [
        { prop: 'name', label: 'websiteName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'websiteUrl', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdateImport }
          ]
        }
      ],
      textMap: {
        createUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'create'),
        updateUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'update')
      },
      urlRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_enterUrl'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }
        ]
      },
      urlDialogStatus: '',
      urlTreeNode: [],
      urlDialogFormVisible: false,
      needValidateAddress: true,
      urlTemp: {},
      defaultUrlTemp: { // 表单字段
        id: undefined,
        name: '',
        remark: '',
        address: '',
        groupId: ''
      },
      dlgSubmitting: false
    }
  },
  created() { 
    this.loadGroupTree()
  },
  activated() {
    this.loadGroupTree()
  },
  methods: { 
    createUrl,
    updateUrl,
    getTreeNode,
    listUrlByGroupId,
    createUrlGroup,
    updateUrlGroup,
    deleteUrlGroup,
    getUrlGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    countUrlByGroupId,
    deleteUrl,
    getUrlList,
    createUrlData() {
      this.dlgSubmitting = true
      this.$refs['urlDataForm'].validate((valid) => {
        if (valid) {
          // 网址不存在，先将网址新增到网址信息库，再将网址添加到策略
          this.urlTemp.groupName = findNodeLabel(this.treeSelectNode, this.urlTemp.groupId, 'dataId')
          createUrl(this.urlTemp).then(respond => {
            const url = respond.data
            url.dealWay = this.dealWay
            this.dlgSubmitting = false
            this.$refs.urlImportTable.refreshTableData()
            this.urlDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    updateUrlData() {
      this.dlgSubmitting = true
      this.$refs['urlDataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.urlTemp)
          updateUrl(tempData).then(respond => {
            const url = respond.data
            url.dealWay = this.dealWay
            this.$refs.urlImportTable.refreshTableData()
            this.dlgSubmitting = false
            this.urlDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    getNeedAddIds(needIds) {
      // 这里主要是为了文件外发量统计配置邮件外发例外的时候使用，这里只需要网址，因此如果该组件需要在其它地方引用自行修改逻辑
      if (this.moduleName === 'fileOutgoingCheckStatistics') {
        if (needIds.length === 0) {
          this.$emit('getUrlAddress', [])
        } else {
          getUrlList({ ids: needIds.join(',') }).then(res => {
            const addressList = []
            res.data.items.forEach(item => {
              addressList.push(item.address)
            })
            this.$emit('getUrlAddress', addressList)
          })
        }
      }
    },
    importDeleteEnd() {
      this.$refs['urlImportTable'].refreshTableData()
    },
    changeGroupAfter() {
      this.loadGroupTree()
    },
    handleUpdateImport(row) {
      this.urlDialogStatus = 'updateUrl'
      this.loadGroupTree()
      this.urlTemp = Object.assign({}, row)
      this.urlTemp.groupId = row.groupId ? row.groupId + '' : ''
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
      this.urlDialogFormVisible = true
      this.needValidateAddress = true
    },
    handleCreateElg(selectedGroupId) {
      this.urlDialogStatus = 'createUrl'
      this.loadGroupTree()
      this.urlTemp = Object.assign({}, this.defaultUrlTemp)
      this.urlTemp.groupId = selectedGroupId
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
      this.urlDialogFormVisible = true
      this.needValidateAddress = true
    },
    handleDrag() {

    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.urlTreeNode, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    loadGroupTree: function() {
      return getTreeNode().then(res => {
        this.urlTreeNode = res.data
      })
    },
    addressValidator(rule, value, callback) {
      if (this.needValidateAddress) {
        getUrlByAdress({ url: value }).then(respond => {
          const strategy = respond.data
          if (strategy && strategy.id != this.urlTemp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      } else {
        callback()
      }
    },
    show() {
      this.$refs.urlImportTable.show()
    }
  }
}
</script>
