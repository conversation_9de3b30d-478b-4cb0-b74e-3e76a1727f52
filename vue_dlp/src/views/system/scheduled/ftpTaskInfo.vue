<template>
  <div class="table-container">
    <div class="toolbar">
      <el-descriptions size="small" :column="2" border content-class-name="desc-content">
        <el-descriptions-item v-for="(val, key) in poolInfo" :key="key" :label="key" label-align="right" label-class-name="desc-label">
          {{ val }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="searchCon">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('button.refresh') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="scheduledTable"
      :col-model="colModel"
      :row-datas="rowDatas"
      :show-pager="false"
    />
  </div>
</template>

<script>
import { getPoolInfo } from '@/api/system/deviceManage/backupServer'

export default {
  name: 'FtpTaskInfo',
  props: {
    isShow: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'DevId', label: 'deviceNum', width: '120', sort: 'custom' },
        { prop: 'NumIdle', label: 'NumIdle', width: '100' },
        { prop: 'NumActive', label: 'NumActive', width: '100' },
        { prop: 'NumWaiters', label: 'NumWaiters', width: '100' },
        { prop: 'MaxIdle', label: 'MaxIdle', width: '120' },
        { prop: 'MaxTotal', label: 'MaxTotal', width: '120' },
        { prop: 'CreatedCount', label: 'CreatedCount', width: '120' },
        { prop: 'BorrowedCount', label: 'BorrowedCount', width: '120' },
        { prop: 'ReturnedCount', label: 'ReturnedCount', width: '120' },
        { prop: 'DestroyedCount', label: 'DestroyedCount', width: '120' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      submitting: false,
      rowDatas: [],
      poolInfo: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['scheduledTable']
    }
  },
  watch: {
    isShow(val) {
      this.poolInfoApi()
    }
  },
  created() {
    this.poolInfoApi()
  },
  activated() {

  },
  methods: {
    poolInfoApi() {
      if (!this.isShow) {
        return
      }
      getPoolInfo().then(resp => {
        if (resp.data) {
          this.poolInfo = resp.data['thread']
          this.rowDatas = resp.data['conn']
        }
      })
    },
    handleFilter() {
      this.poolInfoApi()
    }
  }
}
</script>
