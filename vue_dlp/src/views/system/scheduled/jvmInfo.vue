<template>
  <div class="table-container">
    <div class="toolbar">
      <el-descriptions size="small" :column="2" border content-class-name="desc-content">
        <el-descriptions-item v-for="(val, key) in poolInfo" :key="key" :label="key" label-align="right" label-class-name="desc-label">
          {{ val }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="searchCon">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('button.refresh') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="scheduledTable"
      :col-model="colModel"
      :row-datas="rowDatas"
      :show-pager="false"
      default-expand-all
    />
  </div>
</template>

<script>
import { getJvmInfo } from '@/api/system/deviceManage/server'

export default {
  name: 'JvmInfo',
  props: {
    isShow: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'name', width: '120', sort: 'custom' },
        { prop: 'typeName', label: 'typeName', width: '120' },
        { prop: 'init', label: 'init', width: '120' },
        { prop: 'max', label: 'max', width: '120' },
        { prop: 'used', label: 'used', width: '120' },
        { prop: 'committed', label: 'committed', width: '120' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      submitting: false,
      rowDatas: [],
      poolInfo: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['scheduledTable']
    }
  },
  watch: {
    isShow(val) {
      this.poolInfoApi()
    }
  },
  created() {
    this.poolInfoApi()
  },
  activated() {

  },
  methods: {
    poolInfoApi() {
      if (!this.isShow) {
        return
      }
      getJvmInfo().then(resp => {
        if (resp.data) {
          this.poolInfo = resp.data['thread']
          this.rowDatas = resp.data['conn']
        }
      })
    },
    handleFilter() {
      this.poolInfoApi()
    }
  }
}
</script>

