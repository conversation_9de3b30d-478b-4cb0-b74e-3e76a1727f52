<template>
  <div class="app-container scheduled-container">
    <el-tabs ref="tabs" v-model="tabName" type="card" @tab-click="tabClick">
      <el-tab-pane label="JOB" name="jobTab">
        <div class="table-container">
          <div class="toolbar">
            <el-descriptions size="small" :column="2" border content-class-name="desc-content">
              <el-descriptions-item v-for="(val, key) in poolInfo" :key="key" :label="key" label-align="right" label-class-name="desc-label">
                {{ val }}
              </el-descriptions-item>
            </el-descriptions>
            <div class="searchCon">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('button.refresh') }}
              </el-button>
            </div>
          </div>
          <grid-table
            ref="scheduledTable"
            :col-model="colModel"
            :row-datas="rowDatas"
            :show-pager="false"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="FILE" name="ftpTab">
        <ftp-task-info :is-show="tabName === 'ftpTab'"/>
      </el-tab-pane>
      <el-tab-pane label="CACHE" name="cacheTab">
        <cache-info :is-show="tabName === 'cacheTab'"/>
      </el-tab-pane>
      <el-tab-pane label="DB" name="dbTab">
        <db-info :is-show="tabName === 'dbTab'"/>
      </el-tab-pane>
      <el-tab-pane label="JVM" name="jvmTab">
        <jvm-info :is-show="tabName === 'jvmTab'"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getScheduledPage, getThreadPoolInfo } from '@/api/system/scheduled'
import FtpTaskInfo from './ftpTaskInfo'
import CacheInfo from './cacheInfo'
import DbInfo from './dbInfo'
import JvmInfo from './jvmInfo'

export default {
  name: 'Scheduled',
  components: { FtpTaskInfo, CacheInfo, DbInfo, JvmInfo },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'timerName', width: '120', sort: 'custom', iconFormatter: this.stateFormat },
        { prop: 'cron', label: 'timeExpression', width: '80' },
        { prop: 'nextExecTime', label: 'nextExecTime', width: '100' },
        { prop: 'lastExecTime', label: 'lastExecTime', width: '100' },
        { prop: 'lastExecMsg', label: 'lastExecMsg', width: '120' },
        { prop: 'refclass', label: 'actuator', width: '120', formatter: this.execClassFormatter },
        { prop: 'remark', label: 'remark', width: '120' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      submitting: false,
      tabName: 'jobTab',
      rowDatas: [],
      poolInfo: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['scheduledTable']
    }
  },
  created() {
    this.poolInfoApi()
  },
  activated() {

  },
  methods: {
    tabClick(pane, event) {
      // const that = this
    },
    poolInfoApi() {
      getThreadPoolInfo().then(resp => {
        this.poolInfo = resp.data
      })
      getScheduledPage({}).then(resp => {
        this.rowDatas = resp.data.items
      })
    },
    handleFilter() {
      this.poolInfoApi()
    },
    stateFormat(row) {
      return row.state ? { class: 'active', title: '运行中' } : { class: 'offline', title: '已停止', style: 'color: #888;' }
    },
    execClassFormatter(row) {
      return row.refclass + '.' + row.method
    }
  }
}
</script>
<style lang="scss" scoped>
.scheduled-container >>>.desc-content{
  background-color: #03333d;
  color: #c9c9c7 !important;;
}
.scheduled-container >>>.desc-label{
  background-color: #0b4855;
  color: #c9c9c7 !important;
}
</style>
