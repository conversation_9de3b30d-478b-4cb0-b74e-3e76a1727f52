<template>
  <div class="table-container">
    <div class="toolbar">
      <el-descriptions size="small" :column="2" border content-class-name="desc-content">
        <el-descriptions-item v-for="(val, key) in poolInfo" :key="key" :label="key" label-align="right" label-class-name="desc-label">
          {{ val }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="searchCon">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('button.refresh') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="scheduledTable"
      :col-model="colModel"
      :row-datas="rowDatas"
      :show-pager="false"
    />
  </div>
</template>

<script>
import { getCachePoolInfo } from '@/api/system/scheduled'

export default {
  name: 'CacheInfo',
  props: {
    isShow: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'key', label: 'key', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      submitting: false,
      rowDatas: [],
      poolInfo: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['scheduledTable']
    }
  },
  watch: {
    isShow(val) {
      this.poolInfoApi()
    }
  },
  created() {
    this.poolInfoApi()
  },
  activated() {

  },
  methods: {
    poolInfoApi() {
      if (!this.isShow) {
        return
      }
      getCachePoolInfo().then(resp => {
        if (resp.data) {
          this.poolInfo = resp.data['total']
          this.rowDatas = resp.data['data']
        }
      })
    },
    handleFilter() {
      this.poolInfoApi()
    }
  }
}
</script>
