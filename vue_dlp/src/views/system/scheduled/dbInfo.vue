<template>
  <div class="table-container">
    <div class="toolbar">
      <el-descriptions size="small" :column="2" border content-class-name="desc-content">
        <el-descriptions-item v-for="(val, key) in poolInfo" :key="key" :label="key" label-align="right" label-class-name="desc-label">
          {{ val }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="searchCon">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('button.refresh') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="scheduledTable"
      :col-model="colModel"
      :row-datas="rowDatas"
      :show-pager="false"
    />
  </div>
</template>

<script>
import { getDbPoolInfo } from '@/api/system/deviceManage/server'

export default {
  name: 'DbInfo',
  props: {
    isShow: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'name', width: '120', sort: 'custom' },
        { prop: 'isConnect', label: 'isConnect', width: '100' },
        { prop: 'totalConn', label: 'totalConn', width: '100' },
        { prop: 'activeConn', label: 'activeConn', width: '100' },
        { prop: 'idleConn', label: 'idleConn', width: '100' },
        { prop: 'threadsAwaitingConn', label: 'threadsAwaitingConn', width: '150' },
        { prop: 'running', label: 'running', width: '80' },
        { prop: 'closed', label: 'closed', width: '80' },
        { prop: 'jdbcUrl', label: 'jdbcUrl', width: '250' },
        { prop: 'idleTimeout', label: 'idleTimeout', width: '100' },
        { prop: 'maxLifetime', label: 'maxLifetime', width: '110' },
        { prop: 'connectionTimeout', label: 'connectionTimeout', width: '150' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      submitting: false,
      rowDatas: [],
      poolInfo: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['scheduledTable']
    }
  },
  watch: {
    isShow(val) {
      this.poolInfoApi()
    }
  },
  created() {
    this.poolInfoApi()
  },
  activated() {

  },
  methods: {
    poolInfoApi() {
      if (!this.isShow) {
        return
      }
      getDbPoolInfo().then(resp => {
        if (resp.data) {
          this.poolInfo = resp.data['thread']
          this.rowDatas = resp.data['conn']
        }
      })
    },
    handleFilter() {
      this.poolInfoApi()
    }
  }
}
</script>

