<template>
  <div class="app-container">
    <div class="table-container">
      <SearchToolbar
        :tree-menu-btn="false"
        @handleFilter="handleFilter"
      >
        <TimeQuery slot="time" v-model="query" :limit-day="0" />
        <SearchItem model-key="query.userName" :value="query.userName">
          <span>{{ $t('pages.operateUser') }}：</span>
          <el-input v-model="query.userName" v-trim clearable style="width: 150px;" />

        </SearchItem>
        <SearchItem model-key="query.ip" :value="query.ip">
          <span>{{ $t('table.userIP') }}：</span>
          <el-input v-model="query.ip" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.operationType" :value="query.operationType">
          <span>{{ $t('pages.operateType') }}：</span>
          <el-select v-model="query.operationType" :placeholder="$t('text.select')" style="width: 150px;">
            <el-option v-for="item in typesOptions" :key="item.label" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.menuPath" :value="query.menuPath">
          <span>{{ $t('table.menuPath') }}：</span>
          <el-input v-model="query.menuPath" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <!-- <SearchItem model-key="query.searchInfo" :value="query.searchInfo">
          <span>{{ $t('table.funName') }}：</span>
          <el-input v-model="query.searchInfo" v-trim clearable style="width: 150px;" />
        </SearchItem> -->
        
        <audit-log-exporter slot="append" v-permission="'496'" :request="exportFunc"/>
        <el-button v-if="$store.getters.adminLogDeleteAble" slot="append" v-permission="'387'" style="margin-left: 10px;" icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table ref="logList" :col-model="colModel" :multi-select="$store.getters.adminLogDeleteAble && hasPermission('387')" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd">
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ detailFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>

    <el-dialog
      v-el-drag-dialog
      :title="this.$t('pages.detail')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="showDetailDialog"
      width="800px"
    >
      <el-divider content-position="left">{{ $t('pages.baseInfo') }}</el-divider>
      <Form :model="baseInfo" label-width="70px">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.time')"> {{ baseInfo.createTime }} </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.operateUser')"> {{ baseInfo.user.name }} </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.userIP')"> {{ baseInfo.ip }} </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.menuPath')"> {{ baseInfo.menuPath }} </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.funName')"> {{ baseInfo.message }} </FormItem>
          </el-col>
        </el-row>
      </Form>
      <!-- 导入模块的查看详情 -->
      <div v-if="6 === baseInfo.operationType">
        <el-divider content-position="left">{{ $t('table.detailInfo') }}</el-divider>
        <ImportResult :reload="showDetailDialog" :data-info="baseInfo.dataInfo" :height="290" :params="{ dataId: baseInfo.id, dataClass: baseInfo.dataClass}" />
      </div>
      <div v-else >
        <el-divider content-position="left">{{ $t('pages.attributeChange') }}</el-divider>
        <grid-table :col-model="attributeChangeTableCol" :row-datas="attributeChangeData" :height="275" :multi-select="false" :show-pager="false">
          <template slot="popoverContent" slot-scope="props">
            <div style="max-height: 500px; max-width: 600px; overflow: auto;">
              <span style="padding: 5px 10px; display: inline-block;">{{ props.detail }}</span>
            </div>
          </template>
        </grid-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDetailDialog = false">
          {{ this.$t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
    <oplog-detail-dlg ref="detailDlg" :menu-code-formatter="menuPathFormatter"/>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getLogPage, deleteLog, exportLog } from '@/api/system/organizational/opLog'
import { getCurrentRoleId } from '@/api/system/organizational/sysUser'
import ImportResult from './importResult';
import { getSysUserByRoleId } from '@/api/user'
import { getManagerRoleIds, getIsAdminatorLogAble } from '@/api/system/organizational/permission'
import { listChildRoleId } from '@/api/system/organizational/role'
import { stgTypeOps } from '@/views/common/stgTypeOps';
import OplogDetailDlg from './detailDlg'
import { enableStgDelete } from '@/utils'
import { setTheFirstLetterToUppercase } from '@/utils/i18n';

export default {
  name: 'OpLog',
  components: { OplogDetailDlg, ImportResult },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '140', sort: 'custom' },
        { prop: 'user.name', label: 'operateUser', width: '100', sort: 'custom' },
        { prop: 'ip', label: 'userIP', width: '100', sort: 'custom' },
        { prop: 'menuCode', label: 'menuPath', width: '200', formatter: this.menuPathFormatter, sort: 'custom' },
        { prop: 'message', label: 'funName', width: '120', formatter: this.messageFormatter },
        { prop: 'formatData', label: 'params', ellipsis: false, type: 'popover', width: '500', formatter: this.dataFormatter },
        { label: 'operate', type: 'button', fixedWidth: '90', fixed: 'right',
          buttons: [
            { click: this.handleShowDetailDialog, formatter: this.viewDetailsFormatter }
          ]
        }
      ],
      typesOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.insert'), value: 1 },
        { label: this.$t('button.delete'), value: 2 },
        { label: this.$t('button.edit'), value: 3 },
        { label: this.$t('login.handleLogin'), value: 4 },
        { label: this.$t('pages.logout'), value: 5 },
        { label: this.$t('button.import'), value: 6 },
        { label: this.$t('button.export'), value: 8 },
        { label: this.$t('button.upload'), value: 9 },
        { label: this.$t('button.download'), value: 7 },
        { label: this.$t('button.search'), value: 14 },
        { label: this.$t('route.vnc'), value: 15 }
      ],
      query: { // 查询条件
        page: 1,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        userName: '',
        ip: '',
        operationType: null,
        menuPath: '',
        menuCodes: [],
        searchInfo: '',
        // 导出管理员日志所需参数，即菜单编码和菜单路径,因为后端没法通过菜单编码获取菜单对应的路径，所以需要前端传过去
        // 菜单路径不需要再进行多语言处理，在简体、繁体、英语获取到的菜单路径就分别对应为简体、繁体、英语
        codePaths: [],
        prepareCodePaths: [],
        userIds: null
      },
      typeArr: ['[object String]', '[object Number]', '[object Undefined]', '[object Null]', '[object Boolean]'],
      showDetailDialog: false,
      activeName: 'baseInfo',
      baseInfo: {
        user: {
          name: ''
        }
      },
      attributeChangeTableCol: [
        { prop: 'attributeName', label: 'attributeName' },
        { prop: 'originalValue', label: 'originalValue', ellipsis: false, type: 'popover', formatter: this.valueFormatter },
        { prop: 'currentValue', label: 'currentValue', ellipsis: false, type: 'popover', formatter: this.valueFormatter }
        // { prop: 'isChanged', label: 'isChanged', type: 'icon', iconClass: 'active' }
      ],
      attributeChangeData: [],
      // 预定义策略菜单路径map
      prepareMenuMap: new Map(),
      deleteable: false
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'isSuperMode',
      'isSuperRole',
      'isSysRole',
      'roleTree',
      'currentRoleId',
      'menuCodeMapper',
      'menuCodeFullTitleMap' // 菜单路径 map
    ]),
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    // 初始化预定义策略map
    this.setPrepareMenuMap()
  },
  methods: {
    // 当前方法只判断管理员 当前的角色 对应的权限范围内的管理员日志
    // 超管、审计员：查看所有日志；无日志权限：查看自己的日志；有日志权限：查看自己+权限范围内的管理员日志
    async formatRoleIds() {
      // 当前角色id，用户id
      const [currentRoleId, userId] = [this.currentRoleId, this.userId];
      // 当前角色id没有值，从接口获取
      const roleId = currentRoleId || (await getCurrentRoleId()).data;

      // 定义审计安全员的子角色ids、是否超级管理员角色
      const [auditChildrenRes, isSuper] = await Promise.all([
        listChildRoleId(4),
        this.isSuperRole
      ]);

      // 是否 安全审计员
      const isAuditAdmin = [4, ...auditChildrenRes.data].includes(roleId);
      // 登录用户是超级管理员, 或者安全审计员，默认查所有
      if (isSuper || isAuditAdmin) {
        this.query.userIds = null;
        return
      }

      // 判断是否有管理员日志权限
      const adminLogChecks = await Promise.all(
        [roleId].map(id => getIsAdminatorLogAble(id))
      );

      const hasPermission = adminLogChecks.some(res => res.data);
      // 当前登录的管理员对应的角色未开启管理员日志权限，则只查看当前登录的管理员的日志
      if (!hasPermission) {
        this.query.userIds = [userId];
        return
      }

      // 用于处理多角色权限的代码（暂时只有一个角色，如果要增加，需要对 [roleId] 进行处理）
      const validRoleIds = adminLogChecks
        .filter(res => res.data)
        .map((_, i) => [roleId][i]);

      // 获取角色的 管理员角色范围 权限
      const { data: managerRoles } = await getManagerRoleIds(validRoleIds);
      // 获得具体的可以查看的管理员
      const { data: managedUsers } = await getSysUserByRoleId(managerRoles);

      // 使用Set优化去重操作
      this.query.userIds = [...new Set([...managedUsers, userId])];
    },
    async rowDataApi(option) {
      await this.formatRoleIds()
      option.sortName == 'user.name' && (option.sortName = 'userId')
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.menuCodes = this.getMenuCodes(this.query.menuPath)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLogPage(searchQuery)
    },
    async handleFilter() {
      this.query.page = 1
      await this.formatRoleIds()
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    objToStr(obj) {
      if (this.typeArr.includes(Object.prototype.toString.call(obj))) {
        return obj;
      }
      const dataArr = [];
      // 遍历对象的属性
      for (const [key, value] of Object.entries(obj)) {
        let result;
        if (Array.isArray(value)) {
          // 递归处理数组元素
          const dataArr2 = value.map(item => this.objToStr(item));
          result = `${key}：[${dataArr2.join('、')}]`;
        } else if (typeof value === 'object' && value !== null) {
          // 递归处理对象
          result = `${key}：${this.objToStr(value)}`;
        } else {
          result = `${key}：${value}`;
        }
        dataArr.push(result);
      }
      return dataArr.join('，');
    },
    // 列表中参数鼠标悬停展示的内容格式化
    detailFormatter: function(data) {
      if (data != null && data !== '' && data !== 'null') {
        try {
          return data.replace(/"/g, '').replace(/,/g, ', ').replaceAll('\\\\', '\\')
        } catch (err) {
          return ''
        }
      }
      return ''
    },
    // 列表中参数展示的内容格式化
    dataFormatter: function(row, data) {
      if (data != null && data !== '' && data !== 'null') {
        try {
          let dataFormat = data.replace(/"/g, '').replace(/,/g, ', ').replaceAll('\\\\', '\\');
          if (typeof dataFormat === 'string') {
            dataFormat = this.html2Escape(dataFormat);
          }
          return dataFormat;
        } catch (err) {
          return data
        }
      }
      return ''
    },
    // 查看详情中"原始值"和"当前值"的格式化
    valueFormatter: function(row, data) {
      if (data != null && data !== '' && data !== 'null') {
        try {
          let dataFormat = data.replace(/"/g, '').replace(/,/g, ', ');
          if (typeof dataFormat === 'string') {
            dataFormat = this.html2Escape(dataFormat);
          }
          return dataFormat;
        } catch (err) {
          return data
        }
      }
      return ''
    },
    // 通过输入的菜单路径，匹配对应菜单的 code
    getMenuCodes(menuPath) {
      if (menuPath) {
        const menuCodeSet = new Set()
        // 遍历 menuCodeFullTitleMap 和 prepareMenuMap，获取匹配菜单的 code
        Object.entries(this.menuCodeFullTitleMap).forEach(([key, value]) => {
          const isSearch = value.join('/').toLowerCase().includes(menuPath.toLowerCase())
          if (isSearch) menuCodeSet.add(key)
        })
        this.prepareMenuMap.forEach(([key, value]) => {
          const isSearch = value.toLowerCase().includes(menuPath.toLowerCase())
          if (isSearch) menuCodeSet.add(key)
        })
        return menuCodeSet.size > 0 ? [...menuCodeSet] : ['-1']
      }
    },
    menuPathFormatter(row) {
      let menuCode = row.menuCode
      if (menuCode) {
        let path
        // F15:文件自检内容检测策略,E28全盘扫描内容检测策略，菜单屏蔽，但有跳转路口，管理员日志路径显示主策略路径
        menuCode = this.menuCodeMapper[menuCode] || menuCode
        path = (this.menuCodeFullTitleMap[menuCode] || []).join('/')
        // 如果路径为空，再判断菜单编码code是否是预定义策略，如果是从map中获取对应菜单路径
        if (!path && menuCode.includes('-P')) {
          if (this.prepareMenuMap) {
            path = this.prepareMenuMap.get(menuCode);
          }
        }
        return path;
      }
    },
    messageFormatter(row) {
      return this.isEnglish() ? setTheFirstLetterToUppercase(row.message || '') : row.message
    },
    handleShowDetailDialog(row) {
      this.activeName = 'baseInfo'
      this.baseInfo = Object.assign({}, row)
      this.baseInfo.menuPath = this.menuPathFormatter(row)
      this.attributeChangeData = []
      if (!row.uuid) { // 说明是旧的管理员日志，按旧的方式进行展示
        this.showDetailDialog = true
        if (6 === row.operationType) {
          // 防止报错
          return;
        }
        if (row.formatData && row.formatData !== '{}' && row.formatData !== '[]') {
          const dataObj = JSON.parse(row.formatData)
          const isAddType = 1 === row.operationType
          const data = Array.isArray(dataObj) ? dataObj : [dataObj]
          data.forEach((item, index) => {
            this.setAttributeChangeData(item, isAddType, index)
          })
        }
        return;
      }
      this.$refs['detailDlg'].show(this.baseInfo)
    },
    setAttributeChangeData(data, isAddType, index) {
      const AttributeData = Object.entries(data).map(([key, value]) => {
        const attributeName = key + index ? `[${index}]` : ''
        if (isAddType) {
          // 新增类型
          return { attributeName, currentValue: value }
        } else {
          // 修改类型
          const isChanged = typeof value === 'string' && value.includes(' -> ')
          const valueArray = isChanged ? value.split(' -> ') : [value, value]
          const originalValue = valueArray[0]
          const currentValue = valueArray[1]
          return { attributeName, originalValue, currentValue, isChanged }
        }
      })
      this.attributeChangeData.splice(0, 0, ...AttributeData)
    },
    viewDetailsFormatter(row) {
      // 暂时先过滤'导入策略文件'和'上传U盘终端安装包'的查看详情
      if ('导入策略文件' === row.message || '上传U盘终端安装包' === row.message || '导入安装/卸载程序库' === row.message) {
        return '';
      }
      // 操作类型为新增和修改的展示查看详情按钮（操作类型（1：新增，2：删除，3：修改，4：登录，5：登出，6：导入，7：下载，8：导出））
      if (6 === row.operationType && !!row.dataInfo) {
        return this.$t('pages.detail')
      }
      return row.detailAble ? this.$t('pages.detail') : ''
    },
    // 根据策略包名称和菜单编号组成map，格式： code-P:路径，例：C67-P:/终端行为/终端行为策略包/终端行为策略包/MTP管控（预定义）
    setPrepareMenuMap() {
      const titleMap = this.menuCodeFullTitleMap;
      const prepareSuffix = this.$t('route.prepare');
      // 策略包的 key 和 code
      const stgGroups = {
        behaviorGroup: 'C11',
        netGroup: 'D11',
        encryptionGroup: 'E11',
        contentGroup: 'B2C',
        terminalGroup: 'B2C',
        systemGroup: 'B2C'
      };

      // Object.entries 遍历对象
      Object.entries(stgGroups).forEach(([groupKey, groupCode]) => {
        // 获取策略包的子策略
        const groupItems = stgTypeOps[groupKey] || [];
        // 策略包路径
        const groupPath = (titleMap[groupCode] || []).join('/');
        // 过滤子策略，并添加路径
        const validItems = groupItems
          .map(item => {
            const titleArray = titleMap[item.menuCode] || []
            return titleArray.length ? { ...item, fullPath: [...titleArray] } : null;
          })
          .filter(Boolean);

        // 拼接路径，并添加到 prepareMenuMap
        validItems.forEach(({ menuCode, fullPath }) => {
          const lastSegment = fullPath.pop();
          this.prepareMenuMap.set(
            `${menuCode}-P`,
            `${groupPath}/${lastSegment}${prepareSuffix}`
          );
        });
      });
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        toDelete.forEach((item) => {
          item.menuCode = this.menuCodeFullTitleMap[item.menuCode] && this.menuCodeFullTitleMap[item.menuCode].join('/')
        })
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    exportFunc(exportType) {
      this.query.menuCodes = []
      const tempCodePaths = []
      const tempPrepareCodePaths = []
      const tempCodeSet = new Set();
      const queryPath = this.query.menuPath

      Object.entries(this.menuCodeFullTitleMap).forEach(([key, value]) => {
        const menuPath = value.join('/')
        if (!!queryPath && menuPath.includes(queryPath)) {
          tempCodeSet.add(key);
        }
        tempCodePaths.push(`${key}:${menuPath}`)
      })

      this.prepareMenuMap.forEach((key, value) => {
        tempPrepareCodePaths.push(`${key}:${value}`)
      })

      this.query.menuCodes = tempCodeSet.size > 0 ? [...tempCodeSet] : null;
      Object.assign(this.query.codePaths, tempCodePaths)
      Object.assign(this.query.prepareCodePaths, tempPrepareCodePaths)

      return exportLog({ exportType, ...this.query, menuCodes: this.getMenuCodes(queryPath) })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
