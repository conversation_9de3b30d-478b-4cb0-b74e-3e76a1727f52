<!--
    /**
     * [管理员日志]导入类型的查看详情列表显示Table组件
     * @date 2022-06-27
     * <AUTHOR>
     * 调用示例：
     <ImportResult
        :reload="showDetailDialog"                                      // 是否重新加载，true则重新加载数据
        :data-info="baseInfo.dataInfo"                                  // 传入的json参数（数组或对象）
        :height="290"                                                   // 高度
        :params="{ dataId: baseInfo.id, dataClass: baseInfo.dataClass}" // 查询时用的参数
      />
     * 注意事项：importType.js中配置了getColModel(dataClass)以及displayRowDatas(dataClass)，若增加了导入数据，则需在改js中配置
     *  getColModel(dataClass)      说明：根据传入的class获取对应列名(不通类型展示不同的列)
     *  displayRowDatas(dataClass)  说明：根据传入的class决定是否直接展示传入baseInfo的数据(即不走后台请求数据，直接取oplog里baseInfo数据做展示)
     */
-->
<template>
  <div>
    <grid-table v-if="displayRowDatas" ref="importResultList" :col-model="colModel" :row-datas="rowDatas" :show-pager="false" :height="height" />
    <grid-table v-else-if="!displayRowDatas" ref="importResultList" :col-model="colModel" :row-data-api="rowDataApi" :height="height" />
  </div>
</template>

<script>
import { getImportResultPage } from '@/api/system/organizational/opLog'
import { displayRowDatas, getColModel } from './column/importType';

export default {
  name: 'ImportResult',
  props: {
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    height: {
      type: Number,
      default: 350
    },
    reload: {
      type: Boolean,
      default: true
    },
    dataInfo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: [],
      visible: false,
      colModel: [],
      // 查询条件
      query: {
        page: 1,
        operationType: 6,
        searchInfo: '',
        dataId: -1,
        dataClass: '',
        limit: 20,
        sortName: 'id',
        sortOrder: 'desc'
      },
      // 是否直接展示传入的baseInfo数据
      displayRowDatas: false,
      // baseInfo数据
      rowDatas: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['importResultList']
    }
  },
  watch: {
    reload() {
      if (this.reload) {
        this.initData();
        if (!this.displayRowDatas) {
          this.gridTable.execRowDataApi(this.query)
        }
      }
    }
  },
  created() {
    this.initData();
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getImportResultPage(searchQuery)
    },
    initData() {
      this.colModel = getColModel(this.params.dataClass);
      this.displayRowDatas = displayRowDatas(this.params.dataClass);
      if (this.dataInfo) {
        const parse = JSON.parse(this.dataInfo);
        // 如果dataInfo是数组，则直接赋值，若为Object对象则存储在第一个值
        if (parse instanceof Array) {
          this.rowDatas = JSON.parse(this.dataInfo);
        } else if (parse instanceof Object) {
          this.rowDatas[0] = parse;
        }
      }
      this.query.dataId = this.params.dataId;
      this.query.dataClass = this.params.dataClass;
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
