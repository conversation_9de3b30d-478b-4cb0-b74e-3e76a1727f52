/**
 * 各个导入模块的colModel
 * 邮箱信息库(MailLibrary)、网址信息库(UrlLibrary)、服务器信息库(ServerLibrary)、部门管理(Department)、终端操作员(User)、WiFi信息库(Wifi)
 * <AUTHOR>
 */
import i18n from '@/lang'
// 字典
const useNewVersionOptions = { 0: i18n.t('text.yes'), 1: i18n.t('text.no') };
const ipTypeOptions = { 0: i18n.t('pages.ipAddr'), 1: i18n.t('pages.domain') };
const loadTypeOptions = { 0: i18n.t('pages.null'), 1: i18n.t('pages.loadTypeOptions2'), 2: i18n.t('pages.loadTypeOptions3') };
const activeOptions = { true: i18n.t('text.enable'), false: i18n.t('text.disable') };
// format方法
const useNewVersionFormatter = function(row, data) {
  return useNewVersionOptions[data];
}
const ipTypeFormatter = function(row, data) {
  return ipTypeOptions[data]
}
const loadTypeFormatter = function(row, data) {
  return loadTypeOptions[data]
}
const activeFormatter = function(row, data) {
  const label = activeOptions[row.active]
  return row.active ? { class: 'active', title: label } : { class: 'offline', title: label, style: 'color: #888;' }
}
const noPwdLoginFormatter = function(row, data) {
  const noPwdLoginOptions = { 0: i18n.t('pages.allow'), 2: i18n.t('pages.forbid') }
  return noPwdLoginOptions[data & 2]
}
const mobileShareFileFormatter = function(row, data) {
  const options = { 4: i18n.t('pages.allow'), 0: i18n.t('pages.forbid') }
  return options[data & 4]
}
const adFormatter = function(row, data) {
  return data ? i18n.t('text.yes') : i18n.t('text.no')
}
// model
const model = {
  wifiLibraryColModel: [
    { prop: 'name', label: 'emailName', width: '150', fixed: true },
    { prop: 'groupName', label: 'sourceGroup', width: '150', fixed: true },
    { prop: 'macAddress', label: 'macAddr', width: '150' },
    { prop: 'remark', label: 'remark', width: '200' }
  ],
  mailLibraryColModel: [
    { prop: 'name', label: 'emailName', width: '150', fixed: true },
    { prop: 'groupName', label: 'sourceGroup', width: '150', fixed: true },
    { prop: 'address', label: 'email', width: '150' },
    { prop: 'unit', label: 'unit', width: '150' },
    { prop: 'office', label: 'office', width: '150' },
    { prop: 'remark', label: 'remark', width: '200' }
  ],
  urlLibraryColModel: [
    { prop: 'name', label: 'websiteName', width: '150', fixed: true },
    { prop: 'groupName', label: 'sourceGroup', width: '150', fixed: true },
    { prop: 'address', label: 'websiteUrl', width: '150' },
    { prop: 'remark', label: 'remark', width: '200' }
  ],
  serverLibraryColModel: [
    { prop: 'name', label: 'serverName', width: '150', fixed: true },
    { prop: 'groupName', label: 'sourceGroup', width: '150', fixed: true },
    { prop: 'useNewVersion', label: 'useNewVersion', width: '150', formatter: useNewVersionFormatter },
    { prop: 'ipType', label: 'ipType', width: '150', formatter: ipTypeFormatter },
    { prop: 'beginIp', label: 'beginIp', width: '150' },
    { prop: 'endIp', label: 'endIp', width: '150' },
    { prop: 'beginPort', label: 'beginPort', width: '150' },
    { prop: 'endPort', label: 'endPort', width: '150' },
    { prop: 'uploadType', label: 'uploadType', width: '150', formatter: loadTypeFormatter },
    { prop: 'uploadFileExt', label: 'uploadFileExt', width: '150' },
    { prop: 'downloadType', label: 'downloadType', width: '150', formatter: loadTypeFormatter },
    { prop: 'downloadFileExt', label: 'downloadFileExt', width: '150' },
    { prop: 'processName', label: 'processName', width: '150' },
    { prop: 'upLoadLimitSpeed', label: 'upLoadLimitSpeed', width: '150' }
  ],
  departmentColModel: [
    { prop: 'name', label: 'deptName', width: '150', fixed: true, sort: 'custom' },
    { prop: 'parentName', label: 'superiorDept', width: '300' },
    { prop: 'remark', label: 'remark', width: '200' }
  ],
  userColModel: [
    { prop: 'account', label: 'account', width: '150', fixed: true, sort: 'custom', iconFormatter: activeFormatter },
    { prop: 'name', label: 'userName1', sort: 'custom', width: '200', fixed: true },
    { prop: 'groupNames', label: 'userGroup', width: '150' },
    { prop: 'email', label: 'email', width: '150', sort: 'custom' },
    { prop: 'phone', label: 'phone', width: '150', sort: 'custom' },
    { prop: 'flag', label: 'noPwdLogin', width: '150', formatter: noPwdLoginFormatter },
    { prop: 'flag', label: 'mobileShareFile', width: '150', formatter: mobileShareFileFormatter },
    { prop: 'sid', label: 'sid', width: '100', formatter: adFormatter }
  ],
  suffixLibraryColModel: [
    { prop: 'suffix', label: 'suffixes', width: '150', fixed: true },
    { prop: 'groupName', label: 'sourceGroup', width: '150', fixed: true },
    { prop: 'name', label: 'remark', width: '150' }
  ],
  outgoingProcessColModel: [
    { prop: 'processName', label: 'fileName', width: '150', fixed: true },
    { prop: 'fileMd5', label: 'fileMd5_transliterate', width: '200' }
  ],
  defaultColModel: [
    { prop: 'name', label: 'name', width: '150', fixed: true },
    { prop: 'remark', label: 'remark', width: '200' }
  ]
}
// 根据class获取对应colModel
export function getColModel(dataClass) {
  let colModel = [];
  switch (dataClass) {
    case 'com.tipray.dlp.bean.Wifi':
      colModel = model.wifiLibraryColModel;
      break;
    case 'com.tipray.dlp.bean.MailLibrary':
      colModel = model.mailLibraryColModel;
      break;
    case 'com.tipray.dlp.bean.Url':
      colModel = model.urlLibraryColModel;
      break;
    case 'com.tipray.dlp.bean.ServerLibrary':
      colModel = model.serverLibraryColModel;
      break;
    case 'com.tipray.dlp.bean.Department':
      colModel = model.departmentColModel;
      break;
    case 'com.tipray.dlp.bean.User':
      colModel = model.userColModel;
      break;
    case 'com.tipray.dlp.bean.FileSuffixInfotable':
      colModel = model.suffixLibraryColModel;
      break;
    case 'com.tipray.dlp.bean.OutgoingProcess':
      colModel = model.outgoingProcessColModel;
      break;
    default :
      colModel = model.defaultColModel;
      break;
  }
  return colModel;
}

// 根据class获取是否直接展示传入的rowDatas数据（不请求后台数据）
export function displayRowDatas(dataClass) {
  let display;
  switch (dataClass) {
    case 'com.tipray.dlp.bean.OutgoingProcess':
      display = true;
      break;
    default :
      display = false;
      break;
  }
  return display;
}
