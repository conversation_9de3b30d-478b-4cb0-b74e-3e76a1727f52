<template>
  <el-dialog
    v-el-drag-dialog
    :title="this.$t('pages.detail')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    class="show-detail-panel"
    width="800px"
  >
    <div v-if="showList" style="top: -15px;position: relative;">
      <el-divider content-position="left">{{ $t('pages.baseInfo') }}</el-divider>
      <el-descriptions class="margin-top" size="small" :column="2" border>
        <el-descriptions-item :label="$t('table.time')"> {{ baseInfo.createTime }} </el-descriptions-item>
        <el-descriptions-item :label="$t('table.userIP')"> {{ baseInfo.ip }} </el-descriptions-item>
        <el-descriptions-item :label="$t('table.operateUser')"> {{ baseInfo.user && baseInfo.user.name }} </el-descriptions-item>
        <el-descriptions-item :label="$t('table.roleName')"> {{ baseInfo.roleName }} </el-descriptions-item>
        <el-descriptions-item :label="$t('table.menuPath')"> {{ baseInfo.menuPath }} </el-descriptions-item>
        <el-descriptions-item :label="$t('table.funName')"> {{ baseInfo.message }} </el-descriptions-item>
      </el-descriptions>
      <div v-loading="loadingList" :style="!loadingList ? '' : 'height: 290px;'">
        <el-divider v-if="loadingList && detailExtDataList.length === 0 || rowData.length > 0" content-position="left">{{ $t('table.detailInfo') }}</el-divider>
        <!-- 如果支持追踪历史操作，则显示表格-->
        <grid-table
          v-if="rowData.length > 0"
          ref="importResultList"
          :col-model="colModel"
          :row-datas="rowData"
          :show-pager="false"
          :multi-select="false"
          :height="275"
        />
        <!-- 如果不支持追踪历史操作，则显示信息-->
        <!--<el-descriptions v-for="(detailExtDataItem, index) in detailExtDataList" :key="index" :column="2" border style="height: 250px;">
          <el-descriptions-item v-for="item in detailExtDataItem" :key="item.key" :label="item.key"> {{ item.value }} </el-descriptions-item>
        </el-descriptions>-->
        <div v-for="(arrItem, arrIndex) in detailExtDataList" :key="arrIndex">
          <div v-for="(item, key) in arrItem" :key="key" :class="{ 'desc-item-border': arrIndex + 1 < detailExtDataList.length }">
            <el-divider content-position="left">{{ key }}</el-divider>
            <label v-if="(typeof item) === 'string'">
              {{ item }}
            </label>
            <div v-if="(typeof item) == 'object'">
              <el-descriptions v-for="(desItem, index) in [].concat(item)" :key="index" size="small" :column="Object.keys(desItem).length > 1 ? 2 : 1" border :class="{ 'desc-item-border': index + 1 < ([].concat(item)).length }">
                <el-descriptions-item v-for="(itemVal, itemKey) in desItem" :key="itemKey" :label="itemKey" >
                  <img v-if="isSupportImageDataURL(itemVal)" :src="itemVal" alt="itemVal" :title="itemVal">
                  <span v-else>{{ itemVal }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!showList" v-loading="curExtDataLoading" style="top: -15px;position: relative;">
      <div class="list-panel">
        <el-button type="primary" icon="el-icon-back" size="mini" @click="() => { showList = true }">
          {{ $t('button.return') }}
        </el-button>
        <div v-for="(item, index) in detailItem" :key="index" class="list-item" :class="{ highlight: item.id === curId }" @click="handleClickItem(item, index)">
          <span class="list-item-key">{{ item.createTime }}</span>
        </div>
      </div>
      <div class="detail-panel">
        <el-divider content-position="left">{{ $t('pages.baseInfo') }}</el-divider>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item :label="$t('table.time')"> {{ detailLogData.createTime }} </el-descriptions-item>
          <el-descriptions-item :label="$t('table.userIP')"> {{ detailLogData.ip }} </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateUser')" content-class-name="base-item-content"> {{ detailLogData.user && detailLogData.user.name }} </el-descriptions-item>
          <el-descriptions-item :label="$t('table.roleName')" content-class-name="base-item-content"> {{ detailLogData.roleName }} </el-descriptions-item>
          <el-descriptions-item :label="$t('table.menuPath')" :span="2"> {{ detailLogData.menuPath }} </el-descriptions-item>
          <el-descriptions-item :label="$t('table.funName')" :span="2"> {{ detailLogData.message }} </el-descriptions-item>
        </el-descriptions>
        <div v-for="(item, key) in detailExtData" :key="key">
          <el-divider content-position="left">{{ key }}</el-divider>
          <el-descriptions v-for="(desItem, index) in [].concat(item)" :key="index" :column="1" border size="small" :class="{ 'desc-item-border': index + 1 < ([].concat(item)).length }">
            <el-descriptions-item v-for="(itemVal, itemKey) in desItem" :key="itemKey" :label="itemKey" >
              <img v-if="isSupportImageDataURL(itemVal)" :src="itemVal" alt="itemVal" :title="itemVal">
              <span v-else>{{ itemVal }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="!showList" type="primary" :disabled="!upAble || curExtDataLoading" @click="changeToItem(-1)">
        {{ $t('pages.previous') }}
      </el-button>
      <el-button v-if="!showList" type="primary" :disabled="!downAble || curExtDataLoading" @click="changeToItem(1)">
        {{ $t('pages.next') }}
      </el-button>
      <el-button @click="dialogVisible = false">
        {{ this.$t('button.close') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getDataLog, getLogById, listDataLogItem } from '@/api/system/organizational/opLog'
import { isImageDataURL } from '@/utils/image'

export default {
  name: 'OplogDetailDlg',
  props: {
    menuCodeFormatter: {
      type: Function,
      default: function(data) {
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      curExtDataLoading: false,
      showList: true,
      loadingList: false,
      upAble: true,
      downAble: true,
      detailItem: [],
      colModel: [
        { prop: 'cmd_type', label: 'commandType', width: '80', sort: true },
        { prop: 'data_class_label', label: 'dataType', width: '80' },
        { prop: 'detail', label: 'details', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '90', fixed: 'right',
          buttons: [
            { click: this.toDetailPanel, formatter: this.viewDetailsFormatter }
          ]
        }
      ],
      unShowInDetailFields: ['data_class', 'data_class_label', 'uuid_key', 'table_name', 'cmd_type'],
      rowData: [],
      baseInfo: {},
      detailLogData: {},
      detailExtData: {},
      detailExtDataList: [],
      detailLabelWidth: 150,
      curTableName: undefined,
      curKeyValue: undefined,
      curId: 0,
      curIndex: 0
    }
  },
  watch: {

  },
  created() {
  },
  methods: {
    show(data) {
      this.baseInfo = data
      this.rowData.splice(0)
      this.detailItem.splice(0)
      this.detailExtDataList.splice(0)
      this.detailExtData = {}
      this.detailLogData = {}
      this.showList = true
      this.loadingList = true
      this.curId = data.id
      getLogById(data.id).then(resp => {
        if (Array.isArray(resp.data.formatData) && resp.data.formatData.length > 0) {
          resp.data.formatData.forEach(row => {
            row['detail'] = this.formatDetail(row)
            // 对详情为空的过滤
            if (!row['detail'] || row['detail'] === '') {
              return
            }
            this.rowData.push(row);
          })
        } else {
          let dataArr = []
          if ((typeof resp.data.formatData) === 'string') {
            const data = JSON.parse(resp.data.formatData)
            dataArr = Array.isArray(data) ? data : [data]
          }
          if (dataArr.length === 0) {
            dataArr.push(this.$t('pages.null'))
          }
          dataArr.forEach(map => {
            const detailExtDataItem = {}
            this.formatDetailExtData(map, detailExtDataItem, this.$t('table.detailInfo'))
            this.detailExtDataList.push(detailExtDataItem)
          })
        }
        setTimeout(() => {
          this.loadingList = false
        }, 200)
      })
      this.dialogVisible = true
    },
    formatDetail(row) {
      if (typeof row !== 'object') {
        return row + ';  '
      }
      let detailMsg = ''
      for (const rowKey in row) {
        if (this.unShowInDetailFields.indexOf(rowKey) < 0) {
          const rowItem = row[rowKey]
          if (typeof rowItem === 'object') {
            if (Array.isArray(rowItem) && rowItem.length > 0) {
              detailMsg += rowKey + '：'
              for (let i = 0; i < rowItem.length; i++) {
                detailMsg += this.formatDetail(rowItem[i])
              }
            } else {
              detailMsg += rowKey + '：' + this.formatDetail(rowItem)
            }
          } else {
            detailMsg += rowKey + '：' + rowItem + ';  '
          }
        }
      }
      return detailMsg
    },
    handleDrag() {
    },
    viewDetailsFormatter(row) {
      return this.$t('pages.detail')
    },
    toDetailPanel(row) {
      this.curTableName = row.table_name
      this.curKeyValue = row.uuid_key
      this.curExtDataLoading = true
      this.detailItem.splice(0)
      listDataLogItem({ tableName: this.curTableName, keyValue: this.curKeyValue, dataClass: row.data_class }).then(resp => {
        this.detailItem.splice(0, 0, ...resp.data)
        for (let i = 0; i < this.detailItem.length; i++) {
          if (this.detailItem[i].id === this.curId) {
            this.curIndex = i
            break
          }
        }
        this.changeToItem(0)
      }).catch(e => {
        this.curExtDataLoading = false
      })
      this.showList = false
    },
    handleClickItem(data, index) {
      this.curId = data.id
      this.curExtDataLoading = true
      this.curIndex = index
      this.formatBtnAble()
      getDataLog({ id: data.id, tableName: this.curTableName, keyValue: this.curKeyValue }).then(resp => {
        this.detailLogData = resp.data['logData']
        this.detailLogData.menuPath = this.menuCodeFormatter(this.detailLogData)
        const map = resp.data['extData']
        this.detailExtData = {}
        this.formatDetailExtData(map, this.detailExtData, this.$t('table.detailInfo'))
        setTimeout(() => {
          this.curExtDataLoading = false
        }, 200)
      }).catch(e => {
        this.curExtDataLoading = false
      })
    },
    formatDetailExtData(map, targetMap, dividerLabel) {
      if ((typeof map) === 'string') {
        targetMap[dividerLabel] = map
        return
      }
      const detailDataItem = {}
      targetMap[dividerLabel] = detailDataItem
      // Object.keys(map).sort((a, b) => a.toLocaleString().localeCompare(b.toLocaleString())).forEach(key => {
      Object.keys(map).forEach(key => {
        const value = map[key]
        if (this.unShowInDetailFields.indexOf(key) < 0) {
          if (typeof value === 'object') {
            if (Array.isArray(value) && value.length > 0) {
              if (typeof value[0] === 'object') {
                targetMap[key] = value
              } else {
                detailDataItem[key] = value.toString()
              }
            } else {
              this.formatDetailExtData(value, targetMap, key)
            }
          } else {
            detailDataItem[key] = value
          }
        }
      })
      if (Object.keys(detailDataItem).length === 0) {
        delete targetMap[dividerLabel]
      }
    },
    changeToItem(step) {
      this.curIndex += step
      const item = this.detailItem[this.curIndex]
      if (item) {
        this.handleClickItem(item, this.curIndex)
      }
      this.formatBtnAble()
    },
    formatBtnAble() {
      this.downAble = (this.curIndex + 1) < this.detailItem.length
      this.upAble = this.curIndex > 0
    },
    isSupportImageDataURL(content) {
      return isImageDataURL(content, format => ['BMP', 'JPEG', 'PNG'].includes(format))
    }
  }
}
</script>

<style lang="scss" scoped>
.list-panel {
  width: 165px;
  height: 460px;
  position: relative;
  float: left;
  overflow-y: auto;
  border-right: 1px solid #e6e6e6;
  box-sizing: content-box;
  border-right: 1px dashed #cccccc;
}
.list-item {
  border-bottom: 1px dashed #ccc;
  cursor: pointer;
  padding: 5px 0 5px 5px;
  position: relative;
}
.list-item-summary {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  margin-top: 3px;
  font-size: 12px;
}
.list-loading {
  text-align: center;
  padding: 5px 0;
  width: 100%;
  i {
    color: #409eff;
  }
  .el-loading-text {
    color: #409eff;
    margin: 3px 0;
    font-size: 14px;
  }
}
.highlight {
  /*background: #ebf2f9;*/
  color: #409eff;
  font-weight: 700;
}
>>>.el-dialog__body {
  padding-right: 10px;
}
.detail-panel {
  padding-right: 10px;
  margin-left: 182px;
  height: 460px;
  overflow-y: auto;
  >>>.doc-info-item {
    font-size: 14px;
  }
  >>>.doc-info-item__label {
    width: 95px;
  }
  >>>.doc-info-item__content {
    width: calc(100% - 105px);
  }
  >>>.base-item-content{
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.show-detail-panel {
  >>>.desc-item-border{
    border-bottom: dashed #adaeaf 1px;
  }
  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}
</style>
