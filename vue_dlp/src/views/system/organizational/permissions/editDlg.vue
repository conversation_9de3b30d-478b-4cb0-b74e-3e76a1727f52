<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dlgVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div slot="title" class="el-dialog__title">
      <i18n path="pages.rolePermissionConfiguration">
        <template slot="currentRole">{{ currentRole }}</template>
      </i18n>
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          {{ $t('pages.permissionsDescription',{
            managementPermissions: this.$t('pages.managementPermissions'),
            managementScope: this.$t('pages.managementScope'),
            managementRole: this.$t('pages.permission_Msg10'),
            saveAndContinue: this.$t('button.saveAndContinue')
          }) }}
        </div>
        <i class="el-icon-info" style="cursor: default"/>
      </el-tooltip>
    </div>
    <el-container>
      <el-aside width="175px">
        <tree-menu
          ref="roleTree"
          :height="460"
          :data="roleData"
          node-key="dataId"
          :default-expanded-keys="expandedTreeKeys"
          @node-click="handleNodeClick"
        />
      </el-aside>
      <el-main v-loading="tableLoading">
        <el-tabs ref="tabs" v-model="activeTabName" type="card" @tab-click="tabChange">
          <el-tab-pane :label="menuTabLabel" name="menu" >
            <tree-select-panel
              ref="permissionMenuTree"
              height="370px"
              :data="menuPermissionTreeData"
              :selected-button="selectedButton"
              :include-half="true"
              :to-select-title="$t('components.optionalPermissions')"
              :selected-title="$t('components.selectedPermissions')"
              :selected-nodes-expand-all="false"
              :multiple-permission="true"
              :header-style="{color: '#666'}"
              :panel-style="{'border-color': '#aaa', 'height': 'calc(100% - 5px)'}"
              @multipleSetPermission="multipleSetPermission"
              @check="menuCheck"
              @check-change="menuCheckChange"
            />
          </el-tab-pane>
          <el-tab-pane :label="deptTabLabel" name="data">
            <tree-select-panel
              ref="permissionDeptTree"
              height="370px"
              :data="deptPermissionTreeData"
              :to-select-title="$t('components.optionalRange')"
              :selected-title="$t('components.selectedRange')"
              :selected-nodes-expand-all="false"
              :header-style="{color: '#666'}"
              :panel-style="{'border-color': '#aaa', 'height': 'calc(100% - 5px)'}"
              @check-change="deptCheckChange"
            />
          </el-tab-pane>
          <el-tab-pane :label="roleTabLabel" name="role">
            <div>
              <tree-select-panel
                ref="permissionRoleTree"
                height="370px"
                :data="permissionRoleData"
                :to-select-title="$t('pages.permission_Msg11')"
                :selected-title="$t('pages.permission_Msg12')"
                :selected-nodes-expand-all="false"
                :header-style="{color: '#666'}"
                :panel-style="{'border-color': '#aaa', 'height': 'calc(100% - 5px)'}"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="submitData('common')">
        {{ $t('button.saveAndContinue') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import { getIsViewMyselfLogAble } from '@/api/property'
import { getPermissionBtnMap, listMenuTree, listData, updatePermission, getManagerRoleByRoleId } from '@/api/system/organizational/permission'
import funcMenuCode from '@/router/modules/funcPermission'
import TreeSelectPanel from '@/components/TreeSelectPanel'
import { generateTitle, setTheFirstLetterToUppercase } from '@/utils/i18n'
import { asyncRoutes } from '@/router'
import { getAllDeptTree } from '@/api/system/terminalManage/department'
import { isRemoveRouteByDiskScan, isRemoveRouteByLog } from '@/store/modules/permission'

export default {
  name: 'PermissionDlg',
  components: { TreeSelectPanel },
  props: {
    roleData: { type: Array, default() { return [] } },
    disableRoleId: {
      type: Array,
      default() {
        return [1, 2, 3, 4]
      }
    },
    // 是否三员管理
    isThree: {
      type: Boolean,
      default() {
        return this.isSuperThreeUser
      }
    }
  },
  data() {
    return {
      activeTabName: 'menu',
      menuTabLabel: this.$t('pages.managementPermissions'),
      deptTabLabel: this.$t('pages.managementScope'),
      roleTabLabel: this.$t('pages.permission_Msg10'),
      menuTabEdit: false, // 管理权限是否已经配置
      deptTabEdit: false, // 管理范围是否已经配置
      roleTabEdit: false, // 管理员角色范围是否已经配置
      dlgVisible: false,
      submitting: false,
      visible: false,
      expandedTreeKeys: [],
      curClickNodeKey: '',
      curClickParentId: 0,
      currentRole: '',
      temp: {},
      defaultTemp: { // 表单字段
        roleId: null,
        roleName: '',
        parentRoleId: null,
        permissionIds: []
      },
      currentRoleId: '',
      permissionRoleData: [],
      menuCodeTitleData: {}, // 保存菜单权限的多语言key，不包含菜单下的按钮权限 key为菜单编码，value为菜单名称
      tableLoading: false,
      allMenuPermissionTreeData: [],          // 菜单权限树
      allMenuPermissionTreeDataMap: {},       // 菜单权限树 的每一个节点 Map，key: 菜单编码，value：节点数据data
      allDeptPermissionTreeData: [],          // 部门权限树
      menuPermissionTreeData: [],             // 当前登录用户，拥有的菜单权限
      deptPermissionTreeData: [],             // 当前登录用户，拥有的部门权限
      funcPermissionMap: {},                  // 各个菜单的子功能权限 map
      selectedMenuNodes: [],                  // 菜单已选节点
      selectedDeptNodes: [],                  // 部门已选节点
      ope: undefined,
      tempMenuData: [],
      permissionMap: {},                      // 权限map，key为 type+id
      defaultCheckedKeys: [],                 // nodeKey为id，需要通过allMenuPermissionTreeDataMap[meunCode]获取
      selectedButton: [{                      // 菜单权限已选节点删除按钮，因为部分节点有关联性，所以重写按钮
        label: this.$t('text.delete'),
        show: (node, data) => {
          let isShow = true
          if (data.disabled) {
            isShow = false
          }
          return isShow
        },
        onclick: (node, data, component) => {
          component.removeSelectedNode(data)
          // 取消勾选的节点是依赖节点，需要将被关联的节点取消勾选
          if (data.relDataId && data.dataId == data.relDataId) {
            const parentData = node.parent.data
            // 取消勾选只需要传递父节点的数据
            this.menuCheck(data, { checkedKeys: [], halfCheckedNodes: [parentData] })
          }
        }
      }]
    }
  },
  computed: {
    ...mapGetters([
      'isSuperRole',
      'isSuperThreeUser',
      'isSuperMode',
      'roles',
      'deptTree',
      'userMenuCodes'
    ])
  },
  watch: {
    'deptTree'() {
      this.initAllDeptPermissionTreeData()
      this.tabChange()
    }
  },
  created() {
    this.resetTemp()
    // 获取各个菜单的功能权限节点，menuCode 作为 key
    getPermissionBtnMap().then(resp => {
      this.funcPermissionMap = resp.data
      // 获取菜单树数据
      this.allMenuPermissionTreeData = this.initAllMenuPermissionTreeData().nodeData
      // 获取部门树数据
      if (this.deptTree.length > 0) {
        this.initAllDeptPermissionTreeData()
      } else {
        this.loadDeptTree()
      }
    })
  },
  activated() {

  },
  methods: {
    generateTitle, // generateTitle by vue-i18n
    permissionMenuTree() {
      return this.$refs['permissionMenuTree']
    },
    permissionDeptTree() {
      return this.$refs['permissionDeptTree']
    },
    permissionRoleTree() {
      return this.$refs['permissionRoleTree']
    },
    permissionTree(type) {
      const tree = {
        menu: this.permissionMenuTree(),
        data: this.permissionDeptTree(),
        role: this.permissionRoleTree()
      }[type]
      if (!tree) {
        return { setCheckedKeys: () => { console.warn('tree is null') } }
      }
      return tree
    },
    multipleSetPermission(data) {
      if (data.permissionType == 1) {
        // 勾选的是全部权限
        var nodeIds = []
        this.menuPermissionTreeData.forEach(node => {
          // 管理权限树是父子关联的，所以只获取父节点进行勾选就可以，子节点也会自动勾选
          nodeIds.push(node.id)
        })
        if (data.operateType == 1) {
          // 全部选中
          this.permissionMenuTree().setCheckedKeys(nodeIds)
        } else {
          // 取消全选
          const checkedKeys = this.permissionMenuTree().getCheckedKeys()
          for (let i = 0; i < checkedKeys.length; i++) {
            this.permissionMenuTree().setChecked(checkedKeys[i], false)
          }
        }
      } else {
        const ways = data.operates
        ways.forEach(item => {
          this.handleMultipleMenu(data.operateType, item)
        })
      }
      this.$message({
        message: this.$t('pages.permission_Msg9'),
        type: 'success',
        duration: 2000
      })
    },
    handleMultipleMenu(ope, way) {
      // 通过权限树选中的权限 Set
      const selectMenuKeySet = new Set([...this.permissionMenuTree().toSelectMenu().getCheckedKeys(true)])
      // 过滤拿到响应权限的节点，同时如果该节点的兄弟节点包含基础功能，应该一同加入
      const findTree = this.filterPermissionMenu([...this.menuPermissionTreeData], way)
      // 获取叶子节点，批量启用禁用的，如导入、导出、下钻都是叶子节点，基础功能也是叶子节点
      let leafNodes = this.getLeafNodes(findTree, way)
      // 对叶子节点进行去重
      const uniqueIds = new Set();
      leafNodes = leafNodes.filter(item => {
        const exist = uniqueIds.has(item.id)
        !exist && uniqueIds.add(item.id)
        return !exist
      });
      // 过滤掉叶子节点同一层级中只有基础功能，没有导入、导出等其它权限的基础功能节点
      leafNodes = this.filterLeafNodeBasicNode(leafNodes)
      if (ope == 1) {
        // 启用操作，不管启用哪个权限，都应该将基础功能也加入
        leafNodes.forEach(item => {
          if (!selectMenuKeySet.has(item.id)) {
            selectMenuKeySet.add(item.id)
          }
        })
      } else {
        // 禁用操作，禁用时，不禁用基础功能
        leafNodes.forEach(item => {
          if (!item.label.toLowerCase().includes(this.$t('pages.basicFunctions').toLowerCase())) {
            // 删除非基础功能
            selectMenuKeySet.delete(item.id)
          }
        })
      }
      // 拿到已勾选的节点
      this.permissionMenuTree().setCheckedKeys(Array.from(selectMenuKeySet))
    },
    filterLeafNodeBasicNode(leafNodes) {
      const parentIdCounts = {};
      leafNodes.forEach(item => {
        // 没有值则赋值1，有值则 +1
        parentIdCounts[item.parentId] = ++parentIdCounts[item.parentId] || 1
      });
      // 如果只有一个子节点，则将这个子节点过滤
      return leafNodes.filter(item => {
        return parentIdCounts[item.parentId] > 1;
      });
    },
    filterPermissionMenu(nodes, operateName) {
      const filteredNodes = [];
      const baseFunctionNodeMap = new Map(); // 用于存储包含“基础功能”的节点，方便快速查找

      // 辅助函数：构建包含“基础功能”的节点的Map
      const buildBaseFunctionNodeMap = (nodes) => {
        nodes.forEach(node => {
          if (node.label.toLowerCase().includes(this.$t('pages.basicFunctions').toLowerCase())) {
            // 使用parentId作为key，因为我们要在同一层级查找
            baseFunctionNodeMap.set(node.parentId, node);
          }
          if (node.children) {
            buildBaseFunctionNodeMap(node.children);
          }
        });
      };
      // 初始化baseFunctionNodeMap
      buildBaseFunctionNodeMap(nodes);
      nodes.forEach(node => {
      // 检查节点的label是否包含搜索词, 统一转写小写再比较
        if (node.label.toLowerCase().includes(operateName.toLowerCase())) {
          // 如果包含operateName，则添加到新树的当前层级
          filteredNodes.push({ ...node });
          // 查找同一层级的“基础功能”节点
          const baseFunctionNode = baseFunctionNodeMap.get(node.parentId);
          if (baseFunctionNode) {
            // 如果找到了，则添加到filteredNodes中
            filteredNodes.push({ ...baseFunctionNode });
          }
        }
        // 如果节点有子节点，则递归处理子节点
        if (node.children) {
          const filteredChildren = this.filterPermissionMenu(node.children, operateName);
          // 如果子节点中有符合条件的节点，则将子节点添加到当前节点的children中
          if (filteredChildren.length > 0) {
            filteredNodes.push({ ...node, children: filteredChildren });
          }
        }
      });
      return filteredNodes;
    },
    getLeafNodes(nodes, operateName) {
      var leafNodes = [];
      const basicFunction = this.$t('pages.basicFunctions')
      function traverseTree(nodes) {
        nodes.forEach(node => {
          if (!node.children || node.children.length === 0) {
            const nodeLabel = node.label.toLowerCase()
            // 当前节点是叶子节点，添加到结果数组中
            if (nodeLabel.includes(operateName.toLowerCase()) || nodeLabel.includes(basicFunction.toLowerCase())) {
              // 再次过滤，确保拿到正确权限节点
              leafNodes.push({ ...node });
            }
          } else {
            // 当前节点不是叶子节点，递归处理其子节点
            traverseTree(node.children); // 注意这里不再传递leafNodes
          }
        });
      }
      // 从根节点开始遍历树
      traverseTree(nodes);
      // 返回叶子节点数组
      return leafNodes;
    },
    findDataIdInTree(tree, dataId) {
      function traverse(node) {
        if (Number.parseInt(node.dataId) === Number.parseInt(dataId)) {
          return true;
        }
        if (!node.children) {
          return false;
        }
        // 遍历所有子节点
        for (const child of node.children) {
          if (traverse(child)) {
            return true; // 如果在任何子节点中找到匹配项，则返回true
          }
        }
        return false; // 所有子节点检查完毕，未找到匹配项
      }
      return traverse(tree);
    },
    show(role, currentRoleId) {
      this.resetTemp()
      this.dlgVisible = true
      this.clearFilters()
      this.activeTabName = 'menu'
      this.expandedTreeKeys.splice(0)
      this.permissionMap = {}
      if (currentRoleId) {
        this.currentRoleId = currentRoleId
      }
      if (role) {
        const { id, name, parentId } = role
        this.expandedTreeKeys.push(id)
        this.temp.roleId = id
        this.temp.parentRoleId = parentId
        this.temp.roleName = name
        this.currentRole = name
        this.curClickNodeKey = id
        this.curClickParentId = parentId
      }
      this.$nextTick(() => {
        // 选中左侧角色树
        this.$refs.roleTree.checkSelectedNode(this.expandedTreeKeys)
        this.tabChange()
      })
    },
    clearFilters() {
      this.$refs.roleTree && this.$refs.roleTree.clearFilter()
      this.permissionMenuTree() && this.permissionMenuTree().clearFilter()
      this.permissionDeptTree() && this.permissionDeptTree().clearFilter()
      this.permissionRoleTree() && this.permissionRoleTree().clearFilter()
    },
    // 菜单 勾选节点的回调方法
    menuCheck(nodeData, checkedInfo) {
      const checkedKeys = [...checkedInfo.checkedKeys]
      const isCheck = checkedKeys.indexOf(nodeData.id) > -1
      // 当前节点有相关联的节点
      if (nodeData.relDataId) {
        // 在半选节点中查找当前节点的父节点
        const parentNode = checkedInfo.halfCheckedNodes.filter(({ id }) => id == nodeData.parentId)[0]
        // 父节点不存在，说明父节点已勾选 或者 未勾选，那就不需要处理关联的节点
        if (!parentNode) return
        // 勾选的节点是被关联的节点，那么需要将依赖节点勾选上
        if (isCheck && nodeData.dataId !== nodeData.relDataId) {
          // 依赖节点
          const relNodes = parentNode.children.filter(({ dataId }) => nodeData.relDataId.split(',').includes(dataId))
          // 如果依赖节点未勾选，则勾选上
          relNodes.forEach(relNode => {
            if (!checkedKeys.includes(relNode.id)) {
              checkedKeys.push(relNode.id)
              this.permissionMenuTree().setCheckedKeys(checkedKeys)
            }
          })
        }
        // 取消勾选的节点是依赖节点，那么需要将被关联的节点取消勾选
        if (!isCheck) {
          // 被关联的节点数组
          const relNodes = parentNode.children.filter(({ relDataId }) => relDataId.split(',').includes(nodeData.dataId))
          // 遍历并取消勾选
          relNodes.forEach(node => {
            this.permissionMenuTree().removeSelectedNode(node)
          });
        }
      }
    },
    // 菜单 勾选节点变化的回调方法
    menuCheckChange(checkedKey, checkedData, checkedInfo) {
      this.selectedMenuNodes = checkedInfo.checkedNodes
    },
    // 部门 勾选节点变化的回调方法
    deptCheckChange(checkedKey, checkedData, checkedInfo) {
      this.selectedDeptNodes = checkedInfo.checkedNodes
    },
    // tab 页面变更
    tabChange(data) {
      if (this.activeTabName === 'menu') {
        this.permissionMenuTree() && !this.menuTabEdit && this.listRolePermissionMenuId(this.temp.roleId, this.temp.parentRoleId)
        this.menuTabEdit = true
      } else if (this.activeTabName === 'data') {
        this.permissionDeptTree() && !this.deptTabEdit && this.listRolePermissionDataId(this.temp.roleId, this.temp.parentRoleId)
        this.deptTabEdit = true
      } else {
        this.permissionRoleTree() && !this.roleTabEdit && this.listRolePermissionRoleId(this.temp.roleId, this.temp.parentRoleId)
        this.roleTabEdit = true
      }
    },
    // 是否当前登录管理员的角色
    isLoginUserRole(roleId) {
      return this.roles.indexOf(roleId) > -1
    },
    loadDeptTree() {
      if (this.isThree) {
        getAllDeptTree().then(resp => {
          this.allDeptPermissionTreeData = [...resp.data]
        })
      }
    },
    // 点击左侧树节点
    handleNodeClick: function(data, node, el) {
      node.expand()
      const { dataId, label, parentId } = data
      // 禁止配置权限的角色
      const disabledRole = this.disableRoleId.findIndex(id => dataId == id) > -1
      // 三员模式下禁止配置权限的角色
      const isTreeRole = this.isThree && ['2', '3', '4'].findIndex(id => dataId == id) > -1
      // 也不能对自身配置权限
      const isMySelf = this.currentRoleId === Number.parseInt(dataId)
      // 父节点也不可点击
      const isParent = this.findDataIdInTree(data, this.currentRoleId)
      // 角色树顶级分组不提示信息
      const isTopNode = parentId == 0
      // 不能配置权限的情况
      if (!dataId || disabledRole || isTreeRole || isMySelf || isParent) {
        if ((dataId && Number.parseInt(dataId) > 4) || isMySelf || (isParent && !isTopNode)) {
          // 提示 您无权给此角色配置权限！
          this.$notify({ title: this.$t('text.prompt'), message: this.$t('pages.notHavePermissionsConfigRole'), type: 'warning', duration: 2000 })
        }
        this.expandedTreeKeys.splice(0, this.expandedTreeKeys.length, this.temp.roleId)
        this.$refs.roleTree.checkSelectedNode(this.expandedTreeKeys)
        return
      }
      this.resetTemp()
      this.temp.roleId = Number.parseInt(dataId)
      this.temp.parentRoleId = Number.parseInt(parentId.substring(1))
      this.temp.roleName = label
      this.currentRole = label
      this.curClickNodeKey = Number.parseInt(dataId)
      this.curClickParentId = Number.parseInt(parentId.substring(1))
      this.$refs.roleTree.checkSelectedNode([this.curClickNodeKey])
      this.tabChange()
    },
    // 初始化部门权限数据树
    initAllDeptPermissionTreeData() {
      if (this.isThree) {
        // 三员管理
        getAllDeptTree().then(resp => {
          this.allDeptPermissionTreeData = [...resp.data]
        })
      } else {
        this.allDeptPermissionTreeData = [...this.deptTree]
      }
    },
    // 初始化拥有菜单权限的树数据
    initAllMenuPermissionTreeData(routes, parentId) {
      parentId = parentId || ''
      routes = routes || asyncRoutes

      // 用户菜单权限
      const curUserMenuCodes = this.userMenuCodes
      // 构造菜单树数据（nodeData 菜单树数据，disableSize 同级禁用节点的数量）
      const menuNodeDataInfo = { nodeData: [], disableSize: 0 }
      routes.forEach((route, index) => {
        // 隐藏，禁用，常用菜单的路由
        const disableRoute = route.hidden || route.disabled || route.path == 'common_routes'
        if (disableRoute) return
        // 需要过滤的日志模块
        const logRoute = isRemoveRouteByLog(route, curUserMenuCodes)
        if (logRoute) return
        // 需要过滤的全盘扫描模块
        const diskScanRoute = isRemoveRouteByDiskScan(route, curUserMenuCodes)
        if (diskScanRoute) return
        // 构造节点
        const node = {
          id: parentId + '.' + index,
          dataId: route.code,
          label: route.meta ? this.generateTitle(route.meta.title) : '',
          parentId: parentId,
          type: 0,
          disabled: !this.isThree && route.code && curUserMenuCodes.indexOf(route.code) < 0
        }
        if (route.code) {
          this.menuCodeTitleData[route.code] = route.meta.title
        }
        if ('SS9' === route.code) {
          // 征兆报表在管理员权限这边只需要分配二级菜单的权限，三级菜单是根据报表配置随机展示的
        } else if (route.children && route.children.length > 0) {
          // 构造子节点
          const childInfo = this.initAllMenuPermissionTreeData(route.children, node.id)
          node.children = childInfo.nodeData
          if (node.children.length === childInfo.disableSize) {
            node.disabled = true
            menuNodeDataInfo.disableSize++
          }
          delete node.dataId
        } else if (node.disabled) {
          menuNodeDataInfo.disableSize++
        }
        if (node.dataId) this.allMenuPermissionTreeDataMap[node.dataId] = node
        menuNodeDataInfo.nodeData.push(node)
        // 功能节点
        const funcsNodes = this.funcPermissionMap[route.code]
        if (funcsNodes && funcsNodes.length > 0) {
          // 菜单功能分为 基础功能（menuCode继承父节点code） + 其他功能
          const basicNode = { menuCode: route.code, remark: this.$t('pages.basicFunctions') };
          if (!node.children) {
            node.children = []
          }
          // 功能节点处理
          [basicNode].concat(funcsNodes).forEach((funcRoute, index) => {
            // todo 多语言处理：前端->后台。
            const funcExtInfo = Object.assign({}, funcMenuCode[funcRoute.menuCode])
            const funcNode = {
              id: `${node.id}.${index}`,
              dataId: funcRoute.menuCode,
              relDataId: !funcExtInfo.rel ? route.code : `${funcExtInfo.rel},${route.code}`,
              label: funcRoute.remark,
              parentId: node.id,
              type: 0
            }
            node.children.push(funcNode)
            this.allMenuPermissionTreeDataMap[funcNode.dataId] = funcNode
          })
        }
      })
      return menuNodeDataInfo
    },
    // 格式化菜单权限树数据，过滤掉没有权限的数据。permissionIds = [*]表示所有带有编号的菜单
    formatMenuTreeData(nodeDataList, permissionIds, myselfLogAble) {
      // 没有权限，清空所有数据
      if (!permissionIds || permissionIds.length === 0) {
        nodeDataList.splice(0)
        return
      }
      // 遍历树数据
      for (let i = 0; i < nodeDataList.length; i++) {
        const nodeData = nodeDataList[i]
        if (this.isEnglish()) {
          nodeData.label = setTheFirstLetterToUppercase(nodeData.label)
        }
        if (nodeData.children && nodeData.children.length > 0) {
          this.formatMenuTreeData(nodeData.children, permissionIds, myselfLogAble)
        }
        // 超管或根三员
        const isRootRole = this.isSuperRole || this.isSuperThreeUser
        // 非超管或根三员，不允许将“企业密钥管理” E31 权限下发给其它管理员， dataId 菜单编码
        const isRemoveE31 = !isRootRole && 'E31' === nodeData.dataId
        // 如果是父级菜单，但是子节点为0，则无需展示
        const ieEmptyMenu = nodeData.children && nodeData.children.length === 0 || !nodeData.children && !nodeData.dataId
        // 没有权限的数据
        const notPermission = nodeData.dataId && permissionIds.findIndex(id => id == nodeData.dataId || id == '*') < 0
        // 如果未在高级配置里开启允许查看自身管理员日志的配置，默认不在菜单列表里显示管理员日志菜单按钮，因此在权限菜单列表里也进行隐藏
        const adminatorLog = !myselfLogAble && nodeData.dataId === 'A15'
        if (isRemoveE31 || ieEmptyMenu || notPermission || adminatorLog) {
          nodeDataList.splice(i, 1)
          i--
        }
      }
    },
    // 格式化部门权限树数据，过滤掉没有权限的数据。permissionIds = [*]表示所有部门
    formatDeptTreeData(nodeDataList, permissionIds) {
      // 没有数据，返回 false
      if (!nodeDataList || nodeDataList.length == 0) {
        return false
      }
      // 没有权限，清空所有数据，返回 false
      if (!permissionIds || permissionIds.length === 0) {
        nodeDataList.splice(0)
        return false
      }
      // 存放拥有权限的数据节点
      const resultNodes = []
      for (let i = 0; i < nodeDataList.length; i++) {
        const nodeData = nodeDataList[i]
        // 如果当前节点是有权限的节点，则将当前节点加入 resultNodes
        if (permissionIds.findIndex(id => id == nodeData.dataId || id == '*') > -1) {
          resultNodes.push(nodeData)
        } else {
          // 对子节点进行格式化
          const hasData = this.formatDeptTreeData(nodeData.children, permissionIds)
          // 有数据则添加到 resultNodes
          if (hasData > 0) {
            resultNodes.push(...nodeData.children)
          }
        }
      }
      // 部门权限树数据 更新为有权限的数据
      nodeDataList.splice(0, nodeDataList.length, ...resultNodes)
      // 返回 是否有数据
      return resultNodes.length > 0
    },
    // 请求数据的方法，并将返回数据存到 permissionMap
    invokeMethod(func, roleId, type, thenFunc) {
      // 菜单或部门 type + 角色 id
      const key = type + roleId
      // 通过 key 获取权限数据
      const existData = this.permissionMap[key]
      if (existData) {
        // 已存在数据，则将缓存的数据返回
        if (thenFunc) {
          setTimeout(() => {
            thenFunc({ code: 20000, data: existData })
          }, 100)
        }
      } else {
        // 请求数据
        func(roleId).then(respond => {
          if (respond.data) this.permissionMap[key] = respond.data
          if (thenFunc) thenFunc(respond)
        })
      }
    },
    // 展示角色菜单权限树，并勾选节点
    async listRolePermissionMenuId(roleId, parentRoleId) {
      this.tableLoading = true
      const myselfLogAble = await getIsViewMyselfLogAble()
      // 如果点击的角色是当前管理员的角色
      // if (this.isLoginUserRole(roleId)) parentRoleId = roleId
      // 获取父级角色的菜单权限，用于展示可查看节点
      this.invokeMethod(listMenuTree, parentRoleId, 'menu', (respond) => {
        // 拷贝 所有菜单权限树数据
        this.menuPermissionTreeData = JSON.parse(JSON.stringify(this.allMenuPermissionTreeData))
        // 是否 超管或三员管理员
        const isRootRoleId = parentRoleId == 0 && [1, 2, 3, 4].indexOf(roleId) > -1
        // 过滤掉没有权限的数据
        this.formatMenuTreeData(this.menuPermissionTreeData, isRootRoleId ? ['*'] : respond.data, myselfLogAble.data)
        if (this.isSuperMode) {
          // 只有超管模式才过滤，三员模式下，只有系统管理员才有管理员角色页面，如系统管理员可以对安全管理员分配安全管理员拥有的菜单权限，不用考虑自身
          this.filterMenuPermission()
        }
        // 获取当前角色的菜单权限，用于勾选节点
        this.invokeMethod(listMenuTree, roleId, 'menu', (resp) => {
          const checkedKeys = []
          resp.data.forEach(menuCode => {
            const nodeData = this.allMenuPermissionTreeDataMap[menuCode]
            // 根据角色菜单权限，勾选节点
            if (nodeData && !nodeData.children) {
              checkedKeys.push(nodeData.id)
            }
          })
          this.permissionMenuTree().setCheckedKeys(checkedKeys)
          this.tableLoading = false
        })
      })
    },
    removeDisabledNodes(tree) {
      return tree.map(node => {
        // 如果当前节点被禁用，则不返回该节点
        if (node.disabled === true) {
          return null;
        }
        // 递归处理子节点
        const children = node.children ? this.removeDisabledNodes(node.children) : [];
        // 过滤掉被移除的子节点（即null值）
        const filteredChildren = children.filter(child => child !== null);
        // 返回更新后的节点，包括更新后的子节点
        return {
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : undefined
        };
      }).filter(node => node !== null);
    },
    filterMenuPermission() {
      // 将权限树禁止选择的权限过滤掉
      var data = this.removeDisabledNodes([...this.menuPermissionTreeData])
      // 过滤远程维护这种非叶子节点但是却没有下级节点的节点
      data = this.filterNoLeafNodeAndNoChildren(data)
      this.menuPermissionTreeData = [...data]
      // 过滤掉子菜单为空的顶级菜单
      var tempPermission = []
      this.menuPermissionTreeData.forEach(item => {
        if (item.parentId == '' && item.children) {
          tempPermission.push(item)
        }
      })
      // 过滤不在用户菜单权限里的叶子节点（即导入、导出等按钮菜单）
      this.menuPermissionTreeData = this.filterLeafNodeUserMenuCode([...tempPermission], this.userMenuCodes);
    },
    filterNoLeafNodeAndNoChildren(tree) {
      return tree.map(node => {
        if (node.children) {
          // 当前节点有子节点，则先过滤子节点
          node.children = this.filterNoLeafNodeAndNoChildren(node.children)
          // 如果过滤后子节点为空数组，则返回 null
          if (node.children.length === 0) {
            return null;
          }
        }
        if (!('disabled' in node) && !('dataId' in node)) {
          // 如果节点没有 disabled 或 dataId ，且没有子节点，则返回 null
          if ((!node.children || node.children.length === 0)) {
            return null;
          }
        }
        // 返回处理后的节点
        return node;
      }).filter(node => node !== null);
    },
    // 过滤不在 idsToKeep 权限里的叶子节点
    filterLeafNodeUserMenuCode(tree, idsToKeep) {
      const idsSet = Array.isArray(idsToKeep) ? new Set(idsToKeep) : idsToKeep
      return tree.map(node => {
        const newNode = { ...node };
        if (newNode.children) {
          // 当前节点有子节点，则先过滤子节点
          newNode.children = this.filterLeafNodeUserMenuCode(newNode.children, idsSet);
          if (newNode.children.length === 0) {
            // 如果子节点过滤后为空，则返回 null
            return null;
          }
        }
        if (newNode.dataId && !idsSet.has(newNode.dataId)) {
          // 如果当前节点不在权限里，且没有子节点，则返回 null
          if (!newNode.children || newNode.children.length === 0) {
            return null;
          }
        }
        return newNode;
      }).filter(node => node !== null);
    },
    // 展示角色部门权限树，并勾选节点
    listRolePermissionDataId(roleId, parentRoleId) {
      this.tableLoading = true
      if (this.isLoginUserRole(roleId)) parentRoleId = roleId
      // 获取父级角色的部门权限，用于展示可查看节点
      this.invokeMethod(listData, parentRoleId, 'data', (respond) => {
        // 拷贝 所有部门权限树数据
        this.deptPermissionTreeData = JSON.parse(JSON.stringify(this.allDeptPermissionTreeData))
        const rootRoleId = [0, 1, 2, 3, 4]
        // const respndData = (respond.data && respond.data.length == 1 && respond.data[0] == 0) ? ['*'] : respond.data
        // const permissionIds = rootRoleId.indexOf(parentRoleId) > -1 ? ['*'] : respndData
        var permissionIdList = rootRoleId.indexOf(parentRoleId) > -1 ? ['*'] : respond.data
        if (permissionIdList && permissionIdList.length === 1 && permissionIdList[0] === 0) {
          permissionIdList = ['*']
        }
        // 过滤掉没有权限的数据
        this.formatDeptTreeData(this.deptPermissionTreeData, permissionIdList)
        // 获取当前角色的菜单权限，用于勾选节点
        this.invokeMethod(listData, roleId, 'data', resp => {
          const permissionIds = resp.data || []
          const checkedKeys = permissionIds.map(id => 'G' + id)
          const deptPermissionIds = []
          this.deptPermissionTreeData.forEach(deptItem => {
            if (!deptItem.disabled) {
              deptPermissionIds.push(deptItem.id)
            }
          })
          // todo 勾选的节点多的话，使用 setCheckedKeys 效率不高，页面会卡较长时间。使用 default-checked-key 来实现节点勾选
          if (checkedKeys && checkedKeys.length === 1 && checkedKeys[0] === 'G0' && deptPermissionIds.indexOf('G0') < 0) {
            this.permissionDeptTree().setCheckedKeys(deptPermissionIds)
          } else {
            this.permissionDeptTree().setCheckedKeys(checkedKeys)
          }
          this.tableLoading = false
        })
      })
    },
    // 展示管理员角色范围树，并勾选节点
    async listRolePermissionRoleId(roleId, parentRoleId) {
      this.permissionRoleData = []
      this.roleTabEdit = true
      // 树类型数据使用深拷贝
      this.permissionRoleData = JSON.parse(JSON.stringify(this.roleData));
      // 获取角色的管理员角色范围来勾选节点
      this.tableLoading = true
      var range = await getManagerRoleByRoleId(this.temp.roleId)
      if (parentRoleId != 0 && parentRoleId != 2) {
        // 需要根据父角色的管理范围进行过滤
        const parentRange = await getManagerRoleByRoleId(parentRoleId)
        var data = this.filterRoleRange([...this.permissionRoleData[0].children], parentRange.data)
        this.permissionRoleData[0].children = []
        data.length > 0 ? this.permissionRoleData[0].children = data : this.permissionRoleData = []
      }
      
      // 接口拿到的的是树组件的dataId字段，需要加上G才是树组件的id
      const roleIds = range.data.map(item => 'G' + item)
      this.$nextTick(() => {
        this.permissionRoleData = [...this.permissionRoleData]
        this.permissionRoleTree().setCheckedKeys(roleIds)
      })
      this.tableLoading = false
    },
    // 角色管理范围需要根据父角色的角色管理范围来进行限制，子角色的范围不能超过父角色的角色管理范围
    filterRoleRange(tree, ids) {
      const newTreeData = []
      // ids 转成 map ，便于判断 id 是否存在
      const idMap = ids.reduce((result, id) => {
        result[id] = id
        return result
      }, {})
      // 循环判断节点是否保留，若节点保留，则子节点不再判断；若节点不保留，则将其子节点插入到数据前面，继续进行循环
      while (tree.length > 0) {
        const data = tree.shift()
        const id = data.dataId
        if (idMap[id]) {
          newTreeData.push(data)
        } else {
          const children = data.children
          if (children && children.length > 0) {
            tree.unshift(...children)
          }
        }
      }
      return newTreeData
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.menuTabEdit = false
      this.deptTabEdit = false
      this.roleTabEdit = false
      this.menuPermissionTreeData.splice(0)
      this.deptPermissionTreeData.splice(0)
      this.$refs.roleTree && this.$refs.roleTree.checkSelectedNode([])
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    submitData(path, data) {
      if (!this.temp.roleId) {
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.selectAssignPermissions'), type: 'error', duration: 2000 })
        return
      }
      const subTitle = this.$t('pages.permissionsUpdateMsg', {
        roleName: this.temp.roleName,
        menuTabLabel: this.menuTabLabel,
        dataTabLabel: this.deptTabLabel,
        roleTabLabel: this.roleTabLabel
      });
      this.$confirmBox(subTitle, this.$t('text.prompt')).then(() => {
        this.selectedMenuNodes = [...this.permissionMenuTree().toSelectMenu().getCheckedNodes()]
        const permissionIds = {}                // 提交数据的权限id
        const menuPermissions = []              // 保存菜单权限，主要用于管理员日志多语言化

        // 菜单权限
        if (this.menuTabEdit) {
          const menuIds = []
          const existPermissionsSet = new Set()   // 保存已添加的数据
          this.selectedMenuNodes.forEach(item => {
            const { dataId, relDataId = null } = item
            if (dataId) {
              // 选中的节点有 dataId 就添加到 menuIds
              menuIds.push(dataId)
              
              // 选中节点的数据添加到 permissions
              if (!existPermissionsSet.has(dataId)) {
                existPermissionsSet.add(dataId);
                //  type=1 菜单，  type=2 按钮
                const type = !!relDataId && relDataId !== dataId ? 2 : 1
                const i18Key = type === 1 ? this.menuCodeTitleData[item.dataId] || null : null
                menuPermissions.push({ dataId, relDataId, type, i18Key })
              }
            }
          })
          permissionIds[0] = menuIds
        }

        // 部门权限
        if (this.deptTabEdit) {
          const deptIds = []
          const existIdSet = new Set()   // 保存已添加的数据
          this.selectedDeptNodes.forEach(node => {
            const { id, parentId, dataId } = node
            // 判断父节点是否存在，不存在则添加到 deptIds
            if (!existIdSet.has(parentId)) {
              deptIds.push(dataId)
            }
            // 无论是否添加到 deptIds，都要将 id 添加到 existIdSet
            existIdSet.add(id)
          })
          permissionIds[10] = deptIds
        }

        // 角色范围权限
        if (this.roleTabEdit) {
          const checkedNodes = this.permissionRoleTree().toSelectMenu().getCheckedNodes()
          // 如果选择了整棵角色树，需要将根节点即管理员角色移除
          const roleIds = [...new Set(checkedNodes.map(item => item.dataId).filter(Boolean))]
          permissionIds[11] = roleIds
        }

        const { roleId, roleName } = this.temp
        updatePermission({ roleId, permissionIds, roleName, menuPermissions }).then(() => {
          // if (this.menuTabEdit) this.permissionMap['menu' + roleId] = permissionIds[0]
          // if (this.deptTabEdit) this.permissionMap['data' + roleId] = permissionIds[10]
          // 某个角色成功更新权限后，直接清空存储权限信息的对象，避免多层级角色获取父级角色的权限时不是最新的权限信息
          this.permissionMap = {}
          this.resetTemp()
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          this.$nextTick(() => {
            this.$refs.roleTree.checkSelectedNode([this.curClickNodeKey])
            this.temp.roleId = this.curClickNodeKey
            this.temp.parentRoleId = this.curClickParentId
            this.temp.roleName = roleName
            this.tabChange()
          })
        })
      }).catch(() => {
      })
    }
  }
}
</script>
