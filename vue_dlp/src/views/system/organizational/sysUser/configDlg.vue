<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('button.highConfig')"
    :visible.sync="dialogVisible"
    width="600px"
    @dragDialog="handleDrag"
  >
    <Form
      ref="managerConfigForm"
      :model="managerConfig"
      label-width="40px"
      :extra-width="{en: 0}"
      label-position="right"
      style="width: 500px;"
    >
      <FormItem>
        <el-checkbox v-model="dataList.userMultiLogin.value" true-label="1" false-label="0">
          {{ $t('pages.multipleLoginAreAllowedForTheSameAdministratorAccount') }}
        </el-checkbox><br/>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="dataList.lockUserOnError.enable" :true-label="1" :false-label="0">
          {{ $t('pages.whenLoggingInPasswordErrorExceedsLimitLocked') }}
        </el-checkbox><br/>
        <span>
          <label style="width:80px;padding-left: 30px; font-size:13px">{{ $t('pages.limitTimes') }}</label> <el-input-number v-model="dataList.lockUserOnError.errorSize" :min="1" :max="9" :controls="false" :disabled="!dataList.lockUserOnError.enable" :precision="0" style="width: 80px;"/>
          <label style="width:80px;padding-left: 30px; font-size:13px">{{ $t('pages.lockDuration') }}</label> <el-input-number v-model="dataList.lockUserOnError.lockMinute" :min="1" :max="60" :controls="false" :disabled="!dataList.lockUserOnError.enable" :precision="0" style="width: 80px;"/>
          <label style="font-size:13px">{{ $t('pages.minuteLower') }}</label>
        </span>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="dataList.passwordLength.enable" :true-label="1" :false-label="0">
          <i18n path="pages.sysUserPasswordLengthDigit">
            <el-input-number slot="short" v-model="dataList.passwordLength.short" :min="5" :max="16" :controls="false" :disabled="!dataList.passwordLength.enable" :precision="0" style="width: 80px;"/>
            <el-input-number slot="long" v-model="dataList.passwordLength.long" :min="5" :max="16" :controls="false" :disabled="!dataList.passwordLength.enable" :precision="0" style="width: 80px;"/>
          </i18n>
        </el-checkbox>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.sysUserPasswordLengthContent') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </FormItem>
      <FormItem>
        <i18n path="pages.sysUserRegularUpdatePwd">
          <el-checkbox slot="checkbox" v-model="dataList.regularUpdatePwd.enable" :true-label="1" :false-label="0">
            {{ $t('pages.every') }}
            <el-input-number v-model="dataList.regularUpdatePwd.value" size="mini" :controls="false" :min="1" :max="366" :disabled="!dataList.regularUpdatePwd.enable" :precision="0" style="width: 80px;" />
          </el-checkbox>
          <el-select slot="period" v-model="dataList.regularUpdatePwd.unit" style="width: 100px; margin-top: -3px; text-align: center;" :disabled="!dataList.regularUpdatePwd.enable">
            <el-option :label="$t('text.year')" :value="3"/>
            <el-option :label="$t('text.month')" :value="2"/>
            <el-option :label="$t('text.day')" :value="1"/>
          </el-select>
        </i18n>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.sysUserPeriodContent') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="dataList.firstLoginUpPwd.value" true-label="true" false-label="false">{{ $t('pages.sysUserFirstLoginPwd') }}</el-checkbox>
      </FormItem>
      <FormItem>
        <label>{{ $t('pages.sysUserPasswordStrength') }}</label>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.sysUserPasswordStrengthContent">
              <br slot="br" />
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <el-radio-group v-model="dataList.passwordLevel.value" class="group">
          <el-radio :label="1">{{ $t('text.low') }}</el-radio>
          <el-radio :label="2">{{ $t('text.middle') }}</el-radio>
          <el-radio :label="3">{{ $t('text.high') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="dataList.forceValiPwdLevel.value" true-label="true" false-label="false">{{ this.$t('pages.forceValiPwdLevel') }}</el-checkbox>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            <!-- {{ $t('pages.sysUserPasswordMsg10') }} -->
            <i18n path="pages.sysUserPasswordMsg10">
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </FormItem>
      <FormItem>
        <div>
          {{ $t("pages.loginTimeoutSeconds") }}
          <el-select v-model="dataList.loginTimeoutSeconds.value" style="width: 130px; margin-top: -3px; text-align: center;">
            <el-option :label="'10' + $t('text.minute')" :value="'600'"/>
            <el-option :label="'30' + $t('text.minute')" :value="'1800'"/>
            <el-option :label="'60' + $t('text.minute')" :value="'3600'"/>
            <el-option :label="'120' + $t('text.minute')" :value="'7200'"/>
          </el-select>
        </div>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="dataList.loginCaptchaCode.value" true-label="true" false-label="false" @change="loginCaptchaCodeChange">{{ $t('pages.enableRandomVerifyCodeLogin') }}</el-checkbox>
        <el-checkbox v-model="dataList.loginCaptchaIgnoreCase.value" :disabled="dataList.loginCaptchaCode.value === 'false'" true-label="true" false-label="false">{{ $t('pages.ignoreCaseWhenVerify') }}</el-checkbox>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleConfig">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateConfig } from '@/api/system/organizational/sysUser'
import { getConfig } from '@/api/system/configManage/globalConfig'

export default {
  name: 'ConfigSysUser',
  data() {
    return {
      dialogVisible: false,
      dataList: {
        userMultiLogin: { isProp: true, label: this.$t('pages.multipointLogin'), value: '0' },
        lockUserOnError: { isProp: true, label: this.$t('pages.whenLoggingInPasswordErrorExceedsLimitLocked'), errorSize: 1, lockMinute: 10, enable: 0 },
        regularUpdatePwd: { label: this.$t('pages.sysUserPasswordRegularly'), value: 30, isProp: true, enable: 0, unit: 1 },
        firstLoginUpPwd: { label: this.$t('pages.sysUserPasswordFirstLoginChange'), value: true, isProp: true },
        passwordLength: { label: this.$t('pages.sysUserPasswordLengthLimit'), short: 5, long: 16, enable: 0 },
        passwordLevel: { label: this.$t('pages.sysUserPasswordStrengthLevel'), value: 1, isProp: true },
        forceValiPwdLevel: { label: this.$t('pages.forceValiPwdLevel'), value: false, isProp: true },
        loginTimeoutSeconds: { value: '1800', isProp: true },
        loginCaptchaCode: { value: false, isProp: true },  //  随机验证码
        loginCaptchaIgnoreCase: { value: false, isProp: true }  // 校验时忽略大小写
      },
      managerConfig: {},
      submitting: false
    }
  },
  watch: {

  },
  created() {
    this.getConfig()
  },
  methods: {
    show() {
      this.getConfig()
      this.dialogVisible = true
    },
    handleDrag() {
    },
    handleConfig() {
      this.submitting = true
      const formData = []
      for (const key in this.dataList) {
        if (key === 'lockUserOnError') {
          let value = ''
          if ((!this.dataList.lockUserOnError.errorSize || !this.dataList.lockUserOnError.lockMinute) && this.dataList.lockUserOnError.enable) {
            this.$message({
              message: this.$t('pages.sysUserPasswordMsg3', { limitTimes: this.$t('pages.limitTimes'), lockDuration: this.$t('pages.lockDuration') }),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (this.dataList.lockUserOnError.errorSize && this.dataList.lockUserOnError.lockMinute) {
            value = this.dataList.lockUserOnError.errorSize + '' + this.dataList.lockUserOnError.lockMinute
          }
          formData.push({ key: key, value: !this.dataList.lockUserOnError.enable ? '0' : value, isProp: true })
        } else if (key === 'regularUpdatePwd') {
          let value = ''
          if (this.dataList.regularUpdatePwd.enable && !this.dataList.regularUpdatePwd.value) {
            this.$message({
              message: this.$t('pages.sysUserPasswordMsg4'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (this.dataList.regularUpdatePwd.unit == 2 && this.dataList.regularUpdatePwd.value && this.dataList.regularUpdatePwd.enable) {
            value = (this.dataList.regularUpdatePwd.value * 30) + '' + this.dataList.regularUpdatePwd.unit
          } else if (this.dataList.regularUpdatePwd.unit == 3 && this.dataList.regularUpdatePwd.value && this.dataList.regularUpdatePwd.enable) {
            value = (this.dataList.regularUpdatePwd.value * 365) + '' + this.dataList.regularUpdatePwd.unit
          } else if (this.dataList.regularUpdatePwd.unit == 1 && this.dataList.regularUpdatePwd.value && this.dataList.regularUpdatePwd.enable) {
            value = this.dataList.regularUpdatePwd.value + '' + this.dataList.regularUpdatePwd.unit
          }
          formData.push({ key: key, value: !this.dataList.regularUpdatePwd.enable ? '0' + this.dataList.regularUpdatePwd.unit : value, isProp: true })
        } else if (key === 'passwordLength') {
          let value = ''
          if (this.dataList.passwordLength.enable && (!this.dataList.passwordLength.short || !this.dataList.passwordLength.long)) {
            this.$message({
              message: this.$t('pages.sysUserPasswordMsg5'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (this.dataList.passwordLength.short && this.dataList.passwordLength.long) {
            if (parseInt(this.dataList.passwordLength.short) > parseInt(this.dataList.passwordLength.long)) {
              this.$message({
                message: this.$t('pages.sysUserPasswordMsg6'),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
              return
            }
            value = this.dataList.passwordLength.short + '-' + this.dataList.passwordLength.long
          }
          formData.push({ key: key, value: !this.dataList.passwordLength.enable ? '' : value, isProp: true })
        } else {
          formData.push({ key: key, value: this.dataList[key].value, isProp: !!this.dataList[key].isProp })
        }
      }
      updateConfig(formData).then(res => {
        this.dialogVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.getConfig()
        this.submitting = false
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    getConfig() {
      getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'lockUserOnError') {
            this.dataList.lockUserOnError.enable = item.value !== '0' ? 1 : 0
            if (item.value.length == 1) {
              this.dataList.lockUserOnError.errorSize = 1
              this.dataList.lockUserOnError.lockMinute = 10
              continue
            }
            if (item.value.length > 1) {
              this.dataList.lockUserOnError.errorSize = parseInt(item.value.charAt(0))
              this.dataList.lockUserOnError.lockMinute = parseInt(item.value.slice(1))
            }
          } else if (item.key === 'regularUpdatePwd') {
            this.dataList.regularUpdatePwd.enable = item.value.charAt(0) !== '0' ? 1 : 0
            if (item.value.charAt(0) == '0') {
              this.dataList.regularUpdatePwd.unit = 1
              this.dataList.regularUpdatePwd.value = 30
              continue
            }
            const unit = parseInt(item.value.charAt(item.value.length - 1))
            if (unit == 2) {
              item.value = item.value.substr(0, item.value.length - 1) / 30
            } else if (unit == 3) {
              item.value = item.value.substr(0, item.value.length - 1) / 365
            } else if (unit == 1) {
              item.value = item.value.substr(0, item.value.length - 1)
            }
            this.dataList.regularUpdatePwd.unit = unit
            this.dataList.regularUpdatePwd.value = item.value
          } else if (item.key === 'passwordLevel') {
            this.dataList[item.key].value = parseInt(item.value)
          } else if (item.key === 'passwordLength') {
            this.dataList.passwordLength.enable = item.value !== '' ? 1 : 0
            if (item.value == '') {
              this.dataList.passwordLength.short = 5
              this.dataList.passwordLength.long = 16
              continue
            }
            if (item.value.length > 0) {
              this.dataList[item.key].short = item.value.split('-')[0]
              this.dataList[item.key].long = item.value.split('-')[1]
            }
          } else if (item.key === 'userMultiLogin') {
            this.dataList[item.key] = item
          } else if (item.key === 'firstLoginUpPwd') {
            this.dataList[item.key] = item
          } else if (item.key === 'forceValiPwdLevel') {
            this.dataList[item.key] = item
          } else if (item.key === 'loginTimeoutSeconds') {
            this.dataList[item.key] = item
          } else if (item.key === 'loginCaptchaCode') {
            this.dataList[item.key] = item
          } else if (item.key === 'loginCaptchaIgnoreCase') {
            this.dataList[item.key] = item
          }
        }
      })
      this.$forceUpdate()
    },
    loginCaptchaCodeChange(value) {
      if (value === 'false') {
        this.dataList.loginCaptchaIgnoreCase.value = false
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.el-form-item{
  .el-form-item__label{
    color: #ccc;
    line-height: 30px;
  }
  .el-form-item__content{
    line-height: 30px;
    .el-input__inner{
      height: 30px;
      line-height: 30px;
    }
  }
}
.weak,.medium,.strong {
  display: inline-block;
  height: 5px;
  width: 60px;
  margin-left: 3px;
  margin-top: 2px;
  font-size: 2px;
  text-align: center;
}
.btn {
  border: none;
}
.ul {
  list-style-type: none;
  margin-left: -30px;
}
</style>
