<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="roleTree"
        :data="roleTreeData"
        :current-node-key="currentNodeKey"
        @node-click="handleNodeClick"
      ></tree-menu>
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">{{ $t('button.addAdministrator') }}</el-button>
        <el-button v-permission="'378'" icon="el-icon-lock" size="mini" :disabled="!btnEnable" type="primary" @click="batchLockUnlockVisible = true;password = ''">{{ $t('button.batchLockUnlock') }}</el-button>
        <el-button v-if="$store.getters.adminValidConfigAble" icon="el-icon-time" size="mini" :disabled="!btnEnable" type="primary" @click="handleUpdateValid">
          {{ $t('button.updateValidTime') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!btnEnable" @click="handleDelete">{{ $t('button.delAdministrator') }}</el-button>
        <el-button v-permission="'258'" icon="el-icon-setting" size="mini" @click="handleSetting">{{ $t('button.highConfig') }}</el-button>
        <el-button v-permission="'373'" size="mini" @click="loginAuthChange">{{ $t('pages.multiLoginAuth') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('components.searchPlaceholder_3')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('table.sysUserAccount')">
                <el-input v-model="query.account" v-trim clearable maxlength="20"/>
              </FormItem>
              <FormItem :label="$t('table.sysUserName')">
                <el-input v-model="query.name" v-trim clearable maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.sysUserType')">
                <el-select v-model="query.userType" style="width: 100%;">
                  <el-option v-for="(user, i) in userTypes" :key="i" :label="user.label" :value="user.value"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.searchMode')">
                <el-select v-model="query.includeChildRole" style="width: 100%;">
                  <el-option :value="1" :label="$t('pages.searchCurrentAndChildRole')"/>
                  <el-option :value="0" :label="$t('pages.searchCurrentRole')"/>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="() => { resetQuery(); treeMenu && treeMenu.setCurrentKey(null) }">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>

      <grid-table
        ref="adminTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :auto-load="false"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.batchLockUnlock')"
      :visible.sync="batchLockUnlockVisible"
      width="400px"
    >
      <div>
        <el-radio-group v-model="lockType">
          <el-radio :label="0">{{ $t('button.batchLock') }}</el-radio>
          <el-radio :label="1">{{ $t('button.batchUnlock') }}</el-radio>
        </el-radio-group>
        <el-input v-model="password" type="password" :placeholder="$t('pages.enterAdminPwd')" show-password @keydown.enter.native="handleBatchLock"></el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleBatchLock">{{ $t('button.confirm') }}</el-button>
        <el-button @click="batchLockUnlockVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.updateValidTime')"
      :visible.sync="batchUpdateValidTimeVisible"
      width="600px"
    >
      <Form ref="validTimeForm" :rules="rules2" :model="validTemp" label-width="120px">
        <FormItem :label="$t('table.sysUserType')">
          <el-radio-group v-model="validTemp.userType" @change="userTypeChange">
            <el-radio :label="0">{{ $t('pages.permanentUser') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.temporaryUser') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.validTime')" prop="rangeDate">
          <el-date-picker
            v-model="rangeDate"
            :disabled="validTemp.userType === 0"
            :clearable="true"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 400px"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="confirmUpdateValid">{{ $t('button.confirm') }}</el-button>
        <el-button @click="batchUpdateValidTimeVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @open="open"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" :extra-width="{en: 65}" style="width: 550px;">
        <input type="text" class="autocomplete">
        <input type="password" class="autocomplete">
        <FormItem :label="$t('pages.sysUserAccount')" prop="account">
          <el-input v-model="temp.account" maxlength="20" :disabled="isEditRootRole"/>
        </FormItem>
        <FormItem :label="$t('pages.sysUserName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60" :disabled="isEditRootRole"/>
        </FormItem>
        <FormItem :label="$t('form.password')" encrypt prop="password" tooltip-placement="bottom-start">
          <template v-if="dialogStatus !== 'create'" slot="tooltip">
            <el-tooltip effect="dark" placement="top">
              <div slot="content">{{ $t('pages.sysUserPasswordMsg11') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <el-popover
            placement="top-end"
            class="pop"
            width="280"
            trigger="click"
          >
            <div v-show="valiDataList.passwordLength">
              <i v-show="valiDataList.failLength" class="el-icon-close red bolder" />
              <i v-show="valiDataList.successLength" class="el-icon-check green bolder" />
              {{ this.$t('pages.sysUserPasswordMsg1',{passwordLength: passwordLength}) }}
            </div>
            <div v-show="valiDataList.passwordLevel">
              <i v-show="valiDataList.failLevel" class="el-icon-close red bolder" />
              <i v-show="valiDataList.successLevel" class="el-icon-check green bolder" />
              {{ this.$t('pages.sysUserPasswordMsg2',{passwordLevel: valiDataList.passwordLevel}) }}<br/>
            </div>
            <encrypt-input slot="reference" v-model="temp.password" show-password maxlength="64" non-blank @focus.capture.native="strengthShow" @keyup.native="strengthShow" @input="temp.password=temp.password.replace(/[\u4E00-\u9FA5]/g,'').trim()"/>
          </el-popover>
        </FormItem>
        <FormItem :label="$t('form.confirmPassword')" no-submit prop="confirmPassword">
          <encrypt-input v-model="temp.confirmPassword" show-password maxlength="64" non-blank/>
        </FormItem>
        <FormItem v-if="temp.id != 1" :label="$t('pages.role')" prop="roleId">
          <tree-select
            ref="roleTree"
            :data="treeData"
            node-key="dataId"
            :filter-node-method="filterNodeMethod"
            :checked-keys="temp.roleIds"
            hide-tag-close
            :permission="roleRange"
            :width="296"
            check-strictly
            :disabled="isEditRootRole || temp.id === userId"
            :multiple="true"
            @change="roleNodeSelectChange"
          />
        </FormItem>
        <FormItem v-if="$store.getters.adminValidConfigAble" :label="$t('table.sysUserType')">
          <el-radio-group v-model="temp.userType" @change="userTypeChange">
            <el-radio :label="0">{{ $t('pages.permanentUser') }}</el-radio>
            <el-radio :label="1" :disabled="temp.id <= 4 || !$store.getters.adminValidConfigAble">{{ $t('pages.temporaryUser') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="$store.getters.adminValidConfigAble" :label="$t('pages.validTime')" prop="rangeDate">
          <el-date-picker
            v-model="rangeDate"
            :disabled="temp.userType === 0 || !$store.getters.adminValidConfigAble"
            :clearable="true"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 100%"
          />
        </FormItem>
        <FormItem :label="$t('form.email')" prop="email">
          <el-input v-model="temp.email" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('form.phone')" prop="phone">
          <el-input v-model="temp.phone" :maxlength="15" suffix-icon="el-icon-phone"/>
        </FormItem>
        <FormItem :label="$t('form.status')">
          <el-select v-model="temp.active" :placeholder="$t('text.select')">
            <el-option v-for="(value, key) in activeOptions" :key="key" :label="value" :value="key"/>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="$t('button.delAdministrator')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :delete-filter-name="$t('pages.multiLoginAuth_adminMessage')"
      :edit-type="1"
      @submitEnd="deleteData"
    />
    <config-sys-user ref="configDlg"/>
    <login-auth-dlg ref="loginAuthDlg" :default-checked-keys="selectedUserIds" :tree-data="sysUserTreeData"/>
    <validate-pass-dlg ref="validatePass" :tip="validateTip" @validated="BatchUpdateValidTime" @cancel="cancelInit"/>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { isSamePassword } from '@/api/system/organizational/threeUser'
import { isSameTimestamp, initTimestamp } from '@/utils'
import { getPage, getByAccount, createUser, updateUser, deleteUser, unlockUser, kickoutUser, batchLock, updateValidTime } from '@/api/system/organizational/sysUser'
import { getLoginAuthSysUser } from '@/api/user'
import { findNode, toNodeValue, treeDatasToLists } from '@/utils/tree'
import { getManagerRoleByRoleId } from '@/api/system/organizational/permission'
import ConfigSysUser from './configDlg'
import { getConfig } from '@/api/system/configManage/globalConfig'
import { validatePassword } from '@/utils/validate'
import LoginAuthDlg from '@/views/loginAuth/loginAuthDialog'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import EncryptInput from '@/components/EncryptInput'
import { getFactoryPwd } from '@/utils/dictionary'
import { base64DecodeSpe } from '@/utils/encrypt'

// import { getMultiAuthSysUser } from '@/api/loginAuth';
import moment from 'moment'
import ValidatePassDlg from '@/views/common/validatePassDlg'

export default {
  name: 'Administrator',
  components: { ConfigSysUser, LoginAuthDlg, BatchEditPageDlg, ValidatePassDlg, EncryptInput },
  data() {
    return {
      batchLockUnlockVisible: false,
      lockType: 0,
      colModel: [
        { prop: 'account', label: 'sysUserAccount', width: '150', sort: true, iconFormatter: this.activeFormatter },
        { prop: 'name', label: 'sysUserName', width: '150', sort: true },
        { prop: 'roleIds', label: 'role', width: '150', formatter: this.roleFormatter },
        { prop: 'email', label: 'email', width: '150', sort: 'custom' },
        { prop: 'phone', label: 'phone', width: '150', sort: 'custom' },
        { prop: 'userType', label: 'sysUserType', width: '150', formatter: this.userTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate, disabledFormatter: this.updateBtnDisabled },
            { label: 'unlock', click: this.handleUnlock, isShow: (data) => { return data.isLock } }
            // { label: 'kickout', click: this.handleKickout, isShow: row => row.isOnline }
          ]
        }
      ],
      roleTreeData: [],             // 当前角色的树数据
      roleRange: [],                // 当前角色配置的角色范围, 如果没有配置，则范围为当前角色及子角色
      treeData: [],
      currentNodeKey: '',
      activeOptions: { 1: this.$t('text.enable'), 0: this.$t('text.disable') },
      btnEnable: false,
      showTree: true,
      password: '',
      checkedRowKeys: [],
      defaultTemp: {
        id: undefined,
        name: '',
        account: '',
        email: '',
        phone: '',
        password: '',
        confirmPassword: '',
        roleIds: [],
        roleNames: [],
        active: '1',
        upPwdTime: null,
        userType: 0,
        validStartDate: null,
        validEndDate: null
      },
      query: { // 查询条件
        ignoreSuperAdmin: true, //  超管模式下，非admin用户将忽略admin管理账号信息
        searchInfo: '',
        roleId: undefined,
        account: '',
        name: '',
        userType: '',
        validStartDate: null,
        validEndDate: null,
        includeChildRole: 1
      },
      oldRoleNames: [], // 旧的角色名称，用来后台修改时记录角色变化
      temp: {},
      dialogFormVisible: false,
      dialogStatus: '',
      valiDataList: {
        passwordLevel: '',
        passwordLength: '',
        successLength: false,
        failLength: false,
        successLevel: false,
        failLevel: false
      },
      passwordLength: '',
      textMap: {
        update: this.$t('pages.updateSysUser'),
        create: this.$t('pages.addSysUser')
      },
      validateTip: this.$t('pages.delete_group_dlg_text5'),
      rules: {
        account: [{ required: true, trigger: 'blur', validator: this.accountValidator }],
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        email: [{ type: 'email', message: this.$t('pages.validateMsg_email'), trigger: ['blur', 'change'] }],
        phone: [{ trigger: 'blur', validator: this.phoneValidator }],
        password: [{ required: true, trigger: 'blur', validator: this.passwordValidator }],
        confirmPassword: [{ required: true, message: this.$t('pages.threeUserConfirm', { content: this.$t('pages.pwdInfo') }), trigger: 'blur', validator: this.confirmPasswordValidator }],
        roleId: [{ required: true, trigger: 'change', validator: this.roleValidator }],
        rangeDate: [{ trigger: ['blur', 'change'], validator: this.rangeDateValid }]
      },
      rules2: {
        rangeDate: [{ trigger: ['blur', 'change'], validator: this.rangeDateValid }]
      },
      submitting: false,
      selectedUserIds: [],
      sysUserTreeData: [{ id: 'G0', dataId: 0, label: this.$t('pages.multiLoginAuth_adminMessage'), children: [] }],
      factoryPwd: getFactoryPwd(),
      rangeDate: [],
      userTypes: [
        { label: this.$t('pages.all'), value: 0 },
        { label: this.$t('pages.permanentUser'), value: 1 },
        { label: this.$t('pages.temporaryUser'), value: 2 },
        { label: `${this.$t('pages.temporaryUser')} (${this.$t('pages.effective')})`, value: 5 },
        { label: `${this.$t('pages.temporaryUser')} (${this.$t('pages.notActivated')})`, value: 3 },
        { label: `${this.$t('pages.temporaryUser')} (${this.$t('pages.expired')})`, value: 4 }
      ],
      validTemp: {
        ids: '',
        userType: 0,
        validStartDate: null,
        validEndDate: null,
        password: ''
      },
      batchUpdateValidTimeVisible: false
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'isSuperMode',
      'isSuperRole',
      'isSysRole',
      'roleTree',
      'currentRoleId'
    ]),
    roleNameMap() {
      const { lists } = treeDatasToLists(this.roleTree)
      const nameMap = lists.reduce((map, node) => {
        const { dataId, label } = node
        map[dataId] = label
        return map
      }, {})
      return nameMap
    },
    // 角色是否可以修改
    isEditRootRole() {
      if (this.dialogStatus === 'create') {
        return false
      }
      // 审计管理员	安全管理员 系统管理员 不能修改角色
      return [1, 2, 3, 4].includes(this.temp.id)
    },
    tableData() {
      return this.$refs['adminTable'].$data
    },
    gridTable() {
      return this.$refs['adminTable']
    },
    treeMenu() {
      return this.$refs['roleTree']
    }
  },
  watch: {
    roleTree(val) {
      this.filterRoleRange()
    },
    roleRange(val) {
      this.filterRoleRange()
    },
    currentRoleId(val) {
      this.loadRoleRange()
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
    this.loadTreeData()
    this.initFromRouteQuery()
    this.getConfig()
    this.addStatusListener()
  },
  activated() {
    if (!isSameTimestamp(this, 'Role')) {
      this.loadTreeData()
      this.initFromRouteQuery()
    }
  },
  deactivated() {
    // 更新系统管理员列表
    this.$store.dispatch('commonData/setSysUserList')
  },
  beforeDestroy() {
    this.$socket.unsubscribe('/topic/login')
  },
  methods: {
    // 添加 管理员状态变更的 websocket 推送，有变化时重新加载列表
    addStatusListener() {
      this.$socket.subscribe({
        url: '/topic/login',
        callback: (resp, handle) => {
          if (!this.btnEnable) {
            this.query.page = undefined
            this.gridTable.execRowDataApi({ ...this.query, slient: true })
          }
        }
      })
    },
    // 加载角色树数据
    loadTreeData() {
      this.$store.dispatch('commonData/setRoleTree', data => {
        this.loadRoleRange()
      })
    },
    // 加载角色范围
    loadRoleRange() {
      if (this.currentRoleId) {
        getManagerRoleByRoleId(this.currentRoleId).then(res => {
          const range = res.data
          if (range.length > 0) {
            // 有配置角色范围
            this.roleRange = range
          } else {
            // 没有配置范围，
            const currentNode = findNode(this.roleTree, this.currentRoleId, 'dataId')
            if (currentNode) {
              this.roleRange = toNodeValue(currentNode, 'dataId')
            }
          }
          this.treeMenu.setCurrentKey('G' + this.query.roleId)
          this.gridTable.execRowDataApi(this.query)
        })
      }
    },
    initFromRouteQuery: function() {
      const query = this.$route.query
      this.showTree = query.hasOwnProperty('showTree') ? query.showTree : this.showTree
      if (query.hasOwnProperty('roleId') && query.hasOwnProperty('includeChildRole')) {
        this.query.roleId = query.roleId
        this.query.includeChildRole = query.includeChildRole
        this.currentNodeKey = query.roleId
        this.defaultTemp.roleId = query.roleId
      }
    },
    // 根据管理员角色范围过滤管理员
    filterRoleRange() {
      // 非超级管理员、系统管理员
      if (!this.isSuperRole && this.currentRoleId != 2) {
        // 获取当前登录管理员的角色id
        const roleId = this.currentRoleId
        if (!roleId || this.roleRange.length == 0) return
        // 根据 roleRange 过滤树数据
        const roleTree = this.getFilterTreeDataByIds([...this.roleTree], this.roleRange)
        const treeData = [{ id: '', label: this.$t('pages.sysUserRole'), parentId: '0', children: [...roleTree] }]
        this.roleTreeData = treeData
      } else {
        const treeData = [{ id: '', label: this.$t('pages.sysUserRole'), parentId: '0', children: [...this.roleTree] }]
        this.roleTreeData = treeData
      }
      this.treeData = [...this.roleTreeData[0].children]
    },
    // 根据 ids 获取过滤后的树数据，数据节点包含ids及子节点
    getFilterTreeDataByIds(treeData, ids) {
      const newTreeData = []
      // ids 转成 map ，便于判断 id 是否存在
      const idMap = ids.reduce((result, id) => {
        result[id] = id
        return result
      }, {})
      // 循环判断节点是否保留，若节点保留，则子节点不再判断；若节点不保留，则将其子节点插入到数据前面，继续进行循环
      while (treeData.length > 0) {
        const data = treeData.shift()
        const id = data.dataId
        if (idMap[id]) {
          newTreeData.push(data)
        } else {
          const children = data.children
          if (children && children.length > 0) {
            treeData.unshift(...children)
          }
        }
      }
      return newTreeData
    },
    handleNodeClick: function(data, node, el) {
      node.expanded = true
      this.defaultTemp.roleId = data.dataId
      this.query.roleId = data.dataId
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi(option) {
      this.selectedUserIds.splice(0)
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.searchType = 0
      // 不是超管或者系统管理员
      if (!this.isSuperRole && this.currentRoleId != 2) {
        // 获取当前登录管理员的角色id
        const roleId = this.currentRoleId
        if (!roleId || this.roleRange.length == 0) {
          // 若还未加载角色及范围，则返回空数组
          return new Promise((resolve, reject) => {
            resolve({ code: 20000, data: { total: 0, items: [] }})
          })
        }
        if (!searchQuery.roleId) {
          // 查询顶层分组，就是查当前配置的管理员范围
          searchQuery.roleIds = this.roleRange.join(',')
        }
      }
      return getPage(searchQuery)
    },
    selectionChangeEnd(data) {
      this.checkedRowKeys = data.map(v => v.id)
      this.btnEnable = data.length > 0
      this.selectedUserIds.splice(0);
      (data || []).forEach(user => {
        this.selectedUserIds.push(user.id)
      })
    },
    roleNodeSelectChange(ids, options) {
      this.temp.roleIds = [...ids]
      this.temp.roleNames = options.map(option => option.label)
    },
    // 过滤节点的方法，此处通过遍历节点，设置管理范围外的节点禁用
    filterNodeMethod(value, data) {
      if (this.isSuperRole || this.currentRoleId === 2) {
        data.disabled = false
      } else {
        data.disabled = this.roleRange.findIndex(id => id == data.dataId) == -1
      }
      return true
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.oldRoleNames.splice(0)
      this.temp.roleIds.splice(0)
      this.rangeDate = []
    },
    resetQuery() {
      this.query = {
        searchInfo: '',
        roleId: undefined,
        account: '',
        name: '',
        userType: '',
        validStartDate: null,
        validEndDate: null,
        includeChildRole: 1
      }
    },
    handleFilter() {
      this.query.page = 1
      this.query.validStartDate = null
      this.query.validEndDate = null
      this.gridTable.execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleBatchLock() {
      const lock = this.lockType == 0
      const ids = this.gridTable.getSelectedIds().join(',')
      this.submitting = true
      const data = { ids, lock, password: this.password, encryptProps: ['password'] }
      batchLock(data)
        .then(resp => {
          this.submitting = false
          if (!resp.data) {
            this.$message({
              message: this.$t('pages.wrongPassword'),
              type: 'error'
            })
            return
          }
          this.$message({
            message: this.$t('text.operateSuccess'),
            type: 'success'
          })
          this.gridTable.execRowDataApi(this.query)
          this.batchLockUnlockVisible = false
          this.password = ''
        }).catch(() => {
          this.submitting = false
        })
    },
    handleUpdateValid() {
      this.batchUpdateValidTimeVisible = true
      this.rangeDate = []
      this.validTemp.userType = 0
      this.$nextTick(() => {
        this.$refs['validTimeForm'].clearValidate()
      })
    },
    confirmUpdateValid() {
      this.$refs['validTimeForm'].validate(valid => {
        if (valid) {
          this.$refs['validatePass'].show()
          this.batchUpdateValidTimeVisible = false
        }
      })
    },
    cancelInit() {
      this.batchUpdateValidTimeVisible = true
    },
    BatchUpdateValidTime() {
      this.validTemp.ids = this.gridTable.getSelectedIds().join(',')
      if (this.validTemp.userType === 1) {
        this.validTemp.validStartDate = this.rangeDate[0]
        this.validTemp.validEndDate = this.rangeDate[1]
      } else {
        this.validTemp.validStartDate = null
        this.validTemp.validEndDate = null
      }
      this.submitting = true
      updateValidTime(this.validTemp).then(resp => {
        this.submitting = false
        if (!resp.data) {
          this.$message({
            message: this.$t('pages.wrongPassword'),
            type: 'error'
          })
          return
        }
        this.$message({
          message: this.$t('text.operateSuccess'),
          type: 'success'
        })
        this.gridTable.execRowDataApi(this.query)
        this.batchUpdateValidTimeVisible = false
        this.password = ''
      }).catch(() => {
        this.submitting = false
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.rules.password[0].required = true
      this.rules.confirmPassword[0].required = true
      const roleId = this.query.roleId
      this.temp.roleIds.push(roleId)
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleKickout(row) {
      await kickoutUser(row.id)
      this.$message({
        message: this.$t('text.operateSuccess'),
        type: 'success'
      })
      this.gridTable.execRowDataApi(this.query)
    },
    handleUpdate(row) {
      this.temp = JSON.parse(JSON.stringify(Object.assign({}, this.defaultTemp, row))) // copy obj
      this.temp.active = this.temp.active.toString()
      this.dialogStatus = 'update'
      if (this.temp.hasPassword == 1) {
        this.temp.password = '      '
        this.temp.confirmPassword = '      '
      }
      if (this.temp.validStartDate != null || this.temp.validEndDate != null) {
        this.temp.userType = 1
        this.rangeDate = [moment(new Date(this.temp.validStartDate)).format('YYYY-MM-DD HH:mm:ss'), moment(new Date(this.temp.validEndDate)).format('YYYY-MM-DD HH:mm:ss')]
      } else {
        this.temp.userType = 0
        this.rangeDate = []
      }
      this.dialogFormVisible = true
      this.rules.password[0].required = false
      this.rules.confirmPassword[0].required = false
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        const roleNames = this.$refs.roleTree.getSelectedNode().map(node => node.label)
        this.oldRoleNames = [...roleNames]
      })
    },
    handleImport() {},
    handleExport() {},
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.temp.validStartDate = this.temp.userType === 1 ? this.rangeDate[0] : null
          this.temp.validEndDate = this.temp.userType === 1 ? this.rangeDate[1] : null
          const tempData = JSON.parse(JSON.stringify(this.temp))
          createUser(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    async formatData() {
      let flag = false
      if (this.temp.password.trim() === '') {
        flag = false
      } else {
        await isSamePassword({
          id: this.temp.id,
          password: this.temp.password.trim(),
          encryptProps: ['password']
        }).then(response => {
          if (!response.data) {
            flag = true
          }
        })
      }
      return flag
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          this.temp.validStartDate = this.temp.userType === 1 ? this.rangeDate[0] : null
          this.temp.validEndDate = this.temp.userType === 1 ? this.rangeDate[1] : null
          await this.formatData().then(res => {
            if (res) {
              this.temp.upPwdTime = new Date()
            }
          })
          const tempData = JSON.parse(JSON.stringify(this.temp))
          tempData.roleNames_new = tempData.roleNames
          tempData.roleNames = this.oldRoleNames
          updateUser(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$store.dispatch('user/resetUpPwdTime', this.temp.upPwdTime)
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    getConfig() {
      getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'passwordLevel') {
            this.valiDataList.passwordLevel = item.value
          } else if (item.key === 'passwordLength') {
            this.valiDataList.passwordLength = item.value
            if (item.value.split('-')[0] === item.value.split('-')[1]) {
              this.passwordLength = item.value.split('-')[0]
            } else {
              this.passwordLength = item.value
            }
          }
        }
      })
      this.$forceUpdate()
    },
    handleDelete() {
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    deleteData(params, callback) {
      if (params.ids !== undefined && params.ids.length !== 0) {
        const toDeleteIds = params.ids.split(',')
        if (toDeleteIds.some(v => v <= 4)) {
          this.$message({
            message: this.$t('pages.deleteContainsSuperUser'),
            type: 'error'
          })
          this.$refs['batchDeleteDlg'].resetSubmitting()
          return
        }
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteUser(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(() => {
        this.$refs['batchDeleteDlg'].resetSubmitting()
      })
    },
    handleUnlock(row) {
      unlockUser({ id: row.id }).then(respond => {
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.unlockSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handleSetting() {
      this.$refs['configDlg'].show()
    },
    strengthShow() {
      this.valiDataList = validatePassword(null, this.temp.password, this.valiDataList)
    },
    open() {
      this.getConfig()
    },
    accountValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterAccount')))
      } else {
        getByAccount({ account: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('pages.validateMsg_sameAccount')))
          } else {
            callback()
          }
        })
      }
    },
    roleValidator(rule, value, callback) {
      if (!this.temp.roleIds || this.temp.roleIds.length === 0) {
        callback(new Error(this.$t('pages.validateMsg_role')))
      } else {
        callback()
      }
    },
    phoneValidator(rule, value, callback) {
      if (value && !(/^1[3456789]\d{9}$/.test(value))) {
        callback(new Error(this.$t('pages.validateMsg_phone')))
      } else {
        callback()
      }
    },
    passwordValidator(rule, value, callback) {
      let error
      if (!value.trim()) {
        this.valiDataList.failLength = false
        this.valiDataList.failLevel = false
        if (this.dialogStatus === 'update') {
          this.rules.confirmPassword[0].required = false
        }
        if (rule.required) {
          error = new Error(this.$t('pages.validateMsg_password'))
        }
      } else if (value && this.factoryPwd[this.temp.account.toLowerCase()] && value == base64DecodeSpe(this.factoryPwd[this.temp.account.toLowerCase()])) {
        error = new Error(this.$t('pages.validateMsg_factoryPassword'))
      } else if (value && this.factoryPwd[''] && value == base64DecodeSpe(this.factoryPwd[''])) {
        error = new Error(this.$t('pages.validateMsg_factoryPassword'))
      } else {
        if (this.valiDataList.failLength || this.valiDataList.failLevel) {
          error = new Error(this.$t('pages.validateMsg_password_NotMatch'))
        }
        this.rules.confirmPassword[0].required = true
      }
      callback(error)
    },
    confirmPasswordValidator(rule, value, callback) {
      if (value !== this.temp.password) {
        callback(new Error(this.$t('pages.validateMsg_diffPassword')))
      } else {
        callback()
      }
    },
    activeFormatter: function(row, data) {
      const { isOnline, active, isLock: lock } = row
      // 在线图标
      const onlineIcon = {
        true: { class: 'using', title: this.$t('pages.online'), style: 'color: #1CA048;' },
        false: { class: 'using', title: this.$t('pages.offline'), style: 'color: gray;' }
      }[!!isOnline]
      // 是否启用
      const isActive = { 1: this.$t('text.enable'), 0: this.$t('text.disable') }[active]
      const activeClass = { 1: 'active', 0: 'inactive' }[active]
      // 是否锁定
      const isLock = { true: this.$t('pages.lock'), false: this.$t('pages.unlock') }[lock]
      const lockClass = { true: 'Lock', false: 'Unlock' }[lock]
      // 启用、锁定：图标信息合并
      const statusTitle = this.$t('pages.sysUserActiveOpt', { isActive, isLock })
      const iconClass = activeClass + lockClass
      return [
        onlineIcon,
        { class: iconClass, title: statusTitle }
      ]
    },
    roleFormatter: function(row, data) {
      return data.map(dataId => this.roleNameMap[dataId]).join(',')
    },
    userTypeFormatter(row, data) {
      if (row.validStartDate != null || row.validEndDate != null) {
        const now = new Date()
        if (now < new Date(row.validStartDate)) {
          return `${this.$t('pages.temporaryUser')} (${this.$t('pages.notActivated')})`
        } else if (now >= new Date(row.validEndDate)) {
          return `${this.$t('pages.temporaryUser')} (${this.$t('pages.expired')})`
        } else {
          return `${this.$t('pages.temporaryUser')} (${this.$t('pages.effective')})`
        }
      } else {
        return this.userTypes[1].label
      }
    },
    selectable(row, index) {
      if (row.id <= 4) {
        return false
      }
      // 自身不能删除
      if (row.id === this.userId) {
        return false
      }
      return !this.updateBtnDisabled(row)
    },
    // 修改按钮是否禁用
    updateBtnDisabled(rowData) {
      if (this.isSuperRole || this.currentRoleId === 2) {
        // 超级管理员、系统管理员不禁用
        return false
      } else {
        var num = 0
        rowData.roleIds.forEach(item => {
          var formatRoleRange = [...this.roleRange]
          formatRoleRange = formatRoleRange.map(Number)
          if (!formatRoleRange.includes(item)) {
            num++
          }
        })
        return num > 0
      }
    },
    //  登录认证
    loginAuthChange() {
      this.sysUserTreeData[0].children = []
      // 获取当前用户可配置的多重登录认证用户
      // getMultiAuthSysUser().then(res => {
      //   const userData = res.data || []
      //   userData.forEach(item => {
      //     item.dataId = item.userId
      //     item.label = item.username || ''
      //     // todo 登录认证的管理员信息需要过滤吗？children 有啥用
      //     item.children = []
      //     this.sysUserTreeData[0].children.push(item)
      //   })
      // }).finally(() => {
      //   this.$refs['loginAuthDlg'] && this.$refs['loginAuthDlg'].show()
      // })
      // 24D3加了管理员角色范围配置，配置了一个角色可以管理哪些角色（包括了角色拥有的管理员信息，因此可以这里从D3开始修改为：查出哪些管理员就可以对哪些管理员配置登录认证，jira对应：TRDLP-16565）
      // 超管显示全部，系统管理员除超管外都显示
      const obj = {
        roleIds: this.roleRange,
        isSuperAdmin: this.isSuperRole,
        isSysAdmin: this.currentRoleId == 2,
        isSuperMode: this.isSuperMode
      }
      getLoginAuthSysUser(obj).then(res => {
        const userData = res.data || []
        const loginUserData = []
        userData.forEach(item => {
          const obj = {
            userId: Number.parseInt(item.id),
            username: item.name || '',
            dataId: Number.parseInt(item.id),
            label: item.name || '',
            children: []
          }
          loginUserData.push(obj)
        })
        this.sysUserTreeData[0].children = [...loginUserData]
      }).finally(() => {
        this.$refs['loginAuthDlg'] && this.$refs['loginAuthDlg'].show()
      })
    },
    rangeDateValid(rule, value, callback) {
      if (this.batchUpdateValidTimeVisible) {
        if (this.validTemp.userType === 1 && (this.rangeDate == null || this.rangeDate.length === 0)) {
          callback(new Error(this.$t('pages.validTimeEmpty')))
        }
      } else {
        if (this.temp.userType === 1 && (this.rangeDate == null || this.rangeDate.length === 0)) {
          callback(new Error(this.$t('pages.validTimeEmpty')))
        }
      }
      callback()
    },
    userTypeChange() {
      if (this.batchUpdateValidTimeVisible) {
        this.$refs['validTimeForm'].clearValidate(['rangeDate'])
      } else {
        this.$refs['dataForm'].clearValidate(['rangeDate'])
      }
    },
    afterLoad(rowData) {
      // 根据路由查询条件查询之后，清除查询
      this.$router.push({ query: {}});
    }
  }
}
</script>

<style lang='scss' scoped>
  .error-msg {
    line-height: 19px;
    padding-left: 5px;
    font-size: 13px;
    color: #F56C6C;
  }
</style>
