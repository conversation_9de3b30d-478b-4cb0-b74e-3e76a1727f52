<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="dialogTitle"
    :visible.sync="showDialog"
    width="400px"
    :show-close="showClose"
    @close="close"
    @open="open"
    @dragDialog="handleDrag"
  >
    <div class="notice">{{ notice }}</div>
    <Form
      ref="dataUpdateForm"
      :rules="updateRules"
      :model="temp"
      label-position="right"
      label-width="100px"
      style="width: 350px;"
    >
      <FormItem v-if="!resetPasswordFlag" :label="$t('pages.sysUserPasswordOld')" prop="oldPassword">
        <el-input v-model="temp.oldPassword" type="password" tabindex="1" show-password maxlength="64"/>
      </FormItem>
      <FormItem :label="$t('pages.sysUserPasswordNew')" prop="password">
        <el-popover
          placement="top-end"
          class="pop"
          width="280"
          trigger="click"
        >
          <div v-show="dataList.passwordLength">
            <i v-show="dataList.failLength" class="el-icon-close red bolder" />
            <i v-show="dataList.successLength" class="el-icon-check green bolder" />
            {{ this.$t('pages.sysUserPasswordMsg1',{passwordLength: passwordLength}) }}<br/>
          </div>
          <div v-show="dataList.passwordLevel">
            <i v-show="dataList.failLevel" class="el-icon-close red bolder" />
            <i v-show="dataList.successLevel" class="el-icon-check green bolder" />
            {{ this.$t('pages.sysUserPasswordMsg2',{passwordLevel: dataList.passwordLevel}) }}<br/>
          </div>
          <i v-show="dataList.failPassword" class="el-icon-close red bolder" />
          <template v-if="!resetPasswordFlag">
            <i v-show="dataList.successPassword" class="el-icon-check green bolder" />
            {{ this.$t('pages.sysUserPasswordMsg7') }}
          </template>
          <el-input
            slot="reference"
            v-model="temp.password"
            type="password"
            tabindex="2"
            show-password
            :maxlength="64"
            @keyup.native="strengthShow"
            @input="temp.password=temp.password.replace(/[\u4E00-\u9FA5]/g,'').trim()"
          />
        </el-popover>
      </FormItem>
      <FormItem :label="$t('form.confirmPassword')" prop="confirmPassword">
        <el-input v-model="temp.confirmPassword" tabindex="3" type="password" show-password :maxlength="64"/>
      </FormItem>
      <div v-show="resetPasswordFlag" class="adminResetPasswordTips">
        <span>{{ $t('layout.resetPasswordTips') }}</span>
      </div>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" :disabled="!submitting && !!disableTimeInfo" type="primary" @click="updateData()">{{ $t('button.confirm') + (disableTimeInfo ? '(' + disableTimeInfo + ')' : '') }}</el-button>
      <el-button v-if="showClose" @click="showDialog = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateThreeUser } from '@/api/system/organizational/threeUser'
import { getConfig } from '@/api/system/configManage/globalConfig'
import moment from 'moment'
import { validatePassword } from '@/utils/validate'
import { aesEncode, formatAesKey, base64DecodeSpe } from '@/utils/encrypt';
import { getFactoryPwd } from '@/utils/dictionary'

export default {
  name: 'UpdatePwdDlg',
  data() {
    return {
      showDialog: false,
      dialogTitle: this.$t('pages.changePassword'),
      notice: '',
      temp: {},
      showClose: true,
      tips: 'ces',
      defaultTemp: {
        id: undefined,
        mode: '1',
        oldPassword: '',
        password: ''
      },
      dataList: {
        passwordLevel: '',
        passwordLength: '',
        successLength: false,
        failLength: false,
        successLevel: false,
        failLevel: false,
        failPassword: false,
        successPassword: false
      },
      disableTimeInfo: 0,
      passwordLength: '',
      regularUpPwd: '',
      firstLoginUpPwd: true,
      validExpireTime: undefined,
      updateRules: {
        oldPassword: [{ required: true, message: this.$t('pages.validateMsg_password'), trigger: 'blur' }],
        password: [
          { required: true, trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value && this.factoryPwd[this.$store.getters.userId] && value == base64DecodeSpe(this.factoryPwd[this.$store.getters.userId])) {
                callback(new Error(this.$t('pages.validateMsg_factoryPassword')))
              } else if (value && this.factoryPwd[''] && value == base64DecodeSpe(this.factoryPwd[''])) {
                callback(new Error(this.$t('pages.validateMsg_factoryPassword')))
              } else if (value && this.$store.getters.account.toLowerCase() == 'unlockadmin' && value == base64DecodeSpe(this.factoryPwd['unlockadmin'])) {
                callback(new Error(this.$t('pages.validateMsg_factoryPassword')))
              } else if (this.dataList.failLength || this.dataList.failLevel || this.dataList.failPassword) {
                callback(new Error(this.$t('pages.validateMsg_password_NotMatch')))
              } else {
                callback()
              }
            }
          }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value !== this.temp.password) {
                callback(new Error(this.$t('pages.sysUserPasswordMsg9')))
              } else {
                callback()
              }
            }
          }
        ]
      },
      submitting: false,
      resetPasswordFlag: false,
      factoryPwd: getFactoryPwd()
    }
  },
  watch: {
    'temp.oldPassword'(newValue, oldValue) {
      if (newValue) {
        if (newValue == this.temp.password) {
          this.dataList.failPassword = true
          this.dataList.successPassword = false
        } else {
          this.dataList.failPassword = false
          this.dataList.successPassword = true
        }
      }
    }
  },
  created() {
    this.resetTemp()
    this.getConfig()
    window.clearInterval(window.upPwdDisableTimeInfo)
    window.upPwdDisableTimeInfo = setInterval(() => {
      if (this.disableTimeInfo > 0) {
        this.disableTimeInfo--
      }
    }, 1000)
  },
  methods: {
    resetTemp() {
      this.disableTimeInfo = 0
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    getConfig() {
      getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'passwordLevel') {
            this.dataList.passwordLevel = item.value
          } else if (item.key === 'passwordLength') {
            this.dataList.passwordLength = item.value
            if (item.value.split('-')[0] === item.value.split('-')[1]) {
              this.passwordLength = item.value.split('-')[0]
            } else {
              this.passwordLength = item.value
            }
          } else if (item.key === 'firstLoginUpPwd') {
            this.firstLoginUpPwd = item.value
          }
        }
      })
    },
    handleUpdate(row, notice) {
      if ((this.firstLoginUpPwd === 'true' && this.$store.getters.upPwdTime === undefined) ||
      (row.regularUpPwd == 0 ? false : this.$store.getters.upPwdTime ? (this.differentDays(this.$store.getters.upPwdTime, moment(new Date()).format('YYYY-MM-DD')) >= row.regularUpPwd) : true) ||
       row.forceValiPwdLevel || row.factoryPwdFlag) {
        this.showClose = false
      }
      this.resetPasswordFlag = row.resetPasswordFlag || false;
      if (this.resetPasswordFlag) {
        this.dialogTitle = this.$t('layout.resetPassword')
        this.showClose = false
      }
      this.resetTemp()
      this.temp.id = row.id
      this.showDialog = true
      this.notice = notice || ''
      this.$nextTick(() => {
        this.$refs['dataUpdateForm'].clearValidate()
      })
    },
    valiPasswordLength(data) {
      let flag = false
      if (data.passwordLength && data.successLength) {
        flag = true
      } else if (data.passwordLength && data.failLength) {
        flag = false
      } else {
        flag = true
      }
      return flag
    },
    updateData() {
      this.$refs['dataUpdateForm'].validate(valid => {
        if (valid && this.dataList.successLevel && this.valiPasswordLength(this.dataList) && this.dataList.successPassword) {
          this.submitting = true
          this.disableTimeInfo = 10
          const upPwdTime = new Date()
          const { oldPassword, password } = this.temp
          updateThreeUser({
            id: this.temp.id,
            password: aesEncode(JSON.stringify({ oldPassword, password }), formatAesKey('tr838408', String(this.temp.id))),
            upPwdTime: upPwdTime
          }).then(respond => {
            this.submitting = false
            this.showDialog = false
            this.$store.dispatch('user/resetUpPwdTime', upPwdTime)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.$emit('submitEnd')
          }).catch(e => {
            this.submitting = false
          })
        }
      })
    },
    strengthShow() {
      this.dataList = validatePassword(this.temp.oldPassword, this.temp.password, this.dataList)
    },
    differentDays(day1, day2) {
      if (!day1) return -1
      const date1 = day1.substr(0, 10)
      const sdate = Date.parse(date1)
      const edate = Date.parse(day2)
      // 取两个数相差的绝对值
      const timeDiff = Math.abs(sdate - edate)
      // 计算相差天数
      const cdate = Math.ceil(timeDiff / (1000 * 3600 * 24))
      return cdate
    },
    close() {
      this.dataList.successLength = false
      this.dataList.failLength = false
      this.dataList.successLevel = false
      this.dataList.failLevel = false
      this.dataList.failPassword = false
      this.dataList.successPassword = false
    },
    open() {
      this.getConfig()
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-form-item{
    .el-form-item__label{
      color: #ccc;
      line-height: 30px;
    }
    .el-form-item__content{
      line-height: 30px;
      .el-input__inner{
        height: 30px;
        line-height: 30px;
      }
    }
  }
  .weak,.medium,.strong {
  display: inline-block;
  height: 5px;
  width: 60px;
  margin-left: 3px;
  margin-top: 2px;
  font-size: 2px;
  text-align: center;
  }
  .btn {
    border: none;
  }
  .notice {
    text-align: center;
    margin: 0 auto 8px;
    padding-left: 20px;
    color: #0c60a5;
  }
  .adminResetPasswordTips {
    color: #0c60a5;
    margin-left: 40px
  }
</style>
