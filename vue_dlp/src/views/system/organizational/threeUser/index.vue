<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        {{ $t('pages.manageMode') }}：{{ modeName }}
        <el-button v-if="showChangeModeBtn" type="primary" size="mini" style="margin-left:10px;" @click="modeChange">
          {{ $t('pages.changeMode') }}
        </el-button>
        <el-button v-if="!isSuperMode && showChangeModeBtn" type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addAdministrator') }}
        </el-button>
        <el-button v-if="!isSuperMode && showChangeModeBtn && deleteAble" icon="el-icon-delete" size="mini" :disabled="!btnEnable" @click="handleDelete">
          {{ $t('button.delAdministrator') }}
        </el-button>
        <el-button v-if="!isSuperMode && showChangeModeBtn" icon="el-icon-setting" size="mini" @click="handleSetting">
          {{ $t('button.highConfig') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('components.searchPlaceholder_3')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="dataList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" @selectionChangeEnd="selectionChangeEnd"/>
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showCreateDialog"
      width="600px"
      @open="open"
      @dragDialog="handleDrag"
    >
      <Form ref="roleForm" :rules="rules" :model="tempRole" label-position="right" label-width="100px" style="width: 500px; margin-left:20px;">
        <input type="text" class="autocomplete">
        <input type="password" class="autocomplete">
        <FormItem v-show="dialogStatus==='create' || tempRole.id !== 1" :label="$t('pages.roleType')" prop="type">
          <el-select v-model="tempRole.type" :disabled="dialogStatus !== 'create'" :placeholder="$t('text.select')">
            <el-option v-for="item in typeOptions" :key="item.type" :label="item.name" :value="item.type"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.sysUserAccount')" prop="account">
          <el-input v-model="tempRole.account" :disabled="isEditRootRole"/>
        </FormItem>
        <FormItem :label="$t('pages.sysUserName')" prop="name">
          <el-input v-model="tempRole.name" :disabled="isEditRootRole"/>
        </FormItem>
        <FormItem :label="$t('form.password')" :tooltip-content="$t('pages.sysUserPasswordMsg11')" prop="password">
          <el-popover
            placement="top-end"
            class="pop"
            width="280"
            trigger="click"
          >
            <div v-show="valiDataList.passwordLength">
              <i v-show="valiDataList.failLength" class="el-icon-close red bolder" />
              <i v-show="valiDataList.successLength" class="el-icon-check green bolder" />
              {{ this.$t('pages.sysUserPasswordMsg1',{passwordLength: passwordLength}) }}<br/>
            </div>
            <div v-show="valiDataList.passwordLevel">
              <i v-show="valiDataList.failLevel" class="el-icon-close red bolder" />
              <i v-show="valiDataList.successLevel" class="el-icon-check green bolder" />
              {{ this.$t('pages.sysUserPasswordMsg2',{passwordLevel: valiDataList.passwordLevel}) }}<br/>
            </div>
            <el-input slot="reference" v-model="tempRole.password" :maxlength="64" type="password" @focus.capture="strengthShow" @keyup.native="strengthShow" @input="tempRole.password=tempRole.password.replace(/[\u4E00-\u9FA5]/g,'').trim()" >
              <!-- <el-button slot="suffix" style="border: none" icon="el-icon-info"></el-button> -->
            </el-input>
          </el-popover>
        </FormItem>
        <FormItem :label="$t('form.confirmPassword')" prop="confirmPassword">
          <el-input v-model="tempRole.confirmPassword" type="password" />
        </FormItem>
        <FormItem :label="$t('form.email')" prop="email">
          <el-input v-model="tempRole.email" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('form.phone')" prop="phone">
          <el-input v-model="tempRole.phone" :maxlength="15" suffix-icon="el-icon-phone"/>
        </FormItem>
        <FormItem :label="$t('form.status')">
          <el-select v-model="tempRole.active" :placeholder="$t('text.select')">
            <el-option v-for="(value, key) in activeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="showCreateDialog = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <permission-dlg ref="permissionDlg" :role-data="treeData" is-three :disable-role-id="[2, 3, 4]"/>
    <config-sys-user ref="configDlg"/>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  getThreeUserPage, createThreeUser, updateThreeUser, deleteThreeUser, isSamePassword,
  updateMode, getThreeUserType, getThreeUserByName, getThreeUserByAccount, listThreeUserTree
} from '@/api/system/organizational/threeUser'
import { unlockUser } from '@/api/system/organizational/sysUser'
import { findNode } from '@/utils/tree'
import { getIsThreeUserDeleteAble } from '@/api/property'
import PermissionDlg from '../permissions/editDlg'
import ConfigSysUser from '../sysUser/configDlg'
import { getConfig } from '@/api/system/configManage/globalConfig'
import { validatePassword } from '@/utils/validate'

export default {
  name: 'ThreeUser',
  components: { PermissionDlg, ConfigSysUser },
  data() {
    return {
      colModel: [
        { prop: 'account', label: 'sysUserAccount', fixed: true, width: '150', sort: true, iconFormatter: this.activeFormatter },
        { prop: 'name', label: 'sysUserName', width: '150', sort: true },
        { prop: 'roleType', label: 'roleType', width: '100', formatter: this.roleTypeFormatter },
        { prop: 'email', label: 'email', width: '100' },
        { prop: 'phone', label: 'phone', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '160', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'assignPermissions', isShow: (row) => row.id > 4, click: this.handlePermission, disabledFormatter: () => { return !this.showChangeModeBtn } },
            { label: 'unlock', click: this.handleUnlock, isShow: (data) => { return data.isLock } }
            // { label: '停用', click: this.updateActive, formatter: (data) => { return data.active ? '停用' : '启用' } }
          ]
        }
      ],
      query: { // 查询条件
        roleId: undefined,
        searchInfo: ''
      },
      showTree: false,
      showCreateDialog: false,
      showChangeModeBtn: false,
      btnEnable: false,
      deleteAble: false,
      valiDataList: {
        passwordLevel: '',
        passwordLength: '',
        successLength: false,
        failLength: false,
        successLevel: false,
        failLevel: false,
        failPassword: false,
        successPassword: false
      },
      passwordLength: '',
      activeOptions: { 1: this.$t('text.enable'), 0: this.$t('text.disable') },
      typeOptions: [],
      tempRole: {},
      defaultTempRole: {
        id: null,
        parentId: null,
        name: '',
        account: '',
        email: '',
        phone: '',
        type: '',
        active: '1',
        password: '',
        confirmPassword: '',
        remark: '',
        upPwdTime: null
      },
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.updateAdministrator'),
        create: this.$t('pages.addAdministrator')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterAdminName'), trigger: 'blur' },
          { trigger: 'blur', validator: this.nameValidator }
        ],
        account: [
          { required: true, message: this.$t('pages.validateMsg_enterAdminAccount'), trigger: 'blur' },
          { trigger: 'blur', validator: this.accountValidator }
        ],
        email: [{ type: 'email', message: this.$t('pages.validateMsg_email'), trigger: ['blur', 'change'] }],
        phone: [{ trigger: 'blur', validator: this.phoneValidator }],
        type: [{ required: true, message: this.$t('pages.validateMsg_roleType'), trigger: 'blur' }],
        password: [{ required: true, message: this.$t('pages.validateMsg_password_NotMatch'), trigger: 'blur', validator: this.passwordValidator }],
        confirmPassword: [{ required: true, message: this.$t('pages.threeUserConfirm', { content: this.$t('pages.pwdInfo') }), trigger: 'blur', validator: this.confirmPasswordValidator }]
        /* confirmPassword: [
          { required: true, trigger: 'blur',
            validator: (rule, value, callback) => {
              if (rule.required && !value && !this.tempRole.password) {
                callback(new Error(this.$t('pages.validateMsg_password')))
              } else if (value !== this.tempRole.password) {
                callback(new Error(this.$t('pages.validateMsg_diffPassword')))
              } else {
                callback()
              }
            }
          }
        ] */
      },
      submitting: false,
      treeData: []
    }
  },
  computed: {
    ...mapGetters([
      'isSuperMode',
      'isSuperRole',
      'isSuperThreeUser',
      'userId'
    ]),
    gridTable() {
      return this.$refs['dataList']
    },
    isEditRootRole() {
      return this.tempRole.roleIds && this.tempRole.roleIds[0] <= 4
    },
    modeName() {
      return this.isSuperMode ? this.$t('pages.superMode') : this.$t('pages.threeMode')
    }
  },
  created() {
    this.resetTemp()
    this.loadType()
    this.enableChangeMode()
    this.loadRoleTree()
    this.getConfig()
    this.loadDeleteAble()
  },
  activated() {
    this.loadDeleteAble()
  },
  methods: {
    loadType: function() {
      getThreeUserType().then(respond => {
        this.typeOptions = respond.data
      })
    },
    loadRoleTree() {
      listThreeUserTree().then(resp => {
        this.treeData = resp.data
      })
    },
    loadDeleteAble() {
      getIsThreeUserDeleteAble().then(resp => {
        this.deleteAble = resp.data
      })
    },
    enableChangeMode() {
      this.showChangeModeBtn = false
      if (this.isSuperMode) {
        this.showChangeModeBtn = this.isSuperRole
      } else {
        this.showChangeModeBtn = this.isSuperThreeUser
      }
    },
    resetTemp() {
      this.tempRole = Object.assign({}, this.defaultTempRole)
    },
    selectionChangeEnd(data) {
      this.btnEnable = data.length > 0
    },
    selectable(row, index) {
      return row.id > 4
    },
    activeFormatter: function(row, data) {
      const active = { 1: 'active', 0: 'inactive' }[row.active]
      const isLock = { true: 'Lock', false: 'Unlock' }[row.isLock]
      const title = this.$t('pages.sysUserActiveOpt', {
        isActive: active === 'active' ? this.$t('text.enable') : this.$t('text.disable'),
        isLock: isLock === 'Lock' ? this.$t('pages.lock') : this.$t('pages.unlock')
      });
      const key = active + isLock
      return [{ class: key, title: title }]
    },
    roleTypeFormatter: function(row, data) {
      for (let i = 0; i < this.typeOptions.length; i++) {
        const op = this.typeOptions[i]
        if (op.id === data) return op.name
      }
      return ''
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getThreeUserPage(searchQuery)
    },
    modeChange() {
      // 切换后的模式名称
      const modeName = !this.isSuperMode ? this.$t('pages.superMode') : this.$t('pages.threeMode')
      this.$prompt('', this.$t('pages.changeTo', { mode: modeName }), {
        confirmButtonText: this.$t('button.confirm'),
        cancelButtonText: this.$t('button.cancel'),
        cancelButtonClass: 'btn-custom-cancel',
        closeOnClickModal: false,
        inputType: 'password',
        inputPlaceholder: this.$t('pages.validateMsg_password')
      }).then(({ value }) => {
        // 切换后的模式值
        const mode = !this.isSuperMode ? '1' : '0'
        updateMode({
          mode,
          id: this.userId,
          password: value,
          encryptProps: ['password']
        }).then(async(respond) => {
          let msg = this.$t('text.updateSuccess')
          if (!respond.data) {
            msg += this.$t('pages.validateMsg_change')
          }
          this.$notify({
            title: this.$t('text.success'),
            message: msg,
            type: 'success',
            duration: 2000
          })
          await this.$store.dispatch('user/logout')
          this.$store.dispatch('commonData/clearWsNoticeInfo')
          this.$store.dispatch('permission/clearCommonRoute')
          this.$store.dispatch('tagsView/delAllViews')
          this.$router.push('/login')
        })
      }).catch(() => {})
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleSetting() {
      this.$refs['configDlg'].show()
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    getConfig() {
      getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'passwordLevel') {
            this.valiDataList.passwordLevel = item.value
          } else if (item.key === 'passwordLength') {
            this.valiDataList.passwordLength = item.value
            if (item.value.split('-')[0] === item.value.split('-')[1]) {
              this.passwordLength = item.value.split('-')[0]
            } else {
              this.passwordLength = item.value
            }
          }
        }
      })
      this.$forceUpdate()
    },
    handlePermission(row) {
      const node = findNode(this.treeData, row.roleIds[0], 'dataId')
      this.$refs['permissionDlg'].show({
        id: Number.parseInt(node.dataId),
        parentId: Number.parseInt(node.parentId.substring(1)),
        name: node.label
      })
    },
    handleUnlock(row) {
      unlockUser({ id: row.id }).then(respond => {
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.unlockSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteThreeUser({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleCreate() {
      this.resetTemp()
      this.rules.password[0].required = true
      this.rules.confirmPassword[0].required = true
      this.dialogStatus = 'create'
      this.showCreateDialog = true
      this.$nextTick(() => {
        this.$refs['roleForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.dialogStatus = 'update'
      this.showCreateDialog = true
      Object.assign(this.tempRole, row)
      this.tempRole.type = row.roleType
      this.tempRole.active += ''
      if (this.tempRole.hasPassword == 1) {
        this.tempRole.password = '      '
        this.tempRole.confirmPassword = '      '
        this.rules.password[0].required = false
        this.rules.confirmPassword[0].required = false
      }
      this.$nextTick(() => {
        this.$refs['roleForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['roleForm'].validate(valid => {
        if (valid) {
          createThreeUser(this.tempRole).then(respond => {
            this.submitting = false
            this.showCreateDialog = false
            this.gridTable.execRowDataApi(this.query)
            this.loadRoleTree()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    async formatData() {
      let flag = false
      if (this.tempRole.password.trim() === '') {
        flag = false
      } else {
        await isSamePassword({
          id: this.tempRole.id,
          password: this.tempRole.password.trim(),
          encryptProps: ['password']
        }).then(response => {
          if (!response.data) {
            flag = true
          }
        })
      }
      return flag
    },
    updateData() {
      this.$refs['roleForm'].validate(async(valid) => {
        if (valid) {
          this.submitting = true
          await this.formatData().then(res => {
            if (res) {
              this.tempRole.upPwdTime = new Date()
            }
          })
          updateThreeUser(this.tempRole).then(respond => {
            this.submitting = false
            this.showCreateDialog = false
            this.$store.dispatch('user/resetUpPwdTime', this.tempRole.upPwdTime)
            this.gridTable.execRowDataApi(this.query)
            this.loadRoleTree()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    strengthShow() {
      this.valiDataList = validatePassword(null, this.tempRole.password, this.valiDataList)
    },
    updateActive(row) {
      updateThreeUser({
        id: row.id,
        active: (row.active + 1) % 2
      }).then(respond => {
        row.active = respond.data.active
        this.gridTable.updateRowData(row)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    open() {
      this.getConfig()
    },
    passwordValidator(rule, value, callback) {
      let error
      if (!value.trim()) {
        this.valiDataList.failLength = false
        this.valiDataList.failLevel = false
        this.rules.confirmPassword[0].required = false
        if (rule.required) {
          error = new Error(this.$t('pages.validateMsg_password'))
        }
      } else {
        if (this.valiDataList.failLength || this.valiDataList.failLevel) {
          error = new Error(this.$t('pages.validateMsg_password_NotMatch'))
        }
        this.rules.confirmPassword[0].required = true
      }
      callback(error)
    },
    confirmPasswordValidator(rule, value, callback) {
      if (value !== this.tempRole.password) {
        callback(new Error(this.$t('pages.validateMsg_diffPassword')))
      } else {
        callback()
      }
    },
    accountValidator(rule, value, callback) {
      getThreeUserByAccount({ account: value }).then(respond => {
        const user = respond.data
        if (user && user.id !== this.tempRole.id) {
          callback(new Error(this.$t('pages.validateMsg_sameAccount')))
        } else {
          callback()
        }
      })
    },
    nameValidator(rule, value, callback) {
      getThreeUserByName({ name: value }).then(respond => {
        const role = respond.data
        const curRoleId = !this.tempRole.roleIds ? 0 : this.tempRole.roleIds[0]
        if (role && role.id !== curRoleId) {
          callback(new Error(this.$t('pages.validateMsg_sameRole')))
        } else {
          callback()
        }
      })
    },
    phoneValidator(rule, value, callback) {
      if (value && !(/^1[3456789]\d{9}$/.test(value))) {
        callback(new Error(this.$t('pages.validateMsg_phone')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-form-item{
    .el-form-item__label{
      color: #ccc;
      line-height: 30px;
    }
    .el-form-item__content{
      line-height: 30px;
      .el-input__inner{
        height: 30px;
        line-height: 30px;
      }
    }
  }
</style>
