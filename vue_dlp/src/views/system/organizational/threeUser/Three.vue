<template>
  <div class="super-container">
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
        {{ this.$t('pages.addRole') }}
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      stripe
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="45" />
      <el-table-column :label="$t('table.role')" align="center" min-width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.role }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.account')" prop="id" sortable="custom" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('pages.threeUserPermissionsDetail')" min-width="220px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.permission }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('pages.roleStatus')" min-width="220px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.status | statusFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.remark')" min-width="220px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.remark }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('table.operate')" width="200px">
        <template slot-scope="scope">
          <el-button type="text" :disabled="!editable" @click="handleChangePassword(scope.row)">{{ this.$t('pages.changePassword') }}</el-button>
          <el-button type="text" :disabled="!editable">{{ this.$t('text.stop') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addRole')"
      :visible.sync="createVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="roleForm" :rules="rules" :model="tempRole" label-position="right" label-width="100px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.roleName')" prop="role">
          <el-input v-model="tempRole.role" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createVisible = false">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="createVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.changePassword')"
      :visible.sync="passwordVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="passwordForm" :rules="rules" :model="tempPwd" label-position="right" label-width="100px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.sysUserPasswordOld')" prop="oldPassword">
          <el-input v-model="tempPwd.oldPassword" type="password" />
        </FormItem>
        <FormItem :label="$t('pages.sysUserPasswordNew')" prop="password">
          <el-input v-model="tempPwd.password" type="password" />
        </FormItem>
        <FormItem :label="$t('form.confirmPassword')" prop="confirmPassword">
          <el-input v-model="tempPwd.confirmPassword" type="password" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="passwordVisible = false">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="passwordVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { fetchList } from '@/api/system/organizational/threeMember'

export default {
  name: 'User',
  filters: {
    statusFilter(status) {
      const statusMap = {
        on: this.$t('text.enable'),
        off: this.$t('text.disable')
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: false,
      listQuery: { // 查询条件
        page: 1,
        limit: 20,
        sort: '+id'
      },
      editable: true,
      tempRole: { // 表单字段
        id: undefined,
        role: '',
        account: '',
        permission: '',
        status: '',
        remark: ''
      },
      tempPwd: { // 表单字段
        oldPassword: '',
        password: '',
        confirmPassword: ''
      },
      createVisible: false,
      passwordVisible: false,
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      multipleSelection: [], // 选中行数组集合
      submitting: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
        this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.createVisible = true
      this.$nextTick(() => {
        console.log(this.$refs['roleForm'])
        this.$refs['roleForm'].clearValidate()
      })
    },
    handleChangePassword() {
      this.passwordVisible = true
    },
    createData() {},
    handleSelectionChange(val) {
      this.editable = val.length === 1
      this.deleteable = val.length > 0
      this.multipleSelection = val
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.tempRole = {
        id: undefined,
        name: '',
        department: '',
        mail: '',
        agent: 0
      }
      this.tempPwd = {
        oldPassword: '',
        password: '',
        confirmPassword: ''
      }
    }
  }
}
</script>
