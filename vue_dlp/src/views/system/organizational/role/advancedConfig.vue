<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="clearFilterAndChecked"
    >
      <Form ref="dataForm" v-loading="submitting" :rules="rules" :model="temp" label-position="right" label-width="117px" style="margin-left:20px;margin-right: 20px;">
        <el-row>
          <el-col :span="17">
            <FormItem :label="$t('pages.sysUserRole')" prop="roleIds">
              <tree-select
                ref="treeSelect"
                :local-search="true"
                multiple
                is-filter
                collapse-tags
                clearable
                :check-strictly="cascadeEnable === '0'"
                :data="roleData"
                :checked-keys="checkedKeys"
                :width="330"
                @change="handleChange"
              />
            </FormItem>
          </el-col>
          <el-col :span="7" style="margin-top: 5px;padding-left: 5px;">
            <div>
              <el-checkbox v-model="cascadeEnable" true-label="1" false-label="0" @change="cascadeEnableChange">{{ $t('pages.permission_Msg35') }}</el-checkbox>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  <i18n path="pages.permission_Msg36">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="13">
            <FormItem :label="$t('pages.permission_Msg15')" prop="operatorType" label-width="100px">
              <div style="display: flex;align-items: center;">
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.permission_Msg30">
                      <br slot="br"/>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-select v-model="temp.operatorType" style="margin-left: 3px;">
                  <el-option :key="1" :value="1" :label="$t('pages.permission_Msg16')"/>
                  <el-option :key="2" :value="2" :label="$t('pages.permission_Msg17')"/>
                </el-select>
              </div>
            </FormItem>
          </el-col>
        </el-row>
        <div>
          <el-card style="height: 380px;" body-style="padding: 5px;">
            <div slot="header" class="clearfix">
              <span>{{ $t('components.optionalPermissions') }}</span>
              <el-button type="text" @click="handleCheckAllChange(true)">{{ $t('button.selectAll') }}</el-button>
              <el-button type="text" @click="handleCheckAllChange(false)">{{ $t('button.cancelSelectAll') }}</el-button>
              <FormItem :label="$t('pages.permission_Msg18')" prop="showType" label-width="100px" style="width: 230px;float:right;">
                <div style="display: flex;align-items: center;">
                  <el-select v-model="temp.showType" style="margin-left: 3px;" @change="showTypeChange">
                    <el-option :key="1" :value="1" :label="$t('pages.permission_Msg25')"/>
                    <el-option :key="2" :value="2" :label="$t('pages.permission_Msg26')"/>
                    <el-option :key="3" :value="3" :label="$t('pages.permission_Msg27')"/>
                  </el-select>
                  <el-tooltip effect="dark" placement="bottom-end">
                    <div slot="content">
                      {{ $t('pages.permission_Msg28') }}<br><br>
                      {{ $t('pages.permission_Msg29') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </div>
              </FormItem>
            </div>
            <tree-menu
              v-show="temp.showType === 1"
              ref="menuBtnTree"
              :height="330"
              :multiple="true"
              :data="menuPermissionTreeData"
              node-key="id"
              @check="menuBtnPermissionCheck"
            />
            <tree-menu
              v-show="temp.showType === 2"
              ref="btnMenuTree"
              :height="330"
              :multiple="true"
              :data="btnAndMenuTreeData"
              node-key="id"
              @check-change="btnMenuPermissionChange"
            />
            <div v-show="temp.showType === 3" style="height: 330px;overflow-y: auto;">
              <el-row style="margin-top: 5px;">
                <el-col v-for="(opt, index) in permissionOption" :key="index" :span="12">
                  <el-checkbox v-model="opt.value" :true-label="1" :false-label="0">{{ opt.label }}</el-checkbox>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleConfig()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="allocationTitle"
      :visible.sync="updateAfterVisible"
      width="750px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="updateReasonTable"
        :height="360"
        :row-datas="tableData"
        :multi-select="false"
        :show-pager="false"
        :sortable="false"
        :col-model="colModel"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <div v-html="codeNameDetailFormatter(props.detail)"></div>
            <!-- <span style="padding: 5px 10px; display: inline-block;">{{ codeNameDetailFormatter(props.detail) }}</span> -->
          </div>
        </template>
      </grid-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="updateAfterVisible = false">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getBtnAndMenuPermission, getBtnPermission, listMenuTree, getPermissionBtnMap, updateRolesPermission } from '@/api/system/organizational/permission'
import { getCurrentRole } from '@/api/system/organizational/sysUser'
import { asyncRoutes } from '@/router'
import { getIsViewMyselfLogAble } from '@/api/property'
import funcMenuCode from '@/router/modules/funcPermission'
import { isRemoveRouteByDiskScan, isRemoveRouteByLog } from '@/store/modules/permission'
import { generateTitle, setTheFirstLetterToUppercase } from '@/utils/i18n'
export default {
  name: 'AdvancedConfig',
  props: {
    // 角色树数据
    originalRoleData: {
      type: Array,
      default() {
        return []
      }
    },
    height: {
      type: String,
      default() {
        return '370'
      }
    },
    bodyStyle: {
      type: Object,
      default() {
        return { padding: '5px', height: this.height }
      }
    },
    isThree: {
      type: Boolean,
      default() {
        return this.isSuperThreeUser
      }
    }
  },
  data() {
    return {
      title: this.$t('pages.batchUpdatePermission'),
      allocationTitle: this.$t('pages.permission_Msg31'),
      dialogFormVisible: false,
      updateAfterVisible: false,
      submitting: false,
      permissionLoading: false,
      activeTabName: 'menu',
      menuTabLabel: this.$t('pages.managementPermissions'),
      checkedKeys: [],
      temp: {},
      defaultTemp: {
        operatorType: 1,              //  1：指定角色增加权限，2：指定角色去除权限
        showType: 1,                  //  1 菜单-按钮  2 按钮 - 菜单 3 只显示按钮
        appendType: 0,                // 父子角色权限映射方式(operatorType = 1时有效) 0 添加  1 忽略
        roleIds: []
      },
      permissionOption: [             // showType = 3 时展示的按钮权限列表

      ],
      rules: {
      },
      funcPermissionMap: {},           // 各个菜单的子功能权限 map
      permissionMap: {},
      btnPermission: [],               // 存储按钮数据
      allMenuPermissionTreeData: [],          // 菜单权限树
      allMenuPermissionTreeDataMap: {},       // 菜单权限树 的每一个节点 Map，key: 菜单编码，value：节点数据data
      menuPermissionTreeData: [],
      btnAndMenuTreeData: [
      ],
      menuCodeTitleData: {},           // 保存菜单权限的多语言key，不包含菜单下的按钮权限 key为菜单编码，value为菜单名称
      roleNames: [],                   // 选择的角色名称
      menuTreeData: [
      ],
      tableData: [],
      colModel: [
        { prop: 'roleName', label: 'roleName', width: '150' },
        { prop: 'reason', label: 'failReason', width: '210' },
        { prop: 'codeName', label: 'failPermission', width: '250', ellipsis: false, type: 'popover', originData: true, formatter: this.codeNameFormatter }
      ],
      cascadeEnable: '0'
    };
  },
  computed: {
    ...mapGetters([
      'isSuperRole',
      'isSuperThreeUser',
      'isSuperMode',
      'userMenuCodes',
      'menuCodeFullTitleMap',
      'currentRoleId'
    ]),
    roleData() {
      const roleData = this.originalRoleData
      if (roleData.length == 0) return []
      // 使用深拷贝，不然会影响到originalRoleData
      const children = JSON.parse(JSON.stringify(roleData[0].children))
      children.forEach(node => {
        const dataId = Number(node.dataId)
        // 三员管理员 和 当前角色 禁用多选框
        if (dataId === 2 || dataId === 3 || dataId === 4 || dataId === this.currentRoleId) {
          node.disabled = true
        }
      })
      return children
    },
    // 保存菜单code对应的名称 ['code:name', ...]
    codeNameMap() {
      return Object.entries(this.menuCodeFullTitleMap).map(([key, value]) => {
        const title = [...value].pop()
        return `${key}:${title}`
      })
    }
  },
  created() {
    this.formatMenuPersmission()
  },
  methods: {
    generateTitle,
    cascadeEnableChange(val) {
      this.temp.roleIds = []
      this.clearRoleCheck()
      this.handleCheckRole()
    },
    codeNameFormatter: function(row) {
      return row.codeName
    },
    // 鼠标悬停时的权限信息
    codeNameDetailFormatter: function(row) {
      const codeName = row.codeName.replace(/,\s*/g, '<br/>');
      return codeName
    },
    async formatMenuPersmission() {
      const currentRole = await getCurrentRole()
      const response = await listMenuTree(currentRole.data.id)
      const currentPermissionSet = new Set(response.data)
      getBtnAndMenuPermission().then(resp => {
        this.filterBtnAndMenuPermission(resp.data, currentPermissionSet)
      })
      getBtnPermission().then(resp => {
        this.filterBtnOptionPermission(resp.data, currentPermissionSet)
      })
      getPermissionBtnMap().then(resp => {
        this.funcPermissionMap = resp.data
        // 获取菜单树数据
        this.allMenuPermissionTreeData = this.initAllMenuPermissionTreeData().nodeData
        this.listRolePermissionMenuId(currentRole.data.id, currentRole.data.parentId)
      })
    },
    filterBtnAndMenuPermission(btnAndMenuPermissionMap, currentPermissionSet) {
      // 根据当前角色的权限范围来过滤数据
      const tempData = Object.entries(btnAndMenuPermissionMap).map(([key, value]) => {
        // 同时拥有 menuCode 和 btnCode 权限
        value = value.filter(item => currentPermissionSet.has(item.menuCode) && currentPermissionSet.has(item.btnCode))
        return value.length > 0 ? { key, value } : null
      }).filter(item => item)
      // 构建树数据
      const tempTreeData = []
      tempData.forEach((item, index) => {
        const { key, value } = item
        const treeObj = {
          id: index,
          dataId: index,
          label: this.$t(key),
          parentId: 0,
          type: 'G',
          children: [],
          disabled: false
        }
        value.forEach((data, childIndex) => {
          const { menuCode, btnCode } = data
          const label = this.getRoutMenuPermission(menuCode)
          if (label) {
            const childrenObj = {
              id: `${index}.${childIndex}`,
              dataId: btnCode,
              label: label,
              parentId: index,
              type: 'N',
              menuCode,
              btnCode,
              disabled: false
            }
            treeObj.children.push(childrenObj)
          }
        })
        tempTreeData.push(treeObj)
      })
      this.btnAndMenuTreeData = [...tempTreeData]
      // 判断是否是超管或根三员,不是需要移除企业密钥管理，因为企业密钥管理只有超管或根三员可以进行分配
      const isRootRole = this.isSuperRole || this.isSuperThreeUser
      if (!isRootRole) {
        const filterTreeData = this.filterTree(this.btnAndMenuTreeData)
        this.btnAndMenuTreeData = [...filterTreeData]
      }
      this.btnAndMenuTreeData.sort((a, b) => {
        return a.label.localeCompare(b.label)
      })
    },
    
    filterTree(tree) {
      return tree.map(node => this.processNode(node)).filter(Boolean);
    },
    processNode(node) {
      if (node.type === 'N') {
        // 如果menuCode是E31则移除
        // 企业密钥管理(E31)只有超管或根三员可以进行分配
        if (node.menuCode === 'E31') return null;
        const filteredChildren = node.children 
          ? node.children.map(child => this.processNode(child)).filter(Boolean)
          : [];
        return { ...node, children: filteredChildren };
      }
      if (node.type === 'G') {
        const filteredChildren = node.children 
          ? node.children.map(child => this.processNode(child)).filter(Boolean)
          : [];
        return filteredChildren.length ? { ...node, children: filteredChildren } : null;
      }
      return node;
    },

    filterBtnOptionPermission(btnPermissionMap, currentPermissionSet) {
      // 根据当前角色的权限范围来过滤数据
      const tempData = Object.entries(btnPermissionMap).map(([key, value]) => {
        // 同时拥有 menuCode 和 btnCode 权限
        value = value.filter(item => currentPermissionSet.has(item.menuCode) && currentPermissionSet.has(item.btnCode))
        return value.length > 0 ? { key, value } : null
      }).filter(item => item)
      this.btnPermission = [...tempData]
      this.permissionOption = tempData.map(data => {
        return {
          label: this.$t(data.key),
          value: 0,
          code: data.key
        }
      })
      this.permissionOption.sort((a, b) => {
        return a.label.localeCompare(b.label)
      })
    },
    getRoutMenuPermission(data) {
      const titles = this.menuCodeFullTitleMap[data] || []
      return [...titles].pop() || ''
    },
    initAllMenuPermissionTreeData(routes, parentId) {
      parentId = parentId || ''
      routes = routes || asyncRoutes
      
      // 用户菜单权限
      const curUserMenuCodes = this.userMenuCodes
      // 构造菜单树数据（nodeData 菜单树数据，disableSize 同级禁用节点的数量）
      const menuNodeDataInfo = { nodeData: [], disableSize: 0 }
      routes.forEach((route, index) => {
        // 隐藏，禁用，常用菜单的路由
        const disableRoute = route.hidden || route.disabled || route.path == 'common_routes'
        if (disableRoute) return
        // 需要过滤的日志模块
        const logRoute = isRemoveRouteByLog(route, curUserMenuCodes)
        if (logRoute) return
        // 需要过滤的全盘扫描模块
        const diskScanRoute = isRemoveRouteByDiskScan(route, curUserMenuCodes)
        if (diskScanRoute) return
        // 构造节点
        const node = {
          id: `${parentId}.${index}`,
          dataId: route.code,
          label: route.meta ? this.generateTitle(route.meta.title) : '',
          parentId: parentId,
          type: 0,
          disabled: !this.isThree && route.code && curUserMenuCodes.indexOf(route.code) < 0
        }
        if (route.code) {
          this.menuCodeTitleData[route.code] = route.meta.title
        }
        if ('SS9' === route.code) {
          // 征兆报表在管理员权限这边只需要分配二级菜单的权限，三级菜单是根据报表配置随机展示的
        } else if (route.children && route.children.length > 0) {
          // 构造子节点
          const childInfo = this.initAllMenuPermissionTreeData(route.children, node.id)
          node.children = childInfo.nodeData
          if (node.children.length === childInfo.disableSize) {
            node.disabled = true
            menuNodeDataInfo.disableSize++
          }
          delete node.dataId
        } else if (node.disabled) {
          menuNodeDataInfo.disableSize++
        }
        if (node.dataId) this.allMenuPermissionTreeDataMap[node.dataId] = node
        menuNodeDataInfo.nodeData.push(node)
        // 功能节点
        const funcsNodes = this.funcPermissionMap[route.code]
        if (funcsNodes && funcsNodes.length > 0) {
          // 菜单功能分为 基础功能（menuCode继承父节点code） + 其他功能
          const basicNode = { menuCode: route.code, remark: this.$t('pages.basicFunctions') };
          node.children = node.children || [];
          // 功能节点处理
          funcsNodes.concat([basicNode]).forEach((funcRoute, index) => {
            // todo 多语言处理：前端->后台。
            const funcExtInfo = Object.assign({}, funcMenuCode[funcRoute.menuCode])
            const funcNode = {
              id: `${node.id}.${index}`,
              dataId: funcRoute.menuCode,
              relDataId: !funcExtInfo.rel ? route.code : `${funcExtInfo.rel},${route.code}`,
              label: funcRoute.remark,
              parentId: node.id,
              type: 0
            }
            node.children.push(funcNode)
            this.allMenuPermissionTreeDataMap[funcNode.dataId] = funcNode
          })
        }
      })
      return menuNodeDataInfo
    },
    async listRolePermissionMenuId(roleId, parentRoleId) {
      const myselfLogAble = await getIsViewMyselfLogAble()
      // 获取父级角色的菜单权限，用于展示可查看节点
      this.invokeMethod(listMenuTree, parentRoleId, 'menu', (respond) => {
        // 拷贝 所有菜单权限树数据
        this.menuPermissionTreeData = JSON.parse(JSON.stringify(this.allMenuPermissionTreeData))
        // 是否 超管或三员管理员
        const isRootRoleId = parentRoleId == 0 && [1, 2, 3, 4].indexOf(roleId) > -1
        // 过滤掉没有权限的数据
        this.formatMenuTreeData(this.menuPermissionTreeData, isRootRoleId ? ['*'] : respond.data, myselfLogAble.data)
        if (this.isSuperMode) {
          // 只有超管模式才过滤，三员模式下，只有系统管理员才有管理员角色页面，如系统管理员可以对安全管理员分配安全管理员拥有的菜单权限，不用考虑自身
          this.filterMenuPermission()
        }
      })
    },
    filterMenuPermission() {
      // 将权限树禁止选择的权限过滤掉
      var data = this.removeDisabledNodes([...this.menuPermissionTreeData])
      // 过滤远程维护这种非叶子节点但是却没有下级节点的节点
      data = this.filterNoLeafNodeAndNoChildren(data)
      this.menuPermissionTreeData = [...data]
      // 过滤掉子菜单为空的顶级菜单
      var tempPermission = []
      this.menuPermissionTreeData.forEach(item => {
        if (item.parentId == '' && item.children) {
          tempPermission.push(item)
        }
      })
      // 过滤不在用户菜单权限里的叶子节点（即导入、导出等按钮菜单）
      this.menuPermissionTreeData = this.filterLeafNodeUserMenuCode([...tempPermission], new Set(this.userMenuCodes));
    },
    removeDisabledNodes(treeData) {
      return treeData.map(node => {
        // 如果当前节点被禁用，则不返回该节点
        if (node.disabled === true) {
          return null;
        }
        let children 
        if (node.children) {
          // 递归处理子节点
          children = this.removeDisabledNodes(node.children)
          if (children.length === 0) {
            children = undefined
          }
        }
        // 返回更新后的节点，包括更新后的子节点
        return { ...node, children };
      }).filter(node => node);
    },
    filterNoLeafNodeAndNoChildren(treeData) {
      return treeData.map(node => {
        const hasChildren = node.children && node.children.length > 0
        if (!hasChildren && !('disabled' in node) && !('dataId' in node)) {
          return null;
        }

        if (hasChildren) {
          node.children = this.filterNoLeafNodeAndNoChildren(node.children)
          // 如果过滤后子节点为空数组，则也移除这个父节点
          if (node.children.length === 0) {
            return null;
          }
        }

        // 返回处理后的节点（如果节点被移除，则返回undefined）
        return node;
      }).filter(node => node);
    },
    filterLeafNodeUserMenuCode(treeData, idsToKeepSet) {
      return treeData.map(node => {
        const hasChildren = node.children && node.children.length > 0
        if (!hasChildren && node.dataId && !idsToKeepSet.has(node.dataId)) {
          return null
        }

        let children
        if (hasChildren) {
          children = this.filterLeafNodeUserMenuCode(node.children, idsToKeepSet);
          if (children.length === 0) {
            return null;
          }
        }

        return { ...node, children };
      }).filter(node => node);
    },
    formatMenuTreeData(nodeDataList, permissionIds, myselfLogAble) {
      // 没有权限，清空所有数据
      if (!permissionIds || permissionIds.length === 0) {
        nodeDataList.splice(0)
        return
      }
      // 遍历树数据
      for (let i = 0; i < nodeDataList.length; i++) {
        const nodeData = nodeDataList[i]
        if (this.isEnglish()) {
          nodeData.label = setTheFirstLetterToUppercase(nodeData.label)
        }
        if (nodeData.children && nodeData.children.length > 0) {
          this.formatMenuTreeData(nodeData.children, permissionIds, myselfLogAble)
        }
        // 超管或根三员
        const isRootRole = this.isSuperRole || this.isSuperThreeUser
        // 非超管或根三员，不允许将“企业密钥管理” E31 权限下发给其它管理员， dataId 菜单编码
        const isRemoveE31 = !isRootRole && 'E31' === nodeData.dataId
        // 如果是父级菜单，但是子节点为0，则无需展示
        const ieEmptyMenu = nodeData.children && nodeData.children.length === 0 || !nodeData.children && !nodeData.dataId
        // 没有权限的数据
        const notPermission = nodeData.dataId && permissionIds.findIndex(id => id == nodeData.dataId || id == '*') < 0
        // 如果未在高级配置里开启允许查看自身管理员日志的配置，默认不在菜单列表里显示管理员日志菜单按钮，因此在权限菜单列表里也进行隐藏
        const adminatorLog = !myselfLogAble && nodeData.dataId === 'A15'
        if (isRemoveE31 || ieEmptyMenu || notPermission || adminatorLog) {
          nodeDataList.splice(i, 1)
          i--
        }
      }
    },
    invokeMethod(func, roleId, type, thenFunc) {
      // 菜单或部门 type + 角色 id
      const key = type + roleId
      // 通过 key 获取权限数据
      const existData = this.permissionMap[key]
      if (existData) {
        // 已存在数据，则将缓存的数据返回
        if (thenFunc) {
          setTimeout(() => {
            thenFunc({ code: 20000, data: existData })
          }, 100)
        }
      } else {
        // 请求数据
        func(roleId).then(respond => {
          if (respond.data) this.permissionMap[key] = respond.data
          if (thenFunc) thenFunc(respond)
        })
      }
    },
    showTypeChange(val) {
      this.clearFilterAndChecked(val)
    },
    handleChange(selectedKeys, datas) {
      const roleIds = []
      this.roleNames = []
      datas.forEach(data => {
        roleIds.push(Number.parseInt(data.dataId))
        if (data.dataId != 2 && data.dataId != 3 && data.dataId != 4) {
          this.roleNames.push(data.label)
        }
      })
      this.temp.roleIds = [...roleIds]
    },
    handleCheckAllChange(boolean) {
      // showType: 1 菜单-按钮  2 按钮 - 菜单 3 只显示按钮
      if (this.temp.showType == 3) {
        this.permissionOption.forEach(item => {
          item.value = boolean ? 1 : 0
        })
      } else if (this.temp.showType == 2) {
        if (boolean) {
          // 父子级联，只需勾选父节点即可
          const ids = this.btnAndMenuTreeData.map(btn => btn.id)
          this.$refs.btnMenuTree.setCheckedKeys(ids)
        } else {
          this.$refs.btnMenuTree.clearSelectedNodes()
        }
      } else {
        if (boolean) {
          // 父子级联，只需勾选父节点即可
          const ids = this.menuPermissionTreeData.map(btn => btn.id)
          this.$refs.menuBtnTree.setCheckedKeys(ids)
        } else {
          this.$refs.menuBtnTree.clearSelectedNodes()
        }
      }
    },
    handleConfig() {
      const codeSet = new Set()
      if (this.temp.roleIds.length === 0 || this.temp.roleIds === undefined) {
        this.$message({
          message: this.$t('pages.permission_Msg20'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (this.temp.showType === 3) {
        if (!this.permissionOption.find(item => item.value === 1)) {
          this.$message({
            message: this.$t('pages.permission_Msg21'),
            type: 'error',
            duration: 2000
          })
          return
        }
        // 获取 value === 1 的项 code 的 Set 集合
        const permissionCodeSet = new Set(this.permissionOption.filter(item => item.value === 1).map(item => item.code))
        // 过滤，扁平化，遍历
        this.btnPermission
          .filter(btn => permissionCodeSet.has(btn.key))
          .flatMap(btn => btn.value)
          .forEach(permission => {
            // 假设勾选了导入数据这一按钮权限，因为有导入数据权限的前提下，是一定有部门管理的基础功能权限的，
            // 因此如果是移除的话不用移除部门管理，但是如果是追加的话，可能用户是没有部门管理的权限的因此需要加入
            if (this.temp.operatorType === 1) {
              codeSet.add(permission.menuCode)
            }
            codeSet.add(permission.btnCode)
          })
      }
      if (this.temp.showType === 2) {
        const checkedData = this.$refs.btnMenuTree.getCheckedNodes()
        if (checkedData.length === 0) {
          this.$message({
            message: this.$t('pages.permission_Msg21'),
            type: 'error',
            duration: 2000
          })
          return
        }
        checkedData.forEach(node => {
          // 按钮-菜单形式展示的权限列表，需要实现的效果为：假设勾选了导入数据下的部门管理，并选择移除权限，则需要删掉该角色部门管理的导出数据权限
          // 因为有导入数据权限的前提下，是一定有部门管理的基础功能权限的并且可能还有导出、查看详情等权限，因此如果是移除的话不用移除部门管理只移除导入数据权限
          // 但是如果是追加的话，可能用户是没有部门管理的权限的因此需要加入
          if (node.type === 'N') {
            if (this.temp.operatorType === 1) {
              codeSet.add(node.menuCode)
            }
            codeSet.add(node.btnCode)
          }
        })
      }
      if (this.temp.showType === 1) {
        const checkedData = this.$refs.menuBtnTree.getCheckedNodes(false, true)
        if (checkedData.length === 0) {
          this.$message({
            message: this.$t('pages.permission_Msg21'),
            type: 'error',
            duration: 2000
          })
          return
        }
        checkedData.forEach(node => {
          if (node.dataId && !node.children) {
            codeSet.add(node.dataId)
          }
        })
        // 如果是移除权限，在选择了基础功能的前提下，可能有其它功能未选择。如管理员日志下有：基础功能、删除记录、导出数据。在移除情况下就算只选择了基础功能，在最终移除的权限中也得有删除记录和导出数据权限
        if (this.temp.operatorType === 2) {
          checkedData.forEach(node => {
            if (node.children && node.dataId && codeSet.has(node.dataId)) {
              node.children.forEach(children => {
                codeSet.add(children.dataId)
              })
            }
          })
        }
      }
      this.submitting = true
      const obj = Object.assign({}, this.temp)
      obj.codes = Array.from(codeSet)
      // 判断是否是超管或根三员,不是需要移除企业密钥管理，因为企业密钥管理只有超管或根三员可以进行分配
      const isRootRole = this.isSuperRole || this.isSuperThreeUser
      if (!isRootRole) { 
        // E31: 企业密钥管理，147: 导出数据，148：导入数据
        obj.codes = obj.codes.filter(item => (item != 'E31' && item != '147' && item != '148'))
      }
      // 级联模式下，根三员角色可能会被选中，因此需要过滤
      obj.roleIds = obj.roleIds.filter(roleId => roleId != 2 && roleId != 3 && roleId != 4)
      obj.roleNames = [...this.roleNames]
      obj.codeNames = [...this.codeNameMap]
      obj.showType = this.temp.showType
      updateRolesPermission(obj).then(resp => {
        this.dialogFormVisible = false
        this.submitting = false
        if (resp.data.length > 0) {
          this.tableData = []
          resp.data.forEach(item => {
            if (item.failPermissionName.length > 0) {
              const obj = {
                roleName: item.failRoleName,
                reason: item.failReason,
                codeName: item.failPermissionName
              }
              this.tableData.push(obj)
            }
          })
          if (this.tableData.length > 0) {
            this.$confirm(this.$t('pages.permission_Msg34') + '，' + this.$t('pages.permission_Msg37') + '?', this.$t('text.prompt'), { type: 'warning' }).then(() => {
              this.updateAfterVisible = true
            })
          } else {
            this.$message({
              message: this.$t('pages.permission_Msg22'),
              type: 'success',
              duration: 2000
            })
          }
        } else {
          this.$message({
            message: this.$t('pages.permission_Msg22'),
            type: 'success',
            duration: 2000
          })
        }
      }).catch(e => {
        this.submitting = false
      })
    },
    handleDrag() {

    },
    menuBtnPermissionCheck(nodeData, checkedInfo) {
      const checkedKeysSet = new Set(checkedInfo.checkedKeys)
      const isCheck = checkedKeysSet.has(nodeData.id)
      // 当前节点有相关联的节点
      if (nodeData.relDataId) {
        // 在半选节点中查找当前节点的父节点
        const parentNode = checkedInfo.halfCheckedNodes.filter(({ id }) => id == nodeData.parentId)[0]
        // 父节点不存在，说明父节点已勾选 或者 未勾选，那就不需要处理关联的节点
        if (!parentNode) return
        // 勾选的节点是被关联的节点，那么需要将依赖节点勾选上
        if (isCheck && nodeData.dataId !== nodeData.relDataId) {
          // 依赖节点
          const relNodes = parentNode.children.filter(({ dataId }) => nodeData.relDataId.split(',').includes(dataId))
          // 如果依赖节点未勾选，则勾选上
          relNodes.forEach(relNode => {
            checkedKeysSet.add(relNode.id)
          })
          this.$refs.menuBtnTree.setCheckedKeys(Array.from(checkedKeysSet))
        }
        // 取消勾选的节点是依赖节点，那么需要将被关联的节点取消勾选
        if (!isCheck) {
          // 被关联的节点数组
          const relNodes = parentNode.children.filter(({ relDataId }) => relDataId.split(',').includes(nodeData.dataId))
          // 遍历并取消勾选
          relNodes.forEach(node => {
            this.$refs.menuBtnTree.setChecked(node, false)
          });
        }
      }
    },
    btnMenuPermissionChange(data) {
      // console.log('data: ' + JSON.stringify(data))
      // console.log('this.btnAndMenuTreeData: ' + JSON.stringify(this.btnAndMenuTreeData))
    },
    show(currentClickNodeId) {
      const checkedKeys = Array.isArray(currentClickNodeId) ? currentClickNodeId : [currentClickNodeId]
      this.checkedKeys = checkedKeys.map(id => `G${id}`)
      this.cascadeEnable = '0'
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.clearRoleCheck()
      this.clearFilterAndChecked()
      this.$nextTick(() => {
        this.dialogFormVisible = true
        this.handleCheckRole()
      })
    },
    // 清除管理员角色的勾选状态
    clearRoleCheck() {
      if (this.$refs.treeSelect) {
        this.$refs.treeSelect.clearFilter()
        this.$refs.treeSelect.clearSelectedNode()
      }
    },
    // 勾选管理员角色
    handleCheckRole() {
      this.$nextTick(() => {
        this.checkedKeys = [...this.checkedKeys]
      })
    },
    // 清除权限的勾选状态
    clearFilterAndChecked(type) {
      if (this.$refs.menuBtnTree) {
        this.$refs.menuBtnTree.clearFilter()
        this.$refs.menuBtnTree.clearSelectedNodes()
      }
      if (this.$refs.btnMenuTree) {
        this.$refs.btnMenuTree.clearFilter()
        this.$refs.btnMenuTree.clearSelectedNodes()
      }
      this.permissionOption.forEach(item => {
        item.value = 0
      })
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
