<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="roleTree"
        :data="treeData"
        node-key="dataId"
        :current-node-key="currentNodeKey"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      ></tree-menu>
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addRole') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteRole') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleAdvancedConfig">
          {{ $t('pages.batchUpdatePermission') }}
        </el-button>
        {{ $t('pages.manageMode') }}：{{ modeName }}
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.roleName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="roleList" :selectable="selectable" :col-model="colModel" :auto-load="false" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <!-- 角色弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px">
        <FormItem :label="$t('pages.roleName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60" :disabled="isEditRootRole"/>
        </FormItem>
        <FormItem :label="$t('pages.parentRole')" prop="parentId">
          <tree-select
            ref="parentRole"
            :disabled="isEdit"
            :data="roleTreeData"
            node-key="dataId"
            :checked-keys="[temp.parentId]"
            :filter-key="filterKey"
            :width="380"
            @change="parentIdSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('pages.roleStatus')">
          <el-select v-model="temp.active" :placeholder="$t('text.select')" :disabled="isEditRootRole">
            <el-option v-for="(value, key) in activeOptions" :key="key" :label="value" :value="key" />
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 管理员设置 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.sysUserSetting')"
      :visible.sync="dialogUserVisible"
      width="800px"
    >
      <Form ref="userForm" :rules="rules" :model="tempUser" label-position="right" label-width="100px" >
        <el-divider content-position="left"> {{ $t('pages.roleBaseInformation') }} </el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.roleName') + '：'">
              {{ tempUser.name }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.parentRole') + '：'">
              {{ tempUser.parentRoleName }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.roleStatus') + '：'">
              {{ activeFormatter(tempUser, tempUser.active) }}
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left"> {{ $t('pages.roleAdminSetting') }} </el-divider>
        <grid-select
          ref="userGridSelect"
          :height="300"
          :col-model="userColModel"
          :before-select-data-func="beforeSelectDataFunc"
          searchable
          :search-prop="{ key: 'account', label: $t('table.account') }"
          pager-small
          :select-table-title="$t('components.optionalAdministrator')"
          :selected-table-title="$t('components.selectedAdministrator')"
          :selectable="selectable"
          :select-row-data-api="selectUserRowDataApi"
          :selected-row-data-api="selectedUserRowDataApi"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateUser()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogUserVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 分配权限 -->
    <permission-dlg ref="permissionDlg" :is-three="!isSuperMode" :role-data="permissionRoleNode" :disable-role-id="permissionDisableRoleId"/>
    <!-- 高级配置 -->
    <advanced-config ref="advancedConfig" :original-role-data="permissionRoleNode"></advanced-config>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  fetchList, getByName, createRole, updateRole,
  deleteRole, validateDelRole, updateUser, listByUserId, getParentRoleById
} from '@/api/system/organizational/role'
import { getManagerRoleByRoleId/*, updateRoleRangePermission*/ } from '@/api/system/organizational/permission'
import { getPage, listByRoleId } from '@/api/system/organizational/sysUser'
import { findNode, toNodeValue } from '@/utils/tree'
import { activeIconFormatter } from '@/utils/formatter'
import GridSelect from '@/components/GridSelect'
import PermissionDlg from '../permissions/editDlg'
import AdvancedConfig from './advancedConfig.vue'

export default {
  name: 'Role',
  components: { PermissionDlg, GridSelect, AdvancedConfig },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'roleName', width: '150', sort: true, iconFormatter: activeIconFormatter },
        { prop: 'parentId', label: 'parentRole', width: '150', sort: true, formatter: this.parentFormatter },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { prop: 'userSize', label: this.$t('pages.sysUser'), width: '150', type: 'button',
          buttons: [
            { formatter: this.userSizeFormatter, disabledFormatter: () => !this.hasPermission('A13'), click: this.linkToSysUserPage }
          ]
        },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate, disabledFormatter: this.updateBtnFormatter },
            { label: 'assignPermissions', click: this.handlePermission, disabledFormatter: this.assignPermissionBtnFormatter },
            { label: 'sysUserSetting', click: this.handleUser, isShow: () => this.isSuperMode, disabledFormatter: this.updateBtnFormatter }
          ]
        }
      ],
      userColModel: [
        { prop: 'account', label: 'account', width: '100', sort: true },
        { prop: 'name', label: 'name', width: '100', sort: true }
      ],
      treeData: [{ id: '', dataId: '', label: this.$t('pages.sysUserRole'), parentId: '0', children: [] }],
      treeData1: [{ id: '', dataId: '', label: this.$t('pages.null'), parentId: '0', children: [] }],
      roleTreeData: [],
      defaultExpandedKeys: [''],
      treeSelectNode: [],
      permissionRoleNode: [],
      permissionDisableRoleId: [],
      currentNodeKey: '',
      query: { // 查询条件
        parentId: undefined,
        searchInfo: ''
      },
      activeOptions: { 1: this.$t('text.enable'), 0: this.$t('text.disable') },
      showTree: true,
      editable: true,
      deleteable: false,
      oldRoleName: null,
      temp: { },
      tempUser: {},
      defaultTemp: {
        id: undefined,
        parentId: undefined,
        name: '',
        active: '1',
        type: 0,
        remark: '',
        parentName: ''
      },
      defaultTempUser: {
        id: undefined,
        name: '',
        userIds: []
      },
      dialogFormVisible: false,
      dialogUserVisible: false,
      dialogStatus: '',
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        parentId: [{ required: true, message: this.$t('pages.validateMsg_enterParentRole'), trigger: ['blur', 'change'] }]
      },
      roleRange: [],          // 当前角色配置的角色范围, 如果没有配置，则范围为当前角色及子角色
      submitting: false,
      tableNoClickIds: [],
      currentClickNodeId: undefined
    }
  },
  computed: {
    ...mapGetters([
      'userId',
      'isSuperMode',
      'isSuperRole',
      'isSysRole',
      'roleTree',
      'currentRoleId'
    ]),
    modeName() {
      return this.isSuperMode ? this.$t('pages.superMode') : this.$t('pages.threeMode')
    },
    isEdit() {
      if (this.dialogStatus === 'create') {
        return false
      }
      if (this.temp.id <= 4) {
        return true
      }
      if (this.dialogStatus === 'update' && !this.isSuperRole && this.currentRoleId != 2) {
        return true
      }
      return false
    },
    isEditRootRole() {
      if (this.dialogStatus === 'create') {
        return false
      }
      return this.temp.id <= 4
    },
    textMap() {
      return {
        update: this.$t('pages.updateRole'),
        create: this.$t('pages.addRole')
      }
    },
    gridTable() {
      return this.$refs['roleList']
    },
    treeMenu() {
      return this.$refs['roleTree']
    },
    filterKey() {
      return this.temp.id
    }
  },
  watch: {
    roleRange(val) {
      this.filterRoleRange()
      // if (this.currentRoleId) {
      //   updateRoleRangePermission(this.currentRoleId)
      // }
    },
    roleTree(val) {
      this.filterRoleRange()
    },
    currentRoleId(val) {
      this.loadRoleRange()
    }
  },
  created() {
    this.resetTemp()
    this.loadTreeData()
  },
  activated() {
    this.gridTable && this.gridTable.execRowDataApi(this.query)
  },
  methods: {
    selectable(row, index) {
      if (row.id <= 4) {
        return false
      }
      if (row.id === this.currentRoleId) {
        return false
      }
      return true
    },
    filterRoleRange() {
      // 非超级管理员、或者三员模式下的系统管理员
      if (!this.isSuperRole && this.currentRoleId != 2) {
        // 获取当前登录管理员的角色id
        const roleId = this.currentRoleId
        if (!roleId || this.roleRange.length == 0) return
        // 根据 roleRange 过滤树数据
        const roleTree = this.getFilterTreeDataByIds([...this.roleTree], this.roleRange)
        const treeData = [{ id: '', dataId: '', label: this.$t('pages.sysUserRole'), parentId: '0', children: [...roleTree] }]
        this.treeData = treeData
        // this.roleTreeData = [...roleTree]
        this.roleTreeData = [{ id: '0', dataId: '0', label: this.$t('pages.null'), children: [...roleTree], disabled: 'true' }]
      } else {
        const treeData = [{ id: '', dataId: '', label: this.$t('pages.sysUserRole'), parentId: '0', children: [...this.roleTree] }]
        this.treeData = treeData
        this.roleTreeData = [{ id: '0', dataId: '0', label: this.$t('pages.null'), children: [...this.roleTree] }]
      }
      this.changeTreeSelectNode()
    },
    // 根据 ids 获取过滤后的树数据，数据节点包含ids及子节点
    getFilterTreeDataByIds(treeData, ids) {
      const newTreeData = []
      // ids 转成 map ，便于判断 id 是否存在
      const idMap = ids.reduce((result, id) => {
        result[id] = id
        return result
      }, {})
      // 循环判断节点是否保留，若节点保留，则子节点不再判断；若节点不保留，则将其子节点插入到数据前面，继续进行循环
      while (treeData.length > 0) {
        const data = treeData.shift()
        const id = data.dataId
        if (idMap[id]) {
          newTreeData.push(data)
        } else {
          const children = data.children
          if (children && children.length > 0) {
            treeData.unshift(...children)
          }
        }
      }
      return newTreeData
    },
    // 选择用户前的钩子，如果是三员模式且已选择了有用户，则阻止
    beforeSelectDataFunc() {
      const ids = this.userSelect().getSelectedIds()
      if (!this.isSuperMode && ids.length > 0) {
        this.$message({
          message: this.$t('pages.validateMsg_hasBeenOneUserInThreeMode'),
          type: 'error'
        })
        return false;
      }
      return true
    },
    userSelect() {
      return this.$refs['userGridSelect']
    },
    loadTreeData(callback) {
      this.$store.dispatch('commonData/setRoleTree', data => {
        this.loadRoleRange()
        callback()
      })
    },
    loadRoleRange() {
      if (this.currentRoleId) {
        getManagerRoleByRoleId(this.currentRoleId).then(res => {
          const range = res.data
          if (range.length > 0) {
            // 有配置角色范围
            this.roleRange = range
          } else {
            // 没有配置范围
            const currentNode = findNode(this.roleTree, this.currentRoleId, 'dataId')
            if (currentNode) {
              this.roleRange = toNodeValue(currentNode, 'dataId')
            }
          }
          this.gridTable.execRowDataApi(this.query)
        })
      }
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (!this.isSuperRole && this.currentRoleId != 2) {
        // 不是超管
        const roleId = this.currentRoleId
        if (!roleId || this.roleRange.length == 0) {
          // 若还未加载角色及范围，则返回空数组
          return new Promise((resolve, reject) => {
            resolve({ code: 20000, data: { total: 0, items: [] }})
          })
        }
        if (!searchQuery.parentId) {
          // 查询顶级分组，根据配置范围查询
          searchQuery.roleIds = this.roleRange.join(',')
        }
      }
      return fetchList(searchQuery)
    },
    async changeTreeSelectNode() {
      this.permissionRoleNode.splice(0)
      this.permissionDisableRoleId.splice(0)
      this.permissionRoleNode = JSON.parse(JSON.stringify(this.treeData))
      this.findTableClickIds(this.roleTreeData[0].children)
      // 系统管理、安全管理、审计管理都不能修改初始权限
      this.permissionDisableRoleId.push(2, 3, 4)
    },
    // 不能对上级分组进行修改、分配权限、管理员设置等
    findTableClickIds(nodes) {
      for (const node of nodes) {
        const clickAble = this.findDataIdInTree(node, this.currentRoleId)
        if (clickAble) {
          if (!this.tableNoClickIds.includes(node.dataId)) {
            this.tableNoClickIds.push(node.dataId)
          }
        }
        if (node.children) {
          this.findTableClickIds(node.children)
        }
      }
    },
    findDataIdInTree(tree, dataId) {
      function traverse(node) {
        if (Number.parseInt(node.dataId) === Number.parseInt(dataId)) {
          return true;
        }
        if (!node.children) {
          return false;
        }
        // 遍历所有子节点
        for (const child of node.children) {
          if (traverse(child)) {
            return true; // 如果在任何子节点中找到匹配项，则返回true
          }
        }
        return false; // 所有子节点检查完毕，未找到匹配项
      }
      return traverse(tree);
    },
    selectUserRowDataApi: function(option) { // 可选用户
      if (this.roleRange.length > 0 && !this.isSuperRole && this.currentRoleId != 2) {
        // 管理员设置的可选用户得根据角色范围来查询
        return getPage({ ...option, ignoreSuper: true, roleIds: this.roleRange.join(',') })
      }
      return getPage({ ...option, ignoreSuper: true })
    },
    selectedUserRowDataApi: async function(option) { // 已选用户
      const selectedUsers = await listByRoleId({ roleId: this.tempUser.id })
      return selectedUsers
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    parentIdSelectChange: function(data) {
      this.temp.parentId = data
      this.temp.parentName = this.getRoleNames(data)
    },
    getRoleNames(roleId) {
      const tree = this.$refs['roleTree'].tree()
      const node = tree.getNode(roleId)
      return node ? node.label : this.$t('pages.null')
    },
    handleNodeClick: function(data, node, el) {
      data.dataId != '' ? this.currentClickNodeId = Number.parseInt(data.dataId) : this.currentClickNodeId = undefined
      node.expanded = true
      this.defaultTemp.parentId = data.dataId
      this.query.parentId = data.dataId
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.tempUser = Object.assign({}, this.defaultTempUser)
      this.oldRoleName = null
    },
    handlePermission(row) {
      this.$refs['permissionDlg'].show(row, this.currentRoleId)
    },
    handleUser(row) {
      this.resetTemp()
      this.tempUser = Object.assign({}, row)
      this.dialogUserVisible = true
      if (this.userSelect()) {
        this.userSelect().clearFilter()
        this.userSelect().reload()
      }
      this.$nextTick(() => {
        this.$refs['userForm'].clearValidate()
      })
    },
    handleCreate() {
      this.resetTemp()
      if (this.query.parentId) {
        this.temp.parentId = this.query.parentId
      } else {
        this.temp.parentId = ''
      }
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.temp.active = this.temp.active.toString()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.temp.parentName = this.getRoleNames(this.temp.parentId)
        this.oldRoleName = this.temp.parentName
        if (this.temp.parentId == '' && (!this.isSuperRole && this.currentRoleId != 2)) {
          this.temp.parentId = 0
        }
      })
    },
    handleImport() {},
    handleExport() {},
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.isSuperMode) {
            // 三元模式不能创建上级角色为空的角色
            if (this.temp.parentId == 0) {
              this.$message({
                message: this.$t('pages.createRoleMsg'),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
              return
            }
          }
          if ((!this.isSuperRole && this.currentRoleId != 2) && this.temp.parentId == 0) {
            this.$message({
              message: this.$t('pages.createRoleMsg'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          createRole(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.query.parentId = String(respond.data.id)
            this.gridTable.execRowDataApi(this.query)
            this.loadTreeData(() => {
              this.$nextTick(() => {
                this.$refs.roleTree.setCurrentKey(String(respond.data.id))
              })
            })
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    async updateData() {
      var parentId = ''
      if (!this.isSuperRole && this.currentRoleId != 2) {
        const data = await getParentRoleById(this.temp.id)
        parentId = data.data.parentId
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.isSuperMode) {
            // 三元模式不能创建上级角色为空的角色
            if (this.temp.parentId == 0) {
              this.$message({
                message: this.$t('pages.createRoleMsg'),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
              return
            }
          }
          this.submitting = true
          const tempData = Object.assign({}, this.temp)
          tempData.parentName_new = tempData.parentName
          tempData.parentName = this.oldRoleName
          if (!this.isSuperRole && this.currentRoleId != 2) {
            tempData.parentId = parentId
          }
          updateRole(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.updateRowData(respond.data)
            this.loadTreeData()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    async updateUser() {
      const { data } = await listByRoleId({ roleId: this.tempUser.id })
      // 构造一个map对象,key为id,value为user
      const userMap = {}
      data.forEach((user) => {
        const id = user.id
        userMap[id] = user
      })
      // 原来已选择的管理员id集合
      const originalUserIds = data.map((user) => { return user.id })
      // 经过变更后的已选择的管理员id集合
      const userIds = this.userSelect().getSelectedIds()
      if (userIds.length < originalUserIds.length) {
        // 筛选出将要解绑角色的管理员id
        const filterUserIds = originalUserIds.filter((userId) => {
          return !userIds.includes(userId)
        })
        // 判断要解绑角色的管理员是否只对应一个角色，如果只对应一个角色，提示用户“是否让xxx管理员无绑定角色”
        // 绑定少于等于一个角色的管理员id集合
        const bindLeOneRoleUserIds = []
        for (const userId of filterUserIds) {
          const { data } = await listByUserId(userId)
          if (!data || data.length <= 1) {
            bindLeOneRoleUserIds.push(userId)
          }
        }
        if (bindLeOneRoleUserIds.length) {
          // 拼接绑定少于等于一个角色的管理员名字，以逗号分割
          let userNames = ''
          for (let i = 0; i < bindLeOneRoleUserIds.length; i++) {
            userNames += userMap[bindLeOneRoleUserIds[i]].name
            if (i < bindLeOneRoleUserIds.length - 1) {
              userNames += '、'
            }
          }
          this.$confirmBox(this.$t('pages.roleUnbindConfirmBoxMsg', { userNames })).then(() => {
            this.doUpdateUser(userIds)
          }).catch(() => {})
        } else {
          this.doUpdateUser(userIds)
        }
      } else {
        this.doUpdateUser(userIds)
      }
    },
    doUpdateUser(userIds) {
      const userNames = this.userSelect().getSelectedDatas().map(item => {
        return item.name
      })
      const tempData = Object.assign({}, this.tempUser)
      updateUser({
        roleName: this.tempUser.name, // 与业务无关，记录管理员日志显示的详情
        roleId: tempData.id,
        userIds: userIds,
        userNames: userNames // 与业务无关，记录管理员日志显示的详情
      }).then(respond => {
        this.dialogUserVisible = false
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    async handleAdvancedConfig() {
      const toUpdateIds = this.gridTable.getSelectedIds()
      this.$refs.advancedConfig.show(toUpdateIds)
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      validateDelRole({ ids: toDeleteIds.join(',') }).then(respond => {
        if (respond.data === 'disable_4_child') {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.validateMsg_cantDelete1'),
            type: 'warning',
            duration: 2000
          })
        } else if (respond.data === 'disable_4_relate') {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.validateMsg_cantDelete2'),
            type: 'warning',
            duration: 2000
          })
        } else {
          this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
            deleteRole({ ids: toDeleteIds.join(',') }).then(respond => {
              this.gridTable.deleteRowData(toDeleteIds)
              this.loadTreeData()
              this.query.parentId = ''
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.deleteSuccess'),
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
          })
        }
      })
    },
    nameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    activeFormatter: function(row, data) {
      return this.activeOptions[data]
    },
    activeIconFormatter: function(row, data) {
      const label = this.activeOptions[row.active]
      return row.active ? { class: 'active', title: label } : { class: 'offline', title: label, style: 'color: #888;' }
    },
    updateBtnFormatter(rowData) {
      if (!rowData) {
        return false;
      }
      if (this.isSuperRole || this.currentRoleId === 2) {
        return false
      }
      // 不是超管时，根三员不能修改
      if (rowData.id <= 4) {
        return true
      }
      // 不能修改自身和上级节点
      if (rowData.id === this.currentRoleId) {
        return true
      }
      return this.tableNoClickIds.includes(String(rowData.id))
    },
    assignPermissionBtnFormatter(rowData) {
      if (rowData.id <= 4) return true
      return this.updateBtnFormatter(rowData)
    },
    parentFormatter: function(row, data) {
      const node = findNode(this.treeData, data, 'dataId')
      row.parentRoleName = node ? node.label : ''
      return row.parentRoleName
    },
    userSizeFormatter(row, data) {
      return row.userSize
    },
    linkToSysUserPage(row) {
      const url = '/system/organizational/sysUser'
      const queryTemp = { roleId: row.id, includeChildRole: 0 }
      this.$router.push({ path: url, query: queryTemp })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
