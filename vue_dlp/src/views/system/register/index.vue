<template>
  <div class="app-container">
    <el-tabs class="el-dialog__body">
      <el-tab-pane label="注册信息" style="overflow: auto;">
        <div v-for="info in infos" :key="info.productId">
          <div><span style="color: #04a2ff">产品编号：</span>{{ info.productId }}</div>
          <div><span style="color: #68A8D0">用户编号：</span>{{ info.userNo }}</div>
          <div><span style="color: #68A8D0">序列号：</span>{{ info.serNo }}</div>
          <div><span style="color: #68A8D0">内网用户数：</span>{{ info.userCountin }}</div>
          <div><span style="color: #68A8D0">内网总天数：</span>{{ info.totalDaysin }}</div>
          <div><span style="color: #68A8D0">内网试用天数：</span>{{ info.daysLeftin }}</div>
          <div><span style="color: #68A8D0">登记日期：</span>{{ info.registerData }}</div>
          <div><span style="color: #68A8D0">注册状态：</span>{{ info.registerStatus }}</div>
          <div><span style="color: #68A8D0">功能类型：</span>{{ info.functionType }}</div>
          <div><span style="color: #68A8D0">是否有usb中的主密钥：</span>{{ info.hasUsbKey }}</div>
          <div><span style="color: #68A8D0">公司信息：</span>{{ info.companyInfo }}</div>
          <div><span style="color: #68A8D0">主密钥：</span>{{ info.mainKey }}</div>
          <div><span style="color: #68A8D0">控制代码：</span>{{ info.ctrlCode }}</div>
          <div><span style="color: #68A8D0">版本类型：</span>{{ info.versionType }}</div>
          <div><span style="color: #68A8D0">签名：</span>{{ info.signature }}</div>
          <div><span style="color: #68A8D0">升级期限：</span>{{ info.updateData }}</div>
          <br>
        </div>
        <br>
        <div v-show="updateTime !== ''">刷新时间：{{ updateTime }}</div>
        <br>
        <el-button @click="reloadRegisterInfo">刷新注册信息</el-button>
        <el-button @click="getSystemInfo">从内存中读取信息</el-button>
        <el-button @click="putSystemInfo">加测试数据</el-button>
        <el-button @click="addDog">模拟加狗</el-button>
        <el-button @click="subDog">模拟减狗</el-button>
      </el-tab-pane>
      <el-tab-pane label="网络注册">
        <h1>请输入序列号，注册成正式版</h1>

        <Form ref="serialForm" :model="serialForm" label-width="60px" :rules="serialRules">
          <FormItem label="序列号" prop="serialNo">
            <el-input v-model="serialForm.serialNo" style="width: 300px"></el-input>
          </FormItem>
          <div>注册结果： {{ serialRegResult }}</div>

          <FormItem>
            <el-button type="primary" @click="submitRegForm('serialForm')">注册</el-button>
            <el-button @click="resetForm('serialForm')">重置</el-button>
          </FormItem>
        </Form>

      </el-tab-pane>
      <el-tab-pane label="文件注册">
        <h1>使用许可文件，注册成正式版</h1>

        <!--action，上传的地址-->
        <el-upload
          ref="upload"
          class="upload-demo"
          action="regByFile"
          :auto-upload="false"
          :limit="fileLimit"
          :on-exceed="onExceed"
        >
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>

          <div>注册结果：{{ fileRegResult }}</div>
        </el-upload>
      </el-tab-pane>

      <el-tab-pane label="模块分配" style="overflow: auto;">
        <div>总可用模块：{{ moduleNumbers }} 个</div>
        <div v-for="module in modules" :key="module.moduleId">
          <div><span style="color: #04a2ff">模块编号：</span>{{ module.moduleId }}</div>
          <div><span style="color: #68A8D0">可分配数：</span>{{ module.numbers }}</div>
        </div>
        <div>
          <el-button @click="reloadModuleInfo">查看可分配模块信息</el-button>
        </div>
        <div>
          <Form ref="terminalModuleInfoForm" :model="terminalModuleForm" label-width="80px" :inline="true">
            <FormItem label="终端编号" prop="terminalId">
              <el-input v-model="terminalModuleForm.terminalId" style="width: 150px"></el-input>
            </FormItem>
            <el-button @click="getTerminalModuleInfo('terminalModuleForm')">查看终端分配模块信息</el-button>
          </Form>
        </div>
        <div>终端已分配模块信息：{{ terminalModuleInfoResult }}</div>
      </el-tab-pane>

      <el-tab-pane label="终端操作">
        <div>
          <Form ref="updateModuleForm" :model="moduleForm" label-width="80px" :inline="true">
            <FormItem label="终端编号" prop="terminalId">
              <el-input v-model="moduleForm.terminalId" style="width: 150px"></el-input>
            </FormItem>
            <FormItem label="模块编号" prop="moduleId">
              <el-input v-model="moduleForm.moduleId" style="width: 150px"></el-input>
            </FormItem>
            <el-radio-group v-model="moduleForm.radio">
              <el-radio :label="1">新增分配</el-radio>
              <el-radio :label="2">解除分配</el-radio>
            </el-radio-group>
            <el-button @click="updateModuleInfo('moduleForm')">修改终端可分配模块信息</el-button>
          </Form>
        </div>
        <div>修改结果：{{ updateModuleResult }}</div>
        <br>
        <div>
          <Form ref="deleteTerminalForm" :model="delTerminalForm" label-width="80px" :inline="true">
            <FormItem label="终端编号" prop="terminalId">
              <el-input v-model="delTerminalForm.terminalId" style="width: 150px"></el-input>
            </FormItem>
            <el-button @click="deleteTerminal('delTerminalForm')">删除终端</el-button>
          </Form>
        </div>
        <div>删除结果： {{ deleteTerminalResult }}</div>
      </el-tab-pane>
      <el-tab-pane label="批量操作">
        <div>
          <Form ref="modifyModuleForm" :model="modifyModuleForm" label-width="80px">
            <el-radio-group v-model="modifyModuleForm.objectType">
              <el-radio :label="1">终端对象</el-radio>
              <el-radio :label="2">分组对象</el-radio>
            </el-radio-group>
            <FormItem label="对象编号" prop="objectId">
              <el-input v-model="modifyModuleForm.objectId" style="width: 150px"></el-input>
            </FormItem>
            <el-radio-group v-model="modifyModuleForm.operateType">
              <el-radio :label="1">业务模块</el-radio>
              <el-radio :label="2">过滤模块</el-radio>
            </el-radio-group>
            <FormItem label="模块数量" prop="numbers">
              <el-input v-model="modifyModuleForm.numbers" style="width: 50px"></el-input>
            </FormItem>
            <FormItem label="模块数组" prop="moduleId">
              <el-input v-model="modifyModuleForm.moduleId" style="width: 250px"></el-input>
            </FormItem>
            <el-button @click="modifyModuleInfo('modifyModuleForm')">修改模块/过滤信息</el-button>
          </Form>
        </div>
        <div>修改结果：{{ modifyModuleResult }}</div>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>

import { getRegisterInfo,
  getInfo,
  putInfo,
  regBySerial,
  regByFile,
  getModuleInfo,
  getTerminalModuleInfoByNo,
  updateTerminalModules,
  delTerminalByNo,
  modifyModules,
  addDogMemory,
  subDogMemory
} from '@/api/system/register/reg'
import { parseTime } from '@/utils'

export default {
  name: 'Register',
  data() {
    return {
      // 注册信息
      updateTime: '',
      infos: null,
      // 网络注册
      serialForm: {},
      defaultSerialForm: {
        serialNo: ''
      },
      serialRules: {
        serialNo: [{ required: true, message: '序列号不能为空' }]
      },
      serialRegResult: '',
      // 文件注册
      fileLimit: 1,
      fileRegResult: '',
      // 可分配模块信息
      modules: null,
      moduleNumbers: 0,
      // 终端已分配模块信息
      terminalModuleInfoResult: '',
      terminalModuleForm: {
        terminalId: ''
      },
      // 修改终端可分配模块信息
      moduleForm: {
        terminalId: '',
        moduleId: '',
        radio: 1
      },
      updateModuleResult: '',
      // 删除终端
      delTerminalForm: {
        terminalId: ''
      },
      deleteTerminalResult: '',
      // 修改模块/过滤信息
      modifyModuleForm: {
        objectType: 1,
        objectId: '',
        operateType: 1,
        numbers: 0,
        moduleId: ''
      },
      modifyModuleResult: ''
    }
  },
  computed: {
  },
  created() {
    this.resetSerialForm()
    // this.listenRegisterBySerial()
  },
  methods: {
    // 刷新注册信息
    reloadRegisterInfo: function(data) {
      getRegisterInfo().then(response => {
        this.updateTime = parseTime(new Date(), 'y-m-d h:i:s')
      })
    },
    // 从内存中读取注册信息
    getSystemInfo: function(data) {
      getInfo().then(response => {
        this.infos = response.data
        this.infos.forEach(item => {
          item.registerData = parseTime(item.registerData, 'y-m-d h:i:s')
          item.updateData = parseTime(item.updateData, 'y-m-d h:i:s')
        })
      })
    },
    // 如果内存中没有系统信息，则加一些测试数据
    putSystemInfo: function(data) {
      putInfo().then(response => {
        this.infos = response.data
        this.infos.forEach(item => {
          item.registerData = parseTime(item.registerData, 'y-m-d h:i:s')
          item.updateData = parseTime(item.updateData, 'y-m-d h:i:s')
        })
      })
    },
    // 通过序列号注册
    submitRegForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.serialForm)
          const that = this
          regBySerial(tempData).then(response => {
            this.$socket.subscribeToAjax(response, 'registerInfo/registerResult', (respond, handle) => {
              handle.close() // 手动关闭订阅
              that.serialRegResult = respond.data
            })
          }
          )
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    resetSerialForm() {
      this.serialForm = Object.assign({}, this.defaultSerialForm)
    },
    // 通过文件注册
    submitUpload() {
      const formData = new FormData()
      const uploadFiles = this.$refs.upload.uploadFiles
      formData.append('regFile', uploadFiles[0].raw)
      regByFile(formData).then(response => {
        const that = this
        this.$socket.subscribeToAjax(response, 'registerInfo/registerResult', (respond, handle) => {
          handle.close()
          that.fileRegResult = respond.data
        })
      })
    },
    onExceed(file, fileList) {
      alert('只能上传一个文件')
    },
    // 查看可分配模块信息
    reloadModuleInfo: function(data) {
      getModuleInfo().then(response => {
        const that = this
        this.$socket.subscribeToAjax(response, 'moduleInfo/registerResult', (respond, handle) => {
          handle.close()
          that.modules = respond.data.moduleInfos
          that.moduleNumbers = respond.data.moduleNums
        })
      })
    },
    // 查看终端已分配模块信息
    getTerminalModuleInfo(formName) {
      const formData = Object.assign({}, this.terminalModuleForm)
      getTerminalModuleInfoByNo(formData).then(response => {
        const that = this
        this.$socket.subscribeToAjax(response, 'getTerminalModuleInfo/registerResult', (respond, handle) => {
          handle.close() // 手动关闭订阅
          that.terminalModuleInfoResult = respond.data
        })
      })
    },
    // 修改终端可分配模块信息
    updateModuleInfo(formName) {
      const formData = Object.assign({}, this.moduleForm)
      updateTerminalModules(formData).then(response => {
        const that = this
        this.$socket.subscribeToAjax(response, 'updateTerminal/registerResult', (respond, handle) => {
          handle.close()
          that.updateModuleResult = respond.data
        })
      })
    },
    // 删除终端
    deleteTerminal(formName) {
      const formData = Object.assign({}, this.delTerminalForm)
      delTerminalByNo(formData).then(response => {
        const that = this
        this.$socket.subscribeToAjax(response, 'delTerminal/registerResult', (respond, handle) => {
          handle.close() // 手动关闭订阅
          that.deleteTerminalResult = respond.data
        })
      })
    },
    modifyModuleInfo(formName) {
      const formData = Object.assign({}, this.modifyModuleForm)
      modifyModules(formData).then(response => {
        const that = this
        this.$socket.subscribeToAjax(response, 'modifyModules/registerResult', (respond, handle) => {
          handle.close() // 手动关闭订阅
          that.modifyModuleResult = respond.data
        }, true)
      })
    },
    addDog() {
      addDogMemory().then(response => {
        // console.log(response)
        alert(response.data)
      })
    },
    subDog() {
      subDogMemory().then(response => {
        alert(response.data)
      })
    },
    listenRegisterBySerial() {
      const that = this
      this.$socket.subscribeToUser('test', '/registerResult', function(response) {
        that.serialNoResult = response.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
