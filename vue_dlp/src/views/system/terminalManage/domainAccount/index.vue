<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <span class="add-type">
          {{ $t('pages.domainAccountTip') }}
        </span>
        <div class="searchCon">
          <el-input v-model="query.account" v-trim clearable :placeholder="$t('table.account')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="accountList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="90px" style="width: 500px;">
        <FormItem :label="$t('pages.makePackage_text5')" prop="account">
          <el-input v-model="temp.account" style="width: calc(100% - 20px);" :maxlength="60"></el-input>
          <el-tooltip effect="dark" placement="bottom-end">
            <div slot="content">
              {{ $t('pages.makePackage_text6') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('table.password')" encrypt prop="password">
          <encrypt-input v-model="temp.password" style="width: calc(100% - 20px);" maxlength="20" clearable show-password @input.native="inputPwd($event)"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPage, add, deleteAccount, update, getByAccount } from '@/api/system/terminalManage/domainAccount'
import EncryptInput from '@/components/EncryptInput'

export default {
  name: 'DomainAccount',
  components: { EncryptInput },
  data() {
    return {
      colModel: [
        { prop: 'account', label: this.$t('pages.makePackage_text5'), width: '100', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right', hidden: !this.hasPermission('105'),
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        account: ''
      },
      dialogStatus: '',
      textMap: {
        create: this.$t('pages.addDomainAccount'),
        update: this.$t('pages.updateDomainAccount')
      },
      hasInputPwd: false,
      editable: true,
      deleteable: false,
      temp: { },
      defaultTemp: {
        id: undefined,
        account: '',
        password: ''
      },
      dialogFormVisible: false,
      rules: {
        account: [{ required: true, trigger: 'blur', validator: this.accountValidator }],
        password: [
          { required: true, message: this.$t('pages.pwdCantNull'), trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['accountList']
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.hasInputPwd = false
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, this.defaultTemp, row)
      this.hasInputPwd = false
      this.temp.password = '      '
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          add(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          update(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteAccount({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
      })
    },
    inputPwd($event) {
      if (!this.hasInputPwd) {
        $event.target.value = $event.data || ''
      }
      this.hasInputPwd = true
      this.temp.password = $event.target.value
    },
    accountValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterAccount')))
      } else {
        getByAccount({ account: value }).then(respond => {
          const domainAccount = respond.data
          if (domainAccount && domainAccount.id !== this.temp.id) {
            callback(new Error(this.$t('pages.validateMsg_sameAccount')))
          } else {
            callback()
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .add-type {
    position: relative;
    left: 16px;
    font-size: 13px;
    color: #e41b1b;
  }
</style>
