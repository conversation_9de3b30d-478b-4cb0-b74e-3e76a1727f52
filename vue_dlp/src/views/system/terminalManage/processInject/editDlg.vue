<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('pages.controlledInjectionSet')"
    :stg-code="148"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createData"
    :update="updateData"
    :get-by-name="getByName"
    :format-row-data="formatRowData"
    :format-form-data="formatFormData"
    :validate-form="validateFormData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
  >
    <template :slot="slotName">
      <el-card class="box-card" :body-style="{'padding': '0'}">
        <div slot="header">
          <span>{{ $t('pages.controlledInjectionPro') }}</span>
          <span style="color:red">{{ validMsg }}</span>
          <div v-if="formable" class="btn-box" style="display: flex">
            <el-button size="small" @click="createItem">{{ $t('button.insert') }}</el-button>
            <el-upload
              ref="upload"
              accept=".cfg"
              class="upload-demo"
              name="uploadFile"
              action="aaaaaa"
              :show-file-list="false"
              :on-change="beforeUpload"
              :file-list="fileList"
              :auto-upload="false"
            >
              <el-button size="small" :loading="uploadLoading" style="padding: 6px; margin: 0 0 5px 10px">{{ '导入' }}</el-button>
            </el-upload>
            <common-downloader
              :loading="submitting"
              :name="downloadFileName"
              button-size="small"
              button-icon=""
              :button-name="$t('button.export')"
              :before-download="beforeDownload"
              @download="decodeFile"
            />
            <el-button size="small" :disabled="!itemDeleteable" @click="deleteItem">{{ $t('button.delete') }}</el-button>
          </div>
        </div>
        <grid-table
          ref="itemList"
          :height="220"
          :show-pager="false"
          row-no-label=" "
          :multi-select="formable"
          :col-model="itemColModel"
          :row-datas="temp.option"
          @selectionChangeEnd="itemSelectionChange"
        />
      </el-card>
      <p style="color: blue;margin-bottom: 0;">
        {{ $t('pages.beCareful') }}：<br/>
        {{ $t('pages.processInject_Content1') }}<br/>
        {{ $t('pages.processInject_Content2') }}
      </p>
    </template>
  </stg-dialog>
</template>

<script>
import { createData, exportFile, getByName, importFile, updateData } from '@/api/system/terminalManage/processInject'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'ProcessInjectStgDlg',
  components: { CommonDownloader },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      itemColModel: [
        { prop: 'processName', label: 'processName', type: this.formable ? 'input' : 'text', width: '30' },
        { prop: 'config', label: 'parameterSetting1', type: 'select', disabled: !this.formable, width: '30',
          options: [
            { label: this.$t('pages.conventional'), value: 0 },
            { label: this.$t('pages.newInjectionMode'), value: 1 },
            { label: this.$t('pages.filter'), value: 2 }
          ] },
        { prop: 'hookMode', label: 'parameterSetting2', type: 'select', disabled: !this.formable, width: '30', options: [
          { label: this.$t('pages.conventional'), value: 0 },
          { label: this.$t('pages.filter'), value: 1 }
        ] },
        { prop: 'remark', label: 'remark', type: this.formable ? 'input' : 'text', width: '30' }
      ],
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        remark: '',
        entityType: '',
        entityId: undefined,
        option: []
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.stgNameRequired'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      itemDeleteable: false,
      submitting: false,
      slotName: undefined,
      validMsg: '',
      fileList: [],
      uploadLoading: false
    }
  },
  computed: {
    downloadFileName() {
      return '进程高级管控.cfg'
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    createData,
    updateData,
    getByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleFilter() {
    },
    handleDrag() {
    },
    handleCreate() {
      this.validMsg = ''
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate: function(row) {
      this.validMsg = ''
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {
      rowData.option.forEach((item, index) => {
        item.id = index
        //  兼容旧策略
        item.hookType = 1
        item.hookOS = 0
        item.hookMode = !item.hookMode ? 0 : item.hookMode
      })
    },
    formatFormData(formData) {
      formData.option.forEach(item => {
        delete item.id
      })
    },
    validateFormData(formData) {
      this.validMsg = ''
      if (formData.option.length === 0) {
        this.validMsg = this.$t('pages.process_Validate1')
      }
      const names = []
      for (let i = 0; this.validMsg.length === 0 && i < formData.option.length; i++) {
        const rowData = formData.option[i]
        if (!rowData.processName) {
          this.validMsg += this.$t('pages.process_Validate2', { row: i + 1 })
        } else if (names.indexOf(rowData.processName) >= 0) {
          this.validMsg += this.$t('pages.process_Validate3', { row: i + 1 })
        } else {
          names.push(rowData.processName)
        }
      }
      return this.validMsg.length === 0
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    createItem() {
      this.validMsg = ''
      let tempData = null
      if (this.temp.option && this.temp.option.length > 0) {
        tempData = this.temp.option[0]
      }
      if (!tempData || tempData.processName) {
        this.temp.option.splice(0, 0, {
          id: new Date().getTime(),
          processName: '',
          config: 0,
          remark: '',
          filter: 0,
          hookType: 1,  //  注入类型： 1：消息钩子
          hookOS: 0,  //  适用操作系统  0：所有操作系统生效
          hookMode: 0 // 注入模式  0:启动/常规， 1 过滤
        })
      }
    },
    deleteItem() {
      this.validMsg = ''
      const toDeleteIds = this.$refs['itemList'].getSelectedIds()
      for (let i = 0; i < this.temp.option.length; i++) {
        const item = this.temp.option[i]
        if (toDeleteIds.indexOf(item.id) > -1) {
          this.temp.option.splice(i, 1)
          i--
        }
      }
    },
    itemSelectionChange(rowDatas) {
      this.itemDeleteable = rowDatas.length > 0
    },
    processNameValidator(rule, value, callback) {
      const size = this.temp.encType.length
      for (let i = 0; i < size; i++) {
        const item = this.temp.encType[i]
        if (item.processName === value && (this.configBtnMode === 'add' || item.id !== this.encTypeTemp.id)) {
          callback(new Error(this.$t('pages.process_Validate4')))
          return
        }
      }
      callback()
    },
    beforeDownload() {
      const list = this.temp.option || []
      if (!list.length) {
        this.$notify({
          title: this.$t('text.fail'),
          message: '没有配置项',
          type: 'fail',
          duration: 2000
        })
        return false;
      }
      return true;
    },
    decodeFile(file) {
      this.submitting = true
      const list = this.temp.option || []
      const opts = { file, jwt: true, topic: 'ProcessInjectStg' }
      exportFile({ options: list, name: file.name }, opts).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: '导出成功',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.$notify({
          title: this.$t('text.fail'),
          message: '导出失败',
          type: 'fail',
          duration: 2000
        })
      }).finally(() => {
        this.submitting = false
      })
    },
    /**
     * 导入CFG文件
     * @param file
     * @returns {boolean}
     */
    beforeUpload(file) {
      this.uploadLoading = true
      const fd = new FormData()
      fd.append('file', file.raw)// 传文件
      importFile(fd).then(res => {
        if (res.data && res.data.length) {
          let id = new Date().getTime();
          res.data.forEach(item => {
            this.temp.option.push({
              id: id++,
              processName: item.processName,
              config: item.config,
              remark: item.remark,
              filter: 0,
              hookType: 1,  //  注入类型： 1：消息钩子
              hookOS: 0,  //  适用操作系统  0：所有操作系统生效
              hookMode: item.hookMode // 注入模式  0:启动/常规， 1 过滤
            })
          })
        }
        this.$notify({
          title: this.$t('text.success'),
          message: '导入CFG文件成功',
          type: 'success',
          duration: 2000
        })
      }).catch(e => {
        console.error('导入CFG文件失败', e)
        this.$notify({
          title: this.$t('text.success'),
          message: '导入CFG文件失败',
          type: 'error',
          duration: 2000
        })
      }).finally(_ => {
        this.uploadLoading = false
      })
      return false
    }
  }
}
</script>
<style lang="scss" scoped>
.box-card{
  position: relative;
  >>>.el-card__header{
    padding: 10px 20px;
  }
}
.btn-box{
  position: absolute;
  top: 5px;
  right: 20px;
}
</style>
