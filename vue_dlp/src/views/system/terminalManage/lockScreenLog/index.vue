<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.lockType" :value="query.lockType">
          <span>{{ $t('pages.lockLog') }}</span>
          <el-select v-model="query.lockType" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in resultOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'307'" :request="exportFunc"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'417'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="lockScreenLogList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="$store.getters.auditingDeleteAble && hasPermission('417')" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.lockScreenLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.lockType')">
            {{ lockTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.lockStyle')">
            {{ lockStyleFormatter(rowDetail) }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getPage, exportLockScreenLog, deleteLog } from '@/api/behaviorAuditing/terminal/lockScreenLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'LockScreenLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'lockType', label: 'lockType', width: '100', formatter: this.lockTypeFormatter },
        { prop: 'lockStyle', label: 'lockStyle', width: '200', formatter: this.lockStyleFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('261'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        lockType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'lockScreenLogList'
      },
      resultOptions: {
        1: this.$t('pages.lockLogText7'), 2: this.$t('pages.lockLogText8')
      },
      showTree: true,
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      queryVideoMethod: undefined,
      selection: [],
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['lockScreenLogList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    lockTypeFormatter: function(row, data) {
      let lockType = '';
      if (row.lockType === 1) {
        lockType = this.$t('pages.lockLogText7')
      } else if (row.lockType === 2) {
        lockType = this.$t('pages.lockLogText8')
      }
      return lockType
    },
    lockStyleFormatter: function(row, data) {
      let lockStyle = ''
      if (row.lockType === 1 && row.lockStyle === 1) {
        lockStyle = this.$t('pages.lockLogText1')
      } else if (row.lockType === 1 && row.lockStyle === 2) {
        lockStyle = this.$t('pages.lockLogText2')
      } else if (row.lockType === 1 && row.lockStyle === 3) {
        lockStyle = this.$t('pages.lockLogText3')
      } else if (row.lockType === 2 && row.lockStyle === 1) {
        lockStyle = this.$t('pages.lockLogText4')
      } else if (row.lockType === 2 && row.lockStyle === 2) {
        lockStyle = this.$t('pages.lockLogText5')
      } else if (row.lockType === 2 && row.lockStyle === 3) {
        lockStyle = this.$t('pages.lockLogText6')
      } else if (row.lockType === 2 && row.lockStyle === 4) {
        lockStyle = this.$t('pages.lockLogText9')
      }
      return lockStyle
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportLockScreenLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '261', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
