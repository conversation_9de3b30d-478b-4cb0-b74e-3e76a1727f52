<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.approvalResult" :value="query.approvalResult">
          <span>{{ $t('pages.approvalResult') }}:</span>
          <el-select v-model="query.approvalResult" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in resultOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.name" :value="query.name">
          <span>{{ $t('table.softwareName') }}:</span>
          <el-input v-model="query.name" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'878'" :request="handleExport"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'879'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-if="showDel" slot="append" size="mini" @click="handleDelSoft">
          {{ $t('pages.logSoftDel') }}
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">{{ $t('pages.logSoftDelTip') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <el-button v-if="showPull" slot="append" size="mini" @click="handlePullSoft">
          {{ $t('pages.logSoftPull') }}
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">{{ $t('pages.logSoftPullTip') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('879')"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.usbApprovalAccessLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.approvalTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.name')">
            <div v-html="nameFormatter(rowDetail, rowDetail.name)">
            </div>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.size')">
            {{ fileSizeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.mainExe')">
            {{ rowDetail.mainExe }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.architecture')">
            {{ archFormatter(rowDetail,rowDetail.architecture) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.supportSystem')">
            {{ osVerFormatter(rowDetail,rowDetail.minOsVer) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.softwareVendor')">
            {{ rowDetail.manufacturer }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.softwareCopyright')">
            {{ rowDetail.copyright }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.softwareDescription')">
            {{ rowDetail.description }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.releaseNote')">
            {{ rowDetail.releaseNote }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.installParam')">
            {{ rowDetail.installParam }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.upgradeParam')">
            {{ rowDetail.upgradeParam }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.approvalResult')">
            {{ resultFormatter(rowDetail) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- <video-viewer ref="videoViewer"/> -->
  </div>
</template>

<script>
import { getPage, exportLog, deleteLog, pullSoftware, deleteSoftware } from '@/api/behaviorAuditing/terminal/approvalSoftwareSubmitLog'
import { enableStgDelete, formatFileSize } from '@/utils'
// import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import {
  supportedPlatforms,
  softwareArchitectures,
  supportedOsVersions,
  uploadSoftwareStatus
} from '@/api/system/terminalManage/softwareRepository'
import { decodeValToArr } from '@/api/dataEncryption/fileTrace/documentTrack'
import { getPropertyByCode } from '@/api/property'

export default {
  name: 'ApprovalSoftwareSubmitLog',
  data() {
    const style = 'width: 16px; height: 16px; vertical-align: text-bottom;'
    return {
      colModel: [
        { prop: 'createTime', label: 'approvalTime', width: '150', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'name', label: 'name', width: '150', formatter: this.nameFormatter },
        { prop: 'version', label: 'version', width: '120' },
        { prop: 'size', label: 'size', width: '85', formatter: this.fileSizeFormatter },
        { prop: 'mainExe', label: 'mainExe', width: '100', custom: true },
        { prop: 'platform', label: 'platform', width: '80', formatter: this.platformFormatter },
        { prop: 'architecture', label: 'architecture', width: '80', formatter: this.archFormatter },
        { prop: 'minOsVer', label: 'supportSystem', width: '100', custom: true, formatter: this.osVerFormatter },
        { prop: 'manufacturer', label: 'softwareVendor', width: '100', custom: true },
        { prop: 'copyright', label: 'softwareCopyright', width: '100', custom: true },
        { prop: 'description', label: 'softwareDescription', width: '100', custom: true },
        { prop: 'releaseNote', label: 'releaseNote', width: '100', custom: true },
        { prop: 'installParam', label: 'installParam', width: '100', custom: true },
        { prop: 'upgradeParam', label: 'upgradeParam', width: '100', custom: true },
        { prop: 'approvalResult', label: 'approvalResult', width: '100', formatter: this.resultFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('877'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        approvalResult: null,
        name: '',
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      resultOptions: {
        0: this.$t('pages.refuse'), 1: this.$t('pages.agree')
      },
      devTypeOptions: {
        0: this.$t('pages.dictionary_Msg19'), 1: this.$t('pages.dictionary_Msg21')
      },
      driverTypeOptions: {
        1: this.$t('pages.driverType1'), 2: this.$t('pages.driverType2')
      },
      showTree: true,
      rowDetail: {},
      dialogFormVisible: false,
      queryVideoMethod: undefined,
      deleteable: false,
      imgStyle: style,
      archMap: {},
      osMap: {},
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      showDel: false,
      showPull: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    // addViewVideoBtn(this)
    softwareArchitectures.forEach(arch => {
      this.archMap[arch.value] = arch.label
    })
    supportedOsVersions.forEach(os => {
      this.osMap[os.value] = os.label
    })
    this.getProperty()
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resultFormatter: function(row, data) {
      let result = '';
      if (row.approvalResult === 0) {
        result = this.$t('pages.accessReject')
      } else {
        result = this.$t('pages.accessAccept')
      }
      return result
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportLog(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      // this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '877')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    nameFormatter(row, data) {
      return `<img src="${row.icon}" style="${this.imgStyle} margin-right: 5px;" alt="ico">${data}`
    },
    fileSizeFormatter(row, data) {
      return formatFileSize(row.size)
    },
    platformFormatter(row, data) {
      let platform
      for (let i = 0; i < supportedPlatforms.length; i++) {
        platform = supportedPlatforms[i]
        if (platform.value === data) {
          return platform.label
        }
      }
      return ''
    },
    archFormatter(row, data) {
      const architectures = decodeValToArr(data, 4)
      return architectures.map(arch => this.archMap[arch]).join(', ')
    },
    osVerFormatter(row, data) {
      if (data === '0') {
        return this.$t('pages.unlimitedSystem')
      }
      return !data ? '' : data.split('|').map(os => this.osMap[os]).join(', ')
    },
    statusFormatter(row, data) {
      const status = uploadSoftwareStatus[data]
      return !status ? 'Unknown status: ' + data : status
    },
    handleDelSoft() {
      this.$confirmBox(this.$t('pages.softDelTip2'), this.$t('text.prompt')).then(() => {
        deleteSoftware().then(() => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(res => {
        })
      }).catch(() => {})  
    },
    handlePullSoft() {
      this.$confirmBox(this.$t('pages.softPullTip2'), this.$t('text.prompt')).then(() => {
        pullSoftware().then(() => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.pullSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(res => {
        })
      }).catch(() => {})
    },
    getProperty() {
      getPropertyByCode('software.deal').then(resp => {
        const value = resp.data.value
        if (value) {
          this.showDel = value.indexOf('1') > -1
          this.showPull = value.indexOf('2') > -1
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .searchCon {
    .el-input {
      width: 200px;
    }
  }
  >>>.category-item {
    margin-top: 2px;
    .category-select {
      width: 320px;
    }
  }
</style>
