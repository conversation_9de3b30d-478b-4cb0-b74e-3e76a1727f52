<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="receiverGroupTree"
        resizeable
        :default-expand-all="true"
        :data="receiverGroupTreeData"
        :render-content="renderContent"
        @node-click="receiverGroupTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('table.roleName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="receiverList"
        :col-model="colModel"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.roleName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="temp.groupId" :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="title"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteUserRoleGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.roleGroup')"
      :group-tree-data="formTreeData"
      :add-func="createUserRoleGroup"
      :update-func="updateUserRoleGroup"
      :delete-func="deleteUserRoleGroup"
      :move-func="moveGroup"
      :edit-valid-func="getUserRoleGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? i18nConcatText(this.$t('pages.roleGroup'), 'update') : i18nConcatText(this.$t('pages.roleInfo'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')">
        <tree-select :data="receiverGroupTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.userManage')"
      :visible.sync="dialogUserVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="userForm" :rules="userRules" :model="tempUser" label-position="right" label-width="100px" style="width: 750px;">
        <el-divider content-position="left"> {{ this.$t('pages.roleBaseInformation') }}</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.roleName') + '：'">
              {{ tempUser.name }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.sourceGroup') + '：'">
              {{ groupFormatter(undefined, tempUser.groupId) }}
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left"> {{ this.$t('pages.roleOperatorSetting') }} </el-divider>
        <grid-select
          ref="userGridSelect"
          :height="350"
          :col-model="userColModel"
          searchable
          pager-small
          :select-table-title="$t('components.optionalUser')"
          :selected-table-title="$t('components.selectedUser')"
          :search-prop="{ key: 'account', label: $t('table.account')}"
          :select-row-data-api="selectUserRowDataApi"
          :selected-row-data-api="selectedUserRowDataApi"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateUser()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogUserVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getGroupTreeNode,
  createUserRoleGroup,
  updateUserRoleGroup,
  deleteUserRoleGroup,
  deleteGroupAndData,
  moveGroupToOther,
  getUserRoleGroupByName,
  countChildByGroupId,
  getUserRolePage,
  createUserRole,
  updateUserRole,
  deleteUserRole,
  moveGroup,
  batchUpdateGroup,
  batchUpdateAllGroup,
  getRoleByName,
  listUsersByRoleId,
  updateRelatedUsers, checkOnlyBindTheRole, insertDefaultOperatorRole
} from '@/api/system/terminalManage/userRole'
import { findNodeLabel } from '@/utils/tree'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import GridSelect from '@/components/GridSelect'
import { getUserPage } from '@/api/system/terminalManage/user'

export default {
  name: 'UserRole',
  components: { BatchEditPageDlg, DeleteGroupDlg, EditGroupDlg, GridSelect },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'roleName', width: '150', sort: 'custom', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'userSize', label: 'user', width: '150', type: 'button',
          buttons: [
            { formatter: this.userSizeFormatter, disabledFormatter: () => !this.hasPermission('B23'), click: this.linkToUserPage }
          ]
        },
        { prop: 'remark', label: 'remark', width: '200', sort: 'custom' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '150',
          buttons: [
            { label: 'edit', disabledFormatter: (row) => row.id === 201, click: this.handleUpdate },
            { label: 'userManage', click: this.handleUser }
          ]
        }
      ],
      // 查询条件
      query: {
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        groupId: undefined,
        type: 0
      },
      formTreeData: [],
      rules: {
        name: [
          { required: true, message: this.$t('text.pleaseEnterInfo', { info: this.$t('table.roleName') }), trigger: 'blur' },
          { validator: this.roleNameValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.roleInfo'), 'update'),
        create: this.i18nConcatText(this.$t('pages.roleInfo'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.roleGroup'), 'delete')
      },
      treeNodeType: 'G',
      receiverGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.userRoleLibrary'), parentId: '', children: [] }],
      treeSelectNode: [],
      title: this.$t('pages.roleInfo'),
      checkedKeys: [],
      updateGroupForm: false,
      // 批量修改的分组id
      updateGroupId: undefined,
      tempUser: {},
      dialogUserVisible: false,
      userColModel: [
        { prop: 'account', label: 'account', width: '100', sort: true },
        { prop: 'name', label: 'name', width: '100', sort: true }
      ],
      userRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['receiverList']
    },
    receiverGroupTree: function() {
      return this.$refs['receiverGroupTree']
    }
  },
  activated() {
    this.loadReceiverTree()
    this.gridTable && this.gridTable.execRowDataApi(this.query)
  },
  created() {
    this.resetTemp()
    this.loadReceiverTree()
  },
  methods: {
    createUserRoleGroup,
    updateUserRoleGroup,
    deleteUserRoleGroup,
    getUserRoleGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getUserRolePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    receiverGroupTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId !== '0'
      if (checkedData) {
        this.query.groupId = checkedData.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    loadReceiverTree: function() {
      getGroupTreeNode().then(respond => {
        respond.data.map(item => {
          item.disabled = true;
        })
        this.receiverGroupTreeData[0].children = respond.data
        console.log('this.receiverGroupTreeData', this.receiverGroupTreeData);
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.receiverGroupTreeData[0].children
      this.treeSelectNode.forEach(node => {
        node.dataId += ''
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.loadReceiverTree()
      // this.receiverGroupTree.addNode(this.dataToTreeNode(data))
    },
    updateNode(data) {
      this.receiverGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.receiverGroupTree.findNode(this.receiverGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.receiverGroupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.receiverGroupTree.findNode(this.receiverGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.receiverGroupTree.setCurrentKey(nodeData.id)
        this.receiverGroupTreeNodeCheckChange(nodeData, {})
      }
    },
    loadTypeTreeExceptRoot() {
      return this.receiverGroupTreeData[0].children
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleMoving() {
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp.groupId = String(this.query.groupId)
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.changeTreeSelectNode()
      this.temp = JSON.parse(JSON.stringify(row))
      this.temp.groupId += ''
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createUserRole(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateUserRole(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId !== '0'
      const iconEDShow = iconShow && data.dataId !== '1';
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={iconEDShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={iconEDShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.receiverGroupTreeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId === String(dataId)) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteUserRole(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback('cancel') })
      }).catch(e => { callback(e) })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    roleNameValidator(rule, value, callback) {
      getRoleByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    userSizeFormatter(row, data) {
      return row.userSize
    },
    linkToUserPage(row) {
      const url = '/terminalManage/terminalManage/user'
      const queryTemp = { userRoleId: row.id }
      this.$router.push({ path: url, query: queryTemp })
    },
    handleUser(row) {
      this.tempUser = Object.assign({}, row)
      this.dialogUserVisible = true
      if (this.userSelect()) {
        this.userSelect().clearFilter()
        this.userSelect().reload()
      }
      this.$nextTick(() => {
        this.$refs['userForm'].clearValidate()
      })
    },
    userSelect() {
      return this.$refs['userGridSelect']
    },
    selectUserRowDataApi(option) { // 可选操作员
      return getUserPage(option)
    },
    async selectedUserRowDataApi(option) { // 已选操作员
      const selectedUsers = await listUsersByRoleId({ roleId: this.tempUser.id })
      return selectedUsers
    },
    updateUser() {
      this.submitting = true
      const userIds = this.userSelect().getSelectedIds()
      const tempData = Object.assign({}, this.tempUser)
      checkOnlyBindTheRole({ userIds, roleId: tempData.id }).then((respond) => {
        const noRoleAccounts = respond.data.noRoleAccounts
        const noRoleUserIds = respond.data.noRoleUserIds
        if (noRoleAccounts && noRoleAccounts.length > 0) {
          this.$confirmBox(this.$t('pages.userRoleUnbindConfirmBoxMsg', { userAccounts: noRoleAccounts.join(',') }), this.$t('text.prompt')).then(() => {
            updateRelatedUsers({ roleId: tempData.id, userIds: userIds }).then(respond => {
              insertDefaultOperatorRole({ userIds: noRoleUserIds }).then(respond => {
                this.submitting = false
                this.dialogUserVisible = false
                this.gridTable.execRowDataApi()
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.updateSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(() => { this.submitting = false })
            }).catch(() => { this.submitting = false })
          }).catch(() => { this.submitting = false })
        } else {
          updateRelatedUsers({ roleId: tempData.id, userIds: userIds }).then(respond => {
            this.submitting = false
            this.dialogUserVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => { this.submitting = false })
        }
      }).catch(() => { this.submitting = false })
    },
    selectable(row, index) {
      return !(row.id === 201 && row.nameKey === 'pages.employee');
    }
  }
}
</script>
