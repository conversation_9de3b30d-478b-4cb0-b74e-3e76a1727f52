<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="terminalMenuList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :title="textMap[dialogStatus]"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" class="module-form" label-position="right" label-width="80px" style="width: 750px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('components.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-tabs v-model="activeName" type="card" class="process-tab">
          <el-tab-pane :label="$t('route.terminalMenu')" name="first">
            <el-card class="box-card" :body-style="{'padding': '10px', 'margin-left': '25px' }">
              <div slot="header" class="clearfix">
                <span>{{ $t('pages.term_menu_msg1') }}</span>
              </div>
              <el-button v-if="formable" type="text" style="padding-left: 0" @click="selectAll(true, 1)">{{ $t('button.selectAll') }}</el-button>
              <el-button v-if="formable" type="text" @click="selectAll(false, 1)">{{ $t('button.cancelSelectAll') }}</el-button>
              <el-checkbox-group v-model="menusData.checked" :disabled="!formable">
                <el-row>
                  <el-col :span="12">
                    <div v-for="(item, index) in menusData.leftOptions" :key="index">
                      <el-checkbox v-if="item.enabled" :key="item.menuId" :label="item.menuId" :disabled="!item.enabled">
                        {{ item.menuName }}
                      </el-checkbox>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div v-for="(item, index) in menusData.rightOptions" :key="index">
                      <el-checkbox v-if="item.enabled" :key="item.menuId" :label="item.menuId" :disabled="!item.enabled">
                        {{ item.menuName }}
                      </el-checkbox>
                    </div>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </el-card>
            <el-card class="box-card" :body-style="{'padding': '10px', 'margin-left': '25px' }">
              <div slot="header" class="clearfix">
                <span>{{ $t('pages.term_menu_msg2') }}</span>
              </div>
              <el-button v-if="formable" type="text" style="padding-left: 0" @click="selectAll(true, 2)">{{ $t('button.selectAll') }}</el-button>
              <el-button v-if="formable" type="text" @click="selectAll(false, 2)">{{ $t('button.cancelSelectAll') }}</el-button>
              <el-checkbox-group v-model="menusData.openChecked" :disabled="!formable">
                <el-row>
                  <el-col :span="12">
                    <div v-for="(item, index) in menusData.openCheckLeftOptions" :key="index">
                      <el-checkbox v-if="item.enabled" :key="item.menuId" :label="item.menuId" :disabled="!item.enabled">
                        {{ item.menuName }}
                      </el-checkbox>
                    </div>
                  </el-col>
                  <!--右侧列数据-->
                  <el-col :span="12">
                    <div v-for="(item, index) in menusData.openCheckRightOptions" :key="index">
                      <el-checkbox v-if="item.enabled" :key="item.menuId" :label="item.menuId" :disabled="!item.enabled" @change="openChange($event, item)">
                        {{ item.menuName }}
                      </el-checkbox>
                    </div>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </el-card>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.shortcutManagement')" name="second">
            <el-card class="box-card" :body-style="{'padding': '10px', 'margin-left': '25px' }">
              <div slot="header" class="clearfix">
                {{ $t('pages.genShortcut') }}
                <el-tooltip effect="dark" placement="right">
                  <div slot="content">{{ $t('pages.shortcutManagementTip') }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </div>
              <el-button v-if="formable" type="text" style="padding-left: 0" @click="selectAll(true, 3)">{{ $t('button.selectAll') }}</el-button>
              <el-button v-if="formable" type="text" @click="selectAll(false, 3)">{{ $t('button.cancelSelectAll') }}</el-button>
              <el-checkbox-group v-model="menusData.shortcutChecked" :disabled="!formable">
                <el-row>
                  <el-col :span="12">
                    <div v-for="(item, index) in menusData.shortcutLeftOptions" :key="index">
                      <el-checkbox v-if="item.enabled" :key="item.menuId" :label="item.menuId" :disabled="isDisabled(item.menuId)">
                        {{ item.menuName }}
                      </el-checkbox>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div v-for="(item, index) in menusData.shortcutRightOptions" :key="index">
                      <el-checkbox v-if="item.enabled" :key="item.menuId" :label="item.menuId" :disabled="isDisabled(item.menuId)">
                        {{ item.menuName }}
                      </el-checkbox>
                    </div>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importTerminalMenuStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="false"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { getStrategyPage, getByName, createStrategy, updateStrategy, deleteStrategy } from '@/api/system/terminalManage/terminalMenu'
import { listTerminalByGroupId } from '@/api/system/terminalManage/terminal'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { getArmMode } from '@/api/system/register/reg'

export default {
  name: 'TerminalMenu',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 115,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'stgMessage', width: '200', formatter: this.menuFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      menusData: {
        name: 'terminalMenu',
        label: this.$t('pages.terminalManage'),
        checked: [], // 默认显示项选中节点
        defaultChecked: [], // 默认选中节点
        openChecked: [], // 默认隐藏项选中节点
        shortcutChecked: [], // 生成快捷方式选项选中节点
        leftOptions: [ // 顺序需要跟终端菜单的顺序保持一致
          { menuId: 10001, menuName: this.$t('pages.offlineStrategy'), moduleId: 1, enabled: true },
          { menuId: 10002, menuName: this.$t('pages.offBatchEncryption'), moduleIds: [51, 151, 251], enabled: true },
          { menuId: 10003, menuName: this.$t('pages.offBatchDecryption'), moduleIds: [51, 151, 251], enabled: true },
          { menuId: 10004, menuName: this.$t('pages.offBatchConversion'), moduleIds: [51, 151, 251], enabled: true },
          { menuId: 10015, menuName: this.$t('pages.offDiscBurning'), moduleId: 18, enabled: true },
          { menuId: 10005, menuName: this.$t('pages.offOutgoingAuthorizationCode'), moduleId: 52, enabled: true },
          { menuId: 10006, menuName: this.$t('pages.offOutgoingFiles'), moduleId: 52, enabled: true },
          { menuId: 10021, menuName: this.$t('pages.offApplicationAndApproval'), moduleIds: [51, 151, 251, 52, 152, 252, 80, 180, 280], enabled: true },
          { menuId: 10007, menuName: this.$t('pages.offApplicationBatchDecryption'), moduleIds: [51, 151, 251], enabled: true },
          { menuId: 10022, menuName: this.$t('pages.offAlarmRecordQuery'), moduleId: 1, enabled: true },
          // { menuId: 10010, menuName: '关闭打印外发（制作打印外发文件）' },
          // { menuId: 10012, menuName: '关闭申请打印' },
          // { menuId: 10013, menuName: '关闭申请阅读权限转换' },
          // { menuId: 10014, menuName: '关闭申请直接外发' },
          { menuId: 10024, menuName: this.$t('pages.offSearchEncryptedFiles'), moduleId: 51, enabled: true },
          { menuId: 10045, menuName: this.$t('pages.offAddDocumentWatermark'), moduleId: 54, enabled: true },
          // { menuId: 10026, menuName: this.$t('pages.offRemoveInternetRestrictions'), moduleId: 1, enabled: true },
          { menuId: 10030, menuName: this.$t('pages.offRestartTerminal'), moduleId: 1, enabled: true },
          { menuId: 10032, menuName: this.$t('pages.offCancellation'), moduleId: 1, enabled: true },
          { menuId: 10034, menuName: this.$t('pages.offDiskScanSelfCheck'), moduleId: 80, enabled: true },
          { menuId: 10036, menuName: this.$t('pages.offFilePasswordValut'), moduleId: 80, enabled: true },
          { menuId: 10037, menuName: this.$t('pages.offAppBehaviorControl'), moduleIds: [16, 116, 216], enabled: true },  //  申请关闭行为管控
          { menuId: 10040, menuName: this.$t('pages.shutTermSecurityDetection'), moduleIds: [21, 121, 221], enabled: true }, // 关闭终端安全检测
          { menuId: 10042, menuName: this.$t('pages.offTermTaskQuery'), moduleId: [13], enabled: true }, // 关闭终端任务查询
          { menuId: 10049, menuName: this.$t('pages.offLangConfig'), moduleId: 1, enabled: true }, // 关闭语言设置
          { menuId: 10050, menuName: '关闭查看标签', moduleId: 24, enabled: true } // 关闭语言设置
        ],
        rightOptions: [
          { menuId: 10008, menuName: this.$t('pages.offApplicationOffline'), moduleIds: [51, 151, 251], enabled: true },
          { menuId: 10011, menuName: this.$t('pages.offDirectOutgoing'), moduleId: 52, enabled: true },
          { menuId: 10009, menuName: this.$t('pages.offApplyClassification'), moduleIds: [51, 151, 251], enabled: true },
          { menuId: 10016, menuName: this.$t('pages.offUsbRegistrationCertification'), moduleIds: [16, 116, 216], enabled: true },
          { menuId: 10017, menuName: this.$t('pages.offSystemConfig'), moduleId: 1, enabled: true },
          { menuId: 10019, menuName: this.$t('pages.offLiveLog'), moduleId: 1, enabled: true },
          { menuId: 10020, menuName: this.$t('pages.offSystemStatus'), moduleId: 1, enabled: true },
          { menuId: 10018, menuName: this.$t('pages.offSystemMaintain'), moduleId: 1, enabled: true },
          { menuId: 10023, menuName: this.$t('pages.offScreenshot'), moduleId: 1, enabled: true },
          { menuId: 10025, menuName: this.$t('pages.offWorkingMode'), moduleId: 51, enabled: true },
          { menuId: 10027, menuName: this.$t('pages.offRequestCancelDocumentWatermark'), moduleId: 54, enabled: true },
          { menuId: 10046, menuName: this.$t('pages.offCancelDocumentWatermark'), moduleId: 54, enabled: true },
          { menuId: 10028, menuName: this.$t('pages.offApplyDistributionSensitiveDocuments'), moduleIds: [16, 17, 18, 19, 31, 32, 34, 36, 80], enabled: true },
          { menuId: 10029, menuName: this.$t('pages.offApplyPrint'), moduleId: 35, enabled: true },
          { menuId: 10033, menuName: this.$t('pages.offUpdatePwd'), moduleId: 1, enabled: true },
          { menuId: 10035, menuName: this.$t('pages.offNac'), moduleId: 1, enabled: true },
          { menuId: 10038, menuName: this.$t('pages.offSoftwareManager'), moduleId: 20, enabled: true }, // 关闭软件管家
          { menuId: 10041, menuName: this.$t('pages.offFileRelieveJurisdiction'), moduleIds: [51, 151, 251, 351], enabled: true }, // 关闭阅读权限转换
          { menuId: 10047, menuName: this.$t('pages.offAddTag'), moduleId: 24, enabled: true }, // 关闭手动加标签
          { menuId: 10048, menuName: this.$t('pages.offExternalReader'), moduleId: 52, enabled: true } // 关闭安装/卸载外发阅读器
        ],
        openCheckLeftOptions: [
          { menuId: 10031, menuName: this.$t('pages.openDocumentTrack'), moduleId: 54, enabled: true },
          { menuId: 10043, menuName: this.$t('pages.openProactiveBackup'), moduleIds: [23, 123, 223], enabled: true }
        ],
        openCheckRightOptions: [
          { menuId: 10039, menuName: this.$t('pages.openTerminalUninstall'), moduleId: 1, enabled: true },
          { menuId: 10044, menuName: this.$t('pages.openTheBackupList'), moduleIds: [23, 123, 223], enabled: true }
        ],
        shortcutLeftOptions: [
          { menuId: 20001, menuName: this.$t('route.softwareManager'), moduleId: 20, enabled: true },
          { menuId: 20002, menuName: this.$t('route.termSecurityDetection'), moduleId: 21, enabled: true },
          { menuId: 20003, menuName: this.$t('pages.discBurn'), moduleId: 18, enabled: true },
          { menuId: 20004, menuName: this.$t('pages.sensitiveFileSelfInspection'), moduleId: 80, enabled: true },
          { menuId: 20005, menuName: this.$t('pages.applicationAndApproval'), moduleIds: [51, 151, 251, 52, 152, 252, 80, 180, 280], enabled: true }
        ],
        shortcutRightOptions: [
          { menuId: 20006, menuName: this.$t('pages.usbRegistrationCertification'), moduleIds: [16, 116, 216], enabled: true },
          { menuId: 20007, menuName: this.$t('pages.alarmRecordQuery'), moduleId: 1, enabled: true },
          { menuId: 20008, menuName: this.$t('pages.hotkeyOption5'), moduleId: 1, enabled: true },
          { menuId: 20009, menuName: this.$t('pages.filePasswordLibrary'), moduleId: 80, enabled: true },
          { menuId: 20010, menuName: this.$t('pages.searchEncryptedFiles'), moduleId: 51, enabled: true }
        ]
      },
      rowData: [],
      terminals: [],
      terminalName: '',
      tempChecked: [],
      checkedTerminalId: undefined,
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        limit: 20
      },
      temp: {},
      defaultTemp: { // 表单字段
        objectType: 1,
        objectId: undefined,
        active: false,
        remark: '',
        menuIds: []
      },
      editable: false,
      treeable: true,
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.terminalMenuStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.terminalMenuStg'), 'create')
      },
      dialogStatus: '',
      dialogFormVisible: false,
      submitting: false,
      tableLoading: false,
      moduleIds: [],
      // 是否国产版本
      isArmMode: false,
      activeName: 'first',
      closeMenuShortcutMenuMapping: { 10038: 20001, 10040: 20002, 10015: 20003, 10034: 20004, 10021: 20005, 10016: 20006, 10022: 20007, 10023: 20008, 10036: 20009, 10024: 20010 }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['terminalMenuList']
    }
  },
  watch: {
    'menusData.checked'(newValue, oldValue) {
      const needUncheckOfShortcutMenuIds = newValue.map(menuId => this.closeMenuShortcutMenuMapping[menuId]).filter(menuId => menuId !== undefined)
      for (let i = 0; i < needUncheckOfShortcutMenuIds.length; i++) {
        const index = this.menusData.shortcutChecked.indexOf(needUncheckOfShortcutMenuIds[i])
        if (index > -1) {
          this.menusData.shortcutChecked.splice(index, 1)
        }
      }
    }
  },
  created() {
    this.resetTemp()
    this.initArmMode();
    this.listModule()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {},
  methods: {
    async listModule() {
      await listSaleModuleId().then(resp => {
        this.moduleIds.splice(0, this.moduleIds.length, ...resp.data)
      })
      this.menusData.leftOptions.forEach(option => {
        if (option.moduleId && !this.isInArray(this.moduleIds, option.moduleId)) {
          option.enabled = false
          this.menusData.defaultChecked.push(option.menuId)
        } else if (option.moduleIds) {
          const moduleIds = option.moduleIds.filter(moduleId => this.isInArray(this.moduleIds, moduleId))
          if (!moduleIds || moduleIds.length === 0) {
            option.enabled = false
            this.menusData.defaultChecked.push(option.menuId)
          }
        }
      })
      if (this.isArmMode) {
        // 若是国产环境，屏蔽nac网络准入
        this.menusData.rightOptions.forEach(item => {
          if (item.menuId === 10035) {
            item.enabled = false;
          }
        });
      }
      this.menusData.rightOptions.forEach(option => {
        if (option.moduleId && !this.isInArray(this.moduleIds, option.moduleId)) {
          option.enabled = false
          this.menusData.defaultChecked.push(option.menuId)
        } else if (option.moduleIds) {
          const moduleIds = option.moduleIds.filter(moduleId => this.isInArray(this.moduleIds, moduleId))
          if (!moduleIds || moduleIds.length === 0) {
            option.enabled = false
            this.menusData.defaultChecked.push(option.menuId)
          }
        }
      })
      this.menusData.openCheckLeftOptions.forEach(option => {
        if (option.moduleId && !this.isInArray(this.moduleIds, option.moduleId)) {
          option.enabled = false
          this.menusData.defaultChecked.push(option.menuId)
        } else if (option.moduleIds) {
          const moduleIds = option.moduleIds.filter(moduleId => this.isInArray(this.moduleIds, moduleId))
          if (!moduleIds || moduleIds.length === 0) {
            option.enabled = false
            this.menusData.defaultChecked.push(option.menuId)
          }
        }
      })
      this.menusData.openCheckRightOptions.forEach(option => {
        if (option.moduleId && !this.isInArray(this.moduleIds, option.moduleId)) {
          option.enabled = false
          this.menusData.defaultChecked.push(option.menuId)
        } else if (option.moduleIds) {
          const moduleIds = option.moduleIds.filter(moduleId => this.isInArray(this.moduleIds, moduleId))
          if (!moduleIds || moduleIds.length === 0) {
            option.enabled = false
            this.menusData.defaultChecked.push(option.menuId)
          }
        }
      })
      this.menusData.shortcutLeftOptions.forEach(option => {
        if (option.moduleId && !this.isInArray(this.moduleIds, option.moduleId)) {
          option.enabled = false
        } else if (option.moduleIds) {
          const moduleIds = option.moduleIds.filter(moduleId => this.isInArray(this.moduleIds, moduleId))
          if (!moduleIds || moduleIds.length === 0) {
            option.enabled = false
          }
        }
      })
      this.menusData.shortcutRightOptions.forEach(option => {
        if (option.moduleId && !this.isInArray(this.moduleIds, option.moduleId)) {
          option.enabled = false
        } else if (option.moduleIds) {
          const moduleIds = option.moduleIds.filter(moduleId => this.isInArray(this.moduleIds, moduleId))
          if (!moduleIds || moduleIds.length === 0) {
            option.enabled = false
          }
        }
      })
    },
    // 校验数组中是否存在某个元素
    isInArray(arr, value) {
      for (let i = 0; i < arr.length; i++) {
        if (value == arr[i]) {
          return true
        }
      }
      return false
    },
    openChange(event, option) {
      // 开启卸载终端菜单时，需要风险提醒
      if (event && option.menuId == 10039) {
        this.$confirmBox(this.$t('pages.termMenu_msg'), this.$t('text.prompt')).then(() => {
          // 点击确定，无其他操作
        }).catch(() => {
          // 点击取消，取消勾选
          this.menusData.openChecked.splice(this.menusData.openChecked.indexOf(10039), 1)
        })
      }
    },
    selectAll(boolean, checkedItem) { // boolean:是否全选； checkedItem：选中项类型，1 为默认显示项，2 为默认隐藏项，3 生成快捷方式
      if (checkedItem === 1) {
        if (boolean) {
          this.menusData.leftOptions.forEach(option => {
            if (this.menusData.checked.indexOf(option.menuId) < 0) {
              this.menusData.checked.push(option.menuId)
            }
          })
          this.menusData.rightOptions.forEach(option => {
            if (this.menusData.checked.indexOf(option.menuId) < 0) {
              this.menusData.checked.push(option.menuId)
            }
          })
        } else {
          this.menusData.checked.splice(0)
        }
      } else if (checkedItem === 2) {
        if (boolean) {
          this.$confirmBox(this.$t('pages.termMenu_msg'), this.$t('text.prompt')).then(() => {
            this.menusData.openCheckLeftOptions.forEach(option => {
              this.menusData.openChecked.push(option.menuId)
            })
            this.menusData.openCheckRightOptions.forEach(option => {
              this.menusData.openChecked.push(option.menuId)
            })
          }).catch(() => {})
        } else {
          this.menusData.openChecked.splice(0)
        }
      } else if (checkedItem === 3) {
        if (boolean) {
          this.menusData.shortcutLeftOptions.forEach(option => {
            if (!this.isDisabled(option.menuId)) {
              this.menusData.shortcutChecked.push(option.menuId)
            }
          })
          this.menusData.shortcutRightOptions.forEach(option => {
            if (!this.isDisabled(option.menuId)) {
              this.menusData.shortcutChecked.push(option.menuId)
            }
          })
        } else {
          this.menusData.shortcutChecked.splice(0)
        }
      }
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    loadModules: function(menuIds) {
      const checkedMenus = []
      const openCheckedMenus = []
      const shortcutCheckedMenus = []
      this.menusData.leftOptions.forEach(option => {
        if (menuIds.indexOf(option.menuId) > -1) {
          checkedMenus.push(option.menuId)
        }
      })
      this.menusData.rightOptions.forEach(option => {
        if (menuIds.indexOf(option.menuId) > -1) {
          checkedMenus.push(option.menuId)
        }
      })

      this.menusData.openCheckLeftOptions.forEach(option => {
        if (menuIds.indexOf(option.menuId) > -1) {
          openCheckedMenus.push(option.menuId)
        }
      })
      this.menusData.openCheckRightOptions.forEach(option => {
        if (menuIds.indexOf(option.menuId) > -1) {
          openCheckedMenus.push(option.menuId)
        }
      })

      this.menusData.shortcutLeftOptions.forEach(option => {
        if (menuIds.indexOf(option.menuId) > -1) {
          shortcutCheckedMenus.push(option.menuId)
        }
      })
      this.menusData.shortcutRightOptions.forEach(option => {
        if (menuIds.indexOf(option.menuId) > -1) {
          shortcutCheckedMenus.push(option.menuId)
        }
      })

      this.menusData.checked.splice(0)
      this.menusData.checked = [].concat(checkedMenus)
      this.menusData.openChecked.splice(0)
      this.menusData.openChecked = [].concat(openCheckedMenus)
      this.menusData.shortcutChecked.splice(0)
      this.menusData.shortcutChecked = [].concat(shortcutCheckedMenus)
    },
    async handleNodeClickFunc(tabName, data) {
      if (data) {
        this.query.objectType = data.type
        this.query.objectId = data.dataId
        this.terminalName = data.label
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }

      // websocket协议只能查询分页的信息，无法感知总数量
      let total = 0
      this.terminals.splice(0)
      if (this.query.objectType == 3) {
        await listTerminalByGroupId(this.query.objectId).then(respond => {
          this.terminals = respond.data
          total = respond.data.length
        })
      } else if (this.query.objectType == 1) {
        total = 1
      }

      this.gridTable.total = total
      this.rowData.splice(0)
      if (this.gridTable.total > 0) {
        const searchQuery = Object.assign({}, this.query)
        this.getRowData(searchQuery)
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTerminalType: function(terminalId) {
      let type = 0
      this.terminals.forEach(terminal => {
        if (terminal.id === terminalId) {
          type = terminal.type
        }
      })
      return type
    },
    handleCreate() {
      this.resetTemp()
      this.activeName = 'first'
      this.loadModules(this.menusData.defaultChecked)
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.activeName = 'first'
      const menuIds = JSON.parse(JSON.stringify(row.menuIds))
      this.loadModules(menuIds)
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.menuIds = this.menusData.checked.concat(this.menusData.openChecked, this.menusData.shortcutChecked)
          this.hiddenMenuId();
          createStrategy(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.menuIds = this.menusData.checked.concat(this.menusData.openChecked, this.menusData.shortcutChecked)
          this.hiddenMenuId();
          const tempData = Object.assign({}, this.temp)
          updateStrategy(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    hiddenMenuId() {
      if (this.isArmMode) {
        // 增加 10035 “关闭网络准入控制系统”
        if (!this.temp.menuIds.includes(10035)) {
          this.temp.menuIds.push(10035);
        }
      }
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    strategyNameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    menuFormatter: function(row, data) {
      // 将 menuIds 转换为 Set 以提高查找效率
      const menuIdSet = new Set(row.menuIds);
      // 定义不同类型菜单对应的选项数组
      const menuTypeMap = {
        offMenuNames: [
          this.menusData.leftOptions,
          this.menusData.rightOptions
        ],
        enableMenuNames: [
          this.menusData.openCheckLeftOptions,
          this.menusData.openCheckRightOptions
        ],
        shortcutMenuNames: [
          this.menusData.shortcutLeftOptions,
          this.menusData.shortcutRightOptions
        ]
      };
      // 封装筛选匹配菜单名称的函数
      function getMatchedMenuNames(options) {
        return options.filter(el => el.enabled && menuIdSet.has(el.menuId))
          .map(el => el.menuName);
      }
      // 存储不同类型的菜单名称
      const menuNameResult = {};
      for (const [menuType, optionArrays] of Object.entries(menuTypeMap)) {
        menuNameResult[menuType] = getMatchedMenuNames(optionArrays.flat())
      }
      // 拼接不同类型的菜单名称
      const offMenu = menuNameResult.offMenuNames.join('、');
      const enableMenu = menuNameResult.enableMenuNames.join('、');
      const shortcutMenu = menuNameResult.shortcutMenuNames.join('、');
      // 格式化输出结果
      const format = [];
      if (offMenu) {
        format.push(`${this.$t('pages.term_menu_msg1')}: ${offMenu}`);
      }
      if (enableMenu) {
        format.push(`${this.$t('pages.term_menu_msg2')}: ${enableMenu}`);
      }
      if (shortcutMenu) {
        format.push(`${this.$t('pages.genShortcut')}: ${shortcutMenu}`);
      }
      return format.join('; ');
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    async initArmMode() {
      await getArmMode().then(res => {
        const data = res.data
        this.isArmMode = data === true || data === 'true'
      });
    },
    isDisabled(shortcutMenuId) {
      const checked = JSON.parse(JSON.stringify(this.menusData.checked))
      const needDisableArr = checked.map(menuId => this.closeMenuShortcutMenuMapping[menuId]).filter(menuId => menuId !== undefined)
      return needDisableArr.includes(shortcutMenuId)
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    height: 100%;
    overflow: auto;
    .el-col-4{
      min-width: 200px;
    }
  }
  .save-btn-container{
    padding: 10px 50px 0;
  }
  .box-card .el-col.el-col-12>div {
    line-height: 24px;
  }
</style>
