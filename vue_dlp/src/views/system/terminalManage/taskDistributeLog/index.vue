<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.taskName" :value="query.taskName">
          <span>{{ $t('pages.taskName') }}：</span>
          <el-input v-model="query.taskName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.state" :value="query.state">
          <span>{{ $t('table.status') }}：</span>
          <el-select v-model="query.state">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'495'" :request="exportFunc"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'500'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('500')"
        :row-data-api="rowDataApi"
        :custom-col="true"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('route.taskDistributeLogDetail')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.taskName')">
            {{ rowDetail.taskName }}
          </el-descriptions-item>
          <el-descriptions-item label="状态类型">
            {{ taskActiveFormatter(rowDetail.taskActive) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.status')">
            {{ stateFormatter(null, rowDetail.state) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.failCase')">
            {{ taskFailCodeFormatter(null, rowDetail.taskFailCode) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getPage, exportLog, deleteLog } from '@/api/behaviorAuditing/terminal/taskDistributeLog'
import { getStategyPage } from '@/api/system/terminalManage/taskConfig'
import AuditLogExporter from '@/components/AuditFileDownloader/exporter'
import { enableStgDelete } from '@/utils'
import { uploadSoftwareStatus } from '@/api/system/terminalManage/softwareRepository'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'TaskDistributeLog',
  components: { AuditLogExporter },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '200', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'taskName', label: 'taskName2', width: '100' },
        { prop: 'state', label: 'status', width: '150', formatter: this.stateFormatter },
        { prop: 'taskFailCode', label: 'failCase', width: '150', formatter: this.taskFailCodeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('513'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        state: null,
        taskName: null,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      rowDetail: {},
      queryVideoMethod: undefined,
      dialogFormVisible: false,
      deleteable: false,
      statusOptions: [
        { value: null, label: this.$t('pages.all') },
        { value: 1, label: this.$t('pages.transferStatusDict34') },
        { value: 2, label: this.$t('pages.excuteFail') },
        { value: 3, label: this.$t('pages.consoleEndTask') },
        { value: 4, label: this.$t('pages.termEndTask') }
      ],
      taskActiveOptions: {
        0: this.$t('pages.taskEffectiving'),
        1: this.$t('pages.taskEffectiving'),
        2: this.$t('pages.taskUnEffective'),
        3: this.$t('pages.taskUnEffective'),
        4: this.$t('pages.taskNoEffective')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportLog({ exportType, ...this.query })
    },
    async handleView(row) {
      await getStategyPage({ strategyId: row.taskId }).then(res => {
        if (res.data.items) {
          row.taskActive = res.data.items[0].taskActive
        }
      })
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    taskActiveFormatter(data) {
      return this.taskActiveOptions[data]
    },
    stateFormatter(row, data) {
      if (data == 1) {
        return this.$t('pages.transferStatusDict34')
      } else if (data == 2) {
        return this.$t('pages.excuteFail')
      } else if (data == 3) {
        return this.$t('pages.consoleEndTask')
      } else if (data == 4) {
        return this.$t('pages.termEndTask')
      }
    },
    taskFailCodeFormatter(row, data) {
      if (data == 0) {
        return ''
      } else if (data == 1) {
        return this.$t('pages.uninstallSuscess')
      } else if (data == 2) {
        return this.$t('pages.callInstallFail')
      } else if (data == 3) {
        return this.$t('pages.sendFail1')
      } else if (data == 4) {
        return this.$t('pages.uncontroller')
      } else if (data == 5) {
        return this.$t('pages.failCodeText1')
      } else if (data == 6) {
        return this.$t('pages.failCodeText2')
      } else if (data == 7) {
        return this.$t('pages.failCodeText3')
      } else if (data == 8) {
        return this.$t('pages.failCodeText4')
      } else {
        const status = uploadSoftwareStatus[data]
        return !status ? this.$t('pages.encOrDecLog_Msg14') + ':' + data : status
      }
    },
    afterLoad(rowData) {
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '513')
    }
  }
}
</script>
