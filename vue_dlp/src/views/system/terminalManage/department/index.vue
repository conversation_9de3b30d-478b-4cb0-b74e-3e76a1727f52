<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="deptTree"
        :data="treeNode"
        node-key="id"
        :local-search="false"
        :get-search-list="getSearchListFunction"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button v-permission="'105'" type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createDept') }}
        </el-button>
        <el-button v-permission="'105'" icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('pages.delDept') }}
        </el-button>
        <el-button v-permission="'105'" icon="el-icon-edit" size="mini" :disabled="!btnEnable" @click="handleMove">
          {{ $t('pages.moveDept') }}
        </el-button>
        <el-button v-permission="'310'" icon="el-icon-takeaway-box" size="mini" @click="handleSort">
          {{ $t('pages.handleSort') }}
        </el-button>
        <el-button v-permission="'253'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'254'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <div class="searchCon">
          <el-select v-model="query.searchType" style="width: 160px;">
            <el-option :value="1" :label="$t('pages.searchChildDept')"/>
            <el-option :value="2" :label="$t('pages.searchAllDept')"/>
          </el-select>
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.deptName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <grid-table ref="deptList" :col-model="colModel" :row-data-api="rowDataApi" :default-sort="defaultSort" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px">
        <FormItem :label="$t('pages.deptName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.superiorDept')" prop="parentId">
          <tree-select
            v-if="editParentNameFlag"
            ref="parentDept"
            leaf-key="dept"
            :local-search="false"
            :checked-keys="[temp.keyId]"
            is-filter
            :filter-key="filterKey"
            :width="380"
            @change="parentIdSelectChange"
          />
          <el-select v-else v-model="temp.supDeptName" disabled>
            <el-option :key="temp.supDeptName" :value="temp.supDeptName" :label="temp.supDeptName"/>
          </el-select>
        </FormItem>
        <!-- <FormItem v-show="dialogStatus === 'update'" :label="$t('pages.groupManager')" prop="manager">
          <tree-select
            clearable
            :data="userNode"
            :is-filter="true"
            node-key="dataId"
            :checked-keys="[temp.manager]"
            :placeholder="$t('pages.pleaseSelectContent', { content: $t('pages.groupManager') })"
            :width="296"
            @change="managerSelectChange"
          />
        </FormItem> -->
        <FormItem v-show="dialogStatus === 'update'" :label="$t('pages.groupManager')" prop="manager">
          <tree-select
            clearable
            is-filter
            :default-expand-all="false"
            node-key="id"
            multiple
            check-strictly
            :disabled-nodes="disabledNodes"
            :checked-keys="checkedKeysFunc(temp.manager)"
            :placeholder="$t('pages.pleaseSelectContent', { content: $t('pages.groupManager') })"
            @change="managerSelectChange"
          />
        </FormItem>
        <FormItem v-if="dialogStatus === 'update'" :label="$t('pages.branchLeader')" prop="branchLeader">
          <tree-select
            clearable
            is-filter
            :local-search="false"
            :default-expand-all="false"
            node-key="id"
            :checked-keys="checkedKeysFunc(temp.branchLeader)"
            :placeholder="$t('pages.pleaseSelectContent', { content: $t('pages.branchLeader') })"
            :rewrite-node-click-fuc="true"
            :node-click-fuc="userTreeNodeCheckChange"
            @change="branchLeaderSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="temp.remark" v-trim type="textarea" rows="3" resize="none" maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.moveDept')"
      :visible.sync="dialogMoveVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="moveForm" :rules="rules" :model="temp" label-position="right" label-width="75px" style="width: 550px;">
        <el-row>
          <el-col :span="23">
            <FormItem :label="$t('pages.targetDept')" prop="parentId">
              <tree-select
                ref="targetDept"
                leaf-key="dept"
                :local-search="false"
                :checked-keys="[temp.keyId]"
                :width="430"
                is-filter
                :filter-key="moveListIds"
                @change="parentIdSelectChange"
              />
            </FormItem>
          </el-col>
          <el-col :span="1">
            <el-tooltip class="item" effect="dark" :content="$t('pages.department_tooltip')" placement="bottom-end">
              <i class="el-icon-info" style="line-height: 30px; margin-left: 5px;"/>
            </el-tooltip>
          </el-col>
        </el-row>
        <grid-table ref="moveList" :height="250" :col-model="moveColModel" :row-datas="moveListDatas" :multi-select="false" :show-pager="false" />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="moveDept()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogMoveVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.handleSortRemark')"
      :visible.sync="dialogSortVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="sortForm" :rules="rules" :model="temp" label-position="right" label-width="75px" style="width: 560px;">
        <el-row>
          <el-col :span="11" style="margin-top: -6px;margin-left: -6px">
            <FormItem :label="$t('pages.deptName')" label-width="75px" :extra-width="{en: 60}" prop="parentId">
              <tree-select
                ref="sortDeptTree"
                leaf-key="dept"
                :checked-keys="[temp.keyId]"
                :local-search="false"
                is-filter
                @change="parentIdSelectChange"
              />
            </FormItem>
          </el-col>
          <el-col :span="1" style="margin-top: -5px;margin-left: 4px">
            <label>{{ $t('pages.accordingTo') }}</label>
          </el-col>
          <el-col :span="2">
            <el-select v-model="sortRule" :placeholder="$t('text.select')" style="width: 100px; margin-top: -4px; margin-left: -8px;">
              <el-option :value="0" :label="this.$t('pages.department_confirmMsg7')"/>
              <el-option :value="1" :label="this.$t('pages.department_confirmMsg8')"/>
            </el-select>
          </el-col>
          <el-col :span="4" style="margin-left: 4px">
            <el-select v-model="recursion" :placeholder="$t('text.select')" style="width: 140px; margin-top: -4px; margin-left: 46px;">
              <el-option :value="0" :label="this.$t('pages.department_confirmMsg9')"/>
              <el-option :value="1" :label="this.$t('pages.department_confirmMsg10')"/>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-button style="margin-top: -2px; margin-left: 97px;" @click="handleSortDept">{{ $t('pages.department_confirmMsg11') }}</el-button>
          </el-col>
        </el-row>
        <el-row style="margin-top: 5px">
          <tree-menu
            ref="targetTree"
            :height="400"
            :data="targetTreeNode"
            :render-content="renderContent"
            :filter-key="recycleKey"
            draggable
            multiple
            :allow-drag="allowDrag"
            :allow-drop="allowDrop"
            :local-search="false"
            :get-search-list="getSearchListFunction"
            :default-expanded-keys="defaultTargerExpandedKeys"
            @node-click="handleTargetNodeClick"
            @node-drag-start="handleDragStart"
            @node-drag-enter="handleDragEnter"
            @node-drag-leave="handleDragLeave"
            @node-drag-over="handleDragOver"
            @node-drag-end="handleDragEnd"
            @node-drop="handleDrop"
          />
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <span style="color: #409eff; float: left; text-align: left">{{ $t('pages.department_confirmMsg12') }}</span>
        <el-button :loading="submitting" type="primary" @click="sortDept()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogSortVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('route.terminalManage')"
      :visible.sync="dialogTerminalVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="userForm" :rules="rules" :model="tempUser" label-position="right" label-width="100px" style="width: 750px;">
        <el-divider content-position="left"> {{ this.$t('pages.deptInfo',{content: this.$t('pages.baseInfo')}) }} </el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.deptName') + '：'">
              {{ tempUser.name }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.superiorDept') + '：'" label-width="138px">
              {{ tempUser.parentName }}
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left"> {{ this.$t('pages.departmentHas',{ content: this.$t('pages.terminalInfoConfig')}) }} </el-divider>
        <grid-select
          ref="terminalGridSelect"
          :height="350"
          :col-model="terminalColModel"
          :selected-col-model="selectTerminalColModel"
          searchable
          pager-small
          :to-select-table-col-span="14"
          :selected-table-col-span="6"
          :select-table-title="$t('pages.optionalTerminal')"
          :selected-table-title="$t('pages.selectedTerminal')"
          :search-prop="{ key: 'name', label: $t('pages.terminalName')}"
          :select-row-data-api="selectTerminalRowDataApi"
          :selected-row-data-api="selectedTerminalRowDataApi"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateTerminal()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogTerminalVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.userManage')"
      :visible.sync="dialogUserVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="userForm" :rules="rules" :model="tempUser" label-position="right" label-width="100px" style="width: 750px;">
        <el-divider content-position="left"> {{ this.$t('pages.deptInfo',{content: this.$t('pages.baseInfo')}) }}</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.deptName') + '：'">
              {{ tempUser.name }}
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.superiorDept') + '：'">
              {{ tempUser.parentName }}
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left"> {{ this.$t('pages.departmentHas',{ content: this.$t('pages.operateUserConfig')}) }} </el-divider>
        <grid-select
          ref="userGridSelect"
          :height="350"
          :col-model="userColModel"
          searchable
          pager-small
          :select-table-title="$t('components.optionalUser')"
          :selected-table-title="$t('components.selectedUser')"
          :search-prop="{ key: 'account', label: $t('table.account')}"
          :select-row-data-api="selectUserRowDataApi"
          :selected-row-data-api="selectedUserRowDataApi"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateUser()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogUserVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <batch-edit-page-dlg ref="batchDeleteDlg" :title="$t('pages.delDept')" :col-model="colModel" :default-sort="defaultSort" :row-data-api="rowDataApi" @submitEnd="deleteAllDept"/>
    <export-dlg ref="exportDlg" :group-tree-data="treeNode" :title="title" :export-func="exportFunc" :group-tree-id="query.parentId"/>
    <import-dlg ref="importDlg" :title="title" template="dept" :show-import-type="false" :file-name="title" :tip="tip" :show-import-way="true" :user-show-modes="userShowModes" :upload-func="upload" @success="importEndFunc"/>
  </div>
</template>

<script>
import {
  getDeptPage, countByDept, createDept, updateDept, moveDept, validateDelDept,
  deleteDept, getIds, bindTerminal, bindUser, updateSortDept, exportExcel, getById
} from '@/api/system/terminalManage/department'
import { getUserPage, listUserByGroupId } from '@/api/system/terminalManage/user'
import { getTerminalPage, listTerminalByGroupIdOnly } from '@/api/system/terminalManage/terminal'
import GridSelect from '@/components/GridSelect'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import { isSameTimestamp, initTimestamp } from '@/utils'
import request from '@/utils/request'
import { mapGetters } from 'vuex'

export default {
  name: 'Department',
  components: { GridSelect, ExportDlg, ImportDlg, BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'deptName', width: '150', fixed: true, sort: 'custom', type: 'showDetail', searchType: 'department', searchParam: 'id' },
        { prop: 'parentId', label: 'superiorDept', width: '200', formatter: this.parentFormatter },
        { prop: 'terminalSize', type: 'button', label: 'terminal', width: '100',
          buttons: [
            { formatter: this.terminalSizeFormatter, disabledFormatter: () => !this.hasPermission('B24'), click: this.linkToTermGroupPage }
          ]
        },
        { prop: 'userSize', type: 'button', label: 'user', width: '100',
          buttons: [
            { formatter: this.userSizeFormatter, disabledFormatter: () => !this.hasPermission('B23'), click: this.linkToUserGroupPage }
          ]
        },
        { prop: 'manager', label: this.$t('pages.groupManager'), width: '100', formatter: this.userIdsFormatter },
        { prop: 'branchLeader', label: this.$t('pages.branchLeader'), width: '100', formatter: this.userIdFormatter },
        { prop: 'remark', label: 'remark', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right', hidden: !this.hasPermission('105'),
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'terminalManage', click: this.handleTerminal },
            { label: 'userManage', click: this.handleUser }
          ]
        }
      ],
      moveColModel: [
        { prop: 'name', label: 'deptName', width: '150', fixed: true, sort: true },
        { prop: 'parentId', label: 'superiorDept', width: '200', formatter: this.parentFormatter },
        { prop: '', fixedWidth: 40, type: 'icon', iconClass: 'delete', iconFormatter: () => { return true }, style: 'cursor: pointer;', click: this.removeDept }
      ],
      defaultSort: { prop: 'parentId,sortNum,id', order: 'asc' },
      moveListIds: [],
      sortListIds: [],
      moveListDatas: [],
      sortListDatas: [],
      userColModel: [
        { prop: 'account', label: 'account', width: '100', sort: true },
        { prop: 'name', label: 'name', width: '100', sort: true }
      ],
      terminalColModel: [
        { prop: 'id', label: 'terminalCode', width: '100', sort: true },
        { prop: 'name', label: 'terminalName', width: '100', sort: true },
        { prop: 'computerName', label: 'computerName', width: '100', sort: true },
        { prop: 'mainIp', label: 'ipAddr', width: '100', sort: true }
      ],
      selectTerminalColModel: [
        { prop: 'name', label: 'terminalName', width: '80', sort: true }
      ],
      query: { // 查询条件
        page: 1,
        parentId: '',
        searchInfo: '',
        searchType: 1
      },
      showTree: true,
      btnEnable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        parentId: undefined,
        remark: '',
        manager: [],
        branchLeader: undefined
      },
      tempUser: {},
      dialogFormVisible: false,
      dialogMoveVisible: false,
      dialogSortVisible: false,
      dialogUserVisible: false,
      dialogTerminalVisible: false,
      dialogStatus: '',
      tip: this.$t('pages.parentDeptToDeptName'),
      textMap: {
        update: this.$t('pages.editDept'),
        create: this.$t('pages.createDept')
      },
      firstNode: [],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        parentId: [{ required: true, message: this.$t('pages.validateMsg_superiorDept'), trigger: ['blur', 'change'] }]
      },
      submitting: false,
      treeNode: [],
      targetTreeNode: [],
      defaultExpandedKeys: [],
      defaultTargerExpandedKeys: ['G0'],
      oldPanrentName: null,
      title: this.$t('pages.DeptInfo'),
      supDept: null,
      sortRule: 0,
      recursion: 0,
      selectedDatas: [],
      recycleKey: 'G-2',
      userNode: [],
      editParentNameFlag: true, //   是否支持修改上级部门

      userShowModes: [{ label: this.$t('pages.groupManager'), field: 'managerMode', mode: 1 }, { label: this.$t('pages.branchLeader'), field: 'branchLeaderMode', mode: 1 }]
    }
  },
  computed: {
    ...mapGetters(['userTreeList']),
    gridTable() {
      return this.$refs['deptList']
    },
    treeMenu() {
      return this.$refs['deptTree']
    },
    filterKey() {
      // 树节点nodeKey是id，所以这边需要加上'G'
      return 'G' + this.temp.id
    }
  },
  watch: {
    '$store.state.commonData.deptTree'() {
      this.updateDeptTree()
    }
  },
  created() {
    initTimestamp(this)
    if (this.$store.getters.deptTree.length > 0) {
      this.updateDeptTree()
    } else {
      this.loadDeptTree()
    }
  },
  activated() {
    if (!isSameTimestamp(this, 'User') || !isSameTimestamp(this, 'Terminal')) {
      this.gridTable.execRowDataApi(this.query)
    }
  },
  methods: {
    disabledNodes(data, node) {
      return data.type == '4'
    },
    terminalSelect() {
      return this.$refs['terminalGridSelect']
    },
    userSelect() {
      return this.$refs['userGridSelect']
    },
    selectionChangeEnd(rowDatas) {
      this.btnEnable = rowDatas.length > 0
    },
    rowDataApi(option) {
      option.sortName == 'parentId' && (option.sortName = 'parentId,sortNum,id')
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getDeptPage(searchQuery)
    },
    loadDeptTree() {
      // 说明：由于在编辑组织架构相关信息后，后台会通过/topic/dataChange协议通知前端用于更新部门树、终端树、操作员树，因此不需要下列请求
      // this.$store.dispatch('commonData/setDeptTree')
    },
    updateDeptTree() {
      const deptNode = this.$store.getters.deptTree
      this.treeNode = JSON.parse(JSON.stringify(deptNode))
      this.defaultExpandedKeys.splice(0)
      this.treeNode.forEach(nodeData => this.defaultExpandedKeys.push(nodeData.id))
      //  获取拥有权限的最高级部门的上级部门信息
      if (this.treeNode[0].parentId) {
        getById(this.treeNode[0].parentId.substr(1)).then(res => {
          this.supDept = res.data || null
        })
      }
    },
    parentIdSelectChange(key, data) {
      if (data) {
        data = data[0] || data
        this.temp.parentId = data.dataId
        this.temp.keyId = key
        // this.$refs.moveForm && this.$refs.moveForm.validate()
      }
    },
    selectUserRowDataApi(option) { // 可选操作员
      const groupId = this.tempUser.id
      const params = Object.assign({ // 每个操作员仅属于一个操作员组，因此添加查询条件
        includeUnGroup: true,
        groupId: groupId
      }, option)
      return getUserPage(params)
    },
    importEndFunc(groupId) {
      this.loadDeptTree()
      // 导入时 分组未唯一分组时 点击分组节点并查询数据 实现：向上获取节点树路径，并展开父节点
      const nodeData = this.treeMenu.findNode(this.treeNode, (groupId || 0) + '', 'dataId')
      let node = this.treeMenu.getNode(nodeData)
      const nodePath = []
      while (node) {
        if (!node.parent || node.parent.expanded) {
          break
        } else {
          nodePath.unshift(node.parent)
          node = node.parent
        }
      }
      nodePath.forEach(n => { n.expanded = true })
      this.$refs['deptTree'].selectCurrentNode('G' + (groupId || 0))
      this.$nextTick(() => {
        this.handleFilter()
      })
    },
    upload(data) {
      return request.post('/department/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    getDeptName(deptId) {
      const tree = this.$refs['deptTree'].tree()
      const node = tree.getNode(deptId)
      return node ? node.label : this.$t('pages.null')
    },
    selectedUserRowDataApi(option) { // 已选操作员
      return listUserByGroupId(this.tempUser.id)
    },
    selectTerminalRowDataApi(option) { // 可选用户
      const groupId = this.tempUser.id
      const params = Object.assign({ // 每个用户仅属于一个用户组，因此添加查询条件
        includeUnGroup: true,
        groupId: groupId,
        filterUseType: true
      }, option)
      return getTerminalPage(params)
    },
    async selectedTerminalRowDataApi(option) { // 已选终端
      const selectedTerminals = await listTerminalByGroupIdOnly(this.tempUser.id)
      return selectedTerminals
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.manager.splice(0)
    },
    resetSortRule() {
      this.sortRule = 0
      this.recursion = 0
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    // 单击树节点的回调函数
    handleNodeClick(data, node) {
      node.expanded = true
      this.query.parentId = data.dataId
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleTargetNodeClick(data, node) {
      this.defaultTargerExpandedKeys.splice(0, this.defaultTargerExpandedKeys.length, data.parentId)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleTerminal(row) {
      this.resetTemp()
      this.tempUser = Object.assign({}, row)
      this.dialogTerminalVisible = true
      if (this.terminalSelect()) {
        this.terminalSelect().clearFilter()
        this.terminalSelect().reload()
      }
      if (this.supDept && row.parentId === this.supDept.id) {
        this.tempUser.parentDeptName = this.supDept.name || ''
      }
    },
    handleUser(row) {
      this.resetTemp()
      this.tempUser = Object.assign({}, row)
      this.dialogUserVisible = true
      if (this.userSelect()) {
        this.userSelect().clearFilter()
        this.userSelect().reload()
      }
      this.$nextTick(() => {
        this.$refs['userForm'].clearValidate()
      })
      if (this.supDept && row.parentId === this.supDept.id) {
        this.tempUser.parentDeptName = this.supDept.name || ''
      }
    },
    clearUserFilter() {
      this.$refs.parentDept && this.$refs.parentDept.clearFilter()
    },
    handleCreate() {
      this.resetTemp()
      this.temp.parentId = this.query.parentId
      this.temp.keyId = 'G' + this.query.parentId
      this.dialogStatus = 'create'
      this.editParentNameFlag = true
      this.dialogFormVisible = true
      this.clearUserFilter()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      // await this.initCurrentGropUserNodes(row.id);
      const tempRow = JSON.parse(JSON.stringify(row))
      this.temp = Object.assign({}, this.defaultTemp, tempRow) // copy obj
      // this.temp.manager = []
      this.temp.keyId = 'G' + row.parentId
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.clearUserFilter()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        // const node = this.$refs.parentDept.tree().getNode(this.temp.parentId)
        // 部门名称只用来记录管理员日志
        this.temp.parentName = this.getDeptName(this.temp.parentId)
      })
      this.temp.supDeptName = this.supDept && row.parentId === this.supDept.id ? this.supDept.name || '' : row.parentName
      const supDeptName = this.supDept && row.parentId === this.supDept.id ? this.supDept.name || null : null
      this.editParentNameFlag = supDeptName == null
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.query, {
          updateQuery: true
        })
        return exportExcel(q, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          parentId: formData.type === 2 ? formData.groupId : null,
          parentName: formData.type === 2 ? formData.groupName : null,
          updateQuery: false
        }, opts)
      }
    },
    notifySuccess(msg) {
      this.$notify({
        title: this.$t('text.success'),
        message: msg,
        type: 'success',
        duration: 2000
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 部门名称只用来记录管理员日志
          this.temp.parentName = this.getDeptName(this.temp.parentId)
          createDept(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.loadDeptTree()
            this.notifySuccess(this.$t('text.createSuccess'))
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          // 部门名称只用来记录管理员日志
          this.temp.parentName_new = this.getDeptName(this.temp.parentId)
          const tempData = Object.assign({}, this.temp)
          if (tempData.manager.length > 80) {
            this.$notify({
              title: this.$t('text.warning'),
              message: this.$t('pages.managerLimit80'),
              type: 'warning',
              duration: 2000
            })
            this.submitting = false
            return;
          }
          updateDept(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.loadDeptTree()
            this.notifySuccess(this.$t('text.updateSuccess'))
          })
        }
      })
    },
    handleDelete() {
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleMove() {
      this.dialogMoveVisible = true
      // 选中行对应树节点的keys
      const selectedKeys = this.gridTable.getSelectedIds().map(id => 'G' + id)
      // const filterKeys = []
      // 需要过滤节点和子节点的keys
      // selectedKeys.forEach(key => {
      //   filterKeys.push(...this.treeMenu.getChildKeys(key))
      // });
      this.moveListIds.splice(0, this.moveListIds.length, ...new Set(selectedKeys))
      this.moveListDatas.splice(0, this.moveListDatas.length, ...this.gridTable.getSelectedDatas())
      this.resetTemp()
      this.$refs.targetDept && this.$refs.targetDept.clearFilter()
      this.$nextTick(() => {
        this.$refs.moveForm && this.$refs.moveForm.clearValidate()
      })
    },
    removeDept(e, row, rowIndex) {
      const index = this.moveListDatas.indexOf(row)
      this.$confirmBox(this.$t('pages.department_confirmMsg1', { name: row.name }), this.$t('text.prompt')).then(() => {
        this.moveListDatas.splice(index, 1)
        this.moveListIds.splice(index, 1)
      }).catch(() => {

      })
    },
    handleSort() {
      this.defaultTargerExpandedKeys = ['G0']
      this.resetSortRule()
      this.dialogSortVisible = true
      this.targetTreeNode = JSON.parse(JSON.stringify(this.treeNode))
      this.firstNode = this.targetTreeNode.map(node => node.dataId)
      this.sortListIds.splice(0, this.sortListIds.length, ...this.gridTable.getSelectedIds())
      this.sortListDatas.splice(0, this.sortListDatas.length, ...this.gridTable.getSelectedDatas())
      this.resetTemp()
      this.$refs.sortTargetDept && this.$refs.sortTargetDept.clearFilter()
      this.$nextTick(() => {
        this.$refs.sortForm && this.$refs.sortForm.clearValidate()
      })
    },
    removeSortDept(e, row, rowindex) {
      const index = this.sortListDatas.indexOf(row)
      this.$confirmBox(this.$t('pages.department_confirmMsg1', { name: row.name }), this.$t('text.prompt')).then(() => {
        this.sortListDatas.splice(index, 1)
        this.sortListIds.splice(index, 1)
      }).catch(() => {

      })
    },
    moveDept() {
      this.submitting = true
      this.$refs['moveForm'].validate((valid) => {
        if (valid) {
          if (this.moveListIds.length == 0) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.department_notifyMsg5'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          const selectIds = []
          this.moveListIds.forEach(item => {
            if (item.indexOf('G') == 0) {
              selectIds.push(item.slice(1))
            }
          })
          const targetId = this.$refs.targetDept.selectedKeys
          const deptName = this.moveListDatas.map(item => {
            return item.name
          })  // 记录日志用的
          const parentName = this.getDeptName(targetId)  // 记录日志用的
          moveDept({ ids: selectIds.join(','), parentId: this.temp.parentId, parentName: parentName, names: deptName }).then(respond => {
            this.submitting = false
            this.dialogMoveVisible = false
            this.gridTable.execRowDataApi()
            this.loadDeptTree()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.department_confirmMsg2'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleSortDept() {
      this.$refs['sortForm'].validate((valid) => {
        if (valid) {
          const data = Object.assign([], this.targetTreeNode)
          this.sortNode(data, this.$refs.sortDeptTree.selectedKeys, 'id')
          this.$nextTick(() => {
            this.defaultTargerExpandedKeys.splice(0, this.defaultTargerExpandedKeys.length, this.$refs.sortDeptTree.selectedKeys)
          })
        }
      })
    },
    sortMethod(sortNode) {
      if (sortNode && sortNode.children) {
        sortNode.children.sort(this.compareByRule('label', this.sortRule == 0 ? 'asc' : 'desc'))
        if (this.recursion == 0 && sortNode.children) {
          const length = sortNode.children.length
          for (let i = 0; i < length; i++) {
            const node = sortNode.children[i]
            if (node.children) {
              this.sortMethod(node)
            }
          }
        }
      }
    },
    sortNode(nodes, keyValue, keyField) {
      if (nodes && nodes.length > 0) {
        const len = nodes.length
        for (let i = 0; i < len; i++) {
          const nodei = nodes[i]
          if (nodei[keyField] + '' === keyValue + '') {
            this.sortMethod(nodei)
          } else {
            if (nodei.children && nodei.children.length > 0) {
              this.sortNode(nodei.children, keyValue, keyField)
            }
          }
        }
        this.targetTreeNode.splice(0, this.targetTreeNode.length)
        this.$nextTick(() => {
          this.targetTreeNode.splice(0, this.targetTreeNode.length, ...nodes)
        })
      }
    },
    compareByRule(prop, sortRule) {
      return function(obj1, obj2) {
        const a = obj1[prop]
        const b = obj2[prop]
        if (typeof a == 'string' && sortRule == 'asc') {
          return a.localeCompare(b, 'zh-CN')
        } else if (typeof a == 'string' && sortRule == 'desc') {
          return b.localeCompare(a, 'zh-CN')
        } else {
          if (sortRule == 'asc') {
            if (a > b) {
              return 1
            }
            if (a < b) {
              return -1
            }
            return 0
          } else {
            if (b > a) {
              return 1
            }
            if (b < a) {
              return -1
            }
            return 0
          }
        }
      }
    },
    sortDept() {
      this.submitting = true
      updateSortDept(this.targetTreeNode).then(respond => {
        this.submitting = false
        this.dialogSortVisible = false
        this.gridTable.execRowDataApi()
        this.loadDeptTree()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.sortSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.submitting = false
      })
    },
    nodeTop(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === 0) {
        this.$message({
          message: this.$t('pages.department_confirmMsg5'),
          type: 'warning'
        })
      } else if (index !== 0) {
        childNodes.unshift(...childNodes.splice(index, 1))
        childDatas.unshift(...childDatas.splice(index, 1))
      }
    },
    nodeBottom(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === childDatas.length - 1) {
        this.$message({
          message: this.$t('pages.department_confirmMsg6'),
          type: 'warning'
        })
      } else {
        childNodes.push(...childNodes.splice(index, 1))
        childDatas.push(...childDatas.splice(index, 1))
      }
    },
    nodeUp(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === 0) {
        this.$message({
          message: this.$t('pages.department_confirmMsg5'),
          type: 'warning'
        })
      } else {
        childNodes.splice(index - 1, 0, ...childNodes.splice(index, 1))
        childDatas.splice(index - 1, 0, ...childDatas.splice(index, 1))
      }
    },
    nodeDown(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === childDatas.length - 1) {
        this.$message({
          message: this.$t('pages.department_confirmMsg6'),
          type: 'warning'
        })
      } else {
        childNodes.splice(index + 1, 0, ...childNodes.splice(index, 1))
        childDatas.splice(index + 1, 0, ...childDatas.splice(index, 1))
      }
    },
    allowDrag(draggingNode) {
      return true
    },
    allowDrop(draggingNode, dropNode, type) {
      const allowDrop = this.firstNode.indexOf(draggingNode.data.dataId) > -1
      if (type == 'inner' || draggingNode.data.parentId != dropNode.data.parentId || allowDrop) {
        return false
      } else {
        return true
      }
    },
    handleDragStart(node, ev) {
      const checkDatas = this.$refs['targetTree'].getCheckedNodes()
      if (checkDatas && checkDatas.length > 0) {
        this.selectedDatas.splice(0, this.selectedDatas.length, ...checkDatas)
      }
    },
    handleDragEnter(draggingNode, dropNode, ev) {
      // console.log('node-drag-start', draggingNode, dropNode, ev)
    },
    handleDragLeave(draggingNode, dropNode, ev) {
      // console.log('node-drag-leave', draggingNode, dropNode, ev)
    },
    handleDragOver(draggingNode, dropNode, ev) {
      // console.log('node-drag-over', draggingNode, dropNode, ev)
    },
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      // console.log('node-drag-end', draggingNode, dropNode, dropType, ev)
    },
    handleDrop(draggingNode, dropNode, dropType, ev) {
      // 清空所有勾选
      this.$refs.targetTree.clearSelectedNodes()
      const checkDatas = this.selectedDatas
      const checkDataIds = []
      let allowDrop = true
      if (checkDatas && checkDatas.length > 0) {
        checkDatas.forEach(data => {
          if (checkDataIds.indexOf(data.id) < 0) {
            checkDataIds.push(data.id)
          }
          if (data.parentId != dropNode.data.parentId && checkDataIds.indexOf(data.parentId) == -1) {
            allowDrop = false
          }
        })
      }
      if (checkDataIds && checkDataIds.length > 0 && checkDataIds.indexOf(draggingNode.data.id) > -1 && allowDrop) {
        if (checkDataIds.indexOf(dropNode.data.id) == -1) {
          this.$refs.targetTree.removeNode(checkDataIds)
          if (dropType === 'after') {
            for (let i = checkDatas.length - 1; i >= 0; i--) {
              this.$refs.targetTree.insertAfter(checkDatas[i], dropNode.data)
            }
          } else if (dropType === 'before') {
            checkDatas.forEach(node => {
              this.$refs.targetTree.insertBefore(node, dropNode.data)
            })
          }
        }
      }
    },
    renderContent(h, { node, data, store }) {
      const show = this.firstNode.indexOf(data.dataId) > -1
      return (
        <div>
          <span>{data.label}</span>
          <span v-show={!show} class='el-ic'>
            <span class='el-icon-arrow-up' on-click={e => this.nodeTop(e, store, node, data)}>{ this.$t('text.topping') }</span>
            <span class='el-icon-top' on-click={e => this.nodeUp(e, store, node, data)}>{ this.$t('text.up') }</span>
            <span class='el-icon-bottom' on-click={e => this.nodeDown(e, store, node, data)}>{ this.$t('text.down') }</span>
            <span class='el-icon-arrow-down' on-click={e => this.nodeBottom(e, store, node, data)}>{ this.$t('text.bottoming') }</span>
          </span>
        </div>
      )
    },
    updateTerminal() {
      this.submitting = true
      const terminalIds = this.terminalSelect().getSelectedIds()
      const tempData = Object.assign({}, this.tempUser)
      const terminalNames = this.terminalSelect().getSelectedDatas().map(item => {
        return item.name
      })
      bindTerminal({
        groupId: tempData.id,
        ids: terminalIds.join(','),
        terminalNames: terminalNames
      }).then(respond => {
        this.submitting = false
        this.dialogTerminalVisible = false
        this.tempUser.terminalSize = terminalIds.length
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    updateUser() {
      this.submitting = true
      const userIds = this.userSelect().getSelectedIds()
      const tempData = Object.assign({}, this.tempUser)
      bindUser({ groupId: tempData.id, ids: userIds.join(',') }).then(respond => {
        this.submitting = false
        this.dialogUserVisible = false
        this.tempUser.userSize = userIds.length
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    parentFormatter(row, data) {
      if (row.parentName) {
        return row.parentName
      }
      const node = this.treeMenu.findNode(this.treeNode, data, 'dataId')
      if (node === null && this.supDept !== null) {
        let name = ''
        //  查找上级部门
        if (this.supDept.id == data) {
          name = this.supDept.name || ''
        }
        return name
      } else {
        row.parentDeptName = node ? this.html2Escape(node.label) : ''
        return row.parentDeptName
      }
    },
    userIdsFormatter(row, data) {
      if (!data) {
        return ''
      }
      const datavale = data.map(item => ((this.userTreeList || []).find(user => user.id == 'U' + item) || {}).label).filter(item => !!item).join(',')
      return datavale
    },
    userIdFormatter(row, data) {
      return !data ? '' : ((this.userTreeList || []).find(user => user.id == 'U' + data) || {}).label
    },
    nameValidator(rule, value, callback) {
      if (value == this.$t('pages.offline_terminal_text15') && this.temp.parentId == 0) {
        callback(new Error(this.$t('pages.offline_terminal_text27')))
      }
      countByDept({ id: this.temp.id, parentId: this.temp.parentId, name: value }).then(respond => {
        const data = respond.data
        if (value.indexOf('->') > -1) {
          callback(new Error(this.$t('pages.department_confirmMsg4')))
        }
        if (data > 0) {
          callback(new Error(this.$t('pages.department_confirmMsg3')))
        } else {
          callback()
        }
      })
    },
    deleteAllDept(params, callback) {
      // 未选中任何终端的提示信息
      if (params.ids) {
        const ids = params.ids
        validateDelDept({ ids: ids }).then(respond => {
          this.dealDelData(respond, ids, callback)
        })
      } else {
        getIds(params).then(respond => {
          const ids = respond.data
          validateDelDept({ ids: ids }).then(respond => {
            this.dealDelData(respond, ids, callback)
          })
        })
      }
    },
    dealDelData(respond, ids, callback) {
      let errorMsg = ''
      if (respond.data === 'disable_4_child') {
        errorMsg = this.$t('pages.department_notifyMsg1')
      } else if (respond.data === 'disable_4_relate') {
        errorMsg = this.$t('pages.department_notifyMsg2')
      } else if (respond.data === 'disable_4_default') {
        errorMsg = this.$t('pages.department_notifyMsg4')
      } else if (respond.data === 'disable_4_used') {
        errorMsg = this.$t('pages.department_notifyMsg6')
      }
      if (errorMsg && errorMsg.length > 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: errorMsg,
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
      } else {
        this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
          deleteDept({ ids: ids }).then(respond => {
            this.gridTable.execRowDataApi(this.query)
            this.query.parentId = undefined
            this.loadDeptTree()
            this.notifySuccess(this.$t('text.deleteSuccess'))
            callback(respond)
          }).catch(e => { callback(e) })
        }).catch(e => { callback(e) })
      }
    },
    terminalSizeFormatter(row, data) {
      return row.terminalSize
    },
    linkToTermGroupPage(data) {
      const url = '/terminalManage/terminalManage/terminal'
      const queryTemp = { entityId: data.id, entityType: '3' }
      this.$router.push({ path: url, query: queryTemp })
    },
    userSizeFormatter(row, data) {
      return row.userSize
    },
    linkToUserGroupPage(data) {
      const url = '/terminalManage/terminalManage/user'
      const queryTemp = { entityId: data.id, entityType: '4' }
      this.$router.push({ path: url, query: queryTemp })
    },
    // managerSelectChange(data) {
    //   this.temp.manager = (data === '') ? undefined : data
    // },
    checkedKeysFunc(keys) {
      const checkedKeys = keys ? (Array.isArray(keys) ? keys : [keys]) : []
      return checkedKeys.map(key => `U${key}`)
    },
    managerSelectChange(data) {
      this.temp.manager.splice(0)
      data.forEach(item => {
        if (item.indexOf('U') >= 0) {
          this.temp.manager.push(parseInt(item.replace('U', '')))
        }
      })
    },
    branchLeaderSelectChange(data) {
      if (data === '') {
        this.temp.branchLeader = undefined
        return;
      }
      if (data instanceof Object) {
        this.temp.branchLeader = data.dataId
      }
    },
    // manageTreeNodeCheckChange(data, node, vm) {
    //   if (data.id.indexOf('G' + data.dataId) < 0) {
    //     this.temp.manager = data.dataId
    //     vm.setSelectOption(node)
    //     vm.isShowSelect = !vm.isShowSelect
    //     vm.$emit('change', data)
    //   }
    // },
    // userTreeNodeCheckChange(data, node, vm) {
    //   if (data.id.indexOf('G' + data.dataId) < 0) {
    //     this.temp.branchLeader = data.dataId
    //     vm.setSelectOption(node)
    //     vm.isShowSelect = !vm.isShowSelect
    //     vm.$emit('change', data)
    //   }
    // }
    userTreeNodeCheckChange(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.temp.branchLeader = data.dataId
      } else {
        return false
      }
    }
    // async initCurrentGropUserNodes(groupId) {
    //   await getUserTree(groupId).then(respond => {
    //     if (respond.data) {
    //       const nodes = [];
    //       respond.data.map(p => {
    //         if (p.type === '2') {
    //           nodes.push(p);
    //         }
    //       })
    //       this.userNode = nodes
    //     } else {
    //       this.userNode = [];
    //     }
    //   }).catch(e => {
    //     this.userNode = [];
    //   })
    // }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-ic span{
    font-size: 12px;
    &:hover {
      color: #409eff;
    }
  }
</style>
