<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :local-search="false"
        :get-search-list="getSearchListFunction"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-checkbox v-model="query.range">{{ $t('pages.timeQuery') }}</el-checkbox>
        <el-date-picker
          v-show="!query.range"
          v-model="query.date"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <el-date-picker
          v-show="query.range"
          v-model="query.daterange"
          type="daterange"
          range-separator="至"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <label>终端名称：</label><el-input v-model="query.name" style="width: 200px;" />
        <label>上下线类型：</label>
        <el-select v-model="query.type" is-filter placeholder="全部" style="width: 200px;">
          <el-option label="全部类型" :value="undefined"></el-option>
          <el-option label="终端上线" :value="1"></el-option>
          <el-option label="终端下线" :value="0"></el-option>
        </el-select>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="false"
        :row-data-api="rowDataApi"
      />
    </div>
  </div>
</template>

<script>
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { getOnlineOfflineLogPage } from '@/api/system/terminalManage/onlineOfflineLog'

export default {
  name: 'OnlineOfflineLog',
  data() {
    return {
      showTree: true,
      treeData: [],
      defaultExpandedKeys: ['G0'],
      colModel: [
        { prop: 'groupId', label: '分组', width: '150', formatter: this.groupFormatter },
        { prop: 'termName', label: 'terminalName', width: '150' },
        { prop: 'computerName', label: '计算机名称', width: '150' },
        { prop: 'ip', label: 'ip', width: '150' },
        { prop: 'mac', label: 'MAC地址', width: '150' },
        { prop: 'type', label: '上下线类型', width: '150' },
        { prop: 'createTime', label: '时间', width: '150', sort: true },
        { prop: 'termId', label: '终端ID', width: '150' },
        { prop: 'dataSrvId', label: '数据服务器ID', width: '150' }
      ],
      query: { // 查询条件
        page: 1,
        range: false,
        date: undefined,
        daterange: [],
        groupId: undefined,
        name: undefined,
        type: undefined
      }
    }
  },
  computed: {
    groupTree() {
      return this.$refs['groupTree']
    },
    gridTable() {
      return this.$refs['logList']
    }
  },
  watch: {
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    }
  },
  created() {
    this.initGroupTreeNode()
  },
  methods: {
    initGroupTreeNode() {
      getDeptTreeFromCache().then(respond => {
        this.treeData = respond.data
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    // 单击树节点的回调函数
    handleNodeClick(data, node) {
      node.expanded = true
      this.query.groupId = data.dataId
      this.handleFilter()
    },
    getGroupName(groupId, groups) {
      for (const group of groups) {
        if (group.dataId == groupId) {
          return group.label
        }
        const groupName = this.getGroupName(groupId, group.children || [])
        if (groupName) {
          return groupName
        }
      }
      return ''
    },
    groupFormatter(row, data) {
      return this.getGroupName(data, this.treeData)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (searchQuery.range) {
        delete searchQuery.date
        if (searchQuery.daterange.length > 0) {
          searchQuery.startDate = searchQuery.daterange[0]
          searchQuery.endDate = searchQuery.daterange[1]
        }
      }
      delete searchQuery.daterange
      return getOnlineOfflineLogPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-checkbox {
    margin-right: 10px
  }
  .el-date-editor--daterange {
    width: 240px;
    padding: 3px 0 3px 8px;
  }
</style>
