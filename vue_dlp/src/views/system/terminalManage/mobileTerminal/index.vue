<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :os-type-filter="8" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <!-- 暂不支持 因策略直接应用到终端-->
        <!-- <strategy-extend/>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 740px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="222"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem :label="$t('pages.mobileTerminalSuffixShareSpecifySuffixFile')" prop="isShareFile" label-width="141px" :extra-width="{en: 70}">
          <el-radio v-model="temp.isShareFile" :label="1" :disabled="!formable">{{ $t('pages.allow') }}</el-radio>
          <el-radio v-model="temp.isShareFile" :label="0" :disabled="!formable">{{ $t('pages.forbid') }}</el-radio>
        </FormItem>
        <el-card v-show="temp.isShareFile===1 || !formable" class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.specifyFileSuffix') }}</span>
            <el-popover
              v-model="visible1"
              :disabled="!formable"
              style="margin-left: 20px"
              placement="right"
              width="400"
              trigger="click"
            >
              <tree-menu ref="suffixList" style="height: 350px" multiple :data="defaultSuffixList" :is-filter="false" :default-expand-all="true"/>
              <div style="text-align: right; margin-top: 10px">
                <el-button type="primary" size="mini" @click="handleCheckedSuffix">{{ $t('button.confirm') }}</el-button>
                <el-button size="mini" type="primary" @click="visible1 = false">{{ $t('button.cancel') }}</el-button>
              </div>
              <el-button v-if="formable" slot="reference" size="small">{{ $t('button.import') }}</el-button>
            </el-popover>
            <el-button v-if="formable" size="small" @click="handleClear">
              {{ $t('button.clear') }}
            </el-button>
            <br/>
            <span style="color: #409eff;font-size: small;font-weight:bold;">{{ $t('pages.mobileTerminalSuffixShareSpecifySuffixFileConfigTips') }}</span>
          </div>
          <FormItem prop="fileSuffixList" label-width="0px">
            <tag v-model="temp.fileSuffixList" :list="temp.fileSuffixList" :disabled="!formable" @tagChange="fileSuffixListChange"/>
          </FormItem>
        </el-card>
        <!--<el-row></el-row>
        <el-card class="box-card" :body-style="{'padding': '5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.mobileTerminalSuffixSpecifyOpenProgram') }}
              <el-tooltip class="item" effect="dark" content="" placement="bottom">
                <div slot="content">
                  <i18n path="pages.mobileTerminalSuffixSpecifyOpenProgramTips">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </span>
            <br/>
            <span style="color: #409eff;font-size: small;font-weight:bold;padding-top:7px;display:block;line-height: 20px;">{{ $t('pages.mobileTerminalSuffixSpecifyOpenProgramMsg') }}</span>
          </div>
          <div style="margin: 10px 5px 5px 5px">
            <data-editor
              :formable="formable"
              :popover-width="750"
              :updateable="appEditable"
              :deletable="appDeleteable"
              :add-func="createApp"
              :append-to-body="true"
              :update-func="updateApp"
              :delete-func="deleteApp"
              :cancel-func="cancelApp"
              :before-update="beforeupdateApp"
            >
              <Form ref="appForm" :model="appTemp" :rules="appTempprules" label-position="right" label-width="90px" style="width: 725px;">
                <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
                  <div slot="header">
                    <span>{{ $t('pages.suffixes') }}</span>
                    <el-popover
                      v-model="visible2"
                      :disabled="!formable"
                      style="margin-left: 20px"
                      placement="right"
                      width="400"
                      trigger="click"
                    >
                      <tree-menu ref="appSuffix" style="height: 350px" multiple :data="defaultSuffixList" :is-filter="false" :default-expand-all="true"/>
                      <div style="text-align: right; margin-top: 10px">
                        <el-button type="primary" size="mini" @click="handleCheckedAppSuffix">{{ $t('button.confirm') }}</el-button>
                        <el-button size="mini" @click="visible2 = false">{{ $t('button.cancel') }}</el-button>
                      </div>
                      <el-button v-if="formable" slot="reference" size="small" style="padding:5px" @click="showSuffixTree">{{ $t('button.import') }}</el-button>
                      <el-button v-if="formable" slot="reference" size="small" style="padding:5px" @click="handleFileSuffixImport()">
                        {{ $t('button.FileSuffixLibImport') }}
                      </el-button>
                    </el-popover>
                  </div>
                  <FormItem prop="fileSuffix" label-width="0px">
                    <tag v-model="appTemp.fileSuffix" :list="appTemp.fileSuffix" :disabled="!formable" :overflow-able="true" @tagChange="appTempFileSuffixChange"/>
                  </FormItem>
                </el-card>
                <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
                  <div slot="header">
                    <div>{{ $t('pages.specifyOpenProgram') }}</div>
                    <el-upload
                      name="processFile"
                      action="1111"
                      accept=".apk"
                      :on-change="handleChange"
                      :limit="1"
                      :show-file-list="false"
                      :before-upload="getFileName"
                      :disabled="fileSubmitting"
                      style="float: left;top: -24px;margin-left: 105px;position: relative;"
                    >
                      <el-button size="small" :loading="fileSubmitting" style="padding:5px" icon="el-icon-upload">{{ $t('pages.uploadApkFile') }}</el-button>
                    </el-upload>
                    <el-row v-if="loading">
                      <el-col :span="22">
                        <el-progress type="line" :percentage="percentage"/>
                      </el-col>
                      <el-col :span="2">
                        <el-button type="primary" size="small" style="padding:5px; margin-bottom:5px" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
                      </el-col>
                    </el-row>
                  </div>
                  <el-row>
                  </el-row>
                  <el-row style="margin-top: 5px;">
                    <el-col :span="12">
                      <FormItem :label="$t('pages.appName')" prop="appName" label-width="70px">
                        <el-input v-model="appTemp.appName" style="width:230px;"></el-input>
                      </FormItem>
                    </el-col>
                    <el-col :span="12">
                      <FormItem :label="$t('pages.appPackageName')" prop="appPackageName" label-width="70px">
                        <el-input v-model="appTemp.appPackageName" style="width:230px;"></el-input>
                      </FormItem>
                    </el-col>
                  </el-row>
                </el-card>
              </Form>
            </data-editor>
          </div>
          <grid-table
            ref="appList"
            :height="250"
            :multi-select="true"
            :show-pager="false"
            :col-model="appColModel"
            :row-datas="appRowData"
            @selectionChangeEnd="appSelectionChange"
          />
        </el-card>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="222"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!--<file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>-->
  </div>
</template>

<script>
import { getStrategyList, getStrategyByName, createStrategy, updateStrategy, deleteStrategy } from '@/api/system/terminalManage/mobileTerminal'
import {
  enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter,
  entityLink, refreshPage, buttonFormatter
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter } from '@/utils/formatter'
// import request from '@/utils/request'
// import axios from 'axios'
// import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'MobileTerminal',
  components: {},
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'stgMessage', width: '200', formatter: this.msgFormatter },
        // { label: 'programName', width: '200', formatter: this.processNameFormatter },
        // { label: '例外目录', width: '100', formatter: this.exceptionDirFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'operate', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      // appColModel: [
      //   { prop: 'appSuffix', label: 'suffixes', width: '100', formatter: this.suffixFormatter },
      //   { prop: 'appName', label: this.$t('pages.appName'), width: '100' },
      //   { prop: 'appPackageName', label: this.$t('pages.appPackageName'), width: '100' }
      //
      // ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      appEditable: false,
      appDeleteable: false,
      selectedNodeData: {},
      noTargetTree: {},
      isDisabled: 0,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isShareFile: 0, // 是否允许分享指定后缀文件
        fileSuffixList: [], // 指定分享文件后缀列表
        // appNameList: [], // 指定打开app列表（只支持安卓） appName app名称 appPackageName app包名 fileSuffix 指定app打开文件后缀
        entityType: undefined,
        entityId: undefined
      },
      // appTemp: {},
      // defaultAppTemp: {
      //   id: undefined,
      //   appName: '',
      //   appPackageName: '',
      //   fileSuffix: []
      // },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mobileTerminalStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.mobileTerminalStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      // appTempprules: {
      //   appName: [
      //     { required: true, message: this.$t('pages.process_Msg10'), trigger: 'blur' }
      //   ],
      //   appPackageName: [
      //     { required: true, message: this.$t('pages.process_Msg10'), trigger: 'blur' }
      //   ],
      //   fileSuffix: [
      //     { required: true, message: this.$t('pages.fileSuffix_text4'), trigger: 'blur' }
      //   ]
      // },
      multipleSelection: [], // 选中行数组集合
      submitting: false,
      // actionMap: { 1: this.$t('pages.screenshot'), 2: this.$t('pages.recordingScreen') },
      showable: true,
      // recordInterval: 0,
      visible1: false,
      // visible2: false,
      defaultSuffixList: [
        { label: this.$t('pages.builtFileSuffix'), id: 0, children: [
          { label: '.ddb', id: 1 },
          { label: '.doc', id: 2 },
          { label: '.docx', id: 3 },
          { label: '.dwg', id: 4 },
          { label: '.html', id: 5 },
          { label: '.pcb', id: 6 },
          { label: '.pdf', id: 7 },
          { label: '.ppt', id: 8 },
          { label: '.pptx', id: 9 },
          { label: '.txt', id: 10 },
          { label: '.rtf', id: 11 },
          { label: '.xls', id: 12 },
          { label: '.xlsx', id: 13 },
          { label: '.key', id: 14 },
          { label: '.numbers', id: 15 },
          { label: '.pages', id: 16 }
        ]
        }
      ],
      // selectedSuffix: [],
      // updateSuffix: [],
      fileSubmitting: false,
      loading: false,
      percentage: 0,
      source: null  // 上传连接源
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    // connectionSource() {  // 得到取消操作需要的连接
    //   return axios.CancelToken.source()
    // },
    handleCreate() {
      this.isDisabled = 0
      this.resetTemp()
      // this.getConfigByKey()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    // getConfigByKey() {
    //   getConfigByKey({ key: 'recordInterval' }).then(resp => {
    //     if (resp.data) {
    //       this.recordInterval = resp.data.value
    //     }
    //   })
    // },
    // listenKey(e) {
    //   if (e.keyCode === 13) {
    //     e.preventDefault() // 阻止浏览器默认换行操作
    //     return false
    //   }
    // },
    clearappFormValidate() {
      this.$refs['appForm'] && this.$refs['appForm'].clearValidate()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    processTable() {
      return this.$refs['processList']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    appSelectionChange(rowDatas) {
      this.appDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.appEditable = true
      } else {
        this.appEditable = false
        this.cancelApp()
      }
    },
    appTable() {
      return this.$refs['appList']
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.selectedNodeData = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.selectedNodeData = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.fileSuffixList.splice(0)
      // this.selectedSuffix.splice(0)
      // this.updateSuffix.splice(0)
      // this.resetAppTemp()
      // this.appTemp.fileSuffix.splice(0)
      this.submitting = false
    },
    // resetAppTemp() {
    //   this.configBtnMode = 'add'
    //   this.appTemp = Object.assign({}, this.defaultAppTemp)
    //   this.appTemp.fileSuffix.splice(0)
    // },
    // resetSensitiveFileOpTemp() {
    //   this.sensitiveFileOpTemp = Object.assign({}, this.sensitiveFileOpDefaultTemp)
    // },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row))
      // const that = this
      this.dialogFormVisible = true
      this.dialogStatus = 'update'
    },
    // formatDataFormParam() {
    //   this.appRowData.forEach(item => {
    //     delete item.id
    //     const temp = { 'appName': item.appName, 'appPackageName': item.appPackageName, 'fileSuffix': item.fileSuffix }
    //     this.temp.appNameList.push(temp)
    //   })
    // },
    changeObjEmitFun(data) {
      this.temp.entityType = data.changeType
      this.temp.entityId = data.changeDataId
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // this.formatDataFormParam()
          createStrategy(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // this.formatDataFormParam()
          updateStrategy(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    msgFormatter: function(row, data) {
      const msg = []
      if (row.isShareFile == 1) {
        msg.push(this.$t('pages.allowShareSpecifySuffixFile', { file: row.fileSuffixList.join(',') }))
      }
      // if (row.appNameList.length > 0) {
      //   msg.push(` ${this.$t('pages.fileSuffixSpecifyOpenProgram')}`)
      //   row.appNameList.forEach(item => {
      //     msg.push(`{ ${this.$t('pages.fileSuffixSpecifyOpenProgramAppNames', { name: item.appName, suffix: item.fileSuffix.join(',') })} }`)
      //   })
      // }
      return msg.join('')
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    // processNameValidator(rule, value, callback) {
    //   if (!this.processNameReg(value)) {
    //     callback(new Error(this.$t('pages.process_Msg14')))
    //   }
    //   const size = this.appRowData.length
    //   for (let i = 0; i < size; i++) {
    //     const item = this.appRowData[i]
    //     let isContainOperation = false
    //     item.operation.forEach(op => {
    //       if (this.appTemp.operation.indexOf(op) > -1) {
    //         isContainOperation = true
    //       }
    //     })
    //     if (item.processName === value && isContainOperation && (this.configBtnMode === 'add' || item.id !== this.appTemp.id)) {
    //       callback(new Error(this.$t('pages.process_Validate5')))
    //       return
    //     }
    //   }
    //   callback()
    // },
    // fileSuffixValidator(rule, value, callback) {
    //   this.selectedSuffix.splice(0)
    //   const size = this.appRowData.length
    //   for (let i = 0; i < size; i++) {
    //     const item = this.appRowData[i]
    //     item.fileSuffix.forEach(i => {
    //       this.selectedSuffix.unshift(i)
    //     })
    //   }
    //   this.updateSuffix.forEach(i => {
    //     if (this.selectedSuffix.indexOf(i) >= 0) {
    //       this.selectedSuffix.splice(this.selectedSuffix.indexOf(i), 1)
    //     }
    //   })
    //   for (let i = 0; i < value.length; i++) {
    //     if (this.selectedSuffix.indexOf(value[i]) >= 0) {
    //       callback(new Error(this.$t('pages.suffixAlreadyHasAnOpenProgramSpecified', { suffix: value[i] })))
    //     }
    //   }
    //   callback()
    // },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    // handleFileSuffixImport() {  // 从文件后缀库导入
    //   this.$refs.fileSuffixLibImport.show()
    // },
    // importFileSuffix(suffix) {
    //   const new_suffix = suffix.split('|')
    //   this.appTemp.fileSuffix = [...new Set(this.appTemp.fileSuffix.concat(new_suffix))]
    // },
    // handleImport() {
    //   this.$refs.importDlg.show()
    // },
    handleCheckedSuffix() {
      const checkNodes = this.$refs.suffixList.$refs.tree.getCheckedNodes()
      checkNodes.forEach(item => {
        if (item.id != 0 && this.temp.fileSuffixList.indexOf(item.label) == -1) {
          this.temp.fileSuffixList.push(item.label)
        }
      })
      this.visible1 = false
    },
    handleClear() {
      this.temp.fileSuffixList.splice(0)
    },
    // handleCheckedAppSuffix() {
    //   const checkNodes = this.$refs.appSuffix.$refs.tree.getCheckedNodes()
    //   checkNodes.forEach(item => {
    //     if (item.id != 0 && this.appTemp.fileSuffix.indexOf(item.label) == -1) {
    //       this.appTemp.fileSuffix.push(item.label)
    //     }
    //   })
    //   this.visible2 = false
    // },
    // createApp() {
    //   let validate
    //   this.$refs['appForm'].validate((valid) => {
    //     this.selectedSuffix.splice(0)
    //     const size = this.appRowData.length
    //     for (let i = 0; i < size; i++) {
    //       const item = this.appRowData[i]
    //       item.fileSuffix.forEach(i => {
    //         this.selectedSuffix.unshift(i)
    //       })
    //     }
    //     for (let i = 0; i < this.appTemp.fileSuffix.length; i++) {
    //       if (this.selectedSuffix.indexOf(this.appTemp.fileSuffix[i]) >= 0) {
    //         this.$notify({
    //           title: this.$t('text.warning'),
    //           message: this.$t('pages.suffixAlreadyHasASpecifiedProgram', { suffix: this.appTemp.fileSuffix[i] }),
    //           type: 'warning',
    //           duration: 2000
    //         })
    //         return
    //       }
    //     }
    //     if (valid) {
    //       const rowData = JSON.parse(JSON.stringify(this.appTemp))
    //       rowData.id = new Date().getTime()
    //       this.appRowData.unshift(rowData)
    //       this.cancelApp()
    //       validate = valid
    //     }
    //   })
    //   return validate
    // },
    // beforeupdateApp() {
    //   this.configBtnMode = 'update'
    //   this.appTemp = JSON.parse(JSON.stringify(this.appTable().getSelectedDatas()[0]))
    //   this.updateSuffix.splice(0)
    //   this.appTemp.fileSuffix.forEach(i => {
    //     this.updateSuffix.unshift(i)
    //   })
    // },
    // updateApp() {
    //   let validate
    //   this.$refs['appForm'].validate((valid) => {
    //     this.selectedSuffix.splice(0)
    //     const size = this.appRowData.length
    //     for (let i = 0; i < size; i++) {
    //       const item = this.appRowData[i]
    //       item.fileSuffix.forEach(i => {
    //         this.selectedSuffix.unshift(i)
    //       })
    //     }
    //     this.updateSuffix.forEach(i => {
    //       if (this.selectedSuffix.indexOf(i) >= 0) {
    //         this.selectedSuffix.splice(this.selectedSuffix.indexOf(i), 1)
    //       }
    //     })
    //     for (let i = 0; i < this.appTemp.fileSuffix.length; i++) {
    //       if (this.selectedSuffix.indexOf(this.appTemp.fileSuffix[i]) >= 0) {
    //         this.$notify({
    //           title: this.$t('text.warning'),
    //           message: this.$t('pages.suffixAlreadyHasASpecifiedProgram', { suffix: this.appTemp.fileSuffix[i] }),
    //           type: 'warning',
    //           duration: 2000
    //         })
    //         return
    //       }
    //     }
    //     if (valid) {
    //       const rowData = JSON.parse(JSON.stringify(this.appTemp))
    //       for (let i = 0, size = this.appRowData.length; i < size; i++) {
    //         const data = this.appRowData[i]
    //         if (rowData.id === data.id) {
    //           this.appRowData.splice(i, 1, rowData)
    //           break
    //         }
    //       }
    //       this.cancelApp()
    //       validate = valid
    //     }
    //   })
    //   return validate
    // },
    // deleteApp() {
    //   this.cancelApp()
    //   const toDeleteIds = this.appTable().getSelectedIds()
    //   this.appTable().deleteRowData(toDeleteIds, this.appRowData)
    // },
    // cancelApp() {
    //   this.$refs['appForm'] && this.$refs['appForm'].clearValidate()
    //   this.resetAppTemp()
    // },
    // suffixFormatter(row, data) {
    //   let msg = ''
    //   if (row.fileSuffix) {
    //     msg = row.fileSuffix.join(',')
    //   }
    //   return msg
    // },
    // showSuffixTree() {
    //   this.$refs.appSuffix.clearSelectedNodes()
    // },
    // getFileName(file) {
    //   // return false // 屏蔽了action的默认上传
    //   this.fileSubmitting = true
    //   this.loading = true
    //   this.percentage = 0
    //   const fd = new FormData()
    //   fd.append('file', file)// 传文件
    //   // 上传钩子，用来获取进度条
    //   const onUploadProgress = (progressEvent) => {
    //     const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
    //     this.percentage = parseInt(percent)
    //   }
    //   this.source = this.connectionSource()
    //   // 这个是上传会话token，取消上传操作需要的参数
    //   const cacheToken = this.source.token
    //   this.defaultUploadDataFunc(fd, onUploadProgress, cacheToken).then(res => {
    //     // this.resetUploadComponent()
    //     // if (res.data.length > 0) {
    //     //   this.resetAppTempPropety()
    //     //   if (this.autoAppend) {
    //     //     Object.assign(this.appTemp, res.data[0])
    //     //   }
    //     //   this.afterUpload(res.data[0])
    //     // }
    //     this.resetUploadComponent()
    //     this.appTemp.appName = res.data.label
    //     this.appTemp.appPackageName = res.data.packageName
    //   }).catch(res => {
    //     this.resetUploadComponent()
    //     if (axios.isCancel(res)) {
    //       // 取消上传后的操作，待补充
    //     }
    //   })
    //   return false // 屏蔽了action的默认上传
    // },
    handleChange(file, fileLists) {
      // // 本地服务器路径
      // console.log(URL.createObjectURL(file.raw));
      // // 本地电脑路径
      // console.log(document.getElementsByClassName('el-upload__input')[0].value)
    },
    // onUpload(data) {
    //   // if (this.validateFail) {
    //   //   this.errorMsg = this.$t('components.chooseApplicationObj')
    //   //   return false
    //   // }
    //   // this.showUploadInfo = true
    //   // this.submitting = true
    //   const fileName = data.file.name
    //   const fd = new FormData()
    //   // fd.append('uploadFile', data.file)// 传文件
    //   // fd.append('importType', this.temp.importType)
    //   // fd.append('objectType', this.temp.objectType)
    //   // fd.append('objectIds', this.temp.objectIds)
    //   // fd.append('objectGroupIds', this.temp.objectGroupIds)
    //
    //   fd.append('file', data.file)// 传文件
    //   this.defaultUploadDataFunc(fd).then(res => {
    //     // this.$notify({ title: this.$t('text.success'), message: fileName + '文件导入成功', type: 'success', duration: 2000 })
    //     this.uploadEnd()
    //   }).catch(res => {
    //     this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.processStgLib_Msg80', { name: fileName }), type: 'error', duration: 2000 })
    //     this.uploadEnd()
    //   })
    //   return false // 屏蔽了action的默认上传
    // },
    // defaultUploadDataFunc(data, onUploadProgress, token) {
    //   return request.post('/mobileOpenSuffix/importApp', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
    // },
    // uploadEnd() {
    // },
    // cancel() {  // 取消上传
    //   if (this.source) {
    //     this.source.cancel(this.$t('pages.cancelUpload'))
    //   }
    // },
    // resetUploadComponent() {  // 重置上传组件状态
    //   this.fileSubmitting = false
    //   this.loading = false
    //   this.percentage = 0
    // },
    getSuffixList(list) {
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      return Object.keys(newMap) || [];
    },
    fileSuffixListChange(list) {
      this.temp.fileSuffixList = this.getSuffixList(list)
    }
    // appTempFileSuffixChange(list) {
    //   this.appTemp.fileSuffix = this.getSuffixList(list)
    // }
  }
}
</script>
<style lang="scss" scoped>
  >>>.button-new-tag {
    margin-left: 10px;
    padding: 5px;
  }
  >>>.input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
