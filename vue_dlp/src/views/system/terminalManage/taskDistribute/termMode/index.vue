<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    </div>
    <div class="table-container">
      <grid-table ref="termModeList" :multi-select="false" :col-model="colModel" :row-data-api="rowDataApi" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      width="900px"
      :title="$t('table.termTaskDetail')"
      :visible.sync="dialogFormVisible"
      @close="handleClose"
    >
      <div style="height: 37%; display: flex;">
        <grid-table ref="termStatusList" :multi-select="false" :height="300" :col-model="colModel1" :row-data-api="tableRowDataApi" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPage, listTermModeStatus } from '@/api/system/terminalManage/taskStatus'
import { uploadSoftwareStatus } from '@/api/system/terminalManage/softwareRepository'

export default {
  name: 'TermMode',
  components: { },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        termId: undefined,
        taskId: undefined
      },
      showTree: true,
      deleteable: false,
      rowDetail: {},
      dialogFormVisible: false,
      colModel: [
        { prop: 'termName', label: 'terminalName', width: '100', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'termId' },
        { label: 'noExecuteTaskNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.noExecuteSizeFormatterFormatter, click: this.noExecuteClick }]
        },
        { label: 'executingTaskNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.executingSizeFormatter, click: this.executingClick }]
        },
        { label: 'executeSucessTaskNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.successSizeFormatter, click: this.successClick }]
        },
        { label: 'executeFailTaskNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.failSizeFormatter, click: this.failClick }]
        },
        { label: 'executeEndTaskNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.executeEndFormatter, click: this.executeEndClick }]
        }
      ],
      colModel1: [
        { prop: 'taskName', label: 'taskName2', width: '150' },
        { prop: 'state', label: 'executeStatus', width: '150', formatter: this.stateFormatter },
        { prop: 'taskFailCode', label: 'failCase', width: '150', formatter: this.taskFailCodeFormatter },
        { prop: 'type', label: 'effectiveStatus', width: '100', formatter: this.typeFormatter }
      ],
      taskActiveOptions: {
        0: this.$t('pages.taskEffectiving'),
        1: this.$t('pages.taskEffectiving'),
        2: this.$t('pages.taskUnEffective'),
        3: this.$t('pages.taskUnEffective'),
        4: this.$t('pages.taskNoEffective')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['termModeList']
    },
    termStatusListTable() {
      return this.$refs['termStatusList']
    }
  },
  created() {
  },
  methods: {
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (type == 3) {
        // 过滤移动终端
        return false
      }
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.query.termId = undefined
      this.query.taskId = undefined
      this.gridTable.execRowDataApi(this.query)
    },
    handleClose() {
      this.query.termId = undefined
      this.query.taskId = undefined
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    tableRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return listTermModeStatus(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleTermState() {
      this.query.page = 1
      this.termStatusListTable.execRowDataApi(this.query)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    noExecuteSizeFormatterFormatter(row) {
      return row.noExecuteSize
    },
    executingSizeFormatter(row) {
      return row.executingSize
    },
    executeSizeFormatter(row) {
      return row.executeSize
    },
    successSizeFormatter(row) {
      return row.successSize
    },
    failSizeFormatter(row) {
      return row.failSize
    },
    executeEndFormatter(row) {
      return row.executeEndSize
    },
    noExecuteClick(row) {
      if (row.termId && row.noExecuteIds && row.noExecuteIds.length) {
        this.dialogFormVisible = true
        this.query.termId = row.termId
        this.query.taskIds = row.noExecuteIds.join(',')
        this.$nextTick(() => {
          this.handleTermState()
        })
      }
    },
    executingClick(row) {
      if (row.termId && row.executingIds && row.executingIds.length) {
        this.dialogFormVisible = true
        this.query.termId = row.termId
        this.query.taskIds = row.executingIds.join(',')
        this.$nextTick(() => {
          this.handleTermState()
        })
      }
    },
    executeClick(row) {
      if (row.termId && row.executeIds && row.executeIds.length) {
        this.dialogFormVisible = true
        this.query.termId = row.termId
        this.query.taskIds = row.executeIds.join(',')
        this.$nextTick(() => {
          this.handleTermState()
        })
      }
    },
    successClick(row) {
      if (row.termId && row.successIds && row.successIds.length) {
        this.dialogFormVisible = true
        this.query.termId = row.termId
        this.query.taskIds = row.successIds.join(',')
        this.$nextTick(() => {
          this.handleTermState()
        })
      }
    },
    failClick(row) {
      if (row.termId && row.failIds && row.failIds.length) {
        this.dialogFormVisible = true
        this.query.termId = row.termId
        this.query.taskIds = row.failIds.join(',')
        this.$nextTick(() => {
          this.handleTermState()
        })
      }
    },
    executeEndClick(row) {
      if (row.termId && row.executeEndIds && row.executeEndIds.length) {
        this.dialogFormVisible = true
        this.query.termId = row.termId
        this.query.taskIds = row.executeEndIds.join(',')
        this.$nextTick(() => {
          this.handleTermState()
        })
      }
    },
    stateFormatter(row) {
      if (row.state == '0') {
        return this.$t('pages.noExecute')
      } else if (row.state == '1') {
        return this.$t('pages.executing')
      } else if (row.state == '2') {
        return this.$t('pages.executeSuccess')
      } else if (row.state == '3') {
        return this.$t('pages.executeFail')
      } else if (row.state == '4') {
        return this.$t('pages.executeEnd')
      } else if (row.state == '5') {
        return this.$t('pages.termEndTask')
      }
    },
    taskFailCodeFormatter(row) {
      if (row.taskFailCode == 0 || row.taskFailCode == undefined) {
        return ''
      } else if (row.taskFailCode == 1) {
        return this.$t('pages.uninstallSuscess')
      } else if (row.taskFailCode == 2) {
        return this.$t('pages.callInstallFail')
      } else if (row.taskFailCode == 3) {
        return this.$t('pages.sendFail1')
      } else if (row.taskFailCode == 4) {
        return this.$t('pages.uncontroller')
      } else if (row.taskFailCode == 5) {
        return this.$t('pages.failCodeText1')
      } else if (row.taskFailCode == 6) {
        return this.$t('pages.failCodeText2')
      } else if (row.taskFailCode == 7) {
        return this.$t('pages.failCodeText3')
      } else if (row.taskFailCode == 8) {
        return this.$t('pages.failCodeText4')
      } else {
        const status = uploadSoftwareStatus[row.taskFailCode]
        return !status ? this.$t('pages.encOrDecLog_Msg14') + ':' + row.taskFailCode : status
      }
    },
    typeFormatter(row) {
      return this.taskActiveOptions[row.type]
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-descriptions-item__label {
    min-width: 150px;
  }
  >>>.el-descriptions-item__content {
    min-width: 100px;
    max-width: 180px;
  }
</style>
