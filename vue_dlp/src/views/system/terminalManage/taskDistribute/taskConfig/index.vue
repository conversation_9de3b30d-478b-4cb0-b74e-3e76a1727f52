<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange" />
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addTask1') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteTask1') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleEndTask">
          {{ this.$t('button.endTask') }}
        </el-button>
        <div class="searchCon">
          <!-- <span>生效时间：</span>
          <el-date-picker
            v-model="query.startDate"
            style="width: 200px"
            type="date"
          >
          </el-date-picker>
          <span>至</span>
          <el-date-picker
            v-model="query.endDate"
            style="width: 200px"
            type="date"
          >
          </el-date-picker> -->
          <label>{{ $t('table.effectiveStatus') }}：</label>
          <el-select v-model="query.endTaskFlag">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareTask_Validate4')" :maxlength="260" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      width="820px"
      :title="textMap[dialogStatus]"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      @close="handleDialogClose"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
      >
        <stg-target-form-item
          ref="stgTargetItem"
          :stg-code="1"
          :entity-type-span="12"
          :tree-width="350"
          :stg-type="2"
          :form-data="temp"
          :is-disabled="editFlag"
          :terminal-filter="terminalFilter"
          rewrite-node-click-fuc
          :node-click-fuc="treeNodeCheckChange"
        />
        <FormItem :label="$t('pages.taskName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="editFlag" :maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.taskType')">
          <el-radio-group v-model="temp.taskType" :disabled="editFlag" @change="taskTypeChange">
            <el-radio :label="1">{{ $t('pages.installSoft') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.executeFile') }}</el-radio>
            <el-radio :label="3">{{ $t('pages.sendDocument') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <el-row>
          <el-col style="display: flex;">
            <FormItem :label="$t('pages.effectTime')" prop="beginTime">
              <el-date-picker
                v-model="temp.beginTime"
                style="width: 200px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                type="datetime"
                :disabled="editFlag"
                :placeholder="$t('pages.selectDateTime')"
              >
              </el-date-picker>
            </FormItem>
            <span style="margin-top: 8px; margin-left: 5px">{{ $t('pages.manageLibrary_encryptionKey_to') }}</span>
            <FormItem style="margin-left: -72px" prop="endTime">
              <el-date-picker
                v-model="temp.endTime"
                style="width: 200px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                type="datetime"
                :disabled="editFlag"
                :placeholder="$t('pages.selectDateTime')"
                :default-time="'23:59:59'"
              >
              </el-date-picker>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('text.remark')">
          <el-input v-model="temp.remark" :disabled="editFlag" maxlength="100" show-word-limit/>
        </FormItem>
        <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab" @tab-click="tabClick">
          <el-tab-pane :label="$t('pages.basicConfig')" name="baseConfig">
            <el-row>
              <el-col :span="10">
                <FormItem :label="$t('pages.clientType')" style="margin-left: 3px">
                  <el-radio-group v-model="temp.osType" :disabled="editFlag">
                    <!-- <el-radio :label="1" :value="Windows">Windows</el-radio> -->
                    <el-radio :label="1">Windows</el-radio>
                    <!-- <el-radio :label="2">Linux</el-radio>
                    <el-radio :label="3">Mac</el-radio> -->
                  </el-radio-group>
                </FormItem>
              </el-col>
              <el-col :span="13">
                <FormItem :label="$t('table.architecture')" prop="architecture">
                  <el-checkbox-group v-model="architectureList" :disabled="editFlag" @change="handleArchChange">
                    <el-checkbox v-for="(item, index) in architectures" v-show="!item.disabled" :key="index" :label="item.value">
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="3" style="margin-left: 8px;">
                <el-checkbox v-model="temp.limitSystem" :true-label="1" :false-label="0" :disabled="editFlag" style="margin-right: 5px" @change="limitSystemChange">{{ $t('pages.limitSystem') }}</el-checkbox>
              </el-col>
              <el-col :span="21" style="margin-left: -60px; margin-top: -6px">
                <FormItem prop="serverConfig">
                  <i18n path="pages.termSecurityHostCheckWay2">
                    <el-select slot="operator" v-model="temp.sysVerOperator" size="mini" style="width: 100px;" :disabled="editFlag || !!temp.limitSystem" clearable>
                      <el-option v-for="item in osTypeOperators" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</el-option>
                    </el-select>
                    <el-select slot="windowsFigure" v-model="temp.sysVerDesk" style="margin-right: 5px;width: 140px;" size="mini" :disabled="editFlag || !!temp.limitSystem" clearable>
                      <el-option v-for="item in windowsOsTypes" :key="item.os" :value="item.os">{{ item.os }}</el-option>
                    </el-select>
                    <el-select slot="windowsServer" v-model="temp.sysVerService" size="mini" style="width: 170px;" :disabled="editFlag || !!temp.limitSystem" clearable>
                      <el-option v-for="item in windowsServerOsTypes" :key="item.os" :value="item.os">{{ item.os }}</el-option>
                    </el-select>
                  </i18n>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="13" style="margin-top: 5px">
                <!--安装包文件-->
                <FormItem :label="$t('table.fileName')" label-width="65px" prop="fileName">
                  <div class="original-file-item">
                    <el-input :value="temp.fileName" :title="temp.fileName" :maxlength="260" disabled/>
                    <el-upload :class="['original-file-upload', { 'not-allowed': loading }]" action="" :disabled="loading || editFlag" style="" :accept="accept" :before-upload="beforeUpload">
                      <el-button size="mini" type="primary" :loading="loading" :disabled="loading || editFlag" icon="el-icon-upload">{{ $t('pages.offline_uploadFile') }}</el-button>
                    </el-upload>
                    <el-button class="original-button" size="small" :disabled="loading || editFlag" @click="handleOpenSoftwareSelectDlg">
                      <!--从软件库导入-->
                      {{ $t('pages.softwareRepositoryImport') }}
                    </el-button>
                  </div>
                  <div v-show="loading" class="file-upload-progress">
                    <el-progress type="line" :stroke-width="5" :percentage="uploadPercent"/>
                    <i class="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancelUpload"/>
                  </div>
                </FormItem>
              </el-col>
            </el-row>
            <FormItem v-if="showMainExe" :label="$t('table.mainExe')" label-width="100px" style="margin-left: -15px" prop="mainExe">
              <el-input v-model="temp.mainExe" v-trim :disabled="editFlag" :maxlength="260"></el-input>
            </FormItem>
            <FormItem v-if="temp.taskType != 3" :label="$t('pages.executeType')" style="margin-left: -10px">
              <el-radio-group v-model="temp.executeType" :disabled="editFlag" @change="executeTypeChange">
                <el-radio :label="1">{{ $t('pages.autoExecute') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.showTipsExecute') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem v-if="temp.taskType != 3 && temp.executeType == 2" label-width="120px" :label="$t('pages.showAlarmTime')" style="margin-left: -7px">
              <el-input-number v-model="temp.showAlarmTime" :disabled="editFlag" :controls="false" :step="1" step-strictly :max="1440" :min="1" style="width: 70px;"></el-input-number>{{ $t('text.minute') }}
            </FormItem>
            <FormItem v-if="temp.taskType != 3 && temp.executeType == 1" :label="$t('pages.userType')" style="margin-left: -12px">
              <el-radio-group v-model="temp.userType" :disabled="editFlag">
                <el-radio :label="1">{{ $t('pages.systemUser1') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.currentUser') }}</el-radio>
                <el-radio :label="3">{{ $t('pages.appointUser') }}
                  <el-tooltip effect="dark" placement="bottom-end">
                    <div slot="content">
                      {{ $t('pages.userTypeTips') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                  <link-button menu-code="A81" :link-url="'/system/configManage/domainAccount'"/>
                </el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem v-if="temp.taskType != 3 && temp.executeType == 1" :label="$t('pages.executeCommand')" :tooltip-content="$t('pages.executeCommandTips')" prop="executeCommand">
              <el-input v-model="temp.executeCommand" v-trim :disabled="editFlag" :maxlength="260"></el-input>
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('table.highConfig')" name="excuteParamConfig">
            <div>
              <el-divider content-position="left">{{ $t('pages.executeFailTips') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">{{ $t('pages.executeFailText') }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-divider>
              <el-row>
                <el-col :span="8">
                  <FormItem :label="$t('pages.repeatTimes')" label-width="120px" prop="repeatTimes">
                    <el-input-number v-model="temp.repeatTimes" :disabled="editFlag" :controls="false" :step="1" step-strictly :max="1440" :min="0" style="width: 70px"/>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.intervalTimes')" label-width="200px" prop="intervalTime">
                    <el-input-number v-model="temp.intervalTime" :disabled="editFlag" :controls="false" :step="1" step-strictly :max="1440" :min="0" style="width: 70px"/>{{ $t('text.minute') }}
                  </FormItem>
                </el-col>
              </el-row>
            </div>
            <el-divider content-position="left">{{ $t('pages.routerConfig') }}</el-divider>
            <FormItem style="margin-left: -48px">
              <el-checkbox v-model="temp.mandatory" :true-label="1" :false-label="0" :disabled="editFlag">
                指定文件保存路径
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" class="tooltipStyle">
                    若未指定文件保存路径，则文件存放路径默认遵循终端自身设定
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </FormItem>
            <FormItem :label="$t('pages.savePath1')" label-width="120px">
              <span>
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" class="tooltipStyle">
                    {{ $t('pages.savePathTip1') }}<br>
                    <span v-for="item in filePathShortcutKeys" :key="item.path">
                      {{ item.label }}：{{ item.path }}<br>
                    </span>
                    {{ $t('pages.fileDetectionFilePathTip5') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <el-button v-for="item in filePathShortcutKeys" :key="item.path" :disabled="editFlag || temp.mandatory == 0" size="mini" @click="filePathShortcutKey(item.path)">{{ item.label }}</el-button>
            </FormItem>
            <FormItem style="margin-left: -50px" prop="savePath">
              <el-input v-model="temp.savePath" v-trim :disabled="editFlag || temp.mandatory == 0" clearable maxlength="500" show-word-limit @input="filPathInputHandler"></el-input>
            </FormItem>
            <!-- <el-row>
              <el-col :span="8" style="margin-top: 5px">
                <el-checkbox v-model="temp.customPath" true-label="1" false-label="0" :disabled="editFlag">允许终端自定义下载路径</el-checkbox>
              </el-col>
            </el-row> -->
            <el-divider content-position="left">{{ $t('pages.otherConfig') }}</el-divider>
            <el-row style="margin-left: 35px;">
              <el-col v-if="temp.taskType != 3" :span="8">
                <el-checkbox v-model="temp.autoDeleteFile" :true-label="1" :false-label="0" :disabled="editFlag">{{ $t('pages.autoDeleteFile') }}</el-checkbox>
              </el-col>
              <el-col v-if="temp.executeType != 2 && temp.taskType != 3" :span="6">
                <el-checkbox v-model="temp.silentExecute" :true-label="1" :false-label="0" :disabled="editFlag" @change="silentExecuteChange">{{ $t('pages.silentExecute') }}
                  <el-tooltip effect="dark" placement="top">
                    <div slot="content" class="tooltipStyle">
                      若未指定文件保存路径，文件保存至用户临时目录
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-checkbox>
              </el-col>
              <el-col :span="6">
                <el-checkbox v-model="temp.deleteTask" :true-label="1" :false-label="0" :disabled="editFlag || temp.silentExecute == 1">{{ $t('pages.deleteTask1') }}</el-checkbox>
              </el-col>
            </el-row>
            <el-row style="margin-left: 35px; margin-top: 15px; margin-bottom: 15px;">
              <el-col :span="8">
                <el-checkbox v-model="temp.taskTips" :true-label="1" :false-label="0" :disabled="editFlag || temp.silentExecute == 1" @change="taskTipsChange">{{ $t('pages.taskTips') }}</el-checkbox>
              </el-col>
              <el-col :span="10">
                <el-checkbox v-model="temp.taskFailTips" :true-label="1" :false-label="0" :disabled="editFlag || temp.taskTips == 0 || temp.silentExecute == 1">{{ $t('pages.taskFailTips') }}</el-checkbox>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane v-if="temp.taskType != 3" :label="$t('pages.ftpControl_text7')" name="taskDetectionCon">
            <el-divider content-position="left">{{ $t('pages.detectionExecuteTips') }}</el-divider>
            <el-button :disabled="editFlag" @click="createDetectionCon">{{ $t('button.insert') }}</el-button>
            <el-button :disabled="!detectionExecuteDelete || editFlag" @click="deleteDetecteExecuteCon">{{ $t('button.delete') }}</el-button>
            <grid-table
              ref="detectionExecuteList"
              :height="200"
              :col-model="conditionColModel"
              :show-pager="false"
              :row-datas="temp.detectionExecuteList"
              :selectable="selectable"
              @selectionChangeEnd="detectionExecuteSelectionChangeEnd"
            />
            <el-divider content-position="left">{{ $t('pages.detectionNoExecuteTips') }}</el-divider>
            <el-button :disabled="editFlag" @click="createCompleteCon">{{ $t('button.insert') }}</el-button>
            <el-button :disabled="!detectionDelete || editFlag" @click="deleteDetectionCon">{{ $t('button.delete') }}</el-button>
            <grid-table
              ref="detectionList"
              :height="200"
              :col-model="detectionColModel"
              :show-pager="false"
              :row-datas="temp.detectionList"
              :selectable="selectable"
              @selectionChangeEnd="detectionSelectionChangeEnd"
            />
          </el-tab-pane>
          <el-tab-pane v-if="temp.taskType == 1" :label="$t('pages.completeCon')" name="taskCompleteCon">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.completeTips') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-divider>
            <el-button :disabled="editFlag" @click="createCompleteCon">{{ $t('button.insert') }}</el-button>
            <el-button :disabled="!completeDelete || editFlag" @click="deleteCompleteCon">{{ $t('button.delete') }}</el-button>
            <grid-table
              ref="completeList"
              :height="200"
              :col-model="conditionColModel"
              :show-pager="false"
              :row-datas="temp.completeList"
              :selectable="selectable"
              @selectionChangeEnd="completeSelectionChangeEnd"
            />
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <el-radio-group v-model="temp.completeResRule" :disabled="editFlag">
              <el-radio :label="1">{{ $t('pages.anyoneRule') }}</el-radio><br/>
              <el-radio :label="2">{{ $t('pages.allRule') }}</el-radio>
            </el-radio-group>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :disabled="editFlag" type="primary" :loading="loading || submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="titleMap[completeTitle]"
      :visible.sync="dialogFormComVisible"
      width="500px"
    >
      <Form
        ref="conditionForm"
        :rules="completeRules"
        :model="completeTemp"
        label-position="right"
        label-width="90px"
      >
        <FormItem :label="$t('pages.type')" prop="type">
          <el-select v-model="completeTemp.type" size="mini" @change="typeChange">
            <el-option v-for="item in comConditionOptions" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</el-option>
          </el-select>
        </FormItem>
        <FormItem v-if="completeTemp.type == 1" :label="$t('pages.szSoftName')" prop="checkValue">
          <el-input v-model="completeTemp.checkValue" v-trim :maxlength="260"></el-input>
        </FormItem>
        <FormItem v-if="completeTemp.type == 1" :label="$t('pages.softwareVersion')">
          <el-input v-model="completeTemp.version" v-trim :maxlength="100"></el-input>
        </FormItem>
        <FormItem v-if="completeTemp.type == 2" :label="$t('table.servername')" prop="checkValue">
          <el-input v-model="completeTemp.checkValue" v-trim :maxlength="260"></el-input>
        </FormItem>
        <FormItem v-if="completeTemp.type == 3" :label="$t('table.processName')" prop="checkValue">
          <el-input v-model="completeTemp.checkValue" v-trim :maxlength="260"></el-input>
        </FormItem>
        <FormItem v-if="completeTemp.type == 4" :label="$t('pages.filePath')" prop="filePath">
          <el-input v-model="completeTemp.checkValue" v-trim :maxlength="260" style="width: calc(100% - 20px)"></el-input>
          <el-tooltip effect="dark" placement="top">
            <div slot="content" class="tooltipStyle">
              {{ $t('pages.taskDistributeText2') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem v-if="completeTemp.type == 5 || completeTemp.type == 6" :label="$t('pages.regItem')" prop="regItem">
          <el-input v-model="completeTemp.regItem" v-trim :maxlength="260" style="width: calc(100% - 20px)"></el-input>
          <el-tooltip effect="dark" placement="top">
            <div slot="content" class="tooltipStyle">
              {{ $t('pages.registryDetectionRegItemMgs1') }}<br>
              {{ $t('pages.registryDetectionRegItemMgs2') }}<br>
              {{ $t('pages.registryDetectionRegItemMgs3') }}<br>
              HKEY_CLASSES_ROOT、<br>
              HKEY_CURRENT_USER、<br>
              HKEY_LOCAL_MACHINE、<br>
              HKEY_USERS、<br>
              HKEY_CURRENT_CONFIG<br>
              {{ $t('pages.registryDetectionRegItemMgs4') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem v-if="completeTemp.type == 6" :label="$t('pages.registryKey')" prop="regKey">
          <el-input v-model="completeTemp.regKey" v-trim :maxlength="260"></el-input>
        </FormItem>
        <FormItem v-if="completeTemp.type == 6" :label="$t('pages.registryKeyValue')" prop="regValue">
          <el-input v-model="completeTemp.regValue" v-trim :maxlength="260"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.matchType')" prop="matchType">
          <el-select v-model="completeTemp.matchType" size="mini">
            <el-option v-for="item in matchTypeOptions" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</el-option>
          </el-select>
        </FormItem>
        <div v-if="completeTemp.type == 5 || completeTemp.type == 6" class="tooltipStyle" style="margin-left: 20px">
          {{ $t('pages.registryDetectionNote1') }}<br>
          {{ $t('pages.registryDetectionNote2') }}
          <el-tooltip effect="dark" placement="top">
            <div slot="content" class="tooltipStyle">
              {{ $t('pages.registryDetectionNoteTip1') }}<br>
              {{ $t('pages.registryDetectionNoteTip2') }}<br>
              {{ $t('pages.registryDetectionNoteTip3') }}<br>
              {{ $t('pages.registryDetectionNoteTip4') }}<br>
              {{ $t('pages.registryDetectionNoteTip5') }}<br>
              {{ $t('pages.registryDetectionNoteTip6') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="completeTitle == 'create' ? createCompleteData() : updateCompleteData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormComVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <zip-entries-tree ref="zipTree" @selected="handleMainExeSelected" @cancel="cancelUpload" @close="handleZipTreeClose"/>
    <software-repository-select ref="softwareSelect" :add-app-type-able="addAppTypeAble" :single-import="true" :upload-status="2" @select="handleImportSelectedSoftware"/>
  </div>
</template>

<script>
import {
  checkFile, supportedSuffixes, createStrategy, updateStrategy, getStategyPage, getFileProps, deleteStrategy, architectures, endTask, reUploadFtp
} from '@/api/system/terminalManage/taskConfig'
import md5 from '@/utils/md5'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter, parseTime } from '@/utils'
import ZipEntriesTree from '@/views/system/terminalManage/softwareManager/repository/zipEntriesTree'
import {
  uploadChunk,
  getFileSuffix
} from '@/api/system/terminalManage/softwareRepository'
import { validatePolicy } from '@/utils/validate'
import { uploadSoftwareStatus } from '@/api/system/terminalManage/softwareRepository'
import SoftwareRepositorySelect from '@/views/system/terminalManage/softwareManager/repository/select'
// import moment from 'moment'

export default {
  name: 'TaskConfig',
  components: { ZipEntriesTree, SoftwareRepositorySelect },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      showTree: true,
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      colModel: [
        { prop: 'name', label: 'taskName2', width: '100', sort: 'custom' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'createTime', label: 'createTime', width: '200', sort: true },
        { prop: '', label: 'taskMessage', width: '200', formatter: this.taskFormatter },
        { prop: 'uploadStatus', label: 'taskFileState', width: '100', formatter: this.statusFormatter },
        { prop: 'taskActive', label: 'effectiveStatus', width: '100', formatter: this.taskActiveFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 200,
          buttons: [
            { label: 'detail', formatter: this.buttonFormatter, click: this.handleDetail },
            { formatter: this.saveButtonFormatter, click: this.handleCopy },
            { label: 'reUpload', formatter: this.reUploadButtonFormatter, click: this.handleReUpload }
          ]
        }
      ],
      detectionColModel: [
        { prop: 'type', label: 'type', width: '100', sort: true, formatter: this.typeFormatter },
        { prop: 'checkValue', label: 'typeValue', width: '100', sort: true },
        { prop: 'version', label: 'softwareVersion', width: '100', sort: true },
        { prop: 'regItem', label: 'registryKeyItem', width: '100', sort: true },
        { prop: 'regKey', label: 'registryKey', width: '100', sort: true },
        { prop: 'matchType', label: 'matchType', width: '100', sort: true, formatter: this.matchTypeFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', isShow: this.showButton, click: this.delecteUpdate }
          ]
        }
      ],
      conditionColModel: [
        { prop: 'type', label: 'type', width: '100', sort: true, formatter: this.typeFormatter },
        { prop: 'checkValue', label: 'typeValue', width: '100', sort: true },
        { prop: 'version', label: 'softwareVersion', width: '100', sort: true },
        { prop: 'regItem', label: 'registryKeyItem', width: '100', sort: true },
        { prop: 'regKey', label: 'registryKey', width: '100', sort: true },
        { prop: 'regValue', label: 'registryKeyValue', width: '120', sort: true },
        { prop: 'matchType', label: 'matchType', width: '100', sort: true, formatter: this.matchTypeFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', isShow: this.showButton, click: this.completeUpdate }
          ]
        }
      ],
      loading: false,
      oldTemp: {},
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        taskType: 1,
        beginTime: parseTime(new Date(), 'y-m-d h:i'),
        endTime: '',
        osType: 1,
        architecture: null, // 架构
        fileName: '', // 安装包名称
        mainExe: '', // 主安装程序
        executeType: 1, // 执行方式
        showAlarmTime: 10, // 弹窗提醒时间
        userType: 1, // 用户类型
        executeCommand: null, // 执行命令
        repeatTimes: 3, // 执行失败次数
        intervalTime: 5, // 执行失败重复执行间隔时间/分钟
        savePath: '#MAX#', // 文件保存路径
        autoDeleteFile: 0, // 任务执行完毕自动删除文件
        deleteTask: 0, // 允许终端结束任务
        silentExecute: 0, // 终端不显示任务
        taskTips: 0, // 任务执行完成提醒
        taskFailTips: 0, // 执行失败时提醒
        detectionList: [], // 满足条件不执行列表
        detectionExecuteList: [], // 满足条件执行列表
        limitSystem: 1, // 是否限制系统
        sysVerOperator: null, // 操作系统关系（大于小于等于....）
        sysVerDesk: null, // 操作系统版本名称
        sysVerService: null,
        remark: '',
        md5: undefined,
        quickMd5: undefined,
        fileGuid: undefined,
        completeList: [], // 完成条件列表
        completeResRule: 1, // 检测条件的执行规则(1:满足任意一个规则；2：满足所有规则)
        entityType: undefined,
        entityId: undefined,
        fileStatus: 0,
        source: 0, // 是否从软件库导入(1:是)
        uploadStatus: null,
        size: null,
        softId: null, // 从软件库导入的软件id
        mandatory: 0
      },
      completeTemp: {},
      defaultCompleteTemp: { // 表单字段
        id: undefined,
        type: 1, // 完成条件类型
        matchType: 0, // 匹配方式
        matchTypeDesc: '', // 匹配方式描述
        regItem: '', // 注册表项
        regKey: '', // 注册表键
        regValue: '', // 注册表值
        checkValue: '',
        version: '' // 软件版本
      },
      configTemp: {},
      configRowData: [],
      rules: {
        fileName: [{ required: true, message: this.$t('pages.noFileSelected'), trigger: ['blur', 'change'] }],
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        platform: [{ required: true, message: this.$t('pages.softwarePlatformSelectTips'), trigger: 'blur' }],
        architecture: [{ required: true, validator: this.archValidator, trigger: ['blur', 'change'] }],
        mainExe: [{ required: true, message: this.$t('pages.mainExeInputTips'), trigger: 'blur' }],
        beginTime: [{ required: true, validator: this.beginTimeValidator, trigger: 'blur' }],
        endTime: [{ required: true, validator: this.endTimeValidator, trigger: 'blur' }],
        savePath: [{ validator: this.savePathValidator, trigger: 'blur' }],
        executeCommand: [{ validator: this.executeCommandValidator, trigger: 'blur' }],
        repeatTimes: [{ validator: this.repeatTimesValidator, trigger: ['blur', 'change'] }],
        intervalTime: [{ validator: this.intervalTimeValidator, trigger: ['blur', 'change'] }],
        serverConfig: [{ validator: this.serverConfigValidator, trigger: ['blur', 'change'] }]
      },
      completeRules: {
        type: [{ required: true, message: this.$t('pages.configValiTips'), trigger: 'blur' }],
        checkValue: [{ required: true, message: this.$t('pages.configValiTips'), trigger: 'blur' }],
        filePath: [{ required: true, validator: this.filePathValidator, trigger: 'blur' }],
        regItem: [{ required: true, validator: this.regItemValidator, trigger: 'blur' }],
        regKey: [{ required: true, message: this.$t('pages.configValiTips'), trigger: 'blur' }],
        regValue: [{ required: true, message: this.$t('pages.configValiTips'), trigger: 'blur' }],
        matchType: [{ required: true, message: this.$t('pages.configValiTips'), trigger: 'blur' }]
      },
      //  文件路径快捷按钮
      filePathShortcutKeys: [
        { label: this.$t('pages.desktop'), path: '#DESKTOP#' },       //  桌面
        { label: this.$t('pages.userTempDirectory'), path: '#USERTEMP#' },       //  用户临时目录
        { label: this.$t('pages.maxDiskSpace'), path: '#MAX#' }       //  剩余最大磁盘空间
      ],
      configRules: {
        name: [{ required: true, message: this.$t('pages.machineCode_text1'), trigger: 'blur' }],
        code: [
          { required: true, message: this.$t('pages.machineCode_text2'), trigger: 'blur' },
          { validator: this.processNameValidator, trigger: 'blur' },
          { min: 8, max: 8, message: this.$t('pages.machineCode_text3'), trigger: 'blur' }
        ]
      },
      taskActiveOptions: {
        0: this.$t('pages.taskEffectiving'),
        1: this.$t('pages.taskEffectiving'),
        2: this.$t('pages.taskUnEffective'),
        3: this.$t('pages.taskUnEffective'),
        4: this.$t('pages.taskNoEffective')
      },
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        objectType: undefined,
        objectId: undefined,
        endTaskFlag: null
        // startDate: moment(new Date(new Date().setMonth(new Date().getMonth() - 1))).format('YYYY-MM-DD'),
        // endDate: moment(new Date()).format('YYYY-MM-DD')
      },
      showDlg: false,
      dialogStatus: '',
      editFlag: false,
      submitting: false,
      processEditable: false,
      processDeleteable: false,
      textMap: {
        create: this.i18nConcatText(this.$t('pages.taskDistributeConfig'), 'create'),
        update: this.i18nConcatText(this.$t('pages.taskDistributeConfig'), 'update'),
        detail: this.i18nConcatText(this.$t('pages.taskDistributeConfig'), 'details')
      },
      titleMap: {
        create: this.i18nConcatText(this.$t('pages.detectionRules'), 'create'),
        update: this.i18nConcatText(this.$t('pages.detectionRules'), 'update')
      },
      dialogFormVisible: false,
      dialogFormComVisible: false,
      completeTitle: 'create',
      detecteExecuteFlag: false,
      activeName: 'baseConfig',
      completeCon: null,
      osTypeOperators: [
        { value: 1, label: this.$t('pages.greaterThan') },        //  大于
        { value: 2, label: this.$t('pages.equalTo') },        //  等于
        { value: 3, label: this.$t('pages.lessThan') },        //  小于
        { value: 4, label: this.$t('pages.greaterThanOrEqualTo') },        //  大于等于
        { value: 5, label: this.$t('pages.termSecurityLessThanOrEqual') }        //  小于等于
      ],
      osTypeOperatorDesc: {
        1: this.$t('pages.greaterThan'),
        2: this.$t('pages.equalTo'),
        3: this.$t('pages.lessThan'),
        4: this.$t('pages.greaterThanOrEqualTo'),
        5: this.$t('pages.termSecurityLessThanOrEqual')
      },
      comConditionOptions: [
        { value: 1, label: this.$t('pages.szSoftName') },
        { value: 2, label: this.$t('table.servername') },
        { value: 3, label: this.$t('table.processName') },
        { value: 4, label: this.$t('pages.filePath') },
        { value: 5, label: this.$t('pages.regItem') },
        { value: 6, label: this.$t('pages.regValue') }
      ],
      architectureList: [1, 2, 8],
      windowsOsTypes: [],
      windowsServerOsTypes: [],
      supportOsTypes: [
        //  Windows 11及以上
        { type: 1, os: 'Windows 11', label: this.$t('pages.termSecurityWindowsAndAbove') },
        //  Windows Server 2025及以上
        { type: 2, os: 'Windows Server 2025', label: this.$t('pages.termSecurityWindowsServerAndAbove') },

        { type: 1, os: 'Windows 10', label: 'Windows 10' },
        { type: 2, os: 'Windows Server 2022', label: 'Windows Server 2022' },

        { type: 1, os: 'Windows 8.1', label: 'Windows 8.1' },
        { type: 2, os: 'Windows Server 2019', label: 'Windows Server 2019' },

        { type: 1, os: 'Windows 8', label: 'Windows 8' },
        { type: 2, os: 'Windows Server 2016', label: 'Windows Server 2016' },

        { type: 1, os: 'Windows 7', label: 'Windows 7' },
        { type: 2, os: 'Windows Server 2012 R2', label: 'Windows Server 2012 R2' },

        { type: 1, os: 'Windows Vista', label: 'Windows Vista' },
        { type: 2, os: 'Windows Server 2012', label: 'Windows Server 2012' },

        { type: 1, os: 'Windows XP', label: 'Windows XP' },
        { type: 2, os: 'Windows Server 2008 R2', label: 'Windows Server 2008 R2' },

        { type: 2, os: 'Windows Server 2008', label: 'Windows Server 2008' },
        { type: 2, os: 'Windows Server 2003 R2', label: 'Windows Server 2003 R2' },

        { type: 2, os: 'Windows Server 2003', label: 'Windows Server 2003' }
      ],
      showMainExe: false,
      isMsi: false,
      uploadPercent: 0,
      uploadCanceled: false,
      completeDelete: false,
      detectionDelete: false,
      detectionExecuteDelete: false,
      pickerOptions: { // 时间设置今天以及今天之后
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      archMap: {},
      architectures,
      statusOptions: [{ value: null, label: this.$t('pages.all') }, { value: 0, label: this.$t('pages.taskEffectiving') }, { value: 1, label: this.$t('pages.taskUnEffective') }, { value: 2, label: this.$t('pages.taskNoEffective') }],
      addAppTypeAble: false,
      singleImport: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    accept() {
      if (this.temp.taskType == 1) {
        return supportedSuffixes.filter(item => item.type == 1).map(item => item.value).join()
      } else if (this.temp.taskType == 2) {
        return supportedSuffixes.filter(item => item.type == 2).map(item => item.value).join()
      } else {
        return null
      }
    },
    matchTypeOptions() {
      if (this.completeTemp.type == 1 || this.completeTemp.type == 2 || this.completeTemp.type == 3) {
        return [
          { value: 0, label: this.$t('pages.matchTypeOptions2') },
          { value: 1, label: this.$t('pages.matchTypeOptions1') }
        ]
      } else if (this.completeTemp.type == 4 || this.completeTemp.type == 5) {
        return [
          { value: 0, label: this.$t('pages.exist') },
          { value: 1, label: this.$t('pages.notExist') }
        ]
      } else if (this.completeTemp.type == 6) {
        return [
          { value: 0, label: this.$t('pages.exist') },
          { value: 1, label: this.$t('pages.notExist') },
          { value: 2, label: this.$t('pages.termSecurityGreaterThan') },
          { value: 3, label: this.$t('pages.termSecurityEqual') },
          { value: 4, label: this.$t('pages.termSecurityLessThan') },
          { value: 5, label: this.$t('pages.termSecurityGreaterThanOrEqual') },
          { value: 6, label: this.$t('pages.termSecurityLessThanOrEqual') },
          { value: 7, label: this.$t('pages.termSecurityContain') },
          { value: 8, label: this.$t('pages.termSecurityExclude') }
        ]
      } else {
        return [
          { value: 0, label: this.$t('pages.matchTypeOptions2') },
          { value: 1, label: this.$t('pages.matchTypeOptions1') }
        ]
      }
    }
  },
  created() {
    this.$socket.subscribe({
      url: '/topic/refreshFileUploadStatus',
      callback: (resp, handle) => {
        this.handleFilter()
      }
    })
    this.resetTemp()
    this.resetCompleteTemp()
    this.windowsOsTypes = this.supportOsTypes.filter(item => item.type === 1);
    this.windowsServerOsTypes = this.supportOsTypes.filter(item => item.type === 2);
    architectures.forEach(arch => {
      this.archMap[arch.value] = arch.label
    })
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    handleDialogClose() {
      this.cancelUpload()
    },
    tabClick(pane, event) {
    },
    treeNodeCheckChange: function(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) >= 0) {
        return false
      }
    },
    typeChange() {
      this.completeTemp.matchType = 0
    },
    taskTypeChange(data) {
      if (data == 1 && this.activeName == 'taskCompleteCon') {
        this.activeName = 'baseConfig'
      } else if (data == 2 && this.activeName == 'taskCompleteCon') {
        this.activeName = 'baseConfig'
      } else if (data == 3 && (this.activeName == 'taskDetectionCon' || this.activeName == 'taskCompleteCon')) {
        this.activeName = 'baseConfig'
      }
      if (data == 3) {
        this.temp.autoDeleteFile = 0
        this.temp.silentExecute = 0
      }
    },
    regItemValidator(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.registryDetectionValidMsg1') + ''))
      }
      //  不能以 \ 结尾
      if (value.substr(value.length - 1) === '\\') {
        callback(new Error(this.$t('pages.registryDetectionValidMsg2') + ''))
      }
      //  必须已HKEY_CLASSES_ROOT HKEY_CURRENT_USER HKEY_LOCAL_MACHINE HKEY_USERS HKEY_CURRENT_CONFIG 开头
      const beginSuffix = ['HKEY_CLASSES_ROOT', 'HKEY_CURRENT_USER', 'HKEY_LOCAL_MACHINE', 'HKEY_USERS', 'HKEY_CURRENT_CONFIG']
      const split = value.split('\\');
      if (split.length > 0) {
        const suffix = split[0].toUpperCase();
        if (!beginSuffix.includes(suffix)) {
          callback(new Error(this.$t('pages.registryDetectionValidMsg3') + ''))
        }
      }
      callback()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.showMainExe = false
      this.activeName = 'baseConfig'
      this.editFlag = false
      this.architectureList = [1, 2, 8]
    },
    handleArchChange() {
      if (!this.architectureList || this.architectureList.length === 0) {
        this.temp.architecture = 0
      } else {
        this.temp.architecture = this.getSum(this.architectureList)
      }
      this.$refs.dataForm.validateField('architecture')
    },
    resetCompleteTemp() {
      this.completeTemp = Object.assign({}, this.defaultCompleteTemp)
    },
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.handleFilter()
    },
    archFormatter(row, data) {
      const architectures = this.numToList(data, 4)
      return architectures.map(arch => this.archMap[arch]).join(', ')
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    completeSelectionChangeEnd: function(rowDatas) {
      this.completeDelete = rowDatas && rowDatas.length > 0
    },
    detectionSelectionChangeEnd: function(rowDatas) {
      this.detectionDelete = rowDatas && rowDatas.length > 0
    },
    detectionExecuteSelectionChangeEnd: function(rowDatas) {
      this.detectionExecuteDelete = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      // if (this.query.startDate && this.query.endDate) {
      //   this.query.startDate = moment(this.query.startDate).format('YYYY-MM-DD')
      //   this.query.endDate = moment(this.query.endDate).format('YYYY-MM-DD')
      // }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    refresh() {
      return refreshPage(this)
    },
    taskFormatter(row, data) {
      let msg = ''
      if (row.taskType == 1) {
        msg += this.$t('pages.taskType') + '：' + this.$t('pages.installSoft') + '；'
      } else if (row.taskType == 2) {
        msg += this.$t('pages.taskType') + '：' + this.$t('pages.executeFile') + '；'
      } else if (row.taskType == 3) {
        msg += this.$t('pages.taskType') + '：' + this.$t('pages.sendDocument') + '；'
      }
      if (row.beginTime) {
        msg += this.$t('pages.startTime') + '：' + row.beginTime + '；'
      }
      if (row.endTime) {
        msg += this.$t('pages.endTime') + '：' + row.endTime + '；'
      }
      if (row.osType == 1) {
        msg += this.$t('pages.clientType') + '：' + 'Windows' + '；'
      } else if (row.osType == 2) {
        msg += this.$t('pages.clientType') + '：' + 'Linux' + '；'
      } else if (row.osType == 3) {
        msg += this.$t('pages.clientType') + '：' + 'Mac' + '；'
      }
      if (row.architecture) {
        const architectures = this.numToList(row.architecture, 4)
        msg += this.$t('table.architecture') + '：' + architectures.map(arch => this.archMap[arch]).join(', ') + '；'
      }
      if (row.sysVerOperator) {
        if (row.sysVerService) {
          msg += this.$t('pages.termSecurityHostCheckWay2', { operator: this.osTypeOperatorDesc[row.sysVerOperator], windowsFigure: row.sysVerDesk, windowsServer: row.sysVerService }) + '；'
        } else {
          msg += this.$t('pages.termSecurityHostCheckWay3', { operator: this.osTypeOperatorDesc[row.sysVerOperator], windowsFigure: row.sysVerDesk }) + '；'
        }
      } else {
        msg += this.$t('pages.limitSystem') + '：' + (row.limitSystem == 1 ? this.$t('text.yes') : this.$t('text.no')) + '；'
      }
      if (row.fileName) {
        msg += this.$t('table.fileName') + '：' + row.fileName + '；'
      }
      if (row.taskType != 3) {
        if (row.executeType == 1) {
          msg += this.$t('pages.executeType') + '：' + this.$t('pages.autoExecute') + '；'
          if (row.userType == 1) {
            msg += this.$t('pages.userType') + '：' + this.$t('pages.systemUser1') + '；'
          } else if (row.userType == 2) {
            msg += this.$t('pages.userType') + '：' + this.$t('pages.currentUser') + '；'
          } else if (row.userType == 3) {
            msg += this.$t('pages.userType') + '：' + this.$t('pages.appointUser') + '；'
          }
          if (row.executeCommand) {
            msg += this.$t('pages.executeCommand') + '：' + row.executeCommand + '；'
          }
        } else if (row.executeType == 2) {
          msg += this.$t('pages.executeType') + '：' + this.$t('pages.showTipsExecute') + '；'
          if (row.showAlarmTime) {
            msg += this.$t('pages.showAlarmTime') + '：' + row.showAlarmTime + '；'
          }
        }
        if (row.repeatTimes) {
          msg += this.$t('pages.repeatTimes') + '：' + row.repeatTimes + '；'
        }
        if (row.intervalTime) {
          msg += this.$t('pages.intervalTimes') + '：' + row.intervalTime + '；'
        }
        msg += this.$t('pages.autoDeleteFile') + '：' + (row.autoDeleteFile == 1 ? this.$t('text.yes') : this.$t('text.no')) + '；'
        msg += this.$t('pages.silentExecute') + '：' + (row.silentExecute == 1 ? this.$t('text.yes') : this.$t('text.no')) + '；'
        if ((row.detectionList && row.detectionList.length > 0) || (row.detectionExecuteList && row.detectionExecuteList.length > 0)) {
          msg += this.$t('pages.detectionCondition') + '；'
        }
        if (row.completeList && row.completeList.length > 0) {
          msg += this.$t('pages.completionCondition') + '；'
        }
      }
      if (row.mainExe) {
        msg += this.$t('table.mainExe') + '：' + row.mainExe + '；'
      }
      if (row.savePath) {
        msg += this.$t('pages.savePath1') + '：' + row.savePath + '；'
      }
      msg += this.$t('pages.deleteTask1') + '：' + (row.deleteTask == 1 ? this.$t('text.yes') : this.$t('text.no')) + '；'
      msg += this.$t('pages.taskTips') + '：' + (row.taskTips == 1 ? this.$t('text.yes') : this.$t('text.no')) + '；'
      msg += this.$t('pages.taskFailTips') + '：' + (row.taskFailTips == 1 ? this.$t('text.yes') : this.$t('text.no')) + '；'
      return msg
    },
    statusFormatter(row, data) {
      const status = uploadSoftwareStatus[data]
      return !status ? 'Unknown status: ' + data : status
    },
    taskActiveFormatter(row) {
      return this.taskActiveOptions[row.taskActive]
    },
    buttonFormatter: function(row) {
      if (buttonFormatter(row, this) != '') {
        return this.$t('pages.detail')
      } else {
        return ''
      }
    },
    reUploadButtonFormatter: function(row) {
      if (buttonFormatter(row, this) != '' && row.uploadStatus > 1000) {
        return this.$t('table.reUpload')
      } else {
        return ''
      }
    },
    endTaskButtonFormatter(row) {
      if (this.buttonFormatter(row, this) != '' && row.taskActive != 2) {
        return this.$t('pages.handleEndTask')
      } else {
        return ''
      }
    },
    saveButtonFormatter(row) {
      if (this.buttonFormatter(row, this) != '') {
        return this.$t('components.saveAs')
      } else {
        return ''
      }
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStategyPage(searchQuery)
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (type == 3) {
        // 过滤移动终端
        return false
      }
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDetail(row) {
      this.resetTemp()
      this.architectureList = this.numToList(row.architecture, 4)
      this.temp = JSON.parse(JSON.stringify(row))
      if (row.mainExe) {
        this.showMainExe = true
      }
      if (row.savePath && row.mandatory == undefined) {
        this.temp.mandatory = 1
      }
      this.editFlag = true
      this.dialogStatus = 'detail'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCopy(row) {
      row.id = undefined
      row.active = false
      row.createTime = null
      row.beginTime = parseTime(new Date(), 'y-m-d h:i')
      this.resetTemp()
      this.architectureList = this.numToList(row.architecture, 4)
      this.temp = JSON.parse(JSON.stringify(row))
      this.temp.endTime = ''
      if (row.mainExe) {
        this.showMainExe = true
      }
      this.dialogStatus = 'create'
      this.editFlag = false
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleReUpload(row) {
      row.uploadStatus = 1
      return reUploadFtp(row).finally(() => this.handleFilter())
    },
    archValidator(rule, value, callback) {
      if (!value) {
        callback(this.$t('pages.softwareArchSelectTips'))
      } else {
        callback()
      }
    },
    beginTimeValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.webFlow_text2')))
      } else if (this.compareDate(this.temp.beginTime, this.temp.endTime) != 1) {
        callback(new Error(this.$t('pages.timeValiTips')))
      } else {
        callback()
      }
    },
    endTimeValidator(rule, value, callback) {
      const now = parseTime(new Date(), 'y-m-d h:i')
      if (!value) {
        callback(new Error(this.$t('pages.webFlow_text2')))
      } else if (this.compareDate(this.temp.beginTime, this.temp.endTime) != 1) {
        callback(new Error(this.$t('pages.timeValiTips1')))
      } else if (this.compareDate(now, this.temp.endTime) != 1) {
        callback(new Error(this.$t('pages.timeValiTips2')))
      } else {
        callback()
      }
    },
    //  校验文件路径
    savePathValidator(rule, value, callback) {
      if (!value) {
        callback()
      }
      if (!this.verifyValidityOfCharacters(value)) {
        callback(new Error(this.$t('pages.fileDetectionFilePathMsg1')))
      } else if (this.validExistMultiShortcut(value)) {
        callback(new Error())
      } else if (!this.validSavePath(value)) {
        callback(new Error(this.$t('pages.savePathTips')))
      }
      callback()
    },
    filePathValidator(rule, value, callback) {
      if (!this.completeTemp.checkValue) {
        callback(new Error(this.$t('pages.configValiTips')))
      }
      if (!this.validFilePath(this.completeTemp.checkValue)) {
        callback(new Error(this.$t('pages.savePathTips')))
      }
      callback()
    },
    executeCommandValidator(rule, value, callback) {
      if (value && this.validExistMultiKey(value)) {
        callback(new Error(this.$t('pages.executeCommandValiTips')))
      }
      callback()
    },
    repeatTimesValidator(rule, value, callback) {
      if (this.temp.intervalTime && this.temp.intervalTime > 0 && (this.temp.repeatTimes == 0 || this.temp.repeatTimes == undefined)) {
        callback(new Error(this.$t('pages.taskDistributeText')))
      } else if (this.temp.intervalTime == undefined || this.temp.intervalTime == 0 && (this.temp.repeatTimes == 0 || this.temp.repeatTimes == undefined)) {
        this.$refs['dataForm'].clearValidate('intervalTime')
      }
      callback()
    },
    intervalTimeValidator(rule, value, callback) {
      if (this.temp.repeatTimes && this.temp.repeatTimes > 0 && (this.temp.intervalTime == 0 || this.temp.intervalTime == undefined)) {
        callback(new Error(this.$t('pages.taskDistributeText1')))
      } else if (this.temp.repeatTimes == undefined || this.temp.repeatTimes == 0 && (this.temp.intervalTime == 0 || this.temp.intervalTime == undefined)) {
        this.$refs['dataForm'].clearValidate('repeatTimes')
      }
      callback()
    },
    serverConfigValidator(rule, value, callback) {
      if (this.temp.limitSystem) {
        callback()
      }
      if (!this.temp.sysVerOperator || (this.temp.sysVerOperator && !(!!this.temp.sysVerDesk || !!this.temp.sysVerService))) {
        callback(new Error(this.$t('pages.configValiText')))
      }
      callback()
    },
    validExistMultiKey(value) {
      //  全命令执行命令只允许出现一次#exepath#
      const path = ['#exepath#']
      let count = 0
      for (let i = 0, len = path.length; i < len; i++) {
        const key = path[i];
        const start = value.indexOf(key);
        const last = value.lastIndexOf(key)
        if (start > -1) {
          count++;
          if (last !== start) {
            count++;
          }
        }
        if (count > 1) {
          return true;
        }
      }
      return false;
    },
    //  验证字符的有效性
    verifyValidityOfCharacters(value) {
      const notAllowExits = ['/', '*', '?', '"', '<', '>', '|'];
      const values = value.split('') || [];
      return values.findIndex(item => notAllowExits.includes(item)) === -1;
    },
    // 校验是否存在多个特殊字符  true表示存在多个快捷键，例如：多个 #desktop# 或 一个#desktop# 一个#usertemp#
    validExistMultiShortcut(value) {
      //  文件路径不能存在 多个快捷键
      let count = 0
      for (let i = 0, len = this.filePathShortcutKeys.length; i < len; i++) {
        const key = this.filePathShortcutKeys[i].path;
        const start = value.indexOf(key);
        const last = value.lastIndexOf(key)
        if (start > -1) {
          count++;
          if (last !== start) {
            count++;
          }
        }
        if (count > 1) {
          return true;
        }
      }
      return false
    },
    validSavePath(value) {
      const path = this.filePathShortcutKeys.map(item => item.path)
      if (path.indexOf(value) > -1) {
        return true
      }
      const reg = new RegExp('^[a-zA-Z]:\\\\(((?![<>:"/\\\\|?*]).)+((?<![ .])\\\\)?)*$')
      if (reg.test(value)) {
        return true
      }
      return false
    },
    validFilePath(value) {
      const reg = new RegExp('^[a-zA-Z]:\\\\(((?![<>:"/\\\\|?*]).)+((?<![ .])\\\\)?)*$')
      if (reg.test(value)) {
        return true
      }
      return false
    },
    compareDate(date1, date2) {
      if (date1 && date2) {
        const startDate = date1.replace(/\:|\s|-/g, '')
        const endDate = date2.replace(/\:|\s|-/g, '')
        if (startDate >= endDate) {
          return -1
        } else if (startDate < endDate) {
          return 1
        }
        return 0
      } else {
        return 1
      }
    },
    executeTypeChange(data) {
      if (data == 2) {
        this.temp.silentExecute = 0
      } else {
        if (this.temp.userType == undefined) {
          this.$set(this.temp, 'userType', 1)
        }
      }
    },
    limitSystemChange(data) {
      if (data == 1) {
        this.temp.sysVerOperator = null
        this.temp.sysVerDesk = null
        this.temp.sysVerService = null
      }
    },
    silentExecuteChange(data) {
      if (data == 1) {
        this.temp.deleteTask = 0
        this.temp.taskTips = 0
        this.temp.taskFailTips = 0
      }
    },
    taskTipsChange(data) {
      if (data == 0) {
        this.temp.taskFailTips = 0
      }
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleEndTask() {
      this.$confirmBox(this.$t('pages.isEndTask'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        endTask({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.taskEndSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    createCompleteCon() {
      this.resetCompleteTemp()
      this.completeTitle = 'create'
      this.detecteExecuteFlag = false
      this.dialogFormComVisible = true
      this.$nextTick(() => {
        this.$refs['conditionForm'].clearValidate()
      })
    },
    createDetectionCon() {
      this.resetCompleteTemp()
      this.completeTitle = 'create'
      this.detecteExecuteFlag = true
      this.dialogFormComVisible = true
      this.$nextTick(() => {
        this.$refs['conditionForm'].clearValidate()
      })
    },
    deleteCompleteCon() {
      const selectIds = this.$refs.completeList.getSelectedIds()
      this.$refs.completeList.deleteRowData(selectIds)
    },
    deleteDetectionCon() {
      const selectIds = this.$refs.detectionList.getSelectedIds()
      this.$refs.detectionList.deleteRowData(selectIds)
    },
    deleteDetecteExecuteCon() {
      const selectIds = this.$refs.detectionExecuteList.getSelectedIds()
      this.$refs.detectionExecuteList.deleteRowData(selectIds)
    },
    typeFormatter(row, data) {
      var label = ''
      this.comConditionOptions.forEach(item => {
        if (item.value == data) {
          label = item.label
        }
      })
      return label
    },
    matchTypeFormatter(row, data) {
      return row.matchTypeDesc
    },
    formatConData() {
      if (this.completeTemp.type != 1) {
        this.completeTemp.version = ''
      }
      if (this.completeTemp.type != 5 && this.completeTemp.type != 6) {
        this.completeTemp.regItem = ''
      }
      if (this.completeTemp.type != 6) {
        this.completeTemp.regKey = ''
        this.completeTemp.regValue = ''
      }
      if (this.completeTemp.type == 5 || this.completeTemp.type == 6) {
        this.completeTemp.checkValue = ''
      }
    },
    createCompleteData() {
      this.formatConData()
      this.$refs['conditionForm'].validate((valid) => {
        if (valid) {
          this.completeTemp.id = new Date().getTime()
          if (this.activeName == 'taskDetectionCon') {
            if (!this.detecteExecuteFlag) {
              if (this.temp.detectionList && this.temp.detectionList.length >= 10) {
                this.$message({
                  message: this.$t('pages.addRuleTips'),
                  type: 'error',
                  duration: 2000
                })
                return
              }
              const existIndex = this.temp.detectionList.findIndex(exsitData => {
                if (this.completeTemp.type == 1 && exsitData.type == this.completeTemp.type && exsitData.checkValue == this.completeTemp.checkValue &&
                exsitData.version == this.completeTemp.version && exsitData.matchType == this.completeTemp.matchType) {
                  return true
                } else if (this.completeTemp.type == 5 && exsitData.type == this.completeTemp.type &&
                exsitData.regItem == this.completeTemp.regItem) {
                  return true
                } else if (this.completeTemp.type == 6 && exsitData.type == this.completeTemp.type &&
                exsitData.regItem == this.completeTemp.regItem && exsitData.regKey == this.completeTemp.regKey && exsitData.regValue == this.completeTemp.regValue) {
                  return true
                } else {
                  return (this.completeTemp.type != 1 && this.completeTemp.type != 5 && this.completeTemp.type != 6) && exsitData.type == this.completeTemp.type && exsitData.checkValue == this.completeTemp.checkValue
                }
              })
              this.matchTypeOptions.forEach((data, value) => {
                if (this.completeTemp.matchType == value) {
                  this.completeTemp.matchTypeDesc = data.label
                }
              })
              if (existIndex > -1) {
                this.temp.detectionList.splice(existIndex, 1, this.completeTemp)
              } else {
                this.temp.detectionList.push(this.completeTemp)
              }
            } else {
              if (this.temp.detectionExecuteList && this.temp.detectionExecuteList.length >= 10) {
                this.$message({
                  message: this.$t('pages.addRuleTips'),
                  type: 'error',
                  duration: 2000
                })
                return
              }
              const existIndex = this.temp.detectionExecuteList.findIndex(exsitData => {
                if (this.completeTemp.type == 1 && exsitData.type == this.completeTemp.type && exsitData.checkValue == this.completeTemp.checkValue &&
                exsitData.version == this.completeTemp.version && exsitData.matchType == this.completeTemp.matchType) {
                  return true
                } else if (this.completeTemp.type == 5 && exsitData.type == this.completeTemp.type &&
                exsitData.regItem == this.completeTemp.regItem) {
                  return true
                } else if (this.completeTemp.type == 6 && exsitData.type == this.completeTemp.type &&
                exsitData.regItem == this.completeTemp.regItem && exsitData.regKey == this.completeTemp.regKey && exsitData.regValue == this.completeTemp.regValue) {
                  return true
                } else {
                  return (this.completeTemp.type != 1 && this.completeTemp.type != 5 && this.completeTemp.type != 6) && exsitData.type == this.completeTemp.type && exsitData.checkValue == this.completeTemp.checkValue
                }
              })
              this.matchTypeOptions.forEach((data, value) => {
                if (this.completeTemp.matchType == value) {
                  this.completeTemp.matchTypeDesc = data.label
                }
              })
              if (existIndex > -1) {
                this.temp.detectionExecuteList.splice(existIndex, 1, this.completeTemp)
              } else {
                this.temp.detectionExecuteList.push(this.completeTemp)
              }
            }
          } else if (this.activeName == 'taskCompleteCon') {
            if (this.temp.completeList && this.temp.completeList.length >= 10) {
              this.$message({
                message: this.$t('pages.addRuleTips'),
                type: 'error',
                duration: 2000
              })
              return
            }
            const existIndex = this.temp.completeList.findIndex(exsitData => {
              if (this.completeTemp.type == 1 && exsitData.type == this.completeTemp.type && exsitData.checkValue == this.completeTemp.checkValue &&
              exsitData.version == this.completeTemp.version && exsitData.matchType == this.completeTemp.matchType) {
                return true
              } else if (this.completeTemp.type == 5 && exsitData.type == this.completeTemp.type &&
              exsitData.regItem == this.completeTemp.regItem) {
                return true
              } else if (this.completeTemp.type == 6 && exsitData.type == this.completeTemp.type &&
              exsitData.regItem == this.completeTemp.regItem && exsitData.regKey == this.completeTemp.regKey && exsitData.regValue == this.completeTemp.regValue) {
                return true
              } else {
                return (this.completeTemp.type != 1 && this.completeTemp.type != 5 && this.completeTemp.type != 6) && exsitData.type == this.completeTemp.type && exsitData.checkValue == this.completeTemp.checkValue
              }
            })
            this.matchTypeOptions.forEach((data, value) => {
              if (this.completeTemp.matchType == value) {
                this.completeTemp.matchTypeDesc = data.label
              }
            })
            if (existIndex > -1) {
              this.temp.completeList.splice(existIndex, 1, this.completeTemp)
            } else {
              this.temp.completeList.push(this.completeTemp)
            }
          }
          this.dialogFormComVisible = false
        }
      })
    },
    updateCompleteData() {
      this.formatConData()
      this.$refs['conditionForm'].validate((valid) => {
        if (valid) {
          if (this.activeName == 'taskDetectionCon') {
            if (!this.detecteExecuteFlag) {
              const existIndex = this.temp.detectionList.findIndex(exsitData => {
                return exsitData.id == this.completeTemp.id
              })
              this.matchTypeOptions.forEach((data, value) => {
                if (this.completeTemp.matchType == value) {
                  this.completeTemp.matchTypeDesc = data.label
                }
              })
              if (existIndex > -1) {
                this.temp.detectionList.splice(existIndex, 1, this.completeTemp)
              }
            } else {
              const existIndex = this.temp.detectionExecuteList.findIndex(exsitData => {
                return exsitData.id == this.completeTemp.id
              })
              this.matchTypeOptions.forEach((data, value) => {
                if (this.completeTemp.matchType == value) {
                  this.completeTemp.matchTypeDesc = data.label
                }
              })
              if (existIndex > -1) {
                this.temp.detectionExecuteList.splice(existIndex, 1, this.completeTemp)
              }
            }
          } else if (this.activeName == 'taskCompleteCon') {
            const existIndex = this.temp.completeList.findIndex(exsitData => {
              return exsitData.id == this.completeTemp.id
            })
            this.matchTypeOptions.forEach((data, value) => {
              if (this.completeTemp.matchType == value) {
                this.completeTemp.matchTypeDesc = data.label
              }
            })
            if (existIndex > -1) {
              this.temp.completeList.splice(existIndex, 1, this.completeTemp)
            }
          }
          this.dialogFormComVisible = false
        }
      })
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, null)) {
        return
      }
      this.submitting = true
      this.formatFormData()
      this.$refs['dataForm'].validate(async(valid, objs) => {
        if (valid && this.valiFormData()) {
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.validFailSkip(objs)
          this.submitting = false
        }
      })
    },
    validFailSkip(validObj) {
      const repeatTimes = validObj['repeatTimes'];
      const intervalTime = validObj['intervalTime']
      const savePath = validObj['savePath']
      if ((repeatTimes && repeatTimes[0] instanceof Error) ||
          (intervalTime && intervalTime[0] instanceof Error) ||
          (savePath && savePath[0] instanceof Error)) {
        this.activeName = 'excuteParamConfig'
      } else {
        this.activeName = 'baseConfig'
      }
    },
    formatFormData() {
      if (!this.architectureList || this.architectureList.length === 0) {
        this.temp.architecture = 0
      } else {
        this.temp.architecture = this.getSum(this.architectureList)
      }
      if (this.temp.taskType == 2) {
        this.temp.completeList = []
      } else if (this.temp.taskType == 3) {
        this.temp.executeType = 1
        this.temp.userType = 1
        this.temp.autoDeleteFile = 0
        this.temp.silentExecute = 0
        this.temp.executeCommand = null
        this.temp.detectionList = []
        this.temp.detectionExecuteList = []
        this.temp.completeList = []
        this.temp.completeResRule = 1
      }
      if (this.temp.executeType == 1) {
        this.temp.showAlarmTime = null
      } else if (this.temp.executeType == 2) {
        this.temp.silentExecute = 0
        this.temp.userType = null
      }
      if (this.temp.silentExecute == 1) {
        this.temp.deleteTask = 0
        this.temp.taskTips = 0
        this.temp.taskFailTips = 0
      }
    },
    //  文件路径快捷按钮
    filePathShortcutKey(path) {
      this.temp.savePath = path
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, null)) {
        return
      }
      this.submitting = true
      this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.valiFormData()) {
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    valiFormData() {
      const suffix = getFileSuffix(this.temp.fileName)
      if ((this.temp.taskType == 1 || this.temp.taskType == 2) && this.accept.split(',').indexOf(suffix) < 0) {
        if (!suffix) {
          this.$message({
            message: this.$t('pages.fileFormatUnsupportedTips1'),
            type: 'error',
            duration: 2000
          })
          return false
        }
        this.$message({
          message: this.$t('pages.fileFormatUnsupportedTips', { format: suffix }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    handleMainExeSelected(data, config) {
      this.temp.mainExe = data.mainExe
      return this.updateProps(data)
    },
    beforeUpload(file) {
      this.uploadPercent = 0
      this.uploadCanceled = false
      if (file.name.length > 200) {
        this.$message({
          title: this.$t('text.fail'),
          message: this.$t('pages.fileNameLimit'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (file.size === 0) {
        this.$message({
          title: this.$t('text.fail'),
          message: this.$t('pages.fileSizeCantZero'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const suffix = getFileSuffix(file.name)
      if ((this.temp.taskType == 1 || this.temp.taskType == 2) && this.accept.split(',').indexOf(suffix) < 0) {
        if (!suffix) {
          this.$message({
            message: this.$t('pages.fileFormatUnsupportedTips1'),
            type: 'error',
            duration: 2000
          })
          return false
        }
        this.$message({
          message: this.$t('pages.fileFormatUnsupportedTips', { format: suffix }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      this.calcFileSuffixIsMsiOrZip(suffix)
      this.checkAndUploadFile(file)
      return false
    },
    calcFileSuffixIsMsiOrZip(suffix) {
      if (!suffix) {
        this.isMsi = false
        this.showMainExe = false
        return
      }
      this.isMsi = '.msi' === suffix
      this.showMainExe = supportedSuffixes.filter(item => item.archived).map(item => item.value).includes(suffix)
    },
    //  不允许输入 / * ? " < > |
    filPathInputHandler(value) {
      const notAllowExits = ['/', '*', '?', '"', '<', '>', '|'];
      for (let i = 0; i < notAllowExits.length; i++) {
        value = value.replaceAll(notAllowExits[i], notAllowExits[i] === '/' ? '\\' : '')
      }
      this.temp.savePath = value
    },
    checkAndUploadFile(file) {
      this.loading = true
      this.oldTemp = { ...this.temp }
      this.temp.fileName = file.name
      this.temp.size = file.size
      const cancelMd5 = md5.md5File(file,
        fileMd5 => this.afterMd5Calculated(fileMd5, file),
        (loaded, total, abort) => this.onUploadProgress(loaded, total, 0, 30, abort))
      if (this.uploadCanceled) {
        cancelMd5()
      }
    },
    handleOpenSoftwareSelectDlg() {
      this.$refs['softwareSelect'].show(null, null)
    },
    afterMd5Calculated(fileMd5, file) {
      if (this.uploadCanceled) {
        return
      }
      if (fileMd5 === this.temp.md5) {
        this.$message({
          message: this.$t('pages.fileNotChange'),
          type: 'error',
          duration: 2000
        })
        this.cancelUpload()
        return
      }
      this.fileChanged = true
      const data = { fileName: file.name, fileSize: file.size, fileMd5 }
      checkFile(data).then(res => {
        if (this.uploadCanceled) {
          return
        }
        const status = res.data.status
        if (status < 50) {
          this.uploadChunks(file, fileMd5, res.data.uploadedSize).then(() => this.updateProcess(data))
        } else if (status === 50) {
          // 已上传到临时目录，但数据库未存档
          this.updateProcess(data)
        } else if (status === 100) {
          // 数据库已存档
          this.updateProcess(data)
        }
      })
    },
    onUploadProgress(loaded, total, start, weight, abort) {
      if (this.uploadCanceled) {
        abort('canceled')
        return
      }
      this.uploadPercent = start + Math.floor(loaded / total * weight)
    },
    uploadChunks(file, md5, index) {
      return uploadChunk(file, md5, index, (loaded, total, abort) => {
        this.onUploadProgress(loaded, total, 30, 50, abort)
        return this.uploadCanceled
      }).catch(this.cancelUpload)
    },
    cancelUpload() {
      this.uploadCanceled = true
      this.uploadPercent = 0
      this.loading = false
      if (this.oldTemp) {
        this.calcFilenameIsMsiOrZip(this.oldTemp.fileName)
        this.temp = this.oldTemp
      }
    },
    handleZipTreeClose(data, config) {
      this.uploadPercent = 100
      this.loading = false
      this.uploadPercent = 0
      this.temp.fileName = data.fileName
      this.temp.fileSize = data.fileSize
      this.temp.md5 = data.fileMd5
      return this.updateProps(data)
    },
    handleImportSelectedSoftware(data, type) {
      if (type == 0) {
        this.temp.softId = data[0].id
        this.temp.fileName = data[0].originalFilename
        this.temp.fileGuid = data[0].guid
        this.temp.uploadStatus = data[0].uploadStatus
        this.temp.quickMd5 = data[0].quickMd5
        this.temp.md5 = data[0].md5
        this.temp.size = data[0].size
        this.temp.active = true
        this.temp.source = 1
        if (!data[0].mainExe) {
          this.showMainExe = false
        } else {
          this.showMainExe = true
        }
        this.temp.mainExe = data[0].mainExe
      }
    },
    calcFilenameIsMsiOrZip(fileName) {
      if (!fileName) {
        this.isMsi = false
        this.showMainExe = false
        return
      }
      const suffix = getFileSuffix(fileName)
      this.calcFileSuffixIsMsiOrZip(suffix)
    },
    updateProcess(data) {
      if (this.uploadCanceled) {
        return
      }
      if (this.showMainExe) {
        this.uploadPercent = 80
        return this.$refs.zipTree.showZipEntries(data)
      }
      this.updateProps(data)
    },
    updateProps(data) {
      let timer = setInterval(() => {
        if (this.uploadCanceled || this.uploadPercent >= 99) {
          clearInterval(timer)
          timer = undefined
          return
        }
        this.uploadPercent++
      }, 500)
      return getFileProps(data).then(res => {
        if (timer) {
          clearInterval(timer)
        }
        if (this.uploadCanceled) {
          return
        }
        this.temp.fileName = res.data.fileName
        this.temp.md5 = res.data.md5
        this.temp.quickMd5 = res.data.quickMd5
        this.temp.fileGuid = res.data.fileGuid
        this.uploadPercent = 100
        this.loading = false
        this.uploadPercent = 0
      })
    },
    showButton() {
      return !this.editFlag
    },
    completeUpdate(row) {
      this.resetCompleteTemp()
      this.completeTemp = JSON.parse(JSON.stringify(row))
      this.dialogFormComVisible = true
      this.detecteExecuteFlag = true
      this.completeTitle = 'update'
      this.$nextTick(() => {
        this.$refs['conditionForm'].clearValidate()
      })
    },
    delecteUpdate(row) {
      this.resetCompleteTemp()
      this.completeTemp = JSON.parse(JSON.stringify(row))
      this.dialogFormComVisible = true
      this.detecteExecuteFlag = false
      this.completeTitle = 'update'
      this.$nextTick(() => {
        this.$refs['conditionForm'].clearValidate()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .original-file-item {
    display: flex;
    .original-file-upload {
      position: relative;
      bottom: 2px;
      margin-left: 5px;
      &.not-allowed>>>.el-upload {
        cursor: not-allowed
      }
    }
    .original-button {
      height: 30px;
      margin-top: 1px;
      margin-left: 2px;
    }
  }
  .file-upload-progress {
    height: 14px;
    line-height: 14px;
    .el-progress {
      width: calc(100% - 20px);
      display: inline-block;
      >>>.el-progress-bar {
        padding-right: 40px;
        margin-right: -47px;
      }
      >>>.el-progress__text {
        vertical-align: unset;
      }
    }
    i.el-icon-switch-button {
      cursor: pointer;
      /*color: #68a8d0;*/
      color: #f56c6c;
      font-weight: 700;
      &:hover {
        /*color: #4995c5;*/
        color: #f78989;
      }
    }
  }
</style>
