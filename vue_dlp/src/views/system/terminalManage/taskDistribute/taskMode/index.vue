<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <div class="searchCon">
          <label>{{ $t('table.effectiveStatus') }}：</label>
          <el-select v-model="query.endTaskFlag">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input v-model="query.searchInfo" v-trim :maxlength="260" clearable :placeholder="$t('pages.softwareTask_Validate4')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="taskModeList" :multi-select="false" :col-model="colModel" :row-data-api="rowDataApi"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      width="900px"
      :title="$t('table.termTaskDetail')"
      :visible.sync="dialogFormVisible"
      @close="handleClose"
    >
      <div style="height: 37%; display: flex;">
        <grid-table ref="termStatusList" :multi-select="false" :height="300" :col-model="colModel1" :row-data-api="tableRowDataApi" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTaskModeResult, listTaskStatus } from '@/api/system/terminalManage/taskStatus'
import { uploadSoftwareStatus } from '@/api/system/terminalManage/softwareRepository'

export default {
  name: 'TaskMode',
  components: {},
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'taskName2', width: '100', fixed: true, sort: 'custom' },
        { label: 'noExecuteTermNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.noExecuteSizeFormatterFormatter, click: this.noExecuteClick }]
        },
        { label: 'executingTermNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.executingSizeFormatter, click: this.executingClick }]
        },
        { label: 'executeSucessTermNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.successSizeFormatter, click: this.successClick }]
        },
        { label: 'executeFailTermNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.failSizeFormatter, click: this.failClick }]
        },
        { label: 'executeEndTermNum', type: 'button', width: '100',
          buttons: [{ label: '', formatter: this.executeEndFormatter, click: this.executeEndClick }]
        },
        { prop: 'type', label: 'effectiveStatus', width: '100', formatter: this.typeFormatter }
      ],
      colModel1: [
        { prop: 'terminalName', label: 'terminalName', width: '100', fixed: true },
        { prop: 'taskName', label: 'taskName2', width: '100' },
        { prop: 'state', label: 'executeStatus', width: '100', formatter: this.stateFormatter },
        { prop: 'taskFailCode', label: 'failCase', width: '150', formatter: this.taskFailCodeFormatter }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        termIds: '',
        taskId: undefined,
        endTaskFlag: null
      },
      dialogFormVisible: false,
      taskActiveOptions: {
        0: this.$t('pages.taskEffectiving'),
        1: this.$t('pages.taskEffectiving'),
        2: this.$t('pages.taskUnEffective'),
        3: this.$t('pages.taskUnEffective'),
        4: this.$t('pages.taskNoEffective')
      },
      statusOptions: [{ value: null, label: this.$t('pages.all') }, { value: 0, label: this.$t('pages.taskEffectiving') }, { value: 1, label: this.$t('pages.taskUnEffective') }, { value: 2, label: this.$t('pages.taskNoEffective') }]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['taskModeList']
    },
    termStatusListTable() {
      return this.$refs['termStatusList']
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return listTaskModeResult(searchQuery)
    },
    tableRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return listTaskStatus(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleClose() {
      this.query.termId = undefined
      this.query.taskId = undefined
    },
    noExecuteClick(row) {
      if (row.id && row.noExecuteIds && row.noExecuteIds.length > 0) {
        this.dialogFormVisible = true
        this.query.termIds = row.noExecuteIds.join(',')
        this.query.taskId = row.id
        this.$nextTick(() => {
          this.getStatus()
        })
      }
    },
    executingClick(row) {
      if (row.id && row.executingIds && row.executingIds.length > 0) {
        this.dialogFormVisible = true
        this.query.termIds = row.executingIds.join(',')
        this.query.taskId = row.id
        this.$nextTick(() => {
          this.getStatus()
        })
      }
    },
    successClick(row) {
      if (row.id && row.successIds && row.successIds.length > 0) {
        this.dialogFormVisible = true
        this.query.termIds = row.successIds.join(',')
        this.query.taskId = row.id
        this.$nextTick(() => {
          this.getStatus()
        })
      }
    },
    executeEndClick(row) {
      if (row.id && row.executeEndIds && row.executeEndIds.length > 0) {
        this.dialogFormVisible = true
        this.query.termIds = row.executeEndIds.join(',')
        this.query.taskId = row.id
        this.$nextTick(() => {
          this.getStatus()
        })
      }
    },
    failClick(row) {
      if (row.id && row.failIds && row.failIds.length > 0) {
        this.dialogFormVisible = true
        this.query.termIds = row.failIds.join(',')
        this.query.taskId = row.id
        this.$nextTick(() => {
          this.getStatus()
        })
      }
    },
    getStatus() {
      this.query.page = 1
      this.termStatusListTable.execRowDataApi(this.query)
    },
    noExecuteSizeFormatterFormatter(row) {
      return row.noExecuteSize
    },
    executingSizeFormatter(row) {
      return row.executingSize
    },
    executeSizeFormatter(row) {
      return row.executeSize
    },
    successSizeFormatter(row) {
      return row.successSize
    },
    executeEndFormatter(row) {
      return row.executeEndSize
    },
    failSizeFormatter(row) {
      return row.failSize
    },
    typeFormatter(row) {
      return this.taskActiveOptions[row.type]
    },
    stateFormatter(row) {
      if (row.state == '0') {
        return this.$t('pages.noExecute')
      } else if (row.state == '1') {
        return this.$t('pages.executing')
      } else if (row.state == '2') {
        return this.$t('pages.executeSuccess')
      } else if (row.state == '3') {
        return this.$t('pages.executeFail')
      } else if (row.state == '4') {
        return this.$t('pages.executeEnd')
      } else if (row.state == '5') {
        return this.$t('pages.termEndTask')
      }
    },
    taskFailCodeFormatter(row) {
      if (row.taskFailCode == 0 || row.taskFailCode == undefined) {
        return ''
      } else if (row.taskFailCode == 1) {
        return this.$t('pages.uninstallSuscess')
      } else if (row.taskFailCode == 2) {
        return this.$t('pages.callInstallFail')
      } else if (row.taskFailCode == 3) {
        return this.$t('pages.sendFail1')
      } else if (row.taskFailCode == 4) {
        return this.$t('pages.uncontroller')
      } else if (row.taskFailCode == 5) {
        return this.$t('pages.failCodeText1')
      } else if (row.taskFailCode == 6) {
        return this.$t('pages.failCodeText2')
      } else if (row.taskFailCode == 7) {
        return this.$t('pages.failCodeText3')
      } else if (row.taskFailCode == 8) {
        return this.$t('pages.failCodeText4')
      } else {
        const status = uploadSoftwareStatus[row.taskFailCode]
        return !status ? this.$t('pages.encOrDecLog_Msg14') + ':' + row.taskFailCode : status
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-descriptions-item__label {
    min-width: 150px;
  }
  >>>.el-descriptions-item__content {
    min-width: 100px;
    max-width: 180px;
  }
</style>
