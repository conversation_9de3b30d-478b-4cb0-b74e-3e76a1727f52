<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab" @tab-click="tabClick">
      <el-tab-pane :label="$t('route.taskDistribute')" name="taskDistribute">
        <template slot="label">
          {{ $t('route.taskDistribute') }}
          <el-tooltip slot="content" class="item" effect="dark" placement="right" :content="$t('pages.taskDistributeConfigTips')">
            <div></div>
            <i class="el-icon-info"/>
          </el-tooltip>
        </template>
        <task-config ref="taskDistribute"/>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.terminalModule')" name="termMode">
        <term-mode ref="termMode"></term-mode>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.taskMode')" name="taskMode">
        <task-mode ref="taskMode"></task-mode>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import TaskConfig from '@/views/system/terminalManage/taskDistribute/taskConfig'
import TaskMode from '@/views/system/terminalManage/taskDistribute/taskMode'
import TermMode from '@/views/system/terminalManage/taskDistribute/termMode'

export default {
  name: 'TaskDistribute',
  components: { TaskConfig, TaskMode, TermMode },
  data() {
    return {
      activeName: 'taskDistribute'
    }
  },
  created() {
  },
  methods: {
    tabClick(pane, event) {
      this.$nextTick(() => {
        this.$refs[this.activeName].handleFilter()
      })
    },
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    }
  }
}
</script>
<style scoped>
  .app-container{
    padding: 10px 15px;
  }
</style>
