<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!deleteable" @click="handleAccess">
          {{ $t('pages.accessNew') }}
        </el-button>
        <!--<el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteApproval') }}
        </el-button>-->
        <el-button style="text-transform: capitalize" icon="el-icon-remove" size="mini" :disabled="!deleteable" @click="handleForbid">
          {{ $t('pages.accessReject') }}
        </el-button>
        <el-button icon="el-icon-remove" size="mini" :disabled="!deleteable" @click="handleForbidAndDownload">
          {{ $t('pages.accessRejectAndUninstall') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleAccessConfig">
          {{ $t('pages.accessConfig') }}
        </el-button>
        <div class="searchCon">
          <TimeQuery :is-clearable="true" :placeholder="$t('table.applicationTime')" :auto-current-value="false" @getTimeParams="getTimeParams"/>
          <el-input v-model="query.computerName" v-trim clearable :placeholder="$t('table.computerName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="342"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('table.computerName')" prop="computerName">
                <el-input v-model="query.computerName" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.ip')" prop="ips">
                <el-input v-model="query.ips" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.mac')" prop="macs">
                <el-input v-model="query.macs" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.dept')" prop="deptName">
                <el-input v-model="query.deptName" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.terminalName')" prop="termName">
                <el-input v-model="query.termName" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.version')" prop="version">
                <el-input v-model="query.version" v-trim clearable :maxlength="20"/>
              </FormItem>
              <FormItem :label="$t('table.terminalType')" prop="termTypes" @change="termTypesChange">
                <el-select v-model="query.termTypes" style="width: 100%">
                  <el-option :value="'0,1,2,16,128,129,17,130,18'" :label="$t('pages.all')"></el-option>
                  <el-option v-for="(label, value) in termTypeOptions_pc" :key="value" :label="label" :value="value"></el-option>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="approvalAccessList"
        :is-saved-selected="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="titleText"
      :visible.sync="dialogFormVisible"
      width="700px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="155px" style="width: 600px; margin-left:30px;">
        <FormItem v-show="true" :label="$t('pages.approvalReply')" prop="result" label-width="130px">
          <el-row>
            <el-col v-for="(value, key) in resultOptions" :key="key" :label="value" :value="key" :span="12">
              <el-radio v-model="temp.result" :label="key" class="ellipsis" style="width: 98%;"><span :title="value">{{ value }}</span></el-radio>
            </el-col>
          </el-row>
        </FormItem>

        <div v-if="temp.result==1">
          <el-card class="box-card">
            <div v-if="true">
              <FormItem :label="$t('pages.dept')" prop="deptName">
                <el-input v-model="temp.deptName" maxlength="60" disabled/>
              </FormItem>
              <FormItem :label="$t('pages.terminalName')" prop="termName">
                <el-input v-model="temp.termName" maxlength="60" />
              </FormItem>
            </div>
            <FormItem :label="$t('pages.recommendGroup')" prop="groupId">
              <tree-select is-filter :data="treeSelectNode" node-key="dataId" :checked-keys="[temp.groupId]" :filter-key="filterKey" :width="296" @change="groupSelectChange" />
            </FormItem>
            <FormItem v-show="true" :label="$t('pages.userAutoLogin')" prop="userId">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content" class="tip">
                  <p>{{ $t('pages.userAutoLoginNote') }}</p>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <tree-select
                :default-expand-all="false"
                is-filter
                :local-search="false"
                :checked-keys="[userCheckedId]"
                :width="296"
                :rewrite-node-click-fuc="true"
                :node-click-fuc="userTreeNodeCheckChange"
              />
            </FormItem>
          </el-card>
        </div>
        <div v-if="temp.result==2">
          <FormItem :label="$t('pages.selectableTerminal')" prop="termId" label-width="100px">
            <tree-select
              :default-expand-all="false"
              is-filter
              :data="terminalTreeData"
              :checked-keys="[terminalCheckedId]"
              :icon-option="iconOption"
              :rewrite-node-click-fuc="true"
              :filter-key="terminalFilter"
              :node-click-fuc="terminalTreeNodeCheckChange"
            />
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="approvalAccess()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.termAccessConfig')"
      :visible.sync="dialogConfigVisible"
      width="950px"
      @dragDialog="handleDrag"
    >
      <Form ref="configForm" :model="tempC" :rules="rulesC" label-position="right" label-width="120px" style="width: 840px; margin-left:30px;">
        <el-row>
          <FormItem :label="$t('pages.newTerminal_text')" label-width="120px" :extra-width="{en: 70}">
            <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
              <div slot="content" class="tip">
                <p>{{ $t('pages.newTerminal_text1') }}</p>
                <p>{{ $t('pages.newTerminal_text2') }}<em>{{ $t('pages.newTerminal_text3') }}</em>：</p>
                <p class="indent">{{ $t('pages.newTerminal_text4') }}</p>
                <p class="indent">{{ $t('pages.newTerminal_text5') }}</p>
                <p class="indent">{{ $t('pages.newTerminal_text6') }}</p>
                <p>{{ $t('pages.newTerminal_text7') }}</p>
                <p>{{ $t('pages.newTerminal_text8') }}</p>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-col v-for="(value, key) in accessMethodOptions" :key="key" :label="value" :value="key" :span="9-key">
              <el-radio v-model="tempC.accessMethod" :label="parseInt(key)" class="ellipsis" style="width: 98%;"><span :title="value">{{ value }}</span></el-radio>
            </el-col>
          </FormItem>

          <FormItem :label="$t('pages.newTerminalAccessMode')" label-width="120px" :extra-width="{en: 70}">
            <el-col v-for="(value, key) in accessModeOptions" :key="key" :label="value" :value="key" :span="9-key">
              <el-radio v-model="tempC.accessMode" :label="parseInt(key)" class="ellipsis" style="width: 98%;">
                <span :title="value">
                  {{ value }}
                </span>
                <el-tooltip v-if="key==1" effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.newTerminal_text17') }}
                  </div>
                  <i class="el-icon-info"/>
                </el-tooltip>
              </el-radio>
            </el-col>
          </FormItem>
          <template v-if="tempC.accessMode !== 0">
            <el-col :span="11">
              <FormItem :label="$t('pages.newTerminalLoginMode')" label-width="120px" :extra-width="{en: 70}">
                <el-select v-model="tempC.opUserLoginMode">
                  <el-option v-for="item in loginModeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem label-width="6px">
                <el-checkbox v-model="tempC.createOpUser" :true-label="1" :false-label="0" @change="tempC.bindOpUser = tempC.createOpUser == 0 ? 0 : tempC.bindOpUser">
                  <span v-if="tempC.opUserLoginMode===1">{{ $t('pages.autoCreateAndSetUser') }}</span>
                  <span v-else>{{ $t('pages.autoCreateUser') }}</span>
                </el-checkbox>
                <!-- <el-checkbox v-model="tempC.bindOpUser" :disabled="tempC.createOpUser==0" :true-label="1" :false-label="0">直接绑定自动创建的操作员</el-checkbox> -->
              </FormItem>
            </el-col>
            <el-col :span="24">
              <FormItem :label="$t('pages.recommendGroup')" label-width="120px" :extra-width="{en: 70}" prop="groupId">
                <tree-select is-filter :data="treeSelectNode" node-key="dataId" :checked-keys="[tempC.groupId]" :filter-key="filterKey" :width="265" style="width:265px;" @change="groupOfConfigSelectChange"/>
              </FormItem>
            </el-col>
          </template>
        </el-row>

        <FormItem v-show="tempC.accessMode !== 0" :label="$t('pages.smartAccess')" label-width="120px" :extra-width="{en: 70}" prop="intelligent">
          <el-switch v-model="tempC.intelligent" :active-value="1" :inactive-value="0" @change="intelligentChange" />
        </FormItem>
      </Form>

      <div v-show="tempC.intelligent && tempC.accessMode !== 0">
        <el-card class="box-card" :body-style="{ 'padding': '3px' }">
          <div slot="header">
            <span style="color: #2b7aac;">{{ $t('pages.smartAccess_text') }}</span>
          </div>
          <el-row style="height: 60px;">
            <el-col :span="$store.getters.language === 'en' ? 18 : 19">
              <Form ref="iConfigForm" hide-required-asterisk :rules="iConfigRules" :model="tempI" label-position="right" label-width="55px">
                <el-row>
                  <el-col :span="7">
                    <FormItem :label="$t('table.startIP')" label-width="55px" :extra-width="{en: 10}" prop="beginIp">
                      <el-input v-model="tempI.beginIp" :maxlength="64" clearable></el-input>
                    </FormItem>
                  </el-col>
                  <el-col :span="7">
                    <FormItem :label="$t('table.endIP')" label-width="55px" :extra-width="{en: -5}" prop="endIp">
                      <el-input v-model="tempI.endIp" :maxlength="64" clearable></el-input>
                    </FormItem>
                  </el-col>
                  <el-col :span="10">
                    <FormItem :label="$t('pages.recommendGroup')" label-width="70px" prop="groupId">
                      <tree-select is-filter :data="treeSelectTempINode" node-key="dataId" :checked-keys="[tempI.groupId]" :width="280" :rewrite-node-click-fuc="true" :node-click-fuc="groupOfIConfigSelectChange" :filter-key="filterKey" />
                    </FormItem>
                  </el-col>
                </el-row>
              </Form>
            </el-col>
            <el-col :span="$store.getters.language === 'en' ? 6 : 5">
              <div style="float:left;margin: 5px 2px;">
                <el-button size="small" style="margin-left: 3px; font-size: 12px" :disabled="!iConfigAddable" @click="createIConfig">{{ $t('button.insert') }}</el-button>
                <el-button size="small" style="margin-left: 3px; font-size: 12px" :disabled="!iConfigEditable" @click="updateIConfig">{{ $t('button.edit') }}</el-button>
                <el-button size="small" style="margin-left: 3px; font-size: 12px" :disabled="!iConfigDeleteable" @click="deleteIConfig">{{ $t('button.delete') }}</el-button>
                <el-button size="small" style="margin-left: 3px; font-size: 12px" @click="cancelIConfig">{{ $t('button.cancel') }}</el-button>
              </div>
            </el-col>
          </el-row>
          <grid-table
            ref="iConfigList"
            :height="180"
            :show-pager="false"
            :multi-select="true"
            :col-model="iConfigColModel"
            :row-data-api="iConfigRowDataApi"
            @selectionChangeEnd="iConfigSelectionChange"
            @currentChange="iConfigCurrentChange"
          />
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateAccessConfig()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelAccessConfig()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <approval-access-to-new-dlg ref="approvalAccessToNewDlg" @submitEnd="handleRefresh"/>
  </div>
</template>

<script>
import { getApprovalInfoPage, deleteApprovalInfo, approvalAccess, getAccessConfig, getAccessConfigIntelList, addAccessConfig, updateAccessConfig, addAccessConfigIntel, updateAccessConfigIntel, deleteAccessConfigIntel } from '@/api/system/terminalManage/approvalAccess'
import { getDeactiveTerminalTreeNode } from '@/api/system/terminalManage/terminal'
import ApprovalAccessToNewDlg from './approvalAccessToNewDlg'
import { getLoginModeDict, getTermTypeDict } from '@/utils/dictionary'
import { isIPv4, isIPv6 } from '@/utils/validate'

export default {
  name: 'ApprovalAccessPc',
  components: { ApprovalAccessToNewDlg },
  data() {
    return {
      colModel: [
        { prop: 'computerName', label: 'computerName', width: '120', sort: true },
        { prop: 'ips', label: 'ip', width: '120', formatter: this.ipsFormatter },
        { prop: 'macs', label: 'mac', width: '120', sort: true },
        { prop: 'deptName', label: 'dept', width: '100', sort: true },
        { prop: 'termName', label: 'terminalName', width: '100', sort: true },
        { prop: 'version', label: 'version', width: '100', sort: true },
        { prop: 'termType', label: 'terminalType', width: '100', sort: true, formatter: this.termTypeFormatter },
        { prop: 'createTime', label: 'applicationTime', width: '150', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right',
          buttons: [
            { label: 'approval', click: this.handleApproval }
          ]
        }
      ],
      iConfigColModel: [
        { prop: 'beginIp', label: 'startIP', width: '150', sort: true },
        { prop: 'endIp', label: 'endIP', width: '150', sort: true },
        { label: 'defaultGroup', width: '200', formatter: this.groupFormatter, sort: true }
      ],
      termTypeOptions: Object.fromEntries(getTermTypeDict().map(item => [item.value, item.label])),
      termTypeOptions_pc: Object.fromEntries(getTermTypeDict().filter((item) => [0, 1, 2, 16].indexOf(item.value) > -1).map(item => [item.value, item.label])),
      iconOption: {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        127: 'error'
      },
      loginModeOptions: [],
      temTypes: [],
      query: { // 查询条件
        page: 1,
        termTypes: '0,1,2,16,128,129,17,130,18',
        computerName: '',
        ips: '',
        macs: '',
        deptName: '',
        termName: '',
        version: '',
        createDate: '',
        startDate: '',
        endDate: '',
        result: 0
      },
      showTree: false,
      deleteable: false,
      iConfigAddable: true,
      iConfigEditable: false,
      iConfigDeleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        termId: undefined,
        ids: '',
        termName: '',
        deptName: '',
        // userName: '',
        result: '1',
        groupId: 1,
        userId: ''
      },
      tempC: {},
      defaultTempC: {
        id: undefined,
        pcOrMobile: 1,
        intelligent: 0,
        accessMode: 0,
        groupId: 1,
        opUserLoginMode: 1,
        createOpUser: 1,
        bindOpUser: 0,
        matchOpUser: 0,
        accessMethod: 0
      },
      tempI: {},
      defaultTempI: {
        id: undefined,
        beginIp: '',
        endIp: '',
        groupName: '',
        groupId: undefined
      },
      treeSelectNode: [],
      treeSelectTempINode: [],
      userTreeSelectNode: [],
      terminalTreeData: [],
      terminalCheckedId: '',
      userCheckedId: '',
      tempUser: {},
      dialogFormVisible: false,
      dialogConfigVisible: false,
      // isSimple: true,
      existConfig: false,
      dialogStatus: '',
      titleText: this.$t('route.approvalAccess'),
      pvData: [],
      rules: {
        // deptName: [{ required: true, message: this.$t('pages.validateMsg_dept'), trigger: 'blur' }],
        // userName: [{ required: true, message: this.$t('pages.validateMsg_user'), trigger: 'blur' }],
        termName: [{ required: true, message: this.$t('pages.validateMsg_termName'), trigger: 'blur' }]
      },
      rulesC: {},
      iConfigRules: {
        beginIp: [
          { validator: this.compositeIpValidator, trigger: 'blur' }
        ],
        endIp: [
          { validator: this.compositeIpValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('pages.validateMsg1'), trigger: 'blur' }
        ]
      },
      submitting: false,
      deptIds: [],
      filterKey: '-2'
    }
  },
  computed: {
    gridTable() {
      return this.$refs['approvalAccessList']
    },
    resultOptions() {
      return {
        1: this.$t('pages.approvalResult1'),
        2: this.$t('pages.approvalResult2'),
        3: this.$t('pages.approvalResult3'),
        4: this.$t('pages.approvalResult4')
      }
    },
    accessModeOptions() {
      return {
        0: this.$t('pages.accessMode_opt1'),
        1: this.$t('pages.accessMode_opt2'),
        2: this.$t('pages.accessMode_opt3')
      }
    },
    accessMethodOptions() {
      return {
        0: this.$t('pages.accessMethod_opt1'),
        1: this.$t('pages.accessMethod_opt2'),
        2: this.$t('pages.accessMethod_opt3')
      }
    }
  },
  watch: {
    '$store.state.commonData.notice.ApprovalAccess'(val) {
      this.handleRefresh()
    },
    '$store.state.commonData.termNodes'() {
      this.loadTerminalTree()
    }
  },
  created() {
    this.loginModeOptions = getLoginModeDict()
    getTermTypeDict().forEach(item => {
      this.$set(this.iconOption, item.value, item.icon)
    })
    this.resetTemp()
    this.resetTempC()
    this.getDeptTreeNode()
    // this.getUserTreeNode()
    this.loadTerminalTree()
  },
  activated() {
    let exceptRootNodes = this.$store.getters.deptTree;
    // 推荐分组，不允许选择公司根节点
    if (Array.isArray(exceptRootNodes) && exceptRootNodes.length === 1 && exceptRootNodes[0].dataId == 0) {
      exceptRootNodes = this.$store.getters.deptTree[0].children
    }
    this.treeSelectNode.splice(0, this.treeSelectNode.length, ...JSON.parse(JSON.stringify(exceptRootNodes)))
    this.treeSelectTempINode.splice(0, this.treeSelectTempINode.length, ...JSON.parse(JSON.stringify(exceptRootNodes)))
    // this.deptIds = this.getDeptIds(this.$store.getters.deptTree)
    this.initIConfigGridTable()
  },
  methods: {
    initIConfigGridTable() {
      if (this.iConfigGridTable()) {
        this.iConfigGridTable().execRowDataApi()
      }
    },
    iConfigGridTable() {
      return this.$refs['iConfigList']
    },
    loadTerminalTree: function() {
      getDeactiveTerminalTreeNode().then(respond => {
        this.terminalTreeData = respond.data
      })
    },
    terminalSelect() {
      return this.$refs['terminalGridSelect']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getApprovalInfoPage(searchQuery)
    },
    handleRefresh() {
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    iConfigRowDataApi: function(option) {
      return getAccessConfigIntelList()
    },
    filterNodeMethod: function(value, data, node) {
      return data['dataType'] ? JSON.parse(JSON.stringify(this.temTypes)).indexOf(Number(data['dataType'])) > -1 : true
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 'G') {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !(type === 32 || type === 128 || type === 129 || type === 130)
    },
    terminalTreeNodeCheckChange: function(data, node, vm) {
      if (data.type == 1) {
        this.terminalCheckedId = data.id
        this.temp.termId = data.dataId
      } else {
        return false
      }
    },
    groupSelectChange: function(data) {
      this.temp.groupId = data
    },
    userTreeNodeCheckChange: function(data, node, vm) {
      if (data.type == 2) {
        this.temp.userId = data.dataId
      } else {
        return false
      }
    },
    groupOfConfigSelectChange: function(data) {
      this.tempC.groupId = data
    },
    groupOfIConfigSelectChange: function(data, node, vm) {
      this.tempI.groupId = data.dataId
      this.tempI.groupName = data.label
      this.$nextTick(() => {
        this.$refs.iConfigForm && this.$refs.iConfigForm.validateField('groupId')
      })
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    iConfigSelectionChange(rowDatas) {
      this.iConfigDeleteable = rowDatas.length > 0
    },
    iConfigCurrentChange: function(rowData) {
      this.tempI = Object.assign({}, rowData)
      this.$refs['iConfigForm'].clearValidate()
      if (rowData) this.iConfigEditable = true
    },
    intelligentChange: function() {
      this.resetTempI()
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    resetTempC() {
      if (!this.existConfig) {
        getAccessConfig({ pcOrMobile: 1 }).then(respond => {
          if (respond.data) {
            this.defaultTempC.id = respond.data.id
            this.defaultTempC.intelligent = respond.data.intelligent
            this.defaultTempC.accessMode = respond.data.accessMode
            this.defaultTempC.accessMethod = respond.data.accessMethod
            this.defaultTempC.groupId = respond.data.groupId
            this.defaultTempC.createOpUser = respond.data.createOpUser
            this.defaultTempC.opUserLoginMode = respond.data.opUserLoginMode
            this.defaultTempC.bindOpUser = respond.data.bindOpUser
            this.defaultTempC.matchOpUser = respond.data.matchOpUser
          }
          this.existConfig = true
          this.tempC = Object.assign({}, this.defaultTempC)
        })
      } else {
        this.tempC = Object.assign({}, this.defaultTempC)
      }
    },
    resetTempI() {
      this.iConfigEditable = false
      this.iConfigAddable = true
      this.tempI = Object.assign({}, this.defaultTempI)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    getDeptTreeNode: function() {
      let exceptRootNodes = this.$store.getters.deptTree;
      // 推荐分组，不允许选择公司根节点
      if (Array.isArray(exceptRootNodes) && exceptRootNodes.length === 1 && exceptRootNodes[0].dataId == 0) {
        exceptRootNodes = this.$store.getters.deptTree[0].children
      }
      this.treeSelectNode.splice(0, this.treeSelectNode.length, ...JSON.parse(JSON.stringify(exceptRootNodes)))
      this.treeSelectTempINode.splice(0, this.treeSelectTempINode.length, ...JSON.parse(JSON.stringify(exceptRootNodes)))
    },
    // getDeptIds(nodes) {
    //   nodes.forEach(node => {
    //     this.deptIds.push(Number(node.dataId))
    //     if (node.children && node.children.length > 0) {
    //       this.getDeptIds(node.children)
    //     }
    //   })
    // },
    handleApproval(row) {
      this.resetTemp()
      this.temp.ids = row.id
      this.temp.groupId = row.groupId
      this.temp.userId = row.userId
      this.userCheckedId = row.userId
      this.temp.deptName = row.deptName
      // this.temp.userName = row.userName
      this.temp.termName = row.termName ? row.termName : row.computerName
      this.dialogFormVisible = true
      this.terminalCheckedId = ''
      // this.isSimple = true
      this.temTypes.splice(0)
      if (row.termType === 0 || row.termType === 16) {
        this.temTypes = [0, 16]
      } else if (row.termType === 1 || row.termType === 17) {
        this.temTypes = [1, 17]
      } else if (row.termType === 2 || row.termType === 18) {
        this.temTypes = [2, 18]
      } else if (row.termType === 1 || row.termType === 17) {
        this.temTypes = [3]
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    approvalAccess() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.result == 2 && !this.temp.termId) {
            this.$notify({
              title: this.$t('pages.approvalFailed'),
              message: this.$t('pages.selectOfflineTerm'),
              type: 'error',
              duration: 2000
            })
          } else {
            this.submitting = true
            const tempData = Object.assign({}, this.temp)
            const msg = (tempData.result == '3' || tempData.result == '4') ? this.$t('pages.accessReject1') : this.$t('pages.approvalSuccess')
            approvalAccess(tempData).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              // this.isSimple = true
              this.gridTable.execRowDataApi(this.query)
              this.gridTable.selectedDatasDelete()
              this.$notify({
                title: this.$t('text.success'),
                message: msg,
                type: 'success',
                duration: 2000
              })
            })
          }
        }
      })
    },
    handleAccessConfig() {
      this.resetTempC()
      this.resetTempI()
      this.dialogConfigVisible = true
      this.$nextTick(() => {
        this.$refs['configForm'].clearValidate()
        this.$refs['iConfigForm'].clearValidate()
        this.$refs['iConfigList'].execRowDataApi()
      })
    },
    updateAccessConfig() {
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.tempC)
          if (tempData.opUserLoginMode !== 1) { // 非操作员自动登录
            tempData.bindOpUser = 0
          }
          const submitFunc = this.tempC.id ? updateAccessConfig : addAccessConfig
          submitFunc(tempData).then(respond => {
            this.submitting = false
            this.dialogConfigVisible = false
            this.iConfigGridTable().clearSelection()
            this.existConfig = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.settingSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    cancelAccessConfig() {
      this.iConfigGridTable().clearSelection()
      this.iConfigGridTable().setCurrentRow(null)
      this.dialogConfigVisible = false
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteApprovalInfo({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleAccess() {
      // this.isSimple = false
      // this.dialogFormVisible = true
      // this.resetTemp()
      // const toDeleteIds = this.gridTable.getSelectedIds()
      // this.temp.ids = toDeleteIds.join(',')
      // this.temp.groupId = this.tempC.groupId
      // this.temp.termNames = this.gridTable.getSelectedDatas().map(item => { return item.termName }).join(',')
      // 防止修改新数组里面的数据时，会影响到旧数组的数据，所以必须加JSON.parse(JSON.stringify
      const rowData = JSON.parse(JSON.stringify(this.gridTable.getSelectedDatas()))
      this.$refs.approvalAccessToNewDlg.handleUpdate(rowData)
    },
    handleForbid() {
      this.$confirmBox(this.$t('pages.rejectText1'), this.$t('text.prompt')).then(() => {
        this.resetTemp()
        const toDeleteIds = this.gridTable.getSelectedIds()
        this.temp.ids = toDeleteIds.join(',')
        this.temp.result = 3
        this.temp.termNames = this.gridTable.getSelectedDatas().map(item => { return item.termName }).join(',')
        const tempData = Object.assign({}, this.temp)
        approvalAccess(tempData).then(respond => {
          this.submitting = false
          this.dialogFormVisible = false
          this.gridTable.execRowDataApi(this.query)
          this.gridTable.selectedDatasDelete()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.accessReject1'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleForbidAndDownload() {
      this.$confirmBox(this.$t('pages.rejectText2'), this.$t('text.prompt')).then(() => {
        this.resetTemp()
        const toDeleteIds = this.gridTable.getSelectedIds()
        this.temp.ids = toDeleteIds.join(',')
        this.temp.termNames = this.gridTable.getSelectedDatas().map(item => { return item.termName }).join(',')
        this.temp.result = 4
        const tempData = Object.assign({}, this.temp)
        approvalAccess(tempData).then(respond => {
          this.submitting = false
          this.dialogFormVisible = false
          this.gridTable.execRowDataApi(this.query)
          this.gridTable.selectedDatasDelete()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.accessReject1'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    validateTempI(rowData, status) {
      let msg = ''
      if (!rowData.beginIp || !rowData.endIp || !rowData.groupId) {
        msg = this.$t('pages.validateSmartAccess')
      }
      if (rowData.beginIp && rowData.endIp) {
        const iConfigList = this.iConfigGridTable().getDatas()
        if (status === 'create') {
          iConfigList.forEach(config => {
            if ((this.ipToNumber(config.beginIp) <= this.ipToNumber(this.tempI.beginIp) && this.ipToNumber(config.endIp) >= this.ipToNumber(this.tempI.beginIp)) ||
              (this.ipToNumber(config.beginIp) <= this.ipToNumber(this.tempI.endIp) && this.ipToNumber(config.endIp) >= this.ipToNumber(this.tempI.endIp))) {
              msg = this.$t('pages.serverLibrary_text11')
            }
          })
        } else {
          iConfigList.forEach(config => {
            if (config.id !== this.tempI.id &&
              ((this.ipToNumber(config.beginIp) <= this.ipToNumber(this.tempI.beginIp) && this.ipToNumber(config.endIp) >= this.ipToNumber(this.tempI.beginIp)) ||
                (this.ipToNumber(config.beginIp) <= this.ipToNumber(this.tempI.endIp) && this.ipToNumber(config.endIp) >= this.ipToNumber(this.tempI.endIp)))) {
              msg = this.$t('pages.serverLibrary_text11')
            }
          })
        }
      }
      return msg
    },
    ipValidator(rule, value, callback) {
      if (value && isIPv4(value)) {
        let flag = false
        if (this.tempI.beginIp && this.tempI.endIp) {
          const temp1 = this.tempI.beginIp.split('.')
          const temp2 = this.tempI.endIp.split('.')
          for (var i = 0; i < 4; i++) {
            if (temp1[i] - temp2[i] == 0) {
              continue
            } else if (temp1[i] - temp2[i] > 0) {
              flag = true
            }
            break
          }
          if (this.tempI.beginIp == this.tempI.endIp) {
            flag = false
          }

          if (flag === true) {
            callback(new Error(this.$t('pages.serverLibrary_text3')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('pages.validateFile_6')))
      }
    },
    compositeIpValidator(rule, value, callback) {
      if (!(this.tempI.endIp && this.tempI.endIp &&
            isIPv6(this.tempI.beginIp) && isIPv6(this.tempI.endIp))) {
        if (isIPv6(value)) {
          callback(new Error(this.$t('pages.bothIPV6')))
        } else {
          this.ipValidator(rule, value, callback);
        }
      } else {
        this.$refs.iConfigForm.clearValidate()
        callback()
      }
    },
    ipToNumber(ip) {
      const ipStrs = ip.split('.')
      let number = ''
      for (let i = 0; i < ipStrs.length; i++) {
        let ipStr = ipStrs[i]
        if (ipStr.length === 1) {
          ipStr = '00' + ipStr
        } else if (ipStr.length === 2) {
          ipStr = '0' + ipStr
        }
        number += ipStr
      }
      return Number(number)
    },
    createIConfig() {
      this.$refs['iConfigForm'].validate((valid) => {
        if (valid) {
          this.iConfigAddable = false
          const rowData = Object.assign({}, this.tempI)
          const msg = this.validateTempI(rowData, 'create')
          if (!msg) {
            addAccessConfigIntel(rowData).then(respond => {
              this.submitting = false
              rowData.id = respond.data.id
              this.iConfigGridTable().addRowData(rowData)
              this.cancelIConfig()
            })
          } else {
            this.iConfigAddable = true
            this.$notify({
              title: this.$t('text.prompt'),
              message: msg,
              type: 'error',
              duration: 2000
            })
          }
        }
      })
    },
    updateIConfig() {
      this.$refs['iConfigForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempI)
          const msg = this.validateTempI(rowData, 'update')
          if (!msg) {
            updateAccessConfigIntel(rowData).then(respond => {
              this.submitting = false
              this.iConfigGridTable().updateRowData(rowData)
              this.cancelIConfig()
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: msg,
              type: 'error',
              duration: 2000
            })
          }
        }
      })
    },
    deleteIConfig() {
      const toDeleteIds = this.iConfigGridTable().getSelectedIds()
      if (toDeleteIds.length > 0) {
        deleteAccessConfigIntel({ ids: toDeleteIds.join(',') }).then(respond => {
          this.iConfigGridTable().deleteRowData(toDeleteIds)
          this.cancelIConfig()
        })
      }
    },
    cancelIConfig() {
      this.iConfigGridTable().setCurrentRow()
      this.resetTempI()
      this.$refs['iConfigForm'].clearValidate()
    },
    groupFormatter: function(row, data) {
      return row.groupName ? row.groupName : this.$t('pages.unknownGroup')
    },
    ipsFormatter: function(row, data) {
      if (!row.ips && !row.ipv6s) {
        return ''
      } else if (!row.ipv6s) {
        return row.ips
      } else if (!row.ips) {
        return row.ipv6s
      }
      if (row.ipv6s && row.ipv6s.indexOf(row.mainIp) > -1) {
        return row.ipv6s + ',' + row.ips
      }
      return row.ips + ',' + row.ipv6s
    },
    termTypeFormatter: function(row, data) {
      return this.termTypeOptions[data]
    },
    resetQuery() {
      this.query.page = 1
      this.query.termTypes = '0,1,2,16,128,129,17,130,18'
      this.query.computerName = ''
      this.query.ips = ''
      this.query.macs = ''
      this.query.deptName = ''
      this.query.termName = ''
      this.query.version = ''
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    termTypesChange(val) {
      if (!val) {
        this.query.termTypes = '0,1,2,16,128,129,17,130,18'
      } else {
        this.query.termTypes = val
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .tip p {
    font-size: 13px;
    margin: 0 0 5px;
    &:first-child{
      margin: 2px 0 10px;
    }
    &.indent{
      text-indent: 1.5em;
    }
    em{
      font-weight: bold;
    }
  }
</style>
