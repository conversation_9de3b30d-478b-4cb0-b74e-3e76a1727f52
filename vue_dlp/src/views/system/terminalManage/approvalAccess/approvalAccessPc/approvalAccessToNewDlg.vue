<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('route.approvalAccess')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <Form ref="groupForm" label-position="right" label-width="90px" style="width: 700px; margin-left:30px;">
      <el-row>
        <el-col :span="10">
          <FormItem label-width="1px">
            <el-checkbox v-model="multiUpdate">{{ $t('pages.setAccessGroup') }}</el-checkbox>
          </FormItem>
        </el-col>
        <el-col :span="14">
          <FormItem v-if="multiUpdate" :label="$t('pages.accessGroup')" prop="groupId">
            <tree-select
              ref="groupTree"
              :data="groupTreeSelectData"
              node-key="dataId"
              :checked-keys="[groupId]"
              :width="296"
              :placeholder="$t('pages.selectAccessGroup')"
              :rewrite-node-click-fuc="true"
              :node-click-fuc="groupIdSelectChange"
            />
          </FormItem>
        </el-col>
      </el-row>
      <grid-table
        ref="toUpdateTermTable"
        :col-model="terminalColModel"
        :row-datas="terminalRowData"
        :multi-select="false"
        :height="350"
        :show-pager="false"
      />
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="approvalAccess()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { batchApprovalNewTerm } from '@/api/system/terminalManage/approvalAccess'

export default {
  name: 'ApprovalAccessToNewDlg',
  props: {
    terminalType: {// 是否显示行号，并设置行号列表头名
      type: String,
      default: 'pc'
    }
  },
  data() {
    return {
      terminalColModel: [
        { prop: 'computerName', label: this.terminalType == 'pc' ? 'computerName' : 'models', width: '150', sort: true, fixed: true },
        { prop: 'deptName', label: 'dept', width: '150', sort: true },
        { prop: 'termName', label: 'terminalName', width: '150', sort: true },
        { prop: 'groupId', label: 'recommendGroup', width: '150', type: 'treeSelect', treeData: [], checkedKeysFieldName: 'checkedKeys', isFilter: false, localSearch: false, nodeChange: this.changeRowDataGroup, sort: true },
        { prop: '', fixedWidth: 40, type: 'icon', iconClass: 'delete', iconFormatter: () => { return true }, style: 'cursor: pointer;', click: this.removeTerminal }
      ],
      submitting: false,
      dialogVisible: false,
      multiUpdate: false,
      terminalRowData: [],
      groupTreeSelectData: [],
      groupId: undefined
    }
  },
  computed: {
    ...mapGetters([
      'deptTree',
      'deptTreeList'
    ]),
    groupTreeMap() {
      const treeList = this.deptTreeList || []
      return treeList.reduce((map, data) => {
        map[data.dataId] = data.label
        return map
      }, {})
    }
  },
  watch: {
    deptTree() {
      this.setGroupTree(this.deptTree)
    }
  },
  created() {
    this.resetTemp()
    this.getGroupTree()
  },
  activated() {
  },
  methods: {
    groupTree() {
      return this.$refs['groupTree']
    },
    groupIdSelectChange: function(data, node, vm) {
      this.groupId = data
      if (data.id.indexOf('G' + data.dataId) > -1) {
        const checkedKey = { key: data.dataId, label: data.label }
        this.terminalRowData.forEach(rowData => {
          rowData.checkedKeys = [checkedKey]
          rowData.groupId = data.dataId
        })
      } else {
        return false
      }
    },
    toUpdateTerminalTable() {
      return this.$refs['toUpdateTermTable']
    },
    changeRowDataGroup(nodeData, node, vm, rowData, col) {
      if (nodeData.id.indexOf('G' + nodeData.dataId) > -1) {
        rowData.checkedKeys = [{ key: nodeData.dataId, label: nodeData.label }]
        this.multiUpdate = false
        rowData.groupId = nodeData.dataId
      } else {
        return false
      }
    },
    removeTerminal(e, row, rowIndex) {
      const index = this.terminalRowData.indexOf(row)
      this.$confirmBox(this.$t('pages.confirmDeleteTerm') + row.computerName + '？', this.$t('text.prompt')).then(() => {
        this.terminalRowData.splice(index, 1)
      }).catch(() => {

      })
    },
    getGroupTree() {
      if (this.deptTree) {
        this.setGroupTree(this.deptTree)
      } else {
        getDeptTreeFromCache().then(respond => {
          this.setGroupTree(respond.data)
        })
      }
    },
    setGroupTree(treeData) {
      this.groupTreeSelectData.splice(0)
      if (!treeData) return
      let deptData = []
      if (treeData.length == 1 && treeData[0].dataId == 0) {
        // 推荐部门，不允许选择公司根节点, 过滤 回收站 节点
        deptData = treeData[0].children.filter(data => data.dataId != -2)
      } else {
        // 过滤 回收站 节点
        deptData = treeData.filter(data => data.dataId != -2)
      }
      this.groupTreeSelectData = [...deptData]
      this.terminalColModel[3].treeData = [...deptData]
    },
    resetTemp() {
      this.multiUpdate = false
      this.terminalRowData = []
      this.groupId = 0
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleUpdate(rowDatas) {
      this.resetTemp()
      this.dialogVisible = true
      this.terminalRowData.splice(0)
      rowDatas.forEach(rowData => {
        if (rowData.groupId) {
          const groupName = this.groupTreeMap[rowData.groupId]
          if (groupName) {
            rowData.checkedKeys = [{ key: rowData.groupId, label: groupName }]
          } else {
            rowData.checkedKeys = []
          }
        } else {
          rowData.checkedKeys = []
        }
        this.terminalRowData.push(rowData)
      })
      this.terminalRowData.sort((a, b) => a.id - b.id)
    },
    approvalAccess() {
      // const formData = []
      // for (let i = 0; i < this.terminalRowData.length; i++) {
      //   const rowData = this.terminalRowData[i]
      //   if (!rowData.groupId) {
      //     this.$message({ message: '请为终端“' + rowData.name + '(' + rowData.id + ')' + '”指定接入的分组', type: 'error', duration: 2000 })
      //     return
      //   }
      //   formData.push({ id: rowData.id, groupId: rowData.groupId })
      // }
      const rowDatas = this.toUpdateTerminalTable().getDatas()
      if (rowDatas && rowDatas.length > 0) {
        batchApprovalNewTerm(rowDatas).then(respond => {
          this.dialogVisible = false
          this.$emit('submitEnd')
          this.$notify({ title: this.$t('text.success'), message: this.$t('pages.waitTermAccess'), type: 'success', duration: 2000 })
        })
      } else {
        this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.noSelectTerm'), type: 'warning', duration: 2000 })
      }
    },
    groupFormatter: function(row, data) {
      const node = this.groupTreeMap[data]
      return node ? this.groupTreeMap[data] : this.$t('pages.notSpecified')
    }
  }
}
</script>
