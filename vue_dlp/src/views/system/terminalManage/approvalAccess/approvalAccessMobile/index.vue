<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!deleteable" @click="handleAccess">
          {{ $t('pages.accessNew') }}
        </el-button>
        <!--<el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteApproval') }}
        </el-button>-->
        <el-button style="text-transform: capitalize" icon="el-icon-remove" size="mini" :disabled="!deleteable" @click="handleForbid">
          {{ $t('pages.accessReject') }}
        </el-button>
        <!--<el-button icon="el-icon-remove" size="mini" :disabled="!deleteable" @click="handleForbidAndDownload">
          {{ $t('pages.accessRejectAndUninstall') }}
        </el-button>-->
        <el-button icon="el-icon-setting" size="mini" @click="handleAccessConfig">
          {{ $t('pages.accessConfig') }}
        </el-button>
        <div class="searchCon">
          <TimeQuery :is-clearable="true" :placeholder="$t('table.applicationTime')" :auto-current-value="false" @getTimeParams="getTimeParams"/>
          <el-input v-model="query.computerName" v-trim clearable :placeholder="$t('table.models')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="342"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="60px">
              <FormItem :label="$t('table.identificationCode')" prop="guid">
                <el-input v-model="query.guid" v-trim clearable :maxlength="50"/>
              </FormItem>
              <FormItem :label="$t('table.dept')" prop="deptName">
                <el-input v-model="query.deptName" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.userName')" prop="termName">
                <el-input v-model="query.termName" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.models')" prop="computerName">
                <el-input v-model="query.computerName" v-trim clearable :maxlength="64"/>
              </FormItem>
              <FormItem :label="$t('table.version')" prop="version">
                <el-input v-model="query.version" v-trim clearable :maxlength="20"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="approvalAccessList"
        :is-saved-selected="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="titleText"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 500px; margin-left:30px;">
        <FormItem v-show="isSimple" :label="$t('pages.approvalReply')" prop="result">
          <el-row>
            <el-col v-for="(value, key) in resultOptions" :key="key" :label="value" :value="key" :span="12">
              <el-radio v-model="temp.result" :label="key">{{ value }}</el-radio>
            </el-col>
          </el-row>
        </FormItem>

        <div v-if="temp.result==1">
          <el-card class="box-card">
            <FormItem :label="$t('pages.recommendGroup')" prop="groupId">
              <tree-select is-filter :data="treeSelectNode" node-key="dataId" :filter-key="filterNodeKey" :checked-keys="[temp.groupId]" :width="296" @change="groupSelectChange" />
            </FormItem>
          </el-card>
        </div>
        <div v-if="temp.result==2">
          <FormItem :label="$t('pages.selectableTerminal')" prop="termId">
            <tree-select
              :data="terminalTreeData"
              :checked-keys="[terminalCheckedId]"
              :icon-option="iconOption"
              :filter-key="filterKey"
              is-filter
              :rewrite-node-click-fuc="true"
              :node-click-fuc="terminalTreeNodeCheckChange"
            />
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="approvalAccess()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.termAccessConfig')"
      :visible.sync="dialogConfigVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="configForm" :model="tempC" :rules="rulesC" label-position="right" label-width="110px" style="width: 700px; margin-left:30px;">
        <FormItem :label="$t('pages.accessMode')" label-width="110px" :extra-width="{en: -13}">
          <el-row>
            <el-col v-for="(value, key) in accessModeOptions" :key="key" :label="value" :value="key" :span="8">
              <el-radio v-model="tempC.accessMode" :label="parseInt(key)">
                {{ value }}
                <el-tooltip v-if="key==1" effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.newTerminal_text17') }}
                  </div>
                  <i class="el-icon-info"/>
                </el-tooltip>
              </el-radio>
            </el-col>
          </el-row>
        </FormItem>
        <el-col v-if="tempC.accessMode!==0" :span="12">
          <FormItem :label="$t('pages.recommendGroup')" prop="groupId" label-width="110px" :extra-width="{en: 61}">
            <tree-select is-filter :data="treeSelectNode" node-key="dataId" :checked-keys="[tempC.groupId]" :width="270" style="width:275px;" :filter-key="filterNodeKey" @change="groupOfConfigSelectChange"/>
          </FormItem>
        </el-col>
        <!-- <el-row>
          <el-col :span="24">
            <FormItem label="移动终端接入时用户名跟部门名是否必填" label-width="280px">
              <el-radio v-model="userNameIsRequired" label="1">是</el-radio>
              <el-radio v-model="userNameIsRequired" label="0">否</el-radio>
            </FormItem>
          </el-col>
        </el-row> -->
        <!-- 终端未实现，暂时注释 -->
        <!--<FormItem v-show="tempC.accessMode===2">
          <el-checkbox v-model="tempC.mobileAutoLogin" :false-label="0" :true-label="1"> <label>短信认证成功后,允许自动登录</label></el-checkbox>
        </FormItem>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateAccessConfig()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelAccessConfig()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <approval-access-to-new-dlg ref="approvalAccessToNewDlg" :terminal-type="terminalType" @submitEnd="handleRefresh"/>
  </div>
</template>

<script>
import { getApprovalInfoPage, deleteApprovalInfo, approvalAccess, getAccessConfig, addAccessConfig, updateAccessConfig, updateConfigById } from '@/api/system/terminalManage/approvalAccess'
import { getDeactiveTerminalTreeNode } from '@/api/system/terminalManage/terminal'
import { getTermTypeDict } from '@/utils/dictionary'
import { getConfigByKey } from '@/api/system/configManage/globalConfig'
import ApprovalAccessToNewDlg from '@/views/system/terminalManage/approvalAccess/approvalAccessPc/approvalAccessToNewDlg'

export default {
  name: 'ApprovalAccessMobile',
  components: { ApprovalAccessToNewDlg },
  data() {
    return {
      colModel: [
        { prop: 'guid', label: 'identificationCode', width: '120', sort: true },
        { prop: 'deptName', label: 'dept', width: '100', sort: true },
        { prop: 'termName', label: 'userName', width: '100', sort: true },
        { prop: 'computerName', label: 'models', width: '120', sort: true },
        // { prop: 'phone', label: 'phoneNumber', width: '120' },
        { prop: 'version', label: 'version', width: '100', sort: true },
        { prop: 'createTime', label: 'applicationTime', width: '150', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right',
          buttons: [
            { label: 'approval', click: this.handleApproval }
          ]
        }
      ],
      iconOption: {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        127: 'error'
      },
      filterKey: { prop: 'dataType', value: 0, showIfEqual: true, showIfNoProp: true },
      resultOptions: {
        1: this.$t('pages.approvalResult1'),
        2: this.$t('pages.approvalResult2'),
        3: this.$t('pages.approvalResult3')
      },
      accessModeOptions: {
        0: this.$t('pages.accessMode_opt1'),
        1: this.$t('pages.accessMode_opt2'),
        2: this.$t('pages.accessMode_opt3')
      },
      query: { // 查询条件
        page: 1,
        termTypes: '3,19,131',
        result: 0,
        createDate: '',
        startDate: '',
        guid: '',
        deptName: '',
        termName: '',
        computerName: '',
        version: ''
      },
      showTree: false,
      deleteable: false,
      iConfigEditable: false,
      iConfigDeleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        termId: undefined,
        ids: '',
        termName: '',
        deptName: '',
        userName: '',
        result: '1',
        groupId: 1,
        userId: ''
      },
      tempC: {},
      defaultTempC: {
        id: undefined,
        pcOrMobile: 2,
        accessMode: 0,
        groupId: 1,
        mobileAutoLogin: 0,
        opUserLoginMode: 0, // 默认数据，引擎需要的默认数据
        intelligent: 0, // 默认数据，引擎需要的默认数据
        createOpUser: 0, // 默认数据，引擎需要的默认数据
        bindOpUser: 0, // 默认数据，引擎需要的默认数据
        matchOpUser: 0
      },
      tempI: {},
      defaultTempI: {
        id: undefined,
        beginIp: '',
        endIp: '',
        groupName: 'default',
        groupId: 1
      },
      treeSelectNode: [],
      userTreeSelectNode: [],
      terminalTreeData: [],
      terminalCheckedId: '',
      userCheckedId: '',
      tempUser: {},
      dialogFormVisible: false,
      dialogConfigVisible: false,
      isSimple: true,
      existConfig: false,
      dialogStatus: '',
      titleText: this.$t('route.approvalAccess'),
      pvData: [],
      userNameIsRequired: 1,
      globalConfig: {},
      rules: {
        deptName: [{ required: true, message: this.$t('pages.validateMsg_dept'), trigger: 'blur' }],
        userName: [{ required: true, message: this.$t('pages.validateMsg_user'), trigger: 'blur' }],
        termName: [{ required: true, message: this.$t('pages.validateMsg_termName'), trigger: 'blur' }]
      },
      submitting: false,
      terminalType: 'mobile',
      rulesC: {},
      filterNodeKey: '-2'
    }
  },
  computed: {
    gridTable() {
      return this.$refs['approvalAccessList']
    }
  },
  watch: {
    '$store.state.commonData.notice.ApprovalAccess'(val) {
      this.handleRefresh()
    }
  },
  created() {
    getTermTypeDict().forEach(item => {
      this.$set(this.iconOption, item.value, item.icon)
    })
    this.resetTemp()
    this.resetTempC()
    this.getDeptTreeNode()
    this.loadTerminalTree()
    this.getGlobalConfig()
  },
  activated() {
    let exceptRootNodes = this.$store.getters.deptTree;
    // 推荐分组，不允许选择公司根节点
    if (Array.isArray(exceptRootNodes) && exceptRootNodes.length === 1 && exceptRootNodes[0].dataId == 0) {
      exceptRootNodes = this.$store.getters.deptTree[0].children
    }
    this.treeSelectNode = JSON.parse(JSON.stringify(exceptRootNodes))
  },
  methods: {
    terminalTreeNodeCheckChange: function(data, node, vm) {
      if (data.type == 1) {
        this.terminalCheckedId = data.id
        this.temp.termId = data.dataId
      } else {
        return false
      }
    },
    loadTerminalTree: function() {
      getDeactiveTerminalTreeNode().then(respond => {
        const treeData = respond.data
        if (treeData.length == 1 && treeData[0].dataId == 0) {
          // 推荐部门，不允许选择公司根节点, 过滤 回收站 节点
          this.terminalTreeData = treeData[0].children.filter(data => data.dataId != -2)
        } else {
          // 过滤 回收站 节点
          this.terminalTreeData = treeData.filter(data => data.dataId != -2)
        }
      })
    },
    terminalSelect() {
      return this.$refs['terminalGridSelect']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getApprovalInfoPage(searchQuery)
    },
    groupSelectChange: function(data) {
      this.temp.groupId = data
    },
    groupOfConfigSelectChange: function(data) {
      this.tempC.groupId = data
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    handleRefresh() {
      this.gridTable.execRowDataApi(this.query)
      this.gridTable.selectedDatasDelete()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    resetTempC() {
      if (!this.existConfig) {
        getAccessConfig({ pcOrMobile: 2 }).then(respond => {
          if (respond.data) {
            this.defaultTempC.id = respond.data.id
            this.defaultTempC.accessMode = respond.data.accessMode
            this.defaultTempC.mobileAutoLogin = respond.data.mobileAutoLogin
            this.defaultTempC.groupId = respond.data.groupId
          }
          this.existConfig = true
          this.tempC = Object.assign({}, this.defaultTempC)
        })
      } else {
        this.tempC = Object.assign({}, this.defaultTempC)
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    getDeptTreeNode: function() {
      let exceptRootNodes = this.$store.getters.deptTree;
      // 推荐分组，不允许选择公司根节点
      if (Array.isArray(exceptRootNodes) && exceptRootNodes.length === 1 && exceptRootNodes[0].dataId == 0) {
        exceptRootNodes = this.$store.getters.deptTree[0].children
      }
      this.treeSelectNode = JSON.parse(JSON.stringify(exceptRootNodes))
    },
    handleApproval(row) {
      this.resetTemp()
      this.temp.ids = row.id
      this.temp.groupId = row.groupId
      this.temp.userId = row.userId
      this.temp.termName = row.termName
      this.dialogFormVisible = true
      this.terminalCheckedId = ''
      this.isSimple = true
      this.filterKey.value = row.termType
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    approvalAccess() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.result == 2 && !this.temp.termId) {
            this.$notify({
              title: this.$t('pages.approvalFailed'),
              message: this.$t('pages.selectOfflineTerm'),
              type: 'error',
              duration: 2000
            })
          } else {
            this.submitting = true
            const tempData = Object.assign({}, this.temp)
            const msg = tempData.result == '3' ? this.$t('pages.accessReject1') : this.$t('pages.approvalSuccess')
            approvalAccess(tempData).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.isSimple = true
              this.gridTable.execRowDataApi(this.query)
              this.gridTable.selectedDatasDelete()
              this.$notify({
                title: this.$t('text.success'),
                message: msg,
                type: 'success',
                duration: 2000
              })
            })
          }
        }
      })
    },
    handleAccessConfig() {
      this.resetTempC()
      this.submitting = false
      this.dialogConfigVisible = true
      this.$nextTick(() => {
        this.$refs['configForm'].clearValidate()
      })
    },
    updateAccessConfig() {
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.tempC)
          if (this.tempC.id) {
            updateAccessConfig(tempData).then(respond => {
              this.submitting = false
              this.dialogConfigVisible = false
              this.existConfig = false
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.settingSuccess'),
                type: 'success',
                duration: 2000
              })
            })
          } else {
            addAccessConfig(tempData).then(respond => {
              this.submitting = false
              this.dialogConfigVisible = false
              this.existConfig = false
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.settingSuccess'),
                type: 'success',
                duration: 2000
              })
            })
          }
          this.globalConfig.value = this.userNameIsRequired
          updateConfigById(this.globalConfig).then(response => {
            this.$store.dispatch('commonData/changeNotice', 'selectedDepartment')
          })
        }
      })
    },
    cancelAccessConfig() {
      this.dialogConfigVisible = false
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteApprovalInfo({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    // handleAccess() {
    //   this.isSimple = false
    //   this.dialogFormVisible = true
    //   this.resetTemp()
    //   const toDeleteIds = this.gridTable.getSelectedIds()
    //   this.temp.ids = toDeleteIds.join(',')
    //   this.temp.groupId = this.tempC.groupId
    //   this.temp.termNames = this.gridTable.getSelectedDatas().map(item => { return item.termName }).join(',')
    // },
    handleAccess() {
      const rowData = []
      // 防止修改新数组里面的数据时，会影响到旧数组的数据，所以必须加JSON.parse(JSON.stringify
      JSON.parse(JSON.stringify(this.gridTable.getSelectedDatas())).forEach(item => {
        rowData.push(item)
      })
      this.$refs.approvalAccessToNewDlg.handleUpdate(rowData)
    },
    handleForbid() {
      this.$confirmBox(this.$t('pages.rejectText1'), this.$t('text.prompt')).then(() => {
        this.resetTemp()
        const toDeleteIds = this.gridTable.getSelectedIds()
        this.temp.ids = toDeleteIds.join(',')
        this.temp.termNames = this.gridTable.getSelectedDatas().map(item => { return item.termName }).join(',')
        this.temp.result = 3
        const tempData = Object.assign({}, this.temp)
        approvalAccess(tempData).then(respond => {
          this.submitting = false
          this.dialogFormVisible = false
          this.gridTable.execRowDataApi(this.query)
          this.gridTable.selectedDatasDelete()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.accessReject1'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleForbidAndDownload() {
      this.$confirmBox(this.$t('pages.rejectText2'), this.$t('text.prompt')).then(() => {
        this.resetTemp()
        const toDeleteIds = this.gridTable.getSelectedIds()
        this.temp.ids = toDeleteIds.join(',')
        this.temp.result = 4
        const tempData = Object.assign({}, this.temp)
        approvalAccess(tempData).then(respond => {
          this.submitting = false
          this.dialogFormVisible = false
          this.gridTable.execRowDataApi(this.query)
          this.gridTable.selectedDatasDelete()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.accessReject1'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    getGlobalConfig() {
      getConfigByKey({ key: 'userNameIsRequired' }).then(res => {
        const data = res.data
        if (data) {
          this.userNameIsRequired = data.value
          this.globalConfig = Object.assign({}, data)
        }
      })
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    resetQuery() {
      this.query.page = 1
      this.query.guid = ''
      this.query.deptName = ''
      this.query.termName = ''
      this.query.computerName = ''
      this.query.version = ''
    }
  }
}
</script>
