<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.PCTerminal')" name="ApprovalAccessPc">
        <Approval-access-pc ref="ApprovalAccessPc" :limit-type="1"></Approval-access-pc>
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission('115')" :label="$t('pages.MobileTerminal')" name="ApprovalAccessMobile">
        <Approval-access-mobile ref="ApprovalAccessMobile" :limit-type="2"></Approval-access-mobile>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import ApprovalAccessPc from '@/views/system/terminalManage/approvalAccess/approvalAccessPc'
import ApprovalAccessMobile from '@/views/system/terminalManage/approvalAccess//approvalAccessMobile'

export default {
  name: 'ApprovalAccess',
  components: { ApprovalAccessPc, ApprovalAccessMobile },
  data() {
    return {
      activeName: 'ApprovalAccessPc'
    }
  },
  watch: {
    $route(to, from) {
      if (to.query.mobile) {
        this.activeName = 'ApprovalAccessMobile'
      }
      if (to.query.pc) {
        this.activeName = 'ApprovalAccessPc'
      }
    }
  },
  activated() {
    if (this.$route.query.mobile) {
      this.activeName = 'ApprovalAccessMobile'
    }
    if (this.$route.query.pc) {
      this.activeName = 'ApprovalAccessPc'
    }
    this.$refs[this.activeName].$refs.approvalAccessList.execRowDataApi()
  },
  created() {
    if (this.$route.query.mobile) {
      this.activeName = 'ApprovalAccessMobile'
    }

    if (this.$route.query.pc) {
      this.activeName = 'ApprovalAccessPc'
    }
  },
  methods: {
    tabClick(pane, event) {
      window.location = window.location.href.split('?')[0]
      setTimeout(function() {
        pane.$children[0].$refs.approvalAccessList.execRowDataApi()
      }, 0)
    },
    changeTab(activeName, oldActiveName) {
    }
  }
}
</script>
<style scoped>
 .app-container{
  padding: 5px;
 }
</style>
