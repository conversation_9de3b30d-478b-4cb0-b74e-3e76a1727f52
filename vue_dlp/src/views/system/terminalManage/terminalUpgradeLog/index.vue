<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.keyword1" :value="query.keyword1">
          <span>{{ $t('table.UudateSituation') }}：</span>
          <el-select v-model="query.keyword1">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'483'" :request="exportFunc"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'415'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <!-- <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content"><span :style="active" @mouseover="over" @mouseleave="leave" @click="goToDetail">{{ $t('pages.showTips') }}</span></div>
          <i class="el-icon-info" />
        </el-tooltip> -->
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('415')"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.terminalUpgradeLogDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.taskName')">
            {{ rowDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.installPackage')">
            {{ rowDetail.fileName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.installPackageVersionNumber')">
            {{ rowDetail.packageVer }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalVersion')">
            {{ rowDetail.terminalVer }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.UudateSituation')">
            {{ statusFormatter(rowDetail) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getPage, exportTerminalUpgradeLog, deleteLog } from '@/api/behaviorAuditing/terminal/terminalUpgradeLog'
import { enableStgDelete } from '@/utils'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'TerminalUpgradeLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '200', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        // { prop: 'taskGuid', label: '任务编号', width: '150' },
        { prop: 'name', label: 'taskName', width: '100' },
        { prop: 'fileName', label: 'installPackageName', width: '250' },
        { prop: 'packageVer', label: 'installPackageVersionNumber', width: '150' },
        { prop: 'terminalVer', label: 'terminalVersion', width: '150' },
        { prop: 'status', label: 'UudateSituation', width: '200', formatter: this.statusFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('261'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        keyword1: null,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      rowDetail: {},
      dialogFormVisible: false,
      deleteable: false,
      active: 'white-space: pre-wrap;',
      statusOptions: [{ value: null, label: this.$t('pages.all') }, { value: 1, label: this.$t('pages.upgraded') }, { value: 2, label: this.$t('pages.installPackagWaitingDownload') },
        { value: 3, label: this.$t('pages.installPackagDownloading') }, { value: 4, label: this.$t('pages.installPackagStartExecute') }, { value: 5, label: this.$t('pages.installPackagDownloadFailed') }, { value: 6, label: this.$t('pages.installPackagUpgradSucceededButNotReboot') },
        { value: 7, label: this.$t('pages.installPackagSilentUpgradingOrUninstalling') }, { value: 8, label: this.$t('pages.installPackagAlreadyProcessExecuting') }, { value: 9, label: this.$t('pages.installPackagNotExecutedWithAdminPermission') }, { value: 10, label: this.$t('pages.installPackagNotRebbotAfterLastExecutionSucceeded') },
        { value: 11, label: this.$t('pages.installPackagUpgradingNotAllowedDowngrade') }, { value: 12, label: this.$t('pages.installPackagParsingFailed') }, { value: 13, label: this.$t('pages.installPackagConnectServerFailed') },
        { value: 14, label: this.$t('pages.installPackagSilentInstallationSilentPrivilegeInstallationFailed') }, { value: 16, label: this.$t('pages.termVersionFail') }, { value: 17, label: this.$t('pages.termLangVerFail') }],
      queryVideoMethod: undefined,
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    over() {
      this.active = 'white-space: pre-wrap; cursor: pointer;'
    },
    leave() {
      this.active = 'white-space: pre-wrap;'
    },
    goToDetail() {
      this.$router.push({ name: 'MobileTerminalUpgrade', params: { activeName: 'mobileTerminalTaskList' }})
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportTerminalUpgradeLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    statusFormatter(row) {
      let msg = ''
      if (row.step == 1 && row.result == 0) {
        // 等待下载安装包
        msg += this.$t('pages.installPackagWaitingDownload')
      } else if (row.step == 2 && row.result == 0) {
        // 正在下载安装包
        msg += this.$t('pages.installPackagDownloading')
      } else if (row.step == 3 && row.result == 0) {
        // 开始执行安装包
        msg += this.$t('pages.installPackagStartExecute')
      } else if (row.step == 3 && row.result == 1) {
        // 下载安装包失败
        msg += this.$t('pages.installPackagDownloadFailed')
      } else if (row.step == 4 && row.result == 0) {
        // 升级成功但未重启电脑
        msg += this.$t('pages.installPackagUpgradSucceededButNotReboot')
      } else if (row.step == 4 && row.result == 1) {
        // 静默升级或卸载中
        msg += this.$t('pages.installPackagSilentUpgradingOrUninstalling')
      } else if (row.step == 4 && row.result == 2) {
        // 该安装包已有进程在执行
        msg += this.$t('pages.installPackagAlreadyProcessExecuting')
      } else if (row.step == 4 && row.result == 3) {
        // 未以管理员权限执行安装
        msg += this.$t('pages.installPackagNotExecutedWithAdminPermission')
      } else if (row.step == 4 && row.result == 4) {
        // 上一次执行成功后未重启电脑
        msg += this.$t('pages.installPackagNotRebbotAfterLastExecutionSucceeded')
      } else if (row.step == 4 && row.result == 5) {
        // 升级中，不允许降级安装
        msg += this.$t('pages.installPackagUpgradingNotAllowedDowngrade')
      } else if (row.step == 4 && row.result == 6) {
        // 解析安装包失败
        msg += this.$t('pages.installPackagParsingFailed')
      } else if (row.step == 4 && row.result == 7) {
        // 连接服务器失败
        msg += this.$t('pages.installPackagConnectServerFailed')
      } else if (row.step == 4 && row.result == 8) {
        // 静默安装/静默提权安装失败
        msg += this.$t('pages.installPackagSilentInstallationSilentPrivilegeInstallationFailed')
      } else if (row.step == 5 && row.result == 0) {
        // 升级成功
        msg += this.$t('pages.upgraded')
      }
      return msg
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '261')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
