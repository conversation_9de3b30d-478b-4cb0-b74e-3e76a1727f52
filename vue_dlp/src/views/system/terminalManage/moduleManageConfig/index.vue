<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('button.highConfig')"
    :visible.sync="dialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div style="padding-bottom: 5px;">
      <span style="color: #409eff;">
        {{ $t('pages.moduleConfigTip') }}
      </span>
    </div>
    <Form
      ref="sysOptionsForm"
      class="global-config"
      :model="sysOptionsForm"
      label-width="60px"
      label-position="right"
    >
      <tree-select-panel
        ref="saleModuleTree"
        height="400px"
        :data="moduleTreeData"
        :include-half="true"
        :to-select-title="this.$t('pages.toSelectTitle')"
        :selected-title="this.$t('pages.selectedTitle')"
        :selected-nodes-expand-all="false"
        :header-style="{color: '#666'}"
        :panel-style="{'border-color': '#aaa', 'height': '430px'}"
        @check-change="checkChange"
      />
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="saveData">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import TreeSelectPanel from '@/components/TreeSelectPanel'
import { getModuleInfoByProdId } from '@/api/system/terminalManage/moduleConfig'
import { saveNonAutoModule, listNonAutoModule } from '@/api/system/terminalManage/moduleManageConfig'

export default {
  name: 'ModuleManageConfig',
  components: { TreeSelectPanel },
  data() {
    return {
      dialogFormVisible: false,
      submitting: false,
      sysOptionsForm: { // 配置项数据
        recId: undefined,
        optId: 2004,
        optVal: 0,
        optStrVal: '',
        optDesp: ''
      },
      moduleTreeData: [],
      defaultModuleTreeData: [
        { id: 'win', dataId: 'win', label: 'Windows', parentId: '', children: [] },
        { id: 'linux', dataId: 'linux', label: 'Linux', parentId: '', children: [] },
        { id: 'mac', dataId: 'mac', label: 'Mac', parentId: '', children: [] },
        { id: 'phone', dataId: 'phone', label: this.$t('pages.appTerminal'), parentId: '', children: [] }
      ],
      moduleIds: [],
      oldModuleIds: []
    }
  },
  computed: {
  },
  created() {
    this.initTreeData()
  },
  activated() {
  },
  methods: {
    handleDrag() {},
    show() {
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.initSelectedData()
      })
    },
    resetModuleTree() {
      this.moduleTreeData = JSON.parse(JSON.stringify(this.defaultModuleTreeData))
    },
    /**
     * 获取模块信息，生成树数据
     */
    initTreeData() {
      this.resetModuleTree()
      const productId = 511
      getModuleInfoByProdId(productId).then(respond => {
        if (respond.data && respond.data.length > 0) {
          const typeChildren = { win: [], linux: [], mac: [], phone: [] }
          respond.data.forEach(moduleInfo => {
            const { osGroup, moduleId } = moduleInfo
            let { moduleName } = moduleInfo
            if (moduleInfo.moduleName.toLowerCase().startsWith(osGroup + '_')) {
              moduleName = moduleName.substring(osGroup.length + 1)
            }
            if (osGroup && moduleId != 1 && moduleId != 2) {
              const moduleNode = {
                id: moduleId,
                dataId: moduleId,
                label: moduleName,
                parentId: osGroup
              }
              typeChildren[osGroup].push(moduleNode)
            }
          })
          const typeKey = ['win', 'linux', 'mac', 'phone']
          typeKey.forEach((value, key) => {
            this.moduleTreeData[key].children = typeChildren[value]
          });
        }
      })
    },
    initSelectedData() {
      listNonAutoModule().then(res => {
        this.oldModuleIds = res.data || []
        this.$refs['saleModuleTree'] && this.$refs['saleModuleTree'].setCheckedKeys(res.data)
      })
    },
    // 勾选变化时，更新 moduleIds
    checkChange(checkedKey, checkedData, checkedInfo) {
      this.moduleIds = checkedInfo.checkedKeys.filter(key => typeof key == 'number')
    },
    saveData() {
      this.submitting = true
      // 封装成对象
      const postData = {
        // 模块id
        moduleIds: this.moduleIds,
        // 原模块id
        oldModuleIds: this.oldModuleIds
      }
      saveNonAutoModule(postData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.configureSucceed'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    height: 100%;
    overflow: auto;
    .el-col-4{
      min-width: 200px;
    }
  }
  .save-btn-container{
    padding: 10px 50px 0;
  }
  .moudle-box{
    padding: 5px 5px 0;
    height: 420px;
    overflow: auto;
  }
  .el-checkbox{
    width: 40%;
    margin-left: 5%;
  }
  >>>.el-checkbox__label{
    margin-left: 10px;
    padding-left: 0;
  }
  .search >>>.el-checkbox__label{
    background: yellow;
  }
</style>
