<template>
  <el-dialog
    v-el-drag-dialog
    :title="title"
    :modal="false"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="500px"
    @close="clearFormValidate"
  >
    <Form ref="categoryForm" :rules="rules" :model="temp" label-position="right" label-width="60px" style="width: 450px;">
      <FormItem :label="$t('table.name')" prop="name">
        <el-input v-model="temp.name" v-trim :maxlength="20"/>
      </FormItem>
      <icon-item
        v-if="visible"
        v-model="temp.icon"
        :label="$t('table.icon')"
        :width="22"
        :height="22"
        embedded
        prop="icon"
        required
        @error="handleIconErr"
      />
      <FormItem :label="$t('text.remark')" prop="remark">
        <el-input v-model="temp.remark" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit />
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="save">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import IconItem from '@/views/system/terminalManage/softwareManager/components/IconItem'
import { getByName, addCategory, updateCategory } from '@/api/system/terminalManage/softwareCategory'

export default {
  name: 'SoftwareCategoryEdit',
  components: { IconItem },
  data() {
    return {
      visible: false,
      submitting: false,
      dialogStatus: 'create',
      temp: {},
      defaultTemp: {
        id: undefined,
        name: '',
        icon: undefined,
        remark: ''
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.inputCategoryNameTips'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        icon: [{ validator: this.iconValidator, trigger: 'blur' }]
      },
      iconErr: undefined,
      oldName: undefined,
      editFunc: undefined
    }
  },
  computed: {
    title() {
      return this.i18nConcatText(this.$t('table.softwareCategory'), this.dialogStatus)
    }
  },
  methods: {
    clearFormValidate() {
      if (this.$refs.categoryForm) {
        this.$refs.categoryForm.clearValidate()
      }
    },
    show(data, callback) {
      this.clearFormValidate()
      if (data) {
        this.dialogStatus = 'update'
        this.temp = { ...this.defaultTemp, ...data }
        this.oldName = data.name
        this.editFunc = updateCategory
      } else {
        this.dialogStatus = 'create'
        this.temp = { ...this.defaultTemp }
        this.oldName = undefined
        this.editFunc = addCategory
      }
      this.callback = callback
      this.iconErr = undefined
      this.visible = true
    },
    save() {
      this.$refs.categoryForm.validate(valid => {
        if (valid) {
          this.submitting = true
          this.editFunc(this.temp).then(res => {
            if (this.callback) {
              this.callback(res.data)
            }
            this.$store.dispatch('commonData/setSoftwareCategories')
            this.$notify({
              title: this.$t('text.success'),
              message: this.title + this.$t('text.success'),
              type: 'success',
              duration: 2000
            })
            this.visible = false
          }).finally(() => {
            this.submitting = false
          })
        }
      })
    },
    handleIconErr(error) {
      this.iconErr = error
      if (!this.$refs.categoryForm) {
        return
      }
      if (error) {
        this.$refs.categoryForm.validateField('icon')
      } else {
        this.$refs.categoryForm.clearValidate('icon')
      }
    },
    nameValidator(rule, value, callback) {
      if (value && value !== this.oldName) {
        getByName(value).then(res => {
          const category = res.data
          if (category && category.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      } else {
        callback()
      }
    },
    iconValidator(rule, value, callback) {
      if (this.iconErr) {
        callback(this.iconErr)
      } else if (!value) {
        callback(this.$t('page.uploadCategoryIconTips'))
      } else {
        callback()
      }
    }
  }
}
</script>
