<template>
  <div class="category-select">
    <el-select ref="select" :value="value" :disabled="disabled" filterable @change="handleChange">
      <el-option v-for="item in showCategories" :key="item.id" :label="item.name" :value="item.id">
        <div style="display: flex;">
          <div style="flex: 1;">
            <img style="width: 16px; height: 16px; vertical-align: middle;" :src="item.icon" alt="ico"/>
            <span>{{ item.name }}</span>
          </div>
          <div v-if="item.editable">
            <svg-icon v-if="editable" icon-class="edit" :title="$t('pages.edit')" style="color: #68a8d0" @click.stop="editNode(item)"/>
            <svg-icon v-if="deletable" icon-class="delete" :title="$t('pages.delete')" @click.stop="delNode(item)"/>
          </div>
        </div>
      </el-option>
    </el-select>
    <div v-if="addable" :class="addBtnClass">
      <svg-icon icon-class="add" :title="$t('pages.add')" @click="addNode"/>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'SoftwareCategorySelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Number,
      default: undefined
    },
    disabled: {
      type: Boolean,
      default: false
    },
    addable: {
      type: Boolean,
      default: true
    },
    editable: {
      type: Boolean,
      default: true
    },
    deletable: {
      type: Boolean,
      default: true
    },
    hideCategoryIds: {
      type: Array,
      default: null
    }
  },
  computed: {
    ...mapGetters(['softwareCategories']),
    showCategories() {
      if (!this.hideCategoryIds || !this.hideCategoryIds.length) {
        return this.softwareCategories
      }
      return this.softwareCategories.filter(item => this.hideCategoryIds.indexOf(item.id) < 0)
    },
    addBtnClass() {
      let className = 'category-select-add'
      if (this.disabled) {
        className += '-disabled'
      }
      return className
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
    addNode() {
      if (this.disabled) {
        return
      }
      this.$emit('create', item => this.handleChange(item.id))
    },
    editNode(item) {
      if (this.disabled) {
        return
      }
      this.$refs.select.blur()
      this.$emit('update', item)
    },
    delNode(item) {
      if (this.disabled) {
        return
      }
      this.$refs.select.blur()
      this.$emit('delete', item, () => {
        if (this.value === item.id) {
          this.handleChange()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .category-select {
    display: flex;
    .category-select-add {
      color: #68a8d0;
      margin-left: 5px;
      svg {
        cursor: pointer;
      }
    }
    .category-select-add-disabled {
      margin-left: 5px;
      cursor: not-allowed;
    }
  }
</style>
