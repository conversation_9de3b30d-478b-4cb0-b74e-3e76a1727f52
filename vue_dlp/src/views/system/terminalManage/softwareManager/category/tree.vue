<template>
  <tree-menu
    ref="typeTree"
    resizeable
    :default-expand-all="true"
    :data="treeData"
    :is-filter="true"
    :multiple="multiple"
    :checked-keys="checkedKeys"
    :render-content="renderContent"
    @node-click="treeNodeClick"
    @check-change="checkChange"
  />
</template>

<script type="text/jsx">
import { mapGetters } from 'vuex'

export default {
  name: 'SoftwareCategoryTree',
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    checkedKeys: {
      type: Array,
      default() {
        return []
      }
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('route.softwareRepository'), parentId: '', children: [] }]
    }
  },
  computed: {
    ...mapGetters(['softwareCategories'])
  },
  watch: {
    softwareCategories() {
      this.loadTree()
      if (this.multiple) {
        // 只有允许选择分类
        this.$emit('categoryChange', this.treeData)
      }
    }
  },
  created() {
    this.$store.dispatch('commonData/setSoftwareCategories')
  },
  methods: {
    loadTree() {
      this.treeData[0].children = []
      this.softwareCategories.forEach(item => {
        this.treeData[0].children.push({
          id: 'C' + item.id,
          dataId: item.id,
          label: item.name,
          icon: item.icon,
          parentId: 'G0',
          children: [],
          oriData: item
        })
      })
    },
    checkChange(dataId, nodes) {
      if (this.multiple) {
        var nodeIds = []
        nodes.forEach(item => { nodeIds.push(item.id) })
        this.$emit('checkChange', nodeIds)
      }
    },
    // 通过 keys 设置目前勾选的节点，使用此方法必须设置 node-key 属性
    // (keys, leafOnly) 接收两个参数，1. 勾选节点的 key 的数组 2. boolean 类型的参数，若为 true 则仅设置叶子节点的选中状态，默认值为 false
    setCheckedKeys(keys, leafOnly = false) {
      return this.$refs['typeTree'].setCheckedKeys(keys, leafOnly)
    },
    clearSelectedNodes() {
      return this.$refs['typeTree'].clearSelectedNodes
    },
    getSelectedCategories() {
      return this.$refs['typeTree'].getCheckedNodes().filter(item => item.id !== 'G0').map(item => item.oriData)
    },
    renderContent(h, { node, data, store }) {
      const isRoot = data.id === 'G0'
      const image = isRoot ? undefined : (<img style='width: 16px; height: 16px;' src={data.icon} alt='ico'/>)
      const style = isRoot ? undefined : 'margin-left: 5px;'
      let button
      if (this.editable) {
        if (isRoot) {
          button = (
            <span class='el-ic'>
              <svg-icon icon-class='add' title={this.$t('button.insert')} className='icon-space' on-click={r => this.handleCreate()}/>
            </span>
          )
        } else if (data.oriData.editable) {
          button = (
            <span class='el-ic'>
              <svg-icon icon-class='edit' title={this.$t('button.edit')} className='icon-space' on-click={event => this.handleUpdate(event, data)}/>
              <svg-icon icon-class='delete' title={this.$t('button.delete')} className='icon-space' on-click={event => this.removeNode(event, data)}/>
            </span>
          )
        }
      }
      return (
        <div class='custom-tree-node'>
          {image}
          <span style={style}>{data.label}</span>
          {button}
        </div>
      )
    },
    treeNodeClick(data, node, el) {
      this.$emit('click', data.oriData, node)
    },
    handleCreate() {
      this.$emit('create')
    },
    handleUpdate(event, data) {
      event.stopPropagation()
      this.$emit('update', data.oriData)
    },
    removeNode(event, data) {
      event.stopPropagation()
      this.$emit('delete', data.oriData)
    }
  }
}
</script>

<style scoped>

</style>
