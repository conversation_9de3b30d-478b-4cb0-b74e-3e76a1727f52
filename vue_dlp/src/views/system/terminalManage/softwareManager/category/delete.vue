<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="i18nConcatText(this.$t('table.softwareCategory'), 'delete')"
    :visible.sync="visible"
    width="400px"
  >
    <Form ref="dataForm" :model="temp" @submit.native.prevent>
      <!--该分类下存在软件，删除分类时可选择以下操作-->
      <p>{{ $t('pages.softwareCategoryOperateTips') }}</p>
      <FormItem>
        <el-radio-group v-model="temp.deleteType">
          <!--下架该分类下的所有软件-->
          <el-radio :label="1">{{ $t('pages.softwareCategoryOperateTips1') }}</el-radio>
          <!--将该分类下的所有软件移至分类-->
          <el-radio :label="2">{{ $t('pages.softwareCategoryOperateTips2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :error="categoryErr">
        <software-category-select
          v-model="temp.movedId"
          :disabled="temp.deleteType !== 2"
          :deletable="false"
          :hide-category-ids="hideCategoryIds"
          @change="categoryErr = null"
          @create="callback => $emit('create', callback)"
          @update="data => $emit('update', data)"
        />
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleSubmit">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import SoftwareCategorySelect from './select'

export default {
  name: 'SoftwareCategoryDelete',
  components: { SoftwareCategorySelect },
  data() {
    return {
      visible: false,
      submitting: false,
      categoryErr: undefined,
      temp: {},
      defaultTemp: {
        id: undefined,
        deleteType: 1,
        movedId: undefined
      },
      hideCategoryIds: []
    }
  },
  methods: {
    show(id, callback) {
      this.callback = callback
      this.temp = { ...this.defaultTemp, id }
      this.hideCategoryIds = [id]
      this.categoryErr = undefined
      this.submitting = false
      this.visible = true
    },
    handleSubmit() {
      const { deleteType, movedId } = this.temp
      if (deleteType === 2 && !movedId) {
        this.categoryErr = this.$t('pages.pleaseSelectContent', { content: this.$t('table.softwareCategory') })
        return
      }
      this.submitting = true
      this.$emit('submitted', { ...this.temp }, this.submitCallback)
    },
    submitCallback(e) {
      this.submitting = false
      if ('cancel' === e) {
        // 如果是确认框种输入取消，那么不需要关闭
        return
      }
      if (this.callback) {
        this.callback()
      }
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-form>p {
    margin-block: 0.5em;
  }
</style>
