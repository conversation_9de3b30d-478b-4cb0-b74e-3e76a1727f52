<template>
  <el-popover
    v-model="visible"
    trigger="manual"
    placement="bottom-start"
    :width="popoverWidth"
    popper-class="addr-autocomplete"
  >
    <el-input
      slot="reference"
      ref="input"
      :value="value"
      :type="type"
      :title="inputTitle"
      :clearable="clearable"
      :maxlength="maxlength"
      :placeholder="placeholder"
      :show-word-limit="showWordLimit"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
      @clear="handleClear"
      @keydown.up.native.prevent="highlight(highlightedIndex - 1)"
      @keydown.down.native.prevent="highlight(highlightedIndex + 1)"
      @keydown.enter.native="handleKeyEnter"
      @keydown.native.tab="closeSuggestions"
    />
    <div v-if="title || $slots.title" class="el-popover__title" @mousedown="handleMousedown">
      <slot name="title">{{ title }}</slot>
    </div>
    <el-scrollbar ref="suggestions" wrap-class="suggestion-wrap" view-class="suggestion-list">
      <div
        v-for="(item, index) in filteredSuggestions"
        :key="index"
        class="suggestion-item"
        :class="{ highlighted: item.selected || highlightedIndex === index }"
        :title="showContentTitle ? item.value : undefined"
        @click="handleSelect(item, index)"
      >
        <slot :item="item">{{ item.value }}</slot>
      </div>
    </el-scrollbar>
  </el-popover>
</template>

<script>
export default {
  name: 'SuggestInput',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    // 输入绑定值
    value: { type: String, default: undefined },
    // 输入框类型（text 或 textarea）
    type: { type: String, default: 'text' },
    // 是否可清空
    clearable: { type: Boolean, default: false },
    // 最大输入长度
    maxlength: { type: Number, default: undefined },
    // 输入框提示信息
    inputTitle: { type: String, default: undefined },
    // 输入框占位文本
    placeholder: { type: String, default: undefined },
    // 是否显示输入字数统计，只在 type = "text" 或 type = "textarea" 时有效
    showWordLimit: { type: Boolean, default: false },
    // 输入建议对象中用于显示的键名，若对象本身为字符串或数字，将忽略该属性
    valueKey: { type: String, default: 'value' },
    // 建议弹出框标题（输入建议提示信息）
    title: { type: String, default: undefined },
    // 建议的数据
    suggestions: { type: Array, default: () => [] },
    // 总是显示输入建议提示
    alwaysShowTitle: { type: Boolean, default: false },
    // 鼠标移到到建议项上显示全部内容
    showContentTitle: { type: Boolean, default: true }
  },
  data() {
    return {
      visible: false,
      popoverWidth: 200,
      highlightedIndex: -1
    }
  },
  computed: {
    suggestionItems() {
      return this.suggestions.map((item, index) => ({ id: index, value: this.getSuggestionValue(item), data: item }))
    },
    filteredSuggestions() {
      this.suggestionItems.forEach(item => {
        item.selected = false
      })
      if (!this.value) {
        return this.suggestionItems
      }
      const lowerCaseValue = this.value.toLowerCase()
      const filtered = this.suggestionItems.filter(item => item.value.toLowerCase().includes(lowerCaseValue))
      filtered.forEach(item => {
        item.selected = item.value === this.value
      })
      return filtered
    },
    lastSuggestionIndex() {
      return this.filteredSuggestions.length - 1
    }
  },
  watch: {
    visible(value) {
      if (value) {
        this.popoverWidth = parseFloat(getComputedStyle(this.$refs.input.$el, null).width)
      }
    }
  },
  methods: {
    getSuggestionValue(item) {
      if (item == null) {
        return ''
      }
      if (typeof item === 'object') {
        return item[this.valueKey]
      }
      return item.toString()
    },
    showSuggestions() {
      if (this.alwaysShowTitle || this.filteredSuggestions.length > 0) {
        this.visible = true
      }
    },
    closeSuggestions() {
      this.visible = false
    },
    handleInput(value) {
      this.$emit('input', value)
      this.showSuggestions()
    },
    handleChange(value) {
      this.$emit('change', value)
    },
    handleFocus(event) {
      this.highlightedIndex = -1
      this.$emit('focus', event)
      this.showSuggestions()
    },
    handleBlur(event) {
      this.$emit('blur', event)
      this.closeSuggestions()
    },
    handleClear() {
      this.$emit('clear')
      this.showSuggestions()
    },
    highlight(index) {
      if (index < 0) {
        this.highlightedIndex = 0
        return
      }
      if (index > this.lastSuggestionIndex) {
        this.highlightedIndex = this.lastSuggestionIndex
        return
      }
      const suggestion = this.$refs.suggestions.$el.querySelector('.suggestion-wrap');
      const suggestionList = suggestion.querySelectorAll('.suggestion-list .suggestion-item');
      const highlightItem = suggestionList[index];
      const scrollTop = suggestion.scrollTop
      const offsetTop = highlightItem.offsetTop
      if (offsetTop + highlightItem.scrollHeight > (scrollTop + suggestion.clientHeight)) {
        suggestion.scrollTop += highlightItem.scrollHeight
      }
      if (offsetTop < scrollTop) {
        suggestion.scrollTop -= highlightItem.scrollHeight
      }
      this.highlightedIndex = index
    },
    handleKeyEnter(e) {
      e.preventDefault()
      if (this.highlightedIndex >= 0 && this.highlightedIndex < this.suggestions.length) {
        this.handleSelect(this.filteredSuggestions[this.highlightedIndex], this.highlightedIndex)
      } else {
        this.handleSelect({ value: this.value }, -1);
      }
    },
    handleMousedown(e) {
      // 阻止点击输入建议提示时输入框失焦
      e.preventDefault()
    },
    handleSelect(item, index) {
      this.$refs.input.focus()
      this.$emit('input', item.value)
      this.$emit('select', item, index)
      this.$nextTick(() => {
        this.highlightedIndex = -1
        this.$refs.input.blur()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-popover {
    .el-popover__title {
      font-size: 12px;
      color: #909399;
      line-height: 16px;
      margin-bottom: 5px;
      padding: 0 5px;
      cursor: default;
    }
  }
  .el-scrollbar {
    >>>.el-scrollbar__wrap {
      margin-bottom: 0 !important;
      max-height: 190px;
      overflow-x: hidden;
    }
    >>>.el-scrollbar__thumb {
      background: transparent;
    }
    &:hover {
      >>>.el-scrollbar__thumb {
        background-color: rgba(144, 147, 153, .5);
      }
    }
    .suggestion-item {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      padding: 0 5px;
      position: relative;
      color: #606266;
      height: 30px;
      line-height: 30px;
      box-sizing: border-box;
      cursor: pointer;
      &.highlighted, &:hover {
        background-color: #F5F7FA;
      }
    }
  }
</style>
