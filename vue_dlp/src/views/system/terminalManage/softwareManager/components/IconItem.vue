<template>
  <div>
    <FormItem class="icon-item" v-bind="$attrs" :label="label">
      <el-tooltip v-if="tooltipContents.length" slot="tooltip" effect="dark" :placement="tooltipPlacement">
        <div slot="content" style="line-height: 20px">
          <div v-for="(content, index) in tooltipContents" :key="index">{{ content }}</div>
        </div>
        <i class="el-icon-info"/>
      </el-tooltip>
      <div class="icon-item-content">
        <div class="icon-image" :style="imgStyle">
          <img v-show="loaded" :style="imgStyle" :src="src" :alt="alt" @load="handleLoad" @error="handleError"/>
          <div v-show="!loaded" :style="imgStyle" class="img-placeholder">
            <i :class="loading ? 'el-icon-loading' : 'el-icon-picture-outline'"/>
          </div>
        </div>
        <div class="icon-upload" :style="uploadStyle">
          <label for="file">
            <span ref="btn" class="el-button el-button--primary el-button--mini" :class="{ 'is-loading': loading }">
              <i :class="loading ? 'el-icon-loading' : 'el-icon-upload'"/>
              <span>{{ uploadButtonName }}</span>
            </span>
            <text-center-ellipsis v-if="showFilename" :style="ellipsisStyle" :text="filename"/>
          </label>
          <input id="file" type="file" :accept="accept" :disabled="loading" @change="handleChange"/>
        </div>
      </div>
    </FormItem>
    <FormItem v-if="embedded" class="embedded-icons" v-bind="$attrs" label="" prop="">
      <div
        v-for="(icon, index) in embeddedIcons"
        :key="index"
        class="embedded-icon"
        :class="{ 'embedded-icon-checked': index === curIndex }"
        @click="handleEmbeddedIconClick(index)"
      >
        <img :style="imgStyle" :src="icon.url" :alt="icon.name"/>
        <!--<div>{{ icon.name }}</div>-->
      </div>
    </FormItem>
  </div>
</template>

<script>
import { readAsDataURL } from '@/utils/blob'
import { IcoImage, loadImageData } from '@/utils/image'
import { compressPng } from '@/utils/image/png/resize'
import TextCenterEllipsis from './TextCenterEllipsis'

// require.context() 返回的是函数，通过keys()获取到的是文件名数组
const ctx = require.context('@/assets/softwareCategory', false, /(\.jpg|\.png|\.bmp)$/)
const embeddedIcons = ctx.keys()
  .map(key => ({ name: key.slice(2), url: ctx(key) }))
  // 中英文数字混合排序: 数字 < 字母（不区分大小写）< 汉字
  .sort((icon1, icon2) => {
    const a = icon1.name.toLowerCase()
    const b = icon2.name.toLowerCase()
    const len = Math.min(a.length, b.length)
    let charCodeA, charCodeB, compared
    for (let i = 0; i < len; i++) {
      charCodeA = a.charCodeAt(i)
      charCodeB = b.charCodeAt(i)
      if (charCodeA < 128 || charCodeB < 128) {
        compared = charCodeA - charCodeB
      } else {
        compared = String.fromCharCode(charCodeA).localeCompare(String.fromCharCode(charCodeB))
      }
      if (compared !== 0) {
        return compared
      }
    }
    return a.length - b.length
  })

export default {
  name: 'IconItem',
  components: { TextCenterEllipsis },
  model: {
    prop: 'src',
    event: 'change'
  },
  props: {
    label: {
      type: String,
      default: ''
    },
    src: {
      type: String,
      default: undefined
    },
    alt: {
      type: String,
      default: 'ico'
    },
    width: {
      type: Number,
      default: 32
    },
    height: {
      type: Number,
      default: 32
    },
    embedded: {
      type: Boolean,
      default: false
    },
    tooltip: {
      type: [String, Array],
      default() {
        const tips = [this.$t('pages.iconFormatTips'), this.$t('pages.iconSizeTips', { width: this.width, height: this.height })]
        if (this.embedded) {
          tips.push(this.$t('pages.embeddedIconTips'))
        }
        return tips
      }
    },
    tooltipPlacement: {
      type: String,
      default: 'top'
    },
    showFilename: {
      type: Boolean,
      default: true
    },
    uploadButtonName: {
      type: String,
      default() {
        return this.$t('button.uploadIcon')
      }
    }
  },
  data() {
    return {
      loaded: false,
      loading: false,
      changed: false,
      filename: undefined,
      accept: 'image/png,image/jpeg,image/bmp,image/x-icon',
      ellipsisStyle: undefined,
      curIndex: -1,
      embeddedIcons: embeddedIcons
    }
  },
  computed: {
    tooltipContents() {
      if (Array.isArray(this.tooltip)) {
        return this.tooltip
      }
      if (this.tooltip != null && this.tooltip.toString().trim().length > 0) {
        return [this.tooltip]
      }
      return []
    },
    imgStyle() {
      return `width: ${this.width}px; height: ${this.height}px;`
    },
    uploadStyle() {
      return `width: calc(100% - ${10 + this.width}px)`
    }
  },
  watch: {
    src: {
      deep: true,
      handler(value) {
        this.watchSrc(value)
      }
    },
    uploadButtonName: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          const btnW = getComputedStyle(this.$refs.btn, null).width
          const delW = parseFloat(btnW) + 5
          this.ellipsisStyle = `max-width: calc(100% - ${delW}px);`
        })
      }
    }
  },
  beforeMount() {
    if (this.embedded) {
      if (this.src) {
        this.curIndex = embeddedIcons.map(icon => icon.url).indexOf(this.src)
      } else {
        this.handleEmbeddedIconClick(0)
      }
    }
  },
  methods: {
    watchSrc(src) {
      if (this.changed) {
        this.changed = false
        return
      }
      if (!src) {
        this.loaded = false
        this.filename = this.$t('pages.noFileSelected')
      } else {
        this.filename = undefined
      }
    },
    emitError(error) {
      if (error) {
        this.loaded = false
        this.loading = false
      }
      this.$emit('error', error)
    },
    emitChange(url) {
      this.changed = this.src !== url
      this.$emit('change', url)
    },
    beforeUpload(file) {
      this.loaded = false
      this.loading = true
      this.emitChange()
      if (file.type.indexOf('image/') < 0) {
        // 请上传图像文件
        this.emitError(this.$t('pages.uploadImageFileTips'))
        return false
      }
      if (this.accept.indexOf(file.type) < 0) {
        // 上传的图像格式不支持，请上传png、jpg、bmp格式图像
        this.emitError(this.$t('pages.imageFormatUnsupported'))
        return false
      }
      this.emitError()
      if (file.type === 'image/x-icon') {
        this.loadAndResizeIcoImage(file)
      } else {
        this.loadAndResizePngImage(file)
      }
      return false
    },
    loadAndResizeIcoImage(file) {
      IcoImage.fromFile(file).then(ico => {
        const sortedEntries = ico.entries.sort((entry1, entry2) => {
          const cp = entry1.width - entry2.width
          if (cp !== 0) {
            return cp
          }
          return entry2.colorCount - entry1.colorCount
        })
        let entry
        for (let i = 0; i < sortedEntries.length; i++) {
          entry = sortedEntries[i]
          if (entry.width >= this.width && entry.height >= this.height) {
            break
          }
        }
        if (entry.width < this.width) {
          return entry.image.getImageData(entry.width, entry.height)
        }
        return entry.image.getImageData(this.width, this.height)
      }).then(imageData => {
        const pngBuffer = compressPng(imageData)
        const blob = new Blob([pngBuffer], { type: 'image/png' })
        readAsDataURL(blob).then(this.emitChange)
      }).catch(this.catchLoadImageError)
    },
    loadAndResizePngImage(file) {
      loadImageData(file, this.width, this.height).then(imageData => {
        const pngBuffer = compressPng(imageData)
        if (file.size < pngBuffer.byteLength) {
          readAsDataURL(file).then(this.emitChange)
        } else {
          const blob = new Blob([pngBuffer], { type: 'image/png' })
          readAsDataURL(blob).then(this.emitChange)
        }
      }).catch(this.catchLoadImageError)
    },
    catchLoadImageError(e) {
      // 上传的图像被加密或格式不支持，请重新上传合适尺寸的未加密图像
      this.emitError(this.$t('pages.imageEncryptedOrUnsupported'))
      console.error(e)
    },
    handleLoad() {
      this.loading = false
      this.loaded = true
    },
    handleError() {
      this.loading = false
      this.loaded = false
    },
    handleChange(event) {
      const files = event.target.files
      if (!files || files.length === 0) {
        return
      }
      const file = files[0]
      // 清除文件选中状态
      event.target.value = ''

      this.filename = file.name
      this.curIndex = -1
      this.beforeUpload(file)
    },
    handleEmbeddedIconClick(index) {
      this.curIndex = index
      this.filename = undefined
      this.emitChange(embeddedIcons[index].url)
      this.emitError()
    }
  }
}
</script>

<style lang="scss" scoped>
  .icon-item {
    .icon-item-content {
      display: flex;
      align-items: center;
      > div {
        display: inline-block;
      }
      .icon-image {
        img {
          display: block;
        }
        .img-placeholder {
          display: flex;
          justify-content: center;
          align-items: center;
          background: #f5f7fa;
          color: #909399;
          font-size: 14px;
        }
      }
      .icon-upload {
        margin-left: 10px;
        label {
          font-weight: normal;
          color: #666666;
          .el-button {
            margin-bottom: 0;
            vertical-align: bottom;
          }
          .text-center-ellipsis {
            max-width: calc(100% - 80px);
          }
        }
        input[type="file"] {
          display: none;
        }
      }
    }
  }
  .embedded-icons {
    margin-top: 4px;
    .embedded-icon {
      list-style: none;
      line-height: 9px;
      margin: 0 4px 4px 0;
      padding: 10px;
      width: 50px;
      display: inline-block;
      text-align: center;
      box-sizing: border-box;
      border: 1px solid transparent;
      &:hover {
        background-color: #ddd;
        cursor: pointer;
      }
    }
    .embedded-icon.embedded-icon-checked {
      position: relative;
      border-color: #337ab7;
      background-color: transparent;
      &:after {
        position: absolute;
        right: 0;
        top: 0;
        content: '';
        width: 0;
        height: 0;
        border-top: 4px solid #337ab7;
        border-right: 4px solid #337ab7;
        border-left: 4px solid transparent;
        border-bottom: 4px solid transparent;
      }
    }
  }
</style>
