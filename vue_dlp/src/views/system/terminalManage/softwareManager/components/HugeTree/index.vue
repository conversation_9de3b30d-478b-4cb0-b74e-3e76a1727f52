<template>
  <div class="huge-tree-wrapper" :style="`height: ${treeHeight}px`">
    <section v-if="showSearchBar" class="search-bar">
      <el-input
        v-model="keyword"
        clearable
        maxlength
        :placeholder="placeholder"
        @keyup.enter="init"
        @input="debounceInput"
      >
        <slot slot="append" name="search-append"/>
      </el-input>
    </section>
    <section v-show="!isSearching && renderList.length > 0" ref="tree" class="huge-tree" @scroll="onScroll">
      <div class="huge-tree__phantom" :style="`height: ${phantomHeight}px`"></div>
      <div class="huge-tree__content" :style="`transform: translateY(${startIndex * itemHeight}px)`">
        <template v-for="(item, index) in renderList">
          <section
            v-if="item.path"
            :key="'k' + index"
            class="huge-tree-node-wrapper"
            :class="{ 'is-hidden': item.isHidden, 'is-active': highlightCurrent && item.id === currentNode.id }"
            :style="`padding-left: ${(item.path.length - 1) * Number(indent)}px`"
            @click.capture="expandOnClickNode && onExpand(item, index)"
          >
            <div
              v-if="showExpandIcon"
              :class="['node-prefix', { 'expand-node': !item.isLeaf, 'is-expand': item.isExpand }]"
              @click.stop="!expandOnClickNode && !item.isLeaf && onExpand(item, index)"
            />
            <huge-tree-node
              :node="item"
              :show-checkbox="showCheckbox"
              :show-checkbox-leaf-only="showCheckboxLeafOnly"
              :checked-action="checkedAction"
              :check-strictly="checkStrictly"
              :show-node-count="showNodeCount"
              @on-checked="onChecked(item)"
              @on-click-label="onClickLabel(item)"
              @on-click-node="e => $emit('click-node', item, e)"
            >
              <slot slot-scope="{ node }" :node="node" :index="index"/>
              <slot slot="icon" slot-scope="{ node }" name="icon" :node="node" :index="index"/>
            </huge-tree-node>
          </section>
        </template>
      </div>
    </section>
    <section v-show="isSearching || renderList.length === 0" class="no-data">
      <p v-if="loading || isSearching">
        <slot name="loading">{{ loadingText }}</slot>
      </p>
      <p v-else>{{ emptyText }}</p>
    </section>
  </div>
</template>

<script>
import HugeTreeNode from './node'
import {
  isIncludesKeyword,
  calcLeafCount,
  depthFirstEach,
  findSubTree,
  findNode,
  isBrother,
  isCheckedOrIndeterminate,
  throttle,
  debounce,
  clearAll
} from './util'

class BigData {
    tree = [] // 海量数据tree
    list = [] // 扁平化的tree
    listMap = {} // this.big.list 对应的 map
    filterTree = [] // 根据关键词过滤后的tree
    filterList = [] // 根据关键词过滤后的list
    disabledList = [] // disabled 为true组成的数组
    checkedKeys = [] // 选中的 ids
    checkedNodes = [] // 选中的 nodes
    allCheckedList = [] // 所有选中的节点， 用于开启开关 isOnlyInCheckedSearch 时， 仅在选中节点里筛选。
}

export default {
  name: 'HugeTree',
  components: { HugeTreeNode },
  props: {
    checkOver: { type: Number, default: 1000 },
    // 显示搜索框
    showSearchBar: { type: Boolean, default: false },
    searchMethod: {
      type: Function,
      default(keyword, node) {
        if (!node || !node.label) return false
        return !keyword || node.label.toLowerCase().includes(keyword)
      }
    },
    // 输入框 placeholder
    placeholder: { type: String, default: '输入关键字过滤' },
    /** 强制使用id，提高性能 */
    // 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的
    // nodeKey: { type: String, default: 'id' },
    // 缩进
    indent: { type: Number, default: 16 },
    // 树最大高度（不包括搜索框）
    maxHeight: { type: Number, default: 500 },
    // 是否高亮当前选中节
    highlightCurrent: { type: Boolean, default: true },
    // 展开层级 -1: 展开全部; 1: 只展示第一层(最外层); 2: 展示到第二层; ...
    expandLevel: { type: Number, default: -1 },
    // 显示节点对应的数据量
    showNodeCount: { type: Boolean, default: false },
    // 显示展开/折叠箭头图标
    showExpandIcon: { type: Boolean, default: true },
    // 是否在点击节点的时候展开或者收缩节点， 默认值为 false，只有点箭头图标的时候才会展开或者收缩节点。
    expandOnClickNode: { type: Boolean, default: false },
    // 默认展开的节点的 key 的数组
    defaultExpandedKeys: { type: Array, default: () => [] },
    // 加载中
    loading: { type: Boolean, default: false },
    // 加载状态提示文字
    loadingText: { type: String, default: 'loading...' },
    // 在 label 上选中动作, 点击 label 选中 --> none: 不选中；click: 单击； dblclick: 双击；
    checkedAction: { type: String, default: 'none' },
    // 内容为空展示的文本
    emptyText: { type: String, default: '暂无数据' },
    // 是否展示checkbox
    showCheckbox: { type: Boolean, default: false },
    // 是否仅叶子节点展示 checkbox, show-checkbox 为 true 时有效
    showCheckboxLeafOnly: { type: Boolean, default: false },
    // 默认勾选值
    defaultCheckedKeys: { type: Array, default: () => [] },
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
    checkStrictly: { type: Boolean, default: false }
  },
  data() {
    return {
      count: 1, // 用于视图更新
      keyword: '', // 关键词
      isSearching: false, // 搜索中
      itemHeight: 27, // 每一项的高度
      startIndex: 0, // 渲染的开始区间
      endIndex: 70, // 渲染的结束区间
      debounceInput: () => { this.init() }, // 输入防抖
      throttleScroll: () => {}, // 滚动节流
      isOnlyInCheckedSearch: false,
      currentNode: {}
    }
  },
  computed: {
    // 过滤掉 hidden 节点
    unHiddenList() {
      return this.count ? this.big.filterList.filter(i => !i.isHidden) : []
    },
    // 虚拟高度，与隐藏的数量有关
    phantomHeight() {
      return this.unHiddenList.length * this.itemHeight
    },
    renderList() {
      return this.unHiddenList.slice(this.startIndex, this.endIndex)
    },
    treeHeight() {
      let contentHeight = 30
      if (this.renderList.length) {
        contentHeight = this.renderList.length * this.itemHeight + 8
      }
      let visibleHeight = 10 + Math.min(this.maxHeight, contentHeight)
      if (this.showSearchBar) {
        visibleHeight += 36
      }
      return visibleHeight
    }
  },
  watch: {
    defaultCheckedKeys(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.setCheckedKeys(newVal)
      }
    },
    defaultExpandedKeys(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.setExpand(newVal)
      }
    }
  },
  created() {
    this.big = new BigData()
    this.big.checkedKeys = JSON.parse(JSON.stringify(this.defaultCheckedKeys))
    this.throttleScroll = throttle(this.setRenderRange, 80)
  },
  beforeDestroy() {
    this.clear()
  },
  methods: {
    // 设置海量数据
    setData(data) {
      this.clear()
      this.big.tree = data
      this.init('__INIT__')
      const wait = Math.ceil(this.big.list.length / 150)
      this.debounceInput = debounce(this.init, wait)
    },

    init(op) {
      // op: __INIT__, __RESTORE__, __SHOW_CHECKED_ONLY__
      if (this.big.tree.length === 0) return
      if (op === '__INIT__') {
        this.flatTree(this.big.tree)
        this.big.list.forEach(node => { this.big.listMap[node.id] = node })
      }
      this.initFilter(op)
      if (op === '__INIT__' || op === '__RESTORE__') this.initExpand()
      if (this.showCheckbox) {
        this.setCheckedKeys(this.big.checkedKeys)
      }
      this.backToTop()
    },

    // 拉平 tree
    flatTree(data) {
      depthFirstEach(data, true, [], node => {
        this.big.list.push(node)
      })
    },

    // 初始化处理展开逻辑
    initExpand() {
      if (!this.big || this.big.tree.length === 0) return
      if (this.defaultExpandedKeys.length > 0) {
        this.setExpand(this.defaultExpandedKeys)
        return
      }
      this.initExpandTrigger()
      this.setCount()
    },
    initExpandTrigger() {
      // if (/^\d+$/.test(this.expandLevel)) {
      if (this.expandLevel !== -1) {
        this.big.filterList.forEach(node => {
          node.isExpand = Boolean(node.path.length < this.expandLevel)
          node.isHidden = Boolean(node.path.length > this.expandLevel)
          this.initNode(node)
        })
      } else {
        // 展开全部
        this.big.filterList.forEach(node => {
          node.isExpand = true
          node.isHidden = false
          this.initNode(node)
        })
      }
    },
    // 指定id展开
    setExpand(keys = []) {
      if (keys.length <= 0) return
      if (!this.big || this.big.tree.length === 0) return
      const nodes = keys.map(key => this.big.listMap[key]).filter(v => v)
      const ids = Array.from(new Set(nodes.map(node => node.path).flat(1)))
      this.big.filterList.forEach(node => {
        if (node.isLeaf) {
          node.isExpand = false
          node.isHidden = Boolean(!ids.includes(node.pId))
        } else {
          node.isExpand = Boolean(ids.includes(node.id))
          node.isHidden = false
        }
        this.initNode(node)
      })
      this.setCount()
    },

    // 初始化节点所需要的字段
    initNode(node) {
      node.checked = node.checked || false
      node.indeterminate = node.indeterminate || false
      node.disabled = node.disabled || false
    },

    // 设置当前选中节点
    setCurrentNode(node = {}) {
      this.currentNode = node
    },

    // 回显选中状态
    setCheckedKeys(keys = []) {
      if (!Array.isArray(keys)) {
        console.warn('The argument to function setCheckedKeys must be an array')
        return
      }
      this.clearChecked()
      const nodes = keys.map(key => this.big.listMap[key])
      nodes.forEach((node, index) => {
        if (node && node.isLeaf) {
          node.checked = true
          if (!isBrother(node, nodes[index + 1])) this.handleCheckedChange(node)
        }
      })
      this.emitChecked()
    },

    // 回显选中状态
    setCheckedNodes(nodes = []) {
      if (!Array.isArray(nodes)) {
        console.warn('The argument to function setCheckedNodes must be an array')
        return
      }
      if (nodes.length > 0) {
        const keys = nodes.map(i => i.id)
        this.setCheckedKeys(keys)
      }
    },

    // 获取选中状态
    getCheckedKeys() {
      return this.big.checkedKeys
    },

    getCheckedNodes() {
      return this.big.checkedNodes
    },

    // 点击展开与收缩
    onExpand(node) {
      node.isExpand = !node.isExpand
      this.showOrHiddenChildren(node, !node.isExpand)
    },

    // 点击 checkbox
    onChecked(node) {
      this.handleCheckedChange(node)
      this.emitChecked()
      this.$emit('check', node)
    },
    onUnCheckedById(id) {
      const node = this.big.list.find(node => node.id === id)
      if (!node) return
      node.checked = false
      this.doParentChecked(node.pId)
      this.emitChecked()
      this.$emit('uncheck', node)
    },

    // 点击 label
    onClickLabel(node) {
      this.setCurrentNode(node)
      this.$emit('click-label', node)
    },

    // 发送给父组件选中信息
    emitChecked() {
      this.big.checkedNodes = this.big.list.filter(i => i.checked || i.indeterminate) // 返回”所有“选中的节点 或者 父节点(子节点部分选中)
      this.big.checkedKeys = this.big.checkedNodes.map(i => i.id)
      this.$emit('check-change', this.big.checkedKeys, this.big.checkedNodes)
      this.setCount()
    },
    // 处理选中逻辑
    handleCheckedChange(node) {
      // 父子不互相关联
      if (this.checkStrictly) {
        node.indeterminate = node.isLeaf ? false : node.checked
        return
      }
      if (node.checked) node.indeterminate = false
      this.doChildrenChecked(node)
      this.doParentChecked(node.pId)
      this.big.disabledList.forEach((node, index) => {
        if (!isBrother(node, this.big.disabledList[index + 1])) { this.doParentChecked(node.pId) }
      })
    },

    // 1. 隐藏： 子孙后代都要隐藏， 2. 展开：仅儿子展开, value
    showOrHiddenChildren(node, isHidden) {
      if (node.isLeaf) return
      if (isHidden) {
        depthFirstEach(node.children, false, null, childNode => {
          childNode.isHidden = isHidden
          childNode.isExpand = false
        })
      } else {
        node.children.forEach(childNode => {
          childNode.isHidden = isHidden
          childNode.isExpand = false
        })
      }
      this.setCount()
    },

    // 处理子、孙后代
    doChildrenChecked(node) {
      if (!node.children) return
      const checked = node.checked
      depthFirstEach(node.children, false, null, childNode => {
        if (childNode.isLeaf && childNode.disabled) return
        childNode.indeterminate = false
        childNode.checked = checked
      })
    },

    // 处理自己及祖先
    doParentChecked(pId) {
      if (pId === null || pId === undefined) return
      const allDirectChildren = findSubTree(this.big.filterTree, pId)
      const parentNode = findNode(this.big.filterTree, pId)
      const childrenAllChecked = allDirectChildren.every(i => i.checked)
      this.checkParentIndeterminate(parentNode, allDirectChildren)
      parentNode.checked = childrenAllChecked
      if (childrenAllChecked) parentNode.indeterminate = false
      if (parentNode.pId !== null) this.doParentChecked(parentNode.pId)
    },

    // 子元素部分选中，核对祖先是否部分选中
    checkParentIndeterminate(parentNode, directChildren) {
      const hasChecked = directChildren.some(i => i.checked)
      const hasUnchecked = directChildren.some(i => !i.checked)
      const partOfChecked = hasChecked && hasUnchecked
      const childrenHasIndeterminate = directChildren.some(i => i.indeterminate)
      const isIndeterminate = partOfChecked || childrenHasIndeterminate
      parentNode.indeterminate = !!isIndeterminate
      directChildren.forEach(node => {
        if (node.checked) node.indeterminate = false
      })
    },

    // 监听滚动
    onScroll() {
      this.throttleScroll()
    },

    // 设置可见区域的区间
    setRenderRange(scrollTop = this.$refs.tree.scrollTop) {
      const count = Math.ceil(this.$el.clientHeight / this.itemHeight) + 40 // 可见项数
      const startIndex = Math.floor(scrollTop / this.itemHeight)
      this.startIndex = startIndex > 20 ? startIndex - 20 : 0
      this.endIndex = this.startIndex + count
      this.setCount()
    },

    // 筛选节点
    initFilter(op) {
      this.setFilterList(op)
      this.initExpandTrigger()
      this.setLeafCount(op)
      if (this.showCheckbox) {
        this.big.disabledList = this.big.filterList.filter(i => i.disabled)
      }
      this.setCount()
    },
    // set this.big.filterList
    setFilterList(op) {
      if (op === '__SHOW_CHECKED_ONLY__') {
        // 不直接 this.big.filterList = this.big.checkedNodes, 因为之前的 filter 将 滤掉的 非叶子节点indeterminate = true 丢失了。场景，1. 输入关键字，2. 点击showCheckedOnly
        this.big.filterList = this.big.list.filter(node => {
          const is = isCheckedOrIndeterminate(node, this.big.list)
          if (is) {
            node.checked = true
            node.indeterminate = false
          }
          return is
        })
        return
      }
      const keyword = (this.keyword || '').toLowerCase()
      if (this.isOnlyInCheckedSearch && this.big.allCheckedList.length > 0) {
        if (keyword.length === 0 && !this.$slots['search-append']) {
          this.big.filterList = this.big.allCheckedList
          return
        }
        this.big.filterList = this.big.allCheckedList.filter(node => isIncludesKeyword(node, keyword, this.searchMethod, this.big.allCheckedList))
        return
      }
      if (keyword.length === 0 && !this.$slots['search-append']) {
        this.big.filterList = this.big.list
        return
      }
      this.big.filterList = this.big.list.filter(node => isIncludesKeyword(node, keyword, this.searchMethod))
    },

    setLeafCount(op) {
      if (!this.showNodeCount) {
        return
      }
      if (op === '__INIT__' || op === '__RESTORE__') {
        this.big.filterList.forEach(node => {
          if (!node.isLeaf) {
            calcLeafCount(node, this.big.filterList)
            node.leafCountAll = node.leafCount
          }
        })
      } else {
        const isAllNodes = this.big.filterList === this.big.list
        this.big.filterList.forEach(node => {
          if (node.isLeaf) {
            return
          }
          if (isAllNodes) {
            node.leafCount = node.leafCountAll
          } else {
            calcLeafCount(node, this.big.filterList)
          }
        })
      }
    },

    // 回到顶部
    backToTop() {
      this.$nextTick(() => {
        if (this.$refs.tree) {
          this.$refs.tree.scrollTop = 0
          this.setRenderRange()
        }
      })
    },

    // 清空所有选中
    clearChecked() {
      this.big.list.forEach(node => {
        node.checked = false
        node.indeterminate = false
      })
    },

    // 根据 count 触发 computed
    setCount() {
      this.count++
    },

    // 仅展示选中的项
    showCheckedOnly(isOnlyInCheckedSearch = true) {
      this.keyword = ''
      this.init('__SHOW_CHECKED_ONLY__')
      // 开关，仅在选中节点里筛选
      this.isOnlyInCheckedSearch = isOnlyInCheckedSearch
      if (isOnlyInCheckedSearch) {
        this.big.allCheckedList = this.big.checkedNodes.slice()
      } else {
        this.big.allCheckedList = []
      }
    },

    restore() {
      this.isOnlyInCheckedSearch = false
      this.big.allCheckedList = []
      this.init('__RESTORE__')
    },
    // 手动更新选中状态
    update() {
      this.setCount()
    },

    // 清空内存占用
    clear() {
      this.count = 1
      this.keyword = '' // 关键词
      this.isSearching = false // 搜索中
      this.startIndex = 0 // 渲染的开始区间
      this.endIndex = 70 // 渲染的结束区间
      this.isOnlyInCheckedSearch = false
      clearAll(this.big.list)
      if (this.big) {
        this.big.tree = [] // 海量数据tree
        this.big.list = [] // 扁平化的tree
        this.big.listMap = {} // this.big.filterList 对应的 map
        this.big.filterTree = [] // 根据关键词过滤后的tree
        this.big.filterList = [] // 根据关键词过滤后的list
        this.big.disabledList = [] // disabled 为true组成的数组
        this.big.checkedKeys = [] // 选中的 ids
        this.big.checkedNodes = [] // 选中的 nodes
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .huge-tree-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 50px;
    border: 1px solid #aaa;
    padding: 5px 6px;
    * {
      transition: all 0.3s ease-in-out;
    }
    .search-bar {
      margin-bottom: 4px;
      .el-input {
        height: 30px;
        line-height: 30px;
        vertical-align: middle;
      }
    }
    .huge-tree {
      position: relative;
      overflow: auto;
      flex: 1;
      .huge-tree__phantom {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        z-index: -1;
      }
      .huge-tree__content {
        /* fix: div出现横向滚动条，滚动条滚动时背景色没有铺满 */
        min-width: max-content;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        .huge-tree-node-wrapper {
          display: flex;
          align-items: center;
          cursor: pointer;
          white-space: nowrap;
          &:hover {
            background-color: #f5f7fa;
          }
          &.is-active {
            background-color: #f0f7ff;
          }
          &.is-hidden {
            display: none;
          }
          .node-prefix {
            width: 24px;
            height: 26px;
            line-height: 26px;
            /* 不放大 不缩小 固定宽度24px */
            flex: 0 0 24px;
          }
          .expand-node {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            &:hover {
              color: #409eff;
              border-color: #409eff;
              background-color: #f1f5f8;
            }
            &::before {
              content: '\25BA';
              position: absolute;
              font-size: 12px;
              color: #c0c4cc;
              transform: scale(0.5, 1);
              transition: transform 0.3s ease-in-out;
            }
            &.is-expand {
              transform: rotate(90deg);
            }
          }
        }
      }
    }
    .no-data {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      text-align: center;
      p {
        margin: 0;
      }
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }
    ::-webkit-scrollbar-track-piece {
      background: #efefef;
    }
    ::-webkit-scrollbar-thumb {
      background: #aaa;
      min-width: 150px;
      border-radius: 5px;
      &:vertical, &:horizontal {
        &:hover {
          background: #666;
        }
      }
    }
  }
</style>
