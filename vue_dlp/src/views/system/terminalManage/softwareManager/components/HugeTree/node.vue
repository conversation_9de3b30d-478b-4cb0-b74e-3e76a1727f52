<template>
  <div class="huge-tree-node" @click.stop="e => $emit('on-click-node', e)">
    <el-tooltip
      v-if="showBox"
      effect="dark"
      content="至多一次选择1000条"
      placement="top-start"
      popper-class="huge-tree-tooltip"
      :disabled="node.isLeaf || node.leafCount <= checkOver"
    >
      <div
        class="huge-tree-node__checkbox"
        :class="{
          'is-checked': node.checked,
          'is-part-checked': node.indeterminate,
          'is-disabled': node.disabled,
          'check-over': !node.isLeaf && node.leafCount > checkOver
        }"
        @click.stop="(node.isLeaf || (!node.isLeaf && node.leafCount <= checkOver)) && onChecked()"
      />
    </el-tooltip>
    <div class="huge-tree-node__content" @click="onSingleChecked" @dblclick="onDBLChecked">
      <div class="huge-tree-node__icon">
        <slot name="icon" :node="node"/>
      </div>
      <div class="huge-tree-node__label">
        <slot :node="node">{{ node.label }}</slot>
      </div>
      <div v-if="!node.isLeaf && showNodeCount" class="huge-tree-node__count">
        ({{ node.leafCount }})
      </div>
    </div>
  </div>
</template>

<script>
import { depthFirstEach } from './util'

export default {
  name: 'HugeTreeNode',
  props: {
    node: {
      type: Object,
      default() {
        return {
          checked: false,
          indeterminate: false,
          disabled: false,
          isLeaf: true
        }
      }
    },
    showCheckbox: { type: Boolean, default: false },
    checkOver: { type: Number, default: 1000 },
    showCheckboxLeafOnly: { type: Boolean, default: false },
    checkedAction: { type: String, default: 'none' },
    checkStrictly: { type: Boolean, default: false },
    showNodeCount: { type: Boolean, default: false }
  },
  data() {
    return {}
  },
  computed: {
    showBox() {
      if (!this.showCheckbox || this.node.hideCheckbox) {
        return false
      }
      if (this.showCheckboxLeafOnly) {
        return this.node.isLeaf
      }
      return true
    }
  },
  methods: {
    onChecked() {
      if (this.node.disabled) return
      this.node.checked = this.getNewChecked(this.node.checked)
      this.$emit('on-checked')
    },
    labelClick() {
      this.$emit('on-click-label')
    },
    onSingleChecked() {
      if (this.checkedAction === 'click' && this.showBox) this.onChecked()
      this.labelClick()
    },
    onDBLChecked() {
      if (this.checkedAction === 'dblclick' && this.showBox) this.onChecked()
      this.labelClick()
    },
    getNewChecked(oldChecked) {
      if (this.node.isLeaf || this.checkStrictly) {
        return !oldChecked
      }
      let newChecked = false
      depthFirstEach(this.node.children, false, null, node => {
        if (node.isLeaf && !node.disabled && !node.checked) {
          newChecked = true
          return 'break'
        }
      })
      return newChecked
    }
  }
}
</script>

<style lang="scss" scoped>
  .huge-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    line-height: 26px;
    .huge-tree-node__checkbox {
      position: relative;
      flex-shrink: 0;
      width: 14px;
      height: 14px;
      margin-right: 8px;
      border-radius: 2px;
      box-sizing: border-box;
      border: 1px solid #dcdfe6;
      cursor: pointer;
      &:hover {
        border-color: #409eff;
      }
      &::after {
        content: '';
        position: absolute;
        box-sizing: content-box;
        border: 1px solid #fff;
        border-left: 0;
        border-top: 0;
        height: 7px;
        left: 5px;
        top: 2px;
        transform: rotate(45deg) scaleY(0);
        width: 3px;
        transition: transform 0.15s ease-in 0.05s;
        transform-origin: center;
      }
      // 子元素部分选中
      &.is-part-checked::before {
        content: '';
        position: absolute;
        display: block;
        background-color: #fff;
        height: 2px;
        transform: scale(0.5);
        left: 0;
        right: 0;
        top: 6px;
      }
      &.is-checked {
        &::after {
          transform: rotate(45deg) scaleY(1);
        }
      }
      &.is-checked,
      &.is-part-checked {
        color: #fff;
        background-color: #409eff;
        border-color: #409eff;
        transition: all 0.25s;
        &.is-disabled {
          background: #95c9ff;
          color: #c0c4cc;
          border-color: #c0c4cc;
          cursor: not-allowed;
        }
      }
      &.is-disabled {
        background: #edf2fc;
        color: #c0c4cc;
        border-color: #c0c4cc;
        cursor: not-allowed;
      }
      &.check-over {
        cursor: not-allowed;
      }
    }
    .huge-tree-node__content {
      cursor: pointer;
      color: #606266;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      .huge-tree-node__count {
        color: #c0c4cc;
        font-size: 12px;
        font-style: italic;
      }
    }
  }
</style>
<style lang="scss">
  .huge-tree-tooltip {
    &.el-tooltip__popper {
      border-radius: 0.25rem;
      padding: 0.625rem;
      font-size: 0.75rem;
    }
  }
</style>
