/**
 * 当前节点及其子节点是否包含关键字
 * @param node {Object} 当前节点
 * @param keyword {string} 关键字
 * @param searchMethod {function} 搜索方法
 * @param list {Array?}
 * @returns {boolean}
 */
export function isIncludesKeyword(node, keyword, searchMethod, list) {
  const isInclude = searchMethod(keyword, node)
  if (isInclude) {
    // 自己匹配上了
    return true
  }
  if (node.isLeaf) {
    return false
  }
  const directChildren = list ? list.filter(item => item.pId === node.id) : node.children
  return directChildren.some(item => isIncludesKeyword(item, keyword, searchMethod, list))
}

/**
 * 自己 || 子 || 孙 是否选中
 * @param {Object} node 节点
 * @param {Array} list
 */
export function isCheckedOrIndeterminate(node, list) {
  const is = node.checked || node.indeterminate
  if (is) {
    // 自己匹配上了
    return true
  }
  if (!node.isLeaf) {
    const allDirectChildren = list.filter(i => i.pId === node.id)
    return allDirectChildren.some(i => isCheckedOrIndeterminate(i, list))
  }
  return false
}

/**
 * 获取后代 叶子节点的数量
 * @param {Object} node
 * @param {Array} tree
 * @returns {Number}
 */
export function getLeafCount(tree, node) {
  const subTree = findSubTree(tree, node.id)
  let count = 0
  depthFirstEach(subTree, false, null, node => {
    if (node.isLeaf) {
      count++
    }
  })
  return count
}

/**
 * 计算节点的叶子子节点总数
 * @param node
 * @param list
 */
export function calcLeafCount(node, list) {
  let count = 0
  list.forEach(item => {
    if (item.isLeaf && item.path.includes(node.id)) {
      count++
    }
  })
  node.leafCount = count
}

/**
 * 深度优先遍历算法, 遍历tree的每一项
 * @param tree {Array} 树数据
 * @param init {boolean} 是否是初始化
 * @param path {Array} 节点ID路径数组，init 为 true 时有用
 * @param cb {function} 回调函数，Function(node)
 */
export function depthFirstEach(tree, init = false, path, cb) {
  if (!Array.isArray(tree)) {
    console.warn('The tree in the first argument to function depthFirstEach must be an array')
    return
  }
  if (!tree || tree.length === 0) return
  for (const node of tree) {
    const hasChildren = node.children && node.children.length > 0
    if (init) {
      node.path = [...(path || []), node.id]
      node.isLeaf = !hasChildren
    }
    if (cb) {
      const res = cb(node)
      if (res === 'break') return
    }
    if (hasChildren) {
      depthFirstEach(node.children, init, node.path, cb)
    }
  }
}

export function listToTree(filterList) {
  if (!Array.isArray(filterList)) {
    console.warn('The parameter filterList to function listToTree must be an array')
    return
  }
  if (!filterList || filterList.length === 0) return []
  const root = {}

  // 定义查找父节点的函数，根据 path
  const parentNode = (root, path) => {
    const _path = path.slice()
    const rootId = _path.shift()
    if (_path.length > 1) {
      return parentNode(root[rootId].childrenMap, _path)
    }
    if (_path.length === 1) {
      return root[rootId].childrenMap
    }
    return root
  }

  // 设置filter后的 children
  const setChildren = root => {
    const nodes = Object.values(root)
    for (const node of nodes) {
      node.children = Object.values(node.childrenMap)
      if (node.children && node.children.length > 0) {
        setChildren(node.childrenMap)
      }
    }
  }

  filterList.forEach(node => {
    node.childrenMap = {}
    parentNode(root, node.path)[node.id] = node
  })
  setChildren(root)

  return Object.values(root)
}

/**
 * 广度优先遍历算法，
 * @param {Object} {tree, limitDeep: 限制遍历的深度， deep: 当前深度}
 * @param {Callback} cb
 */
export function breadthFirstEach({ tree, limitDeep = Number.MAX_SAFE_INTEGER, deep = 0 }, cb) {
  if (!Array.isArray(tree)) {
    console.warn('The tree in the first argument to function breadthFirstEach must be an array')
    return
  }
  if (!tree || tree.length === 0) return
  tree.forEach(node => {
    if (cb) cb(node)
  })
  const childrenList = tree
    .filter(node => node.children)
    .map(i => i.children)
    .flat(1)
  breadthFirstEach({ tree: childrenList, limitDeep, deep: deep++ }, cb)
}

/**
 * 广度优先遍历算法，找子树, 不包含自己
 * @param {Array} tree 原始树，数组
 * @param {Number|String} rootId 子树根结点
 * @return {Object} 子树
 */
export function findSubTree(tree, rootId) {
  if (!Array.isArray(tree)) {
    console.warn('The parameter tree to function breadthFirstEach must be an array')
    return
  }
  if (!tree || tree.length === 0) return []
  const item = tree.find(node => node.id === rootId)
  if (item) return item.children || []
  const childrenList = tree
    .filter(node => node.children)
    .map(i => i.children)
    .flat(1)
  return findSubTree(childrenList, rootId)
}

/**
 * 广度优先遍历算法，节点自己
 * @param {Array} tree 原始树，数组
 * @param {Number|String} rootId 自身id
 * @return {Object} node
 */
export function findNode(tree, rootId) {
  if (!Array.isArray(tree)) {
    console.warn('The parameter tree to function findNode must be an array')
    return
  }
  if (!tree || tree.length === 0) return {}
  const item = tree.find(node => node.id === rootId)
  if (item) return item
  const childrenList = tree
    .filter(node => node.children)
    .map(i => i.children)
    .flat(1)
  return findNode(childrenList, rootId)
}

/**
 * 判断节点是否是亲兄弟
 * @param {Object} node1
 * @param {Object} node2
 */
export function isBrother(node1, node2) {
  if (!node1 || !node2) return false

  const p1 = String(node1.path.slice(0, -1))
  const p2 = String(node2.path.slice(0, -1))

  return p1 === p2
}

export const deepCopy = function(obj, cache = []) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  // 避免循环引用导致爆栈
  const hit = cache.find(i => i.original === obj)
  if (hit) {
    return hit.copy
  }

  const copy = Array.isArray(obj) ? [] : {}

  cache.push({
    original: obj,
    copy
  })

  Object.keys(obj).forEach(key => {
    copy[key] = deepCopy(obj[key], cache)
  })

  return copy
}

// 基本数据类型置 null, 清空内存占用
export const clearAll = function(obj) {
  if (typeof obj === 'function' || obj === null || typeof obj !== 'object') {
    obj = null
    return
  }

  Object.keys(obj).forEach(key => {
    clearAll(obj[key])
    obj[key] = null
  })
}

// ---------------------- underscore.js utils ----------------------

// Some functions take a variable number of arguments, or a few expected
// arguments at the beginning and then a variable number of values to operate
// on. This helper accumulates all remaining arguments past the function’s
// argument length (or an explicit `startIndex`), into an array that becomes
// the last argument. Similar to ES6’s "rest parameter".
export function restArguments(func, startIndex) {
  startIndex = startIndex == null ? func.length - 1 : +startIndex;
  return function() {
    const length = Math.max(arguments.length - startIndex, 0);
    const rest = Array(length);
    let index = 0;
    for (; index < length; index++) {
      rest[index] = arguments[index + startIndex];
    }
    switch (startIndex) {
      case 0:
        return func.call(this, rest);
      case 1:
        return func.call(this, arguments[0], rest);
      case 2:
        return func.call(this, arguments[0], arguments[1], rest);
    }
    const args = Array(startIndex + 1);
    for (index = 0; index < startIndex; index++) {
      args[index] = arguments[index];
    }
    args[startIndex] = rest;
    return func.apply(this, args);
  };
}

// A (possibly faster) way to get the current timestamp as an integer.
export const now = Date.now || function() {
  return new Date().getTime();
};

// Returns a function, that, when invoked, will only be triggered at most once
// during a given window of time. Normally, the throttled function will run
// as much as it can, without ever going more than once per `wait` duration;
// but if you'd like to disable the execution on the leading edge, pass
// `{leading: false}`. To disable execution on the trailing edge, ditto.
export function throttle(func, wait, options) {
  let timeout, context, args, result;
  let previous = 0;
  if (!options) options = {};

  const later = function() {
    previous = options.leading === false ? 0 : now();
    timeout = null;
    result = func.apply(context, args);
    if (!timeout) context = args = null;
  };

  const throttled = function() {
    const _now = now();
    if (!previous && options.leading === false) previous = _now;
    const remaining = wait - (_now - previous);
    context = this;
    args = arguments;
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = _now;
      result = func.apply(context, args);
      if (!timeout) context = args = null;
    } else if (!timeout && options.trailing !== false) {
      timeout = setTimeout(later, remaining);
    }
    return result;
  };

  throttled.cancel = function() {
    clearTimeout(timeout);
    previous = 0;
    timeout = context = args = null;
  };

  return throttled;
}

// When a sequence of calls of the returned function ends, the argument
// function is triggered. The end of a sequence is defined by the `wait`
// parameter. If `immediate` is passed, the argument function will be
// triggered at the beginning of the sequence instead of at the end.
export function debounce(func, wait, immediate) {
  let timeout, previous, args, result, context;

  const later = function() {
    const passed = now() - previous;
    if (wait > passed) {
      timeout = setTimeout(later, wait - passed);
    } else {
      timeout = null;
      if (!immediate) result = func.apply(context, args);
      // This check is needed because `func` can recursively invoke `debounced`.
      if (!timeout) args = context = null;
    }
  };

  const debounced = restArguments(function(_args) {
    context = this;
    args = _args;
    previous = now();
    if (!timeout) {
      timeout = setTimeout(later, wait);
      if (immediate) result = func.apply(context, args);
    }
    return result;
  });

  debounced.cancel = function() {
    clearTimeout(timeout);
    timeout = args = context = null;
  };

  return debounced;
}
