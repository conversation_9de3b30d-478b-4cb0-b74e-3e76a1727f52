<template>
  <div class="text-center-ellipsis">
    <div class="text-center-ellipsis-wrapper" :title="text">
      <div class="text-center-ellipsis-part text-center-ellipsis-left">{{ ellipsis.left }}</div>
      <div class="text-center-ellipsis-part text-center-ellipsis-right">
        <div class="text-center-ellipsis-right__inner">{{ ellipsis.right }}</div>
      </div>
    </div>
  </div>
</template>

<script>
const DIRECTIONAL_CHARS = {
  '(': ')',
  ')': '(',
  '[': ']',
  ']': '[',
  '<': '>',
  '>': '<',
  '{': '}',
  '}': '{',
  '（': '）',
  '）': '（',
  '【': '】',
  '】': '【',
  '《': '》',
  '》': '《'
}

export default {
  name: 'TextCenterEllipsis',
  props: {
    text: {
      type: String,
      default: undefined
    }
  },
  computed: {
    ellipsis() {
      if (!this.text) {
        return { left: undefined, right: undefined }
      }
      const half = Math.floor(this.text.length / 2)
      const rightPart = this.text.slice(half)
      const reverseChars = []
      let char
      for (let i = rightPart.length - 1; i >= 0; i--) {
        char = rightPart.charAt(i)
        reverseChars.push(DIRECTIONAL_CHARS[char] || char)
      }
      return { left: this.text.slice(0, half), right: reverseChars.join('') }
    }
  }
}
</script>

<style lang="scss" scoped>
  .text-center-ellipsis {
    display: inline-block;
    vertical-align: bottom;
    .text-center-ellipsis-wrapper {
      display: flex;
      flex: 1;
      text-align: justify;
      .text-center-ellipsis-part {
        overflow: hidden;
      }
      .text-center-ellipsis-left {
        white-space: pre;
        text-overflow: ellipsis;
      }
      .text-center-ellipsis-right {
        display: flex;
        height: 30px;
        white-space: pre-wrap;
        direction: rtl;
        .text-center-ellipsis-right__inner {
          flex: 1;
          display: inline;
          unicode-bidi: bidi-override;
        }
      }
    }
  }
</style>
