<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.auditType" :value="query.auditType">
          <span>{{ $t('table.operateType') }}：</span>
          <el-select v-model="query.auditType" style="width: 150px">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option v-for="item in auditTypes" v-show="!item.hide" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.softwareName" :value="query.softwareName">
          <span>{{ $t('table.softwareName') }}：</span>
          <el-input v-model="query.softwareName" clearable style="width: 150px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'411'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'424'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('424')"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :custom-col="true"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.softwareAuditLogDetail')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateType')">
            {{ auditTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.softwareName')">
            {{ rowDetail.softwareName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.softwareVersion')">
            {{ rowDetail.softwareVersion }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.opResult')">
            {{ rowDetail.opResult }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/system/terminalManage/softwareAuditLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'SoftwareAuditLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'auditType', label: 'operateType', width: '100', formatter: this.auditTypeFormatter },
        { prop: 'softwareName', label: 'softwareName', width: '150' },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '120' },
        { prop: 'opResult', label: 'opResult', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '140', fixed: 'right', hidden: !this.hasPermission('410'),
          buttons: [
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('410') }
          ]
        }
      ],
      auditTypes: [
        { label: this.$t('table.download'), value: 1 },
        { label: this.$t('table.uninstall'), value: 2 },
        { label: this.$t('table.upgrade'), value: 3 },
        { label: this.$t('table.install'), value: 4 },
        { label: this.$t('table.clearInstallPkg'), value: 5, hide: true }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        auditType: null,
        softwareName: '',
        deleteRecords: [],
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      queryVideoMethod: undefined,
      sortable: true
    }
  },
  computed: {
    auditTypesLabelMap() {
      return this.auditTypes.reduce((map, item) => {
        map[item.value] = item.label
        return map
      }, {})
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    auditTypeFormatter(row) {
      return this.auditTypesLabelMap[row.auditType]
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '410', this.query.searchReport)
    }
  }
}
</script>

<style lang="scss" scoped>
  .show-detail-panel {
    >>>.el-descriptions__body {
      th {
        background-color: #ddd;
      }
      td {
        background-color: #e4e7e9;
      }
      .el-descriptions__table {
        .el-descriptions-item__cell{
          border-color: #ababab;
        }
      }
      .el-descriptions-item__label {
        width: 120px;
        font-weight: bold;
        color: #606266;
      }
      .el-descriptions-item__content {
        width: 230px;
      }
    }
  }
</style>
