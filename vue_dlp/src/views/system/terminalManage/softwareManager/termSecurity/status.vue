<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="1" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <span>
          <label>{{ $t('pages.detectionResult') }}：</label>
          <el-select v-model="query.pass" clearable is-filter :placeholder="$t('pages.all')" style="width: 180px;">
            <el-option :label="$t('pages.termSecurityPass')" :value="1"></el-option>
            <el-option :label="$t('pages.termSecurityNotPass')" :value="0"></el-option>
          </el-select>
        </span>
        <span>
          <label>{{ $t('table.riskLevel') }}：</label>
          <el-select v-model="query.risk" clearable is-filter :placeholder="$t('pages.all')" style="width: 180px;">
            <el-option :label="$t('text.health')" :value="0"></el-option>
            <el-option :label="$t('text.lowRisk')" :value="1"></el-option>
            <el-option :label="$t('text.mediumRisk')" :value="2"></el-option>
            <el-option :label="$t('text.highRisk')" :value="3"></el-option>
          </el-select>
        </span>
        <el-input v-if="!isSearchTerminal" v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.terminalCode')" style="width: 200px;" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <el-button type="primary" size="mini" @click="resultClick">
          {{ $t('pages.statistical') }}
        </el-button>
        <audit-log-exporter v-if="false" :request="exportFunc"/>
      </div>
      <grid-table
        ref="logList"
        key="id"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <log-details ref="logDetails"></log-details>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.termSecurityResultStatistics')"
      width="900px"
      :visible.sync="resultFormVisible"
      @close="closeResult"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" title="" border :column="2">
          <el-descriptions-item :label="$t('pages.termTotalNumber')">{{ result.termTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.detectionTermNumber')">{{ result.detectionTermTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.passTermNumber')">{{ result.passTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.passTermNumberPercentage')">{{ result.passTotalPercentage }}%</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.notPassTermNumber')">{{ result.notPassTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.notPassTermNumberPercentage')">{{ result.notPassTotalPercentage }}%</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.notDetectionTermNumber')">{{ result.notDetectionTermTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.notDetectionTermNumberPercentage')">{{ result.notDetectionTermTotalPercentage }}%</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.healthTermNumber')">{{ result.healthTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.healthTermNumberPercentage')">{{ result.healthTotalPercentage }}%</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.lowRiskTermNumber')">{{ result.lowRiskTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.lowRiskTermNumberPercentage')">{{ result.lowRiskTotalPercentage }}%</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.mediumRiskTermNumber')">{{ result.mediumRiskTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.mediumRiskTermNumberPercentage')">{{ result.mediumRiskTotalPercentage }}%</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.highRiskTermNumber')">{{ result.highRiskTotal }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.highRiskTermNumberPercentage')">{{ result.highRiskTotalPercentage }}%</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deleteLog, exportTermAccessLog } from '@/api/system/terminalManage/accessLog'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { getModuleInfoByProdId } from '@/api/system/terminalManage/moduleConfig'
import { listTerminal } from '@/api/system/terminalManage/terminal'
import { getLastRecordPage, getStatisticalResult } from '@/api/system/terminalManage/termSecurityLog';
import LogDetails from './logDetails'

export default {
  name: 'TermSecurityStatus',
  components: { LogDetails },
  data() {
    return {
      colModel: [
        { prop: 'terminalName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termId', label: 'terminalCode', width: '100' },
        { prop: 'result', label: 'detectionResult', width: '150', formatter: (row, data) => { return row.risk === 0 ? this.$t('pages.termSecurityPass') : this.$t('pages.termSecurityNotPass') }, colorFormatter: this.resultColorFormatter },
        { prop: 'risk', label: 'riskLevel', width: '150', type: 'text', formatter: this.riskFormatter, colorFormatter: this.riskColorFormatter },
        { prop: 'score', label: 'score', width: '150' },
        { prop: 'lastDate', label: 'lastDetectionTime', width: '150', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('509') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '', //  终端编号或名称
        createDate: '',
        startDate: '',
        endDate: '',
        risk: null,   //  风险等级
        pass: null,   //  通过状态
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      deleteable: false,
      selectedData: [],
      groupTreeSelectData: [],
      rowDetail: {},
      moduleNameMap: {},
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      resultFormVisible: false,
      //  统计结果
      result: {
      },
      defaultResult: {
        termTotal: 0,
        detectionTermTotal: 0,
        passTotal: 0,
        passTotalPercentage: 0,
        notPassTotal: 0,
        notPassTotalPercentage: 0,
        notDetectionTermTotal: 0,
        notDetectionTermTotalPercentage: 0,
        healthTotal: 0,
        healthTotalPercentage: 0,
        lowRiskTotal: 0,
        lowRiskTotalPercentage: 0,
        mediumRiskTotal: 0,
        mediumRiskTotalPercentage: 0,
        highRiskTotal: 0,
        highRiskTotalPercentage: 0
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    },
    //  是否查询终端
    isSearchTerminal() {
      return this.query.objectType && this.query.objectType == '1';
    }
  },
  created() {
    this.listModule()
  },
  methods: {
    async listModule() {
      const productId = 511
      await getModuleInfoByProdId(productId).then(respond => {
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(moduleInfo => {
            if (moduleInfo.osGroup == 'win') {
              this.moduleNameMap[moduleInfo.moduleId] = moduleInfo.moduleName
            }
          })
        }
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    submitEnd() {
      this.gridTable.execRowDataApi()
    },
    initGroupTreeNode() {
      getDeptTreeFromCache().then(respond => {
        this.groupTreeSelectData = respond.data[0].children
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selectedData = []
      this.deleteable = rowDatas && rowDatas.length > 0
      rowDatas.forEach(data => {
        const rowBean = {
          id: data.termId
        }
        this.selectedData.push(rowBean)
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      if (this.isSearchTerminal) {
        //  选中终端时，不支持模块编号模糊查询
        searchQuery.searchInfo = null
      }
      return getLastRecordPage(searchQuery);
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    exportFunc(exportType) {
      return exportTermAccessLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.$refs.logDetails.showStatus(row.id, row.termId, row.lastDate)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    afterLoad(rowData) {
      this.termsInfo = []
      const termNameMap = new Map()
      const termIds = rowData.map(data => {
        termNameMap.set(data.termId, data.termName)
        return data.termId
      }).join()

      if (termIds) {
        listTerminal({ ids: termIds }).then(res => {
          if (res.data) {
            res.data.forEach(item => {
              const { id, groupIds } = item
              const name = termNameMap.get(id)
              const termsInfo = { id, name, groupIds }
              this.termsInfo.push(termsInfo)
            })
          }
        })
      }
      rowData = [{ id: 1 }]
      return rowData;
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    riskFormatter(row, data) {
      let result = ''
      if (row.risk !== undefined && row.risk !== null) {
        switch (row.risk) {
          case 0: result = this.$t('text.health'); break;     //  健康
          case 1: result = this.$t('text.lowRisk'); break;     //  低风险
          case 2: result = this.$t('text.mediumRisk'); break;     //  中风险
          case 3: result = this.$t('text.highRisk'); break;     //  高风险
        }
      }
      return result;
    },
    resultColorFormatter(row, data) {
      if (row.risk !== undefined && row.risk !== null) {
        return row.risk === 0 ? '#4CAF50' : '#F44336';
      }
      return null;
    },
    riskColorFormatter(row, data) {
      let result = ''
      if (row.risk !== undefined && row.risk !== null) {
        switch (row.risk) {
          case 0: result = '#2196F3'; break;
          case 1: result = '#4CAF50'; break;
          case 2: result = '#F88E03'; break;
          case 3: result = '#F44336'; break;
        }
      }
      return result;
    },
    closeResult() {
      this.resultFormVisible = false
    },
    resultClick() {
      getStatisticalResult().then(res => {
        this.result = Object.assign({}, this.defaultResult, res.data);
      })
      this.resultFormVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 180px;
}
</style>
