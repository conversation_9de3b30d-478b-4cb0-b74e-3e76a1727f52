<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
    >
      <Form ref="dataForm" style="margin: 10px" :model="temp" :rules="rules" label-width="100px">
        <FormItem :label="$t('pages.softwareName')" prop="softName">
          <template slot="label">
            {{ $t('pages.softwareName') }}
            <el-tooltip placement="top" effect="dark">
              <div slot="content" style="line-height: 20px;">
                {{ $t('pages.termSecuritySoftwareNameSourceMsg1') }}<br>
                {{ $t('pages.termSecuritySoftwareNameSourceMsg2') }}<br>
                {{ $t('pages.termSecuritySoftwareNameSourceMsg3') }}<br>
                {{ $t('pages.termSecuritySoftwareNameSourceMsg4') }}<br>
                {{ $t('pages.termSecuritySoftwareNameSourceMsg5') }}
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </template>
          <el-input v-model="temp.softName" v-trim maxlength="100" show-word-limit></el-input>
        </FormItem>
        <el-row :style="{ 'margin-left': enLanguage ? '140px' : '100px' }">
          <el-popover
            v-model="versionVisible"
            :disabled="!formable"
            placement="right"
            width="400"
            trigger="click"
          >
            <tree-menu ref="softwareAssetVersionList" style="height: 350px" :data="softwareAssetVersionList" :is-filter="false" :default-expand-all="true"/>
            <div style="text-align: right; margin-top: 10px">
              <el-button v-show="softwareAssetVersionList.length > 0" type="primary" size="mini" @click="handleCheckedVersion">{{ $t('button.confirm') }}</el-button>
              <el-button size="mini" @click="versionCancel">{{ $t('button.cancel') }}</el-button>
            </div>
            <el-button slot="reference" style="padding: 7px" type="primary" size="small" :disabled="!temp.softName" @click="scanSoftwareVersions">
              {{ $t('pages.software_Msg38') }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.softwareNameGetInstalledVersionNumber') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-button>
          </el-popover>
          <el-button type="primary" size="mini" @click="allVersion">{{ $t('pages.software_Msg39') }}</el-button>
        </el-row>
        <FormItem :label="$t('table.versionNumber')" prop="softVersion">
          <el-input v-model="temp.softVersion" v-trim :placeholder="$t('pages.termSecurityAllVersion')" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="type && type === 1 && existSoftwareAuth" :label="$t('pages.repairInstallPackage')">
          <el-button v-if="temp.softwareId === null" type="primary" size="mini" @click="selectFile">{{ $t('button.import') }}</el-button>
          <el-tooltip v-if="temp.softVersion !== null && !!selectedFixFile.name" placement="top" effect="dark">
            <div slot="content">{{ selectedFixFile.name }}</div>
            <span style="margin-left: 5px">
              {{ selectedFixFile.name.length > 15 ? (selectedFixFile.name.substr(0, 15) + '...') : selectedFixFile.name }}
              <el-button style="color: #68a8d0; margin-bottom: 0" type="text" icon="el-icon-close" @click="selectedFileClose"/>
            </span>
          </el-tooltip>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <software-repository-select ref="softwareSelect" :add-app-type-able="false" :single-import="true" @select="handleImportSelectedSoftware"/>
  </div>
</template>

<script>
import SoftwareRepositorySelect from '@/views/system/terminalManage/softwareManager/repository/select';
import { listNamesByIds } from '@/api/system/terminalManage/softwareRepository';
import { listVersion } from '@/api/softwareManage/assets/assetsView';

export default {
  name: 'InstallDlg',
  components: { SoftwareRepositorySelect },
  props: {
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      submitting: false,
      visible: false,
      temp: {},
      defaultTemp: {
        id: null,
        softName: '',  //  软件名称
        softVersion: '',  //  软件版本号
        softwareId: null  //  修复安装包Id
      },
      rules: {
        softName: [{ validator: this.softNameValidator, trigger: 'blur' }],
        softVersion: [{ validator: this.softVersionValidator, trigger: 'blur' }]
      },
      selectedFixFile: {}, //  选中的修复文件
      versionVisible: false,
      type: 1, //  1-必须安装，2-禁止安装
      softwareAssetVersionList: [],  //  软件版本号
      operatorType: null,  //  operatorType 操作类型， 1-创建，2-修改
      listTemp: { mustList: [], stopList: [] }, // mustList: 必须安装列表数据， stopList： 禁止安装软件列表数据
      oldSoftName: '', //  保存原来的软件名称
      oldSoftVersion: '' //  保存原来的软件版本号
    }
  },
  computed: {
    title() {
      return this.operatorType === 2 ? this.$t('pages.editSoftware') : this.$t('pages.addSoftware')
    },
    enLanguage() {
      const language = this.$store.getters.language || 'zh';
      return language === 'en'
    },
    //  是否存在软件管家的销售模块
    existSoftwareAuth() {
      return [20, 120, 220].filter(code => (this.$store.getters.saleModuleIds || []).includes(code)).length > 0
    }
  },
  methods: {
    initData() {
      this.selectedFixFile = {}
      this.oldSoftName = ''
      this.oldSoftVersion = ''
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    //  type=1， 表示必须安装软件；type=2
    async show(data, type, operatorType, listTemp) {
      this.type = type
      this.operatorType = operatorType
      this.listTemp = Object.assign(this.listTemp, listTemp)
      this.initData();
      if (data !== undefined && data !== null) {
        this.temp = Object.assign(this.temp, data)
        this.oldSoftName = this.temp.softName
        this.oldSoftVersion = this.temp.softVersion
        if (this.temp.softwareId) {
          const res = await listNamesByIds([this.temp.softwareId]);
          const idNameMap = res.data || {}
          if (idNameMap && idNameMap[this.temp.softwareId]) {
            this.selectedFixFile = { id: this.temp.softwareId, name: idNameMap[this.temp.softwareId] }
          } else {
            this.temp.softwareId = null
          }
        }
      }
      this.visible = true
    },
    confirm() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          if (!this.temp.id) {
            this.temp.id = Date.now()
          }
          this.visible = false
          this.$emit('submit', JSON.parse(JSON.stringify(this.temp)), this.type)
          this.$refs.dataForm.clearValidate()
        }
      })
    },
    cancel() {
      this.$refs.dataForm.clearValidate()
      this.visible = false
    },
    selectFile() {
      this.$refs.softwareSelect.show()
    },
    //  清除选中的文件
    selectedFileClose() {
      this.temp.softwareId = null
      this.selectedFixFile = {}
    },
    handleImportSelectedSoftware(data, type) {
      //  type: 0:软件，1：分类   data的类型： Array
      if (type === 0) {
        if (data.length > 0) {
          if (this.temp.softName === undefined || this.temp.softName === null || this.temp.softName === '') {
            this.temp.softName = data[0].name
          }
          if (this.temp.softVersion === undefined || this.temp.softVersion === null || this.temp.softVersion === '') {
            this.temp.softVersion = data[0].version
          }
          this.temp.softwareId = data[0].id
          this.selectedFixFile = { id: data[0].id, name: data[0].name }
          this.$refs.dataForm && this.$refs.dataForm.validateField(['softName', 'softVersion'])
        }
      }
    },
    softNameValidator(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.softwareNameNotNull') + ''))
      } else if (this.operatorType === 2 && this.oldSoftName === value) {
        callback();
      }
      if (this.validNameListData(value, this.type, this.listTemp.mustList || [], this.listTemp.stopList || [], callback)) {
        this.$refs.dataForm.validateField('softVersion')
      }
      callback();
    },
    //  判断软件名称是否在必须安装检查列表和禁止安装检查列表中
    validNameListData(value, type, mustList, stopList, callback) {
      if (value !== undefined && value !== null && value.length > 0) {
        const limitList = type === 1 ? stopList : mustList
        if (limitList && limitList.findIndex(item => item.softName === value) > -1) {
          const errorMsg = type === 1 ? this.$t('pages.termSecurityInstallMsg1') : this.$t('pages.termSecurityInstallMsg2')
          callback(new Error(errorMsg + ''))
          return false;
        }
      }
      return true;
    },
    //  判断软件名称+版本号是否在必须安装检查列表和禁止安装检查列表中
    validNameVersionListData(value, type, mustList, stopList, callback) {
      if (value !== undefined && value !== null && value.length > 0) {
        const list = type === 1 ? mustList : stopList
        //  校验名称+版本号不能重复
        if (list && list.findIndex(item => item.softName === this.temp.softName && item.softVersion === value) > -1) {
          callback(new Error(this.$t('pages.softwareNameAndVersionExits') + ''))
        }
      }
    },
    softVersionValidator(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.softwareVersionNotNull') + ''))
      } else if (this.operatorType === 2 && this.oldSoftName === this.temp.softName && this.oldSoftVersion === value) {
        callback();
      }
      this.validNameVersionListData(value, this.type, this.listTemp.mustList || [], this.listTemp.stopList || [], callback);
      callback();
    },
    handleCheckedVersion() {
      const checkNode = this.$refs.softwareAssetVersionList.getCurrentNode() || null;
      if (checkNode) {
        this.temp.softVersion = checkNode.label
        this.$refs.dataForm.validateField('softVersion')
      } else {
        this.$message({
          message: this.$t('pages.placeChooseVersion'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$refs.softwareAssetVersionList.clearSelectedNode();
      this.versionVisible = false
    },
    versionCancel() {
      this.$refs.softwareAssetVersionList.clearSelectedNode();
      this.versionVisible = false
    },
    //  扫描软件的版本号
    scanSoftwareVersions() {
      this.softwareAssetVersionList.splice(0)
      listVersion({ softName: this.temp.softName }).then(res => {
        if (res.data && res.data.length > 0) {
          res.data.forEach((item, index) => {
            if (item) {
              const verNode = {
                id: index + 1,
                label: item
              }
              this.softwareAssetVersionList.push(verNode)
            }
          })
        }
      })
    },
    //  全版本
    allVersion() {
      this.temp.softVersion = '*.*'
      this.$refs.dataForm.validateField('softVersion')
    }
  }
}
</script>

<style scoped>

</style>
