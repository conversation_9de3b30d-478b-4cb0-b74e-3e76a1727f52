<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-width="80px" style="margin: 10px">
        <FormItem :label="$t('pages.fileName')">
          <span>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltipStyle">
                {{ $t('pages.termSecurityFileMsg2') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </span>
          <el-upload
            ref="upload"
            name="uploadFile"
            action="aaaaaa"
            :disabled="fileSubmitting"
            :show-file-list="false"
            :before-upload="beforeUpload"
            style="display: inline-block;"
          >
            <el-button type="primary" :loading="fileSubmitting" size="mini" style="margin-top: 0;">{{ $t('pages.uploadFile') }}</el-button>
          </el-upload>
          <el-button v-if="false" type="primary" size="mini" style="margin: 0" @click="fileImport">{{ $t('pages.importLibrary') }}</el-button>
        </FormItem>
        <FormItem v-if="fileSubmitting">
          <el-row >
            <el-col :span="22">
              <el-progress type="line" :percentage="percentage"/>
            </el-col>
            <el-col :span="2">
              <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="loadFileCancel"></el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem prop="fileName">
          <el-input v-model="temp.fileName" v-trim maxlength="255" show-word-limit @change="fileNameChange"></el-input>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="temp.scanType" :disabled="!procNameImport" :true-label="1" :false-label="0" style="height: 30px">
            {{ $t('pages.termSecurityFileMsg') }}
          </el-checkbox>
        </FormItem>
        <FormItem :label="$t('pages.termSecurityFilePath')">
          <span>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltipStyle">
                {{ $t('pages.fileDetectionFilePathTip1') }}<br>
                <span v-for="item in filePathShortcutKeys" :key="item.path">
                  {{ item.label }}：{{ item.path }}{{ item.remark }}<br>
                </span>
                {{ $t('pages.fileDetectionFilePathTip2') }}<br>
                {{ $t('pages.fileDetectionFilePathTip3') }}<br>
                {{ $t('pages.fileDetectionFilePathTip4') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </span>
          <el-button v-for="item in filePathShortcutKeys" :key="item.path" size="mini" @click="filePathShortcutKey(item.path)">{{ item.label }}</el-button>
        </FormItem>
        <FormItem prop="filePath">
          <el-input v-model="temp.filePath" :placeholder="$t('pages.termSecurityFileMsg1')" clearable maxlength="500" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('table.operate')">
          <el-select v-model="temp.fileExist">
            <div v-for="item in operators" :key="item.id">
              <el-option :key="item.id" :value="item.id" :label="item.label"/>
            </div>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <app-select-dlg ref="appSelectDlg" :os-type="osType" :append-to-body="appendToBody" :single-import="true" :multiple-group="false" @select="appendFile"/>
  </div>
</template>

<script>
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg';
import { upload } from '@/api/behaviorManage/application/appGroup';
import axios from 'axios';
export default {
  name: 'FileDlg',
  components: { AppSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      operators: [
        { id: 0, label: this.$t('pages.notExist') },    //  不存在
        { id: 1, label: this.$t('pages.exist') }    //  存在
      ],
      //  文件路径快捷按钮
      filePathShortcutKeys: [
        { label: this.$t('pages.desktop'), path: '#DESKTOP#', remark: '' },       //  桌面
        { label: this.$t('pages.userTempDirectory'), path: '#USERTEMP#', remark: this.$t('pages.userTempDirectoryRemark') },       //  用户临时目录
        { label: this.$t('pages.sysTempTempDirectory'), path: '#SYSTEMTEMP#', remark: this.$t('pages.sysTempTempDirectoryRemark') }       //  系统临时目录
      ],
      fileLimitSize: 1024,
      submitting: false,
      visible: false,
      oldFileName: '',  //  保存上次上传的文件名称
      oldFileMd5: '', //  保存上次上传的文件Md5
      temp: {},
      defaultTemp: {
        id: null,
        fileName: '',  //  文件名称
        filePath: '',  // 文件路径
        fileExist: 0, //  文件是否存在  0：不存在，1：存在
        fileMd5: '',  //  快速Md5
        scanType: 0 //  扫描方式： 0：进程名 1：快速md5（程序指纹）
      },
      rules: {
        fileName: [{ validator: this.fileNameValidator, trigger: 'blur' }],
        filePath: [{ validator: this.filePathValidator, trigger: 'blur' }]
      },
      fileSubmitting: false,
      percentage: 0,
      procNameImport: false,  //  输入的进程名称是否是通过上传文件获取的
      operatorType: null,
      fileList: []  //  保存列表中存在的配置信息
    }
  },
  computed: {
    title() {
      return this.operatorType === 2 ? this.$t('pages.updateFileDetection') : this.$t('pages.addFileDetection')
    }
  },
  methods: {
    //  初始化数据
    initData() {
      this.procNameImport = false
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.oldFileName = ''
      this.oldFileMd5 = ''
    },
    show(data, operatorType, fileList) {
      this.operatorType = operatorType
      this.fileList = fileList || []
      this.initData()
      if (data !== undefined && data !== null) {
        this.temp = Object.assign(this.temp, data)
        this.procNameImport = !!this.temp.fileMd5
      }
      this.visible = true
    },
    //  保存时，格式化数据
    formatFormData(row) {
      return row;
    },
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (!this.temp.id) {
            this.temp.id = Date.now()
          }
          let index = -1
          if (this.fileList.length > 0) {
            index = this.fileList.findIndex(item => this.temp.fileName === item.fileName && this.temp.scanType === item.scanType && this.temp.filePath === item.filePath)
          }
          //  校验是否存在相同的配置信息
          if (index > -1 && this.fileList[index].id !== this.temp.id) {
            this.$message({
              message: this.$t('pages.repeatDataWarn'),
              type: 'warning',
              duration: 3000
            })
            return;
          }
          const row = this.formatFormData(JSON.parse(JSON.stringify(this.temp)))
          this.$emit('submit', row)
          this.$nextTick(() => {
            this.$refs.dataForm.clearValidate()
          })
          this.visible = false
        }
      })
    },
    loadFileCancel() {
      this.fileSubmitting = false
      this.percentage = 0
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
      this.temp.fileName = this.oldFileName
      this.temp.fileMd5 = this.oldFileMd5
    },
    cancel() {
      this.loadFileCancel()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.visible = false
    },
    //  校验文件名称
    fileNameValidator(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.fileNameNotNull')))
      }
      callback()
    },
    //  校验文件路径
    filePathValidator(rule, value, callback) {
      const msg = this.verifyValidityOfCharacters(value)
      if (msg) {
        callback(new Error(msg));
      } else if (this.validExistMultiShortcut(value)) {
        callback(new Error(this.$t('pages.fileDetectionValidMsg1')))
      }
      callback()
    },
    verifyValidityOfCharacters(value) {
      let msg;
      //  判断是否为磁盘格式
      if (/^[c-zC-Z]:\\/.test(value)) {
        if (!/^[c-zC-Z]:\\([^/:*?"<>|])*$/.test(value)) {
          msg = this.$t('pages.fileDetectionFilePathMsg1')
        }
      } else {
        //  不允许输入 / : * ? " < > |
        const notAllowExits = ['/', ':', '*', '?', '"', '<', '>', '|'];
        const values = value.split('') || [];
        if (values.findIndex(item => notAllowExits.includes(item)) !== -1) {
          msg = this.$t('pages.fileDetectionFilePathMsg1')
        }
      }
      return msg;
    },
    // 校验是否存在多个特殊字符  true表示存在多个快捷键，例如：多个 #desktop# 或 一个#desktop# 一个#usertemp#
    validExistMultiShortcut(value) {
      //  文件路径不能存在 多个快捷键
      let count = 0
      for (let i = 0, len = this.filePathShortcutKeys.length; i < len; i++) {
        const key = this.filePathShortcutKeys[i].path;
        const start = value.indexOf(key);
        const last = value.lastIndexOf(key)
        if (start > -1) {
          count++;
          if (last !== start) {
            count++;
          }
        }
        if (count > 1) {
          return true;
        }
      }
      return false;
    },
    //  文件上传之前
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      //  设置上传文件名称
      this.temp.fileName = file.name
      this.uploadFile(fd)
      return false // 屏蔽了action的默认上传
    },
    uploadFile(formData) {
      this.fileSubmitting = true
      this.percentage = 0
      // 上传钩子，用来获取进度条
      const onUploadProgress = (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
        this.percentage = parseInt(percent)
      }
      this.source = this.connectionSource()
      const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
      // 调用后台接口获取进程windows属性
      upload(formData, onUploadProgress, cacheToken).then(res => {
        this.resetUploadComponent()
        this.appendFile(res.data)
        this.oldFileName = this.temp.fileName
        this.oldFileMd5 = this.temp.fileMd5
      }).catch(res => {
        this.resetUploadComponent()
        if (axios.isCancel(res)) {
          // 取消上传后的操作，待补充
        }
      })
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    //  应用程序库导入回调
    appendFile(softs) {
      if (softs.length > 0) {
        this.temp.fileName = softs[0].processName
        //  文件指纹
        this.temp.fileMd5 = softs[0].quicklyMd5
        this.procNameImport = true
      }
    },
    //  文件路径快捷按钮
    filePathShortcutKey(path) {
      this.temp.filePath = path
    },
    fileImport() {
      this.$refs.appSelectDlg.show()
    },
    fileNameChange(fileName) {
      if (this.procNameImport) {
        this.procNameImport = false
        this.temp.fileMd5 = ''
        this.temp.scanType = 0
      }
    }
  }
}
</script>

<style scoped>
.tooltipStyle {
  line-height: 20px;
}
</style>
