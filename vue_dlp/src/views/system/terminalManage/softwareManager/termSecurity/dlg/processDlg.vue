<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
      @close="cancel"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-width="80px" style="margin: 10px">
        <FormItem prop="procName" :label="$t('table.processName')">
          <div style="display: flex">
            <el-input v-model="temp.procName" v-trim style="width: 280px" maxlength="100" show-word-limit @change="procNameChange"></el-input>
            <el-upload
              ref="upload"
              name="uploadFile"
              action="aaaaaa"
              :accept="osType==1 ?supportFileType.join(','):null"
              :disabled="fileSubmitting"
              :show-file-list="false"
              :before-upload="beforeUpload"
              style="display: inline-block;"
            >
              <el-button type="primary" :loading="fileSubmitting" icon="el-icon-upload" size="mini" style="margin-top: 0;"></el-button>
            </el-upload>
          </div>
        </FormItem>
        <FormItem v-if="fileSubmitting">
          <el-row >
            <el-col :span="22">
              <el-progress type="line" :percentage="percentage"/>
            </el-col>
            <el-col :span="2">
              <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="loadFileCancel"></el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="supportMd5" style="height: 30px" :disabled="!procNameImport" @change="supportMd5Change">
            {{ $t('pages.supportMd5') }}
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div slot="content">{{ $t('pages.processStgLib_Msg19') }}<br/></div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
          <div v-if="supportMd5">
            <span>{{ $t('table.checkMd5Label') }}：</span>
            <el-radio v-model="temp.scanType" :disabled="!temp.atbMd5" :label="3" :value="3">
              {{ $t('table.processCharacter') }}
              <el-tooltip class="item" effect="dark" placement="bottom">
                <div slot="content">{{ $t('pages.appGroup_text20') }}<br/></div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
            <el-radio v-model="temp.scanType" :label="1" :value="1">
              {{ $t('table.programFinger') }}
              <el-tooltip class="item" effect="dark" placement="bottom">
                <div slot="content">{{ $t('pages.appGroup_text21') }}<br/></div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
          </div>
        </FormItem>
        <FormItem v-if="type && type === 1 && existSoftwareAuth" :label="$t('pages.repairFile')">
          <el-button v-if="temp.softwareId === null" type="primary" size="mini" @click="selectFile">{{ $t('button.import') }}</el-button>
          <el-tooltip v-if="temp.softwareId !== null && !!selectedFixFile.name" placement="top" effect="dark">
            <div slot="content">{{ selectedFixFile.name }}</div>
            <span style="margin-left: 5px">
              {{ selectedFixFile.name.length > 15 ? (selectedFixFile.name.substr(0, 15) + '...') : selectedFixFile.name }}
              <el-button style="color: #68a8d0; margin-bottom: 0" type="text" icon="el-icon-close" @click="selectedFileClose"/>
            </span>
          </el-tooltip>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <software-repository-select ref="softwareSelect" :add-app-type-able="false" :single-import="true" @select="handleImportSelectedSoftware"/>

  </div>
</template>

<script>
import SoftwareRepositorySelect from '@/views/system/terminalManage/softwareManager/repository/select';
import { upload } from '@/api/behaviorManage/application/appGroup';
import axios from 'axios';
import { listNamesByIds } from '@/api/system/terminalManage/softwareRepository';

export default {
  name: 'ProcessDlg',
  components: { SoftwareRepositorySelect },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      fileLimitSize: 1024,
      submitting: false,
      visible: false,
      supportFileType: ['.exe', '.ex_'], //  支持上传的文件类型
      oldTemp: {},  //  保存上次上传的进程信息，用于上传取消时，使用上次上传的进程信息
      temp: {},
      defaultTemp: {
        id: null,
        procName: '',  //  进程名称
        procSize: null,  // 进程大小
        procMd: '', //  进程Md5（快速md5）
        atbMd5: '', //  属性md5
        controlCode: null,  //  进程特征标识位
        scanType: 0, //  扫描方式： 0：进程名 1：快速md5（程序指纹），3：属性md5（进程特征）
        procDesc: '', //  进程描述
        softwareId: null  //  修复安装包Id
      },
      supportMd5: false,  //  是否支持防伪冒
      selectedFixFile: {}, //  选中的修复文件
      rules: {
        procName: [{ validator: this.procNameValidator, trigger: 'blur' }]
      },
      fileSubmitting: false,
      percentage: 0,
      procNameImport: false,  //  输入的进程名称是否是通过上传文件获取的
      type: 1,  //  type=1， 表示必须运行进程；type=2，表示禁止运行进程
      operatorType: null,  //  操作类型  1-创建，2-修改
      listTemp: { mustList: [], stopList: [] }, // mustList: 必须运行进程列表数据， stopList： 禁止运行进程列表数据
      oldProcName: '' //  保存原来的软件名称，用于判断修改操作时，进程名称与未修改前的一致
    }
  },
  computed: {
    title() {
      return this.$t('pages.titleFormat', { operator: (this.operatorType === 2 ? this.$t('button.edit') : this.$t('button.insert')), name: this.$t('pages.process') })
    },
    //  是否存在软件管家的销售模块
    existSoftwareAuth() {
      return [20, 120, 220].filter(code => (this.$store.getters.saleModuleIds || []).includes(code)).length > 0
    }
  },
  methods: {
    initData() {
      this.oldTemp = {}
      this.serviceNames = []
      this.selectedFixFile = {}
      this.procNameImport = false
      this.oldProcName = ''
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    //  type=1， 表示必须运行进程；type=2，表示禁止运行进程
    async show(data, type, operatorType, listTemp) {
      this.type = type
      this.operatorType = operatorType
      this.listTemp = Object.assign(this.listTemp, listTemp)
      this.initData();
      if (data !== undefined && data !== null) {
        this.temp = Object.assign(this.temp, data)
        this.oldProcName = this.temp.procName
        if (this.temp.softwareId) {
          const res = await listNamesByIds([this.temp.softwareId]);
          const idNameMap = res.data || {}
          if (idNameMap && idNameMap[this.temp.softwareId]) {
            this.selectedFixFile = { id: this.temp.softwareId, name: idNameMap[this.temp.softwareId] }
          } else {
            this.temp.softwareId = null
          }
        }
        this.procNameImport = !!this.temp.procMd || !!this.temp.atbMd5
      }
      this.supportMd5 = this.temp.scanType > 0;
      this.visible = true
    },
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (!this.temp.id) {
            this.temp.id = Date.now()
          }
          const row = JSON.parse(JSON.stringify(this.temp));
          row.scanType = this.supportMd5 ? row.scanType : 0
          this.$emit('submit', row, this.type)
          this.$nextTick(() => {
            this.$refs.dataForm.clearValidate()
          })
          this.visible = false
        }
      })
    },
    loadFileCancel() {
      this.fileSubmitting = false
      this.percentage = 0
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
      const scanType = this.temp.scanType
      const softwareId = this.temp.softwareId
      const id = this.temp.id
      this.temp = JSON.parse(JSON.stringify(this.oldTemp))
      this.temp = Object.assign(this.temp, { scanType, softwareId, id })
    },
    cancel() {
      this.loadFileCancel()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.visible = false
    },
    selectFile() {
      this.$refs.softwareSelect.show()
    },
    //  清除选中的文件
    selectedFileClose() {
      this.temp.softwareId = null
      this.selectedFixFile = {}
    },
    handleImportSelectedSoftware(data, type) {
      //  type: 0:软件，1：分类   data的类型： Array
      if (type === 0) {
        if (data.length > 0) {
          this.temp.softwareId = data[0].id
          this.selectedFixFile = { id: data[0].id, name: data[0].name }
        }
      }
    },
    procNameValidator: function(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.processNameNotNull') + ''))
      } else if (this.operatorType === 2 && this.oldProcName === value) {
        callback()
      }
      const ext = value.slice(value.lastIndexOf('.')).toLowerCase()
      if (!this.supportFileType.includes(ext)) {
        callback(new Error(this.$t('pages.termSecurityProcessValidMsg1') + ''))
      }
      this.validNameListData(value, this.type, this.listTemp.mustList || [], this.listTemp.stopList || [], callback);
      callback()
    },
    //  判断进程名称是否在进程必须运行检查列表和进程禁止运行检查列表中
    validNameListData(value, type, mustList, stopList, callback) {
      if (value !== undefined && value !== null && value.length > 0) {
        const list = type === 1 ? mustList : stopList
        const limitList = type === 1 ? stopList : mustList
        //  校验名称不能重复
        if (list && list.findIndex(item => item.procName === value) > -1) {
          callback(new Error(this.$t('pages.termSecurityProcessValidMsg2') + ''))
        } else if (limitList && limitList.findIndex(item => item.procName === value) > -1) {
          const errorMsg = type === 1 ? this.$t('pages.termSecurityProcessValidMsg3') : this.$t('pages.termSecurityProcessValidMsg4')
          callback(new Error(errorMsg))
        }
      }
    },
    serviceNameValidator(rule, value, callback) {
      if (value.length > 0 && value.includes('|')) {
        callback(new Error(this.$t('pages.serviceNameNotHaveSpecial') + ''))
      }
      callback();
    },
    //  文件上传之前
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()
      if (!this.supportFileType.includes(ext)) {
        this.$alert(`<label style="color: red">${this.$t('pages.uploadExeOrExMsg')}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      this.temp.procName = file.name
      this.uploadFile(fd)
      return false // 屏蔽了action的默认上传
    },
    uploadFile(formData) {
      this.fileSubmitting = true
      this.percentage = 0
      // 上传钩子，用来获取进度条
      const onUploadProgress = (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
        this.percentage = parseInt(percent)
      }
      this.source = this.connectionSource()
      const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
      // 调用后台接口获取进程windows属性
      upload(formData, onUploadProgress, cacheToken).then(res => {
        this.resetUploadComponent()
        this.appendFile(res.data)
        //  保存文件上传信息
        this.oldTemp = JSON.parse(JSON.stringify(this.temp))
      }).catch(res => {
        this.resetUploadComponent()
        if (axios.isCancel(res)) {
          // 取消上传后的操作，待补充
        }
      })
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    appendFile(softs) {
      if (softs.length > 0) {
        this.temp.procName = softs[0].processName || ''
        this.temp.procMd = softs[0].quicklyMd5 || ''
        this.temp.procSize = softs[0].fileSize || 0;
        this.temp.procDesc = softs[0].fileDescription || ''
        this.temp.atbMd5 = softs[0].propertyMd5 || ''
        this.temp.controlCode = softs[0].propertyMark || null
        //  属性md5需要存在
        if (this.temp.controlCode && this.temp.atbMd5) {
          //  属性Md5（属性指纹）需要加3
          this.temp.controlCode += 3
        }
        //  若进程名称是通过上传文件获取的，支持选中防伪冒
        this.procNameImport = true
        this.$refs.dataForm.validateField('procName')
      }
    },
    supportMd5Change(value) {
      if (this.supportMd5) {
        //  若没有属性md5，则默认选中程序指纹, 进程指纹将被锁定无法选中
        this.temp.scanType = this.temp.scanType === 0 ? (this.temp.atbMd5 ? 3 : 1) : this.temp.scanType;
      }
    },
    procNameChange(name) {
      //  procNameImport = true 时，代表上传文件所获得的文件名称发生改变，清空md5等相关信息
      if (this.procNameImport) {
        this.procNameImport = false
        this.supportMd5 = false
        this.temp.scanType = 0
        this.temp.procMd = ''
        this.temp.procDesc = ''
        this.temp.atbMd5 = ''
        this.temp.procSize = null
        this.temp.controlCode = null
      }
    }
  }
}
</script>

<style scoped>
</style>
