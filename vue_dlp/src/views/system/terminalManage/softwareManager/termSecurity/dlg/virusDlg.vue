<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="600px"
    >
      <Form ref="dataForm" style="margin: 10px" :model="temp" :rules="rules" label-width="140px" :extra-width="{ en: 5 }">
        <el-divider content-position="left" class="first-divider">{{ $t('pages.killSoft') }}</el-divider>
        <FormItem :label="$t('pages.killSoftName')" prop="name">
          <el-select v-model="temp.name" @change="nameChange">
            <el-option v-for="item in virusSoftwareList" :key="item.name" :value="item.name" :label="item.label"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.versionRequirement')">
          <el-select v-model="temp.softSymbol">
            <el-option key="0" :value="null" label="无"></el-option>
            <el-option key="1" value="1" :label="$t('pages.greaterThan')"></el-option>
            <el-option key="2" value="2" :label="$t('pages.equalTo')"></el-option>
            <el-option key="3" value="3" :label="$t('pages.lessThan') "></el-option>
            <el-option key="4" value="4" :label="$t('pages.greaterThanOrEqualTo')"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.versionNumber')" prop="softVersion">
          <el-input v-model="temp.softVersion" v-trim maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="existSoftwareAuth" :label="$t('pages.repairFile')">
          <el-button v-if="!temp.softwareId" type="primary" size="mini" @click="selectFile">{{ $t('button.import') }}</el-button>
          <el-tooltip v-if="!!temp.softwareId && !!selectedFixFile.name" placement="top" effect="dark">
            <div slot="content">{{ selectedFixFile.name }}</div>
            <span style="margin-left: 5px">
              {{ selectedFixFile.name.length > 15 ? (selectedFixFile.name.substr(0, 15) + '...') : selectedFixFile.name }}
              <el-button style="color: #68a8d0; margin-bottom: 0" type="text" icon="el-icon-close" @click="selectedFileClose"/>
            </span>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.repairGuide')">
          <div style="display: flex; align-items: center; height: 35px">
            <el-tooltip v-if="temp.repairMethod !== ''" placement="top" effect="dark">
              <div slot="content">{{ temp.repairMethod }}</div>
              <span style="margin-left: 5px; height: 35px">{{ temp.repairMethod.length > 15 ? (temp.repairMethod.substr(0, 15) + '...') : temp.repairMethod }}</span>
            </el-tooltip>
            <el-button v-if="temp.repairMethod !== ''" type="text" style="color: #68a8d0" @click="addFixPlanClick">{{ $t('button.edit') }}</el-button>
            <el-button v-if="temp.repairMethod === ''" type="primary" size="mini" @click="addFixPlanClick">{{ $t('button.insert') }}</el-button>
            <el-tooltip>
              <div slot="content">
                {{ $t('pages.virusMsg1') }}
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </div>

        </FormItem>
        <el-divider content-position="left" class="first-divider">{{ $t('pages.virusLibraryVersion') }}</el-divider>
        <FormItem :label="$t('pages.virusLibraryVersion')">
          <div style="display: flex">
            <el-select v-model="temp.esetType" style="width: 150px; margin-right: 5px" @change="esetTypeChange">
              <el-option key="0" value="0" :label="$t('pages.doNotDetect')"></el-option>
              <el-option key="1" value="1" :label="$t('pages.virusLibraryVersion')" :disabled="notSupportSoftware"></el-option>
              <el-option key="2" value="2" :label="$t('pages.updateDateGreaterThan')"></el-option>
            </el-select>
            <el-select v-if="temp.esetType === '1'" v-model="temp.esetSymbol" style="width: 150px; margin-right: 5px">
              <el-option value="1" :label="$t('pages.greaterThan')" :disabled="temp.esetType === '2'"></el-option>
              <el-option value="2" :label="$t('pages.equalTo')" :disabled="temp.esetType === '2'"></el-option>
              <el-option value="3" :label="$t('pages.lessThan')" ></el-option>
              <el-option key="4" value="4" :label="$t('pages.greaterThanOrEqualTo')" :disabled="temp.esetType === '2'"></el-option>
            </el-select>
            <FormItem prop="esetVersion" label-width="0" style="margin-bottom: 0">
              <el-input v-if="temp.esetType === '1' && selectedSoft.type === 2" v-model="temp.esetVersion" v-trim :placeholder="$t('pages.virusEsetVersionTip')" maxlength="100" style="width: 150px" show-word-limit></el-input>
              <el-input v-if="temp.esetType === '1' && selectedSoft.type === 3" v-model="temp.esetVersion" v-trim :placeholder="$t('pages.daysNotUpdatedTip')" style="width: 150px" maxlength="100" show-word-limit></el-input>
              <el-date-picker v-if="(temp.esetType === '1' && selectedSoft.type === 1) || temp.esetType === '2'" v-model="temp.esetVersion" type="date" :placeholder="$t('pages.selectDate')" value-format="yyyy-MM-dd" format="yyyy-MM-dd" :picker-options="pickerOptionStart" style="width: 150px"></el-date-picker>
            </FormItem>
          </div>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <software-repository-select ref="softwareSelect" :editable="hasPermission('B61')" :add-app-type-able="false" :single-import="true" @select="handleImportSelectedSoftware"/>
    <fix-plan-dlg ref="fixPlanDlg" @submit="fixPlanDlgSubmit"></fix-plan-dlg>
  </div>
</template>

<script>
import SoftwareRepositorySelect from '@/views/system/terminalManage/softwareManager/repository/select'
import FixPlanDlg from '@/views/system/terminalManage/softwareManager/termSecurity/dlg/fixPlanDlg';
import { listNamesByIds } from '@/api/system/terminalManage/softwareRepository';

export default {
  name: 'VirusDlg',
  components: { FixPlanDlg, SoftwareRepositorySelect },
  props: {
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      submitting: false,
      visible: false,
      temp: {    //  杀毒软件检查
        id: null,
        name: '',  //  杀毒软件名称
        esetType: '0', //  杀毒库扫描类型 0：不检测 1：病毒库, 2未更新天数
        softVersion: '',  //  杀毒软件版本号
        softSymbol: null, // 软件版本运算符  1：大于，2：等于，3：小于，4：大于等于
        esetVersion: '',  //  病毒库版本
        esetSymbol: '1',  //  病毒库版本运算符   1：大于，2：等于，3：小于，4：大于等于
        repairMethod: '', //  修复指南
        softwareId: null  //  修复安装包Id
      },
      defaultTemp: {
        id: null,
        name: '',  //  杀毒软件名称
        esetType: '0', //  杀毒库扫描类型 0：不检测 1：病毒库, 2未更新天数
        softVersion: '',  //  杀毒软件版本号
        softSymbol: null, // 软件版本运算符  1：大于，2：等于，3：小于，4：大于等于
        esetVersion: '',  //  病毒库版本
        esetSymbol: '1',  //  病毒库版本运算符   1：大于，2：等于，3：小于，4：大于等于
        repairMethod: '', //  修复指南
        softwareId: null  //  修复安装包Id
      },
      rules: {
        name: [{ validator: this.nameValidator, trigger: 'blur' }],
        softVersion: [{ validator: this.softVersionValidator, trigger: 'blur' }],
        esetVersion: [
          { validator: this.esetVersionValidator, trigger: 'blur' }
        ]
      },
      //  支持的杀毒软件，后续有添加，addIndex.vue 和后台TermSoftSafeDetection.java中的 virusSoftNameI18nMap属性需要同步更新
      virusSoftwareList: [
        { name: '360杀毒', type: 1, label: this.$t('pages.360AntivirusSoftware') },
        { name: '360安全卫士', type: 1, label: this.$t('pages.360SecurityGuard') },
        { name: '腾讯电脑管家', type: 1, label: this.$t('pages.tencentComputerManager') },
        { name: '火绒安全', type: 4, label: this.$t('pages.tinderSafety') },
        { name: '金山毒霸', type: 1, label: this.$t('pages.kingSoft') },
        { name: '卡巴斯基', type: 2, label: this.$t('pages.kaspersky') },
        { name: '江民杀毒', type: 1, label: this.$t('pages.jiangMingKill') },
        { name: 'Avira Antivirus', type: 1, label: 'Avira Antivirus' },
        { name: '瑞星杀毒', type: 2, label: this.$t('pages.risingAntivirus') },
        { name: 'McAfee', type: 2, label: 'McAfee' },
        { name: '趋势杀毒', type: 2, label: this.$t('pages.trendAntivirus') },
        { name: 'ESET', type: 2, label: 'ESET' },
        { name: 'Avast', type: 3, label: 'Avast' },
        { name: 'Norton', type: 4, label: 'Norton' }
      ],
      pickerOptionStart: {
        disabledDate: (time) => {
          if (this.temp.esetType === '2') {
            return time.getTime() > Date.now()
          }
          return false;
        }
      },
      selectedFixFile: {}, //  选中的修复文件
      existVirusNames: [],  //  列表中已存在的杀毒软件名称
      updateVirusName: '',   //  修改操作时，保持原杀毒软件名称
      operatorType: null //  创建类型
    }
  },
  computed: {
    selectedSoft() {
      const result = this.virusSoftwareList.find(item => item.name === this.temp.name);
      return result !== null ? result : {}
    },
    //  不支持病毒版本库的杀毒软件
    notSupportSoftware() {
      return ['卡巴斯基', '火绒安全', 'Norton'].includes(this.temp.name)
    },
    title() {
      return this.operatorType === 2 ? this.$t('pages.updateKillSoft') : this.$t('pages.addKillSoft');
    },
    //  是否存在软件管家的销售模块
    existSoftwareAuth() {
      return [20, 120, 220].filter(code => (this.$store.getters.saleModuleIds || []).includes(code)).length > 0
    }
  },
  methods: {
    initData() {
      this.updateVirusName = ''
      this.temp = Object.assign({}, this.defaultTemp)
      this.existVirusNames = []
      this.selectedFixFile = {}
    },
    //  existVirusList: 列表中已存在的杀毒软件  operatorType: 1-create, 2-update
    async show(data, existVirusList, operatorType) {
      this.operatorType = operatorType
      this.initData()
      if (data !== undefined && data !== null) {
        this.temp = Object.assign(this.temp, data)
        this.updateVirusName = this.temp.name || ''
        if (this.temp.softwareId) {
          const res = await listNamesByIds([this.temp.softwareId]);
          const idNameMap = res.data || {}
          if (idNameMap && idNameMap[this.temp.softwareId]) {
            this.selectedFixFile = { id: this.temp.softwareId, name: idNameMap[this.temp.softwareId] }
          } else {
            this.temp.softwareId = null
          }
        }
      } else {
        this.temp.name = this.virusSoftwareList[0].name
      }
      if (existVirusList.length > 0) {
        this.existVirusNames = existVirusList.map(t => {
          return t.name
        })
      }

      this.visible = true
    },
    confirm() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          if (!this.temp.id) {
            this.temp.id = Date.now();
          }
          this.visible = false
          this.$refs.dataForm.clearValidate()
          this.$emit('submit', JSON.parse(JSON.stringify(this.temp)))
        }
      })
    },
    cancel() {
      this.visible = false
      this.$refs.dataForm.clearValidate()
    },
    selectFile() {
      this.$refs.softwareSelect.show()
    },
    handleImportSelectedSoftware(data, type) {
      //  type: 0:软件，1：分类   data的类型： Array
      if (type === 0) {
        if (data.length > 0) {
          this.temp.softwareId = data[0].id
          this.selectedFixFile = { id: data[0].id, name: data[0].name }
          this.temp.softVersion = this.temp.softVersion === null || this.temp.softVersion === '' ? data[0].version : this.temp.softVersion
        }
      }
    },
    //  清除选中的文件
    selectedFileClose() {
      this.temp.softwareId = null
      this.selectedFixFile = {}
    },
    //  添加修复指南
    addFixPlanClick() {
      this.$refs.fixPlanDlg.show(this.temp.repairMethod)
    },
    fixPlanDlgSubmit(content) {
      this.temp.repairMethod = content || ''
    },
    //  杀毒软件名称更新
    nameChange() {
      const name = this.temp.name
      const id = this.temp.id
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.temp.id = id;
      this.temp.name = name
      this.$refs.dataForm.clearValidate()
    },
    //  病毒版本库类型更新
    esetTypeChange(value) {
      this.temp.esetSymbol = value === '2' ? '4' : '1'
      this.temp.esetVersion = ''
      this.$refs.dataForm.clearValidate()
    },
    //  校验杀毒软件名称
    nameValidator(rule, value, callback) {
      if (value !== this.updateVirusName && this.existVirusNames.includes(value)) {
        callback(new Error(this.$t('pages.virusSoftConfigured') + ''))
      } else {
        callback();
      }
    },
    //  校验杀毒软件版本号
    softVersionValidator(rule, value, callback) {
      if (this.temp.softSymbol !== null && (value === undefined || value === null || value === '')) {
        callback(new Error(this.$t('pages.newTerminal_text13') + ''))
      }
      callback();
    },
    //  校验病毒版本库
    esetVersionValidator(rule, value, callback) {
      if (this.temp.esetType === '1') {
        //  获取杀毒软件类型
        if (!value || value.length === 0) {
          callback(new Error(this.$t('pages.virusLibVersionNotNull')));
          return
        }
        const software = this.virusSoftwareList.find(item => item.name === this.temp.name);
        //  校验是否输入了非中文字符
        if (software.type === 2) {
          const result = /[\u4e00-\u9fa5]/.test(value);
          if (result) {
            callback(this.$t('pages.virusLibVersionTip1') + '')
            return
          }
        }
      } else if (this.temp.esetType === '2') {
        if (!value || value.length === 0) {
          callback(this.$t('pages.virusValidMsg') + '')
        }
      }
      callback();
    }
  }
}
</script>

<style scoped>
.el-button{
  min-width: 50px;
  margin-bottom: 5px;
}
</style>
