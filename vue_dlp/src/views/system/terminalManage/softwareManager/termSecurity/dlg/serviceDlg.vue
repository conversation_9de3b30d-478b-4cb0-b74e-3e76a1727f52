<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
    >
      <Form ref="dataForm" style="margin: 10px" :model="temp" :rules="rules" label-width="140px" :extra-width="{en:25}">
        <FormItem :label="$t('pages.detectionServiceName')" prop="serviceName">
          <el-input v-model="temp.serviceName" v-trim type="text" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="dependOnEnable" :label="$t('pages.dependentServiceName')">
          <span>
            <el-tooltip>
              <div v-if="type && type === 1" slot="content" class="tooltipStyle">
                {{ $t('pages.serviceDependentMsg1') }}<br>
                {{ $t('pages.serviceDependentMsg2') }}<br>
                {{ $t('pages.serviceDependentMsg3') }}<br>
                {{ $t('pages.serviceDependentMsg4') }}
              </div>
              <div v-else slot="content" class="tooltipStyle">
                {{ $t('pages.serviceDependentMsg5') }}<br>
                {{ $t('pages.serviceDependentMsg6') }}<br>
                {{ $t('pages.serviceDependentMsg7') }}<br>
                {{ $t('pages.serviceDependentMsg8') }}
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
            {{ $t('pages.termSecurityDependentLimitNum', { size: relationServiceMaxSize }) }}
          </span>
          <tag ref="tag" :list="serviceNames" :valid-rule="serviceNameValidRule" input-length="100" :show-word-limit="true" :limit-size="relationServiceMaxSize" border></tag>
        </FormItem>
        <FormItem :label="$t('table.startType')">
          <el-select v-model="temp.startType" style="width: calc(100% - 18px)" clearable>
            <el-option v-if="type === 1" :value="2" :label="$t('pages.termSecurityAuto')"></el-option>
            <el-option :value="3" :label="$t('pages.termSecurityManual')"></el-option>
            <el-option v-if="type === 2" :value="4" :label="$t('pages.termSecurityDisable')"></el-option>
          </el-select>
          <span>
            <el-tooltip>
              <div slot="content" class="tooltipStyle">
                {{ type === 1 ?
                  this.$t('pages.serviceStartTypeMsg1')
                  : this.$t('pages.serviceStartTypeMsg2') }}
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </span>
        </FormItem>
        <FormItem v-if="type && type === 1 && existSoftwareAuth" :label="$t('pages.repairFile')">
          <el-button v-if="temp.softwareId === null" type="primary" size="mini" @click="selectFile">{{ $t('button.import') }}</el-button>
          <el-tooltip v-if="temp.softwareId !== null && !!selectedFixFile.name" placement="top" effect="dark">
            <div slot="content">{{ selectedFixFile.name }}</div>
            <span style="margin-left: 5px">
              {{ selectedFixFile.name.length > 15 ? (selectedFixFile.name.substr(0, 15) + '...') : selectedFixFile.name }}
              <el-button style="color: #68a8d0; margin-bottom: 0" type="text" icon="el-icon-close" @click="selectedFileClose"/>
            </span>
          </el-tooltip>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <software-repository-select ref="softwareSelect" :add-app-type-able="false" @select="handleImportSelectedSoftware"/>
  </div>
</template>

<script>
import SoftwareRepositorySelect from '@/views/system/terminalManage/softwareManager/repository/select';
import { listNamesByIds } from '@/api/system/terminalManage/softwareRepository';

export default {
  name: 'ServiceDlg',
  components: { SoftwareRepositorySelect },
  props: {
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      submitting: false,
      visible: false,
      dependOnEnable: false,  //  依赖项是否启动
      temp: {},
      defaultTemp: {
        id: null,
        serviceName: '',  //  服务名称
        startType: null,  //  启动类型
        softwareId: null  //  修复安装包Id
      },
      selectedFixFile: {}, //  选中的修复文件
      serviceNames: [],  //  保存依赖服务
      serviceNameValidRule: [
        { validator: this.serviceNameValidator, trigger: 'blur' }
      ],
      rules: {
        serviceName: [{ validator: this.nameValidator, trigger: 'blur' }]
      },
      relationServiceMaxSize: 5, //  依赖服务最大格式
      type: 1,  //  type=1， 表示必须运行服务；type=2，表示禁止运行服务
      operatorType: null,    //  操作类型  1-创建 2-修改
      listTemp: { mustList: [], stopList: [] }, // mustList: 必须运行服务列表数据， stopList： 禁止运行服务列表数据
      oldServiceName: ''  //  保存原来的服务名称
    }
  },
  computed: {
    title() {
      return this.operatorType === 2 ? this.$t('pages.updateService') : this.$t('pages.addService')
    },
    //  是否存在软件管家的销售模块
    existSoftwareAuth() {
      return [20, 120, 220].filter(code => (this.$store.getters.saleModuleIds || []).includes(code)).length > 0
    }
  },
  methods: {
    initData() {
      this.oldServiceName = ''
      this.serviceNames = []
      this.selectedFixFile = {}
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.$refs['tag'] && this.$refs['tag'].clearInputValue()
    },
    async show(data, type, operatorType, listTemp, dependOnEnable) {
      this.type = type
      this.operatorType = operatorType
      this.listTemp = Object.assign(this.listTemp, listTemp)
      this.initData();
      if (data !== undefined && data !== null) {
        this.temp = Object.assign(this.temp, data)
        if (this.temp.softwareId) {
          const res = await listNamesByIds([this.temp.softwareId]);
          const idNameMap = res.data || {}
          if (idNameMap && idNameMap[this.temp.softwareId]) {
            this.selectedFixFile = { id: this.temp.softwareId, name: idNameMap[this.temp.softwareId] }
          } else {
            this.temp.softwareId = null
          }
        }
      }
      this.temp = this.initFormatData(this.temp);
      this.oldServiceName = this.temp.serviceName
      this.dependOnEnable = !!dependOnEnable
      this.visible = true
    },
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (!this.temp.id) {
            this.temp.id = Date.now()
          }
          const row = this.formatFormData(this.temp);
          this.$emit('submit', row, this.type)
          this.$nextTick(() => {
            this.$refs.dataForm.clearValidate()
          })
          this.visible = false
        }
      })
    },
    //  初始化, serviceName 中 第一个表示需要检查的服务，后面为依赖服务
    initFormatData(row) {
      //  分割服务名称 用 | 分开
      if (row.serviceName !== null && row.serviceName.length > 0) {
        const name = row.serviceName.split('|')
        if (name instanceof Array) {
          if (name.length > 1) {
            row.serviceName = name[0]
            this.serviceNames = []
            for (let i = 1, len = name.length; i < len; i++) {
              name[i].length > 0 && this.serviceNames.push(name[i]);
            }
          }
        }
      }
      if (row.startType === 0) {
        row.startType = null
      }
      return row;
    },
    formatFormData(row) {
      row = JSON.parse(JSON.stringify(row));
      row.serviceName = row.serviceName + (this.serviceNames.length > 0 ? '|' + this.serviceNames.join('|') : '')
      row.startType = row.startType == null ? 0 : row.startType
      return row;
    },
    cancel() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.visible = false
    },
    selectFile() {
      this.$refs.softwareSelect.show()
    },
    //  清除选中的文件
    selectedFileClose() {
      this.temp.softwareId = null
      this.selectedFixFile = {}
    },
    handleImportSelectedSoftware(data, type) {
      //  type: 0:软件，1：分类   data的类型： Array
      if (type === 0) {
        if (data.length > 0) {
          this.temp.softwareId = data[0].id
          this.selectedFixFile = { id: data[0].id, name: data[0].name }
        }
      }
    },
    nameValidator(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.serviceNameNotNull') + ''))
      } else if (value.includes('|')) {
        callback(new Error(this.$t('pages.serviceNameNotHaveSpecial') + ''))
      } else if (this.operatorType === 2 && this.oldServiceName === value) {
        callback();
      }
      this.validNameListData(value, this.type, this.listTemp.mustList || [], this.listTemp.stopList || [], callback);
      callback()
    },
    //  判断服务名称是否在系统服务必须运行检查列表和系统服务禁止运行检查列表中
    validNameListData(value, type, mustList, stopList, callback) {
      if (value !== undefined && value !== null && value.length > 0) {
        const list = type === 1 ? mustList : stopList
        const limitList = type === 1 ? stopList : mustList
        //  校验名称不能重复
        if (list && list.findIndex(item => item.serviceName.split('|')[0] === value) > -1) {
          callback(new Error(this.$t('pages.serviceNameRepeat') + ''))
        } else if (limitList && limitList.findIndex(item => item.serviceName.split('|')[0] === value) > -1) {
          const errorMsg = type === 1 ? this.$t('pages.termSecurityServiceValidMsg1') : this.$t('pages.termSecurityServiceValidMsg2')
          callback(new Error(errorMsg))
        }
      }
    },
    serviceNameValidator(rule, value, callback) {
      if (value.length > 0 && value.includes('|')) {
        callback(new Error(this.$t('pages.serviceNameNotHaveSpecial') + ''))
      } else if (value.length > 0 && value === this.temp.serviceName) {
        callback(new Error(this.$t('pages.notDetectionServiceRepeat') + ''))
      }
      callback();
    }
  }
}
</script>

<style scoped>
.tooltipStyle {
  line-height: 20px;
}
</style>
