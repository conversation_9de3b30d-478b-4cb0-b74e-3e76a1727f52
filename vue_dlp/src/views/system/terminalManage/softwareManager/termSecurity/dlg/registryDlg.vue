<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
    >
      <Form ref="dataForm" style="margin: 10px" :model="temp" :rules="rules" label-width="110px">
        <FormItem :label="$t('pages.scanType')">
          <el-select v-model="temp.regType" @change="regTypeChange">
            <el-option key="0" :value="0" :label="$t('pages.registryKeyItem')"/>
            <el-option key="1" :value="1" :label="$t('pages.registryValue')"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.registryKeyItem')" prop="regItem">
          <el-input v-model="temp.regItem" v-trim maxlength="255" style="width: calc(100% - 20px)" show-word-limit></el-input>
          <el-tooltip effect="dark" placement="top">
            <div slot="content" class="tooltipStyle">
              {{ $t('pages.registryDetectionRegItemMgs1') }}<br>
              {{ $t('pages.registryDetectionRegItemMgs2') }}<br>
              {{ $t('pages.registryDetectionRegItemMgs3') }}<br>
              HKEY_CLASSES_ROOT、<br>
              HKEY_CURRENT_USER、<br>
              HKEY_LOCAL_MACHINE、<br>
              HKEY_USERS、<br>
              HKEY_CURRENT_CONFIG<br>
              {{ $t('pages.registryDetectionRegItemMgs4') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem v-if="temp.regType === 1" :label="$t('pages.registryKey')" prop="regKey">
          <el-input v-model="temp.regKey" v-trim maxlength="255" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="temp.regType === 1" :label="$t('pages.registryKeyValue')" prop="regValue">
          <el-input v-model="temp.regValue" v-trim maxlength="255" show-word-limit @change="regValueChange"></el-input>
        </FormItem>
        <FormItem :label="$t('table.operate')" prop="operator">
          <el-select v-model="temp.operator">
            <el-option v-if="temp.regType === 0 || (temp.regValue === undefined || temp.regValue === null || temp.regValue.length === 0)" :key="0" :value="0" :label="$t('pages.notExist')"></el-option>
            <el-option v-if="temp.regType === 0 || (temp.regValue === undefined || temp.regValue === null || temp.regValue.length === 0)" :key="1" :value="1" :label="$t('pages.exist')"></el-option>
            <div v-if="temp.regType === 1 && temp.regValue && temp.regValue.length > 0">
              <div v-for="item in operators" :key="item.id">
                <el-option v-if="!item.hid" :key="item.id" :value="item.id" :label="item.label"/>
              </div>
            </div>
          </el-select>
        </FormItem>
        <div class="tooltipStyle">
          {{ $t('pages.registryDetectionNote1') }}<br>
          {{ $t('pages.registryDetectionNote2') }}
          <el-tooltip effect="dark" placement="top">
            <div slot="content" class="tooltipStyle">
              {{ $t('pages.registryDetectionNoteTip1') }}<br>
              {{ $t('pages.registryDetectionNoteTip2') }}<br>
              {{ $t('pages.registryDetectionNoteTip3') }}<br>
              {{ $t('pages.registryDetectionNoteTip4') }}<br>
              {{ $t('pages.registryDetectionNoteTip5') }}<br>
              {{ $t('pages.registryDetectionNoteTip6') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RegistryDlg',
  components: {},
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      //  show=1，表示 扫描类型为文件或注册表项时所需要显示的数据
      operators: [
        { id: 0, label: this.$t('pages.notExist'), hid: true },
        { id: 1, label: this.$t('pages.exist'), hid: true },
        { id: 2, label: this.$t('pages.termSecurityGreaterThan') },
        { id: 3, label: this.$t('pages.termSecurityEqual') },
        { id: 4, label: this.$t('pages.termSecurityLessThan') },
        { id: 5, label: this.$t('pages.termSecurityGreaterThanOrEqual') },
        { id: 6, label: this.$t('pages.termSecurityLessThanOrEqual') },
        { id: 7, label: this.$t('pages.termSecurityContain') },
        { id: 8, label: this.$t('pages.termSecurityExclude') }
      ],
      submitting: false,
      visible: false,
      temp: {},
      defaultTemp: {
        id: null,
        regType: 0,  //  扫描类型 0:注册表项，1：注册表值
        regItem: '',  //  注册表项
        regKey: '', //  注册表键  - 对应注册表编辑器中的名称 若为默认，则不填
        operator: 1,  //  操作项  0：不存在，1：存在， 2：大于，3：等于，4：小于，5：大于等于，6：小于等于，7：包含，8：不包含
        regValue: ''  //  注册表值
      },
      rules: {
        regItem: [{ validator: this.regItemValidator, trigger: 'blur' }],
        regKey: [{ validator: this.regKeyValidator, trigger: 'blur' }],
        regValue: [{ validator: this.regValueValidator, trigger: 'blur' }]
      },
      oldRegItem: '', //  执行更新操作时，保存原来的（regItem值）
      existList: [],  //  已存在的检查项信息(regItem)
      operatorType: null  //  1-创建，2-修改操作
    }
  },
  computed: {
    title() {
      return this.operatorType === 2 ? this.$t('pages.updateRegistryDetection') : this.$t('pages.addRegistryDetection')
    }
  },
  methods: {
    //  初始化数据
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    //  data:更新数据， existList:列表中存在的配置项， operatorType:操作类型，1-create, 2-update
    show(data, existList, operatorType) {
      this.operatorType = operatorType
      this.initData()
      if (data !== undefined && data !== null) {
        this.temp = Object.assign(this.temp, data)
        this.oldRegItem = this.temp.regItem
        this.temp = this.initFormData(this.temp)
      }
      this.existList = existList || [];
      this.visible = true
    },
    initFormData(row) {
      if (row.regType === 1) {
        const index = row.regItem.toString().lastIndexOf('\\');
        row.regKey = row.regItem.substr(index + 1)
        row.regItem = row.regItem.substr(0, index);
      }
      return row;
    },
    formatFormData(row) {
      if (row.regType === 1) {
        row.regItem = row.regItem + '\\' + row.regKey;
      }
      delete row.regKey
      return row;
    },
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (!this.temp.id) {
            this.temp.id = Date.now()
          }
          const row = this.formatFormData(JSON.parse(JSON.stringify(this.temp)));
          if (this.existList && this.existList.findIndex(item => {
            if (this.operatorType === 2 && this.oldRegItem === item.regItem) {
              return false;
            }
            return item.regType === row.regType && item.regItem === row.regItem
          }) > -1) {
            this.$message({
              message: this.$t('pages.repeatDataWarn'),
              type: 'warning',
              duration: 3000
            })
            return;
          }
          this.$emit('submit', row)
          this.$nextTick(() => {
            this.$refs.dataForm.clearValidate()
          })
          this.visible = false
        }
      })
    },
    cancel() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.visible = false
    },
    regItemValidator(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.registryDetectionValidMsg1') + ''))
      }
      //  不能以 \ 结尾
      if (value.substr(value.length - 1) === '\\') {
        callback(new Error(this.$t('pages.registryDetectionValidMsg2') + ''))
      }
      //  必须已HKEY_CLASSES_ROOT HKEY_CURRENT_USER HKEY_LOCAL_MACHINE HKEY_USERS HKEY_CURRENT_CONFIG 开头
      const beginSuffix = ['HKEY_CLASSES_ROOT', 'HKEY_CURRENT_USER', 'HKEY_LOCAL_MACHINE', 'HKEY_USERS', 'HKEY_CURRENT_CONFIG']
      const split = value.split('\\');
      if (split.length > 0) {
        const suffix = split[0].toUpperCase();
        if (!beginSuffix.includes(suffix)) {
          callback(new Error(this.$t('pages.registryDetectionValidMsg3') + ''))
        }
      }
      callback()
    },
    regKeyValidator(rule, value, callback) {
      if (this.temp.regType === 1 && value.length === 0) {
        callback(new Error(this.$t('pages.registryKeyNotNull') + ''))
      }
      //  不能以 \ 结尾
      if (value.substr(value.length - 1) === '\\') {
        callback(new Error(this.$t('pages.registryDetectionValidMsg4') + ''));
      }
      callback()
    },
    regValueValidator(rule, value, callback) {
      if (value.length === 0) {
        this.temp.operator = 1
      }
      callback();
    },
    regTypeChange() {
      this.temp.regItem = ''
      this.temp.regKey = ''
      this.temp.operator = 1
      this.temp.regValue = ''
    },
    regValueChange() {
      if (this.temp.regValue.length > 0) {
        this.temp.operator = this.temp.operator === 1 || this.temp.operator === 0 ? 2 : this.temp.operator
      } else {
        this.temp.operator = this.temp.operator !== 1 || this.temp.operator !== 0 ? 1 : this.temp.operator
      }
    }
  }
}
</script>

<style scoped>
.tooltipStyle {
  line-height: 20px;
}
</style>
