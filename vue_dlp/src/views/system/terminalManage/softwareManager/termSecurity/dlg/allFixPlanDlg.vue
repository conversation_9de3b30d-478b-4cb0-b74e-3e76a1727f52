<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="visible"
      width="500px"
    >
      <Form ref="dataForm" style="margin: 10px" :model="temp" :rules="rules" label-width="70px">
        <div v-if="testItemTree.length === 0" style="color: red; margin-left: 20px">{{ $t('pages.fixPlanTip') }}</div>
        <FormItem :label="$t('pages.detectionItem')" prop="testItemId">
          <tree-select
            ref="testItemTree"
            :checked-keys="testItemCheck"
            node-key="testItemId"
            is-filter
            leaf-key=""
            :data="testItemTree"
            :rewrite-node-click-fuc="true"
            :render-content="renderContent"
            :node-click-fuc="nodeClickFuc"
            @change="testItemChange"
          >
          </tree-select>
        </FormItem>
        <FormItem :label="$t('pages.fixPlan')">
          <el-tooltip>
            <div slot="content">
              {{ $t('pages.repairGuideTip') }}
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <el-button type="primary" size="mini" @click="addFixPlanImport">{{ $t('button.import') }}</el-button>
        </FormItem>
        <FormItem prop="fixPlan">
          <el-input v-model="temp.fixPlan" v-trim type="textarea" :rows="10" show-word-limit maxlength="150"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <fix-plan-import-dlg ref="fixPlanImportDlg" @submit="importFixPlan"></fix-plan-import-dlg>
  </div>
</template>

<script>
import FixPlanImportDlg from '@/views/system/terminalManage/softwareManager/termSecurity/fixPlanImportDlg';

export default {
  name: 'AllFixPlanDlg',
  components: { FixPlanImportDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    //  支持的杀毒软件， key：杀毒软件名称， value：杀毒软件名称（多语言）
    virusMap: { type: Object, default() { return {} } }
  },
  data() {
    return {
      //  show=1，表示 扫描类型为文件或注册表项时所需要显示的数据
      operators: [
        { id: 0, label: this.$t('pages.notExist'), hid: true },   //  不存在
        { id: 1, label: this.$t('pages.exist'), hid: true },      //  存在
        { id: 2, label: this.$t('pages.termSecurityGreaterThan') },   //  大于
        { id: 3, label: this.$t('pages.termSecurityEqual') },   //  等于
        { id: 4, label: this.$t('pages.termSecurityLessThan') },   //  小于
        { id: 5, label: this.$t('pages.termSecurityGreaterThanOrEqual') },   //  大于等于
        { id: 6, label: this.$t('pages.termSecurityLessThanOrEqual') },   //  小于等于
        { id: 7, label: this.$t('pages.termSecurityContain') },   //  包含
        { id: 8, label: this.$t('pages.termSecurityExclude') }   //  不包含
      ],
      submitting: false,
      visible: false,
      temp: {},
      defaultTemp: {
        id: null,
        testItemId: '',
        dataId: '', //  类型， 例如Type1，Type2
        softName: '', //  若为杀毒软件检查，则表示杀毒软件名称
        label: '',  //  测试项类型描述
        typeKey: '', //  测试项类型
        fixPlan: ''
      },
      rules: {
        testItemId: [{ validator: this.testItemIdValidator, trigger: 'blur' }],
        fixPlan: [{ required: true, message: this.$t('pages.fixPlanNotNull'), trigger: 'blur' }]
      },
      operatorType: 1,
      testItemTree: [],
      testItemCheck: [], //  选中的节点
      configuredFixPlans: [], //  已配置的修复指南
      updateTestId: null
    }
  },
  computed: {
    title() {
      return this.operatorType === 2 ? this.$t('pages.updateFixPlan') : this.$t('pages.addFixPlan')
    }
  },
  methods: {
    //  已配置的修复指南
    show(configuredList, row, configuredFixPlans, operatorType) {
      this.operatorType = operatorType
      this.configuredFixPlans = configuredFixPlans;
      this.initTestItemTree(configuredList);
      this.initTempData(row);
      this.visible = true
    },
    //  初始化检测项树
    initTestItemTree(configuredList) {
      this.testItemTree.splice(0)
      if (configuredList !== undefined && configuredList !== null) {
        const virusList = configuredList.filter(item => item.dataId === 'Type1') || []
        if (virusList.length > 0) {
          this.testItemTree.push({ testItemId: virusList[0].dataId, dataId: virusList[0].dataId, label: virusList[0].label, type: 1, children: [] })
          virusList.forEach(item => {
            //  2表示根节点，支持点击选中
            this.testItemTree[0].children.push({ testItemId: 'Type1-' + item.softName, dataId: virusList[0].dataId,
              parentLabel: virusList[0].label, softName: item.softName, label: this.virusMap[item.softName], typeKey: item.typeKey, type: 2 })
          })
        }
        configuredList.forEach(item => {
          if (item.dataId !== 'Type1') {
            this.testItemTree.push({ testItemId: item.dataId, dataId: item.dataId, label: item.label, typeKey: item.typeKey, type: 2 })
          }
        })
      }
    },
    //  初始化数据
    initTempData(row) {
      this.updateTestId = null
      this.temp = Object.assign({}, this.defaultTemp);
      this.testItemCheck = []
      if (row !== undefined && row !== null) {
        this.temp = Object.assign(this.temp, row)
        this.testItemCheck.push(this.temp.testItemId)
        this.updateTestId = this.temp.id;
      }
    },
    //  确认
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          if (!this.temp.id) {
            this.temp.id = Date.now()
          }
          const row = JSON.parse(JSON.stringify(this.temp));
          this.$emit('submit', row)
          this.$nextTick(() => {
            this.$refs.dataForm.clearValidate()
          })
          this.visible = false
        }
      })
    },
    cancel() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.visible = false
    },
    addFixPlanImport() {
      this.$refs.fixPlanImportDlg.show()
    },
    nodeClickFuc(data, node, vm) {
      if (data.type === 1) {
        return false
      }
      this.temp.testItemId = data.testItemId
      this.temp.dataId = data.dataId
      this.temp.typeKey = data.typeKey
      if (data.parentLabel) {
        this.temp.softName = data.softName;
        this.temp.label = data.parentLabel
      } else {
        this.temp.label = data.label;
      }
      this.$refs.dataForm && this.$refs.dataForm.validateField('testItemId')
    },
    testItemChange(key, nodeData) {
      this.temp.testItemId = key;
    },
    renderContent(h, { node, data, store }) {
      return (<span title={node.label}>{node.label}</span>)
    },
    importFixPlan(data) {
      this.temp.fixPlan = data.content
    },
    testItemIdValidator(rule, value, callback) {
      if (this.temp.testItemId === null || this.temp.testItemId === '') {
        callback(new Error(this.$t('pages.testItemNotNull') + ''))
      } else {
        const index = this.configuredFixPlans.findIndex(item => item.testItemId === this.temp.testItemId);
        if (index > -1 && (this.updateTestId == null || this.temp.id !== this.updateTestId)) {
          callback(new Error(this.$t('pages.fixPlanValidMsg1') + ''))
        }
      }
      callback();
    }
  }
}
</script>

<style scoped>

</style>
