<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.fixPlan')"
      :visible.sync="visible"
      width="700px"
    >
      <Form :model="temp" label-width="100px">
        <FormItem :label="$t('pages.fixPlan')">
          <el-button type="primary" size="mini" @click="addFixPlanImport">{{ $t('button.import') }}</el-button>
          <el-tooltip>
            <div slot="content">
              {{ $t('pages.repairGuideTip') }}
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
        </FormItem>
        <FormItem>
          <el-input v-model="temp.content" v-trim style="height: auto;" type="textarea" :maxlength="150" show-word-limit :rows="7"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <fix-plan-import-dlg ref="fixPlanImportDlg" @submit="importFixPlan"></fix-plan-import-dlg>
  </div>
</template>

<script>
import FixPlanImportDlg from '../fixPlanImportDlg'
export default {
  name: 'FixPlanDlg',
  components: { FixPlanImportDlg },
  props: {
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      submitting: false,
      visible: false,
      temp: {
        content: ''
      },
      param: null //  传参，用于判断属于哪个扫描项的修复指南
    }
  },
  methods: {
    show(content, param) {
      this.temp.content = content !== undefined && content !== null ? content : ''
      this.param = param || null
      this.visible = true
    },
    addFixPlanImport() {
      this.$refs.fixPlanImportDlg.show()
    },
    confirm() {
      this.visible = false
      this.$emit('submit', this.temp.content, this.param)
    },
    importFixPlan(data) {
      this.temp.content = data.content
    }
  }
}
</script>

<style scoped>

</style>
