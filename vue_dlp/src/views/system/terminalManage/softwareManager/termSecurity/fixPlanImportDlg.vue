<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="false"
      :modal="false"
      :title="$t('route.fixPlanLibrary')"
      :visible.sync="dialogVisible"
      width="850px"
    >
      <fix-plan-select-table ref="fixPlanSelectTable" :height="400" :multiple="multiple" :lib-tree-node="getTreeNode" :lib-page="getFixPlanPage"/>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :menu-code="'B65'" :link-url="'/terminalManage/termSecurityManager/fixPlanLibrary'" :btn-text="$t('pages.maintainFixPlanLib')" :click-func="'clickLink'" @clickLink="clickLink('/terminalManage/termSecurityManager/fixPlanLibrary')"/>
        <el-button type="primary" @click="confirmImport()">
          {{ $t('pages.addFixPlan1') }}
        </el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getTreeNode, getFixPlanPage } from '@/api/system/terminalManage/fixPlan'
import FixPlanSelectTable from './FixPlanSelectTable'

export default {
  name: 'FixPlanImportDlg',
  components: { FixPlanSelectTable },
  props: {
    multiple: { type: Boolean, default: true } // 能否多选
  },
  data() {
    return {
      dialogVisible: false,
      fileSuffixTree: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs.fixPlanSelectTable.checkedRowKeys = []
      }
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    getTreeNode,
    getFixPlanPage,
    show(suffix) {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.fixPlanSelectTable.show(suffix)
      })
    },
    hide() {
      this.dialogVisible = false
    },
    confirmImport() {
      this.$refs.fixPlanSelectTable.getSelectedDatas().then(resp => {
        if (resp == null || resp.length === 0) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.selectedImportFixPlan'),
            type: 'error',
            duration: 2000
          })
        } else if (resp.length > 1) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.selectOneFixPlan'),
            type: 'error',
            duration: 2000
          })
        } else {
          this.$emit('submit', resp[0])
          this.hide()
        }
      })
    },
    clickLink(path) {
      this.$router.push(path)
    }
  }
}
</script>
