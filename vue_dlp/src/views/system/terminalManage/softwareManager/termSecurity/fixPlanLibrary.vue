<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="typeTree"
        resizeable
        :default-expand-all="true"
        :data="treeData"
        :is-filter="true"
        :expand-on-click-node="false"
        :render-content="renderContent"
        @node-click="treeNodeClick"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" :disabled="selectTreeId==null || selectTreeId == 0" size="mini" @click="handleCreate2()">
          {{ $t('button.add') }}
        </el-button>
        <el-button size="mini" icon="el-icon-delete" @click="handleRemove">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.fixPlanName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="fixPlanTable"
        :show-pager="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible1"
      width="600px"
    >
      <Form
        ref="dataForm2"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
      >
        <div class="toolbar">
          <FormItem :label="$t('pages.groupType')" prop="parentId" style="margin-top:5px;">
            <tree-select
              :data="formTreeData"
              node-key="dataId"
              :checked-keys="checkedKeys"
              @change="parentIdChange"
            />
          </FormItem>
          <FormItem :label="$t('pages.fixPlanName')" prop="name">
            <el-input v-model="temp.name" :maxlength="50"></el-input>
          </FormItem>
          <FormItem :label="$t('pages.termSecurityContent')" prop="content">
            <el-input v-model="temp.content" :maxlength="150" :rows="8" type="textarea" show-word-limit></el-input>
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="dialogStatus==='update1'?updateData1() : createData1()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible1 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <export-dlg ref="exportDlg" :group-tree-data="treeData" :export-func="exportFunc" :group-tree-id="selectTreeId"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      template="fixPlan.xls"
      :show-import-type="false"
      :file-name="title + '.xls'"
      :tip="$t('pages.fixPlan')"
      :show-import-way="true"
      :upload-func="upload"
      @success="importEndFunc"
    />

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="deleteGroupTitle"
      :dlg-title="$t('table.fixPlan')"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteGroup"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
    ></delete-group-dlg>

    <edit-group-dlg
      ref="editGroupDlg"
      show-parent
      :title="$t('pages.fixPlanGroup')"
      :group-tree-data="treeData"
      :add-func="createData"
      :update-func="updateData"
      :delete-func="deleteGroup"
      :move-func="moveGroupToOther"
      :edit-valid-func="getGroupByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="updateGroupForm ? $t('text.editInfo', { info: this.$t('pages.fixPlanGroup') }) : $t('text.deleteInfo', { info: this.$t('pages.fixPlan') })"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')">
        <tree-select :data="treeData[0].children" :checked-keys="checkedKeys2" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>

  </div>
</template>

<script>
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import request from '@/utils/request'
import { findNodeLabel } from '@/utils/tree'
import {
  createData, updateData, deleteGroup, deleteGroupAndData, moveGroupToOther,
  updateFixPlan, deleteFixPlan, getGroupByName, getFixPlanPage,
  countChildByGroupId, getTreeNode, exportExcel, batchUpdateGroup, batchUpdateAllGroup, addFixPlan, getByName
} from '@/api/system/terminalManage/fixPlan'

export default {
  name: 'FixPlanLibrary',
  components: { EditGroupDlg, DeleteGroupDlg, ExportDlg, ImportDlg, BatchEditPageDlg },
  data() {
    return {
      showTree: true,
      deleteable: false,
      selectTreeId: null,
      checkedKeys: [],
      checkedKeys2: [],
      treeNodeType: 'G',
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('route.fixPlanLibrary'), parentId: '', children: [] }],
      formTreeData: [],
      dialogFormVisible1: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.updateFixPlan'),
        create: this.$t('pages.addFixPlan'),
        update1: this.$t('pages.updateFixPlan'),
        create1: this.$t('pages.addFixPlan'),
        batchCreate: this.$t('pages.addFixPlan'),
        delete: this.$t('pages.deleteFixPlan')
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        content: '',
        groupId: '0'
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        content: [{ required: true, trigger: 'blur', message: this.$t('pages.fixPlanContentNotNull') }]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      colModel: [
        { prop: 'name', label: 'fixPlanName', width: '110', sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '110', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'content', label: 'content', width: '110', sort: 'custom' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      loading: false,
      title: this.$t('pages.fixPlan'),
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      deleteGroupTitle: this.i18nConcatText(this.$t('pages.fixPlanGroup'), 'delete')
    }
  },
  computed: {
    gridTable() {
      return this.$refs['fixPlanTable']
    },
    typeTree: function() {
      return this.$refs['typeTree']
    }
  },
  created() {
    this.resetTemp()
    this.loadGroupTree()
  },
  activated() {
    this.loadGroupTree()
    this.gridTable.execRowDataApi()
  },
  methods: {
    createData,
    updateData,
    deleteGroup,
    getGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: this.treeNodeType + data.parentId
      }
    },
    createNode(data) {
      this.typeTree.addNode(this.dataToTreeNode(data))
      this.loadGroupTree()
    },
    updateNode(data) {
      this.typeTree.updateNode(this.dataToTreeNode(data))
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    removeGroupEnd(dataId) {
      const nodeData = this.typeTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.typeTree.removeNode([nodeData.id])
      }
      this.selectTreeId = this.selectTreeId == dataId ? null : this.selectTreeId
    },
    moveGroupEnd() {
      this.gridTable.execRowDataApi()
    },
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        this.selectTreeId = 0
      }
      this.query.groupId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      return getFixPlanPage(newOption)
    },
    handleCreate2() {
      this.dialogStatus = 'batchCreate'
      this.handleCreate()
    },
    handleCreate() {
      this.resetTemp()
      const selectNode = this.typeTree.$refs['tree'].getCurrentNode()
      if (selectNode != null) {
        this.temp.groupId = selectNode.dataId
      } else {
        this.temp.groupId = '0'
      }
      this.formTreeData = JSON.parse(JSON.stringify(this.loadTypeTreeExceptRoot()))
      this.dialogFormVisible1 = true
      this.$nextTick(() => {
        if (selectNode != null) {
          this.checkedKeys = [selectNode.dataId]
        } else {
          this.checkedKeys = [0]
        }
        this.$refs['dataForm2'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.formTreeData = JSON.parse(JSON.stringify(this.loadTypeTreeExceptRoot()))
      this.dialogStatus = 'update1'
      this.dialogFormVisible1 = true
      this.$nextTick(() => {
        this.checkedKeys = [row.groupId]
        this.$refs['dataForm2'].clearValidate()
      })
    },
    handleCreateNode(nodeData) {
      this.$refs['editGroupDlg'].handleCreate(nodeData.dataId)
    },
    handleUpdateNode: function(nodeData) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: nodeData.dataId,
        name: nodeData.label,
        parentId: nodeData.parentId.replace('G', '')
      })
    },
    handleMoving() {
      // this.formTreeData = this.loadTypeTreeExceptRoot()
      // this.$refs['editGroupDlg'].handleMove(this.gridTable.getSelectedIds())
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys2 = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleRemove() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    removeNode(nodeData) {
      countChildByGroupId(nodeData.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(nodeData.dataId)
      })
    },
    createData1() {
      this.$refs['dataForm2'].validate(async valid => {
        if (valid) {
          this.loading = true;
          this.temp.groupName = findNodeLabel(this.formTreeData, this.temp.groupId, 'dataId')
          addFixPlan(this.temp).then((res) => {
            this.loading = false;
            this.dialogFormVisible1 = false
            this.gridTable.execRowDataApi(this.query)
            if (res.data == 'success') {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: this.$t('text.warning'),
                message: res.data,
                type: 'error',
                duration: 5000
              })
            }
            this.loadGroupTree()
          }).catch(() => {
            this.loading = false;
          })
        }
      })
    },
    updateData1() {
      this.$refs['dataForm2'].validate(async valid => {
        if (valid) {
          this.loading = true;
          this.temp.groupName_new = findNodeLabel(this.formTreeData, this.temp.groupId, 'dataId')
          updateFixPlan(this.temp).then((res) => {
            this.loading = false;
            this.dialogFormVisible1 = false
            this.gridTable.execRowDataApi(this.query)
            if (res.data == 'success') {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.editSuccess'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: this.$t('text.warning'),
                message: res.data,
                type: 'error',
                duration: 5000
              })
            }
            this.loadGroupTree()
          }).catch(() => {
            this.loading = false;
          })
        }
      })
    },
    loadTypeTreeExceptRoot() {
      return this.treeData[0].children
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleCreateNode(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleUpdateNode(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    resetQuery() {
      this.query = { // 查询条件
        page: 1,
        groupId: null
      }
    },
    treeNodeClick: function(data, node, element) {
      const selectNode = this.typeTree.$refs['tree'].getCurrentNode()
      this.selectTreeId = selectNode.dataId
      this.resetQuery()
      const options = Object.assign(this.query, { page: 1, groupId: this.selectTreeId })
      this.gridTable.execRowDataApi(options)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    parentIdChange(data) {
      this.temp.groupId = data
      this.checkedKeys.splice(0, this.checkedKeys.length, data)
    },
    selectFirstNode: function() {
      this.typeTree.$refs['tree'].setCurrentKey(this.treeNodeType + '0')
      this.selectTreeId = '0'
    },
    loadGroupTree: function() {
      getTreeNode().then(respond => {
        this.treeData[0].children = respond.data
        this.formTreeData = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
        this.$nextTick(() => {
          this.typeTree.selectCurrentNode(this.treeNodeType + (this.selectTreeId || '0'))
        })
      })
    },
    refreshTableData() {
      this.resetQuery()
      this.gridTable.execRowDataApi(this.query)
    },
    nameValidator(rule, value, callback) {
      if (value.trim().length === 0) {
        callback(new Error(this.$t('pages.nameNotNull')))
        return;
      }
      getByName(value).then(respond => {
        const strategy = respond.data
        if (!strategy) {
          callback()
        } else if (this.dialogStatus === 'update1' && strategy.id === this.temp.id) {
          callback()
        } else {
          callback(new Error(this.$t('pages.alreadyExistsInfo', { info: strategy.name })))
        }
      })
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.typeTree.findNode(this.treeData, dataId, 'dataId')
      if (nodeData) {
        this.typeTree.setCurrentKey(nodeData.id)
        this.treeNodeClick()
      }
    },
    exportFunc(formData, opts) {
      return exportExcel({
        ids: formData.type === 1 ? formData.dataIds.join(',') : null,
        groupId: formData.type === 2 ? formData.groupId : null,
        groupName: formData.type === 2 ? formData.groupName : null
      }, opts)
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    upload(data) {
      return request.post('/fixPlan/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc(groupId) {
      this.selectTreeId = groupId ? String(groupId) : null
      this.loadGroupTree()
    },
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return this.getGroupNameByDataId(this.treeData, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleDrag() {
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveFixPlanData(params, callback)
      } else {
        this.deleteFixPlanData(params, callback)
      }
    },
    deleteFixPlanData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteFixPlan(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    moveFixPlanData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    handleMoreClick(type) {
      switch (type) {
        case 1:
          this.handleImport()
          break
        case 2:
          this.$refs.defaultSelectDlg.show()
          break
        default:
          break
      }
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    }
  }
}

</script>
