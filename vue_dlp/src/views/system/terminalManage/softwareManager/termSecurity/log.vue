<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="1" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.risk" :value="query.risk">
          <span>{{ $t('table.riskLevel') }}：</span>
          <el-select v-model="query.risk" clearable is-filter :placeholder="$t('pages.all')" style="width: 180px;">
            <el-option :label="$t('text.health')" :value="0"></el-option>
            <el-option :label="$t('text.lowRisk')" :value="1"></el-option>
            <el-option :label="$t('text.mediumRisk')" :value="2"></el-option>
            <el-option :label="$t('text.highRisk')" :value="3"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'476'" :request="exportFunc"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'477'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />

      <log-details ref="logDetails"></log-details>
    </div>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { getModuleInfoByProdId } from '@/api/system/terminalManage/moduleConfig'
import { listTerminal } from '@/api/system/terminalManage/terminal'
import { getPage, exportTermSecurityDetectionLog, deleteLog } from '@/api/system/terminalManage/termSecurityLog';
import LogDetails from './logDetails'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
export default {
  name: 'TermSecurityLog',
  components: { LogDetails },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'detectionTime', width: '150', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'risk', label: 'riskLevel', width: '150', formatter: this.riskFormatter, colorFormatter: this.riskColorFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('475'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        createDate: '',
        startDate: '',
        endDate: '',
        risk: null,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      deleteable: false,
      selectedData: [],
      queryVideoMethod: undefined,
      groupTreeSelectData: [],
      rowDetail: {},
      moduleNameMap: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    this.listModule()
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    async listModule() {
      const productId = 511
      await getModuleInfoByProdId(productId).then(respond => {
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(moduleInfo => {
            if (moduleInfo.osGroup == 'win') {
              this.moduleNameMap[moduleInfo.moduleId] = moduleInfo.moduleName
            }
          })
        }
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    submitEnd() {
      this.gridTable.execRowDataApi()
    },
    initGroupTreeNode() {
      getDeptTreeFromCache().then(respond => {
        this.groupTreeSelectData = respond.data[0].children
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selectedData = []
      this.deleteable = rowDatas && rowDatas.length > 0
      rowDatas.forEach(data => {
        const rowBean = {
          id: data.termId
        }
        this.selectedData.push(rowBean)
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery);
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportTermSecurityDetectionLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.$refs.logDetails.showData(row, 2);
    },
    riskColorFormatter(row, data) {
      let result = ''
      if (row.risk !== undefined && row.risk !== null) {
        switch (row.risk) {
          case 0: result = '#2196F3'; break;
          case 1: result = '#4CAF50'; break;
          case 2: result = '#F88E03'; break;
          case 3: result = '#F44336'; break;
        }
      }
      return result;
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        if (toDelete.length > 0) {
          deleteLog(toDelete).then(respond => {
            this.gridTable.deleteRowData(toDelete)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      }).catch(() => {})
    },
    afterLoad(rowData) {
      this.termsInfo = []
      const termNameMap = new Map()
      const termIds = rowData.map(data => {
        termNameMap.set(data.termId, data.termName)
        return data.termId
      }).join()

      if (termIds) {
        listTerminal({ ids: termIds }).then(res => {
          if (res.data) {
            res.data.forEach(item => {
              const { id, groupIds } = item
              const name = termNameMap.get(id)
              const termsInfo = { id, name, groupIds }
              this.termsInfo.push(termsInfo)
            })
          }
        })
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '475')
      rowData = [{ id: 1 }]
      return rowData;
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    riskFormatter(row, data) {
      let result = ''
      if (row.risk !== undefined && row.risk !== null) {
        switch (row.risk) {
          case 0: result = this.$t('text.health'); break;
          case 1: result = this.$t('text.lowRisk'); break;
          case 2: result = this.$t('text.mediumRisk'); break;
          case 3: result = this.$t('text.highRisk'); break;
        }
      }
      return result;
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 180px;
}
</style>
