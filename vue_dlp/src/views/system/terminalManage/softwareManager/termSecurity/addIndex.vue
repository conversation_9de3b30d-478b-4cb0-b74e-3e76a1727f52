<template>
  <div class="serverInfo">
    <div class="table-container">
      <Form ref="dataForm" class="server-form" :model="temp" :rules="rules" label-position="right" :label-width="'100px'">
        <el-card style="position: relative; height: 100%; overflow: auto">
          <div slot="header">
            <span>{{ title }}</span>
          </div>
          <div style="height: 100%; overflow: auto">
            <el-tabs v-model="activeName" class="zTab" @tab-click="activeNameChange">
              <el-tab-pane name="0" :label="$t('pages.stgBaseInfo')" style="overflow: auto; height: 100%">
                <el-divider content-position="left" class="first-divider">{{ $t('pages.termSecurityBaseSettings') }}</el-divider>
                <div style="margin:0 10px; display: flex">
                  <stg-target-form-item
                    ref="formItem"
                    class="stgTargetType"
                    :stg-code="246"
                    :form-data="temp"
                    :is-disabled="!formable"
                    :is-disabled-type="operator !== 'create'"
                    :strategy-def-type="strategyDefType"
                  />
                  <FormItem v-if="!strategyDefType" :label="$t('pages.osTypeText')" label-width="120px" class="required" prop="osType">
                    <el-select v-model="temp.osType" :disabled="!formable" class="targetType">
                      <el-option :value="1" label="Windows">Windows</el-option>
                      <el-option v-if="false" :value="2" label="Linux">Linux</el-option>
                      <el-option v-if="false" :value="3" label="Mac">Mac</el-option>
                      <el-option v-if="false" :value="4" label="移动端">移动端</el-option>
                    </el-select>
                  </FormItem>
                </div>

                <el-row style="margin: 0 10px">
                  <el-col :span="24">
                    <FormItem v-if="!strategyDefType" :label="$t('table.stgName')" prop="name">
                      <el-input v-model="temp.name" v-trim style="width: 700px" class="showInputLimit" :disabled="!formable" maxlength="100" show-word-limit />
                    </FormItem>
                    <div v-else style="display: flex">
                      <FormItem :label="$t('table.stgName')" prop="name">
                        <el-input v-model="temp.name" v-trim style="width: 390px" class="showInputLimit" :disabled="!formable" maxlength="100" show-word-limit />
                      </FormItem>
                      <FormItem :label="$t('pages.osTypeText')" label-width="120px" class="required" prop="osType">
                        <el-select v-model="temp.osType" :disabled="!formable" class="targetType">
                          <el-option :value="1" label="Windows">Windows</el-option>
                          <el-option v-if="false" :value="2" label="Linux">Linux</el-option>
                          <el-option v-if="false" :value="3" label="Mac">Mac</el-option>
                          <el-option v-if="false" :value="4" label="移动端">移动端</el-option>
                        </el-select>
                      </FormItem>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <FormItem :label="$t('text.remark')">
                      <el-input v-model="temp.remark" v-trim style="width: 700px" class="showInputLimit" :disabled="!formable" maxlength="100" show-word-limit />
                    </FormItem>
                  </el-col>
                  <el-col v-if="!strategyDefType" :span="24" >
                    <FormItem :label="$t('components.enable')">
                      <el-switch v-model="temp.active" :disabled="!formable" :active-value="true" :inactive-value="false"/>
                    </FormItem>
                  </el-col>
                </el-row>

                <el-divider content-position="left" class="first-divider">{{ $t('pages.termSecurityAdvancedSettings') }}</el-divider>
                <div style="margin: 20px">
                  <FormItem label-width="130px" :label="$t('pages.safetyDetectionCycle')" :extra-width="{ en: 65 }" prop="timeType">
                    <div style="display: flex; align-items: center; height: 40px">
                      <span style="height: 32px; align-items: center; margin-right: 5px; display: flex">
                        <el-tooltip>
                          <template slot="content">
                            <div>{{ $t('pages.safetyDetectionHit1') }}</div>
                            <div>{{ $t('pages.safetyDetectionHit2') }}</div>
                          </template>
                          <i class="el-icon-info"/>
                        </el-tooltip>
                      </span>
                      <el-select v-model="timeType" :disabled="!formable" style="width: 180px" class="targetType" @change="timeChange">
                        <el-option v-for="(item, index) in timeTypeArray" :key="index" :value="item.value" :label="item.label">{{ item.label }}</el-option>
                      </el-select>
                      <FormItem label-width="0" prop="selfTime" style="margin-bottom: 0; display: flex;" class="targetType">
                        <el-input-number v-if="timeType === -2" v-model="selfTime" step-strictly :disabled="!formable" :placeholder="$t('pages.inputIntervalDay')" :controls="false" :min="1" style="width: 180px" :max="9999" @change="selfTimeChange"></el-input-number>
                      </FormItem>
                    </div>
                  </FormItem>

                  <el-row>
                    <el-col v-if="false" :span="24">
                      <FormItem label-width="130px" :label="$t('pages.powerOnDetection')" :extra-width="{ en: 65 }">
                        <div style="display: flex">
                          <div style="height: 32px; align-items: center; margin-right: 5px">
                            <el-tooltip>
                              <div slot="content">
                                {{ $t('pages.powerOnDetectionHit') }}
                              </div>
                              <i class="el-icon-info"/>
                            </el-tooltip>
                          </div>
                          <el-radio-group v-model="temp.bootCheck" :disabled="!formable" style="margin-top: 5px">
                            <el-radio :key="0" :label="0">{{ $t('text.close') }}</el-radio>
                            <el-radio :key="1" :label="1">{{ $t('text.open') }}</el-radio>
                          </el-radio-group>
                        </div>
                      </FormItem>
                    </el-col>
                    <el-col v-if="false" :span="24">
                      <FormItem label-width="130px" :label="$t('pages.termSelfTest')" :extra-width="{ en: 65 }">
                        <div style="display: flex">
                          <div style="height: 32px; align-items: center; margin-right: 5px">
                            <el-tooltip>
                              <div slot="content">
                                {{ $t('pages.termSelfTestHit') }}
                              </div>
                              <i class="el-icon-info"/>
                            </el-tooltip>
                          </div>
                          <el-radio-group v-model="temp.termCheck" :disabled="!formable" style="margin-top: 5px">
                            <el-radio :key="0" :label="0">{{ $t('text.close') }}</el-radio>
                            <el-radio :key="1" :label="1">{{ $t('text.open') }}</el-radio>
                          </el-radio-group>
                        </div>
                      </FormItem>
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>

              <el-tab-pane name="1" style="overflow: auto;">
                <span slot="label">
                  {{ $t('pages.softSecurityDetection') + configuredNum(1) }}
                </span>
                <el-tabs v-model="softSecurityActiveName" tab-position="left" class="sunTab">
                  <el-tab-pane name="all" :disabled="true">
                    <span slot="label" style="float: left">
                      <el-checkbox v-model="softwareSecurityCheck" :indeterminate="softIndeterminate" :disabled="!formable" @change="handleAllCheckChange(1)"></el-checkbox>
                      {{ $t('pages.all') }}
                    </span>
                  </el-tab-pane>
                  <el-tab-pane :label="$t('pages.killSoftInspect')" name="virus" style="overflow:auto; height: 100%;">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="virusCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.killSoftInspect') }}
                    </span>
                    <div style="padding: 10px 20px 5px 20px;height: 100%">
                      <div style="margin-bottom: 5px; font-size: 14px;height: 30px; display: flex;align-items: center">{{ $t('pages.killSoftInspectMsg') }}</div>
                      <div>
                        <el-button size="small" :disabled="!formable" @click="killAddAppClick">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="small" :disabled="!killSelected || !formable" @click="killDeleteAppClick">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 100px)!important;">
                        <grid-table
                          ref="killVirusList"
                          node-key="name"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="killVirusSoftWareColModel"
                          :row-datas="virusInfoList"
                          @selectionChangeEnd="killSelectedFun"
                        />
                      </div>

                      <FormItem prop="virus"></FormItem>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'软件必须安装检查'" name="installSoft" style="overflow:auto;height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="installSoftCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.softwareMustInstallCheck') }}
                    </span>
                    <div style="padding: 10px 20px 5px 20px;height: 100%">
                      <div style="margin-bottom: 5px">
                        <el-radio-group v-model="softWareInstallTemp.installType" :disabled="!formable">
                          <el-radio :label="0">{{ $t('pages.mustInstallAllSoftware') }}</el-radio>
                          <el-radio :label="1">{{ $t('pages.justNeedInstallOneSoftware') }}</el-radio>
                        </el-radio-group>
                      </div>
                      <div>
                        <el-button size="small" :disabled="!formable" @click="installAddAppClick(1)">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="mini" :disabled="!formable" @click="handleSoftware('install')">{{ $t('pages.termSecuritySoftwareAssetImport') }}</el-button>
                        <el-button size="small" :disabled="!installSelected || !formable" @click="installDeleteAppClick(1)">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>

                      <div class="table-container" style="height: calc(100% - 135px)!important;">
                        <grid-table
                          ref="softWareList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="softWareInstallColModel"
                          :row-datas="softWareInstallTemp.softWareList"
                          @selectionChangeEnd="installSelectedFun"
                        />
                      </div>

                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.termSecurityMustInstallFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityMustInstallFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="softWareInstallTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ softWareInstallTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ softWareInstallTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="softWareInstallTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(softWareInstallTemp.fixPlan, '软件必须安装检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="softWareInstallTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(softWareInstallTemp.fixPlan, '软件必须安装检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'软件禁止安装检查'" name="limitInstallSoft" style="overflow:auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="limitInstallSoftCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.softwareNotInstallCheck') }}
                    </span>
                    <div style="padding: 10px 20px 10px 20px; height: 100%">
                      <div style="margin-bottom: 5px; font-size: 14px;height: 30px; display: flex;align-items: center">
                        {{ $t('pages.termSecurityNotInstallSoftware') }}
                      </div>
                      <div>
                        <el-button size="small" :disabled="!formable" @click="installAddAppClick(2)">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="mini" :disabled="!formable" @click="handleSoftware('limitInstall')">{{ $t('pages.termSecuritySoftwareAssetImport') }}</el-button>
                        <el-button size="small" :disabled="!limitInstallSelected || !formable" @click="installDeleteAppClick(2)">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 130px)!important;">
                        <grid-table
                          ref="limitSoftWareList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="softWareLimitInstallColModel"
                          :row-datas="softWareInstallTemp.limitSoftWareList"
                          @selectionChangeEnd="limitInstallSelectedFun"
                        />
                      </div>

                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.termSecurityNotInstallFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityNotInstallFixPlanTip') }}<br>
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="softWareInstallTemp.limitFixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ softWareInstallTemp.limitFixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ softWareInstallTemp.limitFixPlan }}</span>
                        </el-tooltip>
                        <span v-if="softWareInstallTemp.limitFixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(softWareInstallTemp.limitFixPlan, '软件禁止安装检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="softWareInstallTemp.limitFixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(softWareInstallTemp.limitFixPlan, '软件禁止安装检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'服务白名单检查'" name="whiteServer" style="overflow:auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="whiteServerCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.termSecurityServerWhiteCheck') }}
                    </span>
                    <div style="padding: 10px 20px 5px 20px; height: 100%">
                      <div style="margin-bottom: 5px">
                        <el-radio-group v-model="serviceTemp.runType" :disabled="!formable">
                          <el-radio :label="0">{{ $t('pages.termSecurityMustRunService') }}</el-radio>
                          <el-radio :label="1">{{ $t('pages.termSecurityOnlyOneService') }}</el-radio>
                        </el-radio-group>
                      </div>
                      <div>
                        <el-button size="small" :disabled="!formable" @click="addServiceClick(1)">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="small" :disabled="!serviceSelected || !formable" @click="serviceDeleteAppClick(1)">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 135px)!important;">
                        <grid-table
                          ref="serviceList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="serviceColModel"
                          :row-datas="serviceTemp.serviceList"
                          @selectionChangeEnd="serviceSelectedFun"
                        />
                      </div>
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.termSecurityServerWhiteFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityServerWhiteFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="serviceTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ serviceTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ serviceTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="serviceTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(serviceTemp.fixPlan, '服务白名单检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="serviceTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(serviceTemp.fixPlan, '服务白名单检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'服务黑名单检查'" name="blackServer" style="overflow:auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="blackServerCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.termSecurityServerBlackCheck') }}
                    </span>
                    <div style="padding: 10px 20px 10px 20px; height: 100%">
                      <div style="margin-bottom: 5px; font-size: 14px;height: 30px; display: flex;align-items: center">
                        {{ $t('pages.termSecurityNotRunService') }}
                      </div>
                      <div>
                        <el-button size="small" :disabled="!formable" @click="addServiceClick(2)">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="small" :disabled="!limitServiceSelected || !formable" @click="serviceDeleteAppClick(2)">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 130px)!important;">
                        <grid-table
                          ref="limitServiceList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="limitServiceColModel"
                          :row-datas="serviceTemp.limitServiceList"
                          @selectionChangeEnd="limitServiceSelectedFun"
                        />
                      </div>
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.termSecurityServerBlackFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityServerBlackFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="serviceTemp.limitFixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ serviceTemp.limitFixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ serviceTemp.limitFixPlan }}</span>
                        </el-tooltip>
                        <span v-if="serviceTemp.limitFixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(serviceTemp.limitFixPlan, '服务黑名单检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="serviceTemp.limitFixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(serviceTemp.limitFixPlan, '服务黑名单检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'进程必须运行检查'" name="whiteProcess" style="overflow:auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="whiteProcessCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.termSecurityProcessMustRunCheck') }}
                    </span>
                    <div style="padding: 10px 20px 5px 20px; height: 100%">
                      <div style="margin-bottom: 5px">
                        <el-radio-group v-model="processTemp.runType" :disabled="!formable">
                          <el-radio :label="0">{{ $t('pages.termSecurityMustRunProcess') }}</el-radio>
                          <el-radio :label="1">{{ $t('pages.termSecurityOnlyOneProcess') }}</el-radio>
                        </el-radio-group>
                      </div>
                      <div>
                        <el-button size="small" :disabled="!formable" @click="addProcessClick(1)">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="small" :disabled="!formable" @click="appImport('whiteProcess')">{{ $t('button.applicationLibraryImport') }}</el-button>
                        <el-button size="small" :disabled="!processSelected || !formable" @click="processDeleteAppClick(1)">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 135px)!important;">
                        <grid-table
                          ref="processList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="processColModel"
                          :row-datas="processTemp.processList"
                          @selectionChangeEnd="processSelectedFun"
                        />
                      </div>
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.termSecurityMustRunProcessFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityMustRunProcessFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="processTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ processTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ processTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="processTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(processTemp.fixPlan, '进程必须运行检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="processTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(processTemp.fixPlan, '进程必须运行检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'进程禁止运行检查'" name="blackProcess" style="overflow:auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="blackProcessCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.termSecurityProcessNotRunCheck') }}
                    </span>
                    <div style="padding: 10px 20px 10px 20px; height: 100%">
                      <div style="margin-bottom: 5px; font-size: 14px;height: 30px; display: flex;align-items: center">
                        {{ $t('pages.termSecurityNotRunProcess') }}
                      </div>
                      <div>
                        <el-button size="small" :disabled="!formable" @click="addProcessClick(2)">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="small" :disabled="!formable" @click="appImport('blackProcess')">{{ $t('button.applicationLibraryImport') }}</el-button>
                        <el-button size="small" :disabled="!limitProcessSelected || !formable" @click="processDeleteAppClick(2)">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 130px)!important;">
                        <grid-table
                          ref="limitProcessList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="limitProcessColModel"
                          :row-datas="processTemp.limitProcessList"
                          @selectionChangeEnd="limitProcessSelectedFun"
                        />
                      </div>
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.termSecurityNotRunProcessFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityNotRunProcessFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="processTemp.limitFixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ processTemp.limitFixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ processTemp.limitFixPlan }}</span>
                        </el-tooltip>
                        <span v-if="processTemp.limitFixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(processTemp.limitFixPlan, '进程禁止运行检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="processTemp.limitFixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(processTemp.limitFixPlan, '进程禁止运行检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'文件检查'" name="fileScan" style="overflow:auto; height: 100%;">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="fileScanCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.termSecurityFileCheck') }}
                    </span>
                    <div style="padding: 10px 20px 10px 20px;height: 100%">
                      <div style="margin-bottom: 5px; font-size: 14px;height: 30px; display: flex;align-items: center">
                        {{ $t('pages.termSecurityFileCheckMessage') }}
                      </div>
                      <div style="margin: 0">
                        <el-button size="small" :disabled="!formable" @click="fileClick">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="small" :disabled="!fileSelected || !formable" @click="deleteFileClick">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 130px)!important;">
                        <grid-table
                          ref="fileInfoList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="fileColModel"
                          :row-datas="fileScanTemp.fileInfoList"
                          @selectionChangeEnd="fileSelectedFun"
                        />
                      </div>
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fileFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityFileCheckFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="fileScanTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ fileScanTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ fileScanTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="fileScanTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(fileScanTemp.fixPlan, '文件检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="fileScanTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(fileScanTemp.fixPlan, '文件检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane :label="'注册表检查'" name="regScan" style="overflow:auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="regScanCheck" :disabled="!formable" @change="handleSingeCheckChange(1)"></el-checkbox>
                      {{ $t('pages.termSecurityRegistryCheck') }}
                    </span>
                    <div style="padding: 10px 20px 10px 20px; height: 100%">
                      <div style="margin-bottom: 5px; font-size: 14px;height: 30px; display: flex;align-items: center">
                        {{ $t('pages.termSecurityRegistryCheckMessage') }}
                      </div>
                      <div style="margin: 0">
                        <el-button size="small" :disabled="!formable" @click="registryClick">
                          {{ $t('button.insert') }}
                        </el-button>
                        <el-button size="small" :disabled="!regSelected || !formable" @click="deleteRegClick">
                          {{ $t('button.delete') }}
                        </el-button>
                      </div>
                      <div class="table-container" style="height: calc(100% - 130px)!important;">
                        <grid-table
                          ref="regInfoList"
                          :multi-select="formable"
                          :show-pager="false"
                          :col-model="registryColModel"
                          :row-datas="regScanTemp.regInfoList"
                          @selectionChangeEnd="regSelectedFun"
                        />
                      </div>

                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.registryFixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityRegistryCheckFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="regScanTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ regScanTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ regScanTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="regScanTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(regScanTemp.fixPlan, '注册表检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="regScanTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(regScanTemp.fixPlan, '注册表检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>

              <el-tab-pane :label="'主机安全检测'" name="2" style="overflow: auto; height: 100%">
                <span slot="label">
                  {{ $t('pages.hostSecurityDetection') }}{{ configuredNum(2) }}
                </span>
                <el-tabs v-model="hostActiveName" tab-position="left" class="sunTab">
                  <el-tab-pane name="all" :disabled="true">
                    <span slot="label" style="float: left">
                      <el-checkbox v-model="hostSecurityCheck" :indeterminate="hostIndeterminate" :disabled="!formable" @change="handleAllCheckChange(2)"></el-checkbox>
                      {{ $t('pages.all') }}
                    </span>
                  </el-tab-pane>
                  <el-tab-pane label="操作系统检查" name="host" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="hostCheck" :disabled="!formable" @change="handleSingeCheckChange(2)"></el-checkbox>
                      {{ $t('pages.termSecurityHostCheck') }}
                    </span>
                    <div style="margin: 20px;">
                      <div style="margin-bottom: 10px">{{ $t('pages.termSecurityHostCheckMessage') }}</div>
                      <div style="margin-left: 20px">
                        <el-checkbox v-model="osTypeCheck1" :disabled="!formable" @change="osTypeCheckChange(1)">{{ $t('pages.termSecurityHostCheckWay1') }}</el-checkbox>
                        <div style="margin: 10px 0 0 30px">
                          {{ $t('pages.termSecurityHostCheckWindowsFigure') }}
                          <el-checkbox v-model="osTypeCheckAll1" :disabled="!formable || !osTypeCheck1" :indeterminate="osTypeIndeterminate1" @change="osTypeCheckAllClick1">{{ $t('pages.all') }}</el-checkbox>
                        </div>
                        <el-checkbox-group v-model="windowsOsTypeNames" :disabled="!formable" style="line-height: 0" @change="handleOsTypeCheckChange1">
                          <el-row>
                            <el-col v-for="(item, index) in windowsOsTypes" :key="index" :span="8">
                              <el-checkbox :key="item.os" :label="item.os" :disabled="!osTypeCheck1" style="margin: 7px 0 7px 20%" :value="item.os">{{ item.label }}</el-checkbox>
                            </el-col>
                          </el-row>
                        </el-checkbox-group>

                        <div style="margin: 10px 0 0 30px">
                          {{ $t('pages.termSecurityHostCheckWindowsServer') }}
                          <el-checkbox v-model="osTypeCheckAll2" :disabled="!formable || !osTypeCheck1" :indeterminate="osTypeIndeterminate2" @change="osTypeCheckAllClick2">{{ $t('pages.all') }}</el-checkbox>
                        </div>
                        <el-checkbox-group v-model="windowsServerOsTypeNames" :disabled="!formable" style="line-height: 0" @change="handleOsTypeCheckChange2">
                          <el-row>
                            <el-col v-for="(item, index) in windowsServerOsTypes" :key="index" :span="8">
                              <el-checkbox :key="item.os" :label="item.os" :disabled="!osTypeCheck1" style="margin: 7px 0 7px 20%" :value="item.os">{{ item.label }}</el-checkbox>
                            </el-col>
                          </el-row>
                        </el-checkbox-group>

                        <i18n path="pages.termSecurityHostCheckWay2">
                          <el-checkbox slot="check" v-model="osTypeCheck2" :disabled="!formable" style="margin-right: 5px" @change="osTypeCheckChange(2)"></el-checkbox>
                          <el-select slot="operator" v-model="allowOsTypeTemp.operator" :disabled="!osTypeCheck2 || !formable" size="mini" style="width: 130px;">
                            <el-option v-for="item in osTypeOperators" :key="item.value" :value="item.value" :label="item.label">{{ item.label }}</el-option>
                          </el-select>
                          <el-select slot="windowsFigure" v-model="allowOsTypeTemp.versionName" style="margin-right: 5px" :disabled="!osTypeCheck2 || !formable" size="mini" clearable>
                            <el-option v-for="item in windowsOsTypes" :key="item.os" :value="item.os">{{ item.os }}</el-option>
                          </el-select>
                          <el-select slot="windowsServer" v-model="allowOsTypeTemp.serVersionName" :disabled="!osTypeCheck2 || !formable" size="mini" style="width: 200px; margin-left: 5px" clearable>
                            <el-option v-for="item in windowsServerOsTypes" :key="item.os" :value="item.os">{{ item.os }}</el-option>
                          </el-select>
                        </i18n>
                        <span v-if="osTypeError" style="color: red">{{ $t('pages.termSecurityHostCheckError1') }}</span>
                      </div>

                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityHostCheckFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="allowOsTypeTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ allowOsTypeTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ allowOsTypeTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="allowOsTypeTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(allowOsTypeTemp.fixPlan, '操作系统检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="allowOsTypeTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(allowOsTypeTemp.fixPlan, '操作系统检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin: 10px 0;" class="explainStyle">
                        {{ $t('pages.termSecurityHostCheckInstruction1') }}<br>
                        {{ $t('pages.termSecurityHostCheckInstruction2') }}<br>
                        {{ $t('pages.termSecurityHostCheckInstruction3') }}
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="计算机名称规范检查" name="computerName" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="computerNameCheck" :disabled="!formable" @change="handleSingeCheckChange(2)"></el-checkbox>
                      {{ $t('pages.termSecurityComputerNameCheck') }}
                    </span>
                    <div style="margin: 10px 20px">
                      <el-divider content-position="left" class="first-divider">{{ $t('pages.termSecurityComputerNameRule') }}</el-divider>
                      <FormItem :label="$t('pages.termSecurityRegularExpression')" label-width="140px" :extra-width="{ en: 25 }" prop="regEx">
                        <el-autocomplete
                          v-model="computeNameTemp.regEx"
                          v-trim
                          :disabled="!formable"
                          class="showInputLimit"
                          :fetch-suggestions="computeNameRegExSearch"
                          :placeholder="$t('pages.regularExpressionMsg')"
                          style="width: 80%"
                          :maxlength="100"
                          show-word-limit
                          @select="regExHandleSelect"
                        ></el-autocomplete>
                      </FormItem>
                      <FormItem :label="$t('pages.termSecurityRegularDescription')" label-width="140px" :extra-width="{ en: 25 }" prop="regDes">
                        <el-autocomplete
                          v-model="computeNameTemp.regDes"
                          v-trim
                          :disabled="!formable"
                          class="showInputLimit"
                          :fetch-suggestions="computeNameRegDesSearch"
                          style="width: 80%"
                          :maxlength="100"
                          show-word-limit
                          @select="regDesHandleSelect"
                        ></el-autocomplete>
                      </FormItem>
                      <div style="margin-top: 20px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityComputerNameFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="computeNameTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ computeNameTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ computeNameTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="computeNameTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(computeNameTemp.fixPlan, '计算机名称规范检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="computeNameTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(computeNameTemp.fixPlan, '计算机名称规范检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <el-divider content-position="left" style="margin-top: 10px; font-size: 15px!important; font-weight: 300!important;" class="first-divider">{{ $t('pages.termSecurityComputerNameRuleExample') }}</el-divider>
                      <div>
                        <div style="margin-top: 5px">
                          {{ $t('pages.termSecurityComputerNameExpressionA') }}
                        </div>
                        <div style="margin-top: 5px">
                          {{ $t('pages.termSecurityComputerNameExpressionB') }}
                        </div>
                        <div style="margin-top: 5px">
                          {{ $t('pages.termSecurityComputerNameExpressionC') }}
                        </div>
                        <div style="margin-top: 5px">
                          {{ $t('pages.termSecurityComputerNameExpressionD') }}
                        </div>
                      </div>
                      <div style="margin-top: 20px" class="explainStyle">
                        {{ $t('pages.termSecurityComputerNameInstruction1') }}
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="磁盘使用空间检查" name="disk" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="diskCheck" :disabled="!formable" @change="handleSingeCheckChange(2)"></el-checkbox>
                      {{ $t('pages.termSecurityDiskCheck') }}
                    </span>
                    <div style="margin: 10px 20px">

                      <el-divider content-position="left" class="first-divider">
                        {{ $t('pages.termSecuritySysTemDisk') }}
                        <el-tooltip effect="dark" placement="top">
                          <div slot="content">
                            {{ $t('pages.termSecuritySysTemDiskTip') }}
                          </div>
                          <i class="el-icon-info"/>
                        </el-tooltip>
                      </el-divider>
                      <div style="margin-left: 20px">
                        <FormItem label-width="0" prop="sysDisk">
                          <el-checkbox v-model="sysDiskCheck" :disabled="!formable">
                            <i18n path="pages.termSecuritySysDiskMessage">
                              <el-input-number slot="sysDisk" v-model="diskDetectTemp.sysDisk" :disabled="!sysDiskCheck || !formable" :controls="false" step-strictly :step="1" :min="1" :max="100" style="width: 100px; margin: 0 5px"/>
                            </i18n>
                          </el-checkbox>
                        </FormItem>
                        <FormItem label-width="0" prop="sysSize">
                          <el-checkbox v-model="sysSizeCheck" :disabled="!formable">
                            <i18n path="pages.termSecuritySysSizeMessage">
                              <el-input-number slot="sysSize" v-model="diskDetectTemp.sysSize" :disabled="!sysSizeCheck || !formable" :controls="false" step-strictly :step="1" :min="1" :max="9999" style="width: 100px;  margin: 0 5px"/>
                            </i18n>
                          </el-checkbox>
                        </FormItem>
                      </div>

                      <el-divider content-position="left" class="first-divider">
                        {{ $t('pages.termSecurityTotalDisk') }}
                        <el-tooltip effect="dark" placement="top">
                          <div slot="content">
                            {{ $t('pages.termSecurityTotalDiskTip') }}
                          </div>
                          <i class="el-icon-info"/>
                        </el-tooltip>
                      </el-divider>
                      <div style="margin-left: 20px">
                        <FormItem label-width="0" prop="totalDisk">
                          <el-checkbox v-model="totalDiskCheck" :disabled="!formable">
                            <i18n path="pages.termSecurityTotalDiskMessage">
                              <el-input-number slot="totalDisk" v-model="diskDetectTemp.totalDisk" :disabled="!totalDiskCheck || !formable" :controls="false" step-strictly :step="1" :min="1" :max="100" style="width: 100px; margin: 0 5px"/>
                            </i18n>
                          </el-checkbox>
                        </FormItem>
                        <FormItem label-width="0" prop="totalSize">
                          <el-checkbox v-model="totalSizeCheck" :disabled="!formable">
                            <i18n path="pages.termSecurityTotalSizeMessage">
                              <el-input-number slot="totalSize" v-model="diskDetectTemp.totalSize" :disabled="!totalSizeCheck || !formable" :controls="false" step-strictly :step="1" :min="1" :max="9999" style="width: 100px; margin: 0 5px"/>
                            </i18n>
                          </el-checkbox>
                        </FormItem>
                      </div>

                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityDiskFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="diskDetectTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ diskDetectTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ diskDetectTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="diskDetectTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(diskDetectTemp.fixPlan, '磁盘使用空间检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="diskDetectTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(diskDetectTemp.fixPlan, '磁盘使用空间检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin-top: 10px;" class="explainStyle">
                        {{ $t('pages.termSecurityDiskInstruction1') }}
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="共享配置检查" name="shared" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="sharedCheck" :disabled="!formable" @change="handleSingeCheckChange(2)"></el-checkbox>
                      {{ $t('pages.termSecuritySharedCheck') }}
                    </span>
                    <div style="text-align: left; margin: 20px">
                      <el-row>
                        <el-checkbox v-model="shareConfigTemp.printer" :disabled="!formable" true-label="0" false-label="1">{{ $t('pages.termSecurityDetectionSharedPrinter') }}</el-checkbox>
                      </el-row>
                      <div style="margin-top: 10px">
                        <el-checkbox v-model="shareConfigTemp.shareFlag" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('pages.termSecuritySharedMessage') }}</el-checkbox>
                        <el-row style="margin-top: 5px">
                          <tag
                            :disabled="!formable"
                            :add-able="shareConfigTemp.shareFlag === 1 && formable"
                            :placeholder="$t('pages.termSecuritySharedPlaceholder')"
                            :list="shareConfigTemp.fileNameList"
                            min-height="45px"
                            :overflow-able="true"
                            :input-width="200"
                            border
                            overflow-height="45px"
                            max-height="150px"
                            style="width: 80%; margin: 0!important;"
                          />
                        </el-row>
                      </div>

                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecuritySharedFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="shareConfigTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ shareConfigTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ shareConfigTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="shareConfigTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(shareConfigTemp.fixPlan, '共享配置检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="shareConfigTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(shareConfigTemp.fixPlan, '共享配置检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin-top: 10px;" class="explainStyle">
                        {{ $t('pages.termSecuritySharedInstruction1') }}<br/>
                        {{ $t('pages.termSecuritySharedInstruction2') }}<br/>
                        {{ $t('pages.termSecuritySharedInstruction3') }}
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="来宾账户检查" name="guest" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="guestCheck" :disabled="!formable" @change="handleSingeCheckChange(2)"></el-checkbox>
                      {{ $t('pages.termSecurityGuestCheck') }}
                    </span>
                    <div style="margin: 20px 20px">
                      {{ $t('pages.termSecurityGuestMessage') }}
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityGuestFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="guestTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ guestTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ guestTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="guestTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(guestTemp.fixPlan, '来宾账户检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="guestTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(guestTemp.fixPlan, '来宾账户检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin-top: 10px" class="explainStyle">
                        {{ $t('pages.termSecurityGuestInstruction1') }}
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="计算机屏幕保护检查" name="screen" style="overflow: auto; height: 100%;">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="screenCheck" :disabled="!formable" @change="handleSingeCheckChange(2)"></el-checkbox>
                      {{ $t('pages.termSecurityScreenCheck') }}
                    </span>
                    <div style="margin: 10px 20px">
                      <div style="margin-right: 10px;">
                        {{ $t('pages.termSecurityScreenMessage1') }}
                      </div>
                      <div style=" margin-top: 10px">
                        <el-checkbox v-model="screenTemp.showLogin" :disabled="!formable" :true-label="1" :false-label="0" @change="screenCheckChange"><span>{{ $t('pages.termSecurityScreenMessage') }} </span></el-checkbox>
                      </div>
                      <FormItem label-width="0" prop="waitTime">
                        <el-checkbox v-model="screenTemp.timeCheck" :disabled="!formable">
                          <i18n path="pages.termSecurityScreenMessage2">
                            <el-input-number slot="waitTime" v-model="screenTemp.waitTime" :disabled="!screenTemp.timeCheck || !formable" step-strictly style="width: 60px;" :min="1" :max="99999" :controls="false"/>
                          </i18n>
                        </el-checkbox>
                      </FormItem>
                      <div>
                        <i18n path="pages.termSecurityScreenMessage3">
                          <el-button slot="computerSelf" type="text" style="padding: 0 3px;" :disabled="!formable || !hasPermission('C92')" size="mini" @click="screenToClick">{{ $t('route.PersonalizePolicy') }}</el-button>
                        </i18n>
                      </div>

                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityScreenFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="screenTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ screenTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ screenTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="screenTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(screenTemp.fixPlan, '计算机屏幕保护检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="screenTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(screenTemp.fixPlan, '计算机屏幕保护检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin-top: 10px;" class="explainStyle">
                        {{ $t('pages.termSecurityScreenInstruction1') }}<br>
                        {{ $t('pages.termSecurityScreenInstruction2') }}<br>
                        {{ $t('pages.termSecurityScreenInstruction3') }}
                      </div>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>

              <el-tab-pane :label="'网络安全防护检测'" name="3" style="overflow: auto; height: 100%">
                <span slot="label">
                  {{ $t('pages.netSecurityDetection') }}{{ configuredNum(3) }}
                </span>
                <el-tabs v-model="netActiveName" tab-position="left" class="sunTab">
                  <el-tab-pane name="all" :disabled="true">
                    <span slot="label" style="float: left">
                      <el-checkbox v-model="netSecurityCheck" :disabled="!formable" :indeterminate="netIndeterminate" @change="handleAllCheckChange(3)"></el-checkbox>
                      {{ $t('pages.all') }}
                    </span>
                  </el-tab-pane>
                  <el-tab-pane label="端口检查" name="port" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="portCheck" :disabled="!formable" @change="handleSingeCheckChange(3)"></el-checkbox>
                      {{ $t('pages.termSecurityPortCheck') }}
                    </span>
                    <div style="margin: 20px;">
                      <el-row>
                        <span>{{ $t('pages.termSecurityDisablePortCheck') }}</span>
                        <el-checkbox v-model="portCheckAll" :disabled="!formable" :indeterminate="portIndeterminate" @change="portCheckAllClick">{{ $t('pages.all') }}</el-checkbox>
                        <div style="line-height: 30px">
                          <el-checkbox-group v-model="portTemp.fixPorts" :disabled="!formable" @change="handlePortCheckChange">
                            <el-checkbox v-for="item in fixPortList" :key="item.port" :label="item.port">{{ item.label }}</el-checkbox>
                          </el-checkbox-group>
                        </div>

                      </el-row>
                      <div style="width: 100%; margin-top: 10px">
                        <span>{{ $t('pages.termSecurityPortMessage') }}</span>
                        <el-row style="margin-top: 5px">
                          <tag
                            ref="selfPorts"
                            :placeholder="$t('pages.termSecurityPort')"
                            :list="portTemp.selfPorts"
                            :disabled="!formable"
                            :add-able="formable"
                            :overflow-able="true"
                            :valid-rule="portValidRules"
                            :err-rule-aligning="true"
                            border
                            overflow-height="80px"
                            min-height="80px"
                            max-height="150px"
                            style="width: 80%"
                          ></tag>
                        </el-row>
                      </div>
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityPortFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="portTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ portTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ portTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="portTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(portTemp.fixPlan, '端口检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="portTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(portTemp.fixPlan, '端口检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin-top: 10px" class="explainStyle">
                        <el-row>
                          {{ $t('pages.termSecurityPortInstruction1') }}<br>
                          <span style="color: red">
                            {{ $t('pages.termSecurityPortInstruction2') }}
                          </span>
                        </el-row>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="域环境检查" name="domain" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="domainCheck" :disabled="!formable" @change="handleSingeCheckChange(3)"></el-checkbox>
                      {{ $t('pages.termSecurityDomainCheck') }}
                    </span>
                    <div style="padding: 20px;">
                      <div style="width: 100%;">
                        <span>{{ $t('pages.termSecurityDomainMessage') }}</span>
                        <el-row style="margin-top: 5px">
                          <tag
                            :placeholder="$t('pages.termSecurityDomainPlaceholder')"
                            :disabled="!formable"
                            :add-able="formable"
                            :list="domainTemp.domainNameList"
                            :overflow-able="true"
                            border
                            overflow-height="65px"
                            min-height="65px"
                            max-height="80px"
                            style="width: 80%"
                          ></tag>
                        </el-row>
                      </div>
                      <div style="width: 100%; margin-top: 10px">
                        <span>{{ $t('pages.termSecurityDomainWhiteMessage') }}</span>
                        <el-row style="margin-top: 5px">
                          <tag
                            ref="doMainPassAccountList"
                            :placeholder="$t('pages.termSecurityDomainWhiteBlackPlaceholder')"
                            :disabled="!formable"
                            :add-able="formable"
                            :list="domainTemp.passAccountList"
                            :valid-rule="passValidRule"
                            :overflow-able="true"
                            border
                            overflow-height="65px"
                            min-height="65px"
                            max-height="80px"
                            style="width: 80%"
                          ></tag>
                        </el-row>
                      </div>
                      <div style="width: 100%; margin-top: 10px">
                        <span>{{ $t('pages.termSecurityDomainBlackMessage') }}</span>
                        <el-row style="margin-top: 5px">
                          <tag
                            ref="doMainNoPassAccountList"
                            :placeholder="$t('pages.termSecurityDomainWhiteBlackPlaceholder')"
                            :disabled="!formable"
                            :add-able="formable"
                            :list="domainTemp.noPassAccountList"
                            :valid-rule="noPassValidRule"
                            :overflow-able="true"
                            border
                            overflow-height="65px"
                            min-height="65px"
                            max-height="80px"
                            style="width: 80%"
                          ></tag>
                        </el-row>
                      </div>
                      <el-row>
                        <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                          <span>{{ $t('pages.fixPlan') }}
                            <el-tooltip effect="dark" placement="top">
                              <div slot="content">
                                {{ $t('pages.termSecurityDomainFixPlanTip') }}
                              </div>
                              <i class="el-icon-info"/>
                            </el-tooltip>
                            ：
                          </span>
                          <el-tooltip v-if="domainTemp.fixPlan !== ''" placement="top" effect="dark">
                            <div slot="content">{{ domainTemp.fixPlan }}</div>
                            <span style="margin: 0 5px" class="fixPlanContentClass">{{ domainTemp.fixPlan }}</span>
                          </el-tooltip>
                          <span v-if="domainTemp.fixPlan !== ''" style="color: #68a8d0">
                            <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(domainTemp.fixPlan, '域环境检查')">
                              {{ $t('button.edit') }}
                            </el-button>
                          </span>
                          <el-button v-if="domainTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(domainTemp.fixPlan, '域环境检查')">{{ $t('button.insert') }}</el-button>
                        </div>
                      </el-row>
                      <div style="margin-top: 10px;" class="explainStyle">
                        {{ $t('pages.termSecurityDomainInstruction1') }}<br>
                        {{ $t('pages.termSecurityDomainInstruction2') }}<br>
                        {{ $t('pages.termSecurityDomainInstruction3') }}<br>
                        {{ $t('pages.termSecurityDomainInstruction4') }}
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="防火墙检查" name="firewall" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="firewallCheck" :disabled="!formable" @change="handleSingeCheckChange(3)"></el-checkbox>
                      {{ $t('pages.termSecurityFirewallCheck') }}
                    </span>
                    <div style="margin: 20px">
                      {{ $t('pages.termSecurityFirewallMessage') }}
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityFirewallFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="firewallTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ firewallTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ firewallTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="firewallTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(firewallTemp.fixPlan, '防火墙检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="firewallTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(firewallTemp.fixPlan, '防火墙检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin-top: 10px" class="explainStyle">
                        {{ $t('pages.termSecurityFirewallInstruction1') }}
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="远程桌面检查" name="desktop" style="overflow: auto; height: 100%">
                    <span slot="label" style="float:left;">
                      <el-checkbox v-model="desktopCheck" :disabled="!formable" @change="handleSingeCheckChange(3)"></el-checkbox>
                      {{ $t('pages.termSecurityDesktopCheck') }}
                    </span>
                    <div style="margin: 20px">
                      {{ $t('pages.termSecurityDesktopMessage') }}
                      <div style="margin-top: 10px; text-align: left;height: 40px; display: flex; align-items: center">
                        <span>{{ $t('pages.fixPlan') }}
                          <el-tooltip effect="dark" placement="top">
                            <div slot="content">
                              {{ $t('pages.termSecurityDesktopFixPlanTip') }}
                            </div>
                            <i class="el-icon-info"/>
                          </el-tooltip>
                          ：
                        </span>
                        <el-tooltip v-if="desktopTemp.fixPlan !== ''" placement="top" effect="dark">
                          <div slot="content">{{ desktopTemp.fixPlan }}</div>
                          <span style="margin: 0 5px" class="fixPlanContentClass">{{ desktopTemp.fixPlan }}</span>
                        </el-tooltip>
                        <span v-if="desktopTemp.fixPlan !== ''" style="color: #68a8d0">
                          <el-button type="text" size="mini" :disabled="!formable" @click="addFixPlanClick(desktopTemp.fixPlan, '远程桌面检查')">
                            {{ $t('button.edit') }}
                          </el-button>
                        </span>
                        <el-button v-if="desktopTemp.fixPlan === ''" size="small" :disabled="!formable" @click="addFixPlanClick(desktopTemp.fixPlan, '远程桌面检查')">{{ $t('button.insert') }}</el-button>
                      </div>
                      <div style="margin: 10px 0" class="explainStyle">
                        {{ $t('pages.termSecurityDesktopInstruction1') }}
                      </div>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>

              <!-- 检测项权重分配 -->
              <el-tab-pane :label="$t('pages.termSecurityWeightAllocation')" name="5" style="height: 100%; overflow: auto;">
                <div style="padding: 10px 20px;">
                  <div>
                    <el-row style="margin: 10px 8%;">
                      {{ $t('pages.termSecurityWeightAllocationRule') }}
                      <span v-if="weightDistributionError" style="color: red; margin-left: 10px">{{ $t('pages.weightValueNotNull') }}</span>
                    </el-row>
                    <div style="margin: 0 30px">
                      <el-row>
                        <el-col :span="24">
                          <div class="bigType" style="margin-left: 8%">
                            <i18n path="pages.termSecurityWeightFormatter">
                              <span slot="testItem">{{ $t('pages.softSecurityDetection') }}</span>
                              <span slot="weight">{{ securityWeight || 0 }}</span>
                              <span slot="weightPercent">{{ computeWeight(securityWeight) }}</span>
                            </i18n>
                          </div>
                        </el-col>
                      </el-row>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.killSoftInspect') }}
                        </div>
                        <div v-if="virusEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="virusWeight" >
                              <el-input-number v-model="temp.virusWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }}</div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.virusWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <el-row>
                        <div class="smallType" style="margin-left: 10%">
                          {{ $t('pages.softwareInstallCheck') }}<span>({{ $t('pages.termSecurityWeight') }}{{ (installSoftEffective ? getWeight(temp.installWhiteWeight) : 0)
                            + (limitInstallSoftEffective ? getWeight(temp.installBlackWeight) : 0) }}）</span>
                        </div>
                      </el-row>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.softwareMustInstallCheck') }}
                        </div>
                        <div v-if="installSoftEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="installWhiteWeight" >
                              <el-input-number v-model="temp.installWhiteWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }}</div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.installWhiteWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.softwareNotInstallCheck') }}
                        </div>
                        <div v-if="limitInstallSoftEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="installBlackWeight" >
                              <el-input-number v-model="temp.installBlackWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.installBlackWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <el-row>
                        <div class="smallType" style="margin-left: 10%">
                          {{ $t('pages.termSecurityServiceCheck') }}<span>({{ $t('pages.termSecurityWeight') }} {{ (serviceEffective ? getWeight(temp.serverWhiteWeight) : 0) + (limitServiceEffective ? getWeight(temp.serverBlackWeight) : 0) }}）</span>
                        </div>
                      </el-row>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityServerWhiteCheck') }}
                        </div>
                        <div v-if="serviceEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="serverWhiteWeight" >
                              <el-input-number v-model="temp.serverWhiteWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.serverWhiteWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityServerBlackCheck') }}
                        </div>
                        <div v-if="limitServiceEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="serverBlackWeight" >
                              <el-input-number v-model="temp.serverBlackWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.serverBlackWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <el-row>
                        <div class="smallType" style="margin-left: 10%">
                          {{ $t('pages.termSecurityProcessCheck') }}<span>({{ $t('pages.termSecurityWeight') }} {{ (processEffective ? getWeight(temp.processWhiteWeight) : 0) + (limitProcessEffective ? getWeight(temp.processBlackWeight) : 0) }}）</span>
                        </div>
                      </el-row>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityProcessMustRunCheck') }}
                        </div>
                        <div v-if="processEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="processWhiteWeight" >
                              <el-input-number v-model="temp.processWhiteWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.processWhiteWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityProcessNotRunCheck') }}
                        </div>
                        <div v-if="limitProcessEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="processBlackWeight" >
                              <el-input-number v-model="temp.processBlackWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.processBlackWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <el-row>
                        <div class="smallType" style="margin-left: 10%">
                          {{ $t('pages.termSecurityFileRegistryCheck') }}<span>({{ $t('pages.termSecurityWeight') }} {{ (fileEffective ? getWeight(temp.fileWeight) : 0) + (registryEffective ? getWeight(temp.regWeight) : 0) }}）</span>
                        </div>
                      </el-row>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityFileCheck') }}
                        </div>
                        <div v-if="fileEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="fileWeight" >
                              <el-input-number v-model="temp.fileWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.fileWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div style="display: flex; width: 100%; position: relative; padding-left: 12%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityRegistryCheck') }}
                        </div>
                        <div v-if="registryEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="regWeight" >
                              <el-input-number v-model="temp.regWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.regWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <el-divider class="dividerClass" style="margin-bottom: 20px"></el-divider>
                      <el-row>
                        <el-col :span="24">
                          <div class="bigType" style="margin-left: 8%">
                            <i18n path="pages.termSecurityWeightFormatter">
                              <span slot="testItem">{{ $t('pages.hostSecurityDetection') }}</span>
                              <span slot="weight">{{ hostWeight || 0 }}</span>
                              <span slot="weightPercent">{{ computeWeight(hostWeight) }}</span>
                            </i18n>
                          </div>
                        </el-col>
                      </el-row>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityHostCheck') }}
                        </div>
                        <div v-if="osTypeEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="osWeight" >
                              <el-input-number v-model="temp.osWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.osWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityComputerNameCheck') }}
                        </div>
                        <div v-if="computeNameEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="computeNameWeight" >
                              <el-input-number v-model="temp.computeNameWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.computeNameWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityDiskCheck') }}
                        </div>
                        <div v-if="diskEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="diskUseSpaceWeight" >
                              <el-input-number v-model="temp.diskUseSpaceWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.diskUseSpaceWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecuritySharedCheck') }}
                        </div>
                        <div v-if="sharedEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="shareWeight" >
                              <el-input-number v-model="temp.shareWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.shareWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityGuestCheck') }}
                        </div>
                        <div v-if="guestConfigured" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="guestWeight" >
                              <el-input-number v-model="temp.guestWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.guestWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass" style="margin-bottom: 20px"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityScreenCheck') }}
                        </div>
                        <div v-if="screenConfigured" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="screenWeight" >
                              <el-input-number v-model="temp.screenWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.screenWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <el-row>
                        <el-col :span="24">
                          <div class="bigType" style="margin-left: 8%">
                            <i18n path="pages.termSecurityWeightFormatter">
                              <span slot="testItem">{{ $t('pages.netSecurityDetection') }}</span>
                              <span slot="weight">{{ netWeight || 0 }}</span>
                              <span slot="weightPercent">{{ computeWeight(netWeight) }}</span>
                            </i18n>
                          </div>
                        </el-col>
                      </el-row>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityPortCheck') }}
                        </div>
                        <div v-if="portEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="portWeight" >
                              <el-input-number v-model="temp.portWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.portWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityDomainCheck') }}
                        </div>
                        <div v-if="domainEffective" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="domainWeight" >
                              <el-input-number v-model="temp.domainWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.domainWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityFirewallCheck') }}
                        </div>
                        <div v-if="firewallConfigured" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="fireWallWeight" >
                              <el-input-number v-model="temp.fireWallWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.fireWallWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-divider class="dividerClass"></el-divider>

                      <div style="display: flex; width: 100%; position: relative; padding-left: 10%;">
                        <div class="smallType">
                          {{ $t('pages.termSecurityDesktopCheck') }}
                        </div>
                        <div v-if="desktopConfigured" class="weightValueClass">
                          <div style="display: flex; align-items: center">
                            <FormItem slot="value" class="weightFormItemClass" label-width="100px" :label="$t('pages.termSecurityWeight')" prop="deskWeight" >
                              <el-input-number v-model="temp.deskWeight" solt="value" :controls="false" :disabled="!formable" step-strictly :step="1" :min="1" :max="100" style="width: 100px" @change="weightChange"></el-input-number>
                            </FormItem>
                            <div style="margin-left: 20px">{{ $t('pages.termSecurityWeightPercent') }} </div>
                            <div style="width: 55px;">
                              <span style="float:right;">
                                {{ computeWeight(temp.deskWeight) }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              </el-tab-pane>

              <!-- 响应规则 -->
              <el-tab-pane :label="$t('pages.responseRule')" name="6" style="height: 100%; overflow: auto">
                <div style="margin: 20px;">
                  <el-row>
                    <el-col :span="24">
                      <FormItem :label="$t('pages.termSecurityLowRisk')" class="weight"></FormItem>
                    </el-col>
                    <div>
                      <el-col :span="9">
                        <FormItem class="weight">
                          <ResponseContent
                            :select-style="{ 'margin-top': 0 }"
                            :show-select="true"
                            :editable="formable"
                            read-only
                            :prop-check-rule="lowCheck"
                            :show-check-rule="true"
                            :prop-rule-id="temp.lowRiskAlarmId"
                            style="margin-bottom: 10px"
                            @getRuleId="getRiskRuleId"
                            @ruleIsCheck="getLowChecked"
                          />
                        </FormItem>
                      </el-col>
                      <el-col :span="15">
                        <FormItem style="margin-left: 20px;" :label="$t('pages.termSecurityWeightPercent1')" label-width="140px" prop="lowRiskWeight">
                          <el-input-number v-model="temp.lowRiskWeight" :disabled="!formable" :controls="false" step-strictly :step="1" value="20" style="width: 70px" :max="100" :min="0"></el-input-number> %
                          <span v-if="weightRangShow" style="margin-left: 20px">{{ computeWeightRange(1) }}</span>
                        </FormItem>
                      </el-col>
                    </div>
                  </el-row>

                  <el-row>
                    <el-col :span="24">
                      <FormItem :label="$t('pages.termSecurityMediumRisk')" class="weight"> </FormItem>
                    </el-col>
                    <div>
                      <el-col :span="9">
                        <FormItem class="weight">
                          <ResponseContent
                            :select-style="{ 'margin-top': 0 }"
                            :show-select="true"
                            :editable="formable"
                            read-only
                            :prop-check-rule="mediumCheck"
                            :show-check-rule="true"
                            :prop-rule-id="temp.mediumRiskAlarmId"
                            @getRuleId="getMediumRuleId"
                            @ruleIsCheck="getMediumChecked"
                          />
                        </FormItem>
                      </el-col>
                      <el-col :span="15">
                        <FormItem style="margin-left: 20px;" :label="$t('pages.termSecurityWeightPercent1')" label-width="140px" prop="mediumRiskWeight">
                          <el-input-number v-model="temp.mediumRiskWeight" :disabled="!formable" :controls="false" step-strictly :step="1" value="20" style="width: 70px" :max="100" :min="0"></el-input-number> %
                          <span v-if="weightRangShow" style="margin-left: 20px">{{ computeWeightRange(2) }}</span>
                        </FormItem>
                      </el-col>
                    </div>
                  </el-row>

                  <el-row style="margin-bottom: 10px">
                    <el-col :span="24">
                      <FormItem :label="$t('pages.termSecurityHighRisk')" class="weight"></FormItem>
                    </el-col>
                    <div>
                      <el-col :span="9">
                        <FormItem class="weight">
                          <ResponseContent
                            :select-style="{ 'margin-top': 0 }"
                            :show-select="true"
                            :editable="formable"
                            read-only
                            :prop-check-rule="highCheck"
                            :show-check-rule="true"
                            :prop-rule-id="temp.highRiskAlarmId"
                            style="margin-bottom: 10px"
                            @getRuleId="getHighRuleId"
                            @ruleIsCheck="getHighChecked"
                          />
                        </FormItem>
                      </el-col>
                      <el-col :span="15">
                        <FormItem style="margin-left: 20px;" :label="$t('pages.termSecurityWeightPercent1')" label-width="140px" prop="highRiskWeight">
                          <el-input-number v-model="temp.highRiskWeight" :disabled="!formable" :controls="false" step-strictly :step="1" value="60" style="width: 70px" :max="100" :min="0"></el-input-number> %
                          <span v-if="weightRangShow" style="margin-left: 20px">{{ computeWeightRange(3) }}</span>
                        </FormItem>
                      </el-col>
                    </div>
                  </el-row>

                  <div v-if="validWeightError" style="color: red; margin-left: 20px">
                    {{ $t('pages.termSecurityRiskInstruction1') }}
                  </div>

                  <div style="margin: 10px 20px;" class="explainStyle">
                    {{ $t('pages.termSecurityRiskInstruction2') }}<br>
                    {{ $t('pages.termSecurityRiskInstruction3') }}<br>
                    {{ $t('pages.termSecurityRiskInstruction4') }}
                  </div>
                </div>
              </el-tab-pane>

              <!-- 修复指南总览 -->
              <el-tab-pane v-if="fixPlanShow" :label="$t('pages.termSecurityFixPlanGeneralView')" name="4" style="height: 100%; overflow: auto">
                <div style="padding: 10px 20px; height: 100%">
                  <div style="margin-bottom: 10px">{{ $t('pages.termSecurityFixPlanGeneralViewMessage') }}</div>
                  <div>
                    <el-button size="small" :disabled="!formable" @click="addFixPlanAllClick">
                      {{ $t('button.insert') }}
                    </el-button>
                    <el-button size="small" :disabled="!fixPlanSelected || !formable" @click="deleteFixPlanClick">
                      {{ $t('button.delete') }}
                    </el-button>
                  </div>
                  <div class="table-container" style="height: calc(100% - 100px)!important;">
                    <grid-table
                      ref="fixPlanList"
                      node-key="testItemId"
                      :multi-select="formable"
                      :show-pager="false"
                      :col-model="fixPlanColModel"
                      :row-datas="configuredFixPlans"
                      @selectionChangeEnd="fixPlanSelectedFun"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
            <div class="fixPlanTab">
              <el-button icon="el-icon-view" type="text" size="mini" @click="fixPlanDlg">
                {{ fixPlanTitle }}
              </el-button>
            </div>
          </div>
        </el-card>

      </Form>
      <div style="height: 50px">
        <div class="dialog-footer" style="float: right; padding: 5px 39px 0 0">
          <el-button v-if="formable && operator === 'update'" type="primary" size="medium" @click="handleCopy">
            {{ $t('components.saveAs') }}
          </el-button>
          <el-button v-if="formable" type="primary" :loading="submitting" size="medium" @click="confirm">
            {{ $t('button.confirm') }}
          </el-button>
          <el-button size="medium" @click="cancel">
            {{ $t('button.cancel') }}
          </el-button>
        </div>
      </div>
    </div>

    <virus-dlg ref="virusDlg" @submit="virusSubmit"></virus-dlg>
    <install-dlg ref="installDlg" @submit="installSubmit"></install-dlg>
    <service-dlg ref="serviceDlg" @submit="serviceSubmit"></service-dlg>
    <process-dlg ref="processDlg" @submit="processSubmit"></process-dlg>
    <file-dlg ref="fileDlg" @submit="fileSubmit"></file-dlg>
    <registry-dlg ref="registryDlg" @submit="registrySubmit"></registry-dlg>
    <all-fix-plan-dlg ref="allFixPlanDlg" :virus-map="virusMap" @submit="allFixPlanSubmit"></all-fix-plan-dlg>
    <fix-plan-import-dlg ref="fixPlanImportDlg"></fix-plan-import-dlg>
    <fix-plan-dlg ref="fixPlanDlg" @submit="fixPlanDlgSubmit"></fix-plan-dlg>
    <software-assert-table ref="softwareTable" :associate-hidden="true" :support-version="true" :show-path="false" @submitEnd="softwareImportSubmitEnd"></software-assert-table>
    <app-select-dlg ref="appSelectDlg" :os-type="temp.osType" :append-to-body="false" :single-import="true" :multiple-group="false" @select="appendApp"/>
    <save-as-dlg ref="saveAsDlg" :valid-data-form="validDataForm" :strategy-def-type="strategyDefType" @submitEnd="saveAsSubmitEnd"/>
  </div>
</template>

<script>
import { addStrategy, updateStrategy, getByName, isValidRegex } from '@/api/system/terminalManage/termSecurity';
import { validatePolicy } from '@/utils/validate';
import SoftwareAssertTable from '@/views/softwareManage/strategy/softwareAssertTable'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import FixPlanImportDlg from './fixPlanImportDlg';
import AppSelectDlg from './appSelectDlg'
import SaveAsDlg from './saveAsDlg';
import VirusDlg from './dlg/virusDlg';
import InstallDlg from './dlg/installDlg'
import ServiceDlg from './dlg/serviceDlg'
import ProcessDlg from './dlg/processDlg'
import FixPlanDlg from './dlg/fixPlanDlg';
import FileDlg from './dlg/fileDlg';
import RegistryDlg from './dlg/registryDlg';
import AllFixPlanDlg from './dlg/allFixPlanDlg';

export default {
  name: 'AddTermSecurity',
  components: { SaveAsDlg, AppSelectDlg, AllFixPlanDlg, RegistryDlg, FileDlg, FixPlanDlg, VirusDlg, ResponseContent, FixPlanImportDlg, InstallDlg, ServiceDlg, ProcessDlg, SoftwareAssertTable },
  props: {
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } },
    strategyDefType: {  //  是否是预定义策略
      type: Number,
      default() { return 0 }
    },
    serverInfoList: {
      type: Array,
      default: function() {
        return []
      }
    },
    serverTreeData: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      formable: false,
      submitting: false,
      operator: '', //  操作类型，新增create, 修改update
      temp: {}, // 当前策略的基本信息
      copyFlag: false,  //  是否在执行另存为功能
      defaultTemp: {
        id: null,
        name: '',
        remark: '',
        active: false,
        strategyDefType: this.strategyDefType,
        osType: 1, //  操作系统类型 1-win, 2-linux 4-mac, 8-phone
        entityType: null, //  生效对象类型
        entityId: null, //  生效对象Id
        objectIds: [],  //  生效对象Ids   终端Ids
        objectGroupIds: [],  //  生效对象Ids - 分组Ids
        termCheck: 1, //  是否允许终端自检， 0-否，1-是 （2024-04-02 隐藏自检配置，默认支持终端自检功能）
        bootCheck: 0, //  开机检测  0-否， 1-是
        checkDay: null, //  检测周期-时间间隔 单位：天
        isSelfDay: false, //  检测周期是否为自定义天数，仅控制台在使用
        lowRiskAlarmId: null,   //  低风险响应规则id
        mediumRiskAlarmId: null,  //  中风险响应规则id
        highRiskAlarmId: null,  //  高风险响应规则id
        lowRiskWeight: 20,  //  低风险权重占比
        mediumRiskWeight: 20, //  中风险权重占比
        highRiskWeight: 60,   //  高风险权重占比
        virusWeight: 5,   //  杀毒软件扫描权重占比
        installWhiteWeight: 5,  //  安装软件白名单（必须安装）权重占比
        installBlackWeight: 5,  //  安装软件黑名单（禁止安装）权重占比
        serverWhiteWeight: 5, //  服务白名单权重占比
        serverBlackWeight: 5, //  服务黑名单权重占比
        processWhiteWeight: 5,  //  进程白名单权重占比
        processBlackWeight: 5,  //  进程黑名单权重占比
        fileWeight: 5,  //  文件权重扫描权重占比
        regWeight: 5, //  注册表权重占比
        osWeight: 5,  //  操作系统权重占比
        computeNameWeight: 5, //  计算机名称规范检查权重占比
        guestWeight: 5, //  禁用来宾账号权重占比
        diskUseSpaceWeight: 5,  //  磁盘空间权重占比
        shareWeight: 5,  //  共享配置检查权重占比
        screenWeight: 5,  //  计算机屏幕保护功能权重占比
        portWeight: 5,    //  端口检查扫描权重占比
        fireWallWeight: 5,  //  防火墙权重占比
        domainWeight: 5,  //  域环境权重占比
        deskWeight: 5,  //  远程桌面权重占比
        virus: [], //  里面最多仅有一个元素  元素结构为：{ virusInfoList: [] },  参考控制台策略下发给终端的json字符串格式
        installSoft: [],
        limitInstallSoft: [],
        whiteProcess: [],
        blackProcess: [],
        whiteServer: [],
        blackServer: [],
        fileScan: [],
        regScan: [],
        host: [],
        computerName: [],
        guest: [],
        shared: [],
        disk: [],
        screen: [],
        port: [],
        firewall: [],
        desktop: [],
        domain: []
      },
      rules: {
        name: [
          { required: true, validator: this.nameValid, trigger: 'blur' }
        ],
        osType: [
          { required: true, message: this.$t('pages.required1'), trigger: 'change' }
        ],
        timeType: [
          { required: true, validator: this.timeTypeValid, trigger: 'blur' }
        ],
        selfTime: [
          { required: true, validator: this.selfTimeValid, trigger: 'blur' }
        ],
        regEx: [
          { required: true, validator: this.regExValid, trigger: 'blur' }
        ],
        regDes: [
          { required: true, validator: this.regDesValid, trigger: 'blur' }
        ],
        sysDisk: [
          { required: true, validator: this.sysDiskValid, trigger: 'blur' }
        ],
        sysSize: [
          { required: true, validator: this.sysSizeValid, trigger: 'blur' }
        ],
        totalDisk: [
          { required: true, validator: this.totalDiskValid, trigger: 'blur' }
        ],
        totalSize: [
          { required: true, validator: this.totalSizeValid, trigger: 'blur' }
        ],
        waitTime: [
          { required: true, validator: this.waitTimeValid, trigger: 'blur' }
        ],
        virusWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        installWhiteWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        installBlackWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        serverWhiteWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        serverBlackWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        processWhiteWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        processBlackWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        fileWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        regWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        osWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        computeNameWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        diskUseSpaceWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        shareWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        guestWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        screenWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        portWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        domainWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        fireWallWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        deskWeight: [
          { validator: this.weightValid, trigger: 'blur' }
        ],
        lowRiskWeight: [
          { required: true, validator: this.riskWeightValid, trigger: 'blur' }
        ],
        mediumRiskWeight: [
          { required: true, validator: this.riskWeightValid, trigger: 'blur' }
        ],
        highRiskWeight: [
          { required: true, validator: this.riskWeightValid, trigger: 'blur' }
        ]
      },
      lowCheck: true,
      mediumCheck: true,
      highCheck: true,
      timeType: 7, // 检测时间类型
      timeTypeArray: [
        { value: 1, label: this.$t('pages.everyDay') }, //  每天
        { value: 3, label: this.$t('pages.selfSetDay', { day: 3 }) },  //  每3天
        { value: 7, label: this.$t('pages.selfSetDay', { day: 7 }) }, //  每7天
        { value: 30, label: this.$t('pages.selfSetDay', { day: 30 }) }, //  每30天
        { value: -2, label: this.$t('pages.selfDay') }, //  自定义
        { value: -1, label: this.$t('pages.executeOnce') }, //  执行一次
        { value: 0, label: this.$t('pages.close') } //  关闭
      ],
      selfTime: 15,
      softwareSecurityCheck: false, //  软件安全检测类是否配置，勾选才会保存大类的配置信息
      softIndeterminate: false,
      hostSecurityCheck: false, //  主机安全检测
      hostIndeterminate: false,
      netSecurityCheck: false,  //  网络安全防护检测
      netIndeterminate: false,
      virusCheck: false,  //  杀毒软件检查
      installSoftCheck: false,
      limitInstallSoftCheck: false,
      whiteProcessCheck: false,
      blackProcessCheck: false,
      whiteServerCheck: false,
      blackServerCheck: false,
      fileScanCheck: false,
      regScanCheck: false,
      hostCheck: false,
      computerNameCheck: false,
      guestCheck: false,
      sharedCheck: false,
      diskCheck: false,
      screenCheck: false,
      portCheck: false,
      firewallCheck: false,
      desktopCheck: false,
      domainCheck: false,
      virusInfoList: [],  //  保存杀毒软件检查项
      killSelected: false,  //  是否选中杀毒软件列表中的元素
      virusInfo: {    //  杀毒软件检查
        softName: '',  //  杀毒软件名称
        versionDate: '', //  杀毒软件版本日期 yyyy-MM-dd
        repairingSoftwareId: null, //  关联软件库中的软件id
        versionAsk: 1, // 版本要求
        fixPlan: '', //  修复指南
        versionType: 0  //  病毒库版本
      },
      softWareInstallTemp: {  //  软件安装检查
        installType: 0, //  安装类型：0-必须安装以下安全软件，1-只须安装以下任一软件
        softWareList: [],
        limitSoftWareList: [],
        fixPlan: '',
        limitFixPlan: '',
        repairMethod: ''  //  修复指南
      },
      installSelected: false, //  是否选中必须安装软件列表中的元素
      limitInstallSelected: false, //  是否选中禁止安装软件列表中的元素
      serviceTemp: {    //  服务检查
        runType: 0, //  安装类型：0-必须安装以下安全软件，1-只须安装以下任一软件
        serviceList: [],
        limitServiceList: [],  //  禁止运行以下服务
        fixPlan: '',  //  修复指南
        limitFixPlan: '' //  修复指南
      },
      serviceSelected: false, //  是否选中必须运行服务列表中的元素
      limitServiceSelected: false, //  是否选中禁止运行服务列表中的元素
      processTemp: {    //  进程检查
        runType: 0, //  安装类型：0-必须运行以下进程，1-只须运行以下任一进程
        processList: [],
        limitProcessList: [],  //  禁止运行以下进程
        fixPlan: '',  //  修复指南
        limitFixPlan: '' //  修复指南
      },
      processSelected: false, //  是否选中必须运行进程列表中的元素
      limitProcessSelected: false, //  是否选中禁止运行进程列表中的元素
      fileScanTemp: {
        fixPlan: '',
        fileInfoList: []
      },
      fileSelected: false,
      //  注册表检查
      regScanTemp: {
        fixPlan: '',
        regInfoList: []
      },
      regSelected: false,
      //  注册表检查-操作项
      registryOperators: [
        { id: 0, label: this.$t('pages.notExist') },                        //  不存在
        { id: 1, label: this.$t('pages.exist') },                           //  存在
        { id: 2, label: this.$t('pages.termSecurityGreaterThan') },         //  大于
        { id: 3, label: this.$t('pages.termSecurityEqual') },               //  等于
        { id: 4, label: this.$t('pages.termSecurityLessThan') },            //  小于
        { id: 5, label: this.$t('pages.termSecurityGreaterThanOrEqual') },  //  大于等于
        { id: 6, label: this.$t('pages.termSecurityLessThanOrEqual') },     // 小于等于
        { id: 7, label: this.$t('pages.termSecurityContain') },             // 包含
        { id: 8, label: this.$t('pages.termSecurityExclude') }              //  不包含
      ],
      colModel: [
        { prop: 'type', label: 'category', width: '100', formatter: this.categoryFormatter },
        { prop: 'name', label: 'name', width: '120' }
      ],
      killVirusSoftWareColModel: [
        { prop: 'name', label: 'killSoftName', width: '120', sort: true, formatter: this.killSoftNameFormatter },
        { prop: 'versionDate', label: 'configContent', width: '120', formatter: this.killVirusSoftWareFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => { this.$refs.virusDlg.show(row, this.virusInfoList, 2) } }
          ]
        }
      ],
      softWareInstallColModel: [
        { prop: 'softName', label: 'softwareName', width: '120', sort: true },
        { prop: 'softVersion', label: 'versionNumber', width: '120', formatter: this.softWareVersionFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => {
              this.$refs.installDlg.show(row, 1, 2,
                { mustList: this.softWareInstallTemp.softWareList, stopList: this.softWareInstallTemp.limitSoftWareList })
            } }
          ]
        }
      ],
      softWareLimitInstallColModel: [
        { prop: 'softName', label: 'softwareName', width: '120', sort: true },
        { prop: 'softVersion', label: 'versionNumber', width: '120', formatter: this.softWareVersionFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => {
              this.$refs.installDlg.show(row, 2, 2,
                { mustList: this.softWareInstallTemp.softWareList, stopList: this.softWareInstallTemp.limitSoftWareList })
            } }
          ]
        }
      ],
      serviceColModel: [
        { prop: 'serviceName', label: 'detectionServiceName', width: '120', sort: true, formatter: this.serviceNameFormatter },
        // { prop: 'dependentServiceName', label: '依赖服务名称', width: '120', formatter: this.dependentServiceNameFormatter },
        { prop: 'startType', label: 'startType', width: '120', sort: true, formatter: this.startTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => {
              this.$refs.serviceDlg.show(row, 1, 2,
                { mustList: this.serviceTemp.serviceList, stopList: this.serviceTemp.limitServiceList })
            } }
          ]
        }
      ],
      limitServiceColModel: [
        { prop: 'serviceName', label: 'detectionServiceName', width: '120', sort: true, formatter: this.serviceNameFormatter },
        { prop: 'dependentServiceName', label: 'dependentServiceName', width: '120', sort: true, formatter: this.dependentServiceNameFormatter },
        { prop: 'startType', label: 'startType', width: '120', sort: true, formatter: this.startTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => {
              this.$refs.serviceDlg.show(row, 2, 2,
                { mustList: this.serviceTemp.serviceList, stopList: this.serviceTemp.limitServiceList }, true)
            } }
          ]
        }
      ],
      processColModel: [
        { prop: 'procName', label: 'processName', width: '120', sort: true },
        { label: 'operate', type: 'button', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => {
              this.$refs.processDlg.show(row, 1, 2,
                { mustList: this.processTemp.processList, stopList: this.processTemp.limitProcessList })
            } }
          ]
        }
      ],
      limitProcessColModel: [
        { prop: 'procName', label: 'processName', width: '120', sort: true },
        { label: 'operate', type: 'button', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => {
              this.$refs.processDlg.show(row, 2, 2,
                { mustList: this.processTemp.processList, stopList: this.processTemp.limitProcessList })
            } }
          ]
        }
      ],
      fileColModel: [
        { prop: 'fileName', label: 'fileName1', width: '120', sort: true },
        { prop: 'filePath', label: 'localFilePath', width: '120', sort: true },
        { prop: 'fileExist', label: 'operatorItem', width: '120', sort: true, formatter: this.fileExistFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => { this.$refs.fileDlg.show(row, 2, this.fileScanTemp.fileInfoList) } }
          ]
        }
      ],
      registryColModel: [
        { prop: 'regType', label: 'scanType', width: '120', sort: true, formatter: this.registryTypeFormatter },
        { prop: 'regItem', label: 'registryKeyItem', width: '120', sort: true, formatter: this.registryRegItemFormatter },
        { prop: 'regKey', label: 'registryKey', width: '120', sort: true, formatter: this.registryRegKeyFormatter },
        { prop: 'regValue', label: 'registryKeyValue', width: '120', sort: true },
        { prop: 'operator', label: 'operatorItem', width: '120', sort: true, formatter: this.registryOperatorFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => { this.$refs.registryDlg.show(row, this.regScanTemp.regInfoList, 2) } }
          ]
        }
      ],
      //  修复指南总览
      fixPlanColModel: [
        { prop: 'name', label: 'detectionItem', width: '120', formatter: this.overviewTestItemFormatter },
        { prop: 'fixPlan', label: 'fixPlan', width: '120' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => { return !this.formable }, click: (row) => { this.$refs.allFixPlanDlg.show(this.configuredList, row, this.configuredFixPlans, 2) } }
          ]
        }
      ],
      //  操作系统检查
      allowOsTypeTemp: {
        name: '',
        operator: 4,   //  操作  默认为 大于等于
        versionName: '',  //  判断终端版本是否符号规则， 若symbol=1， versionName：Windows7 即终端版本需要大于Windows7
        serVersionName: '',
        fixPlan: ''
      },
      defaultAllowOsTypeTemp: {
        name: '',
        operator: 4,   //  操作  默认为 大于等于
        versionName: '',  //  判断终端版本是否符号规则， 若symbol=1， versionName：Windows7 即终端版本需要大于Windows7
        serVersionName: '',
        fixPlan: ''
      },
      windowsOsTypeNames: [],
      windowsServerOsTypeNames: [],
      osTypeOperators: [
        { value: 1, label: this.$t('pages.greaterThan') },        //  大于
        { value: 2, label: this.$t('pages.equalTo') },        //  等于
        { value: 3, label: this.$t('pages.lessThan') },        //  小于
        { value: 4, label: this.$t('pages.greaterThanOrEqualTo') },        //  大于等于
        { value: 5, label: this.$t('pages.termSecurityLessThanOrEqual') }        //  小于等于
      ],
      osTypeError: false,
      //  最多只能支持其中一种方式
      osTypeCheck1: false,   // 操作系统检查- 0: 选择允许终端安装的操作系统的方式检查终端的操作系统  1:选择通过范围限制的方式检查终端的操作系统
      osTypeCheck2: false,
      osTypeCheckAll1: false,
      osTypeCheckAll2: false,
      osTypeIndeterminate1: false,
      osTypeIndeterminate2: false,
      //  计算机名称规范检查
      computeNameTemp: {
        regEx: '',  //  正则表达式
        regDes: '',  //  正则表达式描述
        fixPlan: '' //  修复指南
      },
      regDefaultArray: [
        { id: 1, regEx: '^[a-zA-Z][a-zA-Z0-9]*$', regDes: '由英文字符开头，其余英文或者数字！', i18n: this.$t('pages.termSecurityComputerNameExpressionMsgA') },
        { id: 2, regEx: '^[a-zA-Z0-9_]{4,16}$', regDes: '只能输入字母数字下划线,且长度由4-16位字符组成！', i18n: this.$t('pages.termSecurityComputerNameExpressionMsgB') },
        { id: 3, regEx: '^[\\u4e00-\\u9fa5a-zA-Z]+$', regDes: '只能输入中文和英文！', i18n: this.$t('pages.termSecurityComputerNameExpressionMsgC') },
        { id: 4, regEx: '^[A-Za-z]+$', regDes: '必须由英文组成,不能包含其它字符！', i18n: this.$t('pages.termSecurityComputerNameExpressionMsgD') }
      ],
      //  磁盘使用空间检查
      diskDetectTemp: {
        sysDisk: 90,  //  系统磁盘使用阈值百分比    0：表示未配置
        sysSize: 10,   //  系统磁盘使用阈值大小     0：表示未配置
        totalDisk: 90,  //  所有磁盘使用阈值百分比  0：表示未配置
        totalSize: 50, //    所有磁盘使用阈值大小  0：表示未配置
        fixPlan: '' //  修复指南
      },
      sysDiskCheck: false,
      totalDiskCheck: false,
      sysSizeCheck: false,
      totalSizeCheck: false,
      //  共享配置检查
      shareConfigTemp: {
        printer: '1',  //  是否检测共享打印机 0: 需要，1：不需要
        fixPlan: '',
        shareFlag: 0, //  是否检测共享文件夹，1-检测，0-不检测
        fileNameList: []  //  不检测的共享文件夹路径
      },
      //  来宾账户检查
      guestTemp: {
        check: false,
        fixPlan: ''
      },
      //  计算机屏保功能
      screenTemp: {
        timeCheck: false,
        showLogin: 0,  //  恢复是否显示登录屏幕  0: 否， 1： 是
        waitTime: 5,  //  等待时间  单位：分钟
        fixPlan: ''
      },
      //  端口检查
      portTemp: {
        port: '',  //  多个用逗号隔开，
        selfPorts: [], //  自定义端口
        fixPorts: [],  //  固定的端口
        fixPlan: ''
      },
      portIndeterminate: false,
      portCheckAll: false,  //  固定端口是否全选
      portValidRules: [
        { validator: this.portValid, trigger: 'blur' }
      ],
      //  固定端口
      fixPortList: [
        { port: '21', label: 'FTP(21)' },
        { port: '23', label: 'TELNET(23)' },
        { port: '25', label: 'SMTP(25)' },
        { port: '51', label: 'DNS(51)' },
        { port: '67', label: 'DHCP(67)' },
        { port: '80', label: 'HTTP(80)' },
        { port: '110', label: 'POP3(110)' },
        { port: '61', label: 'SNMP(61)' },
        { port: '443', label: 'HTTPS(443)' }
      ],
      //  域环境检查
      domainTemp: {
        domainName: '',   //   域名（不包括域名后缀，例如.com和.cn）
        noPassAccount: '',  //  不可通过操作员账号
        passAccount: '', //  始终通过的账号
        domainNameList: [],
        noPassAccountList: [],
        passAccountList: [],
        fixPlan: ''
      },
      passValidRule: [
        { validator: this.passValidator, trigger: 'blur' }
      ],
      noPassValidRule: [
        { validator: this.noPassValidator, trigger: 'blur' }
      ],
      //  防火墙检查
      firewallTemp: {
        fixPlan: ''
      },
      //  远程桌面检查
      desktopTemp: {
        fixPlan: ''
      },
      //  保存修复指南总览列表
      configuredFixPlans: [],
      //  保存已配置的检测项
      configuredList: [],
      fixPlanSelected: false,

      activeName: '0',  //  大类扫描项Tab
      softSecurityActiveName: 'virus',  //  软件安全检测tab组件
      hostActiveName: 'host',  //  主机安全检测tab
      netActiveName: 'port', //  网络安全防护检测tab
      overVersion: 'Windows 11及以上,Windows Server 2025及以上',  //  保存当前配置时带有以上字样的版本，用来生成结果用
      supportOsTypes: [
        //  Windows 11及以上
        { type: 1, os: 'Windows 11', label: this.$t('pages.termSecurityWindowsAndAbove') },
        //  Windows Server 2025及以上
        { type: 2, os: 'Windows Server 2025', label: this.$t('pages.termSecurityWindowsServerAndAbove') },

        { type: 1, os: 'Windows 10', label: 'Windows 10' },
        { type: 2, os: 'Windows Server 2022', label: 'Windows Server 2022' },

        { type: 1, os: 'Windows 8.1', label: 'Windows 8.1' },
        { type: 2, os: 'Windows Server 2019', label: 'Windows Server 2019' },

        { type: 1, os: 'Windows 8', label: 'Windows 8' },
        { type: 2, os: 'Windows Server 2016', label: 'Windows Server 2016' },

        { type: 1, os: 'Windows 7', label: 'Windows 7' },
        { type: 2, os: 'Windows Server 2012 R2', label: 'Windows Server 2012 R2' },

        { type: 1, os: 'Windows Vista', label: 'Windows Vista' },
        { type: 2, os: 'Windows Server 2012', label: 'Windows Server 2012' },

        { type: 1, os: 'Windows XP', label: 'Windows XP' },
        { type: 2, os: 'Windows Server 2008 R2', label: 'Windows Server 2008 R2' },

        { type: 2, os: 'Windows Server 2008', label: 'Windows Server 2008' },
        { type: 2, os: 'Windows Server 2003 R2', label: 'Windows Server 2003 R2' },

        { type: 2, os: 'Windows Server 2003', label: 'Windows Server 2003' }
      ],
      windowsOsTypes: [],
      windowsServerOsTypes: [],
      killDialogFormVisible: false,
      fixPlanShow: false,
      fixPlanTitle: this.$t('pages.showFixPlanView'),
      //  支持的杀毒软件
      virusSoftwareList: [
        { name: '360杀毒', type: 1, label: this.$t('pages.360AntivirusSoftware') },
        { name: '360安全卫士', type: 1, label: this.$t('pages.360SecurityGuard') },
        { name: '腾讯电脑管家', type: 1, label: this.$t('pages.tencentComputerManager') },
        { name: '火绒安全', type: 4, label: this.$t('pages.tinderSafety') },
        { name: '金山毒霸', type: 1, label: this.$t('pages.kingSoft') },
        { name: '卡巴斯基', type: 2, label: this.$t('pages.kaspersky') },
        { name: '江民杀毒', type: 1, label: this.$t('pages.jiangMingKill') },
        { name: 'Avira Antivirus', type: 1, label: 'Avira Antivirus' },
        { name: '瑞星杀毒', type: 2, label: this.$t('pages.risingAntivirus') },
        { name: 'McAfee', type: 2, label: 'McAfee' },
        { name: '趋势杀毒', type: 2, label: this.$t('pages.trendAntivirus') },
        { name: 'ESET', type: 2, label: 'ESET' },
        { name: 'Avast', type: 3, label: 'Avast' },
        { name: 'Norton', type: 4, label: 'Norton' }
      ],
      virusMap: {},
      limitNum: 1000,  //  配置项支持的最大数量
      weightDistributionError: false  //  存在检测项权重值未设置
    }
  },
  computed: {
    //  标题
    title() {
      const titleMap = {
        create: this.$t('pages.addTermSecurityStg'),
        update: this.formable ? this.$t('pages.updateTermSecurityStg') : this.$t('pages.termSecurityStgDetail')
      }
      return titleMap[this.operator] || this.$t('pages.addTermSecurityStg')
    },
    //  软件安全检测权重
    securityWeight() {
      let t = 0;
      t += this.virusEffective ? this.getWeight(this.temp.virusWeight) : 0;
      t += this.installSoftEffective ? this.getWeight(this.temp.installWhiteWeight) : 0;
      t += this.limitInstallSoftEffective ? this.getWeight(this.temp.installBlackWeight) : 0;
      t += this.serviceEffective ? this.getWeight(this.temp.serverWhiteWeight) : 0;
      t += this.limitServiceEffective ? this.getWeight(this.temp.serverBlackWeight) : 0;
      t += this.processEffective ? this.getWeight(this.temp.processWhiteWeight) : 0
      t += this.limitProcessEffective ? this.getWeight(this.temp.processBlackWeight) : 0
      t += this.fileEffective ? this.getWeight(this.temp.fileWeight) : 0
      t += this.registryEffective ? this.getWeight(this.temp.regWeight) : 0;
      return t;
    },
    //  主机安全检测权重
    hostWeight() {
      let t = 0;
      t += this.osTypeEffective ? this.getWeight(this.temp.osWeight) : 0;
      t += this.computeNameEffective ? this.getWeight(this.temp.computeNameWeight) : 0;
      t += this.guestConfigured ? this.getWeight(this.temp.guestWeight) : 0;
      t += this.diskEffective ? this.getWeight(this.temp.diskUseSpaceWeight) : 0;
      t += this.sharedEffective ? this.getWeight(this.temp.shareWeight) : 0;
      t += this.screenConfigured ? this.getWeight(this.temp.screenWeight) : 0;
      return t;
    },
    //  网络防护检测权重
    netWeight() {
      let t = 0;
      t += this.portEffective ? this.getWeight(this.temp.portWeight) : 0;
      t += this.domainEffective ? this.getWeight(this.temp.domainWeight) : 0;
      t += this.desktopConfigured ? this.getWeight(this.temp.deskWeight) : 0;
      t += this.firewallConfigured ? this.getWeight(this.temp.fireWallWeight) : 0;
      return t;
    },
    //  杀毒软件是否已配置内容
    virusConfigured() {
      return this.virusInfoList.length > 0;
    },
    //  杀毒软件是否有效，有效则可配置权重
    virusEffective() {
      return this.virusConfigured && this.virusCheck;
    },
    //  软件必须安装是否已配置内容
    installSoftConfigured() {
      return this.softWareInstallTemp.softWareList.length > 0;
    },
    //  软件必须安装是否已有效，有效则可配置权重
    installSoftEffective() {
      return this.installSoftConfigured && this.installSoftCheck
    },
    //  软件禁止安装是否已配置内容
    limitInstallSoftConfigured() {
      return this.softWareInstallTemp.limitSoftWareList.length > 0
    },
    //  软件禁止安装是否有效
    limitInstallSoftEffective() {
      return this.limitInstallSoftConfigured && this.limitInstallSoftCheck
    },
    //  服务必须运行是否已配置内容
    serviceConfigured() {
      return this.serviceTemp.serviceList.length > 0
    },
    //  服务必须运行是否有效
    serviceEffective() {
      return this.serviceConfigured && this.whiteServerCheck
    },
    //  服务禁止运行是否已配置内容
    limitServiceConfigured() {
      return this.serviceTemp.limitServiceList.length > 0
    },
    //  服务禁止运行是否有效
    limitServiceEffective() {
      return this.limitServiceConfigured && this.blackServerCheck
    },
    //  进程必须运行是否已配置内容
    processConfigured() {
      return this.processTemp.processList.length > 0
    },
    //  进程必须运行是否有效
    processEffective() {
      return this.processConfigured && this.whiteProcessCheck
    },
    //  进程禁止运行是否已配置内容
    limitProcessConfigured() {
      return this.processTemp.limitProcessList.length > 0
    },
    //  进程禁止运行是否有效
    limitProcessEffective() {
      return this.limitProcessConfigured && this.blackProcessCheck
    },
    //  文件检查是否已配置内容
    fileConfigured() {
      return this.fileScanTemp.fileInfoList.length > 0
    },
    //  文件检查是否有效
    fileEffective() {
      return this.fileConfigured && this.fileScanCheck
    },
    //  注册检查是否已配置内容
    registryConfigured() {
      return this.regScanTemp.regInfoList.length > 0
    },
    //  注册检查是否有效
    registryEffective() {
      return this.registryConfigured && this.regScanCheck
    },
    //  操作系统是否已配置内容
    osTypeConfigured() {
      return (this.osTypeCheck1 && (this.windowsOsTypeNames.length > 0 | this.windowsServerOsTypeNames.length > 0)) || (this.osTypeCheck2 && (this.allowOsTypeTemp.versionName.length > 0 ||
        this.allowOsTypeTemp.serVersionName.length > 0))
    },
    //  操作系统是否有效
    osTypeEffective() {
      return this.osTypeConfigured && this.hostCheck
    },
    //  计算机名称规范检查是否已配置内容
    computeNameConfigured() {
      return this.computeNameTemp.regEx.length > 0
    },
    //  计算机名称规范检查是否有效
    computeNameEffective() {
      return this.computeNameConfigured && this.computerNameCheck
    },
    //  磁盘空间是否已配置内容
    diskConfigured() {
      return this.sysDiskCheck || this.totalDiskCheck || this.sysSizeCheck || this.totalSizeCheck
    },
    //  磁盘空间是否有效
    diskEffective() {
      return this.diskConfigured && this.diskCheck
    },
    //  共享配置检查是否已配置内容
    sharedConfigured() {
      return this.shareConfigTemp.shareFlag || this.shareConfigTemp.printer === '0'
    },
    //  共享配置检查是否有效
    sharedEffective() {
      return this.sharedConfigured && this.sharedCheck
    },
    //  禁用来宾账号是否已配置内容 （有效）
    guestConfigured() {
      return this.guestCheck
    },
    //  屏幕保护是否已配置内容 （有效）
    screenConfigured() {
      return this.screenCheck
    },
    //  域环境是否已配置内容
    domainConfigured() {
      // return this.domainTemp.domainNameList.length > 0 || this.domainTemp.passAccountList.length > 0 || this.domainTemp.noPassAccountList.length > 0
      return this.domainCheck;
    },
    //  域环境是否有效
    domainEffective() {
      return this.domainCheck
    },
    //  端口检查是否已配置内容
    portConfigured() {
      return this.portTemp.fixPorts.length > 0 || this.portTemp.selfPorts.length > 0
    },
    //  端口检查是否有效
    portEffective() {
      return this.portConfigured && this.portCheck
    },
    //  防火墙检查是否已配置内容 （有效）
    firewallConfigured() {
      return this.firewallCheck
    },
    //  远程桌面检查是否已配置内容 （有效）
    desktopConfigured() {
      return this.desktopCheck
    },
    //  权重占比显示
    weightRangShow() {
      const { lowRiskWeight, mediumRiskWeight, highRiskWeight } = this.temp
      const isNumber = typeof lowRiskWeight === 'number' && typeof mediumRiskWeight === 'number' && typeof highRiskWeight === 'number'
      return isNumber && (lowRiskWeight + mediumRiskWeight + highRiskWeight === 100)
    },
    //  权重已配置并且之和不等于100才显示错误
    validWeightError() {
      const { lowRiskWeight, mediumRiskWeight, highRiskWeight } = this.temp
      const isNumber = typeof lowRiskWeight === 'number' && typeof mediumRiskWeight === 'number' && typeof highRiskWeight === 'number'
      const reach100 = lowRiskWeight + mediumRiskWeight + highRiskWeight === 100
      return isNumber && !reach100;
    },
    regExArray() {
      const list = []
      this.regDefaultArray.forEach(item => {
        list.push({ value: item.regEx, regDes: item.regDes, i18n: item.i18n })
      })
      return list;
    },
    regDesArray() {
      const list = []
      this.regDefaultArray.forEach(item => {
        list.push({ value: item.i18n, regDes: item.regDes, regEx: item.regEx })
      })
      return list;
    }
  },
  created() {
    //  获取销售模块信息
    if (this.$store.getters.saleModuleIds && this.$store.getters.saleModuleIds.length == 0) {
      this.$store.dispatch('commonData/setSaleModuleIds')
    }
    this.initVirusMap()
    this.windowsOsTypes = this.supportOsTypes.filter(item => item.type === 1);
    this.windowsServerOsTypes = this.supportOsTypes.filter(item => item.type === 2);
  },
  activated() {
  },
  deactivated() {
  },
  methods: {
    initVirusMap() {
      this.virusSoftwareList.forEach(item => { this.virusMap[item.name] = item.label })
    },
    //  检测周期
    timeChange(time) {
      if (time !== -2) {
        this.temp.checkDay = time
        this.$refs.dataForm.validateField('selfTime')
      } else {
        this.selfTime = 15
      }
    },
    selfTimeChange(time) {
    },
    //   策略名称校验
    async nameValid(rule, value, callback) {
      //  如果为另存为时，不校验策略名称
      if (!this.copyFlag) {
        if (value === null || value === '' || value.trim() === '') {
          callback(this.$t('pages.required1') + '')
          this.setSkipData('0', null)
        } else if (value.length > 0) {
          const res = await getByName(value);
          if (this.operator === 'create' && res && res.data) {
            callback(new Error(this.$t('valid.sameName') + ''))
            this.setSkipData('0', null)
          } else if (this.operator === 'update' && res && res.data && res.data.name !== this.temp.name) {
            callback(new Error(this.$t('valid.sameName') + ''))
            this.setSkipData('0', null)
          }
        }
      }
      callback();
    },
    timeTypeValid(rule, value, callback) {
      if (this.timeType === null) {
        callback(new Error(this.$t('pages.required1')))
        this.setSkipData('0', null)
      }
      callback();
    },
    selfTimeValid(rule, value, callback) {
      if (this.timeType === -2 && (this.selfTime === undefined || this.selfTime === null)) {
        callback(new Error(this.$t('pages.required1')))
        this.setSkipData('0', null)
      }
      callback();
    },
    //  计算机名称规范检查-正则表达式
    regExValid: async function(rule, value, callback) {
      if (this.computerNameCheck && this.computeNameTemp.regEx.length === 0) {
        callback(new Error(this.$t('pages.regularExpressionNotNull') + ''))
        this.setSkipData('2', 'computerName')
      } else if (this.computeNameTemp.regEx !== 0) {
        const res = await isValidRegex(this.computeNameTemp.regEx);
        if (!res.data) {
          callback(new Error(this.$t('pages.regularExpressionMsg') + ''));
          this.setSkipData('2', 'computerName')
        }
      }
      callback();
    },
    //  计算机名称规范检查-正则表达式描述
    regDesValid(rule, value, callback) {
      if (this.computerNameCheck && this.computeNameTemp.regDes.length === 0) {
        callback(new Error(this.$t('pages.regularDescNotNull') + ''))
        this.setSkipData('2', 'computerName')
      }
      callback();
    },
    //  磁盘使用空间检查-系统磁盘的使用空间比例校验
    sysDiskValid(rule, value, callback) {
      if (this.sysDiskCheck && !this.diskDetectTemp.sysDisk) {
        callback(new Error(this.$t('pages.useSpaceRatioNotNull') + ''))
        this.setSkipData('2', 'disk')
      }
      callback();
    },
    //  磁盘使用空间检查-系统磁盘的使用空间校验
    sysSizeValid(rule, value, callback) {
      if (this.sysSizeCheck && !this.diskDetectTemp.sysSize) {
        callback(new Error(this.$t('pages.useSpaceNotNull') + ''))
        this.setSkipData('2', 'disk')
      }
      callback();
    },
    //  磁盘使用空间检查-所有磁盘的使用空间比例校验
    totalDiskValid(rule, value, callback) {
      if (this.diskCheck && this.totalDiskCheck && !this.diskDetectTemp.totalDisk) {
        callback(new Error(this.$t('pages.useSpaceRatioNotNull') + ''))
        this.setSkipData('2', 'disk')
      }
      callback();
    },
    //  磁盘使用空间检查-所有磁盘的使用空间校验
    totalSizeValid(rule, value, callback) {
      if (this.diskCheck && this.totalSizeCheck && !this.diskDetectTemp.totalSize) {
        callback(new Error(this.$t('pages.useSpaceNotNull') + ''))
        this.setSkipData('2', 'disk')
      }
      callback();
    },
    //  计算机屏幕保护检查-等待时间校验
    waitTimeValid(rule, value, callback) {
      if (this.screenCheck && this.screenTemp.timeCheck && !this.screenTemp.waitTime) {
        callback(new Error(this.$t('pages.waitTimeNotNull') + ''))
        this.setSkipData('2', 'screen')
      }
      callback();
    },
    //  检测项权重分配校验
    weightValid(rule, value, callback) {
      if (value === undefined || value === null) {
        callback(new Error(' '));
        this.setSkipData('5', null)
        this.weightDistributionError = true
      }
      callback();
    },
    //  风险权重校验
    riskWeightValid(rule, value, callback) {
      if (value === undefined || value === null) {
        callback(new Error(this.$t('pages.riskWeightNotNull')))
        this.setSkipData('6', null)
      }
      callback();
    },
    //  1：表示软件安全检测，2-主机安全检测 3-网络安全检测
    handleAllCheckChange(type) {
      if (type === 1) {
        this.softIndeterminate = false
        const value = this.softwareSecurityCheck
        this.virusCheck = value
        this.installSoftCheck = value
        this.limitInstallSoftCheck = value
        this.whiteServerCheck = value
        this.blackServerCheck = value
        this.whiteProcessCheck = value
        this.blackProcessCheck = value
        this.fileScanCheck = value
        this.regScanCheck = value
      } else if (type === 2) {
        this.hostIndeterminate = false
        const value = this.hostSecurityCheck
        this.hostCheck = value
        this.computerNameCheck = value
        this.diskCheck = value
        this.sharedCheck = value
        this.guestCheck = value
        this.screenCheck = value
      } else if (type === 3) {
        this.netIndeterminate = false
        const value = this.netSecurityCheck
        this.portCheck = value
        this.domainCheck = value
        this.firewallCheck = value
        this.desktopCheck = value
      }
    },
    // 软件安全检测 统计选中的个数和总个数  type=1统计check（已勾选的），type=2统计已配置项（已勾选+已配置检测项信息）
    countSoftCheckCount(type) {
      let checkedCount = 0;
      if (type === 1) {
        checkedCount = this.getSum([
          this.virusCheck, this.installSoftCheck, this.limitInstallSoftCheck, this.whiteServerCheck,
          this.blackServerCheck, this.whiteProcessCheck, this.blackProcessCheck, this.fileScanCheck, this.regScanCheck])
      } else {
        checkedCount = this.getSum([
          this.virusEffective, this.installSoftEffective, this.limitInstallSoftEffective, this.serviceEffective,
          this.limitServiceEffective, this.processEffective, this.limitProcessEffective, this.fileEffective, this.registryEffective])
      }
      return { count: checkedCount, total: 9 };
    },
    // 主机安全检测 统计选中的个数和总个数  type=1统计check（已勾选的），type=2统计已配置项（已勾选+已配置检测项信息）
    countHostCheckCount(type) {
      let checkedCount = 0
      if (type === 1) {
        checkedCount = this.getSum([this.hostCheck, this.computerNameCheck, this.diskCheck, this.sharedCheck, this.guestCheck, this.screenCheck])
      } else {
        checkedCount = this.getSum([this.osTypeEffective, this.computeNameEffective, this.diskEffective, this.sharedEffective, this.guestConfigured, this.screenConfigured])
      }
      return { count: checkedCount, total: 6 };
    },
    // 网络安全防护检测 统计选中的个数和总个数  type=1统计check（已勾选的），type=2统计有效项（已勾选+已配置检测项信息）
    countNetCheckCount(type) {
      let checkedCount = 0
      if (type === 1) {
        checkedCount = this.getSum([this.portCheck, this.domainCheck, this.firewallCheck, this.desktopCheck])
      } else {
        checkedCount = this.getSum([this.portEffective, this.domainEffective, this.firewallConfigured, this.desktopConfigured])
      }
      return { count: checkedCount, total: 4 };
    },
    getSum(array) {
      // JavaScript中，对于布尔值的运算会有一个隐性转换，true -> 1 , false -> 0
      return array.reduce((sum, cur) => sum + !!cur, 0)
    },
    configuredNum(type) {
      const countFunc = { 1: 'countSoftCheckCount', 2: 'countHostCheckCount', 3: 'countNetCheckCount' }
      const result = this[countFunc[type]](1)
      const { count, total } = result
      return `(${count}/${total})`
    },
    //  1：表示软件安全检测，2-主机安全检测 3-网络安全检测
    handleSingeCheckChange(type) {
      const countFunc = { 1: 'countSoftCheckCount', 2: 'countHostCheckCount', 3: 'countNetCheckCount' }
      const securityCheck = { 1: 'softwareSecurityCheck', 2: 'hostSecurityCheck', 3: 'netSecurityCheck' }
      const indeterminate = { 1: 'softIndeterminate', 2: 'hostIndeterminate', 3: 'netIndeterminate' }
      const result = this[countFunc[type]](1)
      const { count, total } = result
      this.$set(this, securityCheck[type], count === total)
      this.$set(this, indeterminate[type], count > 0 && count < total)
    },
    //  杀毒软件名称多语言化
    killSoftNameFormatter(row, data) {
      return this.virusMap[row.name]
    },
    //  杀毒软件配置内容格式
    killVirusSoftWareFormatter(row, data) {
      let result = ''
      const { name, softVersion, softSymbol, softwareId, repairMethod } = row
      //  杀毒软件名称
      result += `${this.$t('pages.killSoftName')}: ${this.virusMap[name]}; `
      //  杀毒软件版本号
      const hasVersion = softVersion.length > 0 && !!softSymbol
      result += hasVersion ? `${this.$t('pages.killSoftwareVersion')}: ${this.getKillSymbol(softSymbol)}${softVersion}; ` : ''
      //  是否检测病毒版本库
      result += this.detectVirusRepository(row)
      //  修复安装包
      const specified = softwareId ? this.$t('pages.haveSpecified') : this.$t('pages.notSpecified')
      result += `${this.$t('pages.repairFile')}: ${specified}; `
      //  修复指南
      const repairGuide = repairMethod !== undefined && repairMethod !== null && repairMethod.length === 0 ? this.$t('pages.termSecurityNotConfig') : repairMethod
      result += `${this.$t('pages.repairGuide')}: ${repairGuide}`
      return result;
    },
    //  杀毒软件-获取运算符名
    getKillSymbol(symbol) {
      let result = '';
      if (!!symbol && symbol.length > 0) {
        switch (symbol) {
          case '1': result = this.$t('pages.termSecurityGreaterThan'); break;   //  大于
          case '2': result = this.$t('pages.termSecurityEqual'); break;   //  等于
          case '3': result = this.$t('pages.termSecurityLessThan'); break;   //  小于
          case '4': result = this.$t('pages.termSecurityGreaterThanOrEqual'); break;   //  大于等于
          default: result = '';
        }
      }
      return result;
    },
    detectVirusRepository(data) {
      const { esetType, esetVersion, esetSymbol } = data
      switch (esetType) {
        case '0':
          return `${this.$t('pages.virusLibraryVersion')}: ${this.$t('pages.doNotDetect')}; `
        case '1':
          return (esetVersion.length > 0 ? `${this.$t('pages.virusLibraryVersion')}: ${this.getKillSymbol(esetSymbol)}${esetVersion}; ` : '')
        case '2':
          return (esetVersion !== undefined && esetVersion !== null ? `${this.$t('pages.virusVersionUpdateDateMsg')}: ${esetVersion}; ` : '')
        default:
          return ''
      }
    },
    //  杀毒软件检查-添加
    killAddAppClick() {
      this.$refs.virusDlg.show(null, this.virusInfoList, 1)
    },
    //  杀毒软件检测-删除
    killDeleteAppClick() {
      const datas = this.$refs.killVirusList.getSelectedDatas();
      if (datas.length > 0) {
        this.virusInfoList = this.virusInfoList.filter(data => datas.findIndex(item => item.name === data.name) === -1)
      }
      this.killSelected = false
    },
    //  选中杀毒软件列表里的元素
    killSelectedFun(datas) {
      this.killSelected = datas.length > 0
    },
    //  限制输入的最大值，返回可添加的数据
    limitList(oldList, addList, limitSize) {
      let list = []
      if (addList.length > 0) {
        const removeNum = oldList.length + addList.length - limitSize
        const allowAddNum = addList.length - removeNum;
        if (removeNum > 0) {
          let i = 1;
          addList.forEach(item => {
            if (i <= allowAddNum) {
              list.push(item);
            }
            i++
          })
          this.$message({
            message: this.$t('pages.listLimitNumMsg', { limitSize }) + '',
            type: 'warning',
            duration: 2000
          })
        } else {
          list = addList;
        }
      }
      return list;
    },
    //  杀毒软件单项回调
    virusSubmit(virusInfo) {
      //  判断是否已存在该软件
      const index = this.virusInfoList.findIndex(data => data.id === virusInfo.id);
      if (index > -1) {
        this.virusInfoList = this.virusInfoList.map(t => {
          return t.id === virusInfo.id ? virusInfo : t;
        });
      } else {
        const list = this.limitList(this.virusInfoList, [virusInfo], this.limitNum);
        if (list.length > 0) {
          this.virusInfoList.push(list[0])
        }
      }
    },
    //  软件安装，禁止安装单项回调
    installSubmit(installInfo, type) {
      const typeName = type === 1 ? 'softWareList' : 'limitSoftWareList'
      //  判断是否已存在该软件
      const index = this.softWareInstallTemp[typeName].findIndex(data => data.id === installInfo.id);
      if (index > -1) {
        this.softWareInstallTemp[typeName] = this.softWareInstallTemp[typeName].map(t => {
          return t.id === installInfo.id ? installInfo : t;
        });
      } else {
        const list = this.limitList(this.softWareInstallTemp[typeName], [installInfo], this.limitNum);
        if (list.length > 0) {
          this.softWareInstallTemp[typeName].push(list[0])
        }
      }
      //   过滤相同软件名称，存在全版本与其它版本时，只保留全版本
      const allVersion = this.softWareInstallTemp[typeName].find(item => item.softName === installInfo.softName && item.softVersion === '*.*') || null
      //  其它版本
      const otherVersion = this.softWareInstallTemp[typeName].filter(item => item.softName === installInfo.softName && item.softVersion !== '*.*') || []
      if (allVersion && otherVersion.length > 0) {
        this.$message({
          message: '已过滤相同版本的配置信息',
          type: 'warning'
        })
        this.softWareInstallTemp[typeName] = this.softWareInstallTemp[typeName].filter(item => item.softName !== installInfo.softName) || [];
        this.softWareInstallTemp[typeName].push(allVersion)
      }
    },
    //  软件安装检查  type=1：必须安装，type=2：禁止安装
    installAddAppClick(type) {
      this.$refs.installDlg.show(null, type, 1, { mustList: this.softWareInstallTemp.softWareList, stopList: this.softWareInstallTemp.limitSoftWareList })
    },
    installSelectedFun(datas) {
      this.installSelected = datas.length > 0
    },
    limitInstallSelectedFun(datas) {
      this.limitInstallSelected = datas.length > 0
    },
    //  软件安装检查，删除操作
    installDeleteAppClick(type) {
      const typeName = type === 1 ? 'softWareList' : 'limitSoftWareList'
      const keys = this.$refs[typeName].getSelectedKeys();
      if (keys.length > 0) {
        this.softWareInstallTemp[typeName] = this.softWareInstallTemp[typeName].filter(data => !keys.includes(data.id))
      }
      if (type === 1) {
        this.installSelected = false
      } else {
        this.limitInstallSelected = false
      }
    },
    softWareVersionFormatter(row, data) {
      if (row.softVersion && row.softVersion === '*.*') {
        return this.$t('pages.allVersion')
      }
      return row.softVersion
    },
    //  软件资产导入
    handleSoftware(type) {
      this.$refs.softwareTable.show(null, type)
    },
    softwareImportSubmitEnd(datas, type) {
      if (datas) {
        let allowAddList = []
        let id = Date.now();
        let flag = false;
        if (type === 'install') {
          datas.forEach(item => {
            if (this.validExitsSoftwareList(item.softwareName, item.softwareVersion, 'install')) {
              flag = true;
            } else {
              allowAddList.push({ id: id, softName: item.softwareName || '', softVersion: item.softwareVersion || '', softwareId: null })
              id++;
            }
          })
        } else if (type === 'limitInstall') {
          datas.forEach(item => {
            if (this.validExitsSoftwareList(item.softwareName, item.softwareVersion, 'limitInstall')) {
              flag = true
            } else {
              allowAddList.push({ id: id, softName: item.softwareName || '', softVersion: item.softwareVersion || '' })
              id++;
            }
          })
        }
        if (flag) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.installDetectionAddDataMsg'),
            type: 'warning',
            duration: 2000
          })
        }
        const t = type === 'install' ? 'softWareList' : 'limitSoftWareList'
        allowAddList = this.limitList(this.softWareInstallTemp[t], allowAddList, this.limitNum);
        allowAddList.forEach(item => {
          this.softWareInstallTemp[t].push(item);
        })

        //  查询存在全版本信息的数据
        const allVersions = new Map();
        this.softWareInstallTemp[t].forEach(item => {
          if (item.softVersion === '*.*') {
            allVersions.set(item.softName, item);
          }
        })
        if (allVersions.size > 0) {
          //  查询没有全版本的软件名称
          const list = this.softWareInstallTemp[t].filter(item => !allVersions.get(item.softName));
          allVersions.forEach(function(value, key) {
            list.push(value)
          })
          this.softWareInstallTemp[t] = list;
        }
      }
    },
    //  校验指定软件名称是否在黑白名单中
    validExitsSoftwareList(softName, softVersion, type) {
      const whiteList = this.softWareInstallTemp.softWareList || [];
      const blackList = this.softWareInstallTemp.limitSoftWareList || [];
      const whiteIndex = whiteList.findIndex(item => item.softName === softName && (type !== 'install' || item.softVersion === softVersion))
      const blackIndex = blackList.findIndex(item => item.softName === softName && (type !== 'limitInstall' || item.softVersion === softVersion));
      return whiteIndex > -1 || blackIndex > -1
    },
    serviceNameFormatter(row, data) {
      if (row.serviceName && row.serviceName.includes('|')) {
        return row.serviceName.split('|')[0]
      }
      return row.serviceName || ''
    },
    //  依赖服务
    dependentServiceNameFormatter(row, data) {
      if (row.serviceName && row.serviceName.indexOf('|') > -1) {
        const index = row.serviceName.indexOf('|')
        return row.serviceName.substr(index + 1).replaceAll('|', ',');
      }
      return ''
    },
    startTypeFormatter(row, data) {
      let result = this.$t('pages.termSecurityNotConfig')
      if (row.startType !== null) {
        switch (row.startType) {
          case 0: result = this.$t('pages.termSecurityNotConfig'); break;
          case 2: result = this.$t('pages.termSecurityAuto'); break;
          case 3: result = this.$t('pages.termSecurityManual'); break;
          case 4: result = this.$t('pages.termSecurityDisable'); break;
        }
      }
      return result;
    },
    //  端口规范校验
    portValid(rule, value, callback) {
      //  只能输入1-65535， 输入的是端口范围时，格式为：port1-port2  port1<=port2
      let error = false
      if (value === '0') {
        error = true;
      } else if (value.length > 0) {
        var porttest = /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;
        if (value.includes('-')) {
          const rang = value.split('-');
          if (rang.length === 2) {
            if (!porttest.test(rang[0]) || !porttest.test(rang[1])) {
              error = true
            } else if (rang[0] > rang[1]) {
              error = true;
            }
          } else {
            error = true;
          }
        } else if (!porttest.test(value)) {
          error = true;
        }
      }
      if (error) {
        callback(new Error(this.$t('pages.termSecurityPortErrorMsg') + ''));
      } else {
        callback();
      }
    },
    //  校验添加的用户是否在未通过名单中
    passValidator(rule, value, callback) {
      if (this.domainTemp.noPassAccountList && this.domainTemp.noPassAccountList.includes(value)) {
        callback(new Error(value + this.$t('pages.termSecurityDomainErrorMsg1')))
      }
      callback();
    },
    //  校验添加的用户是否在通过名单中
    noPassValidator(rule, value, callback) {
      if (this.domainTemp.passAccountList && this.domainTemp.passAccountList.includes(value)) {
        callback(new Error(value + this.$t('pages.termSecurityDomainErrorMsg2')))
      }
      callback();
    },
    fixPlanDlg() {
      this.fixPlanShow = !this.fixPlanShow
      this.fixPlanTitle = !this.fixPlanShow ? this.$t('pages.showFixPlanView') : this.$t('pages.hiddenFixPlanView')
      //  跳转到修复指南
      if (!this.fixPlanShow && this.activeName === '4') {
        this.activeName = '0'
      }
    },
    //  添加修复指南
    addFixPlanClick(content, type) {
      this.$refs.fixPlanDlg.show(content, type)
    },
    serviceSelectedFun(datas) {
      this.serviceSelected = datas.length > 0
    },
    limitServiceSelectedFun(datas) {
      this.limitServiceSelected = datas.length > 0
    },
    //  服务运行检查
    addServiceClick(type) {
      this.$refs.serviceDlg.show(null, type, 1, { mustList: this.serviceTemp.serviceList, stopList: this.serviceTemp.limitServiceList }, type === 2)
    },
    //  必须运行服务，禁止运行服务单项回调
    serviceSubmit(serviceInfo, type) {
      const typeName = type === 1 ? 'serviceList' : 'limitServiceList'
      //  判断是否已存在该服务
      const index = this.serviceTemp[typeName].findIndex(data => data.id === serviceInfo.id);
      if (index > -1) {
        this.serviceTemp[typeName] = this.serviceTemp[typeName].map(t => {
          return t.id === serviceInfo.id ? serviceInfo : t;
        });
      } else {
        const list = this.limitList(this.serviceTemp[typeName], [serviceInfo], this.limitNum);
        if (list.length > 0) {
          this.serviceTemp[typeName].push(list[0])
        }
      }
    },
    //  服务检查，删除操作
    serviceDeleteAppClick(type) {
      const typeName = type === 1 ? 'serviceList' : 'limitServiceList'
      const keys = this.$refs[typeName].getSelectedKeys();
      if (keys.length > 0) {
        this.serviceTemp[typeName] = this.serviceTemp[typeName].filter(data => !keys.includes(data.id))
      }
      if (type === 1) {
        this.serviceSelected = false
      } else {
        this.limitServiceSelected = false
      }
    },
    //  必须运行进程
    processSelectedFun(datas) {
      this.processSelected = datas.length > 0
    },
    //  禁止运行进程
    limitProcessSelectedFun(datas) {
      this.limitProcessSelected = datas.length > 0
    },
    addProcessClick(type) {
      this.$refs.processDlg.show(null, type, 1, { mustList: this.processTemp.processList, stopList: this.processTemp.limitProcessList })
    },
    //  必须运行进程，禁止运行进程单项回调
    processSubmit(processInfo, type) {
      const typeName = type === 1 ? 'processList' : 'limitProcessList'
      //  判断是否已存在该进程
      const index = this.processTemp[typeName].findIndex(data => data.id === processInfo.id);
      if (index > -1) {
        this.processTemp[typeName] = this.processTemp[typeName].map(t => {
          return t.id === processInfo.id ? processInfo : t;
        });
      } else {
        const list = this.limitList(this.processTemp[typeName], [processInfo], this.limitNum);
        if (list.length > 0) {
          this.processTemp[typeName].push(list[0])
        }
      }
    },
    //  进程删除
    processDeleteAppClick(type) {
      const typeName = type === 1 ? 'processList' : 'limitProcessList'
      const keys = this.$refs[typeName].getSelectedKeys();
      if (keys.length > 0) {
        this.processTemp[typeName] = this.processTemp[typeName].filter(data => !keys.includes(data.id))
      }
      if (type === 1) {
        this.processSelected = false
      } else {
        this.limitProcessSelected = false
      }
    },
    // 进程检查 应用程序库导入
    appImport(type) {
      this.$refs.appSelectDlg.show(type)
    },
    //  校验指定进程名称是否在黑白名单中
    validExitsProcessList(processName) {
      const whiteList = this.processTemp.processList || [];
      const blackList = this.processTemp.limitProcessList || [];
      const whiteIndex = whiteList.findIndex(item => item.procName === processName)
      const blackIndex = blackList.findIndex(item => item.procName === processName);
      return whiteIndex > -1 || blackIndex > -1
    },
    // 进程检查 应用程序库导入
    async appendApp(datas, type) {
      if (datas) {
        let allowAddList = []
        let id = Date.now();
        let flag = false;
        if (type === 'whiteProcess') {
          datas.forEach(item => {
            if (this.validExitsProcessList(item.processName)) {
              flag = true;
            } else {
              allowAddList.push(
                { id: id,
                  procName: item.processName || '',
                  procMd: item.quicklyMd5 || '',
                  atbMd5: item.propertyMd5 || '',
                  controlCode: item.propertyMark ? item.propertyMark + 3 : 0,
                  scanType: item.scanType,
                  procDesc: item.fileDescription,
                  softwareId: null })
              id++;
            }
          })
        } else if (type === 'blackProcess') {
          datas.forEach(item => {
            if (this.validExitsProcessList(item.processName)) {
              flag = true
            } else {
              allowAddList.push(
                { id: id,
                  procName: item.processName || '',
                  procMd: item.quicklyMd5 || '',
                  atbMd5: item.propertyMd5 || '',
                  controlCode: item.propertyMark ? item.propertyMark + 3 : 0,
                  scanType: item.scanType,
                  procDesc: item.fileDescription })
              id++;
            }
          })
        }
        if (flag) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.processDetectionAddDataMsg'),
            type: 'warning',
            duration: 2000
          })
        }
        const t = type === 'whiteProcess' ? 'processList' : 'limitProcessList'
        allowAddList = this.limitList(this.processTemp[t], allowAddList, this.limitNum);
        allowAddList.forEach(item => {
          this.processTemp[t].push(item);
        })
      }
    },
    //  文件检查-添加文件检查信息
    fileClick() {
      this.$refs.fileDlg.show(null, 1, this.fileScanTemp.fileInfoList);
    },
    //  文件检查-删除文件检查信息
    deleteFileClick() {
      const datas = this.$refs.fileInfoList.getSelectedKeys()
      if (datas.length > 0) {
        this.fileScanTemp.fileInfoList = this.fileScanTemp.fileInfoList.filter(item => !datas.includes(item.id))
      }
      this.fileSelected = false
    },
    //  文件检查 单项回调
    fileSubmit(fileInfo) {
      //  判断是否已存在该配置项
      const index = this.fileScanTemp.fileInfoList.findIndex(data => data.id === fileInfo.id);
      if (index > -1) {
        this.fileScanTemp.fileInfoList = this.fileScanTemp.fileInfoList.map(t => {
          return t.id === fileInfo.id ? fileInfo : t;
        });
      } else {
        const list = this.limitList(this.fileScanTemp.fileInfoList, [fileInfo], this.limitNum);
        if (list.length > 0) {
          this.fileScanTemp.fileInfoList.push(list[0])
        }
      }
    },
    //  文件检测-操作
    fileExistFormatter(row, data) {
      if (row.fileExist !== undefined && row.fileExist !== null) {
        return row.fileExist === 0 ? this.$t('pages.notExist') : row.fileExist === 1 ? this.$t('pages.exist') : ''
      }
    },
    fileSelectedFun(datas) {
      this.fileSelected = datas.length > 0;
    },
    regSelectedFun(datas) {
      this.regSelected = datas.length > 0;
    },
    registryClick() {
      this.$refs.registryDlg.show(null, this.regScanTemp.regInfoList, 1);
    },
    deleteRegClick() {
      const datas = this.$refs.regInfoList.getSelectedKeys()
      if (datas.length > 0) {
        this.regScanTemp.regInfoList = this.regScanTemp.regInfoList.filter(item => !datas.includes(item.id))
      }
      this.regSelected = false
    },
    //  注册表检查-添加注册表信息
    registrySubmit(regInfo) {
      //  判断是否已存在该配置项
      const index = this.regScanTemp.regInfoList.findIndex(data => data.id === regInfo.id);
      if (index > -1) {
        this.regScanTemp.regInfoList = this.regScanTemp.regInfoList.map(t => {
          return t.id === regInfo.id ? regInfo : t;
        });
      } else {
        const list = this.limitList(this.regScanTemp.regInfoList, [regInfo], this.limitNum);
        if (list.length > 0) {
          this.regScanTemp.regInfoList.push(list[0])
        }
      }
    },
    //  注册表检查-扫描类型
    registryTypeFormatter(row, data) {
      return row.regType === 0 ? this.$t('pages.registryKeyItem') : row.regType === 1 ? this.$t('pages.registryValue') : ''
    },
    //  注册表检查-注册表项
    registryRegItemFormatter(row, data) {
      if (row.regType === 1) {
        const index = row.regItem.toString().lastIndexOf('\\');
        return index > -1 ? row.regItem.substr(0, index) : '';
      } else {
        return row.regItem;
      }
    },
    //  注册表检查-注册表键
    registryRegKeyFormatter(row, data) {
      if (row.regType === 1) {
        const index = row.regItem.toString().lastIndexOf('\\');
        return index > -1 ? row.regItem.substr(index + 1) : '';
      }
      return ''
    },
    //  注册表检查- 操作项格式化
    registryOperatorFormatter(row, data) {
      const index = this.registryOperators.findIndex(data => data.id === row.operator);
      return index > -1 ? this.registryOperators[index].label : ''
    },
    //  操作系统检查
    osTypeCheckChange(type) {
      const flag = this.osTypeCheck1 && this.osTypeCheck2
      if (type === 1 && flag) {
        this.osTypeCheck2 = false
      } else if (type === 2 && flag) {
        this.osTypeCheck1 = false
      }
    },
    handleOsTypeCheckChange1(value) {
      const checkedCount = value.length
      this.osTypeCheckAll1 = checkedCount === this.windowsOsTypes.length;
      this.osTypeIndeterminate1 = checkedCount > 0 && checkedCount < this.windowsOsTypes.length
    },
    handleOsTypeCheckChange2(value) {
      const checkedCount = value.length
      this.osTypeCheckAll2 = checkedCount === this.windowsServerOsTypes.length;
      this.osTypeIndeterminate2 = checkedCount > 0 && checkedCount < this.windowsServerOsTypes.length
    },
    //  数字版操作系统-全选
    osTypeCheckAllClick1(data) {
      this.windowsOsTypeNames.splice(0);
      if (data) {
        this.windowsOsTypes.forEach(item => { this.windowsOsTypeNames.push(item.os) })
      }
      this.osTypeIndeterminate1 = false
    },
    //  Server版本操作系统-全选
    osTypeCheckAllClick2(data) {
      this.windowsServerOsTypeNames.splice(0);
      if (data) {
        this.windowsServerOsTypes.forEach(item => { this.windowsServerOsTypeNames.push(item.os) })
      }
      this.osTypeIndeterminate2 = false
    },
    //  计算机名称规范检查
    regExChange(value) {
      if (value) {
        const index = this.regDefaultArray.findIndex(item => item.regEx === value)
        if (index > -1) {
          this.computeNameTemp.regDes = this.regDefaultArray[index].regDes
        }
      }
    },
    regDesChange(value) {
      if (value) {
        const index = this.regDefaultArray.findIndex(item => item.regDes === value)
        if (index > -1) {
          this.computeNameTemp.regEx = this.regDefaultArray[index].regEx
        }
      }
    },
    computeNameRegExSearch(queryString, cb) {
      const restaurants = this.regExArray;
      const results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    computeNameRegDesSearch(queryString, cb) {
      const restaurants = this.regDesArray;
      const results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.indexOf(queryString) === 0);
      };
    },
    regExHandleSelect(node) {
      this.computeNameTemp.regEx = node.value
      this.computeNameTemp.regDes = node.i18n
      this.$refs['dataForm'].validateField(['regEx', 'regDes'])
    },
    regDesHandleSelect(node) {
      this.computeNameTemp.regEx = node.regEx
      this.computeNameTemp.regDes = node.value
      this.$refs['dataForm'].validateField(['regEx', 'regDes'])
    },
    //  启用屏保功能- 检测计算机是否启用屏保功能勾选框
    screenCheckChange(value) {
      if (!value) {
        this.screenTemp.showLogin = 0
      }
    },
    //  跳转到计算机个性化界面
    screenToClick() {
      this.$router.push('/behaviorManage/ComputerManage/PersonalizePolicy')
    },
    //  全选端口
    portCheckAllClick(data) {
      this.portTemp.fixPorts.splice(0);
      if (data) {
        this.fixPortList.forEach(item => { this.portTemp.fixPorts.push(item.port) })
      }
      this.portIndeterminate = false
    },
    handlePortCheckChange(value) {
      const checkedCount = value.length
      this.portCheckAll = checkedCount === this.fixPortList.length;
      this.portIndeterminate = checkedCount > 0 && checkedCount < this.fixPortList.length
    },
    // 修复指南总览
    allFixPlanSubmit(data) {
      const index = this.configuredFixPlans.findIndex(item => item.id === data.id);
      if (index > -1) {
        this.configuredFixPlans = this.configuredFixPlans.map(item => {
          return item.id === data.id ? data : item;
        })
      } else {
        this.configuredFixPlans.push(data)
      }
      this.changeAllFixPlan()
    },
    fixPlanSelectedFun(datas) {
      this.fixPlanSelected = datas.length > 0
    },
    //  修复指南总览-添加
    addFixPlanAllClick() {
      this.$refs.allFixPlanDlg.show(this.configuredList, null, this.configuredFixPlans, 1);
    },
    //  修复指南总览-删除
    deleteFixPlanClick() {
      const datas = this.$refs.fixPlanList.getSelectedDatas()
      if (datas.length) {
        const configuredFixPlans = this.configuredFixPlans.filter(item => (datas.findIndex(t => t.id === item.id) === -1))
        this.configuredFixPlans.splice(0);
        configuredFixPlans.forEach(item => { this.configuredFixPlans.push(item) })
        this.changeAllFixPlan()
      }
    },
    //  改变检查项中的所有修复指南
    changeAllFixPlan() {
      this.clearAllFixPlan();
      this.configuredFixPlans.forEach(item => {
        this.changeFixPlan(item.fixPlan, item.dataId, item.typeKey, item.softName);
      })
    },
    //  改变检查项中的修复指南 例子： content:修复指南内容， type：Type1， typeLabel：杀毒软件检查， softName: 360杀毒
    changeFixPlan(content, type, typeLabel, softName) {
      if (type === 'Type1') {
        this.virusInfoList.forEach(item => {
          if (item.name === softName) {
            item.repairMethod = content
          }
        })
      } else {
        this.fixPlanDlgSubmit(content, typeLabel);
      }
    },
    //  清空所有检查项的修复指南
    clearAllFixPlan() {
      this.softWareInstallTemp.fixPlan = ''
      this.softWareInstallTemp.limitFixPlan = ''
      this.serviceTemp.fixPlan = ''
      this.serviceTemp.limitFixPlan = ''
      this.processTemp.fixPlan = ''
      this.processTemp.limitFixPlan = ''
      this.fileScanTemp.fixPlan = ''
      this.regScanTemp.fixPlan = ''
      this.allowOsTypeTemp.fixPlan = ''
      this.computeNameTemp.fixPlan = ''
      this.diskDetectTemp.fixPlan = ''
      this.shareConfigTemp.fixPlan = ''
      this.guestTemp.fixPlan = ''
      this.screenTemp.fixPlan = ''
      this.portTemp.fixPlan = ''
      this.domainTemp.fixPlan = ''
      this.firewallTemp.fixPlan = ''
      this.desktopTemp.fixPlan = ''
      this.virusInfoList.forEach(item => {
        item.repairMethod = ''
      })
    },
    //  修复指南添加回调  content:内容，type：修复指南所属的扫描项
    fixPlanDlgSubmit(content, type) {
      switch (type) {
        case '软件必须安装检查': this.softWareInstallTemp.fixPlan = content; break;
        case '软件禁止安装检查': this.softWareInstallTemp.limitFixPlan = content; break;
        case '服务白名单检查': this.serviceTemp.fixPlan = content; break;
        case '服务黑名单检查': this.serviceTemp.limitFixPlan = content; break;
        case '进程必须运行检查': this.processTemp.fixPlan = content; break;
        case '进程禁止运行检查': this.processTemp.limitFixPlan = content; break;
        case '文件检查': this.fileScanTemp.fixPlan = content; break;
        case '注册表检查': this.regScanTemp.fixPlan = content; break;
        case '操作系统检查': this.allowOsTypeTemp.fixPlan = content; break;
        case '计算机名称规范检查': this.computeNameTemp.fixPlan = content; break;
        case '磁盘使用空间检查': this.diskDetectTemp.fixPlan = content; break;
        case '共享配置检查': this.shareConfigTemp.fixPlan = content; break;
        case '来宾账户检查': this.guestTemp.fixPlan = content; break;
        case '计算机屏幕保护检查': this.screenTemp.fixPlan = content; break;
        case '端口检查': this.portTemp.fixPlan = content; break;
        case '域环境检查': this.domainTemp.fixPlan = content; break;
        case '防火墙检查': this.firewallTemp.fixPlan = content; break;
        case '远程桌面检查': this.desktopTemp.fixPlan = content; break;
      }
    },
    //  低风险ruleId获取
    getRiskRuleId(value) {
      this.temp.lowRiskAlarmId = value
    },
    //  中风险ruleId获取
    getMediumRuleId(value) {
      this.temp.mediumRiskAlarmId = value
    },
    //  高风险ruleId获取
    getHighRuleId(value) {
      this.temp.highRiskAlarmId = value
    },
    getLowChecked(check) {
      this.lowCheck = check === 1
    },
    getMediumChecked(check) {
      this.mediumCheck = check === 1
    },
    getHighChecked(check) {
      this.highCheck = check === 1
    },
    overviewTestItemFormatter(row, data) {
      if (row.dataId === 'Type1') {
        return row.label + '-' + this.virusMap[row.softName || '']
      }
      return row.label
    },
    activeNameChange() {
      //  统计已配置的检查项和有效的修复指南，（有效的修复指南：对应的检查项已配置并且已填写的修复指南）
      if (this.activeName === '4') {
        this.configuredFixPlans.splice(0);
        const configuredList = []
        //  检查杀毒软件检查是否配置，若已配置，统计配置的杀毒软件
        if (this.virusConfigured) {
          this.virusInfoList.forEach(item => {
            configuredList.push({ testItemId: 'Type1-' + item.name, dataId: 'Type1', typeKey: '杀毒软件检查', label: this.$t('pages.killSoftInspect'), softName: item.name, fixPlan: item.repairMethod })
          })
        }
        //  软件必须安装检查
        if (this.installSoftConfigured) {
          configuredList.push({ testItemId: 'Type2', dataId: 'Type2', typeKey: '软件必须安装检查', label: this.$t('pages.softwareMustInstallCheck'), fixPlan: this.softWareInstallTemp.fixPlan })
        }
        //  软件禁止安装检查
        if (this.limitInstallSoftConfigured) {
          configuredList.push({ testItemId: 'Type3', dataId: 'Type3', typeKey: '软件禁止安装检查', label: this.$t('pages.softwareNotInstallCheck'), fixPlan: this.softWareInstallTemp.limitFixPlan })
        }
        //  服务白名单检查
        if (this.serviceConfigured) {
          configuredList.push({ testItemId: 'Type4', dataId: 'Type4', typeKey: '服务白名单检查', label: this.$t('pages.termSecurityServerWhiteCheck'), fixPlan: this.serviceTemp.fixPlan })
        }
        //  服务黑名单检查
        if (this.limitServiceConfigured) {
          configuredList.push({ testItemId: 'Type5', dataId: 'Type5', typeKey: '服务黑名单检查', label: this.$t('pages.termSecurityServerBlackCheck'), fixPlan: this.serviceTemp.limitFixPlan })
        }
        //  进程必须运行检查
        if (this.processConfigured) {
          configuredList.push({ testItemId: 'Type6', dataId: 'Type6', typeKey: '进程必须运行检查', label: this.$t('pages.termSecurityProcessMustRunCheck'), fixPlan: this.processTemp.fixPlan })
        }
        //  进程禁止运行检查
        if (this.limitProcessConfigured) {
          configuredList.push({ testItemId: 'Type7', dataId: 'Type7', typeKey: '进程禁止运行检查', label: this.$t('pages.termSecurityProcessNotRunCheck'), fixPlan: this.processTemp.limitFixPlan })
        }
        //  文件检查
        if (this.fileConfigured) {
          configuredList.push({ testItemId: 'Type8', dataId: 'Type8', typeKey: '文件检查', label: this.$t('pages.termSecurityFileCheck'), fixPlan: this.fileScanTemp.fixPlan })
        }
        //  注册表检查
        if (this.registryConfigured) {
          configuredList.push({ testItemId: 'Type9', dataId: 'Type9', typeKey: '注册表检查', label: this.$t('pages.termSecurityRegistryCheck'), fixPlan: this.regScanTemp.fixPlan })
        }
        //  操作系统检查
        if (this.osTypeConfigured) {
          configuredList.push({ testItemId: 'Type10', dataId: 'Type10', typeKey: '操作系统检查', label: this.$t('pages.termSecurityHostCheck'), fixPlan: this.allowOsTypeTemp.fixPlan })
        }
        //  计算机名称规范检查
        if (this.computeNameConfigured) {
          configuredList.push({ testItemId: 'Type11', dataId: 'Type11', typeKey: '计算机名称规范检查', label: this.$t('pages.termSecurityComputerNameCheck'), fixPlan: this.computeNameTemp.fixPlan })
        }
        //  磁盘使用空间检查
        if (this.diskConfigured) {
          configuredList.push({ testItemId: 'Type12', dataId: 'Type12', typeKey: '磁盘使用空间检查', label: this.$t('pages.termSecurityDiskCheck'), fixPlan: this.diskDetectTemp.fixPlan })
        }
        //  共享配置检查
        if (this.sharedConfigured) {
          configuredList.push({ testItemId: 'Type13', dataId: 'Type13', typeKey: '共享配置检查', label: this.$t('pages.termSecuritySharedCheck'), fixPlan: this.shareConfigTemp.fixPlan })
        }
        //  来宾账户检查
        if (this.guestConfigured) {
          configuredList.push({ testItemId: 'Type14', dataId: 'Type14', typeKey: '来宾账户检查', label: this.$t('pages.termSecurityGuestCheck'), fixPlan: this.guestTemp.fixPlan })
        }
        //  计算机屏幕保护检查
        if (this.screenConfigured) {
          configuredList.push({ testItemId: 'Type15', dataId: 'Type15', typeKey: '计算机屏幕保护检查', label: this.$t('pages.termSecurityScreenCheck'), fixPlan: this.screenTemp.fixPlan })
        }
        //  端口检查
        if (this.portConfigured) {
          configuredList.push({ testItemId: 'Type16', dataId: 'Type16', typeKey: '端口检查', label: this.$t('pages.termSecurityPortCheck'), fixPlan: this.portTemp.fixPlan })
        }
        //  域环境检查
        if (this.domainConfigured) {
          configuredList.push({ testItemId: 'Type17', dataId: 'Type17', typeKey: '域环境检查', label: this.$t('pages.termSecurityDomainCheck'), fixPlan: this.domainTemp.fixPlan })
        }
        //  防火墙检查
        if (this.firewallConfigured) {
          configuredList.push({ testItemId: 'Type18', dataId: 'Type18', typeKey: '防火墙检查', label: this.$t('pages.termSecurityFirewallCheck'), fixPlan: this.firewallTemp.fixPlan })
        }
        //  远程桌面检查
        if (this.desktopConfigured) {
          configuredList.push({ testItemId: 'Type19', dataId: 'Type19', typeKey: '远程桌面检查', label: this.$t('pages.termSecurityDesktopCheck'), fixPlan: this.desktopTemp.fixPlan })
        }
        //  设置id
        const id = Date.now()
        let i = 0;
        configuredList.forEach(item => {
          item.id = id + i;
          i++;
        })

        this.configuredList = configuredList;
        //  统计已配置修复指南的检查项
        this.configuredFixPlans = configuredList.filter(item => item.fixPlan !== null && item.fixPlan && item.fixPlan.length > 0);
      }
    },
    //  计算机权重百分比
    computeWeight(weight) {
      weight = weight || 0
      let t = 0;
      t += this.virusEffective ? this.getWeight(this.temp.virusWeight) : 0;
      t += this.installSoftEffective ? this.getWeight(this.temp.installWhiteWeight) : 0;
      t += this.limitInstallSoftEffective ? this.getWeight(this.temp.installBlackWeight) : 0;
      t += this.serviceEffective ? this.getWeight(this.temp.serverWhiteWeight) : 0;
      t += this.limitServiceEffective ? this.getWeight(this.temp.serverBlackWeight) : 0;
      t += this.processEffective ? this.getWeight(this.temp.processWhiteWeight) : 0
      t += this.limitProcessEffective ? this.getWeight(this.temp.processBlackWeight) : 0
      t += this.fileEffective ? this.getWeight(this.temp.fileWeight) : 0
      t += this.registryEffective ? this.getWeight(this.temp.regWeight) : 0;
      t += this.osTypeEffective ? this.getWeight(this.temp.osWeight) : 0;
      t += this.computeNameEffective ? this.getWeight(this.temp.computeNameWeight) : 0;
      t += this.guestConfigured ? this.getWeight(this.temp.guestWeight) : 0;
      t += this.diskEffective ? this.getWeight(this.temp.diskUseSpaceWeight) : 0;
      t += this.sharedEffective ? this.getWeight(this.temp.shareWeight) : 0;
      t += this.screenConfigured ? this.getWeight(this.temp.screenWeight) : 0;
      t += this.portEffective ? this.getWeight(this.temp.portWeight) : 0;
      t += this.domainEffective ? this.getWeight(this.temp.domainWeight) : 0;
      t += this.desktopConfigured ? this.getWeight(this.temp.deskWeight) : 0;
      t += this.firewallConfigured ? this.getWeight(this.temp.fireWallWeight) : 0
      if (isNaN(t) || t === 0) {
        return '0%';
      }
      const result = ((weight / t) * 100);
      return (result === 0 ? result : (result.toFixed(2)) || 0) + '%';
    },
    getWeight(weight) {
      weight = weight || 0
      return weight
    },
    //  计算权重占比范围    type = 1 : 低风险 type = 2 中风险  type = 3 高风险
    computeWeightRange(type) {
      const lowRiskWeightMin = 100 - (this.temp.lowRiskWeight ? this.temp.lowRiskWeight : 0);
      const lowMediumRiskWeightMin = lowRiskWeightMin - (this.temp.mediumRiskWeight ? this.temp.mediumRiskWeight : 0)
      if (type === 1) {
        if (!this.temp.lowRiskWeight) {
          return ''
        }
        return this.$t('pages.computeWeightRangLowMgs', { lowRiskWeightMin })
      } else if (type === 2) {
        if (!this.temp.mediumRiskWeight) {
          return ''
        }
        return this.$t('pages.computeWeightRangMediumMgs', { mediumRiskWeightMin: lowMediumRiskWeightMin, lowRiskWeightMin })
      } else if (type === 3) {
        if (!this.temp.highRiskWeight) {
          return ''
        }
        return this.$t('pages.computeWeightRangHighMgs', { mediumRiskWeightMin: lowMediumRiskWeightMin })
      }
      return ''
    },
    //  重新校验 检测项分配规则
    weightChange() {
      this.weightDistributionError = false
      const types = []
      if (this.virusEffective) {
        types.push('virusWeight')
      }
      if (this.installSoftEffective) {
        types.push('installWhiteWeight')
      }
      if (this.limitInstallSoftEffective) {
        types.push('installBlackWeight')
      }
      if (this.serviceEffective) {
        types.push('serverWhiteWeight')
      }
      if (this.limitServiceEffective) {
        types.push('serverBlackWeight')
      }
      if (this.processEffective) {
        types.push('processWhiteWeight')
      }
      if (this.limitProcessEffective) {
        types.push('processBlackWeight')
      }
      if (this.fileEffective) {
        types.push('fileWeight')
      }
      if (this.registryEffective) {
        types.push('regWeight')
      }
      if (this.osTypeEffective) {
        types.push('osWeight')
      }
      if (this.computeNameEffective) {
        types.push('computeNameWeight')
      }
      if (this.diskEffective) {
        types.push('diskUseSpaceWeight')
      }
      if (this.sharedEffective) {
        types.push('sharedWeight')
      }
      if (this.guestConfigured) {
        types.push('guestWeight')
      }
      if (this.screenConfigured) {
        types.push('screenWeight')
      }
      if (this.portEffective) {
        types.push('portWeight')
      }
      if (this.domainEffective) {
        types.push('domainWeight')
      }
      if (this.firewallConfigured) {
        types.push('fireWallWeight')
      }
      if (this.desktopConfigured) {
        types.push('deskWeight')
      }
      this.$refs.dataForm.validateField(types)
    },
    handleShow(row) {
      this.formatRowData(row, 'update');
    },
    handleCreate(objectType, objectId) {
      this.formable = true
      this.operator = 'create'
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      if (objectType !== undefined && objectId !== undefined && objectType !== null && objectId !== null) {
        this.temp.entityType = objectType
        this.temp.entityId = objectId;
        if (objectType == 1) {
          this.temp.objectIds = [objectId]
        } else if (objectType == 3) {
          this.temp.objectGroupIds = [objectId]
        }
      }
      this.formatRowData(this.temp, 'create')
    },
    handleUpdate(row, formable) {
      if (formable === undefined || formable === null) {
        this.formable = true
      } else {
        this.formable = formable
      }
      this.operator = 'update'
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      if (row.objectType !== undefined && row.objectId !== undefined && row.objectType !== null && row.objectId !== null) {
        this.temp.entityType = row.objectType
        this.temp.entityId = row.objectId;
        if (row.objectType == 1) {
          this.temp.objectIds = [row.objectId]
        } else if (row.objectType == 3) {
          this.temp.objectGroupIds = [row.objectId]
        }
      }
      this.formatRowData(row, 'update');
    },
    //  初始化
    formatRowData(row, operator) {
      let id = Date.now();  //  自动生成id
      this.temp = Object.assign(this.temp, row)
      //  检测周期
      this.timeType = row.isSelfDay ? -2 : row.checkDay;
      this.selfTime = row.isSelfDay ? row.checkDay : 15;
      this.timeType = this.timeType === null ? 7 : this.timeType
      //  杀毒软件检查
      const virusCheck = row.virus && row.virus.length > 0
      this.virusCheck = virusCheck && !!row.virus[0].open
      this.virusInfoList = virusCheck ? row.virus[0].virusInfoList : []
      this.virusInfoList.forEach(item => { item.id = id; id++; })
      this.temp.virusWeight = !this.virusEffective && this.temp.virusWeight <= 0 ? 5 : this.temp.virusWeight
      //  必须安装软件检查
      const installSoftCheck = row.installSoft && row.installSoft.length > 0
      this.installSoftCheck = installSoftCheck && !!row.installSoft[0].open
      this.softWareInstallTemp.softWareList = installSoftCheck ? row.installSoft[0].softInfoList : [];
      this.softWareInstallTemp.softWareList.forEach(item => { item.id = id; id++; })
      this.softWareInstallTemp.installType = installSoftCheck ? row.installSoft[0].installType : 0;
      this.softWareInstallTemp.fixPlan = installSoftCheck ? row.installSoft[0].repairMethod : ''
      this.temp.installWhiteWeight = !this.installSoftEffective && this.temp.installWhiteWeight <= 0 ? 5 : this.temp.installWhiteWeight
      //  禁止安装软件检查
      const limitInstallSoftCheck = row.limitInstallSoft && row.limitInstallSoft.length > 0
      this.limitInstallSoftCheck = limitInstallSoftCheck && !!row.limitInstallSoft[0].open
      this.softWareInstallTemp.limitSoftWareList = limitInstallSoftCheck ? row.limitInstallSoft[0].softInfoList : [];
      this.softWareInstallTemp.limitSoftWareList.forEach(item => { item.id = id; id++; })
      this.softWareInstallTemp.limitFixPlan = limitInstallSoftCheck ? row.limitInstallSoft[0].repairMethod : ''
      this.temp.installBlackWeight = !this.limitInstallSoftEffective && this.temp.installBlackWeight <= 0 ? 5 : this.temp.installBlackWeight
      //  必须运行服务检查
      const whiteServerCheck = row.whiteServer && row.whiteServer.length > 0
      this.whiteServerCheck = whiteServerCheck && !!row.whiteServer[0].open
      this.serviceTemp.serviceList = whiteServerCheck ? row.whiteServer[0].serviceList : [];
      this.serviceTemp.serviceList.forEach(item => { item.id = id; id++; })
      this.serviceTemp.runType = whiteServerCheck ? row.whiteServer[0].runType || 0 : 0;
      this.serviceTemp.fixPlan = whiteServerCheck ? row.whiteServer[0].repairMethod : ''
      this.temp.serverWhiteWeight = !this.serviceEffective && this.temp.serverWhiteWeight <= 0 ? 5 : this.temp.serverWhiteWeight
      //  禁止运行服务检查
      const blackServerCheck = row.blackServer && row.blackServer.length > 0
      this.blackServerCheck = blackServerCheck && !!row.blackServer[0].open
      this.serviceTemp.limitServiceList = blackServerCheck ? row.blackServer[0].serviceList : [];
      this.serviceTemp.limitServiceList.forEach(item => { item.id = id; id++; })
      this.serviceTemp.limitFixPlan = blackServerCheck ? row.blackServer[0].repairMethod : ''
      this.temp.serverBlackWeight = !this.limitServiceEffective && this.temp.serverBlackWeight <= 0 ? 5 : this.temp.serverBlackWeight
      //  必须运行进程检查
      const whiteProcessCheck = row.whiteProcess && row.whiteProcess.length > 0
      this.whiteProcessCheck = whiteProcessCheck && !!row.whiteProcess[0].open
      this.processTemp.processList = whiteProcessCheck && row.whiteProcess[0].procList ? row.whiteProcess[0].procList : []
      this.processTemp.processList.forEach(item => { item.id = id; id++; })
      this.processTemp.runType = whiteProcessCheck ? row.whiteProcess[0].runType || 0 : 0
      this.processTemp.fixPlan = whiteProcessCheck ? row.whiteProcess[0].repairMethod : ''
      this.temp.processWhiteWeight = !this.processEffective && this.temp.processWhiteWeight <= 0 ? 5 : this.temp.processWhiteWeight
      //  禁止运行进程检查
      const blackProcessCheck = row.blackProcess && row.blackProcess.length > 0
      this.blackProcessCheck = blackProcessCheck && !!row.blackProcess[0].open
      this.processTemp.limitProcessList = blackProcessCheck && row.blackProcess[0].procList ? row.blackProcess[0].procList : []
      this.processTemp.limitProcessList.forEach(item => { item.id = id; id++; })
      this.processTemp.limitFixPlan = blackProcessCheck ? row.blackProcess[0].repairMethod : ''
      this.temp.processBlackWeight = !this.limitProcessEffective && this.temp.processBlackWeight <= 0 ? 5 : this.temp.processBlackWeight
      //  文件检查
      const fileScanCheck = row.fileScan && row.fileScan.length > 0
      this.fileScanCheck = fileScanCheck && !!row.fileScan[0].open
      this.fileScanTemp.fileInfoList = fileScanCheck ? row.fileScan[0].fileInfoList : [];
      this.fileScanTemp.fileInfoList.forEach(item => { item.id = id; id++; })
      this.fileScanTemp.fixPlan = fileScanCheck ? row.fileScan[0].repairMethod : ''
      this.temp.fileWeight = !this.fileEffective && this.temp.fileWeight <= 0 ? 5 : this.temp.fileWeight
      //  注册表检查
      const regScanCheck = row.regScan && row.regScan.length > 0
      this.regScanCheck = regScanCheck && !!row.regScan[0].open
      this.regScanTemp.regInfoList = regScanCheck ? row.regScan[0].regInfoList : [];
      this.regScanTemp.regInfoList.forEach(item => { item.id = id; id++; })
      this.regScanTemp.fixPlan = regScanCheck ? row.regScan[0].repairMethod : ''
      this.temp.regWeight = !this.registryEffective && this.temp.regWeight <= 0 ? 5 : this.temp.regWeight
      //  操作系统检查
      const hostCheck = row.host && row.host.length > 0
      this.hostCheck = hostCheck && !!row.host[0].open
      const allowOsTypeTemp1 = JSON.parse(JSON.stringify(this.defaultAllowOsTypeTemp))
      this.allowOsTypeTemp = hostCheck ? Object.assign(allowOsTypeTemp1, row.host[0]) : allowOsTypeTemp1;
      this.allowOsTypeTemp.fixPlan = hostCheck ? row.host[0].repairMethod : ''
      this.temp.osWeight = this.temp.osWeight <= 0 ? 5 : this.temp.osWeight
      this.windowsOsTypeNames = []
      this.windowsServerOsTypeNames = []
      if (this.allowOsTypeTemp.operator === 0 && this.allowOsTypeTemp.name) {
        const names = this.allowOsTypeTemp.name.split(',')
        names.forEach(item => {
          if (item.includes('Server')) {
            this.windowsServerOsTypeNames.push(item)
          } else {
            this.windowsOsTypeNames.push(item)
          }
        })
      }
      this.osTypeCheck1 = this.windowsServerOsTypeNames.length > 0 || this.windowsOsTypeNames.length > 0
      this.osTypeCheck2 = this.allowOsTypeTemp.operator !== 0 && (this.allowOsTypeTemp.versionName.length > 0 || this.allowOsTypeTemp.serVersionName.length > 0);
      this.handleOsTypeCheckChange1(this.windowsOsTypeNames)
      this.handleOsTypeCheckChange2(this.windowsServerOsTypeNames)
      this.allowOsTypeTemp.operator = this.allowOsTypeTemp.operator === 0 ? 1 : this.allowOsTypeTemp.operator;
      //  计算机名称规范检查
      const computerNameCheck = row.computerName && row.computerName.length > 0
      this.computerNameCheck = computerNameCheck && !!row.computerName[0].open
      this.computeNameTemp.fixPlan = computerNameCheck ? row.computerName[0].repairMethod : ''
      this.computeNameTemp.regEx = computerNameCheck ? row.computerName[0].regInfo[0].regEx : ''
      this.computeNameTemp.regDes = computerNameCheck ? row.computerName[0].regInfo[0].regDes : ''
      this.temp.computeNameWeight = !this.computeNameEffective && this.temp.computeNameWeight <= 0 ? 5 : this.temp.computeNameWeight
      //  来宾账户检查
      const guestCheck = row.guest && row.guest.length > 0
      this.guestCheck = guestCheck && !!row.guest[0].open
      this.guestTemp.fixPlan = guestCheck ? row.guest[0].repairMethod : ''
      this.temp.guestWeight = !this.guestConfigured && this.temp.guestWeight <= 0 ? 5 : this.temp.guestWeight
      //  共享配置检查
      const sharedCheck = row.shared && row.shared.length > 0
      this.sharedCheck = sharedCheck && !!row.shared[0].open
      this.shareConfigTemp.printer = sharedCheck ? row.shared[0].printer : '1'
      const names = []
      if (sharedCheck && row.shared[0].fileNameList) {
        row.shared[0].fileNameList.forEach(item => { names.push(item.fileName) });
      }
      this.shareConfigTemp.fileNameList = names;
      this.shareConfigTemp.shareFlag = sharedCheck ? row.shared[0].shareFlag || 0 : 0
      this.shareConfigTemp.fixPlan = sharedCheck ? row.shared[0].repairMethod : ''
      this.temp.shareWeight = !this.sharedEffective && this.temp.shareWeight <= 0 ? 5 : this.temp.shareWeight
      //  磁盘使用空间检查
      const diskCheck = row.disk && row.disk.length > 0
      this.diskCheck = diskCheck && !!row.disk[0].open
      const totalDisk = diskCheck && row.disk[0].diskUse.length > 0 ? row.disk[0].diskUse[0].totalDisk : 0
      const sysDisk = diskCheck && row.disk[0].diskUse.length > 0 ? row.disk[0].diskUse[0].sysDisk : 0
      const sysSize = diskCheck && row.disk[0].diskUse.length > 0 ? row.disk[0].diskUse[0].sysSize : 0
      const totalSize = diskCheck && row.disk[0].diskUse.length > 0 ? row.disk[0].diskUse[0].totalSize : 0
      this.diskDetectTemp.fixPlan = diskCheck ? row.disk[0].repairMethod : ''
      this.totalDiskCheck = totalDisk > 0
      this.sysDiskCheck = sysDisk > 0;
      this.sysSizeCheck = sysSize > 0
      this.totalSizeCheck = totalSize > 0
      //  磁盘使用空间检查占比默认为90
      this.diskDetectTemp.totalDisk = !totalDisk ? 90 : totalDisk;
      this.diskDetectTemp.sysDisk = !sysDisk ? 90 : sysDisk;
      this.diskDetectTemp.sysSize = !sysSize ? 10 : sysSize;
      this.diskDetectTemp.totalSize = !totalSize ? 50 : totalSize;
      this.temp.diskUseSpaceWeight = !this.diskEffective && this.temp.diskUseSpaceWeight <= 0 ? 5 : this.temp.diskUseSpaceWeight
      //  计算机屏幕保护
      const screenCheck = row.screen && row.screen.length > 0
      this.screenCheck = screenCheck && !!row.screen[0].open
      const showLogin = screenCheck ? row.screen[0].showLogin : 0
      this.screenTemp.showLogin = showLogin !== null && showLogin !== '' ? parseInt(showLogin) : 0
      const waitTime = screenCheck ? row.screen[0].waitTime : null
      this.screenTemp.waitTime = waitTime !== null && waitTime !== '' ? parseInt(waitTime) : null
      this.screenTemp.timeCheck = !!this.screenTemp.waitTime
      //  默认为5
      this.screenTemp.waitTime = this.screenTemp.timeCheck ? this.screenTemp.waitTime : 5;
      this.screenTemp.fixPlan = screenCheck ? row.screen[0].repairMethod : ''
      this.temp.screenWeight = !this.screenConfigured && this.temp.screenWeight <= 0 ? 5 : this.temp.screenWeight
      //  端口检查
      const portCheck = row.port && row.port.length > 0;
      this.portCheck = portCheck && !!row.port[0].open
      this.portTemp.fixPorts = portCheck ? row.port[0].fixPorts || [] : []
      this.portCheckAll = this.portTemp.fixPorts.length > 0 && this.portTemp.fixPorts.length === this.fixPortList.length
      this.portIndeterminate = this.portTemp.fixPorts.length > 0 && this.portTemp.fixPorts.length !== this.fixPortList.length
      this.portTemp.port = portCheck ? row.port[0].port || '' : ''
      this.$refs['selfPorts'] && this.$refs['selfPorts'].clearInputValue()
      this.portTemp.selfPorts = portCheck ? row.port[0].selfPorts || [] : []
      this.portTemp.fixPlan = portCheck ? row.port[0].repairMethod : ''
      this.temp.portWeight = !this.portEffective && this.temp.portWeight <= 0 ? 5 : this.temp.portWeight
      //  防火墙
      const firewallCheck = row.firewall && row.firewall.length > 0
      this.firewallCheck = firewallCheck && !!row.firewall[0].open
      this.firewallTemp.fixPlan = firewallCheck ? row.firewall[0].repairMethod : ''
      this.temp.fireWallWeight = !this.firewallConfigured && this.temp.fireWallWeight <= 0 ? 5 : this.temp.fireWallWeight
      //  远程桌面
      const desktopCheck = row.desktop && row.desktop.length > 0
      this.desktopCheck = desktopCheck && !!row.desktop[0].open
      this.desktopTemp.fixPlan = desktopCheck ? row.desktop[0].repairMethod : ''
      this.temp.deskWeight = !this.desktopConfigured && this.temp.deskWeight <= 0 ? 5 : this.temp.deskWeight
      //  域名
      const domainCheck = row.domain && row.domain.length > 0
      this.domainCheck = domainCheck && !!row.domain[0].open
      this.$refs['doMainPassAccountList'] && this.$refs['doMainPassAccountList'].clearInputValue()
      this.domainTemp.domainNameList = domainCheck && row.domain[0].domainName.length > 0 ? row.domain[0].domainName.split(',') : []
      this.$refs['doMainNoPassAccountList'] && this.$refs['doMainNoPassAccountList'].clearInputValue()
      this.domainTemp.noPassAccountList = domainCheck && row.domain[0].noPassAccount.length > 0 ? row.domain[0].noPassAccount.split(',') : []
      this.domainTemp.passAccountList = domainCheck && row.domain[0].passAccount.length > 0 ? row.domain[0].passAccount.split(',') : []
      this.domainTemp.fixPlan = domainCheck ? row.domain[0].repairMethod : ''
      this.temp.domainWeight = !this.domainEffective && this.temp.domainWeight <= 0 ? 5 : this.temp.domainWeight

      //  检测项权重分配
      this.weightDistributionError = false

      //  风险
      this.lowCheck = operator === 'create' ? true : !!this.temp.lowRiskAlarmId
      this.mediumCheck = operator === 'create' ? true : !!this.temp.mediumRiskAlarmId
      this.highCheck = operator === 'create' ? true : !!this.temp.highRiskAlarmId

      //  检测项的总开关
      const soft = this.countSoftCheckCount(1)
      this.softwareSecurityCheck = soft.count > 0 && soft.count === soft.total
      this.softIndeterminate = soft.count > 0 && soft.count !== soft.total
      const host = this.countHostCheckCount(1)
      this.hostSecurityCheck = host.count > 0 && host.count === host.total
      this.hostIndeterminate = host.count > 0 && host.count !== host.total
      const net = this.countNetCheckCount(1)
      this.netSecurityCheck = net.count > 0 && net.count === net.total
      this.netIndeterminate = net.count > 0 && net.count !== net.total
    },
    //  格式化数据
    formatFormData(row) {
      row = JSON.parse(JSON.stringify(row))
      row.strategyDefType = this.strategyDefType;
      //  检测周期
      row.checkDay = this.timeType === -2 ? this.selfTime : this.timeType
      row.isSelfDay = this.timeType === -2;

      row.virus = []
      //  杀毒软件检查
      if (this.virusConfigured) {
        row.virus.push({ open: this.virusCheck ? 1 : 0, virusInfoList: this.virusInfoList });
      }
      row.installSoft = []
      //  必须安装软件
      if (this.installSoftConfigured) {
        row.installSoft.push({ open: this.installSoftCheck ? 1 : 0, softInfoList: this.softWareInstallTemp.softWareList, installType: this.softWareInstallTemp.installType, repairMethod: this.softWareInstallTemp.fixPlan })
      }
      row.limitInstallSoft = []
      //  禁止安装软件
      if (this.limitInstallSoftConfigured) {
        row.limitInstallSoft.push({ open: this.limitInstallSoftCheck ? 1 : 0, softInfoList: this.softWareInstallTemp.limitSoftWareList, installType: 0, repairMethod: this.softWareInstallTemp.limitFixPlan })
      }
      row.whiteProcess = []
      //  必须运行进程
      if (this.processConfigured) {
        row.whiteProcess.push({ open: this.whiteProcessCheck ? 1 : 0, procList: this.processTemp.processList, runType: this.processTemp.runType, repairMethod: this.processTemp.fixPlan })
      }
      row.blackProcess = []
      //  禁止运行进程
      if (this.limitProcessConfigured) {
        row.blackProcess.push({ open: this.blackProcessCheck ? 1 : 0, procList: this.processTemp.limitProcessList, runType: 0, repairMethod: this.processTemp.limitFixPlan })
      }
      row.whiteServer = []
      //  必须运行服务
      if (this.serviceConfigured) {
        row.whiteServer.push({ open: this.whiteServerCheck ? 1 : 0, serviceList: this.serviceTemp.serviceList, runType: this.serviceTemp.runType, repairMethod: this.serviceTemp.fixPlan })
      }
      row.blackServer = []
      //  禁止运行服务
      if (this.limitServiceConfigured) {
        row.blackServer.push({ open: this.blackServerCheck ? 1 : 0, serviceList: this.serviceTemp.limitServiceList, runType: 0, repairMethod: this.serviceTemp.limitFixPlan })
      }
      row.fileScan = []
      //  文件检查
      if (this.fileConfigured) {
        row.fileScan.push({ open: this.fileScanCheck ? 1 : 0, fileInfoList: this.fileScanTemp.fileInfoList, repairMethod: this.fileScanTemp.fixPlan })
      }
      row.regScan = []
      //  注册表检查
      if (this.registryConfigured) {
        row.regScan.push({ open: this.regScanCheck ? 1 : 0, regInfoList: this.regScanTemp.regInfoList, repairMethod: this.regScanTemp.fixPlan })
      }
      row.host = []
      //  操作系统检查
      if (this.osTypeConfigured) {
        const temp = Object.assign(JSON.parse(JSON.stringify(this.allowOsTypeTemp)), { operator: this.osTypeCheck1 ? 0 : this.allowOsTypeTemp.operator,
          repairMethod: this.allowOsTypeTemp.fixPlan })
        temp.open = this.hostCheck ? 1 : 0
        temp.overVersion = this.overVersion
        if (temp.operator === 0) {
          const nameList = this.windowsOsTypeNames || []
          this.windowsServerOsTypeNames.forEach(item => { nameList.push(item) });
          temp.name = nameList.length > 0 ? nameList.join(',') : ''
          temp.serVersionName = ''
          temp.versionName = ''
        } else {
          temp.name = '';
        }
        row.host.push(temp)
      }
      row.computerName = []
      //  计算机名称规范检查
      if (this.computeNameConfigured) {
        const list = [{ regEx: this.computeNameTemp.regEx, regDes: this.computeNameTemp.regDes }]
        row.computerName.push({ open: this.computerNameCheck ? 1 : 0, regInfo: list, repairMethod: this.computeNameTemp.fixPlan })
      }
      row.guest = []
      //  来宾账户检查
      const guestList = [{ name: 'guest' }]
      row.guest.push({ open: this.guestConfigured ? 1 : 0, guestList: guestList, repairMethod: this.guestTemp.fixPlan })

      row.shared = []
      //  共享配置检查
      if (this.sharedConfigured) {
        const list = this.shareConfigTemp.fileNameList.map(item => { return { fileName: item } })
        row.shared.push({ open: this.sharedCheck ? 1 : 0, shareFlag: this.shareConfigTemp.shareFlag, printer: this.shareConfigTemp.printer, fileNameList: list, repairMethod: this.shareConfigTemp.fixPlan })
      }

      row.disk = []
      //  磁盘使用空间检查
      if (this.diskConfigured) {
        const list = [{ sysDisk: this.sysDiskCheck ? this.diskDetectTemp.sysDisk : 0,
          totalDisk: this.totalDiskCheck ? this.diskDetectTemp.totalDisk : 0,
          sysSize: this.sysSizeCheck ? this.diskDetectTemp.sysSize : 0,
          totalSize: this.totalSizeCheck ? this.diskDetectTemp.totalSize : 0 }]
        row.disk.push({ open: this.diskCheck ? 1 : 0, diskUse: list, repairMethod: this.diskDetectTemp.fixPlan })
      }
      row.screen = []
      //  计算机屏幕保护
      row.screen.push({ open: this.screenConfigured ? 1 : 0, waitTime: !this.screenTemp.timeCheck ? '' : this.screenTemp.waitTime, showLogin: this.screenTemp.showLogin, repairMethod: this.screenTemp.fixPlan })

      row.port = []
      //  端口检查
      if (this.portConfigured) {
        const list = [...this.portTemp.fixPorts];
        this.portTemp.selfPorts.forEach(item => { list.push(item) })
        const port = list.join(',')
        row.port.push({ open: this.portCheck ? 1 : 0, port: port, repairMethod: this.portTemp.fixPlan, fixPorts: this.portTemp.fixPorts, selfPorts: this.portTemp.selfPorts })
      }
      row.firewall = []
      //  防火墙
      row.firewall.push({ open: this.firewallConfigured ? 1 : 0, name: 'firewall', repairMethod: this.firewallTemp.fixPlan })

      row.desktop = []
      //  远程桌面
      row.desktop.push({ open: this.desktopConfigured ? 1 : 0, name: 'desktop', repairMethod: this.desktopTemp.fixPlan })

      row.domain = []
      //  域环境
      row.domain.push({ open: this.domainCheck ? 1 : 0, domainName: this.domainTemp.domainNameList.length > 0 ? this.domainTemp.domainNameList.join(',') : '',
        noPassAccount: this.domainTemp.noPassAccountList.join(',') || '',
        passAccount: this.domainTemp.passAccountList.join(',') || '', repairMethod: this.domainTemp.fixPlan })

      //  权重设置， 未配置的检查项的权重为-1
      row.virusWeight = this.virusEffective ? row.virusWeight : -1;
      row.installWhiteWeight = this.installSoftEffective ? row.installWhiteWeight : -1;
      row.installBlackWeight = this.limitInstallSoftEffective ? row.installBlackWeight : -1;
      row.serverWhiteWeight = this.serviceEffective ? row.serverWhiteWeight : -1;
      row.serverBlackWeight = this.limitServiceEffective ? row.serverBlackWeight : -1;
      row.processWhiteWeight = this.processEffective ? row.processWhiteWeight : -1;
      row.processBlackWeight = this.limitProcessEffective ? row.processBlackWeight : -1;
      row.fileWeight = this.fileEffective ? row.fileWeight : -1;
      row.regWeight = this.registryEffective ? row.regWeight : -1;

      row.osWeight = this.osTypeEffective ? row.osWeight : -1;
      row.computeNameWeight = this.computeNameEffective ? row.computeNameWeight : -1;
      row.guestWeight = this.guestConfigured ? row.guestWeight : -1;
      row.diskUseSpaceWeight = this.diskEffective ? row.diskUseSpaceWeight : -1;
      row.shareWeight = this.sharedEffective ? row.shareWeight : -1;
      row.screenWeight = this.screenConfigured ? row.screenWeight : -1;

      row.portWeight = this.portEffective ? row.portWeight : -1;
      row.fireWallWeight = this.firewallConfigured ? row.fireWallWeight : -1;
      row.domainWeight = this.domainEffective ? row.domainWeight : -1;
      row.deskWeight = this.desktopConfigured ? row.deskWeight : -1;

      row.softwareSecurityCheck = this.softwareSecurityCheck
      row.hostSecurityCheck = this.hostSecurityCheck
      row.netSecurityCheck = this.netSecurityCheck
      return row
    },
    //  进程检查初始化
    processFormatRowData(proInfo) {
      const list = []
      if (proInfo) {
        proInfo.forEach(item => {
          if (item.procList) {
            item.procList.forEach(t => {
              t.scanType = item.scanType;
              list.push(t);
            })
          }
        })
      }
      return list;
    },
    cancel() {
      this.$emit('submit');
      this.clear()
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    clear() {
      this.copyFlag = false
      this.fixPlanTitle = this.$t('pages.showFixPlanView')
      this.fixPlanShow = false
      this.portCheckAll = false
      this.portIndeterminate = false
      this.osTypeError = false
      this.operator = ''
      this.activeName = '0'
      this.softSecurityActiveName = 'virus'
      this.hostActiveName = 'host'
      this.netActiveName = 'port'
      this.killSelected = false;
    },
    setSkipData(tab, sunTab) {
      if (tab !== null && sunTab) {
        switch (tab) {
          case '1': this.softSecurityActiveName = sunTab; break;
          case '2': this.hostActiveName = sunTab; break;
          case '3': this.netActiveName = sunTab; break;
          default:
        }
      }
      if (tab !== null) {
        this.activeName = tab
      }
    },
    //  校验规则，false表示存在不符合规则
    async validRule() {
      let flag = true
      let tab = null
      //  校验风险规则
      if ((this.lowCheck && this.temp.lowRiskAlarmId === null) || (this.mediumCheck && this.temp.mediumRiskAlarmId === null) || (this.highCheck && this.temp.highRiskAlarmId === null)) {
        flag = flag && false
        tab = '6'
      }
      //  风险权重分配错误
      if (this.validWeightError) {
        flag = flag && false
        tab = '6'
      }
      //  校验域环境检查
      const doMainPassAccountFlag = await this.$refs['doMainPassAccountList'].validateForm()
      const doMainNoPassAccountFlag = await this.$refs['doMainNoPassAccountList'].validateForm()
      if (!doMainPassAccountFlag || !doMainNoPassAccountFlag) {
        flag = flag && false
        tab = '3'
      }
      //  校验端口检查
      const selfPortsFlag = await this.$refs['selfPorts'].validateForm()
      if (!selfPortsFlag) {
        flag = flag && false
        tab = '3'
      }

      //  校验操作系统
      if (this.hostCheck && (this.osTypeCheck2 &&
        this.allowOsTypeTemp.versionName.length === 0 && this.allowOsTypeTemp.serVersionName.length === 0)) {
        this.osTypeError = true
        flag = flag && false
        tab = '2'
      } else {
        this.osTypeError = false
      }
      if (tab !== null) {
        this.setSkipData(tab, null)
      }
      return flag;
    },
    //  保存
    confirm() {
      // if (!this.temp.strategyDefType && (this.temp.objectType === null || ((!this.temp.objectGroupIds || this.temp.objectGroupIds.length === 0) &&
      //   (!this.temp.objectIds || this.temp.objectIds.length === 0)))) {
      //   this.$message({
      //     message: this.$t('components.chooseApplicationObj'),
      //     type: 'error',
      //     duration: 2000
      //   })
      //   this.setSkipData('0', null)
      //   return;
      // }
      const _this = this
      this.$refs.dataForm.validate(async(valid, error) => {
        if (valid) {
          if (!await this.validRule()) {
            return;
          }

          // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
          if (!validatePolicy(this.temp, this, this.strategyDefType)) {
            return
          }

          _this.submitting = true
          const row = _this.formatFormData(_this.temp);
          const fun = row.id === null ? addStrategy : updateStrategy;
          fun(row).then(res => {
            _this.$notify({
              title: this.$t('text.success'),
              message: this.operator === 'create' ? this.$t('text.createSuccess') : this.$t('text.editSuccess'),
              type: 'success',
              duration: 2000
            })
            _this.submitting = false
            _this.$emit('submit', res);
            _this.clear()
            _this.$nextTick(() => {
              _this.$refs.dataForm.clearValidate()
            })
          }).catch(() => {
            _this.submitting = false
          })
        }
      })
    },
    //  另存为
    handleCopy() {
      const copyTemp = Object.assign({}, this.temp, { active: false })
      const temp = this.formatFormData(copyTemp)
      this.copyFlag = true
      this.$refs.dataForm.validate(async(valid, error) => {
        if (valid) {
          if (await this.validRule()) {
            this.$refs.saveAsDlg.show(temp)
          }
        }
      });
    },
    saveAsSubmitEnd(type, res) {
      if (type === 'success') {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.$emit('submit', res, true);
        this.clear()
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }
      this.copyFlag = false
    },
    validDataForm(createFun, failFun) {
      this.$refs.dataForm.validate(async(valid, error) => {
        if (valid) {
          if (!await this.validRule()) {
            if (failFun) failFun()
          } else {
            if (createFun) createFun();
          }
        } else {
          if (failFun) failFun()
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container{
  height: calc(100% - 20px);
  overflow: auto;
}
.serverInfo {
  height: 100%;
  padding: 0;
}
>>> .el-card__header{
  padding: 10px 10px;
}
>>> .el-card__body{
  font-size: 14px;
  padding: 0;
  height: calc(100% - 39px);
}
>>> .el-tab-pane {
  font-size: 14px;
}
>>> .text {
  font-size: 14px;
  display: flex;
}
>>> .text>span:first-child{
  width: 40px;
}
>>> .text>span:last-child{
  flex: 1;
  cursor: pointer;
}
>>> .item {
  margin-bottom: 18px;
}
>>> .clearfix{
  font-size: 14px;
}
>>> .clearfix>span:first-child{
  display: inline-block;
  width: 40px;
}
>>> .clearfix:before,
>>> .clearfix:after {
  display: table;
  content: "";
}
>>> .clearfix:after {
  clear: both
}
.activeClass{
  color:#409EFF
}
.el-collapse {
  margin-top: 10px;
}
>>> .el-collapse-item__content {
  padding-bottom: 5px;
}
>>> .el-collapse-item__arrow {
  margin: 0px;
}
.box-card {
  border: solid 1px #eeeeee;
  padding: 5px 5px 5px;
  height: 250px;
}
.item_info {
  width: 400px;
  height: 30px;
  line-height: 30px;
  overflow: hidden;
}
>>>.el-form-item {
  margin-bottom: 10px;
}
.bigType {
  font-size: 15px;font-weight: 500;margin: 10px 0
}
.smallType {
  font-size: 15px;font-weight: 500;margin: 10px 0
}
.miniType {
  font-size: 15px;font-weight: 500;margin: 10px 0
}

.server-form {
  height: calc(100% - 60px);
  //overflow: auto;
  //font-size: 14px;
  padding: 10px 30px 0 10px;
  >>>.el-divider__text {
    font-size: 15px!important;
  }
  .el-button{
    min-width: 50px;
    //padding: 5px;
    margin-bottom: 5px;
  }
  .el-button+.el-button{
    margin-left: 2px;
  }
  .el-button--mini{
    padding: 7px 15px;
  }
  .zTab {
    >>>.el-tabs__content {
      height: calc(100% - 43px) !important;
    }
  }
  .sunTab {
    >>>.el-tabs__content {
      height: calc(100% - 0px) !important;
    }
  }

  .el-form-item {
    margin-bottom: 10px;
  }
  .server-label {
    display: inline-block;
    width: 40%;
  }
  .server-footer {
    position: absolute;
    left: 572px;
    bottom: 20px;
  }
  >>>.el-divider__text {
    font-size: 18px;
    font-weight: 700;
  }
  >>>.el-form-item__label {
    span {
      font-size: 15px;
      font-weight: 700;
      &:before {
        content: ' ';
      }
    }
  }
  .el-input, .el-input-number {
    width: 200px;
    >>>.el-input__inner {
      position: relative;
      text-align: left;
    }
  }
  //>>>.el-checkbox__label {
  //  color: #ccc;
  //}
}
.stgTargetType {
  >>>.targetType .el-input__inner {
    height: 30px !important;
  }
}
.targetType {
  >>>.el-input__inner {
    height: 30px !important;
  }
}
.osTypeBtn {
  color: #ccc;
  border-color: #ccc;
}
.dividerClass.el-divider--horizontal {
  //width: 0px;
  width: calc(100% - 17%);
  //height: 0;
  margin: 5px 8% 5px 8%;
}
.dividerClass.el-divider--vertical {
  height: 20px;
  margin: 10px 8px;
}
.weightValueClass {
  position: absolute;
  align-items: center;
  margin-top: 5px;
  right: 15%;
}
.weightFormItemClass {
  height: 30px;
  margin-bottom: 0!important;
  >>>.el-form-item__label {
    height: 30px;
    line-height: 30px!important;
    padding-right: 5px!important;
  }
  >>>.el-form-item__content {
    line-height: 30px;
    height: 30px;
  }
}
.fixPlanContentClass {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 110px;
}
.weight {
  >>>.el-form-item__content {
    line-height: 15px!important;
  }
}
.explainStyle {
  line-height: 20px;
  color: rgb(64, 158, 255);
}

.fixPlanTab {
  position: absolute;
  right: 0;
  top: 5px;
  color: #e6a23c;
  font-weight: 600;
  font-size: 14px;
}
.showInputLimit {
  >>>.el-input__inner {
    padding-right: 50px!important;
  }
}
</style>
