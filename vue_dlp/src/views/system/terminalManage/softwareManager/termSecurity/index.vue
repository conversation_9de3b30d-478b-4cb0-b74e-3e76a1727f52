<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :treeable="treeable" :show-tree.sync="showTree" :os-type-filter="1" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div v-show="visible" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreated">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode" :os-type-filter="1"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <add-index v-show="!visible" ref="stgDlg" :formable="formable" :strategy-def-type="query.strategyDefType" @submit="submit"></add-index>
  </div>
</template>

<script>
import { deleteStrategy, getStrategyPage } from '@/api/system/terminalManage/termSecurity'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import AddIndex from './addIndex'

export default {
  name: 'TermSecurityDetection',
  components: { AddIndex },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      stgCode: 246,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', fixed: true, sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'stgMessage', label: 'stgMessage', width: '150', formatter: this.stgFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      visible: true,
      formable: true
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    showTree(val) {
      console.log('showTree', val);

      if (val && this.visible == false) {
        this.showTree = false
      }
    }
  },
  async activated() {
    const query = this.$route.query
    if (query.visible !== undefined && query.visible !== null && query.visible === false) {
      const stg = this.$route.query.stg || null
      if (stg != null) {
        this.handleUpdate(stg, false)
      }
      query.stg = null
      query.visible = null
      this.$router.push({ query })
    }
    console.log('activated...', this.showTree, this.visible);

    if (this.showTree && this.visible == false) {
      this.showTree = false
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.$store.dispatch('commonData/setSoftwareCategories')
    // this.$refs['stgDlg'].handleCreate()
  },
  methods: {
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreated() {
      this.formable = true
      this.$refs['stgDlg'].handleCreate(this.query.objectType, this.query.objectId);
      this.visible = false
      this.showTree = false
    },
    handleUpdate: function(row, formable) {
      this.visible = false
      this.showTree = false
      this.$refs['stgDlg'].handleUpdate(row, formable)
    },
    handleShow: function(row) {
      this.$refs['stgDlg'].handleShow(row)
    },
    importSuccess() {
      this.handleFilter()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    stgFormatter(row, data) {
      let result = ''
      if (row) {
        //  当前尚不支持配置允许终端自检和开机检测
        // result += '是否允许终端自检：' + (row.termCheck ? '允许' : '不允许') + '，';
        // result += '是否开机检测：' + (row.bootCheck ? '是' : '否') + '，';
        result += this.$t('pages.safetyDetectionCycle') + this.getCheckDayLabel(row.checkDay) + '，';
        const configuredContent = this.getConfiguratedItem(row)
        if (configuredContent !== '') {
          result += this.$t('pages.termSecurityStgFormatMsg1') + configuredContent + '，';
        }
        result += this.$t('pages.termSecurityStgFormatMsg2') + row.lowRiskWeight + '%，'
        result += this.$t('pages.termSecurityStgFormatMsg3') + row.mediumRiskWeight + '%，'
        result += this.$t('pages.termSecurityStgFormatMsg4') + row.highRiskWeight + '%，'
      }
      return result.length > 0 ? result.substring(0, result.length - 1) : '';
    },
    //  获取已配置的检查项
    getConfiguratedItem(row) {
      let result = ''
      //  杀毒软件检查
      if (row.virusWeight > 0) {
        result += this.$t('pages.killSoftInspect') + '、'
      }
      //  软件必须安装检查
      if (row.installWhiteWeight > 0) {
        result += this.$t('pages.softwareMustInstallCheck') + '、'
      }
      //  软件禁止安装检查
      if (row.installBlackWeight > 0) {
        result += this.$t('pages.softwareNotInstallCheck') + '、'
      }
      //  服务白名单检查
      if (row.serverWhiteWeight > 0) {
        result += this.$t('pages.termSecurityServerWhiteCheck') + '、'
      }
      //  服务黑名单检查
      if (row.serverBlackWeight > 0) {
        result += this.$t('pages.termSecurityServerBlackCheck') + '、'
      }
      //  进程必须运行检查
      if (row.processWhiteWeight > 0) {
        result += this.$t('pages.termSecurityProcessMustRunCheck') + '、'
      }
      //  进程禁止运行检查
      if (row.processBlackWeight > 0) {
        result += this.$t('pages.termSecurityProcessNotRunCheck') + '、'
      }
      //  文件检查
      if (row.fileWeight > 0) {
        result += this.$t('pages.termSecurityFileCheck') + '、'
      }
      //  注册表检查
      if (row.regWeight > 0) {
        result += this.$t('pages.termSecurityRegistryCheck') + '、'
      }
      //  操作系统检查
      if (row.osWeight > 0) {
        result += this.$t('pages.termSecurityHostCheck') + '、'
      }
      //  计算机名称规范检查
      if (row.computeNameWeight > 0) {
        result += this.$t('pages.termSecurityComputerNameCheck') + '、'
      }
      //  来宾账户检查
      if (row.guestWeight > 0) {
        result += this.$t('pages.termSecurityGuestCheck') + '、'
      }
      //  磁盘使用空间检查
      if (row.diskUseSpaceWeight > 0) {
        result += this.$t('pages.termSecurityDiskCheck') + '、'
      }
      //  共享配置检查
      if (row.shareWeight > 0) {
        result += this.$t('pages.termSecuritySharedCheck') + '、'
      }
      //  计算机屏幕保护检查
      if (row.screenWeight > 0) {
        result += this.$t('pages.termSecurityScreenCheck') + '、'
      }
      //  端口检查
      if (row.portWeight > 0) {
        result += this.$t('pages.termSecurityPortCheck') + '、'
      }
      //  防火墙检查
      if (row.fireWallWeight > 0) {
        result += this.$t('pages.termSecurityFirewallCheck') + '、'
      }
      //  域环境检查
      if (row.domainWeight > 0) {
        result += this.$t('pages.termSecurityDomainCheck') + '、'
      }
      //  远程桌面检查
      if (row.deskWeight > 0) {
        result += this.$t('pages.termSecurityDesktopCheck') + '、'
      }
      return result.length > 0 ? result.substr(0, result.length - 1) : ''
    },
    //  获取检测周期
    getCheckDayLabel(checkDay) {
      let result = ''
      if (checkDay !== null) {
        switch (checkDay) {
          case -1: result = this.$t('pages.executeOnce'); break;    //  执行一次
          case 0: result = this.$t('pages.close'); break;   //  关闭
          case 1: result = this.$t('pages.onceDay'); break; //  每天一次
          default: result = this.$t('pages.onceEveryFewDay', { day: checkDay }); break;
        }
      }
      return result;
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleBeforeClose(done) {
      this.$confirmBox(this.$t('pages.confirmCancel'), this.$t('text.prompt')).then(() => {
        done();
      }).catch((e) => {
      });
    },
    submit(row, saveAsFlag) {
      this.visible = true
      this.showTree = true
      if (!this.query.strategyDefType && saveAsFlag !== undefined && saveAsFlag !== null && saveAsFlag) {
        const temp = row.data || {}
        temp.entityName = ''
        entityLink(temp, null, this)
      } else {
        this.handleFilter()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
