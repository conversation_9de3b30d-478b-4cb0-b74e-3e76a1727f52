<template>
  <el-container>
    <el-aside width="210px">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :height="height + 40"
        :multiple="false"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="treeNodeClick"
        @check-change="checkChange"
      />
    </el-aside>
    <el-main>
      <div class="toolbar">
        <div style="float: right;">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.fixPlanName')" style="width: 225px;" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="InfoList"
        :height="height"
        :is-saved-selected="false"
        saved-selected-prop="name"
        :col-model="colModel"
        :multi-select="multiple"
        :row-data-api="rowDataApi"
        :checked-row-keys="checkedRowKeys"
        pager-small
        :after-load="afterLoad"
        @currentChange="currentChange"
      />
    </el-main>
  </el-container>
</template>

<script>
import { findNodeLabel } from '@/utils/tree'
import { getFixPlanPage } from '@/api/system/terminalManage/fixPlan'

export default {
  name: 'FixPlanSelectTable',
  props: {
    height: { type: Number, default: 420 },
    multiple: { type: Boolean, default: true },
    libTreeNode: {
      type: Function,
      default: null
    },
    libPage: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'fixPlanName', width: '110' },
        { prop: 'groupId', label: 'groupName', width: '110', formatter: this.groupFormatter },
        { prop: 'content', label: 'content', width: '110' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined,
        groupIds: ''
      },
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('route.fixPlanLibrary'), parentId: '', children: [] }],
      defaultExpandedKeys: ['G0'],
      currentData: null,
      dataLoadPromise: undefined, // 设置一个函数先执行完后在执行别的操作
      checkedRowKeys: [], // 表格勾选
      datas: [], // 左侧树复选框选中后，获取对应列表值
      currentDataAll: null // 当前表格中所有的数据
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    suffixTable: function() {
      return this.$refs['InfoList']
    }
  },
  created() {
    this.show()
  },
  activated() {
    this.show()
  },
  methods: {
    async show() {
      this.query.searchInfo = null
      this.query.groupId = undefined
      this.query.groupIds = ''
      this.currentData = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNodes()
        setTimeout(() => {
          this.$refs.InfoList && this.$refs.InfoList.clearSelection()
        }, 500)
      })
    },
    /**
     * 左侧树节点点击时右侧列表刷新
     * @param data
     * @param node
     * @param element
     */
    treeNodeClick: function(data, node, element) {
      if (data.dataId === '0') {
        this.query.groupId = data.dataId
        this.query.groupIds = ''
      } else {
        this.query.groupId = undefined
        this.query.groupIds = this.getGroupIds(data).join(',')
      }
      this.handleFilter()
    },
    /**
     * 获取传入的树节点及其子节点的 dataId
     */
    getGroupIds(data) {
      const groups = Array.isArray(data) ? data : [data]
      const ids = []
      while (groups.length > 0) {
        const group = groups.shift()
        if (group.children) {
          groups.push(...group.children)
        }
        ids.push(group.dataId)
      }
      return ids
    },
    /**
     * 表格查询功能
     * */
    handleFilter() {
      this.query.page = 1
      this.suffixTable && this.suffixTable.execRowDataApi(this.query)
    },
    /**
     * 获取表格数据
     * */
    rowDataApi: function(option) {
      const newOption = Object.assign(this.query, option)
      return getFixPlanPage(newOption)
    },

    /**
     * 获取左侧树节点信息
     */
    loadGroupTree: function() {
      this.libTreeNode().then(respond => {
        this.treeData[0].children = respond.data
      })
    },
    currentChange: function(currentRow) {
      this.currentData = currentRow
    },
    /**
     * 表格刷新后调用方法
     * */
    afterLoad(rowData, grid) {
      this.currentDataAll = rowData
      this.selectTable(1)
    },
    /**
     * 左侧树选中状态发生变化时
     * */
    checkChange() {
      this.selectTable(2)
    },
    /**
     * 表格刷新和左侧树复选项发生变化时后调用方法
     * 如果左侧树复选框有选中
     * 右边表格中有包含左侧复选框选中的子项
     * 将表格中对应项勾选起来
     * */
    selectTable(selectType) {
      this.dataLoadPromise = Promise.all([ // 先执行
        this.getSelectedDatas(selectType)
      ]).then(() => { // 上面执行完后在执行下面
        // this.checkedRowKeys = []
        if (this.currentDataAll.length > 0 && this.datas.length > 0) {
          for (let i = 0; i < this.datas.length; i++) { // 左侧树选中数据
            for (let j = 0; j < this.currentDataAll.length; j++) { // 右侧表格现有数据
              if (this.datas[i].id == this.currentDataAll[j].id) {
                this.checkedRowKeys.push(this.datas[i].id)
              }
            }
          }
        }
      })
    },
    /**
     * 获取左侧树勾选分组下的列表数据
     * @param type 用于区分是选中左侧树还是直接选中列表
     * @returns {Promise<null>}
     */
    async getSelectedDatas(type) {
      if (this.multiple) { // 说明支持多选
        this.datas = this.suffixTable.getSelectedDatas()
        const groupNodes = this.$refs['groupTree'].getCheckedNodes() // 当前选中的左侧分组
        const groupIds = []
        groupNodes.forEach(data => groupIds.push(data.dataId))
        if (groupIds.length > 0) {
          await getFixPlanPage({ groupIds: groupIds.join(',') }).then(resp => {
            // this.datas = resp.data.items
            this.datas.splice(0, 0, ...resp.data.items)
          })
        } else {
          if (type === 2) {
            this.datas = []
          }
        }
      } else if (this.currentData != null) {
        this.datas = [this.currentData]
      }
      return this.datas
    },
    groupFormatter(row, data) {
      if (!row.groupId) {
        return this.$t('pages.undefined')
      }
      return findNodeLabel(this.treeData, data, 'dataId')
    }
  }
}
</script>
