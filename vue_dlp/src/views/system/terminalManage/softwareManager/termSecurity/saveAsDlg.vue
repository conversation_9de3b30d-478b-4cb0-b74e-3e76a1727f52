<template>
  <el-dialog v-el-drag-dialog :close-on-click-modal="false" :modal="false" :title="$t('components.saveAs')" :visible.sync="copyVisible" width="600px">
    <p style="padding-left: 10px;">{{ $t('components.confirmSaveAs') }}</p>
    <Form ref="copyForm4StgDialog" :model="copyTemp" label-position="right" label-width="80px" style="width: 560px">
      <stg-target-form-item
        v-if="!strategyDefType"
        ref="copyStgTargetItem"
        :stg-code="stgCode"
        :entity-type-span="entityTypeSpan"
        :tree-width="350"
        :form-data="copyTemp"
        :multiple="multipleEntity"
        :is-disabled="!formable"
        :strategy-def-type="strategyDefType"
        @entityNodeData="nodeData => entityIdChange(nodeData, copyTemp)"
      />
      <FormItem :label="$t('components.stgName')" class="required">
        <el-input v-model="copyTemp.name" v-trim maxlength="30"/>
      </FormItem>
      <FormItem :label="$t('text.remark')">
        <el-input v-model="copyTemp.remark" v-trim :disabled="!formable" maxlength="100" show-word-limit class="word-limit-3digits" />
      </FormItem>
      <FormItem v-if="!strategyDefType" :label="$t('components.enable')">
        <el-switch v-model="copyTemp.active" :disabled="!formable" />
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="formable" type="primary" @click="saveCopyData">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="cancelCopy">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>

</template>

<script>
import { addStrategy, getByName } from '@/api/system/terminalManage/termSecurity';
import { entityLink } from '@/utils';

export default {
  name: 'SaveAsDlg',
  components: { },
  props: {
    appendToBody: { type: Boolean, default: false },
    entityTypeSpan: { type: Number, default: 12 },                        // 策略应用树，占用span
    multipleEntity: { type: Boolean, default: true },                     // 是否提供应用对象多选，即是否提供复选框
    validDataForm: { type: Function, default: null },                     //  校验form表达
    strategyDefType: { type: Number, default() { return 0 } }             //  是否是预定义策略 0：普通策略 1：预定义策略
  },
  data() {
    return {
      usedScope: 1,                 // 生效对象可配置类型，1：终端，2：操作员，3：都可配置
      copyTemp: {},                 // 另存为策略的基本信息
      defaultTemp: {                // 公共表单默认temp
        name: '',
        remark: '',
        timeId: 1,
        active: false,
        osType: null,
        entityType: null,
        entityId: null,
        objectIds: [],
        objectGroupIds: [],
        strategyDefType: this.strategyDefType
      },
      stgCode: 246,                 //  终端安全检测策略编号
      copyEntityType: 3,            // 3 终端（组）， 4 操作员（组）
      copyVisible: false,           // 另存为窗口显示
      formable: true,               // 表单是否可编辑
      osAble: true,                // 策略是否区分不同终端（true：显示不同终端tab）
      originTemp: null             //  源数据
    }
  },
  computed: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    initCopyTemp(copyTemp) {
      //  清空生效对象
      copyTemp.entityId = null
      copyTemp.entityType = null
      copyTemp.objectIds = []
      copyTemp.objectGroupIds = []
    },
    show(originData) {
      this.originTemp = originData
      this.copyTemp = Object.assign(this.defaultTemp, this.originTemp)
      this.initCopyTemp(this.copyTemp)
      this.copyVisible = true
    },
    //  生效对象Id
    entityIdChange(key, nodeData) {
      const data = nodeData[0] || nodeData
      this.copyTemp.entityId = data.dataId
    },
    saveCopyData() {
      if (this.copyTemp.name === '' || this.copyTemp.name == null) {
        this.$message({
          message: this.$t('components.stgNameNotNull'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (!this.copyTemp.strategyDefType && this.copyTemp.objectGroupIds.length === 0 && this.copyTemp.objectIds.length === 0) {
        this.$message({
          message: this.$t('components.chooseApplicationObj'),
          type: 'error',
          duration: 2000
        })
        return
      }
      getByName(this.copyTemp.name).then(respond => {
        const res = respond.data
        const isRepeat = res ? (Array.isArray(res) ? res.length : true) : false
        if (isRepeat) {
          this.$message({
            message: this.$t('components.someStgName'),
            type: 'error',
            duration: 2000
          })
        } else {
          this.createData(this.copyTemp)
        }
      })
    },
    addStrategy,
    entityLink,
    createData(data) {
      this.submitFormData(this.addStrategy, data)
    },
    submitFormData(submitFunc, data) {
      this.validDataForm(() => {
        this.submitting = true
        // 校验是否是回收站分组，回收站分组不允许配置策略
        if ((data.entityId && data.entityId == -2) || (data.objectGroupIds && (data.objectGroupIds.includes(-2) || data.objectGroupIds.includes('-2')))) {
          this.$message({
            message: this.$t('components.recycleNodeTips'),
            type: 'error',
            duration: 2000
          })
          this.submitting = false
          return
        }
        const temp = JSON.parse(JSON.stringify(data))
        submitFunc(temp).then(respond => {
          // const vm = this.$store.getters.currentVm
          // this.copyVisible && this.isGeneralStrategy && this.entityLink(this.copyTemp, null, vm)
          this.$emit('submitEnd', 'success', respond)
          this.submitting = false
          this.hide()
        }).catch(e => {
          this.submitting = false
        })

        this.submitting = false
      }, () => {
        this.$emit('submitEnd', 'fail')
        this.hide()
      })
    },
    cancelCopy() {
      this.$emit('submitEnd', 'cancel')
      this.copyVisible = false
    },
    hide() {
      this.copyVisible = false
      this.submitting = false
    }
  }
}
</script>

<style scoped>

</style>
