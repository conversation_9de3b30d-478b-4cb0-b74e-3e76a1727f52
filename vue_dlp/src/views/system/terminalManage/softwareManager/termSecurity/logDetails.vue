<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.termSecurityDetails')"
      :visible.sync="visible"
      width="800px"
      @close="close"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" title="" border :column="2">
          <el-descriptions-item :label="$t('table.terminal')">{{ temp.terminalName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('table.user')">{{ temp.userName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('table.riskLevel')"><span :style="{ color: riskColorFormatter(temp.risk) }">{{ riskFormatter(temp.risk) }}</span></el-descriptions-item>
          <el-descriptions-item :label="$t('table.detectionTime')">{{ temp.createTime }}</el-descriptions-item>
        </el-descriptions>
        <el-card :body-style="{'height': '290px', 'overflow-y': 'auto'}" class="box-card" style="max-height: 500px; margin-bottom: 15px">
          <div slot="header" class="clearfix" style="padding-left: 0;">
            <div style="display: flex">
              <div style="align-items: center; height: 30px; margin-right: 10px; display: flex">
                <span>{{ $t('pages.detectionContent') }}</span>
                <span style="text-align: center">
                  <i18n path="pages.detectionContentMsg">
                    <span slot="total" style="color: #2196F3">{{ testTotal }}</span>
                    <span slot="passTotal" style="color: #4CAF50">{{ passTotal }}</span>
                    <span slot="notPassTotal" style="color: #F44336">{{ notPassTotal }}</span>
                  </i18n>
                </span>
              </div>
              <common-downloader
                :loading="submitting"
                :name="getFileName"
                :button-name="$t('button.export')"
                button-type="primary"
                button-size="mini"
                button-icon=""
                @download="exportDetailsFunc"
              />
            </div>
          </div>
          <Form ref="dataForm" :model="resultJson" label-width="160px">
            <div v-if="softwareShow" style="margin: 0; padding: 0">
              <div style="font-size: 15px; font-weight: 800; margin-left: -10px;">
                <span>{{ $t('pages.softSecurityDetection') }}</span>
              </div>

              <!-- 杀毒软件检查 -->
              <el-row v-if="resultJson.virusRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.killSoftInspect') }}：<span v-html="isSuccess(1, resultJson.virusRet.success)"/>
                </el-col>
                <el-col v-if="resultJson.virusRet.virusUninstall" :span="24" style="margin: 5px 0 0 20px">
                  <FormItem :label="$t('pages.killSoftwareNotStart')" style="margin-bottom: 0" label-width="120px">
                    <span>{{ virusUninstallFormatter(resultJson.virusRet.virusUninstall) }}</span>
                  </FormItem>
                </el-col>
                <el-col v-if="resultJson.virusRet.softVersion" :span="24" style="margin: 5px 0 0 20px">
                  <FormItem :label="$t('pages.softwareNotNormal')"/>
                  <div style="margin-left: 20px">
                    <div v-for="(item, index) in resultJson.virusRet.softVersion" :key="index" style="margin-top: 5px">
                      {{ virusSoftVersionFormatter(item, index, resultJson.virusRet.softVersion.length) }}
                    </div>
                  </div>
                </el-col>
                <el-col v-if="resultJson.virusRet.virusVersion" :span="24" style="margin: 5px 0 0 20px">
                  <FormItem :label="$t('pages.existVirusLibVersionNotNormal')"/>
                  <div style="margin-left: 20px">
                    <div v-for="(item, index) in resultJson.virusRet.virusVersion" :key="index" style="margin-top: 5px">
                      {{ virusSoftVersionFormatter(item) }}
                    </div>
                  </div>
                </el-col>
                <el-col v-if="resultJson.virusRet.updateDate" :span="24" style="margin: 5px 0 0 20px">
                  <FormItem :label="$t('pages.updateDateNotNormal')"/>
                  <div style="margin-left: 20px">
                    <div v-for="(item, index) in resultJson.virusRet.updateDate" :key="index" style="margin-top: 5px">
                      {{ updateDateFormatter(item) }}
                    </div>
                  </div>
                </el-col>
                <el-col v-if="resultJson.virusRet.illegalVer" :span="24" style="margin: 5px 0 0 20px">
                  <FormItem :label="$t('pages.softwareCounterfeit')" label-width="90px">
                    <span>{{ illegalVerFormatter(resultJson.virusRet.illegalVer) }}</span>
                  </FormItem>
                </el-col>
              </el-row>

              <!-- 软件安装检查 -->
              <el-row v-if="resultJson.installSoftRet || resultJson.limitInstallSoftRet" style="padding: 10px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800; margin-bottom: 5px">
                  {{ $t('pages.softwareInstallCheck') }}：<span
                    v-html="isSuccess(2, !resultJson.installSoftRet || resultJson.installSoftRet.success,
                                      !resultJson.limitInstallSoftRet || resultJson.limitInstallSoftRet.success)"
                  />
                </el-col>
                <el-col v-if="resultJson.installSoftRet" :span="24" style="padding-left: 20px; margin-top: 10px">
                  <el-col :span="24" style="font-size: 14px; font-weight: 800;">
                    {{ $t('pages.softwareMustInstallCheck') }}：<span v-html="isSuccess(1, resultJson.installSoftRet.success)"/>
                  </el-col>
                  <el-col v-if="!resultJson.installSoftRet.success" :span="24" style="padding-left: 20px; margin-top: 10px">
                    <span>{{ installSoftFormatter(resultJson.installSoftRet.softInfoList, 1) }}</span>
                  </el-col>
                </el-col>
                <el-col v-if="resultJson.limitInstallSoftRet" :span="24" style="padding-left: 20px; margin-top: 10px">
                  <el-col :span="24" style="font-size: 14px; font-weight: 800;">
                    {{ $t('pages.softwareNotInstallCheck') }}：<span v-html="isSuccess(1, resultJson.limitInstallSoftRet.success)"/>
                  </el-col>
                  <el-col v-if="!resultJson.limitInstallSoftRet.success" :span="24" style="padding-left: 20px; margin-top: 10px">
                    <span>{{ installSoftFormatter(resultJson.limitInstallSoftRet.softInfoList, 2) }}</span>
                  </el-col>
                </el-col>
              </el-row>

              <!-- 服务检查 -->
              <el-row v-if="resultJson.whiteServerRet || resultJson.blackServerRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityServiceCheck') }}：<span
                    v-html="isSuccess(2, !resultJson.whiteServerRet || resultJson.whiteServerRet.success,
                                      !resultJson.blackServerRet || resultJson.blackServerRet.success)"
                  />
                </el-col>
                <el-col v-if="resultJson.whiteServerRet" :span="24" style="margin: 5px 0 0 20px">
                  <FormItem :label="$t('pages.termSecurityServerWhiteCheck') + '：'" label-width="125px">
                    <span style="font-weight: 800;" v-html="isSuccess(1, resultJson.whiteServerRet.success,null)"/>
                  </FormItem>
                  <div v-if="!resultJson.whiteServerRet.success" style="margin-left: 20px;">
                    <div v-if="resultJson.whiteServerRet.errorType1List.length > 0" style="margin-top: 5px">
                      {{ $t('pages.serviceWhiteErrorMsg1') }}
                      {{ resultJson.whiteServerRet.errorType1List.join('，') }}
                    </div>
                    <div v-if="resultJson.whiteServerRet.errorType2List.length > 0" style="margin-top: 5px">
                      {{ $t('pages.serviceWhiteErrorMsg2') }}
                      {{ resultJson.whiteServerRet.errorType2List.join('，') }}
                    </div>
                    <div v-if="resultJson.whiteServerRet.errorType3List.length > 0" style="margin-top: 5px">
                      {{ $t('pages.serviceWhiteErrorMsg3') }}
                      {{ startErrorFormatter(resultJson.whiteServerRet.errorType3List, 1) }}
                    </div>
                    <div v-if="resultJson.whiteServerRet.errorType4List.length > 0" style="margin-top: 5px">
                      {{ $t('pages.serviceWhiteErrorMsg4') }}
                      {{ startErrorFormatter(resultJson.whiteServerRet.errorType4List, 2) }}
                    </div>
                  </div>
                </el-col>

                <el-col v-if="resultJson.blackServerRet" :span="24" style="margin: 5px 0 0 20px">
                  <FormItem :label="$t('pages.termSecurityServerBlackCheck') + '：'" label-width="125px">
                    <span style="font-weight: 800;" v-html="isSuccess(1, resultJson.blackServerRet.success,null)"/>
                  </FormItem>
                  <div v-if="!resultJson.blackServerRet.success" style="margin-left: 20px">
                    <div v-if="resultJson.blackServerRet.errorType1List.length > 0" style="margin-top: 5px">
                      {{ $t('pages.serviceBlackErrorMsg1') }}
                      {{ resultJson.blackServerRet.errorType1List.join('，') }}
                    </div>
                    <div v-if="resultJson.blackServerRet.errorType2List.length > 0" style="margin-top: 5px">
                      {{ $t('pages.serviceBlackErrorMsg2') }}
                      {{ startErrorFormatter(resultJson.blackServerRet.errorType2List, 2) }}
                    </div>
                    <div v-if="resultJson.blackServerRet.errorType3List.length > 0" style="margin-top: 5px">
                      {{ $t('pages.serviceBlackErrorMsg3') }}
                      {{ startErrorFormatter(resultJson.blackServerRet.errorType3List, 1) }}
                    </div>
                  </div>
                </el-col>
              </el-row>

              <!-- 进程检查 -->
              <el-row v-if="resultJson.whiteProcessRet || resultJson.blackProcessRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityProcessCheck') }}：<span
                    v-html="isSuccess(2, !resultJson.whiteProcessRet || resultJson.whiteProcessRet.success,
                                      !resultJson.blackProcessRet || resultJson.blackProcessRet.success)"
                  />
                </el-col>
                <el-col v-if="resultJson.whiteProcessRet" :span="24" style="margin: 5px 0 0 20px">
                  <div>
                    <FormItem :label="$t('pages.termSecurityProcessMustRunCheck') + '：'" label-width="135px">
                      <span style="font-weight: 800;" v-html="isSuccess(1, resultJson.whiteProcessRet.success,null)"/>
                    </FormItem>
                    <div v-if="!resultJson.whiteProcessRet.success" style="margin-left: 20px;">
                      <div v-if="resultJson.whiteProcessRet.errorType1List.length > 0" style="margin-top: 5px">
                        {{ $t('pages.processErrorMsg1') }}
                        {{ resultJson.whiteProcessRet.errorType1List.join('，') }}
                      </div>
                      <div v-if="resultJson.whiteProcessRet.errorType2List.length > 0" style="margin-top: 5px">
                        {{ $t('pages.processErrorMsg2') }}
                        {{ resultJson.whiteProcessRet.errorType2List.join('，') }}
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col v-if="resultJson.blackProcessRet" :span="24" style="margin: 5px 0 0 20px">
                  <div>
                    <FormItem :label="$t('pages.termSecurityProcessNotRunCheck') + '：'" label-width="135px">
                      <span style="font-weight: 800;" v-html="isSuccess(1, resultJson.blackProcessRet.success,null)"/>
                    </FormItem>
                    <div v-if="!resultJson.blackProcessRet.success" style="margin-left: 20px">
                      <div v-if="resultJson.blackProcessRet.errorTypeList.length > 0">
                        {{ $t('pages.processLimitErrorMsg1') }}
                        {{ resultJson.blackProcessRet.errorTypeList.join('，') }}
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <!-- 文件检查 -->
              <el-row v-if="resultJson.fileScanRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityFileCheck') }}：<span v-html="isSuccess(1, resultJson.fileScanRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.fileScanRet.success" :span="24" style="margin-top: 10px">
                  <div style="margin-left: 20px">
                    <div v-for="(item, index) in resultJson.fileScanRet.fileInfoList" :key="index" style="margin-bottom: 5px">
                      <span>{{ fileFormatter(item) }}</span>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <!-- 注册表检查 -->
              <el-row v-if="resultJson.regScanRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityRegistryCheck') }}：<span v-html="isSuccess(1, resultJson.regScanRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.regScanRet.success" :span="24">
                  <!-- 注册表项不符合要求 -->
                  <div v-if="resultJson.regScanRet.errorCode1List.length > 0 || resultJson.regScanRet.errorCode2List.length > 0" style="margin-left: 20px;">
                    <FormItem style="margin-bottom: 0" :label="$t('pages.registryItemErrorMsg')" label-width="230px"/>
                    <div v-if="resultJson.regScanRet.errorCode1List.length > 0" style="margin-left: 20px;">
                      <FormItem :label="$t('pages.registryItemErrorMsg1')" label-width="230px" style="margin-bottom: 0"/>
                      <div v-for="(item, index) in resultJson.regScanRet.errorCode1List" :key="index" style="margin-left: 20px; margin-bottom: 5px">
                        {{ item }}
                      </div>
                    </div>
                    <div v-if="resultJson.regScanRet.errorCode2List.length > 0" style="margin-left: 20px;">
                      <FormItem :label="$t('pages.registryItemErrorMsg2')" label-width="230px" style="margin-bottom: 0"/>
                      <div v-for="(item, index) in resultJson.regScanRet.errorCode2List" :key="index" style="margin-left: 20px;margin-bottom: 5px">
                        {{ item }}
                      </div>
                    </div>
                  </div>
                  <!-- 注册表键不符合要求 -->
                  <div v-if="resultJson.regScanRet.errorCode3List.length > 0 || resultJson.regScanRet.errorCode4List.length > 0" style="margin-left: 20px;">
                    <FormItem style="margin-bottom: 0" :label="$t('pages.registryKeyErrorMsg')" label-width="230px"/>
                    <div v-if="resultJson.regScanRet.errorCode3List.length > 0" style="margin-left: 20px;">
                      <FormItem :label="$t('pages.registryKeyErrorMsg1')" label-width="230px" style="margin-bottom: 0"/>
                      <div v-for="(item, index) in resultJson.regScanRet.errorCode3List" :key="index" style="margin-left: 20px;margin-bottom: 5px">
                        {{ regFormatter(item, false) }}
                      </div>
                    </div>
                    <div v-if="resultJson.regScanRet.errorCode4List.length > 0" style="margin-left: 20px;">
                      <FormItem :label="$t('pages.registryKeyErrorMsg2')" label-width="230px" style="margin-bottom: 0"/>
                      <div v-for="(item, index) in resultJson.regScanRet.errorCode4List" :key="index" style="margin-left: 20px;margin-bottom: 5px">
                        {{ regFormatter(item, false) }}
                      </div>
                    </div>
                  </div>
                  <!-- 注册表值不符合要求 -->
                  <div style="margin-left: 20px;">
                    <div v-if="resultJson.regScanRet.errorCode5List.length > 0" style="margin-top: 5px">
                      <FormItem :label="$t('pages.registryErrorMsg3')" label-width="230px" style="margin-bottom: 0"/>
                      <div v-for="(item, index) in resultJson.regScanRet.errorCode5List" :key="index" style="margin-left: 20px;margin-bottom: 5px">
                        {{ regFormatter(item, true) }}
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <div v-if="hostShow">
              <div :style="{ 'font-size': '15px', 'font-weight': '800', 'margin-left': '-10px', 'margin-top': softwareShow ? '25px' : '0' }">
                <span>{{ $t('pages.hostSecurityDetection') }}</span>
              </div>

              <!-- 操作系统检查 -->
              <el-row v-if="resultJson.hostRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityHostCheck') }}：<span v-html="isSuccess(1, resultJson.hostRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.hostRet.success" :span="24" style="margin-top: 10px">
                  <div style="margin-left: 20px">
                    <span>{{ hostFormatter(resultJson.hostRet) }}</span>
                  </div>
                </el-col>
              </el-row>

              <!-- 计算机名称规范检查 -->
              <el-row v-if="resultJson.computerNameRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityComputerNameCheck') }}：<span v-html="isSuccess(1, resultJson.computerNameRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.computerNameRet.success" :span="24" style="margin-top: 10px">
                  <span style="margin-left: 20px">{{ $t('pages.computeNameErrorMsg1') }}</span>
                </el-col>
                <el-col v-if="!resultJson.computerNameRet.success" :span="24" style="margin-top: 10px">
                  <span style="margin-left: 20px">{{ resultJson.computerNameRet.regDes || '' }}</span>
                </el-col>
              </el-row>

              <!-- 共享磁盘检查 -->
              <el-row v-if="resultJson.diskRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityDiskCheck') }}：<span v-html="isSuccess(1, resultJson.diskRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.diskRet.success" :span="24" style="margin-top: 10px;">
                  <div style="margin-left: 20px; ">
                    <span>{{ diskFormatter(resultJson.diskRet.diskUse) }}</span>
                  </div>
                </el-col>
              </el-row>

              <!-- 共享配置置检查 -->
              <el-row v-if="resultJson.sharedRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecuritySharedCheck') }}：<span v-html="isSuccess(1, resultJson.sharedRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.sharedRet.success" :span="24" style="padding-left: 20px; margin-top: 10px">
                  <div v-if="resultJson.sharedRet.sharePrinter && resultJson.sharedRet.sharePrinter.length > 0">
                    {{ sharePrinterFormatter(resultJson.sharedRet.sharePrinter) }}
                  </div>
                  <div v-if="resultJson.sharedRet.shareFloder && resultJson.sharedRet.shareFloder.length > 0">
                    <div>{{ sharedFormatter(resultJson.sharedRet) }}
                    </div>
                  </div>
                </el-col>
              </el-row>

              <!-- 来宾账户检查 -->
              <el-row v-if="resultJson.guestRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityGuestCheck') }}：<span v-html="isSuccess(1, resultJson.guestRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.guestRet.success" :span="24" style="margin-top: 10px">
                  <span style="margin-left: 20px; ">{{ $t('pages.guestErrorMsg1') }}</span>
                </el-col>
              </el-row>

              <!-- 计算机屏幕保护检查 -->
              <el-row v-if="resultJson.screenRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityScreenCheck') }}：<span v-html="isSuccess(1, resultJson.screenRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.screenRet.success" :span="24" style="padding-left: 20px; margin-top: 10px">
                  <span>{{ screenFormatter(resultJson.screenRet) }}</span>
                </el-col>
              </el-row>
            </div>

            <div v-if="netShow">
              <div :style="{ 'font-size': '15px', 'font-weight': '800', 'margin-left': '-10px', 'margin-top': softwareShow || hostShow ? '25px' : '0' }">
                <span>{{ $t('pages.netSecurityDetection') }}</span>
              </div>

              <!-- 端口检查 -->
              <el-row v-if="resultJson.portRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityPortCheck') }}：<span v-html="isSuccess(1, resultJson.portRet.success)"/>
                </el-col>
                <div v-if="!resultJson.portRet.success" >
                  <el-col v-for="(item, index) in resultJson.portRet.portList" :key="index" :span="24" style="padding-left: 20px; margin-top: 10px">
                    <span>{{ portFormatter(item) }}</span>
                  </el-col>
                </div>
              </el-row>

              <!-- 域环境检查 -->
              <el-row v-if="resultJson.domainRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800; ">
                  {{ $t('pages.termSecurityDomainCheck') }}：<span v-html="isSuccess(1, resultJson.domainRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.domainRet.success" :span="24" style="padding-left: 20px; margin-top: 10px">
                  <span>{{ domainFormatter(resultJson.domainRet) }}</span>
                </el-col>
              </el-row>

              <!-- 防火墙检查 -->
              <el-row v-if="resultJson.firewallRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityFirewallCheck') }}：<span v-html="isSuccess(1, resultJson.firewallRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.firewallRet.success" :span="24" style="padding-left: 20px; margin-top: 10px">
                  <span>{{ firwallFormatter(resultJson.firewallRet) }}</span>
                </el-col>
              </el-row>

              <!-- 远程桌面检查 -->
              <el-row v-if="resultJson.desktopRet" style="padding: 15px 0 0 10px">
                <el-col :span="24" style="font-size: 15px; font-weight: 800;">
                  {{ $t('pages.termSecurityDesktopCheck') }}：<span v-html="isSuccess(1, resultJson.desktopRet.success)"/>
                </el-col>
                <el-col v-if="!resultJson.desktopRet.success" :span="24" style="padding-left: 20px; margin-top: 10px">
                  <span>{{ $t('pages.desktopErrorMsg1') }}</span>
                </el-col>
              </el-row>

            </div>

          </Form>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  exportTermSecurityDetectionLogDetails,
  getById,
  getLastById
} from '@/api/system/terminalManage/termSecurityLog';
import CommonDownloader from '@/components/DownloadManager/common'
import moment from 'moment';

export default {
  name: 'InstallDlg',
  components: { CommonDownloader },
  props: {},
  data() {
    return {
      visible: false,
      temp: {},
      defaultTemp: {
        id: null,
        termId: null,
        userId: null,
        terminalName: '',
        termGroupName: '',
        userName: '',
        risk: null,
        resultJson: '',
        createDate: null,
        createTime: null,
        timestamp: null
      },
      testTotal: 0,     //  检测项数
      passTotal: 0,     //  通过项数
      notPassTotal: 0,  //  未通过项数
      resultJson: {},
      softwareShow: false,
      hostShow: false,
      netShow: false,
      submitting: false,
      type: 1  //  检测记录类型： 1-检测状态，2-检测记录

    }
  },
  methods: {
    //  检测记录根据id
    showRecord(id, createTime, timestamp) {
      this.type = 2
      this.temp = {};
      getById({ id, createTime, timestamp }).then(res => {
        if (!res.data) {
          this.$message({
            message: this.$t('pages.termSecurityExportMsg2'),
            type: 'warning',
            duration: 2000
          })
          return;
        }
        this.temp = Object.assign(this.defaultTemp, res.data);
        this.formatResultJson(res.data.resultJsonArray)
        this.visible = true
      })
    },
    //  检测状态根据id
    showStatus(id, termId, createTime) {
      this.type = 1
      this.temp = {};
      //  createDate 仅用于防止提示查询日期不能为空，未参与条件筛选
      getLastById({ id, termIds: termId + '', createTime, createDate: createTime.slice(0, 10) }).then(res => {
        if (!res.data) {
          this.$message({
            message: this.$t('pages.termSecurityExportMsg1'),
            type: 'warning',
            duration: 2000
          })
          return;
        }
        this.temp = Object.assign(this.defaultTemp, res.data);
        this.temp.createTime = this.temp.lastDate
        this.formatResultJson(res.data.resultJsonArray)
        this.visible = true
      })
    },
    //  type 1: 检测状态  2：检测记录
    showData(row, type) {
      this.type = type
      this.temp = {}
      this.temp = Object.assign(this.defaultTemp, row);
      if (type === 1) {
        this.temp.createTime = this.temp.lastDate
      }
      this.formatResultJson(row.resultJsonArray)
      this.visible = true
    },
    formatResultJson(resultArr) {
      this.resultJson = {}
      const resultJson = {}
      this.notPassTotal = 0
      this.testTotal = resultArr ? resultArr.length : 0;
      if (resultArr) {
        resultArr.forEach(result => {
          for (const key in result) {
            resultJson[key] = result[key] ? result[key][0] : null
          }
        })
        for (const key in resultJson) {
          const result = resultJson[key]
          if (!result.success) {
            //  统计不通过的检测项数量
            this.notPassTotal++;
            //  杀毒软件检查
            if (key === 'virusRet' && result.virusUninstall) {
              resultJson.virusUninstall = result.virusUninstall.map(item => item.name)
            } else if (key === 'whiteProcessRet' && result.procInfo) {
              //  进程必须运行检查
              result.errorType1List = []
              result.errorType2List = []
              result.procInfo.forEach(item => {
                if (item.errorType === 0) {
                  result.errorType1List.push(item.procName);
                } else if (item.errorType === 1) {
                  result.errorType2List.push(item.procName)
                }
              })
            } else if (key === 'blackProcessRet' && result.install) {
              //  进程禁止运行检查
              result.errorTypeList = result.install.map(item => item.procName)
            } else if (key === 'whiteServerRet' && result.serviceList) {
              //  服务器白名单检查
              result.errorType1List = []
              result.errorType2List = []
              result.errorType3List = []
              result.errorType4List = []
              result.serviceList.forEach(item => {
                if (item.errorType === 1) {
                  result.errorType1List.push(item.serviceName);
                } else if (item.errorType === 2) {
                  result.errorType2List.push(item.serviceName)
                } else if (item.errorType === 3) {
                  result.errorType3List.push(item)
                } else if (item.errorType === 4) {
                  result.errorType4List.push(item)
                }
              })
            } else if (key === 'blackServerRet' && result.serviceList) {
              //  服务器黑名单检查
              result.errorType1List = []
              result.errorType2List = []
              result.errorType3List = []
              result.serviceList.forEach(item => {
                if (item.errorType === 1) {
                  const serviceNames = item.serviceName.split('|')
                  if (serviceNames.length > 0) {
                    result.errorType1List.push(serviceNames[0])
                  }
                } else if (item.errorType === 2) {
                  result.errorType2List.push(item)
                } else if (item.errorType === 3) {
                  result.errorType3List.push(item)
                }
              })
            } else if (key === 'regScanRet' && result.regInfoList) {
              //  注册表检查
              result.errorCode1List = []   //  注册表项必须存在
              result.errorCode2List = []   //  注册表项不允许存在
              result.errorCode3List = []   //  注册表键必须存在
              result.errorCode4List = []   //  注册表键不允许存在
              result.errorCode5List = []   //  注册表值不符合要求
              result.regInfoList.forEach(item => {
                if (item.regType === 0 && item.errorCode === 0) {
                  result.errorCode1List.push(item.regItem)
                } else if (item.regType === 0 && item.errorCode === 1) {
                  result.errorCode1List.push(item.regItem)
                } else if (item.regType === 1 && item.operator === 1) {
                  result.errorCode3List.push(item)
                } else if (item.regType === 1 && item.operator === 0) {
                  result.errorCode4List.push(item)
                } else if (item.regType === 1 && item.operator !== 1 && item.operator !== 0) {
                  result.errorCode5List.push(item)
                }
              })
            }
          }
        }
        this.resultJson = resultJson;

        this.softwareShow = resultJson.virusRet || resultJson.installSoftRet || resultJson.limitInstallSoftRet || resultJson.whiteServerRet ||
          resultJson.blackServerRet || resultJson.whiteProcessRet || resultJson.blackProcessRet || resultJson.fileScanRet || resultJson.regScanRet

        this.hostShow = resultJson.hostRet || resultJson.computerNameRet || resultJson.diskRet || resultJson.sharedRet || resultJson.screenRet || resultJson.guestRet
        this.netShow = resultJson.portRet || resultJson.domainRet || resultJson.firewallRet || resultJson.desktopRet

        this.passTotal = this.testTotal - this.notPassTotal;
      }
    },
    close() {
      this.visible = false
    },
    //  健康状态格式
    riskFormatter(risk) {
      let result = ''
      if (risk !== undefined && risk !== null) {
        switch (risk) {
          case 0: result = this.$t('text.health'); break;
          case 1: result = this.$t('text.lowRisk'); break;
          case 2: result = this.$t('text.mediumRisk'); break;
          case 3: result = this.$t('text.highRisk'); break;
        }
      }
      return result;
    },
    //  是否通过
    isSuccess(type, success1, success2) {
      const flag = type === 1 ? success1 : (success1 && success2)
      return flag ? `<span style='color: green'>${this.$t('pages.termSecurityPass')}</span>` : `<span style='color: red'>${this.$t('pages.termSecurityNotPass')}</span>`
    },
    virusUninstallFormatter(list) {
      let result = ''
      if (list) {
        list.forEach(item => {
          if (item.name) {
            result += item.name + '，'
          }
        })
      }
      return result.length > 0 ? result.substr(0, result.length - 1) : '';
    },
    //  杀毒软件版本不匹配
    virusSoftVersionFormatter(row) {
      return this.$t('pages.virusSoftVersionFormat', { softwareName: row.name, symbol: this.getSymbolLabel(row.symbol), version: row.version })
    },
    //  未更新天数
    updateDateFormatter(row) {
      return this.$t('pages.virusUpdateDateFormat', { name: row.name, version: row.version })
    },
    //  软件非正版
    illegalVerFormatter(datas) {
      const names = []
      if (datas && datas.length > 0) {
        datas.forEach(item => names.push(item.name))
      }
      return names.length > 0 ? names.join('，') : ''
    },
    //  软件安装检查
    installSoftFormatter(list, type) {
      let result = type === 1 ? this.$t('pages.unInstallSoftware') : this.$t('pages.illegalInstallSoftware')
      if (list) {
        for (let i = 0, len = list.length; i < len; i++) {
          const item = list[i];
          result += item.softName + this.getInstallSoftVersion(item.softVersion) + (i === len - 1 ? '' : '，')
        }
      }
      return result;
    },
    getInstallSoftVersion(softVersion) {
      //  全版本将不显示版本号
      if (softVersion === '*.*') {
        return '';
      }
      return '(' + softVersion + ')';
    },
    //  服务运行检查  type=1：必须运行  type=2:禁止运行
    serverFormatter(list, type) {
      let result = ''
      if (list) {
        for (let i = 0, len = list.length; i < len; i++) {
          const item = list[i];
          //  获取主服务
          const serviceName = item.serviceName.includes('|') ? item.serviceName.split('|')[0] : item.serviceName
          if (item.errorType !== 0) {
            result += serviceName + this.getServiceErrorType(item.errorType, type) + '，'
          }
        }
      }
      return result.length > 0 ? result.substr(0, result.length - 1) : result;
    },
    //  服务检查  启动类型不符合的  type=1: {serviceName}的启动类型{curStartType}与要求的{startType}不符
    //  type=2 : {serviceName}未运行且启动类型{curStartType}与要求的{startType}不符
    startErrorFormatter(list, type) {
      let result = '';
      list && list.forEach(item => {
        const serviceNames = item.serviceName.split('|') || []
        if (serviceNames.length > 0) {
          result += this.$t(('pages.serviceStartTypeErrorMsg' + type), { serviceName: serviceNames[0], curStartType: this.getStartTypeLabel(item.curStartType), startType: this.getStartTypeLabel(item.startType) }) + ','
        }
      })
      return result.length > 0 ? result.substring(0, result.length - 1) : '';
    },
    //  获取启动类型
    getStartTypeLabel(type) {
      let result = ''
      if (type) {
        switch (type) {
          case 2: result = this.$t('pages.termSecurityAuto'); break;
          case 3: result = this.$t('pages.termSecurityManual'); break;
          case 4: result = this.$t('pages.termSecurityDisable'); break;
        }
      }
      return result;
    },
    //  进程检查  type=
    processFormatter(list, type) {
      let result = ''
      if (list) {
        if (type === 1) {
          const notRunList = []
          const md5NotList = []
          list.forEach(item => {
            if (item.errorType === 0) {
              item.procName !== undefined && item.procName !== null && notRunList.push(item.procName);
            } else if (item.errorType === 1) {
              item.procName !== undefined && item.procName !== null && md5NotList.push(item.procName);
            }
          })
          if (notRunList) {
            result += this.$t('pages.processNotRunFormat', { process: notRunList.join('、') })
          }
          if (md5NotList) {
            if (result.length > 0) {
              result += '，'
            }
            result += this.$t('pages.processMd5ErrorFormat', { process: md5NotList.join('、') })
          }
        } else {
          const procList = []
          list.forEach(item => { item.procName !== undefined && item.procName !== null && procList.push(item.procName) })
          if (procList) {
            result += this.$t('pages.processCloseFormat', { process: procList.join(',') })
          }
        }
      }
      return result;
    },
    //  文件检查
    fileFormatter(item) {
      return item.fileExist === 1 ? this.$t('pages.fileDetectionFileExistFormat1', { filePath: this.getFilePath(item.filePath), fileName: item.fileName })
        : this.$t('pages.fileDetectionFileExistFormat2', { filePath: this.getFilePath(item.filePath), fileName: item.fileName })
    },
    getFilePath(filePath) {
      if (filePath === null || filePath.length === 0) {
        filePath = this.$t('pages.system32Folder')
      } else {
        filePath = filePath.replaceAll('#DESKTOP#', this.$t('pages.desktop')).replaceAll('#USERTEMP#', this.$t('pages.userTempDirectory')).replaceAll('#SYSTEMTEMP#', this.$t('pages.sysTempTempDirectory'))
      }
      return filePath
    },
    //  注册表检查 - addValue=false: 注册表键格式， addValue=true：注册表值格式
    regFormatter(item, addValue) {
      const index = item.regItem.lastIndexOf('\\');
      const regItem = item.regItem.substr(0, index > 0 ? index : 0);
      const regKey = item.regItem.substr(index + 1);
      let result = this.$t('pages.regDetectionDetailsFormat1', { regItem, regKey });
      if (addValue) {
        const operatorStr = this.getRegistryOperator(item.operator);
        result += (item.regValue !== undefined && item.regValue !== null && item.regValue !== '' ? this.$t('pages.regDetectionDetailsFormat2', { operator: operatorStr, regValue: item.regValue }) : '')
      }
      return result;
    },
    //  注册表检查
    getRegErrorCode(errorCode) {
      let result = ''
      if (errorCode !== undefined && errorCode !== null) {
        switch (errorCode) {
          case 0: result = this.$t('pages.notExist'); break;                //  不存在
          case 1: result = this.$t('pages.exist'); break;                   //  存在
          case 2: result = this.$t('pages.regDetectionErrorMsg'); break;    //  不符合运算要求
          default: result = '';
        }
      }
      return result;
    },
    //  计算机操作系统检查
    hostFormatter(host) {
      let result = ''
      if (host.errorType !== undefined && host.errorType !== null) {
        if (host.errorType === 0) {
          result = this.$t('pages.hostDetectionErrorFormat1', { curVersion: (host.curVersion ? `（${host.curVersion}）` : ''), name: (host.name ? `（${host.name}）` : '') })
        } else if ([1, 2, 3].includes(host.errorType)) {
          const operatorStr = this.getSymbolLabel(host.operator)
          result = this.$t('pages.hostDetectionErrorFormat2', { curVersion: host.curVersion, operator: operatorStr, versionName: host.versionName })
        } else if (host.errorType === 4) {
          const desc = host.versionName.toLocaleLowerCase().includes('server') ? this.$t('pages.hostDetectionErrorDeskLowCase') : this.$t('pages.hostDetectionErrorServiceLowCase')
          const operatorStr = this.getSymbolLabel(host.operator)
          result = this.$t('pages.hostDetectionErrorFormat3', { curVersion: host.curVersion, desc: desc, operator: operatorStr, versionName: host.versionName })
        } else if (host.errorType === 5) {
          result = this.$t('pages.hostDetectionErrorFormat4')
        }
      }
      return result;
    },
    //  禁用共享打印机
    sharePrinterFormatter(sharePrinter) {
      const names = []
      if (sharePrinter) {
        sharePrinter.forEach(item => {
          names.push(item.name)
        })
      }
      return names.length > 0 ? (this.$t('pages.sharedDetectionErrorMsg1') + '：' + names.join('、')) : this.$t('pages.sharedDetectionErrorMsg1')
    },
    //  禁用共享配置检查
    sharedFormatter(shared) {
      let result = ''
      if (shared.shareFloder) {
        let name = ''
        shared.shareFloder.forEach(item => {
          name += item.path + '、'
        })
        if (name.length > 0) {
          result = this.$t('pages.sharedDetectionErrorFormat2') + name.substr(0, name.length - 1);
        }
      }
      return result;
    },
    //  磁盘使用空间
    diskFormatter(diskUse) {
      let result = ''
      if (diskUse) {
        const disk = diskUse[0];
        if (disk.curSysDisk) {
          result += this.$t('pages.diskDetectionSystemErrorFormat1', { sysDisk: disk.sysDisk })
        }
        if (disk.curTotalDisk) {
          result += result.length > 0 ? '，' : ''
          result += this.$t('pages.diskDetectionTotalErrorFormat1', { totalDisk: disk.totalDisk })
        }
        if (disk.curSysSize) {
          result += result.length > 0 ? '，' : ''
          result += this.$t('pages.diskDetectionSystemErrorFormat2', { sysSize: disk.sysSize })
        }
        if (disk.curTotalSize) {
          result += result.length > 0 ? '，' : ''
          result += this.$t('pages.diskDetectionTotalErrorFormat2', { totalSize: disk.totalSize })
        }
      }
      return result;
    },
    //  屏保
    screenFormatter(screen) {
      let result = ''
      const errorType = parseInt(screen.errorType)
      if ((errorType & 1) === 1) {
        result += this.$t('pages.screenDetectionErrorMsg1');
      }
      if ((errorType & 2) === 2) {
        result += (result.length > 0 ? '，' : '') + this.$t('pages.screenDetectionErrorMsg2');
      }
      if ((errorType & 4) === 4) {
        result += (result.length > 0 ? '，' : '') + this.$t('pages.screenDetectionErrorMsg3', { screenTime: screen.screenTime })
      }
      return result;
    },
    //  端口检查
    portFormatter(portItem) {
      return this.$t('pages.portDetectionErrorMsg1', { name: portItem.name, procId: portItem.procId, port: portItem.port })
    },
    //  防火墙
    firwallFormatter(firewall) {
      let result = ''
      if (firewall.name) {
        if (firewall.name.includes(',')) {
          const list = firewall.name.split(',')
          list.forEach(item => {
            result += this.getFirewallName(item) + '，'
          })
          result = result.substr(0, result.length - 1);
        } else {
          result += this.getFirewallName(firewall.name);
        }
        //  去除最后一个字符为，的问题
        if (result.lastIndexOf('，') === result.length - 1) {
          result = result.substr(0, result.length - 1);
        }
        result = this.$t('pages.firewallDetectionErrorFormat', { desc: result })
      }
      return result;
    },
    //  防火墙名称获取
    getFirewallName(name) {
      let result = ''
      if (name) {
        switch (name) {
          case 'PublicProfile': result = this.$t('pages.firewallDetectionPublicProfileMsg'); break;
          case 'StandardProfile': result = this.$t('pages.firewallDetectionStandardProfileMsg'); break;
          case 'DomainProfile': result = this.$t('pages.firewallDetectionDomainProfileMsg'); break;
          default: result = '';
        }
      }
      return result;
    },
    //  域环境检查
    domainFormatter(domain) {
      let result = '';
      if (domain.errorType) {
        switch (domain.errorType) {
          case 'blackUser': result = this.$t('pages.domainDetectionBlackUserMsg', { curAccount: (domain.curAccount ? `（${domain.curAccount}）` : '') }); break;
          case 'notDomainEnv': result = this.$t('pages.domainDetectionNotDomainEnvMsg'); break;
          case 'errorDomainEnv': result = this.$t('pages.domainDetectionErrorDomainEnvMsg', { curDomainEnv: domain.curDomainEnv, enableDomainEnv: domain.enableDomainEnv }); break;
          case 'notDomainUser': result = this.$t('pages.notDomainUser', { curAccount: (domain.curAccount ? `（${domain.curAccount}）` : '') }); break;
          default: result = '';
        }
      }
      return result;
    },
    //  服务检查-获取错误类型
    getServiceErrorType(type, serverType) {
      let result = ''
      if (type !== null) {
        switch (type) {
          case 1: result = serverType === 1 ? this.$t('pages.serviceWhiteDetectionErrorType1') : this.$t('pages.serviceBlackDetectionErrorType1'); break;
          case 2: result = serverType === 1 ? this.$t('pages.serviceWhiteDetectionErrorType2') : this.$t('pages.serviceBlackDetectionErrorType2'); break;
          case 3: result = this.$t('pages.serviceDetectionErrorType3'); break;
          case 4: result = this.$t('pages.serviceDetectionErrorType4'); break;
          default: result = '';
        }
      }
      return result;
    },
    //  符号标签获取
    getSymbolLabel(key) {
      key = key + ''
      let result = ''
      if (key) {
        switch (key) {
          case '1': result = this.$t('pages.greaterThanLowerCase'); break;
          case '2': result = this.$t('pages.equalToLowerCase'); break;
          case '3': result = this.$t('pages.lessThanLowerCase'); break;
          case '4': result = this.$t('pages.greaterThanOrEqualToLowerCase'); break;
          case '5': result = this.$t('pages.termSecurityLessThanOrEqualLowerCase'); break;
          case '6': result = this.$t('pages.termSecurityNotEqualLowerCase'); break;
          default: result = '';
        }
      }
      return result;
    },
    //  注册表检查-运算符
    getRegistryOperator(key) {
      let result = ''
      if (key) {
        switch (key) {
          case 0: result = this.$t('pages.notExistLowerCase'); break;                          //  不存在
          case 1: result = this.$t('pages.existLowerCase'); break;                             //  存在
          case 2: result = this.$t('pages.termSecurityGreaterThanLowerCase'); break;           //  大于
          case 3: result = this.$t('pages.termSecurityEqualLowerCase'); break;                 //  等于
          case 4: result = this.$t('pages.termSecurityLessThanLowerCase'); break;              //  小于
          case 5: result = this.$t('pages.termSecurityGreaterThanOrEqualLowerCase'); break;    //  大于等于
          case 6: result = this.$t('pages.termSecurityLessThanOrEqualLowerCase'); break;       //  小于等于
          case 7: result = this.$t('pages.termSecurityContainLowerCase'); break;               //  包含
          case 8: result = this.$t('pages.termSecurityExcludeLowerCase'); break;               //  不包含
          default: result = '';
        }
      }
      return result;
    },
    riskColorFormatter(risk) {
      let result = null
      if (risk !== undefined && risk !== null) {
        switch (risk) {
          case 0: result = '#2196F3'; break;
          case 1: result = '#4CAF50'; break;
          case 2: result = '#F88E03'; break;
          case 3: result = '#F44336'; break;
        }
      }
      return result;
    },
    getFileName() {
      const title = this.$t('pages.termSecurityDetectionReport')
      const date = moment().format('YYYYMMDD')
      return `${title}_${date}.xlsx`
    },
    exportDetailsFunc(file) {
      this.submitting = true
      const opts = { file, jwt: true, topic: this.$route.name }
      return exportTermSecurityDetectionLogDetails({ exportType: 'xlsx', id: this.temp.id, termIds: this.temp.termId + '', createDate: this.temp.createTime.slice(0, 10), createTime: this.temp.createTime, exportDetailsType: this.type, timestamp: this.temp.timestamp }, opts)
        .then(res => {
          this.submitting = false
        }).catch(() => {
          this.submitting = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-form-item__label {
    text-align: left;
  }
</style>
