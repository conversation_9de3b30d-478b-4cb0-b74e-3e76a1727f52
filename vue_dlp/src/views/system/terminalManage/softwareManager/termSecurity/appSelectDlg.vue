<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="appendToBody"
    :modal="false"
    :title="$t('pages.appLibrary')"
    :remark="$t('pages.appGroup_text13')"
    :visible.sync="dlgVisible"
    width="850px"
    style="max-height: 800px !important;"
    @dragDialog="handleDrag"
  >
    <div class="toolbar">
      <app-select-table ref="appSelectTable" :height="330" :multiple="multiple" :multiple-group="multipleGroup"/>
      <div style="margin-top: 10px">
        <el-checkbox v-model="supportMd5" style="height: 30px">
          {{ $t('pages.supportMd5') }}
          <el-tooltip class="item" effect="dark" placement="bottom">
            <div slot="content">{{ $t('pages.processStgLib_Msg19') }}<br/></div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-checkbox>
        <div v-if="supportMd5" style="margin-left: 20px">
          <span>{{ $t('table.checkMd5Label') }}：</span>
          <el-radio v-model="scanType" :label="3" :value="3">
            {{ $t('pages.md5LevelMap3') }}
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div slot="content">{{ $t('pages.appGroup_text20') }}<br/></div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
          <el-radio v-model="scanType" :label="1" :value="1">
            {{ $t('pages.md5Level2') }}
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div slot="content">{{ $t('pages.appGroup_text21') }}<br/></div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <link-button btn-type="primary" btn-style="float: left" :menu-code="'A58'" :link-url="'/system/baseData/appGroup'" :btn-text="$t('pages.maintainInfo', { info : $t('pages.appLibrary') })" :before-click="beforeClick"/>
      <el-button v-if="multiple" type="primary" :loading="submitting" @click="handleSelect()">
        {{ $t('pages.addSelectedApp') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import AppSelectTable from '@/views/system/baseData/appLibrary/appSelectTable'
import { getSoftwareInfotableWithVersion } from '@/api/system/baseData/fingerprintRela';

export default {
  name: 'AppSelectDlg',
  components: { AppSelectTable },
  props: {
    appendToBody: { type: Boolean, default: false },
    multipleGroup: { type: Boolean, default: true },
    addType: { type: String, default: '' }, // 用于程序版本限制区分导入的是起始版本还是截止版本
    multiple: { type: Boolean, default: true }
  },
  data() {
    return {
      dlgVisible: false,
      submitting: false,
      supportMd5: false,
      scanType: 3, //  扫描类型 3-进程特征  1-程序特征
      param: null   //  参数
    }
  },
  computed: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    initData() {
      this.scanType = 3;
      this.supportMd5 = false
    },
    show(param) {
      this.initData();
      this.param = param
      this.dlgVisible = true
      this.$nextTick(() => {
        this.$refs.appSelectTable.show()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      this.$refs.appSelectTable.getSelectedDatas().then(async resp => {
        if (resp.length === 0) {
          this.$message({
            message: this.$t('pages.processStgLib_addSelectedProcessNameHint'),
            type: 'error',
            duration: 2000
          })
          return
        }
        this.submitting = true
        //  获取md5
        let flag = false  //  若选中进程特征，是否存在进程没有属性md5，若没有将强制转换成程序md5
        const ids = []
        resp.forEach(soft => ids.push(soft.id))
        const reg = await getSoftwareInfotableWithVersion({ ids: ids.join(',') })
        if (reg.data && reg.data.length > 0) {
          resp = reg.data
        }
        reg.data.forEach(item => {
          if (this.scanType === 3 && !flag && !item.propertyMd5) {
            flag = true
          }
          if (this.supportMd5) {
            item.scanType = item.propertyMd5 ? this.scanType : 1
          } else {
            item.scanType = 0
          }
        })
        if (flag && this.supportMd5) {
          this.$confirmBox(this.$t('pages.appGroup_text6'), this.$t('text.prompt')).then(() => {
            this.$emit('select', resp, this.param)
            this.dlgVisible = false
          }).catch(() => {
          })
        } else {
          this.$emit('select', resp, this.param)
          this.dlgVisible = false
        }
        this.submitting = false
      })
    },
    beforeClick() {
      this.dlgVisible = false
      this.$emit('toAppLibraryFlag')
    }
  }
}
</script>
<style scoped>
</style>

