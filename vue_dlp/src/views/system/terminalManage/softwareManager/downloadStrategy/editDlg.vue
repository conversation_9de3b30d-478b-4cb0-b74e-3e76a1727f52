<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.softwareDownloadStrategy')"
      :stg-code="236"
      :active-able="activeAble"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <div :slot="slotName" style="padding-left: 30px;">
        <div>
          <div v-if="formable" class="toolbar">
            <el-button size="small" @click="handleOpenSoftwareSelectDlg">
              <!--从软件库导入-->
              {{ $t('pages.softwareRepositoryImport') }}
            </el-button>
            <el-button size="small" :disabled="!deletable" @click="handleDeleteSelection">
              {{ $t('button.delete') }}
            </el-button>
          </div>
          <grid-table
            ref="softwareTable"
            :show-pager="false"
            :height="200"
            row-key="key"
            :default-sort="{prop:''}"
            :multi-select="formable"
            :col-model="colModel"
            :row-datas="softwareTree"
            default-expand-all
            :selectable="selectable"
            @selectionChangeEnd="handleSelectionChange"
          />
        </div>
      </div>
    </stg-dialog>

    <software-repository-select ref="softwareSelect" @select="handleImportSelectedSoftware"/>
  </div>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/system/terminalManage/softwareDownloadStrategy'
import { listByCategories, listNamesByIds, listByIds } from '@/api/system/terminalManage/softwareRepository'
import SoftwareRepositorySelect from '@/views/system/terminalManage/softwareManager/repository/select'
import { mapGetters } from 'vuex'

export default {
  name: 'SoftwareDownloadStrategyDlg',
  components: { SoftwareRepositorySelect },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        softwareList: []
      },
      colModel: [
        { prop: 'name', label: 'name', width: '150', formatter: this.nameFormatter },
        { prop: 'source', label: 'source', width: '60', formatter: this.sourceFormatter }
      ],
      softwareTree: [],
      selection: []
    }
  },
  computed: {
    ...mapGetters(['softwareCategories', 'softwareRepositoryVer']),
    deletable() {
      return this.selection.length > 0
    }
  },
  watch: {
    softwareCategories(value) {
      if (!this.temp || !this.temp.softwareList || this.temp.softwareList.length === 0) {
        return
      }
      const types = value.map(item => item.id)
      for (let i = 0; i < this.temp.softwareList.length; i++) {
        const item = this.temp.softwareList[i]
        if (item.objType && types.indexOf(item.id) < 0) {
          this.temp.softwareList.splice(i, 1)
        }
      }
      this.calcSoftwareTree()
    },
    softwareRepositoryVer() {
      if (!this.temp || !this.temp.softwareList || this.temp.softwareList.length === 0) {
        return
      }
      const ids = this.temp.softwareList.filter(item => !item.objType).map(item => item.id)
      if (ids.length === 0) {
        return
      }
      listByIds(ids).then(res => {
        const softMap = res.data || {}
        for (let i = 0; i < this.temp.softwareList.length; i++) {
          const item = this.temp.softwareList[i]
          if (item.objType) {
            continue
          }
          const soft = softMap[item.id]
          if (!soft) {
            this.temp.softwareList.splice(i, 1)
          } else {
            item.name = soft.name
            item.type = soft.category
          }
        }
        this.calcSoftwareTree()
      })
    }
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
      this.calcSoftwareTree()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.selection = []
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs['stgDlg'].clearValidate()
      })
    },
    handleUpdate(row) {
      row.softwareList.forEach(item => {
        if (item.objType) {
          item.key = '1_' + item.id
        } else {
          item.key = '0_' + item.id
        }
      })
      const softIds = row.softwareList.filter(item => !item.objType && !item.name).map(item => item.id)
      const showUpdateDlg = () => {
        this.$refs['stgDlg'].show(row, this.formable)
        this.clearSelection()
      }
      if (softIds.length > 0) {
        listNamesByIds(softIds).then(respond => {
          const idNameMap = respond.data
          row.softwareList = row.softwareList.map(software => {
            if (!software.name) {
              software.name = software.objType ? '-' : idNameMap[software.id]
            }
            return software
          }).filter(software => !!software.name)
          showUpdateDlg()
        })
      } else {
        showUpdateDlg()
      }
    },
    handleShow(row, isGenerateStrategy) {
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    formatRowData(row) {
      this.temp.softwareList = row.softwareList || []
    },
    formatFormData(formData) {
      formData.softwareList = this.temp.softwareList.map(({ id, type, objType }) => ({ id, type, objType }))
    },
    validateFormData(formData) {
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    getSoftListId(softList) {
      var typeIds = []
      var softIds = []
      softList.forEach(item => {
        if (item.typed) {
          typeIds.push(item.id)
        }
        if (item.children) {
          item.children.forEach(children => {
            if (children.source === 1) {
              softIds.push(children.id)
            }
          })
        }
      })
      var map = new Map()
      typeIds.length > 0 ? map.set('type', typeIds) : null
      softIds.length > 0 ? map.set('soft', softIds) : null
      return map
    },
    handleOpenSoftwareSelectDlg() {
      const map = this.getSoftListId(this.softwareTree)
      var typeIds = []
      var softIds = []
      map.forEach((value, key) => {
        if (key === 'type') {
          value.forEach(item => {
            typeIds.push('C' + item)
          })
        }
        if (key === 'soft') {
          softIds = [...value]
        }
      })
      this.$refs['softwareSelect'].show(typeIds.length == 0 ? null : typeIds, softIds.length == 0 ? null : softIds)
    },
    handleImportSelectedSoftware(data, type) {
      if (type === 0) { // 软件
        const dataIds = this.$refs.softwareSelect.getRemoveCheckedRowKeys()
        this.temp.softwareList = this.temp.softwareList.filter(item => (!dataIds.includes(item.id) && item.objType === 0) || item.objType === 1)
        const softwareIds = this.temp.softwareList.filter(item => !item.objType).map(item => item.id)
        for (let i = data.length - 1; i >= 0; i--) {
          const { id, name, category: type } = data[i]
          if (softwareIds.indexOf(id) < 0) {
            this.temp.softwareList.splice(0, 0, { key: '0_' + id, id, name, type, objType: 0 })
          }
        }
      }
      if (type === 1) { // 分类
        this.temp.softwareList = this.temp.softwareList.filter(item => (data.includes(item.id) && item.objType === 1) || item.objType === 0)
        const categoryIds = this.temp.softwareList.filter(item => item.objType === 1).map(item => item.type)
        for (let i = data.length - 1; i >= 0; i--) {
          const type = data[i]
          if (categoryIds.indexOf(type) < 0) {
            this.temp.softwareList.splice(0, 0, { key: '1_' + type, id: type, name: '-', type, objType: 1 })
          }
        }
      }
      this.calcSoftwareTree()
    },
    handleDeleteSelection() {
      let type = false
      let self = false
      for (let i = 0; i < this.selection.length; i++) {
        if (this.selection[i].children) {
          type = true
        } else {
          self = true
        }
      }
      let msg = this.$t('pages.software_Msg7')
      if (type && self) {
        msg = this.$t('pages.software_Msg7_2')
      } else if (type) {
        msg = this.$t('pages.software_Msg7_1')
      }
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        this.selection.forEach(row => {
          if (row.children) {
            for (let i = this.softwareTree.length - 1; i >= 0; i--) {
              const parent = this.softwareTree[i]
              if (parent.key !== row.key) {
                continue
              }
              if (parent.typed) {
                parent.typed = false
                for (let j = parent.children.length - 1; j >= 0; j--) {
                  if (parent.children[j].source == 2) {
                    parent.children.splice(j, 1)
                  }
                }
                if (parent.children.length === 0) {
                  this.softwareTree.splice(i, 1)
                }
              }
              break
            }
          } else {
            for (let i = this.softwareTree.length - 1; i >= 0; i--) {
              const parent = this.softwareTree[i]
              for (let j = parent.children.length - 1; j >= 0; j--) {
                if (parent.children[j].key === row.key) {
                  if (parent.typed) {
                    parent.children[j].source = 2
                  } else {
                    parent.children.splice(j, 1)
                  }
                  break
                }
              }
              if (!parent.typed && parent.children.length === 0) {
                this.softwareTree.splice(i, 1)
              }
            }
          }
        })

        const softwareList = []
        for (let i = 0; i < this.softwareTree.length; i++) {
          const parent = this.softwareTree[i]
          if (parent.typed) {
            softwareList.push({ id: parent.id, type: parent.id, objType: 1, key: parent.key })
          }
          for (let j = 0; j < parent.children.length; j++) {
            const child = parent.children[j]
            if (child.source == 1) {
              softwareList.push({ id: child.id, type: parent.id, objType: 0, key: child.key, name: child.name })
            }
          }
        }
        this.temp.softwareList = softwareList
        this.clearSelection()
      }).catch(() => {})
    },
    clearSelection() {
      this.$nextTick(() => {
        this.selection = []
        if (this.$refs['softwareTable']) {
          this.$refs['softwareTable'].clearSelection()
        }
      })
    },
    selectable(row) {
      return row.source == 1 || (row.children && row.typed)
    },
    handleSelectionChange(selection) {
      this.selection = selection || []
    },
    nameFormatter(row, data) {
      if (row.children || row.key.startsWith('1_')) {
        return '<i class="el-icon el-icon-folder"/> ' + data
      }
      return data
    },
    sourceFormatter(row, data) {
      const sourceOpts = { 1: this.$t('pages.self'), 2: this.$t('table.typeId') }
      return sourceOpts[data]
    },
    categoryFormatter(row, data) {
      let category
      for (let i = 0; i < this.softwareCategories.length; i++) {
        category = this.softwareCategories[i]
        if (category.id == data) {
          return category.name
        }
      }
      return this.$t('pages.unknownCategory', { category: data })
    },
    calcSoftwareTree() {
      const softwareList = this.temp.softwareList
      if (!softwareList || softwareList.length === 0) {
        this.softwareTree = []
        return
      }
      const selfMap = {}
      const typeMap = {}
      for (let i = 0; i < softwareList.length; i++) {
        const item = softwareList[i]
        if (item.objType) {
          typeMap[item.id] = []
        } else {
          let children = selfMap[item.type]
          if (!children) {
            children = []
            selfMap[item.type] = children
          }
          children.push({ ...item, source: 1 })
        }
      }
      const types = Object.keys(typeMap)
      if (types.length === 0) {
        this.softwareTree = Object.entries(selfMap)
          .map(([type, children]) => ({ id: type, key: '1_' + type, name: this.categoryFormatter(null, type), children }))
      } else {
        listByCategories(types).then(res => {
          if (res.data && res.data.length > 0) {
            res.data.forEach(item => {
              typeMap[item.category].push({
                id: item.id,
                key: '0_' + item.id,
                name: item.name,
                type: item.category,
                source: 2
              })
            })
          }
          Object.entries(selfMap).forEach(([type, children]) => {
            const typeChildren = typeMap[type]
            if (!typeChildren) {
              typeMap[type] = children
            } else {
              typeChildren.forEach(item => {
                if (children.find(child => child.key === item.key)) {
                  item.source = 1
                }
              })
            }
          })

          this.softwareTree = Object.entries(typeMap)
            .map(([id, children]) => {
              const item = { id, key: '1_' + id, name: this.categoryFormatter(null, id), children }
              if (types.find(type => type == id)) {
                item.typed = true
              }
              return item
            })
        })
      }
    }
  }
}
</script>
