<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="title"
    :visible.sync="visible"
    width="825px"
    @close="handleDialogClose"
  >
    <Form ref="softwareForm" :model="temp" :rules="rules" label-position="right" label-width="110px" :extra-width="{ en: 50 }">
      <el-row>
        <el-col :span="12">
          <!--安装包文件-->
          <FormItem :label="$t('table.setupFile')" prop="originalFilename" :tooltip-content="$t('pages.importSupportFile', { format: accept })">
            <div class="original-file-item">
              <el-input :value="temp.originalFilename" :title="temp.originalFilename" maxlength disabled/>
              <el-upload v-show="!isApprovalPage" :class="['original-file-upload', { 'not-allowed': loading }]" action="" :disabled="loading" style="" :accept="accept" :before-upload="beforeUpload">
                <el-button size="mini" type="primary" :loading="loading" icon="el-icon-upload">{{ $t('button.uploadSetup') }}</el-button>
              </el-upload>
            </div>
            <div v-show="loading" class="file-upload-progress">
              <el-progress type="line" :stroke-width="5" :percentage="uploadPercent"/>
              <i class="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancelUpload"/>
            </div>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <!--软件图标-->
          <icon-item
            v-if="visible"
            ref="icon"
            v-model="temp.icon"
            :label="$t('table.softwareIcon')"
            prop="icon"
            :width="iconSize.width"
            :height="iconSize.height"
            @error="handleIconErr"
          />
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <!--软件名称-->
          <FormItem :label="$t('table.softwareName')" prop="name">
            <suggest-input
              v-model="temp.name"
              v-trim
              :maxlength="60"
              :title="$t('pages.referenceInfo', { info: $t('table.softwareName') })"
              :suggestions="suggestions.name"
              :placeholder="$t('pages.softwareNamePlaceholder')"
              :input-title="temp.name || $t('pages.softwareNamePlaceholder')"
            />
            <el-tooltip slot="tooltip" effect="dark" placement="top">
              <div slot="content" style="line-height: 20px">
                <div>{{ $t('pages.softwareNameTips') }}</div>
                <div v-for="(tip, index) in softwareNameAndVersionTips" :key="index">{{ tip }}</div>
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <!--软件版本-->
          <FormItem :label="$t('table.softwareVersion')" prop="version">
            <suggest-input
              v-model="temp.version"
              v-trim
              :maxlength="32"
              :title="$t('pages.referenceInfo', { info: $t('table.softwareVersion') })"
              :suggestions="suggestions.version"
              :placeholder="$t('pages.softwareVersionPlaceholder')"
              :input-title="temp.version || $t('pages.softwareVersionPlaceholder')"
            />
            <el-tooltip slot="tooltip" effect="dark" placement="top">
              <div slot="content" style="line-height: 20px">
                <div>{{ $t('pages.softwareVersionTips') }}</div>
                <div v-for="(tip, index) in softwareNameAndVersionTips" :key="index">{{ tip }}</div>
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </FormItem>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <!--软件分类-->
          <FormItem :label="$t('table.softwareCategory')" prop="category">
            <software-category-select
              v-model="temp.category"
              :addable="categoryAddable"
              :editable="categoryEditable"
              :deletable="categoryDeletable"
              @create="handleCreateCategory"
              @update="handleUpdateCategory"
              @delete="handleDeleteCategory"
            />
          </FormItem>
        </el-col>
        <el-col :span="12">
          <!--软件大小-->
          <FormItem :label="$t('table.softwareSize')">
            <el-input :value="fileSize" maxlength disabled/>
          </FormItem>
        </el-col>
      </el-row>

      <!--主执行程序-->
      <FormItem v-if="showMainExe" :label="$t('table.mainExe')" prop="mainExe">
        <el-input v-model="temp.mainExe" v-trim maxlength="260"/>
      </FormItem>

      <el-row>
        <el-col :span="12">
          <!--软件位数-->
          <!--<FormItem :label="$t('table.softwareBits')">
            <el-select v-model="temp.bits">
              <el-option v-for="bits in softwareBitsOpts" :key="bits" :value="bits" :label="bits"/>
            </el-select>
          </FormItem>-->
          <!--支持平台-->
          <FormItem :label="$t('table.supportPlatform')" prop="platform">
            <el-radio-group v-model="temp.platform">
              <el-radio v-for="(item, index) in supportedPlatformOpts" v-show="!item.disabled" :key="index" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <!--软件架构-->
          <FormItem :label="$t('table.softwareArch')" prop="architecture">
            <el-checkbox-group v-model="architectures" @change="handleArchChange">
              <el-checkbox v-for="(item, index) in supportedArchOpts" v-show="!item.disabled" :key="index" :label="item.value">
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </FormItem>
        </el-col>
      </el-row>

      <!--支持系统-->
      <FormItem :label="$t('table.supportSystem')" prop="minOsVer">
        <!--不限定系统-->
        <el-checkbox v-model="allOs" @change="handleAllOsCheck">
          {{ $t('pages.unlimitedSystem') }}
        </el-checkbox>
        <el-checkbox-group v-show="!allOs" v-model="osVersions" class="os-checkbox-group" @change="handleOsVersionsCheck">
          <el-checkbox v-for="(item, index) in supportedOsVerOpts" :key="index" :label="item.value">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </FormItem>

      <el-row>
        <el-col :span="12">
          <!--软件厂商-->
          <FormItem :label="$t('table.softwareVendor')" prop="manufacturer">
            <suggest-input
              v-model="temp.manufacturer"
              v-trim
              :maxlength="100"
              :title="$t('pages.referenceInfo', { info: $t('table.softwareVendor') })"
              :suggestions="suggestions.manufacturer"
              :input-title="temp.manufacturer"
            />
          </FormItem>
        </el-col>
        <el-col :span="12">
          <!--软件版权-->
          <FormItem :label="$t('table.softwareCopyright')" prop="copyright">
            <suggest-input
              v-model="temp.copyright"
              v-trim
              :maxlength="120"
              :title="$t('pages.referenceInfo', { info: $t('table.softwareCopyright') })"
              :suggestions="suggestions.copyright"
              :input-title="temp.copyright"
            />
          </FormItem>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <!--软件描述-->
          <FormItem :label="$t('table.softwareDescription')" prop="description">
            <suggest-input
              v-model="temp.description"
              v-trim
              type="textarea"
              :maxlength="500"
              show-word-limit
              :title="$t('pages.referenceInfo', { info: $t('table.softwareDescription') })"
              :suggestions="suggestions.description"
              :input-title="temp.description"
            />
          </FormItem>
        </el-col>
        <el-col :span="12">
          <!--版本说明-->
          <FormItem :label="$t('table.releaseNote')" prop="releaseNote">
            <el-input v-model="temp.releaseNote" v-trim type="textarea" maxlength="500" show-word-limit/>
          </FormItem>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <!--安装指南-->
          <!--<FormItem :label="$t('table.installGuide')" prop="installGuide">
            <el-input v-model="temp.installGuide" v-trim type="textarea" maxlength="500" show-word-limit/>
          </FormItem>-->
          <!--安装参数-->
          <FormItem :label="$t('table.installParam')" prop="installParam">
            <el-input v-model="temp.installParam" v-trim type="textarea" maxlength="500" show-word-limit/>
            <el-tooltip v-if="isMsi" slot="tooltip" effect="dark" placement="top">
              <template slot="content">{{ $t('pages.msiInstallParamTips') }}</template>
              <i class="el-icon-info"/>
            </el-tooltip>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <!--升级参数-->
          <FormItem :label="$t('table.upgradeParam')" prop="upgradeParam">
            <el-input v-model="temp.upgradeParam" v-trim type="textarea" maxlength="500" show-word-limit/>
          </FormItem>
        </el-col>
      </el-row>

      <!--备注-->
      <FormItem :label="$t('table.remark')">
        <el-input v-model="temp.remark" v-trim type="textarea" maxlength="300" show-word-limit/>
      </FormItem>
    </Form>
    <div v-if="!isApprovalPage" slot="footer" class="dialog-footer">
      <el-button :loading="loading || submitting" type="primary" @click="handleSave">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
    <div v-else slot="footer" class="dialog-footer">
      <el-button :loading="loading || submitting" type="primary" @click="handleAgree">
        {{ $t('pages.agree') }}
      </el-button>
      <el-button :loading="loading || submitting" type="primary" @click="handleRefuse">
        {{ $t('pages.refuse') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>

    <zip-entries-tree ref="zipTree" @selected="handleMainExeSelected" @cancel="cancelUpload" @close="handleZipTreeClose"/>

    <el-dialog
      v-el-drag-dialog
      :title="$t('text.prompt')"
      :close-on-click-modal="false"
      :modal="false"
      :show-close="true"
      width="400px"
      class="server-dlg"
      :visible.sync="confirmVisible"
      @close="handleServerClose"
    >
      <div class="server-dlg-label">{{ $t('pages.uploadToSoftwareServerTips') }}</div>
      <el-checkbox checked disabled>{{ getServerLabel(mainServer) }}</el-checkbox>
      <el-checkbox-group v-model="uploadServerIds">
        <el-checkbox v-for="(item, index) in slaveServers" :key="index" :label="item.devId">{{ getServerLabel(item) }}</el-checkbox>
      </el-checkbox-group>
      <div slot="footer">
        <el-button type="primary" @click="handleServerSelected">{{ $t('button.confirm') }}</el-button>
        <el-button @click="confirmVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import IconItem from '@/views/system/terminalManage/softwareManager/components/IconItem'
import SuggestInput from '@/views/system/terminalManage/softwareManager/components/SuggestInput'
import SoftwareCategorySelect from '@/views/system/terminalManage/softwareManager/category/select'
import ZipEntriesTree from '@/views/system/terminalManage/softwareManager/repository/zipEntriesTree'
import { formatFileSize } from '@/utils'
import md5 from '@/utils/md5'
import {
  checkFile,
  uploadChunk,
  getSoftwareProps,
  getByName,
  addSoftware,
  getFileSuffix,
  updateSoftware,
  supportedPlatforms,
  softwareArchitectures,
  supportedOsVersions,
  supportedSuffixes
} from '@/api/system/terminalManage/softwareRepository'
import { listServers } from '@/api/system/deviceManage/softwareServer'
import { encodeArrToVal, decodeValToArr } from '@/api/dataEncryption/fileTrace/documentTrack'
import { now } from '@/views/system/terminalManage/softwareManager/components/HugeTree/util'
import {
  approval
} from '@/api/system/terminalManage/approvalSoftware'

export default {
  name: 'SoftwareRepositoryEdit',
  components: { IconItem, SuggestInput, SoftwareCategorySelect, ZipEntriesTree },
  props: {
    categoryAddable: {
      type: Boolean,
      default: true
    },
    categoryEditable: {
      type: Boolean,
      default: true
    },
    categoryDeletable: {
      type: Boolean,
      default: true
    },
    // 是否是审批页面
    isApprovalPage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      submitting: false,
      dialogStatus: 'create',
      temp: {},
      oldTemp: {},
      copyTemp: {},
      defaultTemp: {
        id: undefined,
        originalFilename: undefined,
        md5: undefined,
        name: undefined,
        icon: undefined,
        category: undefined,
        size: undefined,
        version: undefined,
        bits: 32,
        manufacturer: undefined,
        platform: 1,
        architecture: 0,
        minOsVer: '0',
        copyright: undefined,
        description: undefined,
        installGuide: undefined,
        installParam: undefined,
        upgradeParam: undefined,
        releaseNote: undefined,
        mainExe: undefined,
        uploadStatus: 0,
        remark: undefined
      },
      rules: {
        originalFilename: [{ required: true, message: this.$t('pages.uploadSetupTips'), trigger: 'blur' }],
        name: [
          { required: true, message: this.$t('text.pleaseEnterInfo', { info: this.$t('table.softwareName') }), trigger: 'blur' }
        ],
        icon: [{ required: true, validator: this.iconValidator, trigger: 'blur' }],
        category: [{ required: this.categoryRequired, validator: this.categoryValidator, trigger: 'change' }],
        version: [{ required: true, message: this.$t('text.pleaseEnterInfo', { info: this.$t('table.softwareVersion') }), trigger: 'blur' }],
        platform: [{ required: true, message: this.$t('pages.softwarePlatformSelectTips'), trigger: 'blur' }],
        architecture: [{ required: true, validator: this.archValidator, trigger: 'blur' }],
        minOsVer: [{ required: true, message: this.$t('pages.softwareOsVersionSelectTips'), trigger: 'blur' }],
        mainExe: [{ required: true, message: this.$t('pages.mainExeInputTips'), trigger: 'blur' }],
        installParam: [{ required: false, message: this.$t('text.pleaseEnterInfo', { info: this.$t('table.installParam') }), trigger: 'blur' }]
      },
      iconSize: {
        width: 32,
        height: 32
      },
      suggestions: {
        name: [],
        version: [],
        manufacturer: [],
        copyright: [],
        description: []
      },
      isMsi: false,
      uploadPercent: 0,
      uploadCanceled: false,
      architectures: [],
      allOs: true,
      osVersions: [],
      uploadServerIds: [],
      mainServer: {},
      slaveServers: [],
      iconErr: undefined,
      showMainExe: false,
      oldName: undefined,
      fileChanged: false,
      confirmed: false,
      confirmTime: 0,
      confirmVisible: false,
      editFunc: undefined,
      softwareNameAndVersionTips: [],
      softwareBitsOpts: [32, 64],
      supportedPlatformOpts: supportedPlatforms,
      supportedArchOpts: softwareArchitectures,
      supportedOsVerOpts: supportedOsVersions,
      isNeedValid: true
    }
  },
  computed: {
    title() {
      if (this.isApprovalPage) {
        return this.$t('route.softwareApproval')
      } else {
        return this.i18nConcatText(this.$t('pages.software'), this.dialogStatus)
      }
    },
    categoryRequired() {
      if (this.isApprovalPage) {
        return false
      } else {
        return true
      }
    },
    accept() {
      return supportedSuffixes.map(item => item.value).join()
    },
    fileSize() {
      if (!this.temp || null == this.temp.size) {
        return ''
      }
      return formatFileSize(this.temp.size)
    }
  },
  watch: {
    isMsi(value) {
      this.rules.installParam[0].required = value
      this.clearFormValidate()
    }
  },
  created() {
    for (let i = 0; i < 5; i++) {
      this.softwareNameAndVersionTips.push(this.$t('pages.softwareNameAndVersionTip' + i))
    }
    this.isNeedValid = true
  },
  methods: {
    handleDialogClose() {
      this.cancelUpload()
      this.clearFormValidate()
    },
    clearFormValidate() {
      if (this.$refs.softwareForm) {
        this.$refs.softwareForm.clearValidate()
      }
    },
    show(data, category) {
      this.isNeedValid = true
      this.clearFormValidate()
      if (data) {
        this.dialogStatus = 'update'
        this.temp = { ...this.defaultTemp, ...data }
        this.oldName = data.name
        this.editFunc = updateSoftware
        this.calcFilenameIsMsiOrZip(data.originalFilename)
      } else {
        this.dialogStatus = 'create'
        this.temp = { ...this.defaultTemp, category }
        this.oldName = undefined
        this.editFunc = addSoftware
        this.calcFilenameIsMsiOrZip(null)
      }
      this.resetArchAndOs()
      Object.values(this.suggestions).forEach(item => item.splice(0))
      this.uploadPercent = 0
      this.uploadCanceled = false
      this.submitting = false
      this.uploadServerIds = []
      this.slaveServers = []
      this.iconErr = undefined
      this.loading = false
      this.submitting = false
      this.fileChanged = false
      this.confirmTime = 0
      this.confirmVisible = false
      this.copyTemp = Object.assign({}, this.temp)
      this.visible = true
    },
    resetArchAndOs() {
      this.architectures = decodeValToArr(this.temp.architecture, 4)

      this.allOs = this.temp.minOsVer === '0'
      if (this.allOs) {
        this.osVersions = []
      } else {
        this.osVersions = this.temp.minOsVer.split('|').map(value => parseInt(value))
      }
    },
    calcFileSuffixIsMsiOrZip(suffix) {
      if (!suffix) {
        this.isMsi = false
        this.showMainExe = false
        return
      }
      this.isMsi = '.msi' === suffix
      this.showMainExe = supportedSuffixes.filter(item => item.archived).map(item => item.value).includes(suffix)
    },
    calcFilenameIsMsiOrZip(filename) {
      if (!filename) {
        this.isMsi = false
        this.showMainExe = false
        return
      }
      const suffix = getFileSuffix(filename)
      this.calcFileSuffixIsMsiOrZip(suffix)
    },
    cancelUpload() {
      this.uploadCanceled = true
      this.uploadPercent = 0
      this.loading = false
      if (this.oldTemp) {
        this.calcFilenameIsMsiOrZip(this.oldTemp.originalFilename)
        this.temp = this.oldTemp
      }
    },
    beforeUpload(file) {
      this.uploadPercent = 0
      this.uploadCanceled = false
      if (file.size === 0) {
        this.$message({
          title: this.$t('text.fail'),
          message: this.$t('pages.installPackageFileSizeCantZero'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const suffix = getFileSuffix(file.name)
      if (!suffix) {
        this.$message({
          message: this.$t('pages.setupFormatUnsupportedTips1'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (supportedSuffixes.map(item => item.value).indexOf(suffix) < 0) {
        this.$message({
          message: this.$t('pages.setupFormatUnsupportedTips', { format: suffix }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      this.iconErr = undefined
      this.calcFileSuffixIsMsiOrZip(suffix)
      this.checkAndUploadFile(file)
      return false
    },
    checkAndUploadFile(file) {
      this.loading = true
      this.oldTemp = { ...this.temp }
      this.temp.originalFilename = file.name
      this.temp.size = file.size
      if (this.isMsi) {
        this.temp.installParam = '-i'
      } else {
        this.temp.installParam = undefined
      }
      // const category = this.temp.category
      // this.temp = { ...this.defaultTemp, category, originalFilename: file.name, size: file.size }
      this.$refs.softwareForm.clearValidate()
      const cancelMd5 = md5.md5File(file,
        fileMd5 => this.afterMd5Calculated(fileMd5, file),
        (loaded, total, abort) => this.onUploadProgress(loaded, total, 0, 30, abort))
      if (this.uploadCanceled) {
        cancelMd5()
      }
    },
    afterMd5Calculated(fileMd5, file) {
      if (this.uploadCanceled) {
        return
      }
      if (fileMd5 === this.temp.md5) {
        this.$message({
          message: this.$t('pages.setupNotChange'),
          type: 'error',
          duration: 2000
        })
        this.cancelUpload()
        return
      }
      this.fileChanged = true
      const data = { fileName: file.name, fileSize: file.size, fileMd5 }
      checkFile(data).then(res => {
        if (this.uploadCanceled) {
          return
        }
        const status = res.data.status
        if (status < 50) {
          this.uploadChunks(file, fileMd5, res.data.uploadedSize).then(() => this.updateSoftwareProps(data))
        } else if (status === 50) {
          this.updateSoftwareProps(data)
        } else if (status === 100) {
          this.$confirmBox(this.$t('pages.setupExisted')).then(() => {
            this.dialogStatus = 'update'
            this.temp = { ...this.defaultTemp, ...res.data.config }
            this.resetArchAndOs()
            this.$refs.icon.watchSrc(this.temp.icon)
            this.oldName = data.name
            this.editFunc = updateSoftware
            this.loading = false
            this.uploadPercent = 0
          }).catch(() => {
            this.cancelUpload()
          })
        } else if (status === 101) {
          this.uploadChunks(file, fileMd5, res.data.uploadedSize).then(() => this.updateSoftwareProps(data, res.data.config))
        }
      })
    },
    uploadChunks(file, md5, index) {
      return uploadChunk(file, md5, index, (loaded, total, abort) => {
        this.onUploadProgress(loaded, total, 30, 50, abort)
        return this.uploadCanceled
      }).catch(this.cancelUpload)
    },
    onUploadProgress(loaded, total, start, weight, abort) {
      if (this.uploadCanceled) {
        abort('canceled')
        return
      }
      this.uploadPercent = start + Math.floor(loaded / total * weight)
    },
    updateSoftwareProps(data, config) {
      if (this.uploadCanceled) {
        return
      }
      data.iconWidth = this.iconSize.width
      data.iconHeight = this.iconSize.height
      if (this.showMainExe) {
        this.uploadPercent = 80
        return this.$refs.zipTree.showZipEntries(data, config)
      }
      return this.getAndUpdateProps(data, config)
    },
    getAndUpdateProps(data, config) {
      let timer = setInterval(() => {
        if (this.uploadCanceled || this.uploadPercent >= 99) {
          clearInterval(timer)
          timer = undefined
          return
        }
        this.uploadPercent++
      }, 500)
      return getSoftwareProps(data).then(res => {
        if (timer) {
          clearInterval(timer)
        }
        if (this.uploadCanceled) {
          return
        }
        this.updateTemp(res.data, config)
      })
    },
    updateTemp(data, config) {
      this.uploadPercent = 100
      this.setSuggestions(data, config, 'name', 60)
      this.setSuggestions(data, config, 'version', 32)
      this.setSuggestions(data, config, 'manufacturer', 100)
      this.setSuggestions(data, config, 'copyright', 120)
      this.setSuggestions(data, config, 'description', 500)

      const { id, category, architecture, minOsVer, installGuide, installParam, upgradeParam, releaseNote } = this.temp
      // 名称、版本、架构需手动输入
      const name = undefined
      const version = undefined
      // const architecture = 0
      // const minOsVer = '0'
      // 厂商、版权、描述字段长度超过限制进行截取
      if (data.manufacturer && data.manufacturer.length > 100) {
        data.manufacturer = data.manufacturer.slice(0, 100)
      }
      if (data.copyright && data.copyright.length > 120) {
        data.copyright = data.copyright.slice(0, 120)
      }
      if (data.description && data.description.length > 500) {
        data.description = data.description.slice(0, 500)
      }
      this.temp = { ...this.defaultTemp, ...data, id, name, version, architecture, minOsVer, category, installGuide, installParam, upgradeParam, releaseNote }
      this.resetArchAndOs()
      this.$refs.icon.watchSrc(this.temp.icon)
      this.loading = false
      this.uploadPercent = 0
    },
    setSuggestions(data, config, field, len) {
      const suggestions = []
      let value = (data[field] || '').trim()
      if (value.length > len) {
        value = value.slice(0, len)
      }
      if (value.length) {
        suggestions.push(value)
      }
      if (config) {
        const configVal = config[field]
        if (configVal && configVal !== value) {
          suggestions.push(configVal)
        }
      }
      this.suggestions[field] = suggestions
    },
    handleMainExeSelected(data, config) {
      this.temp.mainExe = data.mainExe
      return this.getAndUpdateProps(data, config)
    },
    handleZipTreeClose(data, config) {
      this.uploadPercent = 100
      this.loading = false
      this.uploadPercent = 0
      const info = {
        originalFilename: data.fileName,
        size: data.fileSize,
        md5: data.fileMd5,
        icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAAXNSR0IB2cksfwAAAS9QTFRFAAAARDQsAAAAVDQsTDQcBAxsJTIYdDRMhDRctESs0djrZDQ7GFMOCEkcizRrf39/GW4MXDgsACNkeUsFGZYruL/bVDw8i1MHt7e39vf9DFQMFBysH377P0bgLCwcpVWViIqHdHJwiW9ODe8ElDR0tDyUHmEM7Ybm/Kz8CRPE6e7/bEMEqGwlwsrhHPxkREz0CnMZhGYzOScDXDwEZ2cEtJxs0ppVsIJLPzApGyPWFogaEBSUDNEEr7nP4OCx3HzU6rdyk2EhLDwsK4kxPlDzAAAgTDQEdHSEDAx829NBOKX9nKScIpP8wIAx/Nz8vK18of+/Wbf9BAQEvVmyYEsSlX9anVwEm5YrjM/8rKy0Vf2LzFTEglUc//8EpKEONERWysqXk4EMDLQ0dKz8rOH8bSKB5QAAAGV0Uk5TAP////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9iplCQAAABxElEQVR4nNXT2XLaMBQGYMuiloMDRBLYoSTeQp0GQ2uXfQkEwpJ939uk6/s/Qy1LJnmBXvQ7Nz4zv2f0z0iS9D8AqVc0pPXlAkTAASkIw58hhhBi6n67gSk+bxIQauGL62MNa0Z7TDHkA0Vi10lhDYXnt0iLxmiNqSaIRK3g6D5S3fNbFakoO96rGkjQ4kQ6XcjpajmbTDVKZAUUJcB6JUqgr9crnFVu791RSywqkHJgvZgu6MWzs4+x7Wuj1fat0TYzsjoST+ijleFQ/IXuDKxaH2LlKLAbJSrAUptNNYawX8S+KpYOaxElAMKuC2OguKgtbCh0eAtgyjIAMjOb1K6OTVsWRAvne+ngHUcmj1cLM0fERpYt/jxcXh4dnZxsdI9N03Q2NziStPg9Hf74zEy7tq5PGqVNjiQtpt1mcyfWb7Taxk2/xJGkRf/AdfkxyMyg8xlJjiTlkhaEJCdvnVL5TQubtdAD730ic1qlmSWWYC2eLy6ePjFbg/2qQXuDrdhAkXhC/5U/PMwz+73AoH7Qy/NNie+tXQFBcH+/FvM8Wq97a2JR+M22gbfaaKxymfp8nkm+FXH3TeXVl8hy+devjvkL0PBH2aT3dcIAAAAASUVORK5CYII='
      }
      return this.updateTemp(info, config)
    },
    handleIconErr(error) {
      this.iconErr = error
      if (error) {
        this.$refs.softwareForm.validateField('icon')
      } else {
        this.$refs.softwareForm.clearValidate('icon')
      }
    },
    handleArchChange() {
      if (!this.architectures || this.architectures.length === 0) {
        this.temp.architecture = 0
      } else {
        this.temp.architecture = encodeArrToVal(this.architectures)
      }
      this.$refs.softwareForm.validateField('architecture')
    },
    handleAllOsCheck() {
      if (this.allOs) {
        this.temp.minOsVer = '0'
        this.$refs.softwareForm.validateField('minOsVer')
      } else {
        this.handleOsVersionsCheck()
      }
    },
    handleOsVersionsCheck() {
      if (!this.osVersions || this.osVersions.length === 0) {
        this.temp.minOsVer = undefined
      } else {
        this.temp.minOsVer = this.osVersions.join('|')
      }
      this.$refs.softwareForm.validateField('minOsVer')
    },
    nameValidator(rule, value, callback) {
      if (value && value !== this.oldName) {
        getByName(value).then(res => {
          const category = res.data
          if (category && category.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      } else {
        callback()
      }
    },
    iconValidator(rule, value, callback) {
      if (this.iconErr) {
        callback(this.iconErr)
      } else if (!value) {
        callback(this.$t('pages.uploadSoftwareIconTips'))
      } else {
        callback()
      }
    },
    archValidator(rule, value, callback) {
      if (!value) {
        callback(this.$t('pages.softwareArchSelectTips'))
      } else {
        callback()
      }
    },
    categoryValidator(rule, value, callback) {
      if (!value && this.isNeedValid) {
        callback(this.$t('pages.pleaseSelectContent', { content: this.$t('table.softwareCategory') }))
      } else {
        callback()
      }
    },
    handleCreateCategory(callback) {
      this.$emit('createCategory', callback)
    },
    handleUpdateCategory(data) {
      this.$emit('updateCategory', data)
    },
    handleDeleteCategory(data, callback) {
      this.$emit('deleteCategory', data, callback)
    },
    listSoftwareServers() {
      return listServers().then(res => {
        const servers = res.data || []
        if (servers.length === 1) {
          const server = servers[0]
          if (!server.mainPath) {
            return []
          }
          const isPort = port => {
            if (port == null || isNaN(port)) {
              return false
            }
            if (typeof port === 'number') {
              return port >= 0 && port <= 65535
            }
            const portStr = port.toString().trim()
            if (!portStr || portStr.indexOf('.') >= 0) {
              return false
            }
            const portVal = parseInt(portStr)
            return portVal >= 0 && portVal <= 65535
          }
          const isAddressConfigured = (server.enableInternet && (server.internetIp || server.internetIpv6) && isPort(server.internetPort)) ||
            (!server.enableInternet && (server.intranetIp || server.intranetIpv6) && isPort(server.intranetPort))
          if (!isAddressConfigured) {
            return []
          }
        }
        return servers
      })
    },
    handleSave() {
      this.$refs.softwareForm.validate(valid => {
        if (valid) {
          this.submitting = true
          if (!this.fileChanged) {
            this.saveData(this.temp)
            return
          }
          this.listSoftwareServers().then(servers => {
            if (servers.length === 0) {
              this.$alert(this.$t('pages.softwareServerConfigTips'), this.$t('text.prompt'), {
                confirmButtonText: this.$t('button.confirm2'),
                type: 'warning',
                callback: action => {
                  if (action === 'confirm' && this.hasPermission('A4K')) {
                    this.$router.push('/system/deviceManage/softwareServer')
                  }
                }
              })
              return
            }
            const isOnline = servers.filter(item => item.devId === 6401).some(item => !!item.onlineStatus)
            if (!isOnline) {
              this.$alert(this.$t('pages.softwareServerStatusTips'), this.$t('text.warning'), {
                confirmButtonText: this.$t('button.confirm2'),
                type: 'error'
              })
              return
            }
            if (servers.length === 1) {
              this.saveData(this.temp)
              return
            }
            const slaveServers = []
            servers.forEach(item => {
              if (item.devId === 6401) {
                this.mainServer = item
              } else {
                slaveServers.push(item)
              }
            })
            this.slaveServers = slaveServers
            this.confirmTime = 0
            this.confirmVisible = true
          })
        }
      })
    },
    handleAgree() {
      this.isNeedValid = true
      this.$refs.softwareForm.validate(valid => {
        if (valid) {
          this.submitting = true
          const tempData = { approvalSoftwareSubmits: [this.temp], type: 1, softNames: [this.temp.name] }
          approval(tempData).then(() => {
            this.$emit('change')
            this.visible = false
          }).finally(() => {
            this.submitting = false
          })
        }
      })
    },
    handleRefuse() {
      this.submitting = true
      const tempData = { approvalSoftwareSubmits: [this.copyTemp], type: 0, softNames: [this.copyTemp.name] }
      approval(tempData).then(() => {
        this.$emit('change')
        this.visible = false
      }).finally(() => {
        this.submitting = false
      })
    },
    saveData(data) {
      this.editFunc(data).then(() => {
        this.$emit('change')
        this.$notify({
          title: this.$t('text.success'),
          message: this.title + this.$t('text.success'),
          type: 'success',
          duration: 2000
        })
        this.visible = false
      }).finally(() => {
        this.submitting = false
      })
    },
    handleServerClose() {
      if (this.confirmed) {
        this.confirmed = false
        return
      }
      this.submitting = false
      this.uploadServerIds = []
    },
    handleServerSelected() {
      this.confirmVisible = false
      const current = now()
      const wait = current - this.confirmTime
      this.confirmTime = current
      if (wait > 5000) {
        this.confirmed = true
        const data = { ...this.temp, uploadServerIds: this.uploadServerIds }
        this.saveData(data)
      } else {
        console.log('[select software servers to upload] repeat click!')
      }
    },
    getServerLabel(data) {
      let label = data.name;
      if (data.intranetIpv6) {
        label += ' (' + data.devId + '_' + data.intranetIpv6 + ':' + data.intranetPort + ')'
      } else if (data.internetIpv6) {
        label += ' (' + data.devId + '_' + data.internetIpv6 + ':' + data.internetPort + ')'
      } else if (data.intranetIp) {
        label += ' (' + data.devId + '_' + data.intranetIp + ':' + data.intranetPort + ')'
      } else if (data.internetIp) {
        label += ' (' + data.devId + '_' + data.internetIp + ':' + data.internetPort + ')'
      }
      return label
    }
  }
}
</script>

<style lang="scss" scoped>
  .original-file-item {
    display: flex;
    .original-file-upload {
      position: relative;
      bottom: 2px;
      margin-left: 5px;
      &.not-allowed>>>.el-upload {
        cursor: not-allowed
      }
    }
  }
  .file-upload-progress {
    height: 14px;
    line-height: 14px;
    .el-progress {
      width: calc(100% - 20px);
      display: inline-block;
      >>>.el-progress-bar {
        padding-right: 40px;
        margin-right: -47px;
      }
      >>>.el-progress__text {
        vertical-align: unset;
      }
    }
    i.el-icon-switch-button {
      cursor: pointer;
      /*color: #68a8d0;*/
      color: #f56c6c;
      font-weight: 700;
      &:hover {
        /*color: #4995c5;*/
        color: #f78989;
      }
    }
  }
  .el-form-item__content>.el-radio-group {
    margin-left: 0;
    >>>.el-radio__label {
      padding-left: 3px;
    }
  }
  .os-checkbox-group {
    .el-checkbox {
      width: 180px;
    }
  }
  .server-dlg {
    .server-dlg-label {
      height: 30px;
      font-weight: 700;
    }
    .el-checkbox {
      line-height: 30px;
    }
  }
</style>
