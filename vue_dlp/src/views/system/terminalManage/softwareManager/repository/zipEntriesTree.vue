<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.mainExeSelectTitle')"
    :close-on-click-modal="false"
    :modal="false"
    :show-close="true"
    width="500px"
    :visible.sync="visible"
    @close="handleClose"
  >
    <Form label-position="left" label-width="78px" :extra-width="{ en: 90 }">
      <FormItem label-width="0">
        <huge-tree
          ref="tree"
          show-search-bar
          show-node-count
          :max-height="200"
          :expand-level="3"
          :placeholder="$t('components.enterContent')"
          :empty-text="$t('text.noData')"
          :search-method="searchMethod"
          @click-node="handleNodeClick"
        >
          <el-select
            slot="search-append"
            v-model="fileType"
            :class="['search-append-select', lang ]"
            :placeholder="$t('pages.pleaseSelectContent', { content: $t('table.fileType') })"
            @change="handleFileTypeChange"
          >
            <el-option :label="$t('pages.collectAll')" :value="0"></el-option>
            <el-option :label="$t('pages.executableFile1')" :value="1"></el-option>
          </el-select>
          <svg-icon slot="icon" slot-scope="{ node }" :icon-class="node.icon"/>
        </huge-tree>
      </FormItem>
      <FormItem :label="$t('table.mainExe')" :error="error">
        <el-input v-model="mainExe" v-trim :title="mainExe" maxlength disabled/>
      </FormItem>
    </Form>

    <div slot="footer">
      <el-button type="primary" @click="handleMainExeSelected">{{ $t('button.confirm') }}</el-button>
      <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script type="text/jsx">
import { getZipEntries } from '@/api/system/terminalManage/softwareRepository'
import { getFileNameIcon } from '@/icons/extension'
import HugeTree from '@/views/system/terminalManage/softwareManager/components/HugeTree'

const EXECUTABLE_SUFFIXES = ['.exe', '.msi', '.bat', '.cmd', '.vbs', '.ps1', '.sh']

function isExecutable(filename) {
  let i
  if (!filename || (i = filename.lastIndexOf('.')) < 0) {
    return false
  }
  const suffix = filename.slice(i).toLowerCase()
  return EXECUTABLE_SUFFIXES.includes(suffix)
}

function pathsToTreeMap(paths) {
  const pathTree = {}
  let pathParts
  let parentPath
  let currentPath
  let pathLabel
  let id = 1
  let pid
  for (let i = 0; i < paths.length; i++) {
    pathParts = paths[i].split('/')
    parentPath = pathTree
    pid = 0
    for (let j = 0; j < pathParts.length; j++) {
      pathLabel = pathParts[j]
      currentPath = parentPath[pathLabel]
      if (!currentPath) {
        currentPath = {
          id: id++,
          pId: pid,
          label: pathLabel,
          name: paths[i]
        }
        if (j === pathParts.length - 1) {
          currentPath.icon = getFileNameIcon(pathLabel)
          currentPath.executable = isExecutable(pathLabel)
        } else {
          currentPath.icon = 'dir1'
          currentPath.children = {}
        }
        parentPath[pathLabel] = currentPath
      }
      pid = currentPath.id
      parentPath = currentPath.children
    }
  }
  return pathTree
}

function convertTreeMapToTreeList(treeMap) {
  return Object.values(treeMap).map(item => {
    if (item.children) {
      item.children = convertTreeMapToTreeList(item.children)
    }
    return item
  })
}

export default {
  name: 'ZipEntriesTree',
  components: { HugeTree },
  data() {
    return {
      visible: false,
      selected: false,
      fileType: 1,
      entries: [],
      mainExe: undefined,
      error: undefined
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    }
  },
  methods: {
    handleClose() {
      if (this.$refs.tree) {
        this.$refs.tree.clear()
      }
      if (this.selected) {
        this.selected = false
        return
      }
      this.$emit('close', { ...this.temp }, this.config)
    },
    showZipEntries(data, config) {
      return getZipEntries(data.fileMd5).catch(e => {
        this.$emit('cancel')
        return Promise.reject(e)
      }).then(res => {
        const paths = res.data || []
        if (paths.length <= 1) {
          this.$message({
            message: this.$t('pages.noFileInZip'),
            type: 'error',
            duration: 3000
          })
          this.$emit('cancel')
          return
        }
        this.fileFormat = paths.splice(paths.length - 1, 1)[0]
        const entries = [{
          id: 0,
          pId: null,
          label: data.fileName,
          icon: getFileNameIcon(data.fileName),
          children: convertTreeMapToTreeList(pathsToTreeMap(paths))
        }]
        this.fileType = 1
        this.mainExe = undefined
        this.error = undefined
        this.temp = data
        this.config = config
        this.selected = false
        this.visible = true
        this.$nextTick(() => {
          this.$refs.tree.setData(entries)
        })
      })
    },
    searchMethod(keyword, node) {
      if (!node || !node.label) return false
      const included = !keyword || node.label.toLowerCase().includes(keyword)
      return included && (!this.fileType || node.executable)
    },
    handleFileTypeChange() {
      if (this.$refs.tree) {
        this.$refs.tree.debounceInput()
      }
    },
    handleNodeClick(node, event) {
      this.error = undefined
      if (node.isLeaf) {
        this.mainExe = node.name
        this.$refs.tree.setCurrentNode(node)
      } else {
        this.mainExe = undefined
        this.$refs.tree.setCurrentNode()
      }
      event.stopPropagation()
    },
    handleMainExeSelected() {
      if (!this.mainExe) {
        this.error = this.$t('pages.mainExeSelectTips')
        return
      }
      this.selected = true
      this.$emit('selected', { ...this.temp, mainExe: this.mainExe, fileFormat: this.fileFormat }, this.config)
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-dialog {
    .el-dialog__body {
      padding: 10px 15px 5px;
    }
    .el-dialog__footer {
      padding: 8px 15px;
      .el-button {
        margin: 0;
      }
    }
  }
  .huge-tree-wrapper {
    .el-select.search-append-select {
      width: 120px;
      &.en {
        width: 145px;
      }
      &.tw {
        width: 106px;
      }
    }
    >>>.search-bar>.el-input>.el-input-group__append {
      color: #fff;
      background: linear-gradient(#7cc0ea, #4384ad);
      border-color: #7cc0ea;
      &:hover {
        background: linear-gradient(#7cc0ea, #358cc2);
        color: #7cc0ea;
      }
      .el-input {
        .el-input__inner {
          position: relative;
          bottom: 2px;
          text-align: center;
        }
        .el-input__suffix .el-select__caret {
          color: #000;
        }
      }
    }
    >>>.huge-tree-node-wrapper {
      &:hover, &.is-active {
        background-color: #ccc!important;
      }
      .expand-node {
        &:hover {
          background-color: #ccc!important;
        }
        &::before {
          color: #666 !important;
        }
      }
      .huge-tree-node__icon {
        width: 20px;
      }
      .huge-tree-node__count {
        color: #909399 !important;
      }
    }
  }
</style>
