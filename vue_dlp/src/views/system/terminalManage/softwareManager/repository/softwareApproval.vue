<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <!-- <el-button icon="el-icon-s-check" size="mini" :disabled="!approvable" @click="handleApproval">
          {{ $t('pages.approval') }}
        </el-button> -->
        <div class="searchCon">
          <el-input
            v-model="query.name"
            v-trim
            clearable
            :placeholder="$t('table.softwareName')"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="softwareApproval"
        :show-pager="true"
        :custom-col="true"
        :col-model="colModel"
        :multi-select="false"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <software-category-edit ref="scedit"/>
    <software-category-delete
      ref="scdelete"
      @create="handleCreateCategory"
      @update="handleUpdateCategory"
      @submitted="categoryDelFunc"
    />
    <software-repository-edit
      ref="sredit"
      :is-approval-page="true"
      @change="handleFilter"
      @createCategory="handleCreateCategory"
      @updateCategory="handleUpdateCategory"
      @deleteCategory="handleDeleteCategory"
    />
    <!-- <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="软件上架审批"
      :visible.sync="approvalFormVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="approvalForm" :rules="rules" :model="tempApproval" label-position="right" label-width="100px" style="width: 400px;">
        <FormItem :label="$t('pages.approvalOpinion')">
          <el-radio-group v-model="tempApproval.type" @change="changeType()">
            <el-radio :label="1">{{ $t('pages.accessAccept') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.accessReject') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="tempApproval.type==1" :label="$t('pages.groupType')" prop="usbGroupId">
          <el-select v-model="tempApproval.usbGroupId" class="input-with-button" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in groupTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
          <el-button :title="$t('pages.addType')" class="editBtn" @click="handleGroupCreate({dataId: 0})"><svg-icon icon-class="add" /></el-button>
        </FormItem>
      </Form>
      <p class="tip">{{ $t('pages.usbDevice_text9') }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveApproval">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="approvalFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import SoftwareCategoryEdit from '@/views/system/terminalManage/softwareManager/category/edit'
import SoftwareCategoryDelete from '@/views/system/terminalManage/softwareManager/category/delete'
// import SoftwareCategorySelect from '@/views/system/terminalManage/softwareManager/category/select'
import SoftwareRepositoryEdit from '@/views/system/terminalManage/softwareManager/repository/edit'
import {
  countByCategory,
  supportedPlatforms,
  softwareArchitectures,
  supportedOsVersions,
  uploadSoftwareStatus
} from '@/api/system/terminalManage/softwareRepository'
import {
  getPage
} from '@/api/system/terminalManage/approvalSoftware'
import { deleteCategory } from '@/api/system/terminalManage/softwareCategory'
import { downloadTool } from '@/api/dataEncryption/encryption/fileOutgoing'
import { formatFileSize } from '@/utils'
import { mapGetters } from 'vuex'
import { getStgNamesByCategoryId } from '@/api/system/terminalManage/softwareDownloadStrategy'
import { decodeValToArr } from '@/api/dataEncryption/fileTrace/documentTrack'

export default {
  name: 'SoftwareApproval',
  components: {
    SoftwareCategoryEdit,
    SoftwareCategoryDelete,
    // SoftwareCategorySelect,
    SoftwareRepositoryEdit
  },
  data() {
    const style = 'width: 16px; height: 16px; vertical-align: text-bottom;'
    return {
      showTree: true,
      colModel: [
        { prop: 'name', label: 'name', width: '150', sort: 'custom', fixed: true, formatter: this.nameFormatter },
        { prop: 'terminalName', label: 'terminalName2', width: '150', sort: 'custom', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'userName', label: 'applicant', width: '150', sort: 'custom', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'version', label: 'version', width: '120' },
        { prop: 'size', label: 'size', width: '85', sort: 'custom', formatter: this.fileSizeFormatter },
        { prop: 'mainExe', label: 'mainExe', width: '120', sort: 'custom', custom: true },
        { prop: 'platform', label: 'platform', width: '80', sort: 'custom', formatter: this.platformFormatter },
        { prop: 'architecture', label: 'architecture', width: '80', sort: 'custom', formatter: this.archFormatter },
        { prop: 'minOsVer', label: 'supportSystem', width: '120', sort: 'custom', custom: true, formatter: this.osVerFormatter },
        { prop: 'manufacturer', label: 'softwareVendor', width: '100', sort: 'custom', custom: true },
        { prop: 'copyright', label: 'softwareCopyright', width: '100', sort: 'custom', custom: true },
        { prop: 'description', label: 'softwareDescription', width: '100', sort: 'custom', custom: true },
        { prop: 'releaseNote', label: 'releaseNote', width: '100', custom: true },
        { prop: 'installParam', label: 'installParam', width: '100', sort: 'custom', custom: true },
        { prop: 'upgradeParam', label: 'upgradeParam', width: '100', sort: 'custom', custom: true },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom', custom: true },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
          buttons: [
            { label: 'approval', click: this.handleUpdate }
          ]
        }
      ],
      query: {
        page: 1,
        name: undefined
      },
      movable: false,
      categoryId: undefined,
      categoryErr: undefined,
      deleteFunc: undefined,
      imgStyle: style,
      archMap: {},
      osMap: {},
      softwareArchitectures,
      isApprovalPage: true,
      approvable: false,
      checkedTableData: [],
      multiChecked: false
    }
  },
  computed: {
    ...mapGetters(['softwareCategories'])
  },
  watch: {
    '$store.state.commonData.notice.SoftApproval'(val) {
      if (this.curActiveTab === 'SoftApproval') {
        this.handleFilter()
      } else {
        this.$emit('changeTabName')
        this.$nextTick(() => {
          this.handleRefresh()
        })
      }
      this.$router.push({ query: {}})
    },
    softwareCategories(val, old) {
      const len1 = (val || []).length
      const len2 = (old || []).length
      if (len1 < len2) {
        this.handleFilter()
      }
    }
  },
  created() {
    softwareArchitectures.forEach(arch => {
      this.archMap[arch.value] = arch.label
    })
    supportedOsVersions.forEach(os => {
      this.osMap[os.value] = os.label
    })
  },
  methods: {
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleDownloadTool(file) {
      downloadTool({ toolType: 7 }, file)
    },
    handleClickCategory(data, node) {
      if (data) {
        this.query.category = data.id
      } else {
        this.query.category = undefined
      }
      this.handleFilter()
    },
    handleCreateCategory(callback) {
      this.$refs.scedit.show(null, callback)
    },
    handleUpdateCategory(data) {
      this.$refs.scedit.show(data)
    },
    handleDeleteCategory(data, callback) {
      const id = data.id
      getStgNamesByCategoryId(id).then(respond => {
        if (respond.data && respond.data.length) {
          this.$message({
            message: this.$t('pages.softwareCategoryCannotDeleteTips', { name: data.name, stgs: respond.data.join(', ') }),
            type: 'error',
            duration: 2000
          })
          return
        }
        countByCategory(id).then(res => {
          if (res.data) {
            // alert('当前分组下有已上架的软件，不能删除')
            this.$refs.scdelete.show(id, callback)
            return
          }
          this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
            deleteCategory({ id }).then(() => {
              this.handleDeleteCategorySuccess()
              callback()
            })
          }).catch(() => {})
        })
      })
    },
    categoryDelFunc(data, callback) {
      this.deleteFunc = cancel => {
        if (cancel) {
          callback('cancel')
        } else {
          deleteCategory(data).then(res => {
            this.handleDeleteCategorySuccess()
            this.query.category = undefined
            callback(res)
          }).catch(e => {
            callback(e)
          })
        }
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        this.deleteFunc()
      }).catch(e => { callback(e) })
    },
    handleDeleteCategorySuccess() {
      this.$store.dispatch('commonData/setSoftwareCategories')
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.deleteSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    rowDataApi(option) {
      return getPage(Object.assign(this.query, option))
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.softwareApproval.execRowDataApi(this.query)
    },
    handleRefresh() {
      this.resetQuery()
      this.$refs.softwareApproval.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.sredit.show(null, this.query.category)
    },
    handleUpdate(row) {
      this.$refs.sredit.show(row)
    },
    handleBatchEdit(movable) {
      this.movable = movable
      const table = this.$refs.softwareApproval
      const total = table.getTotal()
      const selectedData = total > 0 ? table.getSelectedDatas() : []
      this.$refs['batchEditDlg'].show(selectedData, total, this.query)
    },
    // 方法在表格多选框改变时执行
    selectionChangeEnd: function(rowDatas) {
      this.approvable = rowDatas && rowDatas.length > 0
      this.checkedTableData = rowDatas
      this.multiChecked = rowDatas.length > 1
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    nameFormatter(row, data) {
      return `<img src="${row.icon}" style="${this.imgStyle} margin-right: 5px;" alt="ico">${data}`
    },
    categoryFormatter(row, data) {
      let category
      for (let i = 0; i < this.softwareCategories.length; i++) {
        category = this.softwareCategories[i]
        if (category.id === data) {
          return `<img src="${category.icon}" style="${this.imgStyle} margin-right: 5px;" alt="ico">${category.name}`
        }
      }
      return this.$t('pages.unknownCategory', { category: data })
    },
    fileSizeFormatter(row, data) {
      return formatFileSize(data)
    },
    platformFormatter(row, data) {
      let platform
      for (let i = 0; i < supportedPlatforms.length; i++) {
        platform = supportedPlatforms[i]
        if (platform.value === data) {
          return platform.label
        }
      }
      return ''
    },
    archFormatter(row, data) {
      const architectures = decodeValToArr(data, 4)
      return architectures.map(arch => this.archMap[arch]).join(', ')
    },
    osVerFormatter(row, data) {
      if (data === '0') {
        return this.$t('pages.unlimitedSystem')
      }
      return data.split('|').map(os => this.osMap[os]).join(', ')
    },
    statusFormatter(row, data) {
      const status = uploadSoftwareStatus[data]
      return !status ? 'Unknown status: ' + data : status
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = undefined
      this.query.version = ''
      this.query.architecture = ''
      this.query.description = ''
    },
    handleApproval() {  // 打开审批页面
      // this.resetTemp()
      // // 初始化设备名称
      // this.initVolueName()
      // this.approvalFormVisible = true
      // this.$nextTick(() => {
      //   // this.$refs['approvalUsbList'].execRowDataApi()
      //   this.$refs['approvalForm'].clearValidate()
      // })
      // this.editVolumeNameBtn = true
      // this.checkedTableData.forEach((item, i) => {
      //   this.usbApprovalObject = {
      //     id: '',
      //     volumeName: '',
      //     status: false
      //   }
      //   this.usbApprovalObject.id = item.id
      //   this.usbApprovalObject.volumeName = item.volumeName
      //   this.tempApproval.tempVolumeName[i] = this.usbApprovalObject
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
  .searchCon {
    .el-input {
      width: 200px;
    }
  }
  >>>.category-item {
    margin-top: 2px;
    .category-select {
      width: 320px;
    }
  }
</style>
