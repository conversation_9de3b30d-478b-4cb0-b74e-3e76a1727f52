<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <software-category-tree
        ref="sctree"
        @click="handleClickCategory"
        @create="handleCreateCategory"
        @update="handleUpdateCategory"
        @delete="handleDeleteCategory"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          <!--移动分类-->
          {{ $t('button.moveCategory') }}
        </el-button>

        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <common-downloader
          :name="$t('pages.softwareTools') + '.exe'"
          :button-name="$t('pages.downloadAuxiliaryTool')"
          button-size="mini"
          @download="handleDownloadTool"
        />
        <!--<el-button v-permission="'371'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>-->
        <!--<el-button v-permission="'372'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>-->
        <div class="searchCon">
          <el-input
            v-model="query.name"
            v-trim
            clearable
            :placeholder="$t('table.softwareName')"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="70px">
              <FormItem :label="$t('table.name')" prop="name">
                <el-input v-model="query.name" v-trim clearable :maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.version')" prop="version">
                <el-input v-model="query.version" v-trim clearable :maxlength="50"/>
              </FormItem>
              <FormItem :label="$t('table.architecture')" prop="architecture">
                <el-select v-model="query.architecture" clearable style="width: 100%">
                  <el-option v-for="(item, index) in softwareArchitectures" v-show="!item.disabled" :key="index" :label="item.label" :value="item.label"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.softwareDescription')" prop="architecture">
                <el-input v-model="query.description" v-trim clearable :maxlength="50"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="softwareRepository"
        :show-pager="true"
        :custom-col="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
      />
    </div>
    <software-category-edit ref="scedit"/>
    <software-category-delete
      ref="scdelete"
      @create="handleCreateCategory"
      @update="handleUpdateCategory"
      @submitted="categoryDelFunc"
    />
    <software-repository-edit
      ref="sredit"
      @change="handleFilter"
      @createCategory="handleCreateCategory"
      @updateCategory="handleUpdateCategory"
      @deleteCategory="handleDeleteCategory"
    />
    <batch-edit-page-dlg
      ref="batchEditDlg"
      :title="movable ? i18nConcatText(this.$t('table.softwareCategory'), 'update') : i18nConcatText(this.$t('pages.software'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="movable" :label="$t('table.softwareCategory')" class="category-item" label-width="70px" :extra-width="{en: 70}" required :error="categoryErr">
        <software-category-select
          v-model="categoryId"
          :deletable="false"
          @change="categoryErr = null"
          @create="handleCreateCategory"
          @update="handleUpdateCategory"
        />
      </FormItem>
    </batch-edit-page-dlg>
    <validate-pass-dlg ref="pwdDlg" @validated="deleteFunc()" @cancel="deleteFunc(true)"/>
  </div>
</template>

<script>
import CommonDownloader from '@/components/DownloadManager/common'
import ValidatePassDlg from '@/views/common/validatePassDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import SoftwareCategoryTree from '@/views/system/terminalManage/softwareManager/category/tree'
import SoftwareCategoryEdit from '@/views/system/terminalManage/softwareManager/category/edit'
import SoftwareCategoryDelete from '@/views/system/terminalManage/softwareManager/category/delete'
import SoftwareCategorySelect from '@/views/system/terminalManage/softwareManager/category/select'
import SoftwareRepositoryEdit from '@/views/system/terminalManage/softwareManager/repository/edit'
import {
  getPage,
  reUploadFtp,
  deleteSoftware,
  countByCategory,
  batchUpdateCategory,
  supportedPlatforms,
  softwareArchitectures,
  supportedOsVersions,
  uploadSoftwareStatus
} from '@/api/system/terminalManage/softwareRepository'
import { deleteCategory } from '@/api/system/terminalManage/softwareCategory'
import { downloadTool } from '@/api/dataEncryption/encryption/fileOutgoing'
import { formatFileSize } from '@/utils'
import { mapGetters } from 'vuex'
import { getStgNamesByCategoryId } from '@/api/system/terminalManage/softwareDownloadStrategy'
import { decodeValToArr } from '@/api/dataEncryption/fileTrace/documentTrack'

export default {
  name: 'SoftwareLib',
  components: {
    CommonDownloader,
    ValidatePassDlg,
    BatchEditPageDlg,
    SoftwareCategoryTree,
    SoftwareCategoryEdit,
    SoftwareCategoryDelete,
    SoftwareCategorySelect,
    SoftwareRepositoryEdit
  },
  data() {
    const style = 'width: 16px; height: 16px; vertical-align: text-bottom;'
    return {
      showTree: true,
      colModel: [
        // { prop: 'icon', label: 'icon', fixedWidth: '50', type: 'img', alt: 'ico', attributes: { style }},
        { prop: 'name', label: 'name', width: '150', sort: 'custom', sortOriginal: true, fixed: true, formatter: this.nameFormatter },
        { prop: 'version', label: 'version', width: '120', fixed: true },
        // { prop: 'originalFilename', label: 'originalFilename', width: '100' },
        { prop: 'category', label: 'category', width: '100', sort: 'custom', formatter: this.categoryFormatter },
        { prop: 'size', label: 'size', width: '85', sort: 'custom', sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'mainExe', label: 'mainExe', width: '120', sort: 'custom', custom: true },
        { prop: 'platform', label: 'platform', width: '80', sort: 'custom', formatter: this.platformFormatter },
        { prop: 'architecture', label: 'architecture', width: '80', sort: 'custom', formatter: this.archFormatter },
        { prop: 'minOsVer', label: 'supportSystem', width: '120', sort: 'custom', custom: true, formatter: this.osVerFormatter },
        { prop: 'manufacturer', label: 'softwareVendor', width: '100', sort: 'custom', custom: true },
        { prop: 'copyright', label: 'softwareCopyright', width: '100', sort: 'custom', custom: true },
        { prop: 'description', label: 'softwareDescription', width: '100', sort: 'custom', custom: true },
        { prop: 'releaseNote', label: 'releaseNote', width: '100', custom: true },
        { prop: 'installParam', label: 'installParam', width: '100', sort: 'custom', custom: true },
        { prop: 'upgradeParam', label: 'upgradeParam', width: '100', sort: 'custom', custom: true },
        { prop: 'operateUser', label: 'operateUser', width: '120', sort: 'custom' },
        { prop: 'uploadStatus', label: 'softwareStatus', width: '100', sort: 'custom', formatter: this.statusFormatter },
        { prop: 'publishTime', label: 'publishTime', width: '155', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom', custom: true },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'reUpload', click: this.handleReUpload, isShow: this.isShowReUploadButton }
          ]
        }
      ],
      query: {
        page: 1,
        name: undefined,
        category: undefined,
        version: '',
        architecture: '',
        description: ''
      },
      movable: false,
      categoryId: undefined,
      categoryErr: undefined,
      deleteFunc: undefined,
      imgStyle: style,
      archMap: {},
      osMap: {},
      softwareArchitectures
    }
  },
  computed: {
    ...mapGetters(['softwareCategories'])
  },
  watch: {
    softwareCategories(val, old) {
      const len1 = (val || []).length
      const len2 = (old || []).length
      if (len1 < len2) {
        this.handleFilter()
      }
    }
  },
  created() {
    this.$socket.subscribe({
      url: '/topic/refreshSoftwarePackageUploadStatus',
      callback: (resp, handle) => {
        this.handleFilter()
      }
    })
    softwareArchitectures.forEach(arch => {
      this.archMap[arch.value] = arch.label
    })
    supportedOsVersions.forEach(os => {
      this.osMap[os.value] = os.label
    })
  },
  methods: {
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleDownloadTool(file) {
      downloadTool({ toolType: 7 }, file)
    },
    handleClickCategory(data, node) {
      if (data) {
        this.query.category = data.id
      } else {
        this.query.category = undefined
      }
      this.handleFilter()
    },
    handleCreateCategory(callback) {
      this.$refs.scedit.show(null, callback)
    },
    handleUpdateCategory(data) {
      this.$refs.scedit.show(data)
    },
    handleDeleteCategory(data, callback) {
      const id = data.id
      getStgNamesByCategoryId(id).then(respond => {
        if (respond.data && respond.data.length) {
          this.$message({
            message: this.$t('pages.softwareCategoryCannotDeleteTips', { name: data.name, stgs: respond.data.join(', ') }),
            type: 'error',
            duration: 2000
          })
          return
        }
        countByCategory(id).then(res => {
          if (res.data) {
            // alert('当前分组下有已上架的软件，不能删除')
            this.$refs.scdelete.show(id, callback)
            return
          }
          this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
            deleteCategory({ id }).then(() => {
              this.handleDeleteCategorySuccess()
              callback()
            })
          }).catch(() => {})
        })
      })
    },
    categoryDelFunc(data, callback) {
      this.deleteFunc = cancel => {
        if (cancel) {
          callback('cancel')
        } else {
          deleteCategory(data).then(res => {
            this.handleDeleteCategorySuccess()
            this.query.category = undefined
            callback(res)
          }).catch(e => {
            callback(e)
          })
        }
      }
      // this.$refs.pwdDlg.show()
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        this.deleteFunc()
      }).catch(e => { callback(e) })
    },
    handleDeleteCategorySuccess() {
      this.$store.dispatch('commonData/setSoftwareCategories')
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.deleteSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    handleMoving() {
      this.categoryId = undefined
      this.categoryErr = undefined
      this.handleBatchEdit(true)
    },
    batchEditFunc(data, callback) {
      const handlePromise = promise => promise.then(res => {
        this.handleFilter()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(res)
      }).catch(e => { callback(e) })

      if (!this.movable) {
        this.deleteFunc = cancel => {
          if (cancel) {
            callback('cancel')
          } else {
            handlePromise(deleteSoftware(data))
          }
        }
        // this.$refs.pwdDlg.show()
        this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
          this.deleteFunc()
        }).catch(e => { callback(e) })
        return
      }
      if (this.movable && !this.categoryId) {
        this.categoryErr = this.$t('pages.pleaseSelectContent', { content: this.$t('table.softwareCategory') })
        callback('cancel')
        return
      }
      handlePromise(batchUpdateCategory(this.categoryId, data))
    },
    rowDataApi(option) {
      return getPage(Object.assign(this.query, option))
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.softwareRepository.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.sredit.show(null, this.query.category)
    },
    handleUpdate(row) {
      this.$refs.sredit.show(row)
    },
    handleDelete() {
      this.handleBatchEdit(false)
    },
    handleReUpload(row) {
      row.uploadStatus = 1
      return reUploadFtp(row).finally(() => this.handleFilter())
    },
    isShowReUploadButton(row) {
      return row.uploadStatus > 1000
    },
    handleBatchEdit(movable) {
      this.movable = movable
      const table = this.$refs.softwareRepository
      const total = table.getTotal()
      const selectedData = total > 0 ? table.getSelectedDatas() : []
      this.$refs['batchEditDlg'].show(selectedData, total, this.query)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    nameFormatter(row, data) {
      return `<img src="${row.icon}" style="${this.imgStyle} margin-right: 5px;" alt="ico">${data}`
    },
    categoryFormatter(row, data) {
      let category
      for (let i = 0; i < this.softwareCategories.length; i++) {
        category = this.softwareCategories[i]
        if (category.id === data) {
          return `<img src="${category.icon}" style="${this.imgStyle} margin-right: 5px;" alt="ico">${category.name}`
        }
      }
      return this.$t('pages.unknownCategory', { category: data })
    },
    fileSizeFormatter(row, data) {
      return formatFileSize(data)
    },
    platformFormatter(row, data) {
      let platform
      for (let i = 0; i < supportedPlatforms.length; i++) {
        platform = supportedPlatforms[i]
        if (platform.value === data) {
          return platform.label
        }
      }
      return ''
    },
    archFormatter(row, data) {
      const architectures = decodeValToArr(data, 4)
      return architectures.map(arch => this.archMap[arch]).join(', ')
    },
    osVerFormatter(row, data) {
      if (data === '0') {
        return this.$t('pages.unlimitedSystem')
      }
      return data.split('|').map(os => this.osMap[os]).join(', ')
    },
    statusFormatter(row, data) {
      const status = uploadSoftwareStatus[data]
      return !status ? 'Unknown status: ' + data : status
    },
    resetQuery() {
      this.query.page = 1
      this.query.name = undefined
      this.query.version = ''
      this.query.architecture = ''
      this.query.description = ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .searchCon {
    .el-input {
      width: 200px;
    }
  }
  >>>.category-item {
    margin-top: 2px;
    .category-select {
      width: 320px;
    }
  }
</style>
