<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.importSoftware')"
      :visible.sync="visible"
      width="850px"
    >
      <el-container>
        <el-aside width="210px">
          <software-category-tree
            ref="tree"
            :checked-keys="checkedKeys"
            :editable="editable"
            :multiple="addAppTypeAble"
            @click="handleClickCategory"
            @create="handleCreateCategory"
            @update="handleUpdateCategory"
            @delete="handleDeleteCategory"
            @categoryChange="categoryChange"
            @checkChange="checkChange"
          />
        </el-aside>
        <el-main>
          <div class="toolbar">
            <el-button v-show="editable" icon="el-icon-plus" size="mini" @click="handleCreate">
              {{ $t('button.add') }}
            </el-button>
            <el-button v-show="editable" icon="el-icon-delete" size="mini" @click="handleDelete">
              {{ $t('button.delete') }}
            </el-button>
            <div style="float: right;">
              <el-input
                v-model="query.name"
                style="width: 225px;"
                clearable
                :placeholder="$t('table.softwareName')"
                @keyup.enter.native="handleFilter"
              />
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </div>
          <grid-table
            ref="softTable"
            :height="400"
            :col-model="colModel"
            :row-data-api="rowDataApi"
            is-saved-selected
            saved-selected-prop="name"
            :after-load="afterLoad"
            :page-sizes="[ 20, 50, 100, 500, 1000 ]"
            pager-small
            @selectionChangeEnd="softSelectionChangeEnd"
          />
        </el-main>
      </el-container>

      <div slot="footer" class="dialog-footer">
        <el-tooltip :content="$t('pages.categoryConstrainTips')" effect="dark" placement="top">
          <el-button v-if="addAppTypeAble" type="primary" :loading="submitting" @click="handleSelectCategories">
            {{ $t('pages.addSelectedCategory') }}
          </el-button>
        </el-tooltip>
        <el-button type="primary" :loading="submitting" @click="handleSelectSoftware">
          {{ $t('pages.addSelectedSoftware') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <software-category-edit ref="scedit"/>
    <software-category-delete
      ref="scdelete"
      @create="handleCreateCategory"
      @update="handleUpdateCategory"
      @submitted="categoryDelFunc"
    />
    <software-repository-edit
      ref="sredit"
      @change="handleFilter"
      @createCategory="handleCreateCategory"
      @updateCategory="handleUpdateCategory"
      @deleteCategory="handleDeleteCategory"
    />
    <batch-edit-page-dlg
      ref="batchEditDlg"
      :title="i18nConcatText(this.$t('pages.software'), 'delete')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    />
  </div>
</template>

<script>
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import SoftwareCategoryTree from '@/views/system/terminalManage/softwareManager/category/tree'
import SoftwareCategoryEdit from '@/views/system/terminalManage/softwareManager/category/edit'
import SoftwareCategoryDelete from '@/views/system/terminalManage/softwareManager/category/delete'
import SoftwareRepositoryEdit from '@/views/system/terminalManage/softwareManager/repository/edit'
import { mapGetters } from 'vuex'
import { formatFileSize } from '@/utils'
import {
  countByCategory,
  deleteSoftware,
  getPage,
  listByCategories,
  supportedPlatforms
} from '@/api/system/terminalManage/softwareRepository'
import { getStgNamesByCategoryId } from '@/api/system/terminalManage/softwareDownloadStrategy'
import { deleteCategory } from '@/api/system/terminalManage/softwareCategory'

export default {
  name: 'SoftwareRepositorySelect',
  components: {
    BatchEditPageDlg,
    SoftwareCategoryTree,
    SoftwareCategoryEdit,
    SoftwareCategoryDelete,
    SoftwareRepositoryEdit
  },
  props: {
    appendToBody: {
      type: Boolean,
      default: false
    },
    //  是否支持选择分类
    addAppTypeAble: {
      type: Boolean,
      default: true
    },
    //  是否支持单个导入（addAppTypeAble = false时可生效）
    singleImport: {
      type: Boolean,
      default: false
    },
    //  是否支持修改
    editable: {
      type: Boolean,
      default: true
    },
    // 根据上传状态查询对应软件，默认查所有
    uploadStatus: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      visible: false,
      checkedKeys: [],
      submitting: false,
      colModel: [
        { prop: 'name', label: 'name', width: '100', sort: 'custom' },
        { prop: 'category', label: 'category', fixedWidth: '100', sort: 'custom', formatter: this.categoryFormatter },
        { prop: 'version', label: 'version', fixedWidth: '120' },
        { prop: 'size', label: 'size', fixedWidth: '85', sort: 'custom', formatter: this.fileSizeFormatter },
        { prop: 'platform', label: 'platform', fixedWidth: '80', sort: 'custom', formatter: this.platformFormatter },
        { prop: 'remark', label: 'remark', width: '120', sort: 'custom' }
      ],
      query: {
        page: 1,
        name: undefined,
        category: undefined,
        uploadStatus: undefined
      },
      checkedRowKeys: [],
      checkedPageRowKeys: [],
      copyCheckedRowKeys: [],
      removeCheckedRowKeys: [],
      tempRowDatas: [],
      categoryIds: []
    }
  },
  computed: {
    ...mapGetters(['softwareCategories'])
  },
  activated() {
    if (this.visible) {
      this.handleFilter()
    }
  },
  created() {
    if (this.uploadStatus == 2) {
      this.$socket.subscribe({
        url: '/topic/refreshSoftwarePackageUploadStatus',
        callback: (resp, handle) => {
          this.handleFilter()
        }
      })
    }
  },
  methods: {
    softGridTable() {
      return this.$refs['softTable']
    },
    groupTree() {
      return this.$refs['tree']
    },
    show(typeIds, softIds) {
      this.visible = true
      this.checkedKeys = []
      this.query.page = 1
      this.query.category = undefined
      this.resetRowKeys()
      this.$nextTick(() => {
        // this.softGridTable().clearSelection()
        this.softGridTable().clearSaveSelection()
        this.groupTree().clearSelectedNodes()
        this.softGridTable().execRowDataApi(this.query)
        if (this.addAppTypeAble) {
          if (typeIds) {
            this.setCategoryIds(typeIds)
            this.groupTree().setCheckedKeys(typeIds)
          }
          if (softIds) {
            this.setCheckedRowKeys(softIds)
            // 这里不需要勾选，表格勾选数据在afterLoad方法，即表格数据加载后执行
            // this.softGridTable().checkSelectedRows(softIds)
          }
        }
      })
    },
    checkChange(nodeIds) {
      this.categoryIds = [...nodeIds]
    },
    categoryChange(data) {
      if (this.addAppTypeAble) {
        this.$nextTick(() => {
          this.groupTree().setCheckedKeys(this.categoryIds)
        })
      }
    },
    getRemoveApp(rowDatas) {
      // 整个表格勾选的数据的 map
      const rowDataIdsMap = rowDatas.reduce((map, item) => { map[item.id] = item.id; return map; }, {})
      // 策略选中的程序信息的 map
      const copyCheckedRowKeysMap = this.copyCheckedRowKeys.reduce((map, item) => { map[item] = item; return map; }, {})
      // tempRowDats 存储了表格上一次勾选状态变更时所勾选的数据, unCheckIds 为 被取消勾选 且 存在于策略里 的数据的 id
      const unCheckIds = this.tempRowDatas.filter(item => !rowDataIdsMap[item.id] && !!copyCheckedRowKeysMap[item.id]).map(item => item.id)
      // 合并、去重
      this.removeCheckedRowKeys = Array.from(new Set(this.removeCheckedRowKeys.concat(unCheckIds)))
    },
    removeStrategyApp(rowDatas) {
      const checkedPageRowKeysSet = new Set(this.checkedPageRowKeys)
      // 用来存储当前页还有哪些策略程序被勾选
      const checkedIdsSet = new Set(
        rowDatas.filter(item => checkedPageRowKeysSet.has(item.id)).map(item => item.id)
      );

      // 收集取消勾选的数据
      const toRemove = new Set(
        this.checkedPageRowKeys.filter(item => !checkedIdsSet.has(item))
      );

      // 从checkedRowKeys数组里移除取消勾选的数据
      this.checkedRowKeys = this.checkedRowKeys.filter(data => !toRemove.has(data));
    },
    softSelectionChangeEnd(rowDatas) {
      if (this.addAppTypeAble) {
        // 获取被移除的程序（需要获取原先策略里存在的程序，哪些被取消勾选了）
        this.getRemoveApp(rowDatas)
        this.tempRowDatas = [...rowDatas]
        // 策略里的程序被移除时，更新存储策略程序的数组
        this.removeStrategyApp(rowDatas)
      }
    },
    afterLoad() {
      if (this.addAppTypeAble) {
        const checkedRowKeysSet = new Set(this.checkedRowKeys)
        // 用于存储当前页需要进行勾选的行数据
        const checkPageDataIdsSet = new Set()
        // 获取表格当前页，在策略里存在的数据
        this.softGridTable().getDatas()
          .filter(data => checkedRowKeysSet.has(data.id))
          .forEach(data => {
            checkPageDataIdsSet.add(data.id)
          })
        // 记录表格当前页，在策略里存在的数据
        this.checkedPageRowKeys = Array.from(checkPageDataIdsSet)
        // 获取整个表格勾选的数据
        this.softGridTable().getSelectedDatas().forEach(item => {
          checkPageDataIdsSet.add(item.id)
        })
        this.$nextTick(() => {
          const checkPageDataIds = Array.from(checkPageDataIdsSet)
          if (checkPageDataIds.length > 0) {
            // 在表格当前页，将选中的数据勾选上
            this.softGridTable().checkSelectedRows(checkPageDataIds)
          }
        })
      }
    },
    resetRowKeys() {
      this.checkedRowKeys = []
      this.checkedPageRowKeys = []
      this.copyCheckedRowKeys = []
      this.removeCheckedRowKeys = []
      this.tempRowDatas = []
      this.categoryIds = []
    },
    setCheckedRowKeys(data) {
      this.checkedRowKeys = [...data]
      this.copyCheckedRowKeys = [...data]
    },
    setCategoryIds(data) {
      this.categoryIds = [...data]
    },
    getRemoveCheckedRowKeys() {
      return this.removeCheckedRowKeys
    },
    handleClickCategory(data, node) {
      if (data) {
        this.query.category = data.id
      } else {
        this.query.category = undefined
      }
      this.handleFilter()
    },
    handleCreateCategory(callback) {
      this.$refs.scedit.show(null, callback)
    },
    handleUpdateCategory(data) {
      this.$refs.scedit.show(data)
    },
    handleDeleteCategory(data, callback) {
      const id = data.id
      getStgNamesByCategoryId(id).then(respond => {
        if (respond.data && respond.data.length) {
          this.$message({
            message: this.$t('pages.softwareCategoryCannotDeleteTips', { name: data.name, stgs: respond.data.join(', ') }),
            type: 'error',
            duration: 2000
          })
          return
        }
        countByCategory(id).then(res => {
          if (res.data) {
            // alert('当前分组下有已上架的软件，不能删除')
            this.$refs.scdelete.show(id, callback)
            return
          }
          this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
            deleteCategory({ id }).then(() => {
              this.handleDeleteCategorySuccess()
              callback()
            })
          }).catch(() => {})
        })
      })
    },
    categoryDelFunc(data, callback) {
      this.deleteFunc = cancel => {
        if (cancel) {
          callback('cancel')
        } else {
          deleteCategory(data).then(res => {
            this.handleDeleteCategorySuccess()
            this.query.category = undefined
            this.handleFilter()
            callback(res)
          }).catch(e => {
            callback(e)
          })
        }
      }
      // this.$refs.pwdDlg.show()
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        this.deleteFunc()
      }).catch(e => { callback(e) })
    },
    handleDeleteCategorySuccess() {
      this.$store.dispatch('commonData/setSoftwareCategories')
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.deleteSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    handleCreate() {
      this.$refs.sredit.show(null, this.query.category)
    },
    handleDelete() {
      const table = this.$refs.softTable
      const total = table.getTotal()
      const selectedData = total > 0 ? table.getSelectedDatas() : []
      this.$refs['batchEditDlg'].show(selectedData, total, this.query)
    },
    batchEditFunc(data, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt'))
        .then(() => deleteSoftware(data))
        .then(res => {
          this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(res)
        }).catch(e => { callback(e) })
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.softTable && this.$refs.softTable.execRowDataApi(this.query)
    },
    rowDataApi(option) {
      if (this.uploadStatus) {
        this.query.uploadStatus = this.uploadStatus
      }
      return getPage(Object.assign(this.query, option))
    },
    categoryFormatter(row, data) {
      let category
      for (let i = 0; i < this.softwareCategories.length; i++) {
        category = this.softwareCategories[i]
        if (category.id === data) {
          return category.name
        }
      }
      return this.$t('pages.unknownCategory', { category: data })
    },
    fileSizeFormatter(row, data) {
      return formatFileSize(data)
    },
    platformFormatter(row, data) {
      let platform
      for (let i = 0; i < supportedPlatforms.length; i++) {
        platform = supportedPlatforms[i]
        if (platform.value === data) {
          return platform.label
        }
      }
      return ''
    },
    handleSelectCategories() {
      const selectedCategories = this.$refs['tree'].getSelectedCategories().map(item => item.id)
      if (selectedCategories.length > 0) {
        this.visible = false
        this.$emit('select', selectedCategories, 1)
      } else {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.addSelectedCategoryTips'),
          type: 'error',
          duration: 2000
        })
      }
    },
    handleSelectSoftware() {
      const selectedSoftware = this.$refs['softTable'].getSelectedDatas()
      if (selectedSoftware.length > 0) {
        if (!this.addAppTypeAble && this.singleImport && selectedSoftware.length > 1) {
          this.$message({
            title: this.$t('text.prompt'),
            message: '只能选中一个软件',
            type: 'error',
            duration: 2000
          })
          return;
        }
        if (this.addAppTypeAble) {
          const selectRows = this.softGridTable().getSelectedDatas()
          const selectIdSet = new Set(selectRows.map(item => item.id))
          this.removeCheckedRowKeys = this.removeCheckedRowKeys.filter(item =>
            !selectIdSet.has(item)
          )
        }
        this.visible = false
        this.$emit('select', selectedSoftware, 0)
      } else {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.addSelectedSoftwareTips'),
          type: 'error',
          duration: 2000
        })
      }
    },
    handleSelect() {
      const selectedSoftware = this.$refs['softTable'].getSelectedDatas()
      const selectedCategories = this.$refs['tree'].getSelectedCategories().map(item => item.id)
      if (selectedCategories.length > 0) {
        listByCategories(selectedCategories).then(res => {
          if (res.data.length > 0) {
            const selectedSoftwareIdsSet = new Set(selectedSoftware.map(item => item.id))
            res.data.forEach(item => {
              !selectedSoftwareIdsSet.has(item.id) && selectedSoftware.push(item)
            })
          }
          this.visible = false
          this.$emit('select', selectedSoftware)
        })
      } else {
        this.visible = false
        this.$emit('select', selectedSoftware)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-aside {
    max-height: 440px;
  }
</style>
