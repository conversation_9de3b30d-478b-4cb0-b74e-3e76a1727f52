<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('route.softwareRepository')" name="SoftLib">
        <Soft-Lib
          ref="SoftLib"
        />
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('route.softwareApproval')" name="SoftApproval">
        <Soft-Approval
          ref="SoftApproval"
          @changeTabName="activeName = 'SoftApproval'"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SoftLib from './softwareLib'
import SoftApproval from './softwareApproval'

export default {
  name: 'SoftwareRepository',
  components: {
    SoftLib, SoftApproval
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.activeName = to.query.tabName || vm.activeName
      vm.$router.push({ query: {}})
    })
  },
  data() {
    return {
      activeName: 'SoftLib'
    }
  },
  created() {
  },
  methods: {
    tabClick(pane, event) {
      if (this.activeName === 'SoftLib') {
        // this.$refs['SoftLib'].checkedUsbGroup()
      } else {
        // this.$refs.UsbLib.loadReceiverTree()
        // this.$refs.UsbLib.handleRefresh()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .searchCon {
    .el-input {
      width: 200px;
    }
  }
  >>>.category-item {
    margin-top: 2px;
    .category-select {
      width: 320px;
    }
  }
</style>
