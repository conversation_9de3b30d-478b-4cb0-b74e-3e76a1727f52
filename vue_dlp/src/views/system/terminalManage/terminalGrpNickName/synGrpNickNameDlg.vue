<template>
  <div>
    <!-- 同步分组昵称 -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.syncGroupNickName')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="synDlgVisible"
      width="800px"
    >
      <div class="searchCon">
        <Form >
          <el-row>
            <el-col :span="24">
              <FormItem :label="$t('pages.terminal_text19')">
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.matchDepartemntTips">
                      <br slot="br" />
                      <template slot="space">&nbsp;</template>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-select v-model="requestVO.deptRule" style="width: 115px;" @change="changeDeptRule($event)">
                  <el-option :value="1" :label="$t('pages.terminal_text21')"></el-option>
                  <el-option :value="2" :label="$t('pages.terminal_text22')"></el-option>
                </el-select>
              </FormItem>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="14">
              <FormItem >
                <label class="el-form-item__label" style="margin-right: 10px">{{ $t('pages.synchronousOption') }}</label>
                <el-checkbox v-model="defaultTemp.synchronizationTerminalChk">{{ $t('pages.synchronousTermGroup') }}</el-checkbox>
                <el-checkbox v-model="defaultTemp.synchronizationNicNameChk">{{ $t('pages.synchronousTermName') }}</el-checkbox>
              </FormItem>
            </el-col>

            <el-col :span="10">
              <div style="margin-top: 25px">
                <el-button type="primary" icon="el-icon-search" size="mini" style="float: right;margin-top: 2px;margin-left: 0;" @click="handleRefresh">
                  {{ $t('table.search') }}
                </el-button>
                <FormItem label-width="0px" style="float: right">
                  <tree-select ref="groupTree" :data="treeData" node-key="dataId" is-filter :placeholder="$t('pages.pleaseSelectContent', { content: $t('table.currentDept') })" clearable :width="290"/>
                </FormItem>
              </div>
            </el-col>
          </el-row>
        </Form>
        <el-row>
          <el-checkbox v-model="multiSelect">{{ $t('pages.terminal_text26') }}</el-checkbox>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.selectTerminalSyncDataTips">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-row>
      </div>

      <grid-table
        ref="synGrpNickNameTable"
        :col-model="synGrpNickNameColModel"
        :row-data-api="rowDataApiSyn"
        row-key="termId"
        :height="350"
        :width="800"
        :show-pager="true"
        :multi-select="multiSelect"
        :is-saved-selected="multiSelect"
        pager-small
        @selectionChangeEnd="handleSelectionChange"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="startSynchronizationHandle">
          {{ $t('pages.startSync') }}
        </el-button>
        <el-button @click="synDlgVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>

    <!--调整终端分组和名称中表格的修改弹框-->
    <el-dialog
      v-el-drag-dialog
      :title="$t('text.edit')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="updateTabList"
      width="300px"
    >
      <div>
        <Form>
          <FormItem :label="$t('pages.syncToNewGroup')" class="required" prop="typeId">
            <tree-select
              :data="groupTreeSelectData"
              node-key="dataId"
              :height="350"
              :width="275"
              :checked-keys="groupDefault"
              is-filter
              @change="groupIdSelectChange"
            />
          </FormItem>
          <FormItem :label="$t('pages.terminalName')" class="required">
            <el-input v-model="updateVO.newNickName" v-trim :maxlength="60" :placeholder="$t('pages.terminalName')"></el-input>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateGroupNickNameBtn">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="updateTabList = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量调整终端分组-->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.batchChangeGroups')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="batchDlgVisible"
      width="400px"
    >
      <Form>
        <FormItem >
          <span style="margin: 0px 5px 10px;">{{ $t('pages.batch_text1', { num: 0 }) }}</span>
        </FormItem>
        <FormItem :label="$t('table.terminalGroup')" label-width="70px" class="required">
          <tree-select
            :data="groupTreeSelectData"
            node-key="dataId"
            :height="350"
            :width="275"
            :checked-keys="groupDefault"
            is-filter
            @change="groupIdSelectChange"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateGroup()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="batchDlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { findNode } from '@/utils/tree'
import { getTermGroupNickNameToSncPage, updateGroupNickName, synGroupNickName } from '@/api/system/terminalManage/terminalGroupNick';

export default {
  name: 'SynGrpNickNameDlg',
  props: {
  },
  data() {
    return {
      batchDlgVisible: false, // 批量修改分组
      synDlgVisible: false, // 弹窗是否显示
      updateTabList: false, // 调整终端分组和名称表格修改
      editAble: false,
      multiSelect: false, // 列表多选 false:代表全选
      defaultTemp: { // 表单字段
        synchronizationTerminalChk: true,
        synchronizationNicNameChk: true,
        isAllSynTerminalChk: false
      },
      // 部门
      groupTreeSelectData: [],
      treeData: [],
      // 列表
      synGrpNickNameColModel: [
        { prop: 'name', label: 'terminalName', width: '110', fixed: true, sort: 'custom' },
        { prop: 'termId', label: 'terminalCode', width: '110', sort: 'custom' },
        { prop: 'oldGroupIds', label: 'currentDept', width: '110', formatter: this.groupFormatter },
        { prop: 'groupName', label: 'collectionDept', width: '110' },
        { prop: 'nickName', label: 'collectionName', width: '120', sort: 'custom' },
        { prop: 'newGroupId', label: 'grpToBeSync', width: '110', formatter: this.groupFormatter },
        { prop: 'newNickName', label: 'nameToBeSync', width: '110', sort: true, sortOriginal: true },
        { prop: '', label: 'operate', type: 'button', fixed: 'right', width: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdateGrpNickName }
          ]
        }
      ],
      grpNickNameListTotal: undefined, // 收集终端信息列表记录总数
      // 功能
      groupDefault: [], // 修改功能分组默认值
      requestVO: { // 操作功能参数 例如：添加终端、开始收集、重新收集、开始同步
        termList: [],
        groupId: undefined,
        synGrp: undefined,
        synNickName: undefined,
        status: 3,
        newGroupId: undefined,
        newGroupName: undefined,
        newNickName: undefined,
        deptRule: 2
      },
      updateVO: {
        termList: [],
        groupList: [],
        groupIds: '',
        termId: undefined,
        groupId: undefined,
        synGrp: undefined,
        synNickName: undefined,
        status: undefined,
        newGroupId: undefined,
        newGroupName: undefined,
        newNickName: undefined,
        deptRule: 2
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['synGrpNickNameTable']
    }
  },
  watch: {
    '$store.state.commonData.deptTree'() {
      this.initGroupTreeNode()
    },
    'multiSelect'() {
      this.gridTable.selectedDatasDelete()
    }
  },
  created() {
    this.initGroupTreeNode()
  },
  activated() {
  },
  mounted() {
  },
  methods: {
    // 打开终端分组和名称规范
    show(dataIds) {
      if (dataIds) {
        this.requestVO.termList = dataIds
      }
      this.synDlgVisible = true
      this.$nextTick(() => {
        this.handleRefresh()
      })
    },
    // 部门
    initGroupTreeNode: function() {
      const treeNode = JSON.parse(JSON.stringify(this.$store.getters.deptTree))
      // 分组，不允许选择公司根节点
      if (Array.isArray(treeNode) && treeNode.length === 1 && treeNode[0].dataId == 0) {
        this.groupTreeSelectData = treeNode[0].children
      } else {
        this.groupTreeSelectData = treeNode
      }
      this.treeData = treeNode
    },
    /**
     * 列表
     * 终端分组和名称规范列表数据的获取
     * @returns {AxiosPromise}
     */
    rowDataApiSyn: function(option) {
      const searchQuery = Object.assign({}, this.requestVO, option)
      const promise = getTermGroupNickNameToSncPage(searchQuery)
      promise.then(res => {
        this.grpNickNameListTotal = res.data.total
      })
      return promise;
    },
    handleSelectionChange(val) {
      this.editAble = val.length > 0
    },
    groupFormatter: function(row, data) {
      const groupNames = []
      if (data || data === 0) {
        const that = this
        if (typeof data === 'object') {
          data.forEach(function(groupId) {
            const node = findNode(that.treeData, groupId, 'dataId')
            if (node) groupNames.push(node.label)
          })
        }
        if (typeof data === 'number') {
          const node = findNode(that.treeData, data, 'dataId')
          if (node) groupNames.push(node.label)
        }
      }
      return groupNames.join(',')
    },
    handleRefresh() {
      const searchGroup = this.$refs['groupTree'].getSelectedNode()
      console.log('searchGroup', searchGroup)
      if (searchGroup && searchGroup.length > 0) {
        this.requestVO.groupId = searchGroup[0].dataId
        // 如果按分组查询，那么就不按终端查询
        this.requestVO.termList = []
      } else {
        this.requestVO.groupId = undefined
      }
      this.gridTable.execRowDataApi(this.requestVO)
    },
    // 修改功能
    // 调整终端分组和名称 修改弹框数据回显
    handleUpdateGrpNickName(row) {
      this.updateTabList = true
      this.updateVO.termId = row.termId
      this.updateVO.newGroupId = row.newGroupId
      this.updateVO.newGroupName = row.newGroupName
      this.updateVO.newNickName = row.newNickName
      this.groupDefault = [row.newGroupId, row.newGroupName]
    },
    // 分组改变时，修改部门的id
    groupIdSelectChange(data) {
      this.updateVO.newGroupId = data
    },
    updateGroup() {

    },
    // 保存修改
    updateGroupNickNameBtn() {
      const that = this
      const vo = Object.assign({}, this.updateVO)
      if (!this.updateVO.newGroupId) {
        this.$message({
          message: this.$t('pages.grpNickName_text10'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.updateVO.newNickName === '') {
        this.$message({
          message: this.$t('pages.grpNickName_text11'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      updateGroupNickName(vo).then(respond => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.editSuccess'),
          type: 'success',
          duration: 2000
        })
        that.updateTabList = false
        that.handleRefresh()
      })
    },
    // 部门匹配规则改变
    changeDeptRule: function(data) {
      this.requestVO.deptRule = data
      const vo = Object.assign({}, this.requestVO)
      this.gridTable.execRowDataApi(vo)
    },
    startSynchronizationHandle() {
      // 开始同步
      if (!this.defaultTemp.synchronizationTerminalChk && !this.defaultTemp.synchronizationNicNameChk) {
        this.$message({
          message: this.$t('pages.selectAtLeastOneSynOption'),
          type: 'error',
          duration: 2000
        })
        return false
      } else {
        this.requestVO.synGrp = this.defaultTemp.synchronizationTerminalChk
        this.requestVO.synNickName = this.defaultTemp.synchronizationNicNameChk
        const selectIds = this.$refs['synGrpNickNameTable'].getSelectedKeys()
        if (this.multiSelect && selectIds.length === 0) {
          this.$message({
            message: this.$t('pages.terminal_text1'),
            type: 'error',
            duration: 2000
          })
          return false
        }
        if (this.multiSelect) {
          this.requestVO.termList = selectIds.length > 0 ? selectIds : Array.of(0)
        } else {
          // 当有选中外部列表终端时，只同步外部列表的选中终端
          // this.requestVO.termList.splice(0)
        }
        console.log('this.requestVO.termList', this.requestVO.termList)

        // 清除勾选数据
        this.$refs['synGrpNickNameTable'].selectedDatasDelete()
        const vo = Object.assign({}, this.requestVO)
        synGroupNickName(vo).then(respond => {
          const { success, fail } = respond.data
          let msg = `${this.$t('pages.grpNickName_text7')}(${success}), ${this.$t('pages.grpNickName_text8')}(${fail})`
          if (fail > 0) {
            msg = msg.concat(` ${this.$t('pages.grpNickName_text12')}`)
          }
          this.$notify({
            title: this.$t('text.success'),
            message: msg,
            type: 'success',
            duration: 2000
          })
          this.requestVO.termList = []
          this.handleRefresh()
          this.$emit('submitEnd')
        })
      }
    }
  }

}
</script>
