<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :os-type-filter="8" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange" />
    </div>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <!-- 暂不支持 因策略直接应用到终端-->
        <!-- <strategy-extend :stg-type="222"/>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <wps-setup-dlg ref="stgDlg" :active-able="treeable" :formable="formable" :entity-node="checkedEntityNode" @submitEnd="submitEnd"></wps-setup-dlg>
  </div>
</template>

<script>
import { getStrategyPage, getStrategyByName, deleteStrategy } from '@/api/system/terminalManage/mobileWPSConfigStrategy'
import {
  enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter,
  entityLink, refreshPage, buttonFormatter
} from '@/utils'
import { stgActiveIconFormatter } from '@/utils/formatter'
import axios from 'axios'
import WpsSetupDlg from './editDlg'

export default {
  name: 'WpsSetup',
  components: { WpsSetupDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'stgMessage', width: '200', formatter: this.msgFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'operate', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      appEditable: false,
      appDeleteable: false,
      selectedNodeData: {},
      noTargetTree: {},
      isDisabled: 0,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        appNameList: [], // 指定打开app列表（只支持安卓） appName app名称 appPackageName app包名 fileSuffix 指定app打开文件后缀
        entityType: undefined,
        entityId: undefined
      },
      appTemp: {},
      defaultAppTemp: {
        id: undefined,
        appName: '',
        appPackageName: '',
        fileSuffix: []
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('route.appOpenSuffixStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('route.appOpenSuffixStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      appTempprules: {
        appName: [
          { required: true, message: this.$t('pages.process_Msg10'), trigger: 'blur' }
        ],
        appPackageName: [
          { required: true, message: this.$t('pages.process_Msg10'), trigger: 'blur' }
        ],
        fileSuffix: [
          { required: true, message: this.$t('pages.fileSuffix_text4'), trigger: 'blur' }
        ]
      },
      multipleSelection: [], // 选中行数组集合
      submitting: false,
      useWPSOptions: {
        0: this.$t('pages.useWPS_label1'),
        1: this.$t('pages.useWPS_label2'),
        2: this.$t('pages.useWPS_label3'),
        3: this.$t('pages.useWPS_label4'),
        4: this.$t('pages.useWPS_label5')
      },
      wpsOpenTypeOptions: [
        { type: 1, desc: 'DOC' },
        { type: 2, desc: 'DOCX' },
        { type: 4, desc: 'XLS' },
        { type: 8, desc: 'XLSX' },
        { type: 16, desc: 'PPT' },
        { type: 32, desc: 'PPTX' },
        { type: 64, desc: 'WPS' },
        { type: 128, desc: 'ET' },
        { type: 256, desc: 'DPS' },
        { type: 512, desc: 'PDF' },
        { type: 1024, desc: 'TXT' }
      ],
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    processTable() {
      return this.$refs['processList']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    appSelectionChange(rowDatas) {
      this.appDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.appEditable = true
      } else {
        this.appEditable = false
        this.cancelApp()
      }
    },
    appTable() {
      return this.$refs['appList']
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    msgFormatter: function(row, data) {
      if (row.useWPS == 0) {
        return this.$t('pages.useWPS_label6')
      }
      const msg = []
      msg.push(this.useWPSOptions[row.useWPS])
      if (row.useWPS !== 0) {
        let value = this.$t('pages.WPSType')
        if (!data.wpsOpenType) {
          value = this.$t('pages.notLimit') + value
        } else {
          value = value + this.$t('pages.limit') + this.$t('pages.colon')
          const wpsOpenTypes = []
          this.wpsOpenTypeOptions.forEach(option => {
            if (data.wpsOpenType & option.type) {
              wpsOpenTypes.push(option.desc)
            }
          })
          value = value + wpsOpenTypes.join(',')
        }
        msg.push(value)
      }
      return msg.join(this.$t('pages.comma'))
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    }
  }
}
</script>
<style lang="scss" scoped>
>>>.button-new-tag {
  margin-left: 10px;
  padding: 5px;
}
>>>.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
