<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('pages.mobileWPSConfig')"
    :stg-code="266"
    os-label-w="125px"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :get-by-name="getStrategyByName"
    :format-row-data="formatRowData"
    :format-form-data="formatFormData"
    :validate-form="validateFormData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
  >
    <template :slot="slotName">
      <FormItem :label="$t('pages.useWPS')" label-width="125px">
        <el-radio-group v-model="temp.useWPS" :disabled="!formable">
          <el-row>
            <el-radio :label="0">{{ $t('pages.useWPS_label1') }}</el-radio>
          </el-row>
          <el-row>
            <el-radio :label="1">{{ $t('pages.useWPS_label2') }}</el-radio>
          </el-row>
          <el-row>
            <el-radio :label="2" @change="showTip">{{ $t('pages.useWPS_label3') }}</el-radio>
          </el-row>
          <el-row>
            <el-radio :label="3">{{ $t('pages.useWPS_label4') }}</el-radio>
          </el-row>
          <el-row>
            <el-radio :label="4" @change="showTip">{{ $t('pages.useWPS_label5') }}</el-radio>
          </el-row>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.WPSType')" label-width="125px">
        <el-radio-group v-model="temp.limitType" :disabled="temp.useWPS == 0 || !formable" @change="handleLimitTypeChange">
          <el-radio :label="0">{{ $t('pages.notLimit') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.limit') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <el-card v-show="temp.limitType == 1" class="box-card" :body-style="{'padding': ' 5px 10px'}">
        <div slot="header">
          <span>{{ $t('pages.wpsFileType') }}</span>
        </div>
        <el-button v-if="formable" type="text" style="padding-left: 0" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
        <el-button v-if="formable" type="text" @click="selectAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
        <el-checkbox-group v-model="wpsOpenTypeData.checked" :disabled="!formable" @change="wpsOpenTypeChange">
          <el-row>
            <el-col v-for="(item, index) in wpsOpenTypeData.options" :key="index" :span="6">
              <el-checkbox :key="item.type" :label="item.type" :disabled="temp.useWPS==0 || temp.limitType==0">
                {{ item.desc }}
              </el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-card>
      <span v-if="validMsg" style="color: red;font-size: smaller;">{{ validMsg }}</span>
      <div><label style="color: #71afef;font-size: 15px;">{{ $t('pages.WPSTip') }}</label></div>
    </template>
  </stg-dialog>
</template>

<script>
import { createStrategy, updateStrategy, getStrategyByName } from '@/api/system/terminalManage/mobileWPSConfigStrategy'

export default {
  name: 'WpsSetupDlg',
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      itemColModel: [
        { prop: 'processName', label: 'processName', type: this.formable ? 'input' : 'text', width: '30' },
        { prop: 'config', label: 'parameterSetting1', type: 'select', disabled: !this.formable, width: '30', editIconShowFunc: this.editIconShowFunc,
          options: [
            { label: this.$t('pages.conventional'), value: 0 },
            { label: this.$t('pages.newInjectionMode'), value: 1 },
            { label: this.$t('pages.filter'), value: 2 }
          ] },
        { prop: 'hookMode', label: 'parameterSetting2', type: 'select', disabled: !this.formable, width: '30', options: [
          { label: this.$t('pages.conventional'), value: 0 },
          { label: this.$t('pages.filter'), value: 1 }
        ] },
        { prop: 'remark', label: 'remark', type: this.formable ? 'input' : 'text', width: '30' }
      ],
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        remark: '',
        entityType: '',
        entityId: undefined,
        useWPS: 0,
        wpsOpenType: 0,
        limitType: 0
      },
      wpsOpenTypeData: {
        checked: [],
        options: [
          { type: 1, desc: 'DOC' },
          { type: 2, desc: 'DOCX' },
          { type: 4, desc: 'XLS' },
          { type: 8, desc: 'XLSX' },
          { type: 16, desc: 'PPT' },
          { type: 32, desc: 'PPTX' },
          { type: 64, desc: 'WPS' },
          { type: 128, desc: 'ET' },
          { type: 256, desc: 'DPS' },
          { type: 512, desc: 'PDF' },
          { type: 1024, desc: 'TXT' }
        ]
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.stgNameRequired'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      itemDeleteable: false,
      submitting: false,
      slotName: undefined,
      validMsg: ''
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.validMsg = ''
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate: function(row) {
      this.validMsg = ''
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {
      if (rowData.wpsOpenType !== 0) {
        rowData.limitType = 1
      }
      this.wpsOpenTypeData.checked = []
      if (rowData.limitType === 0) {
        this.wpsOpenTypeData.options.forEach(data => {
          this.wpsOpenTypeData.checked.push(data.type)
        })
      } else {
        this.wpsOpenTypeData.options.forEach(data => {
          if (rowData.wpsOpenType & data.type) {
            this.wpsOpenTypeData.checked.push(data.type)
          }
        })
      }
    },
    formatFormData(formData) {
      formData.wpsOpenType = 0
      if (formData.limitType === 1) {
        this.wpsOpenTypeData.checked.forEach(el => {
          formData.wpsOpenType = formData.wpsOpenType + el
        })
      }
    },
    validateFormData(formData) {
      if (formData.limitType === 1 && this.wpsOpenTypeData.checked.length === 0) {
        this.validMsg = this.$t('pages.terminal_text3')
      } else {
        this.validMsg = ''
      }
      return !this.validMsg
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    handleLimitTypeChange(data) {
      if (data == 0) {
        this.wpsOpenTypeData.checked = []
        this.wpsOpenTypeData.options.forEach(data => {
          this.wpsOpenTypeData.checked.push(data.type)
        })
      }
    },
    selectAll(boolean) {
      if (boolean) {
        this.wpsOpenTypeData.checked = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024]
        this.validMsg = ''
      } else {
        this.wpsOpenTypeData.checked.splice(0)
      }
    },
    wpsOpenTypeChange(data) {
      if (data.length > 0) {
        this.validMsg = ''
      }
    },
    showTip() {
      this.$confirmBox(this.$t('pages.terminal_text14'), this.$t('text.prompt'), {
        confirmButtonText: this.$t('button.confirm2'),
        showCancelButton: false,
        type: 'warning'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.box-card{
  position: relative;
  >>>.el-card__header{
    padding: 10px 20px;
  }
}
.btn-box{
  position: absolute;
  top: 5px;
  right: 20px;
}
</style>
