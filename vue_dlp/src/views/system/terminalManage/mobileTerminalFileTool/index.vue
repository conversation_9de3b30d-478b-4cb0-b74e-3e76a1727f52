<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('route.appOpenSuffixStg')" name="AppOpenSuffixStg">
        <App-open-suffix-stg ref="AppOpenSuffixStg"></App-open-suffix-stg>
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.mobileWPSConfig')" name="WpsSetup">
        <Wps-setup ref="WpsSetup"></Wps-setup>
      </el-tab-pane>
      <el-tab-pane v-if="enhancedPlugVisible" :label="$t('route.enhancesPlug')" name="EnhancesPlug">
        <Enhanced-plug ref="EnhancesPlug"></Enhanced-plug>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import AppOpenSuffixStg from './appOpenSuffixStg'
import EnhancedPlug from './enhancedPlug'
import WpsSetup from './wpsSetup'
import { activeEnhancedPlug } from '@/api/property';

export default {
  name: 'MobileTerminalFileTool',
  components: { AppOpenSuffixStg, EnhancedPlug, WpsSetup },
  props: {
    tabName: { type: String, default: 'AppOpenSuffixStg' }
  },
  data() {
    return {
      activeName: this.tabName,
      enhancedPlugVisible: false
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.activeName = this.$route.query.tabName || this.activeName
    this.getEnhancedPlugPermission()
  },
  activated() {
    this.activeName = this.$route.query.tabName || this.activeName
    this.getEnhancedPlugPermission()
  },
  methods: {
    tabClick(pane, event) {
    },
    getEnhancedPlugPermission() {
      activeEnhancedPlug().then(res => {
        if (res.data) {
          this.enhancedPlugVisible = true
          this.activeName = this.activeName == 'EnhancesPlug' ? 'AppOpenSuffixStg' : this.activeName
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
  .app-container{
    padding: 10px 15px;
  }
</style>
