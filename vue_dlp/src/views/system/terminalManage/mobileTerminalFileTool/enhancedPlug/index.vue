<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar" >
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" size="mini" @click="handleFileImport">
          {{ $t('layout.importAuthorizationFile') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" @click="handleAlarmSetting">{{ $t('pages.alarmSetting') }}</el-button>
        <el-button size="mini" @click="handlePermissionSetting">
          <svg-icon icon-class="permission" />
          {{ $t('pages.chargePlug_Msg15') }}
        </el-button>
        <el-checkbox v-model="query.displayable" style="margin-left: 10px;" @change="displayableChange">{{ $t('pages.chargePlug_Msg1') }}</el-checkbox>
        <span v-if="alarmMsg" style="color: red; margin-left: 10px;">{{ alarmMsg }}</span>
      </div>
      <grid-table ref="authInfoList" :col-model="colModel" :row-data-api="rowDataApi" :multi-select="false" />
    </div>

    <!--  管理插件使用权限  -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.chargePlug_Msg15')"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogPermissionVisible"
      width="500px"
    >
      <div>
        <tree-menu
          ref="permissionTree"
          v-loading="loading"
          multiple
          :height="370"
          :data="treeData"
          :is-filter="false"
          :default-expand-all="false"
          :icon-option="iconOption"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="savePermissionData">
          {{ $t('button.saveAndContinue') }}
        </el-button>
        <el-button @click="dialogPermissionVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!--导入授权文件-->
    <el-dialog
      v-el-drag-dialog
      :title="$t('layout.importAuthorizationFile2')"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFileVisible"
      width="500px"
    >
      <Form ref="fileDataForm" :rules="fileRules" :model="fileTemp" label-position="right" label-width="60px" :extra-width="{en: 0}" style="width: 420px;">
        <FormItem :label="$t('table.file')" prop="fileName">
          <el-input
            v-model="fileTemp.fileName"
            class="input-with-button"
            readonly
            :placeholder="$t('layout.importAuthorizationFile3')"
            style="width: calc(100% - 76px);"
          />
          <el-upload
            ref="upload"
            accept=".ini,.ldk"
            class="upload-demo"
            name="uploadFile"
            action="aaaaaa"
            :show-file-list="false"
            :on-change="beforeUpload"
            :file-list="fileList"
            :auto-upload="false"
            style="width:72px; display: inline-block;"
          >
            <el-button size="small" type="primary">{{ $t('button.upload') }}</el-button>
          </el-upload>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateFile">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFileVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!--授权内容-->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.chargePlug_Msg2')"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFileDetailVisible"
      width="500px"
    >
      <p style="white-space: pre-wrap; font-size: 14px; line-height: normal;">{{ content }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="titleStatus" :loading="submitting" type="primary" @click="grantAuth">
          {{ $t('table.grantAuthor') }}
        </el-button>
        <el-button v-if="titleStatus" @click="dialogFileDetailVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
        <el-button v-if="!titleStatus" @click="dialogFileDetailVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>

    <!--预警设置-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.alarmSetting')"
      :visible.sync="dialogConfigVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="sysAlarmsetupDialog" :model="tempC" class="sys-alarm-setup" label-width="160px" label-position="right" style="width: 750px;">
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <el-checkbox v-model="tempC.excessAlarm" @change="excessAlarmChange">{{ $t('pages.sysAlarmSetup_chargePlug1') }}</el-checkbox>
        <FormItem v-if="tempC.excessAlarm" :label="$t('pages.undercount')" prop="senderName">
          <el-input v-model.number="tempC.undercount" style="width: 150px;" maxlength="6" min="1" max="999999" @input="numberLimit('undercount')">
            <template slot="append"> {{ $t('pages.openTimes3') }}</template>
          </el-input>
        </FormItem>
        <el-checkbox v-model="tempC.imminentAlarm" style="display:block;" @change="imminentAlarmChange">{{ $t('pages.sysAlarmSetup_chargePlug2') }}</el-checkbox>
        <FormItem v-if="tempC.imminentAlarm" :label="$t('pages.imminentAlarmDays')" prop="senderName">
          <el-input v-model.number="tempC.imminentAlarmDays" style="width: 150px;" maxlength="2" min="1" max="99" @input="numberLimit('imminentAlarmDays')">
            <template slot="append"> {{ $t('pages.day1') }}</template>
          </el-input>
        </FormItem>
        <FormItem v-if="tempC.imminentAlarm" :label="$t('pages.alarmFrequency')">
          <el-radio-group v-model="tempC.imminentAlarmFrequency">
            <el-radio :label="1">{{ $t('pages.onlyOneAlarm') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.dailyOneAlarm') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <el-checkbox v-model="tempC.expireAlarm" style="display:block;" @change="expireAlarmChange">{{ $t('pages.sysAlarmSetup_chargePlug3') }}</el-checkbox>
        <FormItem v-if="tempC.expireAlarm" :label="$t('pages.expireAlarmDays')" prop="senderName">
          <el-input v-model.number="tempC.expireAlarmDays" style="width: 150px;" maxlength="2" min="1" max="99" @input="numberLimit('expireAlarmDays')">
            <template slot="append"> {{ $t('pages.day1') }}</template>
          </el-input>
        </FormItem>
        <FormItem v-if="tempC.expireAlarm" :label="$t('pages.alarmFrequency')">
          <el-radio-group v-model="tempC.expireAlarmFrequency">
            <el-radio :label="1">{{ $t('pages.onlyOneAlarm') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.dailyOneAlarm') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <!-- 控制台弹窗告警 -->
        <el-divider content-position="left">{{ $t('pages.serverAlarmResponseRule') }}</el-divider>
        <div class="warnRule">
          <el-checkbox v-model="tempC.alarmPop" :disabled="!editable" style="display:block;">{{ $t('pages.serverAlarmPopUp') }}</el-checkbox>
          <FormItem v-if="tempC.alarmPop && editable" label-width="0px">
            <el-select v-model="tempC.sysUserIds" :disabled="!editable" filterable multiple :placeholder="$t('pages.alarmSetup_text7')" @change="sysUserChange">
              <el-option
                v-for="user in sysUserList"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              >
              </el-option>
            </el-select>
          </FormItem>
          <!-- 邮件告警 -->
          <el-checkbox v-model="tempC.alarmMail" :disabled="!editable" style="display:block;">{{ $t('pages.serverEmailAlarm') }}</el-checkbox>
          <data-editor
            v-if="tempC.alarmMail && editable"
            append-to-body
            :popover-width="500"
            :updateable="updateable"
            :deletable="deleteable"
            :add-func="createEmail"
            :update-func="updateEmail"
            :delete-func="deleteEmail"
            :cancel-func="cancelEmail"
            :before-update="beforeUpdateEmail"
            style="padding: 0 20px;"
          >
            <Form ref="emailForm" :model="emailTemp" :rules="emailRules" label-position="right" label-width="85px" style="margin-left: -10px">
              <FormItem :label="$t('table.emailName')" prop="name">
                <el-input v-model="emailTemp.name" maxlength="32"/>
              </FormItem>
              <FormItem :label="$t('table.emailAddress')" prop="address">
                <el-input v-model="emailTemp.address" maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.remark')" prop="remark">
                <el-input v-model="emailTemp.remark" type="textarea" show-word-limit maxlength="128"/>
              </FormItem>
            </Form>
            <empty-mail-server-prompt slot="attach-btn"/>
          </data-editor>
          <grid-table
            v-if="tempC.alarmMail && editable"
            ref="emailTable"
            :height="150"
            :multi-select="true"
            :show-pager="false"
            :col-model="emailColModel"
            :row-datas="tempC.emails"
            style="margin-bottom: 10px; padding: 0 20px;"
            @selectionChangeEnd="handleSelectionChange"
          />
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogConfigVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <mail-import-table-dlg ref="mailImportTableDlg" :append-to-body="false" @submitEnd="importEnd"/>
  </div>
</template>

<script>
import { getAuthInfoPage, uploadFile, grantAuthIni, getAlarmSetup, saveAlarmSetup, getAlarmMsg, exchangeTrwfe } from '@/api/system/terminalManage/chargePlugAuth'
import { getStrategy, updateStrategy } from '@/api/system/terminalManage/enhancesPlugStrategy'
import { initTimestamp } from '@/utils'
import moment from 'moment'
import i18n from '@/lang'
import MailImportTableDlg from '@/views/system/baseData/groupImportList/mailImportTableDlg'
import { mapGetters } from 'vuex'
import EmptyMailServerPrompt from '@/views/system/deviceManage/mailServer/prompt'
import { listCompleteTermTreeNode } from '@/api/system/terminalManage/terminal'

export default {
  name: 'EnhancedPlug',
  components: { EmptyMailServerPrompt, MailImportTableDlg },
  data() {
    return {
      colModel: [
        { prop: 'guid', label: 'authSerial', width: '150', sort: true, iconFormatter: this.activeStatusIconFormatter },
        { prop: 'modifyTime', label: 'authTime', width: '150', sort: true },
        { prop: 'deadline', label: 'deadline', width: '150', sort: true },
        { prop: 'totalTimes', label: 'authTotalTimes', width: '150', sort: true },
        { prop: 'leftTimes', label: 'authLeftTimes', width: '150' }
      ],
      emailColModel: [
        { prop: 'name', label: 'emailName', width: '100', sort: true },
        { prop: 'address', label: 'emailAddress', width: '120', sort: true },
        { prop: 'remark', label: 'remark', width: '100', sort: true }
      ],
      query: { // 查询条件
        page: 1,
        displayable: true,
        searchInfo: undefined
      },
      tempC: {},                     // 当前预警设置的基本信息
      defaultTempC: {
        id: null,
        bizType: 1, // 1：插件授权预警
        excessAlarm: false,
        undercount: null,
        imminentAlarm: false,
        imminentAlarmFrequency: 1,
        imminentAlarmDays: null,
        expireAlarm: false,
        expireAlarmDays: null,
        expireAlarmFrequency: 1,
        alarmPop: false,
        alarmMail: false,
        sysUserIds: [],
        emails: []
      },
      emailTemp: {},
      defaultEmailTemp: {
        id: undefined,
        name: '',
        address: '',
        remark: ''
      },
      deleteable: false,            // 邮箱删除按钮是否可用
      updateable: false,            // 邮箱修改按钮是否可用
      editable: false,              // 响应规则可编辑状态
      fileTemp: {
        fileName: ''
      },
      textTemp: {
        type: '',
        OfflineLockScreen: '',
        CompetitorsRights: '',
        CompetitorsDeadline: '',
        CompetitorsRightsEx: '',
        ResRights: '',
        TranEncrypt: '',
        moduleName: '',
        authNum: undefined,
        oldAuthNum: undefined
      },
      tempW: {
        id: undefined,
        useWPS: 0,
        wpsOpenType: 0,
        limitType: 0
      },
      rule: undefined,
      dialogVisible: false,
      dialogFileVisible: false,
      dialogFileDetailVisible: false,
      dialogFormVisible: false,
      dialogConfigVisible: false,
      dialogMobileWPSConfigVisible: false,
      dialogPermissionVisible: false,
      submitting: false,
      activeTab: 'configTab',
      fileRules: {
        fileName: [
          { required: true, message: this.$t('pages.signatureData_text2'), trigger: 'change' }
        ]
      },
      emailRules: { // 邮箱校验
        name: [{ required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }],
        address: [
          { required: true, type: 'email', message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.emailAddressCheck, trigger: 'blur' }
        ]
      },
      showHidden: false,
      fileList: [],
      notDeleteCodes: [],
      isArray: false,
      moduleFileName: '',
      uploadVisible: false,
      fileName: '',
      fileSubmitting: false,
      fileSaved: false,
      title: '',
      content: '',
      menuOptions: {
        AppOG: this.$t('pages.chargePlug_AppOG'),
        AppOD: this.$t('pages.chargePlug_AppOD'),
        AppOT: this.$t('pages.chargePlug_AppOT')
      },
      titleStatus: 0,  // 标识授权按钮展示
      alarmMsg: '', // 告警提醒信息
      wpsOpenTypeData: {
        checked: [],
        options: [
          { type: 1, desc: 'DOC' },
          { type: 2, desc: 'DOCX' },
          { type: 4, desc: 'XLS' },
          { type: 8, desc: 'XLSX' },
          { type: 16, desc: 'PPT' },
          { type: 32, desc: 'PPTX' },
          { type: 64, desc: 'WPS' },
          { type: 128, desc: 'ET' },
          { type: 256, desc: 'DPS' },
          { type: 512, desc: 'PDF' },
          { type: 1024, desc: 'TXT' }
        ]
      },
      wpsOpenTypeOptions: [
        { type: 1, desc: 'DOC' },
        { type: 2, desc: 'DOCX' },
        { type: 4, desc: 'XLS' },
        { type: 8, desc: 'XLSX' },
        { type: 16, desc: 'PPT' },
        { type: 32, desc: 'PPTX' },
        { type: 64, desc: 'WPS' },
        { type: 128, desc: 'ET' },
        { type: 256, desc: 'DPS' },
        { type: 512, desc: 'PDF' },
        { type: 1024, desc: 'TXT' }
      ],
      iconOption: { 'G': 'terminalGroup', 'T': 'phone' },
      treeData: [],                          // 当前授权使用的终端（组）权限
      tempP: {                               // 增强型文档插件策略对象
        objectIds: [],
        objectGroupIds: []
      },
      stgType: 267,
      objFormTextMap: {
        'add': this.i18nConcatText(this.$t('table.limitUseObj'), 'create'),
        'update': this.i18nConcatText(this.$t('table.limitUseObj'), 'update')
      },
      isAddObj: true,
      tempUpdateObj: {},                     // 临时更新对象
      deleteAble1: false,
      formTemp: {
        objectType: 3,
        objectIdsForAdd: [],
        objectIdsForUpdate: []
      },
      formRule: {
        objectIds: { validator: this.objectIdsValidator, trigger: 'blur' }
      },
      logTermTreeIds: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters([
      'logTermTree',
      // 系统管理员列表
      'sysUserList'
    ]),
    gridTable() {
      return this.$refs['authInfoList']
    }
  },
  watch: {
    logTermTree() {
      this.initTreeNode()
    }
  },
  created() {
    initTimestamp(this)
    this.getAlarmConfig()
    // this.getMobileWPSConfig()
    this.exchangeTrwfe()
    this.resetTempC()
    this.resetEmailTemp()
    this.initCompleteTree()
    if (this.logTermTree && this.logTermTree.length > 0) {
      this.initTreeNode()
    }
  },
  activated() {
    initTimestamp(this)
    this.initCompleteTree()
    this.getAlarmConfig()
    // this.getMobileWPSConfig()
    this.exchangeTrwfe()
  },
  provide() {
    return {
      showImportAccDlg: () => this.$refs['mailImportTableDlg'] && this.$refs['mailImportTableDlg'].show(),
      importDlgBtnName: this.$t('pages.emailLibImport')
    }
  },
  methods: {
    initCompleteTree() {
      listCompleteTermTreeNode({ recycle: 0 }).then(res => {
        this.treeData.splice(0)
        res.data.forEach(data => {
          this.formatCompleteTree(data)
          this.treeData.push(data)
        })
      })
    },
    formatCompleteTree(checkedNode) {
      if (!checkedNode.children || checkedNode.children.length === 0) {
        return
      }
      const children = []
      checkedNode.children.forEach(childNode => {
        if (childNode.type === 'G' || childNode.dataType == 3) {
          this.formatCompleteTree(childNode)
          children.push(childNode)
        }
      })
      checkedNode.children = children
    },
    // 加载可授权终端树
    initTreeNode() {
      this.logTermTreeIds.splice(0)
      this.logTermTree.forEach(data => {
        this.logTermTreeIds.push(data.id)
        this.formatterSelectedData(data)
      })
    },
    formatterSelectedData(checkedNode) {
      if (!checkedNode.children || checkedNode.children.length === 0) {
        return
      }
      checkedNode.children.forEach(childNode => {
        if (childNode.dataType === 'G') {
          this.logTermTreeIds.push(childNode.id)
          this.formatterSelectedData(childNode)
        }
        if (childNode.dataType == 3) {
          this.logTermTreeIds.push(childNode.id)
        }
      })
    },
    formatPermissionData() {
      this.tempP.objectGroupIds.splice(0)
      this.tempP.objectIds.splice(0)
      const checkedNodes = this.$refs['permissionTree'].getCheckedNodes()
      if (checkedNodes && checkedNodes.length > 0) {
        const checkedKeys = []
        checkedNodes.forEach(node => {
          if (checkedKeys.indexOf(node.parentId) < 0) {
            const key = node.id
            checkedKeys.push(key)
            const startWith = key.substring(0, 1)
            if (startWith === 'G') {
              this.tempP.objectGroupIds.push(key.substring(1, key.length))
            }
            if (startWith === 'T') {
              this.tempP.objectIds.push(key.substring(1, key.length))
            }
          }
        })
      }
    },
    savePermissionData() {
      this.submitting = true
      this.formatPermissionData()
      updateStrategy(this.tempP).then(respond => {
        this.submitting = false
        this.dialogPermissionVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(res => {
        this.submitting = false
      })
    },
    showTip() {
      this.$confirmBox(this.$t('pages.terminal_text14'), this.$t('text.prompt'), {
        confirmButtonText: this.$t('button.confirm2'),
        showCancelButton: false,
        type: 'warning'
      })
    },
    sysUserChange(selections) {
      const length = selections.length
      if (length > 1) {
        if (selections[length - 1] == 0 || length == this.tempC.sysUserIds.length - 1) {
          // 当最后一个选择‘所有管理员’，或者选择了除‘所有管理员’的其他所有选项时，清空所有其他选项，只保留‘所有管理员’
          selections.splice(0, length, 0)
        } else if (selections[0] == 0) {
          // ‘所有管理员’在选项第一个时，去掉该选项
          selections.splice(0, 1)
        }
      }
    },
    validateForm() {
      if ((this.tempC.excessAlarm || this.tempC.imminentAlarm) && !this.tempC.alarmPop && !this.tempC.alarmMail) {
        this.$message({
          message: this.$t('pages.sysAlarm_Msg1'),
          type: 'error'
        })
        return false
      }
      return true
    },
    emailAddressCheck(rule, value, callback) {
      const email = (this.tempC.emails || []).find(email => email.address === value)
      if (email && email.id !== this.emailTemp.id) {
        callback(new Error(this.$t('pages.effectiveContent_text31')))
        return
      }
      callback()
    },
    // 超额提醒启用状态变更
    excessAlarmChange(data) {
      // 启用剩余授权打开次数不足提醒时，默认剩余打开文件次数不足1000次时进行告警提醒
      if (data) {
        this.tempC.undercount = 1000
      }
      if (!data && !this.tempC.imminentAlarm) {
        this.tempC.alarmPop = false
        this.tempC.alarmMail = false
        this.editable = false
      } else {
        this.editable = true
      }
    },
    // 到期提醒启用状态变更
    imminentAlarmChange(data) {
      // 启用临期告警提醒时，临期天数默认7天
      if (data) {
        this.tempC.imminentAlarmDays = 7
      }
      if (!data && !this.tempC.excessAlarm) {
        this.tempC.alarmPop = false
        this.tempC.alarmMail = false
        this.editable = false
      } else {
        this.editable = true
      }
    },
    // 到期提醒启用状态变更
    expireAlarmChange(data) {
      // 启用到期告警提醒时，到期天数默认3天
      if (data) {
        this.tempC.expireAlarmDays = 3
      }
      if (!data && !this.tempC.excessAlarm && !this.tempC.imminentAlarm) {
        this.tempC.alarmPop = false
        this.tempC.alarmMail = false
        this.editable = false
      } else {
        this.editable = true
      }
    },
    numberLimit(field) {
      if (!this.tempC[field] || isNaN(this.tempC[field]) || (this.tempC[field] && this.tempC[field].toString().startsWith('-'))) {
        this.tempC[field] = 1
      }
    },
    getAlarmMsg() {
      getAlarmMsg().then(res => {
        this.alarmMsg = res.data
      })
    },
    exchangeTrwfe() {
      exchangeTrwfe().then(res => {
        this.gridTable.execRowDataApi()
        this.getAlarmMsg()
      })
    },
    getAlarmConfig() {
      this.editable = false
      getAlarmSetup().then(res => {
        this.tempC = Object.assign(this.tempC, res.data)
        if (this.tempC.excessAlarm || this.tempC.imminentAlarm) {
          this.editable = true
        }
      })
    },
    resetTempC() {
      this.tempC = JSON.parse(JSON.stringify(this.defaultTempC))
      this.submitting = false
    },
    resetEmailTemp() {
      this.emailTemp = JSON.parse(JSON.stringify(this.defaultEmailTemp))
    },
    emailTable() {
      return this.$refs['emailTable']
    },
    emailForm() {
      return this.$refs['emailForm']
    },
    // 新增邮箱数据
    createEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          rowData.id = new Date().getTime()
          const emails = this.tempC.emails
          // 防止重复
          if (emails.length > 0 && rowData.id < emails[0].id) {
            rowData.id = emails[0].id + 1
          }
          this.tempC.emails.unshift(rowData)
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 修改邮箱数据前，更新emailTemp数据
    beforeUpdateEmail() {
      Object.assign(this.emailTemp, this.emailTable().getSelectedDatas()[0])
    },
    // 修改邮箱数据
    updateEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          for (let i = 0, size = this.tempC.emails.length; i < size; i++) {
            const data = this.tempC.emails[i]
            if (rowData.id === data.id) {
              this.tempC.emails.splice(i, 1, rowData)
              break
            }
          }
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 删除邮箱数据
    deleteEmail() {
      const toDeleteIds = this.emailTable().getSelectedIds()
      this.tempC.emails.splice(0, this.tempC.emails.length, ...this.emailTable().deleteRowData(toDeleteIds))
      this.cancelEmail()
    },
    // 重置邮箱表单
    cancelEmail() {
      this.emailTable() && this.emailTable().setCurrentRow()
      this.emailForm() && this.emailForm().clearValidate()
      this.resetEmailTemp()
    },
    // 邮箱列表数据勾选
    handleSelectionChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.updateable = true
      } else {
        this.updateable = false
        this.cancelEmail()
      }
    },
    clearValidate() {
      this.$nextTick(() => {
        this.$refs['sysAlarmsetupDialog'] && this.$refs['sysAlarmsetupDialog'].clearValidate()
        let dataForm = this.$refs['sysAlarmsetupDialog' + this.activeSlotName]
        if (Array.isArray(dataForm)) dataForm = dataForm[0]
        if (dataForm) {
          dataForm.clearValidate()
        }
      })
    },
    submitFormData(submitFunc) {
      this.validFormData(() => {
        this.submitting = true
        const tempData = Object.assign({}, this.tempC)
        if (!tempData.alarmPop) {
          tempData.sysUserIds = null
        }
        if (!tempData.alarmMail) {
          tempData.emails = null
        }
        submitFunc(tempData).then(respond => {
          this.$emit('submitEnd', tempData)
          this.exchangeTrwfe()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.saveSuccess'),
            type: 'success',
            duration: 2000
          })
          this.dialogConfigVisible = false
          this.submitting = false
        }).catch(e => {
          this.submitting = false
        })
      })
    },
    validFormData(submitFunc) {
      this.submitting = true
      // 先验证头部公共部分的表单
      this.$refs['sysAlarmsetupDialog'].validate((valid) => {
        if (valid) {
          if (this.validateForm()) {
            if (this.tempC.alarmPop && (!this.tempC.sysUserIds || this.tempC.sysUserIds.length == 0)) {
              this.$message({
                message: this.$t('pages.sysAlarm_Msg2'),
                type: 'error'
              })
              this.submitting = false
            } else if (this.tempC.alarmMail && (!this.tempC.emails || this.tempC.emails.length == 0)) {
              this.$message({
                message: this.$t('pages.sysAlarm_Msg3'),
                type: 'error'
              })
              this.submitting = false
            } else if (submitFunc) {
              submitFunc()
            } else {
              this.submitting = false
            }
          } else {
            this.submitting = false
          }
        } else { this.submitting = false }
      })
    },
    saveData() {
      this.submitFormData(saveAlarmSetup)
    },
    handleDrag() {
    },
    refresh() {
      this.handleFilter()
      this.exchangeTrwfe()
    },
    parseRule(rule) {
      if (typeof rule !== 'string' || rule.trim().length === 0) {
        return {}
      }
      try {
        return JSON.parse(rule)
      } catch (e) {
        console.log(e)
        return {}
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFileImport() {
      this.fileTemp = {
        fileName: ''
      }
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles()
        this.$refs['fileDataForm'].clearValidate()
      })
      this.dialogFileVisible = true
    },
    handleAlarmSetting() {
      this.resetTempC()
      this.getAlarmConfig()
      this.dialogConfigVisible = true
    },
    handlePermissionSetting() {
      this.loading = true
      this.dialogPermissionVisible = true
      getStrategy().then(respond => {
        if (respond.data) {
          this.tempP = Object.assign(this.tempP, JSON.parse(JSON.stringify(respond.data)))
          const defaultCheckedKeys = []
          if (this.tempP.objectIds.length > 0) {
            this.tempP.objectIds.forEach(item => {
              defaultCheckedKeys.push('T' + item)
            })
          }
          if (this.tempP.objectGroupIds.length > 0) {
            this.tempP.objectGroupIds.forEach(item => {
              defaultCheckedKeys.push('G' + item)
            })
          }
          this.$nextTick(() => {
            if (defaultCheckedKeys.length > 0) {
              this.$refs['permissionTree'] && this.$refs['permissionTree'].setCheckedKeys(defaultCheckedKeys)
              this.treeData.forEach(item => {
                if (this.logTermTreeIds.indexOf(item.id) < 0) {
                  item.disabled = true
                }
                this.formatDisbaledNode(item)
              })
              this.loading = false
            }
          })
        }
      })
    },
    formatDisbaledNode(node) {
      if (!node.children || node.children.length === 0) {
        return
      }
      const children = []
      node.children.forEach(childNode => {
        if (this.logTermTreeIds.indexOf(childNode.id) < 0) {
          childNode.disabled = true
        }
        this.formatDisbaledNode(childNode)
        children.push(childNode)
      })
      node.children = children
    },
    displayableChange() {
      this.refresh()
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getAuthInfoPage(searchQuery)
    },
    updateFile() {
      this.submitting = true
      this.$refs['fileDataForm'].validate((valid) => {
        if (valid) {
          const fd = new FormData()
          const uploadFiles = this.$refs['upload'].uploadFiles
          fd.append('uploadFile', uploadFiles[uploadFiles.length - 1].raw) // 传文件
          uploadFile(fd).then(res => {
            this.submitting = false
            this.titleStatus = 0
            this.content = ''
            const result = res.data
            if (result.type === 999) {
              this.content = this.$t('pages.authorizationUploadFailed')
            } else if (result.type === 998) {
              this.content = this.$t('pages.authorizationFileExist')
            } else if (result.type === 997) {
              this.content = this.$t('pages.regesterInfoError')
            } else if (result.type === 996) {
              this.content = this.$t('pages.authorizationFileExpired')
            } else if (result.type === 99) {
              this.content = this.$t('pages.illegalFile')
            } else if (result.type === 3) { // 收费插件授权授权文件类型为3
              this.dialogFileVisible = false
              this.titleStatus = 1
              const AppOG = this.menuOptions.AppOG + result.AppOG + '\n'
              const AppOD = this.menuOptions.AppOD + result.AppOD + '\n'
              const AppOT = this.menuOptions.AppOT + result.AppOT
              this.content = AppOG + AppOD + AppOT
            } else {
              this.content = this.$t('pages.illegalFile')
            }
            this.dialogFileDetailVisible = true
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    grantAuth() {
      this.submitting = true
      if (this.titleStatus === 1) {
        grantAuthIni({ fileName: this.fileTemp.fileName }).then(res => {
          this.dialogFileDetailVisible = false
          this.submitting = false
          this.refresh()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.authSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }
    },
    beforeUpload(file, fileList) {
      this.fileTemp.fileName = file.name
    },
    activeStatusIconFormatter(row) {
      const date = moment(new Date()).format('YYYY-MM-DD')
      if (moment(date).isAfter(moment(row.deadline)) || !row.leftTimes) {
        return { class: 'forbidden', title: i18n.t('text.expired') }
      } else {
        return { class: 'active', title: i18n.t('text.active') }
      }
    },
    importEnd(datas) {
      if (datas) {
        datas.forEach(item => {
          const { name, address, remark } = item
          const emails = this.tempC.emails || []
          const time = new Date().getTime()
          const id = emails.length === 0 || time > emails[0].id ? time : emails[0].id + 1
          this.emailAddressCheck(null, address, info => {
            if (!info) {
              this.tempC.emails.unshift({ id, name, address, remark })
            }
          })
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-dialog__body .el-upload .el-button {
    margin: 0;
  }
  .el-form.sys-alarm-setup {
    .el-form-item {
      padding: 0 20px;
    }
    .el-checkbox {
      padding-left: 20px;
      line-height: 30px;
    }

  }
</style>
