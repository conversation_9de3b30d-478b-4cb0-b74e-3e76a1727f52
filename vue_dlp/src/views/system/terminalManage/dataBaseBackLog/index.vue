<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <span>
          <label style="margin-right: 5px">{{ $t('table.operateStatus') }}</label>
          <el-select v-model="query.operateStatus" clearable style="width: 120px;">
            <el-option v-for="item in operateStatues" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>
        <span style="margin-left: 10px">
          <label style="margin-right: 5px">{{ $t('table.backupPosition') }}</label>
          <el-select v-model="query.backupPosition" style="width: 200px;">
            <el-option v-for="item in backupPositions" v-show="!item.hide" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </span>
        <span style="margin-left: 10px">
          <TimeQuery
            ref="timeQuery"
            :is-clearable="true"
            :leaf-label="$t('pages.manageLibrary_encryptionKey_text5')"
            @getTimeParams="getRecordTimeParams"
          />
        </span>
        <el-button style="margin-left: 10px" type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <el-button style="margin-left: 10px" type="primary" icon="el-icon-refresh" size="mini" @click="handleFilter">
          {{ $t('button.refresh') }}
        </el-button>
      </div>
      <grid-table ref="databaseBackupLogList" :multi-select="false" :col-model="colModel" :default-sort="{ prop: 'recordTime' }" :row-data-api="rowDataApi" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
  </div>
</template>

<script>
import { getPage } from '@/api/system/terminalManage/databaseBackLog'
import { enableStgDelete } from '@/utils';
import { deleteDbBackupNoticeByIds } from '@/api/strategyIssue'
export default {
  name: 'DataBaseBackLog',
  props: {
    noteId: { type: Number, default: null },    //  从通知传递过来的通知Id，目的是为了删除该通知，表示已读过
    createTime: { type: String, default: null } //  从通知传递过来的备份时间
  },
  data() {
    return {
      colModel: [
        { prop: 'recordTime', label: 'operateTime', width: '100', sort: true },
        { prop: 'operateStatus', label: 'operateStatus', width: '100', type: 'text', textColor: 'textColor', formatter: this.operateStatusFormatter, sort: true },
        { prop: 'operateMessage', label: 'operateMessage', width: '100', type: 'text', textColor: 'textColor', formatter: this.operateMessageFormatter },
        { prop: 'backupPosition', label: 'backupPosition', width: '100', formatter: this.backupPositionFormatter, sort: true },
        { prop: 'localPath', label: 'backupPath', width: '150', sort: true },
        { prop: 'backupSize', label: 'backupSize', width: '100', formatter: this.fileSizeFormatter, sort: true }
      ],
      // 查询条件
      query: {
        page: 1,
        operateStatus: null,
        backupPosition: null,
        recordStartTime: null,
        recordEndTime: null,
        backupStartTime: null,
        backupEndTime: null
      },
      backupPositions: [
        { value: null, label: this.$t('pages.all') },
        { value: 1, label: this.$t('pages.manageLibrary_localDiskToEngine') },
        // { value: 2, label: this.$t('pages.manageLibrary_encryptionKey_cloud_platform') }
        { value: 3, label: this.$t('pages.manageLibrary_shareDisk') }
      ],
      operateStatues: [{ value: null, label: this.$t('pages.all') }, { value: 0, label: this.$t('pages.manageLibrary_encryptionKey_backup_fail') }, { value: 1, label: this.$t('pages.manageLibrary_encryptionKey_backup_success') }, { value: 2, label: this.$t('pages.manageLibrary_encryptionKey_delete_fail') }, { value: 3, label: this.$t('pages.manageLibrary_encryptionKey_delete_success') }],
      recordDate: [],
      backDate: [],
      supportShare: ['windows'] //  当前仅windows支持共享目录，若linux需要支持则添加linux，可填值为：windows/linux/mac
    }
  },
  computed: {
    gridTable() {
      return this.$refs['databaseBackupLogList']
    }
  },
  watch: {
    noteId: function(noteId) {
      if (this.createTime !== null) {
        this.$refs.timeQuery && this.$refs.timeQuery.setDate(this.createTime)
        this.handleFilter()
      }
      if (noteId) {
        const ids = []
        ids.push(noteId)
        //  删除通知
        deleteDbBackupNoticeByIds(ids);
      }
    }
  },
  created() {
  },
  methods: {
    init() {
      this.setBackupPositions()
      this.handleFilter()
    },
    setBackupPositions() {
      const isSupportShare = this.supportShare.includes(this.$store.getters.engineOsType);
      const shareIndex = this.backupPositions.findIndex(data => { return data.value === 3 })
      if (shareIndex > -1) {
        this.$set(this.backupPositions[shareIndex], 'hide', !isSupportShare)
      }
      //  设置all 是否隐藏
      const notHideList = this.backupPositions.filter(data => { return !data.hide }) || []
      if (notHideList.length === 2) {
        this.$set(this.backupPositions[0], 'hide', !isSupportShare)
      }
      const index = this.backupPositions.findIndex(data => { return !data.hide })
      this.query.backupPosition = this.backupPositions[0].hide && index > -1 ? this.backupPositions[index].value : null
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    afterLoad(array) {
      array.forEach(row => {
        let value = row.operateStatus
        if (value !== undefined && value != null) {
          value = value + ''
          switch (value) {
            case '0' :
            case '2' : row.textColor = '#F80317FF'; break;
            default : row.textColor = '';
          }
        }
      })
    },
    handleFilter() {
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    //  文件大小
    fileSizeFormatter(row, data) {
      return row.backupSizeLabel || ''
    },
    //  备份位置格式
    backupPositionFormatter(row) {
      const value = row.backupPosition
      if (value) {
        const position = this.backupPositions.find(item => { return item.value === value });
        return position != null ? position.label : '';
      }
      return '';
    },
    //  操作状态
    operateStatusFormatter(row) {
      let value = row.operateStatus
      let result = ''
      if (value !== undefined && value != null) {
        value = value + ''
        switch (value) {
          case '0' : result = this.$t('pages.manageLibrary_encryptionKey_backup_fail'); row.textColor = '#F80317FF'; break;
          case '1' : result = this.$t('pages.manageLibrary_encryptionKey_backup_success'); break;
          case '2' : result = this.$t('pages.manageLibrary_encryptionKey_delete_fail'); row.textColor = '#F80317FF'; break;
          case '3' : result = this.$t('pages.manageLibrary_encryptionKey_delete_success'); break;
          default : result = '';
        }
      }
      return result;
    },
    operateMessageFormatter(row) {
      let value = row.operateStatus
      let result = ''
      if (value !== undefined && value != null) {
        value = value + ''
        switch (value) {
          case '0' : result = row.backupName + ' ' + (row.operateFailMsg && row.operateFailMsg !== '' ? row.operateFailMsg : this.$t('pages.manageLibrary_encryptionKey_backup_fail')); row.textColor = '#F80317FF'; break;
          case '1' : result = row.backupName + ' ' + this.$t('pages.manageLibrary_encryptionKey_backup_success'); break;
          case '2' : result = row.backupName + ' ' + (row.operateFailMsg && row.operateFailMsg !== '' ? row.operateFailMsg : this.$t('pages.manageLibrary_encryptionKey_delete_fail')); row.textColor = '#F80317FF'; break;
          case '3' : result = row.backupName + ' ' + this.$t('pages.manageLibrary_encryptionKey_delete_success'); break;
          default : result = '';
        }
      }
      return result;
    },
    getRecordTimeParams(val) {
      if (val.createDate) {
        this.query.recordStartTime = val.createDate + ' 00:00:00'
        this.query.recordEndTime = val.createDate + ' 23:59:59'
      } else if (val.startDate && val.endDate) {
        this.query.recordStartTime = val.startDate + ' 00:00:00'
        this.query.recordEndTime = val.endDate + ' 23:59:59'
      } else {
        this.query.recordStartTime = null
        this.query.recordEndTime = null
      }
    }
  }
}
</script>
