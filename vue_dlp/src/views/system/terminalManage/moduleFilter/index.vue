<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button type="primary" icon="el-icon-edit" size="mini" @click="handleBatchUpdate">
          {{ $t('pages.batchModification') }}
        </el-button>
        <div class="searchCon">
          <tree-select
            ref="filterModuleTreeSelect"
            style="display: inline-block;width: 200px;"
            :height="350"
            :width="400"
            :data="moduleTreeData"
            :collapse-tags="true"
            is-filter
            multiple
            clearable
            :placeholder="$t('pages.pleaseSelectContent', { content: $t('pages.module') })"
            @change="filterModuleSelectedChange"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :multi-select="false"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <!--  新增、修改策略-->
    <Drawer
      ref="drawer"
      class="my-draw"
      :title="textMap[dialogStatus]"
      :visible.sync="visible"
      :status="dialogStatus"
      :create-data="createData"
      :update-data="updateData"
      :before-close="beforeClose"
    >
      <span style="color: #666666;font-weight: bold">{{ $t('table.applicationObj') }}:</span>
      <tree-select
        :placeholder="$t('pages.placeChooseApplicationObj')"
        node-key="id"
        multiple
        check-strictly
        collapse-tags
        is-filter
        :filter-key="{ prop: 'dataType', value: '3', showIfNoProp: true }"
        :checked-keys="temp.checkedObjectKeys"
        :local-search="false"
        :width="400"
        :disabled="dialogStatus === 'update'"
        leaf-key="terminal"
        style="display: inline-block; width: calc(100% - 73px)"
        @change="checkedObjKeysChange"
      />
      <div style="height: calc(100% - 65px);margin-top: 5px">
        <el-button type="info" size="small" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
        <el-button type="info" size="small" @click="selectAll(false)">{{ $t('button.cancelSelected') }}</el-button>
        <tree-menu
          ref="moduleTree"
          multiple
          :data="moduleTreeData"
          :filter-key="filterKey"
          :expand-on-click-node="true"
          :checked-keys="checkedModuleFilterIds"
          @check="checkedModuleFilterKeysChange"
        >
        </tree-menu>
      </div>
    </Drawer>

    <!--  批量修改-->
    <Drawer
      ref="batchDrawer"
      class="my-draw"
      :title="$t('pages.batchModification')"
      :visible.sync="batchDlgVisible"
      status="update"
      :update-data="batchUpdateData"
      :before-close="beforeClose"
    >
      <span style="color: #666666;display: inline-block;font-weight: bold;margin-left: 22px">{{ $t('table.applicationObj') }}:</span>
      <tree-select
        :placeholder="$t('pages.placeChooseApplicationObj')"
        node-key="id"
        multiple
        check-strictly
        collapse-tags
        is-filter
        :filter-key="{ prop: 'dataType', value: '3', showIfNoProp: true }"
        :checked-keys="temp.checkedObjectKeys"
        :local-search="false"
        :width="400"
        leaf-key="terminal"
        style="display: inline-block; width: calc(100% - 98px)"
        @change="checkedObjKeysChange"
      />
      <div style="margin-top: 5px">
        <span style="color: #666666;font-weight: bold;">
          {{ $t('pages.allocationMode') }}
          <el-tooltip placement="top">
            <template slot="content">
              <i18n path="pages.moduleFilterAllocationModeTip">
                <br slot="br"/>
              </i18n>
            </template>
            <i class="el-icon-info"/>
          </el-tooltip>
          :</span>
        <el-select v-model="temp.operateType" style="display: inline-block; width: calc(100% - 98px)">
          <el-option :key="1" :value="1" :label="$t('pages.addModuleFilter')"/>
          <el-option :key="2" :value="2" :label="$t('pages.removeModuleFilter')"/>
          <el-option :key="3" :value="3" :label="$t('pages.settingModuleFilter')"/>
        </el-select>
      </div>
      <div style="height: calc(100% - 100px);margin-top: 5px">
        <el-button type="info" size="small" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
        <el-button type="info" size="small" @click="selectAll(false)">{{ $t('button.cancelSelected') }}</el-button>
        <tree-menu
          ref="batchModuleTree"
          multiple
          :data="moduleTreeData"
          :filter-key="filterKey"
          :expand-on-click-node="true"
          :checked-keys="checkedModuleFilterIds"
          @check="checkedModuleFilterKeysChange"
        >
        </tree-menu>
      </div>
    </Drawer>
  </div>
</template>

<script>
import {
  getPageModuleFilter,
  listSubModule,
  createModuleFilter,
  updateModuleFilter,
  batchUpdateModuleFilter,
  deleteModuleFilter,
  listExistSelfStgObjName
} from '@/api/system/terminalManage/moduleFilter'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils';
import { setTheFirstLetterToUppercase } from '@/utils/i18n';
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'ModuleFilter',
  data() {
    return {
      colModel: [
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'filterSubModules', label: 'closeModule', width: '300', formatter: this.formatClosedModule },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '150',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate },
            { label: 'delete', formatter: this.deleteButtonFormatter, click: this.handleDelete }
          ]
        }
      ],
      moduleTreeData: [],
      filterKey: ['TermFilterNode159'],
      checkedModuleFilterIds: [],
      query: { // 查询条件
        objectType: undefined,
        objectId: undefined,
        filterModuleIds: []
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        objectType: undefined,
        objectId: undefined,
        filterSubModules: '',
        checkedObjectKeys: [],
        objectIds: [],
        objectGroupIds: [],
        operateType: undefined
      },
      showTree: true,
      // auditOptions: {
      //   '179': 'B2H'
      // },
      visible: false,
      batchDlgVisible: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.moduleSubFilter'), 'update'),
        create: this.i18nConcatText(this.$t('pages.moduleSubFilter'), 'create')
      },
      dialogStatus: 'create',
      deleteable: false,
      selectedApplicationObject: []
    }
  },
  computed: {
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    },
    gridTable() {
      return this.$refs['strategyListTable']
    },
    drawer() {
      return this.$refs['drawer']
    },
    batchDrawer() {
      return this.$refs['batchDrawer']
    },
    moduleTree() {
      return this.$refs['moduleTree']
    },
    batchModuleTree() {
      return this.$refs['batchModuleTree']
    },
    enLang() {
      return this.$store.getters.language === 'en'
    }
  },
  created() {
    this.listAllSubModule()
  },
  mounted() {
    setTimeout(() => {
      const { entityId, entityType } = this.$route.query
      if (entityId && entityType) {
        entityLink({ entityType, entityId }, {}, this)
      }
      // 根据路由查询条件entityId、entityType查询之后，清除查询
      this.$router.push({ query: {}});
    }, 150)
  },
  activated() {
    const { entityId, entityType } = this.$route.query
    if (entityId && entityType) {
      entityLink({ entityType, entityId }, {}, this)
    }
    // 根据路由查询条件entityId、entityType查询之后，清除查询
    this.$router.push({ query: {}});
  },
  methods: {
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPageModuleFilter(searchQuery)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    entityFormatter(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter(row) {
      return buttonFormatter(row, this)
    },
    deleteButtonFormatter(row) {
      if (this.query.objectId === undefined) {
        return ''
      }
      const isG = this.query.objectType == 3 || this.query.objectType == 4
      if (!isG && row.objectIds && row.objectIds.indexOf(Number.parseInt(this.query.objectId)) > -1) {
        return this.$t('text.delete')
      } else if (isG && row.objectGroupIds && row.objectGroupIds.indexOf(Number.parseInt(this.query.objectId)) > -1) {
        return this.$t('text.delete')
      }
      return ''
    },
    async strategyTargetNodeChange(tabName, data) {
      if (data) {
        this.query.objectType = data.type
        this.query.objectId = data.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.handleFilter()
    },
    listAllSubModule() {
      const that = this
      listSubModule().then(respond => {
        if (respond && respond.data) {
          that.moduleTreeData = respond.data
          that.moduleTreeData.forEach(item => {
            if (item.children) {
              item.children.forEach(child => {
                if (this.enLang) {
                  child.label = setTheFirstLetterToUppercase(child.label)
                }
              })
            }
          })
        }
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.formatFormObject(this.query)
      this.visible = true
    },
    handleBatchUpdate() {
      this.resetTemp()
      this.batchDlgVisible = true
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.formatFormObject(row)
      this.visible = true
      this.$nextTick(() => {
        this.checkedModuleFilterIds = row.filterSubModules ? row.filterSubModules.split(',').map(module => 'TermFilterNode' + module) : []
      })
    },
    createData() {
      this.formatSubmitData()
      if (this.temp.objectIds.length === 0 && this.temp.objectGroupIds.length === 0) {
        this.$message({
          message: this.$t('pages.placeChooseApplicationObj')
        })
        return
      }
      listExistSelfStgObjName({ objectIds: this.temp.objectIds, objectGroupIds: this.temp.objectGroupIds }).then((resp) => {
        const existSelfStgObjNames = resp.data
        if (existSelfStgObjNames.length > 0) {
          this.$confirmBox(this.$t('pages.moduleFilter_confirmMsg2', { objNames: existSelfStgObjNames }), this.$t('text.prompt')).then(() => {
            this.execCreateOrUpdate()
          }).catch(() => {})
        } else {
          this.execCreateOrUpdate()
        }
      })
    },
    batchUpdateData() {
      this.formatBatchSubmitData()
      if (this.temp.objectIds.length === 0 && this.temp.objectGroupIds.length === 0) {
        this.$message({
          message: this.$t('pages.placeChooseApplicationObj')
        })
        return
      }
      if (this.temp.operateType === undefined) {
        this.$message({
          message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.allocationMode') })
        })
        return
      }
      batchUpdateModuleFilter(this.temp).then(respond => {
        this.batchDrawer.handleClose()
        this.handleFilter()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.moduleFilter_notifyMsg1'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('text.fail'),
          type: 'error',
          duration: 2000
        })
      })
    },
    updateData() {
      this.formatSubmitData()
      this.execCreateOrUpdate()
    },
    execCreateOrUpdate() {
      if (this.dialogStatus === 'create') {
        createModuleFilter(this.temp).then(respond => {
          this.drawer.handleClose()
          this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.moduleFilter_notifyMsg1'),
            type: 'success',
            duration: 2000
          })
        }).catch(reason => {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('text.fail'),
            type: 'error',
            duration: 2000
          })
        })
      } else if (this.dialogStatus === 'update') {
        updateModuleFilter(this.temp).then(respond => {
          this.drawer.handleClose()
          this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.moduleFilter_notifyMsg1'),
            type: 'success',
            duration: 2000
          })
        }).catch(reason => {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('text.fail'),
            type: 'error',
            duration: 2000
          })
        })
      }
    },
    formatSubmitData() {
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      this.selectedApplicationObject.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          this.temp.objectIds.push(nodeData.dataId)
        } else {
          this.temp.objectGroupIds.push(nodeData.dataId)
        }
      })
      const checkedIds = []
      const checkedNodes = this.moduleTree.getCheckedNodes()
      checkedNodes.forEach(node => {
        if (node.id.indexOf('G') < 0) {
          checkedIds.push(node.dataId)
          if (node.dataId == 158) { // 关闭程序动作监控(158)，会连带关闭 程序动作监控记录(159)
            checkedIds.push(159)
          }
        }
      })
      this.temp.filterSubModules = checkedIds.join(',')
    },
    formatBatchSubmitData() {
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      this.selectedApplicationObject.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          this.temp.objectIds.push(nodeData.dataId)
        } else {
          this.temp.objectGroupIds.push(nodeData.dataId)
        }
      })
      const checkedIds = []
      const checkedNodes = this.batchModuleTree.getCheckedNodes()
      checkedNodes.forEach(node => {
        if (node.id.indexOf('G') < 0) {
          checkedIds.push(node.dataId)
          if (node.dataId == 158) { // 关闭程序动作监控(158)，会连带关闭 程序动作监控记录(159)
            checkedIds.push(159)
          }
        }
      })
      this.temp.filterSubModules = checkedIds.join(',')
    },
    handleDelete(row) {
      this.$confirmBox(this.$t('pages.moduleFilter_confirmMsg1'), this.$t('text.prompt')).then(() => {
        const tempObj = {
          id: row.id
        }
        deleteModuleFilter(tempObj).then(respond => {
          this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.clearSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.checkedModuleFilterIds = []
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleRefresh() {
      return refreshPage(this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleImport() {},
    handleExport() {},
    formatFormObject(objData) {
      const { objectId, objectType } = objData
      this.temp.checkedObjectKeys.splice(0)
      if (objectType == 3) {
        this.temp.checkedObjectKeys.push('G' + objectId)
      } else if (objectType == 1) {
        this.temp.checkedObjectKeys.push('T' + objectId)
      }
    },
    formatClosedModule(row, data) {
      const labels = []
      const moduleFilterIds = data.split(',') || []
      for (const moduleFilterId of moduleFilterIds) {
        if (moduleFilterId == 158) { // 关闭程序动作监控(158)，会连带关闭 程序动作监控记录(159)，但是界面只显示程序动作监控
          labels.push(this.$t('pages.processMonitor'))
          // eslint-disable-next-line no-empty
        } else if (moduleFilterId == 159) {

        } else {
          const label = findNodeLabel(this.moduleTreeData, 'TermFilterNode' + moduleFilterId, 'id')
          labels.push(label)
        }
      }
      return labels.join(', ')
    },
    beforeClose(done) {
      done()
    },
    checkedObjKeysChange(keys, options) {
      this.selectedApplicationObject.splice(0, this.selectedApplicationObject.length, ...options)
    },
    checkedModuleFilterKeysChange(curCheckedData, checkedAllDatas) {

    },
    selectAll(boolean) {
      this.checkedModuleFilterIds = []
      if (boolean) {
        this.moduleTreeData.forEach(data => {
          const children = data.children
          if (children && children.length > 0) {
            children.forEach(child => {
              this.checkedModuleFilterIds.push('TermFilterNode' + child.dataId)
            })
          }
        })
      }
    },
    filterModuleSelectedChange(idList, dataList) {
      this.query.filterModuleIds = dataList.filter(data => data.type !== 'G').map(data => data.dataId)
    }
  }
}
</script>

<style lang='scss' scoped>
  >>>.el-checkbox__label{
    margin-left: 10px;
    padding-left: 0;
  }
  .search >>>.el-checkbox__label{
    background: yellow;
  }
  >>>.el-card__body{
    padding: 20px 15px 0;
    height: calc(100% - 39px);
  }
  >>>.my-draw{
    .el-drawer__header{
      padding: 10px 20px;
    }
  }
</style>
