<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleFilter">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button v-show="isRecoverAuth" style="margin-left: 10px" type="primary" size="mini" @click="decodeTool">
          {{ $t('pages.databaseBackupRecord_decryTool') }}
        </el-button>
      </div>

      <div class="table-container">
        <div style=" position: relative">
          <div style="width: 100%; float: left;">
            <el-tabs ref="tabs" v-model="tabLabel" type="card" style="height: 40px; border-width: 1px;" @tab-click="tabClick">
              <el-tab-pane :label="$t('pages.databaseBackupRecord_localBackup')" name="localBackup"/>
              <!-- 云平台功能隐藏 -->
              <el-tab-pane v-if="false" :label="$t('pages.databaseBackupRecord_cloudBackup')" name="cloudBackup"/>
            </el-tabs>
          </div>
          <div v-if="tabLabel === 'localBackup'" style="position: absolute; width: 35%; right: 0.5%;">
            <el-tabs style="width: 100%; border: 0;">
              <div style="display: flex">
                <el-button style="margin-right: 5px" size="mini" @click="updateShareAddressElg">
                  {{ $t('pages.databaseBackupRecord_updateBackupAddress') }}
                </el-button>
                <el-tooltip :content="localPath" placement="top-start">
                  <el-input v-model="localPath" type="text" :disabled="true" style="width: 80%" :placeholder="$t('pages.databaseBackupRecord_backupPathPlaceholder')"/>
                </el-tooltip>
                <el-button style="margin-left: 5px" type="primary" icon="el-icon-search" size="mini" @click="getLocalList">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </el-tabs>
          </div>
        </div>

        <grid-table
          v-show="tabLabel === 'localBackup'"
          ref="localBackupList"
          :multi-select="false"
          :col-model="localColModel"
          :default-sort="{ prop: 'createTime' }"
          :row-datas="rowDatas"
          :loading="listSubmitting"
          :show-pager="false"
        />
        <grid-table
          v-show="tabLabel === 'cloudBackup'"
          ref="cloudBackupList"
          :multi-select="false"
          :col-model="cloudColModel"
          :default-sort="{ prop: 'createTime' }"
          :row-datas="cloudRowDatas"
          :loading="listSubmitting"
          :show-pager="false"
        />
      </div>
    </div>

    <!-- 系统管理员一键恢复授权弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.databaseBackupRecord_onceRecoverAuth')"
      :visible.sync="recoverVisible"
      width="500px"
    >
      <Form ref="recoverForm" :model="temp" :rules="rules" label-width="80px" label-position="right" size="mini">
        <el-card class="box-card" style="background-color: transparent;padding-top: 10px" :body-style="{ padding: '10px 40px' }">
          <div slot="header">
            <span>{{ $t('pages.databaseBackupRecord_secAdmin') }}</span>
          </div>
          <FormItem :label="$t('pages.databaseBackupRecord_account')">
            <el-input v-model="temp.secAccount" disabled prefix-icon="el-icon-user" style="width: 250px" clearable/>
          </FormItem>
          <FormItem :label="$t('pages.databaseBackupRecord_password')" prop="secPassword">
            <el-input v-model="temp.secPassword" prefix-icon="el-icon-lock" style="width: 250px" type="password" show-password clearable/>
          </FormItem>
        </el-card>

        <el-card class="box-card" style="background-color: transparent;padding-top: 10px" :body-style="{ padding: '10px 40px' }">
          <div slot="header">
            <span>{{ $t('pages.databaseBackupRecord_audAdmin') }}</span>
          </div>
          <FormItem :label="$t('pages.databaseBackupRecord_account')">
            <el-input v-model="temp.audAccount" disabled prefix-icon="el-icon-user" style="width: 250px" clearable/>
          </FormItem>
          <FormItem :label="$t('pages.databaseBackupRecord_password')" prop="audPassword">
            <el-input v-model="temp.audPassword" prefix-icon="el-icon-lock" type="password" show-password style="width: 250px" clearable/>
          </FormItem>
        </el-card>
        <el-button style="margin-left: 10px; float: right" size="mini" @click="recoverData">
          {{ $t('pages.databaseBackupRecord_btnRecover') }}
        </el-button>
        <el-button style="margin-left: 10px; float: right" type="primary" size="mini" @click="recoverCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </Form>
    </el-dialog>

    <!-- 加解密备份文件下载 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.databaseBackupRecord_backupFileDownload')"
      :visible.sync="downloadFileVisible"
      width="500px"
      @close="closeDownloadFileElg"
    >
      <Form label-width="80px" label-position="right" size="mini">
        <FormItem :label="$t('pages.databaseBackupRecord_fileType')" prop="audPassword">
          <el-radio-group v-model="isDecode">
            <el-radio :value="0" :label="0">{{ $t('pages.databaseBackupRecord_encryFile') }}</el-radio>
            <el-radio :disabled="!isRecoverAuth" :value="1" :label="1">{{ $t('pages.databaseBackupRecord_decryFile') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <el-button style="margin-left: 10px; float: right" size="mini" @click="closeDownloadFileElg">
          {{ $t('button.return') }}
        </el-button>
        <el-button style="margin-left: 10px; float: right" type="primary" size="mini" @click="downloadFile">
          {{ $t('button.download') }}
        </el-button>
      </Form>
    </el-dialog>

    <backup-file-decode ref="backupFileDecode"/>

    <!-- 版本号对比 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="isSame ? $t('pages.databaseBackupRecord_systemVersion') : $t('pages.databaseBackupRecord_systemVersionCt')"
      :visible.sync="fileVersionVisible"
      width="500px"
      @close="closeFileVersionElg"
    >
      <Form>
        <FormItem v-if="fileStatusMessage != null && fileStatusMessage !== ''">
          <span style="color: red">
            {{ $t('pages.databaseBackupRecord_attention') }}：{{ fileStatusMessage }}
          </span>
        </FormItem>
        <FormItem v-if="isSame" :label="$t('pages.databaseBackupRecord_backupFileDlpSystemVersion')">
          {{ fileDlpVersion }}
        </FormItem>
        <FormItem v-if="!isSame" :label="$t('pages.databaseBackupRecord_backupFileDlpSystemVersion')" style="color: red">
          {{ fileDlpVersion }}
        </FormItem>
        <FormItem :label="$t('pages.databaseBackupRecord_dlpSystemVersion')">
          {{ sysDlpVersion }}
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="fileVersionLoading" @click="fileVersionConfirm(1)" >{{ $t('pages.databaseBackupRecord_btnRecover') }}</el-button>
        <el-button @click="fileVersionConfirm(2)">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 修改本地地址信息 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.databaseBackupRecord_backupAddress')"
      :visible.sync="shareVisible"
      width="500px"
      @close="shareClose"
    >
      <Form
        ref="dataForm"
        :rules="shareRules"
        :model="shareForm"
        :hide-required-asterisk="true"
        label-width="120px"
      >
        <!-- 备份路径类型 -->
        <FormItem key="localType" :label="$t('pages.manageLibrary_backupPathType')" style="margin-right: 10px;" >
          <el-select v-model="shareForm.localType" style="width: 300px" @change="localTypeChange">
            <el-option :key="1" :value="1" :label="$t('pages.manageLibrary_localDiskToEngine')"/>
            <el-option v-if="isSupportShare" :key="2" :value="2" :label="$t('pages.manageLibrary_shareDisk')"/>
          </el-select>
        </FormItem>

        <!-- IP地址 -->
        <FormItem v-if="shareForm.localType === 2" key="lpRemoteName" prop="lpRemoteName" :label="$t('pages.manageLibrary_ipAddress')">
          <el-input v-model="shareForm.lpRemoteName" style="width: 300px" clearable maxlength @change="testFlag = true"></el-input>
        </FormItem>

        <!-- 用户名 -->
        <FormItem v-if="shareForm.localType === 2" key="lpUserName" prop="lpUserName" :label="$t('pages.manageLibrary_username')">
          <el-input v-model="shareForm.lpUserName" style="width: 300px" clearable maxlength @change="testFlag = true"></el-input>
        </FormItem>

        <!-- 密码 -->
        <FormItem v-if="shareForm.localType === 2" key="lpPassword" prop="lpPassword" :label="$t('pages.manageLibrary_loginPassword')">
          <el-input v-model="shareForm.lpPassword" type="password" style="width: 300px" maxlength @input.native="inputPwd($event)" @change="testFlag = true"></el-input>
        </FormItem>

        <!-- 共享网络路径 -->
        <FormItem v-show="shareForm.localType === 2" key="path" prop="path" :label="$t('pages.manageLibrary_shareDiskAddress')">
          <span slot="label">
            <el-tooltip class="item" effect="dark" :content="$t('pages.manageLibrary_shareDiskAddress') + '：' + $t('pages.manageLibrary_helpMessage1')" placement="top-start">
              <i class="el-icon-question" style="color: #409EFF"/>
            </el-tooltip>
            <label>{{ $t('pages.manageLibrary_shareDiskAddress') }}</label>
          </span>
          <el-input v-model="shareForm.path" style="width: 300px" clearable maxlength @change="testFlag = true"></el-input>
        </FormItem>

        <!-- 本地磁盘 -->
        <FormItem v-show="isWindows && shareForm.localType !== 2" key="devId" :label="$t('pages.manageLibrary_localDisk')" prop="devId">
          <el-select v-model="shareForm.devId" style="width: 300px" @change="getDiskContent">
            <el-option v-for="item in serverDiskInfoList" :key="item.devId" :value="item.keyId" :label="item.diskName"/>
          </el-select>
        </FormItem>

        <!-- 磁盘信息 -->
        <FormItem v-show="isWindows && shareForm.localType !== 2 && devContent !== ''">
          <div style="width: 300px">
            {{ devContent }}
          </div>
        </FormItem>

        <!-- 本地备份路径 -->
        <FormItem v-if="shareForm.localType !== 2" key="localPath" prop="localPath" :label="$t('pages.databaseBackupRecord_backupAddress1')">
          <span slot="label">
            <el-tooltip class="item" effect="dark" :content="$t('pages.databaseBackupRecord_backupAddress1') + '：' + localPathTooltip" placement="top-start">
              <i class="el-icon-question" style="color: #409EFF"/>
            </el-tooltip>
            <label>{{ $t('pages.databaseBackupRecord_backupAddress1') }}</label>
          </span>
          <el-input v-model="shareForm.localPath" style="width: 300px" clearable maxlength @change="testFlag = true"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <span v-if="shareForm.localType === 2" slot="label">
          <el-tooltip class="item" effect="dark" :content="$t('pages.manageLibrary_testConnectionHelpMessage')" placement="top-start">
            <i class="el-icon-question" style="color: #409EFF"/>
          </el-tooltip>
        </span>
        <el-button
          v-if="shareForm.localType === 2"
          id="test"
          type="primary"
          size="mini"
          :disabled="isAlloweTestArr.length !== 3"
          :loading="testLoading"
          @click="test"
        >{{ $t('pages.manageLibrary_testConnection') }}</el-button>
        <el-button type="primary" :disabled="testFlag && shareForm.localType === 2" @click="shareConfirm" >{{ $t('button.confirm') }}</el-button>
        <el-button @click="shareClose">{{ $t('button.return') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  backupRecover, backupRecoverByPassword, confirmRecover, downloadFile,
  downloadProclaimedInWritingFile, getCloudList, getLocalBackupPath, getLocalList,
  pretreatmentRecover, removeIsRecovering, removeMessage, removeProcess, testConnection, getCurrentDataBaseServerInfo
} from '@/api/system/terminalManage/manageLibrary'
import BackupFileDecode from '@/views/system/terminalManage/databaseBackupRecord/backupFileDecode'
import { aesEncode, formatAesKey } from '@/utils/encrypt';
import { getToken } from '@/utils/auth';
import { isIPv4, isIPv6 } from '@/utils/validate'

export default {
  name: 'DatabaseBackupRecord',
  components: { BackupFileDecode },
  props: {
    tabName: { type: String, default: 'localBackup' }
  },
  data() {
    const ipValidator = (rule, value, callback) => {
      if (this.shareForm.localType === 2) {
        this.deleteShareTestConnection('lpRemoteName')
      }
      this.shareForm.lpRemoteName = this.shareForm.lpRemoteName ? this.shareForm.lpRemoteName.trim() : ''
      if (this.shareForm.lpRemoteName === null || this.shareForm.lpRemoteName === '') {
        callback(new Error(this.$t('pages.manageLibrary_ipAddressNotNull')));
        return;
      }
      //  支持IPv4，Ipv6
      const flag = isIPv4(this.shareForm.lpRemoteName) || isIPv6(this.shareForm.lpRemoteName);
      // const flag = isIPv4(this.shareForm.lpRemoteName)
      if (!flag) {
        callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')))
      } else {
        if (this.shareForm.localType === 2) {
          this.addShareTestConnection('lpRemoteName')
        }
        callback()
      }
    }
    const lpUserNameValidator = (rule, value, callback) => {
      this.deleteShareTestConnection('lpUserName')
      if (value.length === 0) {
        callback(new Error(this.$t('pages.manageLibrary_usernameValidMessage')))
      } else {
        this.addShareTestConnection('lpUserName')
        callback();
      }
    }
    const pathValidator = (rule, value, callback) => {
      if (this.shareForm.localType === 1) {
        this.localPathValidator(value, callback, 0);
      }
      if (this.shareForm.localType === 2) {
        this.sharePathValidator(value, callback);
      }
    }
    const devIdValidator = (rule, value, callback) => {
      if (this.isWindows && this.shareForm.localType && this.shareForm.localType === 1) {
        if (this.shareForm.devId === null || this.shareForm.devId === '') {
          callback(new Error(this.$t('pages.manageLibrary_diskNotNull')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      localColModel: [
        { prop: 'origFileName', label: 'fileName', width: '150', sort: true },
        { prop: 'fileSize', label: 'maxFileSize2', width: '150', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'createTime', label: 'createTime', width: '100', sort: true },
        { label: 'operate', type: 'button', width: '100', fixed: 'right', hidden: () => { return !this.isRecoverAuth },
          buttons: [
            // { label: 'download', click: this.downloadFileElg },
            { label: 'oneKeyRecovery', click: this.recover, isShow: () => { return this.isRecoverAuth } }
          ]
        }
      ],
      cloudColModel: [
        { prop: 'origFileName', label: 'fileName', width: '150', sort: true },
        { prop: 'fileSize', label: 'maxFileSize2', width: '150', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'createTime', label: 'createTime', width: '100', sort: true },
        { label: 'operate', type: 'button', width: '100', fixed: 'right',
          buttons: [
            { label: 'download', click: this.downloadFileElg },
            { label: 'oneKeyRecovery', click: this.recover, isShow: () => { return this.isRecoverAuth } }
          ]
        }
      ],
      rowDatas: [],
      cloudRowDatas: [],
      tabLabel: this.tabName,
      recoverVisible: false,
      temp: {
        backupFileName: '',   //  本地备份文件名称
        backupFileId: '',   //  云平台备份文件Id
        secAccount: 'SecAdmin',
        secPassword: '',
        audAccount: 'AudAdmin',
        audPassword: '',
        backupType: 1,  //  备份类型，1：本地，2：云平台
        backFilePath: ''  //  本地备份文件路径  本地备份路径 + 文件名称
      },
      rules: {
        secPassword: [{ required: true, message: this.$t('pages.databaseBackupRecord_validateMsgPassword'), trigger: 'blur' }],
        audPassword: [{ required: true, message: this.$t('pages.databaseBackupRecord_validateMsgPassword'), trigger: 'blur' }]
      },
      isRecoverAuth: false,  //  是否具有一键恢复权限
      downloadFileVisible: false,
      isDecode: 0,   //  默认下载加密文件
      tempRow: null, //  临时存储备份文件的数据
      decodeVisible: false,  //  解密工具

      local: {
        path: '',
        type: null,  // 备份类型 1：本地磁盘，2：window共享目录，3：linux
        lpRemoteName: '', //  共享目录所在机子的IP地址  \\**************[注]备份地址的开头需与lpRemoteName一样
        lpUserName: '',  //  共享目录所在机子的登录密码
        lpPassword: '', //  共享目录所在机子的用户名
        devId: ''
      },
      localPath: '',  //  本地备份路径（引擎服务器）
      isWindows: true,  //   os类型是否为windows
      isLinux: false,   //   os类型是否为linux
      supportShare: ['windows'], //  当前仅windows支持共享目录，若linux需要支持则添加linux，可填值为：windows/linux/mac
      // defaultWindows: true, //  默认为windows,  如果支持linux后，此默认值需要删除
      listSubmitting: false,

      //  文件版本对比
      fileVersionVisible: false,
      isSame: true,
      sysDlpVersion: '',  //  DLP系统版本
      fileDlpVersion: '',  //  备份文件DLP系统版本
      fileVersionLoading: false,
      fileStatus: null,   //  文件状态
      fileStatusMessage: '',  //  文件提示信息

      //  共享文件夹和本地磁盘
      shareVisible: false,
      shareForm: {
        localPath: '',
        localType: null,
        path: '',
        lpRemoteName: '', //  共享目录所在机子的IP地址  \\**************[注]备份地址的开头需与lpRemoteName一样
        lpPassword: '', //  共享目录所在机子的用户名
        lpUserName: '',  //  共享目录所在机子的登录密码
        devId: ''
      },
      shareRules: {
        lpRemoteName: [
          { required: true, validator: ipValidator, trigger: 'blur' }
        ],
        lpUserName: [
          { required: true, validator: lpUserNameValidator, trigger: 'blur' }
        ],
        path: [
          { required: true, validator: pathValidator, trigger: 'blur' }
        ],
        localPath: [
          { required: true, validator: pathValidator, trigger: 'blur' }
        ],
        devId: [{ required: true, validator: devIdValidator, trigger: 'change' }]
      },
      isAlloweTestArr: [], //  表示是否允许测试连接  共享文件夹测试连接共需要测试ip，用户名，路径
      testFlag: true,   //  表示测试连接成功
      testLoading: false,
      serverDiskInfoList: [],  //  服务器磁盘信息
      devContent: '',  //  磁盘信息
      hasChangePwd: false,
      encryptPwd: ''
    }
  },
  computed: {
    localBackupList() {
      return this.$refs['localBackupList']
    },
    cloudBackupList() {
      return this.$refs['cloudBackupList']
    },
    //  是否支持共享文件夹
    isSupportShare() {
      const osType = this.$store.getters.engineOsType || ''
      return this.supportShare.includes(osType)
    },
    //  获取本地备份路径提示信息
    localPathTooltip() {
      let content = ''
      if (this.isWindows) {
        content = this.$t('pages.manageLibrary_localPathTooltip1') || ''
      } else if (this.isLinux) {
        content = this.$t('pages.manageLibrary_localPathTooltip2') || ''
      }
      return content;
    }
  },
  watch: {
    'shareForm.devId'(val) {
      if (this.isWindows && val !== null && val !== undefined) {
        this.getDiskContent()
      }
    }
  },
  created() {
    //  配置操作系统类型
    const osType = this.$store.getters.engineOsType || ''
    if (osType === 'windows') {
      this.isWindows = true
      this.isLinux = false
    } else if (osType === 'linux') {
      this.isWindows = false
      this.isLinux = true
    }
  },
  methods: {
    fileSizeFormatter(row, data) {
      return row.fileSizeLabel || ''
    },
    init() {
      this.getServerInfo()
      this.getRecoverAuth()
      const _this = this
      getLocalBackupPath().then(res => {
        _this.setLocal(res.data)
        _this.localPath = this.local.type === 2 ? '\\\\' + this.local.lpRemoteName + '\\' + this.local.lpUserName + '\\' + this.local.path : this.local.path || ''
        if (_this.localPath.length === 0) {
          this.$message({
            message: this.$t('pages.databaseBackupRecord_backupPathPlaceholder'),
            type: 'warning'
          })
        } else {
          this.getList()
        }
      });
    },
    //   设置本地信息
    setLocal(data) {
      if (data !== undefined && data !== null) {
        this.local.lpRemoteName = data.lpRemoteName || ''
        this.local.type = data.type === undefined || data.type === null ? 1 : data.type
        this.local.path = data.path || ''
        this.local.lpUserName = data.lpUserName || ''
        this.local.lpPassword = data.lpPassword || ''
        this.encryptPwd = this.local.lpPassword
        this.local.lpPassword && (this.local.lpPassword = '     ')
        this.hasChangePwd = false
      }
    },
    getCloudList() {
      this.cloudRowDatas = []
      this.listSubmitting = true
      getCloudList().then(res => {
        this.cloudRowDatas = res.data || []
        this.listSubmitting = false
      }).catch(() => {
        this.listSubmitting = false
      })
    },
    handleFilter() {
      this.getList()
    },
    downloadFileElg(row) {
      this.tempRow = row
      this.downloadFileVisible = true
    },
    closeDownloadFileElg() {
      this.downloadFileVisible = false
      this.tempRow = null
      this.isDecode = 0;
    },
    downloadFile() {
      const row = this.tempRow
      if (row.id) {
        this.isDecode === 0 ? downloadFile({ id: row.id, fileName: row.origFileName })
          : downloadProclaimedInWritingFile({ id: row.id, fileName: row.origFileName })
      } else {
        this.$message({
          message: this.$t('pages.databaseBackupRecord_hintMessage1'),
          type: 'error',
          duration: 2000
        })
      }
    },
    recover(row) {
      //  设置备份类型（1：本地，2：云平台）
      if (this.tabLabel === 'localBackup') {
        this.temp.backupType = 1
      } else if (this.tabLabel === 'cloudBackup') {
        this.temp.backupType = 2
      }
      this.temp.backupFileName = row.origFileName || ''
      pretreatmentRecover().then(res => {
        if (res.code === 20000) {
          this.temp.backupFileId = row.id || ''
          //  超级管理员执行一键恢复
          if (res.data === 1) {
            this.$confirmBox(this.$t('pages.databaseBackupRecord_confirmMessage1'), this.$t('text.prompt')).then(() => {
              const temp = this.setRecoverForm()
              //  判断dlp系统版本和备份文件中的dlp版本
              this.getBackupFileSystemVersion();
              backupRecover(temp);
              //  开启一键恢复监听socket
              // this.recoverAfter()
            }).catch(() => {})
            //  系统管理员执行一键恢复
          } else if (res.data === 2) {
            this.recoverVisible = true
          } else {
            this.$message({
              message: this.$t('pages.databaseBackupRecord_notAuth'),
              type: 'error',
              duration: 2000
            });
          }
        }
      })
    },
    //  恢复数据
    recoverData() {
      this.$refs['recoverForm'].validate((valid) => {
        if (valid) {
          this.$confirmBox(this.$t('pages.databaseBackupRecord_confirmMessage1'), this.$t('text.prompt')).then(() => {
            const temp = this.setRecoverForm()
            //  判断dlp系统版本和备份文件中的dlp版本
            this.getBackupFileSystemVersion();
            backupRecoverByPassword(temp).then(res => {
              this.recoverVisible = false
              this.clearRecover()
            })
          }).catch(() => {})
        }
      })
    },
    setRecoverForm() {
      const temp = JSON.parse(JSON.stringify(this.temp));
      //  将密码进行加密
      const secPwd = aesEncode(temp.secPassword, formatAesKey('tr838408', temp.secAccount.trim()))
      const audPwd = aesEncode(temp.audPassword, formatAesKey('tr838408', temp.audAccount.trim()))
      temp.secPassword = secPwd
      temp.audPassword = audPwd
      if (this.tabLabel === 'localBackup') {
        temp.backupType = 1
        //  共享文件夹
        if (this.local.type === 2) {
          temp.backFilePath = '\\\\' + this.local.lpRemoteName + '\\' + this.local.path + '\\' + temp.backupFileName
          //  非共享文件夹
        } else {
          if (this.isWindows) {
            temp.backFilePath = this.localPath + '\\' + temp.backupFileName
          } else {
            temp.backFilePath = this.localPath + '/' + temp.backupFileName
          }
        }
      } else if (this.tabLabel === 'cloudBackup') {
        temp.backupType = 2
      }
      return temp
    },
    recoverCancel() {
      this.recoverVisible = false
      this.clearRecover()
    },
    clearRecover() {
      this.temp.audPassword = ''
      this.temp.secPassword = ''
      this.temp.backupFileId = ''
    },
    //  转换成byte
    stringToByte(str) {
      const re = []
      let idx;
      for (let i = 0; i < str.length; i++) {
        idx = str.charCodeAt(i);
        if (idx & 0xff00) {
          re.push(idx >> 8);
          re.push(idx & 0xff);
        } else {
          re.push(idx);
        }
      }
      return re;
    },
    getRecoverAuth() {
      pretreatmentRecover().then(res => {
        this.isRecoverAuth = res.data > 0
      }).catch(() => { this.isRecoverAuth = false })
    },
    //  文件解密
    decodeTool() {
      this.$refs['backupFileDecode'].show()
    },

    //  改变备份类型
    tabClick(tab) {
      this.init()
    },
    //  本地备份列表获取
    getLocalList() {
      //  校验路径是否正确
      this.rowDatas = []
      if (this.local.type === 2 || this.localPathValidator(this.local.path, null, 1)) {
        this.listSubmitting = true
        this.getLocalListSocketApi();
        const query = this.local.type !== 2 ? { path: this.localPath, type: this.local.type } : this.encryptPassword(this.local, 'lpPassword')
        getLocalList(query);
      }
    },
    //  获取本地备份文件列表
    getLocalListSocketApi() {
      this.$socket.subscribeToUser('localBackupTaskId', '/databaseBackup/getLocalBackupList', (resp, handle) => {
        this.rowDatas = resp.data || []
        this.listSubmitting = false
        handle.close();
      }, false);
    },
    //  判断dlp系统版本和备份文件中的dlp版本
    getBackupFileSystemVersion() {
      this.$socket.subscribeToUser('localBackupTaskId', '/databaseBackup/getBackupFileSystemVersion', (resp, handle) => {
        handle.close();
        this.fileStatus = resp.data.fileStatus || null;
        if (this.fileStatus !== null) {
          if ([2, 3, 5].includes(this.fileStatus)) {
            this.$message({
              message: this.getFileStatusMessage(this.fileStatus),
              type: 'error',
              duration: 1000
            });
            return;
          }
        }
        //  版本号是否一致
        this.isSame = resp.data.isSame === undefined || resp.data.isSame === null ? true : resp.data.isSame;
        this.fileStatusMessage = this.getErrorFileStatusMessage(1, this.fileStatus, this.isSame)
        this.sysDlpVersion = resp.data.sysDlpVersion
        this.fileDlpVersion = resp.data.fileDlpVersion
        //  展示版本号
        this.fileVersionVisible = true
        this.sysDlpVersion = resp.data.sysDlpVersion
        this.fileDlpVersion = resp.data.fileDlpVersion
      }, false)
    },
    //  根据文件状态获取提示信息
    getFileStatusMessage(fileStatus) {
      let message = ''
      if (fileStatus !== null) {
        switch (fileStatus) {
          case 2: message = this.$t('pages.databaseBackupRecord_fileError'); break;
          case 3: message = this.$t('pages.databaseBackupRecord_onlineDownloadError'); break;
          case 4: message = this.$t('pages.databaseBackupRecord_fileUnEncryptedHintMessage'); break;
          case 5: message = this.$t('pages.databaseBackupRecord_productCodeNoDifference'); break;
        }
      }
      return message;
    },
    //  获取错误信息(即，版本不一致提示信息和文件未加密信息）
    getErrorFileStatusMessage(type, fileStatus, isSame) {
      let message = ''
      //  type:1, 冲突弹窗中的提示信息，  type2:点击恢复按钮后的警告弹窗提示信息
      if (type === 1) {
        //  如果文件未加密，进行提示
        if (fileStatus === 4) {
          message += this.getFileStatusMessage(fileStatus)
        }
        //  若版本号不一致，额外添加提示语
        if (!isSame) {
          message += ' ' + this.$t('pages.databaseBackupRecord_systemVersionNoDifference')
        }
      } else if (type === 2) {
        //  如果文件未加密，进行提示
        if (fileStatus === 4) {
          message += this.$t('pages.databaseBackupRecord_fileUnEncryptedHintMessage1')
        }
        //  若版本号不一致，额外添加提示语
        if (!isSame) {
          message += this.$t('pages.databaseBackupRecord_systemVersionNoDifference1')
        }
        message += this.$t('pages.databaseBackupRecord_isContinueToRecover')
      }
      return message;
    },
    //  一键恢复后执行的操作
    async recoverAfter() {
      //  清空缓存
      removeIsRecovering()
      removeMessage()
      removeProcess()
      await this.clearToken()
      //  跳转至静态界面
      this.$router.push({ path: '/recovering' });
    },
    //  清空token
    async clearToken() {
      const token = getToken()
      if (token) {
        await this.$store.dispatch('user/logout')
        this.$store.dispatch('user/setToken', '')
        this.$store.dispatch('commonData/clearWsNoticeInfo')
        this.$store.dispatch('permission/clearCommonRoute')
        await this.$store.dispatch('tagsView/delAllViews')
      }
    },
    getList() {
      this.listSubmitting = false
      if (this.tabLabel === 'localBackup') {
        this.getLocalList()
        //  获取本地备份列表
      } else if (this.tabLabel === 'cloudBackup') {
        this.getCloudList()
      }
    },
    fileVersionConfirm(status) {
      if (status === 1 && (this.fileStatus === 4 || !this.isSame)) {
        let message = this.getErrorFileStatusMessage(2, this.fileStatus, this.isSame)
        message = "<span style='color:red'>" + message + '</span>'
        this.$confirmBox(message, this.$t('text.warning'), {
          confirmButtonText: this.$t('pages.databaseBackupRecord_continueToRecover'),
          type: 'warning',
          dangerouslyUseHTMLString: true
        }).then(_ => {
          confirmRecover({ recoverNotice: 1, backupFileName: this.temp.backupFileName, backupFileLocation: this.temp.backupType === 1 ? this.local.type === 2 ? 3 : 1 : this.temp.backupType });
          this.recoverSocket();
          this.closeFileVersionElg()
        }).catch(_ => {
          confirmRecover({ recoverNotice: 2, backupFileName: this.temp.backupFileName, backupFileLocation: this.temp.backupType === 1 ? this.local.type === 2 ? 3 : 1 : this.temp.backupType });
          this.closeFileVersionElg()
        });
        return;
      }
      confirmRecover({ recoverNotice: status, backupFileName: this.temp.backupFileName, backupFileLocation: this.temp.backupType === 1 ? this.local.type === 2 ? 3 : 1 : this.temp.backupType });
      if (status === 1) {
        this.recoverSocket();
      }
      this.closeFileVersionElg()
    },
    //  关闭文件版本号信息
    closeFileVersionElg() {
      this.fileVersionVisible = false
      this.fileDlpVersion = ''
      this.sysDlpVersion = ''
      this.fileStatus = null
      this.fileStatusMessage = ''
    },
    recoverSocket() {
      const _this = this
      this.$socket.subscribeToUserTimeOut('localBackupTaskId', '/databaseBackup/backupRecover', (resp, handle) => {
        handle.close();
        const data = JSON.parse(resp.body) || {};
        //  进行恢复
        if (data.status === 4) {
          _this.recoverAfter()
        } else if (data.status === 5) {
          //  恢复时，数据库正在进行数据库备份，中止恢复
          this.$message({
            message: this.$t('pages.databaseBackupRecord_fileStatus5'),
            type: 'error',
            duration: 2000
          });
        } else {
          this.$message({
            message: this.$t('pages.databaseBackupRecord_notRecoverRefreshMessage'),
            type: 'error',
            duration: 1000
          });
        }
        //  1 表示获取恢复状态
      }, false, (handle) => {
        handle.close()
        this.$message({
          message: this.$t('pages.databaseBackupRecord_noAuthMessage'),
          type: 'error',
          duration: 1000
        });
      })
    },

    //  共享盘符
    //  修改共享文件夹
    updateShareAddressElg() {
      this.shareForm.localType = this.local.type || 1
      this.shareForm.lpRemoteName = this.local.lpRemoteName || ''
      this.shareForm.lpUserName = this.local.lpUserName || ''
      this.shareForm.lpPassword = this.local.lpPassword || ''
      this.shareForm.path = this.shareForm.localType === 2 ? this.local.path || '' : ''
      if (this.shareForm.localType === 1) {
        //  若为windows，解析路径
        if (this.isWindows) {
          const split = this.local.path.split(':\\\\')
          if (split.length > 0) {
            this.shareForm.devId = split[0].toUpperCase() || null
            this.shareForm.localPath = split[1]
          }
        } else if (this.isLinux) {
          this.shareForm.localPath = this.local.path
        }
      } else {
        this.shareForm.localPath = ''
      }
      if (this.shareForm.localType === 2) {
        this.validTestField()
      }
      //  读取配置文件
      this.shareVisible = true
      this.hasChangePwd = false
    },
    localPathValidatorRule(localPath, type) {
      //  校验路径不能为空字符串，
      //  windows下，文件夹名称不能有空格，文件夹名称只能是数字，字母，中文，反斜杠；
      let windowsPattern
      if (type) {
        windowsPattern = /([a-zA-z]:\\){1}(\\{1}[0-9a-zA-Z\u4e00-\u9fa5_]+)*$/g;
      } else {
        windowsPattern = /(\\{0,1}[0-9a-zA-Z\u4e00-\u9fa5_]+)*$/g;
      }
      //  linux下，文件夹名称不能有空格，文件夹名称只能是数字，字母，斜杠
      const linuxPattern = /^\/(\w+\/?)+$/;
      const pattern = this.isWindows ? windowsPattern : linuxPattern;
      let flag = false;
      if (localPath !== '') {
        const flagArray = localPath.match(pattern) || []
        for (let i = 0; i < flagArray.length; i++) {
          if (flagArray[i] === localPath) {
            flag = true;
            break;
          }
        }
      }
      return flag;
    },
    //  本地磁盘路径校验    type： 1：表示校验包含盘符（C:\\）的， 0：表示不包含盘符的校验， 默认为0
    localPathValidator(value, callback, type) {
      let localPath = value || ''
      localPath = localPath.trim()
      if (localPath === '') {
        if (callback) {
          callback(new Error(this.$t('pages.databaseBackupRecord_backupAddressNotNull')))
          return;
        } else {
          this.$message({
            message: this.$t('pages.databaseBackupRecord_backupAddressNotNull'),
            type: 'error'
          })
          return false;
        }
      }
      if (!this.localPathValidatorRule(localPath, type)) {
        if (callback) {
          callback(new Error(this.$t('pages.manageLibrary_ValidMessage1')))
          return;
        } else {
          this.$message({
            message: this.$t('pages.manageLibrary_ValidMessage1'),
            type: 'error'
          })
          return false;
        }
      }
      return true;
    },
    //  共享盘符
    sharePathValidator(value, callback) {
      this.deleteShareTestConnection('path')
      //  windows下，文件夹名称不能有空格，文件夹名称只能是数字，字母，中文,反斜杠；
      const windowsPattern = /(([0-9a-zA-Z\u4e00-\u9fa5_]{1})||([0-9a-zA-Z\u4e00-\u9fa5_]+\\{0,1}[0-9a-zA-Z\u4e00-\u9fa5_]+))*$/g;
      if (this.shareForm.path.length === 0) {
        callback(new Error(this.$t('pages.manageLibrary_addressNotNull')))
        return;
      }
      let flag = false;
      if (this.shareForm.path.length > 0) {
        const flagArray = this.shareForm.path.match(windowsPattern) || []
        for (let i = 0; i < flagArray.length; i++) {
          if (flagArray[i] === this.shareForm.path) {
            flag = true;
            break;
          }
        }
      }
      if (!flag) {
        callback(new Error(this.$t('pages.manageLibrary_addressNotRules')))
      } else {
        this.addShareTestConnection('path')
        callback();
      }
    },
    //  若为类型为共享文件夹，校验ip地址、用户名、共享网络路径
    validTestField() {
      const field = []
      if (this.shareForm.lpRemoteName) {
        field.push('lpRemoteName')
      }
      if (this.shareForm.lpUserName) {
        field.push('lpUserName')
      }
      if (this.shareForm.path) {
        field.push('path')
      }
      if (field.length > 0) {
        this.$nextTick(() => {
          this.$refs['dataForm'].validateField(field);
        })
      }
    },
    test() {
      const _this = this
      this.testLoading = true;
      const tempData = this.encryptPassword(this.shareForm, 'lpPassword')
      testConnection(tempData).then(res => {
        _this.$message({
          message: this.$t('pages.manageLibrary_testConnectionSuccess'),
          type: 'success',
          duration: 2000
        })
        this.testLoading = false;
        this.testFlag = false
      }).catch(() => { this.testLoading = false; this.testFlag = true })
    },
    shareClose() {
      this.shareVisible = false
      this.shareForm.localType = null
      this.shareForm.localPath = ''
      this.shareForm.lpRemoteName = ''
      this.shareForm.lpUserName = ''
      this.shareForm.lpPassword = ''
      this.shareForm.path = ''
      this.isAllowTestArr = []
      this.$refs['dataForm'].clearValidate();
    },
    shareConfirm() {
      let passFlag = false
      this.$refs['dataForm'].validate((valid) => {
        passFlag = !valid;
      })
      if (passFlag) {
        return;
      }
      this.local.lpRemoteName = this.shareForm.lpRemoteName || ''
      this.local.lpUserName = this.shareForm.lpUserName || ''
      this.local.lpPassword = this.shareForm.lpPassword || ''
      this.local.type = this.shareForm.localType || null
      this.local.path = this.shareForm.localType === 2 ? this.shareForm.path || ''
        : this.isWindows ? (this.shareForm.devId + ':\\\\' + this.shareForm.localPath || '') : this.shareForm.localPath || ''

      this.localPath = this.shareForm.localType === 2 ? '\\\\' + this.shareForm.lpRemoteName + '\\' + this.shareForm.lpUserName + '\\' + this.shareForm.path
        : this.isWindows ? (this.shareForm.devId + ':\\\\' + this.shareForm.localPath || '') : this.shareForm.localPath || ''
      this.shareClose()
      this.handleFilter()
    },
    localTypeChange() {
      this.testFlag = true
      this.isAlloweTestArr = []
      if (this.shareForm.localType === 2) {
        this.validTestField()
      } else {
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate();
        })
      }
    },

    //  磁盘信息区域
    //  获取服务器信息
    getServerInfo() {
      const _this = this
      //  获取操作系统类型和磁盘信息
      getCurrentDataBaseServerInfo().then(res => {
        // const array = [];
        const serverInfo = res.data.serverInfo
        if (serverInfo) {
          let systemName = serverInfo.serverBaseInfo ? serverInfo.serverBaseInfo.systemName || '' : '';

          //  如果systemName 为空，表示后台的操作系统类型没有缓存成功，前端通过socket请求操作系统类型
          if (systemName === '' || serverInfo.serverDiskInfoList === undefined || serverInfo.serverDiskInfoList === null) {
            const data = JSON.parse(localStorage.getItem('backUp_serverBaseInfo'));
            if (data != null && data.serverInfo && data.dataBaseBack) {
              this.setServerDiskInfoList(serverInfo)
              // _this.isWindows = (data.serverInfo && data.serverInfo.serverBaseInfo && data.serverInfo.serverBaseInfo.systemName || '').trim().toLowerCase().toString().indexOf('windows') > -1 || this.defaultWindows
              const count = localStorage.getItem('backUp_usedCacheCount');
              if (count == null) {
                localStorage.setItem('backUp_usedCacheCount', 0)
              } else if (count <= 10) {
                localStorage.setItem('backUp_usedCacheCount', count + 1)
              } else {
                localStorage.removeItem('backUp_serverBaseInfo');
              }
              return
            }
            serverInfo.serverBaseInfo && serverInfo.serverBaseInfo.devId &&
            this.$socket.sendToUser(serverInfo.serverBaseInfo.devId, '/getServerInfo', serverInfo.serverBaseInfo.devId, (respond, handle) => {
              const data = JSON.parse(JSON.stringify(respond.data))
              systemName = data.serverBaseInfo && data.serverBaseInfo.systemName || ''
              // _this.isWindows = systemName.trim().toLowerCase().toString().indexOf('windows') > -1 || this.defaultWindows
              _this.setServerDiskInfoList(serverInfo)
              if (data.serverBaseInfo && data.serverBaseInfo.systemName && data.serverBaseInfo.systemName !== '' && res.data.dataBaseBack) {
                localStorage.setItem('backUp_serverBaseInfo', JSON.stringify(data))
                localStorage.removeItem('backUp_usedCacheCount')
              }
            });
          } else {
            _this.setServerDiskInfoList(serverInfo)
            // _this.isWindows = systemName.trim().toLowerCase().toString().indexOf('windows') > -1 || this.defaultWindows;
            localStorage.setItem('backUp_serverBaseInfo', JSON.stringify(res.data))
            localStorage.removeItem('backUp_usedCacheCount')
          }
        }
      });
    },
    //  设置磁盘信息
    setServerDiskInfoList(serverInfo) {
      const array = [];
      //  判断操作系统类型
      (serverInfo.serverDiskInfoList || []).forEach(item => {
        this.$set(item, 'keyId', item.diskName && item.diskName.length > 4 ? item.diskName.substr(item.diskName.length - 3, 1).toUpperCase() : '');
        array.push(item);
      })
      this.serverDiskInfoList = array
    },
    //  获取当前磁盘信息
    getDiskContent() {
      const temp = this.getDiskNode(this.shareForm.devId)
      if (temp != null) {
        const totalDiskSize = this.getFixed((temp.totalDiskSize || 0) / 1024 / 1024 / 1024) + 'GB';
        const useDiskSize = this.getFixed((temp.useDiskSize || 0) / 1024 / 1024 / 1024) + 'GB';
        const freeSize = this.getFixed((temp.freeSize || 0) / 1024 / 1024 / 1024) + 'GB';
        const usedPercent = (temp.usedPercent || 0) + '%'
        const prama = { 1: totalDiskSize, 2: useDiskSize, 3: freeSize, 4: usedPercent }
        this.devContent = (this.$t('pages.manageLibrary_diskInformation', prama) || '')
      } else {
        this.devContent = this.$t('pages.manageLibrarySave_text3');
      }
    },
    //  根据devId获取磁盘信息
    getDiskNode(keyId) {
      let temp = null
      for (let i = 0; i < this.serverDiskInfoList.length; i++) {
        if (this.serverDiskInfoList[i].keyId === keyId) {
          temp = this.serverDiskInfoList[i];
          break;
        }
      }
      return temp
    },
    getFixed(fractionDigits) {
      if (fractionDigits === 0) {
        return 0;
      }
      return fractionDigits.toFixed(2);
    },
    //  删除isAlloweTestArr中的元素
    deleteShareTestConnection(value) {
      if (value) {
        const index = this.isAlloweTestArr.indexOf(value);
        if (index > -1) {
          this.isAlloweTestArr.splice(index, 1);
        }
      }
    },
    //  添加isAlloweTestArr中的元素（不重复）
    addShareTestConnection(value) {
      if (value) {
        const index = this.isAlloweTestArr.indexOf(value);
        if (index === -1) {
          this.isAlloweTestArr.push(value);
        }
      }
    },
    encryptPassword(obj, pwdField) {
      const password = aesEncode(this.hasChangePwd ? obj[pwdField] : this.encryptPwd, formatAesKey('tr838408', ''))
      const tempData = JSON.parse(JSON.stringify(obj))
      tempData[pwdField] = password
      return tempData
    },
    inputPwd($event) {
      if (!this.hasChangePwd) {
        $event.target.value = $event.data || ''
      }
      this.hasChangePwd = true
      this.shareForm.lpPassword = $event.target.value
    }
  }
}
</script>
