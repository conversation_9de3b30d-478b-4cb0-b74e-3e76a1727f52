<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.databaseBackupRecord_backupFileDecry')"
    :visible.sync="decodeVisible"
    width="500px"
    @close="closeDecodeElg"
  >
    <el-upload
      ref="decodeFileList"
      action="1111"
      class="upload-demo"
      style="display:inline-block;max-width: 670px;"
      :limit="fileLimit"
      list-type="text"
      :file-list="fileList"
      :auto-upload="false"
      :http-request="onUpload"
      :on-exceed="onExceed"
      :on-change="onChange"
      :on-remove="onRemove"
      :on-error="onError"
    >
      <el-tooltip class="item" effect="dark" :content="$t('pages.databaseBackupRecord_backupFileDownloadHint').replaceAll('{}', fileLimit)" placement="right-start">
        <el-button :disabled="fileUploadExist" size="small" type="primary">{{ fileUploadExist ? $t('pages.databaseBackupRecord_selected') : $t('pages.databaseBackupRecord_selectBackupFile') }}</el-button>
      </el-tooltip>
      <i v-show="fileList.length > 0" class="el-icon-delete-solid" :title="$t('pages.processStgLib_Msg76')" @click="clearFiles"></i>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <common-downloader
        :loading="submitting"
        :name="downloadFileName"
        button-type="primary"
        button-icon=""
        :button-name="$t('button.confirm')"
        :before-download="beforeDownload"
        @download="decodeFile"
      />
      <el-button @click="closeDecodeElg">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import CommonDownloader from '@/components/DownloadManager/common'
import { decodeBackupFile } from '@/api/system/terminalManage/manageLibrary'

export default {
  name: 'BackupFileDecode',
  components: { CommonDownloader },
  data() {
    return {
      uploadFile: undefined,
      decodeVisible: false,
      fileLimit: 1,
      fileList: [],
      submitting: false,
      fileUploadExist: false  //  判断文件是否有上传
    }
  },
  computed: {
    downloadFileName() {
      if (this.uploadFile) {
        return this.uploadFile.name + '.zip'
      }
      return undefined
    }
  },
  created() {
  },
  methods: {
    show() {
      this.fileList = []
      this.submitting = false
      this.fileUploadExist = false
      this.decodeVisible = true
    },
    //  备份文件上传
    async onUpload(data) {
      return false
    },
    onExceed(files, fileList) {
      this.$message({
        message: this.$t('pages.databaseBackupRecord_fileCountOut'),
        type: 'error'
      })
    },
    onChange(file, fileList) {
      this.uploadFile = file
      this.fileUploadExist = fileList.length > 0
    },
    onRemove(file, fileList) {
      this.fileUploadExist = fileList.length > 0
    },
    onError(err) {
      console.log(err);
    },
    beforeDownload() {
      if (this.fileUploadExist) {
        return true
      }
      this.$message({
        message: this.$t('pages.databaseBackupRecord_notSelectUploadFile'),
        type: 'error'
      })
      return false
    },
    decodeFile(file) {
      this.submitting = true
      // 备份文件上传
      const formData = new FormData()
      formData.append('file', this.uploadFile.raw)
      const opts = { file, jwt: true, topic: 'DbBackup' }
      decodeBackupFile(formData, opts).then(() => {
        //  关闭导入弹窗
        this.submitting = false
        this.decodeVisible = false
      }).catch(() => {
        this.submitting = false
      })
    },
    closeDecodeElg() {
      this.decodeVisible = false
      this.fileUploadExist = false
      this.fileList = []
    },
    clearFiles(e) {
      window.event ? window.event.cancelBubble = true : e.stopPropagation()
      this.fileList = []
      this.$refs.decodeFileList.clearFiles()
    }
  }
}
</script>
