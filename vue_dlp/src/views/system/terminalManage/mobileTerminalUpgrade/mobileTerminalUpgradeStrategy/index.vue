<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :os-type-filter="8" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange" />
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <!-- 暂不支持 因策略直接应用到终端-->
        <!-- <strategy-extend ref="stgExtend" :stg-type="stgCode"/>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateMsg_enterName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      top="10vh"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" style="width: 700px; margin-left:10px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30" />
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" />
        </FormItem>
        <FormItem :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem label="">
          <el-checkbox v-model="temp.forceUpgrade" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.forceUpgrade') }}</el-checkbox>
        </FormItem>
        <el-tabs v-model="activeName" :before-leave="changeTab" type="card" style="margin-left: 10px">
          <el-tab-pane label="android" name="android" style="margin-top: -15px">
            <el-card :body-style="{ 'padding': '5px' }" style="margin-top: 20px">
              <div slot="header" class="clearfix">
                <span style="color: #0c60a5;">
                  {{ $t('pages.installPackageTips') }}
                </span>
                <el-button v-if="formable" size="mini" style="float: right;padding: 3px;margin: 0px;height: 20px;" @click="handleViewTermVersion">
                  {{ $t('pages.curTermVersion') }}
                </el-button>
              </div>
              <div v-if="formable">
                <el-button icon="el-icon-upload" size="small" @click="handleCreatePackage">
                  {{ $t('pages.uploadInstallPackage') }}
                </el-button>
                <el-button icon="el-icon-delete" size="small" :disabled="!packageDeleteable" @click="handleDeletePack">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button icon="el-icon-refresh" size="small" @click="handlePackageSearch">
                  {{ $t('button.refresh') }}
                </el-button>
              </div>
              <div>
                <grid-table
                  ref="packageList"
                  :height="240"
                  :show-pager="false"
                  :multi-select="true"
                  :col-model="packageModel"
                  :selectable="() => { return formable }"
                  :row-data-api="loadPackage"
                  :after-load="afterLoad2"
                  @selectionChangeEnd="packageSelectChange"
                />
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="ios" name="ios">
            <FormItem :label="$t('table.installPackageVersionNumber')+ '：'">
              <el-input v-model="temp.iosVersion" :disabled="!formable" :placeholder="$t('pages.versionExample')" maxlength="" @input="temp.iosVersion=temp.iosVersion.replace(/[^V|v0-9.]/g,'')"></el-input>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.uploadInstallPackage')"
      :visible.sync="uploadVisible"
      width="400px"
    >
      <Form ref="uploadForm" :rules="rules" :model="packageTemp" label-position="right" label-width="90px" style="width: 340px;">
        <el-row>
          <el-col :span="21">
            <FormItem :label="$t('pages.exeName')" prop="fileName">
              <el-input v-model="packageTemp.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="upload"
              action="1111"
              accept=".apk"
              :on-change="fileChange"
              :show-file-list="false"
              :auto-upload="false"
              :file-list="fileList"
              :disabled="fileSubmitting"
              :on-progress="handleProgress"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting||fileSaving" style="margin: 0 0 0 1px;"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <el-row v-if="fileSaving">
          <el-col :span="22">
            <el-progress type="line" :percentage="percentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveFile()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="uploadVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.curTermVersion')"
      :visible.sync="dialogTermVisible"
      width="800px"
    >
      <el-container style="height: 400px;">
        <el-aside width="180px">
          <tree-menu
            ref="groupTree"
            :data="treeData"
            :local-search="false"
            :get-search-list="getSearchListFunction"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
          />
        </el-aside>
        <el-main>
          <div class="toolbar" style="height: 33px; margin-bottom: 7px;">
            <div class="searchCon">
              <el-input v-model="termQuery.name" v-trim clearable :placeholder="$t('pages.terminalName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFIlterTerm">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </div>
          <grid-table
            ref="terminalTable"
            :height="360"
            :multi-select="false"
            :row-data-api="loadTerminalTable"
            :col-model="termColModel"
            pager-small
          />
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTermVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  uploadSoft, getPacketList, deletePack, reUploadFile, getStrategyPage,
  addTerminalUpgrade, updateTerminalUpgrade, deleteTerminalUpgrade, getByName
} from '@/api/assets/systemMaintenance/mobileTerminalUpgrade'
import { enableStgBtn, enableStgDelete, selectable, objectFormatter, entityLink, refreshPage, buttonFormatter, hiddenActiveAndEntity } from '@/utils'
import { getTerminalPage } from '@/api/system/terminalManage/terminal'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import axios from 'axios'
import { stgActiveIconFormatter } from '@/utils/formatter'
import { validatePolicy } from '@/utils/validate'

export default {
  name: 'MobileTerminalUpgradeStrategy',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      stgCode: 227,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'stgMessage', width: '200', formatter: this.msgFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'operate', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      termColModel: [
        { prop: 'name', label: 'terminalName', width: '80', sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '80', sort: 'custom' },
        { prop: 'version', label: 'terminalVersion', width: '100', sort: 'custom' }
      ],
      packageModel: [
        { prop: 'fileName', label: 'installPackageName', width: '140', sort: 'custom' },
        /* { prop: 'osType', label: 'system', width: '80', formatter: (row) => { return this.osTypeMap[row.osType] } }, */
        { prop: 'version', label: 'installPackageVersion', width: '100', sort: 'custom' },
        { prop: 'statusInfo', label: 'uploadStatus', width: '100', sort: 'custom' },
        { prop: 'createTime', label: 'time', width: '120', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'reUpload', disabledFormatter: this.disableFormatter, click: this.reUploadFile }
          ]
        }
      ],
      osTypeMap: {
        1: 'Windows',
        2: 'Linux',
        4: 'Mac'
      },
      treeData: [],
      defaultExpandedKeys: ['G0'],
      query: { // 查询条件
        page: 1,
        strategyDefType: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      termQuery: { // 查询条件
        page: 1,
        groupId: undefined,
        name: '',
        // 默认包含子部门
        includeSubGroup: true,
        type: 3
      },
      activeName: 'android',
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      exeTableH: 200,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        entityType: undefined,
        entityId: undefined,
        forceUpgrade: 0,
        remark: '',
        pack: null,
        packList: [],
        androidVersion: '',
        iosVersion: ''
      },
      dialogFormVisible: false,
      dialogExeVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mobileTerminalUpgrade'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.mobileTerminalUpgrade'), 'create'),
        exe: this.$t('pages.installPackageManage')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      submitting: false,
      uploadVisible: false,
      dialogTermVisible: false,
      fileList: [],
      packageDeleteable: false,
      fileSubmitting: false,
      fileSaving: false,
      packageTemp: {}, // 表单字段
      defaultPackageTemp: {
        id: undefined,
        fileName: ''
      },
      source: null,
      percentage: 0
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    uploadVisible(val) {
      if (!val) {
        this.cancel()
      }
    }
  },
  created() {
    this.resetTemp()
    this.resetPackageTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    handleViewTermVersion() {
      this.initGroupTreeNode()
      this.dialogTermVisible = true
      this.$nextTick(() => {
        this.$refs.groupTree && this.$refs.groupTree.clearFilter()
        this.termQuery.name = ''
        this.$refs.terminalTable.execRowDataApi(this.query)
      })
    },
    initGroupTreeNode: function() {
      getDeptTreeFromCache().then(respond => {
        this.treeData = respond.data
      })
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    handleNodeClick(data, node) {
      node.expanded = true
      this.termQuery.page = 1
      this.termQuery.groupId = data.dataId
      this.$refs.terminalTable.execRowDataApi(this.termQuery)
    },
    loadTerminalTable(option) {
      const searchQuery = Object.assign({}, this.termQuery, option)
      return getTerminalPage(searchQuery)
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    getPackageTable() {
      return this.$refs['packageList']
    },
    loadPackage(option) {
      option.limit = null
      return getPacketList(option)
    },
    packageSelectChange(val) {
      this.packageDeleteable = val.length > 0
    },
    afterLoad2: function(rowData, grid) {
      if (!this.temp.packList) {
        return
      }
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          for (const pack of this.temp.packList) {
            if (pack.id == item.id) {
              grid.toggleRowSelection(item)
              break
            }
          }
        })
      })
    },
    handlePackageSearch() {
      this.getPackageTable().execRowDataApi()
    },
    reUploadFile: function(row, data) {
      reUploadFile(row).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.upgrade_text1'),
          type: 'success',
          duration: 2000
        })
      })
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          message: this.$t('pages.upgrade_text2'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      // const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      this.packageTemp.fileName = fileName
      this.fileSubmitting = false
      this.fileList.splice(0, 1, file)
    },
    handleProgress() {
      this.fileSubmitting = true
    },
    handleCreatePackage() {
      this.uploadVisible = true
      this.resetPackageTemp()
    },
    handleDeletePack() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.getPackageTable().getSelectedIds()
        deletePack({ ids: toDeleteIds.join(',') }).then(respond => {
          this.getPackageTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    resetPackageTemp() {
      this.packageTemp = Object.assign({}, this.defaultPackageTemp)
      this.fileList.splice(0)
    },
    saveFile() {
      this.submitting = true
      this.fileSaving = true
      this.percentage = 0
      this.$refs['uploadForm'].validate((valid) => {
        if (valid) {
          const onUploadProgress = (progressEvent) => {
            const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
            this.percentage = parseInt(percent)
          }
          this.source = this.connectionSource()
          const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.packageTemp)
          fd.append('uploadFile', this.fileList[0].raw)
          // 发起请求
          uploadSoft(fd, onUploadProgress, cacheToken).then(res => {
            this.submitting = false
            this.fileSaving = false
            this.percentage = 0
            this.uploadVisible = false
            this.getPackageTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.upgrade_text3'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
            this.fileSaving = false
            this.percentage = 0
          })
        } else {
          this.submitting = false
          this.fileSaving = false
          this.percentage = 0
        }
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    afterLoad1(rowData, grid) {
      if (!rowData || rowData.length < 1) {
        return
      }
      const data = []
      rowData.forEach(item => {
        if (item.type == 3) {
          data.push(item)
        }
      })
      rowData.splice(0, rowData.length, ...data)
      if (rowData || rowData.length > 0) {
        this.$refs['terminalTable'].updateRowData(rowData)
      }
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFIlterTerm() {
      this.termQuery.page = 1
      this.$refs.terminalTable.execRowDataApi(this.termQuery)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.activeName = 'android'
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.getPackageTable().execRowDataApi()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.getPackageTable().execRowDataApi()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatData() {
      const rows = this.getPackageTable().getSelectedDatas()
      if (rows && rows.length > 0) {
        const failRows = rows.filter(row => row.status != 6)
        if (failRows && failRows.length > 0) {
          this.$message({
            message: this.$t('pages.upgrade_text10'),
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      if (rows.length > 1) {
        this.$message({
          message: this.$t('pages.upgrade_text9'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (rows.length > 0) {
        rows.forEach(item => {
          this.temp.androidVersion = item.version
        })
      } else {
        this.temp.androidVersion = ''
      }
      this.temp.packList = rows
      return true
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.formatData()) {
            this.submitting = false
            return
          }
          addTerminalUpgrade(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.formatData()) {
            this.submitting = false
            return
          }
          const tempData = Object.assign({}, this.temp)
          updateTerminalUpgrade(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        const obj = {
          ids: toDeleteIds.join(',')
        }
        deleteTerminalUpgrade(obj).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    disableFormatter(row) {
      return !this.formable
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    msgFormatter(row, data) {
      let msg = ''
      const names = []
      let version = ''
      if (row.packList.length) {
        row.packList.forEach(item => {
          names.push(`${item.fileName}`)
          version = item.version
        })
        msg += this.$t('pages.mobileTerminalUpgradeStrategyMsg1', {
          names: names,
          version: version
        })
      }
      if (row.forceUpgrade) {
        msg += this.$t('pages.mobileTerminalUpgradeStrategyMsg2')
      }
      if (row.iosVersion) {
        msg += this.$t('pages.mobileTerminalUpgradeStrategyMsg3', { version: row.iosVersion })
      }
      return msg
    }
  }
}
</script>
