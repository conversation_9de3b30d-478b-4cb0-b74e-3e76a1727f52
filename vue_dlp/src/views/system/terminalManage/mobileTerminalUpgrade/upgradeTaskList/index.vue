<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :os-type-filter="8" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange" />
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <el-select v-model="query.keyword1">
            <el-option :label="$t('pages.all')" :value="null"></el-option>
            <el-option :label="$t('pages.hasBeenUpgraded')" :value="1"></el-option>
            <el-option :label="$t('pages.notUpgraded')" :value="2"></el-option>
            <el-option :label="$t('pages.downloadFailed')" :value="3"></el-option>
          </el-select>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyListTable" :default-sort="{ prop: 'createTime' }" :multi-select="false" :col-model="colModel" :row-data-api="rowDataApi" />
    </div>
    <!-- <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="升级状态详情"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.transmissionTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item label="安装包名称">
            {{ rowDetail.packageName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" label="设备类型">
            {{ typeFormatter(rowDetail, rowDetail.mobileType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" label="安装包版本号">
            {{ rowDetail.installVersion }}
          </el-descriptions-item>
          <el-descriptions-item span="2" label="当前终端版本">
            {{ rowDetail.versionNum }}
          </el-descriptions-item>
          <el-descriptions-item label="升级状态">
            {{ statusFormatter(rowDetail, rowDetail.upStatus) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import { getNewStatusPage } from '@/api/assets/systemMaintenance/mobileTerminalUpgrade'
import { refreshPage } from '@/utils'

export default {
  name: 'MobileTerminalTaskList',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '150', sort: true },
        { prop: 'terminalName', label: 'terminalName', sort: 'custom', fixedWidth: '200', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'packageName', label: 'installPackageName', width: '200', sort: 'custom' },
        { prop: 'mobileType', label: 'devType', width: '200', sort: 'custom', formatter: this.typeFormatter },
        { prop: 'installVersion', label: 'installPackageVersionNumber', width: '150', sort: 'custom' },
        { prop: 'versionNum', label: 'curTermVersion', width: '150', sort: 'custom' },
        { prop: 'upStatus', label: 'upgradeStatus', width: '150', sort: 'custom', formatter: this.statusFormatter }
        /* { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('358,360'),
          buttons: [
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('360') }
          ]
        } */
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        keyword1: null
      },
      treeable: true,
      showTree: true,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        endTime: new Date(),
        softWareTasks: [],
        oneTime: 1,
        remark: '',
        active: true,
        objectIds: [],
        objectGroupIds: []
      },
      rowDetail: {},
      dialogFormVisible: false
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getNewStatusPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    refresh() {
      return refreshPage(this)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    typeFormatter(row, data) {
      let msg = ''
      if (data == 0) {
        msg = 'android'
      } else if (data == 1) {
        msg = 'ios'
      }
      return msg
    },
    statusFormatter(row, data) {
      let msg = ''
      if (data == 1) {
        msg = this.$t('pages.hasBeenUpgraded')
      } else if (data == 2) {
        msg = this.$t('pages.notUpgraded')
      } else if (data == 3) {
        msg = this.$t('pages.downloadFailed')
      }
      return msg
    }
  }
}
</script>
