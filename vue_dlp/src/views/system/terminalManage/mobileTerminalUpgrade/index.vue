<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.mobileTerminalUpgrade')" name="mobileTerminalUpgradeStrategy">
        <mobile-terminal-upgrade-strategy ref="mobileTerminalUpgradeStrategy"/>
      </el-tab-pane>
      <el-tab-pane :label="$t('table.upgradeStatus')" name="mobileTerminalTaskList">
        <mobile-terminal-task-list ref="mobileTerminalTaskList"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MobileTerminalTaskList from './upgradeTaskList'
import MobileTerminalUpgradeStrategy from './mobileTerminalUpgradeStrategy'

export default {
  name: 'MobileTerminalUpgrade',
  components: { MobileTerminalTaskList, MobileTerminalUpgradeStrategy },
  data() {
    return {
      activeName: 'mobileTerminalUpgradeStrategy'
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
    tabClick(pane, event) {
    },
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    },
    goToTab() {
      if (this.$route.params != null) {
        this.activeName = this.$route.params.activeName
      }
    }
  }
}
</script>
