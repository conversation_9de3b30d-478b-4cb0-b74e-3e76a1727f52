<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.processName" :value="query.processName">
          <span>{{ $t('pages.programName') }}：</span>
          <el-input v-model="query.processName" v-trim clearable style="width: 200px;"/>
        </SearchItem>
        <SearchItem model-key="query.appGroupId" :value="query.appGroupId">
          <span>{{ $t('pages.appGroup') }}：</span>
          <tree-select
            ref="treeSelect"
            :data="appGroupData"
            node-key="dataId"
            clearable
            style="display: inline-block"
            @change="handleGroupChange"
          />
        </SearchItem>
        
        <audit-log-exporter slot="append" v-permission="'325'" :multi-dataset="hasPermission('275')" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'437'" :selection="selection" :date-range="dateRange" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('437')"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('components.fileTransfer')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <el-progress :text-inside="true" :stroke-width="26" :percentage="percent"></el-progress>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.appReportExDetails')"
      :visible.sync="dialogFormDetailVisible"
      width="900px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item v-for="item in panelModel" :key="rowDetail[item.value]" :label="$t(item.label)" :span="item.span" >
            <terminal-detail v-if=" 'terminal' === item.type" :label="rowDetail[item.value]" :search-id="rowDetail[item.param]"/>
            <user-detail v-else-if=" 'user' === item.type" :label="rowDetail[item.value]" :search-id="rowDetail[item.param]"/>
            <span v-else-if="'createTime' === item.value">
              {{ `${rowDetail[item.value]}`.split(' ')[0] }}
            </span>
            <span v-else>
              {{ rowDetail[item.value] }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
        <grid-table
          ref="detailList"
          style="margin-top:10px;margin-top:10px"
          :row-data-api="detailRowDataApi"
          :height="400"
          :multi-select="false"
          :after-load="detailAfterLoad"
          :col-model="[
            {prop: 'startTime', label: 'processStartTime', width: '150'},
            {prop: 'endTime', label: 'processEndTime', width: '150'},
            {prop: 'runTime', label: 'runTime', width: '150'},
            { prop: 'sysUserName', label: 'linuxLoginUser', width: '150' },
            {prop: 'winTitle', label: 'processWindowTitle', width: '150'},
            { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
              buttons: [
                { label: $t('pages.viewScreenRecord'), click: playVideo, isShow: row => !disabledLogViewerBtn(row) }
              ]
            }]"
        />
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportAppReportEx, getDetailPage } from '@/api/system/terminalManage/appReportExLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { getTypeTree } from '@/api/behaviorManage/application/appVersion';
import { asyncGetLogVideoInfo, handleViewLogVideo, disabledLogViewerBtn } from '@/utils/logVideo'

export default {
  name: 'AppReportEx',
  data() {
    return {
      percent: 0,
      colModel: [
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'processName', label: 'programName', width: '150' },
        { prop: 'appGroupName', label: this.$t('pages.appGroup'), width: '150' },
        { prop: 'createTime', label: 'runDate', width: '150', formatter: row => logSourceFormatter(row, row.createTime.split(' ')[0]) },
        { prop: 'runTime', label: 'runTime', width: '150' },
        { prop: 'sysUserName', label: 'linuxLoginUser', width: '150' },
        { prop: 'appPath', label: 'applicationPath', width: '150' },
        { prop: 'productName', label: 'productName', width: '150' },
        { prop: 'productVer', label: 'productVersion', width: '150' },
        { prop: 'originFileName', label: 'originalName', width: '150' },
        { prop: 'fileDesc', label: 'fileDesc', width: '150' },
        { prop: 'companyName', label: 'companyName', width: '150' },
        { prop: 'copyright', label: 'copyright', width: '150' },
        { prop: 'interName', label: 'internalName', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('275'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        processName: null,
        isTimes: false,
        sortName: 'createTime',
        sortOrder: 'desc',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        appGroupId: undefined
      },
      detailQuery: {
        page: 1,
        appPath: '',
        termId: undefined,
        userId: '',
        createDate: '',
        isTimes: false
      },
      showTree: true,
      selection: [],
      dialogFormVisible: false,
      percentOptions: [
        { status: 0, percent: 0 },
        { status: 1, percent: 1 },
        { status: 5, percent: 50 },
        { status: 6, percent: 100 }
      ],
      rowDetail: {},
      dialogFormDetailVisible: false,
      panelModel: [
        { value: 'terminalName', label: 'pages.terminalName', type: 'terminal', param: 'terminalId' },
        { value: 'userName', label: 'pages.user', type: 'user', param: 'userId' },
        { value: 'processName', label: 'table.programName' },
        { value: 'appGroupName', label: 'pages.appGroup' },
        { value: 'createTime', label: 'table.runDate' },
        { value: 'runTime', label: 'table.runTime' },
        { value: 'sysUserName', label: 'table.linuxLoginUser' },
        { value: 'originFileName', label: 'table.originalName' },
        { value: 'productName', label: 'table.productName' },
        { value: 'productVer', label: 'table.productVersion' },
        { value: 'companyName', label: 'table.companyName' },
        { value: 'interName', label: 'table.internalName' },
        { value: 'copyright', label: 'table.copyright', span: 2 },
        { value: 'fileDesc', label: 'table.fileDesc', span: 2 },
        { value: 'appPath', label: 'table.applicationPath', span: 2 }
      ],
      dateRange: undefined,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      appGroupData: []
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.loadAppGroupData()
  },
  activated() {
    this.loadAppGroupData()
    this.gridTable().execRowDataApi()
  },
  methods: {
    deleteLog,
    disabledLogViewerBtn,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      const { createDate, startDate, endDate, isTimes } = this.query
      this.dateRange = { createDate, startDate, endDate, isTimes }
      return getLogPage(searchQuery)
    },
    detailRowDataApi(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      searchQuery.searchReport = this.query.searchReport
      this.query.sortName = 'createTime'
      this.query.sortOrder = option.sortOrder
      return getDetailPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportAppReportEx(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormDetailVisible = true
      this.detailQuery.termId = row.terminalId
      this.detailQuery.appPath = row.appPath
      this.detailQuery.userId = row.userId
      this.detailQuery.createDate = row.createTime.split(' ')[0]
      this.detailQuery.page = 1
      this.$nextTick(() => {
        this.$refs.detailList.execRowDataApi(this.detailQuery)
      })
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
    },
    detailAfterLoad(rowData) {
      asyncGetLogVideoInfo(rowData, undefined, this.query.searchReport, undefined, 'startTime')
    },
    playVideo(row) {
      handleViewLogVideo(row, this)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    async loadAppGroupData() {
      const { data } = await getTypeTree()
      this.appGroupData = [{ dataId: '0', id: 'G0', label: this.$t('pages.ungrouped'), parentId: 'G0', type: 'G' }, ...data]
    },
    handleGroupChange(data) {
      this.query.appGroupId = data
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-descriptions-item__label {
    min-width: 150px;
  }
  >>>.el-descriptions-item__content {
    min-width: 100px;
    max-width: 180px;
  }
</style>
