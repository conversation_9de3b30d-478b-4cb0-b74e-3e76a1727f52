<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 748px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName">
          <el-tab-pane name="control" :label="$t('pages.remoteDesktopControlSet')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <detect-grid-table ref="controlGrid" :formable="formable" :row-datas="temp.relControlIps" style="padding: 0 10px"/>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="28px">
              <el-radio-group v-model="temp.controlExecuteRule" :disabled="!formable" style="margin-left: 0">
                <el-radio :label="1">
                  <i18n path="pages.terminalDetectRuleTip1">
                    <template slot="info">{{ $t('pages.remoteDesktopAddress') }}</template>
                  </i18n>
                </el-radio>
                <el-radio :label="0">
                  <i18n path="pages.terminalDetectRuleTip2">
                    <template slot="info">{{ $t('pages.remoteDesktopAddress') }}</template>
                  </i18n>
                </el-radio>
              </el-radio-group>
              <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('pages.remoteDesktopAddress'), info1: $t('pages.remoteDesktopLogin') }) }}</label>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.remoteDesktopRuleTip1') }}
                  <br/>
                  {{ $t('pages.remoteDesktopRuleTip2') }}
                  <br/>
                  {{ $t('pages.remoteDesktopRuleTip3') }}
                  <br/>
                </div>
                <i class="el-icon-info"/>
              </el-tooltip>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="30px">
              <el-checkbox v-model="temp.isBlock" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('pages.forbidRemoteDesktopConnect') }}</el-checkbox>
            </FormItem>
            <div v-show="temp.isBlock == 0">
              <FormItem label-width="30px">
                <el-row>
                  <el-col :span="16">
                    <el-checkbox-group v-model="approvalTypeList" :disabled="!formable" @change="handleApprovalTypeChange">
                      <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                      <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                      <br>
                      <el-checkbox v-if="hasEncPermision" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                      <el-checkbox v-if="hasEncPermision" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                </el-row>
              </FormItem>
              <ResponseContent
                :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
                :status="dialogStatus"
                :show-select="true"
                :rule-type-name="ruleTypeName"
                :editable="formable && ruleDisable"
                :clearable="false"
                read-only
                :prop-check-rule="!!temp.hasAlarmRuleId"
                :show-check-rule="true"
                :prop-rule-id="propRuleId"
                @ruleIsCheck="getRuleIsCheck"
                @getRuleId="value => propRuleId = value"
              />
              <ResponseContent
                v-if="hasEncPermision"
                :select-style="{ 'margin': '5px 0 5px 13px' }"
                :status="dialogStatus"
                :show-select="true"
                :rule-type-name="encRuleTypeName"
                :editable="formable && encRuleDisable"
                :clearable="false"
                read-only
                :prop-check-rule="!!temp.isEncAlarm"
                :show-check-rule="true"
                :prop-rule-id="encPropRuleId"
                @ruleIsCheck="getEncRuleIsCheck"
                @getRuleId="(value) => encPropRuleId = value"
              />
            </div>
            <div v-show="temp.isBlock == 1">
              <ResponseContent
                :select-style="{ 'margin': '5px 0 5px 13px' }"
                :status="dialogStatus"
                :show-select="true"
                :rule-type-name="$t('pages.forbidConnVioRule')"
                :editable="formable"
                :clearable="false"
                read-only
                :prop-check-rule="!!temp.hasLimitRuleId"
                :show-check-rule="true"
                :prop-rule-id="limitRuleId"
                @ruleIsCheck="val => temp.hasLimitRuleId = val"
                @getRuleId="val => limitRuleId = val"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane name="record" :label="$t('pages.remoteDesktopLogSet')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <detect-grid-table ref="recordGrid" :formable="formable" :row-datas="temp.relRecordIps" style="padding: 0 10px"/>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="28px">
              <el-radio-group v-model="temp.recordExecuteRule" :disabled="!formable" style="margin-left: 0">
                <el-radio :label="1">
                  <i18n path="pages.terminalDetectRuleTip1">
                    <template slot="info">{{ $t('pages.remoteDesktopAddress') }}</template>
                  </i18n>
                </el-radio>
                <el-radio :label="0">
                  <i18n path="pages.terminalDetectRuleTip2">
                    <template slot="info">{{ $t('pages.remoteDesktopAddress') }}</template>
                  </i18n>
                </el-radio>
              </el-radio-group>
              <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('pages.remoteDesktopAddress'), info1: $t('pages.remoteDesktopLogin') }) }}</label>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  <i18n path="pages.withinDetectionRuleTip">
                    <span slot="info">{{ $t('pages.noneAuditRemoteDesktop') }}</span>
                  </i18n>
                  <br/>
                  <i18n path="pages.outsideDetectionRuleTip">
                    <span slot="info">{{ $t('pages.allAuditRemoteDesktop') }}</span>
                  </i18n>
                </div>
                <i class="el-icon-info"/>
              </el-tooltip>
            </FormItem>
            <el-divider content-position="left" style="margin: 0 15px">{{ $t('pages.responseRule') }}</el-divider>
            <div class="response-rule">
              <FormItem label-width="28px" prop="limitFileSize">
                <el-checkbox v-model="temp.isRecord" :disabled="!formable" :false-label="0" :true-label="1" @change="recordLogChange">
                  <span style="margin-left: 8px">{{ $t('pages.recordRemoteDesktop') }}</span>
                </el-checkbox>
                <div style="height: 2px"/>
                <el-checkbox v-model="temp.needBackup" :disabled="!formable || temp.isRecord === 0" :false-label="0" :true-label="1" @change="needBackupChange">
                  <i18n path="pages.blueTooth_Msg1" style="margin-left: 8px">
                    <el-input-number
                      slot="size"
                      v-model="temp.limitFileSize"
                      :disabled="temp.needBackup === 0 || !formable"
                      :controls="false"
                      :min="1"
                      :max="10240"
                      style="width: 80px;"
                      size="mini"
                    />
                  </i18n>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.ftpControl_text3') }}<br/>
                      {{ $t('pages.ftpControl_text4') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                  <!-- <span style="color: #2b7aac;margin-left: 10px">{{ $t('pages.processMonitor_Msg28') }}</span> -->
                  <el-button :disabled="!formable || temp.isRecord === 0" style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
                </el-checkbox>
              </FormItem>
            </div>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importBrowserFileStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>
<script>
import {
  getPage, getByName, createData, updateData, deleteData
} from '@/api/behaviorManage/application/remoteDesktop'
import { validatePolicy } from '@/utils/validate'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'
import DetectGridTable from '@/views/system/terminalManage/remoteDesktopControl/detectGridTable'

export default {
  name: 'RemoteDesktopControl',
  components: { DetectGridTable, ResponseContent, ImportStg, BackupRuleContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 254,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        limitFileSize: 20,
        entityType: '',
        entityId: undefined,
        // isBackup: 0,  // 是否备份 1-备份 0-不备份
        isBlock: 0, // 是否阻断远程桌面的连接
        isAlarm: 0,  // 响应规则-是否告警 按位 第一位：0-连接限制不告警 1-连接限制告警 第二位：0-明文外发不告警 1-明文外发告警 第三位：0-密文外发不告警 1-密文外发告警
        hasAlarmRuleId: 0, // 明文文件触发响应规则
        isEncAlarm: 0, // 密文文件触发响应规则
        hasLimitRuleId: 0, // 阻断远程桌面连接触发响应规则
        ruleId: undefined, // 明文文件响应规则id
        encRuleId: undefined, // 密文文件响应规则id
        limitRuleId: undefined, // 阻断远程桌面连接响应规则id
        approvalType: 0, // 位与 审批外发 1-明文文件禁止外发, 2-密文文件禁止外发, 4-允许明文文件外发, 8-允许密文文件外发
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        relControlIps: [], // 管控检测规则
        relRecordIps: [], //  记录检测规则
        recordExecuteRule: 0, // 审计检测规则 0 - 除列表之外   1 - 包括列表之内   当 === 0 时，列表为空，则是所有网页
        controlExecuteRule: 0, // 管控检测规则 0 - 检测规则外   1 - 检测规则内   当 === 0 时，列表为空，则是所有网页
        isRecord: 0, // 是否记录日志
        needBackup: 0 // 是否备份
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.remoteDesktopControlStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.remoteDesktopControlStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        limitFileSize: [
          { validator: this.limitFileSizeValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      propRuleId: undefined,
      encPropRuleId: undefined, // 密文文件响应规则id
      limitRuleId: undefined, // 禁止远程桌面连接时，响应规则ID
      approvalTypeList: [], // 审批外发
      hasEncPermision: true,  // 是包含加解密模块
      // 触发明文文件响应规则
      ruleTypeName: this.$t('pages.burnMsg10'),
      // 触发密文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),
      validRuleId: true,
      validEncRuleId: true,
      validForbidRuleId: true,
      ruleDisable: false, // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false, // 当密文执行规则没有配置时,明文响应规则要置灰
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      activeName: 'control'   // 策略tabs页
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  watch: {

  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
  },
  activated() {
  },
  methods: {
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
        this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    // sendTypeChange: function(value) {
    //   this.temp.sendType = value
    //   if (value === 1 || value === '1') {
    //     // 允许发送文件的话，初始化为不限制
    //     this.temp.disable = false
    //     this.temp.fileSendLimit = 0
    //   } else {
    //     this.temp.disable = true
    //     this.temp.fileSendLimit = -1
    //   }
    // },
    number() {
      if (isNaN(this.temp.fileSendLimit)) {
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace(/[^\.\d]/g, '')
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace('.', '')
      }
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.limitRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.validForbidRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
      this.activeName = 'control'
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      if (row.limitFileSize > 0) {
        this.temp.needBackup = 1
      }
      if (!this.temp.needBackup || row.limitFileSize === 0) {
        this.temp.limitFileSize = 20
      }
      if (this.temp.approvalType > 0) {
        this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
        // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
        if (this.approvalTypeList.indexOf(4) > -1) {
          this.approvalTypeList.push(1)
        }
        if (this.approvalTypeList.indexOf(8) > -1) {
          this.approvalTypeList.push(2)
        }
      }
      this.propRuleId = row.ruleId
      this.encPropRuleId = row.encRuleId
      this.limitRuleId = row.limitRuleId
      this.temp.backupRuleId = row.backupRuleId === 0 ? undefined : row.backupRuleId
      if (row.ruleId > 0) { this.temp.hasAlarmRuleId = true }
      if (row.encRuleId > 0) { this.temp.isEncAlarm = true }
      if (row.limitRuleId > 0) { this.temp.hasLimitRuleId = true }
      if (row.backupRuleId > 0) { this.temp.isBackupRule = true }
      this.temp.relControlIps.forEach((info, index) => { info.id = index + 1 })
      this.temp.relRecordIps.forEach((info, index) => { info.id = index + 1 })
      this.dialogStatus = 'update'
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.hasAlarmRuleId = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        // 多此一举再设置一次是因为为了修复监听属性sendType改变fileSendLimit的值
        this.$set(this.temp, 'fileSendLimit', row.fileSendLimit)
        this.temp.relControlIps.splice(0, 0)
        this.temp.relRecordIps.splice(0, 0)
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      const formData = this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (!(this.validRuleId && this.validEncRuleId && this.validForbidRuleId)) {
          this.activeName = 'control'
          return
        }
        if (valid) {
          this.submitting = true
          createData(formData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      const formData = this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (!(this.validRuleId && this.validEncRuleId && this.validForbidRuleId)) {
          this.activeName = 'control'
          return
        }
        if (valid) {
          this.submitting = true
          updateData(formData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    limitFileSizeValidator(rule, value, callback) {
      if (this.temp.needBackup === 1) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      let str = this.$t('pages.remoteDesktopControlSet') + ': '
      if (row.controlExecuteRule) {
        msgArr.push(this.$t('pages.terminalDetectRuleTip1', { info: this.$t('pages.remoteDesktopAddress') }))
      } else {
        msgArr.push(this.$t('pages.terminalDetectRuleTip2', { info: this.$t('pages.remoteDesktopAddress') }))
      }
      if (!row.isBlock) {
        if (row.approvalType >= 0) {
          const approvalList = this.numToList(row.approvalType, 4)
          if (this.hasEncPermision) {
            if (approvalList.indexOf(1) > -1) {
              // 禁止明文文件传输
              msgArr.push(this.$t('pages.burnMsg14'))
            }
            if (approvalList.indexOf(2) > -1) {
              // 禁止密文文件传输
              msgArr.push(this.$t('pages.burnMsg15'))
            }
            if (approvalList.indexOf(4) > -1) {
              // 允许明文文件外发审批
              msgArr.push(this.$t('pages.burnMsg6'))
            }
            if (approvalList.indexOf(8) > -1) {
              // 允许密文文件外发审批
              msgArr.push(this.$t('pages.burnMsg9'))
            }
          }
        }
        if (this.hasEncPermision) {
          if (row.ruleId) {
            // 触发明文文件响应规则
            msgArr.push(this.$t('pages.burnMsg10'))
          }
          if (row.encRuleId) {
            // 触发密文文件响应规则
            msgArr.push(this.$t('pages.burnMsg11'))
          }
        } else {
          if (row.encRuleId) {
            // 触发响应规则
            msgArr.push(this.$t('pages.triggerResponseRule'))
          }
        }
      } else {
        msgArr.push(this.$t('pages.forbidRemoteDesktopConnect'))
        if (row.limitRuleId) {
          msgArr.push(this.$t('pages.forbidConnVioRule'))
        }
      }
      str += msgArr.join('; ')
      if (row.isRecord) {
        msgArr.splice(0)
        str += '; ' + this.$t('pages.remoteDesktopLogSet') + ': '
        if (row.recordExecuteRule) {
          msgArr.push(this.$t('pages.terminalDetectRuleTip1', { info: this.$t('pages.remoteDesktopAddress') }))
        } else {
          msgArr.push(this.$t('pages.terminalDetectRuleTip2', { info: this.$t('pages.remoteDesktopAddress') }))
        }
        if (!row.limitFileSize) {
          // 不备份文件
          msgArr.push(this.$t('pages.burnMsg12'))
        } else {
          // 文件备份限制{row.limitFileSize}MB
          msgArr.push(this.$t('pages.burnMsg13', { BackupSize: row.limitFileSize }))
        }
        str += msgArr.join('; ')
      }
      return str
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    formatFormData() {
      const formData = JSON.parse(JSON.stringify(this.temp))
      this.validRuleId = true
      this.validEncRuleId = true
      this.validForbidRuleId = true
      if (formData.isBlock) {
        formData.limitRuleId = this.limitRuleId
        if (formData.hasLimitRuleId === 1 && !formData.limitRuleId) {
          this.validForbidRuleId = false
        }
        formData.limitRuleId && (formData['isAlarm'] = 1)
      } else {
        formData.ruleId = this.propRuleId
        formData.encRuleId = this.encPropRuleId
        // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
        formData.approvalType = this.getSum(this.approvalTypeList)
        if (this.approvalTypeList.indexOf(4) > -1) {
          formData.approvalType = formData.approvalType - 1
        }
        if (this.approvalTypeList.indexOf(8) > -1) {
          formData.approvalType = formData.approvalType - 2
        }

        if (formData.hasAlarmRuleId === 1 && !formData.ruleId) {
          this.validRuleId = false
        }
        if (formData.isEncAlarm === 1 && !formData.encRuleId) {
          this.validEncRuleId = false
        }
        formData['isAlarm'] = 0
        formData.ruleId && (formData['isAlarm'] |= 2)
        formData.encRuleId && (formData['isAlarm'] |= 4)
      }
      if (formData.needBackup === 0) {
        formData.limitFileSize = 0
      }
      return formData
    },
    getRuleIsCheck(value) {
      this.temp.hasAlarmRuleId = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.hasAlarmRuleId = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
    },
    importSuccess() {
      this.handleFilter()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    recordLogChange(val) {
      this.temp.isRecord = val
      if (!val && this.temp.needBackup === 1) {
        this.temp.needBackup = 0
        this.$refs['dataForm'].clearValidate('limitFileSize')
      }
    },
    needBackupChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('limitFileSize')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 防止默认style 造成网页上传文件记录设置中响应规则看不到的情况
.el-divider.el-divider--horizontal {
  margin: 20px 0 15px;
}
>>>.response-rule .el-form-item__error {
  padding-left: 160px;
}
</style>
