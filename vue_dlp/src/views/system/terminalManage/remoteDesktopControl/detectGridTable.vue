<template>
  <div>
    <div v-if="formable">
      <el-button size="small" @click="createData">
        {{ $t('button.insert') }}
      </el-button>
      <el-button :disabled="!deleteable" size="small" @click="deleteData">
        {{ $t('button.delete') }}
      </el-button>
      <el-button size="small" @click="clearData">
        {{ $t('button.clear') }}
      </el-button>
    </div>
    <grid-table
      ref="gridTable"
      :autoload="false"
      :col-model="portColModel"
      :height="120"
      :multi-select="true"
      :row-datas="rowDatas"
      :show-pager="false"
      @selectionChangeEnd="selectionChangeEnd"
    />

    <!--单个添加/修改Telnet检测规则-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dlgStatus]"
      :visible.sync="dlgVisible"
      append-to-body
      width="380px"
    >
      <Form ref="dataForm" :model="temp" :rules="rules">
        <FormItem :label="$t('table.limitType')" label-width="70px" prop="limitType">
          <el-select v-model="temp.limitType" @change="handleLimitTypeChange">
            <el-option v-for="(label, key) in limitTypeOptions" :key="key" :label="label" :value="Number(key)"/>
          </el-select>
        </FormItem>

        <FormItem label-width="60px">
          <el-radio-group v-show="temp.limitType == 0 || temp.limitType == 1" v-model="temp.isCustomIp" @change="handleCustomIpChange">
            <el-radio :label="0">{{ $t('pages.allIP') }}</el-radio>
            <el-radio :label="1">{{ $t('route.custom') }}</el-radio>
          </el-radio-group>
        </FormItem>

        <FormItem :label="ipRangeOrDomainOrCommandLabel" label-width="70px" prop="checkData">
          <el-input v-model="temp.checkData" v-trim :disabled="(temp.limitType == 0 || temp.limitType == 1) && !temp.isCustomIp" maxlength="255"/>
        </FormItem>

        <FormItem v-if="temp.limitType == 0 || temp.limitType == 1" :label="endIpv4OrEndIpV6Label" label-width="70px" prop="endIp">
          <el-input v-model="temp.endIp" v-trim :disabled="!temp.isCustomIp" maxlength="40"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveData">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dlgVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { isIPv6 } from '@/utils/validate'

export default {
  name: 'DetectGridTable',
  props: {
    rowDatas: {
      type: Array,
      required: true
    },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      textMap: {
        update: this.i18nConcatText(this.$t('pages.detectionRules'), 'update'),
        create: this.i18nConcatText(this.$t('pages.detectionRules'), 'create')
      },
      limitTypeOptions: {
        0: this.$t('pages.telnetLimitType1'),
        1: this.$t('pages.telnetLimitType2'),
        2: this.$t('pages.telnetLimitType3')
      },
      ipRangeOrDomainOrCommandLabel: '',
      endIpv4OrEndIpV6Label: '',
      dlgStatus: 'create',
      dlgVisible: false,
      deleteable: false,
      temp: {},
      defaultTemp: {
        limitType: 0,
        checkData: '',
        endIp: '',
        isCustomIp: 0
      },
      rules: {
        checkData: [{ validator: this.checkDataValidator, trigger: 'blur' }],
        endIp: [{ validator: this.endIpValidator, trigger: 'blur' }]
      },
      portColModel: [
        { prop: 'limitType', label: 'limitType', width: '80', sort: true, sortOriginal: true, formatter: (row, data) => this.limitTypeOptions[data] },
        { prop: 'checkData', label: 'ipRange', width: '150', formatter: this.checkDataFormatter },
        {
          label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: () => !this.formable, click: this.updateData }
          ]
        }
      ]
    }
  },
  watch: {
    rowDatas: {
      deep: true,
      handler(val) {
        this.$nextTick(() => {
          this.$refs['gridTable'] && this.$refs['gridTable'].execRowDataApi()
        })
      }
    }
  },
  methods: {
    rowDataApi() {
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: { total: 0, items: this.rowDatas }})
      })
    },
    createData() {
      this.restTemp()
      this.handleLimitTypeChange(this.temp.limitType)
      this.dlgVisible = true
      this.dlgStatus = 'create'
      this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
    },
    updateData(row) {
      this.restTemp()
      this.temp = Object.assign(this.temp, row)
      this.changeFormLabel()
      this.dlgVisible = true
      this.dlgStatus = 'update'
      this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
    },
    deleteData() {
      const ids = this.$refs['gridTable'].getSelectedKeys()
      this.$refs['gridTable'].deleteRowData(ids)
    },
    clearData() {
      this.rowDatas.splice(0, this.rowDatas.length)
    },
    saveData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (this.rowDatas.some(e => e.limitType == this.temp.limitType && e.checkData == this.temp.checkData && e.endIp == this.temp.endIp && e.id != this.temp.id)) {
            this.$message({
              message: this.$t('pages.wifiBlock_text5'),
              type: 'error',
              duration: 2000
            })
          } else {
            if (this.temp.id) {
              this.$refs['gridTable'].updateRowData(this.temp)
            } else {
              this.temp.id = Date.now()
              this.rowDatas.push(this.temp)
            }
            this.dlgVisible = false
          }
        }
      })
    },
    checkDataFormatter(row, data) {
      return row.checkData + (row.limitType == 0 || row.limitType == 1 ? ' ~ ' + row.endIp : '')
    },
    restTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    checkDataValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('text.pleaseEnterInfo', { info: this.ipRangeOrDomainOrCommandLabel })))
      } else {
        this.ipValidator(rule, value, callback)
      }
    },
    endIpValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('text.pleaseEnterInfo', { info: this.endIpv4OrEndIpV6Label })))
      } else {
        this.ipValidator(rule, value, callback)
      }
    },
    changeFormLabel(val) {
      const limitType = val || this.temp.limitType
      if (limitType == 0) { // 按ipv4地址匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.beginIpv4')
        this.endIpv4OrEndIpV6Label = this.$t('pages.endIpv4')
      } else if (limitType == 1) { // 按ipv6地址匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.beginIpv6')
        this.endIpv4OrEndIpV6Label = this.$t('pages.endIpv6')
      } else if (limitType == 2) { // 按域名匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.domain')
        this.endIpv4OrEndIpV6Label = ''
      } else if (limitType == 3) { // 按命令匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('table.command')
        this.endIpv4OrEndIpV6Label = ''
      }
    },
    handleLimitTypeChange(val) {
      this.changeFormLabel(val)
      if (val == 0) { // 按ipv4地址匹配
        this.temp.isCustomIp = 0
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        this.temp.checkData = '0.0.0.0'
        this.temp.endIp = '***************'
      } else if (val == 1) { // 按ipv6地址匹配
        this.temp.isCustomIp = 0
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        this.temp.checkData = '0:0:0:0:0:0:0:0'
        this.temp.endIp = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      } else if (val == 2) { // 按域名匹配
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        this.temp.checkData = ''
        this.temp.endIp = ''
      }
    },
    handleCustomIpChange(val) {
      if (this.temp.limitType == 0) { // 按ipv4地址匹配
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        this.temp.checkData = '0.0.0.0'
        this.temp.endIp = '***************'
      } else if (this.temp.limitType == 1) { // 按ipv6地址匹配
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
        this.temp.checkData = '0:0:0:0:0:0:0:0'
        this.temp.endIp = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      }
    },
    ipValidator(rule, value, callback) {
      if (this.temp.limitType == 2) { // 域名、命令
        callback()
        return
      }
      if (this.temp.limitType == 0) { // ipv4
        const re = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/
        if (value && re.test(value) && RegExp.$1 < 256 && RegExp.$2 < 256 && RegExp.$3 < 256 && RegExp.$4 < 256) {
          if (this.temp.checkData && this.temp.endIp) {
            const temp1 = this.temp.checkData.split('.')
            const temp2 = this.temp.endIp.split('.')
            let flag = false
            for (let i = 0; i < 4; i++) {
              if (temp1[i] - temp2[i] == 0) {
                continue
              } else if (temp1[i] - temp2[i] > 0) {
                flag = true
              }
              break
            }
            if (flag) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      } else if (this.temp.limitType == 1) { // ipv6
        if (value && isIPv6(value)) {
          if (this.temp.checkData && this.temp.endIp) {
            const fullbeginIpv6 = this.getFullIPv6(this.temp.checkData)
            const fullendIpv6 = this.getFullIPv6(this.temp.endIp)
            if (fullbeginIpv6 > fullendIpv6) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0')
        } else {
          ret += t.padStart(4, '0')
        }
      }
      return ret.toLocaleLowerCase()
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = (rowDatas || []).length > 0
    }
  }
}
</script>

<style scoped>

</style>
