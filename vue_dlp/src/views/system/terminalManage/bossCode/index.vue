<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addAuthorizationCode') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteAuthorizationCode') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.authorizationCode')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="codeList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addBosssAuthorization')"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="90px" style="width: 500px;">
        <FormItem :label="$t('pages.authorizationCode')" prop="code">
          <el-tooltip slot="tooltip" class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.bossCode_Content1') }}<br/>
              {{ $t('pages.bossCode_Content2') }}<br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-input v-model="temp.code" disabled></el-input>
        </FormItem>
        <FormItem prop="startTime" :label="$t('pages.initialValidityPeriod')">
          <el-date-picker
            v-model="temp.startTime"
            type="datetime"
            :clearable="false"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
            @change="timeRangeChange"
          ></el-date-picker>
        </FormItem>
        <FormItem prop="endTime" :label="$t('pages.expirationDate')">
          <el-radio v-model="endTimeType" label="1">{{ $t('pages.timeInterval') }}</el-radio>
          <el-radio v-model="endTimeType" label="2">{{ $t('pages.timeSelector') }}</el-radio>
          <el-card v-if="endTimeType==='1'">
            <el-row>
              <el-col :span="8">
                <el-input-number v-model="hourSize" size="small" :precision="0" :min="0" :max="24" @blur="e => timeRangeBlur('hourSize')" @change="timeRangeChange"></el-input-number>
              </el-col>
              <el-col :span="4" style="padding-left: 5px;">{{ $t('text.hour') }}</el-col>
              <el-col :span="8">
                <el-input-number v-model="minuteSize" size="small" :precision="0" :min="0" :max="59" @blur="e => timeRangeBlur('minuteSize')" @change="timeRangeChange"></el-input-number>
              </el-col>
              <el-col :span="4" style="padding-left: 5px;">{{ $t('text.minute') }}</el-col>
            </el-row>

          </el-card>
          <el-card v-if="endTimeType==='2'">
            <el-date-picker
              v-model="temp.endTime"
              type="datetime"
              :clearable="false"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 98%;"
            ></el-date-picker>
          </el-card>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBossCode, addBossCode, deleteBossCode, getBossCodePage } from '@/api/system/terminalManage/terminal'
import { parseTime } from '@/utils'

export default {
  name: 'BossCode',
  data() {
    return {
      colModel: [
        { prop: 'code', label: 'authorizationCode', width: '100', sort: true },
        { prop: 'endTime', label: 'termValidity', width: '250', sort: true, formatter: this.enableTimeFormatter },
        { prop: 'createTime', label: 'createTime', width: '150', sort: true }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      editable: true,
      deleteable: false,
      temp: { },
      defaultTemp: {
        id: undefined,
        startTime: undefined,
        endTime: undefined,
        code: undefined
      },
      dialogFormVisible: false,
      rules: {
        startTime: [{ required: true, message: this.$t('pages.bossCode_Validate1'), trigger: 'blur' }],
        endTime: [
          { required: true, message: this.$t('pages.bossCode_Validate2'), trigger: 'blur' },
          { validator: this.timeValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      endTimeType: '1',
      hourSize: 12,
      minuteSize: 0
    }
  },
  computed: {
    gridTable() {
      return this.$refs['codeList']
    }
  },
  watch: {
    endTimeType(val) {
      this.timeRangeChange()
      this.$refs['dataForm'].validateField('endTime')
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getBossCodePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    timeRangeBlur(attr) {
      this[attr] = this[attr] || 0
    },
    timeRangeChange() {
      if (this.endTimeType === '1' || !this.temp.endTime) {
        if (typeof this.temp.startTime === 'object') {
          this.temp.startTime = parseTime(this.temp.startTime, 'y-m-d h:i:s')
        }
        // IE不支持 new Date('y-m-d h:i:s'), 所以将'-'修改为'/'
        const startTime = this.temp.startTime.split('-').join('/')
        const endTime = new Date(startTime)
        endTime.setHours(endTime.getHours() + (this.hourSize || 0))
        endTime.setMinutes(endTime.getMinutes() + (this.minuteSize || 0))
        this.temp.endTime = parseTime(endTime, 'y-m-d h:i:s')
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.hourSize = 12
      this.minuteSize = 0
      this.endTimeType = '1'
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.startTime = new Date()
      this.timeRangeChange()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      if (this.query.parentId) {
        this.temp.parentId = this.query.parentId
      }
      this.dialogFormVisible = true
      getBossCode().then(resp => {
        this.temp.code = resp.data
      })
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          addBossCode(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogFormVisible = false
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteBossCode({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
      })
    },
    enableTimeFormatter(row, data) {
      return row.startTime + this.$t('pages.till') + row.endTime
    },
    timeValidator(rule, value, callback) {
      if (this.temp.startTime > this.temp.endTime) {
        callback(this.$t('pages.bossCode_Validate3'))
      } else {
        callback()
      }
    }
  }
}
</script>
