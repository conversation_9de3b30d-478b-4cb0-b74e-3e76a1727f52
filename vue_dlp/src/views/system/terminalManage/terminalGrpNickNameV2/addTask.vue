<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.standardizeTasksInfo', { name: funcName })"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="20px">
        <el-row v-if="!restart">
          <el-col :span="span1">
            <FormItem v-if="temp.dataIds && temp.dataIds.length > 0">
              <el-radio v-model="temp.type" :label="1" @change="typeChangeFunc">{{ $t('pages.terminalAddTaskType1', { name: funcName }) }}</el-radio>
            </FormItem>
          </el-col>
          <el-col :span="span2">
            <FormItem>
              <el-radio v-model="temp.type" :label="2" @change="typeChangeFunc">{{ $t('pages.terminalAddTaskType2', { name: funcName }) }}</el-radio>
            </FormItem>
          </el-col>
          <el-col :span="9">
            <FormItem v-if="temp.type===2" label-width="0px" prop="groupId">
              <tree-select
                ref="groupTree"
                :disabled="temp.type !== 2"
                :data="groupTreeData"
                node-key="dataId"
                is-filter
                :width="290"
                @change="groupIdSelectChange"
              />
            </FormItem>
          </el-col>
        </el-row>

        <el-divider v-if="specialStatusVisible" content-position="left">
          {{ $t('pages.terminalSelectionProcessingModeInTheFollowingStates', { total: termStatus.total}) }}
        </el-divider>
        <el-row style="padding-left: 20px;">
          <el-col v-if="termStatus.doing" :span="10">
            <label>{{ $t('pages.terminalsBeingCollectedNumber', { num: termStatus.doing }) }}</label>
          </el-col>
          <el-col v-if="termStatus.doing" :span="14">
            <el-radio v-model="dealStatus.doing" :label="0" @change="refreshTerm">{{ $t('pages.ignoreTerminalBeingCollected') }}</el-radio>
            <el-radio v-model="dealStatus.doing" :label="4" @change="refreshTerm">{{ $t('button.restartTask') }}</el-radio>
          </el-col>
          <el-col v-if="termStatus.done" :span="10">
            <label>{{ $t('pages.terminalCompletingCollection', { num: termStatus.done} ) }}</label>
          </el-col>
          <el-col v-if="termStatus.done" :span="14">
            <el-radio v-model="dealStatus.done" :label="0" @change="refreshTerm">{{ $t('pages.ignoreAcquisitionCompletionTerminal') }}</el-radio>
            <el-radio v-model="dealStatus.done" :label="8" @change="refreshTerm">{{ $t('button.restartTask') }}</el-radio>
          </el-col>
          <el-col v-if="termStatus.finish" :span="10">
            <label>{{ $t('pages.terminalCompleteSynchronization', { num: termStatus.finish }) }}</label>
          </el-col>
          <el-col v-if="termStatus.finish" :span="14">
            <el-radio v-model="dealStatus.finish" :label="0" @change="refreshTerm">{{ $t('pages.ignoreSynchronizationCompletionTerminal') }}</el-radio>
            <el-radio v-model="dealStatus.finish" :label="16" @change="refreshTerm">{{ $t('button.restartTask') }}</el-radio>
          </el-col>
          <el-col v-if="termStatus.offline" :span="12">
            <label>{{ $t('pages.offlineTerminalNumber', { num: termStatus.offline }) }}</label>
          </el-col>
          <el-col v-if="termStatus.onlineNoWindowsTerm" :span="12">
            <label>{{ $t('pages.onlineNoWindowsTerm', { num: termStatus.onlineNoWindowsTerm }) }}</label>
          </el-col>
        </el-row>
        <el-divider v-if="specialStatusVisible" content-position="left">
          {{ $t('pages.terminalTaskHaveBeenFilterOnlyFollowingTerminals', { name: funcName }) }}
        </el-divider>
        <el-divider v-if="!specialStatusVisible" content-position="left">
          {{ $t('pages.terminalTaskOnlyFollowingTerminals', { name: funcName }) }}
        </el-divider>
        <FormItem v-if="temp.type===2" label-width="0px">
          <el-checkbox v-model="multiSelect">{{ $t('pages.terminal_text26') }}</el-checkbox>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <span>
                <i18n path="pages.selectTerminalTaskTips">
                  <br slot="br"/>
                </i18n>
              </span>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <grid-table
          ref="termTable"
          :col-model="terminalColModel"
          :row-data-api="rowDataApi"
          :height="200"
          :multi-select="multiSelect"
          :is-saved-selected="multiSelect"
          pager-small
        />
      </Form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="addTask">{{ $t('button.confirm') }}</el-button>
        <el-button @click="hide">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getTerminalStatus, startCollectTerminal, getActiveTermPage } from '@/api/system/terminalManage/terminalGroupNick'

export default {
  name: 'AddGrepNickNameTask',
  props: {
    groupTreeData: { type: Array, default() { return [] } },
    exportFunc: { // 上传函数
      type: Function,
      default: null
    },
    restart: { // 是否重启任务，与新增任务进行区分
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      specialStatusVisible: false,
      multiSelect: false,
      funcName: '',
      errorMsg: '',
      terminalColModel: [
        { prop: 'name', label: 'terminalName', width: '150', sort: 'custom', fixed: true },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'computerName', label: 'computerName', width: '150', sort: 'custom' },
        { prop: 'mainIp', label: 'IP', width: '150', sort: 'custom' },
        { prop: 'mainMac', label: 'macAddr', width: '150' }
      ],
      terminalRowData: [],
      termStatus: {
        total: 0,    // 总共
        offline: 0,  // 离线终端
        onlineNoWindowsTerm: 0, // 在线非 windows 终端
        doing: 0,    // 正在采集
        done: 0,     // 采集完成
        finish: 0    // 同步完成
      },
      dealStatus: {
        offline: 0,
        doing: 0,    // 正在采集
        done: 0,     // 采集完成
        finish: 0    // 同步完成
      },
      temp: {
        type: 2,
        groupId: undefined,
        dataIds: []
      },
      rules: {
      }
    }
  },
  computed: {
    span1() {
      if (this.isEnglish() && this.temp.dataIds && this.temp.dataIds.length > 0) {
        return 24;
      }
      return 7;
    },
    span2() {
      return this.isEnglish() ? 12 : 8;
    }
  },
  watch: {
    'multiSelect'() {
      this.$refs['termTable'].selectedDatasDelete()
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show(dataIds) {
      this.resetTemp()
      this.multiSelect = false
      this.dialogVisible = true
      Object.assign(this.temp, {
        type: 2,
        groupId: undefined,
        dataIds: dataIds
      })
      if (this.temp.dataIds && this.temp.dataIds.length > 0) {
        this.temp.type = 1
        this.getTerminalStatus({
          termList: this.temp.dataIds
        })
      }
      this.$nextTick(() => {
        if (this.restart) {
          this.funcName = this.$t('pages.reissue')
          Object.assign(this.dealStatus, {
            doing: 4,    // 正在采集
            done: 8,     // 采集完成
            finish: 16    // 同步完成
          })
        } else {
          this.funcName = this.$t('text.add')
        }
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
      this.refreshTerm()
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
      this.specialStatusVisible = false
      this.errorMsg = ''
      this.termStatus = {
        total: 0,    // 总共
        offline: 0,  // 离线终端
        doing: 0,    // 正在采集
        done: 0,     // 采集完成
        finish: 0    // 同步完成
      }
      this.dealStatus = {
        offline: 0,
        doing: 0,    // 正在采集
        done: 0,     // 采集完成
        finish: 0    // 同步完成
      }
      this.temp.groupId = null
      this.$refs['groupTree'] && this.$refs['groupTree'].clearSelectedNode()
    },
    typeChangeFunc(val) {
      this.resetTemp()
      if (val === 1) {
        this.getTerminalStatus({
          termList: this.temp.dataIds
        })
      } else {
        this.getTerminalStatus({
          groupId: Number.parseInt(this.temp.groupId)
        })
      }
    },
    refreshTerm() {
      this.$refs['termTable'] && this.$refs['termTable'].execRowDataApi({ page: 1 })
    },
    rowDataApi: function(option) {
      const vo = {}
      if (this.temp.type === 2) {
        vo.groupId = this.temp.groupId == null ? -1 : this.temp.groupId
      } else {
        vo.termList = this.temp.dataIds
      }
      vo.addTaskType = 0;
      for (const key in this.dealStatus) {
        vo.addTaskType += this.dealStatus[key]
      }
      vo.types = '0,16' // 终端类型限制windows 和 老板windows '0,16'
      const searchQuery = Object.assign({}, vo, option)
      return getActiveTermPage(searchQuery)
    },
    getTerminalStatus(data) {
      getTerminalStatus(data).then(resp => {
        this.termStatus = resp.data
        this.specialStatusVisible = 0
        if (this.termStatus) {
          const keys = ['offline', 'doing', 'done', 'finish']
          for (const key in this.termStatus) {
            if (keys.indexOf(key) > -1) {
              this.specialStatusVisible += this.termStatus[key]
            }
          }
        }
        this.refreshTerm()
      })
    },
    groupIdSelectChange(data, node, vm) {
      this.temp.groupId = data
      if (data) {
        this.getTerminalStatus({
          groupId: Number.parseInt(data)
        })
      }
    },
    handleDrag() {
    },
    addTask() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const vo = {}
          const selectIds = this.$refs['termTable'].getSelectedKeys()
          if (this.multiSelect && selectIds.length === 0) {
            this.$message({
              message: this.$t('pages.pleaseSelectContent', { content: this.$t('table.terminal') }),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return false
          }
          if (this.temp.type === 2) {
            if (this.temp.groupId == null) {
              this.$message({
                message: this.$t('pages.pleaseSelectAtLeastOneDepartment', { content: this.$t('pages.dept') }),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
              return false
            }
            if (this.multiSelect) {
              vo.groupId = undefined
              vo.termList = selectIds.length > 0 ? selectIds : Array.of(0)
            } else {
              vo.groupId = this.temp.groupId
            }
          } else {
            vo.termList = this.temp.dataIds
            if (this.multiSelect) {
              vo.termList = selectIds.length > 0 ? selectIds : Array.of(0)
            }
          }
          vo.addTaskType = 0;
          for (const key in this.dealStatus) {
            vo.addTaskType += this.dealStatus[key]
          }
          console.log('getSelectedKeys', this.$refs['termTable'].getSelectedKeys())
          startCollectTerminal(vo).then(resp => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.grpNickName_text3'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false
            this.dialogVisible = false
            this.$emit('submitEnd', resp)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
