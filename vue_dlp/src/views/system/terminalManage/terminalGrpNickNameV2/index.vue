<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :local-search="false"
        :get-search-list="getSearchListFunction"
        :render-content="renderContent"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <!--        可支持3.55版本以上离线终端-->
        <!--        <el-checkbox v-model="query.taskSendStatus">任务下发</el-checkbox>-->
        <!--        <el-tooltip class="item" effect="dark" placement="bottom-start">-->
        <!--          <div slot="content">-->
        <!--            <span>-->
        <!--              （仅支持3.55以上新版本终端搜集）-->
        <!--              &lt;!&ndash;                <i18n path="pages.selectTerminalTaskTips">&ndash;&gt;-->
        <!--              &lt;!&ndash;                  <br slot="br"/>&ndash;&gt;-->
        <!--              &lt;!&ndash;                </i18n>&ndash;&gt;-->
        <!--            </span>-->
        <!--          </div>-->
        <!--          <i class="el-icon-info" />-->
        <!--        </el-tooltip>-->
        <el-button v-if="isTaskTab() && !query.taskSendStatus" size="mini" :loading="submitting" @click="handleAddTask">
          {{ $t('button.addTask') }}
        </el-button>
        <el-button v-if="isTaskTab() && !query.taskSendStatus" size="mini" :loading="submitting" type="primary" :disabled="!restartAble" @click="deleteTerminalHandle">
          {{ $t('button.deleteTask') }}
        </el-button>
        <el-button v-if="isTaskTab() && !query.taskSendStatus" size="mini" :loading="submitting" type="primary" :disabled="!restartAble" @click="handleRestartTask">
          {{ $t('button.restartTask') }}
        </el-button>
        <el-button v-if="isTaskTab() && query.taskSendStatus" size="mini" :loading="submitting" @click="handleAddTask2">
          {{ $t('button.addTask') }}
        </el-button>
        <el-button v-if="isTaskTab() && query.taskSendStatus" size="mini" :loading="submitting" type="primary" :disabled="!restartAble" @click="deleteTerminalHandleV2">
          {{ $t('button.deleteTask') }}
        </el-button>
        <!--        <el-button v-if="isTaskTab() && query.taskSendStatus" size="mini" :loading="submitting" type="primary" :disabled="!restartAble" @click="handleRestartTask2">-->
        <!--          {{ $t('button.restartTask') }}-->
        <!--        </el-button>-->
        <el-button v-if="isTaskTab()" size="mini" :loading="submitting" @click="synGrpNickNameHandle">
          {{ $t('pages.syncGroupNickName') }}
        </el-button>
        <el-dropdown style="padding-left: 10px;" @command="handleMoreClick">
          <el-button size="mini" type="primary">
            {{ $t('pages.chatAllLog_Msg3') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">终端版本低于5.01</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <div class="searchCon">
          <el-select v-model="query.status" style="width: 110px;" @change="handleFilter">
            <el-option v-for="(status, i) in collectStatusOption" :key="i" :label="status.label" :value="status.value"></el-option>
          </el-select>
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.terminalNameOrNumber')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <!-- <el-tabs ref="tabs" v-model="activeTabName" type="card" @tab-click="tabClick" >
        <el-tab-pane label="规范任务" name="taskTab"> -->
      <div class="table-container">
        <grid-table
          ref="termGrpNickNameTable"
          row-key="termId"
          :col-model="taskColModel"
          :row-data-api="rowDataApi"
          :autoload="false"
          :multi-select="multiSelect"
          @selectionChangeEnd="taskTableSelectionChange"
        />
      </div>
      <!-- </el-tab-pane>
        <el-tab-pane label="在线终端" name="termTab">
          <div class="table-container">
            <grid-table
              ref="termTable"
              :col-model="termColModel"
              :row-data-api="termRowDataApi"
              :autoload="false"
              :multi-select="multiSelect"
              @selectionChangeEnd="termTableSelectionChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs> -->
    </div>

    <syn-grp-nick-name-dlg ref="synGrpNickNameDlg" @submitEnd="handleRefresh"/>
    <add-grep-nick-name-task ref="addGrepNickNameTask" :group-tree-data="treeData" :restart="isRestart" @submitEnd="handleRefresh"/>
    <add-grep-nick-name-task-V2 ref="addGrepNickNameTaskV2" :group-tree-data="treeData" :restart="isRestart" @submitEnd="handleRefresh"/>
  </div>
</template>
<script>
import { initTimestamp } from '@/utils';
import { getTerminalGroupNickNamePage, deleteTerminalGroupNickName, deleteTerminalGroupNickNameV2 } from '@/api/system/terminalManage/terminalGroupNick'
import { getTerminalPage } from '@/api/system/terminalManage/terminal'
import { termStatusIconFormatter, deptNameFormatter } from '@/utils/formatter'
import SynGrpNickNameDlg from './synGrpNickNameDlg'
import AddGrepNickNameTask from './addTask'
import AddGrepNickNameTaskV2 from '@/views/system/terminalManage/terminalGrpNickNameV2/addTask2.vue';
import { addRecycleNode } from '@/utils/tree';
export default {
  name: 'TerminalGrpNickNameV2',
  components: { AddGrepNickNameTaskV2, AddGrepNickNameTask, SynGrpNickNameDlg },
  data() {
    return {
      // 公共参数
      submitting: false, // 按钮的加载
      showTree: true,
      restartAble: false,
      isRestart: false,
      treeData: [],
      defaultExpandedKeys: ['G0'],
      groupTreeSelectData: [],
      activeTabName: 'taskTab',
      // 列表
      multiSelect: true,
      // isTaskSend: true,
      taskColModel: [
        { prop: 'status', label: 'taskStatus', fixedWidth: '150', fixed: true, formatter: this.collectStatusFormatter },
        { prop: 'name', label: 'terminalName', width: '150', fixed: true, sort: 'custom', iconFormatter: termStatusIconFormatter },
        { prop: 'termId', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'oldGroupIds', label: 'currentDept', width: '150', formatter: this.groupFormatter },
        { prop: 'groupName', label: 'collectionDept', width: '150' },
        { prop: 'nickName', label: 'collectionName', width: '150', sort: 'custom' },
        { prop: 'version', label: 'terminalVersion', width: '150' },
        { prop: 'createTime', label: 'updateTime', width: '200' }
      ],
      termColModel: [
        { prop: 'name', label: 'terminalName', width: '150', fixed: true, sort: 'custom', iconFormatter: termStatusIconFormatter },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'groupIds', label: 'terminalGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'computerName', label: 'computerName', width: '150', sort: 'custom' },
        { prop: 'mainIp', label: 'IP', width: '150', sort: 'custom' },
        { prop: 'mainMac', label: 'macAddr', width: '150' },
        { prop: 'version', label: 'terminalVersion', width: '150' },
        { prop: 'lastOnlineTime', label: 'lastOnlineTime', width: '200' }
      ],
      query: { // 查询条件
        page: 1,
        groupIds: '',
        searchInfo: '',
        termStatus: null, // 4 在线
        types: '0,16', // 终端类型限制windows 和 老板windows '0,16'
        status: null,
        name: '',
        searchId: '',
        type: null,
        offlineTime: null,
        taskSendStatus: true // 区分新旧版本（true:终端版本>5.01.0318;false:旧版终端）
      },
      currentCheckGroup: {},
      collectStatusOption: [
        { label: this.$t('pages.allTerminal'), value: null },
        { label: this.$t('pages.statusOpt2'), value: 1 },
        { label: this.$t('pages.statusOpt3'), value: 2 },
        { label: this.$t('pages.statusOpt4'), value: 3 },
        { label: this.$t('pages.statusOpt5'), value: 4 }
      ],
      collectStatus: {
        undefined: this.$t('pages.statusOpt2'),
        1: this.$t('pages.statusOpt2'),
        2: this.$t('pages.statusOpt3'),
        3: this.$t('pages.statusOpt4'),
        4: this.$t('pages.statusOpt5')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['termGrpNickNameTable']
    },
    termTable() {
      return this.$refs['termTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    }
  },
  watch: {
    '$store.state.commonData.deptTree'() {
      this.initGroupTreeNode()
    },
    '$store.state.commonData.notice.terminalGrpNickName'(val) {
      this.synGrpNickNameHandle()
    }
  },
  created() {
    initTimestamp(this)
    this.initGroupTreeNode()
    this.$nextTick(() => {
      if (this.$route.query.hasOwnProperty('status')) {
        this.initFromDataPanel()
      } else {
        this.handleRefresh()
      }
    })
  },
  activated() {
    // if (!isSameTimestamp(this, 'Department')) {
    this.initGroupTreeNode()
    this.gridTable.execRowDataApi(this.query)
    // }
  },
  methods: {
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    isTaskTab() {
      return this.activeTabName === 'taskTab'
    },
    tabClick(pane, event) {
      this.handleFilter()
    },
    handleMoreClick(type) {
      this.$router.push({ name: 'terminalGrpNickName' })
    },
    initGroupTreeNode: function() {
      const treeNode = JSON.parse(JSON.stringify(this.$store.getters.deptTree))
      addRecycleNode(treeNode, null)
      console.log('treeNode', treeNode)
      // 分组，不允许选择公司根节点
      if (Array.isArray(treeNode) && treeNode.length === 1 && treeNode[0].dataId == 0) {
        this.groupTreeSelectData = treeNode[0].children
      } else {
        this.groupTreeSelectData = treeNode
      }
      this.treeData = treeNode
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    renderContent(h, { node, data, store }) {
      return h('div', { 'class': { 'custom-tree-node': data.type === 'D' }}, [h('span', data.label)])
    },
    // 单击树节点的回调函数
    handleNodeClick(data, node) {
      console.log('handleNodeClick data', data)
      console.log('handleNodeClick node', node)
      node.expanded = true
      this.resetQuery()
      this.query.groupId = data.dataId
      // 缓存部门节点
      this.currentCheckGroup = data
      this.handleFilter()
    },
    resetQuery() {
      this.query.page = 1
      this.query.groupId = null
      this.query.type = null
      this.query.status = null
      this.query.termStatus = null
      this.query.types = '0,16'  // 终端类型限制windows 和 老板windows '0,16'
      this.query.oldNickName = ''
      this.query.searchId = ''
      this.query.offlineTime = null
      this.query.taskSendStatus = true
    },
    // 列表
    isActive(row) {
      return row.loginStatus && row.loginStatus.status != 0 ? { class: 'active', title: this.$t('pages.online') } : { class: 'offline', title: this.$t('pages.offline'), style: 'color: #888;' }
    },
    groupFormatter: function(row, data, col) {
      return this.html2Escape(deptNameFormatter(row, data, col, this.treeData))
    },
    collectStatusFormatter: function(row, data) {
      return this.collectStatus[data]
    },
    /**
     * 终端分组和名称规范列表数据的获取
     * @param option
     * @returns {AxiosPromise}
     */
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      console.log('rowDataApi', searchQuery)
      const promise = getTerminalGroupNickNamePage(searchQuery)
      promise.then(res => {
        this.grpNickNameListTotal = res.data.total
      })
      return promise
    },
    termRowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option, {
        status: 4, // 只查询在线终端
        includeSubGroup: true // 查询选中部门及子部门数据
      })
      return getTerminalPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.handleRefresh()
    },
    handleRefresh() {
      // if (this.isTaskTab()) {
      console.log('this.query', this.query)
      this.$refs['termGrpNickNameTable'].execRowDataApi(this.query)
      // } else {
      //   this.termTable.execRowDataApi(this.query)
      // }
    },
    taskTableSelectionChange(rowDatas) {
      this.restartAble = rowDatas.length > 0
    },
    termTableSelectionChange(rowDatas) {
    },
    // 功能区 协议下发
    handleAddTask() {
      this.isRestart = false
      const selectedDatas = this.gridTable.getSelectedKeys()
      this.$refs['addGrepNickNameTask'].show(selectedDatas)
    },
    handleRestartTask() {
      this.isRestart = true
      const selectedDatas = this.gridTable.getSelectedKeys()
      this.$refs['addGrepNickNameTask'].show(selectedDatas)
    },
    // 任务下发
    handleAddTask2() {
      this.isRestart = false
      const selectedDatas = this.gridTable.getSelectedKeys()
      const vo = { 'dataIds': selectedDatas, 'group': this.currentCheckGroup }
      this.$refs['addGrepNickNameTaskV2'].show(vo)
    },
    handleRestartTask2() {
      this.isRestart = true
      const selectedDatas = this.gridTable.getSelectedKeys()
      const vo = { 'dataIds': selectedDatas, 'group': this.currentCheckGroup }
      this.$refs['addGrepNickNameTaskV2'].show(vo)
    },
    // 清空终端收集数据
    deleteTerminalHandle() {
      this.$confirmBox(this.$t('pages.grpNickName_text1'), this.$t('text.prompt')).then(() => {
        const selectedIds = this.gridTable.getSelectedKeys()
        deleteTerminalGroupNickName({ termList: selectedIds }).then(respond => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.handleRefresh()
        })
      }).catch(() => {})
    },
    deleteTerminalHandleV2() {
      this.$confirmBox(this.$t('pages.grpNickName_text1'), this.$t('text.prompt')).then(() => {
        const selectedIds = this.gridTable.getSelectedKeys()
        deleteTerminalGroupNickNameV2({ termList: selectedIds }).then(respond => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.handleRefresh()
        })
      }).catch(() => {})
    },
    synGrpNickNameHandle() {
      const selectedDatas = this.gridTable.getSelectedKeys()
      this.$refs.synGrpNickNameDlg.show(selectedDatas)
    }
  }

}
</script>
