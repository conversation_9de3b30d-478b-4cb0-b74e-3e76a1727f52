<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.softType" :value="query.uninstallResult">
          <span>{{ $t('table.uninstallResult') }}：</span>
          <el-select v-model="query.uninstallResult" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in uninstallResultOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'634'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'633'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
  </div>
</template>

<script>
import { getPage, exportSoftwareUnauthorAuditLog, deleteLog } from '@/api/behaviorAuditing/terminal/softwareUnauthorAuditLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'SoftwareUnauthorAuditLog',
  components: {},
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchType: 'user', searchParam: 'userId' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'softwareName', label: 'softwareName', width: '150' },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
        { prop: 'uninstallResult', label: 'uninstallResult', width: '100', formatter: this.uninstallResultFormatter }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        uninstallResult: undefined,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      temp: {
        typeId: undefined,
        processList: []
      },
      osType: 1,
      submitting: false,
      submitting1: false,
      submitting2: false,
      selection: [],
      dialogVisible: false,
      dialogFormVisible: false,
      existDialogVisible: false,
      rowDetail: {},
      uninstallResultOptions: {
        0: this.$t('text.fail'), 1: this.$t('text.success')
      },
      showTree: true,
      sortable: true,
      dialogEditVisible: false,
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  watch: {
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    // 新增分组数据后的回调
    groupSubmitEnd(data, dlgStatus) {
      // 通知安装/卸载库树刷新
      this.$store.dispatch('commonData/changeNotice', 'installAppLibGroup')
      const nodeData = {
        dataId: data.id.toString(),
        label: data.name,
        parentId: '0'
      }
      this.appTypeTreeData.push(nodeData)
      this.$nextTick(() => {
        this.temp.typeId = nodeData.dataId
      })
      this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp.typeId = undefined
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    uninstallResultFormatter(row, data) {
      if (row.uninstallResult === 1) {
        return this.$t('text.success')
      } else {
        return this.$t('text.fail')
      }
    },
    handleExport(exportType) {
      return exportSoftwareUnauthorAuditLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      // this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '315', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
