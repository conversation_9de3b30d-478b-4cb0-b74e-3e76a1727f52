<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="strategySelectionChangeEnd"
      />
    </div>
    <offline-lock-screen-dlg
      ref="offlineLockScreenDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      :terminal-filter="terminalFilter"
      @submitEnd="submitEnd"
    />
  </div>
</template>
<script>
import { deleteStrategy, getStrategyPage } from '@/api/behaviorManage/application/offlineLockScreen'

import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import OfflineLockScreenDlg from './editDlg'

export default {
  name: 'OfflineLockScreen',
  components: { OfflineLockScreenDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      stgCode: 210,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: {
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        configs: [],
        timeId: 1,
        timeName: '',   // 时间段名称，记录管理员日志用的
        entityType: undefined,
        entityId: undefined,
        ruleId: ''
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.stgNameRequired'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      funcOptions: [],
      checkedKeys: [],
      checkedEntityNode: {},
      editable: true
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTable']
    }
  },
  watch: {
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },

  methods: {
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategySelectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.editable = true
      this.$refs['offlineLockScreenDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.editable = true
      this.$refs['offlineLockScreenDlg'].handleUpdate(row)
    },
    submitEnd() {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    handleRefresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    strategyFormatter: function(row, data) {
      let stgmsg = ''
      const serverUnit = row.disconnectServerUnit == 1 ? this.$t('text.minute') : (row.disconnectServerUnit == 2 ? this.$t('text.hour') : this.$t('text.day'))
      const serverTime = serverUnit == this.$t('text.minute') ? row.disconnectServer : serverUnit == this.$t('text.hour') ? row.disconnectServer / 60 : row.disconnectServer / 24 / 60
      const unLockUnit = row.unLockUnit == 1 ? this.$t('text.minute') : (row.unLockUnit == 2 ? this.$t('text.hour') : this.$t('text.day'))
      const unlockTime = unLockUnit == this.$t('text.minute') ? row.unlockTime : unLockUnit == this.$t('text.hour') ? row.unlockTime / 60 : row.unlockTime / 24 / 60
      if (row.isDisconnectServer) {
        stgmsg += this.$t('pages.lockScreenText1') + serverTime + `${this.$store.getters.language === 'en' ? ' ' : ''}` + serverUnit + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('pages.lockScreenText23')
      }
      if (row.isLockAlarm && row.beforeLockTime) {
        stgmsg += this.$t('pages.lockScreenText2') + `${this.$store.getters.language === 'en' ? ' ' : ''}` + row.beforeLockTime + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('text.minute') + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('pages.lockScreenText24')
      }
      if (row.isUnLock) {
        stgmsg += this.$t('pages.afterLockScreen') + `${this.$store.getters.language === 'en' ? ' ' : ''}` + unlockTime + `${this.$store.getters.language === 'en' ? ' ' : ''}` + unLockUnit + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('pages.lockScreenText5') + ';'
      }
      if (row.reConnectUnlock == 1) {
        stgmsg += this.$t('pages.screenLockMsg')
      }
      if (!row.isDisconnectServer && !(row.isLockAlarm && row.beforeLockTime) && !row.isUnLock && row.reConnectUnlock == 2) {
        stgmsg = this.$t('pages.screenLockMsg1')
      }
      return stgmsg
    }
  }
}
</script>
