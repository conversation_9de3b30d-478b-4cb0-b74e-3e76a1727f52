<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('route.offlineLockScreen')"
    :stg-code="210"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :get-by-name="getStrategyByName"
    :format-row-data="formatRowData"
    :format-form-data="formatFormData"
    :validate-form="validateFormData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
    @closed="closed"
  >
    <template :slot="slotName">
      <div style="padding-left: 10px">
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label-width="60px" prop="disconnectServer" :class="langClass + '-validate-position1'">
          <el-checkbox v-model="temp.isDisconnectServer" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.disconnectServer') }}</el-checkbox>
          <el-input-number v-model="temp.disconnectServer" :disabled="!temp.isDisconnectServer || !formable" style="width: 90px" :controls="false" :min="disconnectTimeMin" :max="disconnectTimeMax" :precision="0"></el-input-number>
          <el-select v-model="temp.disconnectServerUnit" style="width: 80px;margin-bottom: 4px" :disabled="!temp.isDisconnectServer || !formable">
            <el-option :label="$t('text.day')" :value="3"/>
            <el-option :label="$t('text.hour')" :value="2"/>
            <el-option :label="$t('text.minute')" :value="1"/>
          </el-select>
          <span v-show="temp.isDisconnectServer" style="color: #409EFF">{{ $t('pages.doLockScreen') }}</span>
          <span v-show="!temp.isDisconnectServer" style="color: #666">{{ $t('pages.doLockScreen') }}</span>
        </FormItem>
        <FormItem label-width="60px" prop="beforeLockTime" :class="langClass + '-validate-position2'">
          <el-checkbox v-model="temp.isLockAlarm" :true-label="1" :false-label="0" :disabled="!formable || !temp.isDisconnectServer">{{ $t('pages.beforreLock') }}</el-checkbox>
          <el-input-number v-model="temp.beforeLockTime" :disabled="!temp.isLockAlarm || !formable" style="width: 90px" :controls="false" :min="1" :max="5256000" :precision="0"></el-input-number>
          <span v-show="temp.isLockAlarm && temp.isDisconnectServer" style="color: #409EFF">{{ $t('pages.lockAlarm') }}</span>
          <span v-show="!temp.isLockAlarm || !temp.isDisconnectServer" style="color: #666">{{ $t('pages.lockAlarm') }}</span>
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.lockScreenText4') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem label-width="60px" prop="unlockTime" :class="langClass + '-validate-position3'">
          <el-checkbox v-model="temp.isUnLock" :true-label="1" :false-label="0" :disabled="!formable || !temp.isDisconnectServer">{{ $t('pages.afterLockScreen') }}</el-checkbox>
          <el-input-number v-model="temp.unlockTime" :disabled="!temp.isUnLock || !formable" style="width: 90px" :controls="false" :min="1" :max="unlockTimeMax" :precision="0"></el-input-number>
          <el-select v-model="temp.unLockUnit" style="width: 80px;margin-bottom: 4px" :disabled="!temp.isUnLock || !formable">
            <el-option :label="$t('text.day')" :value="3"/>
            <el-option :label="$t('text.hour')" :value="2"/>
            <el-option :label="$t('text.minute')" :value="1"/>
          </el-select>
          <span v-show="temp.isUnLock && temp.isDisconnectServer" style="color: #409EFF">{{ $t('pages.lockScreenText5') }}</span>
          <span v-show="!temp.isUnLock || !temp.isDisconnectServer" style="color: #666">{{ $t('pages.lockScreenText5') }}</span>
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.lockScreenText6') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem label-width="60px">
          <el-checkbox v-model="temp.reConnectUnlock" :true-label="1" :false-label="2" :disabled="!formable || !temp.isDisconnectServer">{{ $t('pages.lockScreenText7') }}</el-checkbox>
        </FormItem>
        <div style="margin-left: 50px;margin-top: 10px; font-size: 14px; color: #666">
          <span style="color: #2b7aac;font-size: 13px">{{ $t('pages.lockScreenText8') }}<a style="color:red;" @click="()=>{if( hasPermission('B24')) this.$router.push('/terminalManage/terminalManage/terminal')}">{{ `${this.$store.getters.language==='en'?' ':''}` + $t('pages.lockScreenText9') + `${this.$store.getters.language==='en'?' ':''}` }}</a>{{ $t('pages.lockScreenText10') }}</span>
        </div>
        <div style="margin-left: 50px;margin-top: 10px">
          <span style="color: #2b7aac;font-size: 13px">{{ $t('pages.lockScreenText11') }}<a style="color:red;" @click="()=>{if( hasPermission('B24')) this.$router.push('/terminalManage/terminalManage/terminal')}">{{ `${this.$store.getters.language==='en'?' ':''}` + $t('pages.lockScreenText9') + `${this.$store.getters.language==='en'?' ':''}` }}</a>{{ $t('pages.lockScreenText12') }}</span>
        </div>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <div style="margin-top: 10px">
          <el-row>
            <ResponseContent
              ref="resContent"
              :show-select="true"
              :editable="formable"
              read-only
              :prop-check-rule="true"
              :show-check-rule="false"
              :prop-rule-id="propRuleId"
              :filter-data="filterData"
              :check-empty-rule="!!ruleCheck"
              :select-style="{ 'margin-top': '0px' }"
              @getRuleId="getRuleId"
            >
              <el-tooltip slot="tail" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.lockScreenText13') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </ResponseContent>
          </el-row>
        </div>
      </div>
    </template>
  </stg-dialog>
</template>

<script>
import { createStrategy, getStrategyByName, updateStrategy } from '@/api/behaviorManage/application/offlineLockScreen'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'

export default {
  name: 'OfflineLockScreenDlg',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    editable: { type: Boolean, default: true }, // 能否编辑
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } },
    terminalFilter: { type: Function, default: null } // 终端过滤
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        remark: '',
        active: false,
        entityType: '',
        entityId: '',
        isDisconnectServer: 0,
        disconnectServer: 72,
        disconnectServerUnit: 2,
        isLockAlarm: 0,
        beforeLockTime: 5,
        isUnLock: 0,
        unlockTime: 1,
        unLockUnit: 2,
        reConnectUnlock: 2,
        ruleId: undefined,
        info: ''
      },
      propRuleId: undefined,
      disconnectTimeMin: 1,
      disconnectTimeMax: 87600,
      unlockTimeMax: 87600,
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        disconnectServer: [{ required: true, validator: this.validateDisconnectServer, trigger: 'blur' }],
        beforeLockTime: [{ required: true, validator: this.validateBeforeLockTime, trigger: 'blur' }],
        unlockTime: [{ required: true, validator: this.validateUnlockTime, trigger: 'blur' }]
      },
      langClass: 'zh-tw'
    }
  },
  computed: {
    ruleCheck() {
      return this.temp.isDisconnectServer && this.temp.isLockAlarm && this.temp.ruleId == undefined && this.temp.beforeLockTime != 0;
    }
  },
  watch: {
    'temp.disconnectServerUnit'(newVal, oldVal) {
      if (newVal === 1) {
        this.disconnectTimeMax = 5256000
      } else if (newVal === 2) {
        this.disconnectTimeMax = 87600
        if (this.temp.disconnectServer > 87600) {
          this.temp.disconnectServer = 87600
        }
      } else if (newVal === 3) {
        this.disconnectTimeMax = 3650
        if (this.temp.disconnectServer > 3650) {
          this.temp.disconnectServer = 3650
        }
      }
    },
    'temp.unLockUnit'(newVal, oldVal) {
      if (newVal === 1) {
        this.unlockTimeMax = 5256000
      } else if (newVal === 2) {
        this.unlockTimeMax = 87600
        if (this.temp.unlockTime > 87600) {
          this.temp.unlockTime = 87600
        }
      } else if (newVal === 3) {
        this.unlockTimeMax = 3650
        if (this.temp.unlockTime > 3650) {
          this.temp.unlockTime = 3650
        }
      }
    }
  },
  created() {
    this.resetTemp()
    this.getLanguage()
  },
  activated() {},
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.propRuleId = undefined
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
      if (this.temp.ruleId) {
        this.propRuleId = this.temp.ruleId
      }
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    closed() {
      this.resetTemp()
    },
    handleChange(val) {
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      }, undefined, () => {
        this.$nextTick(() => { // 新增弹框打开时，清除响应规则的选中情况
          this.$refs['resContent'].ruleId = undefined
        })
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.propRuleId = row.ruleId
      this.$refs['stgDlg'].show(row, this.formable, () => {
        if (!row.ruleId && this.$refs['resContent']) {
          this.$refs['resContent'].ruleId = undefined
        }
      })
    },
    formatRowData(rowData) {
      if (rowData.isDisconnectServer) {
        if (rowData.disconnectServerUnit == 2) {
          rowData.disconnectServer = rowData.disconnectServer / 60
        } else if (rowData.disconnectServerUnit == 3) {
          rowData.disconnectServer = rowData.disconnectServer / 24 / 60
        }
      }
      if (rowData.isUnLock) {
        if (rowData.unLockUnit == 2) {
          rowData.unlockTime = rowData.unlockTime / 60
        } else if (rowData.unLockUnit == 3) {
          rowData.unlockTime = rowData.unlockTime / 24 / 60
        }
      }
      if (!rowData.isLockAlarm || rowData.beforeLockTime == 0) {
        rowData.ruleId = undefined
        rowData.isLockAlarm = 0
      }
    },
    formatInfo(data) {
      data.info = ''
      const serverUnit = data.disconnectServerUnit == 1 ? this.$t('text.minute') : (data.disconnectServerUnit == 2 ? this.$t('text.hour') : this.$t('text.day'))
      const serverTime = serverUnit == this.$t('text.minute') ? data.disconnectServer : serverUnit == this.$t('text.hour') ? data.disconnectServer / 60 : data.disconnectServer / 24 / 60
      const unLockUnit = data.unLockUnit == 1 ? this.$t('text.minute') : (data.unLockUnit == 2 ? this.$t('text.hour') : this.$t('text.day'))
      const unlockTime = unLockUnit == this.$t('text.minute') ? data.unlockTime : unLockUnit == this.$t('text.hour') ? data.unlockTime / 60 : data.unlockTime / 24 / 60
      if (data.isDisconnectServer) {
        data.info += this.$t('pages.lockScreenText1') + serverTime + `${this.$store.getters.language === 'en' ? ' ' : ''}` + serverUnit + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('pages.lockScreenText23')
      }
      if (data.isLockAlarm && data.beforeLockTime) {
        data.info += this.$t('pages.lockScreenText2') + `${this.$store.getters.language === 'en' ? ' ' : ''}` + data.beforeLockTime + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('text.minute') + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('pages.lockScreenText24')
      }
      if (data.isUnLock) {
        data.info += this.$t('pages.afterLockScreen') + `${this.$store.getters.language === 'en' ? ' ' : ''}` + unlockTime + `${this.$store.getters.language === 'en' ? ' ' : ''}` + unLockUnit + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('pages.lockScreenText5') + ';'
      }
      if (data.reConnectUnlock == 1) {
        data.info += this.$t('pages.screenLockMsg')
      }
      if (!data.isDisconnectServer && !(data.isLockAlarm && data.beforeLockTime) && !data.isUnLock && data.reConnectUnlock == 2) {
        data.info = this.$t('pages.screenLockMsg1')
      }
    },
    // 格式化数据
    formatFormData(formData) {
      this.dataToMinute(formData)
      this.formatInfo(formData)
      if (!formData.isLockAlarm || formData.beforeLockTime == 0) {
        formData.ruleId = undefined
      }
    },
    dataToMinute(data) {
      if (data.isDisconnectServer) {
        if (data.disconnectServerUnit == 2) {
          data.disconnectServer = data.disconnectServer * 60
        } else if (data.disconnectServerUnit == 3) {
          data.disconnectServer = data.disconnectServer * 24 * 60
        }
        if (data.isUnLock) {
          if (data.unLockUnit == 2) {
            data.unlockTime = data.unlockTime * 60
          } else if (data.unLockUnit == 3) {
            data.unlockTime = data.unlockTime * 24 * 60
          }
        } else {
          data.unlockTime = 0
        }
        if (!data.isLockAlarm) {
          data.beforeLockTime = 0
        }
      } else {
        data.disconnectServer = 0
        data.isUnLock = 0
        data.unlockTime = 0
        data.disconnectServerUnit = 2
        data.isLockAlarm = 0
        data.beforeLockTime = 0
        data.isUnLock = 0
        data.unlockTime = 0
        data.unLockUnit = 2
        data.reConnectUnlock = 2
        data.ruleId = undefined
      }
    },
    validateFormData(formData) {
      let flag = true
      if (formData.isDisconnectServer && formData.isLockAlarm && formData.ruleId == undefined && formData.beforeLockTime != 0) {
        flag = false
      }
      if (formData.isDisconnectServer && formData.isLockAlarm) {
        this.dataToMinute(formData)
        if (formData.disconnectServer <= formData.beforeLockTime) {
          flag = false
          this.$message({
            message: this.$t('pages.lockScreenText14'),
            type: 'error',
            duration: 2000
          })
        }
      }
      return flag
    },
    validateDisconnectServer(rule, value, callback) {
      if (this.temp.isDisconnectServer && !this.temp.disconnectServer) {
        callback(new Error(this.$t('pages.energySaving_validMessage1')))
      } else {
        callback()
      }
    },
    validateBeforeLockTime(rule, value, callback) {
      if (this.temp.isDisconnectServer && this.temp.isLockAlarm && !this.temp.beforeLockTime) {
        callback(new Error(this.$t('pages.energySaving_validMessage1')))
      } else {
        callback()
      }
    },
    validateUnlockTime(rule, value, callback) {
      if (this.temp.isDisconnectServer && this.temp.isUnLock && !this.temp.unlockTime) {
        callback(new Error(this.$t('pages.energySaving_validMessage1')))
      } else {
        callback()
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    filterData(alarmRules) {
      if (Array.isArray(alarmRules)) {
        if (alarmRules.length > 0) {
          alarmRules = alarmRules.filter(item => {
            if (item.alarmLimit) {
              return (item.alarmLimit & 32) !== 32
            }
          }).filter(item => {
            return (item.alarmLimit & 2) == 2
          })
        }
      }
      return alarmRules
    },
    // 获取当前语言系统，设置文件备份阈值输入框的class
    getLanguage() {
      const currentLang = this.$store.getters.language
      if (currentLang != undefined || currentLang != null || currentLang != '') {
        if (currentLang == 'zh' || currentLang == 'tw') {
          this.langClass = 'zh-tw'
        } else {
          this.langClass = 'en'
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.zh-tw-validate-position1 {
  >>>.el-form-item__error {
    margin-left: 181px;
  }
}
.zh-tw-validate-position2 {
  >>>.el-form-item__error {
    margin-left: 82px;
  }
}
.zh-tw-validate-position3 {
  >>>.el-form-item__error {
    margin-left: 112px;
  }
}
.en-validate-position1 {
  >>>.el-form-item__error {
    margin-left: 328px;
  }
}
.en-validate-position2 {
  >>>.el-form-item__error {
    margin-left: 136px;
  }
}
.en-validate-position3 {
  >>>.el-form-item__error {
    margin-left: 212px;
  }
}
</style>
