<template>
  <el-dialog
    v-el-drag-dialog
    :modal="false"
    :title="$t('pages.strategyOverViews_export')"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="500px"
    @close="handleDrag"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-width="50px">
      <FormItem style="margin-bottom: 0">
        <label>{{ $t('table.terminalType') }}：<el-checkbox v-model="termTypeAll" :indeterminate="termTypeIndeterminate" @change="termTypeAllChange">{{ $t('button.selectAll') }}</el-checkbox></label>
      </FormItem>
      <FormItem label-width="70px" prop="termTypes">
        <el-checkbox-group v-model="temp.termTypes" @change="termTypeChange">
          <el-checkbox v-for="item in termTypeArray" :key="item.value" :value="item.value" :label="item.value">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
      </FormItem>
      <FormItem>
        <label>{{ $t('pages.exportMode') }}：</label>
        <el-row>
          <el-col :span="24">
            <el-radio-group v-model="temp.mode" style="padding-left: 20px" @change="modeChange">
              <el-radio :label="1" style="width: 300px">{{ $t('pages.exportCurrentSearchResult') }}</el-radio>
              <el-radio :label="2" style="width: 300px">
                {{ $t('pages.exportDeptAllTermInfo') }}
                <FormItem label-width="20px" prop="objectId">
                  <tree-select
                    ref="groupTree"
                    node-key="id"
                    is-filter
                    :width="300"
                    :height="500"
                    :filter-key="['G-2']"
                    :disabled="temp.mode !== 2"
                    :default-expanded-keys="defaultExpandedKeys"
                    :leaf-key="'dept'"
                    clearable
                    @change="groupChange"
                  />
                </FormItem>
              </el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <common-downloader
        ref="commonDownloader"
        :loading="submitting"
        :name="getFileName"
        :button-name="$t('pages.strategyOverViews_export')"
        button-type="primary"
        button-icon=""
        :before-download="beforeDownload"
        @download="exportDataHandler"
      />
      <el-button @click="handleDrag">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { exportData } from '@/api/system/terminalManage/moduleConfig'
import CommonDownloader from '@/components/DownloadManager/common'
import moment from 'moment'

export default {
  name: 'ExcelExport',
  components: { CommonDownloader },
  props: {
    osTypeFun: { type: Function, default() { return '' } },    //  操作系统类型
    query: { type: Object, default: null },    //  查询条件
    colModelOptions: { type: Object, default() { return {} } }  //  各个终端所显示的模块
  },
  data() {
    return {
      visible: false,
      defaultExpandedKeys: ['all-0'],
      submitting: false,
      termTypeArray: [
        { value: 1, label: 'Windows' },
        { value: 2, label: 'Linux' },
        { value: 4, label: 'Mac' },
        { value: 8, label: this.$t('pages.appTerminal') }
      ],
      temp: {},
      defaultTemp: {
        osType: null, //  需要导出的终端类型， 用于传输  1-windows，2-linux，4-mac，8-移动终端，  3：windows+linux，7：windows+linux+mac 15：windows+linux+mac+移动终端
        termTypes: [], //  需要导出的终端类型， 仅用于存储
        objectId: null, //  部门Id
        objectType: 3,    //  默认为终端组
        mode: 1  //  导出方式， 1-导出当前查询的数据，2：指定部门导出
      },
      rules: {
        termTypes: [
          { required: true, validator: this.termTypesValidator, trigger: 'blur' }
        ],
        objectId: [
          { required: true, validator: this.objectIdValidator, trigger: 'change' }
        ]
      },
      termTypeAll: false,   //  终端类型-全选
      termTypeIndeterminate: false
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    getFileName() {
      return this.$t('route.moduleConfig') + '_' + moment().format('YYYYMMDD') + '.xlsx'
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.termTypes = []
    },
    show() {
      this.resetTemp()
      this.visible = true
    },
    termTypeAllChange(val) {
      this.temp.termTypes.splice(0)
      if (val) {
        this.termTypeArray.forEach(item => {
          this.temp.termTypes.push(item.value)
        })
      }
      this.termTypeIndeterminate = false;
      this.$refs['dataForm'].validateField('termTypes', valid => {});
    },
    termTypeChange(value) {
      const checkedCount = value.length;
      this.termTypeAll = checkedCount === this.termTypeArray.length;
      this.termTypeIndeterminate = checkedCount > 0 && checkedCount < this.termTypeArray.length;
      this.$refs['dataForm'].validateField('termTypes', valid => {});
    },
    //  模式-change
    modeChange(mode) {
      if (mode === 1) {
        this.$refs['dataForm'].validateField('objectId', valid => {});
      }
    },
    //  终端类型校验
    termTypesValidator(rule, value, callback) {
      if (this.temp.termTypes.length === 0) {
        callback(new Error(this.$t('pages.moduleConfigChooseTermTypeMsg') + ''));
      }
      callback()
    },
    //  部门校验
    objectIdValidator(rule, value, callback) {
      if (this.temp.mode === 2 && this.temp.objectId === null) {
        callback(new Error(this.$t('pages.moduleConfigDeptNotNull') + ''));
      }
      callback()
    },
    clearData() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs['groupTree'].clearSelectedNode()
      })
      this.resetTemp()
      this.visible = false
      this.termTypeAll = false
      this.termTypeIndeterminate = false
    },
    handleDrag() {
      this.clearData()
    },
    groupChange(keys, options) {
      this.temp.objectId = parseInt(keys.substr(1))
    },
    beforeDownload() {
      this.$refs['dataForm'].validate();
      return (this.temp.termTypes.length !== 0) && (this.temp.mode !== 2 || this.temp.objectId !== null)
    },
    exportDataHandler: function(file) {
      this.submitting = true
      const data = Object.assign({}, this.temp)
      //  设置所有终端类型的模块
      if (this.colModelOptions) {
        data.allModules = {}
        const osTypes = ['win', 'linux', 'mac', 'phone'];
        for (const key in osTypes) {
          data.allModules[osTypes[key]] = [];
          (this.colModelOptions[osTypes[key]] || []).forEach(item => { data.allModules[osTypes[key]].push(parseInt(item.prop)) })
        }
      }
      //  设置终端类型
      let osType = 0
      if (this.temp.termTypes.length > 0) {
        this.temp.termTypes.forEach(termType => { osType += termType })
      }
      if (data.mode === 1) {
        //  mode=1时，采用当前查询的对象
        Object.assign(data, this.query)
        //  为确保
        //  仅显示勾选的模块信息
        if (data.showModules && data.showModules.length > 0) {
          data.allModules[this.osTypeFun()] = data.showModules || []
        }
      } else if (data.mode === 2) {
        //  mode=2时，采用配置的部门
        data.objectType = this.temp.objectType
        data.objectId = this.temp.objectId
      }
      data.osType = osType
      const opts = { file, jwt: true, topic: this.$route.name }
      exportData(data, opts).then(res => {
        this.clearData()
        this.submitting = false
      }).catch(() => {
        this.submitting = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table__indent {
  padding-left: 20px;
}
.app-container.strategy{
  position: relative;
  width: 100%;
  height: calc(100vh - 90px);
  padding: 5px 20px 20px;
}
.select_tags {
  ::v-deep .el-select__tags {
    flex-wrap: unset;
    overflow: auto;
  }
}
</style>

