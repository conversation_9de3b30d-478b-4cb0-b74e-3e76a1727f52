<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('route.moduleConfig')"
    :visible.sync="visible"
    width="800px"
    @close="cancel"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">
      <el-divider content-position="left"> {{ $t('pages.basicInformation') }} </el-divider>
      <el-row>
        <el-col :span="10">
          <FormItem :label="$t('pages.osTypeText')" prop="osTypes">
            <el-select v-model="temp.osTypes" multiple clearable collapse-tags @change="typeChange">
              <el-option
                v-for="item in osTypeDict"
                v-show="exitsTypes.includes(item.value)"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </FormItem>
        </el-col>
        <el-col :span="14">
          <FormItem :label="$t('pages.deptOrTerm')" class="ellipsis" prop="termIds">
            <el-tooltip slot="tooltip" effect="dark" placement="bottom">
              <div slot="content">
                <span>{{ $t('pages.allocationModuleDeptTermMsg1') }}</span><br>
                <span>{{ $t('pages.allocationModuleDeptTermMsg2') }}</span><br>
                <span>{{ $t('pages.allocationModuleDeptTermMsg3') }}</span>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <tree-select
              ref="terminalTree"
              :local-search="true"
              :leaf-key="'terminal'"
              multiple
              is-filter
              collapse-tags
              clearable
              check-strictly
              :default-expanded-keys="[]"
              :data="termDatas"
              :width="330"
              @change="terminalTreeChange"
              @clickSelect="clickSelect"
            />
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
          <FormItem :label="$t('pages.allocationMode')" prop="operatorType">
            <el-select v-model="temp.operatorType">
              <el-option :key="1" :value="1" :label="$t('pages.addModule')"/>
              <el-option :key="2" :value="2" :label="$t('pages.removeModule')"/>
              <el-option :key="3" :value="3" :label="$t('pages.settingModule')"/>
            </el-select>
          </FormItem>
        </el-col>
      </el-row>

      <el-divider content-position="left"> {{ $t('layout.moduleInfo') }} </el-divider>
      <label v-if="!temp.osTypes.length" style="margin-left: 10px; color: red">请选择操作系统类型</label>

      <el-tabs v-if="temp.osTypes.length" v-model="tabName">
        <el-tab-pane v-if="temp.osTypes.includes(1)" name="1" label="Windows">
          <div class="module-check-group">
            <div class="module-check-all">
              <FormItem label-width="0" prop="winModuleIds">
                <el-button type="text" @click="handleCheckAllChange(true, 'win')">{{ $t('button.selectAll') }}</el-button>
                <el-button type="text" @click="handleCheckAllChange(false, 'win')">{{ $t('button.cancelSelectAll') }}</el-button>
                <span v-if="selectedWindowsEmpty" style="color: #F56C6C; font-size: 12px; line-height: 1">{{ $t('pages.moduleNotAllocationNull') }}</span>
              </FormItem>
            </div>
            <el-checkbox-group v-model="temp.windowsModuleIds" @change="validateModuleIds">
              <el-checkbox
                v-for="moduleInfo in modules['win']"
                :key="moduleInfo.moduleId"
                :value="moduleInfo.moduleId"
                :checked="moduleInfo.checked"
                :disabled="moduleInfo.disabled"
                :label="moduleInfo.moduleId"
                @change="moduleChange"
              >
                {{ moduleInfo.moduleName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="temp.osTypes.includes(2)" name="2" label="Linux">
          <div class="module-check-group">
            <div class="module-check-all">
              <FormItem label-width="0" prop="linuxModuleIds">
                <el-button type="text" @click="handleCheckAllChange(true, 'linux')">{{ $t('button.selectAll') }}</el-button>
                <el-button type="text" @click="handleCheckAllChange(false, 'linux')">{{ $t('button.cancelSelectAll') }}</el-button>
                <span v-if="selectedLinuxEmpty" style="color: #F56C6C; font-size: 12px; line-height: 1">{{ $t('pages.moduleNotAllocationNull') }}</span>
              </FormItem>
            </div>
            <el-checkbox-group v-model="temp.linuxModuleIds" @change="validateModuleIds">
              <el-checkbox
                v-for="moduleInfo in modules['linux']"
                :key="moduleInfo.moduleId"
                :value="moduleInfo.moduleId"
                :checked="moduleInfo.checked"
                :disabled="moduleInfo.disabled"
                :label="moduleInfo.moduleId"
                @change="moduleChange"
              >
                {{ moduleInfo.moduleName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="temp.osTypes.includes(4)" name="4" label="Mac">
          <div class="module-check-group">
            <div class="module-check-all">
              <FormItem label-width="0" prop="macModuleIds">
                <el-button type="text" @click="handleCheckAllChange(true, 'mac')">{{ $t('button.selectAll') }}</el-button>
                <el-button type="text" @click="handleCheckAllChange(false, 'mac')">{{ $t('button.cancelSelectAll') }}</el-button>
                <span v-if="selectedMacEmpty" style="color: #F56C6C; font-size: 12px; line-height: 1">{{ $t('pages.moduleNotAllocationNull') }}</span>
              </FormItem>
            </div>
            <el-checkbox-group v-model="temp.macModuleIds" @change="validateModuleIds">
              <el-checkbox
                v-for="moduleInfo in modules['mac']"
                :key="moduleInfo.moduleId"
                :value="moduleInfo.moduleId"
                :checked="moduleInfo.checked"
                :disabled="moduleInfo.disabled"
                :label="moduleInfo.moduleId"
                @change="moduleChange"
              >
                {{ moduleInfo.moduleName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="temp.osTypes.includes(8)" name="8" label="移动终端">
          <div class="module-check-group">
            <div class="module-check-all">
              <FormItem label-width="0" prop="phoneModuleIds">
                <el-button type="text" @click="handleCheckAllChange(true, 'phone')">{{ $t('button.selectAll') }}</el-button>
                <el-button type="text" @click="handleCheckAllChange(false, 'phone')">{{ $t('button.cancelSelectAll') }}</el-button>
                <span v-if="selectedPhoneEmpty" style="color: #F56C6C; font-size: 12px; line-height: 1">{{ $t('pages.moduleNotAllocationNull') }}</span>
              </FormItem>
            </div>
            <el-checkbox-group v-model="temp.phoneModuleIds" @change="validateModuleIds">
              <el-checkbox
                v-for="moduleInfo in modules['phone']"
                :key="moduleInfo.moduleId"
                :value="moduleInfo.moduleId"
                :checked="moduleInfo.checked"
                :disabled="moduleInfo.disabled"
                :label="moduleInfo.moduleId"
                @change="moduleChange"
              >
                {{ moduleInfo.moduleName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-tab-pane>
      </el-tabs>

    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleModuleSave">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="cancel">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getModuleInfoByProdId, listTermOsType, saveModuleAllocation } from '@/api/system/terminalManage/moduleConfig'
import TreeSelect from '@/components/TreeSelect';

export default {
  name: 'AllModuleEdit',
  components: { TreeSelect },
  data() {
    return {
      visible: false,
      submitting: false,
      clearDisabledMsg: undefined,
      temp: {},
      defaultTemp: {
        osTypes: [],  //  选中的操作系统类型
        deptIds: [],  //  选中的部门
        termIds: [],  //  选中的终端
        moduleIds: [],  //  模块信息
        windowsModuleIds: [], //  Windows终端模块信息
        linuxModuleIds: [], //  Linux终端模块信息
        macModuleIds: [], //  Mac终端模块信息
        phoneModuleIds: [], //  移动终端模块信息
        operatorType: 1 //  当模式=1时，operatorType属性有效，1：指定终端增加模块，2：指定终端去除模块 3：指定终端设置模块
      },
      rules: {
        osTypes: [
          { validator: this.osTypesValid, trigger: 'change' }
        ],
        termIds: [
          { required: true, validator: this.termIdsOrDeptIdsValid, trigger: 'blur' }
        ],
        winModuleIds: [
          { validator: this.moduleIdsValid, trigger: 'change' }
        ],
        linuxModuleIds: [
          { validator: this.moduleIdsValid, trigger: 'change' }
        ],
        macModuleIds: [
          { validator: this.moduleIdsValid, trigger: 'change' }
        ],
        phoneModuleIds: [
          { validator: this.moduleIdsValid, trigger: 'change' }
        ]
      },
      modules: { 'win': [], 'linux': [], 'mac': [], 'phone': [] },  //  过滤后的各模块信息
      allModules: [], //  所有终端模块信息
      osTypes: { 0: 'win', 1: 'linux', 2: 'mac', 3: 'phone' },
      osTypeDict: [
        { value: 1, label: 'Windows' },
        { value: 2, label: 'Linux' },
        { value: 4, label: 'Mac' },
        { value: 8, label: this.$t('pages.MobileTerminal') }
      ],
      phoneModuleId: 351, //  移动终端权限
      phoneCadModuleId: 60001,  //  移动端CAD权限
      osType: 1, //  待分配的终端类型 1-windows 2-linux  4-mac  8-phone
      exitsTypes: [],  //  获取当前系统中所有终端数据中，存在的OsType
      termDatas: [],
      tabName: null //  标签
    }
  },
  computed: {
    // 终端或部门是否不为空
    selectedTermNotEmpty() {
      const { termIds = [], deptIds = [] } = this.temp
      return termIds.concat(deptIds).length > 0
    },
    //  windows终端是否为空
    selectedWindowsEmpty() {
      return this.selectedTermNotEmpty && this.temp.osTypes.includes(1) && !this.temp.windowsModuleIds.length
    },
    //  linux终端是否为空
    selectedLinuxEmpty() {
      return this.selectedTermNotEmpty && this.temp.osTypes.includes(2) && !this.temp.linuxModuleIds.length
    },
    //  mac终端是否为空
    selectedMacEmpty() {
      return this.selectedTermNotEmpty && this.temp.osTypes.includes(4) && !this.temp.macModuleIds.length
    },
    //  移动终端是否为空
    selectedPhoneEmpty() {
      return this.selectedTermNotEmpty && this.temp.osTypes.includes(8) && !this.temp.phoneModuleIds.length
    }
  },
  created() {
    this.restTemp()
  },
  methods: {
    show() {
      this.restTemp()
      this.loadTermTree()
      this.getAllTermExitsOsTypes().then(res => {
        return getModuleInfoByProdId(511).then(res => {
          this.allModules = res.data || []
          this.exitsTypes.forEach(type => {
            const termType = this.changeTypeFormat(type);
            this.changeModel(termType, this.osTypes[termType])
          })
          this.visible = true
        })
      })
    },
    restTemp() {
      this.temp = Object.assign({}, JSON.parse(JSON.stringify(this.defaultTemp)))
    },
    /**
     * 加载终端树
     */
    loadTermTree() {
      this.termDatas = JSON.parse(JSON.stringify(this.$store.getters.termTree))
      this.generateTermTree(this.termDatas)
    },
    /**
     * 生成终端树
     * @param termTree
     */
    generateTermTree(termTree) {
      //  过滤不需要展示的终端  过滤永久离线终端和U盘终端
      for (let i = 0; i < termTree.length; i++) {
        const item = termTree[i]
        if (item.type !== '3' && this.isOfflineTerm(Number(item.dataType))) {
          termTree.splice(i, 1);
          i--
        } else if (item.type === '3' && item.children) {
          this.generateTermTree(item.children)
        }
      }
    },
    /**
     * 获取当前系统中所有终端数据中，存在的OsType
     */
    getAllTermExitsOsTypes() {
      return listTermOsType({}).then(res => {
        this.exitsTypes = res.data || []
        return res;
      });
    },
    /**
     * 改变模块信息
     * @param type 终端类型 0-windows， 1-linux  2-mac  3-移动
     * @param termType ['win', 'linux', 'mac', 'phone']
     */
    changeModel(type, termType) {
      const osGroup = this.osTypes[type & 0x0f]
      this.modules[termType] = this.allModules.filter(moduleInfo => this.moduleFilter(moduleInfo, osGroup, type));
    },
    /**
     * 是否为离线终端或U盘终端
     * @param type  终端类型
     * @returns {boolean}
     */
    isOfflineTerm(type) {
      const tag = type & 0xf0
      return tag === 0x20 || tag === 0x80 // U盘终端或永久离线终端
    },
    /**
     * 模块过滤
     * @param moduleInfo  模块信息
     * @param osGroup
     * @param type   终端类型 0-windows， 1-linux  2-mac  3-移动
     * @returns {boolean}
     */
    moduleFilter(moduleInfo, osGroup, type) {
      const { moduleId, osGroup: moduleInfoOsGroup } = moduleInfo
      const flag = moduleId > 2 && moduleInfoOsGroup === osGroup
      if (!flag) {
        return false
      }
      const isBTerm = type === 16 || type === 17 || type === 18 // 是否老板终端
      const mod = moduleId % 100
      // 老板终端不支持模块：文件相似度识别(82,182,282)、文件分类(83,183,283)
      if (isBTerm && (mod === 82 || mod === 83)) {
        return false
      }

      const isUTerm = (type & 0xf0) === 0x20
      const isOTerm = (type & 0xf0) === 0x80
      if (!isUTerm && !isOTerm) {
        return true
      }

      // 永久离线终端和U盘终端不支持模块：文件操作监视(12,112,212)、文件相似度识别(82,182,282)、文件分类(83,183,283)
      if (mod === 12 || mod === 20 || mod === 82 || mod === 83) {
        return false
      }
      if (isUTerm) {
        // U盘终端不支持模块：存储设备管控(16,116,216)、刻录管控模块(18,118,218)
        return !(mod === 16 || mod === 18)
      }
      return true
    },
    /**
     * 全选 / 取消权限事件
     * @param checked true：全选，  false：取消权限
     * @param type  终端类型 win， linux， mac， phone
     * 注意：移动终端模块中，拥有移动终端CAD权限必须先拥有移动终端权限
     */
    handleCheckAllChange(checked, type) {
      if (checked) {
        this.setAllModuleIds(type)
      } else {
        this.getTempModuleIds(type).splice(0);
        this.validateModuleIds(type + 'ModuleIds')
      }
    },
    /**
     * 根据类型获取对应temp中的终端类型模块Ids
     * @param type  终端类型 ['win', 'linux', 'mac', 'phone']
     * @returns {*[]}
     */
    getTempModuleIds(type) {
      let moduleIds = [];
      switch (type) {
        case 'win': moduleIds = this.temp.windowsModuleIds; break;
        case 'linux': moduleIds = this.temp.linuxModuleIds; break;
        case 'mac': moduleIds = this.temp.macModuleIds; break;
        case 'phone': moduleIds = this.temp.phoneModuleIds; break;
        default: console.error('not exist terminal type %o', type)
      }
      return moduleIds;
    },
    /**
     * 全选模块信息
     */
    setAllModuleIds(type) {
      //   设置moduleIds
      this.temp.moduleIds = []
      const moduleIds = this.getTempModuleIds(type);
      this.modules[type].forEach(moduleInfo => {
        moduleIds.push(moduleInfo.moduleId)
      })
      this.validateModuleIds(type + 'ModuleIds')
    },
    /**
     * form表单-校验模块信息
     */
    validateModuleIds(field) {
      this.$refs.dataForm && this.$refs.dataForm.validateField(field)
    },
    /**
     * 校验表单数据
     */
    validateFormData() {
      if (this.selectedWindowsEmpty) {
        this.tabName = '1'
      } else if (this.selectedLinuxEmpty) {
        this.tabName = '2'
      } else if (this.selectedMacEmpty) {
        this.tabName = '4'
      } else if (this.selectedPhoneEmpty) {
        this.tabName = '8'
      }
      return !this.selectedWindowsEmpty && !this.selectedLinuxEmpty && !this.selectedMacEmpty && !this.selectedPhoneEmpty;
    },
    /**
     * 保存
     */
    handleModuleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid && this.validateFormData()) {
          this.submitting = true;
          //  格式化osType
          const rowData = JSON.parse(JSON.stringify(this.temp));
          const osTypes = [...rowData.osTypes];
          //  设置有分配的终端操作系统 osTypes的值可包含 [0,1,2,3]
          rowData.osTypes = osTypes.map(osType => this.changeTypeFormat(osType))
          //  仅在指定终端设置模块时，需要额外添加基础模块和报表模块
          if (rowData.operatorType === 3) {
            //  基础模块信息(1)
            const baseModuleIds = [1];
            //  若销售模块中存在报表即添加报表模块(2)
            this.allModules.map(item => item.moduleId).includes(2) && baseModuleIds.push(2);
            rowData.windowsModuleIds.length && rowData.windowsModuleIds.push(...baseModuleIds);
            rowData.linuxModuleIds.length && rowData.linuxModuleIds.push(...baseModuleIds);
            rowData.macModuleIds.length && rowData.macModuleIds.push(...baseModuleIds);
            rowData.phoneModuleIds.length && rowData.phoneModuleIds.push(...baseModuleIds);
          }

          saveModuleAllocation(rowData).then(res => {
            let notAllocationNodes = null
            //  是否存在节点不足的模块
            if (res.data && res.data.filter(t => t.moduleNodeNumInsufficientLength).length) {
              //  存在模块点数不足，部分终端未分配到模块
              this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.moduleAllocationDeficiencyMsg'), type: 'warning', duration: 2000 })
              notAllocationNodes = [...res.data]
              //  返回节点注释 moduleNodeNumInsufficient：节点数不足的模块 key:模块ID，value：不足的数量；
              //  termModuleNodeNumInsufficient：key: 终端id  value:节点数不足的模块ID；
              //  termModules: 终端信息， key：终端ID，value:终端名称
              notAllocationNodes.forEach(item => {
                item.osType = this.osTypes[item.osType]
              })
            } else {
              this.$notify({ title: this.$t('text.success'), message: this.$t('pages.moduleConfigSuccessNotify1'), type: 'success', duration: 2000 })
            }
            this.submitting = false
            //  返回节点分配不足的信息，
            this.$emit('submit', notAllocationNodes)
            this.cancel()
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    },
    /**
     * 取消
     */
    cancel() {
      this.$nextTick(() => {
        this.restTemp()
        this.$refs.dataForm.clearValidate()
        this.$refs.terminalTree.clearSelectedNode()
        this.visible = false
      })
    },
    /**
     * 终端树发生改变
     * @param selectedKeys  选中节点的Id集
     * @param datas 选中节点的数据集
     */
    terminalTreeChange(selectedKeys, datas) {
      const termIds = []
      const deptIds = []
      if (datas) {
        datas.forEach(item => {
          if (item.type === '1') {
            termIds.push(item.dataId)
          } else if (item.type === '3') {
            deptIds.push(parseInt(item.dataId));
          }
        })
      }
      this.temp.termIds = termIds
      this.temp.deptIds = deptIds
      this.$refs.dataForm && this.$refs.dataForm.validateField('termIds')
    },
    /**
     * 转换类型   1->Windows， 2->linux  4->mac  8->phone
     * @param type
     * @returns {*}
     */
    changeTypeFormat(type) {
      const types = { '1': 0, '2': 1, '4': 2, '8': 3 };
      return types[type + '']
    },
    /**
     * 终端类型改变时
     */
    typeChange() {
      if (this.temp.osTypes.length && (!this.tabName || !this.temp.osTypes.includes(parseInt(this.tabName)))) {
        //  确保tabName值为字符串
        this.tabName = this.temp.osTypes[0] + '';
      }
    },
    /**
     * 校验操作系统类型信息
     * @param rule
     * @param value
     * @param callback
     */
    osTypesValid(rule, value, callback) {
      if (!value || !value.length) {
        callback(new Error('操作系统类型不能为空'));
      }
      callback();
    },
    /**
     * 校验终端信息
     * @param rule
     * @param value
     * @param callback
     */
    termIdsOrDeptIdsValid(rule, value, callback) {
      if (this.temp.deptIds.length === 0 && this.temp.termIds.length === 0) {
        callback(new Error(this.$t('pages.deptOrTermNotAllocationNull') + ''))
      }
      callback()
    },
    /**
     * 校验模块信息
     * @param rule
     * @param value
     * @param callback
     */
    moduleIdsValid(rule, value, callback) {
      callback()
    },
    /**
     * 选中模块信息时
     * @param value
     */
    moduleChange(value) {
      //  移动终端模块中，拥有移动终端CAD权限必须先拥有移动终端权限
      if (!this.temp.phoneModuleIds.includes(this.phoneModuleId) && this.temp.phoneModuleIds.includes(this.phoneCadModuleId)) {
        this.temp.phoneModuleIds = this.temp.phoneModuleIds.filter(t => t !== this.phoneCadModuleId)
        //  若为true，表示是通过选中【移动终端CAD权限】触发的
        if (value) {
          this.$message({
            message: this.$t('pages.moduleConfig_confirmMsg4'),
            type: 'warning',
            duration: 2000
          })
        }
      }
    },
    /**
     * 选中树组件
     */
    clickSelect() {
      //  清空搜索框内容
      this.$refs.terminalTree.clearFilter()
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-form {
    width: 700px;
    margin-left:30px;
    >>>.el-form-item__label:after {
      content: '：';
    }
    >>>.el-form-item__content {
      cursor: default;
    }
  }
  .module-check-group {
    margin: 10px 0;
    .el-checkbox {
      width: 40%;
      margin-left: 5%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      >>>.el-checkbox__label{
        margin-left: 10px;
        padding-left: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>
