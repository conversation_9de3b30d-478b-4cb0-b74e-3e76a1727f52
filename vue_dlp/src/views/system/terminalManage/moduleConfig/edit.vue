<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('route.moduleConfig')"
    :visible.sync="visible"
    width="800px"
  >
    <Form label-position="right" label-width="100px">
      <el-divider content-position="left"> {{ $t('pages.basicTerminalInfo') }} </el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('table.terminalCode')">
            <span :title="temp.id">{{ temp.id }}</span>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.terminalName')" class="ellipsis">
            <span :title="temp.name">{{ temp.name }}</span>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.terminalType')">
            <span v-if="temp.type" :title="temp.type.title">
              <svg-icon :icon-class="temp.type.icon" :style="temp.type.style"/>
              {{ temp.type.label }}
            </span>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.terminalGroup')" class="ellipsis">
            <span :title="temp.groupNames">{{ temp.groupName }}</span>
          </FormItem>
        </el-col>
      </el-row>
      <el-divider content-position="left"> {{ $t('layout.moduleInfo') }} </el-divider>
      <div class="module-check-all">
        <el-button type="text" @click="handleCheckAllChange(true)">{{ $t('button.selectAll') }}</el-button>
        <el-button type="text" @click="handleCheckAllChange(false)">{{ $t('button.cancelSelectAll') }}</el-button>
      </div>
      <div class="module-check-group">
        <el-checkbox
          v-for="moduleInfo in modules"
          :key="moduleInfo.moduleId"
          v-model="moduleInfo.checked"
          :checked="moduleInfo.checked"
          :disabled="moduleInfo.disabled"
          @click.native.prevent="handleModuleCheck(moduleInfo)"
        >
          <span :title="`${moduleInfo.moduleName}(${moduleInfo.usedNum}/${moduleInfo.numbers})`">
            {{ `${moduleInfo.moduleName}(${moduleInfo.usedNum}/${moduleInfo.numbers})` }}
          </span>
        </el-checkbox>
      </div>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleModuleSave">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getModuleInfoByProdId, listTerminalModule, saveModule, getFilterSalesModule } from '@/api/system/terminalManage/moduleConfig'
import { listParentByTermId } from '@/api/system/terminalManage/department'
import { getTermTypeDict } from '@/utils/dictionary'

export default {
  name: 'ModuleEdit',
  data() {
    return {
      visible: false,
      submitting: false,
      clearDisabledMsg: undefined,
      temp: {},
      defaultTemp: {
        id: undefined,
        name: undefined,
        type: undefined,
        groupName: undefined,
        groupNames: undefined,
        occupied: false
      },
      modules: [],
      allModules: [],
      oldModuleIds: [],
      filterSaleModule: [],
      osTypes: { 0: 'win', 1: 'linux', 2: 'mac', 3: 'phone' },
      termTypes: {},
      phoneModuleId: 351,
      phoneCadModuleId: 60001
    }
  },
  async created() {
    getTermTypeDict().forEach(item => {
      this.termTypes[item.value] = item
    })
    // 获取过滤销售模块
    const resp = await getFilterSalesModule()
    this.filterSaleModule = resp.data
  },
  methods: {
    show(terminalId, clearDisabledMsg) {
      this.clearDisabledMsg = typeof clearDisabledMsg === 'string' && clearDisabledMsg
      this.temp = { ...this.defaultTemp }
      const query = { objectType: 1, objectId: terminalId }
      return getModuleInfoByProdId(511).then(res => {
        this.allModules = res.data || []
      }).then(() => this.getTermGroup(terminalId)).then(group => {
        return listTerminalModule(query).then(res => ({ ...res.data, group }))
      }).then(res => {
        const { id, name, type, guid, loginStatus } = res.items[0]
        const occupied = !!guid
        const typeInfo = this.getTermTypeInfo(type, loginStatus, occupied)
        const { name: groupName, names: groupNames } = res.group
        this.temp = { id, name, type: typeInfo, guid, occupied, groupName, groupNames }
        const osGroup = this.osTypes[type & 0x0f]
        const moduleIds = res.modules[terminalId]
        this.oldModuleIds = !moduleIds || !Array.isArray(moduleIds) ? [] : moduleIds
        this.modules = this.allModules
          .filter(moduleInfo => this.moduleFilter(moduleInfo, osGroup, type))
          .map(moduleInfo => this.buildModuleInfo(moduleInfo, this.oldModuleIds, occupied, type))
        this.visible = true
      })
    },
    getTermGroup(terminalId) {
      return listParentByTermId(terminalId).then(res => {
        const groups = res.data
        if (groups.length === 0) {
          return {}
        }
        const rootName = groups[0].name
        if (groups.length === 1) {
          return { ...groups[0], names: rootName }
        }
        const groupNames = []
        for (let i = 0; i < groups.length; i++) {
          groupNames.push(groups[i].name)
        }
        return { ...groups[groups.length - 1], names: groupNames.join(' -> ') }
      })
    },
    getTermTypeInfo(type, loginStatus, occupied) {
      const { label, icon } = this.termTypes[type]
      const info = { value: type, label, icon }
      if (loginStatus && loginStatus.status) {
        info.style = 'color: green;'
        info.title = this.$t('pages.online') + '\r\n' + label
      } else if (occupied) {
        info.title = this.$t('pages.offline') + '\r\n' + label
      } else {
        info.title = this.$t('pages.unused') + '\r\n' + label
        info.icon += '-x'
      }
      return info
    },
    isOfflineTerm(type) {
      const tag = type & 0xf0
      return tag === 0x20 || tag === 0x80 // U盘终端或永久离线终端
    },
    getModuleName(moduleInfo) {
      const { moduleName, osGroup } = moduleInfo
      const index = moduleName.toLowerCase().indexOf(osGroup)
      if (index === 0) {
        return moduleName.slice(osGroup.length + 1)
      }
      return moduleName
    },
    moduleFilter(moduleInfo, osGroup, type) {
      const { moduleId, osGroup: moduleInfoOsGroup } = moduleInfo
      const flag = moduleId > 2 && moduleInfoOsGroup === osGroup
      if (!flag) {
        return false
      }
      // 方式1：部分终端不支持一些销售模块，包括U盘终端、永久离线终端等
      const filterModules = this.filterSaleModule[type]
      if (filterModules && filterModules.length > 0 && filterModules.indexOf(moduleId) > -1) {
        return false;
      }
      // 方式2：此过滤方式后续版本可以考虑去掉，同意使用方式一，如此前后端过滤只需要在一个地方配置即可

      const isBTerm = type === 16 || type === 17 || type === 18 // 是否老板终端
      const mod = moduleId % 100
      // 老板终端不支持模块：文件相似度识别(82,182,282)、文件分类(83,183,283)
      if (isBTerm && (mod === 82 || mod === 83)) {
        return false
      }

      const isUTerm = (type & 0xf0) === 0x20
      const isOTerm = (type & 0xf0) === 0x80
      if (!isUTerm && !isOTerm) {
        return true
      }

      // 永久离线终端和U盘终端不支持模块：文件操作监视(12,112,212)、文件相似度识别(82,182,282)、文件分类(83,183,283)
      if (mod === 12 || mod === 20 || mod === 82 || mod === 83) {
        return false
      }
      if (isUTerm) {
        // U盘终端不支持模块：存储设备管控(16,116,216)、刻录管控模块(18,118,218)
        return !(mod === 16 || mod === 18)
      }
      return true
    },
    buildModuleInfo(moduleInfo, moduleIds, occupied, type) {
      const moduleName = this.getModuleName(moduleInfo)
      const checked = !!moduleIds && moduleIds.indexOf(moduleInfo.moduleId) >= 0
      // 已占用的U盘终端和永久离线终端：已分配的模块不能取消
      const disabled = occupied && this.isOfflineTerm(type) && checked
      return { ...moduleInfo, moduleName, checked, disabled }
    },
    getModuleById(moduleId) {
      let moduleInfo
      for (let i = 0; i < this.modules.length; i++) {
        moduleInfo = this.modules[i]
        if (moduleInfo.moduleId === moduleId) {
          return moduleInfo
        }
      }
      return {}
    },
    cancelPhoneCadModule() {
      const moduleInfo = this.getModuleById(this.phoneCadModuleId)
      if (moduleInfo.checked) {
        moduleInfo.usedNum--
        moduleInfo.checked = false
      }
    },
    handleCheckAllChange(checked) {
      let outOfUsed = false
      let outOfUsedPhone = false
      this.modules.forEach(moduleInfo => {
        if (moduleInfo.disabled || moduleInfo.checked === checked) {
          return
        }
        if (checked) {
          const used = moduleInfo.usedNum + 1 <= moduleInfo.numbers
          if (used) {
            moduleInfo.usedNum++
            moduleInfo.checked = true
          } else {
            outOfUsed = true
            outOfUsedPhone |= moduleInfo.moduleId === this.phoneModuleId
          }
        } else {
          moduleInfo.usedNum--
          moduleInfo.checked = false
        }
      })
      if (outOfUsed) {
        this.confirmMsg(this.$t('pages.moduleConfig_confirmMsg3'))
        if (outOfUsedPhone) {
          this.cancelPhoneCadModule()
        }
      }
    },
    handleModuleCheck(moduleInfo) {
      // 永久离线终端和U盘终端禁止取消已分配的模块
      if (moduleInfo.disabled) {
        return
      }
      // 取消模块
      if (moduleInfo.checked) {
        moduleInfo.usedNum--
        moduleInfo.checked = false
        // 取消移动终端权限需将移动终端CAD权限同步取消
        if (moduleInfo.moduleId === this.phoneModuleId) {
          this.cancelPhoneCadModule()
        }
        return
      }
      // 需要开启移动终端权限，移动终端CAD权限才允许使用
      if (moduleInfo.moduleId === this.phoneCadModuleId && !this.getModuleById(this.phoneModuleId).checked) {
        this.confirmMsg(this.$t('pages.moduleConfig_confirmMsg4'))
        return
      }
      // 开启模块需要检查点数是否够用
      const used = moduleInfo.usedNum + 1 <= moduleInfo.numbers
      if (used) {
        moduleInfo.usedNum++
        moduleInfo.checked = true
      } else {
        this.confirmMsg(this.$t('pages.moduleConfig_confirmMsg2'))
      }
    },
    handleModuleSave() {
      const moduleIds = this.modules.filter(item => item.checked).map(item => item.moduleId)
      if (this.clearDisabledMsg && moduleIds.length === 0) {
        this.confirmMsg(this.clearDisabledMsg)
        return
      }
      moduleIds.push(1) // 基础模块，必须有
      if (this.allModules.map(item => item.moduleId).includes(2)) {
        moduleIds.push(2) // 综合报表，可能有
      }
      const termType = (this.temp.type || { value: 0 }).value & 0xf0
      const isOTerm = termType === 0x80
      const isUTerm = termType === 0x20
      if (this.temp.occupied) {
        if (isOTerm) {
          [12, 112, 212, 82, 182, 282, 83, 183, 283].forEach(moduleId => {
            if (this.oldModuleIds.includes(moduleId)) {
              moduleIds.push(moduleId)
            }
          })
        } else if (isUTerm) {
          [12, 112, 212, 82, 182, 282, 83, 183, 283, 16, 116, 216, 18, 118, 218].forEach(moduleId => {
            if (this.oldModuleIds.includes(moduleId)) {
              moduleIds.push(moduleId)
            }
          })
        }
      }

      this.submitting = true
      const { id, name, guid } = this.temp
      const data = { id, name, guid, moduleIds }
      saveModule([data]).then(() => {
        this.$emit('change', this.modules)
        let message = ''
        if (isOTerm || isUTerm) {
          message = this.$t('pages.moduleConfigSuccessNotify2', { term: '' })
        } else {
          message = this.$t('pages.moduleConfigSuccessNotify1')
        }
        this.$notify({ title: this.$t('text.success'), message, type: 'success', duration: 2000 })
        this.visible = false
      }).finally(() => {
        this.submitting = false
      })
    },
    confirmMsg(msg) {
      const ignoreCb = () => {}
      this.$confirmBox(msg, this.$t('text.prompt'), { showCancelButton: false }).then(ignoreCb).catch(ignoreCb)
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-form {
    width: 700px;
    margin-left:30px;
    >>>.el-form-item__label:after {
      content: '：';
    }
    >>>.el-form-item__content {
      cursor: default;
    }
  }
  .module-check-group {
    margin-top: 15px;
    .el-checkbox {
      width: 40%;
      margin-left: 5%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      >>>.el-checkbox__label{
        margin-left: 10px;
        padding-left: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>
