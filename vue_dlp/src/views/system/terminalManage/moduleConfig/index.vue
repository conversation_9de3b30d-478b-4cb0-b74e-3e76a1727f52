<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>

        <el-button type="primary" size="mini" style="width:80px;" :loading="btnLoading" :disabled="!submitable" @click="saveData">{{ $t('button.save') }}</el-button>
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button v-permission="'380'" type="primary" icon="el-icon-setting" size="mini" @click="handlesetting">
          {{ $t('button.highConfig') }}
        </el-button>

        <el-button type="primary" size="mini" @click="handleAllSetting">
          {{ $t('table.allocationModule') }}
        </el-button>

        <el-button style="margin-left: 10px" icon="el-icon-download" size="mini" @click="excelExportHandler">{{ $t('pages.strategyOverViews_export') }}</el-button>

        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.terminalNameOrNumber')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <div style="position: relative">
        <div style="width: 100%; float: left;">
          <el-tabs v-if="showTabs.length > 0" ref="tabs" v-model="activeTab" type="card" style="height: 40px; border-width: 1px;" @tab-click="tabClickFunc">
            <el-tab-pane v-if="showTabs.indexOf('win') > -1" key="win" label="Windows" name="winTab" />
            <el-tab-pane v-if="showTabs.indexOf('linux') > -1" key="linux" label="Linux" name="linuxTab" />
            <el-tab-pane v-if="showTabs.indexOf('mac') > -1" key="mac" label="Mac" name="macTab" />
            <el-tab-pane v-if="showTabs.indexOf('phone') > -1" key="phone" :label="$t('pages.MobileTerminal')" name="phoneTab" />
          </el-tabs>
        </div>
        <div style="position: absolute; right: 0.5%;">
          <el-tabs style="width: 270px; border: 0;">
            <el-select
              v-model="query.showModules"
              filterable
              clearable
              multiple
              collapse-tags
              type="text"
              style="height: 35px; width: 100%; margin: 0;"
              :placeholder="$t('pages.placeChooseShowModule')"
              @visible-change="modelVisibleChange"
              @remove-tag="modelRemoveTag"
              @clear="modelClear"
            >
              <el-option v-for="item in showModules" :key="item.prop" :value="item.prop" :label="item.label"></el-option>
            </el-select>
          </el-tabs>
        </div>
        <div :style="utermTipsStyle">
          <!--控制台没有检测到U盘终端插入，无法取消该终端对应的销售模块。-->
          <el-tooltip :content="$t('pages.moduleConfigTooltip')" placement="right">
            <i class="el-icon-info"></i>
          </el-tooltip>
        </div>
      </div>
      <grid-table
        ref="terminalModuleList"
        v-loading="tableLoading || submitting"
        :col-model="colModel"
        :row-datas="rowData"
        :page-sizes="[50, 100, 150, 200]"
        :on-sort-change="updateSort"
        @select="selectionClick"
        @select-all="selectAllRow"
        @updateLimit="updateLimit"
        @updatePage="updatePage"
      />
    </div>
    <module-edit ref="editModule" @change="callUpdateModuledUsed"/>
    <all-module-edit ref="allEditModule" @submit="updateModuleSubmit"/>
    <module-manage-config ref="moduleManageConfig"></module-manage-config>
    <module-insufficient-dlg ref="moduleInsufficientDlg"/>

    <excel-export ref="exportDlg" :query="query" :col-model-options="colModelOptions" :os-type-fun="getOsGroup"/>
  </div>
</template>

<script>
import ModuleEdit from './edit'
import AllModuleEdit from './allEdit'
import ModuleInsufficientDlg from './moduleInsufficientDlg'
import { getModuleInfoByProdId, listTerminalModule, saveModule, getFilterSalesModule } from '@/api/system/terminalManage/moduleConfig'
import { termStatusIconFormatter } from '@/utils/formatter'
import { getTermTypeDict } from '@/utils/dictionary'
import ModuleManageConfig from '@/views/system/terminalManage/moduleManageConfig';
import { entityLink } from '@/utils';
import ExcelExport from '@/views/system/terminalManage/moduleConfig/excelExport';

export default {
  name: 'ModuleConfig',
  components: { ExcelExport, ModuleEdit, ModuleManageConfig, AllModuleEdit, ModuleInsufficientDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'terminalName', width: '150', fixed: true, sort: true, type: 'button',
          buttons: [
            { formatter: this.terminalNameFormatter, style: 'width: calc(100% - 25px);', click: this.elDragClick }
          ],
          iconFormatter: termStatusIconFormatter
        }
      ],
      colModelOptions: { win: [], linux: [], mac: [], phone: [] },
      rowDataOptions: { win: [], linux: [], mac: [], phone: [] },
      showTabs: ['win', 'linux', 'mac', 'phone'],
      dlpModuleOptions: [],
      deskModuleOptions: [],
      behaviorModuleOptions: [],
      allModuleIds: [],
      moduleDatas: [],
      moudleUsed: {},
      moduleData: {},
      moduleNameMap: {},
      defaultModuleData: {
        productId: null,
        productName: '',
        checked: [],
        options: []
      },
      moduleState: {},
      terminalTypeOptions: {}, // 终端类型，通过字典获取
      timer: null,
      rowData: [],
      terminalName: '',
      tempChecked: [],
      checkedTerminalId: undefined,
      query: { // 查询条件
        page: 1,
        limit: 50,
        sortName: 'id',
        searchInfo: '',
        objectType: undefined,
        objectId: undefined,
        osType: undefined,
        showModules: []
      },
      showModules: [],
      temp: {},
      defaultTemp: { // 表单字段
        objectType: 1,
        objectId: undefined,
        terminalName: '',
        filterSubModules: ''
      },
      submitable: false,
      showTree: true,
      dialogFormVisible: false,
      submitting: false,
      tableLoading: false,
      tableLoadMillis: 0,
      btnLoading: false,
      activeTab: 'winTab',
      modelSelectFlag: false,
      moudleUsedInit: {},
      nextMoudleUsedFlag: false,  // 如果为true，则下一次获取的使用数（used），从moduleUsed中获取
      tempShowModules: [], //  保存刚点击选中需要展示模块框时，选中的模块
      filterSaleModule: {},
      bossDisabledSaleModule: [82, 182, 282, 83, 183, 283]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['terminalModuleList']
    },
    utermTipsStyle() {
      const tabsNum = this.showTabs.length
      if (tabsNum === 0) {
        return 'display: none'
      }
      for (let i = 0; i < this.rowData.length; i++) {
        const row = this.rowData[i]
        const termType = row.type & 0xf0
        if ((termType === 32 || termType === 0x80) && row.guid) {
          const tabsNav = document.querySelector('.table-container .el-tabs__nav.is-top')
          const offsetLeft = (tabsNav ? Math.round(tabsNav.getBoundingClientRect().width) : 85 * tabsNum) + 10
          return `position: absolute; top: 13px; left: ${offsetLeft}px;`
        }
      }
      return 'display: none'
    }
  },
  async created() {
    // 从字典获取终端数据
    const termMap = getTermTypeDict()
    termMap.forEach(term => {
      const type = term.type == 'windows' ? 'win' : term.type
      this.terminalTypeOptions[term.value] = type
    })
    this.listModule()
    // 获取过滤销售模块
    const resp = await getFilterSalesModule()
    this.filterSaleModule = resp.data
  },
  mounted() {
    setTimeout(() => {
      const { entityId, entityType } = this.$route.query
      if (entityId && entityType) {
        entityLink({ entityType, entityId }, {}, this)
      }
      // 根据路由查询条件entityId、entityType查询之后，清除查询
      this.$router.push({ query: {}});
    }, 150)
  },
  activated() {
    const { entityId, entityType } = this.$route.query
    if (entityId && entityType) {
      entityLink({ entityType, entityId }, {}, this)
    }
    // 根据路由查询条件entityId、entityType查询之后，清除查询
    this.$router.push({ query: {}});
  },
  deactivated() {
    if (this.timer != null) {
      clearInterval(this.timer)
    }
  },
  methods: {
    updateLimit(val) {
      if (this.query.limit !== val) {
        this.query.limit = val
        this.handleRefresh()
      }
    },
    updatePage(val) {
      if (this.query.page !== val) {
        this.query.page = val
        this.reloadTable()
      }
    },
    updateSort(val) {
      if (val) {
        let isChange = false
        for (const valKey in val) {
          if (this.query[valKey] !== val[valKey]) {
            this.query[valKey] = val[valKey]
            isChange = true
          }
        }
        if (isChange) {
          this.reloadTable()
        }
      }
    },
    setSubmitable() {
      if (!this.submitable) {
        this.submitable = true
      }
    },
    getOsGroup() {
      return this.activeTab.substring(0, this.activeTab.length - 3)
    },
    getOsType() {
      return { 'winTab': 1, 'linuxTab': 2, 'macTab': 4, 'phoneTab': 8 }[this.activeTab]
    },
    getTabByOsType(osType) {
      return { 1: 'win', 2: 'linux', 4: 'mac', 8: 'phone' }[osType]
    },
    changeColModelFunc() {
      const osGroup = this.getOsGroup()
      //  过滤掉不显示的模块
      const colModel = this.showModulesFilter(this.colModelOptions[osGroup])
      this.colModel.splice(1, this.colModel.length - 1, ...colModel)
    },
    tabClickFunc() {
      this.query.osType = this.getOsType()
      this.rowData.splice(0)
      this.query.showModules.splice(0)
      this.handleFilter()
      this.changeColModelFunc()
    },
    activeFirstTab(osTypes, osType) {
      if (osTypes) {
        this.showTabs.splice(0)
        for (let i = 0; i < osTypes.length; i++) {
          const tab = this.getTabByOsType(osTypes[i])
          if (tab && this.showTabs.indexOf(tab) < 0) {
            this.showTabs.push(tab)
          }
        }
      }
      let tab = this.getTabByOsType(osType)
      if (!tab) {
        tab = this.showTabs[0]
      }
      this.activeTab = tab + 'Tab'
      this.query.osType = this.getOsType()
      this.changeColModelFunc()
    },
    resetModuleData() {
      this.moduleData = JSON.parse(JSON.stringify(this.defaultModuleData))
    },
    search(val) {
      this.moduleDatas.forEach(el => {
        el.options.forEach(e => {
          const v = val && val.toLowerCase()
          const name = e.moduleName.toLowerCase()
          if (v && name.indexOf(v) > -1) {
            e.search = true
          } else {
            e.search = false
          }
        })
      })
    },
    reloadTable() {
      this.tableLoading = true
      const searchQuery = Object.assign({}, this.query)
      this.getRowData(searchQuery)
    },
    getRowData: function(searchQuery) {
      //  支持根据编号和终端名称条件查询
      const query = JSON.parse(JSON.stringify(searchQuery))
      if (query.searchInfo) {
        query.searchIdAndName = query.searchInfo
        query.searchInfo = null
      }
      clearInterval(this.timer)
      this.submitable = false
      const that = this
      // 表头数据更新完毕的标识
      this.colModelUpdated = false
      // 刷新表数据需要同时刷新表头（主要是刷新表头中的可分配数据）
      this.listModule()
      listTerminalModule(query).then(response => {
        if (response.data && response.data.total) {
          this.gridTable.total = response.data.total
          this.rowDataOptions = {}
          this.rowData.splice(0)
          const terminals = {}
          // 格式化终端信息
          response.data.items.forEach(item => {
            terminals[item.id] = item
            const termType = this.terminalTypeOptions[item.type]
            if (termType) {
              if (!this.rowDataOptions[termType]) {
                this.rowDataOptions[termType] = []
              }
              this.rowDataOptions[termType].push(item)
            }
          })
          for (const termId in response.data.modules) {
            const m = { ...this.moduleState }
            const terminal = terminals[termId]
            if (terminal) {
              Object.assign(terminal, m, { moduleIds: response.data.modules[termId] })
              terminal.moduleIds.forEach(moduleId => { terminal[moduleId] = true })
            }
          }
          // 显示有数据的Tab
          this.activeFirstTab(response.data.osTypes, response.data.osType)
          if (this.timer !== null) {
            clearInterval(that.timer)
          }
          that.timer = setInterval(() => {
            if (that.colModelUpdated) {
              const osGroup = this.getOsGroup()
              const rowDataOps = that.rowDataOptions[osGroup]
              that.rowData.splice(0)
              rowDataOps && that.rowData.push(...rowDataOps)
              this.tableLoading = false
              clearInterval(that.timer)
            }
          }, 100)
        } else {
          if (this.timer !== null) {
            clearInterval(that.timer)
          }
          this.timer = setInterval(() => {
            if (that.colModelUpdated) {
              that.rowData.splice(0)
              this.gridTable.total = 0
              clearInterval(that.timer)
            }
            this.tableLoading = false
          }, 100)
        }
      })
      // 根据路由查询条件entityId、entityType查询之后，清除查询
      if (this.$route.query.entityId && this.$route.query.entityType) {
        this.$route.query.entityId = undefined
        this.$route.query.entityType = undefined
      }
    },
    /**
     * 获取模块信息
     * colModul初始化后不再修改
     */
    listModule: function() {
      const _this = this
      const allModuleIds = []
      const moduleDatas = []
      this.colModelOptions = { win: [], linux: [], mac: [], phone: [] }
      const productId = 511
      getModuleInfoByProdId(productId).then(respond => {
        // 再次初始化，避免多次异步请求导致数据异常
        this.colModelOptions = { win: [], linux: [], mac: [], phone: [] }
        if (respond.data && respond.data.length > 0) {
          this.resetModuleData()
          this.moduleData.productId = productId
          this.moduleData.options = respond.data
          this.moduleState = {}
          respond.data.forEach(moduleInfo => {
            const { osGroup, moduleId, usedNum, numbers } = moduleInfo
            let { moduleName } = moduleInfo
            if (moduleName.toLowerCase().startsWith(osGroup + '_')) {
              moduleName = moduleName.substring(osGroup.length + 1)
            }
            this.moduleNameMap[moduleId] = moduleName
            this.moduleState[moduleId] = false
            allModuleIds.push(moduleId)
            if (osGroup && moduleId != 1 && moduleId != 2) {
              if (!this.nextMoudleUsedFlag) {
                this.moudleUsed[moduleId] = usedNum
                this.moudleUsedInit[moduleId] = usedNum
              }
              const col = {
                prop: String(moduleId),
                label: moduleName,
                headerType: 'checkbox',
                headerChecked: false,
                type: 'checkbox',
                disabled: (col, row) => {
                  const termType = row.type
                  const isUTerm = (termType & 0xf0) === 0x20
                  const isOTerm = (termType & 0xf0) === 0x80
                  const isBTerm = (termType & 0xf0) === 0x10
                  if (isUTerm || isOTerm) {
                    // 已占用的U盘终端和永久离线终端：已分配的模块不能取消
                    const usedModule = !!row.guid && row.moduleIds && row.moduleIds.indexOf(moduleId) >= 0
                    if (usedModule) {
                      return true
                    }
                  }
                  // 老板终端不支持下发文件相似度识别模块和文件分类模块：已分配的模块不能取消
                  if (isBTerm && this.bossDisabledSaleModule.indexOf(moduleId) > -1) {
                    return true
                  }
                  const filterModules = this.filterSaleModule[termType]
                  // 只有有配置过滤销售模块的配置才进入这个分支
                  if (filterModules && filterModules.length > 0) {
                    return filterModules.indexOf(moduleId) > -1
                  }
                  return false
                },
                width: '200',
                numbers,
                headerFormat: (col, index) => {
                  const { label, numbers, prop } = col
                  const usedNum = this.moudleUsed[prop]
                  return `${label} (${usedNum}/${numbers})`
                },
                headerCheckboxChange: this.headerCheckboxChange,
                checkboxChange: this.checkboxChange
              }
              if (this.colModelOptions[osGroup]) {
                this.colModelOptions[osGroup].push(col)
              }
            }
          })
          if (this.moduleData.options && this.moduleData.options.length > 0) {
            moduleDatas.push(this.moduleData)
          }
          this.allModuleIds.splice(0, this.allModuleIds.length, ...allModuleIds)
          this.moduleDatas.splice(0, this.moduleDatas.length, ...moduleDatas)
          this.$nextTick(() => {
            this.changeColModelFunc()
            this.colModelUpdated = true
          })
        }
        //  初始化模块筛选框的值
        _this.initModulesFilter()
        _this.nextMoudleUsedFlag = false
      })
    },
    selectionClick(selection, row) {
      this.setSubmitable()
      this.moduleDatas.forEach(moduleData => {
        if (moduleData.productId == 511) { // 目前仅511产品可用
          const selectIndex = selection.indexOf(row)
          this.colModel.forEach(col => {
            const moduleId = +col.prop
            if (isNaN(moduleId) || col.disabled(col, row)) {
              return
            }
            if (selectIndex > -1) {
              if (!row[moduleId] || row[moduleId] == false) {
                this.updateColLabel(col, 1) && (row[moduleId] = true)
              }
            } else {
              if (row[moduleId] && row[moduleId] == true) {
                this.updateColLabel(col, -1) && (row[moduleId] = false)
              }
            }
          })
        }
      })
    },
    selectAllRow(selection) {
      this.rowData.forEach(se => {
        this.selectionClick(selection, se)
      })
    },
    updateColLabel(col, value) {
      const usedNum = +this.moudleUsed[col.prop] + value
      if (usedNum <= col.numbers) {
        this.moudleUsed[col.prop] = usedNum
        return true
      } else {
        if (!this.promptShowed) {
          this.promptShowed = true
          this.$confirmBox(this.$t('pages.moduleConfig_confirmMsg3'), this.$t('text.prompt'), { showCancelButton: false }).then(() => {
            this.promptShowed = false
          }).catch(() => {
            this.promptShowed = false
          })
        }
        return false
      }
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      return !(node.oriData != 0)
    },
    headerCheckboxChange(val, scope, col) {
      if (this.rowData.length > 0) this.setSubmitable()
      const rowData = this.rowData
      const moduleId = +col.prop
      let usedNum = this.moudleUsed[moduleId]
      const numbers = col.numbers
      for (let i = 0, len = rowData.length; i < len; i++) {
        const row = { ...rowData[i] }
        if (col.disabled(col, row)) {
          continue
        }
        if (val === true) {
          if (!row[moduleId] || row[moduleId] === false) {
            if (usedNum < numbers) {
              row[moduleId] = true
              this.$set(this.rowData, i, row)
              usedNum += 1
            } else {
              this.$confirmBox(this.$t('pages.moduleConfig_confirmMsg1'), this.$t('text.prompt'), { showCancelButton: false }).then(() => {}).catch(() => {})
              break
            }
          }
        } else {
          if (row[moduleId] === true) {
            row[moduleId] = false
            this.$set(this.rowData, i, row)
            usedNum -= 1
          }
        }
      }
      this.$set(this.moudleUsed, col.prop, usedNum)
    },
    checkboxChange(val, row, col) {
      this.setSubmitable()
      const moduleId = +col.prop
      let usedNum = this.moudleUsed[moduleId]
      const numbers = col.numbers
      if (val === true) {
        if (!row[moduleId] || row[moduleId] === false) {
          // 需要开启移动终端权限，移动端CAD权限才允许使用
          if (moduleId == 60001 && row[351] === false) {
            this.$confirmBox(this.$t('pages.moduleConfig_confirmMsg4'), this.$t('text.prompt'), { showCancelButton: false }).then(() => {}).catch(() => {})
          } else {
            if (usedNum < numbers) {
              row[moduleId] = true
              usedNum += 1
            } else {
              this.$confirmBox(this.$t('pages.moduleConfig_confirmMsg2'), this.$t('text.prompt'), { showCancelButton: false }).then(() => {}).catch(() => {})
            }
          }
        }
      } else {
        if (row[moduleId] === true) {
          row[moduleId] = false
          usedNum -= 1
          // 需要开启移动终端权限，移动端CAD权限才允许使用
          if (moduleId == 351) {
            row[60001] = false
          }
        }
      }
      this.moudleUsed[col.prop] = usedNum
      // 修改行数据无法触发表头更新，通过下面方式实现表头更新
      this.$refs.terminalModuleList.toggleRowSelection(row)
      this.$refs.terminalModuleList.toggleRowSelection(row, false)
    },
    async strategyTargetNodeChange(tabName, data) {
      const curMillis = new Date().getTime()
      if (this.tableLoading && curMillis - this.tableLoadMillis < 1000) {
        // 进行限制，1秒内重复点击同一个树节点，则直接返回
        if (data && data.type && this.query.objectType && this.query.objectType === data.type) {
          return
        }
      }
      this.tableLoadMillis = curMillis
      if (data) {
        this.query.objectType = data.type
        this.query.objectId = data.dataId
        this.terminalName = data.label
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.query.osType = null
      this.handleRefresh()
    },
    handlesetting() {
      this.$refs['moduleManageConfig'].show()
    },
    handleAllSetting() {
      this.$refs['allEditModule'].show()
    },
    handleRefresh() {
      this.rowData.splice(0)
      this.query.showModules = []
      this.query.page = 1
      //  在切换终端类型时，页码设置为1
      this.$refs.terminalModuleList.updatePage(1)
      this.reloadTable()
      this.initModulesFilter()
      this.submitting = false
    },
    handleFilter() {
      this.query.page = 1
      //  在切换终端类型时，页码设置为1
      this.$refs.terminalModuleList.updatePage(1)
      this.getRowData(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleImport() {},
    handleExport() {},
    formatSubmitData(data) {
      if (!data.moduleIds) {
        data.moduleIds = []
      } else {
        data.moduleIds.splice(0)
      }
      this.allModuleIds.forEach(moduleId => {
        const moduleCheckVal = data[moduleId]
        if ((moduleCheckVal && moduleCheckVal === true) || moduleId === 1 || moduleId === 2) data.moduleIds.push(moduleId)
      })
    },
    saveData() {
      if (this.submitting) return
      let hasOTerm = false
      let hasUTerm = false
      let hasOther = false
      this.rowData.forEach(data => {
        const termType = data.type & 0xf0
        if (termType === 0x80) {
          hasOTerm |= true
        } else if (termType === 0x20) {
          hasUTerm |= true
        } else {
          hasOther |= true
        }
        this.formatSubmitData(data)
      })
      const that = this
      saveModule(this.rowData).then(res => {
        let messages = ''
        const OTerm = hasOTerm ? this.$t('pages.OTerm') : ''
        const UTerm = hasUTerm ? this.$t('pages.UTerm') : ''
        const term = (hasOTerm && hasUTerm) ? this.$t('pages.OUTerm') : (OTerm || UTerm)
        if (hasOTerm || hasUTerm) {
          if (hasOther) {
            // 终端模块信息设置成功，{term}请更新策略生效，其它终端请耐心等待生效。
            messages = this.$t('pages.moduleConfigSuccessNotify3', { term })
          } else {
            // 终端模块信息设置成功，{term}请更新策略生效。
            messages = this.$t('pages.moduleConfigSuccessNotify2', { term })
          }
        } else {
          // 终端模块信息设置成功，请耐心等待生效。
          messages = this.$t('pages.moduleConfigSuccessNotify1')
        }
        this.$notify({ title: this.$t('text.success'), message: messages, type: 'success', duration: 3000 })
        this.submitable = false
        this.nextMoudleUsedFlag = true
        this.$socket.subscribeToAjax(res, '/modifyTerminalModules', (respond, handle) => {
          handle.close()
          if (respond.data == 'success') {
            that.reloadTable()
            that.initModulesFilter()
          }
        })
      }).catch(() => {
        this.reloadTable()
        this.initModulesFilter()
        this.submitting = false
      })
    },
    terminalNameFormatter(row, data) {
      const terminalName = !row.name ? this.$t('pages.unknownTerminal') : row.name
      return `${terminalName}(${row.id})`
    },
    //  清空展示模块选择框时触发事件
    modelClear() {
      this.handleFilter()
    },
    //  展示模块选框择焦点事件
    modelVisibleChange(flag) {
      if (flag) {
        this.modelSelectFlag = false
        this.tempShowModules = JSON.parse(JSON.stringify(this.query.showModules));
      } else {
        this.modelSelectFlag = true
        //  展示模块选择框失去焦点事件
        if (!this.validShowModuleChange(this.tempShowModules, this.query.showModules)) {
          this.handleFilter()
        }
      }
    },
    //  判断两个模块是否一致
    validShowModuleChange(temp, array) {
      if (temp.length === array.length) {
        let flag = false;
        for (let i = 0; i < temp.length; i++) {
          flag = false;
          if (this.arrayFind(array, temp[i])) {
            flag = true
          }
          if (!flag) {
            return false
          }
        }
        return true;
      }
      return false;
    },
    //  查询item算法在array中
    arrayFind(array, item) {
      for (let i = 0; i < array.length; i++) {
        if (array[i] == item) {
          return true
        }
      }
      return false
    },
    //  展示模块选框删除标签事件
    modelRemoveTag() {
      if (this.modelSelectFlag) {
        this.handleFilter()
      }
    },
    //  点击终端名称展示模块事件
    elDragClick(row) {
      //  永久离线终端和U盘终端不支持分配
      if (![128, 129, 130, 131, 32, 33, 34].includes(row.type)) {
        this.$refs['editModule'].show(row.id)
      }
    },
    //  初始化模型展示内容
    initModulesFilter() {
      if (this.query.osType === undefined || this.query.osType === null) {
        //  展示模块筛选框中的内容是第一操作系统所对应的模块
        if (this.showTabs.length > 0) {
          this.showModules = this.colModelOptions[this.showTabs[0]]
        }
      } else {
        const my_osType = this.getTabByOsType(this.query.osType)
        this.showModules = this.colModelOptions[my_osType]
      }
    },
    //  过滤掉不展示的模块
    showModulesFilter(colModel) {
      if (this.query.showModules.length > 0) {
        const colModelFilter = []
        for (let i = 0; i < colModel.length; i++) {
          if (colModel[i].prop === 'name') {
            colModelFilter.push(colModel[i])
          } else {
            for (let j = 0; j < this.query.showModules.length; j++) {
              if (colModel[i].prop === this.query.showModules[j]) {
                colModelFilter.push(colModel[i])
                break;
              }
            }
          }
        }
        return colModelFilter
      } else {
        return colModel;
      }
    },
    //  弹窗回调方法,更新moduleUsed
    callUpdateModuledUsed(result) {
      result = result || []
      result.forEach(item => {
        this.moudleUsed[item.moduleId] = item.usedNum
        this.moudleUsedInit[item.moduleId] = item.usedNum
      })
      this.nextMoudleUsedFlag = true
      // 控制台延时500毫秒刷新界面，避免因为引擎那边更新缓存不及时导致数据不同步
      setTimeout(() => {
        this.handleRefresh()
      }, 800)
    },
    excelExportHandler() {
      this.$refs['exportDlg'].show()
    },
    //  分配模块结束
    updateModuleSubmit(notAllocationNodes) {
      //  延迟加载,确保模块分配完成
      setTimeout(() => {
        this.handleRefresh()
      }, 1500)

      //  显示模块节点数不足的
      if (notAllocationNodes) {
        //  去除所有节点分配成功的模块分配信息
        notAllocationNodes = notAllocationNodes.filter(item => item.moduleNodeNumInsufficientLength);
        notAllocationNodes.length && this.$refs['moduleInsufficientDlg'].show(notAllocationNodes, this.colModelOptions);
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    height: 100%;
    overflow: auto;
    .el-col-4{
      min-width: 200px;
    }
  }
  .save-btn-container{
    padding: 10px 50px 0;
  }
  .moudle-box{
    padding: 5px 5px 0;
    height: 420px;
    overflow: auto;
  }
  .el-checkbox{
    width: 40%;
    margin-left: 5%;
  }
  >>>.el-checkbox__label{
    margin-left: 10px;
    padding-left: 0;
  }
  .search >>>.el-checkbox__label{
    background: yellow;
  }
</style>
