<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.moduleNodeInsufficient')"
    :visible.sync="visible"
    width="800px"
    @close="close"
  >
    <div >
      <div class="table-container">
        <el-tabs v-model="tabName" class="tabsClass" tab-position="left" style="height: 460px">
          <el-tab-pane v-for="(item, index) in temps" :key="index" :name="item.osType" :label="osTypeDict[item.osType]">
            <div v-if="tabName === item.osType" style="padding-top: 10px">
              <el-divider content-position="left" class="dividerClass" style="width: 200px;"> {{ $t('pages.terminalInfo') }} </el-divider>
              <grid-table
                :ref="item.osType + 'termList'"
                :col-model="termColModel"
                :multi-select="false"
                :row-data-api="rowDataApi"
                :height="190"
                style="margin-bottom: 18px"
                @row-click="rowClick"
              />

              <el-divider content-position="left" style="width: 200px;"> {{ $t('pages.moduleInfo') }} </el-divider>
              <grid-table
                :ref="item.osType + 'moduleList'"
                :col-model="moduleColModel"
                :multi-select="false"
                :row-datas="moduleDatas"
                :height="190"
                :show-pager="false"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">
        {{ $t('button.close') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'AllModuleEdit',
  components: {},
  data() {
    return {
      visible: false,
      submitting: false,
      termColModel: [
        { prop: 'termName', label: 'temNameTermId', width: '150', formatter: this.termNameTermIdFormatter },
        { prop: 'notAllocationModuleIds', label: 'notAllocationSuccessModule', width: '200', formatter: this.notAllocationModuleFormatter }
      ],
      moduleColModel: [
        { prop: 'moduleName', label: 'moduleName', sort: true, width: '150' },
        { prop: 'total', label: 'moduleNodeNumber', sort: true, width: '200' },
        { prop: 'notSuccessNum', label: 'notAllocationSuccessModuleNum', sort: true, width: '200' }
      ],
      temps: {},  //  存储各个操作系统类型的终端分配模块数据
      defaultTemp: {  //  仅作为备注
        //  节点数不足的模块 key:模块ID，value：不足的数量
        moduleNodeNumInsufficient: {},
        //  key: 终端id  value:节点数不足的模块ID
        termModuleNodeNumInsufficient: {},
        //  终端信息， key：终端ID，value:终端名称
        termModules: {}
      },
      osTypeDict: {
        'win': 'Windows',
        'linux': 'Linux',
        'mac': 'Mac',
        'phone': '移动终端'
      },
      tabName: '',
      moduleDatas: [],  //  模块信息列表数据
      colModelOptions: {} //  各终端操作系统类型的模块信息
    }
  },
  computed: {
    gridTable() {
      return this.$refs[this.tabName + 'termList']
    }
  },
  created() {
  },
  methods: {
    /**
     * 显示
     * @param temps 模块分配情况
     * @param colModelOptions 各终端操作系统类型的模块信息
     */
    show(temps, colModelOptions) {
      this.moduleDatas = []
      this.tabName = temps[0].osType
      this.temps = {}
      temps.forEach(temp => {
        this.temps[temp.osType] = temp;
      })
      this.colModelOptions = {}
      const osTypes = ['win', 'linux', 'mac', 'phone'];
      if (colModelOptions) {
        osTypes.forEach(type => {
          this.colModelOptions[type] = []
          colModelOptions[type].forEach(option => {
            this.colModelOptions[type][option.prop] = option
          })
        })
      }
      this.visible = true
      this.$nextTick(() => {
        this.gridTable && this.gridTable[0].execRowDataApi()
      })
    },
    /**
     * 关闭弹窗
     */
    close() {
      this.visible = false
    },
    /**
     * 终端名称格式化
     * @param row
     * @param data
     * @returns {string|*}
     */
    termNameTermIdFormatter(row, data) {
      if (row.termName === undefined) {
        return row.termId
      }
      return row.termName + '(' + row.termId + ')'
    },
    /**
     * 分页查询
     * @param option
     * @returns {Promise<*>}
     */
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, option)
      return this.getTermPage(searchQuery, this.tabName)
    },
    /**
     * 获取当前的模块分配信息
     * @returns {*}
     */
    getCurrentTemp() {
      return this.temps[this.tabName];
    },
    /**
     * 分页查询
     * @param searchQuery 查询条件
     * @param termOsType  终端操作系统类型 [win, linux, mac, phone]
     * @returns {Promise<unknown>}
     */
    getTermPage(searchQuery, termOsType) {
      let i = 1;
      const start = (searchQuery.page - 1) * searchQuery.limit + 1
      let limit = searchQuery.limit
      const list = []
      const keys = Object.keys(this.getCurrentTemp().termModules);
      for (const key of keys) {
        if (i >= start && limit > 0) {
          //  构建列表元素
          const map = {}
          map.termId = key
          map.termName = this.getCurrentTemp().termModules[key]
          map.notAllocationModuleIds = this.getCurrentTemp().termModuleNodeNumInsufficient[map.termId]
          //  去除 1:基础模块，2：综合报表模块
          map.notAllocationModuleIds = map.notAllocationModuleIds ? map.notAllocationModuleIds.filter(item => ![1, 2].includes(item)) : []
          map.notAllocationModules = []
          map.notAllocationModuleIds.forEach(data => {
            map.notAllocationModules.push(this.colModelOptions[termOsType][data + '']);
          })
          list.push(map);
          limit--;
        }
        i++;
      }
      return new Promise((resolve, reject) => { resolve({ code: 20000, data: { items: list, total: keys.length }}) })
    },
    notAllocationModuleFormatter(row, data) {
      const names = []
      row.notAllocationModules && row.notAllocationModules.forEach(item => {
        names.push(item.label)
      })
      return names.join('，');
    },
    rowClick(rowData, column, event) {
      this.moduleDatas = []
      rowData.notAllocationModules && rowData.notAllocationModules.forEach(item => {
        this.moduleDatas.push({ id: parseInt(item.prop), moduleName: item.label, total: item.numbers, notSuccessNum: this.getCurrentTemp().moduleNodeNumInsufficient[parseInt(item.prop)] })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dividerClass {
  >>> .el-divider.el-divider--horizontal {
    margin: 7px 0 15px!important;
  }
}
>>> .el-dialog__body .el-divider.el-divider--horizontal {
  margin: 10px 0 15px;
}
>>> .tabsClass .el-tabs__content {
  height: 100%;
}
</style>
