<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">{{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t("table.search") }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ processNameFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 580px"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit/>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <Form
          ref="appReportSetForm"
          :rules="appReportRules"
          :model="temp"
          label-position="right"
          label-width="80px"
          style="position: relative"
        >
          <el-row>
            <el-col :span="24">
              <FormItem label-width="15px" prop="unActiveTime">
                <el-checkbox
                  v-model="autoShutdown"
                  :disabled="!formable"
                  @change="checkChange"
                ></el-checkbox>
                <i18n path="pages.appReportSetText">
                  <el-input-number
                    v-show="autoShutdown"
                    slot="second"
                    v-model="temp.unActiveTime"
                    :controls="false"
                    :disabled="!formable || !autoShutdown"
                    style="width: 70px"
                    :min="autoShutdown ? 1 : 0"
                    :max="999"
                  ></el-input-number>
                  <el-input-number
                    v-show="!autoShutdown"
                    slot="second"
                    :controls="false"
                    :disabled="true"
                    style="width: 70px"
                  ></el-input-number>
                </i18n>
              </FormItem>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <FormItem label-width="15px" prop="termReportLimitTime">
                <i18n path="pages.appReportSetText1">
                  <el-input-number
                    slot="second"
                    v-model="temp.termReportLimitTime"
                    :controls="false"
                    :disabled="!formable"
                    style="width: 70px"
                    :min="1"
                    :max="999"
                  />
                </i18n>
              </FormItem>
            </el-col>
          </el-row>
          <FormItem :label="$t('pages.monitorProcess')">
            <el-radio v-model="temp.allProcess" :label="0" :disabled="!formable" @change="changeActive()">{{ $t('pages.monitorProcess1') }}</el-radio>
            <el-radio v-model="temp.allProcess" :label="1" :disabled="!formable" @change="changeActive()">{{ $t('pages.monitorProcess2') }}</el-radio>
          </FormItem>
          <el-card v-if="temp.allProcess===1" class="box-card" :body-style="{'padding': '5px 5px 0'}">
            <div slot="header" class="clearfix">
              <span>{{ $t('pages.controlProcess') }}</span>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.controlProcess_tip') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button v-if="formable" size="small" @click="handleFingerImport()">
                {{ $t('button.applicationLibraryImport') }}
              </el-button>
              <el-button v-if="formable" size="small" @click="handleClear">
                {{ $t('button.clear') }}
              </el-button>
            </div>
            <FormItem label-width="0" prop="processList">
              <tag v-model="temp.processList" :border="true" :list="temp.processList" :disabled="!formable"/>
            </FormItem>
          </el-card>
          <div style="color: rgb(43, 122, 172)">{{ $t('pages.appReportSetText2') }}</div>
        </Form>
      </Form>

      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button
          v-if="formable"
          :loading="submitting"
          type="primary"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="elgClose">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :modal-append-to-body="true"
      :title="i18nConcatText($t('pages.applicationProgram'), 'create')"
      :visible.sync="appFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="appDataForm"
        :rules="appRules"
        :model="appTemp"
        label-position="right"
        label-width="120px"
        style="width: 500px; margin-left: 25px"
      >
        <FormItem :label="$t('pages.controlApp')" prop="tempProcessName" label-width="110px">
          <el-row>
            <el-col :span="20">
              <el-input v-model="appTemp.tempProcessName" disabled></el-input>
            </el-col>
            <el-col :span="2">
              <el-upload
                name="processFile"
                action="1111"
                accept=".exe"
                :limit="1"
                :show-file-list="false"
                :before-upload="getFileName"
              >
                <el-button type="primary" icon="el-icon-upload"></el-button>
              </el-upload>
            </el-col>
          </el-row>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createApp()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="appFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <app-select-dlg ref="appSelectDlg" @select="appendData2Table"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importAppReportSetStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { getStrategyList, getStrategyByName, createStrategy, updateStrategy, deleteStrategy } from '@/api/system/terminalManage/appReportSetStrategy'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { exportStg } from '@/api/stgCommon'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'

export default {
  name: 'AppReportSet',
  components: { AppSelectDlg, ImportStg },
  props: {
    listable: { type: Boolean, default: true },       // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    formable: { type: Boolean, default: true },       // 能否提交表单
    importAble: { type: Boolean, default: false },    // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false }     // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 143,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        {
          prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        // { label: 'programName', ellipsis: false, type: 'popover', originData: true, width: '200', formatter: this.processNameFormatter },
        { prop: '', label: 'stgMessage', width: '300', formatter: this.stgFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        {
          label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      controlledColModel: [
        { prop: 'processName', label: 'controlProcess', width: '100' }
      ],
      exceptionModel: [
        { prop: 'processName', label: 'exceptProcess', width: '150' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        // 未进行操作时停止计时
        unActiveTime: 0,
        // 限制记录的有效时间
        termReportLimitTime: 3,
        appReportProcess: [],
        appReportProcessExcept: [],
        processList: [],
        entityType: undefined,
        entityId: undefined,
        allProcess: 0
      },
      dialogFormVisible: false,
      softTable: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.appReportSetStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.appReportSetStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      appRules: {
        tempProcessName: [
          { required: true, message: this.$t('pages.appGroup_text25'), trigger: 'blur' }
        ]
      },
      submitting: false,
      autoShutdown: false,
      libTemp: {},
      appTemp: { tempProcessName: null },
      activeName: 'first',
      appFormVisible: false,
      checkProcessDeleteable: false,
      checkProcessExceptDeleteable: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    appReportRules() {
      return {
        unActiveTime: [
          { required: this.autoShutdown, message: this.$t('pages.fileServerContent_10'), trigger: 'blur' }
        ],
        termReportLimitTime: [
          { required: true, message: this.$t('pages.fileServerContent_10'), trigger: 'blur' }
        ],
        processList: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
  },
  methods: {
    stgFormatter(row, data) {
      let msg = ''
      if (row.allProcess == 0) {
        msg += this.$t('pages.monitorProcess1') + ';'
      } else {
        let processMsg = ''
        for (var i = 0; i < row.controlledProcess.length; i++) {
          if (i == row.controlledProcess.length - 1) {
            processMsg = processMsg + row.controlledProcess[i]
          } else {
            processMsg = processMsg + row.controlledProcess[i] + '、'
          }
        }
        msg += this.$t('pages.monitorProcess2') + ':' + processMsg + ';'
      }
      if (row.unActiveTime > 0) {
        msg += this.$t('pages.appReportSetText', { second: row.unActiveTime }) + ';'
      }
      if (row.termReportLimitTime > 0) {
        msg += this.$t('pages.appReportSetText1', { second: row.termReportLimitTime })
      }
      return msg
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.appReportProcess.splice(0, this.temp.appReportProcess.length)
      this.temp.appReportProcessExcept.splice(0, this.temp.appReportProcessExcept.length)
      this.temp.processList.splice(0)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.activeName = 'first'
      this.dialogFormVisible = true
      this.autoShutdown = false
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        this.$refs['appReportSetForm'] && this.$refs['appReportSetForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.$set(this.temp, 'processList', [])
      const index = this.temp.appReportProcess.findIndex(item => item.processName == '*.*')
      if (index != -1) {
        this.temp.appReportProcess.splice(index, 1)
      } else {
        this.$set(this.temp, 'processList', this.temp.appReportProcess.map(item => item.processName))
      }
      this.autoShutdown = this.temp.unActiveTime !== 0
      this.dialogStatus = 'update'
      // this.activeName = 'first'
      this.activeName = 'second'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        this.$refs['appReportSetForm'] && this.$refs['appReportSetForm'].clearValidate()
      })
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$refs['appReportSetForm'].validate((valid) => {
            if (valid) {
              this.temp.appReportProcess.splice(0)
              this.temp.processList.forEach((process, index) => {
                this.temp.appReportProcess.push({ id: index, processName: process })
              });
              this.temp.controlledProcess = this.temp.appReportProcess.map(item => item.processName)
              createStrategy(this.temp).then(respond => {
                this.submitting = false
                this.dialogFormVisible = false
                this.elgClose()
                this.gridTable.execRowDataApi()
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(reason => {
                this.submitting = false
              })
            } else {
              this.submitting = false
            }
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$refs['appReportSetForm'].validate((valid) => {
            if (valid) {
              this.temp.appReportProcess.splice(0)
              this.temp.processList.forEach((process, index) => {
                this.temp.appReportProcess.push({ id: index, processName: process })
              });
              const tempData = Object.assign({}, this.temp)
              if (this.temp.allProcess === 0) {
                tempData.controlledProcess = []
              } else {
                tempData.controlledProcess = this.temp.appReportProcess.map(item => item.processName)
              }
              updateStrategy(tempData).then(respond => {
                this.submitting = false
                this.dialogFormVisible = false
                this.elgClose()
                this.gridTable.execRowDataApi()
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.updateSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(reason => {
                this.submitting = false
              })
            } else {
              this.submitting = false
            }
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => { })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    processNameFormatter: function(row, data) {
      const names = []
      if (row.appReportProcess) {
        row.appReportProcess.forEach(function(bean) {
          names.push(bean.processName)
        })
      }
      return names.join(', ')
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    checkChange(value) {
      this.temp.unActiveTime = this.autoShutdown ? 30 : 0
    },
    handleFingerImport() {  // 从指纹库导入
      this.$refs.appSelectDlg.show()
    },
    handleAppCreate() {
      this.appTemp.tempProcessName = null
      this.appFormVisible = true
      this.$nextTick(() => {
        this.$refs['appDataForm'] && this.$refs['appDataForm'].clearValidate()
      })
    },
    createApp() {
      this.appendData2Table([{
        processName: this.appTemp.tempProcessName
      }])
      this.appFormVisible = false
    },
    appendData2Table(datas) {
      if (!datas || datas.length === 0) return
      /* const list = this.activeName === 'first' ? this.temp.appReportProcessExcept : this.temp.appReportProcess */
      const list = this.temp.processList
      datas.forEach(item => {
        const theIndex = list.findIndex(existApp => {
          return existApp === item.processName
        })
        if (theIndex > -1) {
          list.splice(theIndex, 1)
        }
        list.unshift(item.processName)
      })
    },
    handleClear() {
      this.temp.processList.splice(0)
    },
    getFileName(file) {
      this.appTemp.tempProcessName = file.name
      return false // 屏蔽了action的默认上传
    },
    processSelectionChangeEnd(rowDatas) {
      this.checkProcessDeleteable = rowDatas && rowDatas.length > 0
    },
    processExceptSelectionChangeEnd(rowDatas) {
      this.checkProcessExceptDeleteable = rowDatas && rowDatas.length > 0
    },
    handleDeleteCheckedProcess() {
      this.$confirmBox(this.$t('pages.install_Msg28'), this.$t('text.prompt')).then(() => {
        // 使用Set收集待删除的ID集合
        const deleteIds = new Set(
          this.$refs[
            this.activeName === 'first' ? 'processExceptGrid' : 'processGrid'
          ].getSelectedDatas().map(item => item.id)
        );

        // 根据activeName选择目标数组
        const targetArray = this.activeName === 'first'
          ? this.temp.appReportProcessExcept
          : this.temp.appReportProcess;

        // 使用filter实现一次性过滤
        targetArray.splice(0, targetArray.length,
          ...targetArray.filter(item => !deleteIds.has(item.id))
        );
      }).catch(() => {})
    },
    changeActive() {
      if (this.temp.allProcess === 0) {
        this.activeName = 'first'
      } else {
        this.activeName = 'second'
      }
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    elgClose() {
      this.dialogFormVisible = false
      this.temp.processList.splice(0, this.temp.processList.length)
    }
  }
}
</script>
