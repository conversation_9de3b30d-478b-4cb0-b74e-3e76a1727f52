<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('button.highConfig')"
    :visible.sync="dialogVisible"
    width="600px"
    @dragDialog="handleDrag"
  >
    <div slot="title" class="el-dialog__title">
      {{ $t('button.highConfig') }}
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          <i18n path="pages.userglobalConfig_tip">
            <br slot="br"/>
          </i18n>
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <Form
      ref="managerConfigForm"
      :model="managerConfig"
      :label-width="$store.getters.language === 'en' ? '0' : '80px'"
      label-position="right"
    >
      <FormItem>
        <el-checkbox v-model="loginLimitTemp.active" @change="activeChange">
          {{ $t('pages.userglobalConfig_active') }}
        </el-checkbox><br/>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="loginLimitTemp.isActiveValidateCode" :disabled="!loginLimitTemp.active">
          <i18n path="pages.userglobalConfig_activeValidateCode">
            <el-input-number
              slot="times"
              v-model="loginLimitTemp.activeValidateCode"
              :disabled="!loginLimitTemp.active || !loginLimitTemp.isActiveValidateCode"
              :min="1"
              :max="100"
              :controls="false"
              :precision="0"
              style="width:100px;"
            />
          </i18n>
        </el-checkbox>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="loginLimitTemp.isActiveLock" :disabled="!loginLimitTemp.active">
          <i18n path="pages.userglobalConfig_activeLock">
            <el-input-number
              slot="times"
              v-model="loginLimitTemp.activeLock"
              :disabled="!loginLimitTemp.active || !loginLimitTemp.isActiveLock"
              :min="1"
              :max="100"
              :controls="false"
              :precision="0"
              style="width:100px;"
            />
          </i18n>
        </el-checkbox>
      </FormItem>
      <FormItem>
        <div style="padding-left: 17px;">
          <i18n path="pages.userglobalConfig_unLock">
            <el-input-number
              slot="minutes"
              v-model="loginLimitTemp.unlockTime"
              :disabled="!loginLimitTemp.active || !loginLimitTemp.isActiveLock"
              :min="1"
              :max="100"
              :controls="false"
              :precision="0"
              style="width:100px;"
            />
          </i18n>
        </div>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleConfig">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getConfigByKey } from '@/api/system/configManage/globalConfig'
import { updateConfigById } from '@/api/system/terminalManage/user'

export default {
  name: 'ConfigUser',
  data() {
    return {
      dialogVisible: false,
      configKey: 'operatorLoginLimit',
      managerConfig: {},
      submitting: false,
      loginLimitTemp: {
        id: undefined,
        active: false, // 开启账号安全登录机制
        failLockDay: 1, // 登录失败锁定计算天数
        isActiveValidateCode: false, // 登录失败超过指定次数后开启随机验证码
        activeValidateCode: 2, // 登录失败超过指定次数后开启随机验证码
        isActiveLock: false, // 登录超过指定次数后将账号锁定
        activeLock: 5, // 登录超过指定次数后将账号锁定
        unlockTime: 30 // 账号被锁定N分钟后自动解锁
      },
      operatorLoginLimit: {}

    }
  },
  watch: {

  },
  created() {
    this.getConfigByKey()
  },
  methods: {
    show() {
      this.getConfigByKey()
      this.dialogVisible = true
    },
    handleDrag() {
    },
    handleConfig() {
      this.submitting = true
      this.operatorLoginLimit.value = JSON.stringify(this.loginLimitTemp)
      if (this.loginLimitTemp.active && !this.loginLimitTemp.isActiveLock && !this.loginLimitTemp.isActiveValidateCode) {
        this.$message({
          message: this.$t('pages.unselect_msg'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      if ((this.loginLimitTemp.isActiveLock && !this.loginLimitTemp.activeLock) || (this.loginLimitTemp.isActiveValidateCode && !this.loginLimitTemp.activeValidateCode)) {
        this.$message({
          message: this.$t('pages.failnumber_msg'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      if (this.loginLimitTemp.isActiveLock && !this.loginLimitTemp.unlockTime) {
        this.$message({
          message: this.$t('pages.unlocktime_msg'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      if (this.loginLimitTemp.isActiveValidateCode && this.loginLimitTemp.isActiveLock && (this.loginLimitTemp.activeValidateCode >= this.loginLimitTemp.activeLock)) {
        this.$message({
          message: this.$t('pages.compare_msg'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      updateConfigById(this.operatorLoginLimit).then(res => {
        this.dialogVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.getConfigByKey()
        this.submitting = false
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    getConfigByKey() {
      getConfigByKey({ key: 'operatorLoginLimit' }).then(respond => {
        this.operatorLoginLimit = respond.data
        const temp = this.operatorLoginLimit.value ? JSON.parse(this.operatorLoginLimit.value) : {}
        this.loginLimitTemp = Object.assign(this.loginLimitTemp, temp)
      })
      // this.$forceUpdate()
    },
    activeChange(data) {
      if (!data) {
        this.loginLimitTemp.isActiveValidateCode = false
        this.loginLimitTemp.isActiveLock = false
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-form-item{
    .el-form-item__label{
      color: #ccc;
      line-height: 30px;
    }
    .el-form-item__content{
      line-height: 30px;
      .el-input__inner{
        height: 30px;
        line-height: 30px;
      }
    }
  }
  .weak,.medium,.strong {
  display: inline-block;
  height: 5px;
  width: 60px;
  margin-left: 3px;
  margin-top: 2px;
  font-size: 2px;
  text-align: center;
  }
  .btn {
    border: none;
  }
  .ul {
    list-style-type: none;
    margin-left: -30px;
  }
</style>
