<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.batchChangeUserGroups')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="400px"
    @dragDialog="handleDrag"
  >
    <Form>
      <FormItem >
        <span style="margin: 0px 5px 10px;">{{ $t('pages.userBatch_text1', { num: selectedData.length}) }}</span>
      </FormItem>
      <FormItem :label="$t('table.userGroup')" label-width="70px" class="required">
        <tree-select :data="groupTreeData" is-filter :width="296" @change="parentIdObjChange" />
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="submitData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { batchUpdateGroup } from '@/api/system/terminalManage/user'

export default {
  name: 'BatchModifyGroupDlg',
  props: {
    groupTreeData: { type: Array, default() { return [] } },
    selectedData: { type: Array, default() { return [] } }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      temp: {
        ids: [],
        groupId: undefined,
        syncGroup: undefined
      }
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      // 控制dialog是否显示（父调用子方法）
      this.dialogVisible = true
    },
    submitData() {
      if (this.temp.groupId == undefined) {
        this.$message({
          message: this.$t('pages.userBatch_text2'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.temp.ids.splice(0, this.temp.ids.length)
      this.selectedData.forEach(o => {
        this.temp.ids.push(o.id)
      });
      batchUpdateGroup({ groupId: this.temp.groupId, ids: this.temp.ids.join(','), syncGroup: this.temp.syncGroup }).then(respond => {
        this.dialogVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.$emit('submitEnd')
      })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.temp.groupId = nodeData.dataId
      }
    },
    handleDrag() {

    }
  }
}
</script>
