<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="userGroupTree"
        :data="userGroupTreeData"
        :local-search="false"
        :get-search-list="getSearchListFunction"
        :default-expanded-keys="defaultExpandedKeys"
        @current-change="nodeChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button v-permission="'106'" type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createUser') }}
        </el-button>
        <el-button v-permission="'106'" icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('pages.deleteUser') }}
        </el-button>
        <el-button v-if="$store.getters.userValidConfigAble" icon="el-icon-time" size="mini" :disabled="!deleteable" type="primary" @click="handleUpdateValid">
          {{ $t('button.updateValidTime') }}
        </el-button>
        <el-button v-permission="'106'" style="text-transform: capitalize" icon="el-icon-document-copy" size="mini" @click="batchModifyGroup">
          {{ $t('pages.batchChangeUserGroups') }}
        </el-button>
        <el-button v-permission="'365'" icon="el-icon-setting" size="mini" @click="handleSetting">{{ $t('button.highConfig') }}</el-button>
        <el-button icon="el-icon-view" size="mini" style="text-transform: capitalize" @click="handleLockScreenVerify">{{ $t('pages.operatorLoginUnlock') }}</el-button>
        <!-- <el-dropdown style="padding-left: 10px;" @command="handleMoreClick">
          <el-button size="mini">
            {{ $t('pages.moreOperate') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1" icon="el-icon-document-copy" :disabled="!deleteable">{{ $t('pages.batchChangeUserGroups') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
        <el-button v-permission="'255'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button v-permission="'256'" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <!--<el-button v-permission="'106'" icon="el-icon-edit" size="mini" :disabled="!deleteable">
          移动部门
        </el-button>
        <el-button v-permission="'106'" disabled icon="el-icon-edit" size="mini">
          导入导出
        </el-button>-->
        <!--<el-button icon="el-icon-edit" size="mini">
          自定义表单
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="this.$t('pages.userNameOrAccount')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom-end"
            width="350"
            trigger="click"
            :append-to-body="false"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('pages.userName')">
                <el-input v-model="query.name" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.userStatus')">
                <el-select v-model="query.status" style="width: 100%;" @change="statusChange">
                  <el-option v-for="(status, i) in userStatus" :key="i" :label="status.label" :value="status.value"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.account')">
                <el-input v-model="query.account" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('table.email')">
                <el-input v-model="query.email" v-trim :maxlength="60" clearable suffix-icon="el-icon-message"/>
              </FormItem>
              <FormItem :label="$t('table.phone')">
                <el-input v-model="query.phone" v-trim :maxlength="15" clearable suffix-icon="el-icon-phone"/>
              </FormItem>
              <FormItem :label="$t('form.status')">
                <el-select v-model="query.active" clearable style="width: 100%;" :placeholder="$t('text.select')">
                  <el-option v-for="(value, key) in activeOptions" :key="key" :label="value" :value="key"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.immediateSupervisor')">
                <tree-select
                  clearable
                  is-filter
                  :local-search="false"
                  :default-expand-all="false"
                  node-key="id"
                  :checked-keys="['U' + query.immediateSupervisor]"
                  :placeholder="$t('pages.pleaseSelectContent', { content: $t('pages.immediateSupervisor') })"
                  :rewrite-node-click-fuc="true"
                  :node-click-fuc="userTreeNodeCheckChangeQuery"
                  @change="userIdSelectChangeQuery"
                />
              </FormItem>
              <FormItem :label="$t('table.role')">
                <tree-select
                  ref="roleTree"
                  :data="userRoleOptions"
                  node-key="id"
                  style="width: 100%;"
                  :checked-keys="query.userRoleIds"
                  check-strictly
                  :multiple="true"
                  class="input-with-button"
                  @change="userRoleNodeSelectChangeQuery"
                />
              </FormItem>
              <FormItem :label="$t('table.termUserType')">
                <el-select v-model="query.userType" style="width: 100%;">
                  <el-option v-for="(user, i) in userTypes" :key="i" :label="user.label" :value="user.value"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.searchMode')">
                <el-select v-model="query.includeSubGroup" style="width: 100%;">
                  <el-option :value="true" :label="$t('pages.searchChildDeptOperator')"/>
                  <el-option :value="false" :label="$t('pages.searchDeptOperator')"/>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="() => { resetQuery(); groupTree && groupTree.setCurrentKey(null) }">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="userList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :autoload="false"
        :after-load="afterLoad"
        @updatePage="updatePage"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">
        <input value="" class="autocomplete" />
        <input type="password" value="" class="autocomplete" />
        <FormItem :label="$t('table.account')" prop="account">
          <el-input v-model="temp.account" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.userName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="60"/>
        </FormItem>
        <div v-if="dialogStatus==='create'|| temp.updatePassword">
          <FormItem :label="$t('form.password')" encrypt prop="password">
            <encrypt-input v-model="temp.password" maxlength="20" show-password clearable @input.native="inputPwd($event)"/>
          </FormItem>
          <FormItem :label="$t('form.confirmPassword')" no-submit prop="confirmPassword">
            <encrypt-input v-model="temp.confirmPassword" maxlength="20" show-password clearable @input.native="inputConfirmPwd($event)"/>
          </FormItem>
        </div>
        <FormItem :label="$t('pages.userGroup')" prop="groupId">
          <tree-select
            :data="userGroupTreeSelectData"
            node-key="dataId"
            is-filter
            :checked-keys="temp.groupIds"
            :width="460"
            @change="groupIdSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('pages.mailAdd')" prop="email">
          <el-input v-model="temp.email" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('form.phone')" prop="phone">
          <el-input v-model="temp.phone" :maxlength="15" suffix-icon="el-icon-phone"/>
        </FormItem>
        <FormItem :label="$t('pages.immediateSupervisor')" prop="immediateSupervisor">
          <tree-select
            clearable
            is-filter
            :local-search="false"
            :default-expand-all="false"
            node-key="id"
            :checked-keys="immediateSupervisor"
            :filter-key="`U${temp.id}`"
            :placeholder="$t('pages.pleaseSelectContent', { content: $t('pages.immediateSupervisor') })"
            :rewrite-node-click-fuc="true"
            :node-click-fuc="userTreeNodeCheckChange"
            @change="userIdSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('table.role')" prop="roleIds">
          <tree-select
            ref="roleTree"
            :width="296"
            node-key="id"
            :data="userRoleOptions"
            :multiple="true"
            check-strictly
            :checked-keys="temp.roleIds"
            class="input-with-button"
            style="margin-top: 1px"
            @change="userRoleNodeSelectChange"
          />
          <link-button btn-class="editBtn" :formable="true" :menu-code="'B2S'" :link-url="'/terminalManage/terminalManage/userRole'"/>
        </FormItem>
        <FormItem v-if="$store.getters.userValidConfigAble" :label="$t('table.termUserType')">
          <el-radio-group v-model="temp.userType" @change="userTypeChange">
            <el-radio :label="0">{{ $t('pages.permanentUser') }}</el-radio>
            <el-radio :label="1" :disabled="!$store.getters.userValidConfigAble">{{ $t('pages.temporaryUser') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="$store.getters.userValidConfigAble" :label="$t('pages.validTime')" prop="rangeDate">
          <el-date-picker
            v-model="rangeDate"
            :disabled="temp.userType === 0 || !$store.getters.userValidConfigAble"
            :clearable="true"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 100%"
          />
        </FormItem>
        <FormItem :label="$t('form.status')">
          <el-select v-model="temp.active" :placeholder="$t('text.select')">
            <el-option v-for="(value, key) in activeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="noPwdLogin" :true-label="2" :false-label="0">{{ $t('pages.noPwdLogin') }}</el-checkbox>
        </FormItem>
        <FormItem v-permission="'115'">
          <el-checkbox v-model="mobileShareFile" :true-label="4" :false-label="0">{{ $t('pages.mobileShareFile') }}</el-checkbox>
        </FormItem>
        <!--<FormItem>
          <el-tooltip class="item" effect="dark" content="此名称的window域用户登录域后，终端自动登录" placement="right">
            <el-checkbox v-model="autoAddomainLogin" :true-label="1" :false-label="0">域用户自动登录</el-checkbox>
          </el-tooltip>
        </FormItem>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <config-user ref="configDlg"/>

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="updateGroupForm ? this.$t('text.editInfo', { info: this.$t('pages.userGroup1') }) : this.$t('text.deleteInfo', { info: this.$t('pages.user') })"
      :notice="updateGroupForm ? '' : this.$t('pages.userBatchDelectMsg') "
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.updateUserGroup')" label-width="130px" :extra-width="{en: 149}">
        <tree-select :data="userGroupTreeSelectData" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>

    <!-- <batch-modify-group-dlg ref="batchModifyGroupDlg" :group-tree-data="userGroupTreeSelectData" :selected-data="selectedData" @submitEnd="handleFilter"/> -->
    <export-dlg ref="exportDlg" :group-tree-data="userGroupTreeData" :export-func="exportFunc" :group-tree-id="selectTreeId"/>
    <import-dlg
      ref="importDlg"
      :title="title"
      template="user"
      :show-import-type="false"
      :file-name="title"
      :tip="tip"
      :show-import-way="true"
      :user-show-modes="userShowModes"
      :upload-func="upload"
      @success="importEndFunc"
    />

    <el-dialog
      v-el-drag-dialog
      :title="vcTitle"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="vcVisible"
      :width="$store.getters.language === 'en' ? '455px' : '400px'"
      @dragDialog="handleDrag"
    >
      <Form ref="vcForm" :model="vcForm" label-position="right" label-width="58px">
        <FormItem :label="$t('pages.randomCode')" label-width="58px" :extra-width="{en: 75}">
          <el-input v-model="vcForm.random" maxlength="" @input="vcForm.random=vcForm.random.replace(/[^\w\.\/]/ig,'')"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.captcha')" label-width="58px" :extra-width="{en: 75}">
          <el-input v-model="vcForm.verifiCode" maxlength=""></el-input>
        </FormItem>
      </Form>
      <span slot="footer" class="dialog-footer" >
        <el-button
          type="primary"
          style="float:left;"
          :loading="vcLoading"
          :disabled="!vcForm.random"
          @click="getOperatorUnlockVerify"
        >{{ $t('pages.generateCaptcha') }}</el-button>
        <el-button @click="vcVisible = false">{{ $t('pages.exit') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.updateValidTime')"
      :visible.sync="batchUpdateValidTimeVisible"
      width="600px"
    >
      <Form ref="validTimeForm" :rules="rules2" :model="validTemp" label-width="120px">
        <FormItem :label="$t('table.termUserType')">
          <el-radio-group v-model="validTemp.userType" @change="userTypeChange">
            <el-radio :label="0">{{ $t('pages.permanentUser') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.temporaryUser') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.validTime')" prop="rangeDate">
          <el-date-picker
            v-model="rangeDate"
            :disabled="validTemp.userType === 0"
            :clearable="true"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 400px"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="confirmUpdateValid">{{ $t('button.confirm') }}</el-button>
        <el-button @click="batchUpdateValidTimeVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <allocation-auto-user-dlg ref="allocationAutoUserDlg" @submitEnd="allocationAutoUserSubmitEnd"/>
    <validate-pass-dlg ref="validatePass" :tip="validateTip" @validated="BatchUpdateValidTime" @cancel="cancelInit"/>
  </div>
</template>

<script>
import {
  fetchList, getByAccount, getByPhone, listTerminalByUserId,
  createUser, updateUser, deleteUser, deleteAllUser, exportExcel,
  batchUpdateGroup, batchUpdateAllGroup, getOperatorUnlockVerify,
  validUserIsAutoLoginUser, getUserByIds, listUser, updateValidTime
} from '@/api/system/terminalManage/user'
import { getTerminalPage, updateAutoLoginUser, getAutoLoginTerminalListByUserIds, listTerminal } from '@/api/system/terminalManage/terminal'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { initTimestamp } from '@/utils'
import request from '@/utils/request'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import { mapGetters } from 'vuex';
import ConfigUser from './configDlg'
import AllocationAutoUserDlg from '@/views/system/terminalManage/terminal/allocationAutoUserDlg'
import moment from 'moment'
import ValidatePassDlg from '@/views/common/validatePassDlg'
import EncryptInput from '@/components/EncryptInput'

export default {
  name: 'User',
  components: { ExportDlg, ImportDlg, BatchEditPageDlg, ConfigUser, AllocationAutoUserDlg, ValidatePassDlg, EncryptInput },
  data() {
    return {
      colModel: [
        { prop: 'account', label: 'account', width: '150', fixed: true, sort: 'custom', iconFormatter: this.activeFormatter, type: 'showDetail', searchType: 'user', searchParam: 'id', labelIndent: 25 },
        { prop: 'name', label: 'userName1', sort: 'custom', width: '150' },
        { prop: 'groupIds', label: 'userGroup', width: '150', formatter: this.groupFormatter, type: 'showDetail', searchType: 'department', searchParam: 'groupIds', sort: 'custom', attributes: { selfRouteCallBack: this.selfRouteCallBack }},
        { prop: 'email', label: 'email', width: '150', sort: 'custom' },
        { prop: 'phone', label: 'phone', width: '150', sort: 'custom' },
        { prop: 'noPwdLogin', label: 'noPwdLogin', width: '180', formatter: this.noPwdLoginFormatter, hidden: !this.hasPermission('106'), sort: 'custom' },
        { prop: 'mobileShareFile', label: 'mobileShareFile', width: '180', formatter: this.mobileShareFileFormatter, hidden: !this.hasPermission('106'), sort: 'custom' },
        { prop: 'sid', label: 'sid', width: '150', formatter: this.adFormatter, hidden: !this.hasPermission('106'), sort: 'custom' },
        { prop: 'immediateSupervisorName', label: this.$t('pages.immediateSupervisor'), width: '150' },
        { prop: 'userRoleName', label: 'role', width: '150' },
        { prop: 'userType', label: 'termUserType', width: '150', formatter: this.userTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('106'),
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      selectedTerminalData: [
        { id: 'G1', label: this.$t('pages.specifiedTerminal'), type: 3, children: [] },
        { id: 'G2', label: this.$t('pages.bindTerm'), type: 3, children: [] }
      ],
      autoLoginTerminalIds: [],
      selectedButtons: [
        {
          label: this.$t('pages.specified'),
          show: (node, data) => {
            return data.id.indexOf('G' + data.dataId) < 0 && this.autoLoginTerminalIds.indexOf(data.dataId) < 0
          },
          onclick: (node, data, conponent) => { this.handleAutoLogin(node, data, conponent, this) }
        },
        {
          label: this.$t('button.delete'),
          show: (node, data) => { return ['G1', 'G2'].indexOf(data.id) < 0 },
          onclick: (node, data, conponent) => { this.handleUnbind(node, data, conponent, this) }
        }
      ],
      activeOptions: { 1: this.$t('text.enable'), 0: this.$t('text.disable') },
      query: {},            // 查询条件
      defaultQuery: {       // 默认查询条件
        page: 1,
        groupId: undefined,
        groupIds: '',
        account: '',
        name: '',
        email: '',
        phone: '',
        status: null,
        searchInfo: '',
        active: null,
        immediateSupervisor: undefined,
        userRoleIds: undefined,
        userType: 0,
        validStartDate: null,
        validEndDate: null,
        includeSubGroup: true
      },
      searchQuery: undefined,
      showTree: true,
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        account: '',
        password: '',
        confirmPassword: '',
        groupIds: [],
        mail: '',
        phone: '',
        active: '1',
        flag: 0,
        immediateSupervisor: undefined,
        roleIds: [],
        userType: 0, // 0永久 1临时
        validStartDate: null,
        validEndDate: null
      },
      userStatus: [
        { label: this.$t('pages.allUser'), value: null },
        { label: this.$t('pages.onlineUser'), value: 1 },
        { label: this.$t('pages.offlineUser'), value: 2 }
      ],
      noPwdLogin: 0,
      autoAddomainLogin: 0,
      mobileShareFile: 0,
      tempUser: {},
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.$t('pages.updateUser'),
        create: this.$t('pages.createUser')
      },
      dialogPvVisible: false,
      pvData: [],
      tip: this.$t('table.account'),
      validateTip: this.$t('pages.delete_group_dlg_text5'),
      rules: {
        account: [{ required: true, trigger: 'blur', validator: this.accountValidator }],
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        email: [{ type: 'email', message: this.$t('pages.validaEmail'), trigger: ['blur', 'change'] }],
        phone: [{ trigger: 'blur', validator: this.phoneValidator }],
        password: [{ required: false, trigger: 'blur', validator: this.passwordValidator }],
        confirmPassword: [{ required: false, trigger: 'blur', validator: this.confirmPasswordValidator }],
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'change', validator: this.groupIdValidator }],
        roleIds: [{ required: true, message: this.$t('pages.validaUserRole'), trigger: 'change', validator: this.roleIdsValidator }],
        rangeDate: [{ trigger: ['blur', 'change'], validator: this.rangeDateValid }]
      },
      rules2: {
        rangeDate: [{ trigger: ['blur', 'change'], validator: this.rangeDateValid }]
      },
      submitting: false,
      userGroupTreeData: [],
      defaultExpandedKeys: ['G0'],
      userGroupTreeSelectData: [],
      title: this.$t('route.user'),
      updateGroupForm: false,
      updateGroupId: undefined, // 批量修改的分组id
      checkedKeys: [], // 修改分组默认不勾选
      selectTreeId: '0',
      vcVisible: false,
      vcLoading: false,
      vcTitle: '',
      vcForm: {
        random: '',
        verifiCode: ''
      },
      paramsTemp: {},
      hasInputPwd: false,
      hasInputConfirmPwd: false,
      rangeDate: [],
      userTypes: [
        { label: this.$t('pages.all'), value: 0 },
        { label: this.$t('pages.permanentUser'), value: 1 },
        { label: this.$t('pages.temporaryUser'), value: 2 },
        { label: this.$t('pages.temporaryUser') + '(' + this.$t('pages.effective') + ')', value: 5 },
        { label: this.$t('pages.temporaryUser') + '(' + this.$t('pages.notActivated') + ')', value: 3 },
        { label: this.$t('pages.temporaryUser') + '(' + this.$t('pages.expired') + ')', value: 4 }
      ],
      validTemp: {
        ids: '',
        userType: 0,
        validStartDate: null,
        validEndDate: null,
        password: ''
      },
      batchUpdateValidTimeVisible: false,
      userShowModes: [{ label: this.$t('pages.immediateSupervisor'), field: 'immediateSupervisorMode', mode: 1 }]
    }
  },
  computed: {
    ...mapGetters([
      'termStatusMap',
      'userStatusMap',
      'userRoleOptions'
    ]),
    gridTable() {
      return this.$refs['userList']
    },
    groupTree() {
      return this.$refs['userGroupTree']
    },
    immediateSupervisor() {
      const { immediateSupervisor, immediateSupervisorName } = this.temp
      return immediateSupervisor ? [{ label: immediateSupervisorName, id: `U${immediateSupervisor}` }] : []
    }
  },
  watch: {
    'temp.flag'(newValue, oldValue) {
      this.autoAddomainLogin = newValue & 1
      this.noPwdLogin = newValue & 2
      this.mobileShareFile = newValue & 4
    },
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    },
    autoAddomainLogin(val) {
      this.temp.flag = this.autoAddomainLogin + this.noPwdLogin + this.mobileShareFile
    },
    noPwdLogin(val) {
      this.temp.flag = this.autoAddomainLogin + this.noPwdLogin + this.mobileShareFile
    },
    mobileShareFile(val) {
      this.temp.flag = this.autoAddomainLogin + this.noPwdLogin + this.mobileShareFile
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
    this.resetQuery()
    this.initGroupTreeNode()
    this.$nextTick(() => {
      if (this.$route.params.reQuery) {
        // 首页查询 操作员数量（在线 / 总数）
        this.initFromDataPanel()
      } else if (this.$route.query.hasOwnProperty('entityId') && this.$route.query.hasOwnProperty('entityType')) {
        const { entityId, entityType } = this.$route.query
        if (entityId && entityType) {
          // 部门跳转到操作员，查询类型为操作员组类型
          if (entityType === '4') {
            this.resetQuery()
            this.groupTree.setCurrentKey('G' + entityId)
            this.query.groupId = entityId
            // 导出当前查询时，分组查询条件为跳转的分组
            this.selectTreeId = entityId
            // 查询时不包含子部门
            this.query.includeSubGroup = false
            this.gridTable.execRowDataApi(this.query)
          }
        }
      } else if (this.$route.query.hasOwnProperty('userRoleId')) {
        const { userRoleId } = this.$route.query
        if (userRoleId !== undefined) {
          // 终端操作员角色跳转到操作员
          this.resetQuery()
          this.query.userRoleIds = [userRoleId]
          this.gridTable.execRowDataApi(this.query)
        }
      } else {
        this.handleFilter()
      }
    })
  },
  mounted() {
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.params.reQuery) {
        // 首页查询 操作员数量（在线 / 总数）
        this.initFromDataPanel()
      } else if (this.$route.query.hasOwnProperty('entityId') && this.$route.query.hasOwnProperty('entityType')) {
        const { entityId, entityType } = this.$route.query
        if (entityId && entityType) {
          // 部门跳转到操作员，查询类型为操作员组类型
          if (entityType === '4') {
            this.resetQuery()
            this.groupTree.setCurrentKey('G' + entityId)
            this.query.groupId = entityId
            // 导出当前查询时，分组查询条件为跳转的分组
            this.selectTreeId = entityId
            // 查询时不包含子部门
            this.query.includeSubGroup = false
            this.gridTable.execRowDataApi(this.query)
          }
        }
      } else if (this.$route.query.hasOwnProperty('userRoleId')) {
        const { userRoleId } = this.$route.query
        if (userRoleId !== undefined) {
          // 终端操作员角色跳转到操作员
          this.resetQuery()
          this.query.userRoleIds = [userRoleId]
          this.gridTable.execRowDataApi(this.query)
        }
      } else {
        this.handleFilter()
      }
    })
  },
  methods: {
    initFromDataPanel() {
      if (this.$route.params.reQuery) {
        this.resetQuery()
        this.query.status = this.$route.params.status || null
        this.query.groupIds = ''
        this.$refs.userGroupTree.clearSelectedNode()
        this.handleFilter()
      }
    },
    statusChange(val) {
      // if (val !== 0) {
      //   this.offLineType = 0
      //   this.query.offlineTime = null
      // }
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.searchQuery, {
          groupId: (this.query.groupIds && this.query.groupIds.split(',')[0]) || this.query.groupId,
          includeSubGroup: this.query.includeSubGroup,
          updateQuery: true
        })
        return exportExcel(q, opts)
      } else {
        const { sortName, sortOrder } = this.searchQuery
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          includeSubGroup: this.query.includeSubGroup,
          sortName,
          sortOrder,
          updateQuery: false
        }, opts)
      }
    },
    selectedNodeFilter(nodeDatas) {
      const that = this
      let resultNodeDatas = []
      nodeDatas.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0 && that.autoLoginTerminalIds.indexOf(nodeData.dataId) < 0) { // 不是终端组节点，也不是自动登录节点时，才添加到选中节点中
          resultNodeDatas.push(nodeData)
        } else if (nodeData.children && nodeData.children.length > 0) {
          const childrenNodes = that.selectedNodeFilter(nodeData.children)
          resultNodeDatas = resultNodeDatas.concat(childrenNodes)
        }
      })
      return resultNodeDatas
    },
    handleAutoLogin: (node, data, conponent, that) => {
      that.$confirmBox(this.$t('pages.specifiedUserForAutoLogin'), this.$t('text.prompt')).then(() => {
        updateAutoLoginUser({
          id: data.dataId,
          userId: that.tempUser.id
        }).then(respond => {
          conponent.removeSelectedNode(data)
          const rootNodeData = that.selectedTerminalData[0]
          rootNodeData.children.push(data)
          that.autoLoginTerminalIds.push(data.dataId)
          that.$notify({ title: this.$t('text.success'), message: this.$t('pages.specifiedSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    handleUnbind: (node, data, conponent, that) => {
      if (node.parent.data.id === 'G1') {
        that.$confirmBox(this.$t('pages.deleteUserAndUnbind'), this.$t('text.prompt')).then(() => {
          updateAutoLoginUser({
            id: data.dataId,
            userId: 0
          }).then(respond => {
            conponent.removeSelectedNode(data)
            const rootNodeData = that.selectedTerminalData[0]
            const index = that.autoLoginTerminalIds.indexOf(data.dataId)
            that.autoLoginTerminalIds.splice(index, 1)
            rootNodeData.children.splice(index, 1)
            that.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      } else {
        conponent.removeSelectedNode(data)
      }
    },
    upload(data) {
      return request.post('/user/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc(groupId) {
      // 导入时 分组未唯一分组时 点击分组节点并查询数据 实现：向上获取节点树路径，并展开父节点
      const nodeData = this.groupTree.findNode(this.userGroupTreeData, (groupId || 0) + '', 'dataId')
      let node = this.groupTree.getNode(nodeData)
      const nodePath = []
      while (node) {
        if (!node.parent || node.parent.expanded) {
          break
        } else {
          nodePath.unshift(node.parent)
          node = node.parent
        }
      }
      nodePath.forEach(n => { n.expanded = true })
      this.groupTree.setCurrentKey('G' + (groupId || '0'))
      this.$nextTick(() => {
        this.handleFilter()
      })
    },
    initGroupTreeNode: function() {
      getDeptTreeFromCache().then(respond => {
        // 分组，不允许选择公司根节点
        if (Array.isArray(respond.data) && respond.data.length === 1 && respond.data[0].dataId == 0) {
          const nodes = respond.data[0].children.filter(data => data.dataId != '-2')
          respond.data[0].children = nodes
          this.userGroupTreeData = respond.data
          this.userGroupTreeSelectData = nodes
        } else {
          const nodes = respond.data.filter(data => data.dataId != '-2')
          this.userGroupTreeData = nodes
          this.userGroupTreeSelectData = nodes
        }
      })
    },
    rowDataApi: function(option) {
      this.searchQuery = Object.assign({}, this.query, option)
      return fetchList(this.searchQuery)
    },
    selectTerminalRowDataApi: function(option) { // 可选终端
      return getTerminalPage(option)
    },
    selectedTerminalRowDataApi: function(option) { // 已选终端
      return listTerminalByUserId({ userId: this.tempUser.id })
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    groupIdSelectChange: function(data) {
      if (this.temp.groupIds[0] != data) {
        this.temp.groupIds.splice(0, 1, data)
      }
    },
    resetQuery() {
      this.query = Object.assign({}, this.defaultQuery)
    },
    handleRefresh() {
      const stTree = this.$refs.userGroupTree
      if (stTree) {
        // 清除选中节点
        stTree.clearSelectedNode()
        this.nodeChange()
      } else {
        console.error('树组件不存在！');
        this.gridTable.execRowDataApi(this.query)
      }
    },
    handleFilter() {
      this.query.page = 1
      this.query.validStartDate = null
      this.query.validEndDate = null
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.rangeDate = []
    },
    // 获取所有子部门
    getAllChildrenGroupId(groups, groupIds) {
      groups.forEach(group => {
        if (group.id.indexOf('G') > -1) {
          groupIds.push(group.dataId)
          if (group.children && group.children.length > 0) {
            this.getAllChildrenGroupId(group.children, groupIds)
          }
        }
      })
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    // 树节点变化的回调函数
    nodeChange(data, node) {
      const groupIds = []
      if (!data) {
        this.selectTreeId = '0'
      } else {
        if (data.id.indexOf('G') > -1) {
          groupIds.push(data.dataId)
          this.selectTreeId = data.dataId
        }
        if (data.children && data.children.length > 0) {
          this.getAllChildrenGroupId(data.children, groupIds)
        }
        node.expanded = true
      }
      // this.resetQuery()
      this.query.groupId = undefined
      this.query.groupIds = ''
      this.query.groupIds = groupIds.join(',')
      this.gridTable.execRowDataApi()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleCreate() {
      this.resetTemp()
      // 创建时增加默认角色(201 普通员工)
      this.temp.roleIds = ['201']
      if (this.query.groupIds) {
        // 左侧树节点选中时，会将其子节点拼接在一起，所以此处取第一个id
        const id = this.query.groupIds.split(',')[0]
        // 根节点id为0，不允许选中，所以此处删掉
        this.temp.groupIds = id != 0 ? [id] : []
      } else {
        this.temp.groupIds = []
      }
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.hasInputPwd = false
      this.hasInputConfirmPwd = false
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row))) // copy obj
      if (row.hasPassword == 1) {
        this.temp.password = '      '
        this.temp.confirmPassword = '      '
      }
      if (this.temp.validStartDate != null || this.temp.validEndDate != null) {
        this.temp.userType = 1
        this.rangeDate = [moment(new Date(this.temp.validStartDate)).format('YYYY-MM-DD HH:mm:ss'), moment(new Date(this.temp.validEndDate)).format('YYYY-MM-DD HH:mm:ss')]
      } else {
        this.temp.userType = 0
        this.rangeDate = []
      }
      this.temp.active = this.temp.active.toString()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.hasInputPwd = false
      this.hasInputConfirmPwd = false
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.validStartDate = this.temp.userType === 1 ? this.rangeDate[0] : null
          this.temp.validEndDate = this.temp.userType === 1 ? this.rangeDate[1] : null
          const tempData = JSON.parse(JSON.stringify(this.temp))
          !tempData['encryptProps'] && (tempData['encryptProps'] = ['password'])
          !tempData['noSubmitProps'] && (tempData['noSubmitProps'] = ['confirmPassword'])
          createUser(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          this.submitting = true
          this.temp.validStartDate = this.temp.userType === 1 ? this.rangeDate[0] : null
          this.temp.validEndDate = this.temp.userType === 1 ? this.rangeDate[1] : null
          const tempData = JSON.parse(JSON.stringify(this.temp))
          if (tempData.active == 0) {
            const result = await validUserIsAutoLoginUser(tempData.id);
            if (result.data) {
              this.submitting = false
              this.$message({
                message: this.$t('pages.userBindUnAbleMessage'),
                type: 'error',
                duration: 2000
              })
              return;
            }
          }
          !tempData['encryptProps'] && (tempData['encryptProps'] = ['password'])
          !tempData['noSubmitProps'] && (tempData['noSubmitProps'] = ['confirmPassword'])
          updateUser(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    },
    handleDelete() {
      this.updateGroupForm = false
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    handleUpdateValid() {
      this.batchUpdateValidTimeVisible = true
      this.rangeDate = []
      this.validTemp.userType = 0
    },
    confirmUpdateValid() {
      this.$refs['validTimeForm'].validate(valid => {
        if (valid) {
          this.$refs['validatePass'].show()
          this.batchUpdateValidTimeVisible = false
        }
      })
    },
    cancelInit() {
      this.batchUpdateValidTimeVisible = true
    },
    BatchUpdateValidTime() {
      this.validTemp.ids = this.gridTable.getSelectedIds().join(',')
      if (this.validTemp.userType === 1) {
        this.validTemp.validStartDate = this.rangeDate[0]
        this.validTemp.validEndDate = this.rangeDate[1]
      } else {
        this.validTemp.validStartDate = null
        this.validTemp.validEndDate = null
      }
      this.submitting = true
      updateValidTime(this.validTemp).then(resp => {
        this.submitting = false
        if (!resp.data) {
          this.$message({
            message: this.$t('pages.wrongPassword'),
            type: 'error'
          })
          return
        }
        this.$message({
          message: this.$t('text.operateSuccess'),
          type: 'success'
        })
        this.gridTable.execRowDataApi(this.query)
        this.batchUpdateValidTimeVisible = false
        this.password = ''
      }).catch(() => {
        this.submitting = false
      })
    },
    batchModifyGroup() {
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.updateAllUserGroup(params, callback)
      } else {
        this.deleteAllUser(params, callback)
      }
    },
    async deleteAllUser(params, callback) {
      this.paramsTemp = params;
      //  不被删除的用户id
      const backupUnSelectedIds = params.backupUnSelectedIds || []
      let terms = []
      //  获取需要被删除的用户id
      const needDeleteUserIds = []
      if (params.ids) {
        const userIds = params.ids.split(',') || []
        const result = await getAutoLoginTerminalListByUserIds(userIds);
        terms = result.data || []
      } else {
        const searchQuery = Object.assign({}, this.query)
        searchQuery.page = null
        searchQuery.limit = null
        const userResult = await listUser(searchQuery);
        (userResult.data || []).forEach(item => {
          if (!backupUnSelectedIds.includes(item.id)) {
            needDeleteUserIds.push(item.id)
          }
        })
        if (needDeleteUserIds.length > 0) {
          const result = await listTerminal({ loginMode: 1, autoLoginUserIds: needDeleteUserIds.join(',') })
          terms = result.data || []
        }
      }
      if (terms.length === 0) {
        // 未选中任何终端的提示信息
        this.$confirmBox(this.$t('pages.confirmDeleteCheckedDataMsg', { info: this.$t('pages.user') }), this.$t('text.prompt')).then(async() => {
          //  查询被删除的操作员名称
          const result = await this.deleteFunc()
          result.then(respond => {
            callback(respond)
          }).catch(e => {
            callback(e)
          })
        }).catch(e => {
          callback(e)
        })
      } else {
        let userIds = []
        if (params.ids) {
          userIds = params.ids.split(',') || []
        } else {
          userIds = needDeleteUserIds
        }
        const result = await getUserByIds(userIds) || []
        this.$refs.allocationAutoUserDlg.show(terms, result.data || []);
      }
    },
    deleteFunc() {
      const params = this.paramsTemp;
      const deleteFunc = params.ids ? deleteUser : deleteAllUser
      return deleteFunc(params).then(respond => {
        this.$refs.batchDeleteDlg.submitCallback()
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.deleteSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(e => {
      })
    },
    updateAllUserGroup(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    accountValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterAccount')))
      } else {
        getByAccount({ account: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('pages.validateMsg_sameAccount')))
          } else {
            callback()
          }
        })
      }
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterUserName')))
      } else {
        callback()
      }
    },
    phoneValidator(rule, value, callback) {
      if (value) {
        if (!(/^1[3456789]\d{9}$/.test(value))) {
          callback(new Error(this.$t('pages.validateMsg_phone')))
        } else {
          let flag = false
          getByPhone({ phone: value }).then(respond => {
            const userList = respond.data
            userList.forEach(user => {
              if (user && user.id !== this.temp.id) {
                flag = true
              }
            })
            if (flag) {
              callback(new Error(this.$t('pages.phoneAlreadyExistsMsg')))
            } else {
              callback()
            }
          })
        }
      } else {
        callback()
      }
    },
    passwordValidator(rule, value, callback) {
      let error
      if (!value) {
        this.rules.confirmPassword[0].required = false
        this.$refs['dataForm'].clearValidate(['confirmPassword'])
        if (rule.required) {
          error = new Error(this.$t('pages.validateMsg_password'))
        }
        if (this.noPwdLogin) {
          error = new Error(this.$t('pages.validateMsg_notEmptyPwd'))
        }
      } else {
        this.rules.confirmPassword[0].required = true
      }
      callback(error)
    },
    confirmPasswordValidator(rule, value, callback) {
      if (rule.required && !value) {
        callback(new Error(this.$t('pages.validateMsg_password')))
      } else if (rule.required && value !== this.temp.password) {
        callback(new Error(this.$t('pages.validateMsg_diffPassword')))
      } else {
        callback()
      }
    },
    groupIdValidator(rule, value, callback) {
      let tempIds = null
      if (this.temp.groupIds) {
        tempIds = this.temp.groupIds.filter(t => {
          return t !== null && t !== undefined && t !== ''
        })
      }
      if (tempIds && tempIds.length > 0) {
        callback()
      } else {
        callback(new Error(this.$t('pages.selectUserGroup')))
      }
    },
    roleIdsValidator(rule, value, callback) {
      if (this.temp.roleIds && this.temp.roleIds.length > 0) {
        callback()
      } else {
        callback(new Error(this.$t('pages.validaUserRole')))
      }
    },
    noPwdLoginFormatter: function(row, data) {
      const noPwdLoginOptions = { 0: this.$t('pages.allow'), 2: this.$t('pages.forbid') }
      return noPwdLoginOptions[row.flag & 2]
    },
    mobileShareFileFormatter: function(row, data) {
      const options = { 4: this.$t('pages.allow'), 0: this.$t('pages.forbid') }
      return options[row.flag & 4]
    },
    activeFormatter: function(row, data) {
      const obj = {
        class: 'offline',
        title: this.activeOptions[row.active],
        style: 'color: #888'
      }
      if (row.active) {
        obj.class = 'user'
        const status = this.termStatusMap[this.userStatusMap[row.id]]
        const online = !!status && status.status == 3
        if (online) {
          obj.title = this.$t('pages.online')
          obj.style = 'color: green'
        } else {
          obj.title = this.$t('pages.offline')
        }
      }
      return obj
    },
    adFormatter: function(row, data) {
      // source = 2的是AD域 {1:本地创建 2:域服务 3:IDM(建发) 4:企业微信 5:钉钉 7:LDAP}
      return row.source === 2 && row.sid ? this.$t('text.yes') : this.$t('text.no')
    },
    groupFormatter: function(row, data) {
      const groupNames = []
      if (data) {
        const that = this
        data.forEach(function(groupId) {
          const node = that.groupTree.findNode(that.userGroupTreeData, groupId, 'dataId')
          // 建发版本：label是普通文本，使用formatter需要对label进行转义，使用 that.html2Escape(node.label)
          // Q3版本可以查看详情，使用的是btn，不需要转义，使用 node.label
          if (node) groupNames.push(node.label)
        })
      }
      return groupNames.join(',')
    },
    userTypeFormatter(row, data) {
      if (row.validStartDate != null || row.validEndDate != null) {
        const now = new Date()
        if (now < new Date(row.validStartDate)) {
          return this.$t('pages.temporaryUser') + '(' + this.$t('pages.notActivated') + ')'
        } else if (now >= new Date(row.validEndDate)) {
          return this.$t('pages.temporaryUser') + '(' + this.$t('pages.expired') + ')'
        } else {
          return this.$t('pages.temporaryUser') + '(' + this.$t('pages.effective') + ')'
        }
      } else {
        return this.userTypes[1].label
      }
    },
    handleMoreClick(type) {
      switch (type) {
        case 1:
          this.batchModifyGroup()
          break
        default:
          break
      }
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    handleSetting() {
      this.$refs['configDlg'].show()
    },
    getOperatorUnlockVerify() {
      this.vcLoading = true
      const data = {
        random: this.vcForm.random
      }
      getOperatorUnlockVerify(data).then(res => {
        this.vcForm.verifiCode = res.data
        this.vcLoading = false
      }).catch(() => {
        this.vcLoading = false
      })
    },
    handleLockScreenVerify() {
      this.vcForm = {
        random: '',
        verifiCode: ''
      }
      this.vcTitle = this.$t('pages.userVerificationCode')
      this.vcVisible = true
    },
    allocationAutoUserSubmitEnd(type) {
      if (type === 'confirm') {
        this.deleteFunc()
      } else {
        this.$refs.batchDeleteDlg.submitCallback('cancel')
      }
    },
    selfRouteCallBack: function(applyObject) {
      this.$nextTick(() => {
        const { entityId, entityType } = applyObject
        if (entityId && entityType) {
          // 查询类型为操作员组类型
          if (entityType === '4') {
            this.groupTree.setCurrentKey('G' + entityId)
            this.resetQuery()
            this.query.groupId = entityId
            // 导出当前查询时，分组查询条件为跳转的分组
            this.selectTreeId = entityId
            // 查询时不包含子部门
            this.query.includeSubGroup = false
            this.gridTable.execRowDataApi(this.query)
          }
        }
      })
    },
    updatePage(page) {
      this.query.page = page
    },
    afterLoad(rowData) {
      // 根据路由查询条件entityId、entityType查询之后，清除查询
      // if (this.$route.query.entityId && this.$route.query.entityType) {
      //   this.$route.query.entityId = undefined
      //   this.$route.query.entityType = undefined
      // }
      // if (this.$route.query.userRoleId) {
      //   this.$route.query.userRoleId = undefined
      // }
      this.$router.push({ query: {}});
    },
    inputPwd($event) {
      if (!this.hasInputPwd) {
        $event.target.value = $event.data || ''
      }
      this.hasInputPwd = true
      this.temp.password = $event.target.value
    },
    inputConfirmPwd($event) {
      if (!this.hasInputConfirmPwd) {
        $event.target.value = $event.data || ''
      }
      this.hasInputConfirmPwd = true
      this.temp.confirmPassword = $event.target.value
    },
    userTreeNodeCheckChange(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.temp.immediateSupervisor = data.dataId
      } else {
        return false
      }
    },
    userRoleNodeSelectChange(selectKey, data) {
      if (!selectKey || selectKey.length === 0) {
        this.temp.roleIds = []
      } else if (data && data.length > 0) {
        this.temp.roleIds = selectKey
      }
    },
    userIdSelectChange(data) {
      if (data === '') {
        this.temp.immediateSupervisor = undefined
        return;
      }
      if (data instanceof Object) {
        this.temp.immediateSupervisor = data.dataId
      }
    },
    userTreeNodeCheckChangeQuery(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.query.immediateSupervisor = data.dataId
      } else {
        return false
      }
    },
    userIdSelectChangeQuery(data) {
      if (data === '') {
        this.query.immediateSupervisor = undefined
        return;
      }
      if (data instanceof Object) {
        this.query.immediateSupervisor = data.dataId
      }
    },
    userRoleNodeSelectChangeQuery(selectKey, data) {
      if (!selectKey || selectKey.length === 0) {
        this.query.userRoleIds = []
      } else if (data && data.length > 0) {
        this.query.userRoleIds = selectKey
      }
    },
    rangeDateValid(rule, value, callback) {
      if (this.batchUpdateValidTimeVisible) {
        if (this.validTemp.userType === 1 && (this.rangeDate == null || this.rangeDate.length === 0)) {
          callback(new Error(this.$t('pages.validTimeEmpty')))
        }
      } else {
        if (this.temp.userType === 1 && (this.rangeDate == null || this.rangeDate.length === 0)) {
          callback(new Error(this.$t('pages.validTimeEmpty')))
        }
      }
      callback()
    },
    userTypeChange() {
      if (this.batchUpdateValidTimeVisible) {
        this.$refs['validTimeForm'].clearValidate(['rangeDate'])
      } else {
        this.$refs['dataForm'].clearValidate(['rangeDate'])
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .error-msg {
    line-height: 19px;
    padding-left: 5px;
    font-size: 13px;
    color: #F56C6C;
  }
</style>
