<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.termType" :value="query.termType">
          <span>{{ $t('pages.terminalType') }}：</span>
          <el-select v-model="query.termType" clearable is-filter :placeholder="$t('pages.all')" style="width: 180px;">
            <el-option :label="$t('pages.all')" :value="null"></el-option>
            <el-option :label="$t('pages.dictionary_Msg5')" :value="0"></el-option>
            <el-option v-if="!isTrialDlp()" :label="$t('pages.dictionary_Msg6')" :value="128"></el-option>
            <el-option :label="$t('pages.WinBoss')" :value="16"></el-option>
            <el-option :label="$t('pages.winUsb')" :value="32"></el-option>
            <el-option :label="$t('pages.dictionary_Msg7')" :value="1"></el-option>
            <el-option :label="$t('pages.dictionary_Msg8')" :value="129"></el-option>
            <!--<el-option label="LINUX老板终端" :value="17"></el-option>-->
            <el-option :label="$t('pages.dictionary_Msg9')" :value="2"></el-option>
            <el-option :label="$t('pages.dictionary_Msg10')" :value="130"></el-option>
            <!--<el-option label="MAC老板终端" :value="18"></el-option>-->
            <el-option v-permission="'115'" :label="$t('pages.dictionary_Msg11')" :value="3"></el-option>
            <!--<el-option label="移动老板终端" :value="19"></el-option>-->
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.accessCode" :value="query.accessCode">
          <span>{{ $t('table.accessMode') }}：</span>
          <el-select v-model="query.accessCode" clearable is-filter :placeholder="$t('pages.all')" style="width: 160px;">
            <el-option v-for="(val, key) in accessCodeOptions" :key="key" :label="val" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <!-- <SearchItem model-key="query.searchInfo" :value="query.searchInfo">
          <span>{{ $t('pages.terminalName') }}：</span>
          <el-input v-model="query.searchInfo" v-trim clearable style="width: 180px;" />
        </SearchItem> -->
        <SearchItem model-key="query.ip" :value="query.ip">
          <span>{{ $t('pages.ipAddr') }}：</span>
          <el-input v-model="query.ip" v-trim clearable style="width: 180px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'306'" :request="exportFunc"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'414'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button slot="append" v-permission="'369'" icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleClick">
          {{ $t('pages.editTerminalPacket') }}
        </el-button>
      </SearchToolbar>
      <grid-table ref="logList" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :row-data-api="rowDataApi" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <batch-modify-group-dlg ref="batchModifyGroupDlg" :group-tree-data="groupTreeSelectData" :selected-data="selectedData" @submitEnd="submitEnd" />
    <video-viewer ref="videoViewer"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.accessLogDetails')"
      :visible.sync="dialogFormVisible"
      width="900px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item v-for="item in panelModel" :key="rowDetail[item.value]" :label="$t(item.label)" >
            <terminal-detail v-if=" 'terminal' === item.type" :label="rowDetail[item.value]" :search-id="rowDetail[item.param]"/>
            <user-detail
              v-else-if=" 'user' === item.type && 'user.name' === item.value"
              :label="rowDetail.user && rowDetail.user.name ? rowDetail.user.name : $t('pages.null')"
              :search-id="rowDetail.user && rowDetail.user.id ? rowDetail.user.id : null"
            />
            <department-detail
              v-else-if=" 'department' === item.type && 'group.name' === item.value"
              :label="rowDetail.group && rowDetail.group.name ? rowDetail.group.name : $t('pages.null')"
              :search-id="rowDetail.group && rowDetail.group.name ? rowDetail.groupId : null"
            />
            <department-detail
              v-else-if=" 'department' === item.type && 'currentGroup.name' === item.value"
              :label="rowDetail.currentGroup && rowDetail.currentGroup.name ? rowDetail.currentGroup.name : $t('pages.null')"
              :search-id="rowDetail.currentGroup && rowDetail.currentGroup.name ? rowDetail.currentGroup.id : null"
            />
            <span v-else-if="'deleted' === item.value">
              {{ rowDetail.deleted ? $t('pages.software_Msg28') : rowDetail.cleaned ? $t('pages.cleaned') : $t('pages.using') }}
            </span>
            <span v-else-if="'termType' === item.value">
              {{ termTypeFormatter(rowDetail, rowDetail.termType) }}
            </span>
            <span v-else-if="'accessCode' === item.value">
              {{ accessCodeFormatter(rowDetail, rowDetail.accessCode) }}
            </span>
            <span v-else>
              {{ rowDetail[item.value] }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAccessLogPage, exportTermAccessLog, deleteLog } from '@/api/system/terminalManage/accessLog'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import BatchModifyGroupDlg from '@/views/system/terminalManage/terminal/batchModifyGroupDlg'
import DepartmentDetail from '@/components/ShowDetail/DepartmentDetail'
import { getTermTypeDict } from '@/utils/dictionary'
import { getModuleInfoByProdId } from '@/api/system/terminalManage/moduleConfig'
import { listTerminal } from '@/api/system/terminalManage/terminal'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'AccessLog',
  components: { BatchModifyGroupDlg, DepartmentDetail },
  data() {
    return {
      colModel: [
        { prop: 'termName', label: 'terminalName', width: '200', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'termId', iconFormatter: this.isActive, labelIndent: 25, attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'createTime', label: 'time', width: '150', sort: true },
        { prop: 'user.name', label: 'automaticLoginOperator', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'group.name', label: 'accessGroup', width: '150', type: 'showDetail', searchParam: 'groupId', searchType: 'department' },
        { prop: 'currentGroup.name', label: 'currentGroup', width: '150', formatter: this.currentGroupNameFormatter, type: 'showDetail', searchParam: 'currentGroup.id', searchType: 'department' },
        { prop: 'termType', label: 'terminalType', width: '150', formatter: this.termTypeFormatter },
        { prop: 'mainIp', label: 'ip', width: '150' },
        { prop: 'mainMac', label: 'mac', width: '150' },
        { prop: 'accessCode', label: 'accessMode', width: '150', formatter: this.accessCodeFormatter },
        { prop: 'remark', label: 'recoverFailContent', width: '150', formatter: this.recoverFailFormatter },
        { prop: 'version', label: 'versionNumber', width: '150' },
        { prop: 'mcode', label: 'machineNumber', width: '150' },
        { prop: 'termId', label: 'terminalCode', width: '150' },
        { prop: 'guid', label: 'terminalGuid', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('260'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        ip: '',
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        termType: undefined,
        accessCode: undefined,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      termTypeOptions: Object.fromEntries(getTermTypeDict().map(item => [item.value, item.label])),
      accessCodeOptions: { 1: this.$t('pages.accessLog_Msg1'), 2: this.$t('pages.accessLog_Msg2'), 3: this.$t('pages.accessLog_Msg3'), 11: this.$t('pages.accessLog_Msg4'), 12: this.$t('pages.accessLog_Msg5'),
        13: this.$t('pages.accessLog_Msg6'), 14: this.$t('pages.accessLog_Msg7'), 21: this.$t('pages.accessLog_Msg8'), 22: this.$t('pages.accessLog_Msg9') },
      showTree: true,
      deleteable: false,
      selectedData: [],
      groupTreeSelectData: [],
      rowDetail: {},
      moduleNameMap: {},
      dialogFormVisible: false,
      panelModel: [
        { value: 'deleted', label: 'table.usedStatus' },
        { value: 'createTime', label: 'table.time' },
        { value: 'termName', label: 'pages.terminalName', type: 'terminal', param: 'termId' },
        { value: 'user.name', label: 'pages.autoLoginUser', type: 'user', param: 'user.id' },
        { value: 'group.name', label: 'pages.accessGroup', type: 'department', param: 'accessGroup.id' },
        { value: 'currentGroup.name', label: 'table.currentGroup', type: 'department', param: 'currentGroup.id' },
        { value: 'termType', label: 'pages.terminalType' },
        { value: 'mainIp', label: 'table.ip' },
        { value: 'mainMac', label: 'table.mac' },
        { value: 'accessCode', label: 'table.accessMode' },
        { value: 'version', label: 'table.versionNumber' },
        { value: 'termId', label: 'table.terminalCode' },
        { value: 'mcode', label: 'table.machineNumber' },
        { value: 'guid', label: 'table.terminalGuid' }
      ],
      queryVideoMethod: undefined,
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    this.listModule()
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    async listModule() {
      const productId = 511
      await getModuleInfoByProdId(productId).then(respond => {
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(moduleInfo => {
            this.moduleNameMap[moduleInfo.moduleId] = moduleInfo.moduleName
          })
        }
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    submitEnd() {
      this.gridTable.execRowDataApi()
    },
    initGroupTreeNode() {
      getDeptTreeFromCache().then(respond => {
        if (respond.data[0] && respond.data[0].dataId == 0) {
          this.groupTreeSelectData = respond.data[0].children
        } else {
          this.groupTreeSelectData = respond.data
        }
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleClick() {
      let deleteable = true
      const datas = this.gridTable.getSelectedDatas().filter(data => {
        return data.cleaned
      })
      if (datas.length > 0) {
        this.$message({
          message: this.$t('pages.accessLog_Msg10') + datas.map(data => data.termName).join(','),
          type: 'error',
          duration: 2000
        })
        return
      }
      const selectedDatas = this.gridTable.getSelectedDatas()
      selectedDatas.forEach(data => {
        if (data.deleted) {
          deleteable = false
        }
      })
      if (deleteable) {
        this.initGroupTreeNode()
        this.$refs.batchModifyGroupDlg.show()
      } else {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('layout.noUpdateGroupForTermMsg'),
          type: 'warning',
          duration: 2000
        })
      }
    },
    selectionChangeEnd: function(rowDatas) {
      this.selectedData = []
      this.deleteable = rowDatas && rowDatas.length > 0
      rowDatas.forEach(data => {
        const rowBean = {
          id: data.termId
        }
        this.selectedData.push(rowBean)
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getAccessLogPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    isActive(row) {
      return row.deleted ? { class: 'forbidden', title: this.$t('pages.software_Msg28') } // 已卸载
        : row.cleaned ? { class: 'using', title: this.$t('pages.cleaned'), style: 'color: gray;' } // 已清理
          : { class: 'using', title: this.$t('pages.using'), style: 'color: #1CA048;' } // 使用中
    },
    currentGroupNameFormatter(row, data) {
      let groupName = ''
      // if (row.cleaned) {
      //   groupName = this.$t('pages.offline_terminal_text15')
      // } else if (row.currentGroup) {
      //   groupName = row.currentGroup.name
      // }
      if (row.currentGroup) {
        groupName = row.currentGroup.name
      }
      return groupName
    },
    termTypeFormatter: function(row, data) {
      let typeName = this.termTypeOptions[data]
      if (!typeName) {
        typeName = this.$t('pages.accessLog_Msg')
      }
      return typeName
    },
    accessCodeFormatter: function(row, data) {
      return this.accessCodeOptions[data]
    },
    recoverFailFormatter(row, data) {
      let msg = ''
      if (data) {
        const moduleName = []
        msg += this.$t('pages.accessLog_Msg11')
        data.split(',').forEach(moduleId => {
          moduleName.push(this.moduleNameMap[moduleId])
        })
        msg += moduleName.join(',')
      }
      return msg
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportTermAccessLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    afterLoad(rowData) {
      this.termsInfo = []
      const termNameMap = new Map()
      const termIds = rowData.map(data => {
        termNameMap.set(data.termId, data.termName)
        return data.termId
      }).join()

      if (termIds) {
        listTerminal({ ids: termIds }).then(res => {
          if (res.data) {
            res.data.forEach(item => {
              const { id, groupIds } = item
              const name = termNameMap.get(id)
              const termsInfo = { id, name, groupIds }
              this.termsInfo.push(termsInfo)
            })
          }
        })
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '260')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-descriptions-item__label {
    min-width: 150px;
  }
  >>>.el-descriptions-item__content {
    min-width: 100px;
    max-width: 180px;
  }
</style>
