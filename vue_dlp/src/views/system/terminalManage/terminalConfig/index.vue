<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="deleteData">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode" :scope="1"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="WinTitleTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @close="close"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
        class="module-form"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
          @entityTypeData="entityTypeData"
        />
        <FormItem :label="$t('components.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-tabs ref="tabs" v-model="activeName" class="inner-tabs" type="card">
          <el-tab-pane v-if="baseConfig.enable" :label="baseConfig.label " :name="baseConfig.name">
            <div style="line-height: 22px;">
              <el-checkbox-group v-model="baseConfig.checked">
                <el-row>
                  <el-col v-for="(item, i) in baseConfig.options" :key="i" :span="24">
                    <el-checkbox v-if="(!item.format && !item.isHide)" :label="item.id" :disabled="!formable">
                      {{ item.label }}
                      <span v-if="item.id === 4073">
                        <el-tooltip effect="dark" placement="bottom-start">
                          <div slot="content">
                            <i18n path="pages.backupFileAlarmReminderLoginTip">
                              <br slot="br"/>
                            </i18n>
                          </div>
                          <i class="el-icon-info"/>
                        </el-tooltip>
                      </span>
                      <span v-if="item.id === 4075">
                        <el-tooltip effect="dark" placement="bottom-start">
                          <div slot="content">
                            <span>{{ $t('pages.backupFileAlarmAutoCloseProcessTip') }}</span>
                          </div>
                          <i class="el-icon-info"/>
                        </el-tooltip>
                      </span>
                    </el-checkbox>
                    <div v-if="item.format === 6 && !(query.objectType == 2 || query.objectType == 4 || isHide)">
                      <el-checkbox :label="4017" :disabled="!formable" @change="handleNotLongCheckBox">
                        {{ $t('pages.backupFileAlarmLoginWindowPosition') }}
                      </el-checkbox>
                      <div style="margin-top: 2px; padding-left: 25px;">
                        <el-checkbox :label="4072" :disabled="!formable || !(baseConfig.checked && baseConfig.checked.includes(4017))">
                          {{ $t('pages.backupFileAlarmForbidLoginWindow') }}
                        </el-checkbox>
                      </div>
                    </div>
                    <el-checkbox v-if="item.format === 1" :label="item.id" :disabled="!formable">
                      <i18n path="pages.terminalConfig1">
                        <el-date-picker slot="date" v-model="item.time" type="date" value-format="yyyy-MM-dd" :disabled="!formable" :placeholder="$t('pages.chooseDate')" style="width:140px;"></el-date-picker>
                      </i18n>
                    </el-checkbox>
                    <el-checkbox v-if="item.format === 2" :label="item.id" :disabled="!formable">
                      <i18n path="pages.terminalConfig2">
                        <el-input slot="minutes" v-model="item.minute" :disabled="!formable" maxlength="5" style="width:75px;margin: 5px;"></el-input>
                        <el-select slot="status" v-model="item.status" :disabled="!formable" size="mini" style="width: 110px;">
                          <el-option :value="4" :label="$t('pages.lockScreen')"></el-option>
                          <el-option :value="5" :label="$t('pages.dormancy')"></el-option>
                          <el-option :value="6" :label="$t('pages.turnOffTheDisplay')"></el-option>
                        </el-select>
                      </i18n>
                    </el-checkbox>
                    <div v-if="item.format === 3">
                      <el-checkbox :label="item.id" :disabled="!formable">
                        <i18n path="pages.terminalConfig3">
                          <el-input-number
                            slot="seconds"
                            v-model="item.second"
                            :disabled="!formable"
                            :controls="false"
                            :min="30"
                            :max="3600"
                            style="width:80px;margin: 5px;"
                          ></el-input-number>
                        </i18n>
                      </el-checkbox>
                      <p style="color: #0033CC; font-size: 13px;">
                        {{ $t('pages.terminalConfigMsg_tips') }}
                      </p>
                    </div>
                    <el-checkbox v-if="item.format === 4" :label="item.id" :disabled="!formable">
                      <i18n path="pages.terminalConfig4">
                        <el-time-picker
                          slot="time"
                          v-model="item.time"
                          :picker-options="{ selectableRange: '00:00:00 - 23:59:59', format: 'HH:mm' }"
                          value-format="HH:mm"
                          :disabled="!formable"
                          :placeholder="$t('pages.time')"
                          style="width: 99px;"
                        ></el-time-picker>
                        <el-select slot="action" v-model="item.type" :disabled="!formable" size="mini" style="width: 80px;">
                          <el-option :value="0" :label="$t('pages.shutDown')"></el-option>
                          <el-option :value="1" :label="$t('pages.restart')"></el-option>
                        </el-select>
                        <el-input slot="minutes" v-model="item.minute" :disabled="!formable" maxlength="2" style="width:75px;margin: 5px;"></el-input>
                      </i18n>
                    </el-checkbox>
                    <div v-if="item.format === 4">
                      <i18n path="pages.timingFrequency" style="font-size: 13px; margin-left: 24px;">
                        <el-select slot="days" ref="rateSelect" v-model="item.rate" multiple :disabled="!formable" size="mini" style="width: 200px;" @change="rateChange">
                          <el-option :value="0" :label="$t('pages.everyDay')"></el-option>
                          <el-option :value="1" :label="$t('pages.sunday')"></el-option>
                          <el-option :value="2" :label="$t('pages.monday')"></el-option>
                          <el-option :value="4" :label="$t('pages.tuesday')"></el-option>
                          <el-option :value="8" :label="$t('pages.wednesday')"></el-option>
                          <el-option :value="16" :label="$t('pages.Thursday')"></el-option>
                          <el-option :value="32" :label="$t('pages.friday')"></el-option>
                          <el-option :value="64" :label="$t('pages.saturday')"></el-option>
                        </el-select>
                      </i18n>
                    </div>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </el-tab-pane>
          <el-tab-pane v-if="otherConfig.enable" :label="$t('pages.backupFileConfig')" name="backupConfig">
            <el-row>
              <el-col :span="24">
                <el-radio-group v-model="backupConfig.backupType" :disabled="!formable">
                  <el-radio :label="1" >
                    <i18n path="pages.backupFileAlarmConfig1">
                      <el-input-number
                        v-if="backupConfig.backupType==1"
                        slot="size"
                        v-model="backupConfig.diskFreeSpace"
                        :step="1"
                        :step-strictly="true"
                        :disabled="!formable || backupConfig.backupType!=1"
                        :controls="false"
                        :min="2"
                        :max="8192"
                        style="width: 70px;"
                        @blur="diskFreeSpaceBlur"
                      ></el-input-number>
                      <el-input-number v-if="backupConfig.backupType==2" slot="size" :disabled="backupConfig.backupType!=1" :controls="false" :min="2" :max="8192" style="width: 70px;"></el-input-number>
                    </i18n>
                    <el-tooltip effect="dark" content="" placement="bottom-start">
                      <div slot="content">
                        {{ this.$t('pages.backupFileAlarmMsg1', {size: backupConfig.diskFreeSpace }) }}
                      </div>
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </el-radio>
                  <br>
                  <el-radio :label="2" >
                    <i18n path="pages.backupFileAlarmConfig2">
                      <el-input-number v-if="backupConfig.backupType==1" slot="size" :disabled="backupConfig.backupType!=2" :controls="false" :min="2" :max="8192" style="width: 70px;"></el-input-number>
                      <el-input-number
                        v-if="backupConfig.backupType==2"
                        slot="size"
                        v-model="backupConfig.diskFreeSpace"
                        :step="1"
                        :step-strictly="true"
                        :disabled="!formable || backupConfig.backupType!=2"
                        :controls="false"
                        :min="2"
                        :max="8192"
                        style="width: 70px;"
                        @blur="diskFreeSpaceBlur"
                      ></el-input-number>
                    </i18n>
                    <el-tooltip effect="dark" content="" placement="bottom-start">
                      <div slot="content">
                        {{ this.$t('pages.backupFileAlarmMsg2', {size: backupConfig.diskFreeSpace }) }}
                      </div>
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </el-col>
              <el-col v-for="(item, i) in baseConfig.options" :key="i" :span="24">
                <el-checkbox
                  v-if="item.format === 5"
                  v-model="checkRetention"
                  :label="item.id"
                  :disabled="!formable"
                  @change="retentionDataCheck(arguments[0], item.id)"
                >
                  <i18n path="pages.terminalConfig5">
                    <el-input-number slot="days" v-model="item.day" :disabled="!formable" :controls="false" :min="1" :max="100" style="width:80px;margin: 5px;"/>
                  </i18n>
                </el-checkbox>
              </el-col>
            </el-row>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem :label="$t('pages.require_Msg4')" label-width="235px" prop="alarmFrequency">
              <el-input v-model="backupConfig.alarmFrequency" type="Number" maxlength="4" min="0" :disabled="!formable" style="width: 120px;" @input="numberLimit(arguments[0], 'alarmFrequency', 1, 1440)" ></el-input>
              <span>{{ $t('pages.require_Msg5') }}</span>
            </FormItem>
            <FormItem :label="$t('pages.require_Msg6')" label-width="235px" prop="alarmCount">
              <el-input v-model="backupConfig.alarmCount" type="Number" maxlength="4" min="0" :disabled="!formable" style="width: 120px;" @input="numberLimit(arguments[0], 'alarmCount', 1, 1440)" ></el-input>
              <span>{{ $t('pages.require_Msg7') }}</span>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.backupFileAlarmMsg3') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="0px" style="margin-left: 40px;">
              <ResponseContent
                :status="respondStatus"
                :show-select="true"
                :editable="formable"
                read-only
                :prop-check-rule="true"
                :show-check-rule="false"
                :prop-rule-id="backupConfig.alarmStgId"
                :check-empty-rule="false"
                @getRuleId="(ruleId) => { getRuleId(ruleId) }"
              />
            </FormItem>
          </el-tab-pane>
          <el-tab-pane v-if="otherConfig.enable" :label="otherConfig.label" :name="otherConfig.name">
            <div style="line-height: 22px;">
              <el-checkbox-group v-model="otherConfig.checked">
                <el-row>
                  <el-col v-for="(item, index) in otherConfig.options" :key="index" :span="24">
                    <el-checkbox v-if="!item.format" :label="item.id" :disabled="!formable">{{ item.label }}</el-checkbox>
                    <div v-if="item.format === 1" >
                      <el-checkbox v-if="item.format === 1" :label="item.id" :disabled="!formable">
                        <i18n path="pages.terminalConfig6">
                          <div slot="minutes" :class="{ 'other-config': true ,'is-error': otherConfig.checked.includes(item.id) && !item.minute}">
                            <el-input-number v-model="item.minute" :controls="false" :step="1" step-strictly :min="1" :max="30000" :disabled="!formable" style="width:105px;margin: 5px;"></el-input-number>
                            <div v-if="otherConfig.checked.includes(item.id) && !item.minute" class="error-msg">
                              {{ $t('pages.terminalConfigErr1') }}
                            </div>
                          </div>
                        </i18n>
                        <el-tooltip effect="dark" content="" placement="bottom-start">
                          <div slot="content">
                            {{ $t('pages.terminalConfigTip1') }}
                          </div>
                          <i class="el-icon-info" />
                        </el-tooltip>
                      </el-checkbox>
                    </div>
                    <el-checkbox v-if="item.format === 2" :label="item.id" :disabled="!formable">
                      <i18n path="pages.terminalConfig7">
                        <el-input slot="size" v-model="item.size" :disabled="!formable" maxlength="5 " style="width:75px;margin: 5px;"></el-input>
                        <el-select slot="action" v-model="item.type" :disabled="!formable" size="mini" style="width: 180px;">
                          <el-option :value="0" :label="$t('pages.terminalConfigMsg_maxDisk')" :title="$t('pages.terminalConfigMsg_maxDisk')"></el-option>
                          <el-option :value="1" :label="$t('pages.noBackup')" :title="$t('pages.noBackup')"></el-option>
                        </el-select>
                      </i18n>
                    </el-checkbox>
                    <div v-if="item.format === 3">
                      <el-checkbox :label="item.id" :disabled="!formable">{{ item.label }}</el-checkbox>
                      <div style="margin-top: -5px; padding-left: 25px;">
                        <div style="display: inline-block; margin-right: 20px;">
                          <el-radio v-model="item.type" :label="1" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0">
                            {{ $t('pages.overwriteOriginalFile') }}
                          </el-radio>
                        </div>
                        <div style="display: inline-block;">
                          <el-radio v-model="item.type" :label="2" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0">
                            <i18n path="pages.saveToPath">
                              <div slot="minutes" :class="{'other-config': true ,'is-error': otherConfig.checked.includes(item.id) && !item.minute}">
                                <el-input-number v-model="item.minute" :controls="false" :step="1" :min="1" :max="30000" step-strictly :disabled="!formable" style="width:105px;margin: 5px;"></el-input-number>
                                <div v-if="otherConfig.checked.includes(item.id) && !item.minute" class="error-msg">
                                  {{ $t('pages.terminalConfigErr1') }}
                                </div>
                              </div>
                              <div slot="path" :class="{'other-config': true ,'is-error': otherConfig.checked.indexOf(item.id) >= 0 && item.type == 2 && !item.path}">
                                <el-input v-model="item.path" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0" clearable maxlength="300" style="width:200px;margin: 5px;"/>
                                <div v-if="otherConfig.checked.indexOf(item.id) >= 0 && item.type == 2 && !item.path" class="error-msg">
                                  {{ $t('pages.terminalConfigErr2') }}
                                </div>
                              </div>
                            </i18n>
                          </el-radio>
                        </div>
                        <div>
                          <el-checkbox :label="4" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0">{{ $t('pages.unableUpdateApplyParam') }}</el-checkbox>
                        </div>
                      </div>
                    </div>
                    <div v-if="item.format === 6">
                      <el-checkbox :label="item.id" :disabled="!formable">{{ item.label }}</el-checkbox>
                      <div style="margin-top: -5px; padding-left: 25px;">
                        <div style="display: inline-block; margin-right: 20px;">
                          <el-radio v-model="item.type" :label="1" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0">
                            {{ $t('pages.overwriteOriginalFile') }}
                          </el-radio>
                        </div>
                        <div style="display: inline-block;">
                          <el-radio v-model="item.type" :label="2" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0">
                            <i18n path="pages.saveToPath">
                              <div slot="minutes" :class="{'other-config': true ,'is-error': otherConfig.checked.includes(item.id) && !item.minute}">
                                <el-input-number v-model="item.minute" :controls="false" :step="1" :min="1" :max="30000" step-strictly :disabled="!formable" style="width:105px;margin: 5px;"></el-input-number>
                                <div v-if="otherConfig.checked.includes(item.id) && !item.minute" class="error-msg">
                                  {{ $t('pages.terminalConfigErr1') }}
                                </div>
                              </div>
                              <div slot="path" :class="{'other-config': true ,'is-error': otherConfig.checked.indexOf(item.id) >= 0 && item.type == 2 && !item.path}">
                                <el-input v-model="item.path" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0" clearable maxlength="300" style="width:200px;margin: 5px;"/>
                                <div v-if="otherConfig.checked.indexOf(item.id) >= 0 && item.type == 2 && !item.path" class="error-msg">
                                  {{ $t('pages.terminalConfigErr2') }}
                                </div>
                              </div>
                            </i18n>
                          </el-radio>
                        </div>
                        <div>
                          <el-checkbox :label="8" :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0">{{ $t('pages.unableUpdateApplyParam') }}</el-checkbox>
                        </div>
                      </div>
                    </div>
                    <div v-if="item.format === 4">
                      <el-checkbox v-if="item.format === 4" :label="item.id" :disabled="!formable">
                        <i18n path="pages.terminalConfig8">
                          <div slot="port" :class="{'other-config': true ,'is-error': otherConfig.checked.includes(item.id) && !item.port}">
                            <el-input-number v-model="item.port" :controls="false" :precision="0" :min="1025" :max="65535" :disabled="!formable" style="width:105px;margin: 5px;"></el-input-number>
                            <div v-if="otherConfig.checked.includes(item.id) && !item.port" class="error-msg">
                              {{ $t('pages.terminalConfigErr3') }}
                            </div>
                          </div>
                        </i18n>
                        <el-tooltip effect="dark" content="" placement="bottom-start">
                          <div slot="content">
                            {{ $t('pages.terminalConfigTip2') }}
                          </div>
                          <i class="el-icon-info" />
                        </el-tooltip>
                      </el-checkbox>
                    </div>
                    <div v-if="item.format === 5">
                      <el-checkbox :label="item.id" :disabled="!formable" @change="handleCheckChange">
                        {{ item.label }}：
                      </el-checkbox>
                    </div>
                  </el-col>
                </el-row>
              </el-checkbox-group>
              <div v-if="batchDecLimitIndex > -1" style="padding-left: 25px;">
                <FormItem label-width="0px" prop="decLimitType">
                  <i18n path="pages.batchDecLimit">
                    <el-select
                      slot="timePeriod"
                      v-model="otherConfig.options[batchDecLimitIndex].decLimitCycleType"
                      :disabled="!formable || otherConfig.checked.indexOf(4050) < 0"
                      :placeholder="$t('text.select')"
                      class="select-cycle-type"
                      @click.native="(e) => { e.preventDefault() }"
                    >
                      <el-option :label="$t('pages.day1')" :value="1"/>
                      <el-option :label="$t('pages.week1')" :value="2"/>
                      <el-option :label="$t('text.month')" :value="3"/>
                    </el-select>
                    <div slot="count" :class="{'other-config': true ,'is-error': otherConfig.checked.includes(4050) && (!otherConfig.options[batchDecLimitIndex].decLimitTypes || otherConfig.options[batchDecLimitIndex].decLimitTypes.length === 0)}">
                      <el-input-number
                        v-model="otherConfig.options[batchDecLimitIndex].decLimitCycleNum"
                        :controls="false"
                        :disabled="!formable || otherConfig.checked.indexOf(4050) < 0"
                        style="width: 70px"
                        :precision="0"
                        :min="1"
                        :max="999"
                      />
                      <div v-if="otherConfig.checked.includes(4050) && !otherConfig.options[batchDecLimitIndex].decLimitCycleNum" class="error-msg">
                        {{ $t('pages.fileNumberNotEmpty') }}
                      </div>
                    </div>

                    <div slot="decLimitType" :class="{'other-config': true ,'is-error': otherConfig.checked.includes(4050) && (!otherConfig.options[batchDecLimitIndex].decLimitTypes || otherConfig.options[batchDecLimitIndex].decLimitTypes.length === 0)}">
                      <el-select
                        v-model="otherConfig.options[batchDecLimitIndex].decLimitTypes"
                        :disabled="!formable || otherConfig.checked.indexOf(4050) < 0"
                        multiple
                        class="select-dec-limit-type"
                        @click.native.prevent="(e) => { e.preventDefault() }"
                      >
                        <el-option :label="$t('pages.batchDecryption')" :value="1"/>
                        <el-option :label="$t('pages.requestDecryption')" :value="2"/>
                      </el-select>
                      <div v-if="otherConfig.checked.includes(4050) && (!otherConfig.options[batchDecLimitIndex].decLimitTypes || otherConfig.options[batchDecLimitIndex].decLimitTypes.length === 0)" class="error-msg">
                        {{ $t('pages.decryptionModeCannotBeEmpty') }}
                      </div>
                    </div>
                  </i18n>
                </FormItem>
                <!-- 违规响应规则选择组件中有多个复选框，在el-checkbox-group中使用组件不能正常显示复选框状态 -->
                <FormItem label-width="25px" prop="decLimitTypeArr">
                  <el-checkbox-group v-model="otherConfig.options[batchDecLimitIndex].decLimitTypeArr" :disabled="!formable || otherConfig.checked.indexOf(4050) < 0">
                    <el-checkbox :label="1">{{ $t('pages.restrictedBatchDecryption') }}</el-checkbox>
                  </el-checkbox-group>
                  <FormItem label-width="0px" style="margin-left: -13px">
                    <ResponseContent
                      :rule-type-name="$t('pages.triggerResponseRule')"
                      :show-select="true"
                      :editable="formable && otherConfig.checked.indexOf(4050) >= 0"
                      read-only
                      :prop-check-rule="decLimitIsDoResponse"
                      :show-check-rule="true"
                      :prop-rule-id="otherConfig.options[batchDecLimitIndex].ruleId"
                      :select-style="{ 'margin-left': '-5px' }"
                      @getRuleId="(id) => { otherConfig.options[batchDecLimitIndex].ruleId = id }"
                      @ruleIsCheck="getRuleIsChecked2"
                      @validate="(val) => { responseValidate = val }"
                    />
                  </FormItem>
                  <div v-if="otherConfig.checked.indexOf(4050) >= 0 && otherConfig.options[batchDecLimitIndex].decLimitTypeArr.length <= 0" style="line-height: 19px; font-size: 13px; color: #F56C6C;">
                    {{ $t('pages.limitDecryption3') }}
                  </div>
                </FormItem>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane v-if="throughConfig.enable" :label="throughConfig.label" :name="throughConfig.name">
            <div style="line-height: 22px;">
              <el-checkbox-group v-model="throughConfig.checked">
                <el-row>
                  <el-col v-for="(item, i) in throughConfig.options" :key="i" :span="12">
                    <el-checkbox :label="item.id" :disabled="!formable">
                      {{ $t('pages.terminalConfigMsg_zipped', { action: item.label }) }}
                    </el-checkbox>
                    <el-checkbox-group v-model="item.suffix" style="margin: 5px 30px;">
                      <el-checkbox v-for="(suffix, index) in zipSuffixes" :key="index" :label="suffix" :disabled="!formable">{{ suffix }}</el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importTerminalConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import {
  addConfig,
  deleteConfig,
  getConfig,
  getConfigByName,
  getConfigPage,
  updateConfig
} from '@/api/system/terminalManage/terminalConfig'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig';

export default {
  name: 'TerminalConfig',
  components: { ResponseContent, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 106,
      baseConfig: {
        enable: true, // 是否启用此配置
        name: 'baseConfig',
        label: this.$t('pages.generalSettings'),
        checked: [],
        options: [
          { id: 4001, label: this.$t('pages.hideTerminalIcon') },
          { id: 4002, label: this.$t('pages.hideTerminalProcess') },
          // { id: 4003, label: '关闭运行日志' },
          { id: 4009, label: this.$t('pages.hideTerminalContextmenu') },
          { id: 4010, label: this.$t('pages.hideTerminalAlarm') },
          { id: 4015, label: this.$t('pages.terminalProtection') },
          { id: 4017, label: this.$t('pages.backupFileAlarmLoginWindowPosition'), format: 6 },
          { id: 4072, label: this.$t('pages.backupFileAlarmForbidLoginWindow'), isHide: true },
          { id: 4073, label: this.$t('pages.backupFileAlarmReminderLogin') },
          { id: 4074, label: this.$t('pages.backupFileAlarmCancelAutoLogin') },
          { id: 4075, label: this.$t('pages.backupFileAlarmManualOperationCloseProcess') }, //  终端注销操作员后手动关闭受控进程
          // { id: 4020, label: '终端在设定时间后关闭加解密功能', format: 1, time: '',
          //   strValueFormatter: this.strValueFormatter4020,
          //   formatStrValue: (o, strValue, isChecked) => { if (isChecked) o.time = strValue.split('|')[1].trim().split(' ')[0] }
          // },
          // { id: 4043, label: '无鼠标键盘输入时，切换状态', format: 2, minute: '', status: 4,
          //   strValueFormatter: this.strValueFormatter4043, formatStrValue: this.formatStrValue4043
          // },
          // 关闭文件加解密功能后，未关闭的受控程序可以继续工作 {seconds} 秒
          { id: 4014, label: this.$t('pages.terminalConfigMsg_setTime'), format: 3, second: '',
            valueFormatter: (o, isChecked) => { return !isChecked ? 300 : o.second },
            formatValue: (o, value, isChecked) => { o.second = isChecked && value ? value : 300 }
          },
          // { id: 4044, label: '定时关闭计算机或重启', format: 4, type: 0, rate: [], time: '', minute: '',
          //   strValueFormatter: this.strValueFormatter4044,
          //   formatStrValue: (o, strValue, isChecked) => {
          //     o.rate = [0]
          //     o.time = '20:00'
          //     o.type = 0
          //     o.minute = ''
          //     if (isChecked) {
          //       const array = strValue.split('|')
          //       const rateValue = Number.parseInt(array[0])
          //       o.rate = rateValue === 0 ? [0] : this.numToList(array[0], 6)
          //       o.time = array[1]
          //       o.type = Number.parseInt(array[2])
          //       o.minute = array[3]
          //     }
          //   }
          // },
          // 终端监控数据保留天数
          { id: 5016, label: this.$t('pages.terminalLogRetentionDays'), format: 5, day: '',
            valueFormatter: (o, isChecked) => { return !isChecked ? 7 : o.day },
            formatValue: (o, value, isChecked) => { o.day = isChecked ? value : 7 }
          }
        ]
      },
      otherConfig: {
        enable: true, // 是否启用此配置
        name: 'otherConfig',
        label: this.$t('button.highConfig'),
        checked: [],
        options: [  //  当需要额外添加元素时，请注意id=4050的元素项可能需要同时修改
          // { id: 4004, label: '关闭内外网功能' },
          { id: 5010, label: this.$t('pages.allowSymantec') },    //  允许赛门铁克杀毒软件
          { id: 4008, label: this.$t('pages.allowTheKingsoftBully') },    //  允许金山毒霸
          // { id: 4016, label: '终端启动时，验证已绑定硬件信息' },
          { id: 5011, label: this.$t('pages.allowMobileMTPCopy') },
          { id: 4019, label: this.$t('pages.off_Office') },
          // { id: 4038, label: '允许Win10升级时清理绿盾文件' },
          // { id: 4100, label: '不保存终端标记（如云桌面母盘的终端）' },
          // { id: 4007, label: '关闭增强的网络文件访问' },
          // { id: 5005, label: '开始explore刷TMP文件功能' },
          // { id: 5006, label: '启用分页内存申请功能' },
          // { id: 4011, label: '关闭控制台加解密进度条' },
          // { id: 5009, label: '加密文件启用密级判断' },
          // { id: 4005, label: '简化加密功能' },
          // { id: 5008, label: '保护内核函数不受破坏' },
          // { id: 4099, label: '允许打开一代加密的邮件附件' },
          // { id: 5014, label: '允许杀毒软件分析加密文件' },
          // { id: 4046, label: '审批申请关闭文件阅读权限判断' },
          // { id: 5015, label: '关闭受控程序防伪冒', valueFormatter: (o, isChecked) => { return !isChecked ? 0 : 3 } },
          // { id: 4101, label: '允许Outlook拖拽' },
          // { id: 5007, label: '允许加密的EXE程序执行' },
          // { id: 4022, label: '关闭网络监控功能' },
          // { id: 4018, label: '终端启动时，仅验证系统盘配置' },
          // { id: 4006, label: '批量解密时支持宽字符' },
          { id: 4070, label: this.$t('pages.notDisplayIcon') },
          { id: 4021, label: this.$t('pages.batchConversion'),
            strValueFormatter: (o, isChecked) => { return isChecked ? 1 : 0 },
            formatStrValue: (o, strValue, isChecked) => {} },
          { id: 3019, label: this.$t('pages.winSafeModeAble') },
          { id: 3034, label: this.$t('pages.enableTermLinkage'), format: 4, port: '',
            strValueFormatter: this.strValueFormatter3034,
            formatStrValue: (o, strValue, isChecked) => {
              o.port = isChecked ? strValue : ''
            }
          },
          { id: 4103, label: this.$t('pages.backupFileAlarmReEncrypt'), format: 1, minute: '',
            strValueFormatter: this.strValueFormatter4103,
            formatStrValue: (o, strValue, isChecked) => { o.minute = isChecked ? strValue : '' }
          },
          // { id: 4104, label: '设置磁盘空间不足指定大小备份方式', format: 2, size: '', type: 0,
          //   strValueFormatter: this.strValueFormatter4104,
          //   formatStrValue: (o, strValue, isChecked) => {
          //     o.size = '', o.type = 0
          //     if (isChecked) {
          //       const array = strValue.split('|')
          //       o.size = array[0], o.type = Number.parseInt(array[1])
          //     }
          //   }
          // },
          { id: 4049, label: this.$t('pages.autoDownAfterApply'), format: 3, type: 1, path: '',
            strValueFormatter: this.autoDownAfterApplyStrValueFormatter,
            valueFormatter: this.autoDownAfterApplyValueFormatter,
            formatStrValue: (o, strValue, isChecked) => { o.path = strValue },
            formatValue: this.formatValue4049
          },
          { id: 4051, label: this.$t('pages.autoDownAfterApply_1'), format: 6, type: 1, path: '',
            strValueFormatter: this.autoDownAfterApplyStrValueFormatter,
            valueFormatter: this.waterAutoDownAfterApplyValueFormatter,
            formatStrValue: (o, strValue, isChecked) => { o.path = strValue },
            formatValue: this.formatValue4051
          },
          // index = 10 如需修改请同步修改 batchDecLimitIndex  （index表示 id=4050在otherConfig中第 index+1 个元素）
          { id: 4050, label: this.$t('pages.enableBatchDecLimit'), format: 5, decLimitType: 0, decLimitTypes: [], decLimitCycleType: 1, decLimitCycleNum: 20, decLimitTypeArr: [],
            ruleId: undefined, alarmType: 46, sstType: 0,
            strValueFormatter: this.decLimitAfterApplyStrValueFormatter,
            valueFormatter: this.decLimitAfterApplyValueFormatter,
            formatStrValue: this.formatStrValue4050,
            formatValue: this.formatValue4050
          }
        ]
      },
      throughConfig: {
        enable: false, // 是否启用此配置
        name: 'throughConfig',
        label: this.$t('pages.pierceThrough'),
        checked: [],
        options: [
          { id: 4039, label: this.$t('pages.globalEncryption'), active: 1, suffix: [],
            strValueFormatter: this.throughConfigStrValueFormatter, formatStrValue: this.formatThroughConfigStrValue },
          { id: 4040, label: this.$t('pages.batchEncryption'), active: 1, suffix: [],
            strValueFormatter: this.throughConfigStrValueFormatter, formatStrValue: this.formatThroughConfigStrValue },
          { id: 4041, label: this.$t('pages.decryptFile'), active: 1, suffix: [],
            strValueFormatter: this.throughConfigStrValueFormatter, formatStrValue: this.formatThroughConfigStrValue }
        ]
      },
      backupConfigTemp: {
        backupType: 1, // 备份文件位置 1 最大磁盘 2 终端目录
        diskFreeSpace: 10, // 保留磁盘控件 默认10GB
        sstType: 0, // 普通告警
        alarmType: 33, // 备份空间不足告警
        alarmStgId: null, //  违规响应规则id 为空和0时表示不告警
        alarmFrequency: 1, // 告警频率 间隔多久告警一次，最大不超过24小时
        alarmCount: 1 // 告警次数 最大限制次数需要 用24小时和告警频率去计算
      },
      backupConfig: {
        backupType: 1, // 备份文件位置 1 最大磁盘 2 终端目录
        diskFreeSpace: 10, // 保留磁盘控件 默认2GB
        sstType: 0, // 普通告警
        alarmType: 33, // 备份空间不足告警
        alarmStgId: null, //  违规响应规则id 为空和0时表示不告警
        alarmFrequency: 1, // 告警频率 间隔多久告警一次，最大不超过24小时
        alarmCount: 1 // 告警次数 最大限制次数需要 用24小时和告警频率去计算
      },
      backupTypeMap: {
        1: this.$t('pages.backupFileBackupTypeMap1'),
        2: this.$t('pages.backupFileBackupTypeMap2')
      },
      configMap: undefined,
      zipSuffixes: ['.zip', '.rar', '.7z'],
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ] },
        { prop: 'option', label: 'stgMessage', width: '200', formatter: this.optionFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        entityType: undefined,
        entityId: undefined
      },
      temp: {},
      oldTemp: {},
      entityName: '',
      showTree: true,
      submitting: false,
      isSelfConfig: false,
      validate: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      dialogFormVisible: false,
      dialogStatus: '',
      activeName: 'baseConfig', // tab页
      respondStatus: '', // 标识当前是新增或修改的状态
      isHide: false, // 终端未登录，则登录窗口显示在屏幕正中间配置项是否隐藏
      moduleIds: [],
      isPermission: false,
      filterDataId: [4103, 4021, 4049, 4050, 4070], // 与权限相关需过滤的id
      textMap: {
        update: this.i18nConcatText(this.$t('pages.terminalConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.terminalConfigStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      decLimitIsDoResponse: false,
      batchDecLimitIndex: 11
    }
  },
  computed: {
    gridTable() {
      return this.$refs['WinTitleTable']
    },
    checkRetention: {
      get() {
        return this.baseConfig.checked.includes(5016)
      },
      set() {}
    }
  },
  created() {
    this.formatConfig()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    async formatConfig() {
      await listSaleModuleId().then(resp => {
        this.moduleIds.splice(0, this.moduleIds.length, ...resp.data)
      })
      // 根据是否有文件透明加密模块权限限制相关配置项的显示
      this.isPermission = this.moduleIds.indexOf(51) < 0 && this.moduleIds.indexOf(151) < 0 && this.moduleIds.indexOf(251) < 0 && this.moduleIds.indexOf(351) < 0
      if (this.isPermission) {
        this.baseConfig.options = this.baseConfig.options.filter(data => data.id != 4014)
        this.otherConfig.options = this.otherConfig.options.filter(data => this.filterDataId.indexOf(data.id) < 0)
      }
      // 根据是否有文件溯源模块模块权限限制相关配置项的显示
      const isPermission = this.moduleIds.indexOf(54) < 0 && this.moduleIds.indexOf(154) < 0 && this.moduleIds.indexOf(254) < 0
      if (isPermission) {
        this.otherConfig.options = this.otherConfig.options.filter(data => data.id != 4051)
      }
      // otherConfig.options发生变化后，需要重新计算下标
      this.batchDecLimitIndex = this.otherConfig.options.map(t => t.id).indexOf(4050)
    },
    selectable(row, index) {
      return selectable(row, index, this)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getConfigPage(searchQuery)
    },
    diskFreeSpaceBlur(val) {
      if (!this.backupConfig.diskFreeSpace) {
        this.backupConfig.diskFreeSpace = 2
      }
    },
    LocationError(tabName) {
      this.validate = false
      this.activeName = tabName
    },
    strValueFormatter4020(o, isChecked) {
      if (!isChecked) return ''
      if (!o.time) {
        this.LocationError('baseConfig')
        return null
      }
      return '1970-01-01 00:00:00|' + o.time + ' 23:59:59'
    },
    strValueFormatter4043(o, isChecked) {
      if (!isChecked) return ''
      if (!o.minute) {
        this.LocationError('baseConfig')
        return null
      }
      return !isChecked ? '' : o.minute + '|' + o.status
    },
    close() {
      this.isHide = false
    },
    entityTypeData(data) {
      this.isHide = data == 4
    },
    formatStrValue4043(o, strValue, isChecked) {
      o.minute = ''
      o.status = 4
      if (isChecked) {
        const array = strValue.split('|')
        o.minute = array[0]
        o.status = Number.parseInt(array[1])
      }
    },
    strValueFormatter4044(o, isChecked) {
      if (!isChecked) return ''
      if (!o.time) {
        this.LocationError('baseConfig')
        return null
      } else if (!o.rate || o.rate.length === 0) {
        this.LocationError('baseConfig')
        return null
      }
      const rateValue = this.getSum(o.rate)
      o.minute = !o.minute ? 0 : o.minute
      return !isChecked ? '' : rateValue + '|' + o.time + '|' + o.type + '|' + o.minute
    },
    strValueFormatter4103(o, isChecked) {
      if (!isChecked) return ''
      if (!o.minute) {
        this.LocationError('otherConfig')
        return null
      }
      return o.minute
    },
    strValueFormatter3034(o, isChecked) {
      if (!isChecked) return ''
      if (!o.port) {
        this.LocationError('otherConfig')
        return null
      }
      return o.port
    },
    strValueFormatter4104(o, isChecked) {
      if (!isChecked) return ''
      if (!o.size) {
        this.LocationError('otherConfig')
        return null
      }
      return o.size + '|' + o.type
    },
    throughConfigStrValueFormatter(o, isChecked) {
      return !isChecked ? '' : o.suffix.join('|')
    },
    formatThroughConfigStrValue(o, strValue, isChecked) {
      o.suffix = []
      if (isChecked) {
        o.suffix = strValue.split('|')
      }
    },
    autoDownAfterApplyStrValueFormatter(o, isChecked) {
      if (isChecked && o.type === 2) {
        if (!o.path) {
          this.LocationError('otherConfig')
          return ''
        }
        return o.path
      }
      return ''
    },
    decLimitAfterApplyStrValueFormatter(o, isChecked) {
      console.log(this.responseValidate)
      if (isChecked && (o.decLimitTypeArr.length <= 0 || !this.responseValidate || o.decLimitTypes.length === 0 || !o.decLimitCycleNum)) {
        this.LocationError('otherConfig')
        return ''
      }
      //  解密类型，1：批量解密，2：申请解密，3：批量解密+申请解密
      let decLimitType = 0
      if (o.decLimitTypes.length > 0) {
        o.decLimitTypes.forEach(type => {
          decLimitType += type
        })
      }
      const json = {
        decLimitCycleType: o.decLimitCycleType,
        decLimitCycleNum: o.decLimitCycleNum,
        ruleId: o.ruleId,
        alarmType: 46,
        sstType: 0,
        decLimitType: decLimitType
      }
      return JSON.stringify(json)
    },
    autoDownAfterApplyValueFormatter(o, isChecked) {
      let changeType = 4
      changeType = this.otherConfig.checked.indexOf(changeType) < 0 ? 0 : changeType
      return !isChecked ? 0 : o.type + changeType
    },
    waterAutoDownAfterApplyValueFormatter(o, isChecked) {
      let changeType = 8
      changeType = this.otherConfig.checked.indexOf(changeType) < 0 ? 0 : changeType
      return !isChecked ? 0 : o.type + changeType
    },
    decLimitAfterApplyValueFormatter(o, isChecked) {
      return !isChecked ? 0 : this.getSum(o.decLimitTypeArr)
    },
    formatValue4049(o, value, isChecked) {
      const changeType = 4
      if ((value & changeType) === changeType) {
        this.otherConfig.checked.push(changeType)
        value -= 4
      }
      o.type = !value ? 1 : value
    },
    formatValue4051(o, value, isChecked) {
      const changeType = 8
      if ((value & changeType) === changeType) {
        this.otherConfig.checked.push(changeType)
        value -= 8
      }
      o.type = !value ? 1 : value
    },
    formatValue4050(o, value, isChecked) {
      o.decLimitTypeArr = this.numToList(value, 2)
      this.decLimitIsDoResponse = o.decLimitTypeArr.indexOf(2) >= 0
    },
    formatStrValue4050(o, strValue, isChecked) {
      if (strValue && strValue.length > 0) {
        const obj = JSON.parse(strValue)
        o.decLimitCycleType = obj.decLimitCycleType
        o.decLimitCycleNum = obj.decLimitCycleNum
        o.ruleId = obj.ruleId
        o.decLimitTypes = []
        if (obj.decLimitType) {
          if ((obj.decLimitType & 1) === 1) {
            o.decLimitTypes.push(1)
          }
          if ((obj.decLimitType & 2) === 2) {
            o.decLimitTypes.push(2)
          }
        } else {  // 兼容旧数据（2024-D2之前版本的数据）没有decLimitType字段，原功能默认仅限制批量解密方式
          o.decLimitTypes.push(1)
        }
      } else {
        o.decLimitCycleType = 1
        o.decLimitCycleNum = 1
        o.ruleId = undefined
      }
      //  若未选中解密申请选项，默认解密类型为“1-批量解密“
      if (!o.decLimitTypes || o.decLimitTypes.length === 0) {
        o.decLimitTypes = [1];
      }
      o.alarmType = 46
      o.sstType = 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    loadConfigByObjectId() {
      getConfig({
        objectType: this.query.entityType,
        objectId: this.query.entityId
      }).then(respond => {
        this.entityName = ''
        this.temp.id = undefined
        this.isSelfConfig = false
        if (!respond.data) {
          this.resetFormItem()
        } else {
          this.bean2FromItem(respond.data)
        }
      })
    },
    resetTemp() {
      this.activeName = 'baseConfig'
      this.temp = {
        name: undefined,
        id: undefined,
        active: false,
        entityType: undefined,
        entityId: undefined,
        option: [],
        strOption: [],
        backupDirs: []
      }
      this.backupConfig = Object.assign({}, this.backupConfigTemp)
    },
    rateChange(data) {
      if (data.indexOf(0) > -1 || data.length === 7) {
        data.splice(0, data.length, 0)
      }
    },
    handleCreate() {
      this.resetTemp()
      this.resetFormItem()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      const isUserStg = row.entityType == 2 || row.entityType == 4
      this.isHide = !this.formable && isUserStg
      this.resetTemp()
      this.resetFormItem()
      this.temp = JSON.parse(JSON.stringify(row))
      this.oldTemp = JSON.parse(JSON.stringify(row))
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.bean2FromItem(this.temp)
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    resetFormItem() {
      const editConfigs = [this.baseConfig, this.otherConfig, this.throughConfig]
      editConfigs.forEach(config => {
        config.checked.splice(0)
        config.options.forEach(option => {
          if (option.formatStrValue) option.formatStrValue(option, '', false)
          if (option.formatValue) option.formatValue(option, '', false)
        })
      })
    },
    bean2FromItem(bean) {
      this.isSelfConfig = bean.entityType == this.query.entityType && bean.entityId == this.query.entityId
      this.entityName = this.isSelfConfig ? this.$t('pages.self') : bean.entityName
      this.temp.id = bean.id
      const editOptions = bean.option.concat(bean.strOption)
      const flag = editOptions.some(item => item.id === 4103)
      if (!flag) {
        const option = { id: 4103, value: 0 }
        editOptions.push(option)
      }
      //  兼容旧数据没有3034的问题
      const flag3034 = editOptions.some(item => item.id === 3034)
      if (!flag3034) {
        const option = { id: 3034, value: 0 }
        editOptions.push(option)
      }
      const optionMap = {}
      editOptions.forEach(option => { optionMap[option.id] = option })
      const editConfigs = [this.baseConfig, this.otherConfig, this.throughConfig]
      editConfigs.forEach(config => {
        config.checked.splice(0)
        config.options.forEach(option => {
          const checkedOption = optionMap[option.id]
          let isChecked = false
          if (checkedOption && (checkedOption.id === 4009 || checkedOption.id === 4010)) {
            isChecked = checkedOption.value == 1
          } else {
            isChecked = checkedOption && !!checkedOption.value
          }
          if (isChecked) config.checked.push(option.id)
          if (option.formatStrValue) option.formatStrValue(option, !checkedOption ? null : checkedOption.strValue, isChecked)
          if (option.formatValue) option.formatValue(option, !checkedOption ? null : checkedOption.value, isChecked)
        })
      })
      // 为兼容旧策略同时勾选隐藏图标和隐藏进程，需要勾选上隐藏告警弹窗项的问题(界面勾选)，用于判断升级后，未手动保存过策略，这时策略json没有4010配置项，界面需勾选上
      const index = bean.option.findIndex(item => item.id === 4010)
      if (this.baseConfig.checked.includes(4001) && this.baseConfig.checked.includes(4002) && !this.baseConfig.checked.includes(4010) && index === -1) {
        this.baseConfig.checked.push(4010)
      }
      if (bean.backupDirs) {
        this.backupConfig = bean.backupDirs[0]
      }
    },
    fromItem2Bean() {
      this.validate = true
      const { objectType: entityType, objectId: entityId } = this.query
      const stg = { ...this.temp, entityType, entityId, option: [], strOption: [], backupDirs: [] }
      if (this.baseConfig.checked.length > 0) {
        const filterBaseConfig = []
        this.baseConfig.checked.forEach(item => {
          if ((this.query.objectType == 2 || this.query.objectType == 4) && item != 4017) {
            filterBaseConfig.push(item)
          }
        })
        if (filterBaseConfig.length > 0) {
          this.baseConfig.checked.splice(0, this.baseConfig.checked.length, ...filterBaseConfig)
        }
      }
      const editConfigs = [this.baseConfig, this.otherConfig, this.throughConfig]
      for (let i = 0, size = editConfigs.length; i < size; i++) {
        const config = editConfigs[i]
        for (let j = 0, opSize = config.options.length; j < opSize; j++) {
          const option = config.options[j]
          const bean = { id: option.id }
          const isChecked = config.checked.indexOf(option.id) >= 0
          if (isChecked) {
            if (option.value) bean.value = option.value
            if (option.strValue) bean.strValue = option.strValue
            if (option.valueFormatter) bean.value = option.valueFormatter(option, isChecked)
            if (option.strValueFormatter) bean.strValue = option.strValueFormatter(option, isChecked)
          }
          if (this.validate) {
            if (bean.value === undefined) {
              bean.value = isChecked ? 1 : 0
            }
            if (bean.id === 4009 || bean.id === 4010) {
              bean.value = isChecked ? 1 : 2
            }
            if (bean.strValue !== undefined) {
              stg.strOption.push(bean)
            } else {
              stg.option.push(bean)
            }
          } else {
            return null // 验证失败，直接返回
          }
        }
      }
      // 备份文件
      stg.backupDirs.splice(0)
      stg.backupDirs.push(this.backupConfig)
      return stg
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const stg = this.fromItem2Bean()
          if (this.validate) {
            stg.entityId = this.temp.entityId
            stg.entityType = this.temp.entityType
            stg.description = this.optionFormatter(stg)
            addConfig(stg).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const stg = this.fromItem2Bean()
          if (this.validate) {
            stg.description = this.optionFormatter(this.oldTemp)
            stg.description_new = this.optionFormatter(stg)
            updateConfig(stg).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    deleteData() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteConfig({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getConfigByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    optionFormatter: function(row, data) {
      if (!this.configMap) {
        this.configMap = {}
        this.baseConfig.options.forEach(item => { this.configMap[item.id] = item.label })
        this.otherConfig.options.forEach(item => { this.configMap[item.id] = item.label })
        this.throughConfig.options.forEach(item => { this.configMap[item.id] = item.label })
      }
      const result = []
      const isUserStg = row.entityType == 2 || row.entityType == 4
      row.option.forEach(item => {
        if (isUserStg && item.id == 4017 && item.value) {
          item.value = 0
        }
        if (item.value) {
          if (item.id === 4009 || item.id === 4010) {
            if (item.value === 1) {
              result.push(this.configMap[item.id])
            }
          } else {
            result.push(this.configMap[item.id])
          }
        }
      })
      row.strOption.forEach(item => {
        if (item.value) result.push(this.configMap[item.id])
      })
      if (typeof (row.backupDirs) != 'undefined') {
        const backupDir = row.backupDirs[0]
        result.push(this.$t('pages.backupFileBackupDirMsg1', { info: this.backupTypeMap[backupDir.backupType] }))
        result.push(this.$t('pages.backupFileBackupDirMsg2', { info: backupDir.diskFreeSpace }))
        result.push(this.$t('pages.backupFileBackupDirMsg3', { info: backupDir.alarmFrequency }))
        result.push(this.$t('pages.backupFileBackupDirMsg4', { info: backupDir.alarmCount }))
      }
      return result.join('；')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    numberLimit(value, type, min, max) {
      if (value === 0) {
        this.backupConfig.alarmCount = 0
        this.backupConfig.alarmFrequency = 0
      }
      if ('alarmCount' === type) {
        max = parseInt(1440 / this.backupConfig.alarmFrequency)
        this.backupConfig.alarmCount = !value ? min : value > max ? max : value < min ? min : parseInt(value)
      } else {
        this.backupConfig.alarmFrequency = value > max ? max : value < min ? min : parseInt(value)
        const alarmCount = parseInt(1440 / this.backupConfig.alarmFrequency)
        this.backupConfig.alarmCount = this.backupConfig.alarmCount > alarmCount ? alarmCount : this.backupConfig.alarmCount < 0 ? 0 : parseInt(this.backupConfig.alarmCount)
      }
    },
    getRuleId(ruleId) {
      this.backupConfig.alarmStgId = ruleId
    },
    getRuleIsChecked2(check) {
      const item = this.otherConfig.options.filter(t => t.id === 4050)[0]
      if (check === 1) {
        item.decLimitTypeArr.push(2)
        this.decLimitIsDoResponse = true
      } else {
        const index = item.decLimitTypeArr.indexOf(2);
        if (index > -1) {
          item.decLimitTypeArr.splice(index, 1);
        }
        this.decLimitIsDoResponse = false
      }
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    retentionDataCheck(val, checkLabel) {
      const checked = this.baseConfig.checked
      if (val) {
        checked.push(checkLabel)
      } else {
        const index = checked.indexOf(checkLabel)
        if (index >= 0) {
          checked.splice(index, 1)
        }
      }
    },
    handleNotLongCheckBox(value) {
      if (!value) {
        const index = this.baseConfig.checked.findIndex(item => { return item == 4072 });
        if (index > -1) {
          this.baseConfig.checked.splice(index, 1);
        }
      }
    },
    handleCheckChange(val) {
      const item = this.otherConfig.options.filter(t => t.id === 4050)[0]
      const index = item.decLimitTypeArr.indexOf(1);
      if (val && index === -1) {
        item.decLimitTypeArr.push(1)
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-radio {
    margin-right: 0;
    white-space: unset;
    display: inline;
  }
  >>>.el-checkbox__label {
    display: inline;
  }
  .other-config {
    display: inline-block;
    position: relative;
  }
  .is-error {
    margin-bottom: 15px;
    & >>>.el-input__inner {
      border-color: #F56C6C;
    }
  }
  .error-msg {
    width: 200px;
    line-height: 19px;
    padding-left: 5px;
    position: absolute;
    font-size: 13px;
    color: #F56C6C;
  }
  .portClass {
    >>>.el-input__inner {
      text-align: center;
    }
  }
  .select-cycle-type {
    width: 90px;
    >>>input {
      height: 30px !important;
      text-align: center;
    }
  }
  .select-dec-limit-type {
    width: 210px;
    >>>input {
      height: 30px !important;
    }
  }
</style>
