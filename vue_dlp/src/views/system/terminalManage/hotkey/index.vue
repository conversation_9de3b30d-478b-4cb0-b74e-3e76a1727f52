<template>
  <div class="app-container">
    <Form
      ref="dataForm"
      :hide-required-asterisk="true"
      style="width: 700px;"
    >
      <el-card v-for="(item, index) in hotkeyOption" :key="index" class="box-card" style="background-color: transparent;">
        <div slot="header">
          <span>{{ item.title }}</span>
        </div>
        <el-row >
          <el-radio-group v-model="hotkeyList[index].keyCaption" @change="radioChange">
            <el-col :span="12">
              <el-radio :label="0">{{ item.defaultLabel }}</el-radio>
            </el-col>
            <el-col :span="4">
              <el-radio :label="1">{{ $t('pages.useHotkey') }}</el-radio>
            </el-col>
            <el-col :span="8" style="padding-left: 15px">
              <el-input
                v-model="hotkeyList[index].remark"
                :disabled="hotkeyList[index].keyCaption == 0 || hotkeyList[index].keyCaption == 2"
                @keyup.native="keyupHandle($event, index)"
                @keydown.native="keydownHandle($event, index)"
                @blur="blurHandle"
              />
            </el-col>
            <el-col :span="12">
              <el-radio :label="2">{{ $t('pages.notUseHotkey') }}</el-radio>
            </el-col>
          </el-radio-group>
        </el-row>
        <div v-if="index === 0" style="margin-top: 15px;font-size: 14px;color:#2b7aac;">Window和Mac终端使用上述默认热键，Linux终端使用默认热键 Ctrl + Shift + F8</div>
        <div v-show="hotkeyList[index].toolTip !== ''" style="text-align: center;font-size: 14px;color:red;">{{ hotkeyList[index].toolTip }}</div>
      </el-card>
      <div class="save-btn-container">
        <el-button :loading="submitting" type="primary" size="mini" @click="createData()">
          {{ $t('button.save') }}
        </el-button>
      </div>
    </Form>
  </div>
</template>

<script>
import { getHotkeyList, saveHotkey } from '@/api/system/terminalManage/hotkey'
import { deepMerge } from '@/utils'
export default {
  name: 'Hotkey',
  data() {
    return {
      hotkeyOption: [
        { title: this.$t('pages.hotkeyOption1'), defaultLabel: `${this.$t('pages.defaultHotkey')} Ctrl + Alt + F8` },
        { title: this.$t('pages.hotkeyOption2'), defaultLabel: `${this.$t('pages.defaultHotkey')} Ctrl + Alt + F9` },
        { title: this.$t('pages.hotkeyOption3'), defaultLabel: `${this.$t('pages.defaultHotkey')} Ctrl + Alt + F11` },
        { title: this.$t('pages.hotkeyOption4'), defaultLabel: `${this.$t('pages.defaultHotkey')} Ctrl + Alt + F12` },
        { title: this.$t('pages.hotkeyOption5'), defaultLabel: `${this.$t('pages.defaultHotkey')} Ctrl + Alt + P` }
      ],
      hotkeyList: [
        { higherKey: 0, id: 1, keyCaption: 0, lowerKey: 0, remark: '', type: 1, toolTip: '', 'default': 'Ctrl+Alt+F8' },
        { higherKey: 0, id: 2, keyCaption: 0, lowerKey: 0, remark: '', type: 2, toolTip: '', 'default': 'Ctrl+Alt+F9' },
        { higherKey: 0, id: 3, keyCaption: 0, lowerKey: 0, remark: '', type: 3, toolTip: '', 'default': 'Ctrl+Alt+F11' },
        { higherKey: 0, id: 4, keyCaption: 0, lowerKey: 0, remark: '', type: 4, toolTip: '', 'default': 'Ctrl+Alt+F12' },
        { higherKey: 0, id: 5, keyCaption: 0, lowerKey: 0, remark: '', type: 5, toolTip: '', 'default': 'Ctrl+Alt+P' }
      ],
      submitting: false,
      // 用于存储按下的低位键
      keyCodes: [],
      // 低位键集合
      lowerkeyCodes: ['Control', 'Alt', 'Shift'],
      // 快捷键的值
      remark: '',
      // F1-F12 112-123, 小键盘0-9 96-105, A-Z 65-90, 0-9 48-57
      // 高位键符号
      highkey: '',
      // 低位键位与后的值
      lowkey: 0,
      keyMap: {
        48: '0', 49: '1', 50: '2', 51: '3', 52: '4', 53: '5', 54: '6', 55: '7', 56: '8', 57: '9'
      }
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    getHotkeyList().then(res => {
      this.hotkeyList = deepMerge(this.hotkeyList, res.data)
    })
  },
  methods: {
    formatterSubmit() {
      this.hotkeyList.forEach(hotkey => {
        if (hotkey.keyCaption === 1 && hotkey.remark === '') {
          hotkey.keyCaption = 0
        }
      })
    },
    createData() {
      this.formatterSubmit();
      const pass = this.validateRepeatHotkey()
      if (pass) {
        saveHotkey(this.hotkeyList).then(res => {
          this.submitting = false
          getHotkeyList().then(res => {
            this.hotkeyList = deepMerge(this.hotkeyList, res.data)
          })
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.saveSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }
    },
    keyupHandle(e, number) {
      e.preventDefault()
      if (this.lowerkeyCodes.indexOf(e.key) >= 0 && this.keyCodes.indexOf(e.key) == -1) {
        this.keyCodes.push(e.key)
      }
      // F1-F12 112-123, 小键盘0-9 96-105, A-Z 65-90, 0-9 48-57
      if (e.keyCode >= 65 && e.keyCode <= 90) {
        this.highkey = e.key
        this.highkey = this.highkey.toUpperCase()
        this.hotkeyList[number].higherKey = e.keyCode
      } else if (e.keyCode >= 112 && e.keyCode <= 123 || e.keyCode >= 96 && e.keyCode <= 105 || e.keyCode >= 48 && e.keyCode <= 57) {
        this.highkey = e.key
        this.hotkeyList[number].higherKey = e.keyCode
      }
      if (this.keyCodes.indexOf('Control') >= 0) {
        this.remark += 'Ctrl+'
        this.lowkey += 2
      }
      if (this.keyCodes.indexOf('Alt') >= 0) {
        this.remark += 'Alt+'
        this.lowkey += 1
      }
      if (this.keyCodes.indexOf('Shift') >= 0) {
        this.remark += 'Shift+'
        this.lowkey += 4
      }
      this.hotkeyList[number].lowerKey = this.lowkey
      if (this.remark != '') {
        this.remark += this.highkey
      }
      if (this.remark == '' || this.highkey == '') {
        this.hotkeyList[number].remark = ''
        this.hotkeyList[number].lowerKey = 0
      } else {
        this.hotkeyList[number].remark = this.remark
        this.hotkeyList[number].lowerKey = this.lowkey
      }
      this.lowkey = 0
      this.remark = ''
    },
    keydownHandle(e, number) {
      e.preventDefault()
      this.lowkey = 0
      this.highkey = ''
      this.keyCodes.splice(0)
      this.remark = ''
      this.hotkeyList[number].lowerKey = 0
      this.hotkeyList[number].higherKey = 0
      this.hotkeyList[number].remark = ''
    },
    blurHandle() {
      this.validateRepeatHotkey()
    },
    radioChange() {
      this.validateRepeatHotkey()
    },
    validateRepeatHotkey() {
      let pass = true
      for (const hotkeyObj of this.hotkeyList) {
        hotkeyObj.toolTip = '' // 清空提示
        if (hotkeyObj.keyCaption === 1) { // 只校验按钮为使用热键的项
          const otherHotKeys = this.hotkeyList.filter((item) => {
            if (item.id == hotkeyObj.id) { // 排除自身项
              return false
            }
            // 排除按钮为默认热键及不使用热键的项，以及排除按钮为使用热键，但是值为空的情况（保存时会保存为默认热键）
            return !(item.keyCaption === 0 || item.keyCaption === 2 || (item.keyCaption === 1 && (item.remark === undefined || item.remark === '')));
          }).map((item) => {
            return item.remark
          })
          const otherDefaultHotKeys = this.hotkeyList.filter((item) => {
            // 只保留按钮为默认热键的项
            return item.keyCaption === 0
          }).map((item) => {
            return item.default
          })
          const curVal = hotkeyObj.remark
          if (otherDefaultHotKeys.includes(curVal)) {
            hotkeyObj.toolTip = this.$t('pages.sameDefaultHotkey')
            pass = false
          }
          if (otherHotKeys.includes(curVal)) {
            hotkeyObj.toolTip = this.$t('pages.sameHotkey')
            pass = false
          }
        }
      }
      return pass
    }
  }
}
</script>
<style lang="scss" scoped>
  .app-container{
    overflow: auto;
  }
  .save-btn-container{
    width: 700px;
    margin-top: 10px;
    text-align: right;
  }
</style>
