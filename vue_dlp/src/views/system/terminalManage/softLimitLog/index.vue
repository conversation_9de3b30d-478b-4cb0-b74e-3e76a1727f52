<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.softType" :value="query.softType">
          <span>{{ $t('pages.installText') }}：</span>
          <el-select v-model="query.softType" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in reportTypeOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <el-button slot="append" v-permission="'320'" :disabled="selection.length === 0" type="primary" size="mini" @click="handleImport">{{ $t('pages.installText1') }}</el-button>
        <audit-log-exporter slot="append" v-permission="'314'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'423'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd"/>
    </div>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.installText2')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="processForm" :rules="rules" :model="temp" label-position="right" label-width="70px" style="width: 650px">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.installText3')" prop="typeId">
              <el-select v-model="temp.typeId" filterable :placeholder="$t('text.select')" class="input-with-button">
                <el-option v-for="item in appTypeTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
              <el-button :title="$t('pages.addType')" class="editBtn" @click="handleAppTypeCreate"><svg-icon icon-class="add" /></el-button>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <el-button :disabled="batchEditRemarkAble" style="float: right;margin-top: 5px;" @click="batchUpdateRemark()">{{ $t('pages.installText18') }}</el-button>
          </el-col>
        </el-row>
        <grid-table
          ref="processTable"
          :col-model="processModel"
          :row-datas="processData"
          :multi-select="true"
          :height="350"
          :show-pager="false"
          style="margin-top: 10px;"
          @selectionChangeEnd="processSelectionChangeEnd"
        />
      </Form>
      <span slot="footer" class="dialog-footer" >
        <el-button type="primary" :loading="submitting" @click="importProcess()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.installText20')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="existDialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="existProcessForm" label-position="right" label-width="70px" style="width: 750px">
        <grid-table
          ref="existProcessTable"
          :col-model="existProcessModel"
          :row-datas="existProcessData"
          :multi-select="false"
          :height="350"
          :show-pager="false"
          style="margin-top: 10px;"
        />
      </Form>
      <span slot="footer" class="dialog-footer" >
        <el-button @click="existDialogVisible = false">{{ $t('button.close') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.installText18')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogBacthEditVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="batchRemarkForm" :model="temp1" label-position="right" label-width="80px">
        <FormItem :label="$t('pages.installText14')" prop="batchRemark">
          <el-input v-model="temp1.batchRemark" :maxlength="200"></el-input>
        </FormItem>
      </Form>
      <span slot="footer" class="dialog-footer" >
        <el-button type="primary" :loading="submitting1" @click="batchEditProcess()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogBacthEditVisible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.installText15')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogEditVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="editRemarkForm" :model="temp2" label-position="right" label-width="80px">
        <FormItem :label="$t('table.processExeName')" prop="processName">
          <el-input v-model="temp2.processName" :disabled="true"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.installText14')" prop="remark">
          <el-input v-model="temp2.remark" :maxlength="200"></el-input>
        </FormItem>
      </Form>
      <span slot="footer" class="dialog-footer" >
        <el-button type="primary" :loading="submitting2" @click="editProcess()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogEditVisible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.installText4')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.installText6')">
            {{ reportTypeFormatter(rowDetail, rowDetail.reportType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.productName')">
            {{ rowDetail.productName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.installText7')">
            {{ rowDetail.productVer }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.installText5')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.installText8')">
            {{ rowDetail.fileDesc }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <app-group-dlg
      ref="appGroupDlg"
      :os-type="osType"
      :append-to-body="false"
      :create="createType"
      :update="updateType"
      @submitEnd="groupSubmitEnd"
    />
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getPage, importProcess, isRepeatTerm, exportSoftLimitLog, deleteLog } from '@/api/behaviorAuditing/terminal/softLimitLog'
import { getTreeNode, createType, updateType } from '@/api/system/baseData/softPacket'
import AppGroupDlg from '@/views/system/baseData/appLibrary/appGroupDlg'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'SoftLimitLog',
  components: { AppGroupDlg },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchType: 'user', searchParam: 'userId' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'processName', label: 'processExeName', width: '200' },
        { prop: 'reportType', label: 'processType', width: '100', formatter: this.reportTypeFormatter },
        { prop: 'productName', label: 'productName', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('315'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      processModel: [
        { prop: 'processName', label: 'processExeName', width: '200' },
        { prop: 'remark', label: 'exeDesc', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('315'),
          buttons: [
            { label: 'edit', click: this.handleEditExeDesc }
          ]
        }
      ],
      existProcessModel: [
        { prop: 'processName', label: 'processExeName', width: '200' },
        { prop: 'existProcessName', label: 'processExeNameInLib', width: '200' },
        { prop: 'quicklyMd5', label: 'fingerprint', width: '300' }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        softType: '',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      temp: {
        typeId: undefined,
        processList: []
      },
      osType: 1,
      processName: [],
      submitting: false,
      submitting1: false,
      submitting2: false,
      selection: [],
      dialogVisible: false,
      dialogFormVisible: false,
      existDialogVisible: false,
      rowDetail: {},
      reportTypeOptions: {
        1: this.$t('pages.installText9'), 2: this.$t('pages.installText10')
      },
      showTree: true,
      appTypeTreeData: [],
      rules: {
        typeId: [
          { required: true, message: this.$t('pages.installText12'), trigger: 'change' }
        ]
      },
      batchRules: {
        batchRemark: [
          { required: true, message: this.$t('pages.installText19'), trigger: 'blur' }
        ]
      },
      sortable: true,
      processData: [],
      existProcessData: [],
      batchEditRemarkAble: true,
      dialogBacthEditVisible: false,
      dialogEditVisible: false,
      temp2: {
        id: '',
        processName: '',
        remark: ''
      },
      temp1: {
        batchRemark: ''
      },
      queryVideoMethod: undefined,
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  watch: {
    '$store.state.commonData.notice.updateInstallAppLog'(val) {
      if (this.dialogVisible) {
        this.loadAppTypeTree()
      }
    },
    appTypeTreeData(val) {
      // 已选择了类别
      if (this.temp.typeId) {
        const typeIdIndex = val.findIndex(item => item.dataId == this.temp.typeId)
        // 选中的类别被删除了
        if (typeIdIndex == -1) {
          this.$nextTick(() => {
            // 清除选中的值
            this.temp.typeId = undefined
          })
        }
      }
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    createType,
    updateType,
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    handleAppTypeCreate() {
      this.$refs.appGroupDlg.show()
    },
    // 新增分组数据后的回调
    groupSubmitEnd(data, dlgStatus) {
      // 通知安装/卸载库树刷新
      this.$store.dispatch('commonData/changeNotice', 'installAppLibGroup')
      const nodeData = {
        dataId: data.id.toString(),
        label: data.name,
        parentId: '0'
      }
      this.appTypeTreeData.push(nodeData)
      this.$nextTick(() => {
        this.temp.typeId = nodeData.dataId
      })
      this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getPage(searchQuery)
    },
    batchEditProcess() {
      this.submitting1 = true
      const selectData = this.$refs.processTable.getSelectedDatas()
      selectData.forEach(item => {
        item.remark = this.temp1.batchRemark
      })
      this.submitting1 = false
      this.dialogBacthEditVisible = false
      this.temp1.batchRemark = ''
    },
    editProcess() {
      this.submitting2 = true
      this.processData.forEach(item => {
        if (item.id == this.temp2.id) {
          item.remark = this.temp2.remark
        }
      })
      this.submitting2 = false
      this.dialogEditVisible = false
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp.typeId = undefined
    },
    batchUpdateRemark() {
      this.temp1.batchRemark = ''
      this.dialogBacthEditVisible = true
      // this.$refs.batchRemarkForm.clearValidate()
    },
    handleEditExeDesc(row) {
      this.temp2.id = row.id
      this.temp2.processName = row.processName
      this.temp2.remark = row.remark
      this.dialogEditVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
      this.temp.processList = [...rowDatas]
      if (rowDatas.length > 0) {
        this.processName.splice(0, this.processName.length)
        rowDatas.forEach(row => {
          if (this.processName.indexOf(row.processName) == -1) {
            this.processName.push(row.processName)
          }
        })
      }
    },
    processSelectionChangeEnd: function(datas) {
      datas.length == 0 ? this.batchEditRemarkAble = true : this.batchEditRemarkAble = false
    },
    handleImport() {
      // 添加至软件安装/卸载程序库，需要根据选中的数据，返回终端类型
      isRepeatTerm({ processList: this.temp.processList }).then(res => {
        if (res.data.length == 1) {
          // 修改终端类型
          this.osType = res.data[0]
          this.loadAppTypeTree()
          this.resetTemp()
          this.initProcessData()
          this.dialogVisible = true
        } else {
          this.$message({
            message: this.$t('pages.installText11'),
            type: 'error',
            duration: 2000
          })
          return
        }
        this.$nextTick(() => {
          this.$refs['processTable'] && this.$refs['processTable'].toggleAllSelection()
        })
      })
    },
    initProcessData() {
      // MD5相同的，只取一个
      this.temp.processList = [...this.$refs.logList.getSelectedDatas()]
      const obj = this.temp.processList.reduce((accumulator, currentValue) => {
        const existingItem = accumulator.find(item => item.quickMd5 === currentValue.quickMd5);
        if (!existingItem) {
          accumulator.push(currentValue);
        }
        return accumulator;
      }, []);
      var descript = {}
      this.processData = []
      obj.forEach(item => {
        // descript.id = item.id
        // descript.processName = item.processName
        descript = Object.assign({}, item)
        descript.remark = item.fileDesc
        this.processData.push(descript)
        descript = {}
      })

      this.$nextTick(() => {
      // 选中全部
        this.$refs.processForm.clearValidate()
        // this.$refs.processTable.toggleAllSelection()
        this.$refs.processTable.clearSelection()
        this.$refs.processTable.getSelectedDatas().length == 0 ? this.batchEditRemarkAble = true : this.batchEditRemarkAble = false
      })
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    async loadAppTypeTree() {
      // 根据终端类型获取数据
      await getTreeNode({ osType: this.osType == 7 ? 1 : this.osType }).then(respond => {
        this.appTypeTreeData = respond.data
      })
    },
    importProcess() {
      if (this.$refs.processTable.getSelectedDatas().length == 0) {
        this.$message({
          title: this.$t('text.error'),
          message: this.$t('pages.currentNoSelectedAnyData'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.submitting = true
      this.$refs['processForm'].validate((valid) => {
        if (valid) {
          this.temp.processList = [...this.$refs.processTable.getSelectedDatas()]
          importProcess(this.temp).then(res => {
            this.submitting = false
            this.dialogVisible = false
            if (res.data) {
              this.existProcessData.splice(0)
              this.existDialogVisible = true
              for (const key in res.data) {
                this.existProcessData.push({ processName: key, existProcessName: res.data[key][0].processName, quicklyMd5: res.data[key][0].quicklyMd5 })
              }
            } else {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.insertSuccess'),
                type: 'success',
                duration: 2000
              })
            }
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    reportTypeFormatter(row, data) {
      if (row.reportType === 1) {
        return this.$t('pages.installText9')
      } else if (row.reportType === 2) {
        return this.$t('pages.installText10')
      }
    },
    handleExport(exportType) {
      return exportSoftLimitLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '315', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
