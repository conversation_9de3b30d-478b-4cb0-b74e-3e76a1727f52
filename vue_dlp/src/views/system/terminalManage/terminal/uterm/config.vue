<template>
  <el-dialog
    v-el-drag-dialog
    :title="title"
    :close-on-click-modal="false"
    :modal="false"
    width="400px"
    :visible.sync="visible"
  >
    <Form :model="temp" label-position="right" label-width="80px">
      <FormItem :label="$t('pages.messageEncryption')">
        <el-switch v-model="temp.packetEncrypted"/>
      </FormItem>
      <FormItem :label="$t('pages.plugInHeartbeat')">
        <el-switch v-model="temp.heartbeatEnable"/>
      </FormItem>
      <FormItem :label="$t('pages.serverlog_level')">
        <el-select v-model="temp.logLevel">
          <el-option v-for="level in levels" :key="level" :value="level" :label="level"/>
        </el-select>
      </FormItem>
    </Form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="savePlugConfig">{{ $t('button.confirm') }}</el-button>
      <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { Level } from './client'

export default {
  name: 'UsbPlugConfig',
  data() {
    return {
      title: this.$t('text.highConfigInfo', { info: this.$t('pages.plugInCommunication') }),
      visible: false,
      temp: {
        packetEncrypted: false,
        heartbeatEnable: false,
        logLevel: Level.OFF.name
      },
      levels: Object.keys(Level)
    }
  },
  methods: {
    show(logLevel, packetEncrypted, heartbeatEnable) {
      this.temp.logLevel = logLevel
      this.temp.packetEncrypted = !!packetEncrypted
      this.temp.heartbeatEnable = !!heartbeatEnable
      this.visible = true
    },
    savePlugConfig() {
      this.$emit('change', this.temp)
      this.visible = false
    }
  }
}
</script>

