<template>
  <el-dialog
    v-el-drag-dialog
    append-to-body
    :title="$t('pages.downloadPlugin')"
    :close-on-click-modal="false"
    :modal="false"
    show-close
    width="600px"
    :visible.sync="visible"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <FormItem :label="$t('pages.downloadForm')">
        <el-radio-group v-model="temp.type">
          <el-radio :label="1">{{ $t('pages.downloadForm1') }}</el-radio>
          <el-radio v-if="addAuthDownloadAble" :label="2">{{ $t('pages.downloadForm2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <el-card v-if="temp.type === 2" :header="$t('pages.makePackage_text4')">
        <FormItem :label="$t('pages.makePackage_text5')" prop="userName">
          <el-input v-model="temp.userName" :maxlength="64"/>
          <el-tooltip :content="$t('pages.makePackage_text6')" effect="dark" placement="bottom-end">
            <i class="el-icon-info" style="color: rgb(104, 168, 208);"/>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.pwdInfo')" prop="userPassword">
          <el-input v-model="temp.userPassword" type="password" :maxlength="64" show-password></el-input>
        </FormItem>
      </el-card>
    </Form>

    <div slot="footer" class="dialog-footer">
      <common-downloader
        :loading="downloading"
        :name="filename"
        button-type="primary"
        button-icon=""
        @download="downloadPluginPkg"
      />
      <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSystemResources } from '@/utils/i18n'
import { downloadPlugin } from '@/api/system/terminalManage/uterm'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'UsbPlugDownloader',
  components: { CommonDownloader },
  props: {
    addAuthDownloadAble: {  //  是否支持添加用户权限下载
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      version: undefined,
      downloading: false,
      temp: {},
      defaultTemp: {
        type: 1,
        userName: undefined,
        userPassword: undefined
      },
      rules: {
        userPassword: [
          { validator: this.passwordRightValid, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    filename() {
      const systemTitle = getSystemResources('pluginName')
      // `数据泄露防护系统插件(V${this.version}).exe`
      return this.$t('pages.dlpPluginName', { systemTitle: systemTitle || '控制台插件', version: this.version || '1.01.230824.SC' })
    }
  },
  methods: {
    passwordRightValid(rule, value, callback) {
      if (this.temp.userName && !this.temp.userPassword) {
        callback(new Error(this.$t('pages.makePackage_text8')))
      } else {
        callback()
      }
    },
    show(version) {
      this.version = version
      this.temp = { ...this.defaultTemp }
      this.visible = true
    },
    downloadPluginPkg(file) {
      this.downloading = true
      const opts = { file, jwt: true, topic: 'uterm' }
      const data = this.temp.type === 1 ? undefined : this.temp
      data && Object.assign(data, { encryptProps: ['userPassword'] })
      return downloadPlugin(data, opts).then(() => {
        this.visible = false
        this.downloading = false
      }).catch(() => {
        this.downloading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-card {
    .el-input {
      width: 95%;
    }
    >>>.el-card__body {
      padding: 5px 10px;
    }
  }
</style>
