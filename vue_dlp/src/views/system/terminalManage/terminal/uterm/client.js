import i18n from '@/lang'
import moment from 'moment'
import { Base64 } from 'js-base64'

/**
 * 加密
 */
export function encode(obj) {
  const payload = typeof obj === 'string' ? obj : JSON.stringify(obj)
  return Base64.encode(Base64.encode(payload))
}

/**
 * 解密
 */
export function decode(str) {
  if (str == null || typeof str !== 'string' || str.length < 2) {
    return {}
  }
  if (str.charAt(0) === '{' && str.charAt(str.length - 1) === '}') {
    return JSON.parse(str)
  }
  return JSON.parse(Base64.decode(Base64.decode(str)))
}

/**
 * 日志打印级别
 */
export class Level {
  constructor(code, name, backgroundColor, fontColor) {
    this.code = code
    this.name = name
    this.backgroundColor = backgroundColor
    this.fontColor = fontColor
  }

  static DEBUG = new Level(1, 'DEBUG ', '#909399')
  static INFO = new Level(2, 'INFO ', '#41b883')
  static WARN = new Level(3, 'WARN ', '#E6A23C', '#5C3C00')
  static ERROR = new Level(4, 'ERROR', '#F56C6C', '#E10000')
  static OFF = new Level(5, 'OFF')

  static of(name) {
    if (name) {
      const level = Level[name.trim().toUpperCase()]
      if (level) {
        return level
      }
    }
    return Level.OFF
  }
}

/**
 * 日志记录器
 */
export class Logger {
  constructor(level) {
    this.level = level
  }

  log(level, ...messages) {
    if (!console || level.code < this.level.code) {
      return
    }
    console.log(`[%s] %c UTermPlugin %c ${level.name} %c%s`,
      moment().format('YYYY-MM-DD HH:mm:ss.SSS'),
      'background: #35495E; padding: 1px; border-radius: 3px 0 0 3px; color: #FFF;',
      `background: ${level.backgroundColor}; padding: 1px; border-radius: 0 3px 3px 0; color: #FFF;`,
      `background: transparent; margin-left: 5px; color: ${level.fontColor};`,
      ...messages)
  }
  info() {
    this.log(Level.INFO, ...arguments)
  }
  warn() {
    this.log(Level.WARN, ...arguments)
  }
  error() {
    this.log(Level.ERROR, ...arguments)
  }
}

export const protocols = {
  // 心跳
  GetPlugInfo: { name: 'GetPlugInfo', remark: '获取插件状态' },
  ListUsbInfo: { name: 'ListUsbInfo', remark: '获取本机U盘信息' },
  // 只更新U盘头信息
  UpdateUsbTerm: { name: 'UpdateUsbTerm', remark: '制作U盘终端' },
  // 不包含策略文件，只包含LdUterm.cab、LdUTerm.exe，打包成压缩包
  UpdateUsbTermFile: { name: 'UpdateUsbTermFile', remark: '更新U盘终端安装包文件' },
  // 只包含LdUterm.luf文件，打包成压缩包
  UpdateUsbPolicy: { name: 'UpdateUsbPolicy', remark: '更新U盘终端策略' },
  DeleteUsbTerm: { name: 'DeleteUsbTerm', remark: '卸载U盘终端' },
  UpdateDecryptionKey: { name: 'UpdateDecryptionKey', remark: '授权解密' },
  DeleteDecryptionKey: { name: 'DeleteDecryptionKey', remark: '解除解密授权' },
  ListMacAddress: { name: 'ListMacAddress', remark: '获取客户端MAC地址' },
  ListIPAddress: { name: 'ListIPAddress', remark: '获取客户端IPv4和IPv6' },
  ListUsbDisk: { name: 'ListUsbDisk', remark: '获取U盘信息' },
  ListFileAttribute: { name: 'ListFileAttribute', remark: '获取指定文件属性' },
  ScanDirFileList: { name: 'ScanDirFileList', remark: '获取指定路径下的文件夹和文件列表' },
  GeneTermInstallPackage: { name: 'GeneTermInstallPackage', remark: '生成终端安装包' }
}

/**
 * U盘终端插件通信客户端
 */
export class UTermPluginClient {
  #sn = 1
  #cache = {}
  #timer
  #notify

  /**
   * 获取插件通信端口
   */
  static getPort() {
    return localStorage.getItem('UTermPluginPort') || '22077'
  }

  static normalizeErrs(err) {
    if (typeof err === 'string' && err.startsWith('WebSocket')) {
      return i18n.t(`pages.uterm_${err.toLowerCase().replace('.', '_')}`)
    }
    if (err instanceof Error) {
      return err.message || err
    }
    return err
  }

  /**
   * U盘终端插件构造函数
   * @param vm Vue实例
   * @param port 插件通信断开
   * @param level 日志打印级别
   * @param encrypted 是否加密报文
   */
  constructor(vm, port = UTermPluginClient.getPort(), level = Level.OFF, encrypted = true) {
    this.vm = vm
    if (vm) {
      this.#notify = msg => vm.$notify({
        title: vm.$t('text.fail'),
        message: msg,
        type: 'error',
        duration: 2000
      })
    } else {
      this.#notify = msg => alert(msg)
    }
    this.port = port
    localStorage.setItem('UTermPluginPort', port)
    this.logger = new Logger(level)
    this.encrypted = !!encrypted
    this.connected = false
    this.connect()

    // 定时清理过期缓存任务
    const handleCacheTimeout = () => {
      if (this.#timer) {
        clearTimeout(this.#timer)
      }
      this.#timer = setTimeout(() => {
        this.clearCache()
        this.#timer = undefined
        handleCacheTimeout()
      }, 1)
    }
    handleCacheTimeout()
  }

  /**
   * 连接 WebSocket
   */
  connect() {
    this.connectedPromise = new Promise((resolve, reject) => {
      this.closed = false
      this.ws = new WebSocket('ws://localhost:' + this.port)
      this.ws.onopen = ev => {
        this.logger.info('WebSocket connect successfully!')
        this.connected = true
        resolve()
      }
      this.ws.onmessage = ev => {
        const message = this.encrypted ? decode(ev.data) : JSON.parse(ev.data)
        this.logger.info('WebSocket receive message: ', message)
        const executor = this.#cache[message.sn]
        if (executor && executor.name === message.name) {
          executor.onResponse(message.data, message.end)
          if (message.end === 1) {
            delete this.#cache[message.sn]
          }
        }
      }
      this.ws.onerror = ev => {
        this.logger.warn('WebSocket error.')
      }
      this.ws.onclose = ev => {
        if (this.closed) {
          return
        }
        this.closed = true
        this.logger.error('WebSocket close with code: ', ev.code)
        if (this.connected) {
          this.connected = false
        } else {
          this.logger.error('WebSocket connect failed，please check the plugin whether started！')
          reject('WebSocket.Failed')
        }
      }
    })
    return this.connectedPromise
  }

  /**
   * 发送消息
   * @param message 消息对象
   * @param onResponse 响应回调
   * @param onTimeout 超时回调
   * @param timeout 超时时间（单位：毫秒，默认1分钟；0表示不超时）
   * @returns {Promise}
   */
  send(message, onResponse, onTimeout, timeout = 60000) {
    const readyState = this.ws.readyState
    switch (readyState) {
      case WebSocket.CONNECTING:
        this.logger.warn('WebSocket is CONNECTING, please send message later.')
        return this.connectedPromise.then(() => this.send(message, onResponse, onTimeout, timeout))
      case WebSocket.OPEN:
        if (message.name === protocols.ListUsbInfo.name) {
          message.sn = 65535
        } else {
          message.sn = this.#sn++
          if (this.#sn > 65534) {
            this.#sn = 1
          }
        }
        this.#cache[message.sn] = { name: message.name, onResponse, onTimeout, timeout, create: Date.now() }
        this.logger.info('WebSocket send message: ', message)
        this.ws.send(this.encrypted ? encode(message) : JSON.stringify(message))
        return Promise.resolve()
      case WebSocket.CLOSING:
        this.logger.warn('WebSocket is CLOSING, can\'t send message.')
        // this.#notify('WebSocket.CLOSING')
        return Promise.reject('WebSocket.CLOSING')
      case WebSocket.CLOSED:
        this.logger.warn('WebSocket is CLOSED, please reconnect and send message later.')
        // this.#notify('WebSocket.CLOSED')
        return Promise.reject('WebSocket.CLOSED')
      default:
        this.logger.error('WebSocket readyState[' + readyState + '] is unsupported, can\'t send message.')
        // this.#notify('unsupported WebSocket readyState: ' + readyState)
        return Promise.reject('WebSocket.UnsupportedState')
    }
  }

  /**
   * 发送消息只获取一次应答（一问一答）
   * @param {Object} protocol 协议
   * @param {Object?} data 数据
   * @param {Number} timeout 超时时间（单位：毫秒，默认1分钟；0表示不超时）
   * @returns {Promise}
   */
  sendForOnceReply(protocol, data, timeout = 60000) {
    return new Promise((resolve, reject) => {
      this.send({ name: protocol.name, data }, resolve, reject, timeout).catch(reject)
    })
  }

  /**
   * 获取插件状态
   * @param timeout 超时时间
   * @returns {Promise}
   */
  getPlugInfo(timeout) {
    return this.sendForOnceReply(protocols.GetPlugInfo, timeout)
  }

  /**
   * 获取本机U盘信息
   * @param callback 回调
   * @param UsbDiskType U盘类型（0：U盘终端专用U盘（默认）；1：解密key专用U盘）
   * @returns {Promise}
   */
  listUsbInfo(callback, UsbDiskType = 0) {
    return this.send({ name: protocols.ListUsbInfo.name, data: { UsbDiskType }}, callback, null, 0)
  }

  /**
   * 制作U盘终端（只更新U盘头信息）
   * @param data U盘头参数
   * @returns {Promise}
   */
  createUsbTerm(data) {
    return this.sendForOnceReply(protocols.UpdateUsbTerm, data).then(res => {
      switch (res.status) {
        case 0:
          return Promise.resolve()
        case 1:
          return Promise.reject('U盘不存在')
        case 2:
          return Promise.reject('写U盘头失败')
        case 3:
          return Promise.resolve('U盘格式不支持')
        default:
          return Promise.reject('制作U盘终端失败：未知状态(' + res.status + ')')
      }
    })
  }

  /**
   * 更新U盘终端安装包文件（不包含策略文件，只包含LdUterm.cab、LdUTerm.exe，打包成压缩包）
   * @param data 安装包下载参数
   * @returns {Promise}
   */
  updateUsbTermFile(data) {
    return this.sendForOnceReply(protocols.UpdateUsbTermFile, data, 1000000).then(res => {
      switch (res.status) {
        case 0:
          return Promise.resolve()
        case 1:
          return Promise.reject('U盘不存在')
        case 2:
          return Promise.reject('下载文件失败')
        case 3:
          return Promise.reject('写文件失败')
        default:
          return Promise.reject('更新U盘终端安装包文件失败：未知状态(' + res.status + ')')
      }
    })
  }

  /**
   * 更新终端安装包文件
   * @param data 安装包下载参数
   * @returns {Promise}
   */
  geneTermInstallPackage(data) {
    return this.sendForOnceReply(protocols.GeneTermInstallPackage, data, 1000000).then(res => {
      switch (res.status) {
        case 0:
          return Promise.resolve()
        case 1:
          return Promise.reject('下载文件失败')
        case 2:
          return Promise.reject('写文件失败')
        default:
          return Promise.reject('更新终端安装包文件失败：未知状态(' + res.status + ')')
      }
    })
  }

  /**
   * 更新U盘终端策略（只包含LdUterm.luf文件，打包成压缩包）
   * @param data 策略下载参数
   * @returns {Promise}
   */
  updateUsbPolicy(data) {
    return this.sendForOnceReply(protocols.UpdateUsbPolicy, data, 500000).then(res => {
      switch (res.status) {
        case 0:
          return Promise.resolve()
        case 1:
          return Promise.reject('U盘不存在')
        case 2:
          return Promise.reject('下载文件失败')
        case 3:
          return Promise.reject('读写U盘头失败')
        case 4:
          return Promise.reject('写文件失败')
        default:
          return Promise.reject('更新U盘终端策略失败：未知状态(' + res.status + ')')
      }
    })
  }

  /**
   * 卸载U盘终端
   * @param UsbDiskId U盘编号
   * @param Guid U盘终端GUID
   * @returns {Promise}
   */
  deleteUsbTerm(UsbDiskId, Guid) {
    return this.sendForOnceReply(protocols.DeleteUsbTerm, { UsbDiskId, Guid }).then(res => {
      switch (res.status) {
        case 0:
          return Promise.resolve()
        case 1:
          return Promise.reject('U盘不存在')
        case 2:
          return Promise.reject('写U盘头失败')
        default:
          return Promise.reject('卸载U盘终端失败：未知状态(' + res.status + ')')
      }
    })
  }

  /**
   * 授权解密
   * @param data 终端参数
   * @param callback 回调
   * @returns {Promise}
   */
  updateDecryptionKey(data, callback) {
    return this.send({ name: protocols.UpdateDecryptionKey.name, data }, callback, null, 0)
  }

  /**
   * 解除解密授权
   * @param UsbDiskSerial U盘编号
   * @returns {Promise}
   */
  deleteDecryptionKey(UsbDiskSerial) {
    return this.sendForOnceReply(protocols.DeleteDecryptionKey, { UsbDiskSerial })
  }

  /**
   * 获取客户端MAC地址
   */
  listMacAddress() {
    return this.sendForOnceReply(protocols.ListMacAddress)
  }

  /**
   * 获取客户端IPv4和IPv6地址
   */
  listIpAddress() {
    return this.sendForOnceReply(protocols.ListIPAddress)
  }

  /**
   * 获取U盘信息
   */
  listUsbDisk() {
    return this.sendForOnceReply(protocols.ListUsbDisk)
  }

  /**
   * 获取指定文件属性
   */
  listFileAttribute(data, resolve, reject, timeout = 60000) {
    this.send({ name: protocols.ListFileAttribute.name, data }, resolve, reject, timeout).catch(reject)
  }

  /**
   * 获取指定路径下的文件夹和文件列表
   */
  scanDirFileList(data, resolve, reject, timeout = 60000) {
    this.send({ name: protocols.ScanDirFileList.name, data }, resolve, reject, timeout).catch(reject)
  }

  /**
   * 清理缓存
   * @param notOnlyExpired 不仅仅清理过期缓存
   */
  clearCache(notOnlyExpired = false) {
    if (notOnlyExpired) {
      this.#cache = {}
      return
    }
    const now = Date.now()
    const keys = [...Object.getOwnPropertyNames(this.#cache)]
    const timeoutTip = i18n.t('text.requestTimeout')
    for (let i = 0; i < keys.length; i++) {
      const value = this.#cache[keys[i]]
      if (value.timeout === 0) {
        continue
      }
      if (now - value.create > value.timeout) {
        delete this.#cache[keys[i]]
        const timeoutMsg = protocols[value.name].remark + ': ' + timeoutTip
        this.logger.warn(timeoutMsg)
        const timeoutCallback = value.onTimeout || this.#notify
        timeoutCallback(timeoutMsg)
      }
    }
  }

  /**
   * 设置日志级别
   * @param {Level} level 日志级别
   */
  setLevel(level) {
    this.logger.level = level
  }

  /**
   * 关闭
   */
  close() {
    this.logger.warn('WebSocket closed actively')
    this.closed = true
    if (this.ws) {
      this.ws.close()
      this.ws = undefined
    }
    this.connected = false
    this.vm = undefined
    this.#notify = undefined
    if (this.#timer) {
      clearTimeout(this.#timer)
      this.#timer = undefined
    }
    this.clearCache(true)
    this.#cache = undefined
  }
}
