<template>
  <div class="pkg-upload">
    <el-upload v-show="btnShow" action="" accept=".exe" :before-upload="beforeUpload">
      <el-tooltip :content="tips" placement="top">
        <el-button ref="submitBtn" size="mini" type="primary" icon="el-icon-upload" :loading="loading">{{ $t('pages.uploadInstallPackage') }}</el-button>
      </el-tooltip>
    </el-upload>
  </div>
</template>

<script>
import { readAsArrayBuffer } from '@/utils/blob'
import { uploadUTermPkg, compareVersion, decodeInstallPkgJsonStr } from '@/api/system/terminalManage/uterm'

export default {
  name: 'PackageUpload',
  model: {
    prop: 'version',
    event: 'change'
  },
  props: {
    version: {
      type: String,
      default: undefined
    },
    tips: {
      type: String,
      required: true
    },
    //  按钮是否默认显示
    btnShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      sizeLimit: 1024 * 1024,
      pkgHeader: '~~~~~~~~LeaderCab~~~~~~~~',
      loading: false,
      percent: undefined,
      progressTimer: undefined
    }
  },
  methods: {
    // 外部触发提交
    triggerSubmit() {
      this.$refs.submitBtn.$el.click(); // 通过 ref 获取按钮并触发点击
    },
    beforeUpload(file) {
      this.loading = true
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = undefined
      }
      if (file.size < this.sizeLimit) {
        this.notifyErr(file.size === 0 ? this.$t('pages.installPackageFileSizeCantZero') : this.$t('pages.invalidInstallationPackage'))
        return false
      }
      const blob = file.slice(-10240)
      readAsArrayBuffer(blob).then(async buffer => {
        const bytes = new Uint8Array(buffer)
        let header = this.decodeUnicodeLittle(bytes.subarray(-2048, -2048 + 50))
        if (header === this.pkgHeader) {
          const len = this.calcJsonLen(bytes, -2048 + 50)
          const jsonStr = this.decodeUnicodeLittle(bytes.subarray(-2048 + 54, -2048 + 54 + len))
          this.compareVerAndUploadExe(jsonStr, file)
          return
        }
        header = this.decodeUnicodeLittle(bytes.subarray(-50))
        if (header === this.pkgHeader) {
          //  json长度
          const len = this.calcJsonLen(bytes, -54)
          //  json字符串
          let jsonStr = this.decodeUnicodeLittle(bytes.subarray(-(len + 54), -54))
          //  获取是否存在version
          const version = this.calcVersion(bytes, -56 - len);
          if (version === 1) {
            //  加密类型
            const encryptType = this.calcVersion(bytes, -58 - len);
            //  密钥类型
            const keyType = this.calcVersion(bytes, -60 - len);
            //  调用接口，传入加密的jsonBytes和加密类型，密钥类型，得到解密后的jsonStr
            const jsonBytes = bytes.subarray(-(len + 54), -54);
            const res = await decodeInstallPkgJsonStr({ jsonBytes: Array.from(jsonBytes), encryptType, keyType });
            if (res.code === 20000) {
              jsonStr = res.data
            }
          }
          this.compareVerAndUploadExe(jsonStr, file)
          return
        }
        this.notifyErr(this.$t('pages.installPackageNoVersionInfo'))
      }).catch(e => {
        console.error('终端安装包读取失败', e)
        this.notifyErr(this.$t('pages.installPackageVersionInfoReadFailed'))
      })
      return false
    },
    compareVerAndUploadExe(jsonStr, file) {
      let jsonObj
      try {
        jsonObj = JSON.parse(jsonStr)
      } catch (e) {
        this.notifyErr(this.$t('pages.installPackageVersionInfoParsingFailed'))
        console.error('终端安装包json解析失败', jsonStr, e)
        return
      }
      // productType 安装包产品类型
      // 0x01-客户端
      // 0x02-服务端
      // 0x03-报表
      // 0x04-插件
      if (jsonObj.productType !== 1) {
        this.notifyErr('安装包类型错误，请选择正确的终端安装包重新上传')
        return
      }
      const exeVer = jsonObj.version
      const cmp = compareVersion(exeVer, this.version)
      if (cmp > 0) {
        this.uploadExeFile(file)
      } else {
        const msg = cmp < 0 ? this.$t('pages.installPackageReplacedMsg') : this.$t('pages.installPackageSameVersionOnTheServerMsg')
        this.$confirmBox(this.$t('pages.installPackageUploadVersionMsg', { msg: msg }), this.$t('text.prompt')).then(() => {
          this.uploadExeFile(file)
        }).catch(() => {
          this.loading = false
        })
      }
    },
    uploadExeFile(file) {
      this.percent = 0
      this.$emit('upload', this.percent)
      return uploadUTermPkg(file, progressEvent => {
        const percent = Math.floor((progressEvent.loaded / progressEvent.total) * 50)
        if (percent <= this.percent) {
          return
        }
        this.percent = percent
        this.$emit('upload', this.percent)
        if (this.percent === 50) {
          this.progressTimer = setInterval(() => {
            if (this.percent < 99) {
              this.percent++
              this.$emit('upload', this.percent)
            } else {
              clearInterval(this.progressTimer)
              this.progressTimer = undefined
            }
          }, 200)
        }
      }).then(data => {
        this.$emit('change', data.fileVer)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.uDiskTerminalInstallPackageUploadSucceeded'),
          type: 'success',
          duration: 2000
        })
      }).finally(() => {
        this.percent = 100
        this.$emit('upload', this.percent)
        this.loading = false
      })
    },
    decodeUnicodeLittle(bytes) {
      const codes = []
      for (let i = 0; i < bytes.length;) {
        const code = bytes[i++] + bytes[i++] * 256
        if (code > 0) {
          codes.push(code)
        }
      }
      return String.fromCharCode(...codes)
    },
    calcJsonLen(bytes, offset) {
      if (offset < 0) {
        offset = bytes.length + offset
      }
      let uint8 = 0
      for (let i = 0; i < 4; i++) {
        uint8 |= (bytes[offset + i] & 0xff) << (i * 8)
      }
      return uint8
    },
    calcVersion(bytes, offset) {
      if (offset < 0) {
        offset = bytes.length + offset
      }
      let uint8 = 0
      for (let i = 0; i < 2; i++) {
        uint8 |= (bytes[offset + i] & 0xff) << (i * 8)
      }
      return uint8
    },
    notifyErr(error) {
      this.loading = false
      this.$notify({
        title: this.$t('text.fail'),
        message: error,
        type: 'error',
        duration: 3000
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .pkg-upload {
    display: inline-block;
  }
</style>
