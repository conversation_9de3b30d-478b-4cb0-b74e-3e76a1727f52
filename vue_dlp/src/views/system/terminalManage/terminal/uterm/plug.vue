<template>
  <div v-loading="loading" class="usb-plug-info">
    <div v-if="connected" class="plugin-check-box plugin-check-ok">
      <div class="plugin-status is-ok">
        <i class="el-icon-success"></i>
        <span>{{ $t('pages.pluginNormal') }}</span>
      </div>
      <div class="cfg-item">
        <label>{{ $t('pages.pluginVersion') }}</label>
        <span>{{ version }}</span>
      </div>
      <div v-show="isNotifyUpgrade">
        <span>{{ this.$t('pages.pluginNewVersionMsg', { info: latestVersion }) }}</span>
        <el-button type="text" @click="downloadPluginPkg">{{ $t('pages.downloadPlugin') }}</el-button>
      </div>
    </div>
    <div v-else class="plugin-check-box">
      <div class="plugin-status is-err">
        <i class="el-icon-error"></i>
        <span v-if="interrupted">{{ $t('pages.pluginConnectionInterrupted') }}</span>
        <span v-else>{{ $t('pages.pluginConnectionFailed') }}</span>
      </div>
      <!--<div v-if="interrupted">
        请检查插件运行状况
      </div>-->
      <div v-if="customDesc">
        <slot name="customDescSlot"></slot>
      </div>
      <div v-else>
        <div>
          <i18n path="pages.pluginRunStatusMsg1">
            <el-button slot="button" type="text" @click="downloadPluginPkg">{{ $t('pages.downloadPlugin') }}</el-button>
          </i18n>
        </div>
        <div>
          <i18n path="pages.pluginRunStatusMsg2">
            <el-tooltip slot="icon" effect="light" placement="top-start" :visible-arrow="false">
              <img slot="content" :src="iconSrc" :alt="iconAlt">
              <img class="plugin-icon" :src="iconSrc" :alt="iconAlt">
            </el-tooltip>
          </i18n>
        </div>
        <div>
          {{ this.$t('pages.pluginRunStatusMsg3') }}
        </div>
        <div>
          {{ this.$t('pages.pluginRunStatusMsg4') }}
        </div>
      </div>
      <el-row :gutter="gutter">
        <el-col :span="span">
          <FormItem :label="$t('pages.pluginPort')" :error="error" required>
            <el-input v-model="port" maxlength="5" @blur="handleBlur" @input="handleInput" @keyup.enter.native="testConnect"/>
          </FormItem>
        </el-col>
        <el-col :span="span">
          <el-button type="primary" size="mini" @click="testConnect">{{ $t('pages.reconnect') }}</el-button>
        </el-col>
      </el-row>
    </div>

    <usb-plug-downloader ref="downloader"/>
  </div>
</template>

<script>
import { UTermPluginClient } from './client'
import UsbPlugDownloader from './downloader'
import { getPluginVersion, compareVersion } from '@/api/system/terminalManage/uterm'

export default {
  name: 'UsbPlugInfo',
  components: { UsbPlugDownloader },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    /**
     * 是否连接成功
     */
    connected: {
      type: Boolean,
      default: false
    },
    /**
     * 连接是否中断
     */
    interrupted: {
      type: Boolean,
      default: false
    },
    version: {
      type: String,
      default: undefined
    },
    gutter: {
      type: Number,
      default: 2
    },
    span: {
      type: Number,
      default: 12
    },
    //  自定义描述信息,搭配customDescSlot插槽使用
    customDesc: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      iconSrc: require('@/assets/uterm_plug.png'),
      iconAlt: this.$t('pages.pluginIcon'),
      port: undefined,
      error: undefined,
      latestVersion: undefined
    }
  },
  computed: {
    isNotifyUpgrade() {
      if (this.version && this.latestVersion) {
        return compareVersion(this.version, this.latestVersion) < 0
      }
      return false
    }
  },
  created() {
    this.port = UTermPluginClient.getPort()
    getPluginVersion().then(ver => {
      this.latestVersion = ver
    })
  },
  methods: {
    downloadPluginPkg() {
      this.$refs.downloader.show(this.latestVersion)
    },
    handleBlur() {
      if (this.port) {
        this.error = undefined
      } else {
        this.error = this.$t('text.cantNullInfo', { info: this.$t('pages.portNumber') })
      }
    },
    handleInput(value) {
      if (value.length === 0) {
        return
      }
      let correctiveValue = ''
      let code
      for (let i = 0; i < value.length; i++) {
        code = value.charCodeAt(i)
        // 只能输入数字0~9
        if (code >= 48 && code <= 57) {
          correctiveValue += value.charAt(i)
        }
      }
      if (correctiveValue.length > 0) {
        this.error = undefined
        const port = parseInt(correctiveValue)
        if (port > 65535) {
          this.port = '65535'
          return
        }
      }
      this.port = correctiveValue
    },
    testConnect() {
      if (!this.port) {
        this.error = this.$t('text.cantNullInfo', { info: this.$t('pages.portNumber') })
        return
      }
      this.$emit('connect', this.port)
    },
    getPort() {
      return this.port
    }
  }
}
</script>

<style lang="scss" scoped>
  .is-ok {
    color: #36ab60;
  }
  .is-err {
    color: #f56c6c;
  }
  .plugin-check-ok {
    padding-left: 15px;
    div {
      display: inline-block;
    }
    .cfg-item {
      margin-left: 20px;
      margin-right: 50px;
    }
  }
  .plugin-check-box {
    div {
      min-height: 30px;
      line-height: 30px;
    }
    .el-button.el-button--text {
      margin-bottom: 3px;
    }
  }
  .plugin-status {
    font-weight: bold;
    /*font-size: 16px;*/
  }
  .cfg-item {
    height: 30px;
    line-height: 30px;
    label {
      display: inline-block;
      width: 120px;
      text-align: right;
      &:after {
        content: ":";
      }
    }
    .el-button {
      margin-bottom: 0;
    }
  }
  .plugin-icon {
    width: 18px;
    position: relative;
    top: 3px;
  }
  .el-col {
    .el-button {
      margin-left: 16px;
    }
  }
</style>
