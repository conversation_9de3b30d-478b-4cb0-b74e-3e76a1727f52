<template>
  <el-popover placement="top-end" trigger="click" :disabled="disabled">
    <div v-show="dataList.passwordLength">
      <i v-show="dataList.failLength" class="el-icon-close red bolder"/>
      <i v-show="dataList.successLength" class="el-icon-check green bolder"/>
      <span>{{ this.$t('pages.sysUserPasswordMsg1',{passwordLength: passwordLength}) }}</span>
    </div>
    <div v-show="dataList.passwordLevel">
      <i v-show="dataList.failLevel" class="el-icon-close red bolder"/>
      <i v-show="dataList.successLevel" class="el-icon-check green bolder"/>
      <span>{{ this.$t('pages.sysUserPasswordMsg2',{passwordLevel: dataList.passwordLevel}) }}</span>
    </div>
    <el-input
      slot="reference"
      :value="value"
      :type="type"
      :minlength="minlength"
      :maxlength="maxlength"
      :class="inputClass"
      :disabled="disabled"
      :clearable="clearable"
      :show-password="showPassword"
      @input="handleInput"
    />
  </el-popover>
</template>

<script>
import { getConfig } from '@/api/system/configManage/globalConfig'
import { validatePassword } from '@/utils/validate'
import { visibleAsciiWithoutSpace } from '@/utils/inputLimit'

export default {
  name: 'PasswordInput',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      default: undefined
    },
    inputClass: {
      type: String,
      default: undefined
    },
    type: {
      type: String,
      default: 'text'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    showPassword: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataList: {
        passwordLevel: '',
        passwordLength: '',
        successLength: false,
        failLength: false,
        successLevel: false,
        failLevel: false
      },
      passwordLength: '',
      minlength: undefined,
      maxlength: 64
    }
  },
  created() {
    this.getPasswordConfig()
  },
  methods: {
    getPasswordConfig() {
      getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'passwordLevel') {
            this.dataList.passwordLevel = item.value
          } else if (item.key === 'passwordLength') {
            this.dataList.passwordLength = item.value
            const lens = item.value.split('-')
            if (lens.length > 0) {
              this.minlength = parseInt(lens[0])
              this.maxlength = parseInt(lens[1])
              if (this.minlength === this.maxlength) {
                this.passwordLength = this.minlength
                return
              }
            }
            this.passwordLength = item.value
          }
        }
      })
    },
    handleInput(value) {
      this.$emit('input', visibleAsciiWithoutSpace(value))
    },
    validate() {
      this.dataList = validatePassword(null, this.value || '', this.dataList)
      return !(this.dataList.failLength || this.dataList.failLevel)
    }
  }
}
</script>
