<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.uDiskTerminalManagement')"
      width="800px"
      :visible.sync="visible"
      @close="handleClose"
    >
      <Form ref="usbForm" class="usb-term-form" :model="temp" :rules="rules" label-position="right" label-width="90px">
        <el-divider content-position="left">
          {{ $t('pages.pluginStatus') }}
          <el-button class="plug-cfg-btn" type="text" icon="el-icon-s-tools" @click="handlePlugConfig"/>
        </el-divider>
        <usb-plug-info
          ref="upi"
          :loading="loading"
          :connected="pluginWorkOk"
          :interrupted="pluginInterrupted"
          :version="pluginWorkVer"
          :gutter="gutter"
          :span="span"
          @connect="testConnect"
        />
        <el-divider content-position="left">{{ $t('pages.uDiskInfo') }}</el-divider>
        <usb-disk-info
          ref="udi"
          v-model="usbDiskId"
          :disks="usbDiskList"
          :disabled="!pluginWorkOk"
          :gutter="gutter"
          :span="span"
          @refresh="listUsbInfo"
          @select="selectUsbDisk"
        />
        <usb-term-select
          :disk="usbDisk"
          :group-tree-data="groupTreeData"
          :gutter="gutter"
          :span="span"
          @add="handleAddTerm"
          @change="handleTermChange"
        />
        <el-divider content-position="left">{{ $t('pages.policyValidity') }}</el-divider>
        <el-row :gutter="gutter">
          <el-col v-for="(item, key) in validTime" :key="key" :span="span">
            <FormItem :label="item.label" :prop="key">
              <el-date-picker
                v-model="temp[key]"
                :clearable="false"
                :editable="false"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :disabled="!pluginWorkOk || !hasGroupPermission"
                :picker-options="item.options"
                :placeholder="item.placeholder"
                @change="item.change"
              />
            </FormItem>
          </el-col>
        </el-row>
        <div v-permission="'A22'" class="usb-strategy-tips">
          <span>{{ $t('pages.uDiskStrategyTips1') }}{{ $t('pages.uDiskStrategyTips2') }}</span>
          <el-button type="mini" :disabled="moduleDisabled" @click="allocateModules()">{{ $t('route.moduleConfig') }}</el-button>
          <br>
          <!--<span><strong>注意：</strong>重新分配模块后将自动更新U盘终端的策略</span>-->
        </div>
        <div v-permission="'!A22'" class="usb-strategy-tips">
          <span>{{ $t('pages.uDiskStrategyTips1') }}</span>
        </div>
        <FormItem v-if="modifiable" label-width="0">
          <el-checkbox v-model="temp.updatePwd" :true-label="1" :false-label="0">
            {{ $t('pages.uDiskTerminalUpdatePwdMsg') }}
            <el-tooltip class="item" effect="dark" placement="right" :content="$t('pages.passwordNotInputSpace')">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </FormItem>
        <FormItem v-if="creatable" label-width="0">
          <el-checkbox v-model="temp.needPwd" :true-label="1" :false-label="0">
            {{ $t('pages.uDiskTerminalNeedPwdMsg') }}
            <el-tooltip class="item" effect="dark" placement="right" :content="$t('pages.passwordNotInputSpace')">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </FormItem>
        <el-row v-if="(creatable && temp.needPwd) || (modifiable && temp.updatePwd)" :gutter="gutter">
          <el-col :span="span">
            <FormItem :label="$t('form.password')" prop="password">
              <password-input v-if="visible" ref="pwdinput" v-model="temp.password" input-class="pwd-clr" type="password" show-password clearable/>
            </FormItem>
          </el-col>
          <el-col :span="span">
            <FormItem :label="$t('form.confirmPassword')" prop="confirmPwd">
              <el-input v-model="temp.confirmPwd" class="pwd-clr" type="password" show-password clearable @input="handleConfirmPwdInput"/>
            </FormItem>
          </el-col>
        </el-row>

        <el-divider content-position="left">{{ '安装包有效期' }}</el-divider>
        <el-row>
          <el-col :span="24">
            <FormItem label="截至有效期" prop="codeEndTime">
              <el-date-picker
                v-model="temp.codeEndTime"
                :disabled="!!temp.isPermanent"
                :class="{'date-picker-disabled':temp.isPermanent}"
                :picker-options="setEndTimeRange()"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width: 300px;"
              />
              <el-checkbox v-model="temp.isPermanent" :true-label="1" :false-label="0" style="padding-left: 20px" @change="isPermanentChange">
                {{ $t('pages.permanentEffective') }}
              </el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
      </Form>

      <div slot="footer" class="dialog-footer">
        <div class="btn-gen-lic">
          <!-- 上传安装包迁移到【安装包制作】界面 -->
<!--          <package-upload v-show="pkgUploadEnable" v-model="pkgVer" :tips="pkgVerTips" @upload="handlePkgUpload"/>-->
          <el-button type="primary" @click="openPwdLicDlg">{{ $t('pages.generateResetPasswordFile') }}</el-button>
          <el-tooltip :content="$t('pages.uDiskTerminalPasswordMsg')" placement="right">
            <i class="el-icon-info"/>
          </el-tooltip>
        </div>
        <el-tooltip v-show="creatable" :content="pkgVerTips" placement="top">
          <el-button type="primary" :disabled="updateDisabled || diskUnsupported" @click="makeUTerm">{{ $t('pages.productionTerminal') }}</el-button>
        </el-tooltip>
        <el-button v-show="deletable" v-permission="'103'" :disabled="!hasGroupPermission" type="primary" @click="handleDelete">{{ $t('pages.agreementType9') }}</el-button>
        <el-tooltip v-show="modifiable" :content="pkgVerTips" placement="top">
          <el-button type="primary" :disabled="updateDisabled" @click="upgradeUTerm">{{ $t('pages.upgradeTerminal') }}</el-button>
        </el-tooltip>
        <el-button v-show="modifiable" type="primary" :disabled="updateDisabled" @click="updateTermStg">{{ $t('pages.updatePolicy') }}</el-button>
        <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
      </div>

      <usb-plug-config ref="plugCfg" @change="savePlugConfig"/>
      <delete-usb-term ref="del" @delete="deleteUTerm"/>
      <el-dialog
        :title="progressTitle"
        :close-on-click-modal="false"
        :modal="false"
        :show-close="progressShowClose"
        width="600px"
        :visible.sync="progressVisible"
      >
        <el-progress type="line" :text-inside="true" :stroke-width="15" :percentage="percent" style="height: 40px;"/>
      </el-dialog>
    </el-dialog>

    <password-licence ref="pwdLic"/>
    <bind-user-dlg2 ref="bindUsrDlg"/>
    <module-edit ref="moduleEdit" @change="handleModuleChange"/>
  </div>
</template>

<script type="text/jsx">
import { Tooltip } from 'element-ui'
import { decode, Level, Logger, UTermPluginClient } from './client'
import UsbPlugConfig from './config'
import UsbPlugInfo from './plug'
import UsbDiskInfo from './disk'
import UsbTermSelect from './term'
import DeleteUsbTerm from './delete'
import PasswordInput from './pwdinput'
import PasswordLicence from './password'
import PackageUpload from './upload'
import BindUserDlg2 from '../bindUserDlg2'
import ModuleEdit from '@/views/system/terminalManage/moduleConfig/edit'
import md5 from '@/utils/md5'
import {
  buildDownloadUrl,
  compareVersion,
  deleteUTerm,
  generateUsbGuid,
  getServerTime,
  getUTermVersion,
  makeUTerm,
  preUpgradeUTermPkg,
  updateStrategy,
  upgradeUTermPkg
} from '@/api/system/terminalManage/uterm'
import { getTerminalAccessVersion } from '@/api/system/terminalManage/approvalAccess'
import moment from 'moment'
import { loginModeFormatter } from '@/utils/formatter'
import { notSpace } from '@/utils/inputLimit'
import { aesEncode, formatAesKey } from '@/utils/encrypt'
import { getMainCode, recordInstallPackageLog } from '@/api/system/installPkgManage/installPkg';
import { parseTime } from '@/utils';
import { getUserNo } from '@/api/system/register/reg';

export default {
  name: 'UsbTerm',
  components: {
    UsbPlugConfig,
    UsbPlugInfo,
    UsbDiskInfo,
    UsbTermSelect,
    DeleteUsbTerm,
    PasswordInput,
    PasswordLicence,
    PackageUpload,
    BindUserDlg2,
    ModuleEdit
  },
  props: {
    groupTreeData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      gutter: 2,
      span: 12,
      making: false, // 正在制作U盘终端
      visible: false,
      loading: false,
      client: undefined,
      logLevel: Level.WARN.name,
      packetEncrypted: true, // 插件报文是否加密
      pluginWorkOk: false, // 插件工作状态是否正常
      pluginWorkVer: undefined, // 当前插件版本
      pluginInterrupted: false, // 插件连接中断
      heartbeatTimer: undefined, // 心跳计时器
      heartbeatEnable: true, // 是否启用心跳检测
      heartbeatNotified: false, // 心跳错误是否已通知
      reconnectCount: 0, // 重连次数
      usbDiskId: undefined,
      usbDisk: undefined,
      usbDiskList: [],
      outOfFreeSpace: false,  // 剩余空间不足
      diskUnsupported: false,  // U盘文件系统或分区格式不支持
      testUserMK: md5(''),
      mainKey: undefined,
      pkgVer: undefined,
      pkgUploadEnable: false,
      hasGroupPermission: true,
      temp: {},
      defaultTemp: {
        termId: undefined,
        termName: undefined,
        type: undefined,
        usbGuid: undefined,
        beginTime: undefined,
        endTime: undefined,
        codeEndTime: undefined,   //  安装码截止时间
        isPermanent: 1        //  是否永久有效
      },
      defaultPwdTemp: {
        updatePwd: 0,
        needPwd: 0,
        password: undefined,
        confirmPwd: undefined
      },
      validTime: {
        beginTime: {
          label: this.$t('pages.startTime'),
          placeholder: this.$t('pages.selectDateTime'),
          options: {},
          change: this.handleBeginTimeChange
        },
        endTime: {
          label: this.$t('pages.endTime'),
          placeholder: this.$t('pages.selectDateTime'),
          options: {},
          change: this.handleEndTimeChange
        }
      },
      rules: {
        beginTime: [{ required: true, trigger: 'blur', message: this.$t('pages.policyValidityBeginTimeCantNull') }],
        endTime: [{ required: true, trigger: 'blur', message: this.$t('pages.policyValidityEndTimeCantNull') }],
        password: [{ required: true, trigger: 'blur', validator: this.passwordValidator }],
        confirmPwd: [{ required: false, trigger: 'blur', validator: this.confirmPasswordValidator }],
        codeEndTime: [{ trigger: 'blur', validator: this.codeEndTimeValidator }]
      },
      percent: 0,
      progressTimer: undefined,
      progressTitle: undefined,
      progressVisible: false,
      progressShowClose: false,
      progressCancelTimer: undefined,
      osTypes: { 0: 'win', 1: 'linux', 2: 'mac', 3: 'phone' },
      userNo: null  //  用户编号
    }
  },
  computed: {
    // 心跳间隔
    heartbeatInterval() {
      if (this.reconnectCount < 12) {
        return 5000
      }
      if (this.reconnectCount < 24) {
        return 10000
      }
      if (this.reconnectCount < 36) {
        return 15000
      }
      if (this.reconnectCount < 54) {
        return 30000
      }
      if (this.reconnectCount < 69) {
        return 60000
      }
      return 300000
    },
    // 可制作U盘终端
    creatable() {
      return !!this.usbDisk && !this.usbDisk.UsbDiskStatus
    },
    // 可卸载U盘终端
    deletable() {
      return !!(this.usbDisk && this.usbDisk.UsbDiskStatus && this.temp.usbGuid)
    },
    // 可更新U盘终端
    modifiable() {
      return !!(this.deletable && this.temp.termId)
    },
    // 是否禁用更新
    updateDisabled() {
      return this.outOfFreeSpace || !this.hasGroupPermission
    },
    moduleDisabled() {
      return !this.pluginWorkOk || !this.usbDisk || !this.temp.termId || !this.hasGroupPermission
    },
    pkgVerTips() {
      if (this.pkgVer) {
        return this.$t('pages.serverInstallPackageVersion', { info: this.pkgVer })
      }
      return this.$t('pages.serverInstallPackageNotExitsts')
    }
  },
  watch: {
    progressVisible(value) {
      if (!value && this.progressCancelTimer) {
        clearTimeout(this.progressCancelTimer)
        this.progressCancelTimer = undefined
      }
    },
    creatable: {
      immediate: true,
      handler(value) {
        if (value) {
          this.temp = { ...this.temp, ...this.defaultPwdTemp }
        }
        this.rules.password[0].required = value
      }
    },
    logLevel() {
      if (this.logLevel && this.client) {
        this.client.setLevel(Level.of(this.logLevel))
      }
    },
    packetEncrypted(encrypted) {
      if (this.client) {
        this.client.encrypted = !!encrypted
      }
    },
    heartbeatEnable(enable) {
      if (enable) {
        this.heartbeat()
        return
      }
      if (this.heartbeatTimer) {
        clearTimeout(this.heartbeatTimer)
        this.heartbeatTimer = undefined
      }
    }
  },
  created() {
    this.logger = new Logger(Level.WARN)
  },
  deactivated() {
    this.logger.warn('UsbTerm component is deactivated.')
  },
  destroyed() {
    this.logger.warn('UsbTerm component is destroyed.')
    this.handleClose()
  },
  methods: {
    show() {
      this.handleClose()
      this.getUserNo()
      this.resetDefaultValidTime()
      getUTermVersion().then(decode).then(data => {
        this.mainKey = data.mainKey
        this.pkgVer = data.pkgVer
        this.pkgUploadEnable = data.hasGroupPermission
        this.temp = { ...this.defaultTemp, ...this.defaultPwdTemp }
        this.visible = true
        this.$nextTick(() => {
          this.$refs['usbForm'].clearValidate()
        })
        this.connect()
      })
    },
    /**
     * 获取用户编号
     */
    getUserNo() {
      getUserNo().then(res => {
        this.userNo = res.data
      })
    },
    resetDefaultValidTime() {
      const date = new Date()
      this.defaultTemp.beginTime = moment(date).format('YYYY-MM-DD') + ' 00:00:00'
      this.defaultTemp.endTime = moment(date).add(7, 'days').format('YYYY-MM-DD') + ' 23:59:59'
    },
    connect(port) {
      this.loading = true
      this.client = new UTermPluginClient(this, port, Level.of(this.logLevel), this.packetEncrypted)
      return this.client.getPlugInfo().then(data => {
        this.loading = false
        this.pluginWorkOk = data.PlugWorkType === 0
        this.pluginWorkVer = data.PlugVersion
        if (this.pluginWorkOk) {
          this.listUsbInfo()
          this.client.reconnected = true
          this.heartbeatNotified = false
        }
      }).catch(reason => {
        this.loading = false
        this.heartbeatNotified = true
        this.$notify({
          title: this.$t('text.fail'),
          message: UTermPluginClient.normalizeErrs(reason),
          type: 'error',
          duration: 2000
        })
      }).finally(() => {
        this.heartbeat()
      })
    },
    listUsbInfo() {
      this.client.listUsbInfo(data => {
        const oldDiskId = this.usbDiskId
        this.usbDiskList = (data || []).filter(item => item.UsbDiskStatus !== 2)
        if (this.usbDiskList.length > 0) {
          this.usbDiskId = oldDiskId || this.usbDiskList[0].UsbDiskId
        } else {
          this.usbDiskId = undefined
        }
        const diskName = this.usbDisk && this.usbDisk.label
        this.$nextTick(() => {
          if (!this.$refs.udi.select(this.usbDiskId) && oldDiskId) {
            this.$message({
              message: this.$t('pages.uDiskUnplugMsg', { name: diskName }),
              type: 'warning',
              duration: 5000
            })
            this.usbDiskId = undefined
          }
        })
      })
    },
    selectUsbDisk(disk, outOfFreeSpace, diskUnsupported) {
      this.outOfFreeSpace = outOfFreeSpace
      this.diskUnsupported = diskUnsupported
      this.usbDisk = disk
      this.temp.usbGuid = disk && disk.UsbTermGuid
      if (this.isChangeUserNo(disk)) {
        this.$message({
          message: this.$t('pages.uDiskTerminalSelectMsg'),
          type: 'warning',
          duration: 5000
        })
      }
    },
    isChangeUserNo(disk) {
      if (!disk || !disk.UsbTermMainKey) {
        return false
      }
      const mk = disk.UsbTermMainKey
      return mk !== this.testUserMK && mk !== this.mainKey
    },
    handleAddTerm() {
      this.$emit('change')
    },
    handleTermChange(term, hasGroupPermission) {
      this.hasGroupPermission = hasGroupPermission
      if (term) {
        this.temp = { ...this.temp, ...term }
      } else {
        this.temp.termId = undefined
        this.temp.termName = undefined
      }
    },
    handlePlugConfig() {
      this.$refs.plugCfg.show(this.logLevel, this.packetEncrypted, this.heartbeatEnable)
    },
    savePlugConfig(data) {
      this.logLevel = data.logLevel
      this.packetEncrypted = data.packetEncrypted
      this.heartbeatEnable = data.heartbeatEnable
    },
    handlePkgUpload(percent) {
      this.percent = percent
      if (percent === 0) {
        if (this.progressTimer) {
          clearInterval(this.progressTimer)
          this.progressTimer = undefined
        }
        this.progressTitle = this.$t('pages.uploadProgress')
        this.progressShowClose = false
        this.progressVisible = true
        return
      }
      if (percent === 100) {
        this.progressVisible = false
      }
    },
    openPwdLicDlg() {
      const termId = this.temp.usbGuid && this.hasGroupPermission ? this.temp.termId : undefined
      const termName = this.temp.usbGuid && this.hasGroupPermission ? this.temp.termName : undefined
      this.$refs.pwdLic.show(termId, termName)
    },
    async makeUTerm() {
      if (!this.temp.termId) {
        this.$message({
          message: this.$t('pages.selectTerm'),
          type: 'warning',
          duration: 2000
        })
        return
      }
      if (!this.pkgVer) {
        this.$message({
          message: this.$t('pages.uDiskTerminalInstallPackageNotExistsMsg'),
          type: 'warning',
          duration: 2000
        })
        return
      }
      const accVer = await getTerminalAccessVersion(this.temp.type & 0xf).then(res => res.data)
      if (accVer && compareVersion(accVer, this.pkgVer) > 0) {
        this.$message({
          message: this.$t('pages.offlineTerm_bindErr5'),
          type: 'error',
          duration: 3000
        })
        return
      }
      //  判断是否有主安装码
      const res = await getMainCode();
      const code = res.data
      if (!code) {
        this.$message({
          message: '安装识别码未生成，请到【安装码】生成！',
          type: 'warning',
          duration: 2000
        })
        return
      }
      await this.$refs['usbForm'].validate()
      const genRes = await generateUsbGuid(this.temp.termId, this.usbDisk.UsbTermGuid)
      const { time, usbGuid, needAllocModules, loginMode, autoLoginUser, needBindUser } = genRes.data
      const checkModulesAndCreateUTerm = () => {
        if (!needAllocModules || !this.hasPermission('A22')) {
          return this.createUTermWithRollback(time, usbGuid, false, code)
        }
        const message = this.$t('pages.uDiskTerminalUnassignedModuleMsg1', { action: this.$t('pages.makeUsbTerminal') })
        const option = { confirmButtonText: this.$t('pages.autoAllocation'), cancelButtonText: this.$t('pages.manualAllocation'), distinguishCancelAndClose: true }
        return this.$confirmBox(message, this.$t('text.prompt'), option).then(() => {
          return this.createUTermWithRollback(time, usbGuid, true, code)
        }).catch(reason => {
          if (reason === 'cancel') {
            this.making = { time, usbGuid }
            this.allocateModules(message)
            // return Promise.reject(this.temp.termName + '未分配模块，需要分配模块后才能制作U盘终端');
          }
        })
      }

      if (!needBindUser) {
        return checkModulesAndCreateUTerm()
      }
      return this.$confirmBox(this.$t('pages.uDiskTerminalUnassignedModuleMsg2', { name: this.temp.termName, mode: loginModeFormatter(loginMode) }), this.$t('text.prompt')).then(() => {
        // 绑定操作员弹窗
        const data = {
          id: this.temp.termId,
          name: this.temp.termName,
          loginMode,
          userId: autoLoginUser && autoLoginUser.id,
          userName: autoLoginUser && autoLoginUser.name
        }
        this.$refs.bindUsrDlg.show(data)
        return Promise.reject('bindUser')
      }).catch(reason => {
        if (reason === 'bindUser') {
          return Promise.reject('cancel')
        }
        return checkModulesAndCreateUTerm()
      })
    },
    //  code 主安装码
    createUTermWithRollback(time, usbGuid, needAllocModules, code) {
      const data = { ...this.temp }
      this.openProgress(this.$t('pages.uDiskTerminalFabricationProgress'), 500)
      return this.client.createUsbTerm({
        UsbDiskId: this.usbDiskId,
        UserNo: this.mainKey,
        Guid: usbGuid,
        ServerTime: time,
        NeedPwd: data.needPwd,
        PwdMD5: data.needPwd ? md5(data.password) : ''
      }).then(res => {
        if (res) {
          const h = this.$createElement
          this.$msgbox({
            type: 'warning',
            title: this.$t('text.prompt'),
            customClass: 'usb-partition-msgbox',
            message: h('div', null, [
              h('p', null, '您的U盘分区表类型较旧，请用磁盘工具转换分区表类型：'),
              h('p', null, '（1）下载：DiskGenius 工具；'),
              h('p', null, [
                '（2）点击菜单 “',
                h('span', { style: 'font-weight: 700;' }, '硬盘'),
                '” -> “',
                h('span', { style: 'font-weight: 700;' }, '快速分区'),
                '”；'
              ]),
              h('p', null, '（3）“分区表类型” 推荐选择 GUID。'),
              h('p', { style: 'color: red; font-weight: 700; font-size: 15px;' }, '注意：'),
              h('p', { style: 'color: red;' }, [
                '（1）转换分区时请不要勾选 “',
                h('span', { style: 'font-weight: 700;' }, '创建新ESP分区'),
                '” 和 “',
                h('span', { style: 'font-weight: 700;' }, '创建MSR分区'),
                '”。',
                h(Tooltip, { props: { effect: 'light', placement: 'left', visibleArrow: false }}, [
                  h('img', { domProps: { src: require('@/assets/usb_partition.png') }, slot: 'content' }),
                  h('i', { class: 'el-icon-info', style: 'color: #666;' })
                ])
              ]),
              h('p', { style: 'color: red;' }, '（2）该操作需要重新格式化U盘，操作前请先备份U盘文件，以免数据丢失！')
            ]),
            showCancelButton: false,
            confirmButtonText: this.$t('button.confirm2'),
            closeOnClickModal: false
          })
          return Promise.reject()
        }
      }).then(() => {
        if (this.percent < 30) {
          this.percent = 30
        }
        this.temp.usbGuid = usbGuid
        data.usbGuid = usbGuid
        data.pkgVer = this.pkgVer
        delete data.updatePwd
        if (needAllocModules) {
          data.needAllocModules = true
        }
        return makeUTerm(data).catch(() => {
          this.client.deleteUsbTerm(this.usbDiskId, usbGuid)
          this.temp.usbGuid = undefined
          return Promise.reject(this.$t('pages.uDiskTerminalProductionFailed'))
        })
      }).then(async res => {
        this.listUsbInfo()
        this.$emit('change')
        const { taskId, token, stg } = res.data
        const { startTime, endTime } = this.getCodeTime(this.temp.isPermanent, this.temp.codeEndTime)
        return this.updateTermPkg(taskId, token, stg, code, startTime, endTime) // 更新安装包
          .then(() => {
            // 记录安装包制作日志
            //  记录该安装码的使用日志  type=4使用U盘终端制作安装包
            const { installCode, startTime, endTime } = res.data
            recordInstallPackageLog({ type: 4, code: installCode, startTime, endTime });
            return getServerTime()
          }) // 更新策略
          .then(time => this.client.updateUsbPolicy({
            UsbDiskId: this.usbDiskId,
            Guid: this.temp.usbGuid,
            ServerTime: time,
            UpdatePwd: 0,
            NeedPwd: 0,
            PwdMD5: '',
            PackageUrl: buildDownloadUrl(taskId, 1),
            token
          }))
      }).then((res) => {
        this.percent = 100
        this.progressVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.uDiskTerminalProductionFinished'),
          type: 'success',
          duration: 5000
        })
      }).catch(reason => {
        this.progressVisible = false
        this.percent = 100
        if (!reason || reason.toString().trim().length === 0 || reason === 'cancel' || reason === 'close') {
          return
        }
        this.$notify({
          title: this.$t('text.fail'),
          message: reason,
          type: 'error',
          duration: 5000
        })
      })
    },
    /**
     * 升级终端
     * @returns {Promise<void>}
     */
    async upgradeUTerm() {
      if (!this.modifiable || this.updateDisabled) {
        return
      }
      if (!this.pkgVer) {
        this.$message({
          message: this.$t('pages.uDiskTerminalInstallPackageNotExistsMsg'),
          type: 'warning',
          duration: 2000
        })
        return
      }
      //  判断是否有主安装码
      const res = await getMainCode();
      const code = res.data
      if (!code) {
        this.$message({
          message: '安装码未生成，请到【安装码】生成！',
          type: 'warning',
          duration: 2000
        })
        return
      }
      const cpv = compareVersion(this.temp.pkgVer, this.pkgVer)
      let promise;
      if (cpv === 0) {
        promise = this.$confirmBox(this.$t('pages.uDiskTerminalUpgradeMsg1'), this.$t('text.prompt'))
      } else if (cpv > 0) {
        promise = this.$confirmBox(this.$t('pages.uDiskTerminalUpgradeMsg2'), this.$t('text.prompt'))
      } else {
        promise = Promise.resolve()
      }
      promise.then(() => {
        this.openProgress(this.$t('pages.uDiskTerminalUpgradeProgress'), 200)
        return preUpgradeUTermPkg()
      }).then(res => {
        const { taskId, token } = res.data
        const { startTime, endTime } = this.getCodeTime(this.temp.isPermanent, this.temp.codeEndTime)
        return this.updateTermPkg(taskId, token, null, code, startTime, endTime)
      }).then((res) => {
        // 记录安装包制作日志
        //  记录该安装码的使用日志  type=4使用U盘终端制作安装包
        const { installCode, startTime, endTime } = res.data
        recordInstallPackageLog({ type: 4, code: installCode, startTime, endTime });

        const data = {
          pkgVer: this.pkgVer,
          termId: this.temp.termId,
          termName: this.temp.termName
        }
        return upgradeUTermPkg(data)
      }).then(() => {
        this.listUsbInfo()
        this.$emit('change')
        this.percent = 100
        this.progressVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.uDiskTerminalUpgradeSucceeded'),
          type: 'success',
          duration: 5000
        })
      }).catch(reason => {
        if (reason === 'cancel') {
          return
        }
        this.progressVisible = false
        this.percent = 100
        this.$notify({
          title: this.$t('text.fail'),
          message: reason,
          type: 'error',
          duration: 5000
        })
      })
    },
    /**
     *
     * @param taskId    原始安装包下载TakeId
     * @param token     原始安装下载Token
     * @param stg       策略
     * @param installCode 系统接入码
     * @param startTime   安装包生效时间
     * @param endTime     安装包失效时间
     * @param UserNo      用户编号
     * @returns {Promise<unknown>}
     */
    updateTermPkg(taskId, token, stg, installCode, startTime, endTime) {
      //  UserNo: 用户编号
      return this.client.updateUsbTermFile({
        UsbDiskId: this.usbDiskId,
        Guid: this.temp.usbGuid,
        PackageUrl: buildDownloadUrl(taskId, stg ? 2 : 0),
        token,
        installCode,
        startTime,
        endTime,
        UserNo: this.userNo
      }).then(res => {
        return new Promise((resolve, reject) => { resolve({ data: { startTime, endTime, installCode }}) });
      })
    },
    updateTermStg() {
      if (!this.modifiable || this.updateDisabled) {
        return
      }
      this.$refs['usbForm'].validate().then(() => {
        this.openProgress(this.$t('pages.uDiskTerminalPolicyUpdateProgress'), 150)
        const data = { ... this.temp }
        delete data.pkgVer
        if (!data.updatePwd) {
          delete data.needPwd
          delete data.password
          delete data.confirmPwd
        } else {
          data.needPwd = !data.password ? 0 : 1
        }
        data.password = aesEncode(data.password, formatAesKey('tr838408', ''))
        data.confirmPwd = aesEncode(data.confirmPwd, formatAesKey('tr838408', ''))
        return updateStrategy(data)
      }).then(res => {
        const { time, taskId, token } = res.data
        const needPassword = this.temp.updatePwd && this.temp.password
        return this.client.updateUsbPolicy({
          UsbDiskId: this.usbDiskId,
          Guid: this.temp.usbGuid,
          ServerTime: time,
          UpdatePwd: this.temp.updatePwd,
          NeedPwd: needPassword ? 1 : 0,
          PwdMD5: needPassword ? md5(this.temp.password) : '',
          PackageUrl: buildDownloadUrl(taskId, 1),
          token
        })
      }).then(() => {
        this.percent = 100
        this.progressVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.uDiskTerminalPolicyUpdateSucceeded'),
          type: 'success',
          duration: 5000
        })
      }).catch(reason => {
        if (!reason || reason.toString().trim().length === 0) {
          return
        }
        this.progressVisible = false
        this.percent = 100
        this.$notify({
          title: this.$t('text.fail'),
          message: reason,
          type: 'error',
          duration: 5000
        })
      })
    },
    handleDelete() {
      this.$refs['del'].show()
    },
    deleteUTerm(data, end) {
      this.openProgress(this.$t('pages.uDiskTerminalUninstallProgress'), 50)
      return this.client.deleteUsbTerm(this.usbDiskId, this.temp.usbGuid).then(() => {
        this.listUsbInfo()
        if (this.temp.termId) {
          data.termId = this.temp.termId
          data.termName = this.temp.termName
          data.usbGuid = this.temp.usbGuid
          return deleteUTerm(data)
        }
        return Promise.resolve()
      }).then(() => {
        end()
        this.percent = 100
        this.progressVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.uDiskTerminalUninstallSucceeded'),
          type: 'success',
          duration: 5000
        })
        this.$emit('change')
      }).catch(reason => {
        end()
        this.progressVisible = false
        this.percent = 100
        this.$notify({
          title: this.$t('text.fail'),
          message: reason,
          type: 'error',
          duration: 5000
        })
      })
    },
    heartbeat(interval) {
      if (!this.visible) {
        this.handleClose()
        return
      }
      if (this.heartbeatTimer) {
        clearTimeout(this.heartbeatTimer)
      }
      if (!this.heartbeatEnable) {
        return
      }
      const connected = this.pluginWorkOk
      this.heartbeatTimer = setTimeout(() => {
        if (!this.client || !this.heartbeatEnable) {
          this.heartbeatTimer = undefined
          return
        }
        this.client.getPlugInfo().then(data => {
          this.reconnectCount = 0
          this.pluginWorkOk = data.PlugWorkType === 0
          if (this.pluginWorkOk) {
            this.heartbeatNotified = false
            if (!connected) {
              this.listUsbInfo()
            }
          }
          this.pluginWorkVer = data.PlugVersion
        }).catch(reason => {
          this.pluginWorkOk = false
          if (!this.heartbeatNotified) {
            this.$notify({
              title: this.$t('text.fail'),
              message: UTermPluginClient.normalizeErrs(reason),
              type: 'error',
              duration: 2000
            })
            this.heartbeatNotified = true
          }
          if (this.client.closed) {
            this.reconnectCount++
            return this.client.connect().then(() => {
              this.heartbeat(1)
            })
          }
        }).finally(() => {
          if (connected) {
            this.pluginInterrupted = !this.pluginWorkOk
          }
          this.heartbeatTimer = undefined
          this.heartbeat()
        })
      }, interval || this.heartbeatInterval)
    },
    openProgress(title, interval) {
      this.progressTitle = title
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = undefined
      }
      this.percent = 0
      this.progressShowClose = false
      this.progressVisible = true
      this.progressTimer = setInterval(() => {
        if (this.percent < 99) {
          this.percent++
        } else {
          clearInterval(this.progressTimer)
          this.progressTimer = undefined
        }
      }, interval)
      this.progressCancelTimer = setTimeout(() => {
        if (this.progressVisible) {
          const msg = this.$t('pages.uDiskTerminalOpenProgressMsg', { info: title, percent: this.percent });
          const opt = { confirmButtonText: this.$t('pages.continue'), cancelButtonText: this.$t('pages.exit') }
          this.$confirmBox(msg, this.$t('text.prompt'), opt).then(() => {
            this.progressShowClose = true
          }).catch(() => {
            if (this.progressTimer) {
              clearInterval(this.progressTimer)
              this.progressTimer = undefined
            }
            this.progressVisible = false
          })
        }
        this.progressCancelTimer = undefined
      }, 600000)
    },
    handleClose() {
      if (this.heartbeatTimer) {
        clearTimeout(this.heartbeatTimer)
        this.heartbeatTimer = undefined
      }
      if (this.client) {
        this.client.close()
        this.client = undefined
      }
      this.pluginWorkOk = false
      this.pluginWorkVer = undefined
      this.pluginInterrupted = false
      this.heartbeatNotified = false
      this.reconnectCount = 0
      this.usbDiskId = undefined
      this.usbDisk = undefined
      this.usbDiskList = []
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = undefined
      }
      this.percent = 0
      this.progressTitle = undefined
      this.progressVisible = false
      this.userNo = null
    },
    testConnect(port) {
      if (this.client) {
        this.client.close()
      }
      this.connect(port)
    },
    handleBeginTimeChange() {
      const startTime = new Date(this.temp.beginTime).getTime()
      const endTime = new Date(this.temp.endTime).getTime()
      if (startTime > endTime) {
        // 将结束时间设置成和开始时间一样
        this.$set(this.temp, 'endTime', this.temp.beginTime)
      }
    },
    handleEndTimeChange() {
      const startTime = new Date(this.temp.beginTime).getTime()
      const endTime = new Date(this.temp.endTime).getTime()
      if (startTime > endTime) {
        // 将开始时间设置成和结束时间一样
        this.$set(this.temp, 'beginTime', this.temp.endTime)
      }
    },
    handleConfirmPwdInput(value) {
      this.temp.confirmPwd = notSpace(value)
    },
    passwordValidator(rule, value, callback) {
      let error
      if (!value) {
        this.rules.confirmPwd[0].required = false
        this.$refs['usbForm'].clearValidate(['confirmPassword'])
        if (rule.required) {
          error = new Error(this.$t('pages.validateMsg_password'))
        }
      } else {
        this.rules.confirmPwd[0].required = true
        if (!this.$refs.pwdinput.validate()) {
          error = new Error(this.$t('pages.validateMsg_password_NotMatch'))
        }
      }
      callback(error)
    },
    confirmPasswordValidator(rule, value, callback) {
      if (rule.required && !value) {
        callback(new Error(this.$t('pages.validateMsg_password')))
      } else if (rule.required && value !== this.temp.password) {
        callback(new Error(this.$t('pages.validateMsg_diffPassword')))
      } else {
        callback()
      }
    },
    codeEndTimeValidator(rule, value, callback) {
      if (!this.temp.isPermanent && !value) {
        callback(new Error('安装包有效期不能为空'))
      }
      callback();
    },
    allocateModules(message) {
      if (typeof message !== 'string') {
        this.making = false
      }
      this.$refs['moduleEdit'].show(this.temp.termId, message)
    },
    handleModuleChange(modules) {
      if (this.making) {
        const { time, usbGuid } = this.making
        this.making = false
        if (modules.filter(item => item.checked).length > 0) {
          this.createUTermWithRollback(time, usbGuid, false)
        }
      }
    },
    // 设置截止时间的选择范围
    setEndTimeRange() {
      const dateObj = new Date()
      return {
        disabledDate: time => time.getTime() < dateObj.getTime()
      }
    },
    /**
     * 获取当前配置的安装码有效期
     */
    getCodeTime(isPermanent, codeEndTime) {
      let startTime = parseTime(new Date(), 'y-m-d')
      let endTime = codeEndTime
      if (isPermanent) {
        startTime = null
        endTime = null
      } else {
        startTime += ' 00:00:00'
        endTime += ' 23:59:59'
      }
      return { startTime, endTime }
    },
    isPermanentChange(data) {
      if (data) {
        this.temp.codeEndTime = null
        this.$refs.usbForm.validateField('codeEndTime')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-dialog__body {
    max-height: 660px;
  }
  .usb-term-form {
    padding: 0 20px;
    .plug-cfg-btn {
      display: none;
      margin: 0;
    }
    .usb-strategy-tips {
      line-height: 30px;
      span {
        color: #2674b2;
        strong {
          color: #f56c6c;
        }
      }
    }
    .el-date-editor.el-input {
      width: 100%;
    }
  }
  .btn-gen-lic {
    position: relative;
    float: left;
    .el-icon-info {
      color: #666;
      position: relative;
      top: 3px;
    }
  }
</style>

<style lang="scss">
  .pwd-clr.el-input--suffix {
    &:hover .el-input__inner, .el-input__inner:focus {
      padding-right: 55px;
    }
  }
  .usb-partition-msgbox {
    width: 555px;
  }
</style>
