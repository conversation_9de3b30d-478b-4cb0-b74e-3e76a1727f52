<template>
  <div v-if="disk">
    <template v-if="disk.UsbTermGuid && disk.UsbDiskStatus && term">
      <div class="term-status">
        <i18n path="pages.uDiskTerminalInstallMsg1">
          <strong slot="isInstall" class="is-ok">{{ $t('pages.alreadyInstalled') }}</strong>
        </i18n>
        <span v-show="!hasGroupPermission">
          。<strong class="is-err">{{ $t('pages.uDiskTerminalInstallMsg2') }}</strong>
        </span>
      </div>
      <el-row :gutter="gutter">
        <el-col :span="span">
          <FormItem :label="$t('pages.terminalName')">
            <el-input :value="term.termName" :title="term.termName" disabled/>
          </FormItem>
        </el-col>
        <el-col :span="span">
          <FormItem :label="$t('table.terminalVersion')">
            <el-input :value="term.pkgVer" disabled/>
          </FormItem>
        </el-col>
      </el-row>
    </template>
    <div v-else-if="disk.UsbTermGuid && disk.UsbDiskStatus && !term" class="term-status">
      <i18n path="pages.uDiskTerminalInstallMsg3">
        <strong slot="terminalNotExitsts" class="is-err">{{ $t('pages.terminalNotExist') }}</strong>
      </i18n>
    </div>
    <template v-else>
      <div class="term-status">
        <i18n path="pages.uDiskTerminalInstallMsg4">
          <strong slot="noInstall" class="is-err">{{ $t('pages.notInstalled') }}</strong>
        </i18n>
      </div>
      <el-row :gutter="gutter">
        <el-col :span="span">
          <FormItem :label="$t('pages.terminalName')">
            <tree-select
              ref="trees"
              is-filter
              is-log-terminal-tree
              local-search
              :default-expand-all="false"
              leaf-key="terminal"
              node-key="id"
              :filter-key="terminalFilter"
              rewrite-node-click-fuc
              :node-click-fuc="handleTreeClick"
            />
          </FormItem>
        </el-col>
        <el-col :span="span">
          <el-button @click="handleAddTerm">E</el-button>
        </el-col>
      </el-row>
    </template>

    <add-terminal ref="at" :type-filter="isUTerm" :group-tree-data="groupTreeData" @add="handleAddedTerm"/>
  </div>
</template>

<script>
import AddTerminal from '../add'
import { getUTermByGuid } from '@/api/system/terminalManage/uterm'

export default {
  name: 'UsbTermSelect',
  components: { AddTerminal },
  props: {
    disk: {
      type: Object,
      default: undefined
    },
    groupTreeData: {
      type: Array,
      default() {
        return []
      }
    },
    gutter: {
      type: Number,
      default: 2
    },
    span: {
      type: Number,
      default: 12
    }
  },
  data() {
    return {
      term: undefined,
      hasGroupPermission: true
    }
  },
  watch: {
    disk() {
      this.hasGroupPermission = true
      if (this.disk && !this.disk.UsbDiskStatus) {
        this.term = undefined
        this.handleChange()
      } if (this.disk && this.disk.UsbDiskStatus && this.disk.UsbTermGuid) {
        getUTermByGuid(this.disk.UsbTermGuid).then(res => {
          if (res.data) {
            const { termId, termName, type, pkgVer, hasGroupPermission } = res.data
            this.hasGroupPermission = hasGroupPermission
            this.term = { termId, termName, type, pkgVer }
            this.checkNode(termId, true)
          } else {
            this.term = undefined
          }
          this.handleChange()
        })
      } else if (this.term && this.term.pkgVer) {
        this.checkNode(this.term.termId, true)
        this.term = undefined
        this.handleChange()
      }
    }
  },
  methods: {
    terminalFilter(node) {
      const type = parseInt(node.dataType)
      // 分组显示
      if (node.type == 3) {
        return true
      }
      return this.isUTerm(type) && !node.dataCode
    },
    isUTerm(type) {
      return (type & 0xf0) === 32
    },
    handleTreeClick(data, node, vm) {
      if (data.dataType === 'G' || data.type === 'G') {
        // 分组节点不选中
        return false
      }
      this.term = {
        termId: data.dataId,
        termName: data.label,
        type: data.dataType,
        pkgVer: undefined
      }
      this.handleChange()
    },
    handleChange() {
      this.$emit('change', this.term, this.hasGroupPermission)
    },
    handleAddTerm() {
      this.$refs.at.show(this.currentGroupId)
    },
    handleAddedTerm({ id, name, type }) {
      this.checkNode({ id, label: name, dataType: type }, true)
      this.term = {
        termId: id,
        termName: name,
        type: type,
        pkgVer: undefined
      }
      this.handleChange()
      this.$emit('add')
    },
    // 选中节点的方法，data：选中的节点的data或nodeKey，select：布尔值，true为选中 false为取消选中
    checkNode(data, select) {
      if (!this.$refs.trees) {
        return
      }
      if (typeof data === 'object') {
        data.dataId = data.id
        data.id = 'T' + data.dataId
      } else {
        data = 'T' + data
      }
      this.$refs.trees.checkNode(data, select)
    }
  }
}
</script>

<style lang="scss" scoped>
  .term-status {
    height: 30px;
    line-height: 28px;
    .is-ok {
      color: #36ab60;
    }
    .is-err {
      color: #f56c6c;
    }
  }
  .el-button {
    padding: 5px 9px;
    margin: 3px 0 0 16px;
  }
</style>
