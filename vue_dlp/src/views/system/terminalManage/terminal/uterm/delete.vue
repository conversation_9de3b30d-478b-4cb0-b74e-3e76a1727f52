<template>
  <el-dialog
    :title="$t('pages.uninstallTheUSBDiskTerminal')"
    :close-on-click-modal="false"
    :modal="false"
    width="465px"
    :visible.sync="visible"
  >
    <Form ref="delForm" :model="temp">
      <FormItem :label="$t('pages.enterAdminPwd')" label-width="182px" :error="error" required>
        <el-input v-model="temp.password" type="password" maxlength="100" show-password @blur="handleBlur" @input="handleInput"/>
      </FormItem>
      <FormItem v-if="hasPermission('370')" label="" label-width="2px">
        <el-tooltip class="item" effect="dark" :content="$t('pages.delTerminal_text0')" placement="bottom-start">
          <el-checkbox v-model="temp.deleteTerm">{{ $t('pages.delTerminal') }}</el-checkbox>
        </el-tooltip>
      </FormItem>
      <FormItem v-if="temp.deleteTerm" v-permission="'106&370'" label="" label-width="2px">
        <el-tooltip class="item" effect="dark" :content="$t('pages.delUser_text')" placement="bottom-start">
          <el-checkbox v-model="temp.deleteUser">{{ $t('pages.delUser') }}</el-checkbox>
        </el-tooltip>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="handleDelete">{{ $t('button.confirm') }}</el-button>
      <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { validatePassword } from '@/api/user'
import { aesEncode, formatAesKey } from '@/utils/encrypt';

export default {
  name: 'DeleteUsbTerm',
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        password: undefined,
        deleteTerm: true,
        deleteUser: true
      },
      error: undefined
    }
  },
  methods: {
    show() {
      this.temp = { ...this.defaultTemp }
      this.submitting = false
      this.error = undefined
      this.visible = true
    },
    handleBlur() {
      this.passwordValidate()
    },
    handleInput() {
      this.error = undefined
    },
    handleDelete() {
      this.submitting = true
      this.passwordValidate(true).then(() => {
        return this.$confirmBox(this.$t('pages.confirmUninstallUSBTerminalMsg'), this.$t('text.prompt')).then(() => {
          const data = { deleteTerm: this.temp.deleteTerm }
          if (data.deleteTerm) {
            data.deleteUser = this.temp.deleteUser
          } else {
            data.deleteUser = false
          }
          this.$emit('delete', data, () => {
            this.submitting = false
            this.visible = false
          })
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    passwordValidate(notOnlyRequired) {
      if (!this.temp.password) {
        this.error = this.$t('pages.pwdCantNull')
        return Promise.reject()
      }
      if (notOnlyRequired) {
        const password = aesEncode(this.temp.password, formatAesKey('tr838408', ''))
        return validatePassword({ password }).then(respond => {
          if (respond.data) {
            this.error = undefined
            return Promise.resolve()
          } else {
            this.error = this.$t('pages.administratorPasswordError')
            return Promise.reject()
          }
        })
      } else {
        this.error = undefined
        return Promise.resolve()
      }
    }
  }
}
</script>
