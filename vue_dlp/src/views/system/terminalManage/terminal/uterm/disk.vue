<template>
  <div class="usb-disk-info">
    <el-row :gutter="gutter">
      <el-col :span="span">
        <FormItem :label="$t('table.usbDevice')">
          <el-select :value="value" :disabled="disabled" @change="handleSelectDisk">
            <el-option
              v-for="(item, index) in disks"
              :key="index"
              :value="item.UsbDiskId"
              :label="getDiskLabel(item)"
              :disabled="item.UsbDiskStatus === 3"
            />
          </el-select>
        </FormItem>
      </el-col>
      <el-col class="disk-status" :span="span">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefreshDisks">
          <svg-icon icon-class="refresh"/>
        </el-button>
        <span v-if="!disabled && disks.length === 0">{{ $t('pages.pleaseInsertUSBStick') }}</span>
      </el-col>
    </el-row>
    <el-row :gutter="gutter">
      <el-col :span="span">
        <FormItem :label="$t('pages.spaceSize')">
          <el-input :value="disk.totalSpace" disabled/>
        </FormItem>
      </el-col>
      <el-col :span="span">
        <FormItem :label="$t('table.remainingSpace')" :error="freeSpaceError">
          <el-input :value="disk.freeSpace" disabled/>
        </FormItem>
      </el-col>
    </el-row>
    <el-row :gutter="gutter">
      <el-col :span="span">
        <FormItem :label="$t('pages.fileSystem')" :error="fileSystemError">
          <el-input :value="disk.UsbFormat" disabled/>
        </FormItem>
      </el-col>
      <el-col :span="span">
        <FormItem :label="$t('pages.partitionFormat')" :error="diskPartitionError">
          <el-input :value="disk.UsbParFormat" disabled/>
        </FormItem>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { formatFileSize } from '@/utils'

export default {
  name: 'UsbDiskInfo',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      default: undefined
    },
    disks: {
      type: Array,
      default() {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    gutter: {
      type: Number,
      default: 2
    },
    span: {
      type: Number,
      default: 12
    }
  },
  data() {
    return {
      disk: {},
      defaultDisk: {
        UsbDiskId: undefined,
        UsbDiskName: undefined,
        totalSpace: undefined,
        freeSpace: undefined,
        UsbDiskStatus: 0,
        UsbTermGuid: undefined,
        UsbDiskTotalSize: 0,
        UsbDiskFreeSize: 0,
        UsbFormat: '',
        UsbParFormat: ''
      },
      diskSizeLimit: 1024 * 1024 * 100,
      unsupportedFormats: ['exFAT'],
      supportedPartition: ['MBR', 'GPT']
    }
  },
  computed: {
    diskSpaceLimit() {
      return formatFileSize(this.diskSizeLimit)
    },
    outOfFreeSpace() {
      return this.disk.freeSpace && this.disk.UsbDiskFreeSize < this.diskSizeLimit
    },
    freeSpaceError() {
      if (this.outOfFreeSpace) {
        return this.$t('pages.utermDiskFreeSpaceErrorMsg', { size: this.diskSpaceLimit })
      }
      return undefined
    },
    fileSystemError() {
      if (this.disk && this.disk.UsbFormat) {
        for (let i = 0; i < this.unsupportedFormats.length; i++) {
          if (this.unsupportedFormats[i].toLowerCase() === this.disk.UsbFormat.toLowerCase()) {
            return this.$t('pages.utermDiskFileSystemErrorMsg', { info: this.disk.UsbFormat })
          }
        }
      }
      return undefined
    },
    diskPartitionError() {
      if (this.disk && this.disk.UsbParFormat) {
        for (let i = 0; i < this.supportedPartition.length; i++) {
          if (this.supportedPartition[i].toLowerCase() === this.disk.UsbParFormat.toLowerCase()) {
            return undefined
          }
        }
        return this.$t('pages.utermDiskDiskPartitionErrorMsg', { info: this.disk.UsbParFormat })
      }
      return undefined
    },
    diskUnsupported() {
      return !!this.fileSystemError || !!this.diskPartitionError
    }
  },
  created() {
    this.disk = this.defaultDisk
  },
  methods: {
    getDiskLabel(disk) {
      return disk.UsbDiskName + '（' + disk.UsbDiskId + ':）'
    },
    select(diskId) {
      if (diskId) {
        for (let i = 0; i < this.disks.length; i++) {
          const disk = this.disks[i]
          if (disk.UsbDiskId === diskId) {
            this.disk = {
              ...disk,
              label: this.getDiskLabel(disk),
              totalSpace: formatFileSize(disk.UsbDiskTotalSize),
              freeSpace: formatFileSize(disk.UsbDiskFreeSize)
            }
            this.$emit('select', this.disk, this.outOfFreeSpace, this.diskUnsupported)
            return disk
          }
        }
      }
      this.disk = this.defaultDisk
      this.$emit('select', undefined, this.outOfFreeSpace, this.diskUnsupported)
      return undefined
    },
    handleSelectDisk(diskId) {
      this.$emit('change', diskId)
      this.select(diskId)
    },
    handleRefreshDisks() {
      this.$emit('refresh')
    }
  }
}
</script>

<style lang="scss" scoped>
  .disk-status {
    .el-button {
      margin-left: 16px;
    }
    span {
      color: #f56c6c;
      margin-left: 16px;
    }
  }
  .el-input.is-disabled >>>.el-input__inner {
    color: #666666;
  }
  .el-form-item.is-error >>>.el-input__inner {
    border-color: #f56c6c;
  }
</style>
