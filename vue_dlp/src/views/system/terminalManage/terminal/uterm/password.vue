<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    width="450px"
    :visible.sync="visible"
  >
    <div slot="title" class="el-dialog__title">
      {{ $t('pages.uDiskTerminalPasswordResetFile') }}
      <el-tooltip>
        <div slot="content">
          <img class="sample-image" :src="require('@/assets/uterm_password_reset.png')" :alt="$t('pages.forgotPassword')">
        </div>
        <i class="el-icon-info"/>
      </el-tooltip>
    </div>
    <Form ref="pwdForm" :model="temp" :rules="rules" label-width="88px" label-position="right">
      <FormItem :label="$t('pages.terminalName')" prop="termId">
        <tree-select
          is-filter
          :local-search="false"
          :default-expand-all="false"
          :checked-keys="checkedKeys"
          leaf-key="terminal"
          node-key="id"
          :filter-key="terminalFilter"
          rewrite-node-click-fuc
          :node-click-fuc="handleTreeClick"
        />
      </FormItem>
      <FormItem :label="$t('form.password')" :tooltip-content="$t('pages.passwordNotInputSpace')" prop="password">
        <password-input v-if="visible" ref="pwdinput" v-model="temp.password" input-class="pwd-clr" type="password" show-password clearable/>
      </FormItem>
      <FormItem :label="$t('form.confirmPassword')" :tooltip-content="$t('pages.passwordNotInputSpace')" prop="confirmPwd">
        <el-input v-if="visible" v-model="temp.confirmPwd" class="pwd-clr" maxlength="64" clearable type="password" show-password @input="handleConfirmPwdInput"/>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <common-downloader
        :name="getLicenceName"
        :button-name="$t('pages.generate')"
        button-type="primary"
        button-icon=""
        :before-download="beforeDownload"
        @download="genLicence"
      />
      <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import PasswordInput from './pwdinput'
import { genPasswordResetLicence } from '@/api/system/terminalManage/uterm'
import CommonDownloader from '@/components/DownloadManager/common'
import moment from 'moment'
import { notSpace } from '@/utils/inputLimit'
import { aesEncode, formatAesKey } from '@/utils/encrypt'

export default {
  name: 'PasswordLicence',
  components: { PasswordInput, CommonDownloader },
  data() {
    return {
      visible: false,
      checkedKeys: [],
      temp: {
        termId: undefined,
        termName: undefined,
        password: undefined,
        confirmPwd: undefined
      },
      rules: {
        termId: [{ required: true, trigger: 'change', message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.UTerm') }) }],
        password: [{ trigger: 'blur', validator: this.passwordValidator }],
        confirmPwd: [{ trigger: 'blur', validator: this.confirmPasswordValidator }]
      }
    }
  },
  methods: {
    show(termId, termName) {
      if (termId) {
        this.checkedKeys = [{ key: termId, label: termName }]
      } else {
        this.checkedKeys = []
      }
      this.temp = { termId, termName, password: undefined, confirmPwd: undefined }
      this.visible = true
      this.$nextTick(() => {
        this.$refs.pwdForm.clearValidate()
      })
    },
    beforeDownload() {
      return new Promise((resolve, reject) => {
        this.$refs['pwdForm'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    genLicence(file) {
      const opts = { file, jwt: true, topic: 'uterm' }
      const tempData = JSON.parse(JSON.stringify(this.temp))
      tempData.password = aesEncode(tempData.password, formatAesKey('tr838408', ''))
      tempData.confirmPwd = aesEncode(tempData.confirmPwd, formatAesKey('tr838408', ''))
      genPasswordResetLicence(tempData, opts).then(() => {
        this.visible = false
      })
    },
    getLicenceName() {
      return `${this.temp.termName}_${moment().format('YYYYMMDD')}.lrp`
    },
    passwordValidator(rule, value, callback) {
      let error
      if (!value) {
        this.rules.confirmPwd[0].required = false
        this.$refs['pwdForm'].clearValidate(['confirmPassword'])
        if (rule.required) {
          error = new Error(this.$t('pages.validateMsg_password'))
        }
      } else {
        this.rules.confirmPwd[0].required = true
        if (!this.$refs.pwdinput.validate()) {
          error = new Error(this.$t('pages.validateMsg_password_NotMatch'))
        }
      }
      callback(error)
    },
    confirmPasswordValidator(rule, value, callback) {
      if (rule.required && !value) {
        callback(new Error(this.$t('pages.validateMsg_password')))
      } else if (rule.required && value !== this.temp.password) {
        callback(new Error(this.$t('pages.validateMsg_diffPassword')))
      } else {
        callback()
      }
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return (type & 0xf0) === 32 && !!node.dataCode
    },
    handleTreeClick(data, node, vm) {
      if (data.dataType !== 'G') {
        this.temp.termId = data.dataId
        this.temp.termName = data.label
      } else {
        return false
      }
    },
    handleConfirmPwdInput(value) {
      this.temp.confirmPwd = notSpace(value)
    }
  }
}
</script>

