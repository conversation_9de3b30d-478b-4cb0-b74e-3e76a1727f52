<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.batchChangeGroups')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    :width="width"
    @dragDialog="handleDrag"
  >
    <Form>
      <FormItem >
        <span style="margin: 0px 5px 10px;">{{ $t('pages.batch_text1', { num: selectedData.length }) }}</span>
      </FormItem>
      <FormItem :label="$t('table.terminalGroup')" label-width="80px" :extra-width="{ en: 20 }" class="required">
        <tree-select :data="groupTreeData" is-filter :width="296" @change="parentIdObjChange" />
      </FormItem>
      <FormItem >
        <el-checkbox v-model="temp.syncGroup" style="margin-left: 5px;">{{ $t('pages.synchronousGroup') }}</el-checkbox>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="submitData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { batchUpdateGroup } from '@/api/system/terminalManage/department'

export default {
  name: 'BatchModifyGroupDlg',
  props: {
    groupTreeData: { type: Array, default() { return [] } },
    selectedData: { type: Array, default() { return [] } },
    width: {
      type: String, default() {
        return '500px'
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      temp: {
        ids: [],
        groupId: undefined,
        syncGroup: undefined
      }
    }
  },
  computed: {
  },
  watch: {

  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      // 控制dialog是否显示（父调用子方法）
      this.dialogVisible = true
    },
    submitData() {
      if (this.temp.groupId == undefined) {
        this.$message({
          message: this.$t('pages.delTerminal_text1'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.temp.ids.splice(0, this.temp.ids.length)
      this.selectedData.forEach(o => {
        this.temp.ids.push(o.id)
      });
      batchUpdateGroup({ groupId: this.temp.groupId, ids: this.temp.ids.join(','), syncGroup: this.temp.syncGroup }).then(respond => {
        this.dialogVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.$emit('submitEnd')
      })
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.temp.groupId = nodeData.dataId
      }
    },
    handleDrag() {

    }
  }
}
</script>
