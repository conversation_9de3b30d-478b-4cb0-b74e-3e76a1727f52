<template>
  <el-dialog
    v-el-drag-dialog
    width="800px"
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.bindUserManage')"
    :visible.sync="visible"
    @close="handleClose"
  >
    <Form ref="bindForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 700px; margin-left:30px;">
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.terminalName')">
            <el-input :value="temp.name" disabled></el-input>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.terminalCode')">
            <el-input :value="temp.id" disabled></el-input>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem v-show="temp.loginMode === 1" :label="$t('pages.autoLoginUser')" prop="userId">
            <tree-select
              clearable
              is-filter
              :local-search="false"
              :default-expand-all="false"
              node-key="id"
              :checked-keys="checkedKeys"
              :width="296"
              :placeholder="$t('pages.selectAutoLoginUser')"
              :rewrite-node-click-fuc="true"
              :node-click-fuc="userTreeNodeCheckChange"
              @change="userSelectedChange"
            />
          </FormItem>
        </el-col>
      </el-row>

      <tree-select-panel
        ref="userSelectTree"
        leaf-key="user"
        :local-search="false"
        :selected-data="selectedUsers"
        :selected-node-filter="selectedNodeFilter"
        :to-select-title="$t('pages.selectableBindUser')"
        :selected-title="$t('pages.selectedBindUser')"
        height="350px"
      >
      </tree-select-panel>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="clearBind">{{ $t('pages.clearBind') }}</el-button>
      <el-button :loading="submitting" type="primary" @click="updateBind">{{ $t('button.confirm') }}</el-button>
      <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import TreeSelectPanel from '@/components/TreeSelectPanel'
import { bindUser } from '@/api/system/terminalManage/terminal'
import { getUserNodeByTermId } from '@/api/system/terminalManage/user'

export default {
  name: 'BindUserDlg2',
  components: { TreeSelectPanel },
  data() {
    return {
      visible: false,
      submitting: false,
      selectedUsers: [],
      userNames: [],
      temp: {},
      rules: {
        userId: [{ required: true, trigger: 'change', validator: this.userValidator }]
      }
    }
  },
  computed: {
    checkedKeys() {
      if (!this.temp || !this.temp.userName) {
        return []
      }
      return [{ key: 'User' + this.temp.userId, label: this.temp.userName }]
    }
  },
  methods: {
    userValidator(rule, value, callback) {
      if (this.temp.loginMode === 1 && !value) {
        callback(new Error(this.$t('pages.terminal_text13')))
      } else {
        callback()
      }
    },
    show({ id, name, loginMode, userId, userName }) {
      this.temp = { id, name, loginMode, userId, userName }
      this.selectedUsers.splice(0)
      this.userNames.splice(0)
      this.visible = true
      this.$nextTick(() => {
        const tree = this.$refs['userSelectTree']
        if (tree) {
          tree.clearSelectedNode()
          tree.clearFilter()
        }
        getUserNodeByTermId(id).then(respond => {
          if (respond.data) {
            const checkedKeys = []
            respond.data.forEach(nodeData => {
              checkedKeys.push(nodeData.id)
              this.userNames.push(nodeData.label)
            })
            tree && tree.setCheckedKeys(checkedKeys)
          }
        })
      })
    },
    userTreeNodeCheckChange(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.temp.userId = data.dataId
        this.temp.userName = data.label
      } else {
        return false
      }
    },
    userSelectedChange(selectKey, data) {
      if (!selectKey) {
        this.temp.userId = 0
        this.temp.userName = ''
      } else if (data && data.length > 0) {
        const userId = data[0].dataId || this.temp.userId
        const name = data[0].label || this.temp.userName
        this.temp.userId = userId
        this.temp.userName = name
      }
    },
    selectedNodeFilter(nodes) {
      const that = this
      let result = []
      nodes.forEach(node => {
        if (node.id.indexOf('G' + node.dataId) < 0) { // 不是终端组节点，也不是自动登录节点时，才添加到选中节点中
          result.push(node)
        } else if (node.children && node.children.length > 0) {
          const childrenNodes = that.selectedNodeFilter(node.children)
          result = result.concat(childrenNodes)
        }
      })
      return result
    },
    clearBind() {
      this.$confirmBox(this.$t('pages.terminal_text6'), this.$t('text.prompt')).then(() => {
        this.bindUser(null, null, null, this.$t('pages.terminal_text7'), null)
      }).catch(() => {})
    },
    updateBind() {
      this.$refs['bindForm'].validate((valid) => {
        if (valid) {
          const bindUserIds = []
          const bindUserNames = []
          const bindGroupIds = []
          this.$refs['userSelectTree'].forEachSelectedData(nodeData => {
            if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
              bindUserIds.push(nodeData.dataId)
            } else {
              bindGroupIds.push(nodeData.dataId)
            }
            bindUserNames.push(nodeData.label)
          })
          const msg = bindUserIds.length > 0 || bindGroupIds.length > 0 ? this.$t('pages.terminal_text5') : this.$t('pages.terminal_text4')
          this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
            this.bindUser(this.temp.userId, bindUserIds, bindUserNames, this.$t('text.bindSuccess'), bindGroupIds)
          }).catch(() => {})
        }
      })
    },
    bindUser(autoLoginUserId, bindUserIds, bindUserNames, msg, bindGroupIds) {
      this.submitting = true
      bindUser({
        id: this.temp.id,
        autoLoginUserId: autoLoginUserId,
        userIds: bindUserIds,
        bindGroupIds: bindGroupIds,
        userNames: bindUserNames
      }).then(() => {
        this.submitting = false
        this.visible = false
        this.$emit('change')
        this.$notify({ title: this.$t('text.success'), message: msg, type: 'success', duration: 2000 })
      }).catch(() => {
        this.submitting = false
      })
    },
    handleClose() {
      this.$emit('end')
    }
  }
}
</script>
