<template>
  <div class="app-container">
    <div class="tree-container">
      <strategy-target-tree
        ref="terminalTree"
        expand-on-click-node
        :showed-tree="['terminal']"
        :terminal-filter-key="terminalFilter"
        refresh-offline-terminal-status
        @data-change="handleTreeNodeChange"
      />
    </div>
    <div class="table-container" style="overflow: auto;">
      <Form ref="termForm" class="term-form" :model="temp" label-position="right" label-width="82px">
        <el-divider content-position="left">{{ $t('route.terminal') }}</el-divider>
        <div class="term-box">
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.terminalCode')">
                <div v-if="!temp.termId" class="term-form__error">{{ error.termId }}</div>
                <span v-else :title="temp.termId">{{ temp.termId }}</span>
              </FormItem>
            </el-col>
            <template v-if="!!temp.termId">
              <el-col :span="12">
                <FormItem :label="$t('pages.terminalName')">
                  <span :title="temp.termName">{{ temp.termName }}</span>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.terminalType')">
                  <span :title="temp.typeName">{{ temp.typeName }}</span>
                </FormItem>
              </el-col>
              <el-col v-show="!!temp.pkgVer" :span="12">
                <FormItem :label="$t('table.terminalVersion')">
                  <span :title="temp.pkgVer">{{ temp.pkgVer }}</span>
                </FormItem>
              </el-col>
            </template>
          </el-row>
        </div>
        <el-divider content-position="left">{{ $t('pages.uninstallVerify') }}</el-divider>
        <div class="term-box">
          <div class="uninstall-tips">
            <i class="el-icon-info"/>
            <span>{{ $t('pages.pleaseContactManufacturerMsg', { tips: manufacturerTips }) }}</span>
          </div>
          <FormItem v-if="(temp.type & 0xf0) === 0x80" label-width="2px">
            <common-downloader
              :name="getOTermFileName"
              :button-name="$t('pages.exportOTermInfo')"
              button-type="primary"
              button-size="mini"
              button-icon=""
              @download="handleExport"
            />
          </FormItem>
          <FormItem v-else :label="$t('pages.uninstallCode')">
            <el-input :value="temp.uninstallCode" readonly/>
          </FormItem>
          <FormItem :label="$t('pages.captcha')" :error="error.checkCode" required>
            <el-input v-model="temp.checkCode" maxlength="10" clearable @blur="handleCheckCodeBlur" @input="handleCheckCodeInput"/>
          </FormItem>
        </div>
        <el-divider content-position="left">{{ $t('text.deleteInfo', { info: $t('pages.data')}) }}</el-divider>
        <div class="term-box">
          <FormItem label-width="2px">
            <el-tooltip class="item" effect="dark" :content="$t('pages.delTerminal_text0')" placement="bottom-start">
              <el-checkbox v-model="temp.deleteTerm">{{ $t('pages.delTerminal') }}</el-checkbox>
            </el-tooltip>
          </FormItem>
          <FormItem v-if="temp.deleteTerm" v-permission="'106'" label-width="2px">
            <el-tooltip class="item" effect="dark" :content="$t('pages.delUser_text')" placement="bottom-start">
              <el-checkbox v-model="temp.deleteUser">{{ $t('pages.delUser') }}</el-checkbox>
            </el-tooltip>
          </FormItem>
        </div>
        <el-button class="uninstall-btn" type="primary" size="mini" :disabled="!temp.termId" @click="handleDelete">
          {{ $t('pages.uninstall') }}
        </el-button>
      </Form>
    </div>

    <el-dialog
      :title="$t('text.prompt')"
      :close-on-click-modal="false"
      :modal="false"
      show-close
      width="400px"
      :visible.sync="visible"
      @opened="$refs['pwdInput'].focus()"
    >
      <Form :model="temp" label-position="top" @submit.native.prevent>
        <FormItem class="pwd-item" :label="$t('pages.enterAdminPwd')" :error="error.password" required>
          <el-input
            ref="pwdInput"
            v-model="temp.password"
            type="password"
            maxlength="100"
            show-password
            clearable
            @blur="handlePasswordBlur"
            @input="handlePasswordInput"
            @keyup.enter.native="handleConfirmDelete"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleConfirmDelete">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUninstallCode, deleteUTermByCode } from '@/api/system/terminalManage/uterm'
import { downloadExtInfo } from '@/api/dataEncryption/encryption/offlineTerminal'
import { validatePassword } from '@/api/user'
import { getTermTypeDict } from '@/utils/dictionary'
import moment from 'moment'
import CommonDownloader from '@/components/DownloadManager/common'
import { aesEncode, formatAesKey } from '@/utils/encrypt';

export default {
  name: 'AuthorizeUninstall',
  components: { CommonDownloader },
  data() {
    return {
      submitting: false,
      visible: false,
      temp: {},
      defaultTemp: {
        termId: undefined,
        termName: undefined,
        pkgVer: undefined,
        uninstallCode: undefined,
        checkCode: undefined,
        password: undefined,
        deleteTerm: true,
        deleteUser: true
      },
      error: {
        termId: undefined,
        checkCode: undefined,
        password: undefined
      },
      termTypeDict: {}
    }
  },
  computed: {
    containerHeight() {
      const padding = Object.values(this.error).filter(item => !!item).length
      return 385 + 10 * padding
    },
    manufacturerTips() {
      if (!this.temp.termId) {
        return ''
      }
      if ((this.temp.type & 0xf0) === 0x80) {
        return this.$t('pages.manufacturerTipsOTerm')
      }
      return this.$t('pages.manufacturerTipsUTerm')
    }
  },
  watch: {
    'temp.termId': {
      immediate: true,
      handler(value) {
        if (value) {
          this.error.termId = undefined
        } else {
          this.error.termId = this.$t('pages.pleaseSelectContent', { content: this.$t('pages.terminal') })
        }
      }
    }
  },
  created() {
    this.setMenuAccessStatus('authorizeUninstall')
    getTermTypeDict().forEach(item => {
      this.termTypeDict[item.value] = item.label
    })
    this.show()
  },
  beforeRouteEnter(to, from, next) {
    const isPageLoaded = window.getMenuAccessStatus('authorizeUninstall')
    const isRefresh = from.path == '/redirect/config/authorizeUninstall'
    // 通过 搜索、已访问过、右键菜单刷新
    if (to.params.search || isPageLoaded || isRefresh) {
      next()
    } else {
      next('/404')
    }
  },
  methods: {
    show(terminalId, type) {
      if (terminalId) {
        getUninstallCode(terminalId).then(res => {
          this.temp = { ...this.defaultTemp, ...res.data, typeName: this.termTypeDict[type] }
        })
      } else {
        this.temp = { ...this.defaultTemp }
      }
      this.error.checkCode = undefined
      this.error.password = undefined
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return ((type & 0xf0) === 32 || (type & 0xf0) === 0x80) && !!node.dataCode
    },
    handleTreeNodeChange(tab, node) {
      if (node.dataType === 'G' || node.dataId == this.temp.termId) {
        return
      }
      this.show(node.dataId, node.dataType)
    },
    handleCheckCodeBlur() {
      this.checkCodeValidate()
    },
    handleCheckCodeInput() {
      this.error.checkCode = undefined
    },
    handlePasswordBlur() {
      this.passwordValidate()
    },
    handlePasswordInput() {
      this.error.password = undefined
    },
    getOTermFileName() {
      return `${this.temp.termName}(${this.temp.termId})_${moment().format('YYYYMMDD')}.info`
    },
    handleExport(file) {
      const data = { terminalId: this.temp.termId, terminalName: this.temp.termName, licInfo: this.temp.uninstallCode }
      const opts = { file, jwt: true, topic: 'offlineTerm' }
      downloadExtInfo(data, opts)
    },
    handleDelete() {
      if (!this.temp.termId) {
        this.$message({
          message: this.$t('pages.pleaseSelectContent', { content: this.$t('route.terminal') }),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (this.checkCodeValidate()) {
        this.visible = true
      }
    },
    handleConfirmDelete() {
      this.submitting = true
      this.passwordValidate(true)
        // .then(() => this.$confirmBox(this.$t('pages.confirmUninstallTerminalMsg', { terminal: this.temp.termName }), this.$t('text.prompt')))
        .then(this.deleteUTerm)
        .then(this.handleDeletedOK)
        .catch(reason => {
          if (reason) {
            this.visible = false
            if (reason.response && reason.response.data) {
              this.error.checkCode = reason.response.data.data
            }
          }
          this.submitting = false
        })
    },
    deleteUTerm() {
      const data = { ...this.temp, ids: '', password: '' }
      if (data.deleteTerm) {
        data.deleteUser = this.temp.deleteUser
      } else {
        data.deleteUser = false
      }
      return deleteUTermByCode(data)
    },
    handleDeletedOK() {
      const stgTreeRef = this.$refs.terminalTree
      const treeRef = stgTreeRef.$refs[stgTreeRef.checkedTab][0].$refs.tree
      treeRef.remove({ id: 'T' + this.temp.termId })
      this.visible = false
      this.submitting = false
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('pages.uninstallSuccess'),
        type: 'success',
        duration: 5000
      })
      this.show()
    },
    checkCodeValidate() {
      if (this.temp.checkCode) {
        this.error.checkCode = undefined
        return true
      } else {
        this.error.checkCode = this.$t('text.cantNullInfo', { info: this.$t('pages.captcha') })
        return false
      }
    },
    passwordValidate(notOnlyRequired) {
      if (!this.temp.password) {
        this.error.password = this.$t('text.cantNullInfo', { info: this.$t('pages.pwdInfo') })
        return Promise.reject()
      }
      if (notOnlyRequired) {
        const tempData = { password: aesEncode(this.temp.password, formatAesKey('tr838408', '')) }
        return validatePassword(tempData).then(respond => {
          if (respond.data) {
            this.error.password = undefined
            return Promise.resolve()
          } else {
            this.error.password = this.$t('pages.administratorPasswordError')
            return Promise.reject()
          }
        })
      } else {
        this.error.password = undefined
        return Promise.resolve()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-container {
    .term-form {
      padding-left: 25px;
      margin: 20px 0 0 20px;
      width: 600px;
      >>>.el-divider__text {
        font-size: 15px;
        font-weight: 700;
      }
      .term-box {
        padding: 5px 0;
      }
      .el-col>.el-form-item {
        margin: 10px 0;
      }
      >>>.el-form-item__label, >>>.el-form-item__content {
        line-height: 30px;
      }
      >>>.el-form-item__label:after {
        content: '：';
      }
      >>>.el-form-item__content {
        span {
          cursor: default;
        }
        .el-button span {
          cursor: pointer;
        }
      }
      .term-form__error {
        color: #F56C6C;
        font-size: 12px;
      }
    }

    .uninstall-tips {
      height: auto;
      line-height: 28px;
      color: #2674b2;
      font-size: 14px;
      margin: 10px 0;
    }

    .uninstall-btn {
      margin-top: 10px;
      float: right;
    }

    .pwd-item>>>.el-form-item__label {
      &:before {
        left: 0;
      }
      span {
        margin-left: 4px;
      }
    }
  }
</style>
