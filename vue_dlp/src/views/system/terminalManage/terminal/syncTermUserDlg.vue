<template>
  <el-dialog
    v-if="visible"
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="visible"
    width="820px"
  >
    <div slot="title" class="el-dialog__title">
      {{ $t('pages.synchronizeInformation') }}
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          {{ $t('pages.syncTermText1') }} <br/>
          {{ $t('pages.syncTermText2') }} <br/>
          {{ $t('pages.syncTermText3') }}
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <Form ref="syncForm" :model="temp">
      <el-divider class="dividerClass1" content-position="left">{{ $t('pages.autoSync') }}</el-divider>
      <el-checkbox v-model="temp.syncUserGroupToTermGroup" :true-label="1" :false-label="0">{{ $t('pages.termGroupSyncUserGroupMessage') }}</el-checkbox>
      <el-divider class="dividerClass2" content-position="left">{{ $t('pages.manualSync') }}</el-divider>
      <el-row>
        <el-col :span="14">
          <FormItem :label="$t('pages.syncMode')">
            <el-select v-model="syncType" style="width: 320px" @change="handleSearch">
              <el-option value="1" :label="$t('pages.syncMode2')"/>
              <el-option value="2" :label="$t('pages.syncMode1')"/>
              <el-option value="3" :label="$t('pages.syncMode4')"/>
              <el-option value="4" :label="$t('pages.syncMode3')"/>
              <!--<el-option value="5" :label="$t('pages.syncMode5')"/>-->
            </el-select>
          </FormItem>
        </el-col>
        <el-col :span="10" style="height: 30px; display: flex; align-items: center;">
          <el-checkbox v-model="query.filterSameAccountTerm" @change="handleSearch">{{ $t('pages.filterOneUserBindMultipleTerminal') }}</el-checkbox>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="14" style="height: 30px; display: flex; align-items: center;">
          <el-checkbox v-model="checkAll" style="margin-top: 5px" @change="changeSelectedAll">{{ $t('pages.checkAllPagesData') }}</el-checkbox>
        </el-col>
        <el-col :span="4" style="width: 120px;">
          <el-select v-model="queryType" style="width: 120px">
            <el-option value="term" :label="$t('pages.terminalName')"/>
            <el-option value="user" :label="$t('pages.userName')"/>
            <!--<el-option value="account" :label="$t('table.userAccount')"/>-->
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input v-model="queryStr" v-trim :placeholder="$t('pages.plzEnter')" clearable maxlength=""/>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch()">
            {{ $t('table.search') }}
          </el-button>
        </el-col>
      </el-row>
    </Form>
    <grid-table
      ref="gridTable"
      row-key="termId"
      saved-selected-prop="termName"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :after-load="afterLoad"
      :multi-select="true"
      :height="340"
      show-pager
      pager-small
      @select="selectRemoteData"
      @select-all="selectAllRemoteData"
    />
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="submitData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTermUserSyncPage, updateTermUserSync, needSyncTermUserSync } from '@/api/system/terminalManage/terminal'
import { listByOptIds } from '@/api/system/configManage/adDomain';

export default {
  name: 'SyncTermUserDlg',
  data() {
    return {
      syncTypeOptions: {
        1: { syncGroup: 1, syncName: undefined, syncAccount: undefined },
        2: { syncGroup: 2, syncName: undefined, syncAccount: undefined },
        3: { syncGroup: undefined, syncName: 1, syncAccount: undefined },
        4: { syncGroup: undefined, syncName: 2, syncAccount: undefined }
        // 5: { syncGroup: undefined, syncName: undefined, syncAccount: 1 }
      },
      formItems: [
        {
          label: this.$t('pages.syncGroup'),
          type: 'syncGroup',
          opts: [
            { value: 1, text: this.$t('pages.synchronousGroup') },
            { value: 2, text: this.$t('pages.synchronousGroup2') }
          ]
        },
        {
          label: this.$t('pages.syncName'),
          type: 'syncName',
          opts: [
            { value: 1, text: this.$t('pages.synchronousName') },
            { value: 2, text: this.$t('pages.synchronousName2') }
          ]
        }
      ],
      searchOpts: {
        termName: this.$t('pages.terminalName'),
        userName: this.$t('pages.userName')
      },
      queryType: 'term',
      queryStr: '',
      colModel: [
        { prop: 'termName', label: 'terminalName', width: '110', sort: 'custom', fixed: true },
        { prop: 'termId', label: 'terminalCode', width: '90', sort: 'custom' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100' },
        { prop: 'userName', label: 'userName1', width: '120', sort: 'custom' },
        { prop: 'account', label: 'userAccount', width: '100', sort: 'custom' },
        { prop: 'userGroupName', label: 'userGroup', width: '100' }
      ],
      visible: false,
      submitting: false,
      temp: {
        syncGroup: undefined,
        syncName: undefined,
        syncUserGroupToTermGroup: 0   // 操作员登录后自动将终端的分组同步为操作员的分组
      },
      query: {
        page: 1,
        termName: '',
        userName: '',
        account: '',
        filterSameAccountTerm: true
      },
      syncType: '1',
      checkAll: false, //  全选
      backupSelectedIds: [], // 备份当前选中的id
      backupUnSelectedIds: [] // 备份当前未选中的id
    }
  },
  methods: {
    show() {
      this.getSyncEnable()
      this.visible = true
      this.syncType = '1'
      this.temp.syncGroup = undefined
      this.temp.syncName = undefined
      this.query.termName = ''
      this.query.userName = ''
      this.query.filterSameAccountTerm = true
      this.queryStr = ''
      this.checkAll = false
      this.backupSelectedIds = []
      this.backupUnSelectedIds = []
    },
    resetQuery() {
      this.query = {
        page: 1,
        termName: '',
        userName: ''
      }
    },
    rowDataApi: function(option) {
      const page = option.page
      const syncTypeOption = this.syncTypeOptions[this.syncType]
      const query = Object.assign(option, syncTypeOption, this.query)
      query.page = page
      return getTermUserSyncPage(query)
    },
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        // 选中当前页面所有的终端,处理未选中的数据
        if (rowData.length > 0 && this.checkAll) {
          const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
          rowData.forEach(r => {
            if (!backupUnSelectedIdsSet.has(r['termId'])) {
              this.$refs.gridTable.toggleRowSelection(r, true)
            }
          })
        } else { // 未选中当前页面所有的终端,处理已选中的数据
          const backupSelectedIdsSet = new Set(this.backupSelectedIds)
          rowData.forEach(r => {
            if (backupSelectedIdsSet.has(r['termId'])) {
              this.$refs.gridTable.toggleRowSelection(r, true)
            }
          })
        }
      })
      return rowData;
    },
    handleSearch() {
      if ('term' === this.queryType) {
        this.query.userName = ''
        // this.query.account = ''
        this.query.termName = this.queryStr
      } else if ('user' === this.queryType) {
        this.query.userName = this.queryStr
        // this.query.account = ''
        this.query.termName = ''
      }
      // else {
      //   this.query.userName = ''
      //   this.query.account = this.queryStr
      //   this.query.termName = ''
      // }
      this.$refs.gridTable.selectedDatasDelete()
      this.$refs.gridTable.execRowDataApi(this.query)
    },
    submitData: async function() {
      let items = this.$refs.gridTable.getSelectedDatas();
      const syncTypeOption = this.syncTypeOptions[this.syncType]
      if (this.checkAll) {
        const checkAll = this.checkAll
        const unSelectedIds = this.backupUnSelectedIds || []
        const selectedIds = this.backupSelectedIds || []
        if ('term' === this.queryType) {
          this.query.userName = ''
          this.query.termName = this.queryStr
        } else if ('user' === this.queryType) {
          this.query.userName = this.queryStr
          this.query.termName = ''
        }
        const query = Object.assign({}, this.query, { page: null })
        const data = { ...syncTypeOption, items, checkAll, unSelectedIds, selectedIds, ...query }
        const result = await needSyncTermUserSync(data)
        items = result.data || []
      }

      const data = { ...this.temp, ...syncTypeOption, items }
      let message = this.$t('pages.autoSyncSuccess')
      message += (items.length === 0 ? '' : '，' + this.$t('pages.manualSyncSuccess'))
      updateTermUserSync(data).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: message,
          type: 'success',
          duration: 2000
        });
        // this.visible = false;
        this.$emit('submitEnd')
        this.$refs.gridTable.execRowDataApi()
        this.$refs.gridTable.selectedDatasDelete()
      })
    },
    getSyncEnable() {
      listByOptIds({ optIds: '2007' }).then(res => {
        this.temp.syncUserGroupToTermGroup = res.data ? res.data[0].optVal : 0;
      })
    },
    changeSelectedAll() {
      this.backupSelectedIds.splice(0) // 备份已选中的行的id
      this.backupUnSelectedIds.splice(0)
      if (this.checkAll) {
        this.$refs.gridTable.toggleAllSelection()
      } else {
        if (this.$refs.gridTable.getSelectedDatas().length > 0) {
          this.$refs.gridTable.clearSelection()
        }
      }
    },
    selectRemoteData(selection, row) {
      this.$nextTick(() => {
        const id = row['id']
        const selectedIds = this.$refs.gridTable.getSelectedKeys()
        const isSelect = selectedIds.indexOf(id) >= 0
        // 选中全部的情况下，缓存取消勾选的数据
        if (this.checkAll) {
          // 当前 row 在缓存的未勾选的数据ids中的位置
          const index = this.backupUnSelectedIds.indexOf(id)
          if (isSelect) {
            // 如果是勾选，则移除
            index >= 0 && this.backupUnSelectedIds.splice(index, 1)
          } else {
            // 如果是取消勾选，则添加
            index == -1 && this.backupUnSelectedIds.push(id)
          }
        } else {
          // 未选中的情况下，缓存已勾选的数据
          // 当前 row 在缓存的勾选的数据ids中的位置
          const index = this.backupSelectedIds.indexOf(id)
          if (isSelect) {
            // 如果是勾选，且未添加到数组，则添加
            if (index == -1) {
              this.backupSelectedIds.push(id)
            }
          } else {
            // 如果是取消勾选，且已添加到数组，则移除
            if (index >= 0) {
              this.backupSelectedIds.splice(index, 1)
            }
          }
        }
      })
    },
    selectAllRemoteData(selection) {
      this.$nextTick(() => {
        const rowDatas = this.$refs.gridTable.rowData
        // 当前页面选中数据的长度为0，证明是取消全选
        const isUnselectAll = this.$refs.gridTable.getSelectedKeys().length === 0
        // 选中全部的情况下，缓存取消勾选的数据
        if (this.checkAll) {
          const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
          rowDatas.forEach(r => {
            const id = r['termId']
            const isExist = backupUnSelectedIdsSet.has(id)
            // 取消全选且未缓存，则添加
            if (isUnselectAll && !isExist) {
              backupUnSelectedIdsSet.add(id)
            }
            // 全选且已缓存，则删除
            if (!isUnselectAll && isExist) {
              backupUnSelectedIdsSet.delete(id)
            }
          })
          // 根据 Set 更新 this.backupUnSelectedIds
          this.backupUnSelectedIds = Array.from(backupUnSelectedIdsSet)
        } else {
          // 未选中全部的情况下，缓存已勾选的数据
          const backupSelectedIdsSet = new Set(this.backupSelectedIds)
          rowDatas.forEach(r => {
            const id = r['termId']
            const isExist = backupSelectedIdsSet.has(id)
            // 全选且已未缓存，则添加
            if (!isUnselectAll && !isExist) {
              backupSelectedIdsSet.add(id)
            }
            // 取消全选且已缓存，则删除
            if (isUnselectAll && isExist) {
              backupSelectedIdsSet.delete(id)
            }
          })
          // 根据 Set 更新 this.backupSelectedIds
          this.backupSelectedIds = Array.from(backupSelectedIdsSet)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }
  .dividerClass1 {
    margin: 0 0 15px 10px !important;
  }
  .dividerClass2 {
    margin: 18px 0 15px 10px !important;
  }
</style>
