<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.allocationTermBindUser')"
    :visible.sync="dialogVisible"
    width="800px"
    @close="cancel"
  >
    <el-row>
      <div style="display:flex;">
        <div>{{ $t('pages.allocationDeletedUser') }}</div>
        <div class="deleteUser" :title="deleteUserAccounts">{{ deleteUserAccounts }}</div>
      </div>
    </el-row>
    <el-row>
      <div style="color: #0c60a5;padding-top: 5px; padding-bottom: 5px">
        {{ $t('pages.allocationHint') }}
      </div>
    </el-row>
    <grid-table
      ref="toUpdateTermTable"
      :col-model="colModel"
      :row-datas="rowData"
      :multi-select="false"
      :height="358"
      :show-pager="false"
      @selectionChangeEnd="handleSelectionChange"
    />
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="allocationAutoUser">{{ $t('button.confirm') }}</el-button>
      <el-button @click="cancel">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>

</template>

<script>
import { updateLoginMode } from '@/api/system/terminalManage/terminal'
import { validUserForbidden } from '@/api/system/terminalManage/user'

export default {
  name: 'AllocationAutoUserDlg',
  data() {
    return {
      dialogVisible: false,
      colModel: [
        { prop: 'name', label: 'terminalName', width: '120' },
        { prop: 'id', label: 'terminalCode', width: '100' },
        { prop: 'loginMode', label: 'loginMode', width: '150', type: 'select', options: [
          { value: 0, label: 'manualLogin' }, { value: 1, label: 'userAutoLogin' }, { value: 2, label: 'domainAutoLogin' }
        ],
        change: this.changeTableRowLoginMode },
        {
          prop: 'userId',
          label: 'autoLoginUser',
          width: '150',
          type: 'treeSelect',
          checkedKeysFieldName: 'checkedKeys',
          isFilter: true,
          clearable: true,
          localSearch: false,
          disabled: (col, row) => { return row.loginMode === 0 },
          nodeChange: this.changeTableRowLoginUser
        }
        // { prop: '', fixedWidth: 40, type: 'icon', iconClass: 'delete', iconFormatter: () => { return true }, style: 'cursor: pointer;', click: this.removeTerminal }
      ],
      rowData: [],
      submitting: false,
      deleteUserAccounts: '', //  被删除的自动操作员账号
      deleteUserIds: [] //  被删除的自动登录操作员id
    }
  },
  created() {
  },
  methods: {
    show(terms, deleteUserList) {
      const userAccounts = []
      this.deleteUserIds = []
      terms.forEach(item => {
        item.userId = null
        item.user = {}
        item.checkedKeys = []
      })
      deleteUserList.forEach(item => {
        if (item.id && item.account) {
          this.deleteUserIds.push(item.id)
          userAccounts.push(item.account)
        }
      })

      this.rowData = terms;
      this.deleteUserAccounts = userAccounts ? userAccounts.join(',') : ''
      this.dialogVisible = true
    },
    allocationAutoUser() {
      const formData = []
      for (let i = 0; i < this.rowData.length; i++) {
        const rowData = this.rowData[i]
        if (rowData.loginMode === 1 && !rowData.userId) {
          this.$message({ message: this.$t('pages.setAutoLoginUserMessage'), type: 'error', duration: 2000 })
          return
        }
        formData.push({ id: rowData.id, loginMode: rowData.loginMode, userId: rowData.loginMode === 1 ? rowData.userId : 0 })
      }
      this.$confirmBox(this.$t('pages.confirmDeleteUserMsg'), this.$t('text.prompt')).then(() => {
        updateLoginMode({ terminals: formData }).then(respond => {
          this.dialogVisible = false
          this.$emit('submitEnd', 'confirm')
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.editSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {
      })
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('submitEnd', 'cancel')
    },
    handleSelectionChange(val) {
    },
    changeTableRowLoginMode(row, col, rowIndex) {
      this.resetMultiUpdateLoginModeParams()
    },
    resetMultiUpdateLoginModeParams() {
      this.$refs['multiUserSelectTree'] && this.$refs['multiUserSelectTree'].clearSelectedNode()
    },
    changeTableRowLoginUser(nodeData, node, vm, rowData, col) {
      return new Promise((resolve, reject) => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          // 判断是否为已被删除的操作员
          const isUserNode = nodeData && nodeData.id && nodeData.id.startsWith('U')
          const userDeleted = this.deleteUserIds.findIndex(item => item == nodeData.dataId) > -1
          if (isUserNode && userDeleted) {
            this.$message({
              message: this.$t('pages.deletedUserNotSelectedMsg'),
              type: 'error',
              duration: 2000
            })
            resolve(false)
          }

          // 判断是否为已被禁用的操作员
          const res = validUserForbidden(nodeData.dataId);
          if (res.data) {
            this.$message({
              message: this.$t('pages.unUsedUserNotSelectedMsg'),
              type: 'error',
              duration: 2000
            })
            resolve(false)
          }

          rowData.userId = nodeData.dataId
          rowData.checkedKeys = [{ key: nodeData.dataId, label: nodeData.label }]
          resolve()
        } else {
          resolve(false)
        }
      })
    }
  }
}
</script>

<style scoped>
  .deleteUser {
    width: 80%;
    color: #0c60a5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
