<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :local-search="false"
        :get-search-list="getSearchListFunction"
        :render-content="renderContent"
        :default-expanded-keys="defaultExpandedKeys"
        @current-change="nodeChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddTerm">{{ $t('button.add') }}</el-button>
        <el-button v-permission="'103'" icon="el-icon-delete" size="mini" @click="handleUninstall">
          {{ $t('pages.uninstall') }}
        </el-button>
        <el-dropdown v-permission="'112'" style="padding-left: 10px;" @command="handleRemoteControl">
          <el-button size="mini" style="text-transform: capitalize">
            {{ $t('pages.remoteControl') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(value, key) in agreementTypeItems" v-show="key!=99 && hasUnlockPermission(key)" :key="key" :command="key">{{ value }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown v-permission="'112'" style="padding-left: 10px;" @command="handleCleanClick">
          <el-button size="mini" style="text-transform: capitalize">
            {{ '清理终端' }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-permission="'395'" :command="1" icon="el-icon-remove-outline">
              {{ $t('pages.offline_terminal_text18') }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content"> {{ $t('pages.offline_terminal_text32') }} </div>
                <i class="el-icon-info"/>
              </el-tooltip>
            </el-dropdown-item>
            <el-dropdown-item v-permission="'394'" :command="2" icon="el-icon-refresh-right">{{ $t('pages.offline_terminal_text21') }}</el-dropdown-item>
            <el-dropdown-item v-if="isSuperRole || isSysRole" :command="3" icon="el-icon-unlock">{{ $t('pages.offline_terminal_text20') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <!-- <el-button icon="el-icon-setting" size="mini" @click="handleUpdateAutoLoginUser">
          配置自动登录操作员
        </el-button>-->
        <!-- <el-button icon="el-icon-circle-close" size="mini" @click="handleRecycle">
          回收终端
          <el-tooltip effect="dark" placement="right">
            <div slot="content">
              自动清理的终端回收后，可以被任意新接入终端使用
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
        </el-button> -->
        <el-button v-permission="'257'" icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-dropdown style="padding-left: 10px;" @command="handleMoreClick">
          <el-button size="mini">
            {{ $t('pages.moreOperate') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="4" icon="el-icon-edit">{{ $t('pages.changeLoginMode') }}</el-dropdown-item>
            <el-dropdown-item v-permission="'104'" :command="1" icon="el-icon-setting">{{ $t('pages.setCollectionServer') }}</el-dropdown-item>
            <el-dropdown-item v-permission="'603'" :command="3" icon="el-icon-view" :disabled="!selectedData || !(remoteable && selectedData.length === 1)">{{ $t('pages.terminalVerificationCode') }}</el-dropdown-item>
            <el-dropdown-item v-permission="'603'" :command="9" icon="el-icon-unlock">{{ $t('pages.getGeneralCode') }}</el-dropdown-item>
            <el-dropdown-item :command="6" icon="el-icon-set-up">{{ $t('pages.synchronizeInformation') }}</el-dropdown-item>
            <!--<el-dropdown-item :command="7" icon="el-icon-connection" >{{ $t('pages.nameSpecifications') }}</el-dropdown-item>-->
            <el-dropdown-item :command="8" icon="el-icon-document-copy">{{ $t('pages.batchChangeGroups') }}</el-dropdown-item>
            <el-dropdown-item :command="10"><svg-icon icon-class="usb"/> {{ $t('pages.uDiskTerminalManagement') }}</el-dropdown-item>
            <el-dropdown-item v-if="!isTrialDlp()" :command="11"><svg-icon icon-class="computerManage"/> {{ $t('pages.offlineTermManagement') }}</el-dropdown-item>
            <el-dropdown-item :command="13" icon="el-icon-edit">{{ $t('pages.newTerminalTermVersionRestriction') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!--<el-button icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleMove">
          移动分组
        </el-button>
        <el-button disabled size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.searchIdAndName" v-trim clearable :placeholder="$t('pages.terminalNameOrNumber')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('pages.terminalType')">
                <el-select v-model="query.type" style="width: 100%;">
                  <el-option v-for="(type, i) in terminalTypes" v-show="!type.hidden" :key="i" :label="type.label" :value="type.value"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.terminalStatus')">
                <el-select v-model="query.status" style="width: 100%;" @change="statusChange">
                  <el-option v-for="(status, i) in terminalStatus" :key="i" :label="status.label" :value="status.value"></el-option>
                </el-select>
              </FormItem>
              <FormItem v-show="query.status === 0" :label="$t('pages.offlineTime')" label-width="80px" :extra-width="{en: 9}">
                <i18n path="pages.offline_time">
                  <el-input
                    slot="day"
                    v-model="offLineType"
                    v-trim
                    clearable
                    :min="0"
                    :max="99999"
                    maxlength="5"
                    size="small"
                    style="width: 90px"
                    @change="offLineTypeChange"
                    @input="offLineType=offLineType.replace(/^(0+)|[^\d]+/g,'')"
                  />
                </i18n>
              </FormItem>
              <FormItem :label="$t('pages.terminalName')">
                <el-input v-model="query.name" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.computerName')">
                <el-input v-model="query.computerName" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.terminalCode')">
                <el-input v-model="query.searchId" v-trim clearable maxlength="11"/>
              </FormItem>
              <FormItem :label="$t('table.terminalVersion')">
                <el-input v-model="query.version" v-trim clearable maxlength="11"/>
              </FormItem>
              <FormItem label="IP">
                <el-input v-model="query.ip" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.macAddr')">
                <el-input v-model="query.mac" v-trim clearable maxlength="100"/>
              </FormItem>
              <FormItem :label="$t('table.serverId')">
                <el-input v-model.number="query.serverId" v-trim clearable :maxlength="4" :placeholder="this.$t('pages.serverLibrary_text5')" @input="number('serverId')"/>
              </FormItem>
              <FormItem :label="$t('table.loginMode')">
                <el-select v-model="query.loginMode" style="width: 100%;">
                  <el-option :value="null" :label="$t('table.allLoginMode')"></el-option>
                  <el-option v-for="item in loginModeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.lastLoginUser')">
                <tree-select
                  clearable
                  is-filter
                  :local-search="false"
                  :default-expand-all="false"
                  node-key="id"
                  :checked-keys="(query.lastLoginUserId) ? ['U' + query.lastLoginUserId] : []"
                  :width="296"
                  :placeholder="$t('pages.selectLastLoginUser')"
                  :rewrite-node-click-fuc="true"
                  :disabled="false"
                  :node-click-fuc="lastLoginUserTreeNodeCheckChange"
                  @change="lastLoginUserSelectedChange"
                />
              </FormItem>
              <FormItem :label="$t('pages.searchMode')">
                <el-select v-model="query.includeSubGroup" style="width: 100%;">
                  <el-option :value="true" :label="$t('pages.searchChildDeptTermInfo')"/>
                  <el-option :value="false" :label="$t('pages.searchDeptTermInfo')"/>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="() => { resetQuery(); groupTree && groupTree.setCurrentKey(null) }">{{ $t('button.reset') }}</el-button>
              <el-button ref="searchBtn" :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="terminalTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :autoload="false"
        :multi-select="multiSelect"
        :after-load="afterLoadClearRouteQuery"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <edit-terminal
      ref="editTerm"
      :title="dialogFormVisible2 ? $t('pages.viewTerminal') : $t('pages.editTerminal')"
      :group-data="groupTreeSelectData"
      :disabled="dialogFormVisible2"
      @updated="handleUpdated"
      @userChange="handleUserChange"
    />

    <el-dialog
      v-el-drag-dialog
      :title=" updateGroupForm ? i18nConcatText(this.$t('pages.group'), 'update') : agreementTypeItems[agreementType]"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible2"
      width="800px"
    >
      <Form ref="dataForm2" label-position="right" label-width="90px" style="width: 700px;">
        <input v-if="!updateGroupForm" type="text" class="autocomplete">
        <input v-if="!updateGroupForm" type="password" class="autocomplete">
        <FormItem :label="$t('pages.effectiveScope')" :extra-width="{en:50}">
          <el-tooltip v-if="windowsFilter" slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.effectiveScopeFilterMsg') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <span style="padding-left: 20px;">
            <el-radio v-model="effective" :label="1">{{ $t('pages.currentlySelectedTerminal', { len: selectLength} ) }}</el-radio>
            <el-radio v-model="effective" :label="2">{{ $t('pages.currentlyQueriedAllTerminals', { len: allResultLength} ) }}</el-radio>
          </span>
        </FormItem>
        <FormItem v-if="updateGroupForm" class="required" :label="$t('pages.updateTermName')" label-width="120px">
          <tree-select :data="treeSelectData" :checked-keys="checkedKeys" is-filter :width="296" style="width: 180px;" @change="parentIdObjChange" />
        </FormItem>
        <FormItem v-show="effective === 2" label-width="1px">
          <el-checkbox v-model="checkAll" @change="changeSelected">{{ $t('pages.checkAllPagesData') }}</el-checkbox>
        </FormItem>
        <grid-table
          v-show="effective === 1"
          ref="remoteTable"
          :col-model="remoteColModel"
          :row-datas="remoteControlTerms"
          :multi-select="true"
          :height="350"
          :show-pager="false"
        />
        <grid-table
          v-show="effective === 2"
          ref="allRemoteTable"
          :col-model="remoteColModel"
          :row-data-api="remoteRowDataApi"
          :multi-select="true"
          :height="315"
          :show-pager="true"
          :after-load="afterLoad"
          :autoload="false"
          @select="selectRemoteData"
          @select-all="selectAllRemoteData"
        />
        <!-- <FormItem v-if="!updateGroupForm" :label="$t('pages.enterAdminPwd')" label-width="180px" prop="password">
          <el-input v-model="password" type="password" />
        </FormItem> -->
        <el-row v-if="!updateGroupForm" v-permission="'370'">
          <el-col :span="8">
            <FormItem v-if="agreementType==99" label="" label-width="2px">
              <el-tooltip class="item" effect="dark" :content="$t('pages.delTerminal_text')" placement="bottom-start">
                <el-checkbox v-model="deleteTerm">{{ $t('pages.delTerminal') }}</el-checkbox>
              </el-tooltip>
            </FormItem>
          </el-col>
          <el-col :span="16">
            <FormItem v-if="agreementType==99 && deleteTerm" v-permission="'106'" label="" label-width="0px">
              <el-tooltip class="item" effect="dark" :content="$t('pages.delUser_text')" placement="bottom-start">
                <el-checkbox v-model="deleteUser">{{ $t('pages.delUser') }}</el-checkbox>
              </el-tooltip>
            </FormItem>
          </el-col>
        </el-row>
        <el-row v-if="updateGroupForm">
          <el-col :span="20">
            <FormItem label-width="1px">
              <el-checkbox v-model="syncGroup">{{ $t('pages.synchronousGroup') }}</el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!updateGroupForm" :loading="submitting" type="primary" @click="effective ===1 ? controlComputer() : controlAllComputer()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button v-else :loading="submitting" type="primary" @click="effective ===1 ? updateGroup() : updateAllGroup()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible2 = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.bindUserManage')"
      :visible.sync="dialogBindVisible"
      width="800px"
      @close="dialogBindClose"
    >
      <Form ref="terminalForm" :model="tempUser" :rules="tempUserRules" label-position="right" label-width="120px" style="width: 700px; margin-left:30px;">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.terminalName')">
              <el-input :value="tempUser.name" disabled></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.terminalCode')">
              <el-input :value="tempUser.id" disabled></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem v-show="tempUser.loginMode === 1" :label="$t('pages.autoLoginUser')" prop="userId">
              <tree-select
                clearable
                is-filter
                :local-search="false"
                :default-expand-all="false"
                node-key="id"
                :checked-keys="(tempUser.user && tempUser.user['name']) ? [{key: 'User' + tempUser.userId, label: tempUser.user['name']}] : []"
                :width="296"
                :placeholder="$t('pages.selectAutoLoginUser')"
                :rewrite-node-click-fuc="true"
                :node-click-fuc="userTreeNodeCheckChange"
                @change="userSelectedChange"
              />
            </FormItem>
          </el-col>
        </el-row>

        <tree-select-panel
          ref="userSelectTree"
          leaf-key="user"
          :check-strictly="false"
          :local-search="false"
          :default-checked-keys="defaultCheckedKeys"
          :include-child="false"
          :selected-node-filter="selectedBindedNodeFilter"
          :to-select-title="$t('pages.selectableBindUser')"
          :selected-title="$t('pages.selectedBindUser')"
          height="350px"
          @checkedData="checkedUserData"
        >
        </tree-select-panel>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="clearBinded()">{{ $t('pages.clearBind') }}</el-button>
        <el-button :loading="submitting" type="primary" @click="updateBinded()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogBindVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="vcTitle"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="vcVisible"
      :width="isEnglish() ? '500px' : '400px'"
    >
      <Form ref="vcForm" :model="vcForm" label-position="right" label-width="70px" :extra-width="{en: 30}">
        <FormItem :label="$t('pages.randomCode')">
          <el-input v-model="vcForm.random" @input="vcForm.random=vcForm.random.replace(/[^\w\.\/]/ig,'')"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.captcha')">
          <el-input v-model="vcForm.verifiCode"></el-input>
        </FormItem>
      </Form>
      <span slot="footer" class="dialog-footer" >
        <el-button
          type="primary"
          style="float:left;"
          :loading="vcLoading"
          :disabled="!vcForm.random"
          @click="getLockScreenVerify"
        >{{ $t('pages.generateCaptcha') }}</el-button>
        <el-button style="width:120px" @click="vcVisible = false">{{ $t('pages.exit') }}</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="generalCodeTitle"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="codeVisible"
      width="550px"
    >
      <Form ref="dataListForm" :model="dataList" label-position="right" label-width="10px">
        <FormItem>
          <span>{{ $t('pages.lockScreenText15') }}</span><br/>
          <span style="color: black">{{ $t('pages.lockScreenText16') }}</span>
          <span>{{ $t('pages.lockScreenText18') }}</span><span v-show="dataList.generalAlarmLockCode.remark">( </span>{{ dataList.generalAlarmLockCode.remark }} <span v-show="dataList.generalAlarmLockCode.remark">)</span><span>{{ $t('pages.lockScreenText17') }}</span><br/>
          <el-button style="height: 28px; width: 78px; line-height:0px;" @click="getGeneralAlarmLockCode">{{ $t('pages.lockScreenText20') }}</el-button>
          <el-input v-model="dataList.generalAlarmLockCode.value" style="width: 110px; margin-bottom: 5px" readonly></el-input>
          <el-tooltip
            :content="$t('pages.lockScreenText19')"
            placement="bottom"
          >
            <el-button v-show="dataList.generalAlarmLockCode.value" style="margin-bottom: 7px" icon="el-icon-document-copy" @click="copyAlarmLockCode"></el-button>
          </el-tooltip>
        </FormItem>
        <FormItem v-permission="'B2H'" style="margin-bottom: 20px;margin-top: 15px">
          <span>{{ $t('pages.lockScreenText21') }}</span><br/>
          <span style="color: black">{{ $t('pages.lockScreenText16') }}</span>
          <span>{{ $t('pages.lockScreenText18') }}</span><span v-show="dataList.generalLockScreenCode.remark">( </span>{{ dataList.generalLockScreenCode.remark }} <span v-show="dataList.generalLockScreenCode.remark">)</span><span>{{ $t('pages.lockScreenText17') }}</span><br/>
          <el-button style="height: 28px; width: 78px; line-height:0px;margin-bottom: 8px" @click="getGeneralLockScreenCode">{{ $t('pages.lockScreenText20') }}</el-button>
          <el-input v-model="dataList.generalLockScreenCode.value" style="width: 110px; margin-bottom: 8px" readonly></el-input>
          <el-tooltip
            :content="$t('pages.lockScreenText19')"
            placement="bottom"
          >
            <el-button v-show="dataList.generalLockScreenCode.value" style="margin-bottom: 10px" icon="el-icon-document-copy" @click="copyLockCode"></el-button>
          </el-tooltip>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="codeVisible = false">
          {{ $t('pages.exit') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.verifyIdentity')"
      :visible.sync="verifyVisible"
      width="400px"
      @close="cancelClick"
    >
      <Form ref="adminForm" status-icon label-width="100px" @submit.native.prevent>
        <FormItem :label="$t('pages.sysUserPassword')" prop="password">
          <el-input v-model="password" clearable type="password" :placeholder="$t('pages.enterAdminPwd')" show-password autocomplete="off" @keyup.enter.native="submitPass"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="loadSubmitting" type="primary" @click="submitPass">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click.native="cancelClick">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <grp-nickName-dlg ref="grpNickNameDlg" :select-tab-data="selectedData" @submitEnd="handleRefresh"/>

    <login-mode-dlg ref="loginModeDlg" @submitEnd="handleRefresh"/>
    <bind-daq-server-dlg ref="bindDAQServerDlg" @submitEnd="handleRefresh"/>
    <batch-modify-group-dlg ref="batchModifyGroupDlg" :group-tree-data="groupTreeSelectData" :selected-data="selectedData" @submitEnd="handleRefresh"/>
    <auto-login-user-dlg ref="autoLoginUserDlg" @submitEnd="handleRefresh"/>
    <export-dlg ref="exportDlg" :group-tree-data="exportTreeData" :export-func="exportFunc" :group-tree-id="selectTreeId"/>
    <sync-term-user-dlg ref="syncTermUserDlg" @submitEnd="handleRefresh"/>
    <usb-term ref="uterm" :group-tree-data="groupTreeSelectData" @change="handleRefresh"/>
    <add-terminal ref="at" :group-tree-data="groupTreeSelectData" @add="handleRefresh"/>
    <offline-term ref="offterm" :group-data="groupTreeSelectData" @change="handleRefresh"/>
    <clean-up-term ref="cleanUpTerm"/>
    <batch-operate-dlg
      ref="batchOperateDlg"
      :row-datas="tableSelectedData"
      :col-model="remoteColModel"
      :tips="tips"
      :operation-func="tips=='recoverTerm' ? recoverTerm : cleanTerm"
      :operation-all-func="tips=='recoverTerm' ? recoverAllTerm : cleanAllTerm"
    />
    <allocation-auto-user-dlg ref="allocationAutoUserDlg" @submitEnd="allocationAutoUserSubmitEnd"/>
    <lower-version-limit-dlg ref="lowerVersionLimitDlg"/>
  </div>
</template>

<script>
import {
  getTerminalPage, deleteTerminal, remoteControl, remoteControlAll,
  bindUser, uninstallTerminal, uninstallAllTerminal, exportExcel,
  getTermIdByVO, validPassword, getNeedReallocateTerm
} from '@/api/system/terminalManage/terminal'
import { batchUpdateGroup, batchUpdateAllGroup, getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { getUserNodeByTermId, getLockScreenVerify, validUserForbidden } from '@/api/system/terminalManage/user'
import { cleanOfflineTerm, recoverOfflineTerm, recycleTerms } from '@/api/system/configManage/cleanUpConfig'
import { getGeneralCodePage, getGeneralCode } from '@/api/system/configManage/globalConfig'
import { initTimestamp, deepMerge } from '@/utils'
import { getDict, getLoginModeDict, getTermTypeDict } from '@/utils/dictionary'
import { loginModeFormatter, termStatusIconFormatter } from '@/utils/formatter'
import { addRecycleNode } from '@/utils/tree'
import TreeSelectPanel from '@/components/TreeSelectPanel'
import ExportDlg from '@/views/common/export'
import LoginModeDlg from './loginModeDlg'
import BindDaqServerDlg from './bindDaqServerDlg'
import BatchModifyGroupDlg from './batchModifyGroupDlg'
import AutoLoginUserDlg from './autoLoginUserDlg'
import GrpNickNameDlg from './grpNickNameDlg'
import SyncTermUserDlg from './syncTermUserDlg'
import UsbTerm from './uterm'
import AddTerminal from './add'
import EditTerminal from './edit'
import OfflineTerm from './offline'
import CleanUpTerm from './cleanOfflineTermDlg'
import batchOperateDlg from './batchOperateDlg'
import AllocationAutoUserDlg from './allocationAutoUserDlg'
import LowerVersionLimitDlg from './lowerVersionLimitDlg';
import { mapGetters } from 'vuex'

export default {
  name: 'TerminalInfo',
  components: {
    LowerVersionLimitDlg, AllocationAutoUserDlg, AddTerminal, EditTerminal, UsbTerm,
    OfflineTerm, ExportDlg, AutoLoginUserDlg, BindDaqServerDlg, LoginModeDlg,
    TreeSelectPanel, GrpNickNameDlg, SyncTermUserDlg, BatchModifyGroupDlg, CleanUpTerm, batchOperateDlg
  },
  filters: {
    usersFilter(roles) {
      const rolesMap = {
        system: this.$t('pages.sysAdmin'),
        security: this.$t('pages.securityAdmin')
      }
      return rolesMap[roles]
    }
  },
  data() {
    return {
      multiSelect: true,
      colModel: [
        { prop: 'name', label: 'terminalName', width: '150', fixed: true, sort: 'custom', iconFormatter: termStatusIconFormatter, type: 'showDetail', searchType: 'terminal', searchParam: 'id', labelIndent: 25 },
        { prop: 'groupIds', label: 'terminalGroup', width: '150', formatter: this.groupFormatter, type: 'showDetail', searchType: 'department', searchParam: 'groupIds', sort: true, attributes: { selfRouteCallBack: this.selfRouteCallBack }},
        { prop: 'computerName', label: 'computerName', width: '150', sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'loginStatus.userName', label: 'curLoginUser', width: '150', formatter: this.logingUserFormatter, type: 'showDetail', searchParam: this.curLoginUserSearchParam, searchType: 'user', attributes: { selfRouteCallBack: this.selfRouteCallBack }},
        { prop: 'loginMode', label: 'loginMode', width: '150', formatter: this.loginModeFormatter, sort: 'custom' },
        { prop: 'user.name', label: 'autoLoginUser', width: '150', type: 'showDetail', searchParam: this.autoLoginSearchParam, searchType: 'user', formatter: this.autoLoginUserFormatter, sort: 'custom', attributes: { selfRouteCallBack: this.selfRouteCallBack }},
        { prop: 'mainIp', label: 'IP', width: '150', sort: 'custom' },
        { prop: 'mainMac', label: 'macAddr', width: '150', sort: 'custom' },
        { prop: 'version', label: 'terminalVersion', width: '150', sort: 'custom' },
        { prop: 'serverId', label: 'serverId', width: '150', sort: 'custom' },
        { prop: 'lastOnlineTime', label: 'lastOnlineTime', width: '200', sort: 'custom' },
        { prop: '', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '140',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdate },
            { label: 'bindUser', disabledFormatter: this.buttonFormatter, isShow: (row) => row.type !== 3, click: this.handleBind }
          ]
        }
      ],
      remoteColModel: [
        { prop: 'name', label: 'terminalName', width: '150', fixed: true, sort: 'custom', iconFormatter: termStatusIconFormatter },
        { prop: 'groupIds', label: 'terminalGroup', width: '150', formatter: this.groupFormatter, sort: true },
        { prop: 'computerName', label: 'computerName', width: '150', sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'userName', label: 'curLoginUser', width: '150', formatter: this.logingUserFormatter },
        { prop: 'loginMode', label: 'loginMode', width: '150', sort: 'custom', formatter: this.loginModeFormatter },
        { prop: 'user.name', label: 'autoLoginUser', width: '150', sort: 'custom', formatter: this.userNameFormatter },
        { prop: 'mainMac', label: 'macAddr', width: '150', sort: 'custom' },
        { prop: 'serverId', label: 'serverId', width: '150', sort: 'custom' },
        { prop: 'lastOnlineTime', label: 'lastOnlineTime', width: '200', sort: 'custom' }
      ],
      agreementTypeItems: {
        1: this.$t('pages.agreementType1'),
        2: this.$t('pages.agreementType2'),
        3: this.$t('pages.agreementType3'),
        4: this.$t('pages.agreementType4'),
        5: this.$t('pages.agreementType5'),
        // 6: this.$t('pages.agreementType6'),
        // 7: this.$t('pages.agreementType7'),
        8: this.$t('pages.agreementType8'),
        99: this.$t('pages.agreementType9')
      },
      query: {}, // 查询条件
      defaultQuery: {
        page: 1,
        groupId: undefined,
        groupIds: '',
        searchInfo: '',
        searchIdAndName: '',
        status: null,
        name: '',
        computerName: '',
        version: '',
        searchId: '',
        ip: '',
        mac: '',
        type: null,
        useType: null,
        offlineTime: null,
        loginMode: undefined,
        serverId: undefined,
        lastLoginUserId: null,
        includeSubGroup: true
      },
      searchQuery: {}, // 查询条件，用于dialog框里面的查询
      // 0:Windows終端 1:linux终端 2：Mac终端 3:移动设备终端 16:Windows老板终端 32:Windows U盘终端
      // 128(0x80):Windows永久离线终端 129(0x81):Linux永久离线终端 130(0x82):Mac永久离线终端
      terminalTypes: [
        { label: this.$t('pages.allTerminal'), value: null },
        { label: this.$t('pages.dictionary_Msg5'), value: 0 },
        { label: this.$t('pages.dictionary_Msg6'), value: 0x80, hidden: this.isTrialDlp() },
        { label: this.$t('pages.WinBoss'), value: 16 },
        { label: this.$t('pages.winUsb'), value: 32 },
        { label: this.$t('pages.dictionary_Msg7'), value: 1 },
        { label: this.$t('pages.dictionary_Msg8'), value: 0x81, hidden: this.isTrialDlp() },
        // { label: this.$t('pages.LinuxBoss'), value: 17 },
        { label: this.$t('pages.dictionary_Msg9'), value: 2 },
        { label: this.$t('pages.dictionary_Msg10'), value: 0x82, hidden: this.isTrialDlp() },
        // { label: this.$t('pages.MacBoss'), value: 18 },
        { label: this.$t('pages.dictionary_Msg11'), value: 3 }
        // { label: this.$t('pages.MobileBoss'), value: 19 }
      ],
      terminalStatus: [
        { label: this.$t('pages.allTerminal'), value: null },
        { label: this.$t('pages.terminalStatus2'), value: 0 },
        { label: this.$t('pages.terminalStatus3'), value: 4 },
        { label: this.$t('pages.terminalStatus4'), value: 1 },
        { label: this.$t('pages.terminalStatus5'), value: 3 }
      ],
      loginModeOptions: [],
      offLineType: 0,
      bindedUserData: [],
      defaultCheckedKeys: [],
      showTree: true,
      deleteable: false,
      remoteable: false,
      verifyVisible: false,
      tempW: {
        id: undefined,
        useWPS: 0,
        wpsOpenType: 0,
        limitType: 0
      },
      tempUser: {},
      temp: {}, // 表单字段
      defaultTemp: {
        name: '',
        id: undefined,
        userId: undefined,
        groupIds: [],
        remark: '',
        loginMode: 0,
        mobileExt: undefined,
        syncGroup: false,
        syncName: false,
        user: {
          name: ''
        }
      },
      tempUserRules: {
        userId: [{ required: true, trigger: 'change', validator: this.tempUserValidator }]
      },
      dialogFormVisible2: false,
      dialogBindVisible: false,
      wpsOpenTypeData: {
        checked: [],
        options: [
          { type: 1, desc: 'DOC' },
          { type: 2, desc: 'DOCX' },
          { type: 4, desc: 'XLS' },
          { type: 8, desc: 'XLSX' },
          { type: 16, desc: 'PPT' },
          { type: 32, desc: 'PPTX' },
          { type: 64, desc: 'WPS' },
          { type: 128, desc: 'ET' },
          { type: 256, desc: 'DPS' },
          { type: 512, desc: 'PDF' },
          { type: 1024, desc: 'TXT' }
        ]
      },
      wpsOpenTypeOptions: [
        { type: 1, desc: 'DOC' },
        { type: 2, desc: 'DOCX' },
        { type: 4, desc: 'XLS' },
        { type: 8, desc: 'XLSX' },
        { type: 16, desc: 'PPT' },
        { type: 32, desc: 'PPTX' },
        { type: 64, desc: 'WPS' },
        { type: 128, desc: 'ET' },
        { type: 256, desc: 'DPS' },
        { type: 512, desc: 'PDF' },
        { type: 1024, desc: 'TXT' }
      ],
      treeData: [],
      exportTreeData: [],
      defaultExpandedKeys: ['G0'],
      groupTreeSelectData: [],
      treeSelectData: [],
      // userTreeSelectNode: [{ id: 'User0', dataId: 0, label: this.$t('pages.null'), type: 2 }],
      userCheckedId: '',
      addEmptyNode: true,
      submitting: false,
      loadSubmitting: false,
      password: '',
      deleteUser: false,
      deleteTerm: false,
      agreementType: null,
      agreementMsg: null,
      remoteControlTerms: [],
      disableUserIds: [], // 终端指定操作员时，不允许指定的操作员，如已经绑定终端，但不包含当前终端的操作员
      selectedData: [],
      vcVisible: false,
      vcLoading: false,
      vcTitle: '',
      vcForm: {
        random: '',
        verifiCode: ''
      },
      terminalRowData: [],
      generalCodeTitle: this.$t('pages.getGeneralCode'),
      codeVisible: false,
      dataList: {
        generalAlarmLockCode: { value: '', remark: '' },
        generalLockScreenCode: { value: '', remark: '' }
      },
      effective: 1,                     // 生效范围 1-当前选中终端 2-当前查询的所有终端
      selectLength: 0,                  // 当前选中终端的条数
      allResultLength: 0,               // 当前查询的所有终端的条数
      checkAll: true,
      backupSelectedIds: [],            // 备份当前选中的id
      backupSelectedDatas: [],
      backupUnSelectedIds: [],          // 备份当前未选中的id
      backupRowData: [],
      windowsFilter: true,              // 是否只查询windows类型
      notUsbFilter: false,              // 是否只查询非usb类型
      userNames: [],                    // 用于管理员日志记录绑定操作员操作之前终端绑定的操作员
      userNames_new: [],                // 用于管理员日志记录绑定操作员操作之后终端绑定的操作员
      userIds: [],                      // 绑定操作员 勾选的操作员节点id
      groupIds: [],                     // 绑定操作员 勾选的部门节点id
      updateGroupForm: false,           // 批量操作修改分组
      updateGroupId: undefined,         // 分组id
      checkedKeys: [],                  // 修改分组默认不勾选
      syncGroup: false,                 // 是否同步操作员分组
      selectTreeId: '0',
      currentGroupId: undefined,
      currentNodeKey: [],
      searchId: undefined,
      tips: '',
      tableSelectedData: []
    }
  },
  computed: {
    ...mapGetters([
      'notice',
      'termStatusMap',
      'isSuperRole',
      'isSysRole',
      'cleanDuration',
      'deptTreeList'
    ]),
    gridTable() {
      return this.$refs['terminalTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    }
  },
  watch: {
    termStatusMap() {
      this.changeTerminalStatus(this.termStatusMap)
    },
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    },
    'notice.autoCleanOffilneTerm'() {
      this.gridTable.execRowDataApi(this.query)
    },
    'notice.autoReloadData'() {
      this.resetData()
      this.gridTable.execRowDataApi(this.query)
    },
    dialogFormVisible2(val) {
      if (!val) {
        // 关闭弹窗，清除远程控制的终端缓存
        this.remoteControlTerms = []
      }
    }
  },
  created() {
    this.loginModeOptions = getLoginModeDict()
    initTimestamp(this)
    this.resetTemp()
    this.resetQuery()
    this.initGroupTreeNode()
    this.$nextTick(() => {
      if (this.$route.params.hasOwnProperty('status')) {
        // 首页查询终端数量（在线 / 总数）、终端未上线数量（超1周 / 超1月）
        this.initFromDataPanel()
      } else if (this.$route.query.hasOwnProperty('entityId') && this.$route.query.hasOwnProperty('entityType')) {
        const { entityId, entityType } = this.$route.query
        if (entityId && entityType) {
          // 查询类型为终端组
          if (entityType === '3') {
            this.groupTree.setCurrentKey('G' + entityId)
            this.resetQuery()
            this.query.groupId = entityId
            // 导出当前查询时，分组查询条件为跳转的分组
            this.selectTreeId = entityId
            // 查询时不包含子部门
            this.query.includeSubGroup = false
            // 不包含回收站
            this.query.useType = 0
            this.gridTable.execRowDataApi(this.query)
          }
          // 查询类型为当前登录操作员
          if (entityType === '2') {
            this.$refs.groupTree.clearSelectedNode()
            this.resetQuery()
            this.query.lastLoginUserId = entityId
            this.gridTable.execRowDataApi(this.query)
          }
        }
      } else {
        this.handleRefresh()
      }
    })
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.params.hasOwnProperty('status')) {
        // 首页查询终端数量（在线 / 总数）、终端未上线数量（超1周 / 超1月）
        this.initFromDataPanel()
      } else if (this.$route.query.hasOwnProperty('entityId') && this.$route.query.hasOwnProperty('entityType')) {
        const { entityId, entityType } = this.$route.query
        if (entityId && entityType) {
          // 查询类型为终端组
          if (entityType === '3') {
            this.groupTree.setCurrentKey('G' + entityId)
            this.resetQuery()
            this.query.groupId = entityId
            // 导出当前查询时，分组查询条件为跳转的分组
            this.selectTreeId = entityId
            // 查询时不包含子部门
            this.query.includeSubGroup = false
            // 不包含回收站
            this.query.useType = 0
            this.gridTable.execRowDataApi(this.query)
          }
          // 查询类型为当前登录操作员
          if (entityType === '2') {
            this.$refs.groupTree.clearSelectedNode()
            this.resetQuery()
            this.query.lastLoginUserId = entityId
            this.gridTable.execRowDataApi(this.query)
          }
        }
      } else {
        this.handleRefresh()
      }
    })
  },
  methods: {
    handleAddTerm() {
      this.$refs.at.show(this.currentGroupId)
    },
    handleMgrUTerm() {
      this.$refs.uterm.show()
    },
    handleMgrOfflineTerm() {
      this.$refs.offterm.show()
    },
    handleCleanUpTermConfig() {
      this.$refs.cleanUpTerm.show()
    },
    initFromDataPanel() {
      const params = this.$route.params
      if (params.hasOwnProperty('status')) {
        this.resetQuery()
        this.query.status = params.status
        this.query.groupIds = ''
        this.query.useType = params.useType
        this.$refs.groupTree.clearSelectedNode()
        this.offLineType = params.offLineType
        this.offLineTypeChange(this.offLineType)
        this.gridTable.execRowDataApi(this.query)
      }
    },
    offLineTypeChange(val) {
      if (!val) {
        this.offLineType = 0;
        this.query.offlineTime = null
      } else {
        const date = new Date()
        date.setDate(date.getDate() - val)
        this.query.offlineTime = date
      }
    },
    resetData() {
      this.query = Object.assign({}, this.defaultQuery)
      this.query.status = 0
      this.query.useType = 0
      this.offLineType = this.cleanDuration
      this.offLineTypeChange(this.offLineType)
    },
    statusChange(val) {
      if (val !== 0) {
        this.offLineType = 0
        this.query.offlineTime = null
      }
    },
    exportFunc(formData, opts) {
      if (formData.type == 3) {
        const q = Object.assign({}, this.searchQuery, {
          groupId: (this.query.groupIds && this.query.groupIds.split(',')[0]) || this.query.groupId,
          includeSubGroup: this.query.includeSubGroup,
          updateQuery: true
        })
        return exportExcel(q, opts)
      } else {
        const { sortName, sortOrder } = this.searchQuery
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          groupId: formData.type === 2 ? formData.groupId : null,
          includeSubGroup: this.query.includeSubGroup,
          sortName,
          sortOrder,
          updateQuery: false
        }, opts)
      }
    },
    changeTerminalStatus(statusMap) {
      this.$nextTick(() => {
        const rowDatas = this.gridTable.getDatas()
        rowDatas.forEach(item => {
          const status = statusMap[item.id]
          item.loginStatus = status
        })
      })
    },
    isActive(row) {
      return row.loginStatus && row.loginStatus.status != 0 ? { class: 'active', title: this.$t('pages.online') } : { class: 'offline', title: this.$t('pages.offline'), style: 'color: #888;' }
    },
    // selectable(row, index) {
    //   return row.type !== 3
    // },
    userSelectTree() {
      return this.$refs['userSelectTree']
    },
    initGroupTreeNode: function() {
      const initRecycleDeptTree = (deptTreeData) => {
        this.treeData = addRecycleNode(JSON.parse(JSON.stringify(deptTreeData)))
        this.exportTreeData = this.treeData
      }
      getDeptTreeFromCache().then(respond => {
        if (Array.isArray(respond.data) && respond.data.length === 1 && respond.data[0].dataId == 0) {
          this.groupTreeSelectData = respond.data[0].children
        } else {
          this.groupTreeSelectData = respond.data
        }
        initRecycleDeptTree(respond.data)
      })
    },
    // 获取所有子部门
    getAllChildrenGroupId(groups, groupIds) {
      groups.forEach(group => {
        if (group.id.indexOf('G') > -1) {
          groupIds.push(group.dataId)
          if (group.children && group.children.length > 0) {
            this.getAllChildrenGroupId(group.children, groupIds)
          }
        }
      })
    },
    getSearchListFunction() {
      const groupList = this.deptTreeList || []
      return groupList
    },
    renderContent(h, { node, data, store }) {
      return h('div', { 'class': { 'custom-tree-node': data.type === 'D' }}, [h('span', data.label)])
    },
    // 树节点变化的回调函数
    nodeChange(data, node) {
      const groupIds = []
      // this.resetQuery()
      this.query.groupId = undefined
      this.query.groupIds = ''
      if (!data) {
        this.selectTreeId = '0'
        this.currentGroupId = undefined
        this.query.useType = '0,1,2'
      } else {
        this.currentGroupId = data.dataId
        this.selectTreeId = data.dataId
        // 回收站节点下的终端，useType 不为 0
        if (data.dataId === '-2') {
          this.query.useType = '1,2'
        } else {
          groupIds.push(data.dataId)
          // 如果选中根节点，则
          this.query.useType = data.dataId === '0' ? '0,1,2' : '0'
        }

        if (data.children && data.children.length > 0) {
          this.getAllChildrenGroupId(data.children, groupIds)
        }
        node.expanded = true
      }
      this.query.groupIds = groupIds.join(',')
      this.gridTable.execRowDataApi()
    },
    selectedBindedNodeFilter(nodeDatas) {
      return nodeDatas
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = { ...this.temp, ...JSON.parse(JSON.stringify(row)) }
      this.tempUser = JSON.parse(JSON.stringify(row))
      this.$refs['editTerm'].show(this.temp)
    },
    buttonFormatter(data, btn) {
      return data.useType > 0
    },
    handleUpdated() {
      this.gridTable.execRowDataApi()
    },
    handleUserChange(id, name) {
      this.tempUser.userId = id
      this.tempUser.user.name = name
    },
    rowDataApi: function(option) {
      this.searchQuery = Object.assign({}, this.query, option)
      return getTerminalPage(this.searchQuery)
    },
    isWinTerm(row) {
      return row.type % 16 === 0
    },
    isWinTermRidOfBoss(row) {
      return row.type === 0
    },
    handleSelectionChange(val) {
      this.selectedData = val
      this.deleteable = val.length > 0
      // 暂时只有windows终端支持远程控制
      if (val.length > 0 && val.some(this.isWinTerm)) {
        this.remoteable = true
      } else {
        this.remoteable = false
      }
      // this.remoteable = val.length > 0 && val.some(this.isWinTerm)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = deepMerge({ user: { name: '' }}, this.defaultTemp)
    },
    userTreeNodeCheckChange(data, node, vm) {
      return new Promise((resolve, reject) => {
        if (data.id.indexOf('G' + data.dataId) < 0) {
          //  判断是否为已被禁用的操作员
          const res = validUserForbidden(data.dataId);
          if (res.data) {
            this.$message({
              message: this.$t('pages.unUsedUserNotSelectedMsg'),
              type: 'error',
              duration: 2000
            })
            resolve(false)
          }
          this.userCheckedId = data.id
          this.temp.userId = data.dataId
          this.temp.user.name = data.label
          this.tempUser.userId = data.dataId
          this.tempUser.user.name = data.label
          resolve()
        } else {
          resolve(false)
        }
      })
    },
    lastLoginUserTreeNodeCheckChange: function(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.query.lastLoginUserId = data.dataId
      } else {
        return false
      }
    },
    userSelectedChange(selectKey, data) {
      if (!selectKey) {
        this.temp.userId = 0
        this.temp.user.name = ''
        this.tempUser.userId = 0
        this.tempUser.user.name = ''
      } else if (data && data.length > 0) {
        const userId = data[0].dataId || this.temp.userId || this.tempUser.userId
        const name = data[0].label || this.temp.user.name
        this.temp.userId = userId
        this.temp.user.name = name
        this.tempUser.userId = userId
        this.tempUser.user.name = name
      }
    },
    lastLoginUserSelectedChange(selectKey, data) {
      // 选中结果为空，则将lastLoginUserId 设置为空，分页查询所有
      if (!selectKey) {
        this.query.lastLoginUserId = null
      } else if (data && data.length > 0) {
        const userId = data[0].dataId || this.query.lastLoginUserId
        this.query.lastLoginUserId = userId
      }
    },
    dialogBindClose() {
      this.defaultCheckedKeys = []
    },
    resetQuery() {
      this.query = Object.assign({}, this.defaultQuery)
      this.offLineType = 0
    },
    handleRecover() {
      this.tips = 'recoverTerm'
      this.tableSelectedData = this.gridTable.getSelectedDatas().filter(data => data.useType != 0)
      this.$refs.batchOperateDlg.show(this.query.groupIds)
      this.$nextTick(() => {
        this.$refs.batchOperateDlg.allDataTable.execRowDataApi()
      })
    },
    recoverTerm(data) {
      if (data) {
        recoverOfflineTerm({
          type: 2,
          termIds: data.join(',')
        }).then(res => {
          if (res.data) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.offline_terminal_text35'),
              type: 'success',
              duration: 2000
            })
          }
        })
        this.responseRecoverTermSocketApi()
      }
      return true
    },
    recoverAllTerm(data) {
      getTermIdByVO(data).then((respond) => {
        if (respond.data) {
          recoverOfflineTerm({
            type: 2,
            termIds: respond.data.join(',')
          }).then(res => {
            if (res.data) {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.offline_terminal_text35'),
                type: 'success',
                duration: 2000
              })
            }
          })
          this.responseRecoverTermSocketApi()
        }
      })
      return true
    },
    responseRecoverTermSocketApi() {
      this.$socket.subscribeToUser('recoverTerm', '/cleanUpConfig/recoverTerm', (respond, handle) => {
        if (respond.data) {
          if (respond.data.type == 2 && respond.data.recoverSuccessTermNum > 0 && respond.data.recoverFailTermNum == 0) {
            this.$notify({ title: this.$t('text.success'), message: this.$t('pages.offline_terminal_text23', { recoverSuccessTermNum: respond.data.recoverSuccessTermNum }), type: 'success', duration: 5000 })
          } else if (respond.data.type == 2 && respond.data.recoverSuccessTermNum > 0 && respond.data.recoverFailTermNum > 0) {
            const h = this.$createElement
            this.$notify({
              title: this.$t('text.success'),
              dangerouslyUseHTMLString: true,
              message: h('div', null, [
                h('span', null, `${this.$t('pages.recover_term_msg', { recoverSuccessTermNum: respond.data.recoverSuccessTermNum })}`),
                h(
                  'a',
                  {
                    style: 'margin-top:5px;color: #409EFF;cursor: pointer;',
                    on: { click: this.goToAccessLogPage }
                  },
                  this.$t('pages.offline_terminal_text30')
                ),
                h('span', null, `${this.$t('pages.detail')}`)
              ]),
              type: 'success', duration: 5000
            })
          } else if (respond.data.type == 2 && respond.data.executeFail == 1) {
            // executeFail == 1，表示请求失败，如引擎在写入数据库时发生异常
            this.$notify({ title: this.$t('text.error'), message: this.$t('pages.offline_terminal_text19'), type: 'error', duration: 5000 })
          }
          this.gridTable.execRowDataApi(this.query)
        }
        handle.close()
      }, false)
    },
    goToAccessLogPage() {
      if (this.hasPermission('B22')) {
        this.$router.push('/behaviorAuditing/systemLog/accessLog')
      } else {
        this.$message({
          message: this.$t('pages.offline_terminal_text31'),
          type: 'error',
          duration: 2000
        })
      }
    },
    handleRecycle() {
      if (this.gridTable.selectedData.length === 0) {
        this.$message({
          message: this.$t('pages.terminal_text1'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const termIds = this.gridTable.selectedData.map(terminal => terminal.id)
      recycleTerms(termIds).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.terminal_text2'),
          type: 'success',
          duration: 2000
        })
        this.gridTable.execRowDataApi()
      })
    },
    handleCleanUpTerm() {
      this.tips = 'cleanTerm'
      const selectedData = this.gridTable.getSelectedDatas().filter(row => row.useType == 0)
        .filter(row => row.guid != '')
        .filter(row => !((row.type & 0xf0) === 32 || (row.type & 0xf0) === 0x80))
        .filter(row => undefined == row.loginStatus)
      this.tableSelectedData.splice(0, this.tableSelectedData.length, ...selectedData)
      this.$refs.batchOperateDlg.show(this.query.groupIds)
      this.$nextTick(() => {
        this.$refs.batchOperateDlg.allDataTable.execRowDataApi()
      })
    },
    cleanTerm(data) {
      if (data) {
        cleanOfflineTerm({
          type: 1,
          termIds: data.join(',')
        }).then(res => {
          if (res.data) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.offline_terminal_text14'),
              type: 'success',
              duration: 2000
            })
          }
        })
        this.responseCleanTermSocketApi()
      }
      return true
    },
    cleanAllTerm(data) {
      getTermIdByVO(data).then((respond) => {
        if (respond.data) {
          cleanOfflineTerm({
            type: 1,
            termIds: respond.data.join(',')
          }).then(res => {
            if (res.data) {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.offline_terminal_text14'),
                type: 'success',
                duration: 2000
              })
            }
          })
          this.responseCleanTermSocketApi()
        }
      })
      return true
    },
    responseCleanTermSocketApi() {
      this.$socket.subscribeToUser('cleanTerm', '/cleanUpConfig/cleanOfflineTerm', (respond, handle) => {
        if (respond.data && respond.data.type == 1) {
          if (respond.data.type == 1 && respond.data.cleanSuccessNum > 0) {
            this.$notify({ title: this.$t('text.success'), message: this.$t('pages.clean_term_msg', { cleanSuccessNum: respond.data.cleanSuccessNum }), type: 'success', duration: 5000 })
          } else if (respond.data.type == 1 && respond.data.executeFail == 1) {
            // executeFail == 1，表示请求失败，如引擎在写入数据库时发生异常
            this.$notify({ title: this.$t('text.error'), message: this.$t('pages.offline_terminal_text19'), type: 'error', duration: 5000 })
          }
          this.gridTable.execRowDataApi(this.query)
        }
        handle.close()
      }, false)
    },
    handleRefresh() {
      const stTree = this.$refs.groupTree
      if (stTree) {
        // 清除选中节点
        stTree.clearSelectedNode()
        this.nodeChange()
      } else {
        console.error('树组件不存在！');
        this.gridTable.execRowDataApi(this.query)
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleLimitTypeChange(data) {
      if (data == 0) {
        this.wpsOpenTypeData.checked = []
        this.wpsOpenTypeData.options.forEach(data => {
          this.wpsOpenTypeData.checked.push(data.type)
        })
      }
    },
    validateData() {
      if (this.tempW.limitType === 1) {
        if (this.wpsOpenTypeData.checked.length === 0) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.terminal_text3'),
            type: 'error',
            duration: 2000
          })
          return false
        } else {
          this.tempW.wpsOpenType = 0
          this.wpsOpenTypeData.checked.forEach(el => {
            this.tempW.wpsOpenType = this.tempW.wpsOpenType + el
          })
        }
      } else {
        this.tempW.wpsOpenType = 0
      }
      return true
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteTerminal({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    checkedUserData(checkedData) {
      this.userIds = []
      this.groupIds = []
      this.userNames_new = []
      checkedData.forEach(data => {
        if (data.id.indexOf('G' + data.dataId) < 0) {
          this.userIds.push(data.dataId)
        } else {
          this.groupIds.push(data.dataId)
        }
        this.userNames_new.push(data.label)
      });
    },
    handleBind(row) {
      this.resetTemp()
      // this.userCheckedId = 'User' + row.userId
      this.tempUser = JSON.parse(JSON.stringify(row))
      this.dialogBindVisible = true
      this.bindedUserData.splice(0)
      if (this.userSelectTree()) {
        this.userSelectTree().clearSelectedNode()
        this.userSelectTree().clearFilter()
      }
      this.defaultCheckedKeys = []
      getUserNodeByTermId(row.id).then(respond => {
        if (respond.data) {
          const keys = respond.data.map(data => {
            this.userNames.push(data.label)
            return data.id
          })
          this.defaultCheckedKeys = keys
        }
      })
    },
    updateBinded() {
      this.$refs['terminalForm'].validate((valid) => {
        if (valid) {
          const msg = this.userIds.length === 0 && this.groupIds.length === 0 ? this.$t('pages.terminal_text4') : this.$t('pages.terminal_text5')
          this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
            this.updateBindUser(this.tempUser.id, this.tempUser.userId, this.userIds, this.$t('text.bindSuccess'), this.groupIds)
          }).catch(() => {})
        }
      })
    },
    clearBinded() {
      this.$confirmBox(this.$t('pages.terminal_text6'), this.$t('text.prompt')).then(() => {
        this.updateBindUser(this.tempUser.id, null, null, this.$t('pages.terminal_text7'), null)
      }).catch(() => {})
    },
    updateBindUser: function(termId, autoLoginUserId, bindUserIds, msg, groupIds) {
      bindUser({
        id: termId,
        autoLoginUserId: autoLoginUserId,
        userIds: bindUserIds,
        bindGroupIds: groupIds,
        userNames: this.userNames_new
      }).then(respond => {
        this.dialogBindVisible = false
        this.gridTable.execRowDataApi()
        this.$notify({ title: this.$t('text.success'), message: msg, type: 'success', duration: 2000 })
      })
    },
    handleBindDAQServer() {
      const rowData = []
      // 防止修改新数组里面的数据时，会影响到旧数组的数据，所以必须加JSON.parse(JSON.stringify
      JSON.parse(JSON.stringify(this.gridTable.getSelectedDatas())).forEach(item => {
        // 移动终端不支持修改登录模式
        if (item.type % 16 != 3) {
          rowData.push(item)
        }
      })
      const allLength = this.gridTable.total
      this.$refs.bindDAQServerDlg.handleUpdate(rowData, allLength, this.query)
    },
    handleUninstall() {
      const selection = this.gridTable.getSelectedDatas()
      const filteredTerms = selection.filter(row => {
        // 绑定U盘的U盘终端和占用的永久离线终端
        const type = row.type & 0xf0
        if (type === 0x20 || type === 0x80) {
          return !row.guid
        }
        return true
      })
      if (selection.length > 0 && filteredTerms.length === 0) {
        this.$message({
          message: this.$t('pages.terminal_text28'),
          type: 'warning',
          showClose: true,
          duration: 5000
        })
        return
      }
      this.backupUnSelectedIds.splice(0)
      this.backupSelectedIds.splice(0)
      this.backupSelectedDatas.splice(0)
      this.checkAll = true
      this.updateGroupForm = false
      this.dialogFormVisible2 = true
      this.effective = 1
      this.password = ''
      this.agreementType = 99
      this.agreementMsg = this.$t('pages.terminal_text8')
      this.remoteControlTerms = filteredTerms
      // this.backupRowData.splice(0)
      // this.backupRowData.splice(0, 0, ...JSON.parse(JSON.stringify(this.remoteControlTerms)))
      this.selectLength = this.remoteControlTerms.length
      if (this.selectLength === 0) {
        this.effective = 2
      }
      this.windowsFilter = false
      this.notUsbFilter = false
      this.$nextTick(() => {
        this.remoteTable().toggleAllSelection()
        // 避免修改的查询参数影响到上一个页面的查询
        this.searchQuery = Object.assign({}, this.query)
        this.allRemoteTable().execRowDataApi(this.searchQuery)
      })
    },
    handleMove() {

    },
    handleRemoteControl(agreementType) {
      this.backupUnSelectedIds.splice(0)
      this.backupSelectedIds.splice(0)
      this.backupSelectedDatas.splice(0)
      this.checkAll = true
      this.updateGroupForm = false
      this.dialogFormVisible2 = true
      this.effective = 1
      this.password = ''
      this.agreementType = agreementType
      if (agreementType == 8) {
        this.agreementMsg = this.$t('pages.terminal_text9')
      } else {
        this.agreementMsg = `${this.$t('pages.terminal_text10')}${this.agreementTypeItems[agreementType]}：`
      }
      // type=0 guid!= '' 普通终端，row.loginStatus.status != 0 在线
      this.remoteControlTerms = this.gridTable.getSelectedDatas()
        .filter(this.isWinTermRidOfBoss)
        .filter((row) => row.guid != '')
        .filter((row) => row.loginStatus && row.loginStatus.status != 0)
      this.selectLength = this.remoteControlTerms.length
      if (this.selectLength === 0) {
        this.effective = 2
      }
      this.windowsFilter = true
      this.notUsbFilter = true
      this.$nextTick(() => {
        this.remoteTable().toggleAllSelection()
        // 避免修改的查询参数影响到上一个页面的查询
        this.searchQuery = Object.assign({}, this.query, { status: 4 })
        console.log('searchQuery', JSON.stringify(this.searchQuery))
        this.allRemoteTable().execRowDataApi(this.searchQuery)
      })
    },
    handleCleanClick(type) {
      const func = {
        1: this.handleCleanUpTerm,
        2: this.handleRecover,
        3: this.handleCleanUpTermConfig
      }[type]
      func && func()
    },
    handleMoreClick(type) {
      const func = {
        1: this.handleBindDAQServer,
        3: this.handleLockScreenVerify,
        4: this.handleUpdateLoginMode,
        6: this.$refs.syncTermUserDlg.show,
        7: this.handleGroupNickNameUpdate,
        8: this.batchModifyGroup,
        9: this.handleGeneralLockScreenVerify,
        10: this.handleMgrUTerm,
        11: this.handleMgrOfflineTerm,
        13: this.handleDlpVersionLimit
      }[type]
      func && func()
    },
    async validPassword(password) {
      let flag = false
      await validPassword({ password: password, encryptProps: ['password'] }).then(res => { flag = res.data })
      return flag;
    },
    uninstallTerminal() {
      const names = this.remoteTable().getSelectedDatas().map(item => {
        return item.name + '（' + item.id + '）'
      })
      const ids = this.remoteTable().getSelectedIds().join(',')
      const objTemp = {
        terminalNames: names,
        password: this.password,
        ids: ids,
        deleteTerm: this.deleteTerm,
        deleteUser: this.deleteUser,
        encryptProps: ['password']
      }
      uninstallTerminal(objTemp).then(res => {
        this.verifyVisible = false
        this.dialogFormVisible2 = false
        this.submitting = false
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.deleteTerm ? this.$t('pages.uninstallSuccess') : this.$t('pages.terminal_text11'),
          type: 'success',
          duration: 2000
        })
        this.gridTable.execRowDataApi(this.query)
      }).catch(() => {
        this.submitting = false
      })
    },
    uninstallTerminalAll() {
      if (this.checkAll) {
        this.searchQuery.page = 1
        this.searchQuery.limit = this.allResultLength
        uninstallAllTerminal({
          query: this.searchQuery,
          backupUnSelectedIds: this.backupUnSelectedIds,
          deleteTerm: this.deleteTerm,
          deleteUser: this.deleteUser,
          password: this.password,
          encryptProps: ['password']
        }).then(res => {
          this.verifyVisible = false
          this.dialogFormVisible2 = false
          this.submitting = false
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.deleteTerm ? this.$t('pages.uninstallSuccess') : this.$t('pages.terminal_text11'),
            type: 'success',
            duration: 2000
          })
          this.gridTable.execRowDataApi(this.query)
        }).catch(() => {
          this.submitting = false
        })
      } else {
        const names = this.backupSelectedDatas.map(item => {
          return item.name + '（' + item.id + '）'
        })
        const ids = this.backupSelectedIds.join(',')
        const objTemp = {
          terminalNames: names,
          password: this.password,
          ids: ids,
          deleteTerm: this.deleteTerm,
          deleteUser: this.deleteUser,
          encryptProps: ['password']
        }
        uninstallTerminal(objTemp).then(res => {
          this.verifyVisible = false
          this.dialogFormVisible2 = false
          this.submitting = false
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.deleteTerm ? this.$t('pages.uninstallSuccess') : this.$t('pages.terminal_text11'),
            type: 'success',
            duration: 2000
          })
          this.gridTable.execRowDataApi()
        }).catch(() => {
          this.submitting = false
        })
      }
    },
    async submitPass() {
      this.submitting = true
      if (this.effective == 1) {
        if (this.agreementType != 99) { // 远程控制
          const termIds = this.remoteTable().getSelectedIds().join(',')
          remoteControl({
            ids: termIds,
            agreementType: this.agreementType,
            password: this.password,
            encryptProps: ['password']
          }).then(res => {
            this.verifyVisible = false
            this.dialogFormVisible2 = false
            this.submitting = false
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = true
          if (!this.deleteTerm || !this.hasPermission('106')) {
            this.deleteUser = false
          }
          //  校验密码是否正确
          if (this.deleteUser && await this.validPassword(this.password)) {
            const selectedDatas = this.remoteTable().getSelectedDatas() || []
            const map = {}
            const deleteUserList = []
            selectedDatas.forEach(item => {
              //  自动登录操作员模式
              if (item.loginMode === 1 && item.userId) {
                if (!map[item.userId]) {
                  map[item.userId] = []
                }
                map[item.userId].push(item.id);
                deleteUserList.push({ id: item.userId, account: item.user.name })
              }
            })
            const result = await getNeedReallocateTerm(map);
            if (result.data.needReallocateTerms && result.data.needReallocateTerms.length > 0) {
              this.$refs.allocationAutoUserDlg.show(result.data.needReallocateTerms, deleteUserList);
              return;
            }
          }
          this.uninstallTerminal()
        }
      } else if (this.effective == 2) {
        if (this.agreementType != 99) { // 远程控制
          if (this.checkAll) {
            this.searchQuery.page = 1
            this.searchQuery.limit = this.allResultLength
            remoteControlAll({
              query: this.searchQuery,
              backupUnSelectedIds: this.backupUnSelectedIds,
              agreementType: this.agreementType,
              password: this.password,
              encryptProps: ['password']
            }).then(res => {
              this.verifyVisible = false
              this.dialogFormVisible2 = false
              this.submitting = false
            }).catch(() => {
              this.submitting = false
            })
          } else {
            const termIds = this.backupSelectedIds.join(',')
            remoteControl({
              ids: termIds,
              agreementType: this.agreementType,
              password: this.password,
              encryptProps: ['password']
            }).then(res => {
              this.verifyVisible = false
              this.dialogFormVisible2 = false
              this.submitting = false
            }).catch(() => {
              this.submitting = false
            })
          }
        } else { // 卸载
          this.submitting = true
          if (!this.deleteTerm || !this.hasPermission('106')) {
            this.deleteUser = false
          }

          //  校验密码是否正确，
          if (this.deleteUser && await this.validPassword(this.password)) {
            let datas = []
            const condition = {}
            const map = {}
            if (this.checkAll) {
              condition.page = 1
              condition.limit = this.allResultLength
              const res = await this.remoteRowDataApi(condition)
              datas = res.data.items || []
            } else {
              datas = this.allRemoteTable().getSelectedDatas() || []
            }
            datas.forEach(item => {
              //  自动登录操作员模式
              if (item.loginMode === 1 && item.userId) {
                if (!map[item.userId]) {
                  map[item.userId] = []
                }
                map[item.userId].push(item.id);
              }
            })

            const result = await getNeedReallocateTerm(map);
            if (result.data.needReallocateTerms && result.data.needReallocateTerms.length > 0) {
              this.$refs.allocationAutoUserDlg.show(result.data.needReallocateTerms, result.data.users || []);
              return;
            }
          }
          this.uninstallTerminalAll()
        }
      }
    },
    pwdDialogClose() {
      this.submitting = false
      this.loadSubmitting = false
      this.password = ''
      this.verifyVisible = false
    },
    cancelClick() {
      this.submitting = false
      this.loadSubmitting = false
      this.password = ''
      this.verifyVisible = false
    },
    controlComputer() {
      this.submitting = true
      // 如果没有勾选任何终端，提示消息
      if (this.remoteTable().getSelectedIds().length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.currentNoSelectedAnyTerminal'),
          type: 'warning',
          duration: 2000
        })
        this.submitting = false
        return
      }
      if (this.agreementType != 99) { // 远程控制
        this.verifyVisible = true
      } else { // 卸载前，弹窗确认
        this.$confirmBox(this.$t('pages.confirmDeleteCheckedDataMsg', { info: this.$t('pages.terminal') }), this.$t('text.prompt')).then(() => {
          this.verifyVisible = true
        }).catch(() => {
          this.submitting = false
        })
      }
    },
    controlAllComputer() {
      this.submitting = true
      // 未选中任何终端的提示信息
      if (this.checkAll) {
        if (this.backupUnSelectedIds.length === this.allResultLength) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
      } else {
        if (this.backupSelectedIds.length === 0) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
      }
      if (this.agreementType != 99) { // 远程控制
        this.verifyVisible = true
      } else { // 卸载前，弹窗确认
        this.$confirmBox(this.$t('pages.confirmDeleteCheckedDataMsg', { info: this.$t('pages.terminal') }), this.$t('text.prompt')).then(() => {
          this.verifyVisible = true
        }).catch(() => {
          this.submitting = false
        })
      }
    },
    updateGroup() {
      this.submitting = true
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        this.submitting = false
        return
      }
      // 如果没有勾选任何终端，提示消息
      if (this.remoteTable().getSelectedIds().length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.currentNoSelectedAnyTerminal'),
          type: 'warning',
          duration: 2000
        })
        this.submitting = false
        return
      }
      const termIds = this.remoteTable().getSelectedIds().join(',')
      batchUpdateGroup({ ids: termIds, groupId: this.updateGroupId, syncGroup: this.syncGroup }).then(res => {
        this.dialogFormVisible2 = false
        this.submitting = false
        this.gridTable.execRowDataApi(this.query)
      }).catch(() => {
        this.submitting = false
      })
    },
    updateAllGroup() {
      this.submitting = true
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        this.submitting = false
        return
      }
      // 未选中任何终端的提示信息
      if (this.checkAll) {
        if (this.backupUnSelectedIds.length === this.allResultLength) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
      } else {
        if (this.backupSelectedIds.length === 0) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
      }
      if (this.checkAll) {
        this.searchQuery.page = 1
        this.searchQuery.limit = this.allResultLength
        batchUpdateAllGroup({ query: this.searchQuery, groupId: this.updateGroupId, backupUnSelectedIds: this.backupUnSelectedIds, syncGroup: this.syncGroup }).then(res => {
          this.dialogFormVisible2 = false
          this.submitting = false
          this.gridTable.execRowDataApi(this.query)
        }).catch(() => {
          this.submitting = false
        })
      } else {
        const termIds = this.backupSelectedIds.join(',')
        batchUpdateGroup({ ids: termIds, groupId: this.updateGroupId, syncGroup: this.syncGroup }).then(res => {
          this.dialogFormVisible2 = false
          this.submitting = false
          this.gridTable.execRowDataApi(this.query)
        }).catch(() => {
          this.submitting = false
        })
      }
    },
    handleUpdateLoginMode() {
      this.$refs.loginModeDlg.handleUpdate()
    },
    handleGroupNickNameUpdate() {
      this.$refs.grpNickNameDlg.handleUpdate()
    },
    handleUpdateAutoLoginUser() {
      this.$refs.autoLoginUserDlg.handleUpdate()
    },
    batchModifyGroup() {
      // this.terminalSelectIds = selectedData
      // this.$refs.batchModifyGroupDlg.show()
      this.getTreeData()
      this.updateGroupForm = true
      this.backupUnSelectedIds.splice(0)
      this.backupSelectedIds.splice(0)
      this.backupSelectedDatas.splice(0)
      this.checkAll = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      this.syncGroup = false
      this.dialogFormVisible2 = true
      this.effective = 1
      this.password = ''
      this.agreementType = undefined
      this.agreementMsg = ''
      this.remoteControlTerms = this.gridTable.getSelectedDatas().filter(row => row.useType == 0)
      // this.backupRowData.splice(0)
      // this.backupRowData.splice(0, 0, ...JSON.parse(JSON.stringify(this.remoteControlTerms)))
      this.selectLength = this.remoteControlTerms.length
      if (this.selectLength === 0) {
        this.effective = 2
      }
      this.windowsFilter = false
      this.notUsbFilter = false
      this.$nextTick(() => {
        this.remoteTable().toggleAllSelection()
        // 避免修改的查询参数影响到上一个页面的查询
        this.searchQuery = Object.assign({}, this.query)
        this.allRemoteTable().execRowDataApi(this.searchQuery)
      })
    },
    getTreeData() {
      this.treeSelectData = this.groupTreeSelectData.filter(treeData => treeData.dataId != -2)
    },
    handleGeneralLockScreenVerify() {
      this.getCode()
      this.codeVisible = true
    },
    handleExport() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    statusIconFormatter(data) {
      const icons = []
      const termTypeDict = getDict(getTermTypeDict(), data.type)
      if (termTypeDict) {
        icons.push({ class: termTypeDict.icon, title: termTypeDict.label })
      }
      return icons
    },
    groupFormatter: function(row, data, col) {
      const groupNames = []
      const that = this
      if (row.useType != 0) {
        const node = that.groupTree.findNode(that.treeData, -2, 'dataId')
        if (node) {
          col.type == 'showDetail' ? groupNames.push(node.label) : groupNames.push(that.html2Escape(node.label))
        }
      } else if (data) {
        data.forEach(function(groupId) {
          const node = that.groupTree.findNode(that.treeData, groupId, 'dataId')
          if (node) {
            col.type == 'showDetail' ? groupNames.push(node.label) : groupNames.push(that.html2Escape(node.label))
          }
        })
      }
      return groupNames.join(',')
    },
    userNameFormatter: function(row, data) {
      return row.loginMode !== 0 && row.userId !== 0 ? (data || this.$t('pages.null')) : this.$t('pages.null')
    },
    logingUserFormatter: function(row, data) {
      if (row.loginStatus && row.loginStatus.status == 3) {
        return row.loginStatus.userName
      }
      return this.$t('pages.null')
    },
    loginModeFormatter: function(row, data) {
      return loginModeFormatter(row.loginMode)
    },
    curLoginUserSearchParam(row) {
      const userId = row.loginStatus && row.loginStatus.status == 3 ? row.loginStatus.userId : null
      return { userId }
    },
    // 接收一个方法，判断自动登录操作员字段是否允许操作查看详情（避免对源数据进行修改）
    autoLoginSearchParam(row) {
      const userId = row.loginMode !== 0 ? row.userId : null
      return { userId }
    },
    autoLoginUserFormatter: function(row, data) {
      return row.loginMode !== 0 && row.userId !== 0 ? row.user.name : this.$t('pages.null')
    },
    versionFormatter: function(row, data) {
      if (!data) {
        return row.version
      } else {
        return data
      }
    },
    tempUserValidator(rule, value, callback) {
      if (this.tempUser.loginMode === 1 && !value) {
        callback(new Error(this.$t('pages.terminal_text13')))
      } else {
        callback()
      }
    },
    showTip() {
      this.$confirmBox(this.$t('pages.terminal_text14'), this.$t('text.prompt'), {
        confirmButtonText: this.$t('button.confirm2'),
        showCancelButton: false,
        type: 'warning'
      })
    },
    handleLockScreenVerify() {
      this.vcForm = {
        random: '',
        verifiCode: ''
      }
      const title = this.selectedData[0].name
      this.vcTitle = this.$t('pages.terminalVerification', { terminal: title })
      this.vcVisible = true
    },
    getLockScreenVerify() {
      this.vcLoading = true
      const data = {
        random: this.vcForm.random,
        termId: this.selectedData[0].id
      }
      getLockScreenVerify(data).then(res => {
        this.vcForm.verifiCode = res.data
        this.vcLoading = false
      }).catch(() => {
        this.vcLoading = false
      })
    },
    getGeneralAlarmLockCode() {
      const formData = []
      for (const key in this.dataList) {
        if (key == 'generalAlarmLockCode') {
          formData.push({ key: key, value: this.dataList[key].value, isProp: false, remark: this.dataList[key].remark })
        }
      }
      getGeneralCode({ globalConfigList: formData, type: 2 }).then(res => {
        if (this.dataList.generalAlarmLockCode.value === res.data.split('+')[0]) {
          this.$message({
            message: this.$t('pages.lockScreenText22'),
            type: 'warning',
            duration: 2000
          })
        }
        this.dataList.generalAlarmLockCode.value = res.data.split('+')[0]
        this.dataList.generalAlarmLockCode.remark = res.data.split('+')[1]
      })
    },
    getGeneralLockScreenCode() {
      const formData = []
      for (const key in this.dataList) {
        if (key == 'generalLockScreenCode') {
          formData.push({ key: key, value: this.dataList[key].value, isProp: false, remark: this.dataList[key].remark })
        }
      }
      getGeneralCode({ type: 3, globalConfigList: formData }).then(res => {
        if (this.dataList.generalLockScreenCode.value === res.data.split('+')[0]) {
          this.$message({
            message: this.$t('pages.lockScreenText22'),
            type: 'warning',
            duration: 2000
          })
        }
        this.dataList.generalLockScreenCode.value = res.data.split('+')[0]
        this.dataList.generalLockScreenCode.remark = res.data.split('+')[1]
      })
    },
    copyLockCode() {
      this.copy(this.dataList.generalLockScreenCode.value)
    },
    copyAlarmLockCode() {
      this.copy(this.dataList.generalAlarmLockCode.value)
    },
    copy(data) {
      const url = data;
      const oInput = document.createElement('input');
      oInput.value = url;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;
      document.execCommand('Copy'); // 执行浏览器复制命令
      this.$message({
        message: this.$t('pages.copy_success'),
        type: 'success'
      });
      oInput.remove()
    },
    getCode() {
      getGeneralCodePage().then(res => {
        const data = res.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'generalAlarmLockCode') {
            this.dataList.generalAlarmLockCode.value = item.value
            this.dataList.generalAlarmLockCode.remark = item.value ? item.remark : ''
          } else if (item.key === 'generalLockScreenCode') {
            this.dataList.generalLockScreenCode.value = item.value
            this.dataList.generalLockScreenCode.remark = item.value ? item.remark : ''
          }
        }
      })
    },
    remoteRowDataApi: function(option) {
      this.searchQuery = Object.assign({ agreementType: this.agreementType }, this.query, option)
      this.searchQuery.useType = 0
      if (this.windowsFilter) {
        this.searchQuery.types = '0' // 查询时只查询windows端(除老板终端16、U盘终端32)
      } else if (this.notUsbFilter) {
        this.searchQuery.filterBoundUTerm = true // 查询时不查询已绑定U盘的U盘终端
      }
      return getTerminalPage(this.searchQuery).then(res => {
        if (this.notUsbFilter) {
          let bindOTerm = 0
          res.data.items = res.data.items.filter(item => {
            //  未上线的pc终端不支持卸载
            if ([0x00, 0x10, 0x01, 0x11, 0x02, 0x12].includes(item.type) && (!item.loginStatus || !item.loginStatus.status)) {
              bindOTerm++
              return false
            }
            if ((item.type & 0xf0) === 128 && !!item.guid) {
              bindOTerm++
              return false
            }
            return true
          })
          res.data.total -= bindOTerm
        }
        this.allResultLength = res.data.total
        return res
      })
    },
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        // 选中当前页面所有的终端,处理未选中的数据
        if (rowData.length > 0 && this.checkAll) {
          const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
          rowData.forEach(r => {
            if (!backupUnSelectedIdsSet.has(r['id'])) {
              this.allRemoteTable().toggleRowSelection(r, true)
            }
          })
        } else { // 未选中当前页面所有的终端,处理已选中的数据
          const backupSelectedIdsSet = new Set(this.backupSelectedIds)
          rowData.forEach(r => {
            if (backupSelectedIdsSet.has(r['id'])) {
              this.allRemoteTable().toggleRowSelection(r, true)
            }
          })
        }
      })
    },
    selectRemoteData(selection, row) {
      this.$nextTick(() => {
        const id = row['id']
        const selectedIds = this.allRemoteTable().getSelectedIds()
        const isSelect = selectedIds.indexOf(id) >= 0
        // 选中全部的情况下，缓存取消勾选的数据
        if (this.checkAll) {
          // 当前 row 在缓存的未勾选的数据ids中的位置
          const index = this.backupUnSelectedIds.indexOf(id)
          if (isSelect) {
            // 如果是勾选，则移除
            index >= 0 && this.backupUnSelectedIds.splice(index, 1)
          } else {
            // 如果是取消勾选，则添加
            index == -1 && this.backupUnSelectedIds.push(id)
          }
        } else {
          // 未选中的情况下，缓存已勾选的数据
          // 当前 row 在缓存的勾选的数据ids中的位置
          const index = this.backupSelectedIds.indexOf(id)
          if (isSelect) {
            // 如果是勾选，且未添加到数组，则添加
            if (index == -1) {
              this.backupSelectedIds.push(id)
              this.backupSelectedDatas.push(row)
            }
          } else {
            // 如果是取消勾选，且已添加到数组，则移除
            if (index >= 0) {
              this.backupSelectedIds.splice(index, 1)
              this.backupSelectedDatas.splice(index, 1)
            }
          }
        }
      })
    },
    selectAllRemoteData(selection) {
      this.$nextTick(() => {
        const rowDatas = this.allRemoteTable().rowData
        // 当前页面选中数据的长度为0，证明是取消全选
        const isUnselectAll = this.allRemoteTable().getSelectedIds().length === 0
        // 选中全部的情况下，缓存取消勾选的数据
        if (this.checkAll) {
          const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
          rowDatas.forEach(r => {
            const id = r['id']
            const isExist = backupUnSelectedIdsSet.has(id)
            // 取消全选且未缓存，则添加
            if (isUnselectAll && !isExist) {
              backupUnSelectedIdsSet.add(id)
            }
            // 全选且已缓存，则删除
            if (!isUnselectAll && isExist) {
              backupUnSelectedIdsSet.delete(id)
            }
          })
          // 根据 Set 更新 this.backupUnSelectedIds
          this.backupUnSelectedIds = Array.from(backupUnSelectedIdsSet)
        } else {
          // 未选中全部的情况下，缓存已勾选的数据
          const backupSelectedMap = new Map()
          this.backupSelectedIds.forEach((id, i) => { backupSelectedMap.set(id, this.backupSelectedDatas[i]) })
          rowDatas.forEach(r => {
            const id = r['id']
            const isExist = backupSelectedMap.has(id)
            // 全选且已未缓存，则添加
            if (!isUnselectAll && !isExist) {
              backupSelectedMap.set(id, r)
            }
            // 取消全选且已缓存，则删除
            if (isUnselectAll && isExist) {
              backupSelectedMap.delete(id)
            }
          })
          // 根据 Map 更新 this.backupSelectedIds, this.backupSelectedDatas
          this.backupSelectedIds = Array.from(backupSelectedMap.keys())
          this.backupSelectedDatas = Array.from(backupSelectedMap.values())
        }
      })
    },
    allRemoteTable() {
      return this.$refs['allRemoteTable']
    },
    remoteTable() {
      return this.$refs['remoteTable']
    },
    changeSelected() {
      this.backupSelectedIds.splice(0) // 备份已选中的行的id
      this.backupSelectedDatas.splice(0)
      this.backupUnSelectedIds.splice(0)
      if (this.checkAll) {
        this.allRemoteTable().toggleAllSelection()
      } else {
        if (this.allRemoteTable().getSelectedDatas().length > 0) {
          this.allRemoteTable().clearSelection()
        }
      }
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = nodeData.dataId
      }
    },
    allocationAutoUserSubmitEnd(type) {
      if (type === 'confirm') {
        if (this.effective == 1) {
          this.uninstallTerminal()
        } else if (this.effective === 2) {
          this.uninstallTerminalAll()
        }
      } else {
        this.verifyVisible = false
      }
    },
    selfRouteCallBack: function(applyObject) {
      this.$nextTick(() => {
        const { entityId, entityType } = applyObject
        if (entityId && entityType) {
          // 查询类型为终端组
          if (entityType === '3') {
            this.groupTree.setCurrentKey('G' + entityId)
            this.resetQuery()
            this.query.groupId = entityId
            // 导出当前查询时，分组查询条件为跳转的分组
            this.selectTreeId = entityId
            // 查询时不包含子部门
            this.query.includeSubGroup = false
            // 不包含回收站
            this.query.useType = 0
            this.gridTable.execRowDataApi(this.query)
          }
          // 查询类型为当前登录操作员
          if (entityType === '2') {
            this.$refs.groupTree.clearSelectedNode()
            this.resetQuery()
            this.query.lastLoginUserId = entityId
            this.gridTable.execRowDataApi(this.query)
          }
        }
      })
    },
    handleDlpVersionLimit() {
      this.$refs.lowerVersionLimitDlg.show()
    },
    afterLoadClearRouteQuery() {
      // 根据路由查询条件entityId、entityType查询之后，清除查询
      // if (this.$route.query.entityId && this.$route.query.entityType) {
      //   this.$route.query.entityId = undefined
      //   this.$route.query.entityType = undefined
      // }
      this.$router.push({ query: {}});
    },
    number(field) {
      const val = this.query[field]
      if (isNaN(val)) {
        this.query[field] = val.replace(/[^\d]/g, '')
      }
    },
    hasUnlockPermission(key) {
      if (key == 1 || key == 2) {
        return this.hasPermission('603')
      } else {
        return true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .remote-term-wrapper {
    max-height: 120px;
    overflow: auto;
    margin-block-start: 5px;
  }

  .remote-term-wrapper-border {
    border: 1px solid #aaa;
    border-radius: 4px;
  }

  .remote-term-item {
    cursor: pointer;
    color: #409eff;
    &:hover {
      color: #66b1ff;
    }
  }
</style>
