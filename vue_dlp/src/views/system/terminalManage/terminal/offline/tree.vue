<template>
  <div class="tree-container">
    <tree-menu
      ref="terminalTree"
      local-search
      expand-on-click-node
      :default-expanded-keys="expandedKeys"
      :data="logTermTree"
      :resizeable="false"
      :filter-key="terminalTypeFilter"
      :render-content="renderTreeNodeContent"
      @node-click="handleTreeNodeChange"
    />
  </div>
</template>

<script type="text/jsx">
import { getTermTypeDict } from '@/utils/dictionary'
import { mapGetters } from 'vuex'

export default {
  name: 'OfflineTermTree',
  data() {
    return {
      currentKey: undefined,
      expandedKeys: ['G1'],
      iconOption: {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        127: 'error',
        'pre': 'arrowUp',
        'next': 'arrowDown',
        1: 'terminal',
        2: 'user',
        3: 'terminalGroup',
        4: 'userGroup',
        'search': 'search'
      }
    }
  },
  computed: {
    ...mapGetters(['logTermTree'])
  },
  watch: {
    logTermTree() {
      this.$nextTick(() => {
        if (this.currentKey) {
          this.treeRef().setCurrentKey(this.currentKey)
        }
      })
    }
  },
  created() {
    getTermTypeDict().forEach(item => {
      this.$set(this.iconOption, item.value, item.icon)
    })
  },
  methods: {
    terminalTypeFilter(node) {
      const type = parseInt(node.dataType)
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const isFilter = (type & 0xf0) === 0x80
      return isFilter
    },
    handleTreeNodeChange(data, node) {
      if (data.dataType === 'G') {
        this.currentKey = undefined
        return
      }
      if (this.currentKey === data.id) {
        return
      }
      this.currentKey = data.id
      this.$emit('change', { ...data })
    },
    renderTreeNodeContent(h, { node, data, store }) {
      let iconClass = this.iconOption[data.dataType] || this.iconOption[data.type]
      if (!iconClass) {
        return (<span title={node.label}>{node.label}</span>)
      }
      // data.active是终端的在线状态，true在线false离线，在线终端图标则显示为绿色
      if ((iconClass.indexOf('usb-') === 0 || iconClass.indexOf('offline-') === 0) && !data.dataCode) {
        iconClass += '-x'
      }
      const isGroup = data.dataType === 'G' || data.type === 3
      let operator
      if (isGroup && data.id !== 'G0') {
        const tips = this.i18nConcatText(this.$t('pages.terminal'), 'create')
        const onClick = event => this.handleAddTerm(data, event)
        operator = (<span class='el-ic' title={tips} on-click={onClick}><svg-icon icon-class='add' className='icon-space'/></span>)
      }
      return (
        <div class='custom-tree-node'>
          <span title={node.label}>
            <svg-icon style={({ color: data.colorClass })} icon-class={iconClass}/>
            {node.label}
          </span>
          {operator}
        </div>
      )
    },
    treeRef() {
      return this.$refs.terminalTree
    },
    cancelCurrentNode() {
      this.setCurrentTreeNode(null)
    },
    setCurrentTreeNode(termId) {
      if (termId == null) {
        this.currentKey = null
        this.treeRef().clearSelectedNode()
      } else {
        this.currentKey = 'T' + termId
        this.treeRef().setCurrentKey(this.currentKey)
      }
    },
    handleAddTerm(data, event) {
      event.preventDefault()
      event.stopPropagation()
      this.$emit('add', { ...data })
    }
  }
}
</script>

<style lang="scss" scoped>
  .tree-container {
    position: absolute;
    >>>.custom-tree-node {
      color: #666666;
      .el-ic {
        color: #4386c6;
      }
    }
  }
</style>
