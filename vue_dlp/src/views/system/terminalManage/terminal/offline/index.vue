<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.offlineTermManagement')"
      :width="$store.getters.language === 'en' ? '800px' : '720px'"
      :visible.sync="visible"
    >
      <div class="term-container">
        <offline-term-tree
          v-if="visible"
          ref="tree"
          @change="handleTreeNodeChange"
          @add="handleTreeNodeAdd"
          @updated="handleTreeNodeUpdated"
        />
        <Form ref="termForm" class="term-form" :model="temp" :rules="rules" label-position="right" label-width="88px">
          <el-divider content-position="left">
            {{ $t('route.terminalInfo') }}
            <el-button v-show="!termNullErr" class="term-edit-btn" type="text" icon="el-icon-edit" :title="$t('pages.editTerminal')" @click="handleUpdate"/>
          </el-divider>
          <FormItem :label="$t('table.terminalCode')" :error="termNullErr">
            <el-input v-model="temp.id" readonly/>
          </FormItem>
          <FormItem :label="$t('table.terminalName')">
            <el-input v-model="temp.name" :title="temp.name" readonly/>
          </FormItem>
          <FormItem :label="$t('pages.terminalType')">
            <el-input v-model="temp.typeName" :title="temp.typeName" readonly/>
          </FormItem>
          <FormItem :label="$t('table.terminalGroup')">
            <el-input v-model="temp.groupName" :title="temp.groupNames" readonly/>
          </FormItem>
          <FormItem :label="$t('table.loginMode')">
            <el-input v-model="temp.loginModeName" readonly/>
          </FormItem>
          <div class="strategy-tips">
            <div>{{ $t('pages.uDiskStrategyTips1') }}</div>
            <!--<span>若要重新分配模块，请点击</span>-->
            <span v-permission="'A22'">{{ $t('pages.uDiskStrategyTips2') }}</span>
            <el-button v-permission="'A22'" type="mini" :disabled="!temp.id" :loading="moduleLoading" @click="handleModule()">
              {{ $t('route.moduleConfig') }}
            </el-button>
          </div>

          <template v-if="temp.id && !temp.occupied">
            <el-divider content-position="left">{{ $t('pages.offlineTerm_installationAuthorization') }}</el-divider>
            <FormItem :label="$t('pages.offlineTerm_identificationCode')" prop="identificationCode" label-width="88px" :extra-width="{en: 100}">
              <el-input v-model="temp.identificationCode" maxlength="6" @input="handleInput"/>
            </FormItem>
            <div class="strategy-tips">{{ identificationCodeTips }}</div>
            <!--
            <FormItem label-width="0">
              <el-checkbox v-model="temp.needPwd" :true-label="1" :false-label="0">终端导入授权文件时需要校验密码</el-checkbox>
            </FormItem>
            <template v-if="temp.needPwd">
              <FormItem :label="$t('form.password')" prop="password">
                <password-input v-if="visible" ref="pwdinput" v-model="temp.password" type="password" input-class="pwd-clr" show-password clearable/>
              </FormItem>
              <FormItem :label="$t('form.confirmPassword')" prop="confirmPwd">
                <el-input v-model="temp.confirmPwd" type="password" class="pwd-clr" show-password clearable/>
              </FormItem>
            </template>
            -->
          </template>
        </Form>
      </div>
      <div class="strategy-tips">
        <i class="el-icon-warning"/>
        <i18n path="pages.offlineTerm_installTips">
          <el-button slot="file" type="text" :disabled="!hasPermission('B2C')" @click="toOfflineStrategyPage">
            {{ $t('route.offlineStrategy') }}
          </el-button>
        </i18n>
      </div>
      <div slot="footer" class="dialog-footer">
        <template v-if="temp.id">
          <el-button v-show="!temp.occupied" type="primary" :loading="licenseLoading" @click="openConfirmBox">
            {{ $t('pages.offlineTerm_generateLicenseFile') }}
          </el-button>
          <el-button v-show="temp.occupied" v-permission="'103'" type="primary" @click="handleDelete">
            {{ $t('pages.agreementType9') }}
          </el-button>
          <el-button v-show="temp.occupied" type="primary" :loading="licenseLoading" @click="handleExportLic">
            {{ $t('pages.offlineTerm_exportLicenseFile') }}
          </el-button>
        </template>
        <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
      </div>

      <el-dialog
        v-el-drag-dialog
        :title="$t('text.prompt')"
        :close-on-click-modal="false"
        :modal="false"
        show-close
        width="400px"
        class="lic-message-box"
        :visible.sync="confirmVisible"
        @closed="closeConfirmBox"
      >
        <div class="lic-message-status el-icon-warning"></div>
        <div class="lic-message-content">
          <p>{{ $t('pages.offlineTerm_bindConfirm') }}</p>
          <p class="lic-message-code">{{ temp.identificationCode }}</p>
          <ul class="lic-message-tips">
            <li>{{ $t('pages.offlineTerm_bindTips1') }}<span>{{ $t('pages.offlineTerm_bindTips3') }}</span></li>
            <li>{{ $t('pages.offlineTerm_bindTips2') }}</li>
          </ul>
        </div>
        <div slot="footer">
          <el-button type="primary" :disabled="confirmCountdown > 0" @click="handleGenLic">{{ licConfirmBtn }}</el-button>
          <el-button @click="confirmVisible = false">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>
    </el-dialog>

    <delete-offline-term ref="dot" :terminal-type="temp.type" @deleted="handleDeleted"/>
    <add-terminal ref="addTerminal" :type-filter="isOfflineTerm" group-readonly @add="handleAdded"/>
    <bind-user-dlg2 ref="bindUsrDlg"/>
    <module-edit ref="mod" @change="handleModuleChange"/>
    <edit-terminal
      ref="editTerm"
      :title="$t('pages.editTerminal')"
      :group-data="groupData"
      :disabled="false"
      @updated="handleUpdated"
    />
  </div>
</template>

<script type="text/jsx">
// import PasswordInput from '../uterm/pwdinput'
import AddTerminal from '../add'
import EditTerminal from '../edit'
import BindUserDlg2 from '../bindUserDlg2'
import OfflineTermTree from './tree'
import DeleteOfflineTerm from './delete'
import ModuleEdit from '@/views/system/terminalManage/moduleConfig/edit'
import {
  getOfflineTermInfo,
  checkBindUserAndModules,
  genLicense,
  exportLicense,
  getUninstallCode,
  inputNumber
} from '@/api/dataEncryption/encryption/offlineTerminal'
import { osTypeFormatter, loginModeFormatter } from '@/utils/formatter'
import { buildDownloadFileByName } from '@/utils/download/helper'
import moment from 'moment'

export default {
  name: 'OfflineTerm',
  components: { AddTerminal, EditTerminal, BindUserDlg2, OfflineTermTree, DeleteOfflineTerm, ModuleEdit },
  props: {
    groupData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      moduleLoading: false,
      licenseLoading: false,
      confirmVisible: false,
      confirmTimer: undefined,
      confirmCountdown: 0,
      generating: false,
      temp: {},
      defaultTemp: {
        id: undefined,
        name: undefined,
        typeName: undefined,
        groupName: undefined,
        groupNames: undefined,
        loginMode: 0,
        loginModeName: undefined,
        occupied: false,
        identificationCode: undefined,
        needPwd: 0,
        password: undefined,
        confirmPwd: undefined
      },
      rules: {
        identificationCode: [{ required: true, len: 6, message: this.$t('pages.offlineTerm_pleaseInputIIC'), trigger: 'blur' }]
      }
    }
  },
  computed: {
    termNullErr() {
      return !this.temp.id ? this.$t('pages.chooseTerminal') : undefined
    },
    identificationCodeTips() {
      if (this.temp.type === 130) {
        return this.$t('pages.offlineTerm_identificationCodeTips2')
      }
      return this.$t('pages.offlineTerm_identificationCodeTips')
    },
    licConfirmBtn() {
      if (!this.confirmCountdown) {
        return this.$t('button.confirm2')
      }
      return `${this.$t('button.confirm2')}（${this.confirmCountdown}s）`
    },
    enLanguage() {
      const language = this.$store.getters.language || 'zh';
      return language === 'en'
    }
  },
  methods: {
    show() {
      this.temp = { ...this.defaultTemp }
      this.visible = true
    },
    toOfflineStrategyPage() {
      this.$router.push('/terminalManage/terminalManage/offlineStrategy')
    },
    isOfflineTerm(type) {
      return (type & 0xf0) === 0x80 && (type & 0xf) < 3
    },
    getTerminalInfo(id) {
      return getOfflineTermInfo(id).then(res => {
        const { type, name, groupNames, loginMode, autoLoginUserId, autoLoginUserName, occupied, remark } = res.data
        const typeName = osTypeFormatter(type)
        const loginModeName = loginModeFormatter(loginMode)
        if (groupNames) {
          const groupName = groupNames.split(' => ').pop()
          this.temp = { ...this.defaultTemp, id, type, typeName, name, groupName, groupNames, loginMode, loginModeName, occupied, remark }
        } else {
          this.temp = { ...this.defaultTemp, id, type, typeName, name, loginModeName, occupied, remark }
        }
        if (autoLoginUserId) {
          this.temp.userId = autoLoginUserId
          this.temp.user = { name: autoLoginUserName }
        } else {
          this.temp.userId = 0
          this.temp.user = { name: '' }
        }
        this.$refs['termForm'].clearValidate()
        return res.data
      })
    },
    handleTreeNodeChange(nodeData) {
      this.getTerminalInfo(nodeData.dataId).then(() => {
        this.temp.groupIds = [parseInt(nodeData.parentId.slice(1))]
      })
    },
    handleTreeNodeUpdated(vm) {
      vm.setCurrentTreeNode(this.temp.id)
    },
    handleTreeNodeAdd(groupNode) {
      this.$refs['addTerminal'].show(parseInt(groupNode.dataId), groupNode.label)
    },
    handleAdded(data) {
      this.$refs.tree.cancelCurrentNode()
      this.$emit('change')
      this.getTerminalInfo(data.id).then(() => {
        this.temp.groupIds = data.groupIds
        this.$refs.tree.setCurrentTreeNode(data.id)
      })
    },
    handleUpdate() {
      this.$refs['editTerm'].show(this.temp)
    },
    handleUpdated(data) {
      this.$emit('change')
      this.getTerminalInfo(data.id).then(() => {
        this.temp.groupIds = data.groupIds
      })
    },
    handleModule(message) {
      this.generating = typeof message === 'string'
      this.moduleLoading = true
      this.$refs['mod'].show(this.temp.id, message).finally(() => {
        this.moduleLoading = false
      })
    },
    handleModuleChange(modules) {
      if (this.generating) {
        this.generating = false
        if (modules.filter(item => item.checked).length > 0) {
          this.generateLicense(false)
        }
      }
    },
    handleInput(value) {
      this.temp.identificationCode = inputNumber(value)
    },
    handleDelete() {
      const { id, name } = this.temp
      getUninstallCode(id).then(res => {
        const { occupied, status, uninstallCode } = res.data
        if (occupied) {
          this.$refs['dot'].show(id, name, uninstallCode)
          return
        }
        this.$notify({
          title: this.$t('text.error'),
          message: {
            1: this.$t('pages.offlineTerm_bindErr1'),
            2: this.$t('pages.offlineTerm_bindErr2'),
            3: this.$t('pages.offlineTerm_bindErr3'),
            4: this.$t('pages.offlineTerm_bindErr4'),
            5: this.$t('pages.offlineTerm_bindErr5')
          }[status],
          type: 'error',
          duration: 5000
        })
      })
    },
    handleDeleted(deleteTerm) {
      if (deleteTerm) {
        this.temp = { ...this.defaultTemp }
      } else {
        this.$set(this.temp, 'occupied', false)
      }
      this.$emit('change')
    },
    openConfirmBox() {
      if (this.confirmTimer) {
        clearInterval(this.confirmTimer)
      }
      this.$refs['termForm'].validate(valid => {
        if (valid) {
          // 倒计时15秒
          this.confirmCountdown = 15
          this.confirmVisible = true
          this.confirmTimer = setInterval(() => {
            this.confirmCountdown--
            if (this.confirmCountdown <= 0) {
              clearInterval(this.confirmTimer)
              this.confirmTimer = undefined
            }
          }, 1000)
        }
      })
    },
    closeConfirmBox() {
      this.confirmCountdown = 0
      if (this.confirmTimer) {
        clearInterval(this.confirmTimer)
        this.confirmTimer = undefined
      }
    },
    buildLicFile() {
      const name = `${this.temp.name}(${this.temp.id})_${this.temp.identificationCode || ''}_${this.$t('pages.installLicenseFile')}_${moment().format('YYYYMMDD')}.lic`
      const file = buildDownloadFileByName(name)
      file.steps = 1
      return file
    },
    handleGenLic() {
      this.confirmVisible = false
      this.licenseLoading = true
      const { id, name, loginMode, loginModeName } = this.temp
      // this.$refs['termForm'].validate().then(() => {
      //   const h = this.$createElement
      //   return this.$msgbox({
      //     type: 'warning',
      //     title: this.$t('text.prompt'),
      //     message: h('div', null, [
      //       h('p', null, this.$t('pages.offlineTerm_bindConfirm')),
      //       h('p', { style: 'font-size: 36px; letter-spacing: 16px; text-align: center; margin: 15px;' }, identificationCode),
      //       h('ul', { style: 'color: #2674b2; line-height: 24px; padding-inline-start: 20px;' }, [
      //         h('li', null, this.$t('pages.offlineTerm_bindTips1')),
      //         h('li', null, this.$t('pages.offlineTerm_bindTips2'))
      //       ])
      //     ]),
      //     showCancelButton: true,
      //     confirmButtonText: this.$t('button.confirm2'),
      //     cancelButtonText: this.$t('button.cancel'),
      //     cancelButtonClass: 'btn-custom-cancel',
      //     closeOnClickModal: false
      //   }).then(() => checkBindUserAndModules(id))
      // })
      checkBindUserAndModules(id).then(res => {
        const { needBindUser, needAllocModules } = res.data
        if (needBindUser) {
          return this.$confirmBox(this.$t('pages.uDiskTerminalUnassignedModuleMsg2', { name, mode: loginModeName }), this.$t('text.prompt')).then(() => {
            // 绑定操作员弹窗
            const data = { id, name, loginMode }
            this.$refs.bindUsrDlg.show(data)
            return Promise.reject('bindUser')
          }).catch(reason => {
            if (reason === 'bindUser') {
              return Promise.reject(false)
            }
            return Promise.resolve(needAllocModules)
          })
        }
        return Promise.resolve(needAllocModules)
      }).then(needAllocModules => {
        if (!needAllocModules || !this.hasPermission('A22')) {
          return Promise.resolve(false)
        }
        const action = this.$t('pages.offlineTerm_generateLicenseFile');
        const message = this.$t('pages.uDiskTerminalUnassignedModuleMsg1', { action: this.enLanguage ? action.toLowerCase() : action })
        const option = { confirmButtonText: this.$t('pages.autoAllocation'), cancelButtonText: this.$t('pages.manualAllocation'), distinguishCancelAndClose: true }
        return this.$confirmBox(message, this.$t('text.prompt'), option).then(() => {
          return Promise.resolve(true)
        }).catch(reason => {
          if (reason === 'cancel') {
            this.handleModule(message)
          }
          return Promise.reject(reason)
        })
      }).then(needAllocModules => {
        return this.generateLicense(needAllocModules)
      }).catch(() => {
        this.licenseLoading = false
      })
    },
    generateLicense(needAllocModules) {
      const { id, name, identificationCode } = this.temp
      const data = { id, name, identificationCode }
      if (needAllocModules) {
        data.needAllocModules = true
      }
      const opts = { file: this.buildLicFile(), jwt: true, topic: 'offlineTerm' }
      return genLicense(data, opts).then(() => {
        this.$set(this.temp, 'occupied', true)
        this.$set(this.temp, 'identificationCode', undefined)
        this.$emit('change')
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.offlineTerm_generateLicenseFile') + this.$t('text.success'),
          type: 'success',
          duration: 2000
        })
      }).finally(() => {
        this.licenseLoading = false
      })
    },
    handleExportLic() {
      this.licenseLoading = true
      const { id, name } = this.temp
      const opts = { file: this.buildLicFile(), jwt: true, topic: 'offlineTerm' }
      exportLicense({ id, name }, opts).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.offlineTerm_exportLicenseFile') + this.$t('text.success'),
          type: 'success',
          duration: 2000
        })
      }).finally(() => {
        this.licenseLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-dialog__body {
    max-height: 660px;
  }
  .strategy-tips {
    line-height: 30px;
    color: #2674b2;
    padding: 15px 20px 0;
    .el-icon-warning {
      color: #E6A23C;
      font-size: 24px;
      padding-right: 5px;
      position: relative;
      top: 3px;
    }
    .el-button--text {
      padding-right: 0;
      >>>span {
        border-bottom: 1px solid;
      }
    }
  }
  .term-container {
    padding: 5px 20px;
    position: relative;
    .term-form {
      overflow: auto;
      padding-left: 225px;
      .term-edit-btn {
        margin: 0;
        padding: 0;
      }
      >>>.el-input__inner[readonly] {
        cursor: default;
        background-color: #e4e7e9;
      }
    }
    .strategy-tips {
      padding: 0;
    }
  }
  .lic-message-box {
    >>>.el-dialog__body {
      padding: 10px 15px;
    }
    .lic-message-status {
      color: #E6A23C;
      font-size: 24px;
      position: absolute;
      top: 44px;
    }
    .lic-message-content {
      padding-left: 36px;
      padding-right: 12px;
      margin: 0;
      >p {
        margin: 0;
        line-height: 24px;
      }
      .lic-message-code {
        font-size: 36px;
        letter-spacing: 16px;
        text-align: center;
        margin: 15px;
      }
      .lic-message-tips {
        color: #2674b2;
        line-height: 24px;
        padding-inline-start: 20px;
        span {
          color: #f56c6c;
        }
      }
    }
    >>>.el-dialog__footer {
      border: 0;
      box-shadow: none;
    }
  }

</style>

<style lang="scss">
  .pwd-clr.el-input--suffix {
    &:hover .el-input__inner, .el-input__inner:focus {
      padding-right: 55px;
    }
  }
</style>
