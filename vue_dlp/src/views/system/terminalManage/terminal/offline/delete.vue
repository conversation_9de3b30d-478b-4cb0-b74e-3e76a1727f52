<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.offlineTerm_uninstall')"
    :close-on-click-modal="false"
    :modal="false"
    width="480px"
    :visible.sync="visible"
  >
    <Form ref="delForm" :model="temp" label-width="182px">
      <FormItem :label="$t('pages.offlineTerm_uninstallCode')">
        <el-input v-model="temp.uninstallCode" readonly/>
      </FormItem>
      <FormItem :label="$t('pages.offlineTerm_uninstallFinishCode')" :error="ufcError" required>
        <el-input v-model="temp.uninstallFinishCode" maxlength="6" @blur="handleUfcBlur" @input="handleUfcInput"/>
      </FormItem>
      <FormItem :label="$t('pages.enterAdminPwd')" :error="pwdError" required>
        <el-input v-model="temp.password" type="password" maxlength="100" show-password @blur="handlePwdBlur" @input="handlePwdInput"/>
      </FormItem>
      <FormItem v-if="hasPermission('370')" label-width="5px">
        <el-checkbox v-model="temp.deleteTerm">
          {{ $t('pages.delTerminal') }}
          <el-tooltip class="item" effect="dark" :content="$t('pages.delTerminal_text0')" placement="top">
            <i class="el-icon-info" />
          </el-tooltip>
        </el-checkbox>
      </FormItem>
      <FormItem v-if="temp.deleteTerm" v-permission="'106&370'" label-width="5px">
        <el-checkbox v-model="temp.deleteUser">
          {{ $t('pages.delUser') }}
          <el-tooltip class="item" effect="dark" :content="$t('pages.delUser_text')" placement="top">
            <i class="el-icon-info" />
          </el-tooltip>
        </el-checkbox>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <common-downloader
        class="download-btn"
        :name="toolName"
        :button-name="$t('pages.offlineTerm_downloadTool')"
        button-type="primary"
        @download="handleDownload"
      />
      <el-button type="primary" :loading="submitting" @click="handleDelete">{{ $t('button.confirm') }}</el-button>
      <el-button @click="visible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { validatePassword } from '@/api/user'
import { uninstallOfflineTerminal, inputNumber } from '@/api/dataEncryption/encryption/offlineTerminal'
import { downloadTool } from '@/api/dataEncryption/encryption/fileOutgoing'
import CommonDownloader from '@/components/DownloadManager/common'
import { aesEncode, formatAesKey } from '@/utils/encrypt';

export default {
  name: 'DeleteOfflineTerm',
  components: { CommonDownloader },
  props: {
    terminalType: {
      type: Number,
      default: 0x80
    }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        uninstallCode: undefined,
        uninstallFinishCode: undefined,
        password: undefined,
        deleteTerm: true,
        deleteUser: true
      },
      ufcError: undefined,
      pwdError: undefined
    }
  },
  computed: {
    toolName() {
      let name = this.$t('pages.uninstallFinishCodeTool')
      if (this.terminalType === 0x80) {
        name += '.exe'
      } else {
        name += '.zip'
      }
      return name
    }
  },
  methods: {
    handleDownload(file) {
      downloadTool({ toolType: this.terminalType }, file)
    },
    show(id, name, uninstallCode) {
      this.temp = { ...this.defaultTemp, id, name, uninstallCode }
      this.submitting = false
      this.ufcError = undefined
      this.pwdError = undefined
      this.visible = true
    },
    handleUfcBlur() {
      if (this.temp.uninstallFinishCode && this.temp.uninstallFinishCode.length === 6) {
        this.ufcError = undefined
        return true
      }
      this.ufcError = this.$t('pages.offlineTerm_pleaseInputUFC')
      return false
    },
    handleUfcInput(value) {
      this.ufcError = undefined
      this.temp.uninstallFinishCode = inputNumber(value)
    },
    handlePwdBlur() {
      this.passwordValidate()
    },
    handlePwdInput() {
      this.pwdError = undefined
    },
    handleDelete() {
      if (!this.handleUfcBlur()) {
        return
      }
      this.submitting = true
      this.passwordValidate(true).then(() => {
        return this.$confirmBox(this.$t('pages.confirmUninstallTerminalMsg', { terminal: this.temp.name }), this.$t('text.prompt')).then(() => {
          const data = { ...this.temp }
          if (!data.deleteTerm) {
            data.deleteUser = false
          }
          const tempData = JSON.parse(JSON.stringify(data))
          tempData.password = undefined
          return uninstallOfflineTerminal(tempData).then(() => {
            this.submitting = false
            this.visible = false
            this.$emit('deleted', data.deleteTerm)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.uninstallSuccess'),
              type: 'success',
              duration: 5000
            })
          })
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    passwordValidate(notOnlyRequired) {
      if (!this.temp.password) {
        this.pwdError = this.$t('pages.pwdCantNull')
        return Promise.reject()
      }
      if (notOnlyRequired) {
        const password = aesEncode(this.temp.password, formatAesKey('tr838408', ''))
        return validatePassword({ password }).then(respond => {
          if (respond.data) {
            this.pwdError = undefined
            return Promise.resolve()
          } else {
            this.pwdError = this.$t('pages.administratorPasswordError')
            return Promise.reject()
          }
        })
      } else {
        this.pwdError = undefined
        return Promise.resolve()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-input__inner[readonly] {
    cursor: default;
    background-color: #e4e7e9;
  }
  .download-btn {
    float: left;
    margin-left: 0;
    >>>.el-button {
      margin-left: 0;
      /*max-width: 180px;*/
    }
  }
</style>
