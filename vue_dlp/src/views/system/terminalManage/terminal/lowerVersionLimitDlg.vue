<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleDrag"
  >
    <template slot="title">
      <span style="color: #333; font-weight: bold">{{ $t('pages.newTerminalTermVersionRestriction') }}</span>
      <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
        <div slot="content" class="tip">
          <p>{{ $t('pages.newTerminal_text9') }}</p>
        </div>
        <i class="el-icon-info" style="color: #333" />
      </el-tooltip>
    </template>

    <el-row :style="{ 'height': '60px', 'position': 'relative' }">
      <el-col :span="isEnglish() ? 24 : 18">
        <Form ref="versionForm" hide-required-asterisk :rules="rules" :model="temp" label-position="right" label-width="55px">
          <el-row>
            <el-col :span="9">
              <FormItem :label="$t('pages.terminalType')" label-width="70px" :extra-width="{en: 10}" prop="type">
                <el-select v-model="temp.type">
                  <el-option v-for="item in versions" :key="item.type" :value="item.type" :label="item.label"/>
                </el-select>
              </FormItem>
            </el-col>
            <el-col :span="15">
              <FormItem :label="$t('table.packageVersion')" prop="version" label-width="80px" :extra-width="{en: 40}">
                <template slot="label">
                  <span style="color: #666; font-weight: bold">{{ $t('table.packageVersion') }}</span>
                  <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                    <div slot="content" class="tip">
                      <p>{{ $t('pages.newTerminal_text10') }}</p>
                      <p>{{ $t('pages.newTerminal_text11') }}</p>
                    </div>
                    <i class="el-icon-info" style="color: #666" />
                  </el-tooltip>
                </template>
                <el-input v-model="temp.version" :placeholder="$t('pages.newTerminal_text12')" :maxlength="25" clearable></el-input>
              </FormItem>
            </el-col>
          </el-row>
        </Form>
      </el-col>
      <el-col v-if="!isEnglish()" :span="6">
        <div style="float:right;margin: 5px 0;">
          <el-button size="small" style="margin-left: 5px;" @click="createVersion">{{ $t('button.insert') }}</el-button>
          <el-button size="small" style="margin-left: 5px;" :disabled="!editAble" @click="updateVersion">{{ $t('button.edit') }}</el-button>
          <el-button size="small" style="margin-left: 5px;" :disabled="!deleteAble" @click="deleteVersion">{{ $t('button.delete') }}</el-button>
          <el-button size="small" style="margin-left: 5px;" @click="cancelVersion">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-col>
      <div v-if="unDerVersionShow" :style="{ 'position': 'absolute', 'bottom': '5px', 'right': isEnglish() ? '10px' : null }">
        {{ unDerVersionLabel }}
      </div>
    </el-row>

    <el-row v-if="isEnglish()">
      <div style="float:left;margin: 0;">
        <el-button size="small" style="margin-left: 5px;" @click="createVersion">{{ $t('button.insert') }}</el-button>
        <el-button size="small" style="margin-left: 5px;" :disabled="!editAble" @click="updateVersion">{{ $t('button.edit') }}</el-button>
        <el-button size="small" style="margin-left: 5px;" :disabled="!deleteAble" @click="deleteVersion">{{ $t('button.delete') }}</el-button>
        <el-button size="small" style="margin-left: 5px;" @click="cancelVersion">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-row>

    <grid-table
      ref="versionList"
      :height="180"
      node-key="id"
      :show-pager="false"
      :multi-select="true"
      :col-model="colModel"
      :row-datas="rowDatas"
      @selectionChangeEnd="versionSelectionChange"
      @currentChange="versionCurrentChange"
    />

    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateDlpVersion()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="cancel">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { countUnderVersionTerminal } from '@/api/system/terminalManage/terminal'
import { getDlpVersionLimit, updateDlpVersionLimit } from '@/api/system/terminalManage/approvalAccess';

export default {
  name: 'LowerVersionLimitDlg',
  props: {
  },
  data() {
    return {
      submitting: false,
      dialogVisible: false,
      temp: {},
      defaultTemp: {
        version: '',
        type: 0,
        isValid: 0
      },
      versions: [
        { type: 0, label: this.$t('pages.newTerminalWindowsTerm'), os: 'Windows' },
        { type: 1, label: this.$t('pages.newTerminalLinuxTerm'), os: 'Linux' },
        { type: 2, label: this.$t('pages.newTerminalMacTerm'), os: 'Mac' },
        { type: 3, label: this.$t('pages.MobileTerminal'), os: this.$t('pages.newTerminal_textPhone') }
      ],
      colModel: [
        { prop: 'type', label: 'terminalType', width: '150', sort: true, formatter: this.termTypeFormatter },
        { prop: 'version', label: 'packageVersion', width: '150', sort: true }
      ],
      rules: {
        type: [{ required: true, validator: this.typeValidator, trigger: 'change' }],
        version: [{ required: true, validator: this.versionValidator, trigger: 'blur' }]
      },
      rowDatas: [],
      unDerVersionShow: false,
      unDerVersionTotal: 0,
      termTypeLabel: '',
      unDerVersionLabel: '',
      editAble: false,
      deleteAble: false
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    show() {
      this.initData()
      this.dialogVisible = true
    },
    initData() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.rowDatas = []
      this.unDerVersionShow = false
      this.deleteAble = false
      this.editAble = false
      getDlpVersionLimit().then(res => {
        const datas = res.data || []
        for (let i = 0, len = datas.length; i < len; i++) {
          datas[i].id = i + 1;
        }
        this.rowDatas = datas
      })
    },
    termTypeFormatter(row) {
      const data = this.versions.find(item => { return item.type === row.type })
      return data !== null ? data.label : ''
    },
    versionSelectionChange(rowDatas) {
      this.deleteAble = rowDatas.length > 0
    },
    versionCurrentChange: function(rowData) {
      this.temp = Object.assign({}, this.defaultTemp, rowData)
      this.$refs['versionForm'].validate('version').then((valid) => { }).catch(() => {})
      if (rowData) this.editAble = true
    },
    restTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    createVersion() {
      this.$refs['versionForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.temp)
          rowData.id = this.rowDatas.length + 1;
          if (this.rowDatas.findIndex(item => { return item.type === rowData.type }) === -1) {
            this.rowDatas.unshift(rowData)
            this.cancelVersion()
          } else {
            this.$message({
              message: this.$t('pages.newTerminalText17'),
              type: 'error',
              duration: 2000
            })
          }
        }
      })
    },
    updateVersion() {
      this.$refs['versionForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.temp)
          //  校验是否已存在该终端类型
          const index = this.rowDatas.findIndex(item => { return item.type === rowData.type })
          if (index > -1 && this.rowDatas[index].id !== rowData.id) {
            this.$message({
              message: this.$t('pages.newTerminalText17'),
              type: 'error',
              duration: 2000
            })
            return;
          }
          for (let i = 0, len = this.rowDatas.length; i < len; i++) {
            if (this.rowDatas[i].id === rowData.id) {
              this.rowDatas[i].type = rowData.type
              this.rowDatas[i].version = rowData.version;
              break;
            }
          }
          this.cancelVersion()
        }
      })
    },
    deleteVersion() {
      const toDeleteIds = this.$refs['versionList'].getSelectedIds()
      if (toDeleteIds.length > 0) {
        this.rowDatas = this.rowDatas.filter(item => { return !toDeleteIds.includes(item.id); })
      }
    },
    cancelVersion() {
      this.$refs['versionList'].setCurrentRow()
      this.temp = Object.assign({}, this.defaultTemp)
      this.editAble = false
      this.unDerVersionShow = false
      this.$refs['versionForm'].clearValidate()
    },
    async typeValidator(rule, value, callback) {
      await this.dlpVersionValidator(rule, value, callback, true);
    },
    async versionValidator(rule, value, callback) {
      await this.dlpVersionValidator(rule, value, callback, false);
    },
    //  校验dlp版本号是否正确,版本号由 大版本号.小版本号.软件发布日期（软件发布日期=年份后两位+月+日）
    async dlpVersionValidator(rule, value, callback, flag) {
      this.unDerVersionShow = false
      if (this.temp.version.length === 0) {
        flag ? callback() : callback(new Error(this.$t('pages.newTerminal_text13') + ''))
        return;
      }
      const arr = this.temp.version.split('.');
      if (arr.length !== 3) {
        flag ? callback() : callback(new Error(this.$t('pages.newTerminal_text14') + ''))
        return;
      } else {
        if (arr[2].length !== 6) {
          flag ? callback() : callback(new Error(this.$t('pages.newTerminal_text14') + ''))
          return;
        }
        const number = new RegExp(/^[0-9]*$/)
        const flag1 = number.exec(arr[0]);
        const flag2 = number.exec(arr[1]);
        const flag3 = number.exec(arr[2]);
        if (!(flag1 && flag2 && flag3)) {
          flag ? callback() : callback(new Error(this.$t('pages.newTerminal_text14') + ''))
          return;
        }
        let month = arr[2].substr(2, 2);
        let day = arr[2].substr(4, 2)
        month = parseInt(month)
        if (month <= 0 || month > 12) {
          flag ? callback() : callback(new Error(this.$t('pages.newTerminal_text14') + ''))
          return;
        }
        day = parseInt(day)
        const twoMaxDay = month === 2 ? 29 : 31;
        if (day <= 0 || day > twoMaxDay) {
          flag ? callback() : callback(new Error(this.$t('pages.newTerminal_text14') + ''))
          return;
        }
      }
      //  查询终端个数
      const data = this.versions.filter(item => item.type === this.temp.type);
      this.termTypeLabel = data ? data[0].os : ''

      this.unDerVersionShow = true
      await this.countUnderVersionTerminal(this.temp.version, this.temp.type);
      this.unDerVersionLabel = this.unDerVersionTotal > 0 ? this.$t('pages.newTerminal_text15', {
        total: this.unDerVersionTotal,
        dlpVersion: this.temp.version,
        termType: this.termTypeLabel
      }) : this.$t('pages.newTerminal_text16', { dlpVersion: this.temp.version, termType: this.termTypeLabel })
      callback();
    },
    handleDrag() {
      this.dialogVisible = false
      this.cancelVersion()
    },
    async countUnderVersionTerminal(version, type) {
      let typeCondition = '';
      switch (type) {
        case 0: typeCondition = '0,16'; break;
        case 1: typeCondition = '1,17'; break;
        case 2: typeCondition = '2,18'; break;
        case 3: typeCondition = '3,19'; break;
        default: typeCondition = '0,1,2,3,16,17,18,19';
      }
      await countUnderVersionTerminal({ version: version, type: typeCondition }).then(res => {
        this.unDerVersionTotal = res.data || 0
      })
    },
    updateDlpVersion() {
      this.cancelVersion()
      this.submitting = true;
      updateDlpVersionLimit(this.rowDatas).then(res => {
        this.submitting = false;
        this.dialogVisible = false
      }).catch(() => { this.submitting = false })
    },
    cancel() {
      this.dialogVisible = false
      this.cancelVersion()
    }
  }
}
</script>
