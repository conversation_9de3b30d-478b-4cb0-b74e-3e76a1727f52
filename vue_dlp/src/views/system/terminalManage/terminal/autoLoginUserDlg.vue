<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div slot="title" class="el-dialog__title">
      {{ $t('pages.autoLogin_text1') }}
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          {{ $t('pages.autoLogin_text2') }}<br/>
          {{ $t('pages.autoLogin_text3') }}<br>
          {{ $t('pages.autoLogin_text4') }}<br>
          {{ $t('pages.autoLogin_text5') }}
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <Form ref="loginModeForm" :model="tempUser" label-position="right" label-width="120px" style="width: 700px; margin-left:30px;">
      <!--<el-row>
        <el-col :span="10">
          <FormItem label-width="1px">
            <el-checkbox v-model="isBindUser">
              同时绑定自动登录操作员
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">
                  终端绑定操作员后，只允许绑定的操作员在此终端登录
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-checkbox>
          </FormItem>
        </el-col>
      </el-row>-->

      <grid-table
        ref="toUpdateTermTable"
        :col-model="updateColModel"
        :row-data-api="rowDataApi"
        :multi-select="false"
        :height="350"
        :show-pager="false"
        @selectionChangeEnd="handleSelectionChange"
      />
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateData()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTerminalPage, updateLoginMode } from '@/api/system/terminalManage/terminal'
import { deepMerge } from '@/utils'

export default {
  name: 'AutoLoginUserDlg',
  data() {
    return {
      updateColModel: [
        { prop: 'name', label: 'terminalName', width: '120' },
        { prop: 'id', label: 'terminalCode', width: '120' },
        { prop: 'userId', label: 'autoLoginUser', width: '150', type: 'treeSelect', checkedKeysFieldName: 'checkedKeys', isFilter: true, localSearch: false, nodeChange: this.changeTableRowLoginUser }
      ],
      tempUser: {},
      temp: {}, // 表单字段
      defaultTemp: {
        name: '',
        id: undefined,
        userId: undefined
      },
      query: { // 查询条件
        page: 1,
        limit: 100,
        loginMode: 1,
        userId: 0
      },
      isBindUser: true,
      dialogVisible: false,
      submitting: false,
      updateRowData: []
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.resetTemp()
  },
  activated() {

  },
  methods: {
    handleSelectionChange(val) {
    },
    resetTemp() {
      this.temp = deepMerge({ user: { name: '' }}, this.defaultTemp)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTerminalPage(searchQuery)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleUpdate() {
      this.dialogVisible = true
      this.$nextTick(() => {
        if (this.$refs['toUpdateTermTable']) {
          this.$refs['toUpdateTermTable'].execRowDataApi()
        }
      })
    },
    updateData() {
      const formData = []
      const rowDatas = this.$refs['toUpdateTermTable'].getDatas()
      for (let i = 0; i < rowDatas.length; i++) {
        const rowData = rowDatas[i]
        if (rowData.userId) {
          formData.push({ loginMode: this.query.loginMode, id: rowData.id, userId: rowData.userId })
        }
      }
      updateLoginMode({ terminals: formData }).then(respond => {
        this.dialogVisible = false
        this.$emit('submitEnd')
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.editSuccess'), type: 'success', duration: 2000 })
      })
    },
    changeTableRowLoginUser(nodeData, node, vm, rowData, col) {
      if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
        rowData.checkedKeys = [{ key: nodeData.dataId, label: nodeData.label }]
        rowData.userId = nodeData.dataId
      } else {
        return false
      }
    }
  }
}
</script>
