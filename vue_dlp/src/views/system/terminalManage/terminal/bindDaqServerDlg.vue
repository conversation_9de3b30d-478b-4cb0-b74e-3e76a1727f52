<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.setCollectionServer')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <Form ref="serverForm" label-position="right" label-width="90px" style="width: 700px; margin-left:30px;">
      <el-row>
        <el-col :span="24">
          <FormItem :label="$t('pages.effectiveScope')" label-width="65px" :extra-width="{en:50}">
            <el-radio v-model="effective" :label="1">{{ $t('pages.currentlySelectedTerminal', { len: selectLength} ) }}</el-radio>
            <el-radio v-model="effective" :label="2">{{ $t('pages.currentlyQueriedAllTerminals', { len: allResultLength} ) }}</el-radio>
          </FormItem>
        </el-col>
      </el-row>
      <el-row v-show="effective === 1">
        <el-col :span="isEnglish() ? 24 : 6">
          <FormItem label-width="1px">
            <el-checkbox v-model="multiUpdate">{{ $t('pages.setDaqServer') }}</el-checkbox>
          </FormItem>
        </el-col>
        <el-col :span="11">
          <FormItem v-if="!multiUpdate"></FormItem>
          <FormItem v-if="multiUpdate" :label="$t('route.daqServer')" prop="assignServerId">
            <tree-select
              ref="serverTree"
              :disabled="!deleteable"
              :data="serverTreeSelectData"
              node-key="dataId"
              :checked-keys="[assignServerId]"
              :icon-option="{ 'S': 'terminal', G: 'terminalGroup'}"
              :width="296"
              :placeholder="$t('pages.selectDaqServer')"
              :rewrite-node-click-fuc="true"
              :node-click-fuc="assignServerIdSelectChange"
            />
          </FormItem>
        </el-col>
        <el-col :span="isEnglish() ? 13 : 7">
          <el-button size="mini" style="float: right;" :disabled="!deleteable" @click="handleDelete">
            {{ $t('pages.batchDelAssignServerId') }}
          </el-button>
        </el-col>
      </el-row>
      <el-row v-show="effective === 2">
        <el-col :span="isEnglish() ? 24 : 6">
          <FormItem label-width="1px">
            <el-checkbox v-model="allMultiUpdate">{{ $t('pages.setDaqServer') }}</el-checkbox>
          </FormItem>
        </el-col>
        <el-col :span="11">
          <FormItem v-if="!allMultiUpdate"></FormItem>
          <FormItem v-if="allMultiUpdate" :label="$t('route.daqServer')" prop="assignServerId">
            <tree-select
              ref="serverTree"
              :disabled="!allDeleteable"
              :data="serverTreeSelectData"
              node-key="dataId"
              :checked-keys="[allAssignServerId]"
              :icon-option="{ 'S': 'terminal', G: 'terminalGroup'}"
              :width="296"
              :placeholder="$t('pages.selectDaqServer')"
              :rewrite-node-click-fuc="true"
              :node-click-fuc="assignServerIdSelectChange"
            />
          </FormItem>
        </el-col>
        <el-col :span="isEnglish() ? 13 : 7">
          <el-button size="mini" style="float: right;" :disabled="!allDeleteable" @click="allHandleDelete">
            {{ $t('pages.batchDelAssignServerId') }}
          </el-button>
        </el-col>
      </el-row>
      <el-row v-show="effective === 2">
        <el-col :span="isEnglish() ? 13 : 6">
          <FormItem label-width="1px">
            <el-checkbox v-model="checkAll" @change="changeSelected">{{ $t('pages.checkAllPagesData') }}</el-checkbox>
          </FormItem>
        </el-col>
      </el-row>
      <grid-table
        v-show="effective === 1"
        ref="toUpdateTermTable"
        :col-model="terminalColModel"
        :row-datas="terminalRowData"
        :multi-select="true"
        :height="350"
        :show-pager="false"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <grid-table
        v-show="effective === 2"
        ref="allTermTable"
        :col-model="terminalColModel"
        :row-data-api="rowDataApi"
        :multi-select="true"
        :height="350"
        :show-pager="true"
        :after-load="afterLoad"
        :autoload="false"
        @selectionChangeEnd="allSelectionChangeEnd"
        @select="selectData"
        @select-all="selectAll"
      />
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="effective === 1 ?saveDAQServerConfig() : allSaveDAQServerConfig()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTerminalPage, updateAssignServer, updateAllAssignServer } from '@/api/system/terminalManage/terminal'
import { getServerTree, getRemainConnectionNum } from '@/api/system/deviceManage/daqServer'

export default {
  name: 'BindDaqServerDlg',
  data() {
    return {
      terminalColModel: [
        { prop: 'name', label: 'terminalName', width: '150', sort: true, fixed: true },
        { prop: 'id', label: 'terminalCode', width: '150', sort: true },
        { prop: 'serverId', label: 'curServer', width: '150', sort: true, formatter: this.serverFormatter },
        {
          prop: 'assignServerId',
          label: 'assignServer',
          width: '150',
          sort: true,
          type: 'treeSelect',
          iconOption: { 'S': 'terminal', G: 'terminalGroup' },
          checkedKeysFieldName: 'checkedKeys',
          isFilter: false,
          localSearch: false,
          nodeChange: this.changeRowDataDAQServer
        }
        // { prop: '', fixedWidth: 40, type: 'icon', iconClass: 'delete', iconFormatter: () => { return true }, style: 'cursor: pointer;', click: this.removeTerminal }
      ],
      submitting: false,
      dialogVisible: false,
      // 设置批量采集服务器
      multiUpdate: false,
      // 当前查询所有终端的 批量设置采集服务器
      allMultiUpdate: false,
      // 从终端信息传过来的行
      terminalRowData: [],
      serverTreeSelectData: [],
      serverTreeMap: {},
      assignServerId: undefined,
      allAssignServerId: 0,
      // 是否允许批量删除
      deleteable: false,
      // 当前查询所有终端的 是否允许批量删除
      allDeleteable: false,
      effective: 1, // 生效范围 1-当前选中终端 2-当前查询的所有终端
      selectLength: 0, // 当前选中终端的条数
      allResult: [],
      allResultLength: 0, // 当前查询的所有终端的条数
      query: {},  // 查询参数
      checkAll: true, // 选中所有的行，默认是选中的
      submitAll: false, // 是否提交所有数据  在全选的情况下批量修改采集服务器，或者批量删除采集服务器，这时候需要提交全部数据
      updateAll: false, // 判断是否全局修改了采集服务器
      backupSelectedIds: [], // 备份已选中的行的id   当未全选所有页面的数据时备份已勾选的行
      backupSelectedDatas: [],
      backupUnSelectedIds: [], // 备份未选中的行的id  当全选了所有页面的数据时备份未勾选的行
      doubleBackupUnSelectedIds: [], // 全选情况下，批量修改采集服务器时备份，未选中的id 目的是未了格式化当前查询页面时，有包含备份id的数据不需要格式化(使用这个的目的是避免有些勾选是操作完批量修改采集服务器才勾选上的)
      editRowDatas: [], // 手动修改的行的数据
      editRowIds: [], // 手动修改行的id
      checkedKeys: [], // 批量选中的采集服务器
      promise: {} // 用来接收返回的查询到的终端列表
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  activated() {

  },
  methods: {
    serverTree() {
      return this.$refs['serverTree']
    },
    selectionChangeEnd(rows) {
      this.deleteable = rows.length > 0
    },
    allSelectionChangeEnd(rows) {
      this.$nextTick(() => {
        if (this.checkAll) {
          this.allDeleteable = this.backupUnSelectedIds.length != this.allResultLength
        } else {
          this.allDeleteable = this.backupSelectedIds.length > 0
        }
      })
    },
    handleDelete() {
      const selectedTerminalIds = this.toUpdateTermTable().getSelectedIds()
      const checkedKey = { key: 0, label: '' }
      this.terminalRowData.forEach(rowData => {
        if (selectedTerminalIds.indexOf(rowData.id) >= 0) {
          rowData.checkedKeys = [checkedKey]
          rowData.bindAssignServerId = 0
        }
      })
    },
    allHandleDelete() {
      const checkedKey = { key: 0, label: '' }
      // 用于处理采集服务器的选中状态
      this.cacheDaqState(checkedKey)
      this.$nextTick(() => {
        this.allTermTable().getSelectedDatas().forEach(item => {
          item.checkedKeys = [checkedKey]
          item.bindAssignServerId = 0
        })
      })
    },
    // 批量设置采集服务器
    assignServerIdSelectChange(data, node, vm) {
      return new Promise((resolve, reject) => {
        if (data.id.indexOf('G' + data.dataId) < 0) {
          const res = getRemainConnectionNum(data.dataId)
          const remainConnectionNum = res.data
          if (this.effective === 1) {
            const selectedData = this.toUpdateTermTable().getSelectedDatas()
            if (remainConnectionNum == null || remainConnectionNum >= selectedData.length) {
              const checkedKey = { key: data.dataId, label: data.label }
              selectedData.forEach(rowData => {
                rowData.checkedKeys = [checkedKey]
                rowData.bindAssignServerId = data.dataId
              })
              resolve()
            } else {
              this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.daq_text4'), type: 'error', duration: 2000 })
              resolve(false)
            }
          } else {
            const selectedLenght = this.checkAll ? (this.allResultLength - this.backupUnSelectedIds.length) : this.backupSelectedIds.length
            if (remainConnectionNum == null || remainConnectionNum >= selectedLenght) {
              const checkedKey = { key: data.dataId, label: data.label }
              // 用于处理采集服务器的选中状态
              this.cacheDaqState(checkedKey)
              this.$nextTick(() => {
                this.allTermTable().getSelectedDatas().forEach(item => {
                  item.checkedKeys = [checkedKey]
                  item.bindAssignServerId = data.dataId
                })
                resolve()
              })
            } else {
              this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.daq_text4'), type: 'error', duration: 2000 })
              resolve(false)
            }
          }
        } else {
          resolve(false)
        }
      })
    },
    toUpdateTermTable() {
      return this.$refs['toUpdateTermTable']
    },
    allTermTable() {
      return this.$refs['allTermTable']
    },
    changeRowDataDAQServer(nodeData, node, vm, rowData, col) {
      return new Promise((resolve, reject) => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          let bindRowNum = 1
          this.terminalRowData.forEach(rowData => {
            if (rowData.bindAssignServerId === nodeData.dataId) {
              bindRowNum++
            }
          })
          const res = getRemainConnectionNum(nodeData.dataId)
          if (res.data == null || res.data >= bindRowNum) {
            rowData.checkedKeys = [{ key: nodeData.dataId, label: nodeData.label }]
            this.multiUpdate = false
            rowData.bindAssignServerId = nodeData.dataId
            // 保存到修改的行
            if (this.editRowIds.indexOf(rowData['id']) >= 0) {
              this.editRowIds.splice(this.editRowIds.indexOf(rowData['id']), 1, rowData['id'])
              this.editRowDatas.splice(this.editRowIds.indexOf(rowData['id']), 1, rowData)
            } else {
              this.editRowIds.push(rowData['id'])
              this.editRowDatas.push(rowData)
            }
            // 修改已勾选的数据
            if (this.backupSelectedIds.indexOf(rowData['id']) >= 0) {
              this.backupSelectedIds.splice(this.backupSelectedIds.indexOf(rowData['id']), 1, rowData['id'])
              this.backupSelectedDatas.splice(this.backupSelectedIds.indexOf(rowData['id']), 1, rowData)
            }
            resolve()
          } else {
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.daq_text4'), type: 'error', duration: 2000 })
            resolve(false)
          }
        } else {
          resolve(false)
        }
      })
    },
    removeTerminal(e, row, rowIndex) {
      const index = this.terminalRowData.indexOf(row)
      this.$confirmBox(this.$t('pages.confirmDeleteTerm') + row.computerName + '？', this.$t('text.prompt')).then(() => {
        this.terminalRowData.splice(index, 1)
      }).catch(() => {

      })
    },
    getServerTree() {
      this.serverTreeSelectData.splice(0)
      getServerTree().then(respond => {
        this.serverTreeSelectData = respond.data
        this.terminalColModel[3].treeData = respond.data
        this.resetServerTreeMap(this.serverTreeSelectData)
      })
    },
    resetServerTreeMap(treeData) {
      if (treeData) {
        treeData.forEach(item => {
          if (item.id.indexOf('G') > -1) {
            this.resetServerTreeMap(item.children)
          } else {
            this.serverTreeMap[item.dataId] = item.label
          }
        })
      }
    },
    resetTemp() {
      this.multiUpdate = false
      this.allMultiUpdate = false
      this.terminalRowData = []
      this.assignServerId = 0
      this.allAssignServerId = 0
      this.checkAll = true
      this.submitAll = false
      this.updateAll = false
      this.backupSelectedIds.splice(0)
      this.backupSelectedDatas.splice(0)
      this.backupUnSelectedIds.splice(0)
      this.doubleBackupUnSelectedIds.splice(0)
      this.checkedKeys.splice(0)
      this.editRowIds.splice(0)
      this.editRowDatas.splice(0)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleUpdate(rowDatas, allLength, query) {
      this.getServerTree()
      this.query = Object.assign({}, query)
      this.query.types = '0,16,1,17,2,18' // 查询时过滤移动端, u盘终端、永久离线终端
      //  过滤掉批量选中的数据中的过滤移动端, u盘终端、永久离线终端
      const types = this.query.types.split(',') || []
      rowDatas = rowDatas.filter(item => {
        return types.includes(item.type + '')
      });
      //  过滤选中终端
      this.dialogVisible = true
      this.resetTemp()
      this.effective = 1
      this.selectLength = rowDatas.length
      if (this.selectLength === 0) {
        this.effective = 2
      }
      if (this.selectLength > 0) {
        rowDatas.forEach(rowData => {
          if (rowData.assignServerId) {
            const serverName = this.serverTreeMap[rowData.assignServerId]
            rowData.bindAssignServerId = rowData.assignServerId
            rowData.checkedKeys = [{ key: rowData.assignServerId, label: serverName }]
          } else {
            rowData.checkedKeys = [{ key: 0, label: '' }]
          }
          this.terminalRowData[rowData.id] = rowData
        })
        // 去除数组的空值部分。添加这一步操作主要为了根据id进行排序
        this.terminalRowData = this.terminalRowData.filter(rowData => { return rowData })
      }
      this.$nextTick(() => {
        this.toUpdateTermTable().toggleAllSelection()
        this.allTermTable().execRowDataApi(this.query)
      })
    },
    saveDAQServerConfig() {
      this.submitting = true
      // 如果没有勾选任何终端，提示消息
      if (this.toUpdateTermTable().getSelectedIds().length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.currentNoSelectedAnyTerminal'),
          type: 'warning',
          duration: 2000
        })
        this.submitting = false
        return
      }
      const formData = []
      for (let i = 0; i < this.toUpdateTermTable().getSelectedDatas().length; i++) {
        const rowData = this.toUpdateTermTable().getSelectedDatas()[i]
        formData.push({ id: rowData.id, name: rowData.name, assignServerId: rowData.bindAssignServerId, assignServerName: rowData.checkedKeys[0].label })
      }
      updateAssignServer({ terminals: formData }).then(respond => {
        this.submitting = false
        this.dialogVisible = false
        this.$emit('submitEnd')
        this.$notify({ title: this.$t('text.success'), message: this.$t('pages.daq_text3'), type: 'success', duration: 2000 })
      }).catch(() => {
        this.submitting = false
      })
    },

    allSaveDAQServerConfig() {
      this.submitting = true
      if (this.checkAll) {
      // 如果没有勾选任何终端，提示消息
        if (this.backupUnSelectedIds.length === this.allResultLength) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
        if (this.updateAll) { // 当勾选了所有页面的数据，且批量操作过数据
          this.query.page = 1
          this.query.limit = this.allResultLength
          const formData = []
          this.editRowDatas.forEach(
            rowData => {
              if (this.backupUnSelectedIds.indexOf(rowData['id'] == -1)) {
                formData.push({ id: rowData.id, name: rowData.name, assignServerId: rowData.bindAssignServerId, assignServerName: rowData.checkedKeys[0].label })
              }
            }
          )
          updateAllAssignServer({ query: this.query, backupUnSelectedIds: this.backupUnSelectedIds, terminals: formData, checkedKeys: this.checkedKeys }).then(respond => {
            this.dialogVisible = false
            this.submitting = false
            this.$emit('submitEnd')
            this.$notify({ title: this.$t('text.success'), message: this.$t('pages.daq_text3'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else { // 当勾选了所有页面的数据，且未批量操作过数据，只需要提交修改过的且选中的数据就可以了
          const formData = []
          this.editRowDatas.forEach(
            rowData => {
              if (this.backupUnSelectedIds.indexOf(rowData['id'] == -1)) {
                formData.push({ id: rowData.id, name: rowData.name, assignServerId: rowData.bindAssignServerId, assignServerName: rowData.checkedKeys[0].label })
              }
            }
          )
          updateAssignServer({ terminals: formData }).then(respond => {
            this.dialogVisible = false
            this.submitting = false
            this.$emit('submitEnd')
            this.$notify({ title: this.$t('text.success'), message: this.$t('pages.daq_text3'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        }
      } else { // 当未勾选全部页面的数据时，只需要提交选中的数据即可
      // 如果没有勾选任何终端，提示消息
        if (this.backupSelectedIds.length === 0) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
        const formData = []
        this.backupSelectedDatas.forEach(
          rowData => {
            if (this.backupUnSelectedIds.indexOf(rowData['id'] == -1)) {
              formData.push({ id: rowData.id, name: rowData.name, assignServerId: rowData.bindAssignServerId, assignServerName: rowData.checkedKeys[0].label })
            }
          }
        )
        updateAssignServer({ terminals: formData }).then(respond => {
          this.dialogVisible = false
          this.submitting = false
          this.$emit('submitEnd')
          this.$notify({ title: this.$t('text.success'), message: this.$t('pages.daq_text3'), type: 'success', duration: 2000 })
        }).catch(() => {
          this.submitting = false
        })
      }
    },
    serverFormatter: function(row, data) {
      const node = this.serverTreeMap[data]
      return node ? this.serverTreeMap[data] : this.$t('pages.notSpecified')
    },
    selectAll(selection) {
      this.$nextTick(() => {
        // 选中全部的情况下，缓存取消勾选的数据，未选中的情况下，缓存已勾选的数据
        if (this.checkAll) {
          // 当前页面选中数据的长度为0，证明是取消全选
          if (this.allTermTable().getSelectedIds().length === 0) {
            this.allTermTable().rowData.forEach(
              r => {
                if (this.backupUnSelectedIds.indexOf(r['id']) === -1) {
                  this.backupUnSelectedIds.push(r['id'])
                }
              }
            )
          } else { // 当前页面全选情况下
            this.allTermTable().rowData.forEach(
              r => {
                if (this.backupUnSelectedIds.indexOf(r['id']) >= 0) {
                  this.backupUnSelectedIds.splice(this.backupUnSelectedIds.indexOf(r['id']), 1)
                }
              }
            )
          }
        } else {
          // 当前页面选中数据的长度为0，证明是取消全选
          if (this.allTermTable().getSelectedIds().length === 0) {
            this.allTermTable().rowData.forEach(
              r => {
                if (this.backupSelectedIds.indexOf(r['id']) >= 0) {
                  this.backupSelectedIds.splice(this.backupSelectedIds.indexOf(r['id']), 1)
                  this.backupSelectedDatas.splice(this.backupSelectedIds.indexOf(r['id']), 1)
                }
              }
            )
          } else { // 当前页面全选情况下
            this.allTermTable().rowData.forEach(
              r => {
                if (this.backupSelectedIds.indexOf(r['id']) === -1) {
                  this.backupSelectedIds.push(r['id'])
                  this.backupSelectedDatas.push(r)
                }
              }
            )
          }
        }
      })
    },
    selectData(selection, row) {
      this.$nextTick(() => {
        // 选中全部的情况下，缓存取消勾选的数据，未选中的情况下，缓存已勾选的数据
        if (this.checkAll) {
          if (this.allTermTable().getSelectedIds().indexOf(row['id']) == -1) {
            this.backupUnSelectedIds.push(row['id'])
          } else {
            if (this.backupUnSelectedIds.indexOf(row['id']) >= 0) {
              this.backupUnSelectedIds.splice(this.backupUnSelectedIds.indexOf(row['id']), 1)
            }
          }
        } else {
          if (this.allTermTable().getSelectedIds().indexOf(row['id']) >= 0) {
            this.backupSelectedIds.push(row['id'])
            this.backupSelectedDatas.push(row)
          } else {
            if (this.backupSelectedIds.indexOf(row['id']) >= 0) {
              this.backupSelectedIds.splice(this.backupSelectedIds.indexOf(row['id']), 1)
              this.backupSelectedDatas.splice(this.backupSelectedIds.indexOf(row['id']), 1)
            }
          }
        }
      })
    },
    /**
     * 表格刷新后调用的方法
     * 表格刷新后，判断<复选框>的选中状态，对表格的全选和全不选状态进行处理
     * */
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        // 选中当前页面所有的终端,处理未选中的数据
        if (rowData.length > 0 && this.checkAll) {
          rowData.forEach(r => {
            if (this.backupUnSelectedIds.indexOf(r['id']) == -1) {
              this.allTermTable().toggleRowSelection(r, true)
            }
          })
          // this.allTermTable().toggleAllSelection()
        } else { // 未选中当前页面所有的终端,处理已选中的数据
          rowData.forEach(r => {
            if (this.backupSelectedIds.indexOf(r['id']) >= 0) {
              this.allTermTable().toggleRowSelection(r, true)
            }
          })
        }
        if (this.updateAll) {
          rowData.forEach(r => {
            if (this.doubleBackupUnSelectedIds.indexOf(r['id']) === -1) {
              // r.checkedKeys = this.checkedKeys
              r.checkedKeys = JSON.parse(JSON.stringify(this.checkedKeys))
            }
          })
        }
        rowData.forEach(r => {
          // 格式化当前缓存的采集服务器的选中状态
          if (this.editRowIds.indexOf(r['id']) >= 0) {
            r.checkedKeys = this.editRowDatas[this.editRowIds.indexOf(r['id'])].checkedKeys
          }
        })
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.types = '0,16,1,17,2,18' // 查询时过滤移动端  和 永久离线终端
      searchQuery.filterUseType = true // 过滤已清理终端
      this.query.types = searchQuery.types
      this.promise = getTerminalPage(searchQuery)
      this.promise.then(res => {
        res.data.items.forEach(rowData => {
          if (rowData.assignServerId) {
            const serverName = this.serverTreeMap[rowData.assignServerId]
            rowData.bindAssignServerId = rowData.assignServerId
            rowData.checkedKeys = [{ key: rowData.assignServerId, label: serverName }]
          } else {
            rowData.checkedKeys = [{ key: 0, label: '' }]
            rowData.bindAssignServerId = 0
          }
        })
        this.allResultLength = res.data.total
      })
      return this.promise
    },
    // 改变是否选中所有页面的数据，则重新缓存页面数据
    changeSelected() {
      this.backupSelectedIds.splice(0) // 备份已选中的行的id
      this.backupSelectedDatas.splice(0)
      this.backupUnSelectedIds.splice(0) // 备份未选中的行的id
      this.doubleBackupUnSelectedIds.splice(0)
      if (this.checkAll) {
        this.allTermTable().toggleAllSelection()
      } else {
        if (this.allTermTable().getSelectedDatas().length > 0) {
          this.allTermTable().clearSelection()
        }
        this.submitAll = false
      }
    },
    // currentChange(currentRow, oldCurrentRow) {
    //   console.log('currentrow.............', currentRow)
    // },
    // 用于处理采集的选中状态
    cacheDaqState(checkedKey) {
      if (this.checkAll) {
        this.checkedKeys.splice(0)
        this.checkedKeys.push(checkedKey)
        this.submitAll = true
        this.updateAll = true
        this.doubleBackupUnSelectedIds.splice(0)
        // 防止修改新数组里面的数据时，会影响到旧数组的数据，所以必须加JSON.parse(JSON.stringify
        JSON.parse(JSON.stringify(this.backupUnSelectedIds)).forEach(item => {
          // 备份当前未选择的终端
          this.doubleBackupUnSelectedIds.push(item)
        })
        this.editRowIds = this.editRowIds.filter(item => this.doubleBackupUnSelectedIds.indexOf(item) >= 0
        )
        this.editRowDatas = this.editRowDatas.filter(item =>
          this.doubleBackupUnSelectedIds.indexOf(item['id']) >= 0)
      } else {
        this.backupSelectedDatas.forEach(
          item => {
            item.checkedKeys = [checkedKey]
            item.bindAssignServerId = checkedKey.key
          }
        )
        for (let i = 0; i < this.backupSelectedIds.length; i++) {
          if (this.editRowIds.indexOf(this.backupSelectedIds[i]) >= 0) {
            this.editRowDatas[this.editRowIds.indexOf(this.backupSelectedIds[i])].checkedKeys = [checkedKey]
          } else {
            this.editRowIds.push(this.backupSelectedIds[i])
            this.editRowDatas.push(Object.assign({}, this.backupSelectedDatas[i]))
          }
        }
      }
    }
  }
}
</script>
