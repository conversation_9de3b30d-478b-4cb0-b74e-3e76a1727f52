<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.nameSpecifications')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogGroupNickNameConfigVisible"
      width="1000px"
    >
      <div class="">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane :label="$t('pages.grpNickName_tab1')" name="first">{{ $t('pages.grpNickName_tab_tip1') }}</el-tab-pane>
          <el-tab-pane :label="$t('pages.grpNickName_tab2')" name="second">{{ $t('pages.grpNickName_tab_tip2') }}</el-tab-pane>
          <el-tab-pane :label="$t('pages.grpNickName_tab3')" name="third">{{ $t('pages.grpNickName_tab_tip3') }}</el-tab-pane>
          <el-tab-pane :label="$t('pages.grpNickName_tab4')" name="fourth">{{ $t('pages.grpNickName_tab_tip4') }}</el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <div class="toolbar" >
          <div class="searchCon" style="width: 500px;">
            <Form v-if="deptRuleCondition">
              <FormItem :label="$t('pages.terminal_text19')">
                <el-tooltip class="item" effect="dark" placement="bottom-end">
                  <div slot="content">
                    {{ $t('pages.terminal_text19') }}<br/>
                    {{ $t('pages.terminal_text21') }}：{{ $t('pages.grpNickNameDlgMatchRulesMsg1') }}<br/>
                    {{ $t('pages.terminal_text22') }}：{{ $t('pages.grpNickNameDlgMatchRulesMsg2') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-select v-model="requestVO.deptRule" clearable style="width: 200px;" @change="changeDeptRule($event)">
                  <el-option :value="0" :label="$t('pages.terminal_text20')"/>
                  <el-option :value="1" :label="$t('pages.terminal_text21')"/>
                  <el-option :value="2" :label="$t('pages.terminal_text22')"/>
                </el-select>
              </FormItem>
            </Form>
          </div>
        </div>
        <grid-table
          ref="grpNickNameTable"
          :col-model="firstGrpNickNameColModel"
          :row-data-api="rowDataApi"
          :after-load="afterLoad"
          :multi-select="true"
          :height="310"
          :show-pager="true"
          @selectionChangeEnd="grpNickNameTableHandleSelectionChange"
        />
        <Form v-if="adjustTerminalGroupingBtn">
          <FormItem >
            <label class="el-form-item__label" style="margin-right: 10px">{{ $t('pages.synchronousOption') }}</label>
            <el-checkbox v-model="defaultTemp.synchronizationTerminalChk">{{ $t('pages.synchronousTermGroup') }}</el-checkbox>
            <el-checkbox v-model="defaultTemp.synchronizationNicNameChk">{{ $t('pages.synchronousTermName') }}</el-checkbox>
            <el-checkbox v-model="requestVO.isSynAllTerminalStatus" @change="allSelectTerminal">{{ $t('pages.terminal_text23') }}</el-checkbox>
          </FormItem>
        </Form>
        <!-- 所有在线终端开始收集-->
        <Form v-if="collectTerminalInformationBtn">
          <FormItem label-width="3px">
            <label class="el-form-item__label" style="margin-right: 10px">{{ $t('pages.terminal_text24') }}</label>
            <el-checkbox v-model="requestVO.isAddAllTerminalStatus" @change="allSelectTerminal">{{ $t('pages.terminal_text25') }}</el-checkbox>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="collectTerminalInformationBtn" :loading="submitting" type="primary" @click="addTerminalHandle">
          {{ $t('pages.addTerminal') }}
        </el-button>
        <el-button v-if="collectTerminalInformationBtn" :loading="submitting" type="primary" @click="deleteTerminalHandle">
          {{ $t('pages.deleteTerminal') }}
        </el-button>
        <el-button v-if="collectTerminalInformationBtn" :loading="submitting" type="primary" @click="emptyTerminalHandle">
          {{ $t('pages.clearTerminal') }}
        </el-button>
        <el-button v-if="collectTerminalInformationBtn" :loading="submitting" type="primary" @click="startCollectionHandle">
          {{ $t('pages.startCollect') }}
        </el-button>
        <el-button v-if="waitingTerminalSubmissionBtn" :loading="submitting" type="primary" @click="reCollectionHandle">
          {{ $t('pages.reCollection') }}
        </el-button>
        <el-button v-if="adjustTerminalGroupingBtn" :loading="submitting" type="primary" @click="startSynchronizationHandle">
          {{ $t('pages.startSync') }}
        </el-button>
        <el-button v-if="commonRefreshBtn" :loading="submitting" type="primary" @click="refreshHandle">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button @click="dialogGroupNickNameConfigVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
    <!--添加终端-->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.terminal_text26')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="selectGroup"
      width="900px"
    >
      <div class="divAddTerminal">
        <div class="tree-container">
          <tree-menu
            ref="groupTree"
            :data="treeData"
            multiple
            is-filter
            :local-search="false"
            :get-search-list="getSearchListFunction"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
            @check-change="checkChangeAddDept"
          />
        </div>
        <div class="table-container">
          <div class="">
            <grid-table
              ref="addTerminalTable"
              :col-model="addColModel"
              :row-data-api="rowDataApi1"
              :after-load="afterLoad"
              :multi-select="multiSelect"
              :height="393"
              :show-pager="true"
              @selectionChangeEnd="handleSelectionChange"
            />
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="selectGroupBtn">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="selectGroup = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!--调整终端分组和名称中表格的修改弹框-->
    <el-dialog
      v-el-drag-dialog
      :title="$t('text.edit')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="updateTabList"
      width="300px"
    >
      <div>
        <Form>
          <FormItem :label="$t('pages.syncToNewGroup')" class="required" prop="typeId">
            <tree-select
              :data="groupTreeSelectData"
              node-key="dataId"
              :height="350"
              :width="275"
              :checked-keys="groupDefault"
              is-filter
              @change="groupIdSelectChange"
            />
          </FormItem>
          <FormItem :label="$t('pages.terminalName')" class="required">
            <el-input v-model="requestVO.newNickName" v-trim :placeholder="$t('pages.terminalName')"></el-input>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateGroupNickNameBtn">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="updateTabList = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department';
import { getTerminalGroupNickNamePage, addTerminalGroupNickName, deleteTerminalGroupNickName, deleteAllTerminalGroupNickName,
  startCollectTerminal, restartCollectTerminal, updateGroupNickName, synGroupNickName, isExistOffline } from '@/api/system/terminalManage/terminalGroupNickName'
import { getTerminalPage } from '@/api/system/terminalManage/terminal';
// import { deleteLog } from '@/api/assets/assetsConfig/assetLog';
export default {
  name: 'GrpNickNameDlg',
  directives: { },
  props: {
    selectTabData: { // 父组件向子组件传值，传递父页面表格选中值，用来实现如果页面有勾选，弹框出现的时候直接新增终端
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      // todo 注：query为添加终端中表格的查询条件，npm run lintquery1为终端分组名称四个表格的查询条件，不能写在一起，否则相互之间会有影响
      query: { // 查询条件--添加终端
        page: 1,
        groupIds: '',
        status: 4, // 在线终端状态 4
        types: '0,16'// 终端类型限制windows 和 老板windows '0,16'
      },
      query1: { // 查询条件--终端分组名称规范
        page: 1,
        status: 1, // 默认显示收集终端信息列表 采集状态 0 - 不采集 1-待采集但尚未发出采集协议 2-已发出协议采集中 3-已上报 4-已完成
        deptRule: undefined
      },
      requestVO: { // 操作功能参数 例如：添加终端、开始收集、重新收集、开始同步
        termList: [],
        groupList: [],
        termId: undefined,
        groupId: undefined,
        synGrp: undefined,
        synNickName: undefined,
        status: undefined,
        newGroupId: undefined,
        newGroupName: undefined,
        newNickName: undefined,
        deptRule: undefined,
        isAddAllTerminalStatus: false, // 所有添加的在线终端复选框
        isSynAllTerminalStatus: false // 同步所有匹配的分组
      },
      dialogGroupNickNameConfigVisible: false, // 弹框显示
      selectGroup: false, // 添加终端弹框
      isAllAddTerminalChk: false, // 所有添加的在线终端复选框
      updateTabList: false, // 调整终端分组和名称表格修改
      collectTerminalInformationBtn: true, // 收集终端信息按钮
      waitingTerminalSubmissionBtn: false, // 等待终端提交按钮
      commonRefreshBtn: false, // 通用刷新按钮
      adjustTerminalGroupingBtn: false, // 调整终端分组和名称按钮
      deptRuleCondition: false, // 部门规则条件是否显示
      activeName: 'first',
      firstGrpNickNameColModel: [// 默认显示收集终端信息列表
        { prop: 'isActive', label: 'online', fixedWidth: '60', type: 'icon', iconClass: 'active', fixed: true, iconFormatter: this.isActive },
        { prop: 'termId', label: 'terminalCode', width: '150' },
        { prop: 'oldGroupName', label: 'subordinateGroup', width: '150' },
        { prop: 'oldNickName', label: 'terminalName', width: '150' },
        { prop: 'status', label: 'status', width: '150', formatter: this.statusFormatter }
      ],
      grpNickNameListTotal: undefined, // 收集终端信息列表记录总数
      submitting: false,
      multiUpdate: true,
      temp: {},
      defaultTemp: { // 表单字段
        synchronizationTerminalChk: true,
        synchronizationNicNameChk: true,
        isAllSynTerminalChk: false
      },
      addColModel: [
        { prop: 'isActive', label: 'online', fixedWidth: '60', type: 'icon', iconClass: 'active', iconFormatter: this.terminalIsActive },
        { prop: 'name', label: 'terminalName', width: '150', sort: 'custom' },
        { prop: 'computerName', label: 'computerName', width: '150', sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' }
      ],
      multiSelect: true,
      selectedData: [], // 添加终端表格选中的数据
      remoteable: false,
      treeData: [],
      defaultExpandedKeys: ['G0'],
      groupTreeSelectData: [],
      nodeCheck: false,
      groupIdsCheck: 0,
      grpNickNameTableSelectedData: [], // 分组名称列表选中的数据
      groupDefault: [] // 修改功能分组默认值
    }
  },
  computed: {
    gridTable() {
      return this.$refs['addTerminalTable']
    },
    groupTree() {
      return this.$refs['groupTree']
    }
  },
  watch: {
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    }
  },
  created() {
    // 添加终端弹框 树和表格
    this.initGroupTreeNode()
  },
  activated() {
  },
  methods: {
    // 打开终端分组和名称规范
    handleUpdate() {
      this.dialogGroupNickNameConfigVisible = true
      if (this.selectTabData.length > 0) {
        // 说明打开弹框时，列表有有、勾选，，，若有勾选，直接添加终端
        this.selectTabData.forEach(item => {
          this.requestVO.termList.push(item.id)
        })
        const vo = Object.assign({}, this.requestVO)
        const that = this
        addTerminalGroupNickName(vo).then(respond => {
          this.selectGroup = false
          that.refreshHandle()
        })
      }
    },
    // 收集终端信息列表状态
    statusFormatter(row, data) {
      const status = {
        0: this.$t('pages.statusOpt1'),
        1: this.$t('pages.statusOpt2'),
        2: this.$t('pages.statusOpt3'),
        3: this.$t('pages.statusOpt4'),
        4: this.$t('pages.statusOpt5')
      }
      return status[row.status]
    },
    // 终端在线状态 登录状态：0未登录，1终端登录，3终端操作员均已登录
    terminalLoginStatusFormatter(row, data) {
      if (row.terminalLoginStatus === 0) {
        return this.$t('pages.notLogin')
      }
      if (row.terminalLoginStatus === 1) {
        return this.$t('pages.login')
      }
      if (row.terminalLoginStatus === 3) {
        return this.$t('pages.login')
      }
    },
    isActive(row) {
      return row.terminalLoginStatus != 0
    },
    terminalIsActive(row) {
      return row.loginStatus && row.loginStatus.status != 0
    },
    // tabs组件点击切换
    handleClick(tab, event) {
      this.firstGrpNickNameColModel = []
      if (tab.name == 'first') {
        this.query1.status = 1
        this.firstGrpNickNameColModel = [
          { prop: 'isActive', label: 'online', fixedWidth: '60', type: 'icon', iconClass: 'active', iconFormatter: this.isActive },
          { prop: 'termId', label: 'terminalCode', width: '150' },
          { prop: 'oldGroupName', label: 'subordinateGroup', width: '150' },
          { prop: 'oldNickName', label: 'terminalName', width: '150' },
          { prop: 'status', label: 'status', width: '150', formatter: this.statusFormatter }
        ]
        this.$refs['grpNickNameTable'].execRowDataApi()
        this.collectTerminalInformationBtn = true  // 收集终端信息按钮
        this.waitingTerminalSubmissionBtn = false  // 等待终端提交按钮
        this.commonRefreshBtn = false // 通用刷新按钮
        this.adjustTerminalGroupingBtn = false // 调整终端分组和名称按钮
        this.deptRuleCondition = false // 部门规则条件是否显示
      }
      if (tab.name == 'second') {
        this.query1.status = 2
        this.firstGrpNickNameColModel = [
          { prop: 'isActive', label: 'online', fixedWidth: '60', type: 'icon', iconClass: 'active', iconFormatter: this.isActive },
          { prop: 'termId', label: 'terminalCode', width: '150' },
          { prop: 'oldGroupName', label: 'subordinateGroup', width: '150' },
          { prop: 'oldNickName', label: 'terminalName', width: '150' },
          { prop: 'createTime', label: 'collectionTime', width: '150' }
        ]
        this.$refs['grpNickNameTable'].execRowDataApi()
        this.collectTerminalInformationBtn = false  // 收集终端信息按钮
        this.waitingTerminalSubmissionBtn = true  // 等待终端提交按钮
        this.commonRefreshBtn = true // 通用刷新按钮
        this.adjustTerminalGroupingBtn = false // 调整终端分组和名称按钮
        this.deptRuleCondition = false // 部门规则条件是否显示
      }
      if (tab.name == 'third') {
        this.requestVO.deptRule = 0
        this.query1.status = 3
        this.firstGrpNickNameColModel = [
          { prop: 'isActive', label: 'online', fixedWidth: '60', type: 'icon', iconClass: 'active', fixed: true, iconFormatter: this.isActive },
          { prop: 'termId', label: 'terminalCode', width: '150' },
          { prop: 'oldGroupName', label: 'subordinateGroup', width: '150' },
          { prop: 'groupName', label: 'collectionDept', width: '150' },
          { prop: 'newGroupName', label: 'syncDept', width: '150' },
          { prop: 'oldNickName', label: 'terminalName', width: '150' },
          { prop: 'nickName', label: 'collectionName', width: '150' },
          { prop: 'newNickName', label: 'correctName', width: '150' },
          { prop: 'createTime', label: 'infoSubmissionTime', width: '150' },
          { prop: '', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '130',
            buttons: [
              { label: 'edit', click: this.handleUpdateGrpNickName }
            ]
          }
        ]
        const vo = Object.assign({}, this.requestVO)
        this.$refs['grpNickNameTable'].execRowDataApi(vo)
        this.collectTerminalInformationBtn = false  // 收集终端信息按钮
        this.waitingTerminalSubmissionBtn = true  // 等待终端提交按钮
        this.commonRefreshBtn = true // 通用刷新按钮
        this.adjustTerminalGroupingBtn = true // 调整终端分组和名称按钮
        this.deptRuleCondition = true // 部门规则条件是否显示
      }
      if (tab.name == 'fourth') {
        this.query1.status = 4
        this.firstGrpNickNameColModel = [
          { prop: 'termId', label: 'terminalCode', width: '150' },
          { prop: 'oldGroupName', label: 'subordinateGroup', width: '150' },
          { prop: 'oldNickName', label: 'terminalName', width: '150' },
          { prop: 'createTime', label: 'syncTime', width: '150' }
        ]
        this.$refs['grpNickNameTable'].execRowDataApi()
        this.collectTerminalInformationBtn = false  // 收集终端信息按钮
        this.waitingTerminalSubmissionBtn = false  // 等待终端提交按钮
        this.commonRefreshBtn = true // 通用刷新按钮
        this.adjustTerminalGroupingBtn = false // 调整终端分组和名称按钮
        this.deptRuleCondition = false // 部门规则条件是否显示
      }
    },
    deleteTerminalHandle() {
      // 删除终端
      if (this.grpNickNameTableSelectedData.length <= 0) {
        this.$message({
          message: this.$t('pages.selectAtLeastOne'),
          type: 'error',
          duration: 2000
        })
        return false
      } else {
        this.$confirmBox(this.$t('pages.grpNickName_text1'), this.$t('text.prompt')).then(() => {
          this.requestVO.termList.push(...this.grpNickNameTableSelectedData.map(item => item.termId))
          const vo = Object.assign({}, this.requestVO)
          deleteTerminalGroupNickName(vo).then(respond => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
            const gridTable = this.$refs.grpNickNameTable
            const isLastPage = gridTable.isLastPage()
            const isSelectAll = gridTable.rowData.length === this.grpNickNameTableSelectedData.length
            let opt
            if (isLastPage && isSelectAll) {
              opt = { page: isLastPage - 1 || 1 }
            }
            this.refreshHandle(opt)
          })
        }).catch(() => {})
      }
    },
    emptyTerminalHandle() {
      // 清空终端
      const that = this
      this.$confirmBox(this.$t('pages.grpNickName_text2'), this.$t('text.prompt')).then(() => {
        deleteAllTerminalGroupNickName().then(respond => {
          that.refreshHandle()
        })
      }).catch(() => {})
    },
    startCollectionHandle() {
      // 开始收集
      if ((this.grpNickNameTableSelectedData.length <= 0 && !this.requestVO.isAddAllTerminalStatus) || this.grpNickNameListTotal === 0) {
        this.$message({
          message: this.$t('pages.selectAtLeastOne'),
          type: 'error',
          duration: 2000
        })
        return false
      } else {
        // 清空请求表单
        this.initRequestVO();
        this.grpNickNameTableSelectedData.forEach(item => {
          this.requestVO.termList.push(item.termId)
        })
        const vo = Object.assign({}, this.requestVO)
        console.log('开始收集', vo)
        const that = this
        isExistOffline(vo).then(respond => {
          if (respond.data) {
            this.$confirmBox(this.$t('pages.terminal_text27'), this.$t('text.prompt')).then(() => {
              startCollectTerminal(vo).then(respond => {
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('pages.grpNickName_text3'),
                  type: 'success',
                  duration: 2000
                })
                that.refreshHandle()
              })
            }).catch(() => {})
          } else {
            startCollectTerminal(vo).then(respond => {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.grpNickName_text3'),
                type: 'success',
                duration: 2000
              })
              that.refreshHandle()
            })
          }
        })
      }
    },
    reCollectionHandle() {
      // 重新收集
      if (this.grpNickNameTableSelectedData.length <= 0) {
        this.$message({
          message: this.$t('pages.selectAtLeastOne'),
          type: 'error',
          duration: 2000
        })
        return false
      } else {
        this.initRequestVO()
        this.grpNickNameTableSelectedData.forEach(item => {
          this.requestVO.termList.push(item.termId)
        })
        const vo = Object.assign({}, this.requestVO)
        const that = this
        isExistOffline(vo).then(respond => {
          if (respond.data) {
            this.$confirmBox(this.$t('pages.terminal_text27'), this.$t('text.prompt')).then(() => {
              restartCollectTerminal(vo).then(respond => {
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('pages.grpNickName_text3'),
                  type: 'success',
                  duration: 2000
                })
                that.refreshHandle()
              })
            }).catch(() => {})
          } else {
            restartCollectTerminal(vo).then(respond => {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.grpNickName_text3'),
                type: 'success',
                duration: 2000
              })
              that.refreshHandle()
            })
          }
        })
      }
    },
    refreshHandle(opt) {
      // 刷新
      this.requestVO.termList.splice(0)
      this.$refs['grpNickNameTable'].execRowDataApi(opt)
      this.$emit('submitEnd')// 同步部门时，同时刷新父组件(终端列表)数据
    },
    startSynchronizationHandle() {
      // 开始同步
      // eslint-disable-next-line no-empty
      if (this.defaultTemp.isAllSynTerminalChk === false && this.grpNickNameTableSelectedData.length < 1) {
        this.$message({
          message: this.$t('pages.selectAtLeastOne'),
          type: 'error',
          duration: 2000
        })
        return false
      } else if (this.defaultTemp.synchronizationTerminalChk === false && this.defaultTemp.synchronizationNicNameChk === false) {
        this.$message({
          message: this.$t('pages.selectAtLeastOneSynOption'),
          type: 'error',
          duration: 2000
        })
        return false
      } else {
        this.requestVO.termList.splice(0)
        this.grpNickNameTableSelectedData.forEach(item => {
          if (item.newGroupName === '') {
            this.$message({
              message: this.$t('pages.grpNickName_text4') + item.termId + this.$t('pages.grpNickName_text5'),
              type: 'error',
              duration: 2000
            })
            return false
          }
          if (item.newNickName === '') {
            this.$message({
              message: this.$t('pages.grpNickName_text4') + item.termId + this.$t('pages.grpNickName_text6'),
              type: 'error',
              duration: 2000
            })
            return false
          }
          this.requestVO.termList.push(item.termId)
        })
        this.requestVO.synGrp = this.defaultTemp.synchronizationTerminalChk
        this.requestVO.synNickName = this.defaultTemp.synchronizationNicNameChk
        const vo = Object.assign({}, this.requestVO)
        const that = this
        synGroupNickName(vo).then(respond => {
          console.log('data', respond.data)
          const { success, fail } = respond.data
          this.$notify({
            title: this.$t('text.success'),
            message: `${this.$t('pages.grpNickName_text7')}(${success}), ${this.$t('pages.grpNickName_text8')}(${fail})`,
            type: 'success',
            duration: 2000
          })
          that.refreshHandle()
        })
      }
    },
    // 调整终端分组和名称 修改弹框数据回显
    handleUpdateGrpNickName(row) {
      this.updateTabList = true
      this.requestVO.termId = row.termId
      this.requestVO.newGroupId = row.newGroupId
      this.requestVO.newGroupName = row.newGroupName
      this.requestVO.newNickName = row.newNickName
      this.groupDefault = [row.newGroupId, row.newGroupName]
    },
    // 分组改变时，修改部门的id
    groupIdSelectChange(data) {
      this.requestVO.newGroupId = data
    },
    // 保存修改
    updateGroupNickNameBtn() {
      const that = this
      const vo = Object.assign({}, this.requestVO)
      if (this.requestVO.newGroupId === '0') {
        this.$message({
          message: this.$t('pages.grpNickName_text9'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.requestVO.newGroupId === undefined) {
        this.$message({
          message: this.$t('pages.grpNickName_text10'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.requestVO.newNickName === '') {
        this.$message({
          message: this.$t('pages.grpNickName_text11'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      updateGroupNickName(vo).then(respond => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.editSuccess'),
          type: 'success',
          duration: 2000
        })
        that.updateTabList = false
        that.refreshHandle()
      })
    },
    /**
     * 终端分组和名称规范列表数据的获取
     * @param option
     * @returns {AxiosPromise}
     */
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query1, option)
      const promise = getTerminalGroupNickNamePage(searchQuery)
      promise.then(res => {
        console.log('response', res.data.total)
        this.grpNickNameListTotal = res.data.total
      })
      return promise
    },
    /**
     * 表格刷新后调用的方法
     * 表格刷新后，判断<复选框>的选中状态，对表格的全选和全不选状态进行处理
     * */
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        if (rowData.length > 0) {
          const status = rowData[0].status
          if (status === 1 && this.requestVO.isAddAllTerminalStatus === true) { // 收集终端信息
            this.$refs.grpNickNameTable.toggleAllSelection()
          } else if (status === 3 && this.requestVO.isSynAllTerminalStatus === true) { // 调整终端分组名称
            this.$refs.grpNickNameTable.toggleAllSelection()
          } else {
            this.$refs.grpNickNameTable.clearSelection()
          }
          if (this.$refs.addTerminalTable) { // 添加终端
            if (this.nodeCheck === true) {
              this.$refs.addTerminalTable.toggleAllSelection()
            } else {
              this.$refs.addTerminalTable.clearSelection()
            }
          }
        }
      })
    },
    /**
     * 点击<复选框>--根据复选框的选中状态，对表格的全选全不选状态进行处理
     * @param e 复选框的选中状态
     */
    allSelectTerminal(e) {
      if (e === true) {
        this.$refs.grpNickNameTable.toggleAllSelection()
      } else {
        this.$refs.grpNickNameTable.clearSelection()
      }
    },
    // 添加终端 弹框显示
    addTerminalHandle() {
      this.selectGroup = true
    },
    // 添加终端弹框
    rowDataApi1: function(option) {
      // 所有在线终端status=4,和终端列表接口共用
      const searchQuery = Object.assign({}, this.query, option)
      const promise = getTerminalPage(searchQuery)
      return promise
    },
    // 添加终端弹框
    handleSelectionChange(val) {
      this.selectedData = val
      // 暂时只有windows终端支持远程控制
      this.remoteable = val.length == 1 && val[0].type % 16 == 0
    },
    // 添加终端确认按钮
    selectGroupBtn() {
      if (this.selectedData.length <= 0 && this.requestVO.groupList.length <= 0) {
        this.$message({
          message: this.$t('pages.selectAtLeastOne'),
          type: 'error',
          duration: 2000
        })
        return false
      } else {
        this.selectedData.forEach(item => {
          this.requestVO.termList.push(item.id)
        })
        const vo = Object.assign({}, this.requestVO)
        const that = this
        addTerminalGroupNickName(vo).then(respond => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.insertSuccess'),
            type: 'success',
            duration: 2000
          })
          this.selectGroup = false
          that.refreshHandle()
        })
      }
    },
    // 分组名称列表-选中数据
    grpNickNameTableHandleSelectionChange(val) {
      this.grpNickNameTableSelectedData = val
      // 选中所有===跨分页选中，当前页全选!=选中所有(除非总条数===当前页全选)
      const selectIds = this.$refs.grpNickNameTable.getSelectedDatas()
      const lastPageSize = this.grpNickNameListTotal % this.$refs.grpNickNameTable.limit;
      if (selectIds.length != this.grpNickNameListTotal &&
        selectIds.length != this.$refs.grpNickNameTable.limit &&
        selectIds.length != lastPageSize) {
        this.requestVO.isSynAllTerminalStatus = false
        this.requestVO.isAddAllTerminalStatus = false
      } else if (selectIds.length === this.grpNickNameListTotal) {
        this.requestVO.isSynAllTerminalStatus = true
        this.requestVO.isAddAllTerminalStatus = true
      }
    },
    // 获取所有子部门 添加终端弹框
    getAllChildrenGroupId(groups, groupIds) {
      groups.forEach(group => {
        if (group.id.indexOf('G') > -1) {
          groupIds.push(group.dataId)
          if (group.children && group.children.length > 0) {
            this.getAllChildrenGroupId(group.children, groupIds)
          }
        }
      })
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    // 单击树节点的回调函数 添加终端弹框
    handleNodeClick(data, node) {
      this.nodeCheck = node.checked
      const groupIds = []
      if (data.id.indexOf('G') > -1) {
        groupIds.push(data.dataId)
      }
      if (data.children && data.children.length > 0) {
        this.getAllChildrenGroupId(data.children, groupIds)
      }
      node.expanded = true
      this.query.groupIds = groupIds.join(',')
      this.$refs['addTerminalTable'].execRowDataApi()
    },
    // 添加终端勾选部门
    checkChangeAddDept(groupId, groupIds) {
      const arr = []
      groupIds.forEach(group => {
        arr.push(group.dataId)
      })
      this.requestVO.groupList = arr;
      if (this.groupIdsCheck < arr.length) {
        this.nodeCheck = true
        this.query.groupIds = arr.join(',')
        this.groupIdsCheck = arr.length
        this.$refs['addTerminalTable'].execRowDataApi()
      } else {
        this.nodeCheck = false
        this.query.groupIds = arr.join(',')
        this.groupIdsCheck = arr.length
        this.$refs['addTerminalTable'].execRowDataApi()
      }
    },
    // 添加终端弹框
    initGroupTreeNode: function() {
      getDeptTreeFromCache().then(respond => {
        this.treeData = respond.data
        // 分组，不允许选择公司根节点
        if (Array.isArray(respond.data) && respond.data.length === 1 && respond.data[0].dataId == 0) {
          this.groupTreeSelectData = respond.data[0].children
        } else {
          this.groupTreeSelectData = respond.data
        }
      })
    },
    // 部门匹配规则改变
    changeDeptRule: function(data) {
      this.query1.deptRule = data
      const vo = Object.assign({}, this.query1)
      this.$refs['grpNickNameTable'].execRowDataApi(vo)
      // this.refreshHandle()
    },
    // 清空请求参数
    initRequestVO: function() {
      this.requestVO.termList.splice(0)
      this.requestVO.groupList.splice(0)
      this.requestVO.termId = undefined
      this.requestVO.groupId = undefined
      this.requestVO.synGrp = undefined
      this.requestVO.synNickName = undefined
      this.requestVO.status = undefined
      this.requestVO.newGroupId = undefined
      this.requestVO.newGroupName = undefined
      this.requestVO.newNickName = undefined
      this.requestVO.deptRule = undefined
      // this.requestVO.isAddAllTerminalStatus = false // 所有添加的在线终端复选框
      // this.requestVO.isSynAllTerminalStatus = false // 同步所有匹配的分组
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-tab-pane {
    height: 100%;
    line-height: 35px;
    padding-left: 10px;
    color: #409eff;
  }
  .divAddTerminal{
    height: 393px;
  }
</style>
