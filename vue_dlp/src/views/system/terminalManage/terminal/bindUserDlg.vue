<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.bindUserManage')"
    :visible.sync="dialogBindVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <Form ref="terminalForm" :model="temp" :rules="tempUserRules" label-position="right" label-width="120px" style="width: 700px; margin-left:30px;">
      <el-divider content-position="left">{{ $t('pages.basicTerminalInfo') }}</el-divider>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.terminalName') + '：'">
            {{ temp.name }}
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.deviceNum') + '：'">
            {{ temp.id }}
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.computerName') + '：'">
            {{ temp.computerName }}
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.ipAddr') + '：'">
            {{ temp.mainIp }}
          </FormItem>
        </el-col>
      </el-row>
      <el-divider content-position="left">{{ $t('pages.terminal_text16') }}</el-divider>
      <FormItem :label="$t('pages.loginMode')" label-width="105px">
        <el-select v-model="temp.loginMode" style="width: 300px">
          <el-option :value="0" :label="$t('pages.manualLogin')"></el-option>
          <el-option :value="1" :label="$t('pages.userAutoLogin2')"></el-option>
          <el-option :value="2" :label="$t('pages.domainAutoLogin')"></el-option>
        </el-select>
      </FormItem>
      <div style="min-height: 200px;">
        <FormItem v-show="temp.loginMode === 1" :label="$t('pages.terminal_text12')" label-width="105px" prop="userId" style="width: 405px">
          <tree-select
            clearable
            is-filter
            :local-search="false"
            :default-expand-all="false"
            node-key="id"
            :checked-keys="(temp.user && temp.user['name']) ? [{key: 'U' + temp.userId, label: temp.user['name']}] : []"
            :width="296"
            :placeholder="$t('pages.selectAutoLoginUser')"
            :rewrite-node-click-fuc="true"
            :node-click-fuc="userTreeNodeCheckChange"
            @change="userSelectedChange"
          />
        </FormItem>
        <label v-show="temp.loginMode === 1" style="padding:90px">{{ $t('pages.terminal_text17') }}</label>
        <label v-show="temp.loginMode === 2" style="padding:90px">{{ $t('pages.terminal_text18') }}</label>
        <tree-select-panel
          v-show="temp.loginMode === 0"
          ref="userSelectTree"
          leaf-key="user"
          :local-search="false"
          :selected-data="bindedUserData"
          :selected-node-filter="selectedBindedNodeFilter"
          :to-select-title="$t('pages.selectableBindUser')"
          :selected-title="$t('pages.selectedBindUser')"
          height="275px"
          style="border-top: 1px solid #d3d6d8;width: 630px;margin-left: 40px;"
        >
        </tree-select-panel>
        <i v-show="temp.loginMode === 0" class="el-icon-delete" style="position: absolute;right: 80px;margin: 7px;color: #4386c6;" @click="clearBind()"></i>
      </div>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateBind()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogBindVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import TreeSelectPanel from '@/components/TreeSelectPanel'
import { bindUser } from '@/api/system/terminalManage/terminal'
import { getUserNodeByTermId } from '@/api/system/terminalManage/user'

export default {
  name: 'BindUserDlg',
  components: { TreeSelectPanel },
  data() {
    return {
      temp: {},
      defaultTemp: {
        id: undefined,
        name: undefined,
        computerName: undefined,
        mainIp: undefined,
        loginMode: 0
      },
      tempUserRules: {
        userId: [{ required: true, trigger: 'change', validator: this.tempUserValidator }]
      },
      bindedUserData: [],
      dialogBindVisible: false,
      submitting: false
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
  },
  activated() {

  },
  methods: {
    userSelectTree() {
      return this.$refs['userSelectTree']
    },
    show(row) {
      this.temp = row
      this.dialogBindVisible = true
      this.bindedUserData.splice(0)
      this.clearBind()
      getUserNodeByTermId(row.id).then(respond => {
        if (respond.data) {
          const checkedKeys = []
          respond.data.forEach(nodeData => {
            checkedKeys.push(nodeData.id)
          })
          this.userSelectTree().setCheckedKeys(checkedKeys)
        }
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    selectedBindedNodeFilter(nodeDatas) {
      const that = this
      let resultNodeDatas = []
      nodeDatas.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) { // 不是终端组节点，也不是自动登录节点时，才添加到选中节点中
          resultNodeDatas.push(nodeData)
        } else if (nodeData.children && nodeData.children.length > 0) {
          const childrenNodes = that.selectedBindedNodeFilter(nodeData.children)
          resultNodeDatas = resultNodeDatas.concat(childrenNodes)
        }
      })
      return resultNodeDatas
    },
    userTreeNodeCheckChange: function(data, node, vm) {
      if (data.id.indexOf('G') < 0) {
        this.temp.userId = data.dataId
        this.temp.user.name = data.label
      } else {
        return false
      }
    },
    userSelectedChange: function(data) {
      if (!data) {
        this.temp.userId = 0
        this.temp.user.name = ''
      }
    },
    updateBind() {
      this.$refs['terminalForm'].validate((valid) => {
        if (valid) {
          const userIds = []
          const bindGroupIds = []
          this.userSelectTree().forEachSelectedData(nodeData => {
            if (nodeData.id.indexOf('G') < 0) {
              userIds.push(nodeData.dataId)
            } else {
              bindGroupIds.push(nodeData.dataId);
            }
          })
          /* if (userIds.indexOf(this.tempUser.userId) < 0) {
            userIds.push(this.tempUser.userId)
          }*/
          const param = {
            id: this.temp.id,
            loginMode: this.temp.loginMode
          }
          let msg = null
          if (this.temp.loginMode === 0) {
            param.userIds = userIds
            param.bindGroupIds = bindGroupIds
            msg = userIds.length === 0 && bindGroupIds.length === 0 ? this.$t('pages.terminal_text4') : this.$t('pages.terminal_text5')
          } else if (this.temp.loginMode === 1) {
            param.autoLoginUserId = this.temp.userId
          }
          if (!msg) {
            this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
              bindUser(param).then(respond => {
                this.dialogBindVisible = false
                this.$notify({ title: this.$t('text.success'), message: this.$t('text.bindSuccess'), type: 'success', duration: 2000 })
              })
            }).catch(() => {})
          } else {
            bindUser(param).then(respond => {
              this.dialogBindVisible = false
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.bindSuccess'), type: 'success', duration: 2000 })
            })
          }
        }
      })
    },
    clearBind() {
      if (this.userSelectTree()) {
        this.userSelectTree().clearSelectedNode()
        this.userSelectTree().clearFilter()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
