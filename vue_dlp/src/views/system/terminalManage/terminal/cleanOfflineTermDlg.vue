<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.offline_terminal')"
    :width="$store.getters.language === 'en' ? '680px' : '580px'"
    :visible.sync="visible"
    @dragDialog="handleDrag"
  >
    <Form
      ref="cleanUpForm"
      class="cleanup-config"
      :model="temp"
      :rules="rules"
      :style="`width: ${$store.getters.language === 'en' ? '580px' : '450px'}`"
    >
      <el-divider content-position="left">
        {{ $t('pages.offline_terminal_text5') }}
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            <div>{{ $t('pages.offline_terminal_text6') }}</div>
            <div>{{ $t('pages.offline_terminal_text7') }}</div>
          </div>
          <i class="el-icon-info"/>
        </el-tooltip>
      </el-divider>
      <FormItem>
        <el-checkbox
          v-model="temp.autoClean"
          :false-label="0"
          :true-label="1"
        >
          {{ $t('pages.offline_terminal_text9') }}
        </el-checkbox>
        <!-- <el-tooltip class="item" effect="dark" placement="right">
          <div slot="content">
            <div>{{ $t('pages.offline_terminal_text28') }}</div>
          </div>
          <i class="el-icon-info"/>
        </el-tooltip> -->
      </FormItem>
      <FormItem prop="cleanTime" class="clean-time">
        <span :style="temp.autoClean ? '' : 'color: #848484;'">{{ $t('pages.offline_terminal_text33') }}</span>
        <el-time-picker
          v-model="temp.cleanTime"
          format="HH:mm"
          value-format="HH:mm"
          :disabled="!temp.autoClean"
          style="margin-top: 15px"
        />
      </FormItem>
      <FormItem>
        <el-checkbox
          v-model="temp.loginRemind"
          :false-label="0"
          :true-label="1"
        >
          {{ $t('pages.offline_terminal_text8') }}
        </el-checkbox>
      </FormItem>

      <el-divider content-position="left">
        {{ $t('pages.offline_terminal_text1') }}
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            <div>{{ $t('pages.offline_terminal_text36') }}</div>
          </div>
          <i class="el-icon-info"/>
        </el-tooltip>
      </el-divider>
      <FormItem prop="offlineDuration">
        <i18n path="pages.offline_terminal_text3">
          <el-input-number
            slot="day"
            ref="offlineDuration"
            v-model="temp.offlineDuration"
            step-strictly
            :controls="false"
            :min="1"
            :max="720"
            :step="1"
          />
        </i18n>
      </FormItem>
      <FormItem>
        <el-checkbox v-model="temp.autoRecover" :false-label="0" :true-label="1">{{ $t('pages.offline_terminal_text4') }}</el-checkbox>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" size="mini" class="clean-cfg-btn" :loading="cleanSubmitting" @click="cleanNow">
        {{ $t('pages.offline_terminal_text13') }}
      </el-button>
      <el-button type="primary" size="mini" class="clean-cfg-btn" :loading="saveSubmitting" @click="saveConfig">
        {{ $t('button.save') }}
      </el-button>
      <el-button type="primary" size="mini" class="clean-cfg-btn" @click="exit">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { saveConfig, getConfig } from '@/api/system/configManage/cleanUpConfig';

export default {
  name: 'CleanUpConfig',
  data() {
    return {
      temp: {},
      visible: false,
      defaultTemp: {
        offlineDuration: 30,
        autoRecover: 0,
        loginRemind: 0,
        autoClean: 0,
        cleanTime: '02:00'
      },
      offlineDuration: 30,
      cleanSubmitting: false,
      saveSubmitting: false
    }
  },
  computed: {
    cleanUpForm() {
      return this.$refs['cleanUpForm']
    },
    rules() {
      return {
        offlineDuration: [{ required: true, message: this.$t('pages.offline_terminal_text12', { min: 1, max: 720 }), trigger: 'blur' }],
        cleanTime: [{ required: this.temp.autoClean, message: this.$t('pages.offline_terminal_text34'), trigger: ['blur', 'change'] }]
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    async show() {
      await this.getConfig()
      this.visible = true
    },
    getConfig() {
      getConfig().then(response => {
        if (response.data) {
          this.temp = response.data
          this.offlineDuration = this.temp.offlineDuration
          if (this.temp.autoClean == 0) {
            this.$set(this.temp, 'cleanTime', '02:00')
          }
        }
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
    },
    cleanNow() {
      this.cleanSubmitting = true
      this.cleanUpForm.validateField(['offlineDuration'], (errorMessage) => {
        if (errorMessage.length === 0) {
          const option = Object.assign({}, this.temp)
          Promise.all([ // 先执行
            saveConfig(option).then(() => {
              this.cleanSubmitting = false
            }).catch(() => { this.cleanSubmitting = false })
          ]).then(() => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.offline_terminal_text14'),
              type: 'success',
              duration: 2000
            })
            this.$socket.sendToUser('', '/notifyExecute', {
              type: 0,
              termIds: ''
            }, (respond, handle) => {
              // 自动清理分为定期清理及手动通知引擎自动清理，所以结果返回至/topic/respondAutoCleanResult，通知后手动关闭订阅
              handle.close()
            }, (handle) => {
              handle.close()
            })
            this.visible = false
          })
        } else {
          this.cleanSubmitting = false
        }
      })
      this.cleanSubmitting = false
    },
    saveConfig() {
      this.saveSubmitting = true
      this.cleanUpForm.validate(valid => {
        if (valid) {
          const option = Object.assign({}, this.temp)
          saveConfig(option).then(() => {
            this.$notify({
              title: this.$t('text.success'),
              dangerouslyUseHTMLString: true,
              message: this.$t('text.saveSuccess'),
              type: 'success',
              duration: 2000
            })
            this.saveSubmitting = false
            this.visible = false
          }).catch(() => { this.saveSubmitting = false })
        } else {
          this.saveSubmitting = false
        }
      })
    },
    exit() {
      this.cleanUpForm.clearValidate()
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
  .cleanup-config {
    width: 500px;

    >>> .el-form-item__label {
      // color: #ddd;
      font-size: 15px;
    }

    // >>> .el-divider__text {
    //   background-color: #303133;
    //   color: #ffffff;
    //   font-size: 15px;
    //   font-weight: 700;
    // }

    >>> .el-form-item__content {
      margin-left: 40px;

      .el-input-number {
        width: auto;

        .el-input-number__increase, .el-input-number__decrease {
          width: auto;
        }

        .el-input {
          .el-input__inner {
            width: 102px;
            padding-left: 15px;
            padding-right: 20px;
            text-align: center;
          }
        }
      }

      .el-date-editor--time {
        top: -8px
      }
    }

    .clean-cfg-btn {
      float: right;
      width: 80px
    }

    .clean-time {
      >>> .el-form-item__error {
        left: 100px;
      }
    }
  }

</style>
