<template>
  <el-dialog
    v-el-drag-dialog
    :title="i18nConcatText(this.$t('pages.terminal'), 'create')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="visible"
    width="600px"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 530px;">
      <FormItem :label="$t('pages.terminalName')" prop="name">
        <el-input v-model="temp.name" v-trim :maxlength="60"/>
      </FormItem>
      <FormItem :label="$t('pages.terminalType')" prop="type">
        <el-select v-model="temp.type">
          <el-option
            v-for="item in types"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          >
            <svg-icon :icon-class="item.icon"/>
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </FormItem>
      <FormItem :label="$t('pages.terminalGroup')" prop="groupIds">
        <el-input v-if="groupReadonly" :value="groupName" readonly disabled/>
        <tree-select
          v-else
          :width="296"
          node-key="dataId"
          :data="filterGroupTreeData"
          :checked-keys="temp.groupIds"
          is-filter
          @change="handleGroupChange"
        />
      </FormItem>
      <FormItem :label="$t('table.loginMode')" prop="loginMode">
        <el-select v-model="temp.loginMode">
          <el-option v-for="item in loginModeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
        <el-checkbox v-show="temp.loginMode < 2" v-model="temp.createUser" :true-label="1" :false-label="0">
          {{ createUserDescription }}
        </el-checkbox>
      </FormItem>
      <FormItem v-if="temp.loginMode===2" label="">
        <label style="color: #409EFF;">{{ '终端域环境用户自动登录失败时，启用辅登录模式' }}</label>
      </FormItem>
      <FormItem v-if="temp.loginMode===2" :label="$t('table.loginMode2')">
        <el-select v-model="loginMode2" :disabled="disabled">
          <el-option v-for="item in loginMode2Options" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </FormItem>
      <FormItem v-show="showUsers" :label="$t('pages.autoLoginUser')" prop="userId">
        <tree-select
          node-key="id"
          :width="296"
          :local-search="false"
          :default-expand-all="false"
          :checked-keys="userIds"
          clearable
          is-filter
          :placeholder="$t('pages.selectAutoLoginUser')"
          :rewrite-node-click-fuc="true"
          :node-click-fuc="userTreeNodeCheckChange"
          style="display: inline-block; width: calc(100% - 33px);"
          @change="userSelectedChange"
        />
        <link-button btn-style="margin:0;" menu-code="B23" link-url="/terminalManage/terminalManage/user"/>
      </FormItem>
      <FormItem :label="$t('pages.remark')" prop="remark">
        <el-input v-model="temp.remark" type="textarea" rows="2" resize="none" maxlength="300" show-word-limit/>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleAdd">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addTerminal } from '@/api/system/terminalManage/terminal'
import { getLoginModeDict, getTermTypeDict } from '@/utils/dictionary'
import { validUserForbidden } from '@/api/system/terminalManage/user'

export default {
  name: 'AddTerminal',
  props: {
    typeFilter: {
      type: Function,
      default: null
    },
    groupReadonly: {
      type: Boolean,
      default: false
    },
    groupTreeData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        name: undefined,
        type: 0,
        loginMode: 1,
        createUser: 1,
        bindUser: 0,
        userId: 0,
        remark: undefined
      },
      loginMode2: 0,
      filterGroupTreeData: [],
      userIds: [],
      groupName: undefined,
      rules: {
        name: [{ required: true, trigger: 'blur', message: this.$t('text.cantNullInfo', { info: this.$t('pages.terminalName') }) }],
        type: [{ required: true, trigger: 'change', message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.terminalType') }) }],
        groupIds: [{ required: true, trigger: 'change', message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.termName') }) }],
        loginMode: [{ required: true, trigger: 'change', message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.loginMode') }) }],
        userId: [{ required: true, trigger: 'change', validator: this.userValidator }]
      },
      loginModeOptions: [],
      loginMode2Options: [], // 辅登录模式
      types: []
    }
  },
  computed: {
    showUsers() {
      return this.temp.loginMode === 1 && this.temp.createUser === 0 || this.temp.loginMode === 2 && this.loginMode2 === 1
    },
    createUserDescription() {
      if (this.temp.loginMode === 1) {
        return this.$t('pages.autoCreateAndSetUser');
      }
      return this.$t('pages.autoCreateUser')
    }
  },
  activated() {
  },
  created() {
    this.loginModeOptions = getLoginModeDict()
    this.loginMode2Options = this.loginModeOptions.filter(item => item.value !== 2)
    const defaultTypeFilter = type => {
      const funcType = (type & 0xf0)
      const osType = (type & 0xf)
      // Windows U盘终端
      if (funcType === 0x20 && osType === 0) {
        return true
      }
      // Windows、Linux、Mac 永久离线终端
      if (funcType === 0x80 && osType < 3) {
        // 试用版不支持永久离线终端
        return !this.isTrialDlp()
      }
      return false
    }
    const realTypeFilter = type => defaultTypeFilter(type) && (!this.typeFilter || this.typeFilter(type))
    this.types = getTermTypeDict()
      .filter(item => realTypeFilter(item.value))
      .map(item => ({ ...item }))
      .sort((a, b) => {
        const c = (a.value & 0xf0) - (b.value & 0xf0)
        return c === 0 ? (a.value & 0xf) - (b.value & 0xf) : c
      })
  },
  methods: {
    show(groupId = 1, groupName) {
      this.getGroupTreeData()
      const type = (this.types[0] || { value: 0 }).value
      this.temp = { ...this.defaultTemp, type, groupIds: [groupId] }
      this.groupName = groupName
      this.userIds = []
      this.visible = true
      this.loginMode2 = 0
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    handleGroupChange(groupId) {
      if (groupId) {
        this.temp.groupIds = [groupId]
      } else {
        this.temp.groupIds = []
      }
    },
    userTreeNodeCheckChange(data, node, vm) {
      return new Promise((resolve, reject) => {
        if (data.id.indexOf('G' + data.dataId) < 0) {
          //  判断是否为已被禁用的操作员
          const res = validUserForbidden(data.dataId);
          if (res.data) {
            this.$message({
              message: this.$t('pages.unUsedUserNotSelectedMsg'),
              type: 'error',
              duration: 2000
            })
            resolve(false)
          }
          this.temp.userId = data.dataId
          resolve()
        } else {
          resolve(false)
        }
      })
    },
    userSelectedChange(selectKey, data) {
      if (!selectKey) {
        this.temp.userId = 0
      } else if (data && data.length > 0) {
        this.temp.userId = data[0].dataId || this.temp.userId
      }
    },
    userValidator(rule, value, callback) {
      if (this.showUsers && !this.temp.userId) {
        callback(new Error(this.$t('pages.terminal_text13')))
      } else {
        callback()
      }
    },
    getGroupTreeData() {
      this.filterGroupTreeData = this.groupTreeData
      this.filterGroupTreeData = this.filterGroupTreeData.filter(treeData => treeData.dataId != -2)
    },
    handleAdd() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.submitting = true
          const { name, type, groupIds, loginMode, remark } = this.temp
          const createUser = loginMode < 2 ? this.temp.createUser : 0
          const userId = loginMode === 1 && createUser === 0 || loginMode === 2 && this.loginMode2 === 1 ? this.temp.userId : 0
          const data = { name, type, groupId: groupIds[0], loginMode, createUser, bindUser: createUser, userId, remark }
          addTerminal(data).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.addTerminalSuccess'),
              type: 'success',
              duration: 5000
            })
            this.submitting = false
            this.$emit('add', { id: res.data, name, type, groupIds })
            this.visible = false
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    }
  }
}
</script>
