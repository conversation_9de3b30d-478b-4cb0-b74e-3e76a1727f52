<template>
  <el-dialog
    v-el-drag-dialog
    :title="title"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="visible"
    width="600px"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" :extra-width="{en: 0}" style="width: 530px;">
      <FormItem :label="$t('pages.terminalCode')" prop="id">
        <el-input v-model="temp.id" readonly :disabled="disabled"/>
      </FormItem>
      <FormItem :label="$t('pages.terminalName')" prop="name">
        <el-input v-model="temp.name" v-trim :maxlength="60" :disabled="disabled"/>
      </FormItem>
      <FormItem :label="$t('pages.terminalGroup')">
        <tree-select
          :data="filterGroupData"
          node-key="dataId"
          is-filter
          :checked-keys="temp.groupIds"
          :width="296"
          :disabled="disabled"
          @change="handleGroupChange"
        />
      </FormItem>
      <div v-if="temp.type !== 3">
        <FormItem :label="$t('table.loginMode')">
          <el-select v-model="temp.loginMode" :disabled="disabled" @change="loginModeChange">
            <el-option v-for="item in loginModeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-if="temp.loginMode===2" label="">
          <label style="color: #409EFF;">{{ '终端域环境用户自动登录失败时，启用辅登录模式' }}</label>
        </FormItem>
        <FormItem v-if="temp.loginMode===2" :label="$t('table.loginMode2')">
          <el-select v-model="loginMode2" :disabled="disabled">
            <el-option v-for="item in loginMode2Options" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-show="temp.loginMode === 1 || loginMode2 === 1" :label="$t('pages.autoLoginUser')" prop="userId">
          <tree-select
            clearable
            is-filter
            :local-search="false"
            :default-expand-all="false"
            node-key="id"
            :checked-keys="(temp.user && temp.user['name']) ? [{key: 'User' + temp.userId, label: temp.user['name']}] : []"
            :width="296"
            :placeholder="$t('pages.selectAutoLoginUser')"
            :rewrite-node-click-fuc="true"
            :disabled="disabled"
            :node-click-fuc="userTreeNodeCheckChange"
            @change="userSelectedChange"
          />
          <el-checkbox v-if="!disabled" v-model="temp.syncGroup" style="margin: 0;">{{ $t('pages.synchronousGroup') }}</el-checkbox>
          <el-checkbox v-if="!disabled" v-model="temp.syncName">{{ $t('pages.synchronousName') }}</el-checkbox>
        </FormItem>
      </div>
      <FormItem :label="$t('pages.remark')" prop="remark">
        <el-input v-model="temp.remark" type="textarea" rows="2" resize="none" maxlength="300" show-word-limit :disabled="disabled"/>
      </FormItem>
      <!-- <FormItem v-if="temp.type === 3">
        <el-checkbox v-model="temp.mobileExt.specifyApp" :false-label="0" :true-label="1"><label>支持指定APP打开文件</label></el-checkbox>
      </FormItem> -->
    </Form>
    <div v-if="!disabled" slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleUpdate">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { validUserForbidden } from '@/api/system/terminalManage/user'
import { updateTerminal } from '@/api/system/terminalManage/terminal'
import { getLoginModeDict } from '@/utils/dictionary'

export default {
  name: 'EditTerminal',
  props: {
    title: {
      type: String,
      default: ''
    },
    groupData: {
      type: Array,
      default() {
        return []
      }
    },
    disabled: Boolean
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      filterGroupData: [],
      loginMode2: 0,
      rules: {
        name: [{ required: true, message: this.$t('pages.grpNickName_text11'), trigger: 'change' }],
        userId: [{ required: true, trigger: 'change', validator: this.userValidator }]
      },
      loginModeOptions: [],
      loginMode2Options: [] // 辅登录模式
    }
  },
  created() {
    this.loginModeOptions = getLoginModeDict()
    this.loginMode2Options = this.loginModeOptions.filter(item => item.value !== 2)
  },
  activated() {
  },
  methods: {
    show(data) {
      this.getTreeData()
      this.temp = { ...data, syncGroup: false, syncName: false }
      this.loginModeChange()
      this.loginMode2 = 0
      if (this.temp.loginMode === 2 && !!this.temp.userId) {
        this.loginMode2 = 1
      }
      //  若为不为手动操作员时，去除自动登录操作员的id和名称
      if (this.temp.loginMode !== 1 && this.loginMode2 !== 1) {
        this.temp.userId = 0;
        if (this.temp.user.name) {
          this.temp.user.name = null
        }
      }
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate() {
      this.submitting = true
      this.$refs['dataForm'].validate().then(() => {
        // 域自动登录模式下，是根据userId是否有值来判断辅登录模式，因此辅登录模式为手动登录时，需要将userId置为null
        return updateTerminal(Object.assign({}, this.temp, { userId: this.temp.loginMode !== 2 || this.loginMode2 === 1 ? this.temp.userId : 0 }))
      }).then(res => {
        this.$emit('updated', res.data)
        this.visible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.editSuccess'),
          type: 'success',
          duration: 2000
        })
      }).finally(() => {
        this.submitting = false
      })
    },
    getTreeData() {
      this.filterGroupData = this.groupData
      this.filterGroupData = this.filterGroupData.filter(treeData => treeData.dataId != -2)
    },
    userValidator(rule, value, callback) {
      if ((this.temp.loginMode === 1 || this.temp.loginMode === 2 && this.loginMode2 === 1) && !value) {
        callback(new Error(this.$t('pages.terminal_text13')))
      } else {
        callback()
      }
    },
    handleGroupChange(data) {
      if (this.temp.groupIds[0] !== data) {
        this.temp.groupIds.splice(0, 1, data)
      }
    },
    userTreeNodeCheckChange(data, node, vm) {
      return new Promise((resolve, reject) => {
        const { id, dataId, label } = data
        if (id.indexOf('G' + dataId) < 0) {
          //  判断是否为已被禁用的操作员
          const res = validUserForbidden(dataId);
          if (res.data) {
            this.$message({
              message: this.$t('pages.unUsedUserNotSelectedMsg'),
              type: 'error',
              duration: 2000
            })
            resolve(false)
          }

          this.temp.userId = dataId
          this.temp.user.name = label
          this.$emit('userChange', dataId, label)
          resolve()
        } else {
          resolve(false)
        }
      })
    },
    userSelectedChange(selectKey, data) {
      if (!selectKey) {
        this.temp.userId = 0
        this.temp.user.name = ''
        this.$emit('userChange', 0, '')
      } else if (data && data.length > 0) {
        const userId = data[0].dataId || this.temp.userId
        const name = data[0].label || this.temp.user.name
        this.temp.userId = userId
        this.temp.user.name = name
        this.$emit('userChange', userId, name)
      }
    },
    loginModeChange() {
      if (this.temp.loginMode !== 2) {
        this.loginMode2 = this.temp.loginMode
      }
    }
  }
}
</script>
