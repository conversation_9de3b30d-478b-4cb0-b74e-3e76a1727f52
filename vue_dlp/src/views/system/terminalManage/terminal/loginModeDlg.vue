<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.changeLoginMode1')"
    :visible.sync="dialogVisible"
    width="900px"
    @dragDialog="handleDrag"
  >
    <Form ref="loginModeForm" :model="tempUser" :rules="rules" label-position="right" label-width="120px" style="width: 840px; margin-left:10px;">
      <div class="app-container" style="height: 450px;">
        <div class="tree-container">
          <strategy-target-tree ref="strategyTargetTree" style="height: 430px;" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange" />
        </div>
        <div style="overflow: auto;padding-left: 10px;">
          <el-row>
            <el-col :span="9">
              <FormItem label-width="1px">
                <el-checkbox v-model="multiUpdateLoginMode" :disabled="loginModeRowData.length == 0">{{ $t('pages.batchChangeLoginMode') }}</el-checkbox>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ this.$t('pages.phoneLoginTypeTip') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </FormItem>

            </el-col>
            <el-col :span="15">
              <FormItem :label="$t('pages.loginMode')">
                <el-select v-model="multiLoginMode" :disabled="!multiUpdateLoginMode" style="width: 99%" @change="multiLoginModeChangeFunc">
                  <el-option v-for="item in loginModeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </FormItem>
            </el-col>
            <el-col :span="9">
              <FormItem label-width="1px">
                <el-checkbox v-model="multiUpdateLoginUser" :disabled="!multiUpdateLoginMode || !multiLoginMode">{{ $t('pages.batchChangeUser') }}</el-checkbox>
              </FormItem>
            </el-col>
            <el-col :span="15">
              <FormItem :label="$t('pages.autoLoginUser')">
                <tree-select
                  ref="multiUserSelectTree"
                  style="width: 99%"
                  clearable
                  is-filter
                  :disabled="!multiUpdateLoginMode || !multiLoginMode || !multiUpdateLoginUser"
                  :local-search="false"
                  :default-expand-all="false"
                  node-key="id"
                  :width="296"
                  :placeholder="$t('pages.selectAutoLoginUser')"
                  :rewrite-node-click-fuc="true"
                  :node-click-fuc="multiLoginUserTreeNodeClickEnd"
                />
              </FormItem>
            </el-col>
          </el-row>
          <grid-table
            ref="toUpdateTermTable"
            :col-model="loginModeColModel"
            :row-datas="loginModeRowData"
            :multi-select="false"
            :height="358"
            :show-pager="false"
            @selectionChangeEnd="handleSelectionChange"
          />
        </div>
      </div>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateLoginMode()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listTerminal, updateLoginMode } from '@/api/system/terminalManage/terminal'
import { deepMerge } from '@/utils'
import { getLoginModeDict } from '@/utils/dictionary'
import { validUserForbidden } from '@/api/system/terminalManage/user'

export default {
  name: 'LoginModeDlg',
  props: {
  },
  data() {
    return {
      loginModeColModel: [
        { prop: 'name', label: 'terminalName', width: '120', sort: true },
        { prop: 'id', label: 'terminalCode', width: '100', sort: true },
        { prop: 'loginMode', label: 'loginMode', width: '150', sort: true, type: 'select', options: [
          { value: 0, label: 'manualLogin' }, { value: 1, label: 'userAutoLogin' }, { value: 2, label: 'domainAutoLogin' }
        ],
        change: this.changeTableRowLoginMode },
        {
          prop: 'userId',
          label: 'autoLoginUser',
          width: '150',
          sort: true,
          type: 'treeSelect',
          checkedKeysFieldName: 'checkedKeys',
          isFilter: true,
          localSearch: false,
          disabled: (col, row) => { return row.loginMode === 0 },
          nodeChange: this.changeTableRowLoginUser
        },
        { prop: '', fixedWidth: 40, type: 'icon', iconClass: 'delete', iconFormatter: () => { return true }, style: 'cursor: pointer;', click: this.removeTerminal }
      ],
      loginModeOptions: [],
      query: {
        groupIds: '',
        ids: '',
        // 0 16 32 128 为windows端
        // 2 18 34 130 为mac端
        // 1 17 33 129 为linux端
        types: '0,16,32,128,1,17,34,129,2,18,33,130' // 查询时过滤移动端
      },
      phoneType: ['3', '19', '131'],
      tempUser: {},
      temp: {}, // 表单字段
      defaultTemp: {
        name: '',
        id: undefined,
        userId: undefined,
        groupIds: [],
        remark: '',
        loginMode: 0,
        mobileExt: undefined,
        user: {
          name: ''
        }
      },
      multiLoginMode: undefined,
      multiUpdateLoginMode: true,
      multiUpdateLoginUser: false,
      rules: {
      },
      dialogVisible: false,
      submitting: false,
      loginModeRowData: []
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.resetQuery()
    this.loginModeOptions = getLoginModeDict()
  },
  activated() {
  },
  methods: {
    resetQuery() {
      this.query.groupIds = ''
      this.query.ids = ''
    },
    terminalFilter(node) {
      if (node.dataId == '-2') {
        return false
      }
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.oriData)
      if (isNaN(type)) {
        return false
      }
      return type == 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.multiUpdateLoginMode = null
      this.multiLoginMode = null
      this.resetQuery()
      const allGroups = this.$store.getters.deptIdMap;
      const groupIdArr = []
      if (checkedNode.id.indexOf('G') > -1) {
        groupIdArr.push(checkedNode.id)
        this.getAllChildrenGroupId(allGroups, checkedNode.id, groupIdArr)
        const groupIds = groupIdArr.map(x => x.replace('G', ''))
        this.query.groupIds = groupIds.join(',')
      } else {
        if (this.phoneType.indexOf(checkedNode.dataType) > -1) {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.phoneLoginTypeTip'),
            type: 'warning'
          });
          return
        }
        this.query.ids = checkedNode.dataId
      }
      listTerminal(this.query).then(respond => {
        this.loginModeRowData.splice(0)
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(data => {
            if (data.useType == 0) {
              if (data.userId && data.user.name) {
                data.checkedKeys = [{ key: data.userId, label: data.user.name }]
              } else {
                data.checkedKeys = []
              }
              if (data.user.name === undefined) {
                data.userId = undefined
              }
              this.loginModeRowData[data.id] = data
            }
          })
          // 去除数组的空值部分。添加这一步操作主要为了根据id进行排序
          this.loginModeRowData = this.loginModeRowData.filter(rowData => { return rowData })
        }
      })
    },
    // 获取所有子部门
    getAllChildrenGroupId(groups, groupId, groupIds) {
      const arr = groups[groupId];
      arr.forEach(id => {
        if (id.indexOf('G') > -1) {
          groupIds.push(id)
        }
        this.getAllChildrenGroupId(groups, id, groupIds)
      })
    },
    handleSelectionChange(val) {
    },
    resetTemp() {
      this.temp = deepMerge({ user: { name: '' }}, this.defaultTemp)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleUpdate() {
      this.dialogVisible = true
      this.resetMultiUpdateLoginModeParams()
      this.loginModeRowData.splice(0)
    },
    updateLoginMode() {
      const formData = []
      for (let i = 0; i < this.loginModeRowData.length; i++) {
        const rowData = this.loginModeRowData[i]
        if (rowData.loginMode === 1 && !rowData.userId) {
          this.$message({ message: this.$t('pages.setAutoLoginUser'), type: 'error', duration: 2000 })
          return
        }
        formData.push({ id: rowData.id, loginMode: rowData.loginMode, userId: rowData.loginMode !== 0 ? rowData.userId : 0 })
      }
      updateLoginMode({ terminals: formData }).then(respond => {
        this.dialogVisible = false
        this.$emit('submitEnd')
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.editSuccess'), type: 'success', duration: 2000 })
      })
    },
    resetMultiUpdateLoginModeParams() {
      this.multiUpdateLoginMode = false
      this.multiLoginMode = undefined
      this.multiUpdateLoginUser = false
      this.$refs['multiUserSelectTree'] && this.$refs['multiUserSelectTree'].clearSelectedNode()
    },
    multiLoginModeChangeFunc() {
      this.loginModeRowData.forEach(rowData => {
        rowData.loginMode = this.multiLoginMode
      })
      this.loginModeRowData.splice(0, 0)
    },
    multiLoginUserTreeNodeClickEnd(data, node, vm) {
      return new Promise((resolve, reject) => {
        if (data.id.indexOf('G' + data.dataId) < 0) {
          //  判断是否为已被禁用的操作员
          const res = validUserForbidden(data.dataId);
          if (res.data) {
            this.$message({
              message: this.$t('pages.unUsedUserNotSelectedMsg'),
              type: 'error',
              duration: 2000
            })
            resolve(false)
          }
          const checkedKey = { key: data.dataId, label: data.label }
          this.loginModeRowData.forEach(rowData => {
            rowData.checkedKeys = [checkedKey]
            rowData.userId = data.dataId
          })
          resolve()
        } else {
          resolve(false)
        }
      })
    },
    changeTableRowLoginMode(row, col, rowIndex) {
      this.resetMultiUpdateLoginModeParams()
    },
    changeTableRowLoginUser(nodeData, node, vm, rowData, col) {
      return new Promise((resolve, reject) => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          //  判断是否为已被禁用的操作员
          const res = validUserForbidden(nodeData.dataId);
          if (res.data) {
            this.$message({
              message: this.$t('pages.unUsedUserNotSelectedMsg'),
              type: 'error',
              duration: 2000
            })
            resolve(false)
          }
          this.multiUpdateLoginUser = false
          rowData.userId = nodeData.dataId
          rowData.checkedKeys = [{ key: nodeData.dataId, label: nodeData.label }]
          resolve()
        } else {
          resolve(false)
        }
      })
    },
    removeTerminal(e, row, rowIndex) {
      const index = this.loginModeRowData.indexOf(row)
      this.$confirmBox(this.$t('pages.confirmDeleteTerm') + row.name + '？', this.$t('text.prompt')).then(() => {
        this.loginModeRowData.splice(index, 1)
      }).catch(() => {

      })
    }
  }
}
</script>
