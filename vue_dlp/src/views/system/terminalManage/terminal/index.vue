<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab" @tab-click="tabClick">
      <el-tab-pane :label="$t('route.terminal')" name="terminalInfo">
        <terminal-info ref="terminalInfo" />
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission('363')" :label="$t('route.TerminalLog')" name="terminalLog">
        <terminal-log ref="terminalLog" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import terminalInfo from './terminalInfo'
import terminalLog from '@/views/assets/systemMaintenance/terminalLog'

export default {
  name: 'Terminal',
  components: { terminalInfo, terminalLog },
  data() {
    return {
      activeName: 'terminalInfo'
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    tabClick(pane, event) {
    },
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    }
  }
}
</script>
