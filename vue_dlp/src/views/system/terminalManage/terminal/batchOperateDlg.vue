<template>
  <el-dialog
    v-el-drag-dialog
    :title="titleMap[tips]"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogFormVisible"
    width="800px"
  >
    <Form ref="dataForm" label-position="right" label-width="90px" style="width: 700px;">
      <FormItem :label="$t('pages.effectiveScope')" label-width="65px" :extra-width="{en:50}">
        <span style="padding-left: 30px;">
          <el-radio v-model="effective" :label="1">{{ $t('pages.currentlySelectedTerminal', { len: selectLength }) }}</el-radio>
          <el-radio v-model="effective" :label="2">{{ $t('pages.currentlyQueriedAllTerminals', { len: allResultLength }) }}</el-radio>
        </span>
      </FormItem>
      <el-row v-show="effective === 2">
        <el-col :span="7">
          <FormItem label-width="1px">
            <el-checkbox v-model="checkAll" @change="changeSelected">{{ $t('pages.checkAllPagesData') }}</el-checkbox>
          </FormItem>
        </el-col>
      </el-row>
      <grid-table
        v-show="effective === 1"
        ref="partDataTable"
        :col-model="colModel"
        :row-datas="tableRowDatas"
        :multi-select="true"
        :height="350"
        :show-pager="false"
      />
      <grid-table
        v-show="effective === 2"
        ref="allDataTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="true"
        :height="315"
        :show-pager="true"
        :after-load="afterLoad"
        :autoload="false"
        @select="selectData"
        @select-all="selectAllData"
      />
      <el-row>
        <label>{{ tipsMap[tips] }}</label>
      </el-row>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleOperate()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTerminalPage } from '@/api/system/terminalManage/terminal'

export default {
  name: 'BatchOperateDlg',
  props: {
    tips: { // 标题
      type: String,
      default: null
    },
    colModel: {// 列模板配置
      type: Array,
      default: function() {
        return []
      }
    },
    rowDatas: { // 表格数据
      type: Array,
      default: function() {
        return []
      }
    },
    operationFunc: { type: Function, default: null }, // effective为1，即生效范围为部分时，执行的方法
    operationAllFunc: { type: Function, default: null } // effective为2，即生效范围查询所有时，执行的方法
  },
  data() {
    return {
      titleMap: {
        cleanTerm: this.$t('pages.offline_terminal_text18'),
        recoverTerm: this.$t('pages.offline_terminal_text21')
      },
      tipsMap: {
        cleanTerm: this.$t('pages.offline_terminal_text25'),
        recoverTerm: this.$t('pages.offline_terminal_text22')
      },
      query: {
        page: 1,
        useType: undefined,
        status: undefined,
        filterType: undefined,
        groupIds: undefined
      },
      submitting: false,
      dialogFormVisible: false,
      checkAll: true,
      effective: 1,
      tableRowDatas: [],
      selectLength: 0, // 当前选中终端的条数
      allResultLength: 0, // 当前查询的所有终端的条数
      backupUnSelectedIds: [], // 未勾选数据id缓存
      backupSelectedIds: [], // 已勾选数据id缓存
      backupSelectedDatas: [] // 已勾选数据
    }
  },
  computed: {
    partDataTable() {
      return this.$refs.partDataTable
    },
    allDataTable() {
      return this.$refs.allDataTable
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (!val) {
        this.checkAll = false
        this.selectLength = 0
        this.allResultLength = 0
        this.tableRowDatas.splice(0)
      }
    },
    rowDatas(val) {
      this.tableRowDatas = val
      if (val.length > 0) {
        this.effective = 1
        this.selectLength = val.length
      } else {
        this.selectLength = 0
        this.effective = 2
      }
    }
  },
  created() {
  },
  methods: {
    show(data) {
      this.backupUnSelectedIds.splice(0)
      this.backupSelectedIds.splice(0)
      this.backupSelectedDatas.splice(0)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.checkAll = true
        this.query.groupIds = data
        if (this.effective == 1) {
          this.partDataTable.toggleAllSelection()
        } else if (this.effective == 2) {
          this.allDataTable.toggleAllSelection()
        }
      })
    },
    rowDataApi: function(option) {
      if (this.tips == 'cleanTerm') {
        this.query.useType = 0
        this.query.status = 0
        this.query.filterType = '32,128,129,130'
      } else if (this.tips == 'recoverTerm') {
        this.query.useType = '1,2'
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getTerminalPage(searchQuery).then(res => {
        this.allResultLength = res.data.total
        return res
      })
    },
    changeSelected(val) {
      if (val) {
        this.allDataTable.toggleAllSelection()
      } else {
        if (this.allDataTable.getSelectedDatas().length > 0) {
          this.allDataTable.clearSelection()
        }
      }
    },
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        // 选中当前页面所有的终端,处理未选中的数据
        if (rowData.length > 0 && this.checkAll) {
          rowData.forEach(r => {
            if (this.backupUnSelectedIds.indexOf(r['id']) == -1) {
              this.allDataTable.toggleRowSelection(r, true)
            }
          })
          // this.gridTable.toggleAllSelection()
        } else { // 未选中当前页面所有的终端,处理已选中的数据
          rowData.forEach(r => {
            if (this.backupSelectedIds.indexOf(r['id']) >= 0) {
              this.allDataTable.toggleRowSelection(r, true)
            }
          })
        }
      })
    },
    selectData(selection, row) {
      this.$nextTick(() => {
        // 选中全部的情况下，缓存取消勾选的数据，未选中的情况下，缓存已勾选的数据
        if (this.checkAll) {
          if (this.allDataTable.getSelectedIds().indexOf(row['id']) == -1) {
            this.backupUnSelectedIds.push(row['id'])
          } else {
            if (this.backupUnSelectedIds.indexOf(row['id']) >= 0) {
              this.backupUnSelectedIds.splice(this.backupUnSelectedIds.indexOf(row['id']), 1)
            }
          }
        } else {
          if (this.allDataTable.getSelectedIds().indexOf(row['id']) >= 0) {
            this.backupSelectedIds.push(row['id'])
            this.backupSelectedDatas.push(row)
          } else {
            if (this.backupSelectedIds.indexOf(row['id']) >= 0) {
              this.backupSelectedIds.splice(this.backupSelectedIds.indexOf(row['id']), 1)
              this.backupSelectedDatas.splice(this.backupSelectedIds.indexOf(row['id']), 1)
            }
          }
        }
      })
    },
    selectAllData(selection) {
      this.$nextTick(() => {
        // 选中全部的情况下，缓存取消勾选的数据，未选中的情况下，缓存已勾选的数据
        if (this.checkAll) {
          // 当前页面选中数据的长度为0，证明是取消全选
          if (this.allDataTable.getSelectedIds().length === 0) {
            this.allDataTable.rowData.forEach(
              r => {
                if (this.backupUnSelectedIds.indexOf(r['id']) === -1) {
                  this.backupUnSelectedIds.push(r['id'])
                }
              }
            )
          } else { // 当前页面全选情况下
            this.allDataTable.rowData.forEach(
              r => {
                if (this.backupUnSelectedIds.indexOf(r['id']) >= 0) {
                  this.backupUnSelectedIds.splice(this.backupUnSelectedIds.indexOf(r['id']), 1)
                }
              }
            )
          }
        } else {
          // 当前页面选中数据的长度为0，证明是取消全选
          if (this.allDataTable.getSelectedIds().length === 0) {
            this.allDataTable.rowData.forEach(
              r => {
                if (this.backupSelectedIds.indexOf(r['id']) >= 0) {
                  this.backupSelectedIds.splice(this.backupSelectedIds.indexOf(r['id']), 1)
                  this.backupSelectedDatas.splice(this.backupSelectedIds.indexOf(r['id']), 1)
                }
              }
            )
          } else { // 当前页面全选情况下
            this.allDataTable.rowData.forEach(
              r => {
                if (this.backupSelectedIds.indexOf(r['id']) === -1) {
                  this.backupSelectedIds.push(r['id'])
                  this.backupSelectedDatas.push(r)
                }
              }
            )
          }
        }
      })
    },
    handleOperate() {
      this.submitting = true
      let result = false
      if (this.effective == 1) {
        if (this.partDataTable.getSelectedDatas().length == 0) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
        if (typeof this.operationFunc == 'function') {
          const termIds = this.partDataTable.getSelectedDatas().map(terminal => terminal.id)
          result = this.operationFunc(termIds)
        }
      } else if (this.effective == 2) {
        const selectedIds = this.allDataTable.getSelectedIds()
        if (selectedIds.length == 0) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.currentNoSelectedAnyTerminal'),
            type: 'warning',
            duration: 2000
          })
          this.submitting = false
          return
        }
        if (this.checkAll) {
          if (typeof this.operationFunc == 'function') {
            const searchQuery = { query: this.query, backupUnSelectedIds: this.backupUnSelectedIds }
            result = this.operationAllFunc(searchQuery)
          }
        } else {
          result = this.operationFunc(this.backupSelectedIds)
        }
      }
      if (result) {
        this.submitting = false
        this.dialogFormVisible = false
      }
    }
  }
}
</script>
