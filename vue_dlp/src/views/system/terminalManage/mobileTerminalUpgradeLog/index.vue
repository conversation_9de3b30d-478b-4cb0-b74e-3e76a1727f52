<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :os-type-filter="8" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.keyword1" :value="query.keyword1">
          <span>{{ $t('table.upgradeStatus') }}：</span>
          <el-select v-model="query.keyword1">
            <el-option :label="$t('pages.all')" :value="null"></el-option>
            <el-option :label="$t('pages.hasBeenUpgraded')" :value="1"></el-option>
            <el-option :label="$t('pages.notUpgraded')" :value="2"></el-option>
            <el-option :label="$t('pages.downloadFailed')" :value="3"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'484'" :request="exportFunc"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'473'" icon="el-icon-delete" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table ref="logList" :default-sort="{ prop: 'createTime' }" :multi-select="$store.getters.auditingDeleteAble && hasPermission('473')" :col-model="colModel" :custom-col="true" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.upgradeStatusDetail')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.transmissionTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.installPackageName')">
            {{ rowDetail.packageName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.devType')">
            {{ typeFormatter(rowDetail, rowDetail.mobileType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.installPackageVersionNumber')">
            {{ rowDetail.installVersion }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.curTermVersion')">
            {{ rowDetail.versionNum }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.upgradeStatus')">
            {{ statusFormatter(rowDetail, rowDetail.upStatus) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPage, exportMobileTerminalUpgradeLog, deleteLog } from '@/api/assets/systemMaintenance/mobileTerminalUpgrade'
import { enableStgDelete } from '@/utils';

export default {
  name: 'MobileTerminalUpgradeLog',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'createTime', width: '150', sort: true },
        { prop: 'terminalName', label: 'terminalName', fixedWidth: '200', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'packageName', label: 'installPackageName', width: '200' },
        { prop: 'mobileType', label: 'devType', width: '200', formatter: this.typeFormatter },
        { prop: 'installVersion', label: 'installPackageVersionNumber', width: '150' },
        { prop: 'versionNum', label: 'curTermVersion', width: '150' },
        { prop: 'upStatus', label: 'upgradeStatus', width: '150', formatter: this.statusFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('358,360'),
          buttons: [
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('360') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        keyword1: null,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      treeable: true,
      showTree: true,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        endTime: new Date(),
        softWareTasks: [],
        oneTime: 1,
        remark: '',
        active: true,
        objectIds: [],
        objectGroupIds: []
      },
      rowDetail: {},
      dialogFormVisible: false,
      deleteable: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    exportFunc(exportType) {
      return exportMobileTerminalUpgradeLog({ exportType, ...this.query })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    typeFormatter(row, data) {
      let msg = ''
      if (data == 0) {
        msg = 'android'
      } else if (data == 1) {
        msg = 'ios'
      }
      return msg
    },
    statusFormatter(row, data) {
      let msg = ''
      if (data == 1) {
        msg = this.$t('pages.hasBeenUpgraded')
      } else if (data == 2) {
        msg = this.$t('pages.notUpgraded')
      } else if (data == 3) {
        msg = this.$t('pages.downloadFailed')
      }
      return msg
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      const toDelete = this.gridTable.getSelectedDatas()
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    }
  }
}
</script>
