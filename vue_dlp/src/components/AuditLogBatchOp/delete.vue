<template>
  <audit-log-batch-op
    style="margin-left:10px"
    :name="$t('button.delete')"
    icon="el-icon-delete"
    :selection="selection"
    :confirm="confirmMessage"
    @click="handleDelete"
  />
</template>

<script>
import AuditLogBatchOp from './index'

export default {
  name: 'AuditLogDelete',
  components: { AuditLogBatchOp },
  props: {
    selection: {
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    dateRange: {
      type: [Object, Function],
      default: null
    },
    deleteLog: {
      type: Function,
      required: true
    },
    tableGetter: {
      type: Function,
      default: null
    },
    confirmMessage: {
      type: [String, Function],
      default() {
        return this.$t('pages.validateMsg_deleteMsg')
      }
    }
  },
  methods: {
    handleDelete(data) {
      const headers = {}
      if (this.dateRange) {
        const dateRange = typeof this.dateRange === 'function' ? this.dateRange() : this.dateRange
        if (dateRange.isTimes) {
          headers['X-Date-Range'] = dateRange.startDate + '~' + dateRange.endDate
        } else {
          headers['X-Date-Range'] = dateRange.createDate
        }
      }
      this.deleteLog(data, headers).then(() => {
        this.$emit('deleted')
        if (this.tableGetter) {
          const gridTable = this.tableGetter()
          if (gridTable) {
            gridTable.deleteRowData(data)
          }
        }
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.deleteSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>
