<template>
  <el-button :size="size" :type="type" :icon="icon" :disabled="disabled" @click="handleOp">
    {{ name }}
    <el-tooltip effect="dark" placement="bottom-start">
      <div slot="content">
        {{ $t('components.logBatchOpTips', { name }) }}
        <template v-if="tips">, {{ tips }}</template>
      </div>
      <i class="el-icon-info"/>
    </el-tooltip>
  </el-button>
</template>

<script>
export default {
  name: 'AuditLogBatchOp',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: 'mini'
    },
    type: {
      type: String,
      default: 'primary'
    },
    icon: {
      type: String,
      default: undefined
    },
    tips: {
      type: String,
      default: undefined
    },
    confirm: {
      type: [String, Function],
      required: true
    },
    selection: {
      type: Array,
      required: true,
      default() {
        return []
      }
    },
    filter: {
      type: Function,
      default(item) {
        return true
      }
    }
  },
  computed: {
    opData() {
      if (Array.isArray(this.selection) && this.selection.length > 0) {
        return this.selection.filter(item => item.searchReport !== 2 && (!this.filter || this.filter(item)))
      }
      return []
    },
    disabled() {
      return this.opData.length === 0
    }
  },
  methods: {
    handleOp() {
      this.$confirmBox(this.getConfirmMessage(), this.$t('text.prompt')).then(() => {
        this.$emit('click', this.opData)
      }).catch(() => {})
    },
    getConfirmMessage() {
      if ('string' === typeof this.confirm) {
        return this.confirm
      }
      if ('function' === typeof this.confirm) {
        return this.confirm(this.opData)
      }
      return ''
    }
  }
}
</script>
