<!--
  画布，支持一下应用场景：
  1、单图片模式：画一张图片
    1.1、showToolbar设置为false，则为单图模式
    1.2、通过调用drawImage方法向画布画图，第二次调用此方法将覆盖第一次所画图像
  2、幻灯片模式：自动切换显示图片集
    2.1、showToolbar设置为true，则为幻灯片模式，默认为true
    2.2、通过调用appendImage方法，向幻灯片图片集中添加图片
    2.3、点击"开始"按钮，进行幻灯片的播放

  调用示例：
  1、单图模式
    1.1、设置画板：<el-canvas ref="canvas" :show-toolbar="false"></el-canvas>
    1.2、画图：this.$refs['canvas'].drawImage('data:image/png;base64,xxxxxx')
    1.2、清理画板：this.$refs['canvas'].clean()
  2、幻灯片模式：
    1.1、设置画板：<el-canvas ref="canvas" :total="imageSize" @start="startFunc" @pause="pauseFunc"></el-canvas>
    1.2、添加图片：that.$refs['canvas'].appendImage('data:image/png;base64,xxxxxx')
    1.3、开始播放：点击开始按钮
-->
<template>
  <div class="canvas-container">
    <div v-show="hasMask" class="canvas-mask">
      <svg-icon v-show="isStop && isFirstImageLoaded && !loading" icon-class="play" class="play" @click="startOrPausePlay"></svg-icon>
      <svg-icon v-show="loading || !isFirstImageLoaded" icon-class="loading" class="loading"></svg-icon>
      <span v-show="loading || isStop && !isFirstImageLoaded" class="loading-text">{{ loadingText }}</span>
    </div>
    <!-- 画布 -->
    <canvas ref="elCanvasRef" :style="canvasStyle"></canvas>
    <!-- 工具栏 -->
    <div v-if="showToolbar" class="canvas-toolbar">
      <!-- 播放/暂停 -->
      <el-button :disabled="unloadPercent == 1" :title="startOrPauseBtnLabel" size="mini" type="primary" @click="startOrPausePlay">
        <svg-icon :icon-class="startOrPauseBtnIcon"></svg-icon>
      </el-button>
      <!-- 停止 -->
      <el-button :disabled="unloadPercent == 1" :title="$t('components.stop')" size="mini" type="primary" @click="stopPlay">
        <svg-icon icon-class="stop"></svg-icon>
      </el-button>
      <!-- 切换播放速度 -->
      <el-popover
        v-model="showSpeed"
        trigger="click"
        placement="top"
        width="120"
        popper-class="speed-popover"
      >
        <ul class="speed-ul">
          <li v-for="(opt, index) in playSpeedOption" :key="index" @click="speedChange(index)">{{ opt.title }}</li>
        </ul>
        <el-button slot="reference" size="mini" type="primary" :title="playSpeedTitle">
          {{ playSpeedLabel }}
        </el-button>
      </el-popover>
      <!-- 图片原始尺寸/适应窗口 -->
      <el-button v-if="switchSize" size="mini" type="primary" :title="screenIconOption.title" @click="switchImgSize">
        <svg-icon :icon-class="screenIconOption.icon"></svg-icon>
      </el-button>
      <!-- 后退（上一张） -->
      <el-button :disabled="disabledPrev" icon="el-icon-caret-left" size="mini" type="primary" :title="$t('components.prev')" @click="toPrevImg"></el-button>
      <!-- 前进（下一张） -->
      <el-button :disabled="disabledNext" icon="el-icon-caret-right" size="mini" type="primary" :title="$t('components.next')" @click="toNextImg"></el-button>
      <!-- 进度条 -->
      <div class="progress-bar">
        <el-progress ref="progress" v-drag-progress :text-inside="true" :stroke-width="progressH" :percentage="percentage" color="#409eff"></el-progress>
        <div class="progress-mask" :style="maskStyle"></div>
      </div>
    </div>
    <!-- 显示的图片 -->
    <div ref="imgContainer" :class="{ 'img-hidden': !imgSrc, 'img-container': true, 'cursor-move': imageDragable }" @mousedown="imageDrag">
      <img :src="imgSrc" alt="" :style="imgStyle" :class="{ 'img-hidden': !imgSrc }">
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import localforage from 'localforage'

export default {
  name: 'ElCanvas',
  directives: {
    dragProgress: {
      // 指令的定义，实现进度条定位、拖动
      bind(el, binding, vnode) {
        const dragDom = el.querySelector('.el-progress-bar')
        dragDom.style.cssText += ';cursor:pointer;'
        const vm = vnode.context
        const moveToCur = debounce((re) => {
          vm.curRecordId = re
          vm.resetPlayInterval()
        }, 0)
        dragDom.onmousedown = (e) => {
          vm.viewByStep = true
          vm.isStop = false
          const total = dragDom.offsetWidth
          const mousedownX = e.clientX
          const progress = e.layerX
          let record = Math.round(progress / total / vm.getStep * 100)
          moveToCur(record)

          document.onmousemove = function(e) {
            const mouseX = e.clientX
            const moveX = mouseX - mousedownX
            const newProgress = progress + moveX
            record = Math.round(newProgress / total / vm.getStep * 100)
            // record 小于 0，则定位到 0
            record = record < 0 ? -1 : record
            // record 大于 total，则不定位
            if (record > vm.total) return
            if (vm.curRecordId != record) {
              moveToCur(record)
            }
          }

          document.onmouseup = function(e) {
            moveToCur(record)
            document.onmousemove = null
            document.onmouseup = null
          }
        }
      }
    }
  },
  props: {
    // 是否可以切换图片尺寸
    switchSize: {
      type: Boolean,
      default: false
    },
    // 是否显示工具条
    showToolbar: {
      type: Boolean,
      default: true
    },
    // 进度条高度
    progressH: {
      type: Number,
      default: 20
    },
    // 图片总数，用于播放进度条的展示。此值与images图片集的数量可能不同，用于image未加载完成时的处理
    total: {
      type: Number,
      default: 1
    },
    showMask: {
      type: Boolean,
      default: true
    },
    // 是否开启浏览器数据库存储
    openDb: {
      type: Boolean,
      default: false
    },
    // 数据库名称，当openDb=true时，该属性可用
    dbName: {
      type: String,
      default: 'canvas-db'
    },
    // 数据库实例名称，当openDb=true时，该属性可用
    storeName: {
      type: String,
      default: ''
    }
  },
  data: function() {
    return {
      startBtnIcon: 'play',
      startBtnLabel: this.$t('components.start'),
      pauseBtnIcon: 'pause',
      pauseBtnLabel: this.$t('components.pause'),
      startOrPauseBtnIcon: null,
      startOrPauseBtnLabel: null,
      playIntervalTimer: null,          // 播放图片的定时器
      isStop: true,                     // 是否停止播放
      isPlaying: false,                 // 是否正在播放
      viewByStep: false,                // 通过上一张/下一张按钮查看
      loading: false,                   // 正在加载图片
      loadingText: this.$t('components.loadingText'),
      isClean: true,                    // 是否空的画布
      curRecordId: null,                // 当前播放记录ID，null说明还未加载图片。当值小于1时，会显示 id 为 1 的图片
      imagesMap: {},                    // 图片 map
      curIndexArr: [],                  // 图片的 curIndex
      dealNum: 0,                       // 记录已绘制的图片的数量，用于计算进度条已加载的进度
      checkInterval: null,              // 检测图片完整性的定时器
      isFirstImageLoaded: false,        // 第一张图片是否已经加载好
      enlarge: -1,                      // 当前放大图片的位置，-1表示都没放大
      timeSpan: 1,                      // 单位秒
      showSpeed: false,                 // 显示播放速度的popover
      playSpeedIndex: 1,
      playSpeedOption: [
        { title: this.$t('components.playSpeed1'), label: '0.5X', delay: 2000 },
        { title: this.$t('components.playSpeed2'), label: '1X', delay: 1000 },
        { title: this.$t('components.playSpeed3'), label: '2X', delay: 500 }
      ],
      imgSrc: '',                       // 当前播放图片的 src
      originalSize: false,              // 是否显示图片的原始尺寸
      aspectRatio: false,               // 是否保持图片的宽高比，在多屏下才能生效
      originalSizeChange: false,        // 是否切换了 originalSize 模式，用于判断是否清空画布
      aspectRatioChange: false,         // 是否切换了 aspectRatio 模式，用于判断是否清空画布
      fullScreenChange: false,          // 是否切换了 全屏 模式，用于判断是否清空画布
      imgConWidth: 0,                   // 图片容器的宽度
      imgConHeight: 0,                  // 图片容器的高度
      smallWidth: true,                 // 图片显示宽度是否比容器小或相等
      smallHeight: true,                // 图片显示高度是否比容器小或相等
      imgWidth: '100%',                 // 图片的宽度
      imgHeight: '100%',                // 图片的高度
      canvasSizeChange: false,          // 画布大小改变
      screenImageLength: 0,
      screenRowColInfo: [],              // 屏幕行列信息
      indexDB: undefined, // indexDB对象
      lock: false, // 事务锁，用于处理同时进行写操作导致的事务冲突问题
      queue: [], // 等待队列,用于存储待插入的图片数据
      timer: undefined // 定时器，用于定时扫描缓存队列是否有数据，有的话插入到indexdb数据库
    }
  },
  computed: {
    canvasStyle() {
      const toolbarHeight = this.showToolbar ? 40 : 0
      const width = this.originalSize ? '' : '100%'
      const height = this.originalSize ? '' : `calc(100% - ${toolbarHeight}px)`
      const visibility = this.showToolbar ? 'hidden' : 'visible'
      this.setImgSize()
      return {
        width,
        height,
        visibility
      }
    },
    imgStyle() {
      if (!this.originalSize) {
        return 'width: 100%; height: 100%; display: block;'
      }
      let position = ''
      if (this.smallWidth && this.smallHeight) {
        position = 'top: 50%; left: 50%; transform: translateX(-50%) translateY(-50%);'
        return `position: absolute; ${position}`
      } else if (this.smallWidth) {
        position = 'left: 50%; transform: translateX(-50%);'
      } else if (this.smallHeight) {
        position = 'top: 50%; transform: translateY(-50%);'
      }
      return `position: absolute; ${position} pointer-events: none;`
    },
    // 图片是否可以拖拽
    imageDragable() {
      return !(this.smallWidth && this.smallHeight)
    },
    // 自适应、原始尺寸 图标信息
    screenIconOption() {
      return this.originalSize ? {
        icon: 'screen-self-adaptation',
        title: '自适应'
      } : {
        icon: 'screen-original-size',
        title: '原始尺寸'
      }
    },
    // 获取canvas绘图对象
    context2D() {
      return this.$refs['elCanvasRef'].getContext('2d')
    },
    // 计算每一张图所占比例
    getStep() {
      const step = Math.ceil((100 / this.total) * 1000) / 1000
      return step
    },
    hasMask() {
      // return this.showMask && (this.loading || !this.isPlaying) && !this.viewByStep
      return this.showMask && (this.loading || this.isStop)
    },
    // 未加载图片的百分比
    unloadPercent() {
      if (this.total) {
        return (this.total - this.dealNum) / this.total
      } else {
        return 1
      }
    },
    percentage() { // 进度条当前进度
      const curRecordId = this.curRecordId === null ? 0 : this.curRecordId
      const total = this.total || 1
      let percent = Math.round((curRecordId / total) * 10000) / 100
      const hasNotFirstPic = this.imagesMap[1] == undefined

      if (percent < 0 || hasNotFirstPic) {
        percent = 0
      } else if (percent > 100) {
        percent = 100
      }
      return percent
    },
    maskStyle() {
      let widthStyle = ''
      if (this.$refs.progress) {
        const outer = this.$refs.progress.$el.querySelector('.el-progress-bar__outer')
        const innerText = this.$refs.progress.$el.querySelector('.el-progress-bar__innerText')
        const width = outer.offsetWidth * this.unloadPercent
        widthStyle = `width: ${(this.unloadPercent * 100).toFixed(2)}%;`
        const textStyle = (outer.offsetWidth - width) < 40 ? 'position: relative; z-index: 1;' : ''
        innerText.style.cssText = textStyle
      }
      const bdr = this.unloadPercent == 1 ? '' : ' border-top-left-radius: 0; border-bottom-left-radius: 0;'
      return `${widthStyle}${bdr}`
    },
    disabledPrev() {
      return this.curRecordId == null || this.curRecordId <= 0 || this.loading
    },
    disabledNext() {
      return this.curRecordId >= this.dealNum || this.loading
    },
    playSpeedTitle() {
      return this.playSpeedOption[this.playSpeedIndex].title
    },
    playSpeedLabel() {
      return this.playSpeedOption[this.playSpeedIndex].label
    },
    playSpeedDelay() {
      return this.playSpeedOption[this.playSpeedIndex].delay
    }
  },
  watch: {
    total(val) {
      if (this.$refs.progress) {
        // 设置进度条刻度，当每张图片占比<10时，说明图片数量大于10张，则将刻度设置为 10
        const p = this.getStep < 10 ? 10 : this.getStep
        const outer = this.$refs.progress.$el.querySelector('.el-progress-bar__outer')
        const inner = this.$refs.progress.$el.querySelector('.el-progress-bar__inner')
        const width = outer.offsetWidth * p / 100

        outer.style.cssText += `background: linear-gradient(to right, #949595 calc(100% - 1px), #fff 1px); background-size: ${p}% 100%;`
        inner.style.cssText += `background: linear-gradient(to right, #409EFF calc(100% - 1px), #fff 1px); background-size: ${width}px 100%;`
      }
    },
    async curRecordId(val) {
      if (val == null) return
      const imgIndex = val <= 0 ? 1 : val
      const image = this.imagesMap[imgIndex]
      if (!image.data) {
        if (this.indexDB) {
          await this.indexDB.getItem(String(imgIndex)).then(value => {
            // 在新的浏览器API中，localStorage的读取方法如 getItem 返回的不再是直接的值，而是一个 Promise 对象,
            // 所以需要在then中获取到实际值
            this.imgSrc = value
          }).catch(function(err) {
            // 当出错时，此处代码运行
            console.warn('get item error:', err)
          })
        }
      } else {
        this.imgSrc = image.data
      }
    },
    screenImageLength() {
      // 当追踪的屏幕数量发生变化时，先情况画布再进行渲染
      this.cleanCanvas()
    },
    storeName(val) {
      this.queue = []
      this.lock = false
      // 实例变更代表已经是另一个播放记录
      if (this.openDb && val) {
        // 创建数据库以及对应的store
        this.indexDB = localforage.createInstance({
          name: this.dbName,
          storeName: val
        })
        if (this.indexDB) {
          console.warn('indexDB created')
        }
      }
    },
    originalSize(val) {
      this.$emit('original', !!val)
    }
  },
  mounted() {
    this.__resizeHandler = debounce(() => {
      this.setImgSize()
      this.canvasSizeChange = true
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)
  },
  created() {
    this.init()
    // 排查问题使用
    window.canvasVm = this
  },
  activated() {
    this.init()
    this.__resizeHandler()
  },
  beforeDestroy() {
    this.clean()
    window.removeEventListener('resize', this.__resizeHandler)
  },
  methods: {
    dropDB() {
      clearInterval(this.timer)
      this.timer = null
      this.indexDB = null
      this.queue = []
      this.lock = false
      if (this.dbName) {
        // 删除数据库，避免缓存占用过多的用户物理机内存
        localforage.dropInstance({
          name: this.dbName
        }).then(() => {
          console.warn('Drop DB Success')
        })
      }
    },
    // indexDB数据存储
    setItem(key, value) {
      if (this.indexDB) {
        return new Promise((resolve, reject) => {
          this.lock = true
          const that = this
          this.indexDB.setItem(String(key), value).then(function(data) {
            if (that.imagesMap[key]) that.imagesMap[key].data = null
            that.lock = false
          }).catch(function(err) {
            that.queue.push({ key, value, resolve, reject })
            console.warn('indexDB setItem error:', err);
          })
          if (!this.timer && this.queue.length) {
            this.timer = setInterval(() => {
              this.processQueue()
            }, 1000)
          } else {
            clearInterval(this.timer)
            this.timer = null
          }
          resolve();
        })
      }
    },
    processQueue() {
      if (this.indexDB) {
        this.queue = this.queue.filter(({ key, value, resolve, reject }) => {
          try {
            if (!this.lock) {
              this.lock = true
              const that = this
              this.indexDB.setItem(String(key), value).then(function(data) {
                if (that.imagesMap[key]) that.imagesMap[key].data = null
                // that.dealNum++
                that.lock = false
                resolve();
                return false
              }).catch(function(err) {
                that.lock = false
                resolve();
                console.warn('indexDB setItem error:', err);
                return true
              })
            } else {
              resolve();
              return true
            }
          } catch (e) {
            reject(e);
          }
        })
      } else {
        this.queue = []
      }
    },
    changeCanvasSize() {
      this.canvasSizeChange = true
    },
    init() {
      if (this.showToolbar) {
        this.stopPlayByLoc() // 避免之前历史遗留的时间戳
      }
    },
    imageDrag(e) {
      if (!this.originalSize) return
      if (!this.imageDragable) return
      const target = e.target
      // 点击鼠标后的滚动值
      const scrollLeft = target.scrollLeft
      const scrollTop = target.scrollTop

      // 鼠标的位置
      let disX = e.clientX
      let disY = e.clientY

      // 滚动边界 (9和11是滚动条的宽和高)
      const minScrollLeft = 0
      const maxScrollLeft = target.scrollWidth - target.offsetWidth + 9
      const minScrollTop = 0
      const maxScrollTop = target.scrollHeight - target.offsetHeight + 11

      document.onmousemove = function(e) {
        const moveX = e.clientX - disX
        const moveY = e.clientY - disY
        const imgMoveX = scrollLeft - moveX
        const imgMoveY = scrollTop - moveY

        target.scrollTo(imgMoveX, imgMoveY)

        // 如果滚动超出范围，更新鼠标的位置
        if (imgMoveX < minScrollLeft) {
          disX += minScrollLeft - imgMoveX
        }
        if (imgMoveX > maxScrollLeft) {
          disX += maxScrollLeft - imgMoveX
        }
        if (imgMoveY < minScrollTop) {
          disY += minScrollTop - imgMoveY
        }
        if (imgMoveY > maxScrollTop) {
          disY += maxScrollTop - imgMoveY
        }
      }

      document.onmouseup = function(e) {
        document.onmousemove = null
        document.onmouseup = null
      }
    },
    // 根据画布区域重置画布的大小，避免出现图片模糊
    resetSize() {
      const canvas = this.$refs['elCanvasRef']
      const bcr = canvas.getBoundingClientRect()
      const dpr = window.devicePixelRatio // 屏幕像素比
      const imgWidth = Math.floor(dpr * bcr.width)
      const imgHeight = Math.floor(dpr * bcr.height)
      if (bcr.height > 0 && (canvas.width != imgWidth || canvas.height != imgHeight)) {
        canvas.width = imgWidth
        canvas.height = imgHeight
        // 由于画布扩大，canvas的坐标系也跟着扩大，如果按照原先的坐标系绘图内容会缩小
        // 所以需要将绘制比例放大
        // this.context2D.scale(dpr, dpr)
      }
    },
    /**
     * 根据 curIndex 验证图片是否缺失
     */
    integrityCheck() {
      let minMissIndex = 1
      let missLength = 0
      this.checkInterval = setInterval(() => {
        // 已下载图片的数量
        const imageLength = this.curIndexArr.length
        // curIndexArr 数组长度为0 或者 图片下载完毕，清除定时器
        if (imageLength == 0 || imageLength == this.total) {
          clearInterval(this.checkInterval)
          this.checkInterval = null
        }
        // 已下载的图片的最大下标
        let maxIndex = Math.max(...this.curIndexArr)
        // 已下载的这部分图片中间没有缺失
        if (maxIndex == imageLength) {
          // 更新最小下标
          minMissIndex = maxIndex
          // 如果缺失的图片数量在 3s 内没有变化，说明已经停止下载，那么缺失的图片就是在后面
          if (missLength == this.total - imageLength) {
            // 将 maxIndex 设置为图片的总数量
            maxIndex = this.total
          } else {
            // 更新缺失图片的数量
            missLength = this.total - imageLength
            return
          }
        }

        // 存放缺失的 index
        const missingIndex = []

        // 遍历从 missingIndex 到最大值的所有 index
        for (let i = minMissIndex; i <= maxIndex; i++) {
          if (this.imagesMap[i] === undefined) {
            missingIndex.push(i);
          }
        }
        // 更新缺失的图片的最小 index
        if (missingIndex.length > 0) {
          minMissIndex = Math.min(...missingIndex)
        }
        // 将缺失的图片 index 传到父组件
        this.$emit('missingIndex', missingIndex)
      }, 3000);
    },
    // 将图片绘制到canvas，并缓存起来
    async drawImage(imgData, width, height, drawEndFunc) {
      const canvas = this.$refs['elCanvasRef']
      if (!canvas) return
      width = !width ? canvas.width : width
      height = !height ? canvas.height : height
      if (imgData) {
        this.isClean = false
        // image数据分多种格式
        // 1、base64格式图片流：data:image/png;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABw...
        // 2、对象格式，如：{row: 2, col: 2, position: 2, data: 'data:image/png;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABw...' }，其中row和col表示将显示屏分成几行几列多个区域，position表示显示区域,从1开始
        // 如果 imgData 是数组，说明是碎片
        if (imgData instanceof Array) {
          const { curIndex, index } = imgData[0]
          // 当前图片是碎片的情况下，获取上一张完整图片作为基础图片（即-1）
          const image = this.imagesMap[curIndex - 1]
          // 如果图片 map 对应的 baseIndex 的图片未加载，则不绘制当前图片
          if (!image) return
          const curImageData = JSON.parse(JSON.stringify(image))
          // 当缓存中已经无图片数据时，需要从数据库中获取
          if (this.indexDB && !curImageData.data) {
            await this.indexDB.getItem(String(curIndex - 1)).then(value => {
              // 在新的浏览器API中，localStorage的读取方法如 getItem 返回的不再是直接的值，而是一个 Promise 对象,
              // 所以需要在then中获取到实际值
              curImageData.data = value
            }).catch(function(err) {
              // 当出错时，此处代码运行
              console.warn('get item error:', err)
            })
          }
          curImageData.children = imgData
          curImageData.curIndex = curIndex
          curImageData.index = index
          curImageData.baseIndex = image.index
          delete curImageData.dealEnd
          this.imagesMap[curImageData.curIndex] = curImageData
          // 画出完整图片
          this.drawImage(curImageData, width, height, drawEndFunc)
        } else if (imgData.pixelDatas) {
          const { curIndex, index } = imgData
          // 当前图片是碎片的情况下，获取上一张完整图片作为基础图片（即-1）
          const image = this.imagesMap[curIndex - 1]
          // 如果图片 map 对应的 baseIndex 的图片未加载，则不绘制当前图片
          if (!image) return
          const curImageData = JSON.parse(JSON.stringify(image))
          // 当缓存中已经无图片数据时，需要从数据库中获取
          if (this.indexDB && !curImageData.data) {
            await this.indexDB.getItem(String(curIndex - 1)).then(value => {
              // 在新的浏览器API中，localStorage的读取方法如 getItem 返回的不再是直接的值，而是一个 Promise 对象,
              // 所以需要在then中获取到实际值
              curImageData.data = value
            }).catch(function(err) {
              // 当出错时，此处代码运行
              console.warn('get item error:', err)
            })
          }
          curImageData.curIndex = curIndex
          curImageData.index = index
          curImageData.baseIndex = image.index
          curImageData.images = []
          delete curImageData.dealEnd
          this.imagesMap[curImageData.curIndex] = curImageData
          // 设置需要绘制的图片
          const myImage = new Image()
          myImage.drawWidth = width
          myImage.drawHeight = height
          myImage.drawX = 0
          myImage.drawY = 0
          curImageData.images.push(myImage)
          curImageData.children = []
          // x轴方向的像素点个数
          const colNum = imgData.right - imgData.left
          // y轴方向的像素点个数
          // const rowNum = imgData.bottom - imgData.top
          imgData.pixelDatas.forEach((data, index) => {
            // 像素值为255时，该像素点不变更
            if (data !== 255) {
              // 设置需要绘制的图片
              const childImage = new Image()
              // 计算当前像素的坐标
              const drawX = index % colNum === 0 ? colNum : index % colNum
              childImage.drawX = imgData.left + drawX
              childImage.drawY = imgData.top + Math.floor(index / colNum)
              childImage.pixel = data
              childImage.isReady = true
              curImageData.children.push(childImage)
            }
          })
          myImage.onload = this.createPixelOnloadFunc(curImageData, myImage, drawEndFunc)
          myImage.src = curImageData.data
        } else {
          // 如果图片带有宽高，且与画布的大小不一致，则调整画布大小
          if ((imgData.width && imgData.width != canvas.width) || (imgData.height && imgData.height != canvas.height)) {
            canvas.width = imgData.width
            canvas.height = imgData.height
            width = canvas.width
            height = canvas.height
            this.setImgSize()
          }
          imgData.images = []
          // 设置需要绘制的图片
          const myImage = new Image()
          myImage.drawWidth = width
          myImage.drawHeight = height
          myImage.drawX = 0
          myImage.drawY = 0
          imgData.images.push(myImage)
          if (imgData.children) {
            imgData.children.forEach((data, index) => {
              // 设置需要绘制的图片
              const childImage = new Image()
              imgData.images.push(childImage)
              // 如果传进来不是一张完整的图片,求出需要绘制的范围
              if (data.row !== 1 || data.col !== 1) {
                // position从1开始
                childImage.drawWidth = myImage.drawWidth / data.col
                childImage.drawHeight = myImage.drawHeight / data.row
                childImage.drawX = childImage.drawWidth * ((data.position - 1) % data.col)
                childImage.drawY = childImage.drawHeight * (Math.ceil(data.position / data.col) - 1)
              }
              childImage.onload = this.createOnloadFunc(imgData, childImage, drawEndFunc)
              childImage.src = data.data
            })
          } else if (imgData.pixels) {
            // 初始化children 走到该if方法块内，imgData对象必定没有pixels方法
            imgData.children = []
            imgData.pixels.forEach(pixel => {
              // x轴方向的像素点个数
              const colNum = pixel.right - pixel.left
              pixel.pixelDatas.forEach((data, index) => {
                // 像素值为255时，该像素点不变更
                if (data !== 255) {
                  // 设置需要绘制的图片
                  const childImage = new Image()
                  // 计算当前像素的坐标
                  const drawX = index % colNum === 0 ? colNum : index % colNum
                  childImage.drawX = pixel.left + drawX
                  childImage.drawY = pixel.top + Math.floor(index / colNum)
                  childImage.pixel = data
                  childImage.isReady = true
                  imgData.children.push(childImage)
                }
              })
            })
            // 当对象无像素差异帧时，不走绘像素差异帧的函数
            if (imgData.children.length == 0) {
              imgData.pixels = undefined
            }
          }
          myImage.onload = imgData.pixels ? this.createPixelOnloadFunc(imgData, myImage, drawEndFunc) : this.createOnloadFunc(imgData, myImage, drawEndFunc)
          myImage.src = imgData.data
        }
      } else {
        // 若没有传入图片数据，则清空画布
        this.isClean = true
        this.context2D.clearRect(0, 0, width, height)
      }
    },
    createPixelOnloadFunc(imgData, image, drawEndFunc) {
      const that = this
      return () => {
        for (let i = 0; i < imgData.images.length; i++) {
          const tempImg = imgData.images[i]
          that.context2D.drawImage(tempImg, tempImg.drawX, tempImg.drawY, tempImg.drawWidth, tempImg.drawHeight)
        }
        const imageData = that.context2D.getImageData(0, 0, image.drawWidth, image.drawHeight)
        for (let i = 0; i < imgData.children.length; i++) {
          const tempImg = imgData.children[i]
          // 像素差异帧进行像素点替换绘制
          const index = (tempImg.drawX + tempImg.drawY * imageData.width) * 4
          const rgb = imgData.colors[tempImg.pixel]
          imageData.data[index] = rgb.red
          imageData.data[index + 1] = rgb.green
          imageData.data[index + 2] = rgb.blue
          imageData.data[index + 3] = 255 // 设置alpha通道为不透明
        }
        // 应用修改后的像素
        that.context2D.putImageData(imageData, 0, 0)
        const canvas = that.$refs['elCanvasRef']
        const dataUrl = canvas.toDataURL('image/jpeg', 0.6) // jpeg格式比较小, 图片质量系数 0-1
        // 如果是碎片化的图片，则替换imagesMap缓存的相关数据
        this.imagesMap[imgData.curIndex] = Object.assign({}, imgData, {
          data: dataUrl,
          position: 1,
          row: 1,
          col: 1,
          children: undefined,
          images: undefined
        })
        // 回调函数，传入图片的url
        if (typeof drawEndFunc == 'function') drawEndFunc(dataUrl)
      // }
      }
    },
    createOnloadFunc(imgData, image, drawEndFunc) {
      const that = this
      return () => {
        image.isReady = true
        let isReady = true
        for (let i = 0; isReady && i < imgData.images.length; i++) {
          if (!imgData.images[i].isReady) {
            isReady = false
          }
        }
        if (isReady) {
          // 在canvas绘制图片
          for (let i = 0; i < imgData.images.length; i++) {
            const tempImg = imgData.images[i]
            that.context2D.drawImage(tempImg, tempImg.drawX, tempImg.drawY, tempImg.drawWidth, tempImg.drawHeight)
          }
          const canvas = that.$refs['elCanvasRef']
          const dataUrl = canvas.toDataURL('image/jpeg', 0.6) // jpeg格式比较小, 图片质量系数 0-1
          if (imgData.children) {
            // 如果是碎片化的图片，则替换imagesMap缓存的相关数据
            this.imagesMap[imgData.curIndex] = Object.assign({}, imgData, {
              data: dataUrl,
              position: 1,
              row: 1,
              col: 1,
              children: undefined,
              images: undefined
            })
          }
          // 回调函数，传入图片的url
          if (typeof drawEndFunc == 'function') drawEndFunc(dataUrl)
        }
      }
    },
    /**
     *  画线
     *  startX-起始坐标X, startY-起始坐标Y, endX-截至坐标X, endY-截至坐标Y, color-颜色, width-线宽
     */
    drawLine(startX, startY, endX, endY, color, width) {
      if (this.context2D) {
        this.context2D.beginPath()
        if (color) {
          this.context2D.strokeStyle = color
        }
        if (width) {
          this.context2D.lineWidth = width
        }
        this.context2D.moveTo(startX, startY)
        this.context2D.lineTo(endX, endY)
        this.context2D.stroke()
      }
    },
    /* 多张图片同时并排显示，且支持放大某张图片 */
    drawImageList(dataList, width, height) {
      // 按照终端 tid 进行升序排列
      // this.groupAndSort(dataList)
      dataList.sort((d1, d2) => d1.tid - d2.tid)
      this.imageList = dataList

      // 初始化画布
      const canvas = this.$refs['elCanvasRef']
      if (!canvas) return
      this.isClean = false
      this.resetSize()
      if (canvas.width == 0 || canvas.height == 0) {
        canvas.width = canvas.clientWidth
        canvas.height = canvas.clientHeight
      }
      // 切换了显示固定宽高比或原始尺寸或全屏
      if (this.aspectRatioChange || this.originalSizeChange || this.fullScreenChange) {
        this.cleanCanvas()
        this.aspectRatioChange = false
        this.originalSizeChange = false
        this.fullScreenChange = false
      }

      // 添加双击事件，使用箭头函数，this指向当前环境的vue实例
      canvas.ondblclick = (e) => {
        // 如果是干净的画布，则直接返回
        if (this.isClean) {
          return
        }
        if (dataList.length === 1) {
          return
        }
        if (this.enlarge != -1) {
          // 双击如果当前图片已经为放大状态，则缩小
          this.enlarge = -1
          // 缩小时，需要先清理画布，否则一些间隙会被原图片渲染
          this.cleanCanvas();
          this.$emit('enlarge', false)
          // 如果显示原始尺寸，切换回平铺
          if (this.originalSize) {
            this.switchImgSize()
          }
        } else {
          // 如果当前图片非放大状态，则放大
          const x = e.offsetX
          const y = e.offsetY
          for (let i = 0; i < this.screenRowColInfo.length; i++) {
            const t = this.screenRowColInfo[i]
            if (t.x <= x && (t.x + t.w) >= x && t.y <= y && (t.y + t.h) >= y) {
              this.enlarge = i // 当前点击的图片序号
              break
            }
          }
          this.enlarge = !this.enlarge ? 0 : this.enlarge
          this.$emit('enlarge', true)
        }
        this.drawImageList(dataList)
      }

      // 屏幕行列数量是否变化
      const isChangeRowCol = !this.screenRowColInfo || this.screenRowColInfo.length !== dataList.length
      // 屏幕行列数量变化，或者画布大小变化
      if (isChangeRowCol || this.canvasSizeChange) {
        this.canvasSizeChange = false
        // 更新屏幕行列信息
        this.screenRowColInfo = this.calculateImageXY(canvas.width, canvas.height, dataList.length)
        // 屏幕数变化时，需要先清理画布，否则一些间隙会被原图片渲染
        this.cleanCanvas();
      }

      // 获取终端数量的 map
      const termNumMap = dataList.reduce((map, data) => {
        map[data.tid] = map[data.tid] ? ++map[data.tid] : 1
        return map
      }, {})

      dataList.forEach((item, i) => {
        const myImage = new Image()
        // 多屏终端的终端名称加上index
        myImage.text = `Screen ${i + 1} - ${item.name}` + (termNumMap[item.tid] > 1 ? `--${item.index + 1}` : '')
        myImage.index = i
        myImage.src = item.data
        myImage.w = item.width
        myImage.h = item.height
        // 使用箭头函数，this指向当前环境的vue实例
        myImage.onload = (e) => {
          const { text, index } = myImage
          let { w, h } = myImage
          let x = 0
          let y = 0
          let xywh = null
          // 获取当前图片所在单元格的信息
          const imgInfo = this.screenRowColInfo[index]
          // 当只追踪一个屏幕，或者放大某个屏幕时
          if (dataList.length == 1 || this.enlarge == index) {
            // 如果显示原始尺寸，则根据图片大小调整画布大小及图片位置
            if (this.originalSize) {
              const canvasCon = canvas.parentNode
              // 图片比画布宽，则调整画布宽度，否则修改图片x坐标
              if (w > canvasCon.clientWidth) {
                canvas.width = w
                canvas.height -= 11
              } else {
                canvas.width = canvasCon.clientWidth
                x = (canvasCon.clientWidth - w) / 2
              }
              // 图片比画布高，则调整画布高度，否则修改图片y坐标
              if (h > canvasCon.clientHeight) {
                canvas.height = h
                canvas.width -= 9
              } else {
                canvas.height = canvasCon.clientHeight
                y = (canvasCon.clientHeight - h) / 2
              }
            } else {
              w = canvas.width
              h = canvas.height
            }
            xywh = { x, y, w, h }
          } else if (this.enlarge == -1) {
            // 没有放大某个屏幕时，并排显示
            let { x, y, w, h } = imgInfo
            if (this.aspectRatio) {
              // 保持宽高比
              const imgAspectRatio = myImage.w / myImage.h
              const canvasAspectRatio = w / h
              if (imgAspectRatio > canvasAspectRatio) {
                y = y + (h - w / imgAspectRatio) / 2
                h = w / imgAspectRatio
              } else {
                x = x + (w - h * imgAspectRatio) / 2
                w = h * imgAspectRatio
              }
            }

            xywh = Object.assign({}, imgInfo, { x: x + 1, y: y + 1, w: w - 2, h: h - 2 })
          }

          if (xywh) {
            const { x, y, w, h } = xywh
            this.context2D.drawImage(myImage, x, y, w, h)
            if (item.name) {
              // 绘画名称信息
              const info = { text, x, y, w, h, fontSize: 15 }
              this.appendScreenInfo(info)
            }
          }
          if (isChangeRowCol) {
            this.$emit('drawEnd')
          }
        }
      })
    },
    // 根据 imageList 重新绘制图片
    reDrawImageList() {
      if (this.imageList && this.imageList.length > 0) {
        this.drawImageList(this.imageList)
      }
    },
    /**
     * 对传进来的图像信息按照终端id进行排序
     */
    groupAndSort(arr) {
      const lastIndexMap = new Map();
      arr.forEach((obj, pos) => {
        const tid = obj.tid;
        lastIndexMap.set(tid, pos);
      });
      arr.sort((a, b) => {
        const tidA = a.tid;
        const tidB = b.tid;
        if (tidA !== tidB) {
          return tidA - tidB;
        }
        return lastIndexMap.get(tidB) - lastIndexMap.get(tidA);
      });
      return arr;
    },
    /**
     * 计算图片所占画布空间的坐标值和长宽
     * width-宽, height-高, size-图片个数
     */
    calculateImageXY(width, height, size) {
      if (size === 0) {
        return []
      }
      if (size === 1) {
        return [{ x: 0, y: 0, w: width, h: height }]
      }

      let rowSize = 1
      let colSize = 1
      // 计算几行几列，按照列行交替增加的规则计算
      for (let i = 0; i < 100; i++) {
        if (rowSize < colSize) {
          rowSize += 1
        } else {
          colSize += 1
        }
        // 单元格能够放下图片结束循环
        if (rowSize * colSize >= size) {
          break
        }
      }

      // 每个单元格的宽高
      const colW = width / colSize
      const colH = height / rowSize
      const imgXY = []
      // 多余的单元格数
      const deltSize = rowSize * colSize - size
      // 遍历图片数量，并设置所占空间的位置、大小
      for (let i = 0; i < size; i++) {
        const w = colW
        let h = colH
        // x坐标为图片序号对列数取余后乘以宽，y坐标为图片序号除以列数向下取整后乘以高
        let x = i % colSize * colW
        let y = Math.floor(i / colSize) * colH
        // 如果图片数量少于单元格数，根据多出来的数量 deltSize，对前 deltSize 个单元格进行向下扩展
        if (deltSize > 0) {
          // 由于列和行是交替增加，所以 deltSize 会小于 colSize，所以只有第一行的单元格会被扩展
          if (deltSize > i) {
            // 扩展的格子高度*2，其余属性不改变
            h = 2 * colH
          } else if (i >= colSize) {
            // 第一行除了扩展的格子外，其余格子属性不改变，i >= colSize 设置的是第一行之后的单元格
            // 由于扩展的格子占用了原本第i个格子的位置，所以需要向后移动 deltSize 个格子，再重新计算位置
            x = (i + deltSize) % colSize * colW
            y = Math.floor((i + deltSize) / colSize) * colH
          }
        }
        imgXY.push({ x, y, w, h })
      }
      return imgXY
    },
    /* 多张图片根据图片数量按照不同方式显示，且支持放大某张图片 */
    newDrawImageList(dataList, width, height) {
      this.screenImageLength = dataList.length
      // this.clean()
      const that = this
      const canvas = this.$refs['elCanvasRef']
      if (!canvas) return
      this.isClean = false
      this.resetSize()
      if (canvas.width == 0 || canvas.height == 0) {
        canvas.width = canvas.clientWidth
        canvas.height = canvas.clientHeight
      }
      // var row = 1
      var col = 1
      // 判断高度和宽度最终采用哪种方法进行计算
      var areaVisible = true
      // 根据图片数量来计算每张图片高度和宽度
      if (!width && !height) {
        // 高度与宽度全为空
        if (dataList.length < 4) {
          // 1~3 并排显示
          width = canvas.width / dataList.length
          height = canvas.height
          // row = 1
          col = dataList.length
        } else if (dataList.length >= 4 && dataList.length <= 12) {
          const res = this.computeImage4and12(dataList, canvas)
          width = res.width
          height = res.height
          // row = res.row
          col = res.col
        } else {
          const res = this.computeImageMorethan12(dataList, canvas)
          width = res.width
          height = res.height
          // row = res.row
          col = res.col
        }
      } else {
        // 宽度与高度有一个或全部不为空，则只压缩宽度，即采用并排显示
        width = !width ? canvas.width / dataList.length : width
        height = !height ? canvas.height : height
        areaVisible = false
      }
      // 双击放大缩小
      canvas.ondblclick = function(e) {
        if (that.isClean) { // 如果是干净的画布，则直接返回
          return
        }
        const x = e.offsetX
        const y = e.offsetY
        const i = parseInt(x / width) // 当前图片的位置
        const j = parseInt(y / height)
        that.cleanCanvas()
        if (that.enlarge != -1) { // 双击如果当前图片已经为放大状态，则缩小
          that.enlarge = -1
        } else { // 如果当前图片非放大状态，则放大
          if (dataList.length < 4) {
            that.enlarge = i
          } else if (dataList.length >= 4 && dataList.length <= 12) {
            if (dataList.length != 5 && dataList.length != 7 && dataList.length != 11) {
              if (j == 0) {
                that.enlarge = i
              } else {
                that.enlarge = j * col + i
              }
            } else if (dataList.length == 5) {
              if (j == 0) {
                that.enlarge = parseInt(x / (canvas.width / 3))
              } else {
                that.enlarge = j * 3 + parseInt(x / (canvas.width / 2))
              }
            } else if (dataList.length == 7) {
              if (j == 0) {
                that.enlarge = parseInt(x / (canvas.width / 4))
              } else {
                that.enlarge = j * 4 + parseInt(x / (canvas.width / 3))
              }
            } else {
              if (j == 0) {
                that.enlarge = parseInt(x / (canvas.width / 3))
              } else if (j == 1) {
                that.enlarge = j * 3 + parseInt(x / (canvas.width / 4))
              } else {
                that.enlarge = 7 + parseInt(x / (canvas.width / 4))
              }
            }
          } else {
            const remainDer = dataList.length % 4
            if (j == 0) {
              that.enlarge = parseInt(x / width)
            } else if (j >= 1 && j <= 3) {
              that.enlarge = j * col + i
            } else {
              that.enlarge = j * col + parseInt(x / (canvas.width / remainDer))
            }
          }
        }
        that.newDrawImageList(dataList)
      }
      // 开始绘制
      dataList.forEach((item, i) => {
        const myImage = new Image()
        myImage.src = item.data
        myImage.onload = function() {
          if (that.enlarge == -1) { // 如果没有放大某张图片，全部显示
            if (areaVisible) { // 根据图片数量划分显示区域
              if (dataList.length < 4) { // 图片数量 < 4并排显示
                that.drawImageLessthan4(dataList, that, myImage, width, i, item, height)
              } else if (dataList.length >= 4 && dataList.length <= 12) { // 图片数量 >= 4 and 图片数量 <= 12
                that.drawImage4And12(dataList, i, that, myImage, item, width, height, canvas)
              } else {
                const remainder = dataList.length % 4
                if (remainder == 0) {
                  // 余数为0则只有4行
                  // 先计算一行有几列
                  const col = dataList.length / 4
                  that.drawImageMorethan12(that, i, myImage, width, height, item, col, remainder, canvas)
                } else {
                  // 余数不为0则有5行
                  // 先计算不包括最后一行，每行有几列
                  const col = (dataList.length - remainder) / 4
                  that.drawImageMorethan12(that, i, myImage, width, height, item, col, remainder, canvas)
                }
              }
            } else { // 并排显示
              that.context2D.drawImage(myImage, width * i, 0, width, height)
            }
          } else if (that.enlarge == i) { // 如果放大某张图片，则该图片充满整个canvas
            that.context2D.drawImage(myImage, 0, 0, canvas.width, canvas.height)
            that.appendTerminalName(that, item, canvas.width, 0, 25)
          }
        }
      })
    },
    // 根据追踪的终端数量计算每个屏幕占据的宽和高
    computeImage4and12(dataList, canvas) {
      var width = ''
      var height = ''
      var row = ''
      var col = ''
      if (dataList.length % 3 == 0) { // 9、12
        const temp = dataList.length / 3
        width = canvas.width / temp
        height = canvas.height / 3
        row = 3
        col = temp
      } else if (dataList.length % 2 == 0) { // 10、8、6、4
        const temp = dataList.length / 2
        width = canvas.width / temp
        height = canvas.height / 2
        row = 2
        col = temp
      } else { // 5、7、11
        // 先将宽度赋值为画布的宽度，具体宽度绘制时再进行计算
        if (dataList.length == 5 || dataList.length == 7) {
          height = canvas.height / 2
          row = 2
        } else {
          height = canvas.height / 3
          row = 3
        }
        col = 3 // 列数， 需要再根据具体第几行重新计算
        width = canvas.width
      }
      return { width: width, height: height, row: row, col: col };
    },
    computeImageMorethan12(dataList, canvas) {
      const remainDer = dataList.length % 4
      var col = (dataList.length - remainDer) / 4
      var width = canvas.width / col
      var height = ''
      var row = ''
      remainDer == 0 ? height = canvas.height / 4 : height = canvas.height / 5
      remainDer == 0 ? row = 4 : row = 5
      return { width: width, height: height, row: row, col: col };
    },
    // 根据终端数量的不同绘制不同风格图像
    drawImageLessthan4(dataList, that, myImage, width, i, item, height) {
      if (dataList.length == 1) {
        that.context2D.drawImage(myImage, width * i, 0, width, height)
        that.appendTerminalName(that, item, width, i, 25)
      } else {
        that.context2D.drawImage(myImage, width * i, 0, width - 15, height)
        that.appendTerminalName(that, item, width, i, 25)
      }
    },
    drawImage4And12(dataList, i, that, myImage, item, width, height, canvas) {
      if (dataList.length % 3 == 0) {
        // 终端数 = [9, 12],分3行
        // 先计算每行有几列
        const col = dataList.length / 3
        if (i < col) { // 第一行
          that.context2D.drawImage(myImage, width * i, 0, width - 15, height - 10)
          that.appendTerminalName(that, item, width, i, 25)
        } else if (i >= col && i < 2 * col) {
          that.context2D.drawImage(myImage, width * (i - col), height, width - 15, height - 10)
          that.appendTerminalName(that, item, width, (i - col), height + 25)
        } else {
          that.context2D.drawImage(myImage, width * (i - 2 * col), 2 * height, width - 15, height - 10)
          that.appendTerminalName(that, item, width, (i - 2 * col), 2 * height + 25)
        }
      } else if (dataList.length % 2 == 0) {
        // 终端数 = [4, 6, 8, 10],分2行
        const col = dataList.length / 2
        if (i < col) {
          that.context2D.drawImage(myImage, width * i, 0, width - 15, height - 10)
          that.appendTerminalName(that, item, width, i, 25)
        } else {
          that.context2D.drawImage(myImage, width * (i - col), height, width - 15, height - 10)
          that.appendTerminalName(that, item, width, (i - col), height + 25)
        }
      } else {
        // 终端数 = [5, 7, 11], [5,7]-2行，11-3行
        if (dataList.length == 5 || dataList.length == 7) {
          var row1 = 0
          var row2 = 0
          if (dataList.length == 5) {
            row1 = 3
            row2 = 2
          } else {
            row1 = 4
            row2 = 3
          }
          if (i < row1) {
            // 第一行
            const width1 = canvas.width / row1
            that.context2D.drawImage(myImage, width1 * i, 0, width1 - 15, height - 10)
            that.appendTerminalName(that, item, width1, i, 25)
          } else {
            const width1 = canvas.width / row2
            that.context2D.drawImage(myImage, width1 * (i - row1), height, width1 - 15, height - 10)
            that.appendTerminalName(that, item, width1, i - row1, height + 25)
          }
        } else {
          if (i < 3) {
            // 11 - 3行，分别是3，4，4
            const width1 = canvas.width / 3
            that.context2D.drawImage(myImage, width1 * i, 0, width1 - 15, height - 10)
            that.appendTerminalName(that, item, width1, i, 25)
          } else if (i >= 3 && i <= 6) {
            const width1 = canvas.width / 4
            that.context2D.drawImage(myImage, width1 * (i - 3), height, width1 - 15, height - 10)
            that.appendTerminalName(that, item, width1, i - 3, height + 25)
          } else {
            const width1 = canvas.width / 4
            that.context2D.drawImage(myImage, width1 * (i - 7), 2 * height, width1 - 15, height - 10)
            that.appendTerminalName(that, item, width1, i - 7, 2 * height + 25)
          }
        }
      }
    },
    drawImageMorethan12(that, i, myImage, width, height, item, col, remainDer, canvas) {
      if (i < col) {
        // 第一行
        that.context2D.drawImage(myImage, width * i, 0, width, height)
        that.appendTerminalName(that, item, width, i, 25)
      } else if (i >= col && i < 2 * col) {
        that.context2D.drawImage(myImage, width * (i - col), height, width, height)
        that.appendTerminalName(that, item, width, (i - col), height + 25)
      } else if (i >= 2 * col && i < 3 * col) {
        that.context2D.drawImage(myImage, width * (i - 2 * col), 2 * height, width, height)
        that.appendTerminalName(that, item, width, (i - 2 * col), 2 * height + 25)
      } else if (i >= 3 * col && i < 4 * col) {
        that.context2D.drawImage(myImage, width * (i - 3 * col), 3 * height, width, height)
        that.appendTerminalName(that, item, width, (i - 3 * col), 3 * height + 25)
      } else {
        const tempWidth = canvas.width / remainDer
        that.context2D.drawImage(myImage, tempWidth * (i - 4 * col), 4 * height, tempWidth, height)
        that.appendTerminalName(that, item, tempWidth, (i - 4 * col), 4 * height + 25)
      }
    },
    // 屏幕追踪时，往画布右上角添加终端名称
    appendTerminalName(that, item, width, i, height) {
      that.context2D.font = '20px Arial';
      that.context2D.fillStyle = 'rgba(255, 0, 0, 0.5)';
      const contextWidth = that.context2D.measureText(item.name).width
      const textPosRow = width * i + (width - 15 - contextWidth)
      that.context2D.fillText(item.name, textPosRow, height)
    },
    // 往画布添加文字信息，info 图片信息，pos 文字添加的位置，默认左上角，'right' 右上角
    appendScreenInfo(info, pos) {
      const context2D = this.context2D
      const { text, x, y, w, fontSize = 16 } = info
      // 文字宽度
      context2D.font = `${fontSize}px Arial`;
      const textW = context2D.measureText(text).width
      // 绘制坐标
      const posX = pos != 'right' ? x : x + w - textW - 12
      const posY = y
      // 绘制文字背景，填充一个矩形
      context2D.fillStyle = 'rgba(255,255,255,0.5)';
      context2D.fillRect(posX, posY, textW + 10, +fontSize + 10)
      // 绘制文字
      context2D.fillStyle = 'rgb(0,0,0)'
      context2D.fillText(text, posX + 5, posY + 17)
    },
    // 根据本地缓存进行播放
    playByLocCache() {
      clearInterval(this.playIntervalTimer)
      // 当前图片处于范围外时，将curRecordId定位到第一张
      if (this.curRecordId > this.total || this.curRecordId <= 0) {
        this.curRecordId = 1
      }
      this.startOrPauseBtnIcon = this.pauseBtnIcon
      this.startOrPauseBtnLabel = this.pauseBtnLabel
      // 设置定时器，定时播放图片，通过修改playSpeedDelay改变播放速度
      this.playIntervalTimer = window.setInterval(() => {
        // 播放到最后一张时停止播放
        if (this.curRecordId >= this.total) {
          this.stopPlayByLoc()
          return
        }
        const nextRecordId = this.curRecordId + 1
        // dealEnd处理错误  需要进行修改.........................
        // 如果下一张图片已下载并绘制完成
        if (this.imagesMap[nextRecordId] && this.imagesMap[nextRecordId].dealEnd) {
          this.loading = false
          this.curRecordId = nextRecordId
        } else {
          this.loading = true
        }
      }, this.playSpeedDelay)
    },
    // 重置播放定时器的时间
    resetPlayInterval() {
      if (this.isPlaying) {
        this.playByLocCache()
      }
    },
    // 停止播放
    stopPlayByLoc() {
      if (this.playIntervalTimer) {
        clearInterval(this.playIntervalTimer)
        this.playIntervalTimer = null
        this.isPlaying = false
      }
      this.loading = false
      this.startOrPauseBtnIcon = this.startBtnIcon
      this.startOrPauseBtnLabel = this.startBtnLabel
    },
    /** 向图片集中添加图片
     * image数据分多种格式:
     * 1、对象格式，如：{ index: 2, baseIndex: 1, row: 2, col: 2, position: 2, data: 'data:image/png;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABw...' }
     *    index表示此图片的索引位置，baseIndex表示此图片关联的图片索引，如果baseIndex不为空，则需要先画baseIndex的图片然后再画index图片
     *    row和col表示将显示屏分成几行几列多个区域，position表示显示区域
     * 2、对象数组，由1所述格式的对象组成
     */
    appendImage(image, storeName) {
      // 存储的数据为非当前实例数据直接返回
      if (storeName && storeName !== this.storeName) {
        return
      }
      // 添加图片，记录index
      const temp = Array.isArray(image) ? image[0] : image
      const { curIndex, baseIndex } = temp
      if (this.imagesMap[curIndex] === undefined) {
        this.imagesMap[curIndex] = image
        this.curIndexArr.push(curIndex)
      }
      // 验证图片数量完整性
      if (!this.checkInterval) {
        this.integrityCheck()
      }
      // 默认显示第一张图片
      if (this.curRecordId === null && this.imagesMap[1]) {
        // 绘制第一张图片前，重置画布大小
        this.resetSize()
        // 绘制第一张图片，并将curRecordId修改为 0
        this.drawImage(this.imagesMap[1], null, null, data => {
          this.setItem(1, data)
          // 因为 setItem 是一个耗时操作，当 curRecordId 修改为 0 时，会去数据库读取 key 为 1 的图片，
          // 此时读取到的是 null，会导致图片不显示（页面全白），所以在这边直接给 imgSrc 赋值
          this.imgSrc = data
          this.curRecordId = 0
          this.imagesMap[1].dealEnd = true
          // this.imagesMap[1].data = null
          this.dealNum++
          if (this.imagesMap[2] && !this.imagesMap[2].drawAble) {
            // 如果下一张图片尚未开始画，则主动开始画
            this.appendImage(this.imagesMap[2])
          }
        })
        this.isFirstImageLoaded = true
        this.loading = false
      } else {
        if (baseIndex === 0 || this.imagesMap[curIndex - 1] && this.imagesMap[curIndex - 1].dealEnd) {
          this.imagesMap[curIndex].dealAble = true
          this.imagesMap[curIndex].dealEnd = false
          // 先确定上一张完整图片是否画完，画完才能画当前图片
          // 图片加载后直接绘制，并将不完整的图片数组替换为完整的图片
          this.drawImage(image, null, null, data => {
            if (this.imagesMap[curIndex]) {
              this.setItem(curIndex, data)
              this.imagesMap[curIndex].dealEnd = true
              // this.imagesMap[curIndex].data = null
              this.dealNum++
            }
            const nextImg = this.imagesMap[curIndex + 1]
            if (nextImg && !nextImg.dealAble && !nextImg.dealEnd) {
              // 如果下一张图片已缓存但是尚未开始画，则主动开始画
              this.appendImage(this.imagesMap[curIndex + 1])
            }
          })
        } else {
          this.imagesMap[curIndex].dealAble = false
        }
      }
    },
    // 复原所有配置，清空画布
    clean() {
      this.imagesMap = {}
      this.dealNum = 0
      this.curIndexArr.splice(0)
      this.imgSrc = ''
      this.smallWidth = true
      this.smallHeight = true
      this.curRecordId = null
      this.enlarge = -1
      this.isStop = true
      this.isPlaying = false
      this.loading = false
      this.isFirstImageLoaded = false
      this.viewByStep = false
      this.originalSize = false
      this.stopPlayByLoc()
      this.drawImage()
    },
    // 开始或暂停
    startOrPausePlay() {
      this.viewByStep = false
      if (this.startOrPauseBtnIcon === this.startBtnIcon) {
        // 如果当前播放的图片是最后一张，此时点开始播放，则将 curRecordId 定位到第一张
        if (this.curRecordId == this.total) {
          this.curRecordId = 1
        }
        this.playByLocCache()
        this.isStop = false
        this.isPlaying = true
        if (this.curIndexArr.length >= this.total) {
          return
        }
        this.$emit('start', { recordId: this.curIndexArr.length })
      } else {
        this.isPlaying = false
        this.stopPlayByLoc()
        this.$emit('pause')
      }
    },
    // 停止播放
    stopPlay() {
      this.viewByStep = false
      this.isStop = true
      this.curRecordId = 0
      this.startOrPauseBtnIcon = this.pauseBtnIcon
      this.startOrPausePlay()
      this.drawImage(this.imagesMap[1])
    },
    // 播放速度变化
    speedChange(index) {
      this.playSpeedIndex = index
      this.showSpeed = false
      this.resetPlayInterval()
    },
    // 设置图片的尺寸
    setImgSize() {
      if (!this.$refs.imgContainer) return
      const canvas = this.$refs['elCanvasRef']
      if (!canvas) return
      this.imgConWidth = this.$refs.imgContainer.offsetWidth
      this.imgConHeight = this.$refs.imgContainer.offsetHeight
      if (this.switchSize) {
        this.imgWidth = this.originalSize ? canvas.width : this.imgConWidth
        this.imgHeight = this.originalSize ? canvas.height : this.imgConHeight

        this.smallWidth = this.imgWidth <= this.imgConWidth
        this.smallHeight = this.imgHeight <= this.imgConHeight
      }
    },
    // 切换图片显示尺寸（平铺或原始尺寸
    switchImgSize() {
      this.originalSize = !this.originalSize
      this.originalSizeChange = true
      this.setImgSize()
      this.reDrawImageList()
    },
    // 多屏显示时，切换图片显示尺寸（平铺或固定宽高比
    switchAspectRatio() {
      this.aspectRatio = !this.aspectRatio
      this.$emit('aspectRatio', !!this.aspectRatio)
      this.aspectRatioChange = true
      this.reDrawImageList()
    },
    switchFullScreen() {
      this.fullScreenChange = true
      this.reDrawImageList()
    },
    // 清空画布内容
    cleanCanvas() {
      const canvas = this.$refs['elCanvasRef']
      if (!canvas) return
      this.context2D.clearRect(0, 0, canvas.width, canvas.height);
    },
    // 上一幅图
    toPrevImg() {
      this.curRecordId--
      this.viewByStep = true
      this.isStop = false
      this.resetPlayInterval()
    },
    // 下一幅图
    toNextImg() {
      this.curRecordId++
      this.viewByStep = true
      this.isStop = false
      this.resetPlayInterval()
    },
    exportImg() {
      const canvas = this.$refs['elCanvasRef']
      // 创建水印上下文
      const ctx = canvas.getContext('2d')
      // 设置水印样式
      ctx.font = '16px Arial'
      ctx.fillStyle = 'rgba(255, 200, 155, 0.5)' // 白色半透明
      ctx.textAlign = 'center'
      // 绘制水印文字
      ctx.fillText('版权所有 © YourCompany', canvas.width - 150, 50)
      ctx.fillText('版权所有 © YourCompany1', canvas.width - 150, 75)

      // 创建图片元素
      const img = new Image()
      img.src = canvas.toDataURL('image/png')
      img.style.width = '100%'
      img.style.height = 'auto'

      // 创建下载链接
      const downloadLink = document.createElement('a')
      downloadLink.href = img.src
      downloadLink.download = 'exported-image.png'
      downloadLink.click()
    }
  }
}
</script>

<style lang="scss" scoped>
  .canvas-container{
    height: 100%;
    position: relative;
    user-select: none;
  }
  .canvas-mask{
    position: absolute;
    background-color: rgba(0,0,0,0.7);
    height: calc(100% - 40px);
    width: 100%;
    z-index: 1;
    .svg-icon{
      color: #ddd;
      font-size: 64px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -35px;
      margin-top: -35px;
      &.play{
        cursor: pointer;
      }
      &.loading{
        -webkit-animation: loading-rotate 1s ease-in-out infinite;
        animation: loading-rotate 1s ease-in-out infinite;
      }
    }
    .loading-text{
      color: #ddd;
      position: absolute;
      top: 50%;
      font-size: 17px;
      width: 100%;
      text-align: center;
      margin-top: 58px;
    }
  }
  .img-container {
    width: 100%;
    height: calc(100% - 40px);
    position: absolute;
    top: 0;
    left: 0;
    overflow: auto;
    background: #000a;
  }
  .img-hidden {
    visibility: hidden;
  }
  .cursor-move {
    cursor: move;
  }
  .speed-popover{
    min-width: 65px;
  }
  .speed-ul{
    list-style: none;
    padding: 0;
    margin: 0;
    li{
      padding: 2px 5px;
      line-height: 22px;
      cursor: pointer;
      &:hover{
        background: #ccc;
      }
    }
  }
  .canvas-toolbar {
    width: 100%;
    height: 40px;
    padding: 5px;
    position: absolute;
    bottom: 0;
    .el-button {
      width: 45px;
      padding: 5px;
      margin: 0 2px;
      float: left;
      .svg-icon, >>>i {
        font-size: 16px;
      }
    }
  }
  .progress-bar {
    width: calc(100% - 320px);
    min-width: 100px;
    padding: 5px 0;
    margin-left: 10px;
    position: relative;
    float: left;
  }
  .progress-mask {
    width: 100%;
    height: 20px;
    display: inline-block;
    background: #cacaca;
    position: absolute;
    top: 5px;
    right: 0;
    border-radius: 10px;
    cursor: not-allowed;
  }
</style>
