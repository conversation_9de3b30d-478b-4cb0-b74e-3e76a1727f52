<!--
  /**
  * echarts普通散点图图封装，可根据实际需求修改配置项
  * 调用示例：
      <line-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      chartData = [
            {
              symbolSize: 10,
              data: [120, 200, 150, 80, 70, 21, 432],
              type: 'scatter'
            },
            {
              symbolSize: 10,
              data: [220, 230, 140, 10, 30, 31, 34],
              type: 'scatter'
            },
            {
              symbolSize: 10,
              data: [12, 43, 12, 43, 13, 432, 122],
              type: 'scatter'
            }
          ]
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'
import { showSaveImage } from '@/components/ECharts/saveImage'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据
    chartData: {
      type: Array,
      required: true
    },
    yAxisName: {
      type: String,
      default() {
        return this.$t('components.number')
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      // console.log('chartData', chartData)
      // console.log('this.chartOption', this.chartOption)
      const series = []

      const defaultOpt = {
        title: {
          show: true,
          text: '', // 大标题
          subtext: '', // 子标题
          left: 'center'
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          confine: true,
          extraCssText: 'white-space: break-spaces;',
          formatter: function(params) {
            // 自定义鼠标悬停时，显示name+value
            // console.log('params', params)
            var result = '';
            params.forEach(function(item) {
              // console.log('item', item)
              if (item.componentType === 'series') {
                // 自定义显示的内容
                if (item.value !== '') {
                  result += item.marker + item.name + '：' + item.value + '<br/>'
                }
              }
            });
            return result;
          }
        },
        toolbox: {
          show: true,
          feature: {
            magicType: {
              show: true,
              type: ['line', 'bar'],
              title: {
                line: this.$t('components.switchToLine'),
                bar: this.$t('components.switchToBar')
              },
              emphasis: {
                iconStyle: {
                  textAlign: 'right'
                }
              }
            },
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            data: [], // 横轴数据，这里以一月份的日期为例
            axisLabel: {
              formatter: function(value) {
                // 自定义标签格式，显示为 MM-DD 的格式
                var date = new Date(value);
                return echarts.format.formatTime('MM-dd', date);
              }
            }
          }
        ],
        yAxis: [
          {
            name: this.yAxisName,
            type: 'value',
            minInterval: 1,
            axisLabel: {
              interval: 0,
              formatter: '{value}'
            }
          }
        ],
        series: series,
        grid: {
          top: '25%',
          width: '85%',
          bottom: 50,
          left: '4%',
          containLabel: true
        },
        loadEnd: function(chart, chartInfo) {}
      }

      this.chart.setOption(deepMerge(defaultOpt, this.chartOption), true)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData)
      showSaveImage(this.chart)
    }
  }
}
</script>
