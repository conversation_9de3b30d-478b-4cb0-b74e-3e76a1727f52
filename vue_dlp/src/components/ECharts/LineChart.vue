<!--
  /**
  * echarts线性图封装，可根据实际需求修改配置项
  * 调用示例：
      <line-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      chartData = [
        { name: 'line1', data: [100, 120, 161, 134, 105, 160, 165] },
        { name: 'line2', data: [120, 82, 91, 154, 162, 140, 145] }
      ]
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'
import { showSaveImage } from '@/components/ECharts/saveImage'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据
    chartData: {
      type: Array,
      required: true
    },
    yAxisName: {
      type: String,
      default() {
        return this.$t('components.number')
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null,
      defaultOpt: {}
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      const series = []
      const legendData = []

      if (chartData.length) {
        chartData.forEach(data => {
          series.push({
            name: '',
            smooth: 0.3,
            type: 'line',
            data: [],
            animationDuration: 2800,
            animationEasing: 'quadraticOut',
            ...data
          })
          legendData.push(data.name)
        })
      }

      this.defaultOpt = {
        title: {
          show: true,
          text: '', // 大标题
          subtext: '', // 子标题
          top: '5%',
          left: 'center'
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          confine: true,
          extraCssText: 'white-space: break-spaces;'
          // formatter: "{a} <br/>{b} : {c} ({d}%)",//格式化数据的显示
          // formatter: function(params) {
          //   let res1 = ''
          //   let res2 = ''
          //   let isTimeCompare = false
          //   if (params.length > 1 && params[0].data.time != params[1].data.time) { // 是同一业务维度，多个时间对比
          //     isTimeCompare = true
          //     res1 = '<p style="margin:5px 0;color:white;">' + params[0].seriesName + '</p>'
          //   } else { // 是同一时间维度，多个业务对比
          //     res1 = '<p style="margin:5px 0;color:white;">' + this.defaultOpt.formatTime(params[0].data.time) + '</p>'
          //   }
          //   for (var i = 0; i < params.length; i++) {
          //     var tempTime = this.defaultOpt.formatTime(params[i].data.time)
          //     res2 += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + params[i].color + ';"></span>'
          //     // res2 += (isTimeCompare ? tempTime : params[i].seriesName) + ' : ' + params[i].data.value + '<br>'
          //     res2 += (isTimeCompare ? tempTime : params[i].seriesName) + ' : ' + params[i].data + '<br>'
          //   }
          //   return res1 + res2
          // }
        },
        legend: {
          show: true,
          type: 'scroll',
          pageIconColor: '#008acd', // 翻页按钮的颜色
          data: legendData,
          bottom: 0,
          x: 'center', // 图例所在位置：left、center
          padding: [0, 20, 5, 20],
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2281e4'
          }
        },
        toolbox: {
          show: true,
          feature: {
            magicType: {
              show: true,
              type: ['line', 'bar'],
              title: {
                line: this.$t('components.switchToLine'),
                bar: this.$t('components.switchToBar')
              },
              emphasis: {
                iconStyle: {
                  textAlign: 'right'
                }
              }
            },
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        xAxis: [
          {
            boundaryGap: false,
            name: this.$t('text.date'),
            type: 'category',
            splitLine: {
              // 柱状图， 不同坐标的柱子之间用虚线隔开
              show: false,
              lineStyle: {
                color: '#ccc',
                type: 'dashed'
              }
            },
            // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            data: [],
            axisLabel: {
              formatter: function(value, index) {
                // return this.defaultOpt.formatTime(value, true)
                // 一行显示10个字符，最多显示3行，超过显示...
                var maxLength = 10;
                value = value.length > 20 ? value.substring(0, 17) + '...' : value
                var valLength = value.length;
                var rowN = Math.ceil(valLength / maxLength);
                if (rowN > 1) {
                  // 根据行数计算每行的字符数
                  const partLength = Math.floor(valLength / rowN);
                  const result = [];
                  let startIndex = 0;
                  // 切割并放入result
                  for (let i = 0; i < rowN; i++) {
                    if (i == rowN - 1) {
                      result.push(value.substring(startIndex));
                    } else {
                      result.push(value.substring(startIndex, startIndex + partLength));
                    }
                    startIndex += partLength;
                  }
                  return result.join('\n')
                } else {
                  return value;
                }
              }
            }
          }
        ],
        yAxis: [
          {
            name: this.yAxisName,
            type: 'value',
            minInterval: 1,
            axisLabel: {
              interval: 0,
              formatter: '{value}'
            }
          }
        ],
        series: series,
        grid: {
          top: '25%',
          width: '85%',
          bottom: 50,
          left: '4%',
          containLabel: true
        },
        // dataZoom: [
        //   {
        //     show: false,
        //     type: 'inside',
        //     start: 0,
        //     end: 100
        //   }
        // ],
        loadEnd: function(chart, chartInfo) {},
        formatTime: function(value, isn) {
          if (value) {
            if (value.length == 'yyyyMMddHHmm'.length) {
              const sp = isn ? '\n' : ' '
              return value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6, 8) +
                sp + value.substring(8, 10) + ':' + value.substring(10, 12)
            } else if (value.length == 'yyyyMMddHH'.length) {
              const sp = isn ? '\n' : ' '
              return value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6, 8) +
                        sp + value.substring(8, 10) + ':00'
            } else if (value.length == 'yyyyMMdd'.length) {
              return value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6, 8)
            } else if (value.length == 'yyyyMM'.length) {
              return value.substring(0, 4) + this.$t('text.year') + value.substring(4, 6) + this.$t('text.month')
            } else if (value.length == 'yyyyQ'.length) {
              return value.substring(0, 4) + this.$t('text.year') + value.substring(4, 5) + this.$t('text.quarter')
            } else if (value.length == 'yyyy'.length) {
              return value.substring(0, 4)
            } else {
              return value
            }
          }
          return ''
        }
      }
      this.chart.setOption(deepMerge(this.defaultOpt, this.chartOption), true)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      // 监听点击工具栏事件（切换为折线图/切换为柱状图点击时）
      this.chart.on('magicTypeChanged', (params) => {
        // console.log('params', params)
        // console.log('this.chartOption', JSON.parse(JSON.stringify(this.chartOption)))
        let isBar = params.currentType === 'bar';
        if (isBar) {
          // 如果切换为柱状图，且一个坐标对应多个柱子(且多个柱子是平铺而不是堆叠)时，则用虚线隔开
          if (Object.keys(this.chartOption).length !== 0 && this.chartOption.series && this.chartOption.series.length > 1) {
            if (this.chartOption.series[0].hasOwnProperty('stack')) {
              isBar = false
            } else {
              isBar = true
            }
          } else {
            isBar = false
          }
        }
        this.chart.setOption({
          xAxis: {
            splitLine: {
              show: isBar // 如果是柱状图，则显示splitLine；如果是折线图，则不显示
            }
          },
          series: [{
            type: params.currentType === 'bar' ? 'bar' : 'line' // 根据新的类型设置series的type
          }]
        });
      })

      this.setOptions(this.chartData)
      showSaveImage(this.chart)
    }
  }
}
</script>
