import { buildDownloadFileByName } from '@/utils/download/helper'

/**
 * 在控制台下载管理器中显示ECharts工具栏保存图片事件
 * @param chart ECharts实例
 * @param filename 下载列表中显示的文件名
 */
export function showSaveImage(chart, filename) {
  chart.getZr().on('click', function(event) {
    // 该监听器正在监听一个`zrender 事件`
    // console.log('zrender event', event)
    const option = chart.getOption();
    const saveAsImageTitle = option.toolbox[0].feature.saveAsImage.title
    if (event.target && event.target.__title === saveAsImageTitle) {
      // console.log('zrender event', '保存为图片')
      event.event.stopPropagation()

      const name = filename || `${option.title[0].text}.png`
      const file = buildDownloadFileByName(name, false)
      file.percent = 1
      file.finished = true
    }
  })
}
