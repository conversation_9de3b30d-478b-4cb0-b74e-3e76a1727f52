<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce } from '@/utils'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    chartData: {
      type: Object,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    resize() {
      window.setTimeout(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 500)
    },
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions({ yAxisData, xAxisData } = {}) {
      // const changeOption = {
      //   xAxis: {
      //     data: xAxisData
      //   },
      //   yAxis: {
      //     data: yAxisData
      //   }
      // }
      // let chartOption = this.chartOption
      // chartOption = deepMerge(chartOption, changeOption)
      const defaultOpt = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          itemStyle: {
            normal: {
              label: {
                show: true
              }
            }
          }
        },
        grid: {
          height: '80%',
          width: '90%',
          top: 20,
          left: 45
        },
        xAxis: {
          type: 'category',
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#315070'],
              width: 1,
              type: 'solid'
            }
          },
          data: xAxisData
        },
        yAxis: {
          min: 0,
          max: function(value) {
            const max = 100
            var num = Math.ceil((value.max * 2 / 5)) * 5
            return num > max ? max : num
          },
          type: 'value',
          axisLabel: {
            formatter: ' {value} %'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#315070'],
              width: 1,
              type: 'solid'
            }
          }
        },
        series: [{
          name: this.$t('components.CPUusage'),
          type: 'line',
          smooth: false,
          areaStyle: {
            normal: {}
          },
          data: yAxisData,
          markLine: {
            symbol: 'none', // 去掉警戒线最后面的箭头
            label: {
              position: 'end', // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
              formatter: this.$t('components.cordon')
            }
            // data: [{
            //   silent: false, // 鼠标悬停事件  true没有，false有
            //   lineStyle: { // 警戒线的样式  ，虚实  颜色
            //     type: 'solid',
            //     color: 'rgba(238, 99, 99)'
            //   },
            //   name: this.$t('components.cordon'),
            //   yAxis: 20
            // }]
          }
        }, // 超下限的数据用三角形展示数据
        {
          type: 'scatter',
          data: yAxisData,
          itemStyle: {
            normal: {
              label: {
                position: 'top',
                show: false,
                color: 'white',
                backgroundColor: 'black'
              }
            }
          }
        }]
      }
      this.chart.setOption(defaultOpt)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions(this.chartData)
    }
  }
}
</script>
