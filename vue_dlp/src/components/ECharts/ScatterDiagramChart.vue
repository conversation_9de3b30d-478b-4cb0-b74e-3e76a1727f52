<!--
  /**
  * echart散点图(存在一二三四象限)封装，可根据实际需求修改配置项
  * 调用示例：
      <scatter-diagram-chart
        :chart-source="chartSource"  // chart数据，散点图各个点
        :chart-data="chartData"       // chart数据，散点图四象限坐标轴
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      // 散点图每一个点的位置
      source: [
        [2, 3],
        [-3, 2],
        [-2, -3],
        [4, -3]
      ]
    // 散点图四象限坐标轴
      chartData =  [
        { value: 0, label: '高危', color: '#e06343' },
        { value: 1, label: '严重', color: '#b55dba' },
        { value: 2, label: '中等', color: '#37A2DA' },
        { value: 3, label: '一般', color: '#37a354' }
      ]
  */
-->
<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'

export default {
  name: 'ScatterDiagramChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chartData数据(四象限)
    chartData: {
      type: Array,
      default() {
        return []
      }
    },
    // chartSource数据(散点图每一个点的坐标)
    chartSource: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val, this.chartSource)
      }
    },
    chartSource: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData, this.chartSource)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData, this.chartSource)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      // 后续优化，根据容器的变化实现图表resize
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData, chartSource) {
      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
        },
        dataset: [
          {
            source: chartSource
          },
          {
            transform: {
              config: {
                clusterCount: 6,
                outputType: 'single',
                outputClusterIndexDimension: 2
              }
            }
          }
        ],
        visualMap: {
          type: 'piecewise',
          top: 'middle',
          min: 0,
          max: 4,
          right: 10,
          inverse: false,
          splitNumber: 4,
          dimension: 4,
          textStyle: {
            color: '#2281e4'
          },
          pieces: chartData
        },
        xAxis: {
          splitLine: {// 去除网格线
            show: false
          },
          splitArea: { show: false }// 去除网格区域
        },
        yAxis: {
          splitLine: {// 去除网格线
            show: false
          },
          splitArea: { show: false }// 去除网格区域
        },
        series: {
          type: 'scatter',
          symbolSize: 12 // 散点图点的大小
        }
      }
      this.chart.setOption(deepMerge(defaultOpt, this.chartOption))
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions(this.chartData, this.chartSource)
    }
  }
}
</script>

<style lang="scss">
  .tooltip-dot{
    display: inline-block;
    margin-right: 5px;
    border-radius: 10px;
    width: 9px;
    height: 9px;
  }
  .dataView{
    width:100%;
    height:100%;
    border-top: 1px solid #000;
    padding: 10px;
    overflow: auto;
    color: #666;
  }
  .dataView table{
    width:100%;
    border:1px solid #666;
    border-collapse: collapse;
    font-size: 14px;
  }
  .dataView table th,.dataView table td{
    padding: 2px 10px;
    border: 1px solid #aaa;
  }
</style>
