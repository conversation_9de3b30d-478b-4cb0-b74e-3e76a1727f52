<!--
  /**
  * 双层饼状图
  * echarts双层饼状图封装，可根据实际需求修改配置项
  * 调用示例：
      <pie-double-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

       pieChartData: {
          outerLayer: [
            { value: 300, name: '2023加密数量' },
            { value: 500, name: '2024加密数量' }
          ],
          innerLayer: [
            { value: 180, name: '2023成功数' },
            { value: 120, name: '2023失败数' },
            { value: 160, name: '2024成功数' },
            { value: 340, name: '2024失败数' }
          ]
        },
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'
import { showSaveImage } from '@/components/ECharts/saveImage'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '200px'
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据，[{ value: 1, name: 'name1' },{ value: 2, name: 'name2' }]
    chartData: {
      type: Object,
      default() {
        return {}
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        // console.log('11111111111111', JSON.parse(JSON.stringify(val)))
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        // console.log('2222222222222222', this.chartData)
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      // 后续优化，根据容器的变化实现图表resize
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      // console.log('chartData11111111111111111', JSON.parse(JSON.stringify(chartData)))
      const legendData = []
      // 双层饼图图例数据处理
      const duplicateMap = {}
      if (chartData.innerLayer.length) {
        chartData.innerLayer.map(item => {
          legendData.push(item.name)
        })
      }
      // console.log('legendData饼图', legendData)
      // 图例、图标label的字数限制值
      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          left: 'center',
          top: '0'
          // x: 'left'
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(16,16,16,0.7)',
          confine: true,
          extraCssText: 'white-space: break-spaces;'
        },
        legend: {
          show: true,
          bottom: '0',
          orient: 'horizontal', // 图例朝向：horizontal横向 vertical竖向
          data: legendData,
          selected: legendData,
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2281e4'
          }
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        series: [
          {
            name: '内层',
            type: 'pie',
            // roseType: 'radius',
            radius: ['30%', '50%'], // 饼图大小
            center: ['49%', '50%'], // 饼图中心点
            label: {
              padding: 0,
              fontSize: 9,
              normal: {
                formatter: (params) => {
                  // 如果标签角度小于20度，不显示标签
                  if (params.percent < 0.2) {
                    return '...';
                  }
                  // 如果名称长度超过10个字符，截断并添加省略号
                  if (params.name.length > 10) {
                    return params.name.substring(0, 10) + '...';
                  } else {
                    return params.name;
                  }
                }
              },
              position: 'outside' // 设置标签显示的位置
            },
            labelLine: {
              normal: { length: 5, length2: 5 }
            },
            data: chartData.innerLayer,
            startAngle: 60,
            // minAngle: 15,
            avoidLabelOverlap: true,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          },
          {
            name: '外层',
            type: 'pie',
            // roseType: 'radius',
            radius: ['10%', '50%'], // 饼图大小
            center: ['49%', '50%'], // 饼图中心点
            label: {
              padding: 0,
              fontSize: 9,
              position: 'outside' // 设置标签显示的位置
            },
            // 修改饼图边上指示线的长度
            labelLine: {
              normal: { length: 35, length2: 35 }
            },
            data: chartData.outerLayer,
            // 设置饼图标签不重叠
            avoidLabelOverlap: true,
            startAngle: 60,
            // minAngle: 15,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      }
      const mergeOption = deepMerge(defaultOpt, this.chartOption)
      this.duplicateFormatter(mergeOption, duplicateMap)
      this.chart.setOption(mergeOption)
    },
    // 对 option 中的 legend 和 series 进行 重复name 的处理
    duplicateFormatter(option, duplicateMap) {
      // console.log('option', option)
      option.legend.data.splice(0)
      const series = Array.isArray(option.series) ? option.series : [option.series]
      // console.log('series', series)
      series.forEach((item) => {
        const seriesData = item.data
        if (seriesData) {
          seriesData.forEach((sData) => {
            const name = sData.name
            const id = sData.id
            if (!duplicateMap[name]) {
              // 非重复的name，直接以 name 作为 key
              duplicateMap[name] = name
            } else {
              // 重复的name，以 name + id 作为 key
              const newName = name + id
              duplicateMap[newName] = name
              // 修改 name， 并增加 duplicateName
              sData.name = newName
              sData.duplicateName = name
            }
          })
          // 添加图例
          option.legend.data.push(...seriesData.map(item => item.name))
        }
      });
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData)
      showSaveImage(this.chart)
    }
  }
}
</script>

<style lang="scss">
  .tooltip-dot{
    display: inline-block;
    margin-right: 5px;
    border-radius: 10px;
    width: 9px;
    height: 9px;
  }
  .dataView{
    width:100%;
    height:100%;
    border-top: 1px solid #000;
    padding: 10px;
    overflow: auto;
    color: #666;
  }
  .dataView table{
    width:100%;
    border:1px solid #666;
    border-collapse: collapse;
    font-size: 14px;
  }
  .dataView table th,.dataView table td{
    padding: 2px 10px;
    border: 1px solid #aaa;
  }
</style>
