<!--
  /**
  * 双轴柱状图
  * echarts双轴柱状图封装，可根据实际需求修改配置项
  * 调用示例：
    <bar-double-chart
    :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
    />

      chartOption = {
          title: {
            text: '文件加解密环比数量统计'
          },
          calculable: true,
          yAxis: [
            // 左轴
            {
              type: 'category',
              gridIndex: 0,
              data: ['成功数', '加密数', '失败数']
            },
            {
              gridIndex: 1,
              type: 'category',
              data: ['成功数', '加密数', '失败数']
            },
            {
              gridIndex: 2,
              type: 'category',
              nameGap: 25,
              // 轴线
              axisLine: {
                show: true
              },
              // 分割线
              axisTick: {
                show: false
              },
              data: ['成功数', '加密数', '失败数']
            }
          ],
          series:
            [
              // 左侧轴
              {
                name: '2023',
                type: 'bar',
                // 对应的左侧轴x轴索引
                xAxisIndex: 0,
                yAxisIndex: 0,
                // 柱体收缩，不会太粗
                barMaxWidth: '40%',
                data: [100, 200, 100]
              },
              // 右侧轴
              {
                name: '2024',
                type: 'bar',
                xAxisIndex: 1,
                yAxisIndex: 1,
                barMaxWidth: '40%',
                data: [100, 250, 110]
              }
            ]
        }
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import { convert, debounce, deepMerge, formatSeconds } from '@/utils'
require('echarts/theme/macarons') // echarts theme
export default {
  name: 'BarDoubleChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据
    chartData: {
      type: Array,
      required: true
    },
    xAxisName: {
      type: String,
      default() {
        return this.$t('components.terminal')
      }
    },
    yAxisName: {
      type: String,
      default() {
        return this.$t('components.number')
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  created() {
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      if (this.chartOption.series.length >= 2) {
        // 两组data数据
        var data1 = this.chartOption.series[0].data;
        var data2 = this.chartOption.series[1].data;
        // 获取到最大值，赋值给坐标轴，保证两边坐标轴最大值相等，从而保证对比效果
        var maxValue = Math.max(...data1, ...data2)
      }
      // 基本柱状图
      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: (d) => {
            // console.log('d11111', d)
            // console.log('chartOption.series[0]', chartOption.series[0])
            if (this.chartOption.series.length > 0) {
              // 鼠标悬停数据处理(如果是文件大小或者时长，需要处理单位)
              const objFlag = this.chartOption.series[0].flag
              const res = d[0].name + '<br>';
              // console.log('res', res)
              let marker = '';
              const marker1 = "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#1171C9;'></span>";
              const marker2 = "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#F76B1C;'></span>";
              if (d[0].seriesName === '当前') {
                marker = marker2
              } else {
                marker = marker1
              }
              let number = d[0].value
              if (objFlag === 'size') {
                number = convert(d[0].value)
              } else if (objFlag === 'time') {
                number = formatSeconds(d[0].value)
              }
              return res + marker + d[0].seriesName + ' : ' + number
            }
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          confine: true,
          extraCssText: 'white-space: break-spaces;'
        },
        legend: {
          // 双轴图如果显示图例，图例关闭时，页面坐标会很丑，所以不显示图例
          show: true,
          x: 'center', // 图例所在位置：left、center
          y: 'bottom',
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2281e4'
          }
        },
        calculable: true,
        xAxis: [
          // 左侧轴
          {
            type: 'value',
            max: maxValue,
            // 应用gird容器样式（指向grid属性）
            gridIndex: 0,
            // 是否反转 若不反转则是图1效果
            inverse: true,
            // 是否显示轴线
            axisLine: {
              show: true
            },
            axisLabel: {
              interval: 0,
              padding: 0,
              fontSize: 9,
              // 倾斜的程度
              rotate: 30
            },
            // 是否显示分割线
            splitLine: {
              show: false
            },
            // 控制隔区域(柱状图默认有个类似背景的阴影去掉)
            splitArea: {
              show: false
            }
          },
          // 右侧轴
          {
            type: 'value',
            max: maxValue,
            gridIndex: 1,
            axisLine: {
              show: true
            },
            axisLabel: {
              interval: 0,
              padding: 0,
              fontSize: 9,
              // 倾斜的程度
              rotate: 30
            },
            splitLine: {
              show: false
            },
            splitArea: {
              show: false
            }
          },
          // 底轴
          {
            gridIndex: 2,
            // 名称位置
            nameLocation: 'center',
            // 名称向下偏移
            nameGap: 40
          }
        ],
        yAxis: [],
        series: [],
        grid: [
          {
            // 边线
            show: false,
            // 左侧
            left: '15%',
            top: '15%',
            bottom: '20%',
            // 宽度
            width: '40%'
          },
          {
            show: false,
            left: '55%',
            top: '15%',
            bottom: '20%',
            width: '40%'
          },
          // 底轴 覆盖左右两个坐标轴
          {
            show: false,
            left: '15%',
            top: '15%',
            bottom: '20%',
            width: '80%'
          }
        ]
      }
      this.chart.setOption(deepMerge(defaultOpt, this.chartOption), true)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData)
    }
  }
};
</script>

