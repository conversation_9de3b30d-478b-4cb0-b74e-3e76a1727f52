<!--
  /**
  * echarts旭日图封装，可根据实际需求修改配置项
  * 调用示例：
      <sunburst-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />
      chartsTerminalDatas: [
      {
        name: 'T1',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: 'T2',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      }
    ],
    chartTerminalOption: {
      title: {
        text: '终端严重程度数量图',
        left: 'center',
        'subtext': '前2名'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          radius: [0, '45%'],
          center: ['50%', '35%'],
          label: {
            rotate: 'radial'
          }
        }
      ]
    },
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'

export default {
  name: 'SunburstChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据，[{ value: 1, name: 'name1' },{ value: 2, name: 'name2' }]
    chartData: {
      type: Array,
      default() {
        return []
      }
    },
    chartIndicator: {
      type: Array,
      default() {
        return []
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartIndicator: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      // 后续优化，根据容器的变化实现图表resize
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      let legendData = []
      if (chartData.length) {
        legendData = chartData.map(item => {
          return item.name
        })
      } else if (chartData['legend']) {
        legendData = chartData.legend['data']
      }

      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
          // x: 'left'
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(16,16,16,0.7)'
        },
        textStyle: {
          color: '#000'
        },
        legend: {
          show: true,
          bottom: '10',
          orient: 'horizontal', // 图例朝向：horizontal横向 vertical竖向
          data: legendData, // 字符串数组，如'直接访问','邮件营销'
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2281e4'
          }
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        series: [
          {
            name: '',
            type: 'sunburst',
            data: chartData,
            radius: [0, '70%'],
            label: {
              rotate: 'radial'
            }
          }
        ]
      }
      this.chart.setOption(deepMerge(defaultOpt, this.chartOption))
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData)
    }
  }
}
</script>

<style lang="scss">
  .tooltip-dot{
    display: inline-block;
    margin-right: 5px;
    border-radius: 10px;
    width: 9px;
    height: 9px;
  }
  .dataView{
    width:100%;
    height:100%;
    border-top: 1px solid #000;
    padding: 10px;
    overflow: auto;
    color: #666;
  }
  .dataView table{
    width:100%;
    border:1px solid #666;
    border-collapse: collapse;
    font-size: 14px;
  }
  .dataView table th,.dataView table td{
    padding: 2px 10px;
    border: 1px solid #aaa;
  }
</style>
