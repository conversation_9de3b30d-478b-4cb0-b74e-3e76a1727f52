<!--折线图和饼图放在一个图中，展示CPU占比和趋势-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce } from '@/utils'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    chartData: {
      type: Object,
      default() {
        return []
      }
    },
    pieData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    pieData: {
      deep: true,
      handler(val) {
        // this.setOptions(val)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    resize() {
      window.setTimeout(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 500)
    },
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions({ yAxisData, xAxisData } = {}) {
      // let chartOption = this.chartOption
      // chartOption = deepMerge(chartOption, changeOption)
      // console.log('piedata', this.pieData)
      const defaultOpt = {
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          itemStyle: {
            normal: {
              label: {
                show: true
              }
            }
          }
        },
        grid: {
          height: '70%',
          width: '90%',
          top: 30,
          left: 45
        },
        xAxis: {
          type: 'category',
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#315070'],
              width: 1,
              type: 'solid'
            }
          },
          data: xAxisData
        },
        yAxis: {
          min: 0,
          max: function(value) {
            const max = 100
            var num = Math.ceil((value.max * 2 / 5)) * 5
            return num > max ? max : num
          },
          type: 'value',
          axisLabel: {
            formatter: ' {value} %'
          },
          // 控制隔区域
          splitArea: {
            show: false
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: ['#315070'],
              width: 1,
              type: 'solid'
            }
          }
        },
        series: [{
          name: this.$t('components.CPUusage'),
          type: 'line',
          smooth: false,
          areaStyle: {
            normal: {
              // color: 'none' // 设置为'none'或不设置areaStyle
            }
          },
          data: yAxisData,
          markLine: {
            symbol: 'none', // 去掉警戒线最后面的箭头
            label: {
              position: 'end', // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
              formatter: this.$t('components.cordon')
            }
            // data: [{
            //   silent: false, // 鼠标悬停事件  true没有，false有
            //   lineStyle: { // 警戒线的样式  ，虚实  颜色
            //     type: 'solid',
            //     color: 'rgba(238, 99, 99)'
            //   },
            //   name: this.$t('components.cordon'),
            //   yAxis: 20
            // }]
          }
        }, // 超下限的数据用三角形展示数据
        {
          type: 'scatter',
          data: yAxisData,
          itemStyle: {
            normal: {
              label: {
                position: 'top',
                show: false,
                color: 'white',
                backgroundColor: 'black'
              }
            }
          }
        },
        {
          name: '',
          type: 'pie',
          tooltip: {
            trigger: 'item'
          },
          radius: ['8%', '15%'],
          center: ['87%', '12%'],
          color: ['#45C2E0', '#FFC851'],
          // 修改饼图指标线长度
          labelLine: {
            normal: { length: 2, length2: 2 }
          },
          data: this.pieData
        }]
      }
      this.chart.setOption(defaultOpt)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions(this.chartData)
    }
  }
}
</script>

