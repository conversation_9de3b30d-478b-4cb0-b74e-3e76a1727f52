<!--
  /**
  * echarts雷达图封装，可根据实际需求修改配置项
  * 调用示例：
      <pie-chart
        :chart-indicator="chartIndicator"  // chart数据，指示器
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      雷达图的指示器，用来指定雷达图中的多个变量（维度），如下示例。
      indicator: [
      { name: '工作', max: 6500 },
      { name: '浏览新闻', max: 16000 },
      { name: '待机', max: 30000 },
      { name: '聊天', max: 38000 },
      { name: '追剧', max: 52000 },
      { name: '网购', max: 25000 }
    ]
      chartData =  [
        {
          name: '加班分析',
          value: [4200, 3000, 20000, 35000, 50000, 18000],
          areaStyle: {  // 可要可不要，圈起来的颜色
            color: '#32dadd'
          }
        }
      ]
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'
import { showSaveImage } from '@/components/ECharts/saveImage'

export default {
  name: 'RadarChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据，[{ value: 1, name: 'name1' },{ value: 2, name: 'name2' }]
    chartData: {
      type: Array,
      default() {
        return []
      }
    },
    chartIndicator: {
      type: Array,
      default() {
        return []
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val, this.chartIndicator)
      }
    },
    chartIndicator: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData, this.chartIndicator)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData, this.chartIndicator)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      // 后续优化，根据容器的变化实现图表resize
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData, chartIndicator) {
      let legendData = []
      if (chartData.length) {
        legendData = chartData.map(item => {
          return item.name
        })
      } else if (chartData['legend']) {
        legendData = chartData.legend['data']
      }

      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
          // x: 'left'
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(16,16,16,0.7)'
        },
        legend: {
          show: true,
          bottom: '10',
          orient: 'horizontal', // 图例朝向：horizontal横向 vertical竖向
          data: legendData, // 字符串数组，如'直接访问','邮件营销'
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2281e4'
          }
        },
        radar: {
          shape: 'circle',
          indicator: chartIndicator,
          radius: 90,
          startAngle: 90,
          splitNumber: 4,
          // 设置隐藏蜘蛛网
          splitLine: {
            // 'show': false
            lineStyle: {
              color: '#c8b2f4' // 每个圈的边框颜色
            }
          },
          splitArea: {
            // 'show': false
            areaStyle: {
              color: ['transparent', 'transparent', 'transparent', 'transparent'], // 每个圆的背景颜色
              shadowColor: '#32dadd', // 每个圈的阴影颜色
              shadowBlur: 10
            }
          },
          axisLine: {
            lineStyle: {
              color: '#63c2ff' // 坐标线颜色
            }
          }
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        series: [
          {
            name: '',
            type: 'radar',
            data: chartData,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
            // 设置点与点之间没有连线
            // lineStyle: {
            //   normal: {
            //     width: 0
            //   }
            // },
            // symbol: 'circle',
            // symbolSize: 10
          }
        ]
      }
      this.chart.setOption(deepMerge(defaultOpt, this.chartOption))
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData, this.chartIndicator)
      showSaveImage(this.chart)
    }
  }
}
</script>

<style lang="scss">
  .tooltip-dot{
    display: inline-block;
    margin-right: 5px;
    border-radius: 10px;
    width: 9px;
    height: 9px;
  }
  .dataView{
    width:100%;
    height:100%;
    border-top: 1px solid #000;
    padding: 10px;
    overflow: auto;
    color: #666;
  }
  .dataView table{
    width:100%;
    border:1px solid #666;
    border-collapse: collapse;
    font-size: 14px;
  }
  .dataView table th,.dataView table td{
    padding: 2px 10px;
    border: 1px solid #aaa;
  }
</style>
