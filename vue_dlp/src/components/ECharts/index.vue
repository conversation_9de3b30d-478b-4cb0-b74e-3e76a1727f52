<!--
  /**
  * 提供快速配置多个chart的组件
  * 调用示例：
      <e-charts
        :charts-option="chartsOption"   // 图表配置，可同时配置多个图表，数据类型：Array
      />
      chartsOption中对象元素的属性：
        type：图表类型，可选值 'line' 'bar' 'pie'
        option：该图表的配置属性，与 echarts一致
        data：该图表的数据，即 series.data ， 也可通过getDataApi 来动态获取data
        getDataApi：动态获取data的api
        para：获取data的方法的参数
        col：图表在容器中所占的比例， 可选值 1-24

      chartsOption: [
        { type: 'line', option: {}, data: lineData, col: 24 },
        { type: 'pie', option: { toolbox: { show: true }}, data: pieData1, col: 12 },
        { type: 'bar', option: {}, data: barData, col: 12 },
        { type: 'pie', option: { toolbox: { show: false }}, data: pieData1 },
        { type: 'bar', option: {}, data: barData },
        { type: 'line', option: {}, data: lineData }
      ]
  */
-->
<template>
  <div class="echarts-container">
    <el-row :gutter="32">
      <el-col v-for="(chart, index) in chartsOption" :key="index" :xs="24" :sm="24" :lg="chart.col || 8">
        <div class="chart-wrapper">
          <pie-chart v-if="chart.type === 'pie'" ref="pie" v-loading="chart.loading || false" :width="width || '100%'" :height="height || '450px'" :chart-data="chartsDatas[index]" :chart-option="chart.option || {}" :click="chart.click"/>
          <bar-chart v-if="chart.type === 'bar'" ref="bar" v-loading="chart.loading || false" :width="width || '100%'" :height="height || '350px'" :chart-data="chartsDatas[index]" :chart-option="chart.option || {}" :click="chart.click" :x-axis-name="xAxisName" :y-axis-name="yAxisName"/>
          <line-chart v-if="chart.type === 'line'" ref="line" v-loading="chart.loading || false" :width="width || '100%'" :height="height || '350px'" :chart-data="chartsDatas[index]" :chart-option="chart.option || {}" :click="chart.click" :y-axis-name="yAxisName" />
          <!-- 一些特殊报表的自定义实现 -->
          <!-- 风险报表散点图 -->
          <risk-report v-if="chart.data && chart.code == 60" :data="chart.data" :sub-text="chart.subText" :width="width || '100%'" :height="height || '350px'"/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from './LineChart'
import PieChart from './PieChart'
import BarChart from './BarChart'

import RiskReport from './RiskReport'

export default {
  name: 'ECharts',
  components: {
    LineChart,
    PieChart,
    BarChart,
    RiskReport
  },
  props: {
    chartsOption: {
      type: Array,
      default() {
        return []
      }
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    xAxisName: {
      type: String,
      default() {
        return ''
      }
    },
    yAxisName: {
      type: String,
      default() {
        return this.$t('components.number')
      }
    }
  },
  data() {
    return {
      // chartsDatas: []
    }
  },
  computed: {
    chartsDatas() {
      return this.chartsOption.map((el, index) => {
        if (el.getDataApi) {
          el.getDataApi(el.para).then(res => {
            return res
          })
        } else {
          const series = el.option && el.option.serise
          return (series && series.data) || []
        }
      })
    }
  },
  methods: {
    reloadData(index) {
      const chartOpt = this.chartsOption[index]
      chartOpt.getDataApi(chartOpt.para).then(res => {
        this.chartsDatas.splice(index, 1, res)
      })
    }
  }
}
</script>

<style lang="scss">
  .echarts-container .el-col{
    padding: 10px 16px;
  }
</style>
