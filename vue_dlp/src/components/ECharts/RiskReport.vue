<template>
  <div :style="{height:height,width:width}">
    <scatter-diagram-chart
      :chart-source="temp.scatterChartSource"
      :chart-option="temp.scatterChartOption"
      :y-avg="temp.scatterYAvg"
      :median="temp.median"
      style="width: 100%;height: 100%;"
    />
  </div>
</template>

<script>

import ScatterDiagramChart from '@/views/report/sensitiveControl/riskStatement/ScatterDiagramChart'
export default {
  name: 'RiskReportChart',
  components: { ScatterDiagramChart },
  props: {
    data: {
      type: Object,
      required: true
    },
    subText: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    }
  },
  data() {
    return {
      temp: {
        scatterChartSource: [],
        scatterYAvg: 0,
        scatterChartOption: {
          title: {
            text: this.$t('route.RiskReport'),
            subtext: '',
            left: 'center'
          },
          toolbox: {
            show: true,
            feature: {
              saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
            }
          }
        }
      }
    }
  },
  created() {
    if (!this.data) {
      return
    }
    // 风险象限
    this.temp.scatterChartSource = (this.data.riskScatterList || []).map(v => [v.riskLevel - 1, v.eventCount, v.userName, v.userId, v.terminalName, v.termId])
    this.temp.scatterYAvg = Math.max(...(this.temp.scatterChartSource || []).map(v => v[1])) / 2
    this.temp.scatterChartOption.title.subtext = this.subText
  }
}
</script>
