
<!--
  /**
  * 数据全景仪表盘
  * 调用示例：
     <data-panorama-gauge
          v-if="today"
          :chart-data="temp.onlineTimeDatas"   // 时间转成秒数
        />
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import { debounce } from '@/utils'
require('echarts/theme/macarons') // echarts theme
export default {
  name: 'DataPanoramaGauge',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chart数据
    chartData: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  created() {
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      // console.log('????chartData', chartData)
      // 基本柱状图
      const defaultOpt = {
        title: {
          text: '终端在线时长统计',
          subtext: '',
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: '时间',
            type: 'gauge',
            min: 0, // 最小值
            max: 86400, // 最大值，即24小时对应的秒数
            splitNumber: 24, // 分割成24个区间，每个区间代表1小时
            // 圆弧的形状(大小)
            radius: '55%',
            center: ['50%', '60%'], // 圆心位置，第一个值是水平方向，第二个值是垂直方向
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.25, '#66cc99'], // 0-6小时
                  [0.5, '#9ACD32'], // 6-12小时
                  [0.75, '#ffcc33'], // 12-18小时
                  [1, '#ff6600'] // 18-24小时
                ]
              }
            },
            // 轴线
            axisTick: {
              show: true,
              length: 10,
              splitNumber: 4, // 每个小时再细分4个小刻度，即每15分钟一个小刻度
              lineStyle: {
                color: 'auto'
              }
            },
            // 分割线
            splitLine: {
              show: true,
              length: 15,
              lineStyle: {
                color: 'auto'
              }
            },
            // 刻度标签
            axisLabel: {
              formatter: function(value) {
                const hours = value / 3600; // 将秒转换为小时
                if (hours % 2 === 0) { // 只显示偶数小时的标签
                  return `${hours}h`;
                } else {
                  return '';
                }
              },
              fontSize: 10, // 文字大小
              distance: 5 // 调整此值以改变刻度与文字间的距离
            },
            pointer: {
              width: 5, // 指针宽度
              length: '65%' // 指针长度
            },
            detail: {
              formatter: function(value) {
                const hours = Math.floor(value / 3600);
                const minutes = Math.floor((value % 3600) / 60);
                const seconds = value % 60;
                return `${hours}小时${minutes}分${seconds}秒`;
              },
              fontSize: 11,
              offsetCenter: [0, '70%']
            },
            data: [{ value: chartData }] // 输入时间为x小时x分x秒对应的秒数,如12小时31分48秒对应的秒数为45108
          }
        ]
      };
      this.chart.setOption(defaultOpt, true)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions(this.chartData)
    }
  }
};
</script>

