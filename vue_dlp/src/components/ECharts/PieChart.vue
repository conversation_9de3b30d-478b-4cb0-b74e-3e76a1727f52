<!--
  /**
  * echarts饼状图封装，可根据实际需求修改配置项
  * 调用示例：
      <pie-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      chartData = [
        { value: 100, name: 'pie1' },
        { value: 85, name: 'pie2' },
        { value: 45, name: 'pie3' }
      ]
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'
import { formatSeconds } from '@/utils'
import { showSaveImage } from '@/components/ECharts/saveImage'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据，[{ value: 1, name: 'name1' },{ value: 2, name: 'name2' }]
    chartData: {
      type: Array,
      default() {
        return []
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      // 后续优化，根据容器的变化实现图表resize
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      let legendData = []
      // 用于存储数据中重复name的map，以 name + id 作为key， name作为 value
      const duplicateMap = {}
      if (chartData.length) {
        legendData = chartData.map(item => {
          return item.name
        })
      } else if (chartData['legend']) {
        legendData = chartData.legend['data']
      }
      // 图例、图标label的字数限制值
      const labelLimit = 15
      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          top: '5%',
          left: 'center'
          // x: 'left'
        },
        tooltip: {
          trigger: 'item',
          // formatter: '{a} <br/>{b} : {c} ({d}%)',
          formatter: (params) => {
            // let res = d[0].axisValueLabel.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '<br>'
            const seriesName = params.seriesName ? `${params.seriesName}<br/>` : ''
            const dot = `<span class="tooltip-dot" style="background-color: ${params.color};"></span>`
            const name = duplicateMap[params.name].replace(/</g, '&lt;').replace(/>/g, '&gt;') || params.name.replace(/</g, '&lt;').replace(/>/g, '&gt;')
            // 截取：后的小标题
            let str = '';
            if (this.chartOption.title.description) {
              str = this.chartOption.title.description
            } else {
              str = this.chartOption.title.text
            }
            let index = -1;
            // console.log('11', str.indexOf('：'))
            // console.log('22', str.indexOf(':'))
            // 区分英文冒号和中文冒号
            if (str.indexOf('：') !== -1) {
              index = str.indexOf('：')
            }
            if (str.indexOf(':') !== -1) {
              index = str.indexOf(':')
            }
            const result = str.substr(index + 1, str.length);   // 04
            const isTimeData = this.chartOption.title.text === this.$t('report.appRunTimeLogCount') || this.chartOption.title.text === this.$t('report.webBrowseCount')
            const value = isTimeData ? formatSeconds(params.value) : params.value
            const percent = params.hasOwnProperty('percent') ? ` (${params.percent}%)` : ''
            // 终端版本报表、策略数量统计报表、终端操作系统报表不显示小标题，其余显示
            if (str === this.$t('report.termVerReport') || str === this.$t('report.stgNubCount') || str === this.$t('report.termOpSysMap')) {
              return `${seriesName}${dot}${name}：${value}${percent}`
            } else {
              return `${seriesName}${name}` + '\n' + result + `：${value}${percent}`
            }
          },
          // position: ['30%', '30%'],
          /**
           * point: 鼠标位置，如 [20, 40]。
             params: 同 formatter 的参数相同。
             dom: tooltip 的 dom 对象。
             rect: 只有鼠标在图形上时有效，是一个用x, y, width, height四个属性表达的图形包围盒。
             size: 包括 dom 的尺寸和 echarts 容器的当前尺寸，例如：{contentSize: [width, height], viewSize: [width, height]}
           */
          position: (point, params, dom, rect, size) => {
            // 鼠标位置
            const [x, y] = point
            // 鼠标位置在图表的左侧：true 左侧， false 右侧
            const mouseOnLeft = x < size.viewSize[0] / 2
            // tooltip 大小
            const [width, height] = size.contentSize
            // tooltip显示在左侧时的边界，超出边界则设置为 10
            const boundaryX = (x - width - 10) > 0 ? (x - width - 10) : 10
            const newX = mouseOnLeft ? x + 20 : boundaryX
            const newY = y - (height - 20) / 2
            return [newX, newY]
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          confine: true,
          extraCssText: 'white-space: break-spaces;'
        },
        legend: {
          show: true,
          bottom: '10',
          orient: 'horizontal', // 图例朝向：horizontal横向 vertical竖向
          data: legendData, // 字符串数组，如'直接访问','邮件营销'
          inactiveColor: '#666', // 图例关闭时的颜色。
          formatter: name => {
            const legendName = duplicateMap[name] || name
            return echarts.format.truncateText(legendName, 120, '14px Microsoft Yahei', '…')
          },
          tooltip: { show: true },
          textStyle: {
            color: '#2281e4'
          }
        },
        toolbox: {
          show: true,
          feature: {
            // dataView: {
            //   show: true,
            //   title: '数据视图',
            //   readOnly: true,
            //   optionToContent: function(opt) {
            //     var Data = opt.series[0].data ? opt.series[0].data : []
            //     var table = '<div class="dataView"><table><thead><tr>' +
            //                 '<th>' + opt.title[0].subtext + '</th>' +
            //                 '<th>违规数量</th>' +
            //                 '</tr></thead><tbody>'
            //     for (var i = 0, l = Data.length - 1; i <= l; i++) {
            //       table += '<tr>' +
            //                '<td>' + Data[i].name + '</td>' +
            //                '<td>' + Data[i].value + '</td>' +
            //                '</tr>'
            //     }
            //     table += '</tbody></table></div>'
            //     return table
            //   }
            // },
            // magicType: {
            //   show: true,
            //   type: ['pie']
            // },
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        series: [
          {
            name: '',
            type: 'pie',
            // roseType: 'radius',
            radius: [0, 70], // 饼图大小
            center: ['49%', '45%'], // 饼图中心点
            label: {
              normal: {
                // formatter: '{b}',
                formatter: (params) => {
                  const labelName = duplicateMap[params.name] || params.name
                  return labelName.length > labelLimit ? labelName.substring(0, labelLimit) + '...' : labelName
                }
              }
            },
            labelLine: {
              normal: { length: 10, length2: 10 }
            },
            data: chartData,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      }
      const mergeOption = deepMerge(defaultOpt, this.chartOption)
      this.duplicateFormatter(mergeOption, duplicateMap)
      this.chart.setOption(mergeOption)
    },
    // 对 option 中的 legend 和 series 进行 重复name 的处理
    duplicateFormatter(option, duplicateMap) {
      option.legend.data.splice(0)
      const series = Array.isArray(option.series) ? option.series : [option.series]
      series.forEach((item) => {
        const seriesData = item.data
        seriesData.forEach((sData) => {
          const name = sData.name
          const id = sData.id
          if (!duplicateMap[name]) {
            // 非重复的name，直接以 name 作为 key
            duplicateMap[name] = name
          } else {
            // 重复的name，以 name + id 作为 key
            const newName = name + id
            duplicateMap[newName] = name
            // 修改 name， 并增加 duplicateName
            sData.name = newName
            sData.duplicateName = name
          }
        })
        // 添加图例
        option.legend.data.push(...seriesData.map(item => item.name))
      });
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData)
      showSaveImage(this.chart)
    }
  }
}
</script>

<style lang="scss">
  .tooltip-dot{
    display: inline-block;
    margin-right: 5px;
    border-radius: 10px;
    width: 9px;
    height: 9px;
  }
  .dataView{
    width:100%;
    height:100%;
    border-top: 1px solid #000;
    padding: 10px;
    overflow: auto;
    color: #666;
  }
  .dataView table{
    width:100%;
    border:1px solid #666;
    border-collapse: collapse;
    font-size: 14px;
  }
  .dataView table th,.dataView table td{
    padding: 2px 10px;
    border: 1px solid #aaa;
  }
</style>
