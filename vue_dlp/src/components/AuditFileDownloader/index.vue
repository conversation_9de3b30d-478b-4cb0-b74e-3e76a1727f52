<!--
  日志审计备份文件下载器

  <audit-file-downloader
    v-permission="'219'"                // 下载权限控制
    ref="auditFileDownloader"           // 组件引用名称
    :name="filename"                    // 批量下载文件名（不带后缀，组件自动添加.zip后缀名），默认使用页面的路由标题
    topic="SensitiveContent"            // 下载主题分类，默认使用页面的路由名称
    :show="false"                       // 是否显示下载按钮，默认true
    :button="$t('button.download')"     // 下载按钮名称，默认“下载”
    :selection="selection"              // 表格选中的行数据
    :before-download="beforeDownload"   // 下载前行数据处理
  />
-->
<template>
  <download-executor
    v-if="isEnableDownloadManager() && !viewImage"
    :disabled="disabledAble"
    :show-button="show"
    :button-name="button"
    button-size="mini"
    @download="batchDownload"
  />
  <div v-else style="display: inline-block;">
    <el-button
      v-show="show"
      :disabled="disabledAble"
      size="mini"
      icon="el-icon-download"
      @click="batchDownload"
    >
      {{ button }}
    </el-button>

    <el-dialog
      v-el-drag-dialog
      :title="title"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="visible"
      :append-to-body="appendToBody"
      :before-close="cancelDownload"
      width="500px"
    >
      <el-progress :text-inside="true" :stroke-width="26" :percentage="percentage"></el-progress>
    </el-dialog>
  </div>
</template>

<script>
import DownloadExecutor from '@/components/DownloadManager/executor'
import { buildDownloadFile, createProxyObj, DEFAULT_DOWNLOAD_FILE } from '@/utils/download/helper'
import { getDownloadTask, cancelDownloadTask, downloadFile, getFtpStatus } from '@/api/behaviorAuditing/download'
import md5 from '@/utils/md5'

export default {
  name: 'AuditFileDownloader',
  components: { DownloadExecutor },
  props: {
    title: {
      type: String,
      default() {
        return this.$t('components.fileTransfer')
      }
    },
    name: {
      type: String,
      default() {
        const label = this.$route.meta.title
        const routeLabel = `route.${label}`
        return this.$te(routeLabel) ? this.$t(routeLabel) : label;
      }
    },
    topic: {
      type: String,
      default() {
        return this.$route.name
      }
    },
    show: {
      type: Boolean,
      default: true
    },
    button: {
      type: String,
      default() {
        return this.$t('components.download')
      }
    },
    selection: {
      type: Array,
      default() {
        return []
      }
    },
    viewImage: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    },
    beforeDownload: {
      type: Function,
      default(row) {
        return { ...row }
      }
    },
    reallyDownload: {
      type: Function,
      // function(tempTasks, taskFile, closeProgress)
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      taskFile: { ...DEFAULT_DOWNLOAD_FILE },
      taskQueue: new Map(),
      ftpStatus: {}
    }
  },
  computed: {
    disabledAble() {
      return !this.selection || this.selection.length === 0 || this.disabled
    },
    percentage() {
      if (this.taskFile.active === 0) {
        return Math.floor(Math.min(this.taskFile.percent, 100) / 2)
      }
      return 50 + Math.floor(Math.min(this.taskFile.percent, 100) / 2)
    }
  },
  created() {
    this.getStatusMap()
  },
  methods: {
    getStatusMap() {
      getFtpStatus().then(res => {
        this.ftpStatus = res.data
      })
    },
    batchDownload() {
      const fileGuidArr = []
      const percentMap = {}
      const notExist = []
      let taskQueueKey = ''
      const tempTasks = this.selection.map(this.beforeDownload).filter(item => {
        const fileGuid = item.fileGuid || item.uploadFileGuid
        if (!fileGuid) {
          notExist.push(item.fileName)
          return false
        }
        if (fileGuidArr.includes(fileGuid)) {
          return false
        }
        taskQueueKey += fileGuid
        fileGuidArr.push(fileGuid)
        percentMap[fileGuid] = 0
        return true
      })
      if (notExist.length) {
        console.warn('The following ftp download tasks don\'t have file guid.', notExist)
        this.$notify({
          title: this.$t('text.fail'),
          message: notExist.join('，') + this.$t('pages.diskScanSens_Msg2'),
          type: 'warning',
          duration: 2000
        })
      }
      if (tempTasks.length === 0) {
        console.warn('The filtered valid ftp download tasks is empty!')
        return
      }
      taskQueueKey = md5(taskQueueKey)
      if (this.taskQueue.has(taskQueueKey)) {
        return
      }
      this.startDownload(tempTasks, percentMap, taskQueueKey)
    },
    handleDownload(row) {
      if (window.event) {
        window.event.stopPropagation()
      }
      const task = this.beforeDownload(row)
      if (this.taskQueue.has(task.fileGuid)) {
        return
      }
      const percentMap = {}
      percentMap[task.fileGuid] = 0
      this.startDownload([task], percentMap, task.fileGuid)
    },
    closeProgressDialog(taskQueueKey) {
      this.taskQueue.delete(taskQueueKey)
      this.visible = false
    },
    handleDownResult(taskQueueKey, opts) {
      this.closeProgressDialog(taskQueueKey)
      this.$notify({
        title: this.$t('text.warning'),
        message: '',
        type: 'warning',
        duration: 3000,
        ...opts
      })
    },
    cancelDownload(done) {
      this.taskFile.abort && this.taskFile.abort()
      typeof done === 'function' && done()
    },
    startDownload(tempTasks, percentMap, taskQueueKey) {
      this.visible = true
      this.taskQueue.set(taskQueueKey, 0)
      const taskFile = buildDownloadFile(tempTasks, tempTasks.length > 1, this.name, this.viewImage)
      taskFile.steps = 3
      taskFile.abort = () => {
        if (taskFile.timer) {
          clearTimeout(taskFile.timer)
          taskFile.timer = undefined
        }
        cancelDownloadTask(tempTasks)
        if (this && this.taskQueue) {
          this.taskQueue.delete(taskQueueKey)
        }
      }
      taskFile.taskRetry = 0
      taskFile.reDownload = true
      taskFile.taskQueueKey = taskQueueKey
      if (!this.isEnableDownloadManager()) {
        this.taskFile = taskFile
      }
      const percentMapProxy = createProxyObj(percentMap, 0, obj => {
        const oldPercent = taskFile.percent
        const newPercent = Object.values(obj).reduce((preVal, curVal) => preVal + curVal)
        if (newPercent <= oldPercent) {
          return
        }
        if (newPercent > 100) {
          taskFile.percent = 100
        } else {
          taskFile.percent = newPercent
        }
      })
      this.downloadTask(tempTasks, percentMapProxy, taskFile)
    },
    downloadTask(tempTasks, percentMap, taskFile, errorMsg) {
      if (taskFile.canceled) {
        return
      }
      if (taskFile.timer) {
        clearTimeout(taskFile.timer)
      }
      if (taskFile.taskRetry >= 10) {
        taskFile.error = errorMsg
        taskFile.abort()
        this.handleDownResult(taskFile.taskQueueKey, { message: errorMsg || '' })
        return
      }
      tempTasks.forEach(task => { task.reDownload = taskFile.reDownload })
      taskFile.timer = setTimeout(() => {
        const average = 1 / tempTasks.length
        getDownloadTask(tempTasks).then(respond => {
          taskFile.timer = undefined
          const results = respond.data || []
          let success = 0
          for (let i = 0; i < results.length; i++) {
            if (taskFile.canceled) {
              return
            }
            // 若返回的数据为空 直接return
            if (results[i] == null) {
              continue
            }
            const status = results[i].status
            if (status === 6 || status === 9999) {
              percentMap[results[i].fileGuid] = 100 * average
              success |= 1
              continue
            }
            success = -1
            if (status === 100 || status === 5 || status === 4 || status < 2) {
              let percent = (results[i].percent || percentMap[results[i].fileGuid] || 0) * average
              if (percent < average) {
                percent += average
              }
              percentMap[results[i].fileGuid] = percent
            } else {
              // taskFile.error = results[i].statusInfo
              taskFile.error = this.ftpStatus[status]
              if (status === 3 || status > 999) {
                taskFile.canceled = true
                // this.handleDownResult(taskFile.taskQueueKey, { message: results[i].statusInfo })
                this.handleDownResult(taskFile.taskQueueKey, { message: this.ftpStatus[status] })
                return
              }
            }
          }
          if (success > 0) {
            const closeProgress = () => this.closeProgressDialog(taskFile.taskQueueKey)
            if (typeof this.reallyDownload === 'function') {
              this.reallyDownload(tempTasks, taskFile, closeProgress)
            } else {
              downloadFile(this.topic, tempTasks, taskFile).then(() => {
                taskFile.percent = 100
                this.handleDownResult(taskFile.taskQueueKey, {
                  type: 'success',
                  title: this.$t('text.success'),
                  message: this.$t('components.fileDownload')
                })
              }).catch(() => {
                closeProgress()
              })
            }
          } else {
            taskFile.reDownload = false
            this.downloadTask(tempTasks, percentMap, taskFile, errorMsg)
          }
        }).catch(e => {
          taskFile.timer = undefined
          taskFile.taskRetry++
          taskFile.reDownload = false
          const message = (e.response && e.response.data && e.response.data.data) || e.message || e
          this.downloadTask(tempTasks, percentMap, taskFile, message)
        })
      }, 1000)
    }
  }
}
</script>
