<!--
  日志审计记录导出器

  <audit-log-exporter
    v-permission="'233'"          // 导出权限控制
    :name="filename"              // 导出文件名（不带后缀，组件自动添加.xlsx后缀名），默认使用页面的路由标题
    :batch="false"                // 是否批量导出，默认 false，批量导出文件后缀名为.zip
    :button="buttonName"          // 导出按钮名称，默认 导出
    :disabled="false"             // 是否禁用导出按钮，默认 false
    :multi-dataset="false"        // 是否导出多个数据集，默认 false
    :supported-types="['xlsx']"   // 支持的导出文件类型，默认 xlsx、et、csv、pdf
    :request="exportFunc"         // 导出任务请求（后台收到该请求开始创建导出任务并返回任务令牌，前端拿到令牌后才真正开始下载）
  />
-->
<template>
  <!--<download-executor :button-name="$t('button.export')" button-size="mini" :disabled="disabled" @download="handleDownload"/>-->
  <el-button
    v-if="isSingleType"
    size="mini"
    icon="el-icon-download"
    :disabled="disabled"
    @click.native.stop="handleDownload(supportedTypes[0])"
  >
    {{ button }}
  </el-button>
  <el-dropdown v-else :disabled="disabled">
    <el-button size="mini" icon="el-icon-download" :disabled="disabled">
      {{ button }}<i class="el-icon-arrow-down el-icon--right"></i>
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="(value, type) in showFileTypes" :key="type" @click.native.stop="handleDownload(type)">
        {{ value.name }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { buildDownloadFileByName, handleAxiosRequestError, isFetchReady, fetchDownload } from '@/utils/download/helper'

export default {
  name: 'AuditLogExporter',
  props: {
    name: {
      type: [String, Function],
      default() {
        const label = this.$route.meta.title
        const routeLabel = `route.${label}`
        return this.$te(routeLabel) ? this.$t(routeLabel) : label;
      }
    },
    batch: {
      type: Boolean,
      default: false
    },
    button: {
      type: String,
      default() {
        return this.$t('button.export')
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiDataset: {
      type: Boolean,
      default: false
    },
    supportedTypes: {
      type: Array,
      default() {
        return ['xlsx'/*, 'et', 'csv', 'pdf'*/]
      }
    },
    request: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      fileTypes: {
        xlsx: {
          name: this.$t('file.xlsx'),
          suffix: '.xlsx'
        },
        et: {
          name: this.$t('file.et'),
          suffix: '.et'
        },
        csv: {
          name: this.$t('file.csv'),
          suffix: '.csv'
        },
        pdf: {
          name: this.$t('file.pdf'),
          suffix: '.pdf'
        }
      }
    }
  },
  computed: {
    isSingleType() {
      return this.supportedTypes && this.supportedTypes.length === 1
    },
    showFileTypes() {
      if (!this.supportedTypes || this.supportedTypes.length === 0) {
        return this.fileTypes
      }
      const types = {}
      this.supportedTypes.forEach(type => {
        const value = this.fileTypes[type]
        if (!value) {
          return
        }
        if (type === 'csv' && this.multiDataset) {
          types[type] = { ...value, suffix: '.zip' }
        } else {
          types[type] = { ...value }
        }
      })
      return types
    }
  },
  methods: {
    handleDownload(exportType) {
      const suffix = this.batch ? '.zip' : this.showFileTypes[exportType].suffix
      const fileName = (typeof this.name === 'function' ? this.name() : this.name) + suffix
      const file = buildDownloadFileByName(fileName)
      file.steps = 3
      this.request(exportType, fileName)
        .then(res => isFetchReady(res.headers ? res.headers.location : res.data, file))
        .catch(e => handleAxiosRequestError(file, e))
        .then(url => fetchDownload({ topic: this.$route.name, url, file }))
    }
  }
}
</script>
