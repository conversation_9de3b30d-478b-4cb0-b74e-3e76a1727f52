<template>
  <el-input
    ref="encryptInput"
    v-model="tempInput"
    :type="type"
    :clearable="clearable"
    :disabled="disabled"
    :maxlength="$attrs.maxlength"
    :placeholder="$attrs.placeholder"
    :show-password="showPassword && showPlaintextBtn"
    @input.native="inputEvent"
    @blur="blurEvent"
  ></el-input>
</template>

<script>
// 加密输入框，当输入框存在数据时，会将数据全部替换成空格，首次输入时，会将输入框内容清空且添加输入的内容
// 使用该组件时，需要使用表单进行包装<Form><FormItem><encrypt-input v-model='temp.password'>，因为需要监听对象的变化来判断是否为新的表单
// temp.password恒为真实值，该组件利用一个临时变量来作输入，只有当进行输入时，才会真正影响到temp.password的值
export default {
  name: 'EncryptInput',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 输入框是否可以输入空格
    nonBlank: {
      type: Boolean,
      default: false
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 可清空
    clearable: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'password'
    },
    // 用于判断是否显示明文按钮
    showPassword: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tempInput: '',             // el-input 绑定的值
      pastedData: '',            // 粘贴事件获取到的值
      replaceNums: 0,            // 用于判断是否首次输入
      propAttrs: {},             // 表单对象，用于判断对象是否变更从而刷新密码字段
      noneInputBlurEvent: false, // 判断是否为从来没输入的blur事件
      showPlaintextBtn: true     // 用于判断是否显示明文按钮
    }
  },
  watch: {
    'propAttrs.model': {
      handler(val, oldVal) {
        if (val != oldVal) {
          this.initInput()
        }
      }
    }
  },
  mounted() {
    const ref = this.$refs['encryptInput']
    this.propAttrs = (ref && ref['elForm'] && ref['elForm']['_props']) || this.$attrs
    // 添加粘贴事件监听器
    ref.$el.addEventListener('paste', this.pasteEvent)
  },
  methods: {
    // 粘贴事件
    pasteEvent(event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      this.pastedData = clipboardData.getData('Text');
    },
    // 输入事件
    inputEvent(event) {
      // replaceNums 大于 0 时，输入内容需要区分 手动输入 或 粘贴 等情况（目前只处理了这两种情况）
      if (this.replaceNums > 0 && this.type === 'password') {
        if (this.noneInputBlurEvent) {
          this.initInput()
          return
        }
        // inputType：insertFromPaste 粘贴； insertText 输入； deleteContentForward 或 deleteContentBackward 删除；
        if (event.inputType == 'insertFromPaste') {
          // 将输入框内容替换为粘贴的文字
          event.target.value = this.pastedData
        } else if (event.inputType == 'insertText') {
          // 将输入框内容清空替换
          event.target.value = event.data
        } else if (event.inputType == 'deleteContentForward' || event.inputType == 'deleteContentBackward') {
          // 输入删除按键时，将输入框内容清空
          event.target.value = ''
        }

        // 移除粘贴事件监听器，清除 pastedData 的值, replaceNums 修改为 0
        this.$refs['encryptInput'].$el.removeEventListener('paste', this.pasteEvent)
        this.pastedData = ''
        this.replaceNums = 0
        this.showPlaintextBtn = true
      }
      // 不能输入空格时，替换掉空格
      if (this.nonBlank) {
        event.target.value = event.target.value.replace(/\s+/g, '')
      }

      // this.tempInput = event.target.value
      this.$emit('input', event.target.value)
    },
    initInput() {
      this.replaceNums = this.value ? 8 : 0
      this.showPlaintextBtn = !this.value
      this.tempInput = this.value && this.type === 'password' ? ' '.repeat(this.replaceNums) : this.value
      this.noneInputBlurEvent = false
      // 移除粘贴事件监听器
      !this.value && this.$refs['encryptInput'].$el.removeEventListener('paste', this.pasteEvent)
    },
    blurEvent(event) {
      // replaceNums > 0 意为没有输入过
      if (this.replaceNums > 0) {
        // 将变量置为true， 如果是组件使用了v-trim指令时，会处理为event.target.value.trim(),处理后会再进入@input方法中
        // 而input方法需要判断：输入框是否输入过，如果没有输入过，则不要清空输入框的内容
        this.noneInputBlurEvent = true
      }
    }
  }
}
</script>

<style scoped>

</style>
