<!--
  /**
  * 权限检测
  * 调用示例：
    <permission-check me-code="101">                     // 菜单编码
      需要权限控制的html内容
    </permission-check>
  */
-->
<template>
  <div v-if="checkFunc()" class="permission-check-contain">
    <slot></slot>
  </div>
</template>

<script>

export default {
  name: 'PermissionCheck',
  props: {
    meCode: { // 菜单编码
      type: String,
      default: null
    }
  },
  methods: {
    checkFunc() {
      if (this.meCode) {
        const userMenuCodes = this.$store.getters.userMenuCodes
        return userMenuCodes.indexOf(this.meCode) >= 0
      }
      return true
    }
  }
}
</script>
