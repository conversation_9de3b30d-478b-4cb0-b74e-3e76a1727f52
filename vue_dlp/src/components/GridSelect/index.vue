<!--
  /**
  * 通过列表的形式实现选择数据的功能，左侧列表显示可选数据，右侧列表显示已选数据
  * @date
  * 调用示例：
    <grid-select
      :height="300"                                   // 列表高度
      :col-model="userColModel"                       // 列表属性配置，eg:[{prop: 'username', label: '名称', width: '150', fixed: true, sort: true}]
      :select-table-title="selectTableTitle"          // 左侧列表标题
      :selected-table-title="selectedTableTitle"      // 左侧列表标题
      :select-row-data-api="selectRowDataApi"         // 查询可选列表数据的API方法，方法返回值为Promise对象
      :selected-row-data-api="selectedRowDataApi"     // 查询已选列表数据的API方法，方法返回值为Promise对象
      searchable                                      // 是否可已搜索
      :search-prop="object"                           // 搜索的属性和字段名，Object: { key: 'name', lable: '名称'}
      :selectable="selectable"                        // 仅对 type=selection 的列有效，类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选. Function(row, index)
      :to-select-table-col-span="14"                  // 只有同时设置了to-select-table-col-span,selected-table-col-span 这两个属性才起作用,共占20，默认按可选列列数与已选列列数的占比分配
      :selected-table-col-span="6"                    // 只有同时设置了to-select-table-col-span,selected-table-col-span 这两个属性才起作用,共占20，默认按可选列列数与已选列列数的占比分配
    />
  * 属性说明：
  * selectedRowDataApi、selectedRowDataApi 方法的返回值Promise对象包括
      {
        code: 20000,
        data: {
          total: 100,
          items: [ { account: 'admin', name: 'administrator', role: '超级管理员' } ]
        }
      }
  * colModel属性配置详情查看 GridTable 组件
  */
-->
<template>
  <div ref="tableSelect" class="grid-select-con" :style="conStyle">
    <el-row style="display: flex; justify-content: space-around">
      <el-col :span="toSelectSpan">
        <div class="grid-select-header">
          <label>{{ selectTableTitle }}</label>
          <el-input
            v-if="searchable"
            v-model="searchText"
            v-trim
            class="search-input"
            :placeholder="searchPlaceholder"
            suffix-icon="el-icon-search"
            clearable
            maxlength=""
            @change="selectFilter"
          ></el-input>
        </div>
        <grid-table
          ref="toSelectGridTable"
          :row-data-api="rowDataApi"
          :height="tableHeight"
          :show-pager="true"
          :row-class-name="rowClassName"
          :col-model="colModel"
          :selectable="toCheckSelectable"
          row-key="id"
          :checked-row-keys="checkedRowKeys"
          :after-load="initClassName"
          :pager-small="pagerSmall"
          @row-dblclick="selectDblclick"
        />
      </el-col>
      <el-col :span="2" :style="{'margin-top': tableHeight / 2 - 50 + 'px'}">
        <div class="select-btn" @click="selectDataFunc">&gt;&gt;</div>
        <div class="select-btn" @click="unselectDataFunc">&lt;&lt;</div>
      </el-col>
      <el-col :span="selectedSpan">
        <div class="grid-select-header"><label>{{ selectedTableTitle }}({{ checkedRowKeys.length }})</label></div>
        <grid-table
          ref="selectedGridTable"
          row-key="id"
          :row-datas="selectedDatas"
          :height="tableHeight - 40"
          :show-pager="false"
          :col-model="selectedColModel"
          :selectable="checkedSelectable"
          @row-dblclick="unselectDblclick"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'GridSelect',
  props: {
    height: {
      type: Number,
      default: 250
    },
    colModel: {// 列模板配置
      type: Array,
      default: function() {
        return []
      }
    },
    selectedColModel: {// 列模板配置
      type: Array,
      default: function() {
        return []
      }
    },
    selectable: {
      type: Function,
      default: function(data) {
        return true
      }
    },
    selectTableTitle: {
      type: String,
      default: function() {
        return this.$t('components.selectable')
      }
    },
    selectedTableTitle: {
      type: String,
      default: function() {
        return this.$t('components.selected')
      }
    },
    selectedTableColSpan: {
      type: Number,
      default: undefined
    },
    toSelectTableColSpan: {
      type: Number,
      default: undefined
    },
    searchable: {
      type: Boolean,
      default: false
    },
    searchProp: {
      type: Object,
      default() {
        return { key: 'name', label: this.$t('components.searchProp') }
      }
    },
    beforeSelectDataFunc: {
      type: Function,
      default() {
        return () => true
      }
    },
    pagerSmall: {// 页码工具条使用小型分页样式
      type: Boolean,
      default: false
    },
    selectRowDataApi: {// 可选数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => {
          resolve({
            code: 20000,
            data: {
              total: 0,
              items: [] // 数组元素格式为{username:'zhangsan',id:1,...}
            }
          })
        })
      }
    },
    selectedRowDataApi: {// 已选数据
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => {
          resolve({
            code: 20000,
            data: {
              total: 0,
              items: [] // 数组元素格式为{username:'zhangsan',id:1,...}
            }
          })
        })
      }
    }
  },
  data: function() {
    return {
      selectedDatas: [], // 已选择的数据
      selectedMap: {}, // 已选数据的map，以id作为key
      toSelectSpan: 11,
      selectedSpan: 11,
      searchText: '',
      checkedRowKeys: []
    }
  },
  computed: {
    conStyle() {
      return 'height:' + this.height + 'px;'
    },
    tableHeight() {
      return this.height - 40
    },
    searchPlaceholder() {
      return this.$t('components.searchPlaceholderMsg', { label: this.searchProp.label })
    },
    selectedIds() {
      return this.selectedDatas.map(data => data.id)
    },
    toCheckSelectable() {
      return (data) => {
        return this.selectable(data) && !data.selected
      }
    },
    checkedSelectable() {
      return (data) => {
        return this.selectable(data)
      }
    }
  },
  watch: {

  },
  mounted() {

  },
  created() {
    this.initSelectedColModel()
    this.loadSelectedDatas()
  },
  methods: {
    clearFilter() { // 清空搜索框内容
      this.searchText = ''
    },
    selectFilter(value) {
      const opt = { page: 1 }
      opt[this.searchProp.key] = value
      this.selectTable().execRowDataApi(opt)
    },
    // 可选列表请求数据的api
    rowDataApi: function(option) {
      option[this.searchProp.key] = this.searchText
      const searchQuery = Object.assign({}, this.query, option)
      return this.selectRowDataApi(searchQuery)
    },
    // 根据属性添加class
    rowClassName({ row, rowIndex }) {
      return row.selected ? 'selected' : ''
    },
    // 给指定数据设置是否选中的状态
    setClassName(ids, type) {
      const rowData = this.selectTable().$data.rowData
      if (rowData) {
        rowData.forEach(row => {
          if (ids.indexOf(row.id) > -1) {
            this.$set(row, 'selected', type)
          }
        })
      }
    },
    // 清除所有数据的选中状态
    removeClassName() {
      const rowData = this.selectTable().$data.rowData
      if (rowData) {
        rowData.forEach(row => {
          this.$set(row, 'selected', false)
        })
      }
    },
    // 初始化数据的状态
    initClassName() {
      if (this.selectedIds.length > 0) {
        this.setClassName(this.selectedIds, true)
      } else {
        this.removeClassName()
      }
      this.refreshCheckedStatus()
    },
    // 更新可选列表勾选状态
    refreshCheckedStatus() {
      this.checkedRowKeys.splice(0, this.checkedRowKeys.length, ...this.selectedIds)
    },
    // 双击可选列表数据，切换选中/未选中状态
    selectDblclick(row, column, event) {
      if (!this.selectable(row)) return
      // row.selected = !row.selected
      if (!row.selected) {
        this.selectData(row)
        this.refreshCheckedStatus()
        row.selected = true
      } else {
        this.unselectDblclick(row)
        row.selected = false
      }
    },
    // 双击已选列表，移除该数据
    unselectDblclick(row, column, event) {
      // 选择前的钩子 判断能否移除
      if (!this.checkedSelectable(row)) {
        return
      }
      const selectedIds = [row.id]
      this.setClassName(selectedIds, false)
      this.selectedTable().deleteRowData(selectedIds)
      this.selectedMap[row.id] = false
      this.refreshCheckedStatus()
    },
    // 将未选中数据设置为已选中
    selectData(data) {
      // 选择前的钩子
      if (!this.beforeSelectDataFunc()) {
        return
      }
      if (!this.toCheckSelectable(data)) {
        return
      }
      const hasSelected = this.selectedMap[data.id]
      if (!hasSelected) {
        this.selectedDatas.push(data)
        this.selectedMap[data.id] = true
      }
    },
    selectTable() {
      return this.$refs['toSelectGridTable']
    },
    selectedTable() {
      return this.$refs['selectedGridTable']
    },
    initSelectedColModel() {
      // 只有同时存在toSelectTableColSpan，selectedTableColSpan才不启用默认按列分配占比
      if (!this.toSelectTableColSpan || !this.selectedTableColSpan) {
        if (this.colModel && this.colModel.length > 0) {
          if (this.selectedColModel.length === 0) {
            this.selectedColModel.push(this.colModel[0])
          }
          const size = this.colModel.length + this.selectedColModel.length
          this.toSelectSpan = Math.floor(this.colModel.length / size * 22)
          this.selectedSpan = 22 - this.toSelectSpan
        }
      } else {
        this.toSelectSpan = this.toSelectTableColSpan
        this.selectedSpan = this.selectedTableColSpan
      }
    },
    // 从可选列表中批量选择数据的方法
    selectDataFunc: function() {
      // 选择前的钩子
      if (!this.beforeSelectDataFunc()) {
        return
      }
      const selectData = this.selectTable().getSelectedDatas()
      const selectIds = this.selectTable().getSelectedIds()
      if (selectData && selectData.length > 0) {
        while (selectData.length > 0) {
          const data = selectData.pop()
          this.selectData(data)
        }
        this.selectTable().clearSelection()
        this.setClassName(selectIds, true)
        this.refreshCheckedStatus()
      }
    },
    // 从已选列表中批量移除数据的方法
    unselectDataFunc: function() {
      const selectedIds = this.selectedTable().getSelectedIds()
      if (selectedIds && selectedIds.length > 0) {
        this.setClassName(selectedIds, false)
        this.selectedTable().deleteRowData(selectedIds)
        selectedIds.forEach(id => {
          this.selectedMap[id] = false
        })
        this.refreshCheckedStatus()
      }
    },
    // 获取已选列表id
    getSelectedIds: function() {
      return this.selectedIds
    },
    // 获取已选列表数据
    getSelectedDatas: function() {
      return this.selectedDatas
    },
    // 重新加载可选列表和已选列表
    reload: function() {
      this.selectTable().execRowDataApi({ page: 1 })
      this.loadSelectedDatas()
    },
    // 加载已选列表
    loadSelectedDatas() {
      this.selectedDatas = []
      this.selectedMap = {}
      this.selectedRowDataApi({ page: 1 }).then(respond => {
        const { data } = respond
        // 设置已选列表数据
        this.selectedDatas = data instanceof Array ? data : typeof data == 'object' ? data.items : []
        this.selectedDatas.forEach(data => {
          this.selectedMap[data.id] = true
        })
        this.initClassName()
      })
    }
  }
}

</script>

<style lang="scss" scope>
.grid-select-con{
  color: #666;
  display: flex;
  flex-direction: column;
  .el-row {
    height: 100%;
  }
  .grid-select-header{
    padding: 0 10px;
    border: 1px solid #aaa;
    border-bottom: 0;
    line-height: 40px;
    position: relative;
    flex-shrink: 0;
  }
  .search-input{
    width: 226px;
    position: absolute;
    right: 5px;
    top: 5px;
  }
  .select-btn{
    cursor: pointer;
    text-align: center;
    margin: 30px 0;
    width: 100%;
  }
}
.selected{
  color: #979797;
  .el-checkbox__input.is-checked .el-checkbox__inner{
    background-color: #aaa;
    border-color: #aaa;
  }
}
</style>
