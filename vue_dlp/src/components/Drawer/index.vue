<!--
    /**
     * 封装 el-drawer
     * 调用示例：
        <Drawer
          ref="drawer"
          :title="title"                              // Drawer 的标题，也可通过具名 slot='title'传入
          :visible.sync="visible"                     // 是否显示 Drawer，支持 .sync 修饰符
          :modal="modal"                              // 是否需要遮罩层，默认false
          :modal-append-to-body="modalAppendToBody"   // 遮罩层是否插入至 body 元素上，若为 false，则遮罩层会插入至 Drawer 的父元素上
          :append-to-body="appendToBody"              // Drawer 自身是否插入至 body 元素上，默认false
          :before-close="beforeClose"                 // 关闭前的回调，会暂停 Drawer 的关闭
          :custom-class="customClass"                 // Drawer 的自定义类名
          :size="size"                                // Drawer 窗体的大小, 当使用 number 类型时, 以像素为单位, 当使用 string 类型时, 请传入 'x%', 否则便会以 number 类型解释
          :direction="direction"                      // Drawer 打开的方向, rtl / ltr / ttb / btt
          :with-header="withHeader"                   // 控制是否显示 header 栏, 默认为 true, 当此项为 false 时, title attribute 和 title slot 均不生效
          :with-footer="withFooter"                   // 控制是否显示 footer 栏, 默认为 true
          ----------- 以下配置在使用内置的 footer 按钮提交数据时才需要配置 -----------
          :status="status"                            // 抽屉的状态，create 新增，update 修改
          :submitting.sync="submitting"               // 是否正在提交数据
          :create-data="createData"                   // 新增数据的方法，返回执行结果（Promise 或 Boolean）
          :update-data="updateData"                   // 修改数据的方法，返回执行结果（Promise 或 Boolean）
        >

          ...内容

          <div slot="footer">                         // 如果 withFooter 为 true，且不使用 slot="footer"，则会使用内置的 footer，需要配置 createData、updateData 方法
            <el-button>按钮</el-button>
          </div>
        </Drawer>
    */
-->
<template>
  <el-drawer
    ref="drawer"
    :title="title"
    :modal="modal"
    :modal-append-to-body="modalAppendToBody"
    :append-to-body="appendToBody"
    :close-on-press-escape="closeOnPressEscape"
    :wrapper-closable="wrapperClosable"
    :custom-class="drawerClass"
    :visible.sync="drawerVisible"
    :size="drawerSize"
    :direction="direction"
    :before-close="beforeCloseFunc"
    :with-header="withHeader"
    v-bind="$attrs"
    v-on="$listeners"
    @open="open"
    @close="close"
  >
    <!-- title的slot -->
    <template slot="title">
      <slot name="title"></slot>
    </template>

    <!-- body的slot -->
    <slot></slot>

    <!-- footer的slot -->
    <div v-if="withFooter" class="el-drawer__footer">
      <!-- 若外部没有传入slot，则使用内置的按钮 -->
      <slot name="footer">
        <!-- 自定义按钮插槽 -->
        <slot name="btn-group" />
        <!-- 确认按钮 -->
        <el-button type="primary" :loading="submitting" @click="handleConfirm">{{ $t('button.confirm') }}</el-button>
        <!-- 取消按钮 -->
        <el-button @click="handleClose">{{ $t('button.cancel') }}</el-button>
      </slot>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'Drawer',
  props: {
    title: { type: String, default: '' },                        // Drawer 的标题，也可通过具名 slot='title'传入
    visible: { type: Boolean, default: false },                  // 是否显示 Drawer，支持 .sync 修饰符
    modal: { type: Boolean, default: false },                    // 是否需要遮罩层
    modalAppendToBody: { type: Boolean, default: false },        // 遮罩层是否插入至 body 元素上，若为 false，则遮罩层会插入至 Drawer 的父元素上
    appendToBody: { type: Boolean, default: false },             // Drawer 自身是否插入至 body 元素上
    closeOnPressEscape: { type: Boolean, default: false },       // 是否可以通过按下 ESC 关闭 Drawer	
    wrapperClosable: { type: Boolean, default: false },          // 点击遮罩层是否可以关闭 Drawer
    beforeClose: { type: Function, default: null },              // 关闭前的回调，会暂停 Drawer 的关闭
    customClass: { type: String, default: '' },                  // Drawer 的自定义类名
    size: { type: [String, Number], default: '50%' },            // Drawer 窗体的大小, 当使用 number 类型时, 以像素为单位, 当使用 string 类型时, 请传入 'x%', 否则便会以 number 类型解释
    minWidth: { type: [Number], default: 600 },                  // Drawer 窗体的最小宽度
    direction: { type: String, default: 'rtl' },                 // Drawer 打开的方向, rtl / ltr / ttb / btt
    withHeader: { type: Boolean, default: true },                // 控制是否显示 header 栏, 默认为 true, 当此项为 false 时, title attribute 和 title slot 均不生效
    withFooter: { type: Boolean, default: true },                // 控制是否显示 footer 栏, 默认为 true
    status: { type: String, default: 'create' },                 // 抽屉的状态，create 新增，update 修改
    submitting: { type: Boolean, default: false },               // 是否正在提交数据
    createData: { type: Function, default: null },               // 新增数据的方法，返回执行结果（Promise 或 Boolean）
    updateData: { type: Function, default: null }                // 修改数据的方法，返回执行结果（Promise 或 Boolean）
  },
  data() {
    return {
      drawerVisible: false,                     // 是否显示 Drawer
      containerWidth: 0                         // drawer容器的宽度
    }
  },
  computed: {
    // Drawer 的自定义类名
    drawerClass() {
      const classArray = this.withFooter ? ['with-footer'] : []
      const newClassArr = classArray.concat(this.customClass.split(' ')).filter(c => c)
      return newClassArr.join(' ')
    },
    drawerSize() {
      if (typeof this.size == 'number') {
        const maxWidth = Math.max(this.size, this.minWidth)
        return this.containerWidth > maxWidth ? maxWidth : this.containerWidth
      } else {
        // 计算当前宽度
        const pixelWidth = this.containerWidth * parseInt(this.size) / 100;
        // 如果按照百分比计算的宽度小于最小宽度，则按最小宽度显示，否则按百分比显示
        return pixelWidth > this.minWidth ? this.size : this.minWidth > this.containerWidth ? '100%' : this.minWidth
      }
    }
  },
  watch: {
    visible(val) {
      this.drawerVisible = val;
    },
    drawerVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  created() {
  },
  methods: {
    // 内置确认按钮的方法，需要外部传入 createData, updateData 方法才能正常工作
    handleConfirm() {
      const confirmFunc = {
        create: this.createData,
        update: this.updateData
      }[this.status]
      if (typeof confirmFunc != 'function') return
      this.$emit('update:submitting', true);
      const result = confirmFunc();
      if (result) {
        if (result instanceof Promise) {
          result.then(valid => {
            this.$emit('update:submitting', false)
          }).catch(() => {
            this.$emit('update:submitting', false)
          })
        } else {
          this.$emit('update:submitting', false)
        }
      } else {
        this.$emit('update:submitting', false)
      }
    },
    open() {
      this.resetContainerWidth()
      window.addEventListener('resize', this.resetContainerWidth)
      this.$emit('open')
    },
    close() {
      window.removeEventListener('resize', this.resetContainerWidth)
      this.$emit('close')
    },
    resetContainerWidth() {
      this.containerWidth = this.$parent.$el.offsetWidth
    },
    // 调用关闭方法
    handleClose() {
      this.closeDrawer()
    },
    // 关闭 Drawer
    closeDrawer() {
      this.$refs.drawer.closeDrawer()
    },
    // 关闭前的回调，会暂停 Drawer 的关闭
    beforeCloseFunc(done) {
      if (typeof this.beforeClose == 'function') {
        return this.beforeClose(done)
      } else {
        // this.$confirmBox('确定关闭吗？', this.$t('text.prompt'), { appendToBody: false }).then(() => {
        //   done();
        // }).catch((e) => {})
        done();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-drawer__wrapper {
    position: absolute;
  }
  >>>.el-drawer__container {
    background: #0009;
  }
  >>>.el-drawer__header {
    padding: 10px 20px;
    margin: 0;
    font-weight: bold;
    color: #333;
    box-shadow: 0px 3px 5px -1px #ccc;
  }
  >>>.el-drawer__body {
    padding: 15px 20px;
  }
  >>>.with-footer .el-drawer__body {
    margin-bottom: 60px;
  }
  .el-drawer__footer {
    width: 100%;
    padding: 12px 20px;
    text-align: right;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #ffffff;
    border-top: 1px solid #ccc;
    box-shadow: 0px 2px 6px 0px inset #ccc;
    z-index: 1;
    >>>.el-steps--simple {
      padding: 7px 8%;
    }
  }
</style>
