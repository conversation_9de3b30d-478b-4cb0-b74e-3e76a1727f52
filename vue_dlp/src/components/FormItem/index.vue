<!--
  /**
  * 封装el-form-item，拓展功能。
  * 实现给label添加title的功能。
  * 
  * 
  * <FormItem
      :label="label"
      label-width="90px"
      :extra-width="{en: 30}"                   // 根据系统语言配置label额外增加的宽度
      encrypt                                   // 添加该属性，则该标签对应的字段在提交数据时将加密传输
      no-submit                                 // 添加该属性，则该标签对应的字段在提交数据前将删除
      :tooltip-content="tooltipContent"         // label 后显示 tooltip 的实现方式之一
      tooltip-placement="top"                   // tooltip 的位置
      tooltip-icon="info"                       // tooltip 的图标
    />
  * 
  * tooltip可通过slot或者porp的方式实现
  * eg: 
  * 1. 使用 slot
      <FormItem :label="label">
        <el-tooltip slot="tooltip">
          ...
        </el-tooltip>
      </FormItem>
  * 2. 使用 prop：通过 tooltip-content 传入 tooltip 需要展示的文字
      <FormItem :label="label" :tooltip-content="tooltipContent"></FormItem>
  * 
  */
-->
<template>
  <el-form-item ref="formItem" v-bind="$attrs" :label-width="newLabelWidth">
    <!-- label 文字部分 -->
    <slot slot="label" name="label">
      <span v-if="label" slot="label" :title="label" :class="{'label-text': hasTooltip, ellipsis: true}">{{ label }}</span>
    </slot>
    <!-- label tooltip部分 -->
    <slot slot="label" name="tooltip">
      <el-tooltip v-if="tooltipContent" slot="label" effect="dark" :placement="tooltipPlacement">
        <div slot="content">{{ tooltipContent }}</div>
        <i :class="`el-icon-${tooltipIcon}`" />
      </el-tooltip>
    </slot>
    <!-- content 部分 -->
    <slot />
  </el-form-item>
</template>

<script>

export default {
  name: 'FormItem',
  props: {
    // label文字
    label: {
      type: String,
      default: ''
    },
    // label的宽度
    labelWidth: {
      type: String,
      default: ''
    },
    // tooltip的内容
    tooltipContent: {
      type: String,
      default: ''
    },
    // tooltip的位置
    tooltipPlacement: {
      type: String,
      default: 'top'
    },
    // tooltip的icon
    tooltipIcon: {
      type: String,
      default: 'info'
    },
    // 根据系统语言配置label额外增加的宽度
    extraWidth: {
      type: Object,
      default() {
        return { en: 20 }
      }
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    },
    newLabelWidth() {
      // 未设置 labelWidth，则使用父组件的 labelWidth
      if (!this.labelWidth) {
        return ''
      }
      // 设置的 labelWidth 宽度
      const labelWidth = parseInt(this.labelWidth.replace('px', ''))
      // label 有值，才会增加额外的宽度
      const extraWidth = this.label ? this.extraWidth[this.lang] || 0 : 0
      return `${labelWidth + extraWidth}px`
    },
    hasTooltip() {
      return !!(this.$slots.tooltip || this.tooltipContent)
    }
  },
  created() {
    
  },
  methods: {
    resetFields() {
      return this.$refs.formItem.resetFields()
    },
    clearValidate(...args) {
      return this.$refs.formItem.clearValidate()
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-dialog__body .el-form .el-form-item__label{
    padding: 0 8px 0 5px;
  }
  >>>.el-form-item__label{
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .label-text {
    max-width: calc(100% - 15px);
    display: inline-block;
    vertical-align: bottom;
  }
  .required, .is-required {
    .label-text {
      max-width: calc(100% - 26px);
    }
  }
  .ellipsis >>>.el-form-item__content {
    text-overflow: ellipsis;
    overflow: hidden;
  }
  >>>.el-form-item__content>.el-radio-group {
    margin-left: 10px;
  }
</style>
