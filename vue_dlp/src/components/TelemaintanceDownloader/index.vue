<template>
  <!--  纯前端导出流程-->
  <el-button
    icon="el-icon-download"
    :disabled="disabled || !ctrlAble"
    :loading="loading"
    size="mini"
    @click="exportMaintenanceInfo"
  >
    {{ $t('button.export') }}
  </el-button>
  <!--  websocket请求流程使用的导出组件，纯前端导出不需要此组件-->
  <!--<common-downloader
    :name="excelName(telemaintanceType)"
    :loading="loading"
    :disabled="disabled || !ctrlAble"
    :button-name="$t('button.export')"
    button-type="primary"
    button-size="mini"
    @download="handleExport"
  />-->
</template>

<script>
// import CommonDownloader from '@/components/DownloadManager/common';
import i18n from '@/lang'
import { easyExportExcel } from '@/utils/excel'
import { exportMaintenanceLog } from '@/api/assets/systemMaintenance/maintenanceCommon'

export default {
  name: 'TelemaintanceDownloader',
  // components: { CommonDownloader },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    telemaintanceType: {
      type: String,
      required: true
    },
    getCurrentNode: {
      type: Function,
      default() {
        return null
      }
    },
    getCurQueryParam: {
      type: Function,
      default() {
        return null
      }
    },
    ctrlAble: {
      type: Boolean,
      default: false
    },
    colModel: {
      type: Array,
      required: true
    },
    getRowData: {
      type: Function,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    nodeData() {
      return this.getCurrentNode()
    }
  },
  methods: {
    handleExport(file) {
      this.exportMaintenanceInfo(this.nodeData, file)
    },
    exportMaintenanceInfo(data, file) {
      this.loading = true
      const rowData = this.getRowData()
      const type = this.telemaintanceType
      easyExportExcel(this.colModel, rowData, this.excelName(), this.sheetName())
      exportMaintenanceLog({ type, total: (rowData || []).length })
      this.loading = false
      // 流程: 发起websocket请求，服务端接收后向终端发起请求，终端响应后，服务端向客户端发送响应sn码，最后客户端向服务端发送导出请求
      // this.$socket.sendToUser(data.dataId, '/exportTermRemoteInfo', { type: type, terminalId: data.dataId }, (respond, handle) => {
      //   handle.close()
      //   if (respond.data) {
      //     // 将当前请求的菜单编码放入请求头中
      //     const currentRoute = router.currentRoute;
      //     let menuCode
      //     if (currentRoute.meta && currentRoute.meta.code) {
      //       menuCode = currentRoute.meta.code
      //     }
      //     const opts = { file, jwt: true, topic: this.$route.name, headers: { 'Menu-Code': menuCode }}
      //     const { sortName, sortOrder } = this.getCurQueryParam() || {}
      //     exportMaintenanceLog({ type: type, terminalId: data.dataId, msgSn: respond.data, sortName, sortOrder }, opts)
      //     this.loading = false
      //   }
      // }, (handle) => {
      //   handle.close()
      //   this.loading = false
      //   this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
      // })
    },
    excelName() {
      const nodeData = this.nodeData || {}
      const type = this.telemaintanceType
      // 根据 type 的值，获取 i18n 的 key
      const i18nKey = {
        process: 'pages.processExcelName',
        softwareInfo: 'pages.softwareExcelName',
        deviceInfo: 'pages.deviceExcelName',
        shareInfo: 'pages.shareExcelName',
        systemService: 'pages.systemServiceExcelName',
        bootItem: 'pages.bootExcelName',
        startup: 'pages.startUpExcelName',
        service: 'pages.serviceExcelName',
        taskPlan: 'pages.taskPlanExcelName'
      }[type] || 'pages.termExcelName'
      const name = i18n.t(i18nKey, { name: nodeData.label })
      return `${name}.xls`
    },
    sheetName() {
      const type = this.telemaintanceType
      // 根据 type 的值，获取 i18n 的 key
      const i18nKey = {
        process: 'pages.processInformation',
        softwareInfo: 'pages.softwareMessage',
        deviceInfo: 'pages.deviceInfo',
        shareInfo: 'pages.shareInfo',
        systemService: 'pages.systemServiceInfo',
        bootItem: 'pages.bootItemInfo',
        startup: 'pages.startUpInfo',
        service: 'pages.startUpServiceInfo',
        taskPlan: 'pages.startUpTaskPlanInfo'
      }[type] || 'pages.termExcelName'
      return i18n.t(i18nKey)
    }
  }
}
</script>
