<!--
  /**
  * 局部放大包裹元素的组件
  * @date 2024-12-04
  * 调用示例：
        <LocalZoomIn
          :height="200"              // 正常状态下的最大高度
          :zoomInHeight="500"        // 放大状态下的最大高度
          :zoomInWidth="800"         // 放大状态下的宽度
          :offsetTop="34"            // 上边距的偏移量
          :offsetLeft="0"            // 左边距的偏移量
          parent-tag="el-dialog"     // 指定父元素标签，用于放大后定位
        >
          <需要局部放大的组件>
          注意：如果组件是 GridTable，不需要设置 maxHeight，否则放大后高度无法撑开，如果需要调整高度，可以使用 max-height="calc(100% - 100px)"
        </LocalZoomIn>
  */
-->
<template>
  <div :class="{ 'zoom-in__wrapper': isZoomIn }">
    <div :class="{ 'local-zoom-in': true, 'fixed': isZoomIn, 'disabled': !useable }" :style="conStyle">
      <svg-icon :icon-class="isZoomIn ? 'screen-scale-down' : 'big'" :class="{ 'zoom-in-icon': true, 'zoom-in': isZoomIn }" @click="switchZoomIn"></svg-icon>
      <slot></slot>
    </div>
  </div>
</template>

<script>

export default {
  name: 'LocalZoomIn',
  props: {
    // 正常状态下的最大高度，默认200，当设置为 0 时，则根据内部元素的高度自适应
    height: {
      type: Number,
      default: 200
    },
    // 正常状态下的最小高度，默认 0
    minHeight: {
      type: Number,
      default: 0
    },
    // 放大状态下的高度
    zoomInHeight: {
      type: Number,
      default: 500
    },
    // 放大状态下的宽度
    zoomInWidth: {
      type: Number,
      default: 800
    },
    // 指定父元素标签，用于放大后定位
    parentTag: {
      type: String,
      default: ''
    },
    // 上边距的偏移量
    offsetTop: {
      type: Number,
      default: 34
    },
    // 左边距的偏移量
    offsetLeft: {
      type: Number,
      default: 0
    },
    // 控制是否可以放大
    useable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isZoomIn: false,          // 是否放大状态
      marginLeft: 0,            // 放大后的左边距，由父元素位置计算获得
      marginTop: 0              // 放大后的上边距，由父元素位置计算获得
    }
  },
  computed: {
    conStyle() {
      return `${this.sizeStyle} ${this.marginStyle}`
    },
    // 放大状态及正常状态的宽高样式
    sizeStyle() {
      if (this.isZoomIn) {
        return `height: ${this.zoomInHeight}px; width: ${this.zoomInWidth}px;`
      }
      // maxHeight 取 height 和 minHeight 的最大值
      const maxHeight = this.height ? `max-height: ${Math.max(this.height, this.minHeight)}px;` : ''
      const minHeight = this.minHeight ? `min-height: ${this.minHeight}px;` : ''
      return `${maxHeight}${minHeight}`
    },
    // 放大状态及正常状态的外边距样式
    marginStyle() {
      return this.isZoomIn ? `margin-top: ${this.marginTop}px; margin-left: ${this.marginLeft}px;` : ''
    }
  },
  watch: {
    isZoomIn(val) {
      if (val) {
        this.getMargin()
      }
    }
  },
  created() {
  },
  methods: {
    // 切换放大/缩小
    switchZoomIn() {
      this.isZoomIn = !this.isZoomIn
    },
    // 获取指定 tag 的父元素
    getParentElement(vm, tag) {
      if (vm.$root === vm) {
        // 如果找到根节点也没有找到指定标签元素，则返回根节点元素
        return vm.$el
      } else if (tag === vm.$parent.$options._componentTag) {
        // 找到指定标签元素，返回 $el
        return vm.$parent.$el
      } else {
        // 继续查找父元素
        return this.getParentElement(vm.$parent, tag)
      }
    },
    // 通过父元素位置信息，获取边距值
    getMargin() {
      const parentEl = this.getParentElement(this, this.parentTag).childNodes[0]
      const { top, left } = parentEl.getBoundingClientRect()
      this.$nextTick(() => {
        const marginLeft = left - this.$el.offsetLeft + this.offsetLeft
        const marginTop = top - this.$el.offsetTop + this.offsetTop
        this.marginLeft = marginLeft >= 0 ? marginLeft : this.$el.offsetLeft + this.offsetLeft
        this.marginTop = marginTop >= 0 ? marginTop : this.$el.offsetTop + this.offsetTop
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .local-zoom-in {
    padding: 10px 5px 5px;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px dashed transparent;
    background: #fff;
    &:hover {
      border-color: #aaa;
      .zoom-in-icon {
        display: block;
      }
    }
    &.disabled {
      border: none;
      .zoom-in-icon {
        display: none;
      }
    }
  }
  .zoom-in-icon {
    position: absolute;
    right: 17px;
    top: -8px;
    font-size: 16px;
    color: #666;
    background: #fff;
    display: none;
    cursor: pointer;
  }
  .fixed {
    background: #fff;
  }
  .el-dialog__body {
    .zoom-in-icon, .local-zoom-in, .fixed {
      background: #e4e7e9;
    }
  }
  .zoom-in {
    border: 1px solid #666;
    display: block;
  }
  .zoom-in__wrapper {
    position: fixed;
    top: 85px;
    bottom: 0;
    left: 210px;
    right: 0;
    z-index: 111;
    background: rgba(0, 0, 0, 0.5);
  }
</style>
