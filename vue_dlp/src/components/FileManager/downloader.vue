<!--
  文件下载器，用于下载资源管理器文件、终端日志、服务器日志等

  <file-downloader
    :disabled="disabled"                      // 是否禁用下载
    :show-zip-mode="showed"                   // 是否显示是否压缩复选框
    progress-title="下载进度"                   // 下载进度弹窗标题
    :progress-visible.sync="progressVisible"  // 是否显示下载进度弹窗
    :progress-active="active"                 // 设置当前激活步骤
    :progress-error="error"                   // 当前激活步骤是否出错，Boolean或String类型；若为String，表示出错信息
    :progress-percent="percent"               // 下载进度
    :progress-steps="progressSteps"           // 下载步骤
    :need-check-address="devId > 0"           // 是否需要检测控制台IP地址配置
    show-address-dialog                       // 控制台IP地址配置不对时，是否弹出地址配置弹窗
    address-config-title="控制台IP端口配置"      // 控制台地址配置弹窗标题
    @download="handleDownload"                // 下载事件回调
    @cancel-task="handleCancelTask"           // 取消下载任务回调
  />
-->
<template>
  <div class="downloader-box">
    <div class="downloader-opts">
      <el-checkbox v-show="showZipMode" v-model="zipped">
        {{ $t('components.compress') }}
      </el-checkbox>
      <el-button
        :disabled="disabled"
        :loading="downloading"
        type="primary"
        size="mini"
        icon="el-icon-download"
        @click="handleDownload"
      >
        {{ buttonLabel }}
      </el-button>
    </div>

    <el-dialog
      v-el-drag-dialog
      :title="progressTitle"
      width="605px"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="progressDlgVisible"
      @close="handleCancelTask"
    >
      <div class="progress-step">
        <progress-step
          :active="progressActive"
          :error="progressError"
          :percent="progressPercent"
          :steps="progressSteps"
        />
      </div>
    </el-dialog>

    <console-address-dialog v-if="showAddressDialog" ref="addrDlg" @updated="$emit('download', zipped)"/>
  </div>
</template>

<script>
import ConsoleAddressDialog from '@/components/DownloadManager/setting/consoleAddressDlg'
import ProgressStep from '@/components/FileManager/progress'
import { checkIntranetIp } from '@/api/assets/systemMaintenance/explorer'

export default {
  name: 'FileDownloader',
  components: { ConsoleAddressDialog, ProgressStep },
  props: {
    disabled: {
      type: Boolean,
      default: true
    },
    showZipMode: {
      type: Boolean,
      default: false
    },
    progressTitle: {
      type: String,
      default() {
        return this.$t('components.extractFile')
      }
    },
    buttonLabel: {
      type: String,
      default() {
        return this.$t('components.download')
      }
    },
    progressVisible: {
      type: Boolean,
      default: false
    },
    progressActive: {
      type: Number,
      default: 0
    },
    progressError: {
      type: [Boolean, String],
      default: false
    },
    progressPercent: {
      type: Number,
      default: 0
    },
    progressSteps: {
      type: Array,
      default() {
        return []
      }
    },
    needCheckAddress: {
      type: Boolean,
      default: true
    },
    showAddressDialog: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      zipped: false,
      downloading: false,
      progressDlgVisible: false
    }
  },
  watch: {
    progressVisible(value) {
      this.progressDlgVisible = value
    },
    progressDlgVisible(value) {
      this.$emit('update:progressVisible', value)
    }
  },
  methods: {
    handleDownload() {
      this.downloading = true
      if (!this.needCheckAddress) {
        this.$emit('download', this.zipped)
        this.downloading = false
        return
      }
      checkIntranetIp().then(resp => {
        if (resp.data) {
          this.$emit('download', this.zipped)
        } else {
          const msg = this.showAddressDialog
            ? this.$t('pages.serverlog_download_address_error')
            : this.$t('components.chunkDownload_Msg11')
          this.$alert(msg, this.$t('text.prompt'), {
            confirmButtonText: this.$t('button.confirm2'),
            type: 'warning',
            callback: action => {
              if (action === 'confirm' && this.hasPermission('A68')) {
                if (this.showAddressDialog) {
                  this.$refs.addrDlg.show()
                } else {
                  this.$router.push('/system/configManage/globalConfig?tabName=consoleAddressTab')
                }
              }
            }
          })
        }
      }).finally(() => {
        this.downloading = false
      })
    },
    handleCancelTask() {
      this.$emit('cancel-task')
    }
  }
}
</script>

<style lang="scss" scoped>
  .downloader-box {
    .downloader-opts {
      position: relative;
      float: right;
      margin-left: 10px;
      z-index: 1;
    }
  }
</style>
