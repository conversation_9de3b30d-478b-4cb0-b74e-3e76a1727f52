<template>
  <el-date-picker
    v-model="dates"
    type="daterange"
    range-separator="--"
    :start-placeholder="$t('pages.startDate')"
    :end-placeholder="$t('pages.endDate')"
    :editable="true"
    :picker-options="pickerOptions"
    @blur="handleDateBlur"
    @change="handleDateChange"
  />
</template>

<script>
export default {
  name: 'DateRangePicker',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      dates: [],
      duration: 1000 * 60 * 60 * 24 * 30,
      minDate: undefined,
      pickerOptions: {}
    }
  },
  watch: {
    value() {
      this.dates = this.value
    }
  },
  created() {
    this.dates = this.value
    // 本地搜索，不限制时间范围
    // this.pickerOptions.disabledDate = this.disabledDate
    this.pickerOptions.onPick = this.onDatePick
  },
  methods: {
    disabledDate(pickedDate) {
      const pickedTime = pickedDate.getTime()
      const beforeToday = pickedTime > Date.now() - 8.64e6;// 如果没有后面的-8.64e6就是不可以选择今天的
      if (beforeToday || this.minDate == null) {
        return beforeToday
      }
      const minTime = this.minDate - this.duration
      const maxTime = this.minDate + this.duration
      return pickedTime < minTime || pickedTime > maxTime
    },
    onDatePick({ maxDate, minDate }) {
      if (maxDate == null) {
        this.minDate = minDate.getTime()
      } else {
        this.minDate = undefined
      }
    },
    handleDateBlur() {
      this.minDate = undefined
    },
    handleDateChange(value) {
      this.$emit('change', value)
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-date-editor.el-range-editor {
    width: 100%;
    >>>.el-range-input, >>>.el-range-separator {
      color: #888;
    }
  }
</style>
