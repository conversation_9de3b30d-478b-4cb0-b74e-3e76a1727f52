<template>
  <div class="progress-step">
    <div class="el-steps el-steps--horizontal">
      <div
        v-for="(step, index) in computedSteps"
        :key="index"
        class="el-step is-horizontal"
        :class="{ 'is-flex': index === steps.length -1 }"
        :style="`flex-basis: ${100/(computedSteps.length - 1)}%;`"
      >
        <div :class="['el-step__head', statusClasses[step.status]]">
          <div class="el-step__line" style="margin-left: 22px">
            <div class="el-step__line-inner" :style="`border-width: 1px; width: ${step.percent}%`">
              <div v-if="step.percent > 0 && step.percent < 100" class="progress-tip" :style="`left:${20-step.percent/5}px`">{{ step.percent }}%</div>
            </div>
          </div>
          <div class="el-step__icon is-text" :class="{'process_animation': step.status === 'process'}">
            <div v-if="step.status === 'wait' || step.status === 'finish'" class="el-step__icon-inner">{{ index + 1 }}</div>
            <div v-else-if="step.status === 'process'" class="el-loading-spinner">
              <svg viewBox="25 25 50 50" class="circular">
                <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
              </svg>
              <div class="el-step__icon-inner">{{ index + 1 }}</div>
            </div>
            <i v-else :class="['el-step__icon-inner', 'is-status', statusIcons[step.status]]"/>
          </div>
        </div>
        <div class="el-step__main">
          <div :class="['el-step__title', statusClasses[step.status]]">{{ step.title }}</div>
          <div :class="['el-step__description', statusClasses[step.status]]">
            <i v-if="step.error" class="el-icon-warning"></i>
            {{ step.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressStep',
  props: {
    active: {
      type: Number,
      default: 0
    },
    error: {
      type: [Boolean, String],
      default: false
    },
    percent: {
      type: Number,
      default: 0
    },
    steps: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      statusClasses: {
        wait: 'is-wait',
        process: 'is-finish',
        finish: 'is-process',
        error: 'is-error',
        success: 'is-success'
      },
      statusIcons: {
        process: 'el-icon-loading',
        error: 'el-icon-close',
        // error: 'el-icon-error',
        success: 'el-icon-check'
        // success: 'el-icon-success'
      }
    }
  },
  computed: {
    computedSteps() {
      const active = this.active
      const activePercent = this.percent
      const activeError = this.error
      const steps = []
      this.steps.forEach((item, index, array) => {
        let status
        let percent
        let error = false
        let description
        if (index < active) {
          status = 'success'
          percent = 100
        } else if (index > active) {
          status = 'wait'
          percent = 0
        } else if (activeError) {
          status = 'error'
          percent = activePercent
          if (typeof activeError === 'string') {
            error = true
            description = activeError
          }
        } else if (active < array.length - 1) {
          status = 'process'
          percent = activePercent
        } else {
          status = 'finish'
          percent = 0
        }
        steps.push({ ...item, status, percent, error, description })
      })
      return steps
    }
  }
}
</script>

<style lang="scss" scoped>
  .progress-tip {
    font-size: 12px;
    position: relative;
    float: right;
    bottom: 14px;
  }
  .el-step__icon.is-text.process_animation {
    border: 0;
  }
  .el-loading-spinner {
    text-align: center;
    position: relative;
    top: 13px;
    right: 2px;
    .circular {
      height: 28px;
      width: 28px;
      .path {
        stroke-width: 4px;
      }
    }
    .el-step__icon-inner {
      position: relative;
      right: 22px;
      bottom: 10px;
    }
  }
  .is-wait .el-step__line-inner {
    border-color: #c0c4cc;
  }
  .is-finish .el-step__line-inner {
    border-color: #409eff;
  }
  .is-process .el-step__line-inner {
    border-color: #303133;
  }
  .is-error .el-step__line-inner {
    border-color: #f56c6c;
  }
  .is-success .el-step__line-inner {
    border-color: #67c23a;
  }
</style>
