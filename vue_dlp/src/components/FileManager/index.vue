<template>
  <div :class="outClass" :style="!selectedFileStatus ? '' : 'padding-bottom: 20px;'">
    <div v-if="hasTree" class="tree-container" :class="{ hidden: !showTree }">
      <slot name="tree"/>
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="hasTree" type="primary" size="mini" style="float:left" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button v-if="showRefresh" type="primary" size="mini" style="float:left" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-breadcrumb v-if="curFilePath && curFilePath.length > 0" separator="/">
          <el-breadcrumb-item v-for="item in curFilePath" :key="item.index">
            <i v-if="item.index === 0 && item.label === ''" class="el-icon-s-platform" style="color: black;" @click="filePathClick(item.index)"/>
            <a v-else style="color: #0c161e;" href="javascript:void(0);" :title="item.fullLabel" @click="filePathClick(item.index)">{{ item.label }}</a>
          </el-breadcrumb-item>
        </el-breadcrumb>
        <slot name="toolbar"/>
      </div>
      <slot/>
      <p v-if="selection && selection.length > 0" class="file-selected-status">{{ selectedFileStatus }}</p>
    </div>
  </div>
</template>

<script>
import { formatFileSize } from '@/utils'

export default {
  name: 'FileManager',
  props: {
    showRefresh: { type: Boolean, default: true },
    pathStrMaxLength: { type: Number, default: 45 },
    outClass: {
      type: String,
      default: 'app-container'
    },
    filePath: {
      type: Array,
      default() {
        return []
      }
    },
    selection: {
      type: Array,
      default() {
        return []
      }
    },
    sizeKey: {
      type: String,
      default: 'size'
    },
    typeKey: {
      type: String,
      default: 'type'
    },
    isFile: {
      type: Function,
      default(data) {
        return data[this.typeKey] === 2
      }
    }
  },
  data() {
    return {
      hasTree: true,
      showTree: true
    }
  },
  computed: {
    curFilePath() {
      const result = []
      const pathLen = this.filePath.length
      let pathStrLength = 0
      for (let i = 1; i <= pathLen; i++) {
        const item = { index: pathLen - i, label: i > 3 ? '...' : this.filePath[pathLen - i], fullLabel: this.filePath[pathLen - i] }
        pathStrLength += item.label.length
        result.splice(0, 0, item)
        if (i > 3) {
          break
        }
      }
      let toDelLength = pathStrLength - this.pathStrMaxLength
      if (toDelLength > 0) {
        for (let i = 0; i < result.length; i++) {
          if (result[i].label.length > 6) {
            result[i].label = result[i].label.substring(0, 3) + '...'
            toDelLength -= result[i].label.length - 6
          }
          if (toDelLength <= 0) {
            break
          }
        }
      }
      return result
    },
    selectedFileStatus() {
      if (!this.selection || this.selection.length === 0) {
        return ''
      }
      let dirCount = 0
      let fileCount = 0
      let filesSize = 0
      this.selection.forEach(item => {
        if (this.isFile(item)) {
          fileCount++
          filesSize += item[this.sizeKey]
        } else {
          dirCount++
        }
      })
      if (dirCount > 0 && fileCount > 0) {
        return this.$t('pages.serverlog_select_msg1', {
          length: this.selection.length,
          dirCount: dirCount,
          fileCount: fileCount,
          filesSize: formatFileSize(filesSize)
        });
      }
      if (dirCount > 0) {
        return this.$t('pages.serverlog_select_msg2', { dirCount: dirCount });
      }
      return this.$t('pages.serverlog_select_msg3', {
        fileCount: fileCount,
        filesSize: formatFileSize(filesSize)
      })
    }
  },
  created() {
    this.hasTree = !!this.$slots.tree
  },
  methods: {
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleRefresh() {
      this.$emit('refresh')
    },
    filePathClick(index) {
      if (this.filePath.length === index + 1) {
        return // 路径的最后一个节点，点击无效
      }
      this.filePath.splice(index + 1, this.filePath.length - index)
      this.$emit('clickPath', index, this.filePath)
    }
  }
}
</script>

<style lang="scss" scoped>
  .file-selected-status {
    position: absolute;
    bottom: -17px;
  }
  .el-breadcrumb {
    width: 370px;
    height: 29px;
    line-height: 28px;
    margin-left: 10px;
    padding-left: 10px;
    border: 1px solid #aaa;
    border-radius: 3px;
    float: left;
  }
  .el-icon-s-platform{
    cursor: pointer;
  }
</style>
