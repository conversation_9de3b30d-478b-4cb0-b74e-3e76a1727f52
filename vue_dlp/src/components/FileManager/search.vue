<template>
  <el-popover placement="bottom-end" width="356" trigger="click">
    <Form ref="searchForm" :model="query" label-position="left" label-width="80px">
      <FormItem :label="$t('table.name')">
        <el-input v-model="query.name" v-trim clearable maxlength=""/>
      </FormItem>
      <FormItem v-if="supportDateRange" :label="$t('table.updateTime')">
        <date-range-picker v-model="query.modified"/>
      </FormItem>
      <FormItem v-if="supportDateRange" :label="$t('table.createTime')">
        <date-range-picker v-model="query.creation"/>
      </FormItem>
    </Form>
    <div style="text-align: right; margin-top: 10px">
      <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSearch">{{ $t('table.search') }}</el-button>
    </div>
    <el-tooltip slot="reference" class="item" effect="light" placement="bottom-end">
      <div slot="content">{{ tips }}</div>
      <el-button type="primary" size="mini" :disabled="disabled">
        <svg-icon icon-class="search" />
      </el-button>
    </el-tooltip>
  </el-popover>
</template>

<script>
import DateRangePicker from './picker'

export default {
  name: 'FileSearch',
  components: { DateRangePicker },
  props: {
    tips: {
      type: String,
      default() {
        return this.$t('pages.terminal_explorer_search_tips')
      }
    },
    disabled: {
      type: Boolean,
      default: true
    },
    supportDateRange: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      query: {
        name: undefined,
        modified: [],
        creation: []
      }
    }
  },
  methods: {
    resetQuery() {
      this.query = { name: undefined, modified: [], creation: [] }
    },
    copyTimeRange(field, dst) {
      const range = this.query[field]
      if (range && range.length > 0) {
        dst[field] = [...range].map(item => item.getTime() / 1000).map((value, index) => index > 0 ? Math.ceil(value) + 24 * 60 * 60 - 1 : Math.floor(value))
      } else {
        dst[field] = [0, Number.MAX_SAFE_INTEGER]
      }
    },
    handleSearch() {
      const data = { name: this.query.name }
      if (this.supportDateRange) {
        this.copyTimeRange('modified', data)
        this.copyTimeRange('creation', data)
      }
      this.$emit('search', data)
    }
  }
}
</script>
