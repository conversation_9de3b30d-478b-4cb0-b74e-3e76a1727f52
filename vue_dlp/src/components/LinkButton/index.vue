<!--
  /**
  * 调用示例：
     <link-button :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
  */
-->
<template>
  <el-button v-if="showLinkBtn" :style="defaultStyle + btnStyle" :type="btnType" :class="btnClass" :size="size" :disabled="!formable" @click="toClick">{{ btnText ? btnText : 'E' }}</el-button>
</template>

<script>
import { getUrlParams } from '@/utils'

export default {
  name: 'LinkButton',
  props: {
    size: { // 按钮大小 值和el-button一样
      type: String,
      default: null
    },
    menuCode: { // 菜单编码
      type: String,
      default: null
    },
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    linkUrl: { // 跳转路径
      type: [String, Object],
      default: null
    },
    alwaysShow: { // 跳转菜单是否是隐藏菜单
      type: Boolean,
      default: false
    },
    //  跳转之前执行的方法
    beforeClick: {
      type: Function,
      default: null
    },
    clickFunc: {
      type: String,
      default: null
    },
    noStyle: { // 是否采用默认按钮样式
      type: Boolean,
      default: false
    },
    btnText: { // 按钮文字
      type: String,
      default: null
    },
    btnStyle: {
      type: String,
      default: ''
    },
    btnClass: {
      type: String,
      default: ''
    },
    btnType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      defaultStyle: this.btnType == 'primary' ? '' : 'line-height: 28px; padding: 0 9px; margin: 2px 0 0;',
      noLinkBtn: false
    }
  },
  computed: {
    showLinkBtn() {
      return (this.hasPermission(this.menuCode) || this.alwaysShow) && !this.noLinkBtn
    }
  },
  created() {
    const { noLayout } = getUrlParams(window.location.href)
    this.noLinkBtn = noLayout === 'true'
  },
  methods: {
    toClick() {
      if (this.beforeClick) {
        this.beforeClick()
      }
      if (this.clickFunc) {
        this.$emit(this.clickFunc)
      } else {
        this.$router.push(this.linkUrl)
      }
    }
  }
}
</script>
