<!--
    /**
     * 弹窗中新增表格数据的组件，页面中只展示按钮，通过按钮调出popover页面新增、修改数据
     * <AUTHOR>
     * @date 2022-06-14
     * 调用示例：
      <data-editor
        :modal="false"                          // 是否需要遮罩层, 默认true
        :append-to-body="true"                  // popover是否添加到body标签下
        :popper-class="className"               // popover类
        :formable="formable"                    // 是否可编辑
        :need-reference="true"                  // 是否需要slot=“reference” ()
        :popover-width="680"                    // popover宽度
        :updateable="updateable"                // 修改按钮是否可用
        :deletable="deletable"                  // 删除按钮是否可用
        :add-func="addFunc"                     // 新增数据的方法, 需要返回是否新增成功
        :update-func="updateFunc"               // 修改数据的方法, 需要返回是否修改成功
        :delete-func="deleteFunc"               // 删除数据的方法
        :cancel-func="cancelFunc"               // 取消的方法
        :before-update="beforeUpdate"           // 修改按钮点击后的方法
        @click="popoverClikc"                   // popover组件点击事件回调
      >
        // 此处放入popover里展示的内容，如el-form
        <Form>
          ...
        </Form>
      </data-editor>
    */
-->
<template>
  <div>
    <div v-if="modal" :class="{'pop-modal': formVisiable}"></div>
    <el-popover
      v-if="!!formable"
      ref="dataEditorPopover"
      v-model="formVisiable"
      v-drag-popover
      :title="titleOpt[type]"
      :append-to-body="appendToBody"
      :popper-class="customClass"
      :placement="placement"
      :visible-arrow="false"
      :width="popoverWidth"
      trigger="manual"
      @show="onShow"
      @hide="onHide"
    >
      <slot></slot>
      <!-- popover底部按钮栏 -->
      <div class="btn-group">
        <!-- 自定义按钮插槽 -->
        <slot name="btn-group" />
        <el-button size="small" type="primary" :loading="submitting" @click="handleConfirm">{{ $t('button.confirm') }}</el-button>
        <el-button size="small" @click="handleCancel">{{ $t('button.cancel') }}</el-button>
      </div>
      <div v-if="needReference" slot="reference">
        <slot name="attach-btn"></slot>
        <el-button size="small" :disabled="!addable" @click="handleAdd">{{ $t('button.add') }}</el-button>
        <el-button v-if="showImportAccDlg != null" :disabled="!importable" size="small" @click="showImportAccDlg()">{{ importDlgBtnName }}</el-button>
        <el-button size="small" :disabled="!updateable" @click="handleUpdate">{{ $t('button.edit') }}</el-button>
        <el-button size="small" :disabled="!deletable" @click="handleDelete">{{ $t('button.delete') }}</el-button>
        <slot name="self-btn"></slot>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'DataEditor',
  directives: {
    dragPopover: {
      // 指令的定义
      bind(el, binding, vnode) {
        const vm = vnode.context
        if (!vm.appendToBody) return
        const dragDom = el.querySelector('.el-popover')
        const dialogHeaderEl = el.querySelector('.el-popover__title')
        dialogHeaderEl.style.cssText += ';cursor:move;'

        // 获取原有属性 ie：dom元素.currentStyle； 火狐谷歌：window.getComputedStyle(dom元素, null);
        const getStyle = (function() {
          if (document.body.currentStyle) {
            return (dom, attr) => dom.currentStyle[attr]
          } else {
            return (dom, attr) => getComputedStyle(dom, false)[attr]
          }
        })()

        dialogHeaderEl.onmousedown = (e) => {
          // 鼠标按下，计算当前元素距离可视区的距离
          const disX = e.clientX - dialogHeaderEl.offsetLeft
          const disY = e.clientY - dialogHeaderEl.offsetTop

          const dragDomWidth = dragDom.offsetWidth
          const dragDomHeight = dragDom.offsetHeight

          const screenWidth = document.body.clientWidth
          const screenHeight = document.body.clientHeight

          const minDragDomLeft = dragDom.offsetLeft - (vm.sidebar.opened ? 210 : 54)
          const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth

          const minDragDomTop = dragDom.offsetTop - 84
          const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomHeight

          // 获取到的值带px 正则匹配替换
          let styL = getStyle(dragDom, 'left')
          let styT = getStyle(dragDom, 'top')

          if (styL == 'auto') {
            styL = screenWidth - dragDomWidth - getStyle(dragDom, 'right').replace(/\px/g, '')
            styT = screenHeight - dragDomHeight - getStyle(dragDom, 'bottom').replace(/\px/g, '')
          } else if (styL.includes('%')) {
            styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100)
            styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100)
          } else {
            styL = +styL.replace(/\px/g, '')
            styT = +styT.replace(/\px/g, '')
          }

          document.onmousemove = function(e) {
            // 通过事件委托，计算移动的距离
            let left = e.clientX - disX
            let top = e.clientY - disY
            // 边界处理
            if (-(left) > minDragDomLeft) {
              left = -minDragDomLeft
            } else if (left > maxDragDomLeft) {
              left = maxDragDomLeft
            }

            if (-(top) > minDragDomTop) {
              top = -minDragDomTop
            } else if (top > maxDragDomTop && maxDragDomTop >= 0) { // 当dialog的下边框处于可视范围内时，才进行边界处理
              top = maxDragDomTop
            }

            // 移动当前元素
            dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px !important;`

            // emit onDrag event
            vnode.child.$emit('dragDialog')
          }

          document.onmouseup = function(e) {
            document.onmousemove = null
            document.onmouseup = null
          }
        }
      }
    }
  },
  props: {
    modal: { type: Boolean, default: true },           // 是否需要遮罩层
    appendToBody: { type: Boolean, default: true },    // popover是否添加到body标签下
    popperClass: { type: String, default: '' },        // popover类
    placement: { type: String, default: 'bottom-start' },
    formable: { type: Boolean, default: true },        // 是否可编辑
    needReference: { type: Boolean, default: true },   // 是否需要slot=“reference”
    popoverWidth: { type: Number, default: 500 },      // popover宽度
    updateable: { type: Boolean, default: false },     // 修改按钮是否可用
    addable: { type: Boolean, default: true },         // 新增按钮是否可用
    importable: { type: Boolean, default: true },      // 导入按钮是否可用
    showable: { type: Boolean, default: false },       // 是否展示内容（作用于只展示数据，无法修改内容）
    deletable: { type: Boolean, default: false },      // 删除按钮是否可用
    addFunc: { type: Function, default: null },        // 新增数据的方法, 需要返回是否新增成功
    updateFunc: { type: Function, default: null },     // 修改数据的方法, 需要返回是否修改成功
    deleteFunc: { type: Function, default: null },     // 删除数据的方法
    cancelFunc: { type: Function, default: null },     // 取消的方法
    beforeUpdate: { type: Function, default: null },    // 修改按钮点击后的方法
    beforeAdd: { type: Function, default: null }       // 新增按钮点击后的方法
  },
  inject: {
    showImportAccDlg: { type: Function, default: null }, // 提供显示导入对话框的方法，具体使用可以查看imTool组件
    importDlgBtnName: { type: String, default: '' }      // 点击后显示导入对话框的按钮
  },
  data() {
    return {
      type: 'create',
      formVisiable: false,
      titleOpt: {
        create: this.i18nConcatText(this.$t('pages.data'), 'create'),
        update: this.i18nConcatText(this.$t('pages.data'), 'update'),
        batchAdd: this.i18nConcatText(this.$t('pages.data'), 'batchAdd')
      },
      curRoute: '',
      submitting: false
    }
  },
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar
    },
    show() {
      return this.appendToBody ? this.curRoute == this.$route.fullPath : true
    },
    customClass() {
      const hidden = this.show ? '' : 'hidden'
      const appendToBody = this.appendToBody ? 'data-editor-append-to-body' : ''
      return `data-editor ${hidden} ${appendToBody} ${this.popperClass || ''}`
    }
  },
  watch: {
    formVisiable(val) {
      const event = val ? 'popoverShow' : 'popoverHide'
      this.$emit(event)
    }
  },
  created() {
    this.curRoute = this.$route.fullPath
  },
  methods: {
    onShow() {
      // 监听 Popover 内容区域的点击事件
      const popoverContent = this.$refs.dataEditorPopover.$el.querySelector('.el-popover');
      if (popoverContent) {
        popoverContent.addEventListener('click', this.popoverClick);
      }
    },
    onHide() {
      // 移除 Popover 内容区域的点击事件
      const popoverContent = this.$refs.dataEditorPopover.$el.querySelector('.el-popover');
      if (popoverContent) {
        popoverContent.removeEventListener('click', this.popoverClick);
      }
    },
    hiddlenTool() {
      this.formVisiable = false
    },
    popoverClick(e) {
      this.$emit('click', e)
    },
    handleAdd() {
      this.type = 'create'
      this.beforeAdd && this.beforeAdd()
      this.formVisiable = true
      this.submitting = false
    },
    handleUpdate() {
      this.type = 'update'
      this.beforeUpdate && this.beforeUpdate()
      this.formVisiable = true
      this.submitting = false
    },
    handleDelete() {
      if (typeof this.deleteFunc == 'function') {
        this.deleteFunc()
      }
    },
    handleConfirm() {
      if (this.type == 'create' && typeof this.addFunc == 'function') {
        const addFunc = this.addFunc();
        if (addFunc) {
          // 新增数据成功时，隐藏popover
          if (addFunc instanceof Promise) {
            this.submitting = true
            addFunc.then(async valid => {
              if (valid) { this.formVisiable = false }
              this.submitting = false
            }).catch(() => {
              this.submitting = false
            })
          } else {
            this.formVisiable = false
          }
        }
      } else if (this.type == 'update' && typeof this.updateFunc == 'function') {
        const updateFunc = this.updateFunc();
        if (updateFunc) {
          // 修改数据成功时，隐藏popover
          if (updateFunc instanceof Promise) {
            this.submitting = true
            updateFunc.then(async valid => {
              if (valid) { this.formVisiable = false }
              this.submitting = false
            }).catch(() => {
              this.submitting = false
            })
          } else {
            this.formVisiable = false
          }
        }
      }
    },
    handleCancel() {
      this.formVisiable = false
      if (typeof this.cancelFunc == 'function') {
        this.cancelFunc()
      }
    }
  }
}
</script>

<style lang="scss">
  .data-editor {
    padding-top: 0;
    .el-popover__title {
      padding: 12px 13px 0;
      margin-left: -12px;
      margin-right: -12px;
      font-weight: bold;
    }
    .el-checkbox__input.is-disabled+span.el-checkbox__label{
      color: #aaa;
    }
    .el-input__inner {
      background-color: transparent;
    }
  }
  .data-editor-append-to-body {
    z-index: 999 !important;
    top: calc(20% + 100px) !important;
  }
</style>
<style lang="scss" scoped>
  .pop-modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.5);
    z-index: 999;
  }
  .el-input__inner {
    background-color: transparent;
  }
  .btn-group {
    text-align: right;
    margin-top: 10px;
    padding-right: 5px;
    .el-button {
      padding: 6px 14px 7px;
      font-size: 15px;
    }
  }
  >>>.el-form-item {
    margin-bottom: 5px;
  }
</style>
