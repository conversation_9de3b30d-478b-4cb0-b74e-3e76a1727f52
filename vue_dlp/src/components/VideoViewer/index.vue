<template>
  <el-dialog
    id="videoRecorderDlg"
    v-el-drag-dialog
    width="90%"
    top="5px"
    :modal="false"
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    :before-close="stopFunc"
    :append-to-body="true"
  >
    <div style="height: 100%; display: flex;">
      <div style="width: 100%; flex: 1; position: relative;">
        <el-canvas ref="videoCanvas" :total="imageSize" :open-db="openDb" :db-name="storeName" :store-name="videoPlayId" switch-size @start="startFunc" @pause="pauseFunc"></el-canvas>
        <el-button v-if="hasMultiple" :title="$t('pages.screenList')" class="see-screen-btn" @click="showPop = !showPop">
          <i v-show="!showPop" class="el-icon-d-arrow-left"></i>
          <i v-show="showPop" class="el-icon-d-arrow-right"></i>
        </el-button>
      </div>
      <transition name="slide" mode="out-in">
        <div v-if="hasMultiple" v-show="showPop" style="width: 15%">
          <el-tabs value="first">
            <el-tab-pane :label="$t('pages.screenList')" name="first">
              <ul>
                <li
                  v-for="(opt, optIndex) in multipleScreens || []"
                  :key="optIndex"
                  :class="screenId == opt ? 'li-disable' : 'li-select'"
                  @click="selectScreen(opt)"
                >{{ $t('pages.screen') + opt }}</li>
              </ul>
            </el-tab-pane>
          </el-tabs>
        </div>
      </transition>
    </div>
  </el-dialog>
</template>

<script>
import { getVideoDataInfo, playVideo, refleshVideo, stopVideo } from '@/api/behaviorManage/monitor/videoRecorder'
import ElCanvas from '@/components/Canvas';
import { alertError } from '@/utils/logVideo';

export default {
  name: 'VideoViewer',
  components: { ElCanvas },
  props: {
    searchReport: {
      type: Number,
      default: -1
    },
    title: {
      type: String,
      default() {
        return this.$t('pages.screenVideoPlayback')
      }
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      selectedCurRowData: null,
      videoPlayId: undefined,
      refleshHandle: undefined,
      reGetVideoHandle: undefined,
      videoPlaySubscribeHandle: undefined,
      imageSize: 0,
      showPop: false,
      multipleScreens: null,
      screenId: null,
      delayNo: 0, // 延时编号
      index: 0, // 当前请求的索引
      lastGetVideoDataInfoTime: 0, // 最近一次调用getVideoDataInfo方法的时间戳
      hasMultiple: false,
      openDb: true, // 是否开启浏览器indexdb数据库存储
      storeName: 'videoViewerDB'  // 数据库名称
    }
  },
  methods: {
    play(params) {
      if (params.multipleScreens) {
        this.multipleScreens = params.multipleScreens
        this.screenId = (this.multipleScreens || [])[0]
        this.hasMultiple = (this.multipleScreens || []).length > 1
        this.showPop = this.hasMultiple
      }
      this.handlePlay(params)
    },
    handlePlay(params, closeWindow) {
      // 每次播放延时编号和索引归零
      this.delayNo = 0
      this.index = 0
      this.lastGetVideoDataInfoTime = 0
      this.dialogFormVisible = true
      // this.imageSize = this.selectedCurRowData.recordNum
      this.imageSize = 10
      if (this.$refs['videoCanvas']) {
        this.$refs['videoCanvas'].clean()
      }
      this.selectedCurRowData = JSON.parse(JSON.stringify(params))
      if (this.videoPlayId) { this.stopFunc(closeWindow) }
      const searchReport = this.searchReport === -1 ? (this.$store.getters.reportModule ? 3 : 1) : this.searchReport
      Object.assign(this.selectedCurRowData, { searchReport, screenId: this.screenId, playVer: 2 })
      delete this.selectedCurRowData.multipleScreens
      playVideo(this.selectedCurRowData).then(respond => {
        this.videoPlayId = respond.data
        respond.requestId = this.videoPlayId
        this.refleshHandle = window.setInterval(() => {
          refleshVideo({ taskId: this.videoPlayId })
        }, 5000)
        this.$socket.subscribeToAjax(respond, '/play', (respond, handle) => {
          this.videoPlaySubscribeHandle = handle
          const imgInfo = respond.data
          if (imgInfo.status) {
            // 说明服务端还在下载视频文件中

          } else if (imgInfo.size) {
            this.imageSize = imgInfo.frameCount
            // 索引数据返回后代表录像数据已经缓存，那么心跳不再需要维持
            if (this.refleshHandle) {
              clearInterval(this.refleshHandle)
              this.refleshHandle = null
            }
            this.convertVideoImg()
          } else if (imgInfo.indexSize) {
            this.imageSize = imgInfo.indexSize
            // 索引数据返回后代表录像数据已经缓存，那么心跳不再需要维持
            if (this.refleshHandle) {
              clearInterval(this.refleshHandle)
              this.refleshHandle = null
            }
            this.convertVideoImg()
          } else {
            const imgInfo = respond.data
            if (!this.$refs['videoCanvas']) {
              this.stopFunc()
            }
            if (imgInfo instanceof Array) {
              // 当返回的数据为数组，说明该帧图片是差异帧，会返回屏幕有变化的区域图片数组
              this.imageSize = imgInfo[0].total
              imgInfo.forEach(obj => {
                obj.data = 'data:image/png;base64,' + obj.data
              })
            } else {
              // 当查看录屏出现异常时，将会以数字的枚举值传送至前端
              // 前端判断枚举值后再弹窗提示
              if (typeof imgInfo === 'number') {
                alertError(imgInfo)
                this.stopFunc(false)
                return
              }
              // 否则的话返回的就是一整个屏幕截图
              this.imageSize = imgInfo.total
              imgInfo.data = 'data:image/png;base64,' + imgInfo.data
            }
            this.$refs['videoCanvas'].appendImage(imgInfo)
          }
        })
      })
    },
    convertVideoImg() {
      if (this.videoPlayId && this.index < this.imageSize) {
        if (!this.reGetVideoHandle) {
          // 定时每分钟检查一次getVideoDataInfo获取图片信息是否超时，重新获取
          this.reGetVideoHandle = window.setInterval(() => {
            if (this.index < this.imageSize && new Date().getTime() - this.lastGetVideoDataInfoTime > 60000) {
              this.getVideoDataInfo()
            }
          }, 60000)
        }
        // 当连续获取图片超过十张数或心跳未正式发起请求时，延时一定时间后再次请求，避免出现拥塞控制导致http请求超时或者请求挂起报500错误
        const num = Math.floor(this.index / 10)
        if (num > this.delayNo) {
          const delayTime = this.refleshFlag ? 5000 : (num - this.delayNo) * 1000
          setTimeout(() => {
            this.lastGetVideoDataInfoTime = new Date().getTime()
            this.getVideoDataInfo()
          }, delayTime);
          this.delayNo = num
        } else {
          this.lastGetVideoDataInfoTime = new Date().getTime()
          this.getVideoDataInfo()
        }
      }
    },
    getVideoDataInfo() {
      getVideoDataInfo({ taskId: this.videoPlayId, index: this.index }).then(resp => {
        const taskId = resp.data.taskId
        // 响应回来的数据为非当前录像播放任务的数据，则直接返回
        if (taskId !== this.videoPlayId) {
          return
        }
        const lastIndex = resp.data.index // 后台返回最新使用解析的索引数据的索引位置
        if (!resp.data.images) {
          // 说明已经解析到最后， 因此只需要查询0
          return
        }
        if (resp.data.images && resp.data.images.length == 0) {
          console.warn('server not exist video data for taskId=' + this.videoPlayId)
          return
        }
        resp.data.images.forEach(img => {
          if (taskId !== this.videoPlayId) {
            return
          }
          this.appendImageToCanvas(img, taskId)
        })
        this.index = lastIndex + 1
        // if (resp.data.length == 100) {
        this.convertVideoImg()
        // }
      })
    },
    appendImageToCanvas(imgInfo, taskId) {
      if (!imgInfo) {
        console.log('append image to canvas error: image is empty')
        return
      }
      if (imgInfo instanceof Array) {
        // 当返回的数据为数组，说明该帧图片是差异帧，会返回屏幕有变化的区域图片数组
        imgInfo.forEach(obj => {
          obj.data = 'data:image/png;base64,' + obj.data
        })
      } else {
        // 否则的话返回的就是一整个屏幕截图
        imgInfo.data = 'data:image/png;base64,' + imgInfo.data
      }
      this.$refs['videoCanvas'].appendImage(imgInfo, taskId)
    },
    selectScreen(screenId) {
      this.screenId = screenId
      this.handlePlay(this.selectedCurRowData, false)
    },
    startFunc(data) { // 开始播放
    },
    pauseFunc() { // 暂停播放
      this.$socket.sendToUser(this.selectedCurRowData.terminalId, '/stopPlay', this.videoPlayId)
    },
    stopFunc: function(closeWindow) {
      if (this.videoPlaySubscribeHandle) {
        this.videoPlaySubscribeHandle.close()
        this.videoPlaySubscribeHandle = null
      }
      if (this.refleshHandle) {
        clearInterval(this.refleshHandle)
        this.refleshHandle = null
      }
      stopVideo({ taskId: this.videoPlayId }).then(respond => {
        this.videoPlayId = null
        if (closeWindow === undefined || closeWindow) {
          this.dialogFormVisible = false
        }
      })
      this.$refs['videoCanvas'].dropDB()
    }
  }
}
</script>
<style lang="scss" scoped>
  #videoRecorderDlg >>>.el-dialog{
    height: calc(100vh - 20px);
    .el-dialog__body{
      height: calc(100% - 35px);
      max-height: none;
      overflow: hidden;
    }
  }
  ul{
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
  }
  .li-select {
    padding: 2px 5px;
    line-height: 22px;
    cursor: pointer;
    &:hover{
      background: #ccc;
    }
  }
  .li-disable {
    padding: 2px 5px;
    line-height: 22px;
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  .see-screen-btn {
    position: absolute;
    top: 40%;
    height: 40px;
    margin: auto 0;
    right: 0;
    z-index: 999;
  }
</style>
