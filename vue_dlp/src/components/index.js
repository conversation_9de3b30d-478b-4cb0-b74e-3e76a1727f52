/**
 * 使用较多的组件进行全局注册
 */
import '@/icons' // 注册SvgIcon组件

/**
 * 从 .vue 文件名中提取 vue组件名
 */
function extractComponentNameFromFileName(fileName) {
  if (fileName.indexOf('index.vue') > -1) {
    // 文件名为index.vue的组件
    return fileName.split('./').join('').split('/')[0]
  }
  // 非index.vue的组件
  return fileName.split('/').pop().split('.')[0]
}

export default {
  install(Vue) {
    // 需要全局注册的组件列表
    const registerList = [
      'AuditFileDownloader', 'AuditLogExporter', 'AuditLogDelete', 'CopyPolicy', 'DataEditor', 'Dialog', 'Drawer', 'Form', 'FormItem',
      'GridTable', 'LinkButton', 'LocalZoomIn', 'SearchToolbar', 'ShowDetail', 'StgDialog', 'StrategyTargetTree',
      'Tag', 'TimeQuery', 'TrDialog', 'TreeMenu', 'TreeSelect', 'StrategyExtend', 'VideoViewer', 'StrategyCommonItems'
    ]
    // 需要全局注册的子组件列表
    const registerSubList = [
      'StgBaseFormItem', 'StgTargetFormItem', 'TransferTree', 'TerminalDetail', 'UserDetail', 'SearchItem'
    ]
    const componentList = registerList.concat(registerSubList)
    // require.context() 返回的是函数，通过keys()获取到的是文件名数组
    const requireComponent = require.context(
      '.', // 查看当前目录下的文件(查找需要文件的相对路径)
      true, // 查看子文件
      /.vue$/ // 匹配方式正则表达式，只查看后缀为.vue的文件
    )
    const componentNames = []
    const componentMap = {}
    requireComponent.keys().forEach(fileName => {
      let component = requireComponent(fileName)
      component = component.default || component
      let componentName = extractComponentNameFromFileName(fileName)
      // 如果组件在需要注册的组件列表中，则进行全局注册
      if (componentList.includes(componentName)) {
        componentNames.push(componentName)
        // 依据文件名处理好的，将要被注册到全局的组件名
        Vue.component(componentName, component)
        return
      }
      componentName = component.name
      if (componentName) {
        componentMap[componentName] = component
      }
    })
    Object.entries(componentMap).forEach(([name, component]) => {
      if (componentNames.includes(name)) {
        return
      }
      if (componentList.includes(name)) {
        Vue.component(name, component)
      }
    })
  }
}
