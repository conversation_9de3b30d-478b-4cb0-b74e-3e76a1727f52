<!--
  /**
  * 树形菜单组件，提供搜索功能及选择某节点功能，方便其他模块调用
  * <AUTHOR>
  * @date 2019-10-15
  * 调用示例：
    <tree-menu
      :data="data"                                  // 树结构的数据
      :height="200"                                 // 树容器的高度，未设置的话则等于父容器的高度
      multiple                                      // 是否支持多选，即是否显示复选框，默认为false
      lazy                                          // 是否懒加载子节点，需与 load 方法结合使用，默认为false
      :load="loadFunc"                              // 加载子树数据的方法，仅当 lazy 属性为true 时生效
      :is-filter="true"                             // 是否提供查找功能，默认为true
      :local-search="false"                         // 是否在本地搜索，默认为true。当设置为false时，需要从后台获取节点数据 getSearchList
      :get-search-list="getSearchList"              // 远程搜索时，获取节点数据的方法
      :select-search-func="selectSearchFunc"        // 选中搜索结果后的回调函数
      check-strictly                                // 显示复选框情况下，是否严格遵循父子不互相关联
      checked-all-keys                              // 是否强制勾选所有传入的节点
      default-expand-all                            // 默认展开所有节点
      :default-expanded-keys="defaultExpandedKeys"  // 默认展开传入的key数组对应的节点
      :accordion="false"                            // 手风琴模式，默认为false
      expand-on-click-node                          // 点击节点时，展开子节点
      :current-node-key="currentNodeKey"            // 当前选中的节点，string, number
      :filter-key="filterKey"                       // 传入需要过滤的节点的nodeKey值，详细见 props.filterKey
      :filter-node-method="filterNodeMethod"        // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏
      :icon-option="iconOption"                     // 树节点icon的配置对象，eg: { typeKey: 'oriData.dbType', key: value}, typeKey可指定data中某个属性为key，默认是type，key为 data.type , value 为 icon的 iconClass
      :render-content="renderContent"               // 自定义渲染函数，可用来更改树节点，默认为添加icon的方法
      :checked-keys="checkedKeys"                   // 传入选中的节点key数组
      :node-key="nodeKey"                           // 绑定nodeKey，默认绑定'id'
      :disabled-all-nodes="true"                    // 是否禁用所有节点的勾选框
      @node-click="nodeClick"                       // 单击节点的回调函数，事件共三个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。
      @check-change="checkChange"                   // 选中状态变更时的回调，事件有两个参数：第一个是所有选中的节点ID，第二个是所有选中的节点数据
    />
  */
-->
<template>
  <div class="tree-menu" :style="{width: conWidth === 0 ? '' : conWidth + 'px', height: height === 0 ? '' : height + 'px'}">
    <div v-if="isFilter" class="input-container">
      <el-input v-if="localSearch" v-model="filterText" clearable :placeholder="$t('components.enterContent')" @focus="focus"></el-input>
      <el-autocomplete
        v-else
        ref="autocomplete"
        v-model="filterText"
        value-key="label"
        :fetch-suggestions="querySearchAsync"
        :placeholder="$t('components.enterContent')"
        clearable
        @select="selectSearch"
        @clear="queryClear"
        @focus="emitState('focus')"
        @blur="emitState('blur')"
      >
        <template slot-scope="{ item }">
          <svg-icon :icon-class="iconTypes[item[iconTypes.typeKey] || item.type] || ''"></svg-icon>
          <span :title="item.label">{{ item.label }}</span>
        </template>
      </el-autocomplete>
    </div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-tree
        ref="tree"
        :data="data"
        :load="load"
        :lazy="lazy"
        :props="defaultProps"
        :node-key="nodeKey"
        :draggable="draggable"
        :allow-drop="allowDrop"
        :filter-node-method="filterNodeFunc"
        :accordion="accordion"
        :show-checkbox="multiple"
        :current-node-key="currentNodeKey"
        :default-checked-keys="defaultCheckedKeys"
        :check-strictly="checkStrictly"
        :default-expand-all="defaultExpandAll"
        :default-expanded-keys="defaultExpandedKeys"
        :expand-on-click-node="expandOnClickNode"
        :check-on-click-node="checkOnClickNode"
        :render-content="renderContentFunc"
        highlight-current
        class="filter-tree"
        @node-click="nodeClick"
        @node-contextmenu="nodeContextmenu"
        @check-change="handleCheckChange"
        @check="handleCheck"
        @current-change="currentChange"
        @node-collapse="nodeCollapse"
        @node-expand="nodeExpand"
      ></el-tree>
    </el-scrollbar>
    <div v-if="resizeable" class="widthResize" @mousedown="mousedown"></div>
  </div>
</template>

<script type="text/jsx">
export default {
  name: 'TreeMenu',
  props: {
    // 树容器的高度，未设置的话则等于父容器的高度
    height: {
      type: Number,
      default: 0
    },
    width: {
      type: Number,
      default: 0
    },
    // 是否可以调整大小
    resizeable: {
      type: Boolean,
      default: false
    },
    // 节点是否可拖拽
    draggable: {
      type: Boolean,
      default: false
    },
    allowDrop: {
      type: Function,
      default() {
        return null
      }
    },
    // 树结构数据
    data: {
      type: Array,
      default() {
        return []
      }
    },
    lazy: {
      type: Boolean,
      default() {
        return false
      }
    },
    load: {
      type: Function,
      default() {
        return null
      }
    },
    multiple: {
      type: Boolean,
      default() {
        return false
      }
    },
    nodeKey: {
      type: String,
      default() {
        return 'id'
      }
    },
    checkStrictly: {
      type: Boolean,
      default() {
        return false
      }
    },
    defaultExpandAll: {
      type: Boolean,
      default() {
        return false
      }
    },
    defaultExpandedKeys: {
      type: Array,
      default() {
        return ['0']
      }
    },
    defaultCheckedKeys: {
      type: Array,
      default() {
        return []
      }
    },
    expandOnClickNode: {
      type: Boolean,
      default() {
        return true
      }
    },
    currentNodeKey: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    isFilter: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 是否在本地搜索，默认为true
    localSearch: {
      type: Boolean,
      default: true
    },
    getSearchList: {
      type: Function,
      default() {
        // 测试数据
        // var searchList = []
        // for (let index = 0; index < 10000; index++) {
        //   var a = { 'label': '三全鲜食（啊啊）' + index, 'id': index }
        //   searchList.push(a)
        // }
        // return searchList
        return []
      }
    },
    // 选中搜索结果后的回调函数
    selectSearchFunc: {
      type: Function,
      default: null
    },
    filterNodeMethod: {
      type: Function,
      default: null
    },
    // 传入需要过滤的节点的nodeKey值，默认是id
    // 当传入值为 Object时，eg: { prop, value, showIfEqual, showIfNoProp }
    // prop为过滤的属性，value为过滤的值，showIfEqual为true时显示值相同的节点，showIfNoProp为ture时显示不包含该属性的节点
    filterKey: {
      type: [Number, String, Array, Object],
      default() {
        return ''
      }
    },
    // 手风琴模式，默认为false
    accordion: {
      type: Boolean,
      default: false
    },
    // 选中节点后，是否禁用子节点的选中操作，默认不禁用
    disableChildOnCheck: {
      type: Boolean,
      default() {
        return false
      }
    },
    checkOnClickNode: {
      type: Boolean,
      default() {
        return false
      }
    },
    checkedKeys: {
      type: [Array, String, Number],
      default() {
        return []
      }
    },
    // icon的配置对象
    iconOption: {
      type: Object,
      default() {
        return null
      }
    },
    // 渲染节点的方法
    renderContent: {
      type: Function,
      default: null
    },
    // 是否禁用所有节点的勾选框
    disabledAllNodes: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 当前加载数据的节点是否展示loading图标
    loadingIcon: {
      type: Boolean,
      default() {
        return false
      }
    },
    loadingNode: { // 与loadingIcon一起生效
      type: Object,
      default: null
    }
  },
  data() {
    return {
      filterText: '',
      tempText: '',
      searchList: [],
      searchListLoaded: false,
      timeout: null,
      selectedKeys: [], // 选中的节点id
      selectedData: [], // 选中的节点
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'isLeaf',
        id: 'id',
        disabled(data, node) {
          if (typeof data.disabled === 'function') {
            return data.disabled(data, node)
          }
          if (typeof data.disabled === 'boolean') {
            return data.disabled
          }
          return false
        }
      },
      iconClasses: {
        1: 'terminal',
        2: 'user',
        3: 'terminalGroup',
        4: 'userGroup',
        'search': 'search'
      },
      resizeLength: 0,
      checkChangeTimeoutHandle: undefined,
      filterTextChange: false // 搜索框内容变更的标识，用于判断是否将展开的节点合起
    }
  },
  computed: {
    filterNodeFunc() {
      const defaultFunc = (value, data, node) => {
        // filterKey和搜索内容为空
        if (!this.filterKey && !value) {
          // 本地搜索、不默认展开所有节点、搜索内容变化的情况，将展开的节点合起来
          if (this.localSearch && !this.defaultExpandAll && this.filterTextChange) {
            node.parent.expanded && this.$set(node.parent, 'expanded', false)
          }
          return true
        }
        if (!node.parent.label) node.parent.visible = true
        // 当前节点是否是被过滤的节点,若父节点为被过滤节点，则当前节点也为被过滤节点
        let isFilterNode = false
        if (Array.isArray(this.filterKey)) {
          this.filterKey.forEach(key => {
            if (node.key == key) isFilterNode = true
          })
        } else if (typeof this.filterKey == 'object') {
          const { prop, value, showIfEqual, showIfNoProp } = this.filterKey
          const data = node.data
          const hasProp = data.hasOwnProperty(prop)
          if (hasProp) {
            isFilterNode = showIfEqual ? data[prop] != value : data[prop] == value
          } else {
            isFilterNode = !showIfNoProp
          }
        } else {
          isFilterNode = this.filterKey && node.key == this.filterKey
        }
        node.isFilterNode = isFilterNode || node.parent.isFilterNode
        // 当前节点是否为搜索的节点
        const filterTextVisible = value ? data.label && data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1 : true
        // 当节点为非过滤节点且是搜索的节点，则显示当前节点(非本地搜索不通过搜索文字隐藏节点)
        const showNode = !node.isFilterNode && (filterTextVisible || !this.localSearch)
        if (showNode) this.showNode(node)
        // 非本地搜索，若父节点未加载则需要手动设置不展开
        if (!this.localSearch && node.parent.label && !node.parent.data.isLoadChildEnd) {
          this.$nextTick(() => {
            this.$set(node.parent, 'expanded', false)
          })
        }
        return showNode
      }
      // filterNodeMethod 未传入值时，使用默认的 defaultFunc
      return this.filterNodeMethod || defaultFunc
    },
    conWidth() {
      const rl = this.resizeLength >= 300 ? 300 : this.resizeLength <= 0 ? 0 : this.resizeLength
      if (this.resizeable) {
        return this.width ? this.width + rl : 200 + rl
      }
      return this.width + rl
    },
    // 树节点的渲染方法
    renderContentFunc() {
      const opts = this.iconTypes
      const typeKey = opts.typeKey || ''
      const defaultFunc = (h, { node, data, store }) => {
        const key = typeKey.split('.')
        let type = data
        key.forEach(k => {
          type = type[k]
        })
        type = type || data.type || ''
        let iconClass = opts[type]
        if (iconClass) {
          // data.active是终端的在线状态，true在线false离线，在线终端图标则显示为绿色
          if (iconClass.indexOf('usb-') === 0 && !data.dataCode) {
            iconClass += '-x'
          }
          const icon = <svg-icon style={data.colorClass ? 'color:' + data.colorClass : ''} icon-class={iconClass} />
          let loadNode = this.loadingNode
          loadNode = !loadNode && this.tree() ? this.tree().getCurrentNode() : loadNode
          return this.loadingIcon && loadNode && loadNode.id === data.id ? (<span title={node.label}>{icon} {node.label}<i class='el-icon-loading' style='position:absolute;right:2px'></i></span>) : (<span title={node.label}>{icon} {node.label}</span>)
        } else {
          return (<span title={node.label}>{node.label}</span>)
        }
      }
      // renderContent 未传入值时，使用默认的 defaultFunc
      return this.renderContent || defaultFunc
    },
    iconTypes() {
      return Object.assign(this.iconClasses, this.iconOption)
    }
  },
  watch: {
    filterText(val) {
      if (this.localSearch) {
        this.filterTextChange = true
        this.$refs.tree.filter(val)
        if (!val) {
          // 搜索内容为空时，展开的节点会被合起，这里将当前节点或默认展开的节点展开
          const currentNode = this.getCurrentNode()
          const nodeKey = currentNode ? currentNode[this.nodeKey] : this.defaultExpandedKeys[0]
          this.defaultExpandedKeys.splice(0, this.defaultExpandedKeys.length, nodeKey)
          this.filterTextChange = false
        }
      }
    },
    checkedKeys(val) {
      // const nodes = val.map((item) => {
      //   return this.$refs.tree.getNode(item) // 所有被选中的节点对应的node
      // })
      // const keys = nodes.map((item) => {
      //   if(!item.children || item.children.length === 0)
      //   return item.data.id
      // })
      this.checkSelectedNode(val)
    },
    filterKey: {
      deep: true,
      handler(val) {
        // 当filterKey变化时，过滤节点
        this.$refs.tree.filter(this.filterText)
      }
    },
    data: {
      deep: true,
      handler(val) {
        this.$nextTick(() => {
          if (this.multiple) {
            this.checkSelectedNode(this.checkedKeys)
          }
          if (this.disabledAllNodes) {
            this.defaultProps.disabled = () => { return this.disabledAllNodes }
          }
          const searchList = this.getSearchList().sort(this.compare('label'))
          if (!this.searchListLoaded || (JSON.stringify(this.searchList) != JSON.stringify(searchList))) {
            this.searchListLoaded = true
            this.searchList = searchList
          }
          this.localSearch && this.$refs.tree.filter(this.filterText)
          this.defaultExpandedKeys.splice()
        })
      }
    },
    disabledAllNodes(val) {
      this.defaultProps.disabled = () => { return val }
    }
  },
  mounted() {
    this.$refs.tree.filter(this.filterText)
  },
  created() {
    if (this.disabledAllNodes) {
      this.defaultProps.disabled = () => { return this.disabledAllNodes }
    }
  },
  methods: {
    emitState(state) {
      this.$emit('input-state', state)
    },
    handleFilter() {
      this.$refs.tree.filter(this.filterText)
    },
    clearFilter() { // 清空搜索框内容
      this.filterText = ''
    },
    showNode(node) {
      // 显示当前节点和其父节点
      node.visible = true
      if (node.parent.visible) return
      this.showNode(node.parent)
    },
    mousedown(e) {
      const target = e.target
      const startX = e.clientX
      const rl = this.resizeLength
      const nextEle = this.$el.parentNode.nextElementSibling
      const that = this
      let moveLen
      document.onmousemove = function(e) {
        moveLen = e.clientX - startX + rl
        that.resizeLength = moveLen
        nextEle.style.marginLeft = that.conWidth + 10 + 'px'
      }
      document.onmouseup = function(e) {
        if (that.resizeLength < 0) {
          that.resizeLength = 0
        } else if (that.resizeLength > 300) {
          that.resizeLength = 300
        }
        document.onmousemove = null
        document.onmouseup = null
        target.releaseCapture && target.releaseCapture() // 当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
      }
      target.setCapture && target.setCapture() // 该函数在属于当前线程的指定窗口里设置鼠标捕获
      return false
    },
    tree() { return this.$refs.tree },
    querySearchAsync(queryString, cb) {
      this.tempText = this.filterText
      // 一次性加载所有节点数据
      if (!this.searchListLoaded) {
        this.searchListLoaded = true
        this.searchList = this.getSearchList().sort(this.compare('label'))
      }
      if (!queryString) {
        this.$refs.tree.filter(this.filterText)
      }
      var searchList = this.searchList
      // 根据搜索内容，过滤节点数据
      var results = queryString ? searchList.filter(state => {
        const isSearchNode = state.label && state.label.toLowerCase().indexOf(queryString.toLowerCase()) > -1
        let isShowNode
        const key = state[this.nodeKey]
        if (Array.isArray(this.filterKey)) {
          isShowNode = this.filterKey.indexOf(key) == -1
        } else if (typeof this.filterKey == 'object') {
          const { prop, value, showIfEqual, showIfNoProp } = this.filterKey
          const data = state
          const hasProp = data.hasOwnProperty(prop)
          if (hasProp) {
            isShowNode = showIfEqual ? data[prop] == value : data[prop] != value
          } else {
            isShowNode = showIfNoProp
          }
        } else {
          isShowNode = key != this.filterKey
        }
        return isShowNode && isSearchNode
      }) : []
      clearTimeout(this.timeout)
      let delay = 0
      if (results.length > 100) {
        // 搜索结果超过100条，只展示前100条数据
        results.splice(100)
        results.push({ 'label': this.$t('components.enterDetails'), 'type': 'search' })
        delay = 200
      }
      if (!queryString) {
        cb(results)
      } else {
        this.timeout = setTimeout(() => {
          cb(results)
        }, delay)
      }
    },
    compare(prop) {
      return function(obj1, obj2) {
        const a = obj1[prop]
        const b = obj2[prop]
        if (typeof a == 'string') {
          return a.localeCompare(b, 'zh-CN')
        } else {
          if (a > b) {
            return 1
          }
          if (a < b) {
            return -1
          }
          return 0
        }
      }
    },
    selectSearch(nodeData) {
      if (nodeData.type == 'search') {
        const message = nodeData.label
        this.filterText = this.tempText
        this.$message({
          message: message,
          type: 'warning',
          duration: 3000
        })
        this.$refs.autocomplete.focus()
        return
      }
      if (this.selectSearchFunc) {
        this.selectSearchFunc(nodeData)
      } else { // 默认展开点击节点的父节点，并选中点击的节点
        if (nodeData.parentId) {
          // parentId 的值对应父节点nodeKey属性的值
          const node = this.findNode(this.data, nodeData.parentId, this.nodeKey)
          this.defaultExpandedKeys.splice(0, this.defaultExpandedKeys.length, node[this.nodeKey])
        }
        const selectedKeys = []
        if (this.multiple) {
          selectedKeys.push(...this.selectedKeys, nodeData[this.nodeKey])
        } else {
          selectedKeys.push(nodeData[this.nodeKey])
        }
        this.checkSelectedNode(selectedKeys)
      }
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(nodeData[this.nodeKey])
        // 搜索选中节点后，滚动到节点所在位置：通过给dom节点设置tabIndex，使其可以被focus
        // 定位到title属性和label一致的节点（文字的位置）
        let dom = this.$refs.tree.$el.querySelector(`span[title="${nodeData.label}"]`)
        // 若未设置title，则定位到is-current节点
        if (!dom) dom = this.$refs.tree.$el.querySelector('.el-tree-node.is-current>.el-tree-node__content')
        if (!dom) return
        dom.tabIndex = 0
        // 设置延时是因为加载节点、展开节点需要耗费时间
        setTimeout(() => {
          dom.focus()
        }, 1000)
      })
    },
    queryClear() {
      const input = this.$refs.autocomplete.$refs.input
      this.$nextTick(() => {
        input.blur()
        input.focus()
      })
    },
    disableChildNode: function(nodeData, disabled) { // 禁用或解禁子节点的选择框
      const flag = nodeData.disableChildOnCheck != undefined
      if (this.disableChildOnCheck || flag) {
        if (!nodeData || !nodeData.children || disabled === undefined) return
        const disableNode = function(nodes) {
          if (!nodes) return
          for (let i = 0, size = nodes.length; i < size; i++) {
            const subNode = nodes[i]
            subNode.disabled = disabled
            disableNode(subNode.children, disabled)
          }
        }
        const nodes = JSON.parse(JSON.stringify(nodeData.children))
        disableNode(nodes)
        nodeData.children.splice(0, nodeData.children.length, ...nodes)
        if (!disabled) delete nodeData.disableChildOnCheck
      }
    },
    // 节点被点击时的回调	共三个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。
    nodeClick(data, node, el) {
      this.$emit('node-click', data, node, el)
    },
    // 当某一节点被鼠标右键点击时会触发该事件 共四个参数，依次为：event、传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。
    nodeContextmenu(event, data, node, el) {
      this.$emit('node-contextmenu', event, data, node, el)
    },
    // 多选，节点勾选状态发生变化时的回调
    handleCheckChange(data, checked, indeterminate) {
      const checkedKeys = this.getCheckedKeys()
      const checkedNodes = this.getCheckedNodes()
      const halfCheckedKeys = this.getHalfCheckedKeys()
      const halfCheckedNodes = this.getHalfCheckedNodes()

      this.selectedKeys = checkedKeys.concat(halfCheckedKeys)
      this.selectedData = checkedNodes.concat(halfCheckedNodes)
      this.disableChildNode(data, checked)

      // 延迟触发check-change，避免多选时，频繁的触发check-change方法，造成卡顿
      if (this.checkChangeTimeoutHandle) {
        window.clearTimeout(this.checkChangeTimeoutHandle)
        this.checkChangeTimeoutHandle = null
      }
      this.checkChangeTimeoutHandle = window.setTimeout(() => {
        this.$emit('check-change', this.selectedKeys, this.selectedData)
      }, 100)
    },
    handleCheck(node, checkedInfo) {
      this.$emit('check', node, checkedInfo)
    },
    currentChange(data, node) { //	当前选中节点变化时触发的事件
      this.$emit('current-change', data, node)
    },
    nodeExpand(data, node, el) { //	节点被展开时触发的事件
      this.$emit('node-expand', data, node, el)
    },
    nodeCollapse(data, node, el) { // 节点被关闭时触发的事件
      this.$emit('node-collapse', data, node, el)
    },
    // 选中传进来的节点
    checkSelectedNode(checkedKeys) {
      if (checkedKeys && !Array.isArray(checkedKeys)) checkedKeys = [checkedKeys]
      if (checkedKeys.length > 0) {
        if (this.multiple) {
          this.checkSelectedNodes(checkedKeys)
        } else {
          var item = checkedKeys[0]
          this.$refs.tree.setCurrentKey(item)
        }
      } else {
        this.clearSelectedNodes()
        this.clearSelectedNode()
      }
    },
    // 多选，勾选上传进来的节点,若父子节点严格不关联，则将所有传进来的节点勾选上
    // 若父子节点相关联，则只将子节点勾选（父节点会自动显示为半选状态）
    checkSelectedNodes(checkedKeys) {
      if (this.checkStrictly) {
        this.$refs.tree.setCheckedKeys(checkedKeys)
      } else {
        const keys = checkedKeys.map(key => {
          const node = this.$refs.tree.getNode(key)
          if (node && node.childNodes.length === 0) {
            return key
          }
        })
        this.$refs.tree.setCheckedKeys(keys)
      }
    },
    // 单选，清空选中
    clearSelectedNode() {
      this.selectedData = ''
      this.$refs.tree.setCurrentKey(null)
    },
    // 多选，清空所有勾选
    clearSelectedNodes() {
      var checkedKeys = this.getCheckedKeys() // 所有被选中的节点的 key 所组成的数组数据
      var halfCheckedKeys = this.getHalfCheckedKeys()
      for (let i = 0; i < checkedKeys.length; i++) {
        this.$refs.tree.setChecked(checkedKeys[i], false)
      }
      for (let i = 0; i < halfCheckedKeys.length; i++) {
        this.$refs.tree.setChecked(halfCheckedKeys[i], false)
      }
    },
    // 获取keys对应的子树节点，keys=[1,2,3,4]
    // includeChildNode 是否包含子节点，默认为否，是：返回树包含keys的子节点
    // includeRootNode 是否包含根节点，默认为否
    getSubTreeNode: function(nodes, keys, includeChildNode, includeRootNode) {
      if (!keys || keys.length === 0 || !nodes || nodes.length === 0) {
        return []
      }
      const keyField = this.nodeKey // key属性名，如 id 或 dataId等
      const keyStrArray = []
      keys.forEach(function(id) {
        keyStrArray.push(id.toString())
      })
      // includeCurrentNode 子树节点，是否包含当前层级的节点
      const toSubTreeNode = function(nodes, includeCurrentNode) {
        let targetNodes = []
        if (nodes && nodes.length > 0) {
          for (let i = 0; i < nodes.length; i++) {
            const nodei = nodes[i]
            const existNode = keyStrArray.indexOf(nodei[keyField]) >= 0
            let subNodes
            if (!includeChildNode || !existNode) {
              subNodes = toSubTreeNode(nodei.children, false)
            }
            if (includeCurrentNode || existNode) {
              const targetNode = JSON.parse(JSON.stringify(nodei))
              if (subNodes && subNodes.length > 0) {
                targetNode.children = subNodes
              }
              targetNodes.push(targetNode)
            } else if (subNodes && subNodes.length > 0) {
              targetNodes = targetNodes.concat(subNodes)
            }
          }
        }
        return targetNodes
      }
      return toSubTreeNode(nodes, includeRootNode)
    },
    getCheckedKeys() {
      return this.$refs.tree.getCheckedKeys()
    },
    getCheckedNodes() {
      return this.$refs.tree.getCheckedNodes()
    },
    getHalfCheckedKeys() {
      return this.$refs.tree.getHalfCheckedKeys()
    },
    getHalfCheckedNodes() {
      return this.$refs.tree.getHalfCheckedNodes()
    },
    getNode(data) { // 根据 data 或者 key 拿到 Tree 组件中的 node,(data) 要获得 node 的 key 或者 data
      return this.tree().getNode(data)
    },
    // 根据 data 或者 key 拿到 Tree 组件中的 node以及所有上级节点的 data数据
    getNodePath(data) {
      return this.tree().getNodePath(data)
    },
    findNode: function(nodes, keyValue, keyField) {
      if (nodes && nodes.length > 0) {
        for (const i in nodes) {
          const nodei = nodes[i]
          if (nodei[keyField] + '' === keyValue + '') {
            return nodei
          } else {
            const temp = this.findNode(nodei.children, keyValue, keyField)
            if (temp) return temp
          }
        }
      }
      return null
    },
    getChildKeys(data) { // 根据 data 或者 key 拿到 Tree 组件中的node以及子节点的keys
      const node = this.getNode(data)
      const datas = [node.data]
      const keys = []
      while (datas.length > 0) {
        const data = datas.shift()
        keys.push(data[this.nodeKey])
        if (data.children) {
          datas.push(...data.children)
        }
      }
      return keys
    },
    addNode: function(node) {
      if (node && node.parentId) {
        // parentId 的值对应父节点nodeKey属性的值
        const temp = this.findNode(this.data, node.parentId, this.nodeKey)
        if (temp) {
          this.$refs.tree.append(node, temp[this.nodeKey])
        } else {
          this.$refs.tree.append(node, null)
        }
      }
    },
    updateNode: function(node) {
      if (node && node.id) {
        const temp = this.findNode(this.data, node[this.nodeKey], this.nodeKey)
        if (temp) {
          if (temp.parentId != node.parentId) {
            this.$refs.tree.remove(temp)
            this.addNode(node)
          } else {
            // 更新节点
            Object.assign(temp, node)
          }
        }
      }
    },
    removeNode(keys) {
      if (this.data && this.data.length > 0 && keys && keys.length > 0) {
        const that = this
        keys = typeof keys == 'object' ? keys : [keys]
        keys.forEach(function(key) {
          const temp = that.findNode(that.data, key.toString(), that.nodeKey)
          if (temp) {
            that.$refs.tree.remove(temp)
          }
        })
      }
    },
    setCurrent(node) {
      this.tree().setCurrentKey(node)
      this.$nextTick(() => {
        // 搜索选中节点后，滚动到节点所在位置：通过给dom节点设置tabIndex，使其可以被focus
        const dom = this.$refs.tree.$el.querySelector('.el-tree-node.is-current>.el-tree-node__content')
        if (!dom) return
        dom.tabIndex = 0
        // 设置延时是因为加载节点、展开节点需要耗费时间
        setTimeout(() => {
          dom.focus()
        }, 300)
      })
    },
    focus(event) {
      event.currentTarget.select()
    },
    selectCurrentNode(nodeKey) {
      this.tree().setCurrentKey(nodeKey)
      this.$nextTick(() => {
        // 搜索选中节点后，滚动到节点所在位置：通过给dom节点设置tabIndex，使其可以被focus
        const dom = this.$refs.tree.$el.querySelector('.el-tree-node.is-current>.el-tree-node__content')
        if (!dom) return
        dom.tabIndex = 0
        setTimeout(() => {
          dom.click()
        }, 300)
      })
    },
    // 获取当前被选中节点的 data，若没有节点被选中则返回 null
    getCurrentNode() {
      return this.tree().getCurrentNode()
    }
  }
}
</script>

<style lang="scss" scoped>
  .tree-menu{
    height: 100%;
    border: 1px solid #666;
    padding: 5px 6px;
    font-size: 14px;
    position: relative;
    .el-scrollbar{
      height: 100%;
    }
    .input-container+.el-scrollbar{
      height: calc( 100% - 30px );
    }
  }
  >>>.el-autocomplete{
    width: 100%;
  }
</style>

<style lang="scss">
  .tree-menu{
    .el-input__inner{
      border: 1px solid #666;
      &:hover{
        border-color: #888;
      }
      &:focus{
        border-color: #888;
      }
    }
    .el-checkbox__input.is-disabled .el-checkbox__inner{
      background-color: #ccc;
    }
    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after{
      border-color: #555;
    }
    .el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner{
      border-color: #888;
      &::before{
        background-color: #555;
      }
    }
    .scrollbar-wrapper{
      overflow-x: hidden;
      height: calc(100% - 15px);
      margin-top: 5px;
    }
  }
  .el-tree>.el-tree-node {
    display: inline-block;
    min-width: 100%;
  }
</style>

<style lang="scss" scoped>
  .widthResize{
    width: 4px;
    position: absolute;
    height: 100%;
    top: 0;
    right: 0;
    cursor: col-resize;
    // transition: background 1s;
    // &:hover{
    //   background: #5555d9;
    // }
  }
</style>
