<!--
  /**
  * 树形菜单组件，提供搜索功能及选择某节点功能，方便其他模块调用
  * <AUTHOR>
  * @date 2019-10-15
  * 调用示例：
    <tree-menu
      :data="data"                                  // 树结构的数据
      :height="200"                                 // 树容器的高度，未设置的话则等于父容器的高度
      multiple                                      // 是否支持多选，即是否显示复选框，默认为false
      lazy                                          // 是否懒加载子节点，需与 load 方法结合使用，默认为false
      :load="load"                                  // 加载子树数据的方法，仅当 lazy 属性为true 时生效
      :is-filter="true"                             // 是否提供查找功能，默认为true
      :local-search="false"                         // 是否在本地搜索，默认为true。当设置为false时，需要从后台获取节点数据 getSearchList
      :get-search-list="getSearchList"              // 远程搜索时，获取节点数据的方法
      :select-search-func="selectSearchFunc"        // 选中搜索结果后的回调函数
      check-strictly                                // 显示复选框情况下，是否严格遵循父子不互相关联
      default-expand-all                            // 默认展开所有节点
      :default-expanded-keys="defaultExpandedKeys"  // 默认展开传入的key数组对应的节点
      :accordion="false"                            // 手风琴模式，默认为false
      expand-on-click-node                          // 点击节点时，展开子节点
      :current-node-key="currentNodeKey"            // 当前选中的节点，string, number
      :filter-key="filterKey"                       // 传入需要过滤的节点的nodeKey值，详细见 props.filterKey
      :filter-node-method="filterNodeMethod"        // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏
      :icon-option="iconOption"                     // 树节点icon的配置对象，eg: { typeKey: 'oriData.dbType', key: value}, typeKey可指定data中某个属性为key，默认是type，key为 data.type , value 为 icon的 iconClass
      :render-content="renderContent"               // 自定义渲染函数，可用来更改树节点，默认为添加icon的方法
      :checked-keys="checkedKeys"                   // 传入选中的节点key数组
      :node-key="nodeKey"                           // 绑定nodeKey，默认绑定'id'
      :disabled-all-nodes="true"                    // 是否禁用所有节点的勾选框
      :disabled-nodes="disabledNodes"               // 禁用节点勾选框的方法
      :menu-name                                    // 区分菜单，主要是报表菜单中的统计对象部门树要做终端、终端分组、操作员、操作员分组不可同时勾选的限制使用
      @node-click="nodeClick"                       // 单击节点的回调函数，事件共三个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。
      @check-change="checkChange"                   // 选中状态变更时的回调，事件有两个参数：第一个是所有选中的节点ID，第二个是所有选中的节点数据
    />
  */
-->
<template>
  <div class="tree-menu" :style="{width: conWidth === 0 ? '' : conWidth + 'px', height: height === 0 ? '' : height + 'px'}">
    <div v-if="isFilter" class="input-container">
      <el-input v-if="localSearch" v-model="filterText" v-trim maxlength="" clearable :placeholder="$t('components.enterContent')" @focus="inputFocus"></el-input>
      <el-autocomplete
        v-else
        ref="autocomplete"
        v-model="filterText"
        v-trim
        value-key="label"
        :fetch-suggestions="querySearchAsync"
        :popper-append-to-body="false"
        :placeholder="$t('components.enterContent')"
        clearable
        @select="selectSearch"
        @clear="queryClear"
        @focus="emitState('focus')"
        @blur="emitState('blur')"
      >
        <template slot-scope="{ item }">
          <svg-icon :icon-class="filterResultIconClass(item)" :class="item.online ? 'green' : ''"></svg-icon>
          <span :title="item.label">{{ item.label }}</span>
        </template>
      </el-autocomplete>
    </div>
    <!-- vue-easy-tree -->
    <vue-easy-tree
      ref="tree"
      v-bind="$attrs"
      :node-key="nodeKey"
      height="100%"
      :data="data"
      :lazy="lazy"
      :load="load"
      :props="defaultProps"
      :indent="indent"
      :draggable="draggable"
      :allow-drag="allowDrag"
      :allow-drop="allowDrop"
      :filter-node-method="filterNodeFunc"
      :accordion="accordion"
      :show-checkbox="multiple"
      :current-node-key="currentNodeKey"
      :default-checked-keys="defaultCheckedKeys"
      :check-strictly="checkStrictly"
      :default-expand-all="defaultExpandAll"
      :default-expanded-keys="defaultExpandedKeys"
      :expand-on-click-node="expandOnClickNode"
      :check-on-click-node="checkOnClickNode"
      :render-content="renderContentFunc"
      highlight-current
      class="filter-tree"
      :style="`height: ${isFilter?'calc(100% - 30px)':'100%'};`"
      @node-click="nodeClick"
      @node-contextmenu="nodeContextmenu"
      @check="check"
      @check-change="checkChange"
      @current-change="currentChange"
      @node-expand="nodeExpand"
      @node-collapse="nodeCollapse"
      @node-drag-start="handleDragStart"
      @node-drag-enter="handleDragEnter"
      @node-drag-leave="handleDragLeave"
      @node-drag-over="handleDragOver"
      @node-drag-end="handleDragEnd"
      @node-drop="handleDrop"
    ></vue-easy-tree>
    <div v-if="resizeable" class="widthResize" @mousedown="mousedown"></div>
  </div>
</template>

<script type="text/jsx">
import VueEasyTree from '@wchbrad/vue-easy-tree';
// 样式文件，可以根据需要自定义样式或主题
// import '@wchbrad/vue-easy-tree/src/assets/index.scss';
import { debounce } from '@/utils'

export default {
  name: 'TreeMenu',
  components: { VueEasyTree },
  props: {
    // 树容器的高度，未设置的话则等于父容器的高度
    height: {
      type: Number,
      default: 0
    },
    // 树容器的宽度
    width: {
      type: Number,
      default: 0
    },
    // 是否可以调整大小
    resizeable: {
      type: Boolean,
      default: false
    },
    // 是否开启拖拽节点功能
    draggable: {
      type: Boolean,
      default: true
    },
    // 判断节点能否被拖拽
    allowDrag: {
      type: Function,
      default(draggingNode) {
        return false
      }
    },
    // 配置区分菜单(报表)
    menuName: {
      type: String,
      default() {
        return ''
      }
    },
    // 获取选中节点(报表)
    clickNode: {
      type: Array,
      default: function() {
        return []
      }
    },
    // 拖拽时判定目标节点能否被放置。type 参数有三种情况：'prev'、'inner' 和 'next'，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后
    allowDrop: {
      type: Function,
      default(draggingNode, dropNode, type) {
        // if (dropNode.data.type !== 3) {
        //   return type !== 'inner'
        // } else {
        //   return true
        // }
        return false
      }
    },
    // 树结构数据
    data: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否懒加载子节点，需与 load 方法结合使用
    lazy: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 加载子树数据的方法，仅当 lazy 属性为true 时生效，function(node, resolve)
    load: {
      type: Function,
      default() {
        return null
      }
    },
    // 节点是否可被选择
    multiple: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的
    nodeKey: {
      type: String,
      default() {
        return 'id'
      }
    },
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
    checkStrictly: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 是否默认展开所有节点
    defaultExpandAll: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 默认展开的节点的 key 的数组
    defaultExpandedKeys: {
      type: Array,
      default() {
        return []
      }
    },
    // 默认勾选的节点的 key 的数组
    defaultCheckedKeys: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。
    expandOnClickNode: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 当前选中的节点
    currentNodeKey: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 是否提供查找功能，默认为true
    isFilter: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 是否在本地搜索，默认为true
    localSearch: {
      type: Boolean,
      default: true
    },
    // 远程搜索时，获取节点数据的方法
    getSearchList: {
      type: Function,
      default() {
        // 测试数据
        // var searchList = []
        // for (let index = 0; index < 10000; index++) {
        //   var a = { 'label': '三全鲜食（啊啊）' + index, 'id': index }
        //   searchList.push(a)
        // }
        // return searchList
        return []
      }
    },
    // 选中搜索结果后的回调函数
    selectSearchFunc: {
      type: Function,
      default: null
    },
    // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏
    filterNodeMethod: {
      type: Function,
      default: null
    },
    // 传入需要过滤的节点的nodeKey值，默认是id
    // 当传入值为 Object时，eg: { prop, value, showIfEqual, showIfNoProp }
    // prop为过滤的属性，value为过滤的值，showIfEqual为true时显示值相同的节点，showIfNoProp为ture时显示不包含该属性的节点
    filterKey: {
      type: [Number, String, Array, Object, Function],
      default() {
        return ''
      }
    },
    // 手风琴模式，默认为false
    accordion: {
      type: Boolean,
      default: false
    },
    // 选中节点后，是否禁用子节点的选中操作，默认不禁用
    disableChildOnCheck: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点。
    checkOnClickNode: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 传入选中的节点key数组
    checkedKeys: {
      type: [Array, String, Number],
      default() {
        return []
      }
    },
    // icon的配置对象
    iconOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // 渲染节点的方法
    renderContent: {
      type: Function,
      default: null
    },
    // 是否禁用所有节点的勾选框
    disabledAllNodes: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 禁用节点勾选框的方法，参数 data, node
    disabledNodes: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      filterText: '',                 // 搜索的文本
      tempText: '',                   // 记录搜索的文本，当选中“输入详细内容以缩小范围”时，将 filterText 修改为记录的值
      searchList: [],                 // 存储搜索列表
      searchListLoaded: false,        // 搜索列表是否已存储
      timeout: null,                  // 展示搜索结果的延时器
      selectedKeys: [],               // 选中的节点id
      selectedData: [],               // 选中的节点
      defaultProps: {                 // 配置选项
        children: 'children',         // 指定子树为节点对象的某个属性值 string
        label: 'label',               // 指定节点标签为节点对象的某个属性值	string, function(data, node)
        isLeaf: 'isLeaf',             // 指定节点是否为叶子节点，仅在指定了 lazy 属性的情况下生效	boolean, function(data, node)
        disabled: (data, node) => {   // 指定节点选择框是否禁用为节点对象的某个属性值	boolean, function(data, node)
          // 禁用所有节点的勾选框
          if (this.disabledAllNodes) {
            return this.disabledAllNodes
          }
          // 外部传入的禁用节点勾选框的方法
          if (typeof this.disabledNodes === 'function') {
            return this.disabledNodes(data, node)
          }
          // 节点数据的 disabled 方法
          if (typeof data.disabled === 'function') {
            return data.disabled(data, node)
          }
          // 节点数据的 disabled 属性
          if (typeof data.disabled === 'boolean') {
            return data.disabled
          }
          return false
        }
      },
      resizeLength: 0,                        // 容器调整宽度的值
      checkChangeTimeoutHandle: undefined,    // 延迟触发check-change
      indent: 18,                             // 相邻级节点间的水平缩进，单位为像素
      filterTextChange: false                 // 搜索框内容变更的标识，用于判断是否将展开的节点合起
    }
  },
  computed: {
    filterNodeFunc() {
      const defaultFunc = (value, data, node) => {
        // filterKey和搜索内容为空
        if (!this.filterKey && !value) {
          // 本地搜索、不默认展开所有节点、搜索内容变化的情况，将展开的节点合起来
          if (this.localSearch && !this.defaultExpandAll && this.filterTextChange) {
            node.parent.expanded && this.$set(node.parent, 'expanded', false)
          }
          return true
        }
        if (!node.parent.label) node.parent.visible = true
        // 当前节点是否是被过滤的节点,若父节点为被过滤节点，则当前节点也为被过滤节点
        let isFilterNode = false
        if (Array.isArray(this.filterKey)) {
          this.filterKey.forEach(key => {
            if (node.key == key) isFilterNode = true
          })
        } else if (typeof this.filterKey == 'function') {
          isFilterNode = !this.filterKey(data)
        } else if (typeof this.filterKey == 'object') {
          const { prop, value, showIfEqual, showIfNoProp } = this.filterKey
          const data = node.data
          const hasProp = data.hasOwnProperty(prop)
          if (hasProp) {
            isFilterNode = showIfEqual ? data[prop] != value : data[prop] == value
          } else {
            isFilterNode = !showIfNoProp
          }
        } else {
          isFilterNode = this.filterKey && node.key == this.filterKey
        }
        node.isFilterNode = isFilterNode || node.parent.isFilterNode
        // 当前节点是否为搜索的节点
        const filterTextVisible = value ? data.label && data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1 : true
        // 当节点为非过滤节点且是搜索的节点，则显示当前节点(非本地搜索不通过搜索文字隐藏节点)
        const showNode = !node.isFilterNode && (filterTextVisible || !this.localSearch)
        if (showNode) this.showNode(node)
        // console.log('filter-----', node.label, showNode, !node.isFilterNode, isFilterNode, (filterTextVisible || !this.localSearch));
        return showNode
      }
      // filterNodeMethod 未传入值时，使用默认的 defaultFunc
      return this.filterNodeMethod || defaultFunc
    },
    // 计算容器的宽度，conWidth 为 0 时，不会设置容器的 width 样式
    conWidth() {
      // 容器调整宽度的边界大小
      const rl = this.resizeLength >= 300 ? 300 : this.resizeLength <= 0 ? 0 : this.resizeLength
      if (this.resizeable) {
        return this.width ? this.width + rl : 200 + rl
      }
      return this.width + rl
    },
    // 树节点icon的配置对象，合并了外部传入的 iconOption。typeKey 指定data的某个属性的值作为key
    iconTypes() {
      return {
        typeKey: 'type',
        search: 'search',
        1: 'terminal',
        2: 'user',
        3: 'terminalGroup',
        4: 'userGroup',
        ...this.iconOption
      }
    },
    // 树节点的渲染方法
    renderContentFunc() {
      const opts = this.iconTypes
      const typeKey = opts.typeKey || ''
      const defaultFunc = (h, { node, data, store }) => {
        const type = this.getValueFromObject(data, typeKey) || data.type || ''
        // 图标的 class
        let iconClass = opts[type]
        if (iconClass) {
          // todo 统一设置终端图标
          // usb终端 或 永久离线终端，未使用的终端 iconClass 添加 -x
          const isUsbOrOffline = iconClass.indexOf('usb-') === 0 || iconClass.indexOf('offline-') === 0
          if (isUsbOrOffline && !data.dataCode) {
            iconClass += '-x'
          }
          // 图标颜色
          const iconColor = data.colorClass ? `color: ${data.colorClass};` : ''
          const icon = <svg-icon style={iconColor} icon-class={iconClass} />
          return (<span title={node.label}>{icon} {node.label}</span>)
        } else {
          return (<span title={node.label}>{node.label}</span>)
        }
      }
      // renderContent 未传入值时，使用默认的 defaultFunc
      return this.renderContent || defaultFunc
    }
  },
  watch: {
    filterText(val) {
      if (this.localSearch) {
        this.filterTextChange = true
        this.handleFilter()
        if (!val) {
          // 搜索内容为空时，展开的节点会被合起，这里将当前节点或默认展开的节点展开
          const currentNode = this.getCurrentNode()
          const nodeKey = currentNode ? currentNode[this.nodeKey] : this.defaultExpandedKeys[0]
          this.defaultExpandedKeys.splice(0, this.defaultExpandedKeys.length, nodeKey)
          this.filterTextChange = false
        }
        this.resetItemWrapperWidth(0)
      }
    },
    checkedKeys(val) {
      // 将 checkedKeys 赋值给 selectedKeys
      if (this.multiple && val.length > 0) {
        this.selectedKeys = val
      }
      this.checkSelectedNode(val)
    },
    filterKey: {
      deep: true,
      handler(val) {
        // 当filterKey变化时，过滤节点
        this.handleFilter()
      }
    },
    data: {
      deep: true,
      handler(newVal, oldVal) {
        // 去掉 this.localSearch 的调用条件并上移至此。当 data 变化时，需要重新过滤节点，否则已过滤的节点会再次显示
        this.$nextTick(() => {
          // 当非本地搜索且搜索框有内容时，调用 handleFilter 后需要将被展开的节点收起来
          if (!this.localSearch && this.filterText) {
            this.handleFilter(true)
          } else {
            this.handleFilter()
          }
        })
        if (!Array.isArray(newVal)) return
        if (Array.isArray(oldVal)) {
          const isSameData = JSON.stringify(newVal) == JSON.stringify(oldVal)
          if (isSameData) return
        }
        this.$nextTick(() => {
          // 多选模式，勾选 checkedKeys
          if (this.multiple) {
            this.checkSelectedNode(this.selectedKeys)
          }
          // 获取搜索列表
          const searchList = this.getSearchList().sort(this.compare('label'))
          // 数据发生变化后，将 searchListLoaded 设置为 false，搜索时重新设置为 true
          this.searchListLoaded = false
          // 如果未存储或数据发生变化，则存储
          if (JSON.stringify(this.searchList) != JSON.stringify(searchList)) {
            this.searchList = searchList
          }
          // 当根节点只有一个，且没有默认展开的节点时，将根节点设置为默认展开
          if (newVal.length == 1 && this.defaultExpandedKeys.length == 0) {
            this.defaultExpandedKeys.push(newVal[0][this.nodeKey])
          }
          this.addScrollerEvent()
          this.resetItemWrapperWidth(0)
          this.$emit('data-change')
        })
      }
    }
  },
  created() {
    // 当根节点只有一个，且没有默认展开的节点时，将根节点设置为默认展开
    if (Array.isArray(this.data) && this.data.length == 1 && this.defaultExpandedKeys.length == 0) {
      this.defaultExpandedKeys.push(this.data[0][this.nodeKey])
    }
    this.resetItemWrapperWidth(100)
  },
  mounted() {
    this.handleFilter()
    this.addScrollerEvent()
  },
  activated() {
    this.data && this.data.splice()
  },
  beforeDestroy() {
    // console.log('tree destroy...................', this.data, this.tree().store)
    this.tree().store = null
    // this.data && this.data.splice(0)
    this.removeScrollerEvent()
  },
  destroyed() {},
  methods: {
    // 树组件
    tree() {
      return this.$refs.tree
    },
    // 添加滚动事件
    addScrollerEvent() {
      const scroller = this.$el.querySelector('.vue-recycle-scroller')
      // 初始化
      if (scroller) {
        this._onScroll = debounce(() => {
          this.resetItemWrapperWidth()
        }, 100)

        scroller.addEventListener('scroll', this._onScroll, true)
        scroller.addEventListener('click', this._onScroll, true)
      }
    },
    // 移除滚动事件
    removeScrollerEvent() {
      const scroller = this.$el.querySelector('.vue-recycle-scroller')
      if (scroller) {
        scroller.removeEventListener('scroll', this._onScroll)
        scroller.removeEventListener('click', this._onScroll)
      }
    },
    // 重置滚动容器的宽度，实现横向滚动条的变化，delay 延时触发时间（毫秒）
    resetItemWrapperWidth(delay) {
      if (delay != undefined) {
        // setTimeout 是宏任务，当delay有传值时，就使用 setTimeout
        setTimeout(() => {
          this.resetWidth()
        }, delay);
      } else {
        this.resetWidth()
      }
    },
    // 重置滚动容器的宽度的代码
    resetWidth() {
      const scroller = this.$el.querySelector('.vue-recycle-scroller')
      if (!scroller) return
      const scrollerWidth = scroller.clientWidth
      const scrollerWrapper = scroller.querySelector('.vue-recycle-scroller__item-wrapper')
      const itemViews = scroller.querySelectorAll('.vue-recycle-scroller__item-view')
      // 获取节点的最大宽度
      const width = Array.from(itemViews).reduce((width, dom) => {
        const maxWidth = Math.max(width, dom.offsetWidth)
        // 节点的 minWidth 设置成容器宽度
        dom.style.minWidth = `${scrollerWidth}px`
        return maxWidth
      }, 0)
      scrollerWrapper.style.width = width ? `${width}px` : '100%'
    },
    // 向父组件传递搜索框状态：focus、blur
    emitState(state) {
      this.$emit('input-state', state)
    },
    filterResultIconClass(item) {
      const attr = this.iconTypes.typeKey
      const classVal = item[attr] || item.type
      const className = this.iconTypes[classVal]
      return className || ''
    },
    // 调用过滤节点的方法, collapse 将展开的节点合起来，并展开默认展开的节点
    handleFilter(collapse) {
      this.tree().filter(this.filterText.trim())
      if (collapse) {
        setTimeout(() => {
          this.collapseAllNodes()
          // 展开默认展开的节点
          this.defaultExpandedKeys.splice()
        }, 0);
      }
    },
    // 将所有节点合起来
    collapseAllNodes() {
      // vue-easy-tree 内部方法获取所有节点 store._getAllNodes
      const nodes = this.$refs.tree.store._getAllNodes();
      nodes.forEach((node) => {
        node.expanded = false;
      });
    },
    // 清空搜索框内容
    clearFilter() {
      this.filterText = ''
    },
    // 设置当前节点及其父节点为显示状态
    showNode(node) {
      node.visible = true
      if (node.parent.visible) return
      this.showNode(node.parent)
    },
    // TreeMenu宽度拖拽变化的方法。TODO：修改为指令的方式？
    mousedown(e) {
      const target = e.target
      const startX = e.clientX
      const rl = this.resizeLength
      const nextEle = this.$el.parentNode.nextElementSibling
      const that = this
      let moveLen
      document.onmousemove = function(e) {
        moveLen = e.clientX - startX + rl
        that.resizeLength = moveLen
        nextEle.style.marginLeft = that.conWidth + 10 + 'px'
      }
      document.onmouseup = function(e) {
        if (that.resizeLength < 0) {
          that.resizeLength = 0
        } else if (that.resizeLength > 300) {
          that.resizeLength = 300
        }
        document.onmousemove = null
        document.onmouseup = null
        // 当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
        target.releaseCapture && target.releaseCapture()
      }
      // 该函数在属于当前线程的指定窗口里设置鼠标捕获
      target.setCapture && target.setCapture()
      return false
    },
    // 查询搜索结果，以列表的形式展示
    querySearchAsync(queryString, cb) {
      this.tempText = this.filterText
      // 一次性加载所有节点数据
      if (!this.searchListLoaded || this.searchList.length == 0) {
        this.searchListLoaded = true
        this.searchList = this.getSearchList().sort(this.compare('label'))
      }
      if (!queryString) {
        this.localSearch && this.handleFilter()
      }
      var searchList = this.searchList
      // 根据搜索内容，过滤节点数据
      var results = queryString ? searchList.filter(state => {
        const isSearchNode = state.label && state.label.toLowerCase().indexOf(queryString.trim().toLowerCase()) > -1
        let isShowNode
        const key = state[this.nodeKey]
        if (Array.isArray(this.filterKey)) {
          isShowNode = this.filterKey.indexOf(key) == -1
        } else if (typeof this.filterKey == 'function') {
          isShowNode = this.filterKey(state)
        } else if (this.filterKey && typeof this.filterKey == 'object') {
          const { prop, value, showIfEqual, showIfNoProp } = this.filterKey
          const data = state
          const hasProp = data.hasOwnProperty(prop)
          if (hasProp) {
            isShowNode = showIfEqual ? data[prop] == value : data[prop] != value
          } else {
            isShowNode = showIfNoProp
          }
        } else {
          isShowNode = key != this.filterKey
        }
        return isShowNode && isSearchNode
      }) : []
      // todo 分组节点和子节点不允许同时勾选可以作为一个组件功能。
      // 报表统计对象树结构搜索时，限制如果选中终端分组或操作员分组，则查询时列表不显示终端和操作员，反之同理（即：分组和组员不能同时选择）
      // 在原本查询结果results的基础上进行过滤，只保留type相同的数据可进行选择
      if (this.menuName === 'reportMenu') {
        if (this.clickNode.length > 0) {
          if (results.length > 0) {
            const filterArr = results.filter(item => item.type == this.clickNode[0].type)
            results = filterArr
          }
        }
      }
      clearTimeout(this.timeout)
      let delay = 0
      if (results.length > 100) {
        // 搜索结果超过100条，只展示前100条数据
        results.splice(100)
        results.push({ 'label': this.$t('components.enterDetails'), 'type': 'search' })
        delay = 200
      }
      // 搜索内容为空，或者没有变化时，直接返回搜索结果
      if (!queryString || this.tempText == this.filterText) {
        cb(results)
      } else {
        // 设置延时展示搜索结果
        this.timeout = setTimeout(() => {
          cb(results)
        }, delay)
      }
    },
    // 根据指定的prop，比较两个对象的prop进行排序
    compare(prop) {
      return function(obj1, obj2) {
        const a = obj1[prop]
        const b = obj2[prop]
        if (typeof a == 'string') {
          return a.localeCompare(b, 'zh-CN')
        } else {
          if (a > b) {
            return 1
          }
          if (a < b) {
            return -1
          }
          return 0
        }
      }
    },
    // 通过列表选中搜索的节点
    selectSearch(nodeData) {
      // 提示输入更多内容以缩小查找范围
      if (nodeData.type == 'search') {
        const message = nodeData.label
        this.filterText = this.tempText
        this.$message({
          message: message,
          type: 'warning',
          duration: 3000
        })
        this.$refs.autocomplete.focus()
        return
      }
      // 通过列表选中的节点属性与树节点的属性可能不一致，所以这边通过 getNode 获取树节点的数据
      const node = this.getNode(nodeData)
      nodeData = node.data
      const key = node.key
      if (this.selectSearchFunc) {
        this.selectSearchFunc(nodeData)
      } else {
        // 选中的节点key
        const selectedKeys = []
        if (this.multiple) {
          selectedKeys.push(...this.getCheckedKeys(), key)
        } else {
          selectedKeys.push(key)
        }
        // 选中节点
        this.checkSelectedNode(selectedKeys)
        this.nodeClick(nodeData, node)
        // 选中节点后，清除搜索框内容
        this.filterText = ''
      }
      // 设置为当前节点，并滚动到节点位置
      this.setCurrentKey(key)
    },
    expandParentNode(data) {
      if (!data) return
      const node = this.getNode(data)
      if (node) {
        // 默认展开点击节点的父节点
        const parentKey = node.parent.key
        parentKey != undefined && this.defaultExpandedKeys.splice(0, this.defaultExpandedKeys.length, parentKey)
      }
    },
    // 搜索框清除内容后，手动设置失焦并聚焦。这样重新输入查询内容时，才能显示搜索结果。
    queryClear() {
      const input = this.$refs.autocomplete.$refs.input
      setTimeout(() => {
        input.blur()
        input.focus()
      }, 300);
    },
    // 禁用或解禁子节点的选择框
    disableChildNode(nodeData, disabled) {
      if (!nodeData || !nodeData.children || disabled === undefined) return
      const flag = nodeData.disableChildOnCheck != undefined
      if (this.disableChildOnCheck || flag) {
        const disableNode = function(nodes) {
          if (!nodes) return
          for (let i = 0, size = nodes.length; i < size; i++) {
            const subNode = nodes[i]
            subNode.disabled = disabled
            disableNode(subNode.children, disabled)
          }
        }
        const nodes = JSON.parse(JSON.stringify(nodeData.children))
        disableNode(nodes)
        nodeData.children.splice(0, nodeData.children.length, ...nodes)
        if (!disabled) delete nodeData.disableChildOnCheck
      }
    },
    // 节点被点击时的回调	共三个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。
    nodeClick(data, node, el) {
      this.$emit('node-click', data, node, el)
    },
    // 当某一节点被鼠标右键点击时会触发该事件 共四个参数，依次为：event、传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。
    nodeContextmenu(event, data, node, el) {
      this.$emit('node-contextmenu', event, data, node, el)
    },
    // 当复选框被点击的时候触发共两个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、树目前的选中状态对象，包含 checkedNodes、checkedKeys、halfCheckedNodes、halfCheckedKeys 四个属性
    check(node, checkedInfo) {
      this.$emit('check', node, checkedInfo)
    },
    // todo 事件名称与 el-tree 相同，但是参数不一致，是否修改？
    // 节点选中状态发生变化时的回调
    // 共三个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、节点本身是否被选中、节点的子树中是否有被选中的节点
    // 本身未被选中，但是子树有被选中的节点，即为半选状态
    checkChange(data, checked, indeterminate) {
      this.disableChildNode(data, checked)
      this.emitCheckChange()
    },
    // 向父组件传递 'check-change' 事件
    emitCheckChange() {
      // 延迟触发check-change，避免多选时，频繁的触发check-change方法，造成卡顿
      if (this.checkChangeTimeoutHandle) {
        window.clearTimeout(this.checkChangeTimeoutHandle)
        this.checkChangeTimeoutHandle = null
      }
      this.checkChangeTimeoutHandle = window.setTimeout(() => {
        const checkedKeys = this.getCheckedKeys()
        const checkedNodes = this.getCheckedNodes()
        const halfCheckedKeys = this.getHalfCheckedKeys()
        const halfCheckedNodes = this.getHalfCheckedNodes()
        const checkedInfo = { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }
        // selectedKeys 只储存勾选的节点，半选节点从 checkedInfo 获取
        this.selectedKeys = checkedKeys
        this.selectedData = checkedNodes
        this.$emit('check-change', this.selectedKeys, this.selectedData, checkedInfo)
        // this.$emit('check-change', data, checked, indeterminate)
      }, 100)
    },
    // 当前选中节点变化时触发的事件
    currentChange(data, node) {
      this.$emit('current-change', data, node)
    },
    // 节点被展开时触发的事件
    nodeExpand(data, node, el) {
      // 实现手风琴模式效果
      if (this.accordion) {
        const nodeKey = node[this.nodeKey]
        const parentNode = node.parent
        const siblings = parentNode.childNodes
        siblings.forEach(n => {
          if (n[this.nodeKey] != nodeKey) {
            n.expanded = false
          }
        });
        this.$nextTick(() => {
          // 滚动到展开的节点的位置
          this.scrollToNode(data)
        })
      }
      this.resetItemWrapperWidth()
      // 展开节点时，将该节点设置为默认展开节点
      this.defaultExpandedKeys.splice(0, this.defaultExpandedKeys.length, data[this.nodeKey])
      this.$emit('node-expand', data, node, el)
    },
    // 节点被关闭时触发的事件
    nodeCollapse(data, node, el) {
      this.resetItemWrapperWidth()
      // 节点关闭时，从 defaultExpandedKeys 移除节点 key
      const index = this.defaultExpandedKeys.findIndex(key => key == data[this.nodeKey])
      if (index != -1) {
        this.defaultExpandedKeys.splice(index, 1)
      }
      this.$emit('node-collapse', data, node, el)
    },
    // 选中传进来的节点
    checkSelectedNode(checkedKeys) {
      if (checkedKeys && !Array.isArray(checkedKeys)) checkedKeys = [checkedKeys]
      if (checkedKeys.length > 0) {
        if (this.multiple) {
          this.checkSelectedNodes(checkedKeys)
        } else {
          var key = checkedKeys[0]
          this.setCurrentKey(key)
        }
      } else {
        this.clearSelectedNodes()
        this.clearSelectedNode()
      }
    },
    // 多选，勾选上传进来的节点, 若父子节点严格不关联，则将所有传进来的节点勾选上
    // 若父子节点相关联，则只将子节点勾选（父节点会自动显示为半选状态）
    checkSelectedNodes(checkedKeys) {
      // checkStrictly 为 true，父子节点不关联，则勾选时不改变父节点的状态
      this.setCheckedKeys(checkedKeys, this.checkStrictly)
      this.emitCheckChange()
    },
    // 单选，清空选中
    clearSelectedNode() {
      this.selectedData = ''
      this.setCurrentKey(null)
    },
    // 多选，清空所有勾选
    clearSelectedNodes() {
      // 所有被选中的节点的 key 所组成的数组数据
      const checkedKeys = [...this.getCheckedKeys(), ...this.getHalfCheckedKeys()]
      for (let i = 0; i < checkedKeys.length; i++) {
        this.setChecked(checkedKeys[i], false)
      }
    },
    // 通过 key / data 设置某个节点的勾选状态，使用此方法必须设置 node-key 属性
    // (key/data, checked, deep) 接收三个参数，1. 勾选节点的 key 或者 data 2. boolean 类型，节点是否选中 3. boolean 类型，是否设置子节点 ，默认为 false
    setChecked(data, checked, deep) {
      this.tree().setChecked(data, checked, deep)
      this.emitCheckChange()
    },
    // 获取keys对应的子树节点，keys=[1,2,3,4]
    // includeChildNode 是否包含子节点，默认为否，是：返回树包含keys的子节点
    // includeRootNode 是否包含根节点，默认为否
    getSubTreeNode(nodes, keys, includeChildNode, includeRootNode) {
      if (!keys || keys.length === 0 || !nodes || nodes.length === 0) {
        return []
      }
      const keyField = this.nodeKey // key属性名，如 id 或 dataId等
      const keyStrArray = []
      keys.forEach(function(id) {
        keyStrArray.push(id.toString())
      })
      // includeCurrentNode 子树节点，是否包含当前层级的节点
      const toSubTreeNode = function(nodes, includeCurrentNode) {
        let targetNodes = []
        if (nodes && nodes.length > 0) {
          for (let i = 0; i < nodes.length; i++) {
            const nodei = nodes[i]
            const existNode = keyStrArray.indexOf(nodei[keyField]) >= 0
            let subNodes
            if (!includeChildNode || !existNode) {
              subNodes = toSubTreeNode(nodei.children, false)
            }
            if (includeCurrentNode || existNode) {
              const targetNode = JSON.parse(JSON.stringify(nodei))
              if (subNodes && subNodes.length > 0) {
                targetNode.children = subNodes
              }
              targetNodes.push(targetNode)
            } else if (subNodes && subNodes.length > 0) {
              targetNodes = targetNodes.concat(subNodes)
            }
          }
        }
        return targetNodes
      }
      return toSubTreeNode(nodes, includeRootNode)
    },
    // 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点所组成的数组
    // (leafOnly, includeHalfChecked) 接收两个 boolean 类型的参数，1. 是否只是叶子节点，默认值为 false 2. 是否包含半选节点，默认值为 false
    getCheckedNodes(leafOnly, includeHalfChecked) {
      return this.tree().getCheckedNodes(leafOnly, includeHalfChecked)
    },
    // 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点的 key 所组成的数组
    // (leafOnly) 接收一个 boolean 类型的参数，若为 true 则仅返回被选中的叶子节点的 keys，默认值为 false
    getCheckedKeys(leafOnly) {
      return this.tree().getCheckedKeys(leafOnly)
    },
    // 通过 keys 设置目前勾选的节点，使用此方法必须设置 node-key 属性
    // (keys, leafOnly) 接收两个参数，1. 勾选节点的 key 的数组 2. boolean 类型的参数，若为 true 则仅设置叶子节点的选中状态，默认值为 false
    setCheckedKeys(keys, leafOnly = false) {
      return this.tree().setCheckedKeys(keys, leafOnly)
    },
    // 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点所组成的数组
    getHalfCheckedNodes() {
      return this.tree().getHalfCheckedNodes()
    },
    // 若节点可被选择（即 show-checkbox 为 true），则返回目前半选中的节点的 key 所组成的数组
    getHalfCheckedKeys() {
      return this.tree().getHalfCheckedKeys()
    },
    // 根据 data 或者 key 拿到 Tree 组件中的 node
    // (data) 要获得 node 的 key 或者 data
    getNode(data) {
      return this.tree().getNode(data)
    },
    // 根据 data 或者 key 拿到 Tree 组件中的 node以及所有上级节点的 data数据
    getNodePath(data) {
      return this.tree().getNodePath(data)
    },
    // 根据 属性及值 从树数据 data 查找对应节点的 data
    // (datas) 要获得 node 的 key 或者 data
    findNode(datas, keyValue, keyField) {
      if (datas && datas.length > 0) {
        for (const i in datas) {
          const data = datas[i]
          if (data[keyField] + '' === keyValue + '') {
            return data
          } else {
            const temp = this.findNode(data.children, keyValue, keyField)
            if (temp) return temp
          }
        }
      }
      return null
    },
    // 根据 data 或者 key 拿到 Tree 组件中的node以及子节点的keys
    getChildKeys(data) {
      const node = this.getNode(data)
      const datas = [node.data]
      const keys = []
      while (datas.length > 0) {
        const data = datas.shift()
        keys.push(data[this.nodeKey])
        if (data.children) {
          datas.push(...data.children)
        }
      }
      return keys
    },
    addNode(node) {
      if (node && node.parentId) {
        // parentId 的值对应父节点nodeKey属性的值
        const temp = this.findNode(this.data, node.parentId, this.nodeKey)
        if (temp) {
          this.tree().append(node, temp[this.nodeKey])
        } else {
          this.tree().append(node, null)
        }
      }
    },
    updateNode(node) {
      if (node && node[this.nodeKey]) {
        const temp = this.findNode(this.data, node[this.nodeKey], this.nodeKey)
        if (temp) {
          if (temp.parentId != node.parentId) {
            this.tree().remove(temp)
            this.addNode(node)
          } else {
            // 更新节点
            Object.assign(temp, node)
          }
        }
      }
    },
    removeNode(keys) {
      if (this.data && this.data.length > 0 && keys && keys.length > 0) {
        keys = typeof keys == 'object' ? keys : [keys]
        keys.forEach((key) => {
          const temp = this.findNode(this.data, key.toString(), this.nodeKey)
          if (temp) {
            this.tree().remove(temp)
          }
        })
      }
    },
    // node:要增加的节点；refNode:放置的目标节点。放置目标节点后
    insertAfter(node, refNode) {
      if (node && node.parentId && refNode && refNode.parentId) {
        const that = this
        // parentId 的值对应父节点nodeKey属性的值
        const nodeTemp = this.findNode(this.data, node.parentId, this.nodeKey)
        const refNodeTemp = this.findNode(this.data, refNode.parentId, this.nodeKey)
        if (nodeTemp && refNodeTemp) {
          if (nodeTemp.parentId == refNodeTemp.parentId) {
            that.$refs.tree.insertAfter(node, refNode)
          }
        }
      }
    },
    // node:要增加的节点；refNode:放置的目标节点。放置目标节点前
    insertBefore(node, refNode) {
      if (node && node.parentId && refNode && refNode.parentId) {
        const that = this
        // parentId 的值对应父节点nodeKey属性的值
        const nodeTemp = this.findNode(this.data, node.parentId, this.nodeKey)
        const refNodeTemp = this.findNode(this.data, refNode.parentId, this.nodeKey)
        if (nodeTemp && refNodeTemp) {
          if (nodeTemp.parentId == refNodeTemp.parentId) {
            that.$refs.tree.insertBefore(node, refNode)
          }
        }
      }
    },
    // 获取当前被选中节点的 key，使用此方法必须设置 node-key 属性，若没有节点被选中则返回 null
    getCurrentKey() {
      return this.tree().getCurrentKey()
    },
    // 通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
    // (key) 待被选节点的 key，若为 null 则取消当前高亮的节点
    setCurrentKey(key) {
      this.tree().setCurrentKey(key)
      this.expandParentNode(key)
      this.$nextTick(() => {
        key && this.scrollToCurrentNode()
      })
    },
    // 获取当前被选中节点的 data，若没有节点被选中则返回 null
    getCurrentNode() {
      return this.tree().getCurrentNode()
    },
    // 通过 node 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
    // (node) 待被选节点的 node
    setCurrentNode(node) {
      this.tree().setCurrentNode(node)
      this.$nextTick(() => {
        node && this.scrollToCurrentNode()
      })
    },
    // 滚动到当前节点的位置
    scrollToCurrentNode() { 
      const currentNodeData = this.getCurrentNode()
      this.scrollToNode(currentNodeData)
    },
    // 滚动到指定节点的位置
    scrollToNode(nodeData) {
      const $tree = this.tree()
      const scroller = $tree.$el.querySelector('.vue-recycle-scroller')
      if (!scroller || !nodeData) return
      const dataList = $tree.dataList
      const dataListLength = dataList.length
      const currentNodeIndex = dataList.findIndex(node => node.key == nodeData[this.nodeKey])
      const scrollerHeight = scroller.offsetHeight
      const wrapperHeight = scroller.querySelector('.vue-recycle-scroller__item-wrapper').offsetHeight
      const scrollerY = currentNodeIndex / dataListLength * wrapperHeight - scrollerHeight / 4
      scroller.scrollTo(scroller.scrollLeft, scrollerY)
    },
    // 该方法即将弃用，替换方法 setCurrentKey，删除前需将项目中调用该方法的地方替换为 setCurrentKey
    setCurrent(node) {
      this.setCurrentKey(node)
    },
    // 该方法即将弃用，替换方法 setCurrentKey，删除前需将项目中调用该方法的地方替换为 setCurrentKey
    selectCurrentNode(nodeKey) {
      this.setCurrentKey(nodeKey)
    },
    // 搜索框聚焦的方法
    inputFocus(event) {
      event.currentTarget.select()
    },
    // 节点开始拖拽时触发的事件	共两个参数，依次为：被拖拽节点对应的 Node、event
    handleDragStart(node, ev) {
      this.$emit('node-drag-start', node, ev)
    },
    // 拖拽进入其他节点时触发的事件	共三个参数，依次为：被拖拽节点对应的 Node、所进入节点对应的 Node、event
    handleDragEnter(draggingNode, dropNode, ev) {
      this.$emit('node-drag-start', draggingNode, dropNode, ev)
    },
    // 拖拽离开某个节点时触发的事件	共三个参数，依次为：被拖拽节点对应的 Node、所离开节点对应的 Node、event
    handleDragLeave(draggingNode, dropNode, ev) {
      this.$emit('node-drag-leave', draggingNode, dropNode, ev)
    },
    // 在拖拽节点时触发的事件（类似浏览器的 mouseover 事件）	共三个参数，依次为：被拖拽节点对应的 Node、当前进入节点对应的 Node、event
    handleDragOver(draggingNode, dropNode, ev) {
      this.$emit('node-drag-over', draggingNode, dropNode, ev)
    },
    // 拖拽结束时（可能未成功）触发的事件	共四个参数，依次为：被拖拽节点对应的 Node、结束拖拽时最后进入的节点（可能为空）、被拖拽节点的放置位置（before、after、inner）、event
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      this.$emit('node-drag-end', draggingNode, dropNode, dropType, ev)
    },
    // 拖拽成功完成时触发的事件	共四个参数，依次为：被拖拽节点对应的 Node、结束拖拽时最后进入的节点、被拖拽节点的放置位置（before、after、inner）、event
    handleDrop(draggingNode, dropNode, dropType, ev) {
      this.$emit('node-drop', draggingNode, dropNode, dropType, ev)
    }
  }
}
</script>

<style lang="scss" scoped>
  .tree-menu{
    height: 100%;
    border: 1px solid #666;
    padding: 5px 6px;
    font-size: 14px;
    position: relative;

    >>>.el-input__inner{
      border: 1px solid #666;
      &:hover{
        border-color: #888;
      }
      &:focus{
        border-color: #888;
      }
    }
    >>>.el-checkbox__input.is-disabled .el-checkbox__inner{
      background-color: #ccc;
    }
    >>>.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after{
      border-color: #555;
    }
    >>>.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner{
      border-color: #888;
      &::before{
        background-color: #555;
      }
    }
    .el-scrollbar{
      height: 100%;
    }
    >>>.scrollbar-wrapper{
      overflow-x: hidden;
      height: calc(100% - 15px);
      margin-top: 5px;
    }
    .input-container+.el-scrollbar{
      height: calc(100% - 30px);
    }
  }
  >>>.el-tree>.el-tree-node {
    display: inline-block;
    min-width: 100%;
  }
  >>>.el-tree-node__content>label.el-checkbox {
    margin: 0;
    padding: 6px;
    padding-left: 0;
  }
  >>>.el-autocomplete{
    width: 100%;
  }
  .widthResize{
    width: 4px;
    position: absolute;
    height: 100%;
    top: 0;
    right: 0;
    cursor: col-resize;
    // transition: background 1s;
    // &:hover{
    //   background: #5555d9;
    // }
  }
  >>>.vue-recycle-scroller.direction-vertical .vue-recycle-scroller__item-wrapper{
    min-width: 100%;
    transition: width 0.3s;
  }
  >>>.vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view{
    width: max-content;
    min-width: 100%;
  }
  >>>::-webkit-scrollbar{
    height: 9px;
  }
  >>>::-webkit-scrollbar-thumb {
    background-color: rgba(144,147,153,.3);
  }
  >>>::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144,147,153,.5);
  }
</style>
