<template>
  <div v-if="wechatObjectTreeData" style="display: flex">
    <tree-select
      ref="pushObjectTree"
      style="flex: 1"
      :width="width"
      multiple
      clearable
      is-filter
      node-key="dataId"
      :disabled="!editable"
      :data="wechatObjectTreeData"
      :checked-keys="checkKeys"
      @change="wechatTreeSelectChange"
    />
    <link-button v-show="showLinkBtn" style="margin-left: 5px" :formable="editable" btn-class="editBtn" :menu-code="'A71'" :link-url="'/system/messageNotify/userBind'"/>
  </div>
</template>

<script>
import { getCloudInfoBindTree } from '@/api/system/messageNotification/messagePush';

export default {
  name: 'WechatUserSelect',
  props: {
    width: {
      type: Number,
      default: 360
    },
    editable: {
      type: Boolean,
      default: true
    },
    checkKeys: {
      type: Array,
      default() {
        return []
      }
    },
    wechatTreeData: {
      type: Array,
      default() {
        return [];
      }
    },
    showLinkBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      wechatObjectTreeData: undefined
    }
  },
  watch: {
    '$store.state.commonData.bindUser'(val) {
      this.loadUserTreeData()
    }
  },
  created() {
    this.initTreeData()
  },
  methods: {
    // 初始化微信公众号用户的节点，当父组件的用户节点有值时，使用父组件的数据
    initTreeData() {
      if (this.wechatTreeData.length == 0) {
        this.loadUserTreeData()
      } else {
        this.wechatObjectTreeData = this.wechatTreeData
      }
    },
    loadUserTreeData() {
      getCloudInfoBindTree().then(res => {
        if (res.data) {
          this.wechatObjectTreeData = res.data
          this.$emit('getTreeData', this.wechatObjectTreeData)
        }
      })
    },
    wechatTreeSelectChange(key, nodeData) {
      const len = key.length
      for (let i = len - 1; i >= 0; i--) {
        if (Number(key[i]) < 0) {
          key.splice(i, 1)
        }
      }
      this.$nextTick(() => {
        this.$emit('getChangeValue', key)
      })
    }
  }
}
</script>
