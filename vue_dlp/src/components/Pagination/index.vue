<!--
  /**
  * 列表分页功能，结合列表使用
  * 调用示例：
    <pagination
      :total="tota"                     // 显示总条数
      :page="page"                      // 当前显示页数
      :limit="limit"                    // 当前页面显示条数
      :pageSizes="pageSizes"            // 每页显示个数选择器的选项设置，eg：[10, 20, 30, 50]
      :layout="layout"                  // 组件布局，子组件名用逗号分隔, "sizes, prev, pager, next, jumper, ->, total, slot"
      :background="false"               // 是否为分页按钮添加背景色,默认 true
      :small="true"                     // 组件显示小尺寸
      :hidden="hidden"                  // 隐藏组件
      disabled-last-page                // 是否禁用最后一页的跳转功能，默认false
      :disabled-last-page-limit="1000"  // 禁用最后一页跳转功能的限制条件，总数据量 total 大于这个值才会禁用
      @update:limit="updateLimit"       // 向父组件传递更新后的limit值
      @update:page="updatePage"         // 向父组件传递更新后的page值
      @pagination="func"                // 当前页或每页个数发生变化时的回调函数
    />
  */
-->
<template>
  <div :class="{'hidden':hidden}" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :small="small"
      :total="total"
      :pager-count="5"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <slot />
    </el-pagination>
  </div>
</template>

<script>

export default {
  name: 'Pagination',
  props: {
    small: {
      type: Boolean,
      default: false
    },
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    },
    // 是否禁用最后一页的跳转功能
    disabledLastPage: {
      type: Boolean,
      default: false
    },
    // 禁用最后一页跳转功能的限制条件，total > disabledLastPageLimit
    disabledLastPageLimit: {
      type: Number,
      default: 10000
    }
  },
  data() {
    return {
      lastPage: 1
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      }
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      }
    },
    maxPage() {
      return Math.ceil(this.total / this.pageSize)
    }
  },
  watch: {
    total(val) {
      this.$nextTick(() => {
        this.disabledLastPageBtn()
      })
    },
    currentPage(val) {
      this.$nextTick(() => {
        this.lastPage = val
        this.disabledLastPageBtn()
      })
    },
    maxPage(val) {
      this.$emit('update:max-page', val)
    },
    '$attrs.disabled'(val) {
      this.$nextTick(() => {
        !val && this.disabledLastPageBtn()
      })
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.$emit('pagination', 1, val)
    },
    // currentPage 改变时会触发
    handleCurrentChange(val) {
      // 总数据量 total > disabledLastPageLimit
      if (this.disabledLastPage && this.total > this.disabledLastPageLimit) {
        // 跳转前的页码距离最后一页大于3页时点击最后一页，阻止跳转
        if (this.lastPage < this.maxPage - 3 && val == this.maxPage) {
          this.currentPage = this.lastPage
          return
        }
      }
      this.currentPage = val
      this.$emit('pagination', val, this.pageSize)
    },
    // 设置最后一个页码的按钮禁用（此处仅设置样式，阻止跳转页码在 handleCurrentChange 实现
    disabledLastPageBtn() {
      if (!this.disabledLastPage) return
      const elPagerBtn = this.$el.querySelector('.el-pager');
      if (!elPagerBtn) return
      const lastBtn = Array.from(elPagerBtn.querySelectorAll('.number')).pop()
      let btnClassArr = lastBtn.className.split(' ')
      // 总数据量 total > disabledLastPageLimit 且 跳转前的页码距离最后一页大于 3页
      if (this.total > this.disabledLastPageLimit && this.lastPage < this.maxPage - 3) {
        !btnClassArr.includes('disabled') && btnClassArr.push('disabled')
      } else {
        btnClassArr = btnClassArr.filter(className => className != 'disabled')
      }
      lastBtn.className = btnClassArr.join(' ')
    }
  }
}
</script>
