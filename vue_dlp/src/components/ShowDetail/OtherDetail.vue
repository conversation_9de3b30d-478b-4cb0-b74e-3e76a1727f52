<template>
  <div>
    <el-button type="text" @click="showDetail()">{{ label }}</el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="label"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      :width="width"
    >
      <div v-if="undefined !== rowDetail" class="detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item v-for="(value, key) in rowDetail" :key="key" :label="$t(key)" >
            {{ value }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'OtherDetail',
  props: {
    searchId: {
      type: Number,
      default: null
    },
    width: {
      type: String,
      default: '700px'
    },
    height: {
      type: String,
      default: '100%'
    },
    label: {
      type: String,
      default: null
    },
    searchUrl: {
      type: Function,
      default: null
    }

  },
  data() {
    return {
      rowDetail: [],
      initRoute: '',  // 组件初始化所在的路由
      loading: false,
      visible: false
    }
  },
  computed: {
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    }
  },
  created() {
    this.initRoute = this.$route.name
    // this.initData();
  },
  methods: {
    initData() {
      this.searchUrl(this.searchId).then(res => {
        if (undefined === res.data) {
          this.visible = false;
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.notFoundData'), type: 'error', duration: 2000 })
          return
        }
        this.rowDetail = res.data
        this.visible = true;
      }).catch(e => { console.log(e) })
    },
    showDetail() {
      this.initData();
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 150px;
  max-width: 300px;
}
</style>
