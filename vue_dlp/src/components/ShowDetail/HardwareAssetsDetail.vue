<template>
  <div v-if="permission">
    <el-button type="text" @click="showDetail()">{{ label }}</el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="label"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      width="900px"
    >
      <div v-if="undefined !== data && data.length > 0" class="detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item v-for="(value, key) in data" :key="key" :label="value.name" >
            {{ value.value }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else class="detail-panel">
        <div style="text-align:center;height: 100%">
          <span style="line-height:200px;color: #909399">{{ $t('text.noData') }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
  <div v-else>{{ label }}</div>
</template>

<script>

import { getAssetInfoDetail } from '@/api/assets/assetsConfig/assetsView';

export default {
  name: 'HardwareAssetsDetail',
  props: {
    searchParam: {
      type: Object,
      default() {
        return {}
      }
    },
    label: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      data: {},
      initRoute: '',  // 组件初始化所在的路由
      visible: false
    }
  },
  computed: {
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    },
    permission() {
      return this.hasPermission('B42')
    }
  },
  created() {
    this.initRoute = this.$route.name
  },
  methods: {
    initData() {
      // 组装需要的数据
      // type=1 硬件资产
      this.searchParam['type'] = 1;
      this.searchParam['page'] = 1;
      this.searchParam['limit'] = 20;
      this.searchParam['sortName'] = 'id';
      this.searchParam['sortOrder'] = 'desc';
      getAssetInfoDetail(this.searchParam).then(res => {
        if (undefined === res.data) {
          this.visible = false;
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.notFoundData'), type: 'error', duration: 2000 })
          return
        }
        const result = res.data
        const arr = [];
        result.map(info => {
          // 处理为 name、value
          const temp = {};
          temp.name = info.name;
          temp.value = info.value;
          arr.push(temp);
        })
        this.data = arr;
        this.visible = true;
      }).catch(e => { console.log(e); })
    },
    showDetail() {
      this.initData();
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
>>>.el-descriptions-item__content {
  max-width: 430px;
}
</style>
