<template>
  <div v-if="childData" class="nowrap-label">
    <el-button type="text" :style="`max-width: calc(100% - ${labelIndent}px);white-space:initial;text-align: left;`" :title="label" @click="showDetail()">{{ label }}</el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      border="true"
      :title="label"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      width="700px"
    >
      <div v-if="undefined !== data" class="detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.usbGroup')">{{ data.groupName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.usbName')">{{ data.usbName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.usbSize')">{{ data.usbSize }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.usbCode')">{{ data.usbCode }}</el-descriptions-item>
          <el-descriptions-item :label="$t('table.vid')">{{ data.vid }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.manufacturer')">{{ data.manufacturer }}</el-descriptions-item>
          <el-descriptions-item :label="$t('table.pid')">{{ data.pid }}</el-descriptions-item>
          <el-descriptions-item :label="$t('table.deviceSerialNumber')">{{ data.srcPnpDeviceId }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.usbType')">{{ usbTypeFormatter(data.usbType) }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.driverType')">{{ driverTypeFormatter(data.driverType) }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.remark')">{{ data.memo }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
  <div v-else class="nowrap-label">
    <span :title="label">{{ label }}</span>
  </div>
</template>

<script>
import { getUsbTypeDict } from '@/utils/dictionary'
export default {
  name: 'TimeInfoDetail',
  props: {
    searchId: {
      type: Number,
      default: null
    },
    label: {
      type: String,
      default: null
    },
    childData: {
      type: Object,
      default() {
        return undefined;
      }
    },
    labelIndent: {
      type: Number, default: 0
    }
  },
  data() {
    return {
      data: [],
      initRoute: '',  // 组件初始化所在的路由
      visible: false,
      usbTypeOptions: getUsbTypeDict(),
      driverTypeOptions: [
        { value: 1, label: this.$t('pages.driverType1') },
        { value: 2, label: this.$t('pages.driverType2') }
      ]
    }
  },
  computed: {
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    }
  },
  created() {
    this.initRoute = this.$route.name
  },
  methods: {
    initData() {
      if (undefined === this.childData) {
        this.visible = false;
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.notFoundData'), type: 'error', duration: 2000 })
        return
      }
      this.data = this.childData
      this.visible = true;
    },
    showDetail() {
      this.initData();
    },
    usbTypeFormatter: function(usbType) {
      let usbTypeName = this.$t('pages.unknownType')
      this.usbTypeOptions.forEach(option => {
        if (option.value === usbType) {
          usbTypeName = option.label
        }
      })
      return usbTypeName
    },
    driverTypeFormatter: function(usbType) {
      let driverTypeName = this.$t('pages.unknownType')
      this.driverTypeOptions.forEach(option => {
        if (option.value === usbType) {
          driverTypeName = option.label
        }
      })
      return driverTypeName
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
.nowrap-label{
  word-break:keep-all;
  text-overflow:ellipsis;
  overflow:hidden;
  white-space: nowrap;
  margin: 0;
}
>>>.el-descriptions-item__label {
  min-width: 140px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 200px;
}
</style>
