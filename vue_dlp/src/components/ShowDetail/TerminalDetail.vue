<template>
  <div v-if="permission" class="nowrap-label">
    <el-button type="text" :style="`max-width: calc(100% - ${labelIndent}px);`" :title="label" @click="showDetail()">{{ label }}</el-button>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      border="true"
      :title="label"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      width="810px"
    >
      <div class="detail-panel">
        <el-descriptions class="margin-top" :column="2" border>
          <el-descriptions-item :label="$t('table.terminalName')" content-class-name="ellipsis">
            <span v-for="(icon,index) in icons" :key="'icon_' + index" :title="icon.title" :style="{ 'margin-right': '3px' }">
              <svg-icon v-if="icon" :icon-class="icon.class" :style="icon.style"/>
            </span>
            <span :title="data.name">{{ data.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalCode')">
            {{ data.id }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.computerName')">
            {{ data.computerName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalType')">
            {{ data.typeName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.terminalVersion')">
            {{ data.version }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.terminalGroup')" content-class-name="ellipsis">
            <span :title="data.groupNames">{{ data.groupNames }}</span>
          </el-descriptions-item>
          <el-descriptions-item v-if="3 !== data.type" :label="$t('table.loginMode')">
            {{ data.loginModeName }}
          </el-descriptions-item>
          <el-descriptions-item v-if="3 !== data.type && 1 === data.loginMode" :label="$t('pages.autoLoginUser')">
            {{ data.autoUserLoginName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.curLoginUser')">
            {{ data.currentLoginUserName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.ip')">
            {{ data.mainIp }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.mac')">
            {{ data.mainMac }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.saleModuleNum')">
            <span v-if="hasPermission('!A22') || isRecycleBin" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ `${saleModules} / ${ modules.length}` }}</span>
            <link-button
              v-else
              menu-code="A22"
              btn-style="border: none"
              :btn-text="String(`${saleModules} / ${ modules.length}`)"
              :click-func="'saleModuleDetail'"
              @saleModuleDetail="saleModuleDetail"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('table.closeModuleNum')">
            <span v-if="hasPermission('!A23') || isRecycleBin" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ data.filterModules }}</span>
            <link-button
              v-else
              menu-code="A23"
              btn-style="border: none"
              :btn-text="String(data.filterModules)"
              :click-func="'closeModuleDetail'"
              @closeModuleDetail="closeModuleDetail"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('table.termStgDefNum')">
            <span v-if="hasPermission('!B11') || isRecycleBin" style="'line-height: 28px; padding: 0 9px; margin: 2px 0 0;'"> {{ String(termStgNum) }}</span>
            <link-button
              v-else
              menu-code="B11"
              btn-style="border: none"
              :btn-text="String(termStgNum)"
              :click-func="'stgDetail'"
              @stgDetail="stgDetail"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('table.lastOnlineTime')">
            {{ data.lastOnlineTime }}
          </el-descriptions-item>

          <el-descriptions-item :label="$t('text.remark')">
            {{ data.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
  <div v-else class="nowrap-label">
    <span :title="label">{{ label }}</span>
  </div>
</template>

<script>
import { getTerminalDetailById } from '@/api/system/terminalManage/terminal'
import { termStatusIconFormatter } from '@/utils/formatter';
import LinkButton from '@/components/LinkButton/index.vue';
import { getModuleInfoByProdId } from '@/api/system/terminalManage/moduleConfig';
import { listParentByTermId } from '@/api/system/terminalManage/department';
export default {
  name: 'TerminalDetail',
  components: { LinkButton },
  props: {
    searchId: { type: Number, default: null },
    label: { type: String, default: null },
    labelIndent: { type: Number, default: 0 },
    getTermsInfo: {
      type: Function,
      default: function() {
        return undefined
      }
    }
  },
  data() {
    return {
      initRoute: '',  // 组件初始化所在的路由
      visible: false,
      data: {},
      termStgNum: 0,
      saleModules: 0, // 终端已分配模块数
      modules: [], // 某终端类型的销售模块
      allModules: [], // 所有终端类型销售模块
      osTypes: { 0: 'win', 1: 'linux', 2: 'mac', 3: 'phone' },
      icons: [] // 终端在线状态图标
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    },
    isRecycleBin() {
      // 回收站分组的终端详情跳转按钮置灰
      return !((this.data.useType !== undefined) && this.data.useType === 0);
    },
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    },
    permission() {
      return this.searchId && this.hasPermission('B24')
    }
  },
  created() {
    this.initRoute = this.$route.name
  },
  methods: {
    initData() {
      const exists = this.checkIdExists(this.searchId);
      const termsInfo = this.getTermsInfo()
      let term
      if (termsInfo) {
        for (const item of termsInfo) {
          if (item.id === this.searchId) {
            term = item
            break
          }
        }
      }
      if (exists) {
        getTerminalDetailById(this.searchId, term).then(async res => {
          if (undefined === res.data) {
            this.visible = false;
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.notFoundData'), type: 'error', duration: 2000 })
            return
          }
          this.data = res.data
          const effectiveStgTreeList = this.filterRowDatas(this.data.effectiveStgTree)
          this.termStgNum = this.getEffectStgNum(effectiveStgTreeList)
          const allModuleRes = await getModuleInfoByProdId(511)
          this.allModules = allModuleRes.data || []
          const data = this.data.moduleMap
          if (data && data.modules) {
            // 过滤模块名称为空的模块
            const nameIsEmptyModuleIds = this.allModules.filter(m => m.moduleName === '').map(m => m.moduleId)
            // 统计终端分配的模块数要排除基础模块和报表模块，报表模块不一定开通了，不能直接减2
            this.saleModules = data.modules[this.searchId].filter(id => nameIsEmptyModuleIds.indexOf(id) <= -1).filter(id => id > 2).length
          }
          if (data && data.items && data.items[0]) {
            const { type } = data.items[0]
            const osGroup = this.osTypes[type & 0x0f]
            this.modules = this.allModules.filter(moduleInfo => this.moduleFilter(moduleInfo, osGroup, type))
          }
          this.icons = termStatusIconFormatter(res.data.items[0])
          this.visible = true
        }).catch(e => { console.log(e); })
      }
    },
    async getTermGroup(terminalId) {
      const res = await listParentByTermId(terminalId)
      const groups = res.data
      if (groups.length === 0) {
        return {}
      }
      const rootName = groups[groups.length - 1].name
      if (groups.length === 1) {
        return { ...groups[0], names: rootName }
      }
      const groupNames = [rootName]
      for (let i = 0; i < groups.length - 1; i++) {
        groupNames.push(groups[i].name)
      }
      return { ...groups[groups.length - 2], names: groupNames.join(' -> ') }
    },
    moduleFilter(moduleInfo, osGroup, type) {
      const { moduleId, osGroup: moduleInfoOsGroup } = moduleInfo
      const flag = moduleId > 2 && moduleInfoOsGroup === osGroup
      if (!flag) {
        return false
      }
      const isBTerm = type === 16 || type === 17 || type === 18 // 是否老板终端
      const mod = moduleId % 100
      // 老板终端不支持模块：文件相似度识别(82,182,282)、文件分类(83,183,283)
      if (isBTerm && (mod === 82 || mod === 83)) {
        return false
      }

      const isUTerm = (type & 0xf0) === 0x20
      const isOTerm = (type & 0xf0) === 0x80
      if (!isUTerm && !isOTerm) {
        return true
      }

      // 永久离线终端和U盘终端不支持模块：文件操作监视(12,112,212)、文件相似度识别(82,182,282)、文件分类(83,183,283)
      if (mod === 12 || mod === 20 || mod === 82 || mod === 83) {
        return false
      }
      if (isUTerm) {
        // U盘终端不支持模块：存储设备管控(16,116,216)、刻录管控模块(18,118,218)
        return !(mod === 16 || mod === 18)
      }
      return true
    },
    isOfflineTerm(type) {
      const tag = type & 0xf0
      return tag === 0x20 || tag === 0x80 // U盘终端或永久离线终端
    },
    showDetail() {
      this.initData();
    },
    checkIdExists(id) {
      if (null === id || undefined === id || 'null' === id) {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.paramIsEmpty'), type: 'error', duration: 2000 })
        return false;
      } else {
        return true;
      }
    },
    saleModuleDetail() {
      const url = '/system/systemRegistry/moduleConfig'
      const queryTemp = { entityId: this.searchId, entityType: '1' }
      this.$router.push({ path: url, query: queryTemp })
    },
    closeModuleDetail() {
      const url = '/system/systemRegistry/moduleFilter'
      const queryTemp = { entityId: this.searchId, entityType: '1' }
      this.$router.push({ path: url, query: queryTemp })
    },
    stgDetail() {
      const url = '/terminalManage/overView/strategyOverViews'
      const queryTemp = { entityId: this.searchId, entityType: '1' }
      this.$router.push({ path: url, query: queryTemp })
    },
    filterRowDatas(rowDatas, refs, flag) {
      for (let i = 0; i < rowDatas.length; i++) {
        const data = rowDatas[i]
        data.children = data.children.filter(item => {
          if (this.filterTypeData(item)) {
            return false;
          }
          if (item.routerPath && this.hasPermission(item.routerPath)) {
            return true
          }
          return false
        })
        if (data.children.length === 0) {
          rowDatas.splice(i, 1)
          i--
        }
      }
      return rowDatas
    },
    //  过滤掉 直接外发程序白名单
    filterTypeData(item) {
      return [231].includes(item.id);
    },
    getEffectStgNum(effectiveStgTreeList) {
      let count = 0;
      effectiveStgTreeList.forEach((effectiveStgTree) => {
        const size = this.getEffectStgSize(effectiveStgTree);
        count += size;
      })
      return count;
    },
    getEffectStgSize(effectiveStgTree) {
      let count = 0
      if (effectiveStgTree && effectiveStgTree.children) {
        const children = effectiveStgTree.children
        for (let i = 0; i < children.length; i++) {
          const child = children[i]
          if (child && child.children) {
            count += this.getEffectStgSize(child)
          } else {
            if (child.hasOwnProperty('objectType')) {
              count++
            }
          }
        }
      }
      return count;
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
.nowrap-label{
  word-break:keep-all;
  text-overflow:ellipsis;
  overflow:hidden;
  white-space: nowrap;
  margin: 0;
  display: inline;
}
.module-check-group {
  margin-top: 15px;
  .el-checkbox {
    width: 40%;
    margin-left: 5%;
    >>>.el-checkbox__label{
      margin-left: 10px;
      padding-left: 0;
    }
  }
}
</style>
