<template>
  <div v-if="permission" >
    <el-button type="text" @click="showDetail()">{{ label }}</el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="label"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      width="1000px"
    >
      <el-tabs ref="tabs" v-model="activeName" type="card" >
        <!--默认选项卡：基本信息-->
        <el-tab-pane :label="$t('pages.basicInformation')" name="base" style="overflow: auto;">
          <div class="global-config ">
            <Form v-if="undefined !== data" ref="dataForm" label-position="right" label-width="120px">
              <el-row>
                <el-col :span="8">
                  <FormItem :label="$t('table.softwareName')">{{ data.softwareName }}</FormItem>
                </el-col>
                <el-col :span="8">
                  <FormItem :label="$t('pages.softwareDesc')">{{ data.softwareDesc }}</FormItem>
                </el-col>
                <el-col :span="8">
                  <FormItem :label="$t('table.publisher2')">{{ data.publisher }}</FormItem>
                </el-col>
                <el-col :span="8">
                  <FormItem :label="$t('table.softwareType')">{{ data.softwareType }}</FormItem>
                </el-col>
                <el-col :span="8">
                  <FormItem :label="$t('table.chargeType')">{{ data.chargeType }}</FormItem>
                </el-col>
                <el-col :span="8">
                  <FormItem :label="$t('table.industryType')">{{ data.industryType }}</FormItem>
                </el-col>
              </el-row>
            </Form>
            <div>
              <h4>{{ $t('pages.software_Msg13') }}：</h4>
              <div class="table-container" >
                <el-table :data="appInfo">
                  <el-table-column show-overflow-tooltip property="processName" :label="$t('table.programName')" ></el-table-column>
                  <el-table-column show-overflow-tooltip property="checkMd5" :label="$t('table.checkMd5')">
                    <template slot-scope="scope">{{ md5LevelFormatter(scope.row.checkMd5) }}</template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <!--选项卡：软件版本-->
        <el-tab-pane :label="$t('pages.softwareVersion')" name="versions">
          <div class="table-container">
            <el-table :data="softwareVersion">
              <el-table-column show-overflow-tooltip property="softwareName" :label="$t('table.softwareName')" ></el-table-column>
              <el-table-column show-overflow-tooltip property="softwareVersion" :label="$t('table.softwareVersion')"></el-table-column>
              <el-table-column show-overflow-tooltip property="installSize" :label="$t('table.installNum')"></el-table-column>
              <el-table-column show-overflow-tooltip property="authorizedSize" :label="$t('table.authNum')"></el-table-column>
              <el-table-column show-overflow-tooltip property="unAuthSize" :label="$t('table.unAuthNum')"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <!--选项卡：订单信息-->
        <el-tab-pane :label="$t('pages.orderMessage')" name="orders">
          <div class="table-container">
            <el-table :data="orderMessage">
              <el-table-column show-overflow-tooltip property="softwareName" :label="$t('table.softwareName')" ></el-table-column>
              <el-table-column show-overflow-tooltip property="softwareExtInfo.softwareId" :label="$t('table.softwareNum')"></el-table-column>
              <el-table-column show-overflow-tooltip property="orderId" :label="$t('table.orderNumber')"></el-table-column>
              <el-table-column show-overflow-tooltip property="softwareVersion" :label="$t('table.softwareVersion')"></el-table-column>
              <el-table-column show-overflow-tooltip property="publisher" :label="$t('table.manufacturerName')"></el-table-column>
              <el-table-column show-overflow-tooltip property="contact" :label="$t('table.manufacturerContactInformation')"></el-table-column>
              <el-table-column show-overflow-tooltip property="purchaseNum" :label="$t('table.numberPurchases')"></el-table-column>
              <el-table-column show-overflow-tooltip property="price" :label="$t('table.purchasesPrice')"></el-table-column>
              <el-table-column show-overflow-tooltip property="purchaseDate" :label="$t('table.purchasesDate')"></el-table-column>
              <el-table-column show-overflow-tooltip property="expirationDate" :label="$t('table.dueDate')"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
  <span v-else :title="label">{{ label }}</span>
</template>

<script>
import { getSoftwareExtInfo } from '@/api/softwareManage/assets/assetsView';
import { getOrderPage } from '@/api/softwareManage/copyrightManage/orders'
import { getSoftwareVersionPageWithoutLimit } from '@/api/softwareManage/assets/assetsView'

export default {
  name: 'SoftwareAssetsDetail',
  props: {
    searchParam: {
      type: Object,
      default() {
        return {}
      }
    },
    label: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      data: {},
      initRoute: '',  // 组件初始化所在的路由
      visible: false,
      activeName: 'base',
      appInfo: [],
      softwareVersion: [],
      orderMessage: [],
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      }
    }
  },
  computed: {
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    },
    permission() {
      return this.searchId && this.hasPermission('B45')
    }
  },
  created() {
    this.initRoute = this.$route.name
  },
  methods: {
    initData() {
      // 组装需要的数据
      this.getSoftwareExtInfoData();
      this.getSoftwareVersion();
      this.getOrderMessage();
    },
    showDetail() {
      this.initData();
    },
    md5LevelFormatter(data) {
      if (data === 0) {
        return this.$t('pages.software_Msg20')
      }
      return this.md5LevelMap[data];
    },
    getSoftwareExtInfoData() {
      // type=2 软件资产
      getSoftwareExtInfo(this.searchParam).then(res => {
        if (undefined === res.data) {
          this.visible = true;
          const info = {};
          info.softwareName = this.searchParam.softName
          this.data = info;
          this.appInfo = [];
          return this.data;
        }
        this.data = res.data
        this.visible = true;
        // 处理appInfo
        if (undefined !== this.data.appInfo) {
          this.appInfo = JSON.parse(this.data.appInfo)
        } else {
          this.appInfo = [];
        }
      }).catch(e => { console.log(e); })
    },
    // 根据softName获取软件版本
    getSoftwareVersion() {
      getSoftwareVersionPageWithoutLimit({ softName: this.searchParam.softName, originalSearch: this.searchParam.originalSearch }).then(res => {
        this.softwareVersion = res.data.items;
      }).catch(e => { console.log(e); })
    },
    // 根据软件名称分页查询
    getOrderMessage() {
      const defaultQuery = { page: 1, limit: 20, softwareName: this.searchParam.softName, softwareVersion: '', sortName: 'id', sortOrder: 'desc', originalSearch: this.searchParam.originalSearch };
      getOrderPage(defaultQuery).then(res => {
        this.orderMessage = res.data.items;
      }).catch(e => { console.log(e); })
    }
  }
}
</script>

<style lang="scss" scoped>

.table-container {
  padding-block: 7px;
}
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
</style>
