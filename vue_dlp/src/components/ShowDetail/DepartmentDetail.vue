<template>
  <div v-if="permission" class="nowrap-label">
    <div v-if="label == this.$t('pages.offline_terminal_text15')" class="ellipsis" :title="label" :style="`max-width: calc(100% - ${labelIndent}px);`">{{ label }}</div>
    <el-button v-else type="text" :style="`max-width: calc(100% - ${labelIndent}px);`" :title="label" @click="showDetail()">{{ label }}</el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="label"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      width="700px"
    >
      <div class="detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item span="2" :label="$t('table.deptName')" content-class-name="ellipsis">
            <span :title="data.name">{{ data.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.superiorDept')">
            {{ data.parentName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.remark')">
            {{ data.remark }}
          </el-descriptions-item>

          <el-descriptions-item :label="$t('table.deptTermNum')">
            <span v-permission="'!B24'" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ data.termSize }}</span>
            <link-button
              v-permission="'B24'"
              menu-code="B24"
              btn-style="border: none"
              :btn-text="String(data.termSize ? data.termSize : 0)"
              :click-func="'termsDetail'"
              @termsDetail="termsDetail"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('table.deptUserNum')">
            <span v-permission="'!B23'" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ data.userSize }}</span>
            <link-button
              v-permission="'B23'"
              menu-code="B23"
              btn-style="border: none"
              :btn-text="String(data.userSize ? data.userSize : 0)"
              :click-func="'usersDetail'"
              @usersDetail="usersDetail"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('table.termGroupStgDefNum')">
            <span v-permission="'!B11'" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ termGroupStgNum }}</span>
            <link-button
              v-permission="'B11'"
              menu-code="B11"
              btn-style="border: none"
              :btn-text="String(termGroupStgNum)"
              :click-func="'termGroupStgDetail'"
              @termGroupStgDetail="termGroupStgDetail"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('table.userGroupStgDefNum')">
            <span v-permission="'!B11'" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ userGroupStgNum }}</span>
            <link-button
              v-permission="'B11'"
              menu-code="B11"
              btn-style="border: none"
              :btn-text="String(userGroupStgNum)"
              :click-func="'userGroupStgDetail'"
              @userGroupStgDetail="userGroupStgDetail"
            />
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
  <div v-else class="nowrap-label"><span :title="label">{{ label }}</span></div>
</template>

<script>
import { getDeptDetailById } from '@/api/system/terminalManage/department'

export default {
  name: 'DepartmentDetail',
  props: {
    searchId: { type: Number, default: null },
    label: { type: String, default: null },
    labelIndent: { type: Number, default: 0 },
    selfRouteCallBack: { type: Function, default: null }
  },
  data() {
    return {
      data: [],
      termGroupStgNum: 0,
      userGroupStgNum: 0,
      initRoute: '',  // 组件初始化所在的路由
      visible: false
    }
  },
  computed: {
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    },
    permission() {
      return this.searchId && this.hasPermission('A31')
    }
  },
  created() {
    this.initRoute = this.$route.name
  },
  methods: {
    initData() {
      getDeptDetailById(this.searchId).then(res => {
        if (undefined === res.data) {
          this.visible = false;
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.notFoundData'), type: 'error', duration: 2000 })
          return
        }
        this.data = res.data
        const termGroupEffectiveStgTree = this.filterRowDatas(this.data.termGroupEffectiveStgTree)
        this.termGroupStgNum = this.getEffectStgNum(termGroupEffectiveStgTree)
        const userGroupEffectiveStgTree = this.filterRowDatas(this.data.userGroupEffectiveStgTree)
        this.userGroupStgNum = this.getEffectStgNum(userGroupEffectiveStgTree)
        // 回收站分组不需要查询对应的分组名称
        // if (this.label == this.$t('pages.offline_terminal_text15')) {
        //   this.data.name = this.$t('pages.offline_terminal_text15')
        //   this.data.parentName = this.data.parentName.split('->')[0]
        // }
        this.visible = true;
      }).catch(e => { console.log(e); })
    },
    showDetail() {
      this.initData();
    },
    termGroupStgDetail() {
      const url = '/terminalManage/overView/strategyOverViews'
      const queryTemp = { entityId: this.searchId, entityType: '3' }
      this.$router.push({ path: url, query: queryTemp })
    },
    userGroupStgDetail() {
      const url = '/terminalManage/overView/strategyOverViews'
      const queryTemp = { entityId: this.searchId, entityType: '4' }
      this.$router.push({ path: url, query: queryTemp })
    },
    termsDetail() {
      const url = '/terminalManage/terminalManage/terminal'
      if (this.$route.path == url) {
        // 弹窗关闭
        this.visible = false
        if (this.selfRouteCallBack) {
          this.selfRouteCallBack({ entityId: this.searchId, entityType: '3' })
        }
      } else {
        const queryTemp = { entityId: this.searchId, entityType: '3' }
        this.$router.push({ path: url, query: queryTemp })
      }
    },
    usersDetail() {
      const url = '/terminalManage/terminalManage/user'
      if (this.$route.path == url) {
        // 弹窗关闭
        this.visible = false
        if (this.selfRouteCallBack) {
          this.selfRouteCallBack({ entityId: this.searchId, entityType: '4' })
        }
      } else {
        const queryTemp = { entityId: this.searchId, entityType: '4' }
        this.$router.push({ path: url, query: queryTemp })
      }
    },
    filterRowDatas(rowDatas) {
      for (let i = 0; i < rowDatas.length; i++) {
        const data = rowDatas[i]
        data.children = data.children.filter(item => {
          if (this.filterTypeData(item)) {
            return false;
          }
          if (item.routerPath && this.hasPermission(item.routerPath)) {
            return true
          }
          return false
        })
        if (data.children.length === 0) {
          rowDatas.splice(i, 1)
          i--
        }
      }
      return rowDatas
    },
    //  过滤掉 直接外发程序白名单
    filterTypeData(item) {
      return [231].includes(item.id);
    },
    getEffectStgNum(effectiveStgTreeList) {
      let count = 0;
      effectiveStgTreeList.forEach((effectiveStgTree) => {
        const size = this.getEffectStgSize(effectiveStgTree);
        count += size;
      })
      return count;
    },
    getEffectStgSize(effectiveStgTree) {
      let count = 0
      if (effectiveStgTree && effectiveStgTree.children) {
        const children = effectiveStgTree.children
        for (let i = 0; i < children.length; i++) {
          const child = children[i]
          if (child && child.children) {
            count += this.getEffectStgSize(child)
          } else {
            if (child.hasOwnProperty('objectType')) {
              count++
            }
          }
        }
      }
      return count;
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
.nowrap-label{
  word-break:keep-all;
  text-overflow:ellipsis;
  overflow:hidden;
  white-space: nowrap;
  margin: 0;
  display: inline;
}
>>>.el-descriptions-item__label {
  min-width: 160px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 480px;
}
</style>
