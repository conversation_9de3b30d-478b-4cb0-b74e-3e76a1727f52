<!--
/**
    <show-detail
      :search-param="searchParam"           // 请求参数 【多个】:search-param="{ terminalId: 10010, parentId: 2004 }"
                                                       【单个】:search-param="{ terminalId: 10010 }"
                                                        注意：若是单个，在colModel可省略{}，具体见下面colModel配置
      :label="label"                        // 显示名称，例如：label="终端0001"
      :search-type="col.searchType"         // 内置模块类型，具体有：终端(terminal)、操作员(user)、分组(department)、硬件资产(hardwareAssets)、软件资产(softwareAssets)
                                               例如：search-type="terminal" 注：省缺时需指定searchUrl;
      :search-url="col.searchUrl"           // 【可缺省】若内置模块不满足要求时，可主动传入URL，例如：search-url="getTestJson"
    >
    </show-detail>
    例子：
     <show-detail
      :search-param="{ terminalId: 10010 }"
      label="终端0001"
      search-type="terminal"
    >
    </show-detail>
    colModel配置：
    # 已封装模块：终端(terminal)、操作员(user)、分组(department)、硬件资产(hardwareAssets)、软件资产(softwareAssets)、应用时间表（timeInfo）
    1.先根据searchType来获取，内置几个常用的，那么可以不用传searchUrl，只需要写明 searchParam、searchType
      例如：
        【单个参数】终端：{ prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: { terminalId: 'terminalId'},
            其中，单个参数情况下，serchParam可简写为： searchParam: 'terminalId'
        【多个参数】硬件资产：{ prop: 'propName', label: 'asset', width: '150', type: 'showDetail',
                                searchParam: { terminalId: 'terminalId', parentId: 'parentId' },
                                searchType: 'hardwareAssets' }
    2.除了以上的单独页面，其他的按照遍历方式(每行两个数据)，不必写searchType，但需写明searchUrl
      例如：
         { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'terminalId', searchUrl: 'getTestJson' }
     */
-->
<template>
  <div style="display: inline;">
    <!--空的情况（null）-->
    <!--    <span v-if="emptyType">{{ this.$t('pages.null') }}</span>-->
    <!-- label 为空就不显示  -->
    <span v-if="emptyType"></span>
    <!--终端（terminal）-->
    <terminal-detail
      v-else-if="'terminal' === searchType"
      :label="label"
      :label-indent="labelIndent"
      :search-id="searchId"
      :get-terms-info="getTermsInfo"
    />
    <!--操作员（user）-->
    <user-detail
      v-else-if="'user' === searchType"
      :label="label"
      :label-indent="labelIndent"
      :search-id="searchId"
      :self-route-call-back="selfRouteCallBack"
    />
    <!--分组（department）-->
    <department-detail
      v-else-if="'department' === searchType"
      :label="label"
      :label-indent="labelIndent"
      :search-id="searchId"
      :self-route-call-back="selfRouteCallBack"
    />
    <!--硬件资产（hardwareAssets）-->
    <hardware-assets-detail
      v-else-if="'hardwareAssets' === searchType"
      :label="label"
      :search-param="searchParam"
    />
    <!--软件资产（softwareAssets）-->
    <software-assets-detail
      v-else-if="'softwareAssets' === searchType"
      :label="label"
      :search-param="searchParam"
    />
    <!--应用时间表（timeInfo）-->
    <time-info
      v-else-if="'timeInfo' === searchType"
      :label="label"
      :search-id="searchId"
    />
    <!--USB外发文件记录-USB设备名称（usbDevice）-->
    <usb-device-detail
      v-else-if="'usbDevice' === searchType"
      :label-indent="labelIndent"
      :label="label"
      :child-data="childData"
    />
    <!--其他情况（other）-->
    <other-detail
      v-else-if="null === searchType || undefined === searchType"
      :label="label"
      :search-id="searchId"
      :search-url="searchUrl"
      :width="width"
      :height="height"
    />
  </div>
</template>

<script>
import TerminalDetail from '@/components/ShowDetail/TerminalDetail'
import UserDetail from '@/components/ShowDetail/UserDetail'
import DepartmentDetail from '@/components/ShowDetail/DepartmentDetail'
import HardwareAssetsDetail from '@/components/ShowDetail/HardwareAssetsDetail'
import SoftwareAssetsDetail from '@/components/ShowDetail/SoftwareAssetsDetail'
import TimeInfo from '@/components/ShowDetail/TimeInfoDetail'
import UsbDeviceDetail from '@/components/ShowDetail/UsbDeviceDetail'
import OtherDetail from '@/components/ShowDetail/OtherDetail'

export default {
  name: 'ShowDetail',
  components: {
    TerminalDetail,
    UserDetail,
    DepartmentDetail,
    HardwareAssetsDetail,
    SoftwareAssetsDetail,
    TimeInfo,
    UsbDeviceDetail,
    OtherDetail
  },
  props: {
    searchParam: {
      type: Object,
      default() {
        return {}
      }
    },
    width: {
      type: String,
      default: '600px'
    },
    height: {
      type: String,
      default: '100%'
    },
    label: {
      type: String,
      default: null
    },
    labelIndent: {
      type: Number,
      default: 0
    },
    searchUrl: {
      type: Function,
      default: null
    },
    searchType: {
      type: String,
      default: null
    },
    selfRouteCallBack: {
      type: Function,
      default: null
    },
    childData: {
      type: Object,
      default() {
        return undefined;
      }
    },
    getTermsInfo: {
      type: Function,
      default: function() {
        return undefined
      }
    }
  },
  data() {
    return {
      searchId: -1,
      emptyType: {
        type: Boolean,
        default: false
      }
    }
  },
  watch: {
    searchParam: {
      deep: true,
      handler(val) {
        this.reloadData(val);
      }
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.reloadData(this.searchParam)
    },
    reloadData(val) {
      if (!!this.searchParam && Object.values(this.searchParam).length > 0) {
        // 如果参数是多个的，将第一个参数作为searchId，
        // 主要适用于 终端、操作员、分组这种单个值查询的，多个值查询的用对象形式传递
        const value = Object.values(this.searchParam)[0]
        if (value instanceof Array) {
          this.searchId = value[0];
        } else {
          this.searchId = value;
        }
      }
      // 判断是否label为空
      this.emptyType = (null === this.label || undefined === this.label || '' === this.label.trim());
    }
  }
}
</script>
