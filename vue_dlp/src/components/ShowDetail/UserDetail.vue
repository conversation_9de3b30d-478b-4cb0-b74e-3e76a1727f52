<template>
  <div v-if="permission" class="nowrap-label">
    <el-button type="text" :style="`max-width: calc(100% - ${labelIndent}px);`" :title="label" @click="showDetail()">{{ label }}</el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="label"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      width="750px"
    >
      <div class="detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.account')" content-class-name="ellipsis">
            <span v-for="(icon,index) in icons" :key="'icon_' + index" :title="icon.title" :style="{ 'margin-right': '3px' }">
              <svg-icon v-if="icon" :icon-class="icon.class" :style="icon.style"/>
            </span>
            <span :title="data.account">{{ data.account }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.userName')" content-class-name="ellipsis">
            <span :title="data.name">{{ data.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.userGroup')">
            {{ data.groupNames }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.role')">
            {{ data.roleName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.immediateSupervisor')">
            {{ data.immediateSupervisorName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.termUserType')">
            {{ data.userType }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.mailAdd')">
            {{ data.email }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.phone')">
            {{ data.phone }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('form.status')">
            {{ 1 === data.active ? this.$t('text.enable') : this.$t('text.disable') }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.noPwdLogin')">
            {{ 2 === (data.flag & 2) ? this.$t('pages.forbid') : this.$t('pages.allow') }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.mobileShareFile')">
            {{ 4 === (data.flag & 4) ? this.$t('pages.allow') : this.$t('pages.forbid') }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.sid')">
            {{ 2 === data.source && data.sid ? this.$t('text.yes') : this.$t('text.no') }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.userLoginTermNum')">
            <span v-permission="'!B24'" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ userLoginTermIds }}</span>
            <link-button
              v-permission="'B24'"
              menu-code="B24"
              btn-style="border: none"
              :btn-text="userLoginTermIds"
              :click-func="'termsDetail'"
              @termsDetail="termsDetail"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.userStgDefNum')">
            <span v-permission="'!B11'" style="line-height: 28px; padding: 0 9px; margin: 2px 0 0;"> {{ userStgNum }}</span>
            <link-button
              v-permission="'B11'"
              menu-code="B11"
              btn-style="border: none"
              :btn-text="String(userStgNum)"
              :click-func="'stgDetail'"
              @stgDetail="stgDetail"
            />
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
  <div v-else class="nowrap-label">
    <span :title="label">{{ label }}</span>
  </div>
</template>

<script>
import { getUserDetailById } from '@/api/system/terminalManage/user';
import { mapGetters } from 'vuex';

export default {
  name: 'UserDetail',
  props: {
    searchId: { type: Number, default: null },
    label: { type: String, default: null },
    labelIndent: { type: Number, default: 0 },
    selfRouteCallBack: { type: Function, default: null }
  },
  data() {
    return {
      data: [],
      initRoute: '',  // 组件初始化所在的路由
      visible: false,
      icons: [], // 操作员在线状态图标
      userStgNum: 0,
      activeOptions: { 1: this.$t('text.enable'), 0: this.$t('text.disable') }
    }
  },
  computed: {
    ...mapGetters([
      'termStatusMap',
      'userStatusMap'
    ]),
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    },
    permission() {
      return this.searchId && this.hasPermission('B23')
    },
    userLoginTermIds() {
      return this.data.userLoginTermIds ? String(this.data.userLoginTermIds.length) : '0'
    }
  },
  created() {
    this.initRoute = this.$route.name
  },
  methods: {
    initData() {
      getUserDetailById(this.searchId).then(res => {
        if (undefined === res.data) {
          this.visible = false;
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.notFoundData'), type: 'error', duration: 2000 })
          return
        }
        this.data = res.data
        const effectiveStgTreeList = this.filterRowDatas(this.data.effectiveStgTree)
        this.userStgNum = this.getEffectStgNum(effectiveStgTreeList)
        this.icons = [this.activeFormatter(res.data)]
        this.visible = true;
      }).catch(e => { console.log(e); })
    },
    showDetail() {
      this.initData();
    },
    activeFormatter: function(row, data) {
      const obj = {
        class: 'offline',
        title: this.activeOptions[row.active],
        style: 'color: #888'
      }
      if (row.active) {
        obj.class = 'user'
        const status = this.termStatusMap[this.userStatusMap[row.id]]
        const online = !!status && status.status == 3
        if (online) {
          obj.title = this.$t('pages.online')
          obj.style = 'color: green'
        } else {
          obj.title = this.$t('pages.offline')
        }
      }
      return obj
    },
    termsDetail() {
      const url = '/terminalManage/terminalManage/terminal'
      if (this.$route.path == url) {
        // 弹窗关闭
        this.visible = false
        if (this.selfRouteCallBack) {
          this.selfRouteCallBack({ entityId: this.searchId, entityType: '2' })
        }
      } else {
        const queryTemp = { entityId: this.searchId, entityType: '2' }
        this.$router.push({ path: url, query: queryTemp })
      }
    },
    stgDetail() {
      const url = '/terminalManage/overView/strategyOverViews'
      const queryTemp = { entityId: this.searchId, entityType: '2' }
      this.$router.push({ path: url, query: queryTemp })
    },
    filterRowDatas(rowDatas) {
      for (let i = 0; i < rowDatas.length; i++) {
        const data = rowDatas[i]
        data.children = data.children.filter(item => {
          if (this.filterTypeData(item)) {
            return false;
          }
          if (item.routerPath && this.hasPermission(item.routerPath)) {
            return true
          }
          return false
        })
        if (data.children.length === 0) {
          rowDatas.splice(i, 1)
          i--
        }
      }
      return rowDatas
    },
    //  过滤掉 直接外发程序白名单
    filterTypeData(item) {
      return [231].includes(item.id);
    },
    getEffectStgNum(effectiveStgTreeList) {
      let count = 0;
      effectiveStgTreeList.forEach((effectiveStgTree) => {
        const size = this.getEffectStgSize(effectiveStgTree);
        count += size;
      })
      return count;
    },
    getEffectStgSize(effectiveStgTree) {
      let count = 0
      if (effectiveStgTree && effectiveStgTree.children) {
        const children = effectiveStgTree.children
        for (let i = 0; i < children.length; i++) {
          const child = children[i]
          if (child && child.children) {
            count += this.getEffectStgSize(child)
          } else {
            if (child.hasOwnProperty('objectType')) {
              count++
            }
          }
        }
      }
      return count;
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
.nowrap-label{
  word-break:keep-all;
  text-overflow:ellipsis;
  overflow:hidden;
  white-space: nowrap;
  margin: 0;
  display: inline;
}
</style>
