<template>
  <div v-if="permission" class="nowrap-label">
    <el-button type="text" :title="label" @click="showDetail()">{{ label }}</el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      border="true"
      :title="$t('table.effectTime')"
      :visible.sync="visible"
      :append-to-body="true"
      :class="{ hidden: !isCurrentRoute}"
      width="700px"
    >
      <div v-if="undefined !== data" class="detail-panel">
        <el-descriptions class="margin-top" :column="1" size="" border>
          <el-descriptions-item :label="$t('pages.timeInfoName')">
            {{ data.name }}
          </el-descriptions-item>
          <el-descriptions-item v-for="(day, dayIndex) in daysMap" :key="dayIndex" :label="day.name" >
            <!--针对时间多个的进行换行显示-->
            <span v-if="typeof data[day.eName] === 'string' && data[day.eName].indexOf(';')>-1">
              <span v-for="(val, index) in data[day.eName].split(';')" :key="index">
                <!--时间字符串-->
                {{ val }}
                <!--每两个再换行-->
                <br v-if="index % 2 === 1"/>
              </span>
            </span>
            <span v-else>{{ data[day.eName] }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
  <div v-else class="nowrap-label">
    <span :title="label">{{ label }}</span>
  </div>
</template>

<script>
import { getTimeInfoById } from '@/api/system/baseData/timeInfo';

export default {
  name: 'TimeInfoDetail',
  props: {
    searchId: {
      type: Number,
      default: null
    },
    label: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      data: [],
      initRoute: '',  // 组件初始化所在的路由
      visible: false,
      daysMap: [
        { name: this.$t('table.sun'), eName: 'sun' },
        { name: this.$t('table.mon'), eName: 'mon' },
        { name: this.$t('table.tue'), eName: 'tue' },
        { name: this.$t('table.wed'), eName: 'wed' },
        { name: this.$t('table.thu'), eName: 'thu' },
        { name: this.$t('table.fri'), eName: 'fri' },
        { name: this.$t('table.sat'), eName: 'sat' }
      ]
    }
  },
  computed: {
    // 当前路由是否是组件初始化时的路由，不是的话则隐藏弹窗
    isCurrentRoute() {
      return this.initRoute == this.$route.name
    },
    permission() {
      return this.searchId && this.hasPermission('A51')
    }
  },
  created() {
    this.initRoute = this.$route.name
  },
  methods: {
    initData() {
      const exists = this.checkIdExists(this.searchId);
      if (exists) {
        getTimeInfoById(this.searchId).then(res => {
          if (undefined === res.data) {
            this.visible = false;
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.notFoundData'), type: 'error', duration: 2000 })
            return
          }
          // 处理符号';'转为换行(字符串换行，但页面没效果，暂时不处理)
          // Object.keys(res.data).map(name => {
          //   if (typeof res.data[name] === 'string') {
          //     // this.$el.append(res.data[name].replaceAll(';', '\n'));
          //     res.data[name] = res.data[name].replaceAll(';', '\n');
          //   }
          // })
          this.data = res.data
          this.visible = true;
        }).catch(e => { console.log(e); })
      }
    },
    showDetail() {
      this.initData();
    },
    checkIdExists(id) {
      if (null === id || undefined === id || 'null' === id) {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.paramIsEmpty'), type: 'error', duration: 2000 })
        return false;
      } else {
        return true;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  margin-bottom: 3px;
  padding: 0;
}
.nowrap-label{
  word-break:keep-all;
  text-overflow:ellipsis;
  overflow:hidden;
  white-space: nowrap;
  margin: 0;
}
>>>.el-descriptions-item__label {
  min-width: 120px;
}
>>>.el-descriptions-item__content {
  min-width: 120px;
  max-width: 220px;
}
</style>
