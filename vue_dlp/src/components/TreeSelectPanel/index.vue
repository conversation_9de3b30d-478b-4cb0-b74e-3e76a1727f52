<!--
  /**
  * 通过树形结构的形式实现选择数据的功能，左侧显示可选数据，右侧显示已选数据
  * 注意事项：
  *        由于使用懒加载的方式，会导致搜索功能及自动勾选节点的功能无法完全工作，所以尽量不使用该方式。
  *        替换方式通过以下配置项实现：
  *        leafKey              树数据类型，组件内置 'terminal'、'user'
  *        getSearchList        获取所有树节点的列表，以供搜索使用
  *        selectSearchFunc     选中搜索结果后的回调函数
  *        当使用内置数据时，只需设置leafKey即可，当使用其他数据时，需要配置多个项

  * 调用示例：
    <tree-select-panel
      lazy                                                  // 是否懒加载子节点，需与 load 方法结合使用
      :load="load"                                          // 加载子树数据的方法，仅当 lazy 属性为 true 时生效，function(node, resolve){}
      :data="data"                                          // 树结构数据
      :selected-data="selectedData"                         // 选中的节点数组
      :default-expanded-keys="defaultExpandedKeys"          // 默认展开的节点 keys 数组
      :include-child="false"                                // selectedTree 树包含 toSelectTree 中选中节点的子节点，默认 true
      :include-half="true"                                  // selectedTree 树包含 toSelectTree 中半选节点，当 includeHalf = true 时有效,默认 false
      :disabled-all-nodes="disabledAllNodes"                // 值为true时禁止勾选节点，并且右侧已勾选节点去掉删除按钮，默认 false
      :disabled-nodes="disabledNodes"                       // 禁用可选节点勾选框的方法，方法返回值为 true 则禁用
      :to-select-title="title"                              // 左侧面板标题，"可选范围"
      :selected-title="title"                               // 右侧面板标题，"已选范围"
      to-select-width="100px"                               // 左侧面板宽度，string ： '100px' 、 'calc( 50% - 2.5px)'
      selected-width="100px"                                // 右侧面板标题，string ： '100px' 、 'calc( 50% - 2.5px)'
      height="100px"                                        // 面板高度，string ： '100px' 、 'calc( 100% - 55px)'
      :panel-style="{'border-color': '#656565'}"
      :header-style="{color: '#ccc'}"
      :body-style="{color: '#ccc'}"
      @to-select-node-expand="nodeExpand"
      @check="check"
      @check-change="checkChange"
    >
  */
-->
<template>
  <div class="tree-select-panel">
    <el-card class="box-card" :style="toSelectStyle" :body-style="bodyStyle">
      <div v-if="toSelectTitle != null" slot="header" class="clearfix" :style="headerStyle">
        <span>{{ toSelectTitle }}</span>
        <el-button v-if="multiplePermission" size="small" style="position: absolute;left: 90px;top: 2px;" @click="handleMultipleSet">{{ $t('pages.multipleSetPermission') }}</el-button>
      </div>
      <tree-menu
        ref="toSelectTree"
        v-loading="loading"
        :node-key="nodeKey"
        :style="{border: '0 !important'}"
        :data="treeData"
        :lazy="lazy"
        :load="load"
        :is-filter="isFilter"
        :filter-key="filterKey"
        :filter-node-method="filterNodeMethod"
        :local-search="localSearch"
        :get-search-list="getSearchListFunc"
        :select-search-func="selectSearchFunction"
        :render-content="renderToSelectTreeContent"
        multiple
        :check-strictly="checkStrictly"
        :default-checked-keys="defaultCheckedKeys"
        :checked-keys="selectedKeys"
        :disabled-all-nodes="disabledAllNodes"
        :disabled-nodes="disabledNodes"
        :default-expanded-keys="defaultExpandedKeys || expandedKeys"
        @check="check"
        @check-change="checkChange"
        @node-expand="nodeExpand"
      >
      </tree-menu>
    </el-card>
    <el-card class="box-card" :style="selectedStyle" :body-style="bodyStyle">
      <div v-if="selectedTitle != null" slot="header" class="clearfix" :style="headerStyle">
        <span>{{ selectedTitle }}</span>
      </div>
      <tree-menu
        ref="selectedTree"
        :style="{border: '0 !important'}"
        :data="selectedTreeData"
        :is-filter="isFilter"
        :filter-node-method="selectedFilterNodeMethod"
        :render-content="renderContent"
        :default-expanded-keys="selectedExpandedKeys"
        :default-expand-all="selectedNodesExpandAll"
        @node-expand="selectedNodeExpand"
        @node-collapse="selectedNodeCollapse"
      >
      </tree-menu>
    </el-card>
    <!-- 批量勾选节点 -->
    <el-dialog
      v-el-drag-dialog
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="this.$t('pages.multipleSetPermission')"
      :visible.sync="multipleSetVisible"
      width="650px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="permissionTemp"
        label-position="right"
        label-width="10px"
        style="width: 610px;"
      >
        <FormItem>
          <el-radio-group v-model="permissionTemp.operateType">
            <el-radio :label="1">{{ $t('pages.permission_Msg7') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.permission_Msg8') }}</el-radio>
          </el-radio-group>
          <!-- <span style="color: #3296FA;">{{ $t('pages.permission_Msg3') }}</span> -->
        </FormItem>
        <FormItem>
          <fieldset style="border-width: 1px;">
            <legend>{{ $t('pages.permission_Msg4') }}</legend>
            <el-radio-group v-model="permissionTemp.permissionType">
              <el-radio :label="1">{{ $t('pages.permission_Msg13') }}</el-radio>
              <el-radio :label="2">{{ $t('pages.permission_Msg14') }}</el-radio>
            </el-radio-group>
            <div v-if="permissionTemp.permissionType == 2">
              <hr>
              <el-button type="text" style="margin-top: 5px;" @click="selectAll(true)">{{ $t('button.selectAll') }}</el-button>
              <el-button type="text" style="margin-top: 5px;" @click="selectAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
              <el-row style="margin-top: 5px;">
                <el-col v-for="(opt, index) in permissionTemp.permissionOption" :key="index" :span="8">
                  <el-checkbox v-model="opt.value" :true-label="1" :false-label="0">{{ opt.label }}</el-checkbox>
                </el-col>
              </el-row>
            </div>
          </fieldset>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="multipleSetVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TreeSelectPanel',
  props: {
    // 可选树的 title
    toSelectTitle: {
      type: String,
      default() {
        return null
      }
    },
    // 已选树的 title
    selectedTitle: {
      type: String,
      default() {
        return null
      }
    },
    // 可选树的宽度
    toSelectWidth: {
      type: String,
      default() {
        return 'calc(50% - 2.5px)'
      }
    },
    // 已选树的宽度
    selectedWidth: {
      type: String,
      default() {
        return 'calc(50% - 2.5px)'
      }
    },
    // 树组件高度
    height: {
      type: String,
      default() {
        return 'calc( 100% - 35px)'
      }
    },
    // 外部传入的可选树数据
    data: {
      type: Array,
      default: null
    },
    // todo
    selectedData: {
      type: Array,
      default() {
        return []
      }
    },
    // 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的
    nodeKey: {
      type: String,
      default() {
        return 'id'
      }
    },
    // 是否懒加载子节点，需与 load 方法结合使用
    lazy: {
      type: Boolean,
      default() {
        return false
      }
    },
    // todo 批量设置应该是一个通用功能，按钮label、勾选项数据由外部传入。
    // 是否显示批量设置权限按钮， 默认不显示
    multiplePermission: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 加载子树数据的方法，仅当 lazy 属性为true 时生效，function(node, resolve)
    load: {
      type: Function,
      default() {
        return null
      }
    },
    // 值为true时禁止勾选节点，并且右侧已勾选节点去掉删除按钮，默认 false
    disabledAllNodes: {
      type: Boolean,
      default: false
    },
    // 禁用节点勾选框的方法，参数 data, node，返回值为 true 则禁用
    disabledNodes: {
      type: Function,
      default: null
    },
    // 默认勾选的节点的 key 的数组
    defaultCheckedKeys: {
      type: Array,
      default() {
        return []
      }
    },
    // 已选树是否默认展开所有节点
    selectedNodesExpandAll: {
      type: Boolean,
      default: false
    },
    // 默认展开的节点的 key 的数组
    defaultExpandedKeys: {
      type: Array,
      default: null
    },
    defaultSelectedRootId: { // todo 默认根节点的ID，如果在 selectedData 中找到ID相同的根节点，则选中的节点都放到此根节点下
      type: String,
      default() {
        return null
      }
    },
    // 已选树节点是否包含子节点，默认为 true
    includeChild: { //
      type: Boolean,
      default() {
        return true
      }
    },
    // 已选树节点是否包含可选树的半选节点，默认为 false
    includeHalf: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
    checkStrictly: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 是否提供查找功能，默认为true
    isFilter: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 是否在本地搜索，默认为true
    localSearch: {
      type: Boolean,
      default: true
    },
    // 树数据类型，组件内置 'terminal'、'user'
    leafKey: {
      type: String,
      default: 'terminal'
    },
    // 远程搜索时，获取节点数据的方法
    getSearchList: {
      type: Function,
      default: null
    },
    // 选中搜索结果后的回调函数
    selectSearchFunc: {
      type: Function,
      default: null
    },
    // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏
    filterNodeMethod: {
      type: Function,
      default: null
    },
    // 传入需要过滤的节点的nodeKey值
    filterKey: {
      type: [Number, String, Array],
      default() {
        return ''
      }
    },
    // todo 选中节点过滤函数， 看使用场景调整（禁止选中从可选树禁用）
    selectedNodeFilter: {
      type: Function,
      default(nodeDatas) {
        return nodeDatas
      }
    },
    // 容器样式
    panelStyle: {
      type: Object,
      default() {
        return {}
      }
    },
    // 容器 header 样式
    headerStyle: {
      type: Object,
      default() {
        return { color: '#666666' }
      }
    },
    // 容器 body 样式
    bodyStyle: {
      type: Object,
      default() {
        return { padding: '5px', height: this.height }
      }
    },
    // 已选列表节点后的操作按钮
    selectedButton: {
      type: Array,
      default() {
        return [{
          label: this.$t('text.delete'),
          show: (node, data) => {
            if (this.disabledAllNodes) return false
            let isShow = true
            if (data.disabled) {
              isShow = false
            }
            return isShow
          },
          onclick: (node, data, component) => this.removeSelectedNode(data)
        }]
      }
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      node_had: null,
      resolve_had: null,
      selectedKeys: [],               // 选中的节点 key 数组
      selectedKeysMap: {},            // 已勾选的节点 key 的 map
      iconOption: {                   // 节点图标 map
        1: 'terminal',
        2: 'user',
        3: 'terminalGroup',
        4: 'userGroup'
      },
      expandedKeys: [],               // 可选树展开的节点 key 数组
      selectedExpandedKeys: [],       // 已选树展开的节点 key 数组
      refactorTreeData: [],           // 重构后的已选树数据
      rules: {},
      multipleSetVisible: false,      // 批量设置权限弹窗是否显示
      permissionTemp: {},             // 批量设置权限的 temp
      defaultPermissionTemp: {
        operateType: 1,               // 操作类型，1：勾选  2：取消勾选
        permissionType: 1,            // 权限类型，1：全部权限  2： 指定权限
        permissionOption: [           // 指定权限的 option
          { label: this.$t('pages.export'), value: 0 },        // 导出
          { label: this.$t('button.import'), value: 0 },       // 导入
          { label: this.$t('button.download'), value: 0 },     // 下载
          { label: this.$t('pages.drillDown'), value: 0 },     // 下钻
          { label: this.$t('pages.delete'), value: 0 },        // 删除
          { label: this.$t('pages.detail'), value: 0 }         // 查看详情
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'termTree',
      'termTreeList',
      'userTree',
      'userTreeList'
    ]),
    // 是否内置模式，没有传入数据或非懒加载的情况
    isBuiltIn() {
      return !this.data && !this.lazy
    },
    // 内置数据类型
    dataType() {
      return this.isBuiltIn ? this.leafKey : ''
    },
    // 可选树的数据
    treeData() {
      return this.isBuiltIn ? this.getTreeData(this.dataType) : this.data
    },
    // 已选树的数据
    selectedTreeData() {
      // 当 选中节点不包括半选节点 时，使用 重构的树数据，否则使用 可选树的数据
      return !this.includeHalf ? this.refactorTreeData : this.treeData
    },
    // 可选树容器样式
    toSelectStyle() {
      return Object.assign({
        width: this.toSelectWidth, 'background-color': 'transparent', float: 'left', height: '100%'
      }, this.panelStyle)
    },
    // 已选树容器样式
    selectedStyle() {
      return Object.assign({
        width: this.selectedWidth, 'background-color': 'transparent', float: 'right', height: '100%', 'margin-left': '5px'
      }, this.panelStyle)
    },
    // 获取搜索节点列表
    getSearchListFunc() {
      // 外部传入获取搜索列表的方法
      if (typeof this.getSearchList === 'function') {
        return this.getSearchList.bind(this);
      }
      // 返回 getTreeList 方法，该方法返回内置数据的搜索节点列表
      return () => {
        return this.getTreeList(this.dataType)
      }
    },
    // 选中搜索结果后的回调函数
    selectSearchFunction() {
      // 外部传入 选中搜索结果后的回调函数
      if (typeof this.selectSearchFunc === 'function') {
        return this.selectSearchFunc.bind(this);
      }
      // 返回默认方法，选中搜索结果后的回调函数，需要带参数 nodeData
      return (nodeData) => {
        const node = this.toSelectMenu().getNode(nodeData)
        // 勾选节点
        this.setChecked(node, true, true)
      }
    }
  },
  watch: {
    selectedTreeData(val) {
      if (val.length == 0) {
        this.selectedExpandedKeys.splice(0)
      }
      this.$emit('checkedData', val)
    },
    defaultCheckedKeys(val) {
      this.updateSelectedKeysMap(val)
    },
    data(val) {
      this.$nextTick(() => {
        // data 变更后，需要更新已选中节点树
      })
    },
    selectedKeysMap: {
      deep: true,
      handler(val) {
        if (!this.includeHalf) {
          // 重构已选树的数据
          this.refactorTreeData = this.refactorSelectedTreeData1()
        }
      }
    }
  },
  created() {
  },
  methods: {
    // 批量设置权限 提交数据
    submitData() {
      const { operateType, permissionType, permissionOption } = this.permissionTemp
      const operates = []
      if (permissionType == 2) {
        // 指定选中权限
        permissionOption.forEach(opt => {
          opt.value == 1 && operates.push(opt.label)
        });
        if (operates.length == 0) {
          this.$message({
            type: 'error',
            message: this.$t('pages.permission_Msg5'),
            duration: 2000
          })
          return
        }
      }
      this.multipleSetVisible = false
      this.$emit('multipleSetPermission', { operateType, permissionType, operates })
    },
    // 点击批量设置权限
    handleMultipleSet() {
      this.multipleSetVisible = true
      this.resetPermissionTemp()
    },
    // 重置 permissionTemp
    resetPermissionTemp() {
      this.permissionTemp = JSON.parse(JSON.stringify(this.defaultPermissionTemp))
    },
    // true 全选，false 取消全选
    selectAll(boolean) {
      const { permissionOption } = this.permissionTemp
      const val = boolean ? 1 : 0
      permissionOption.forEach(opt => {
        opt.value = val
      });
    },
    handleDrag() {},
    // 获取树数据
    getTreeData(type) {
      const keyMap = { terminal: 'termTree' }
      let key = keyMap[type]
      if ('user' === type) { // 避免检测到目标源码中可能存在用户名或者密码信息泄露
        key = 'userTree'
      }
      return this[key] || []
    },
    // 获取节点列表
    getTreeList(type) {
      const keyMap = { terminal: 'termTreeList' }
      let key = keyMap[type]
      if ('user' === type) { // 避免检测到目标源码中可能存在用户名或者密码信息泄露
        key = 'userTreeList'
      }
      return this[key] || []
    },
    // 可选树菜单 tree-menu
    toSelectMenu() {
      return this.$refs.toSelectTree
    },
    // 可选树组件 el-tree
    toSelectTree() {
      return this.$refs.toSelectTree.tree()
    },
    // 已选树菜单 tree-menu
    selectedMenu() {
      return this.$refs.selectedTree
    },
    // 已选树组件 el-tree
    selectedTree() {
      return this.$refs.selectedTree.tree()
    },
    // 清空可选树和已选树的查询关键字
    clearFilter() {
      this.toSelectMenu().clearFilter()
      this.selectedMenu().clearFilter()
    },
    // 已选树菜单的过滤方法
    selectedFilterNodeMethod(value, data, node) {
      // 判断是否已选节点
      const key = data[this.nodeKey]
      const isChecked = this.selectedKeysMap[key]

      // 判断是否显示子节点
      const isChild = node.level > 1
      let isShowChild = true
      if (isChild) {
        const parentKey = node.parent.data[this.nodeKey]
        const isParentChecked = this.selectedKeysMap[parentKey]
        // 隐藏子节点的情况是 当 includeChild 为 false 且 父节点显示
        const hideChild = !this.includeChild && isParentChecked
        // 隐藏子节点时，将父节点的展开图标隐藏
        node.parent.isLeaf = hideChild
        isShowChild = !hideChild
      }

      // 判断是否搜索的节点
      const label = data.label
      const isSearch = label.toLowerCase().includes(value.toLowerCase())

      return isChecked && isShowChild && isSearch
    },
    // 可选树节点展开的回调
    nodeExpand(data, node, el) {
      // 如果节点属性 disableChildOnCheck 为 true，则禁用其子节点
      if (data.disableChildOnCheck) {
        this.setChildNodesDisabled(data, true)
      }
      this.$emit('to-select-node-expand', data, node, el)
    },
    // 禁用或解禁子节点的选择框
    setChildNodesDisabled(data, disabled) {
      const nodeData = this.toSelectMenu().getCheckedNodes().filter(node => node.dataId == data.dataId)[0]
      nodeData.disableChildOnCheck = disabled
      this.toSelectMenu().disableChildNode(nodeData, disabled)
    },
    // 已选树节点展开时触发的事件
    selectedNodeExpand(data, node, el) {
      // 更新已选树展开的节点的key
      const keys = this.selectedExpandedKeys.filter(key => data[this.nodeKey].startsWith(key))
      this.selectedExpandedKeys = [...keys, data[this.nodeKey]]
    },
    // 已选树节点被关闭时触发的事件
    selectedNodeCollapse(data, node, el) {
      // 更新已选树展开的节点的key
      const keys = this.selectedExpandedKeys.filter(key => !key.startsWith(data[this.nodeKey]))
      this.selectedExpandedKeys = [...keys]
    },
    selectTreeDataChangeEnd(val) {
      // // if (val) return
      // const that = this
      // const nodeFilter = (nodes) => { // 过滤selectedTreeData的节点，把不需要的节点剔除，然后再添加到selectedData
      //   if (this.defaultSelectedRootId) {
      //     for (let i = 0, size = that.selectedData.length; i < size; i++) {
      //       const rootData = that.selectedData[i]
      //       if (rootData[this.nodeKey] === that.defaultSelectedRootId) {
      //         rootData.children.splice(0, rootData.children.length)
      //         const filtedNodes = that.selectedNodeFilter(nodes)
      //         filtedNodes.forEach(nodeData => rootData.children.push(nodeData))
      //         break
      //       }
      //     }
      //   } else {
      //     this.selectedData.splice(0)
      //     const filtedNodes = that.selectedNodeFilter(nodes)
      //     filtedNodes.forEach(nodeData => that.selectedData.push(nodeData))
      //   }
      // }
      // const selectedKeys = []
      // const setSelectedKeys = (datas) => {
      //   datas.forEach(data => {
      //     const containChildren = data.children && data.children.length > 0
      //     if (containChildren) {
      //       setSelectedKeys(data.children)
      //     }
      //     // 选中树节点中，包含子节点同时包含半选节点时，setChecked只能对叶子节点进行设置，上级节点由树自己判断选中状态
      //     if (this.includeChild && this.includeHalf && containChildren) {
      //       return
      //     }
      //     selectedKeys.push(data[this.nodeKey])
      //   })
      // }
      // const selectNodeDatas = JSON.parse(JSON.stringify(val))
      // nodeFilter(val)
      // if (this.defaultSelectedRootId) {
      //   // 当已选节点需要指定根节点时，过滤节点不能影响已勾选节点的状态，所以要使用不同数据
      //   setSelectedKeys(selectNodeDatas)
      // } else {
      //   setSelectedKeys(val)
      // }
      // this.selectedKeys.splice(0, this.selectedKeys.length, ...selectedKeys)
    },
    // 获取树根节点map，id作为key
    getTreeRootNodes(tree, includeChild) {
      const rootDataMap = {}
      tree.$children.forEach(function(nodeDom) {
        const nodeData = Object.assign({}, nodeDom.node.data)
        if (!includeChild) {
          delete nodeData.children
        }
        rootDataMap[nodeData[this.nodeKey]] = nodeData
      })
      return rootDataMap
    },
    // 获取待选树的根节点id
    getToSelectTreeRootDataIds() {
      return this.toSelectTree().data.map(data => data[this.nodeKey])
    },
    // 根据 data 或者 key 拿到 Tree 组件中的 node, (data) 要获得 node 的 key 或者 data
    getToSelectNode(data) {
      return this.$refs.toSelectTree.getNode(data)
    },
    doLoad(node, resolve) {
      if (node.level === 0) {
        this.node_had = node // 这里是关键！在data里面定义一个变量，将node.level == 0的node存起来
        this.resolve_had = resolve // 同上，把node.level == 0的resolve也存起来
      }
      this.load(node, nodeDatas => {
        resolve(nodeDatas)
        // this.selectTreeDataChangeEnd(this.selectedTreeData)
      })
    },
    /**
     *  重新加载可选树节点
     *  @param  clearSelected  是否清除历史选中节点
     *  @param  callback       重载后的回调函数callback = function(rootNode, children){}
     */
    reloadToSelectTree(clearSelected, callback) {
      if (clearSelected) {
        this.selectedTreeData.splice(0)
      }
      this.node_had.childNodes = []// 把存起来的node的子节点清空，不然会界面会出现重复树！
      this.doLoad(this.node_had, this.resolve_had)// 再次执行懒加载的方法
      /*
      const rootNode = this.toSelectTree().root
      rootNode.loaded = true
      rootNode.childNodes.splice(0)
      rootNode.loaded = false
      rootNode.loadData((rootNode, children) => {
        if (clearSelected) {
          this.selectedTreeData.splice(0)
        } else {
          this.selectTreeDataChangeEnd(this.selectedTreeData)
        }
        if (typeof callback === 'function') {
          callback(rootNode, children)
        }
      })*/
    },
    // 当复选框被点击的时候触发共两个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、树目前的选中状态对象，包含 checkedNodes、checkedKeys、halfCheckedNodes、halfCheckedKeys 四个属性
    check(nodeData, checkedInfo) {
      this.$emit('check', nodeData, checkedInfo)
    },
    checkChange(checkedKey, checkedData, checkedInfo) {
      this.updateSelectedKeysMap(checkedInfo.checkedKeys)
      this.$emit('check-change', checkedKey, checkedData, checkedInfo)
    },
    // 若节点可被选择（即 show-checkbox 为 true），则返回目前被选中的节点的 key 所组成的数组
    // (leafOnly) 接收一个 boolean 类型的参数，若为 true 则仅返回被选中的叶子节点的 keys，默认值为 false
    getCheckedKeys(leafOnly) {
      return this.toSelectMenu().getCheckedKeys(leafOnly)
    },
    // 通过 keys 设置目前勾选的节点，使用此方法必须设置 node-key 属性
    // (keys, leafOnly) 接收两个参数，1. 勾选节点的 key 的数组 2. boolean 类型的参数，若为 true 则仅设置叶子节点的选中状态，默认值为 false
    setCheckedKeys(keys, leafOnly = false) {
      this.toSelectMenu().setCheckedKeys(keys, leafOnly)
      // 此处使用 this.getCheckedKeys() 是因为如果只传入父节点时，第二次传入相同节点不能触发 checkChange，那么子节点将无法被创建，而 this.getCheckedKeys() 可以获取到被勾选的子节点
      this.updateSelectedKeysMap(this.getCheckedKeys())
    },
    // 更新 已选节点 key 的 map
    updateSelectedKeysMap(keys) {
      // this.selectedKeys = keys
      this.selectedKeysMap = keys.reduce((result, key, index) => {
        result[key] = true
        return result
      }, {})
      this.$nextTick(() => {
        // 过滤可选树
        this.toSelectMenu().handleFilter()
        // 过滤已选树
        this.selectedMenu().handleFilter()
      })
    },
    // 移除选中节点
    removeSelectedNode(data) {
      this.setChecked(data[this.nodeKey], false, true)
      this.updateSelectedKeysMap(this.getCheckedKeys())
    },
    // 清除选中节点
    clearSelectedNode() {
      this.selectedKeysMap = {}
      this.selectedTreeData.splice(0) // 代码清除选中树节点，避免因为待选树使用懒加载，尚未加载对应节点的状况
      this.$refs.toSelectTree.clearSelectedNodes()
    },
    // todo 重构已选树的树数据，
    refactorSelectedTreeData1() {
      const toSelectData = [...JSON.parse(JSON.stringify(this.treeData))]
      const selectedData = []
      // 遍历可选树数据
      while (toSelectData.length > 0) {
        const data = toSelectData.shift()
        const key = data[this.nodeKey]
        const isChecked = this.selectedKeysMap[key]
        // 如果当前节点勾选了，添加到已选树数据
        if (isChecked) {
          // 不包括子节点，删除
          if (!this.includeChild) {
            delete data.children
          }
          selectedData.push(data)
        } else {
          // 如果当前节点没有勾选，且有子节点，则将其子节点添加到 toSelectData 数组前面
          if (data.children && data.children.length > 0) {
            toSelectData.unshift(...data.children)
          }
        }
      }
      return selectedData
    },
    //
    refactorSelectedTreeData(nodeData, isChecked, checkedInfo) {
      // let selectedTreeData = []
      // const checkedNodes = checkedInfo ? checkedInfo.checkedNodes : this.toSelectTree().getCheckedNodes()
      // selectedTreeData.push(...this.treeNodesToNodesArray(checkedNodes))
      // if (this.includeChild && this.includeHalf) {
      //   const halfCheckedNodes = checkedInfo ? checkedInfo.halfCheckedNodes : this.toSelectTree().getHalfCheckedNodes()
      //   selectedTreeData.push(...this.treeNodesToNodesArray(halfCheckedNodes))
      // }
      // if (this.lazy) {
      //   selectedTreeData = this.changeSelectedTreeDataLazy([...this.selectedTreeData], [...selectedTreeData], nodeData, isChecked)
      // }
      // this.selectedTreeData.splice(0, this.selectedTreeData.length, ...selectedTreeData)
      // this.formatTreeNode(this.selectedTreeData)
    },
    /**
     * 树结构的节点数组，转成不包含子节点数据的节点数组
     * @param treeNodes          需转换的节点数组
     */
    treeNodesToNodesArray(treeNodes) {
      const nodesArray = []
      treeNodes.forEach(node => {
        // 若不包含根节点，则将根节点剔除
        const checkedNode = JSON.parse(JSON.stringify(node))
        delete checkedNode.children
        nodesArray.push(checkedNode)
      })
      return nodesArray
    },
    /**
     * 懒加载模式下，有可能出现选中节点，在待选树节点中尚未加载的情况
     * 因此进行节点选择时，需要将历史选中节点
     * oldSelectedData 历史选中节点数组
     * newSelectedData 当前选中节点数组
     * curNode 当前节点
     * isChecked 当前节点是否勾选
     */
    changeSelectedTreeDataLazy(oldSelectedData, newSelectedData, curNode, isChecked) {
      if (oldSelectedData.length > 0) {
        // 遍历历史节点数组，判断是否需要加入当前节点数组
        oldSelectedData.forEach((oldNodeData) => {
          let isRemoveNode = false
          const existDataCode = oldNodeData.dataCode !== null && oldNodeData.dataCode !== undefined
          if (existDataCode) {
            for (let i = 0, size = newSelectedData.length; i < size; i++) {
              // 如果待判断节点（oldNodeData）是已选中节点或子节点, 则待判断节点应该移除
              if (oldNodeData[this.nodeKey] === newSelectedData[i][this.nodeKey]) {
                isRemoveNode = true
                break
              } else if (oldNodeData.dataCode.startsWith(newSelectedData[i].dataCode) && !this.checkStrictly) {
                isRemoveNode = true
                break
              }
            }
          }
          // 与当前节点比较
          if (!isRemoveNode && curNode) {
            // 如果待判断节点（oldNodeData）与当前节点有上下级关系，且当前节点未选中，则待判断节点应该移除
            if (existDataCode && (oldNodeData.dataCode.startsWith(curNode.dataCode) || curNode.dataCode && curNode.dataCode.startsWith(oldNodeData.dataCode))) {
              isRemoveNode = !isChecked
            } else {
              isRemoveNode = !isChecked && oldNodeData[this.nodeKey] === curNode[this.nodeKey]
            }
          }
          if (!isRemoveNode) { // 未被判定为移除的节点，应该添加到选中节点集合中
            newSelectedData.push(oldNodeData)
          }
        })
      }
      return newSelectedData
    },
    formatTreeNode(nodeDatas) {
      this.formatTreeNodeChildren(nodeDatas)
      const arrRemoveEmpty = (arr) => {
        const array = []
        arr.forEach((item, index) => {
          if (item) {
            if (item.children) {
              item.children = arrRemoveEmpty(item.children)
            }
            array.push(item)
          }
        })
        return array
      }
      // 去除nodeDatas中不是节点的数据
      arrRemoveEmpty(nodeDatas)
    },
    formatTreeNodeChildren(nodeDatas) { // 格式化节点，构造上下级
      const toSelectDataOrder = this.treeData.map(data => data[this.nodeKey])
      const nodeMap = {}
      nodeDatas.forEach(nodeData => { if (nodeData[this.nodeKey]) { nodeMap[nodeData[this.nodeKey]] = nodeData } })
      nodeDatas.splice(0)
      for (const id in nodeMap) {
        const nodeData = nodeMap[id]
        const parentNode = nodeMap[nodeData.parentId]
        if (parentNode == null) { // 说明是选中的节点中,需要作为根节点的节点
          if (this.treeData.length > 0) {
            const index = toSelectDataOrder.indexOf(nodeData[this.nodeKey])
            // 通过index确定节点的位置
            if (index == -1) {
              nodeDatas.push(nodeData)
            } else {
              nodeDatas[index] = nodeData
            }
          } else {
            nodeDatas.push(nodeData)
          }
        } else if (this.includeChild) { // 当节点需要展示子节点时，才会将子节点加进去
          const dataOrder = this.toSelectMenu().getNode(nodeData.parentId).data.children.map(data => data[this.nodeKey])
          const dataIndex = dataOrder.indexOf(nodeData[this.nodeKey])
          if (!parentNode.children) {
            parentNode.children = []
          }
          // 通过dataIndex确定节点的位置
          parentNode.children[dataIndex] = nodeData
        } else if (this.checkStrictly) {
          nodeDatas.push(nodeData)
        }
      }
    },
    // 可选树节点的内容区的渲染 Function
    renderToSelectTreeContent(h, { node, data, store }) {
      const iconClassValue = this.iconOption[data.type]
      const labelEles = [node.label]
      if (iconClassValue) {
        labelEles.splice(0, 0, h('svg-icon', { props: { iconClass: iconClassValue }}))
      }
      return h('span', { class: 'custom-tree-node' }, [
        h('span', {}, labelEles)
      ])
    },
    // 已选树节点的内容区的渲染 Function
    renderContent(h, { node, data, store }) {
      const that = this
      const iconClassValue = this.iconOption[data.type]
      const labelEles = [node.label]
      if (iconClassValue) {
        labelEles.splice(0, 0, h('svg-icon', { props: { iconClass: iconClassValue }}))
      }
      const btnEles = []
      this.selectedButton.forEach(btn => {
        if (btn.show && btn.show(node, data)) {
          btnEles.push(h('el-button', { props: { type: 'text' }, style: 'margin-bottom: 0;', on: { click: () => { btn.onclick(node, data, that) } }}, btn.label))
        }
      })
      return h('span', { class: 'custom-tree-node' }, [
        h('span', {}, labelEles),
        h('span', {}, btnEles)
      ])
    },
    // 通过 key / data 设置某个节点的勾选状态，使用此方法必须设置 node-key 属性
    // (key/data, checked, deep) 接收三个参数，1. 勾选节点的 key 或者 data 2. boolean 类型，节点是否选中 3. boolean 类型，是否设置子节点 ，默认为 false
    setChecked(data, checked, deep) {
      this.toSelectMenu().setChecked(data, checked, deep)
    },
    /**
     * 遍历选中节点
     * @param filterFunc          遍历执行函数
     * @param selectedNodeData    选中节点
     * @param exceptNodeDataKeys  排除节点key：节点ID
     */
    // todo 全局去掉该方法， 使用 @check-change 将勾选的节点传递出去，再进行处理
    forEachSelectedData(filterFunc, selectedNodeData, exceptNodeDataKeys) {
      if (!selectedNodeData) selectedNodeData = this.toSelectMenu().getCheckedNodes()
      selectedNodeData.forEach(data => {
        if (!exceptNodeDataKeys || exceptNodeDataKeys.indexOf(data[this.nodeKey]) < 0) {
          if (filterFunc) filterFunc(data)
        }
        if (data.children && data.children.length > 0) {
          this.forEachSelectedData(filterFunc, data.children, exceptNodeDataKeys)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .tree-select-panel{
    height: 100%;
    .custom-tree-node{
      justify-content: space-between;
      width: 100%;
    }
  }
</style>

