<!--
  /**
  * 通过树形结构的形式实现选择数据的功能，左侧显示可选数据，右侧显示已选数据
  * 注意事项：
  *        由于使用懒加载的方式，会导致搜索功能及自动勾选节点的功能无法完全工作，所以尽量不使用该方式。
  *        替换方式通过以下配置项实现：
  *        leafKey              树数据类型，组件内置 'terminal'、'user'，该值为groupNodes，leafNodesGroup的key
  *        groupNode            获取分组节点的配置项，格式参照groupNodes, 内置 'terminal'、'user'的配置
  *        leafNode             获取树节点列表的配置项，格式参照leafNodesGroup, 内置 'terminal'、'user'的配置
  *        getSearchList        获取所有树节点的列表，以供搜索使用
  *        selectSearchFunc     选中搜索结果后的回调函数
  *        当使用内置数据时，只需设置leafKey即可，当使用其他数据时，需要配置多个项

  * 调用示例：
    <tree-select-panel
      lazy                                                  // 是否懒加载子节点，需与 load 方法结合使用
      :load="loadDataPermissionTreeData"                    // 懒加载数据，load = function(node, resolve){}
      :data="data"                                          // 树结构数据
      :selected-data="selectedData"                         // 选中的节点数组
      :default-expanded-keys="defaultExpandedKeys"          // 默认展开的节点keys数组
      :show-but-uninclude-root="true"                       // selectedTree中是否显示toSelectTree的根节点，并且选中节点排除根节点，当树节点存在dataCode属性时有效
      :include-child="false"                                // selectedTree树包含toSelectTree中选中节点的子节点，默认true
      :include-half="true"                                  // selectedTree树包含toSelectTree中半选节点，当includeHalf=true时有效,默认false
      :disabled-all-nodes="disabledAllNodes"                // 值为true时禁止勾选节点，并且右侧已勾选节点去掉删除按钮，默认false
      :to-select-title="title"                              // 左侧面板标题，"可选范围"
      :selected-title="title"                               // 右侧面板标题，"已选范围"
      to-select-width="100px"                               // 左侧面板宽度，string ： '100px' 、 'calc( 50% - 2.5px)'
      selected-width="100px"                                // 右侧面板标题，string ： '100px' 、 'calc( 50% - 2.5px)'
      height="100px"                                        // 面板高度，string ： '100px' 、 'calc( 100% - 55px)'
      :panel-style="{'border-color': '#656565'}"
      :header-style="{color: '#ccc'}"
      :body-style="{color: '#ccc'}"
    >
  */
-->
<template>
  <div class="tree-select-panel">
    <el-card class="box-card" :style="toSelectStyle" :body-style="bodyStyle">
      <div v-if="toSelectTitle != null" slot="header" class="clearfix" :style="headerStyle">
        <span>{{ toSelectTitle }}</span>
      </div>
      <tree-menu
        ref="toSelectTree"
        v-loading="loading"
        :style="{border: '0 !important'}"
        :data="treeData"
        :load="doLoad"
        :is-filter="isFilter"
        :local-search="localSearch"
        :get-search-list="getSearchListFunction()"
        :select-search-func="getSelectSearchFunc()"
        :filter-key="filterKey"
        :filter-node-method="filterNodeMethod"
        :lazy="lazy"
        multiple
        :check-strictly="checkStrictly"
        :checked-keys="selectedKeys"
        :disabled-all-nodes="disabledAllNodes"
        :default-expanded-keys="defaultExpandedKeys || expandedKeys"
        :render-content="renderToSelectTreeContent"
        @check="handleCheck"
        @check-change="checkChange"
        @node-expand="handleNodeExpand"
      >
      </tree-menu>
    </el-card>
    <el-card class="box-card" :style="selectedStyle" :body-style="bodyStyle">
      <div v-if="selectedTitle != null" slot="header" class="clearfix" :style="headerStyle">
        <span>{{ selectedTitle }}</span>
      </div>
      <tree-menu
        ref="selectedTree"
        :style="{border: '0 !important'}"
        :data="selectedData"
        :is-filter="isFilter"
        :render-content="renderContent"
        :default-expanded-keys="selectedExpandedKeys"
        :default-expand-all="selectedNodesExpandAll"
        @node-expand="selectedNodeExpand"
        @node-collapse="selectedNodeCollapse"
      >
      </tree-menu>
    </el-card>
  </div>
</template>
<script>
import TreeMenu from '@/components/TreeMenu/index_old'

export default {
  name: 'TreeSelectPanel',
  components: { TreeMenu },
  props: {
    toSelectTitle: {
      type: String,
      default() {
        return null
      }
    },
    selectedTitle: {
      type: String,
      default() {
        return null
      }
    },
    toSelectWidth: {
      type: String,
      default() {
        return 'calc( 50% - 2.5px)'
      }
    },
    selectedWidth: {
      type: String,
      default() {
        return 'calc( 50% - 2.5px)'
      }
    },
    height: {
      type: String,
      default() {
        return 'calc( 100% - 35px)'
      }
    },
    // 树结构数据
    data: {
      type: Array,
      default: null
    },
    lazy: {
      type: Boolean,
      default() {
        return false
      }
    },
    load: {
      type: Function,
      default() {
        return null
      }
    },
    disabledAllNodes: {
      type: Boolean,
      default: false
    },
    defaultSelectedRootId: { // 默认根节点的ID，如果在selectedData中找到ID相同的根节点，则选中的节点都放到此根节点下
      type: String,
      default() {
        return null
      }
    },
    selectedData: {
      type: Array,
      default() {
        return []
      }
    },
    selectedNodesExpandAll: {
      type: Boolean,
      default: true
    },
    defaultExpandedKeys: {
      type: Array,
      default: null
    },
    showButUnincludeRoot: { // 待选树的根节点，是否需要在选中树种进行显示，但是不作为选中节点
      type: Boolean,
      default() {
        return false
      }
    },
    includeChild: {
      type: Boolean,
      default() {
        return true
      }
    },
    includeHalf: {
      type: Boolean,
      default() {
        return false
      }
    },
    checkStrictly: { // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
      type: Boolean,
      default() {
        return false
      }
    },
    isFilter: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 是否在本地搜索，默认为true
    localSearch: {
      type: Boolean,
      default: true
    },
    typeOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // 树数据类型，组件内置 'terminal'、'user'，该值为groupNodes, leafNodesGroup的key
    leafKey: {
      type: String,
      default: ''
    },
    // 获取分组节点的配置项，格式参照groupNodes, 内置 'terminal'、'user'的配置
    groupNode: {
      type: Object,
      default: null
    },
    // 获取树节点列表的配置项，格式参照leafNodesGroup, 内置 'terminal'、'user'的配置
    leafNode: {
      type: Object,
      default: null
    },
    // 获取所有树节点的列表，以供搜索使用
    getSearchList: {
      type: Function,
      default: null
    },
    // 选中搜索结果后的回调函数
    selectSearchFunc: {
      type: Function,
      default: null
    },
    // 重写过滤节点的方法
    filterNodeMethod: {
      type: Function,
      default: null
    },
    // 传入需要过滤的节点的nodeKey值
    filterKey: {
      type: [Number, String, Array],
      default() {
        return ''
      }
    },
    selectedNodeFilter: { // 选中节点过滤函数
      type: Function,
      default(nodeDatas) {
        return nodeDatas
      }
    },
    panelStyle: { // 容器样式
      type: Object,
      default() {
        return {}
      }
    },
    headerStyle: {
      type: Object,
      default() {
        return { color: '#666666' }
      }
    },
    bodyStyle: {
      type: Object,
      default() {
        return { padding: '5px', height: this.height }
      }
    },
    selectedButton: {
      type: Array,
      default() {
        return [{
          label: this.$t('text.delete'),
          show: (node, data) => {
            if (this.disabledAllNodes) return false
            let isShow = true
            if (this.showButUnincludeRoot) { // 如果选中节点不包含根节点，则根节点不提供删除
              const rootDataIds = this.getToSelectTreeRootDataIds()
              isShow = rootDataIds.indexOf(data.id) < 0
            }
            if (data.disabled) {
              isShow = false
            }
            return isShow
          },
          onclick: (node, data, component) => this.removeSelectedNode(data)
        }]
      }
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      node_had: null,
      resolve_had: null,
      selectedKeys: [],
      selectedTreeData: [],
      iconOption: {
        1: 'terminal',
        2: 'user',
        3: 'terminalGroup',
        4: 'userGroup'
      },
      expandedKeys: [],
      selectedExpandedKeys: [],
      dataType: '',
      groupNodes: { // 分组节点的树形结构数据
        terminal: {
          get: () => {
            return this.$store.getters.deptTree
          },
          set: () => { this.$store.dispatch('commonData/setDeptTree') }
        },
        user: {
          get: () => {
            return this.$store.getters.deptTree
          },
          set: () => { this.$store.dispatch('commonData/setDeptTree') }
        }
      },
      leafNodesGroup: { // 子节点按照父节点id分类，{ id: [nodeData1, nodeData] }
        terminal: {
          get: () => {
            return this.$store.getters.termNodes
          },
          set: () => { this.$store.dispatch('commonData/setTermNode') }
        },
        user: {
          get: () => {
            return this.$store.getters.userNodes
          },
          set: () => { this.$store.dispatch('commonData/setUserNode') }
        }
      },
      groupTreeData: []
    }
  },
  computed: {
    treeData() {
      return this.data || this.groupTreeData
    },
    groupData() { // 分组节点数据
      const groupOption = this.groupNodes[this.dataType]
      return groupOption && groupOption.get()
    },
    leafData() { // 子节点数据，父节点dataId为key，子节点数组为value
      const leafOption = this.leafNodesGroup[this.dataType]
      return leafOption && leafOption.get()
    },
    treeNodesList() { // 搜索的节点列表
      let nodesList = []
      if (!this.localSearch && this.leafData) {
        const nodeMap = this.leafData
        let leafNodes = []
        if (nodeMap) {
          Object.values(nodeMap).forEach(n => { leafNodes = leafNodes.concat(n) })
        }
        const groupList = this.$store.getters.deptTreeList
        nodesList = groupList.concat(leafNodes)
      }
      return nodesList
    },
    toSelectStyle() {
      return Object.assign({
        width: this.toSelectWidth, 'background-color': 'transparent', float: 'left', height: '100%'
      }, this.panelStyle)
    },
    selectedStyle() {
      return Object.assign({
        width: this.selectedWidth, 'background-color': 'transparent', float: 'right', height: '100%', 'margin-left': '5px'
      }, this.panelStyle)
    }
  },
  watch: {
    groupData(val) {
      if (this.leafData && this.oldGroupData != JSON.stringify(val)) {
        this.initTreeNode()
      }
      this.oldGroupData = JSON.stringify(val)
    },
    leafData(val) {
      if (this.groupData && this.oldLeafData != JSON.stringify(val)) {
        this.initTreeNode()
      }
      this.oldLeafData = JSON.stringify(val)
    },
    selectedTreeData(val) {
      if (!this.data && !this.lazy) {
        if (this.includeChild) {
          this.loadAllLeafNode(val)
        }
      }
      this.selectTreeDataChangeEnd(val)
      if (val.length == 0) {
        this.selectedExpandedKeys.splice(0)
      }
    },
    data(val) {
      this.$nextTick(() => {
        // data 变更后，需要更新已选中节点树
        this.refactorSelectedTreeData()
      })
    }
  },
  created() {
    if (!this.data && !this.lazy) {
      this.dataType = this.leafKey || 'user'
      if (this.groupNode) this.groupNodes = { ...this.groupNodes, ...this.groupNode }
      if (this.leafNode) this.leafNodesGroup = { ...this.leafNodesGroup, ...this.leafNode }
      if (!this.groupData) {
        this.groupNodes[this.dataType].set()
      }
      if (!this.leafData) {
        this.leafNodesGroup[this.dataType].set()
      }
    }
  },
  methods: {
    toSelectTree() { // 可选树 el-tree
      return this.$refs.toSelectTree.tree()
    },
    selectedTree() { // 已选树 el-tree
      return this.$refs.selectedTree.tree()
    },
    toSelectMenu() { // 可选树 tree-menu
      return this.$refs.toSelectTree
    },
    selectedMenu() { // 已选树 tree-menu
      return this.$refs.selectedTree
    },
    clearFilter() { // 清空可选树和已选树的查询关键字
      this.toSelectMenu().clearFilter()
      this.selectedMenu().clearFilter()
    },
    // 获取搜索列表
    getSearchListFunction() {
      const defaultFunc = () => {
        return this.treeNodesList
      }
      return this.getSearchList || defaultFunc
    },
    // 选中搜索项的回调，返回值是一个函数，需要带参数 nodeData
    getSelectSearchFunc() {
      const defaultFunc = (nodeData) => {
        const paneTree = this.$refs['toSelectTree']
        const nodePath = paneTree.getNodePath(nodeData.parentId) // 获取搜索节点的所以上级节点
        const notLoadChildNodeDataMap = {} // 未加载子节点的父节点map
        if (nodePath.length > 0) {
          nodePath.forEach(data => {
            if (!data.isLoadChildEnd) notLoadChildNodeDataMap[data.dataId] = data
          })
        }
        const notLoadKeys = Object.keys(notLoadChildNodeDataMap)
        if (notLoadKeys.length > 0) {
          const leafNodesGroup = this.leafNodesGroup[this.dataType].get()
          notLoadKeys.forEach(key => {
            const leafNodes = leafNodesGroup[key]
            const groupNodeData = notLoadChildNodeDataMap[key]
            if (leafNodes) {
              leafNodes.forEach(node => {
                if (groupNodeData.children.length === 1 && !groupNodeData.children[0].id) { // 删除{}空节点。空节点是为了让叶子分组节点能够有展开图标
                  groupNodeData.children.splice(0, 1)
                  paneTree.getNode(groupNodeData).childNodes.splice(0, 1)
                }
                if (!paneTree.$refs.tree.getNode(node)) {
                  paneTree.addNode(node)
                }
              })
            }
            groupNodeData.isLoadChildEnd = true
          })
        }
        paneTree.checkSelectedNode([...paneTree.getCheckedKeys(), nodeData.id])
        if (nodeData.parentId) {
          this.expandedKeys.splice(0, this.expandedKeys.length, nodeData.parentId)
        }
        this.refactorSelectedTreeData(nodeData)
      }
      return this.selectSearchFunc || defaultFunc
    },
    handleNodeExpand(data, node, el) {
      if (data.disableChildOnCheck) {
        this.setChildNodesDisabled(data, true)
      }
      if (this.lazy || this.data) return
      if (!data.isLoadChildEnd) {
        if (!data.children) data.children = []
        if (data.children.length === 1 && !data.children[0].id) { // 删除{}空节点。空节点是为了让叶子分组节点能够有展开图标
          data.children.splice(0, 1)
          node.childNodes.splice(0, 1)
        }
        this.loadLeafNode(data)
        this.$nextTick(() => {
          this.toSelectMenu().checkSelectedNode(this.selectedKeys)
        })
      }
    },
    selectedNodeExpand(data, node, el) {
      const keys = this.selectedExpandedKeys.filter(key => data.id.startsWith(key))
      this.selectedExpandedKeys = [...keys, data.id]
    },
    selectedNodeCollapse(data, node, el) {
      const keys = this.selectedExpandedKeys.filter(key => !key.startsWith(data.id))
      this.selectedExpandedKeys = [...keys]
    },
    loadLeafNode(nodeData) {
      if (nodeData.isLoadChildEnd) return
      nodeData.isLoadChildEnd = true
      const leafNodes = this.leafNodesGroup[this.dataType].get()[nodeData.dataId]
      const childNodes = !leafNodes ? [] : JSON.parse(JSON.stringify(leafNodes))
      if (childNodes && childNodes.length > 0) {
        if (!nodeData.children) nodeData.children = []
        childNodes.forEach(node => nodeData.children.push(node))
      }
    },
    loadAllLeafNode(nodeData) {
      if (nodeData.length != undefined) {
        nodeData.forEach(node => {
          this.loadAllLeafNode(node)
        })
      } else {
        if (!nodeData.isLoadChildEnd && nodeData.id.indexOf('G') > -1) {
          this.loadLeafNode(nodeData)
        }
        if (nodeData.children) {
          nodeData.children.forEach(node => {
            this.loadAllLeafNode(node)
          })
        }
      }
    },
    initTreeNode() {
      const setDefaultChild = (type, nodeDatas) => {
        if (nodeDatas && nodeDatas.length > 0) {
          const group = this.leafNodesGroup[this.dataType].get()
          if (!group) return
          nodeDatas.forEach(nodeData => {
            nodeData.type = type
            if (nodeData.children) {
              setDefaultChild(type, nodeData.children)
            } else {
              // 拥有子节点才添加一个空节点，使节点可以展开
              nodeData.children = group[nodeData.dataId] ? [{}] : []
            }
          })
        }
      }
      let groupTreeData = this.groupData
      groupTreeData = groupTreeData ? JSON.parse(JSON.stringify(groupTreeData)) : []
      const typeOpt = { 'terminal': '3', ...this.typeOption }
      let type = typeOpt[this.dataType]
      if (!type && 'user' === this.dataType) {
        type = '4'
      }
      setDefaultChild(type, groupTreeData)
      this.groupTreeData.splice(0, this.groupTreeData.length, ...groupTreeData)
    },
    selectTreeDataChangeEnd(val) {
      const that = this
      const nodeFilter = (nodes) => { // 过滤selectedTreeData的节点，把不需要的节点剔除，然后再添加到selectedData
        if (this.defaultSelectedRootId) {
          for (let i = 0, size = that.selectedData.length; i < size; i++) {
            const rootData = that.selectedData[i]
            if (rootData.id === that.defaultSelectedRootId) {
              rootData.children.splice(0, rootData.children.length)
              const filtedNodes = that.selectedNodeFilter(nodes)
              filtedNodes.forEach(nodeData => rootData.children.push(nodeData))
              break
            }
          }
        } else {
          this.selectedData.splice(0)
          const filtedNodes = that.selectedNodeFilter(nodes)
          filtedNodes.forEach(nodeData => that.selectedData.push(nodeData))
        }
      }
      const rootNodeDataIds = this.getToSelectTreeRootDataIds()
      const selectedKeys = []
      const setSelectedKeys = (datas) => {
        datas.forEach(data => {
          const containChildren = data.children && data.children.length > 0
          if (containChildren) {
            setSelectedKeys(data.children)
          }
          if (this.showButUnincludeRoot && rootNodeDataIds.indexOf(data.id) >= 0) {
            return
          }
          if (this.includeChild && this.includeHalf && containChildren) { // 选中树节点中，包含子节点同时包含半选节点时，setChecked只能对叶子节点进行设置，上级节点由树自己判断选中状态
            return
          }
          selectedKeys.push(data.id)
        })
      }
      const selectNodeDatas = JSON.parse(JSON.stringify(val))
      nodeFilter(val)
      if (this.defaultSelectedRootId) {
        // 当已选节点需要指定根节点时，过滤节点不能影响已勾选节点的状态，所以要使用不同数据
        setSelectedKeys(selectNodeDatas)
      } else {
        setSelectedKeys(val)
      }
      this.selectedKeys.splice(0, this.selectedKeys.length, ...selectedKeys)
    },
    getTreeRootNodes(tree, includeChildren) { // 树根节点
      const rootDataMap = {}
      tree.$children.forEach(function(nodeDom) {
        const nodeData = Object.assign({}, nodeDom.node.data)
        if (!includeChildren) {
          delete nodeData.children
        }
        rootDataMap[nodeData.id] = nodeData
      })
      return rootDataMap
    },
    getToSelectTreeRootDataIds() { // 待选树的根节点
      const rootDataIds = []
      this.toSelectTree().$children.forEach(nodeDom => {
        rootDataIds.push(nodeDom.node.data.id)
      })
      return rootDataIds
    },
    doLoad(node, resolve) {
      if (node.level === 0) {
        this.node_had = node // 这里是关键！在data里面定义一个变量，将node.level == 0的node存起来
        this.resolve_had = resolve // 同上，把node.level == 0的resolve也存起来
      }
      this.load(node, nodeDatas => {
        resolve(nodeDatas)
        this.selectTreeDataChangeEnd(this.selectedTreeData)
        if (this.showButUnincludeRoot && this.defaultExpandedKeys.length === 0) {
          nodeDatas.forEach(node => this.defaultExpandedKeys.push(node.id))
        }
      })
    },
    /**
     *  重新加载可选树节点
     *  @param  clearSelected  是否清除历史选中节点
     *  @param  callback       重载后的回调函数callback = function(rootNode, children){}
     */
    reloadToSelectTree(clearSelected, callback) {
      if (clearSelected) {
        this.selectedTreeData.splice(0)
      }
      this.node_had.childNodes = []// 把存起来的node的子节点清空，不然会界面会出现重复树！
      this.doLoad(this.node_had, this.resolve_had)// 再次执行懒加载的方法
      /*
      const rootNode = this.toSelectTree().root
      rootNode.loaded = true
      rootNode.childNodes.splice(0)
      rootNode.loaded = false
      rootNode.loadData((rootNode, children) => {
        if (clearSelected) {
          this.selectedTreeData.splice(0)
        } else {
          this.selectTreeDataChangeEnd(this.selectedTreeData)
        }
        if (typeof callback === 'function') {
          callback(rootNode, children)
        }
      })*/
    },
    handleCheck(nodeData, checkedInfo) {
      const isChecked = checkedInfo.checkedNodes.indexOf(nodeData) > -1
      this.$emit('check', nodeData, checkedInfo)
      this.$nextTick(() => {
        this.refactorSelectedTreeData(nodeData, isChecked)
      })
    },
    refactorSelectedTreeData(nodeData, isChecked, checkedInfo) {
      let selectedTreeData = []
      const checkedNodes = checkedInfo ? checkedInfo.checkedNodes : this.toSelectTree().getCheckedNodes()
      selectedTreeData.push(...this.treeNodesToNodesArray(checkedNodes))
      if (this.includeChild && this.includeHalf) {
        const halfCheckedNodes = checkedInfo ? checkedInfo.halfCheckedNodes : this.toSelectTree().getHalfCheckedNodes()
        selectedTreeData.push(...this.treeNodesToNodesArray(halfCheckedNodes))
      }
      if (this.lazy) {
        selectedTreeData = this.changeSelectedTreeDataLazy([...this.selectedTreeData], [...selectedTreeData], nodeData, isChecked)
      }
      this.selectedTreeData.splice(0, this.selectedTreeData.length, ...selectedTreeData)
      this.formatTreeNode(this.selectedTreeData)
    },
    /**
     * 树结构的节点数组，转成不包含子节点数据的节点数组
     * @param treeNodes          需转换的节点数组
     */
    treeNodesToNodesArray(treeNodes) {
      const rootDataIds = this.getToSelectTreeRootDataIds()
      const nodesArray = []
      treeNodes.forEach(node => {
        // 若不包含根节点，则将根节点剔除
        if (this.showButUnincludeRoot && rootDataIds.indexOf(node.id) >= 0) return
        const checkedNode = JSON.parse(JSON.stringify(node))
        delete checkedNode.children
        nodesArray.push(checkedNode)
      })
      return nodesArray
    },
    checkChange(checkedKeys, checkedNodes) {
      // console.log('checkChange', checkedKeys, checkedNodes)
    },
    /**
     * 懒加载模式下，有可能出现选中节点，在待选树节点中尚未加载的情况
     * 因此进行节点选择时，需要将历史选中节点
     * oldSelectedData 历史选中节点数组
     * newSelectedData 当前选中节点数组
     * curNode 当前节点
     * isChecked 当前节点是否勾选
     */
    changeSelectedTreeDataLazy(oldSelectedData, newSelectedData, curNode, isChecked) {
      if (oldSelectedData.length > 0) {
        // 如果配置为不包括根节点，但是oldSelectedData中又存在根节点，则应该进行格式化处理，去掉根节点
        if (this.showButUnincludeRoot) {
          const rootDataIds = this.getToSelectTreeRootDataIds()
          const editDatas = oldSelectedData.splice(0)
          editDatas.forEach(function(nodeData) {
            if (rootDataIds.indexOf(nodeData.id) >= 0) {
              if (nodeData.children) {
                nodeData.children.forEach(cnodeData => oldSelectedData.push(cnodeData))
              }
            } else {
              oldSelectedData.push(nodeData)
            }
          })
        }
        // 遍历历史节点数组，判断是否需要加入当前节点数组
        oldSelectedData.forEach((oldNodeData) => {
          let isRemoveNode = false
          const existDataCode = oldNodeData.dataCode !== null && oldNodeData.dataCode !== undefined
          if (existDataCode) {
            for (let i = 0, size = newSelectedData.length; i < size; i++) {
              // 如果待判断节点（oldNodeData）是已选中节点或子节点, 则待判断节点应该移除
              if (oldNodeData.id === newSelectedData[i].id) {
                isRemoveNode = true
                break
              } else if (oldNodeData.dataCode.startsWith(newSelectedData[i].dataCode) && !this.checkStrictly) {
                isRemoveNode = true
                break
              }
            }
          }
          // 与当前节点比较
          if (!isRemoveNode && curNode) {
            // 如果待判断节点（oldNodeData）与当前节点有上下级关系，且当前节点未选中，则待判断节点应该移除
            if (existDataCode && (oldNodeData.dataCode.startsWith(curNode.dataCode) || curNode.dataCode && curNode.dataCode.startsWith(oldNodeData.dataCode))) {
              isRemoveNode = !isChecked
            } else {
              isRemoveNode = !isChecked && oldNodeData.id === curNode.id
            }
          }
          if (!isRemoveNode) { // 未被判定为移除的节点，应该添加到选中节点集合中
            newSelectedData.push(oldNodeData)
          }
        })
      }
      return newSelectedData
    },
    formatTreeNode(nodeDatas) {
      this.formatTreeNodeChildren(nodeDatas)
      const arrRemoveEmpty = (arr) => {
        const array = []
        arr.forEach((item, index) => {
          if (item) {
            if (item.children) {
              item.children = arrRemoveEmpty(item.children)
            }
            array.push(item)
          }
        })
        return array
      }
      // 去除nodeDatas中不是节点的数据
      arrRemoveEmpty(nodeDatas)
      if (this.showButUnincludeRoot) { // 如果设置为不包含根节点，但是必须显示，则将选中节点添加到根节点中
        const rootNodeMap = this.getTreeRootNodes(this.toSelectTree(), false)
        if (rootNodeMap && nodeDatas.length > 0 && nodeDatas[0].dataCode) {
          const treeNodeDatas = nodeDatas.splice(0)
          for (const id in rootNodeMap) {
            const rootNode = rootNodeMap[id]
            treeNodeDatas.forEach(nodeData => {
              if (nodeData.dataCode.startsWith(rootNode.dataCode)) {
                if (rootNode.children) {
                  rootNode.children.push(nodeData)
                } else {
                  rootNode.children = [nodeData]
                }
              }
            })
            nodeDatas.push(rootNode)
          }
        }
      }
    },
    formatTreeNodeChildren(nodeDatas) { // 格式化节点，构造上下级
      const toSelectDataOrder = this.treeData.map(data => data.id)
      const nodeMap = {}
      nodeDatas.forEach(nodeData => { if (nodeData.id) { nodeMap[nodeData.id] = nodeData } })
      nodeDatas.splice(0)
      for (const id in nodeMap) {
        const nodeData = nodeMap[id]
        const parentNode = nodeMap[nodeData.parentId]
        if (parentNode == null) { // 说明是选中的节点中,需要作为根节点的节点
          if (this.treeData.length > 0) {
            const index = toSelectDataOrder.indexOf(nodeData.id)
            // 通过index确定节点的位置
            if (index == -1) {
              nodeDatas.push(nodeData)
            } else {
              nodeDatas[index] = nodeData
            }
          } else {
            nodeDatas.push(nodeData)
          }
        } else if (this.includeChild) { // 当节点需要展示子节点时，才会将子节点加进去
          const dataOrder = this.toSelectMenu().getNode(nodeData.parentId).data.children.map(data => data.id)
          const dataIndex = dataOrder.indexOf(nodeData.id)
          if (!parentNode.children) {
            parentNode.children = []
          }
          // 通过dataIndex确定节点的位置
          parentNode.children[dataIndex] = nodeData
        } else if (this.checkStrictly) {
          nodeDatas.push(nodeData)
        }
      }
    },
    renderToSelectTreeContent(h, { node, data, store }) {
      const iconClassValue = this.iconOption[data.type]
      const labelEles = [node.label]
      if (iconClassValue) {
        labelEles.splice(0, 0, h('svg-icon', { props: { iconClass: iconClassValue }}))
      }
      return h('span', { class: 'custom-tree-node' }, [
        h('span', {}, labelEles)
      ])
    },
    renderContent(h, { node, data, store }) {
      const that = this
      const iconClassValue = this.iconOption[data.type]
      const labelEles = [node.label]
      if (iconClassValue) {
        labelEles.splice(0, 0, h('svg-icon', { props: { iconClass: iconClassValue }}))
      }
      const btnEles = []
      this.selectedButton.forEach(btn => {
        if (btn.show && btn.show(node, data)) {
          btnEles.push(h('el-button', { props: { type: 'text' }, style: 'margin-bottom: 0;', on: { click: () => { btn.onclick(node, data, that) } }}, btn.label))
        }
      })
      return h('span', { class: 'custom-tree-node' }, [
        h('span', {}, labelEles),
        h('span', {}, btnEles)
      ])
    },
    getToSelectNode(data) { // 根据 data 或者 key 拿到 Tree 组件中的 node,(data) 要获得 node 的 key 或者 data
      return this.$refs.toSelectTree.getNode(data)
    },
    clearSelectedNode() { // 清除选中节点
      this.selectedTreeData.splice(0) // 代码清除选中树节点，避免因为待选树使用懒加载，尚未加载对应节点的状况
      this.$refs.toSelectTree.clearSelectedNodes()
      this.refactorSelectedTreeData()
    },
    setSelectedNode(nodeData) { // 初始化时，自动勾选已选中节点
      this.selectedTreeData.push(nodeData) // 代码添加到选中树节点
      // 当终端节点加载完毕后，才去调用选中节点的方法，否则会导致节点无法被勾选
      if (!this.data && !this.lazy) {
        if (this.leafNodesGroup[this.dataType].get()) {
          this.getSelectSearchFunc()(nodeData)
        } else {
          const interval = setInterval(() => {
            if (this.leafNodesGroup[this.dataType].get()) {
              this.getSelectSearchFunc()(nodeData)
              clearInterval(interval)
            }
          }, 100)
        }
      } else {
        if (this.includeChild && this.includeHalf) { // 选中树节点中，包含子节点同时包含半选节点时，setChecked只能对叶子节点进行设置，上级节点由树自己判断选中状态
          const checkNodeData = (data) => {
            if (data.children && data.children.length > 0) {
              data.children.forEach(cdata => checkNodeData(cdata))
            } else {
              this.toSelectTree().setChecked(data, true, false)
            }
          }
          checkNodeData(nodeData)
        } else {
          this.toSelectTree().setChecked(nodeData, true, true)
        }
        this.refactorSelectedTreeData(nodeData, true)
      }
    },
    removeSelectedNode(data, selectedNodeData) { // 移除选中节点
      if (!selectedNodeData) selectedNodeData = this.selectedTreeData
      for (let i = 0, size = selectedNodeData.length; i < size; i++) {
        const nodeData = selectedNodeData[i] || {}
        if (data.id === nodeData.id) {
          selectedNodeData.splice(i, 1) // 代码移除选中树节点，避免因为待选树使用懒加载，尚未加载对应节点的状况
          if (!this.data && !this.lazy) this.getSelectSearchFunc()(nodeData)
          this.toSelectTree().setChecked(data, false, true)
          this.refactorSelectedTreeData(nodeData)
          return true
        } else if (nodeData.children) {
          const isRemoved = this.removeSelectedNode(data, nodeData.children)
          if (isRemoved) return true
        }
      }
    },
    setChildNodesDisabled(data, disabled) {
      const nodeData = this.toSelectMenu().getCheckedNodes().filter(node => node.dataId == data.dataId)[0]
      nodeData.disableChildOnCheck = disabled
      this.toSelectMenu().disableChildNode(nodeData, disabled)
    },
    /**
     * 遍历选中节点
     * @param filterFunc          遍历执行函数
     * @param selectedNodeData    选中节点
     * @param exceptNodeDataKeys  排除节点key：节点ID
     */
    forEachSelectedData(filterFunc, exceptNodeDataKeys, selectedNodeData) {
      if (!selectedNodeData) selectedNodeData = this.selectedData
      if (!exceptNodeDataKeys && this.showButUnincludeRoot) {
        exceptNodeDataKeys = this.getToSelectTreeRootDataIds()
      }
      const that = this
      selectedNodeData.forEach(function(data) {
        if (!exceptNodeDataKeys || exceptNodeDataKeys.indexOf(data.id) < 0) {
          if (filterFunc) filterFunc(data)
        }
        if (data.children && data.children.length > 0) {
          that.forEachSelectedData(filterFunc, exceptNodeDataKeys, data.children)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .tree-select-panel{
    height: 100%;
    .custom-tree-node{
      justify-content: space-between;
      width: 100%;
    }
  }
</style>

