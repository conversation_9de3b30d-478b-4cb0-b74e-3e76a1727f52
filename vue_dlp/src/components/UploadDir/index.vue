<!--
  使用该组件的菜单有：
  软件安装/卸载限制
  软件黑白名单
  必须运行软件
  程序访问限制
  通讯工具防伪冒
  软件白名单
  软件黑名单
  计算机个性化图片批量上传
  补丁库上传补丁
-->
<template>
  <div>
    <div :class="{'pop-modal': dlgVisible}"></div>
    <el-popover
      v-model="dlgVisible"
      placement="bottom-start"
      :visible-arrow="false"
      :title="popoverTitle"
      :width="popoverWidth"
      :append-to-body="false"
      trigger="manual"
    >
      <div class="search-box">
        <el-input v-model="keyword" v-trim clearable style="width: 200px;" @keyup.enter.native="handleFilter"/>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
      <grid-table
        ref="fileTable"
        :height="popoverHeight"
        multi-select
        row-key="webkitRelativePath"
        :show-pager="true"
        :col-model="filesColModel"
        :row-data-api="rowDataApi"
        style="margin-bottom: 5px;"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
      <p style="position: absolute; bottom: 0;">
        <i18n path="pages.appGroup_text27">
          <label slot="num">{{ selectedFiles.length }}</label>
          <label slot="size" :style="{ color: isOverLimitSize ? 'red': '' }" >{{ selectedFileSize }}</label>
          <label slot="maxSize">{{ limitSize }}</label>
        </i18n>
      </p>
      <div class="btn-group">
        <el-button size="mini" type="primary" :disabled="isOverLimitSize || !selectedFiles.length" @click="handleConfirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button size="mini" @click="handleCancel">{{ $t('button.cancel') }}</el-button>
      </div>
      <el-button slot="reference" type="primary" :loading="loading" size="mini" @click="handleUpload">
        {{ text }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('components.scanDirTip') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </el-button>
    </el-popover>
    <input ref="clearFile" style="display: none" type="file" webkitdirectory @change="changeFile"/>
  </div>
</template>

<script>
import { formatFileSize } from '@/utils'
export default {
  name: 'UploadDir',
  props: {
    text: {
      type: String,
      default() {
        return this.$t('components.scanDir')
      }
    },
    fileSuffix: {
      type: Array,
      default() {
        return ['exe']
      }
    },
    loading: { type: Boolean, default: false },
    limitSize: { type: Number, default: 1024 },
    popoverWidth: { type: Number, default: 760 },
    popoverHeight: { type: Number, default: 300 },
    popoverTitle: { type: String, default: '请调整待上传文件' }
  },
  data() {
    return {
      keyword: undefined,
      dlgVisible: false,
      selectedFiles: [],
      selectedFileSize: '0 MB',
      isOverLimitSize: false,
      filesColModel: [
        { prop: 'name', label: 'fileName', width: '120', sortOriginal: true, sort: true },
        { prop: 'webkitRelativePath', label: 'filePath', width: '200', formatter: this.filePathFormatter, sortOriginal: true, sort: true },
        { prop: 'size', label: 'fileSizeSumAll', width: '100', formatter: this.fileSizeFormatter, sortOriginal: true, sort: true }
      ]
    }
  },
  watch: {
  },
  created() {
    this.uploadFiles = []
  },
  methods: {
    changeFile(e) {
      const files = e.target.files
      const uploadFiles = []
      let totalSize = 0
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        // 只上传exe文件
        const fileName = file.name
        const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
        if (this.fileSuffix.indexOf(ext) > -1) {
          totalSize = totalSize + file.size
          uploadFiles.push(file)
        }
      }
      const isLt2M = totalSize / 1024 / 1024 < this.limitSize
      if (!isLt2M) {
        this.uploadFiles = uploadFiles
        this.dlgVisible = true
        this.$nextTick(() => this.handleFilter())
        this.clearValue()
        return false
      }
      this.$emit('changeFile', uploadFiles)
    },
    handleFilter() {
      const table = this.$refs['fileTable']
      if (table) {
        table.execRowDataApi()
      }
    },
    rowDataApi(option) {
      let files = this.uploadFiles
      if (this.keyword) {
        files = this.uploadFiles.filter(file => file.name.toLowerCase().indexOf(this.keyword) >= 0)
      }
      if (this.filesColModel.map(item => item.prop).indexOf(option.sortName) >= 0) {
        files.sort((f1, f2) => {
          let compared
          if ('size' === option.sortName) {
            compared = f1.size - f2.size
          } else {
            compared = f1[option.sortName].localeCompare(f2[option.sortName], 'zh-CN')
          }
          return option.sortOrder === 'asc' ? compared : -compared
        })
      }
      const end = option.page * option.limit
      const start = end - option.limit
      return Promise.resolve({
        code: 20000,
        data: {
          total: files.length,
          items: files.slice(start, end)
        }
      })
    },
    afterLoad(rows, table) {
      const elTable = table.getTable()
      elTable.clearSelection()
      elTable.toggleAllSelection()
    },
    handleSelectionChange(val) {
      this.selectedFiles = this.$refs['fileTable'].selectedData
      if (this.selectedFiles.length === 0) {
        this.selectedFileSize = '0 MB'
        this.isOverLimitSize = false
        return
      }
      const size = this.selectedFiles.reduce((total, cur) => total + cur.size, 0)
      this.selectedFileSize = formatFileSize(size)
      this.isOverLimitSize = size / 1024 / 1024 > this.limitSize
      if (this.isOverLimitSize && this.selectedFileSize === '1.00GB') {
        this.selectedFileSize = '1.01GB'
      }
    },
    handleUpload() {
      this.$refs.clearFile.click()
    },
    handleConfirm() {
      if (this.isOverLimitSize) {
        return
      }
      this.dlgVisible = false
      this.$emit('changeFile', this.selectedFiles)
    },
    handleCancel() {
      this.keyword = undefined
      this.uploadFiles = []
      this.dlgVisible = false
    },
    clearValue() {
      if (this.$refs.clearFile) {
        this.$refs.clearFile.value = ''
      }
    },
    filePathFormatter(row, data) {
      const index = data.indexOf('/')
      if (index > 0) {
        return '.' + data.slice(index)
      }
      return data
    },
    fileSizeFormatter(row, data) {
      const sizeArr = (Math.ceil(data / 1024) + '').split('')
      let str = ''
      for (let i = sizeArr.length - 1, j = 1; i >= 0; i--) {
        str = sizeArr[i] + str
        if (i > 0 && (j % 3) === 0) {
          str = ',' + str
        }
        j++
      }
      return str + ' KB'
    }
  }
}
</script>

<style lang="scss" scoped>
.pop-modal {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,0.5);
  z-index: 999;
}
.search-box {
  position: absolute;
  top: 4px;
  right: 13px;
  .el-input {
    margin-bottom: 3px;
  }
}
.btn-group {
  text-align: right;
  margin-top: 10px;
  padding-right: 5px;
}
</style>
