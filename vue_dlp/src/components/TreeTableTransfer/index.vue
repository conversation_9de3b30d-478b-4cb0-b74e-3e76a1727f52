<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :append-to-body="true"
    :title="titleName"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleCancel"
    :destroy-on-close="true"
    @open="handleOpen"
    @close="handleClose"
  >
    <slot name="delegate"></slot>
    <div v-show="transferShow" class="transfer">
      <el-row>
        <!-- 设置申请人 -->
        <el-col v-if="nodeModel.id == 'modelRect_launch_1'" :span="7" :offset="1">
          <tree-menu
            ref="roleAndUserTree"
            style="height: 370px"
            :data="roleAndUserData"
            :is-filter="true"
            :filter-key="filterNode.id"
            :default-expand-all="false"
            :icon-option="iconRoleOption"
            @node-click="handleNodeClick"
          />
        </el-col>
        <!-- 按岗位配置审批人 -->
        <el-col v-else-if="nodeModel.auditType == 2" :span="7" :offset="1">
          <tree-menu
            ref="roleTree"
            style="height: 370px"
            :data="nodeModel.userRoleOptions"
            :is-filter="true"
            :filter-key="filterNode.id"
            :default-expand-all="false"
            :icon-option="iconRoleOption"
            @node-click="handleNodeClick"
          />
        </el-col>
        <el-col v-else :span="7" :offset="1">
          <strategy-target-tree
            ref="objectTree"
            :showed-tree="['user']"
            :panel-width="220"
            :resizeable="false"
            :filter-key="filterNode.id"
            @data-change="strategyTargetNodeChange"
          />
        </el-col>
        <!-- 委托审批可选范围 -->
        <!-- <el-col v-if="nodeModel.islimitRange" :span="7" :offset="1">
          <tree-menu
            ref="truteeUserTree"
            style="height: 370px"
            :data="nodeModel.userTreeData"
            :is-filter="true"
            :filter-key="filterNode.id"
            :default-expand-all="false"
            @node-click="handleNodeClick"
          />
        </el-col> -->
        <el-col :span="4" style="height:100%">
          <div class="center">
            <div class="btn">
              <el-button type="primary" :disabled="buttonDisable" @click="choose">{{ $t('button.choose') }} ></el-button>
              <el-button type="primary" :disabled="buttonDisable" @click="handleDelete">&lt; {{ $t('button.delete') }}</el-button>
              <el-button type="primary" :disabled="buttonDisable" @click="clearAll">&lt;&lt; {{ $t('button.clear') }}</el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="11">
          <grid-table
            ref="tableList"
            :col-model="colModel"
            :multi-select="false"
            :header-rendering="true"
            :show-pager="false"
            :row-datas="rowData"
            @row-click="rowClick"
          />
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleClick">{{ $t('button.confirm') }}</el-button>
      <el-button @click="handleCancel">{{ $t('button.cancel') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAllUserById, getAllUserByDeptId } from '@/api/dataEncryption/encryption/approvalProcess'
import { mapGetters } from 'vuex';

export default {
  name: 'TreeTableTransfer',
  props: {
    // 弹窗标题
    title: {
      type: String,
      default() {
        return 'title'
      }
    },
    dialogVisible: {
      type: Boolean,
      default() {
        return false
      }
    },
    nodeModel: {
      type: Object,
      default() {
        return {}
      }
    },
    filterNode: {
      type: Object,
      default() {
        return {}
      }
    },
    btnDisable: {
      type: Boolean,
      default() {
        return false
      }
    },
    userTreedata: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    const allOptions = [
      { value: '0', label: this.$t('components.manualApprover') },
      { value: '1', label: this.$t('components.allAuto') },
      { value: '2', label: this.$t('components.offlineAuto') },
      { value: '4', label: this.$t('components.manualApproverOver') }
    ]
    const multipleOptions = [
      { value: '0', label: this.$t('components.manualApprover') },
      { value: '1', label: this.$t('components.allAuto') },
      { value: '2', label: this.$t('components.offlineAuto') }
    ]
    return {
      currentNode: undefined,
      currentRow: {},
      rowData: [],
      colModel: [],
      allOptions,
      multipleOptions,
      iconRoleOption: {
        'RG': 'roleGroup',
        'R': 'role'
      }
    }
  },
  computed: {
    ...mapGetters([
      'userTree',
      'userRoleOptions'
    ]),
    typeId() {
      return this.nodeModel.id
    },
    buttonDisable() {
      const { inherit, range } = this.nodeModel
      return inherit == true || range == '0' || range == '1'
    },
    transferShow() {
      const { auditType } = this.nodeModel
      return !(auditType == 1 || auditType == 4 || auditType == 5)
    },
    // 获取岗位树，并将岗位树的节点类型改为R，方便跟用户树做区分
    roleData() {
      const roles = JSON.parse(JSON.stringify(this.userRoleOptions))
      roles.forEach(item => {
        item.type = 'RG'
        item.id = item.id.replace('G', 'RG')
        item.parentId = item.parentId.replace('G', 'RG')
        if (item.children) {
          item.children.forEach(child => {
            child.type = 'R'
            child.id = child.id.replace('G', 'R')
            child.parentId = child.parentId.replace('G', 'RG')
          })
        }
      })
      return [{
        'dataId': '0',
        'id': 'RG0',
        'disabled': true,
        'label': this.$t('pages.userRoleLibrary'),
        'parentId': 'RG-1',
        'type': 'RG',
        'children': roles
      }]
    },
    // 角色树和操作员树
    roleAndUserData() {
      return this.userTree ? this.roleData.concat(this.userTree) : this.roleData
    },
    // todo title从外部导入
    titleName() {
      if (this.nodeModel.id == 'trust') {
        return this.$t('components.setPrincipal')
      } else if (this.nodeModel.id == 'modelRect_launch_1') {
        return this.$t('components.setApprover2')
      } else if (this.nodeModel.id == 'delegationLimit') {
        return `${this.$t('pages.delegationLimit')}(${this.nodeModel.userLabel})`
      } else {
        return this.$t('components.setApplicant2')
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$refs.objectTree && this.$refs.objectTree.clearFilter()
        this.$refs.roleAndUserTree && this.$refs.roleAndUserTree.clearFilter()
        this.$refs.roleTree && this.$refs.roleTree.clearFilter()
      }
    },
    'nodeModel.auditType': {
      handler(newV, oldV) {
        this.handleOpen()
      },
      immediate: false
    }
  },
  // created() {
  //   console.log('berofe.............')
  //   this.getColModel()
  // },
  methods: {
    getOptions() {
      if (this.nodeModel.auditType == 3 && this.nodeModel.multiple == true) {
        return this.multipleOptions
      }
      return this.allOptions
    },
    entityFormatter: function(row, data) {
      if (row.type == 'R') {
        return this.$t('table.role')
      } else if (row.type == '4') {
        return this.$t('components.dept')
      } else {
        return this.$t('components.user')
      }
      // return row.type == '4' ? this.$t('components.dept') : this.$t('components.user')
    },
    handleOpen() {
      this.currentNode = undefined
      this.$nextTick(() => {
        this.getColModel()
        // 角色跟操作员选中的值都是chooseData,这边得做下处理，区分下
        if (this.nodeModel.choosedData.length > 0) {
          // 只对审批人这边的模块做处理，其余的不用判断
          if (this.nodeModel.auditType == 3) {
            if (this.nodeModel.choosedDataType == 3) {
              this.rowData = this.nodeModel.choosedData
            } else {
              this.rowData = []
            }
          } else if (this.nodeModel.auditType == 2) {
            if (this.nodeModel.choosedDataType == 2) {
              this.rowData = this.nodeModel.choosedData
            } else {
              this.rowData = []
            }
          } else {
            this.rowData = this.nodeModel.choosedData
          }
        } else {
          this.rowData = []
        }
      })
    },
    getColModel() {
      if (this.typeId === 'modelRect_launch_1') {
        this.colModel =
        [
          { prop: 'label', label: this.$t('components.applicant'), width: '100' },
          {
            prop: 'type',
            label: this.$t('components.type'),
            width: '100',
            formatter: this.entityFormatter
          },
          { prop: 'department', label: this.$t('components.subordinateDept'), width: '140' }
        ]
      } else if (this.typeId === 'trust') {
        this.colModel =
        [
          { prop: 'label', label: this.$t('components.trustee'), width: '120' },
          { prop: 'deptName', label: this.$t('components.subordinateDept'), width: '140' }
        ]
      } else if (this.typeId === 'delegationLimit') {
        this.colModel =
        [
          { prop: 'label', label: this.$t('pages.canDelegation'), width: '120' },
          { prop: 'deptName', label: this.$t('components.subordinateDept'), width: '140' }
        ]
      } else if (this.nodeModel.auditType == 2) {
        this.colModel =
        [
          { prop: 'label', label: this.$t('table.role'), width: '100' },
          {
            prop: 'replyType',
            label: this.$t('components.responseType'),
            // width: '180',
            type: 'select',
            options: this.allOptions
          }
        ]
      } else {
        this.colModel = [
          { prop: 'label', label: this.$t('components.approver'), width: '100' },
          {
            prop: 'replyType',
            label: this.$t('components.responseType'),
            // width: '180',
            type: 'select',
            // options: this.getOptions()
            // options: this.allOptions
            options: this.allOptions
          }
          // { prop: 'userEmail', label: '审批提醒邮箱', width: '160' }
        ]
      }
    },
    // 关闭弹窗，清除搜索内容
    handleClose() {
      //
    },
    strategyTargetNodeChange(tabName, checkedNode) {
      this.currentNode = checkedNode
    },
    rowClick(rowData) {
      this.currentRow = rowData
    },
    choose() {
      if (this.currentNode) {
        let department = ''
        // 判断是否是设置委托人
        // if (!this.nodeModel.islimitRange) {
        //   const parentNode = this.$refs['objectTree'].getNode(this.currentNode.parentId)
        //   department = parentNode ? parentNode.label : ''
        // } else {
        //   const parentNode = this.$refs['truteeUserTree'].getNode(this.currentNode.parentId)
        //   department = parentNode ? parentNode.label : ''
        // }
        if (this.nodeModel.auditType != '2' && this.typeId != 'modelRect_launch_1') {
          const parentNode = this.$refs['objectTree'].getNode(this.currentNode.parentId)
          department = parentNode ? parentNode.label : ''
        }
        if (this.typeId == 'modelRect_launch_1' && this.currentNode.type != 'R' && this.currentNode.type != 'RG') {
          const parentNode = this.$refs['roleAndUserTree'].getNode(this.currentNode.parentId)
          department = parentNode ? parentNode.label : ''
        }
        // 如果是设置申请人
        if (this.typeId === 'modelRect_launch_1') {
          const idArr = this.rowData
            .filter(item => item.type == '2')
            .map(item => item.dataId)
          const deptIdArr = this.rowData
            .filter(item => item.type == '4')
            .map(item => item.dataId)
          const roleIdArr = this.rowData
            .filter(item => item.type == 'R')
            .map(item => item.dataId)
          if (this.currentNode.type == '4') {
            const obj = {
              dataId: this.currentNode.dataId,
              id: this.currentNode.id,
              label: this.currentNode.label,
              leaf: this.currentNode.leaf,
              type: this.currentNode.type,
              department
            }
            deptIdArr.indexOf(this.currentNode.dataId) < 0 &&
              this.rowData.push(obj)
          } else if (this.currentNode.type == 'RG') {
            // 顶级角色
            if (this.currentNode.dataId == 0) {
              if (this.currentNode.children) {
                this.currentNode.children.forEach(item => {
                  if (item.children) {
                    item.children.forEach(i => {
                      roleIdArr.indexOf(this.currentNode.dataId) < 0 && this.rowData.push(i)
                    })
                  }
                })
              }
              // 角色分组
            } else if (this.currentNode.parentId == 'RG0') {
              if (this.currentNode.children) {
                this.currentNode.children.forEach(item => {
                  roleIdArr.indexOf(this.currentNode.dataId) < 0 && this.rowData.push(item)
                })
              }
            } else {
              roleIdArr.indexOf(this.currentNode.dataId) < 0 && this.rowData.push(this.currentNode)
            }
          } else if (this.currentNode.type == 'R') {
            roleIdArr.indexOf(this.currentNode.dataId) < 0 && this.rowData.push(this.currentNode)
          } else {
            idArr.indexOf(this.currentNode.dataId) < 0 && this.rowData.push(Object.assign(this.currentNode, { department }))
          }
        } else if (this.typeId === 'trust') {
          const idArr = this.rowData.map(item => item.dataId)
          // 设置委托人
          if (this.currentNode.type == '4' || this.currentNode.type == 'G') {
            getAllUserByDeptId(this.currentNode.dataId).then(res => {
              res.data.forEach(item => {
                // 判断是否限制委托范围
                if (this.nodeModel.islimitRange) {
                  if (this.nodeModel.rangeIds.indexOf(item.dataId) > -1 && idArr.indexOf(item.dataId) < 0 && item.dataId != this.filterNode.dataId) {
                    this.rowData.push(item)
                  }
                } else {
                  idArr.indexOf(item.dataId) < 0 && this.rowData.push(item)
                }
              })
            })
          } else {
            idArr.indexOf(this.currentNode.dataId) < 0 &&
            this.rowData.push(Object.assign(this.currentNode, { deptName: department }))
          }
        } else if (this.typeId === 'delegationLimit') {
          const idArr = this.rowData.map(item => item.dataId)
          // 设置委托人范围
          if (this.currentNode.type == '4') {
            getAllUserByDeptId(this.currentNode.dataId).then(res => {
              res.data.forEach(item => {
                idArr.indexOf(item.dataId) < 0 && this.rowData.push(item)
              })
            })
          } else {
            idArr.indexOf(this.currentNode.dataId) < 0 &&
            this.rowData.push(Object.assign(this.currentNode, { deptName: department }))
          }
        } else if (this.nodeModel.auditType == '2') {
          const idArr = this.rowData.map(item => item.dataId)
          if (this.currentNode.parentId != 'RG0') {
            const item = Object.assign(this.currentNode)
            this.$set(item, 'replyType', '0')
            idArr.indexOf(this.currentNode.dataId) < 0 && this.rowData.push(item)
          } else {
            if (this.currentNode.children) {
              const dataArr = this.currentNode.children
              dataArr.forEach(item => {
                this.$set(item, 'replyType', '0')
                idArr.indexOf(item.dataId) < 0 && this.rowData.push(item)
              })
            }
          }
        } else {
          const idArr = this.rowData.map(item => item.dataId)
          // 设置审批人
          getAllUserById(this.currentNode.id).then(res => {
            res.data.forEach(item => {
              idArr.indexOf(item.dataId) < 0 && this.rowData.push(item)
            })
          })
        }
      }
    },
    handleDelete() {
      const index = this.rowData.findIndex(
        item => item.label === this.currentRow.label
      )
      index >= 0 && this.rowData.splice(index, 1)
    },
    clearAll() {
      this.rowData = []
    },
    handleClick() {
      if ((this.nodeModel.auditType == '1' || this.nodeModel.auditType == '2' || this.nodeModel.auditType == '4' || this.nodeModel.auditType == '5') && this.nodeModel.emptyAuditorToUserSelected == 'true' && !this.nodeModel.emptyAuditorToUser) {
        this.$message({
          message: this.$t('pages.chooseApproval'),
          type: 'error'
        })
        return
      }
      if (this.nodeModel.auditType == 3 && this.nodeModel.multiple == true && this.rowData.length < 2) {
        this.$message({
          message: this.$t('pages.approvalProcess_Msg87'),
          type: 'error'
        })
        return
      }
      this.$emit('sendApprover', this.rowData, this.nodeModel.flowName, this.nodeModel.flowGroupName)
    },
    handleCancel() {
      this.currentNode = undefined
      this.$emit('close')
    },
    handleNodeClick(data, node, el) {
      this.currentNode = data
    }
  }
}
</script>

<style lang="scss" scoped>
.transfer {
  height: 400px;
  .el-row {
    height: 100%;
    .el-col-7,
    .el-col-11 {
      height: 100%;
    }
  }
  .center {
    background: #ddd;
    height: 100%;
    position: relative;
    .btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .el-button {
        display: block;
        margin: 0 0 10px 10px;
        width: 100px;
      }
    }
  }

  >>>.tableBox{
  height: 100%;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  clear: both;
  }
}
</style>

