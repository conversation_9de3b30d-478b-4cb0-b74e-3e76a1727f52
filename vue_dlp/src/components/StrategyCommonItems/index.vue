<template>
  <div class="flex-container">
    <div class="flex-item" :style="{ flex: `0 0 ${isTimeShow ? (100 - timeItemWidth) : 100}%`}">
      <FormItem :label="$t('table.stgName')" class="required" :label-width="labelWidth" prop="name">
        <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30" @input="value => updateParentData('name', value)"></el-input>
      </FormItem>
    </div>
    <div v-if="timeAble" class="flex-item" :style="{ flex: `0 0 ${timeItemWidth}%`, display: isTimeShow ? '' : 'none' }">
      <FormItem :label="$t('pages.timeInfo')" :label-width="labelWidth" class="required content-flex" prop="timeId">
        <el-select v-model="temp.timeId" :disabled="!formable" @change="value => updateParentData('timeId', value)">
          <el-option
            v-for="item in timeInfoOptions"
            :key="item.value"
            :label="item.label"
            :value="parseInt(item.value)"
          />
        </el-select>
        <link-button v-if="formable" :formable="formable" :link-url="'/system/baseData/timeInfo'" :menu-code="'A51'" btn-class="editBtn"/>
      </FormItem>
    </div>
    <div v-if="isTimePeriodShow" class="flex-item" :style="{ flex: `0 0 50%` }">
      <FormItem :label="$t('table.effectiveDate')" :label-width="labelWidth">
        <el-date-picker
          v-model="temp.beginTime"
          :placeholder="$t('table.startTime')"
          style="width: 100%"
          type="date"
          :disabled="disableTimePicker"
          :class="{'date-picker-disabled':disableTimePicker}"
          :picker-options="setBeginTimeRange(temp.endTime)"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="value => updateParentData('beginTime', value)"
        />
      </FormItem>
    </div>
    <div v-if="isTimePeriodShow" class="flex-item" :style="{ flex: `0 0 50%` }">
      <FormItem label-width="0">
        <div style="display: flex; align-items: center; margin-left: 5px; margin-top: 1px">
          <span style="line-height: 30px; width: 20px">{{ $t('pages.till') }}</span>
          <el-date-picker
            v-model="temp.endTime"
            :placeholder="$t('table.endTime')"
            style="flex: 1"
            type="date"
            :disabled="disableTimePicker"
            :class="{'date-picker-disabled':disableTimePicker}"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="setEndTimeRange(temp.beginTime)"
            @change="value => updateParentData('endTime', value)"
          />
          <el-checkbox
            v-model="temp.isPermanent"
            :disabled="!formable"
            :true-label="1"
            :false-label="0"
            style="margin-left: 5px"
            @change="value => updateParentData('isPermanent', value)"
          >{{ $t('pages.longTermEffective') }}</el-checkbox>
        </div>
      </FormItem>
    </div>
    <div class="flex-item">
      <FormItem :label="$t('table.remark')" :label-width="labelWidth">
        <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit @input="value => updateParentData('remark', value)"></el-input>
      </FormItem>
    </div>
    <div v-if="activeAble" class="flex-item">
      <FormItem :label="$t('table.enable')" :label-width="labelWidth">
        <el-switch v-model="temp.active" :disabled="!formable" @change="value => updateParentData('active', value)"/>
      </FormItem>
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'StrategyCommonItems',
  props: {
    formData: { type: Object, default() { return {} } },                  // 从父组件传入form表单绑定的对象
    labelWidth: { type: String, default: '' },                            // 表单字段宽度
    formable: { type: Boolean, default: true },                           // 能否提交表单
    timeAble: { type: Boolean, default: true },                           // 是否显示生效时间
    timeMode: { type: Number, default: 3 },                               // 显示生效时间的模式：1.只显示生效时间 2.只显示生效日期 3.显示生效时间+生效日期
    activeAble: { type: Boolean, default: true },                         // 是否存在启用按钮
    divideSpan: { type: Number, default: 12 }                             // 时间formItem 占比，总值 24
  },
  data() {
    return {
      temp: {},
      defaultTemp: {
        name: '',
        timeId: 1,
        beginTime: undefined,
        endTime: undefined,
        isPermanent: 1,
        remark: '',
        active: false
      }
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    // 显示生效时间
    isTimeShow() {
      return this.timeAble && this.timeMode & 1
    },
    // 显示生效日期
    isTimePeriodShow() {
      return this.timeAble && this.timeMode & 2
    },
    // 生效时间的 FormItem 宽度占比
    timeItemWidth() {
      return !this.timeAble ? 0 : (this.divideSpan / 24 * 100)
    },
    // 生效日期是否禁用
    disableTimePicker() {
      return !this.formable || !!this.temp.isPermanent
    }
  },
  watch: {
    formData(val) {
      this.setDefaultData()
    }
  },
  mounted() {
    this.setDefaultData()
  },
  methods: {
    // 设置默认数据
    setDefaultData() {
      const formData = this.formData || {}
      console.log('set default data ... ', { ...formData });
      
      this.temp = { ...this.defaultTemp, ...formData }
    },
    // 更新外部传入的对象的数据
    updateParentData(key, value) {
      if (key == 'endTime' && value) {
        value = moment(value).endOf('day').format('YYYY-MM-DD HH:mm:ss')
      }
      this.temp[key] = value;
      this.$set(this.formData, key, value)
    },
    // 设置起始时间的选择范围
    setBeginTimeRange(date) {
      const dateObj = date ? new Date(date) : undefined
      return {
        disabledDate: time => !date ? false : time.getTime() > dateObj.getTime()
      }
    },
    // 设置截止时间的选择范围
    setEndTimeRange(date) {
      const dateObj = date ? new Date(date) : undefined
      return {
        disabledDate: time => !date ? false : time.getTime() < dateObj.getTime()
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.flex-container {
  display: flex;
  flex-wrap: wrap;
}
.flex-item {
  box-sizing: border-box; /* 确保边框和内边距不会增加元素的总宽度 */
  flex: 0 0 100%;
}
>>>.date-picker-disabled input{
  background-color: #e4e7e9 ;
}
</style>
