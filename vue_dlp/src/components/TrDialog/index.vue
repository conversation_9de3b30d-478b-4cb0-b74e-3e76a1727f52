<!--
  /**
  * 弹窗组件，通过配置 type 实现调用不同的弹窗组件[ dialog, drawer ]
  * 调用示例：
    <tr-dialog
      type="drawer"       // type 默认值为 dialog，即调用el-dialog； 值为 drawer，调用 el-drawer
      ...                 // 其余配置根据type的值，配置对应的属性即可。当前使用el-dialog/Dialog/el-drawer/Drawer组件也可以通过修改成 tr-dialog并添加type实现简易的组件切换
    >
      ...
    </tr-dialog>
  */
-->
<template>
  <div>
    <Dialog
      v-if="type == 'dialog'"
      :modal="false"
      :title="title"
      :close-on-click-modal="closeOnClickModal"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <template
        v-for="(item, key) in $slots"
        v-slot:[key]="slotScope"
      >
        <slot :name="key" v-bind="slotScope"></slot>
      </template>
      <slot slot="footer" name="footer"></slot>
    </Dialog>
    
    <Drawer
      v-if="type == 'drawer'"
      :title="title"
      :min-width="minWidth"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <template
        v-for="(item, key) in $slots"
        v-slot:[key]="slotScope"
      >
        <slot :name="key" v-bind="slotScope"></slot>
      </template>
      <slot slot="footer" name="footer"></slot>
    </Drawer>
  </div>
</template>

<script>

export default {
  name: 'TrDialog',
  props: {
    title: { type: String, default: 'dialog' },              // 组件标题
    type: { type: String, default: 'dialog' },               // 组件弹窗的类型，[dialog 普通弹窗; drawer: 抽屉弹窗]
    minWidth: { type: Number, default: 800 },                // drawer 配置抽屉的最小宽度
    closeOnClickModal: { type: Boolean, default: false }     // dialog 是否可以通过点击 modal 关闭 Dialog, 默认false
  },
  data() {
    return {
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
  },
  created() {
  },
  methods: {
    
  }
}
</script>

<style lang="scss" scoped>

</style>
