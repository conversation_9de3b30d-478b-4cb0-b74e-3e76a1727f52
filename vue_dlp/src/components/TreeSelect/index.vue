<!--
    /**
     * 树形下拉选择组件，下拉框展示树形结构，提供选择某节点功能，方便其他模块调用
     * 调用示例：
      <tree-select
        :height="400"                             // 下拉框中树形高度
        :width="200"                              // 下拉框中树形宽度
        size="small"                              // 输入框的尺寸: medium/small/mini
        :data="data"                              // 树结构的数据
        :disabled="true"                          // 组件是否可用，true为不可用，默认false
        multiple                                  // 多选
        lazy                                      // 是否懒加载子节点，需与 load 方法结合使用，默认为false
        :load="loadFunc"                          // 加载节点数据的回调方法
        clearable                                 // 可清空选择
        collapse-tags                             // 多选时将选中值按文字的形式展示
        :is-filter="true"                         // 是否提供查找功能，默认为false
        :local-search="localSearch"               // 是否在本地搜索，默认为true。当设置为false时，需要从后台获取节点数据 getSearchList
        :leaf-key="leafKey"                       // 树数据类型，组件内置 'terminal'、'user'，该值为leafNodes的key
        :get-search-list="getSearchList"          // 远程搜索时，获取节点数据的方法
        :select-search-func="selectSearchFunc"    // 选中搜索结果后的回调函数
        check-strictly                            // 多选时，严格遵循父子不互相关联
        :node-key="nodeKey"                       // 绑定nodeKey，默认绑定'id'
        :disabled-all-nodes="true"                // 是否禁用所有节点的勾选框
        :disabled-nodes="disabledNodes"           // 禁用节点勾选框的方法
        :checked-keys="checkedKeys"               // 传递默认选中的节点key组成的数组，lazy模式或localSearch为false时，需传入{key, label} 或node节点对象数组
        hide-tag-close                            // 隐藏已选节点但没有权限的 tag 标签的关闭按钮，需配合 permission 使用
        :permission="roleRange"                   // 多选模式下，可选范围的key数组，若没有传值视为拥有全部权限，若已选中的节点不在可选范围内，则 tag 的关闭按钮不显示
        :filter-key="filterKey"                   // 传递过滤的值
        :filter-node-method="filterNodeMethod"    // 重写过滤节点的方法
        icon-option="iconOption"                  // 树节点icon的配置对象，eg: { typeKey: 'oriData.dbType', key: value}, typeKey可指定data中某个属性为key，默认是type，key为 data.type , value 为 icon的 iconClass
        :render-content="renderContent"           // 自定义渲染函数，可用来更改树节点，默认为添加icon的方法
        :rewrite-node-click-fuc="true"            // 单选模式下，重写树节点单击事件
        :node-click-fuc="nodeClickFuc"            // 单选模式下，重写树节点单击事件，rewriteNodeClickFuc 需要为true
        :menu-name                                // 区分菜单，主要是报表菜单中的统计对象部门树要做终端、终端分组、操作员、操作员分组不可同时勾选的限制使用
        @clickSelect="clickSelect"                // 提供点击select后的回调方法
      />
    */
-->
<template>
  <div>
    <div v-show="isShowSelect" class="mask" @click="isShowSelect = !isShowSelect" />
    <el-popover
      v-model="isShowSelect"
      placement="bottom-start"
      :width="width"
      popper-class="tree-select-popover"
      trigger="manual"
      @show="popoverShow"
      @after-enter="popoverAfterEnter"
      @hide="popoverHide"
      @after-leave="popoverAfterLeave"
    >
      <div v-if="loading" v-loading="true" class="mask loading" />
      <!-- 树组件 -->
      <tree-menu
        v-show="!showAllSelectTags"
        ref="treeMenu"
        :key="leafKey"
        :data="treeData"
        :height="height"
        :style="style"
        :multiple="multiple"
        :lazy="lazy"
        :load="doLoad"
        :is-filter="isFilter"
        :local-search="localSearch"
        :get-search-list="getSearchListFunc"
        :select-search-func="selectSearchFunction"
        :check-strictly="checkStrictly"
        :default-expand-all="defaultExpandAll"
        :default-expanded-keys="defaultExpandedKeys"
        :expand-on-click-node="expandOnClickNode"
        :check-on-click-node="multiple"
        :filter-key="filterKey"
        :filter-node-method="filterNodeMethod"
        :accordion="accordion"
        :icon-option="defaultIconOption"
        :render-content="renderContent"
        :checked-keys="selectedKeys"
        :node-key="nodeKey"
        :menu-name="menuName"
        :click-node="clickNode"
        :disabled-all-nodes="disabledAllNodes"
        :disabled-nodes="disabledNodes"
        :resizeable="false"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @check="check"
        @check-change="checkChange"
        @input-state="inputState"
        @data-change="dataChange"
      />
      <!-- 已选节点的 tag 列表 -->
      <div v-show="showAllSelectTags" class="select-tag-container">
        <el-tag
          v-for="tag in selectedOptions"
          :key="tag[nodeKey]"
          closable
          :disable-transitions="false"
          class="select-tag"
          @close="handleClose(tag)"
        >
          <span :title="tag.label">{{ tag.label }}</span>
        </el-tag>
      </div>
      <!-- select 选择框 -->
      <el-select
        slot="reference"
        ref="select"
        v-model="selectedKeys"
        v-hide-tag-close="selectedKeys"
        :permission="permission"
        :title="title"
        :disabled="disabled"
        :style="selectStyle"
        :size="size"
        :multiple="multiple"
        :clearable="clearable"
        :collapse-tags="collapseTags"
        :placeholder="placeholder"
        class="tree-select"
        @click.native="clickSelect"
        @remove-tag="removeTag"
        @clear="clear"
        @change="selectChange"
      >
        <el-option v-for="item in selectedOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-popover>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getTermTypeDict } from '@/utils/dictionary'

export default {
  name: 'TreeSelect',
  directives: {
    'hide-tag-close': {
      update(el, binding, vnode) {
        const hideTagClose = vnode.context.hideTagClose
        if (!hideTagClose) return
        // 不加延时器会出现第一次不触发问题
        setTimeout(() => {
          // 可选范围
          const permission = vnode.data.attrs.permission
          // 遍历选中的 tag 列表, prevChildren 中的 VNode 可以获取到 key，用于判断是否在可选范围内
          vnode.componentInstance.$children[0].prevChildren.forEach(VNode => {
            const key = VNode.data.key
            const dom = VNode.elm
            // permission 有值，且当前选中的 key 不在可选范围内，隐藏 tag 的关闭按钮
            const hideClose = permission.length > 0 && permission.findIndex(id => id == key) == -1
            const classNameArray = dom.className.split(' ').filter(name => name != 'hide-tag-close')
            hideClose && classNameArray.push('hide-tag-close')
            dom.className = classNameArray.join(' ')
          })
        }, 0)
      }
    }
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    // 外部传入树结构数据
    data: {
      type: Array,
      default: null
    },
    // 是否懒加载子节点，需与 load 方法结合使用
    lazy: {
      type: Boolean,
      default: false
    },
    // 加载子树数据的方法，仅当 lazy 属性为true 时生效
    load: {
      type: Function,
      default() {
        return null
      }
    },
    // 鼠标在选择框上时，是否显示选中数据的悬浮title
    showTitle: {
      type: Boolean,
      default: true
    },
    // 配置是否可多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 选择框的占位文字
    placeholder: {
      type: String,
      default() {
        return this.$t('text.select')
      }
    },
    // 配置区分菜单(报表)
    menuName: {
      type: String,
      default() {
        return ''
      }
    },
    // 获取选中节点(报表)
    clickNode: {
      type: Array,
      default: function() {
        return []
      }
    },
    // 配置是否可清空选择
    clearable: {
      type: Boolean,
      default: false
    },
    // 多选时是否将选中值按文字的形式展示
    collapseTags: {
      type: Boolean,
      default: false
    },
    // 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 默认展开所有节点
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    // 默认展开的节点keys
    defaultExpandedKeys: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否在点击节点的时候展开或者收缩节点
    expandOnClickNode: {
      type: Boolean,
      default: false
    },
    // 显示复选框情况下，是否严格遵循父子不互相关联
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 默认选中的节点key数组, 当lazy为false时，传入key数组，当lazy为true时，传入{key,label} 对象数组
    checkedKeys: {
      type: Array,
      default() {
        return []
      }
    },
    // 组件是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 选择框的尺寸： medium/small/mini
    size: {
      type: String,
      default: 'mini'
    },
    // 下拉框中树形高度
    height: {
      type: Number,
      default: 300
    },
    // 下拉框中树形宽度
    width: {
      type: Number,
      default: 250
    },
    // 单选模式下，是否重写树节点单击事件，默认为false
    rewriteNodeClickFuc: {
      type: Boolean,
      default: false
    },
    // 单选模式下，重写树节点单击事件，返回值为 false 时不触发选中，rewriteNodeClickFuc 需要为true
    nodeClickFuc: {
      type: Function,
      default: function(data, node, vm) {

      }
    },
    // 是否提供查找功能，默认为false
    isFilter: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 是否在本地搜索，默认为true
    localSearch: {
      type: Boolean,
      default: true
    },
    // 树数据类型，组件内置 'terminal'、'user', 'dept'
    leafKey: {
      type: String,
      default: 'user'
    },
    // 终端过滤方法（废弃，使用filterKey替代）
    // terminalFilter: {
    //   type: Function,
    //   default: null
    // },
    // 获取所有树节点的列表，以供搜索使用
    getSearchList: {
      type: Function,
      default: null
    },
    // 选中搜索结果后的回调函数
    selectSearchFunc: {
      type: Function,
      default: null
    },
    // 重写过滤节点的方法
    filterNodeMethod: {
      type: Function,
      default: null
    },
    // 传入需要过滤的节点的nodeKey值
    filterKey: {
      type: [Number, String, Array, Object, Function],
      default() {
        return ''
      }
    },
    // 是否审计日志的终端树（审计日志使用的数据没有回收站节点），默认为 false
    isLogTerminalTree: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 手风琴模式，默认为false
    accordion: {
      type: Boolean,
      default: false
    },
    // icon的配置对象
    iconOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // 渲染节点的方法
    renderContent: {
      type: Function,
      default: null
    },
    // 是否禁用所有节点的勾选框
    disabledAllNodes: {
      type: Boolean,
      default: false
    },
    // 禁用节点勾选框的方法，参数 data, node
    disabledNodes: {
      type: Function,
      default: null
    },
    // 隐藏已选节点但没有权限的 tag 标签的关闭按钮，需配合 permission 使用
    hideTagClose: {
      type: Boolean,
      default: false
    },
    // 多选模式下，可选范围的key数组，若没有传值视为拥有全部权限，若已选中的节点不在可选范围内，则 tag 的关闭按钮不显示
    permission: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      osType: 15,                           // 支持的终端操作系统类型，1-win,2-linux,4-mac,8-移动设备
      isShowSelect: false,                  // 是否显示树状选择器popover
      popoverCloseable: true,               // 下拉选择框是否可以关闭
      mouseEnter: false,                    // 鼠标进入收缩的tag标签的标识，为 true 时，下拉框显示的内容为已选节点的 tag 标签列表
      selectedKeys: [],                     // 选中的节点 nodeKey
      selectedMap: {},                      // 选中节点的 map
      style: 'width: 100%; border: none;',
      selectStyle: 'width: 100%;',
      builtInData: {
        terminal: {
          getTreeData: () => {
            return this.isLogTerminalTree ? this.logTermTree : this.termTree
          },
          getSearchList: () => {
            return (this.isLogTerminalTree ? this.logTermTreeList : this.termTreeList) || []
          }
        },
        user: {
          getTreeData: () => {
            return this.userTree
          },
          getSearchList: () => {
            return this.userTreeList
          }
        },
        dept: {
          getTreeData: () => {
            return this.deptTree
          },
          getSearchList: () => {
            return this.deptTreeList
          }
        }
      },
      termIcon: {}
    }
  },
  computed: {
    ...mapGetters([
      'deptTree',
      'deptTreeList',
      'termTree',
      'termTreeList',
      'logTermTree',
      'logTermTreeList',
      'userTree',
      'userTreeList',
      'stgBaseConfig'
    ]),
    // 是否内置模式，没有传入数据或非懒加载的情况
    isBuiltIn() {
      return !this.data && !this.lazy
    },
    // 内置数据类型
    dataType() {
      return this.isBuiltIn ? this.leafKey : ''
    },
    // 没有传入数据或非懒加载的模式下，获取内置数据，否则返回data
    treeData() {
      const data = this.isBuiltIn ? this.getCurrentData().getTreeData() : this.data
      // TODO 这边是什么数据？
      // 将数据传递给父组件，报表中有使用
      // this.$emit('deptTreeData', this.deptTreeData);
      this.$emit('deptTreeData', data);
      return data
    },
    // 计算属性，返回 获取搜索节点列表 的方法
    getSearchListFunc() {
      // 外部传入获取搜索列表的方法
      if (typeof this.getSearchList === 'function') {
        return this.getSearchList.bind(this);
      }
      // 返回默认方法，获取内置数据的搜索节点列表
      return () => {
        return (this.dataType && this.builtInData[this.dataType].getSearchList()) || []
      }
    },
    // 计算属性，返回 选中搜索结果后 的回调函数
    selectSearchFunction() {
      // 外部传入 选中搜索结果后的回调函数
      if (typeof this.selectSearchFunc === 'function') {
        return this.selectSearchFunc.bind(this);
      }
      // 返回默认方法，选中搜索结果后的回调函数
      return (nodeData) => {
        const node = this.tree().getNode(nodeData)
        if (this.multiple) {
          // 节点未禁用
          if (!node.disabled) {
            // 勾选节点
            this.checkSelectedNode([...this.selectedKeys, node.key])
          }
        } else {
          // 选中节点
          this.handleNodeClick(nodeData, node)
        }
        // 清空搜索框内容
        this.clearFilter()
      }
    },
    // 选中节点的下拉框选项，使用节点的数据生成（删除children，增加 value）
    selectedOptions() {
      const keys = Array.isArray(this.selectedKeys) ? this.selectedKeys : [this.selectedKeys]
      return keys.reduce((options, key) => {
        if (key) {
          let data = this.selectedMap[key]
          if (data) {
            data = JSON.parse(JSON.stringify(data))
            const value = data[this.nodeKey]
            delete data.children
            options.push({ ...data, value })
          }
        }
        return options
      }, [])
    },
    // 鼠标在选择框上时，选中数据的悬浮title
    title() {
      let title = ''
      if (this.showTitle) {
        if (this.selectedOptions.length > 50) {
          // 勾选的节点数量大于 50 时，title 只显示前 50 项
          title = this.selectedOptions.slice(0, 50).map(opt => opt.label).join(', ') + ' ...'
        } else {
          title = this.selectedOptions.map(opt => opt.label).join(', ')
        }
      }
      return title
    },
    // 鼠标进入收缩tag时，下拉框是否显示已选节点的 tag
    showAllSelectTags() {
      return this.multiple && this.collapseTags && this.mouseEnter
    },
    // 默认图标与外部传入的图标合并的配置
    defaultIconOption() {
      const opt = {
        '': {},
        'terminal': {
          typeKey: 'dataType',
          'G': 'terminalGroup',
          127: 'error',
          ...this.termIcon
        },
        'user': {
          typeKey: 'dataType',
          'G': 'userGroup',
          4: 'userGroup',
          2: 'user'
        }
      }
      return Object.assign({}, opt[this.dataType], this.iconOption)
    }
  },
  watch: {
    $route(val) {
      this.hidePopover()
    },
    isShowSelect(val) {
      // 隐藏select自带的下拉框
      this.$refs.select.blur()
      if (val) {
        document.body.addEventListener('click', this.hidePopover)
        window._treeSelectVm = this
      } else {
        document.body.removeEventListener('click', this.hidePopover)
      }
    },
    checkedKeys(val, oldVal) {
      let multiChanged = false
      let singeChanged = false
      if (this.multiple) {
        multiChanged = JSON.stringify(this.checkedKeys) === JSON.stringify(this.selectedKeys)
      } else {
        if (this.checkedKeys.length == 0) {
          singeChanged = this.selectedKeys == ''
        } else {
          const checkedKey = [...this.checkedKeys].shift()
          singeChanged = (checkedKey != null && typeof checkedKey == 'object')
            ? ('' + checkedKey[this.nodeKey] == this.selectedKeys) || ('' + checkedKey.key == this.selectedKeys)
            : '' + checkedKey == this.selectedKeys
        }
      }
      if ((this.multiple && multiChanged) || (!this.multiple && singeChanged)) {
        return
      }
      this.initCheckedNode()
    },
    selectedOptions(val) {
      // 多选且选项收缩时，给 tag 添加事件
      if (this.multiple && this.collapseTags && val.length > 1) {
        this.addTagEvent()
      }
      if (this.isShowSelect && this.showAllSelectTags) {
        if (val.length > 0) {
          setTimeout(() => {
            this.triggerResize()
          }, 400);
        } else {
          this.isShowSelect = false
        }
      }
    },
    dataType(val, oldVal) {
      // 内置数据发生变化时，清除默认展开的节点
      this.defaultExpandedKeys.splice(0)
    }
  },
  mounted() {
    this.initCheckedNode()
  },
  async created() {
    // 终端图标
    getTermTypeDict().forEach(item => {
      this.$set(this.termIcon, item.value, item.icon)
    })
    // 策略基础配置
    const stgBaseConfigMap = this.stgBaseConfig
    if (!stgBaseConfigMap) {
      await this.$store.dispatch('commonData/setStgBaseConfig')
    }
    const menuCode = this.$route.meta.code
    const config = stgBaseConfigMap && Object.values(stgBaseConfigMap).filter(c => c['routerPath'] === menuCode)[0]
    this.osType = (config && config['osType']) || this.osType
  },
  methods: {
    // TreeMenu组件
    treeMenu() {
      return this.$refs.treeMenu
    },
    // TreeMenu中的tree组件
    tree() {
      return this.treeMenu().$refs.tree
    },
    // 获取当前指定内置数据
    getCurrentData() {
      return this.builtInData[this.dataType]
    },
    // collapse 将展开的节点合起来，并展开默认展开的节点。
    // 因为当搜索框有内容时，数据发生变化会在过滤后展开所有节点，所以这边手动将节点合起来。
    handleFilter(collapse) {
      this.treeMenu().handleFilter(collapse)
    },
    // 初始化勾选的节点
    initCheckedNode() {
      if (this.checkedKeys.length > 0) {
        this.clearSelectedKeys()
        this.checkSelectedNode(this.checkedKeys)
      } else {
        this.clearSelectedKeys()
      }
    },
    // TreeMenu搜索框状态变化事件
    inputState(state) {
      // 当搜索框没有聚焦时才可以关闭popover
      this.popoverCloseable = state != 'focus'
    },
    // 清空搜索框内容
    clearFilter() {
      this.treeMenu().clearFilter()
    },
    // 点击select框显示下拉框，当设置disabled为true时不显示
    clickSelect() {
      if (!this.isShowSelect) {
        // 关闭其他treeSelect的下拉框
        window._treeSelectVm && window._treeSelectVm.hidePopover()
      }
      // 组件未禁用，显示下拉选择框
      if (!this.disabled) {
        this.isShowSelect = !this.isShowSelect
        if (this.isShowSelect) {
          this.treeMenu().addScrollerEvent()
          this.treeMenu().resetItemWrapperWidth(100)
        }
      }
      this.$emit('clickSelect')
    },
    // 给已选 tag 添加事件
    addTagEvent() {
      this.$nextTick(() => {
        const el = this.$refs.select.$el
        // 获取收缩的 tag 节点
        const collapseTagEl = Array.from(el.querySelectorAll('.el-select__tags .el-tag')).pop()
        if (!collapseTagEl) return
        // 添加鼠标进入的事件
        collapseTagEl.addEventListener('mouseenter', this.handleTagMouseEnter)
      })
    },
    // 鼠标进入收缩的 tag 标签
    handleTagMouseEnter() {
      if (this.isShowSelect) return
      this.$nextTick(() => {
        this.mouseEnter = true
        this.clickSelect()
      });
    },
    handleTagMouseLeave() {
    },
    // 下拉框中 tag 列表，移除 tag 的方法
    handleClose(tag) {
      this.selectedKeys = this.selectedKeys.filter(key => key != tag[this.nodeKey])
      this.removeTag(tag[this.nodeKey])
    },
    // 多选模式下移除tag时触发, val：移除的tag值，即树节点的nodeKey值
    removeTag(val) {
      // 如果父子节点互相关联，移除节点时，需要将子节点和所有上级节点也移除
      if (!this.checkStrictly) {
        // 移除节点及其子节点、所有上级节点的 map
        const removeKeysMap = {}
        // 子节点及本身
        this.treeMenu().getChildKeys(val).forEach(key => {
          removeKeysMap[key] = true
        });
        // 所有上级节点及本身
        this.treeMenu().getNodePath(val).forEach(data => {
          removeKeysMap[data[this.nodeKey]] = true
        });
        // 过滤移除的节点
        this.selectedKeys = this.selectedKeys.filter(key => !removeKeysMap[key])
      }
      this.$nextTick(() => {
        this.$emit('change', this.selectedKeys, this.selectedOptions)
      })
    },
    // 可清空的单选模式下用户点击清空按钮时触发
    clear() {
      this.tree().setCurrentKey(null)
    },
    // 选中的select选项改变的回调
    selectChange(selectedKeys) {
      this.$emit('change', this.selectedKeys, this.selectedOptions)
    },
    // 清除选中的 selectedKeys
    clearSelectedKeys() {
      if (this.multiple) {
        this.selectedKeys.splice(0)
      } else {
        this.selectedKeys = ''
      }
    },
    // 调用 clearSelectedKeys
    clearSelectedNode() {
      this.clearSelectedKeys()
    },
    handleNodeExpand(data, node, el) {

    },
    // 根据传入的 keys 勾选节点
    checkSelectedNode(keys) {
      if (this.multiple) {
        // 多选
        if (typeof keys[0] === 'object') {
          // 传入的 key 是对象
          const toCheckKeys = []
          keys.forEach(item => {
            toCheckKeys.push(item[this.nodeKey])
            this.checkNode(item, true)
          })
          this.tree().setCheckedKeys(toCheckKeys)
        } else {
          // 传入的 key 是 nodeKey
          if (this.isBuiltIn) {
            //
            const list = this.getSearchListFunc()
            const keysSet = new Set(keys)
            let keysLength = keys.length
            const toCheckKeys = []
            for (let i = 0; i < list.length; i++) {
              const data = list[i];
              const key = data[this.nodeKey]
              if (keysSet.has(key)) {
                toCheckKeys.push(key)
                this.checkNode(data, true)
                keysLength--
              }
              if (keysLength == 0) {
                break
              }
            }
            this.tree().setCheckedKeys(toCheckKeys)
          } else {
            this.tree().setCheckedKeys(keys)
            const checkedNodes = this.tree().getCheckedNodes()
            checkedNodes.forEach((nodeData) => {
              nodeData && this.checkNode(nodeData, true)
            })
          }
        }
      } else {
        // 单选
        const item = keys[0]
        let node
        // 单选情况下，处理懒加载无法选中未加载的节点。
        if (this.lazy || !this.localSearch) {
          if (typeof item === 'object') {
            // 传入的 key 是对象
            node = item.hasOwnProperty('data') ? item : { data: item }
            // 为了兼容历史数据，{key:xxx, label:xxx}，主动设置nodeKey对应的值
            if (!node.data[this.nodeKey] && node.data.key) {
              node.data[this.nodeKey] = node.data.key
            }
          } else {
            // 传入的 key 是 nodeKey
            const allNodes = this.getSearchListFunc()
            node = { data: allNodes.filter(node => node[this.nodeKey] == item)[0] }
          }
        } else {
          this.tree().setCurrentKey(item)
          node = this.tree().getNode(item)
        }
        this.clearSelectedKeys()
        node && this.checkNode(node.data, true)
      }
      this.$emit('change', this.selectedKeys, this.selectedOptions)
    },
    // 选中节点的方法，data：选中的节点的data或nodeKey，select：布尔值，true为选中 false为取消选中
    checkNode(data, select) {
      if (!data) return
      const key = typeof data === 'object' ? data[this.nodeKey] : data
      this.selectedMap[key] = select ? data : undefined
      if (this.multiple) {
        if (select) {
          !this.selectedKeys.includes(key) && this.selectedKeys.push(key)
        } else {
          this.selectedKeys = this.selectedKeys.filter(nodeKey => key !== nodeKey)
        }
      } else {
        this.selectedKeys = select ? key : ''
      }
    },
    // 获取选中的数据 keys
    getSelectedKeys() {
      return this.selectedKeys
    },
    // 获取选中的数据 options
    getSelectedOptions() {
      return this.selectedOptions
    },
    // 获取选中的数据 datas
    getSelectedNode() {
      const selectKeys = this.multiple ? this.selectedKeys : [this.selectedKeys]
      const selectNode = selectKeys.map(key => this.selectedMap[key]).filter(node => node)
      return selectNode
    },
    // 关闭下拉选择框
    hidePopover() {
      // TreeMenu搜索节点的组件
      const autocomplete = this.treeMenu() && this.treeMenu().$refs.autocomplete
      // 搜索结果的下拉框是否显示
      const suggestionVisible = autocomplete ? autocomplete.suggestionVisible : false

      // 当 popoverCloseable 为 true 且 搜索结果下拉框不显示时，才可以关闭下拉选择框
      if (this.popoverCloseable && !suggestionVisible) {
        this.isShowSelect = false
      }
    },
    // popover 显示的回调方法
    popoverShow() {},
    // popover 显示动画播放完毕后触发
    popoverAfterEnter() {},
    // popover 隐藏的回调方法
    popoverHide() {},
    // popover 隐藏动画播放完毕后触发
    popoverAfterLeave() {
      this.mouseEnter = false
    },
    doLoad(node, resolve) {
      this.load(node, nodeDatas => {
        this.treeMenu() && this.treeMenu().resetItemWrapperWidth(100)
        resolve(nodeDatas)
      })
    },
    // 单选，节点被点击时的回调
    async handleNodeClick(data, node, el) {
      if (!this.multiple) {
        // 当重写节点点击方法，且方法返回值为 false （布尔值false 或 Promise 值为 false）时，代码不继续往下走
        if (this.rewriteNodeClickFuc) {
          const result = this.nodeClickFuc(data, node, this)
          // nodeClickFuc返回 false，则不再执行代码
          if (result === false) return
          // 当点击节点后，需要经过耗时操作再确定是否选中时，返回 Promise 来判断是否选中
          const isPromise = result && typeof result.then === 'function'
          if (isPromise) {
            // 使用 async...await 获取 Promise 的值
            const promiseResolve = await result
            // Promise 返回值是 false，则不再执行代码
            if (promiseResolve === false) return
          }
        }

        this.clearSelectedKeys()
        this.checkNode(node.data, true)
        this.isShowSelect = !this.isShowSelect
        this.$emit('change', this.selectedKeys, data)
      }
    },
    // 多选，节点勾选时的回调
    check(nodeData, checkedInfo) {
      // 根据选中节点生成 map
      this.selectedMap = checkedInfo.checkedNodes.reduce((map, data) => {
        const key = data[this.nodeKey]
        map[key] = data
        return map
      }, {})
      // 修改 selectedKeys
      if (this.multiple) {
        if (checkedInfo.checkedKeys.length == 0) {
          this.clearSelectedKeys()
        } else {
          this.selectedKeys = checkedInfo.checkedKeys
        }
      } else {
        this.selectedKeys = checkedInfo.checkedKeys.toString()
      }
      this.$emit('change', this.selectedKeys, this.selectedOptions)
    },
    checkChange(selectedKeys, selectedData, checkedInfo) {
    },
    dataChange() {
      this.handleFilter(true)
      // 数据发生变化后，更新已选节点的数据
      this.$nextTick(() => {
        if (this.multiple) {
          const selectedKeys = [...this.selectedKeys]
          this.clearSelectedKeys()
          setTimeout(() => {
            this.checkSelectedNode(selectedKeys)
          }, 500);
        } else {
          const selectedData = this.findNode(this.treeData, this.selectedKeys, this.nodeKey)
          if (selectedData) {
            this.clearSelectedKeys()
            this.checkNode(selectedData, true)
          } else {
            this.clearSelectedKeys()
            this.$emit('change', this.selectedKeys, this.selectedOptions)
          }
        }
      })
    },
    // 根据 属性及值 从树数据 data 查找对应节点的 data
    findNode(data, keyValue, keyField) {
      return this.treeMenu().findNode(data, keyValue, keyField)
    },
    // 调用 TreeMenu 的 getCurrentKey 方法
    getCurrentKey() {
      return this.treeMenu().getCurrentKey()
    },
    // 调用 TreeMenu 的 setCurrentKey 方法
    setCurrentKey(key) {
      this.treeMenu().setCurrentKey(key)
    },
    // 调用 TreeMenu 的 getCurrentNode 方法
    getCurrentNode() {
      return this.treeMenu().getCurrentNode()
    },
    // 调用 TreeMenu 的 setCurrentNode 方法
    setCurrentNode(node) {
      this.treeMenu().setCurrentNode(node)
    },
    // 调用 TreeMenu 的 getNode 方法, (data) 要获得 node 的 key 或者 data
    getNode(data) {
      return this.treeMenu().getNode(data)
    }
  }
}
</script>

<style lang="scss" scoped>
  .mask{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: 11;
  }
  .loading {
    border-radius: 3px;
    position: absolute;
    opacity: 1;
    overflow: hidden;
  }
  .tree-select{
    z-index: 111;
  }
  >>>.el-input__inner{
    font-size: 14px;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
  >>>.el-select__tags {
    max-height: 102px;
    margin-top: 2px;
    overflow: auto;
  }
  >>>.el-select--small .el-tag--mini {
    height: 24px;
    padding: 0 8px;
    line-height: 22px;
  }
  >>>.el-select .el-tag {
    max-width: calc(100% - 65px);
  }
  .select-tag-container {
    max-height: 300px;
    overflow: auto;
  }
  .select-tag {
    height: 30px;
    line-height: 28px;
    max-width: calc(100% - 20px);
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
    margin: 3px 5px;
    position: relative;
    padding-right: 20px;
    >>>.el-icon-close {
      top: 6px;
      right: 2px;
      position: absolute;
    }
  }
  >>>.hide-tag-close .el-tag__close {
    display: none;
  }
</style>
