<template>
  <div>
    <stg-target-form-item
      ref="stgTargetItem"
      v-bind="$attrs"
      v-on="$listeners"
    />
    <strategy-common-items
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>

export default {
  name: 'StgBaseFormItem',
  props: {
  },
  data() {
    return {
    }
  },
  methods: {
    valid() {
      return this.$refs['stgTargetItem'].valid()
    }
  }
}

</script>
