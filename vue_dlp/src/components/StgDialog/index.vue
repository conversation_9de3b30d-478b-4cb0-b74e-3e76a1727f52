<!--
  /**
  * 策略弹窗，实现策略根据不同终端类型分别配置不同内容的功能
  * @update 2022-07-14
  * 调用示例：
    <stg-dialog
      ref="stgDlg"
      :title="xx策略"                                           // 标题
      :title-tip="标题提示"                                     // 标题提示
      label-w="125px"                                          // 公共form表单label的宽度
      os-label-w="110px"                                       // 不同配置内form表单label的宽度
      :width="800"                                             // dialog的宽度，单位px
      :pane-height="400"                                       // pane的高度
      :entity-type-span="8"                                    // 策略应用树，占用span
      :stg-code="201"                                          // 策略编码，stg_base_config的number字段值
      :active-able="activeAble"                                // 是否显示启用按钮
      :time-able="false"                                       // 是否显示生效时间
      :rules="rules"                                           // form表单校验规则
      :model="temp"                                            // 表单参数对象
      :entity-node="entityNode"                                // 策略应用对象树节点
      :create="createStrategy"                                 // 新增数据的方法
      :update="updateStrategy"                                 // 修改数据的方法
      :get-by-name="getStrategyByName"                         // 通过策略名和策略编号获取策略的方法
      :format-row-data="formatRowData"                         // 格式化表格数据，有些表格数据需要经过处理后，才能作为form表单数据，否则会出现应该显示中文的显示了数字等情况
      :format-form-data="formatFormData"                       // 格式化Form表单数据，有些表单数据需要经过处理后，才能提交
      :validate-form="validateForm"                            // 返回true说明验证通过，否则验证失败，验证失败的提示自己处理
      @submitEnd="submitEnd"                                   // 数据提交完毕后，向上级组件传递数据，参数 dialogStatus, formData
      @slotChange="slotChange"                                 // slot值发生变化时(切换终端tab)，将该值及对应的temp对象传出去
      @closed="closed"                                         // 窗口关闭后的回调
    >
      <template :slot="slotName">
        ...此处写内容
      </template>
    </stg-dialog>
  */
-->
<template>
  <div>
    <tr-dialog
      v-el-drag-dialog
      :type="type"
      :close-on-click-modal="false"
      :modal="false"
      :fullscreen="fullscreen"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="width + 'px'"
      @close="close"
      @closed="closed"
      @open="handleOpen"
    >
      <div slot="title" class="el-dialog__title">
        {{ dialogTitle }}
        <el-tooltip v-if="titleTip!==''" effect="dark" placement="right">
          <div slot="content">
            {{ titleTip }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="headForm4StgDialog" :rules="rules" :model="temp" label-position="right" :label-width="labelW" :style="{'width': (headFormWidth) + 'px'}">
        <StgBaseFormItem
          ref="stgTargetItem"
          :stg-code="stgCode"
          :form-data="temp"
          :tree-width="370"
          :entity-type-span="entityTypeSpan"
          :multiple="multipleEntity"
          :is-disabled="!formable"
          :strategy-def-type="defaultTemp.strategyDefType"
          :formable="formable"
          :time-able="timeAble"
          :time-mode="timeMode"
          :active-able="activeAble"
          @entityNodeData="nodeData => entityIdChange(nodeData, temp)"
        />
      </Form>
      <el-tabs
        v-if="osAble"
        v-model="activeTab"
        v-loading="dataLoading"
        tab-position="left"
        type="card"
        class="os-tabs"
        @tab-click="tabClick"
      >
        <el-tab-pane v-for="item in osTypeShowedTabs" :key="item.value" :name="item.type" :style="paneStyle">
          <span slot="label" :title="item.label">
            <el-checkbox
              v-show="item.value > 0"
              :key="item.value"
              v-model="item.checked"
              :disabled="!formable"
              @click.native.stop=""
              @change="(val) => { osTypeCheck(val, item.value) }"
            ></el-checkbox>
            <svg-icon :icon-class="item.type" />
          </span>
          <Form
            :ref="'dataForm4StgDialog' + item.value"
            :model="tempChildren[item.value]"
            :rules="rules"
            label-position="right"
            :label-width="osLabelW"
            :style="{'width': '100%', 'min-height': '100px'}"
          >
            <slot :name="item.value" :os-type="item.value" :model="tempChildren[item.value]"></slot>
          </Form>
        </el-tab-pane>
      </el-tabs>
      <Form
        v-if="!osAble"
        :ref="'dataForm4StgDialog' + allOsType"
        :model="tempChildren[allOsType]"
        :rules="rules"
        label-position="right"
        :label-width="osLabelW"
        :style="{'width': (dataFormWidth) + 'px'}"
      >
        <slot :name="allOsType" :os-type="allOsType" :model="tempChildren[allOsType]"></slot>
      </Form>
      <div slot="footer" class="dialog-footer">
        <slot name="button"></slot>
        <el-button v-if="formable && dialogStatus === 'update'" type="primary" @click="handleCopy">
          {{ $t('components.saveAs') }}
        </el-button>
        <el-button v-if="formable" type="primary" :loading="submitting" @click="saveData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('components.saveAs')"
      :visible.sync="copyVisible"
      width="600px"
    >
      <p style="padding-left: 10px;">{{ $t('components.confirmSaveAs') }}</p>
      <Form ref="copyForm4StgDialog" :model="copyTemp" label-position="right" label-width="80px" style="width: 560px">
        <StgBaseFormItem
          ref="copyStgTargetItem"
          :stg-code="stgCode"
          :form-data="copyTemp"
          :tree-width="350"
          :entity-type-span="entityTypeSpan"
          :multiple="multipleEntity"
          :is-disabled="!formable"
          :strategy-def-type="defaultTemp.strategyDefType"
          :formable="formable"
          :time-able="timeAble"
          :time-mode="timeMode"
          :active-able="activeAble"
          @entityNodeData="nodeData => entityIdChange(nodeData, copyTemp)"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" type="primary" @click="saveCopyData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelCopy">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUsedScope } from '@/api/stgCommon'
import { containStgObject, entityLink } from '@/utils'
import { getTermTypeDict } from '@/utils/dictionary'
import { timeIdValidator } from '@/utils/validate'
import { mapGetters } from 'vuex'

export default {
  name: 'StgDialog',
  props: {
    type: { type: String, default: 'dialog' },                            // 弹窗的类型，默认dialog. ['dialog', 'drawer']
    activeAble: { type: Boolean, default: true },                         // 是否显示启用按钮
    timeAble: { type: Boolean, default: false },                          // 是否显示生效时间
    timeMode: { type: Number, default: 3 },                               // 显示生效时间的模式：1.只显示生效时间 2.只显示生效日期 3.显示生效时间+生效日期
    labelW: { type: String, default: '100px' },                           // 公共form表单label的宽度
    osLabelW: { type: String, default: '100px' },                         // 不同配置内form表单label的宽度
    width: { type: Number, default: 800 },                                // dialog的宽度，单位px
    paneHeight: { type: Number, default: 355 },                           // pane的高度
    fullscreen: { type: Boolean, default: false },                        // 是否全屏显示
    entityTypeSpan: { type: Number, default: 12 },                        // 策略应用树，占用span
    multipleEntity: { type: Boolean, default: true },                     // 是否提供应用对象多选，即是否提供复选框
    title: { type: String, default: 'XX策略' },                           // 标题
    titleTip: { type: String, default: '' },                              // 标题提示
    rules: { type: Object, default() { return {} } },                     // form表单校验规则
    model: { type: Object, default() { return {} } },                     // 表单参数对象
    stgCode: { type: Number, default: 0 },                                // 策略编码，stg_base_config的number字段值
    entityNode: { type: Object, default() { return {} } },                // 策略应用对象树节点
    create: {                                                             // 新增数据的方法
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    update: {                                                             // 修改数据的方法
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getByName: {                                                          // 通过策略名和策略编号获取策略的方法
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    formatRowData: { type: Function, default: function(data) {} },        // 格式化表格数据，有些表格数据需要经过处理后，才能作为form表单数据，否则会出现应该显示中文的显示了数字等情况
    formatFormData: { type: Function, default: function(data) {} },       // 格式化Form表单数据，有些表单数据需要经过处理后，才能提交
    validateForm: { type: Function, default: null },                      // 返回true说明验证通过，否则验证失败，验证失败的提示自己处理
    bodyScroll: { type: Function, default: null }                         // 弹窗的body滚动时调用的方法
  },
  data() {
    return {
      usedScope: 3,                 // 生效对象可配置类型，1：终端，2：操作员，3：都可配置
      osTypeOptions: [
        // value 类型的值， checked 是否勾选， disabled 是否可以配置
        // type 图标的值， label 悬浮文字， termTypes 终端类型（created时，从字典中获取）
        { value: 1, checked: false, disabled: false, type: 'windows', label: 'windows', termTypes: [] },
        { value: 2, checked: false, disabled: false, type: 'linux', label: 'linux', termTypes: [] },
        { value: 4, checked: false, disabled: false, type: 'mac', label: 'mac', termTypes: [] },
        { value: 8, checked: false, disabled: false, type: 'phone', label: this.$t('components.mobile'), termTypes: [] }
      ],
      osTypeShowedTabs: [],         // 终端类型tab的显示项
      osTypes: [],                  // 已勾选的终端类型，osTypeOptions选项的value
      osAble: false,                // 策略是否区分不同终端（true：显示不同终端tab）
      allOsType: 7,                 // 所有可配置终端类型的总值，根据 策略基础设置 进行调整
      activeSlotName: '',           // 当前tab的slotName
      activeTab: '',                // 当前tab的值，osTypeOptions选项的type
      stgIds: [],                   // 记录当前策略的id
      temp: {},                     // 当前策略的基本信息
      tempChildren: [],             // 当前策略不同终端类型的数据
      copyTemp: {},                 // 另存为策略的基本信息
      defaultTemp: {                // 公共表单默认temp
        name: '',
        remark: '',
        timeId: 1,
        active: false,
        entityType: null,           // 弃用：应用对象类型
        entityId: null,             // 弃用：应用对象ID
        objectType: null,           // 应用对象类型
        objectIds: null,            // 应用对象ID，包括终端ID或操作员ID
        objectGroupIds: null,       // 应用对象ID，包括终端分组ID或操作员分组ID
        strategyDefType: null,       // 策略定义类型：0-普通策略，1-预定义策略
        isPermanent: 1,               // 策略是否长期有效， 默认长期有效
        beginTime: undefined,        // 策略生效起始时间
        endTime: undefined           // 策略生效终止时间
      },
      dataLoading: false,           // tabs数据加载
      dialogVisible: false,         // 策略窗口显示
      dlgInit: false,               // 窗口是否已初始化，修改数据时根据该值决定窗口弹出的时机
      isHandleClose: true,          // 是否手动关闭了弹窗，避免关闭后因为延时器重新打开弹窗
      copyVisible: false,           // 另存为窗口显示
      isGeneralStrategy: true,      // true 常规策略， false 预定义策略
      dialogStatus: 'create',       // 弹窗类型
      submitting: false,            // 数据提交中
      formable: true,               // 表单是否可编辑
      defaultRules: {               // 公共表单验证规则
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('components.selectEffectTime'), trigger: 'change' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'stgBaseConfig',
      'mstgBaseConfig',
      'currentVm'
    ]),
    // tab页的样式
    paneStyle() {
      // const paneHeight = this.fullscreen ? '' : ` max-height: ${this.paneHeight}px;`
      // return `min-height: 280px;${paneHeight}`
      return `min-height: 280px;`
    },
    // 返回 {name: value}对象， eg：{ 'windows': 1 }
    tabNameValue() {
      return this.osTypeOptions.reduce((total, current) => {
        total[current.type] = current.value
        return total
      }, {})
    },
    // 返回 {value: name}对象， eg：{ 1: 'windows' }
    tabName() {
      return this.osTypeOptions.reduce((total, current) => {
        total[current.value] = current.type
        return total
      }, {})
    },
    dialogTitle() {
      return ({
        create: this.i18nConcatText(this.title, 'create'),
        update: this.i18nConcatText(this.title, 'update'),
        view: this.i18nConcatText(this.title, 'details')
      })[this.dialogStatus]
    },
    headFormWidth() {
      return this.width - 52
    },
    dataFormWidth() {
      return this.headFormWidth - (this.osAble ? 20 : 0)
    },
    // 终端是否可用
    terminalUseable() {
      return this.usedScope != 2
    },
    // 操作员是否可用
    userUseable() {
      return this.usedScope != 1
    }
  },
  watch: {
    stgCode() {
      this.loadStgBaseConfig()
    },
    stgBaseConfig() {
      this.loadStgBaseConfig()
    },
    mstgBaseConfig() {
      this.loadMstgBaseConfig()
    },
    activeSlotName(val) {
      if (val) {
        if (!this.tempChildren[val]) {
          this.tempChildren[val] = {}
        }
        // slot值发生变化时，将该值及对应的temp对象传出去
        this.$emit('slotChange', val, this.tempChildren[val])
      }
    }
  },
  mounted: function() {
    // 根据策略类型编号获取生效范围（1终端、2操作员、3两者）
    if (this.stgCode != 0) {
      const stgBaseConfigMap = this.stgBaseConfig
      if (stgBaseConfigMap) {
        this.usedScope = stgBaseConfigMap[this.stgCode].usedScope
      } else {
        getUsedScope(this.stgCode).then(response => {
          this.usedScope = response.data
        })
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      // 预定义策略
      this.isGeneralStrategy = false
      this.defaultTemp.strategyDefType = 1
    } else {
      // 应用策略
      this.isGeneralStrategy = true
      this.defaultTemp.strategyDefType = 0
    }
    this.initTermTypes()
    this.resetTemp()
    this.loadStgBaseConfig()
    Object.assign(this.rules, this.defaultRules)
  },
  methods: {
    entityLink,
    /**
     * 初始化配置项的终端类型，从字典中获取
     */
    initTermTypes() {
      // 系统类型的map：1：Windows，2：Linux，4：Mac，8：phone
      const termType = { 1: [], 2: [], 4: [], 8: [] }
      // 从字典获取终端数据
      const termMap = getTermTypeDict()
      termMap.forEach(term => {
        // type 终端类型：0：Windows，1：Linux，2：Mac，3：phone
        const type = 15 & term.value
        termType[2 ** type].push(term.value)
      })
      this.osTypeOptions.forEach(opt => {
        opt.termTypes = termType[opt.value]
      })
    },
    /**
     * 调用弹窗组件
     * @param data 表单数据对象
     * @param editable 是否可编辑
     * @param callback 回调方法
     * @param isGeneralStrategy 应用对象选择框是否显示，未配置则不修改
     */
    show(data, editable, callback, isGeneralStrategy) {
      this.isHandleClose = false
      if (isGeneralStrategy !== undefined) {
        this.isGeneralStrategy = isGeneralStrategy
      }
      if (editable !== undefined && !editable) {
        this.handleView(data)
      } else if (data && data.id) {
        this.handleUpdate(data)
      } else {
        this.handleCreate(data)
      }
      this.clearValidate()
      if (typeof callback === 'function') {
        // 新增修改弹框显示后，弹框里组件的调用
        this.$nextTick(() => {
          callback()
        })
      }
      setTimeout(() => {
        // 首次打开弹窗，在300毫秒后将dlgInit修改为true
        if (!this.dlgInit) {
          this.dlgInit = true
        }
        // 300毫秒后，若弹窗未打开，则手动打开
        if (!this.isHandleClose && !this.dialogVisible) {
          this.dialogVisible = true
        }
        // 如果传入窗体滚动事件的方法，则监听窗体滚动，并绑定方法
        if (typeof this.bodyScroll == 'function') {
          const el = this.$el.getElementsByClassName('el-dialog__body')[0]
          el.addEventListener('scroll', this.bodyScroll)
        }
      }, 300);
    },
    objectTree() {
      return this.$refs.objectTree
    },
    hide() {
      this.dialogVisible = false
      this.copyVisible = false
      // 提交成功后的 submitting 状态变更放在 closed 中
      // this.submitting = false
    },
    // 加载 策略基础设置
    loadStgBaseConfig() {
      const stgBaseConfigMap = this.stgBaseConfig
      if (!stgBaseConfigMap || Object.keys(stgBaseConfigMap).length === 0) {
        this.$store.dispatch('commonData/setStgBaseConfig')
      } else {
        const config = stgBaseConfigMap[this.stgCode]
        if (config) {
          this.allOsType = config.osType
          this.osAble = !!config.osAble
          if (config.osAble) {
            // 过滤终端不可配置的类型
            this.osTypeOptions = this.osTypeOptions.filter((item) => {
              return (config.osType & item.value) > 0
            })
            this.osTypeShowedTabs = this.osTypeOptions
            this.osTypes = this.osTypeShowedTabs.filter(opt => opt.checked).map(opt => opt.value)
          } else {
            this.osTypes = [this.allOsType]
          }
        }
      }
    },
    loadMstgBaseConfig() {
      const mstgBaseConfigMap = this.mstgBaseConfig
      if (!mstgBaseConfigMap || Object.keys(mstgBaseConfigMap).length === 0) {
        this.$store.dispatch('commonData/setMstgBaseConfig')
      } else {
        const config = mstgBaseConfigMap[this.stgCode]
        if (config) {
          this.allOsType = config.osType
          this.osAble = !!config.osAble
          if (config.osAble) {
            // 过滤终端不可配置的类型
            this.osTypeOptions = this.osTypeOptions.filter((item) => {
              return (config.osType & item.value) > 0
            })
            // 由于策略的生效对象支持多选，所以配置策略时不会根据选择的终端类型隐藏不能配置的策略，而是改为保存时验证
            this.osTypeShowedTabs = this.osTypeOptions
            this.osTypes = this.osTypeShowedTabs.filter(opt => opt.checked).map(opt => opt.value)
          } else {
            this.osTypes = [this.allOsType]
          }
        }
      }
    },
    osTypeCheck(val, type) {
      if (val) {
        this.osTypes.push(type)
      } else {
        this.osTypes.splice(this.osTypes.indexOf(type), 1)
      }
    },
    // 点击终端类型tab
    tabClick(tab) {
      this.activeSlotName = this.tabNameValue[tab.name]
    },
    clearValidate() {
      this.$nextTick(() => {
        this.$refs['headForm4StgDialog'] && this.$refs['headForm4StgDialog'].clearValidate()
        let dataForm = this.$refs['dataForm4StgDialog' + this.activeSlotName]
        if (Array.isArray(dataForm)) dataForm = dataForm[0]
        if (dataForm) {
          dataForm.clearValidate()
        }
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.activeSlotName = undefined
      this.formable = true
      this.submitting = false
      this.tempChildren.splice(0)
      this.stgIds.splice(0)
      this.osTypes.splice(0)
      // 定义可以配置数据的子数据数组
      const tempArray = [{ value: this.allOsType }]
      // 可以配置数据的类型添加到tempChildren
      if (this.osAble) {
        // 去除勾选状态
        this.osTypeOptions.forEach(os => { os.checked = false })
        tempArray.push(...this.osTypeOptions)
      } else {
        this.osTypes = [this.allOsType]
      }
      // 设置tempChildren默认值
      for (let i = 0; i < tempArray.length; i++) {
        const { value } = tempArray[i]
        const temp = Object.assign(JSON.parse(JSON.stringify(this.model)), { osType: value })
        this.tempChildren[value] = temp
      }
    },
    close() {
      this.isHandleClose = true
      this.$emit('close')
    },
    closed() {
      this.submitting = false
      this.$emit('closed')
    },
    // 设置为当前tab，并勾选
    setActiveTab(option) {
      if (!option) return
      option.checked = true
      this.activeSlotName = option.value
      this.activeTab = option.type
      this.osTypes.indexOf(option.value) == -1 && this.osTypes.push(option.value)
    },
    handleView(row) {
      this.handleUpdate(row)
      this.dialogStatus = 'view'
      this.formable = false
    },
    handleCreate(option = {}) {
      this.dialogVisible = true
      this.dialogStatus = 'create'
      this.resetTemp()
      Object.assign(this.temp, option)
      this.$nextTick(() => {
        if (this.osAble) {
          this.setActiveTab(this.osTypeShowedTabs[0])
        } else {
          this.activeSlotName = this.osTypes[0]
        }
      })
    },
    handleUpdate(row) {
      // 首次打开则立即弹窗，若非首次则在数据更新后再弹窗
      if (!this.dlgInit) {
        this.dialogVisible = true
      }
      this.dialogStatus = 'update'
      this.dataLoading = true
      this.resetTemp()
      row = JSON.parse(JSON.stringify(row))
      // 公共数据赋值
      for (const key in this.temp) {
        this.temp[key] = row[key]
      }
      this.$nextTick(() => {
        if (this.osAble) {
          // 类型修复，比如stg_base_config表中os_type最开始设置为15后面调整为7，历史配置的策略就需要修正为7
          row.osType = row.osType & this.allOsType
        } else {
          // 如果不显示不同系统的tab，则修改osType
          row.osType = this.allOsType
        }
        // 给当前tab的temp赋值
        Object.assign(this.tempChildren[row.osType], JSON.parse(JSON.stringify(row)))
        this.formatRowData(this.tempChildren[row.osType])
        if (!this.osAble) {
          this.setTempValue({ row })
        } else {
          this.getByName({ stgTypeNumber: this.stgCode, name: row.name }).then(respond => {
            this.setActiveTab(this.osTypeShowedTabs.filter(os => os.value == row.osType)[0])
            let beans = []
            if (respond.data) {
              beans = Array.isArray(respond.data) ? respond.data : [respond.data]
            }
            const targetRowData = {}
            for (let i = 0; i < beans.length; i++) {
              const bean = beans[i]
              // 类型修复，比如stg_base_config表中os_type最开始设置为15后面调整为7，历史配置的策略就需要修正为7
              bean.osType = bean.osType & this.allOsType
              // isEntityEquals判断的不准确，新版策略对象不存在entityType和entityId，因此增加containStgObject的判断
              if (this.isEntityEquals(row, bean) || containStgObject(bean, row.entityType, row.entityId)) {
                this.numToArr(bean.osType).forEach(subOsType => {
                  targetRowData[subOsType] = JSON.parse(JSON.stringify(bean))
                  targetRowData[subOsType].osType = subOsType
                })
              }
            }
            this.setTempValue(targetRowData, row.osType)
          }).catch(_ => {
            console.log('error', _);
          })
        }
      })
    },
    /**
     * 查看详情，处理是否预定义策略的临时方法
     * 后续统一修改：删除该方法，统一使用 show 方法，优化是否显示应用对象的配置方式
     */
    handleShow(row, formable, isGeneralStrategy) {
      this.show(row, formable, null, isGeneralStrategy)
    },
    // 是否相同生效对象的数据，entityType和entityId相等或同为预定义策略
    isEntityEquals(bean1, bean2) {
      if (!bean1 || !bean2) {
        return false
      }
      const isTypeEquals = bean1.entityType === bean2.entityType
      if (isTypeEquals) {
        return bean1.entityId === bean2.entityId
      }
      // 是否都是预定义策略，如果是，则也返回true
      return bean1.strategyDefType === 1 && bean2.strategyDefType === 1
    },
    // 给tempChildren中每个temp对象赋值
    setTempValue(rowDatas, osType) {
      const datas = {}
      // 使用osType作为key，保存temp数据
      for (const index in rowDatas) {
        const row = rowDatas[index]
        datas[row.osType] = row
        if (this.osTypes.indexOf(row.osType) < 0) {
          this.osTypes.push(row.osType)
        }
      }
      // 遍历tempChildren赋值
      for (let i = 0; i < this.tempChildren.length; i++) {
        const tempC = this.tempChildren[i]
        const rowData = datas[i]
        if (tempC && rowData) {
          if (rowData.osType === i) {
            Object.assign(tempC, JSON.parse(JSON.stringify(rowData)))
            this.formatRowData(tempC)
            this.stgIds.push(rowData.id)
            osType = osType || i
          }
        }
      }
      this.$nextTick(() => {
        // 数据更新完若未弹窗，则弹窗
        if (!this.dialogVisible) {
          this.dialogVisible = true
        }
        this.osTypeOptions.forEach(os => {
          os.checked = this.osTypes.indexOf(os.value) > -1
        })
        this.activeSlotName = osType
        this.dataLoading = false
        this.clearValidate()
      })
    },
    /**
     * 提交表单数据
     * submitFunc 接口
     * data 数据
     */
    submitFormData(submitFunc, data) {
      this.validFormData(() => {
        this.submitting = true
        const formData = []
        const formOS = []
        // 校验是否是回收站分组，回收站分组不允许配置策略
        if ((data.entityId && data.entityId == -2) || (data.objectGroupIds && (data.objectGroupIds.includes(-2) || data.objectGroupIds.includes('-2')))) {
          this.$message({
            message: this.$t('components.recycleNodeTips'),
            type: 'error',
            duration: 2000
          })
          this.submitting = false
          return
        }
        // 遍历勾选的终端类型，处理数据
        for (let i = 0; i < this.osTypes.length; i++) {
          const os = this.osTypes[i]
          if (formOS.indexOf(os) < 0) {
            formOS.push(os)
          } else {
            continue
          }
          let tempOS = Object.assign({}, this.tempChildren[os], data)
          tempOS = JSON.parse(JSON.stringify(tempOS))
          tempOS['oldIds'] = this.stgIds
          this.formatFormData(tempOS)
          // 提交数据前，删除 selectedOsType
          delete tempOS.selectedOsType
          formData.push(tempOS)
        }

        // if (this.osAble && formData.length == 0) {
        //   // 当前勾选的策略类型，与应用终端类型不匹配，无法保存！
        //   this.$message({
        //     message: this.$t('pages.stgDiglogOsAbleMsg1'),
        //     type: 'error'
        //   })
        //   this.submitting = false
        //   return
        // }

        submitFunc(formData).then(respond => {
          this.copyVisible && this.isGeneralStrategy && this.entityLink(this.copyTemp, null, this.currentVm)
          this.$emit('submitEnd', this.dialogStatus, formData)
          this.hide()
        }).catch(e => {
          this.$emit('submitFailEnd')
          this.submitting = false
        })
      })
    },
    validFormData(submitFunc) {
      this.submitting = true
      // 先验证头部公共部分的表单
      this.$refs['headForm4StgDialog'].validate((validHF) => {
        if (validHF) {
          // 当前显示的form表单需要先验证，然后才能验证其他表单
          const validSlotNames = []
          if (this.osTypes.indexOf(this.activeSlotName) >= 0) {
            validSlotNames.push(this.activeSlotName)
          }
          for (let i = 0; i < this.osTypes.length; i++) {
            const activeSlot = this.osTypes[i]
            if (activeSlot && activeSlot !== this.activeSlotName) {
              validSlotNames.push(this.osTypes[i])
            }
          }
          // 当不允许设置为全部终端时，终端类型至少选一个
          if (this.osAble && this.osTypes.length == 0) {
            this.$message({
              message: this.$t('components.checkOneTerm'),
              type: 'error'
            })
            this.submitting = false
          } else {
            this.validDataForm(validSlotNames, 0, submitFunc)
          }
        } else {
          this.$emit('submitFailEnd')
          this.submitting = false
        }
      })
    },
    validDataForm(validSlotNames, index, callback) { // 迭代验证数据表单
      this.submitting = true
      if (index < validSlotNames.length) {
        const activeSlot = validSlotNames[index]
        const tabName = this.tabName[activeSlot]
        this.activeTab = `${tabName}`
        this.activeSlotName = activeSlot
        this.$nextTick(() => {
          let form = this.$refs['dataForm4StgDialog' + activeSlot]
          if (Array.isArray(form)) form = form[0]
          // 校验违规响应策略勾选但未选择的情况
          const responseContent = form.$slots.default.find(item => item.data && item.data.ref === 'responseContent')
          if (responseContent && responseContent.child.checkRule && !responseContent.child.ruleId) {
            this.submitting = false
            return;
          }
          form.validate((validDF) => {
            if (validDF) {
              if (this.validateForm) {
                // 外部验证方法存在，则验证外部方法
                const tempOS = JSON.parse(JSON.stringify(Object.assign({}, this.tempChildren[activeSlot], this.temp)))
                if (this.validateForm(tempOS)) {
                  this.validDataForm(validSlotNames, ++index, callback)
                } else { this.submitting = false }
              } else {
                this.validDataForm(validSlotNames, ++index, callback)
              }
            } else {
              this.$emit('submitFailEnd')
              this.submitting = false
            }
          })
        })
      } else {
        this.submitting = false
        const activeSlot = validSlotNames[0]
        const tabName = this.tabName[activeSlot]
        this.activeTab = `${tabName}`
        this.activeSlotName = activeSlot
        if (callback) callback()
      }
    },
    saveData() {
      // 常规策略，未选择应用对象
      if (this.isGeneralStrategy && !this.$refs['stgTargetItem'].valid()) {
        return false
      }
      if (this.dialogStatus === 'create') {
        this.createData(this.temp)
      } else {
        this.updateData(this.temp)
      }
    },
    saveCopyData() {
      if (this.isGeneralStrategy && !this.$refs['copyStgTargetItem'].valid()) {
        return false
      }
      if (this.copyTemp.name == '' || this.copyTemp.name == null) {
        this.$message({
          message: this.$t('components.stgNameNotNull'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.getByName({ stgTypeNumber: this.stgCode, name: this.copyTemp.name }).then(respond => {
        const res = respond.data
        const isRepeat = res ? (Array.isArray(res) ? res.length : true) : false
        if (isRepeat) {
          this.$message({
            message: this.$t('components.someStgName'),
            type: 'error',
            duration: 2000
          })
        } else {
          this.createData(this.copyTemp)
        }
      })
    },
    cancelCopy() {
      this.copyVisible = false
    },
    handleCopy() {
      const { entityType, objectType, name, timeId, remark, isPermanent, beginTime, endTime } = this.temp
      this.copyTemp = Object.assign({}, this.defaultTemp, { entityType, objectType, name, timeId, remark, isPermanent, beginTime, endTime })
      this.copyVisible = true
    },
    createData(data) {
      this.submitFormData(this.create, data)
    },
    updateData(data) {
      this.submitFormData(this.update, data)
    },
    // 比较的两个数据都是终端，且终端类型不同，则返回true
    isTermAndDiffType(data1, data2) {
      if (data1 && data1.type == 1 && data2 && data2.type == 1) {
        return (15 & data1.dataType) != (15 & data2.dataType)
      } else {
        return false
      }
    },
    nameValidator(rule, value, callback) {
      this.getByName({ stgTypeNumber: this.stgCode, name: value }).then(respond => {
        let beans = []
        if (respond.data) {
          beans = Array.isArray(respond.data) ? respond.data : [respond.data]
        }
        if (beans.length === 0) {
          callback()
          return
        }
        for (let i = 0; i < beans.length; i++) {
          const bean = beans[i]
          if (this.stgIds.indexOf(bean.id) > -1) {
            callback()
            return
          }
        }
        callback(new Error(this.$t('valid.sameName')))
      })
    },
    /**
     * 生效对象变更的回调
     * nodeData 已勾选的节点数据
     * tempObj 表单数据对象
     */
    entityIdChange(nodeData, tempObj) {
      const osTypes = {}
      let isAllOsType = false
      // 遍历选中的节点，记录可配置的终端类型
      for (let i = 0; i < nodeData.length; i++) {
        const data = nodeData[i];
        if (data.type != '1') {
          isAllOsType = true
          break
        } else {
          const type = this.osTypeConver(data.dataType)
          osTypes[type] = 1
        }
      }
      // 已选对象可配置的终端类型，用于判断与勾选的策略是否相匹配（目前暂未使用
      tempObj.selectedOsType = isAllOsType ? this.allOsType : Object.keys(osTypes).reduce((res, val) => res + Number(val), 0)
    },
    // 终端类型的值转换，Windows：0 -> 1; Linux：1 -> 2; Mac：2 -> 4; 移动终端：3 -> 8;
    osTypeConver(value) {
      if (value >= 0x80) {
        // 离线终端，dataType >= 128
        return Math.pow(2, value - 0x80)
      } else if (value >= 0x20) {
        // U盘终端，dataType >= 32
        return Math.pow(2, value - 0x20)
      } else if (value >= 0x10) {
        // 老板终端，dataType >= 16
        return Math.pow(2, value - 0x10)
      } else {
        // 普通终端
        return Math.pow(2, value)
      }
    },
    //  获取当前slot中的dom元素
    getSlotFormRef() {
      const index = this.osTypeShowedTabs.findIndex(item => item.type === this.activeTab)
      if (index > -1) {
        let form = this.$refs['dataForm4StgDialog' + this.osTypeShowedTabs[index].value]
        if (Array.isArray(form)) form = form[0]
        return form
      }
      return null
    },
    handleOpen() {
      this.$emit('open')
    }
  }
}
</script>

<style lang="scss" scoped>
  .os-tabs {
    border: none !important;
  }
  >>>.os-tabs>.el-tabs__header {
    margin-right: 0;
    margin-bottom: 0;
    border-bottom: none;
    & .el-tabs__nav {
      border: 1px solid #aaa;
      border-radius: 0;
      &.is-top{
        border-bottom: none;
      }
    }
    .el-tabs__item {
      padding: 0 12px;
      &.is-left{
        border-top: 1px solid #aaa;
      }
      &.is-left.is-active{
        border-color: #aaaaaa;
        border-right-color: #409EFF;
      }
    }
    .el-tabs__item:first-child {
      border-top: none;
    }
  }
  >>>.os-tabs>.el-tabs__content{
    height: 100%;
    padding: 0;
    border: 1px solid #aaa;
  }
  >>>.el-tab-pane{
    padding: 10px;
    overflow: auto;
  }
</style>
