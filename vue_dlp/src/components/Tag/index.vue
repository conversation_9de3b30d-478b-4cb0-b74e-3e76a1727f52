<template>
  <div :style="containerStyle">
    <el-tag
      v-for="(tag, index) in list"
      :key="tag"
      :closable="!disabled"
      :title="tag"
      :disable-transitions="false"
      :style="tagStyle"
      @close="handleClose(tag)"
    >
      <i v-if="!disabled && editable" class="el-icon-edit" @click="tagEdit(tag, index)" />
      {{ tag }}
    </el-tag>
    <Form v-show="inputVisible" ref="dataForm" class="tag-input" :model="temp" :rules="allRule">
      <FormItem prop="inputValue">
        <el-input
          ref="saveTagInput"
          v-model="temp.inputValue"
          class="input-new-tag"
          size="small"
          :style="inputStyle"
          :placeholder="placeholder"
          :maxlength="inputLength"
          :show-word-limit="showWordLimit"
          :append-suffix="appendSuffix"
          @keyup.enter.native="handleInputConfirm"
          @blur="handleInputConfirm"
          @input="handleInput"
          @keydown.native="backspaceFunc"
        >
        </el-input>
      </FormItem>
    </Form>
    <el-button v-show="!disabled&&!inputVisible&&addAble&&(limitSize <= 0 || list.length < limitSize)" class="button-new-tag" @click="showInput">+</el-button>
  </div>
</template>

<script>
export default {
  name: 'Tag',
  props: {
    addClick: {
      type: Function,
      default() {}
    },
    validRule: {
      type: Array,
      default() {
        return null
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default() {
        return []
      }
    },
    accept: {
      type: String,
      default: ''
    },
    border: {
      type: Boolean,
      default: false
    },
    inputLimit: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: undefined
    },
    //  是否添加滚动条
    overflowAble: {
      type: Boolean,
      default: false
    },
    //  设置高度，如果
    overflowHeight: {
      type: String,
      default: '150px'
    },
    //  设置最小高度
    minHeight: {
      type: String,
      default: null
    },
    //  设置最大高度
    maxHeight: {
      type: String,
      default: null
    },
    //  输入框的宽度
    inputWidth: {
      type: Number,
      default: null
    },
    //  是否可手动输入
    addAble: {
      type: Boolean,
      default: true
    },
    //  存在不符合规则的值时，tag组件和input的值对齐
    errRuleAligning: {
      type: Boolean,
      default: false
    },
    //  tag支持修改
    editable: {
      type: Boolean,
      default: false
    },
    // 输入框的字数
    inputLength: {
      type: String,
      default: ''
    },
    //  输入框的字数是否显示
    showWordLimit: {
      type: Boolean,
      default: false
    },
    //  限制输入数量，limitSize <= 0,未开启
    limitSize: {
      type: Number,
      default: 0
    },
    inputFunc: {
      type: Function,
      default() {}
    },
    // 在输入完成后补全指定符号
    appendSuffix: {
      type: String,
      default: undefined
    },
    // 是否去除前后空格
    trimAble: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputVisible: false,
      temp: {
        inputValue: ''
      },
      rules: {
        inputValue: [
          { validator: this.inputValidator, trigger: 'blur' }
        ]
      },
      inputStyle: {},    //  输入框样式
      tagStyle: {},    //  tag标签样式值
      timeoutId: undefined, //  延迟执行
      lastKeyWasBackspace: false // 最后一次是否是退格键
    }
  },
  computed: {
    allRule() {
      const allRule = Object.assign({}, this.rules)
      if (this.validRule != null) {
        allRule.inputValue.push(...this.validRule)
      }
      return allRule
    },
    containerStyle() {
      const body = this.border ? {
        'border': '1px solid #aaa',
        'border-radius': '4px',
        'padding': '1px'
      } : {};
      //  设置滚动条
      if (this.overflowAble) {
        body['overflow-y'] = 'auto'
        body['height'] = this.overflowHeight
      }
      if (this.minHeight !== null) {
        body['min-height'] = this.minHeight
      }
      if (this.maxHeight !== null) {
        body['max-height'] = this.maxHeight
        body['height'] = null
      }
      return body;
    }
  },
  watch: {
    'temp.inputValue'(newValue, oldValue) {
      if (this.inputLimit == 'number') {
        this.temp.inputValue = this.temp.inputValue.replace(/[^\d\:]/g, '')
      }
    }
  },
  created() {
    //  添加输入框的长度
    if (this.inputWidth !== null) {
      Object.assign(this.inputStyle, { 'width': this.inputWidth + 'px' })
    }
  },
  methods: {
    inputValidator(rule, value, callback) {
      let flag = !this.accept || !this.temp.inputValue
      if (this.accept && this.temp.inputValue) {
        const fixArr = this.accept.split(',')
        for (const suffix of fixArr) {
          if (suffix && this.temp.inputValue.toLowerCase().lastIndexOf(suffix.toLowerCase()) != -1) {
            flag = true
          }
        }
      }
      if (flag) {
        callback()
      } else {
        callback(new Error(this.$t('components.suffixesOnly') + this.accept))
      }
    },
    handleClose(tag) {
      this.list.splice(this.list.indexOf(tag), 1)
      this.$emit('tagChange', this.list)
    },
    showInput() {
      this.addClick()
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let inputValue = this.temp.inputValue
          // 是否去除前后空格
          if (this.trimAble) {
            inputValue = inputValue.trim()
          }
          if (inputValue && this.list.indexOf(inputValue) == -1) {
            this.list.push(inputValue)
          }
          this.inputVisible = false
          this.temp.inputValue = ''
          this.tagStyle = {}
        } else {
          if (this.errRuleAligning) {
            this.tagStyle = { 'margin-bottom': '20px' }
          }
        }
        this.$emit('tagChange', this.list)
      })
    },
    //  情况输入框的值
    clearInputValue() {
      this.$nextTick(() => {
        this.$refs.dataForm && this.$refs.dataForm.clearValidate()
        this.tagStyle = {}
        this.inputVisible = false
        this.temp.inputValue = ''
      })
    },
    //  校验form表单
    async validateForm() {
      let flag = false
      await this.$refs['dataForm'].validate((valid) => {
        flag = valid;
      })
      return flag;
    },
    tagEdit(val, index) {
      this.list.splice(index, 1)
      this.temp.inputValue = val || ''
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
      this.inputVisible = true
    },
    handleInput(val) {
      if (this.appendSuffix) {
        this.appendSuffixFunc()
      }
    },
    appendSuffixFunc() {
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
        this.timeoutId = undefined
      }
      this.timeoutId = setTimeout(() => {
        if (this.temp.inputValue) {
          const lastChar = this.temp.inputValue.charAt(this.temp.inputValue.length - 1);
          // 检查最后一个字符是否是符号
          const isSymbol = /[^\w\s\u4e00-\u9fa5]/.test(lastChar);
          if (!isSymbol && !this.temp.inputValue.endsWith(this.appendSuffix) && !this.lastKeyWasBackspace) {
            this.temp.inputValue += this.appendSuffix;
          }
        }
      }, 1000)
    },
    backspaceFunc(event) {
      this.lastKeyWasBackspace = event.key === 'Backspace';
    }
  }
}
</script>

<style lang="scss" scoped>
  .tag-input{
    display: inline-block;
    vertical-align: bottom;
    margin: 3px 5px;
    >>>.el-form-item{
      margin: 0;
    }
  }
  .el-tag {
    height: 30px;
    line-height: 28px;
    max-width: 200px;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
    margin: 3px 5px;
    position: relative;
    padding-right: 20px;
    >>>.el-icon-close {
      top: 6px;
      right: 2px;
      position: absolute;
    }
  }
  .button-new-tag {
    width: 28px;
    height: 30px;
    margin: 3px 5px;
    padding: 0;
  }
  .input-new-tag {
    width: 100px;
    margin-bottom: 0 !important;
    vertical-align: bottom;
  }
  >>>.el-form-item__error {
    width: 300px;
  }
</style>
