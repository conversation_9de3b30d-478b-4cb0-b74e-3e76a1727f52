<!--
  /**
  * 切换系统语言的组件
  */
-->
<template>
  <el-dropdown v-if="i18nMode" trigger="click" class="international" @command="handleSetLanguage">
    <div>
      <svg-icon class-name="international-icon" icon-class="language" />
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="option in languageOption" :key="option.lang" :disabled="language === option.lang" :command="option.lang">
        {{ option.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'LangSelect',
  computed: {
    ...mapGetters([
      'languageOption',
      'language',
      'isI18nMode',
      'devI18nMode'
    ]),
    i18nMode() {
      // 开发环境下, 通过setting中的配置devI18nMode，可以开启/关闭功能 便于测试
      return this.isI18nMode || (this.isDev() && this.devI18nMode)
    }
  },
  methods: {
    handleSetLanguage(lang) {
      this.$confirmBox(this.$t('components.switchLanguagePrompt'), this.$t('text.prompt')).then(() => {
        this.$store.dispatch('app/setLanguage', lang)
        location.reload()
      }).catch((e) => {
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .international-icon{
    font-size: 16px;
  }
</style>
