<!--
  /**
  * 封装el-form，拓展功能。
  * 实现根据系统语言调整label-width的功能。
  */
-->
<template>
  <el-form ref="form" v-bind="$attrs" :label-width="newLabelWidth" v-on="$listeners" @submit.native.prevent>
    <template
      v-for="(item, key) in $slots"
      v-slot:[key]="slotScope"
    >
      <slot :name="key" v-bind="slotScope"></slot>
    </template>
  </el-form>
</template>

<script>

export default {
  name: 'Form',
  props: {
    labelWidth: {
      type: String,
      default: ''
    },
    // 根据系统语言配置label额外增加的宽度
    extraWidth: {
      type: Object,
      default() {
        return { en: 40 }
      }
    }
  },
  data() {
    return {
      encryptProps: [],  // 记录需要加密的字段 prop
      noSubmitProps: []  // 记录不需要提交的字段 prop
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    },
    newLabelWidth() {
      const labelWidth = parseInt(this.labelWidth.replace('px', ''))
      const extraWidth = this.extraWidth[this.lang] || 0
      return `${labelWidth + extraWidth}px`
    }
  },
  created() {
    this.$nextTick(() => {
      this.handleProp()
    })
  },
  methods: {
    // 对需要处理的字段进行处理
    handleProp() {
      const formItems = this.$refs.form.$el.querySelectorAll('.el-form-item')
      formItems.forEach(item => {
        const isEncrypt = item.hasAttribute('encrypt')
        const noSubmit = item.hasAttribute('no-submit')
        const prop = item.getAttribute('prop')
        // 需要加密的字段
        if (isEncrypt) {
          !this.encryptProps.includes(prop) && this.encryptProps.push(prop)
        }
        // 不需要提交的字段
        if (noSubmit) {
          !this.noSubmitProps.includes(prop) && this.noSubmitProps.push(prop)
        }
      });
    },
    updateModel() {
      // 校验表单数据前，将需要加密的 prop 添加到 model 数据中
      if (this.encryptProps.length > 0) {
        // 已添加到 model 中的 prop
        const modelProps = this.$attrs.model['encryptProps']
        if (Array.isArray(modelProps)) {
          // 合并去重
          this.encryptProps = Array.from(new Set(this.encryptProps.concat(modelProps)))
        }
        // 更新 model 中的 prop
        this.$set(this.$attrs.model, 'encryptProps', this.encryptProps)
      }
      // 校验表单数据前，将不需要提交的 prop 添加到 model 数据中
      if (this.noSubmitProps.length > 0) {
        // 已添加到 model 中的 prop
        const modelProps = this.$attrs.model['noSubmitProps']
        if (Array.isArray(modelProps)) {
          // 合并去重
          this.noSubmitProps = Array.from(new Set(this.noSubmitProps.concat(modelProps)))
        }
        // 更新 model 中的 prop
        this.$set(this.$attrs.model, 'noSubmitProps', this.noSubmitProps)
      }
    },
    validate(...args) {
      this.handleProp()
      this.updateModel()
    
      // 如果表单中有 Tag 组件，需要校验是否有报错信息，如果有，则不执行当前表单的校验方法
      const tagForms = this.$el.querySelectorAll('.tag-input')
      let hasErrorEl = false
      for (let i = 0; i < tagForms.length; i++) {
        const tag = tagForms[i];
        const error = tag.querySelectorAll('.el-form-item__error')
        if (error.length > 0) {
          hasErrorEl = true
          break
        }
      }
      if (hasErrorEl) {
        // 如果存在错误，则将 false 作为结果，传入回调方法，部分页面需要进行额外操作（比如定位到报错的tab页面），则需要在验证方法调用前进行处理
        console.error('Tag 组件存在错误信息，请修正后再保存！');
        return this.$refs.form.validate(args[0](false))
      }
      return this.$refs.form.validate(...args)
    },
    validateField(...args) {
      return this.$refs.form.validateField(...args)
    },
    resetFields() {
      return this.$refs.form.resetFields()
    },
    clearValidate(...args) {
      return this.$refs.form.clearValidate(...args)
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.required .el-form-item__label:before{
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
  }
</style>
