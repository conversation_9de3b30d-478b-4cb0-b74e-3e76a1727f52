
<!--
  /**
  * 策略页面左侧 tab-tree 组件
  * 调用示例：
    <strategy-target-tree
      ref="strategyTargetTree"
      :showed-tree="['terminal','user']"    // 设置显示哪些树，不设置默认显示全部
      :panelWidth="200"                     // 默认宽度
      :resizeable="true"                    // 是否可以调整大小
      :selectSearchFunc                     // 选中搜索结果后的回调函数
      :filterKey                            // 传入需要过滤的节点的nodeKey值，详细见 props.filterKey
      @data-change="dataChange"             // 选中数据发生变化的回调方法，用于在父组件对数据进行处理
    />
  */
-->
<template>
  <el-tabs
    v-model="checkedTab"
    tab-position="top"
    stretch
    :class="{'strategy-tree': true, 'no-header-tab': !showHead}"
    :style="{width: width + 'px'}"
    @tab-click="handleTabClick"
  >
    <el-tab-pane
      v-for="pane in showedTabPanes"
      :key="pane.name"
      :label="pane.label"
      :name="pane.name"
    >
      <tree-menu
        :ref="pane.name"
        :node-key="pane.nodeKey"
        :local-search="pane.localSearch || false"
        :get-search-list="pane.getSearchList"
        :select-search-func="pane.selectSearchFunc"
        :filter-node-method="filterNodeMethod"
        :accordion="accordion"
        :expand-on-click-node="expandOnClickNode"
        :default-expanded-keys="pane.expandedKeys"
        :data="pane.treeData"
        :icon-option="pane.iconOption"
        :resizeable="false"
        :filter-key="pane.filterKey"
        :multiple="multiple"
        :check-on-click-node="checkOnClickNode"
        :disabled-all-nodes="disabledAllNodes"
        :check-strictly="checkStrictly"
        :render-content="renderContent"
        @node-click="nodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
        @check-change="checkChange"
      />
      <div v-if="resizeable" class="widthResize" @mousedown="mousedown"></div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import { mapGetters } from 'vuex'
// import { enableStgBtn } from '@/utils'
import { getTermTypeDict } from '@/utils/dictionary'
import { getRefreshTermNodeMap } from '@/api/system/terminalManage/terminal'
import { getConfigByKey } from '@/api/system/configManage/globalConfig'

export default {
  name: 'StrategyTargetTree',
  props: {
    // 是否公共组件, 公共组件不过滤面板及节点，而是修改成禁用
    isCommon: {
      type: Boolean,
      default: false
    },
    // 要显示的面板的name
    showedTree: {
      type: Array,
      default() {
        return ['terminal', 'user']
      }
    },
    // 手动控制显示的终端类型，1-win,2-linux,4-mac,8-移动设备（数值的和）
    osTypeFilter: {
      type: Number,
      default: null
    },
    // 默认宽度
    panelWidth: {
      type: Number,
      default: 200
    },
    // 是否可以调整大小
    resizeable: {
      type: Boolean,
      default: true
    },
    // 外部传入值，更新 resizeLength
    resizeWidth: {
      type: Number,
      default: 0
    },
    //  是否可多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点。
    checkOnClickNode: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 是否禁用所有节点的勾选框
    disabledAllNodes: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 渲染节点的方法
    renderContent: {
      type: Function,
      default: null
    },
    // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏
    filterNodeMethod: {
      type: Function,
      default: null
    },
    // 是否在点击节点的时候展开或者收缩节点
    expandOnClickNode: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 是否审计日志的终端树（审计日志使用的数据没有回收站节点），默认为 false
    isLogTerminalTree: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 传入需要过滤的节点的nodeKey值，默认是id
    // 当传入值为 Object时，eg: { prop, value, showIfEqual, showIfNoProp }
    // prop为过滤的属性，value为过滤的值，showIfEqual为true时显示值相同的节点，showIfNoProp为ture时显示不包含该属性的节点
    filterKey: {
      type: [Number, String, Array, Object, Function],
      default() {
        return ''
      }
    },
    // 内置数据终端树的 filterKey，没有传值则使用 filterTermNode
    terminalFilterKey: {
      type: [Number, String, Array, Object, Function],
      default() {
        return ''
      }
    },
    // 内置数据操作员树的 filterKey
    userFilterKey: {
      type: [Number, String, Array, Object, Function],
      default() {
        return ''
      }
    },
    // 公共树组件用于配置终端树过滤节点的方法（公共树组件不会隐藏节点），被过滤的节点返回 false，点击节点显示提示信息
    commonTerminalFilter: {
      type: Function,
      default: null
    },
    // 手风琴模式，默认为false
    accordion: {
      type: Boolean,
      default: false
    },
    // 外部传入的 pane
    otherTabPanes: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否刷新永久离线终端占用状态
    refreshOfflineTerminalStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkedTab: '',                     // 当前显示的 tab 的值
      showHead: false,                    // 是否显示 tab 标签栏
      osType: 15,                         // 支持的终端操作系统类型，1-win,2-linux,4-mac,8-移动设备
      usedScope: 3,                       // 策略支持的对象，1：终端，2：操作员，3：都支持
      builtInPanes: [                     // 内置数据（终端、操作员）
        {
          name: 'terminal',                                     // 数据类型名称
          label: this.$t('components.terminal'),                // tab标签label
          type: '3',                                            // 终端组的类型编号，与服务端一一对应
          nodeKey: 'id',                                        // 树的nodeKey
          treeData: [],                                         // 树数据data
          init: false,                                          // 首次访问tab标签时初始化树，避免同时初始化多个树
          disabled: false,                                      // 是否禁用
          localSearch: false,                                   // 是否本地搜索
          filterKey: this.terminalFilterKey || this.filterTermNode || '', // 过滤节点的key
          expandedKeys: [],                                     // 默认展开的节点
          getSearchList: () => {                                // 获取搜索列表
            return (this.isLogTerminalTree ? this.logTermTreeList : this.termTreeList) || []
          },
          selectSearchFunc: null,                               // 默认值为null，使用TreeMenu的内置方法
          getTreeData: () => {                                  // 通过该方法获取树数据
            return this.isLogTerminalTree ? this.logTermTree : this.termTree
          },
          iconOption: {                                         // 节点icon的class
            typeKey: 'dataType',
            'G': 'terminalGroup',
            3: 'terminalGroup',
            127: 'error'
          }
        },
        {
          name: 'user',                                         // 数据类型名称
          label: this.$t('components.user'),                    // tab标签label
          type: '4',                                            // 操作员组的类型编号，与服务端一一对应
          nodeKey: 'id',                                        // 树的nodeKey
          treeData: [],                                         // 树数据data
          init: false,                                          // 首次访问tab标签时初始化树，避免同时初始化多个树
          disabled: false,                                      // 是否禁用
          localSearch: false,                                   // 是否本地搜索
          filterKey: this.userFilterKey || '',                  // 过滤节点的key
          expandedKeys: [],                                     // 默认展开的节点
          getSearchList: () => { return this.userTreeList || [] },        // 获取搜索列表
          selectSearchFunc: null,                               // 默认值为null，使用TreeMenu的内置方法
          getTreeData: () => {                                  // 通过该方法获取树数据
            return this.userTree
          },
          iconOption: {                                         // 节点icon的class
            typeKey: 'dataType',
            'G': 'userGroup',
            4: 'userGroup',
            2: 'user'
          }
        }
      ],
      resizeLength: this.resizeWidth,      // panel调整的宽度
      termTreeNodeDisplayMode: 1
    }
  },
  computed: {
    ...mapGetters([
      'termTree',
      'termTreeList',
      'logTermTree',
      'logTermTreeList',
      'userTree',
      'userTreeList',

      'termNodes',
      'termStatusMap',
      'userStatusMap',
      'stgBaseConfig'
    ]),
    showedTabPanes() {
      const panes = [...this.builtInPanes, ...this.otherTabPanes]
      if (this.isCommon) {
        return panes
      }
      // 过滤要显示的tabs
      return panes.filter(item => {
        return this.showedTree.includes(item.name)
      })
    },
    width() {
      const rl = this.resizeLength >= 300 ? 300 : this.resizeLength <= 0 ? 0 : this.resizeLength
      return this.panelWidth + rl
    }
  },
  watch: {
    resizeWidth(val) {
      this.resizeLength = val
    },
    $route(val) {
      if (this.isCommon) {
        // 公共组件，每次路由变化时，需要重新加载配置
        this.loadStgBaseConfig()
      }
    },
    userTree(newVal, oldVal) {
      if (this.showedTree.includes('user')) {
        this.loadTreeData(this.builtInPanes[1])
      }
    },
    termTree(newVal, oldVal) {
      if (this.showedTree.includes('terminal') && !this.isLogTerminalTree) {
        this.loadTreeData(this.builtInPanes[0])
      }
    },
    logTermTree(newVal, oldVal) {
      if (this.showedTree.includes('terminal') && this.isLogTerminalTree) {
        this.loadTreeData(this.builtInPanes[0])
      }
    },
    // todo 去掉termNodes 相关代码
    termNodes: {
      immediate: true,
      handler(newVal, oldVal) {
        if (this.refreshOfflineTerminalStatus) {
          getRefreshTermNodeMap().then(res => this.filterTermNodesMap(res.data))
        } else {
          this.filterTermNodesMap(newVal)
        }
      }
    },
    termStatusMap() {
      this.changeTermTreeStatus(this.getTreeDataByType('3'), this.termStatusMap)
    },
    userStatusMap() {
      this.changeUserTreeStatus(this.getTreeDataByType('4'), this.userStatusMap)
    }
  },
  async created() {
    this.osType = this.osTypeFilter || this.osType
    // 请求 策略的基础配置
    if (!this.stgBaseConfig) {
      await this.$store.dispatch('commonData/setStgBaseConfig')
    }
    // 加载 策略的基础配置
    if (this.showedTree.length > 1) {
      this.loadStgBaseConfig()
    }
    // 设置默认展示tab
    if (this.isCommon) {
      const enableName = { 1: 'terminal', 2: 'user' }[this.usedScope]
      this.checkedTab = enableName || this.showedTree[0]
    } else {
      this.checkedTab = this.showedTree[0]
    }
    // 加载终端图标
    if (this.showedTree.indexOf('terminal') > -1) {
      this.loadTermIcons()
    }
    // 加载树数据
    this.loadTreeData()
    // 加载终端树节点显示方式
    this.getTermTreeNodeDisplayMode()
  },
  methods: {
    // 获取当前 panel 的树组件
    getCurrentTree() {
      return this.$refs[this.checkedTab][0]
    },
    // 获取当前 panel
    getCurrentPanel() {
      for (let i = 0; i < this.showedTabPanes.length; i++) {
        const panel = this.showedTabPanes[i]
        if (panel.name === this.checkedTab) {
          return panel
        }
      }
      return null
    },
    // 通过数据类型 type 获取 treeData
    getTreeDataByType(type) {
      const pane = this.showedTabPanes.filter(pane => pane.type == type)[0]
      if (pane) {
        return pane.treeData
      }
      return []
    },
    // 从字典中获取终端图标
    loadTermIcons() {
      const termIcons = this.builtInPanes[0].iconOption
      getTermTypeDict().forEach(item => {
        this.$set(termIcons, item.value, item.icon)
      })
    },
    // 加载策略的基本配置，根据配置修改策略生效范围和终端类型
    loadStgBaseConfig() {
      const stgBaseConfigMap = this.stgBaseConfig
      const menuCode = this.$route.meta.code
      const stgCode = this.$route.meta.stgCode
      const stgBaseConfig = Object.values(stgBaseConfigMap)
      let loadBaseConfig = false
      let targetStgBaseConfig;
      for (let i = 0; i < stgBaseConfig.length; i++) {
        const config = stgBaseConfig[i]
        if (stgCode) {
          if (config['number'] === stgCode) {
            // 如果菜单路由有配置meta.stgCode，则以此策略编码作为依据
            targetStgBaseConfig = config
            break
          }
        } else if (config['routerPath'] === menuCode) {
          // 如果菜单路由没有配置meta.stgCode，则以此菜单编码作为依据
          targetStgBaseConfig = config
          break
        }
      }
      if (targetStgBaseConfig) {
        loadBaseConfig = true
        // 策略支持的终端类型
        this.osType = targetStgBaseConfig['osType']
        // 策略支持的对象，1：终端，2：操作员，3：都支持
        this.usedScope = targetStgBaseConfig['usedScope']
      }
      // 读取不到配置时，根据前端设置的值显示
      if (!loadBaseConfig) {
        this.osType = this.osTypeFilter || this.$route.meta.osType || this.osType
        this.usedScope = this.$route.meta.usedScope
      }
      // 可以应用的类型，若没有值则都可以应用
      const enableName = { 1: 'terminal', 2: 'user' }[this.usedScope]
      // usedScope有值时，策略限制只能应用终端或操作员
      if (!this.isCommon) {
        // 非公共组件，只保留可以应用的类型
        if (enableName) {
          this.showedTree.splice(0, this.showedTree.length, enableName)
        }
        // 有多个tab页时，显示tab标签栏
        this.showHead = this.showedTree.length > 1
      } else {
        // 公共组件，只能应用一种类型，则隐藏 head
        if (enableName) {
          this.checkedTab = enableName
          this.showHead = false
        } else {
          this.showHead = this.showedTree.length > 1
        }
      }
    },
    // 加载树数据
    loadTreeData(pane) {
      const panes = pane ? [pane] : this.showedTabPanes
      panes.forEach(pane => {
        const treeData = pane.getTreeData() || []
        if (treeData.length > 0) {
          // 数据只有一个根节点，且没有默认展开的节点
          if (treeData.length == 1 && pane.expandedKeys.length == 0) {
            // 将根节点设置为默认展开
            pane.expandedKeys.push(treeData[0][pane.nodeKey])
          }
          pane.treeData = treeData
          // 变更终端、操作员节点的状态
          if (pane.type == '3') {
            this.changeTermTreeStatus(pane.treeData, this.termStatusMap)
          } else if (pane.type == '4') {
            this.changeUserTreeStatus(pane.treeData, this.userStatusMap)
          }
        }
      });
    },
    // 过滤终端节点
    filterTermNodesMap(termNodesMap) {
      if (termNodesMap) {
        termNodesMap = JSON.parse(JSON.stringify(termNodesMap))
        for (const id in termNodesMap) {
          if (Object.hasOwnProperty.call(termNodesMap, id)) {
            const node = termNodesMap[id];
            termNodesMap[id] = node.filter(n => this.filterTermNode(n))
          }
        }
      }
      this.termNodesMap = termNodesMap
    },
    // 通过终端类型过滤节点，返回 true 显示
    filterTermNode(node, type) {
      const dataType = node.dataType
      const osType = type || (this.isCommon ? 15 : this.osType)
      // 分组显示
      if (dataType == 'G') {
        return true
      }
      let termOsType = 0
      // 终端类型的值转换，Windows：0 -> 1; Linux：1 -> 2; Mac：2 -> 4; 移动终端：3 -> 8;
      if (dataType >= 0x80) {
        // 离线终端，dataType >= 128
        termOsType = Math.pow(2, dataType - 0x80)
      } else if (dataType >= 0x20) {
        // U盘终端，dataType >= 32
        termOsType = Math.pow(2, dataType - 0x20)
      } else if (dataType >= 0x10) {
        // 老板终端，dataType >= 16
        termOsType = Math.pow(2, dataType - 0x10)
      } else {
        // 普通终端
        termOsType = Math.pow(2, dataType)
      }
      // 使用 & 判断 osType 是否包含 termOsType
      return (osType & termOsType) > 0
    },
    // 清空查询关键字
    clearFilter() {
      this.showedTabPanes.forEach(tab => {
        this.$refs[tab.name][0].clearFilter()
      })
    },
    changeTreeStatus(type, nodeDatas, statusMap) {
    },
    getTermTreeNodeDisplayMode() {
      getConfigByKey({ key: 'termTreeNodeDisplayMode' }).then(respond => {
        this.termTreeNodeDisplayMode = respond.data.value
      })
    },
    // 变更终端树的状态
    changeTermTreeStatus(nodeDatas, statusMap) {
      if (nodeDatas.length > 0) {
        nodeDatas.forEach(nodeData => {
          if (nodeData.children) {
            this.changeTermTreeStatus(nodeData.children, statusMap)
          } else {
            // 1 表示终端
            if (nodeData.type == 1) {
              const statusKey = nodeData.dataId
              const statusObj = statusMap[statusKey]
              const online = !!statusObj && statusObj.status != 0
              nodeData.online = online
              this.$set(nodeData, 'colorClass', online ? 'green' : '')
              // 显示 终端(操作员账号/操作员名称) 的情况
              if (this.termTreeNodeDisplayMode == '3' || this.termTreeNodeDisplayMode == '4') {
                const [terminalLabel] = nodeData.label.split('(')
                const operatorLabel = statusObj && statusObj.userName ? statusObj.userName : this.$t('pages.null')
                this.$set(nodeData, 'label', `${terminalLabel}(${operatorLabel})`)
              }
            }
          }
        })
      }
    },
    // 变更操作员树的状态
    changeUserTreeStatus(nodeDatas, statusMap) {
      if (nodeDatas.length > 0) {
        nodeDatas.forEach(nodeData => {
          if (nodeData.children) {
            this.changeUserTreeStatus(nodeData.children, statusMap)
          } else {
            if (nodeData.type == 2) { // 表示终端操作员
              const status = this.termStatusMap[statusMap[nodeData.dataId]]
              const online = !!status && status.status == 3
              nodeData.online = online
              this.$set(nodeData, 'colorClass', online ? 'green' : '')
            }
          }
        })
      }
    },
    // StrategyTargetTree 宽度拖拽变化的方法。TODO：修改为指令的方式？
    mousedown(e) {
      const target = e.target
      const startX = e.clientX
      const rl = this.resizeLength
      const nextEle = this.$el.parentNode.nextElementSibling
      let moveLen
      document.onmousemove = e => {
        moveLen = e.clientX - startX + rl
        this.resizeLength = moveLen
        const extraWidth = this.isCommon ? 25 : 10
        nextEle.style.marginLeft = this.width + extraWidth + 'px'
      }
      document.onmouseup = e => {
        if (this.resizeLength < 0) {
          this.resizeLength = 0
        } else if (this.resizeLength > 300) {
          this.resizeLength = 300
        }
        // 更新父组件传入的值
        this.$emit('update:resizeWidth', this.resizeLength)
        document.onmousemove = null
        document.onmouseup = null
        target.releaseCapture && target.releaseCapture() // 当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
      }
      target.setCapture && target.setCapture() // 该函数在属于当前线程的指定窗口里设置鼠标捕获
      return false
    },
    // 点击tab事件
    handleTabClick(tab, event) {
      // todo 新增按钮是否可用-> 节点不能新增时，弹出提示
      // enableStgBtn({}, this.$parent)
      setTimeout(() => {
        // this.loadTreeData()
        const data = this.getCurrentNode()
        this.$emit('data-change', tab.name, data, tab)
      }, 0);
    },
    // 节点被点击时的回调	共三个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node、节点组件本身。
    nodeClick(data, node, el) {
      // console.log('node click', data, node);
      if (this.isCommon && this.checkedTab == 'terminal') {
        const filterFunction = typeof this.commonTerminalFilter === 'function' ? this.commonTerminalFilter : this.filterTermNode
        const isTerm = data.type == 1
        if (isTerm && !filterFunction(data, this.osType)) {
          this.setCurrentKey(null)
          this.$message({
            message: this.$t('pages.termTypeNotSupport'),
            type: 'info',
            duration: 2000
          })
          return
        }
      }
      this.$emit('data-change', this.checkedTab, data, node, el)
      // this.$emit('node-click', data, node, el)
    },
    // 展开节点事件
    handleNodeExpand(data, node, el) {
      // console.log('handleNodeExpand', data, node, el, this.getCurrentPanel());
      // 手风琴模式，展开节点后，滚动到展开的节点
      this.$nextTick(() => {
        this.accordion && this.getCurrentTree().scrollToNode(data)
      })
      this.$emit('node-expand', data, node, el)
    },
    // 合起节点事件
    handleNodeCollapse(data, node, el) {
      this.$emit('node-collapse', data, node, el)
    },
    checkChange(data, node) {
      this.$emit('check-change', data, node)
    },
    // 根据 data 或者 key 拿到 Tree 组件中的 node以及所有上级节点
    getNodePath(data) {
      return this.getCurrentTree().getNodePath(data)
    },
    // dataType 数据的 type 值，keyValue：查找的字段的值 , keyField：查找的字段的 key
    findNode(dataType, keyValue, keyField = 'dataId') {
      const datas = this.getTreeDataByType(dataType)
      return this.getCurrentTree().findNode(datas, keyValue, keyField)
    },
    //  TODO：offineStrategyList\index.vue 中使用了该方法，后续修改
    //  不区分字符串
    findNodeUndifferentiatedString(objectType, objectId) {
      for (const index in this.builtInPanes) {
        const tabPane = this.builtInPanes[index]
        const targetNode = this.findNodeByNodesUndifferentiatedString(tabPane.treeData, objectType, objectId)
        if (targetNode) {
          return targetNode
        }
      }
    },
    findNodeByNodesUndifferentiatedString(nodes, objectType, objectId) {
      if (nodes && nodes.length > 0) {
        for (const i in nodes) {
          const nodei = nodes[i]
          if (nodei.type == objectType && nodei.dataId == objectId) {
            return nodei
          } else {
            const temp = this.findNodeByNodesUndifferentiatedString(nodei.children, objectType, objectId)
            if (temp) return temp
          }
        }
      }
    },
    // 调用 TreeMenu 的 getCurrentKey 方法
    getCurrentKey() {
      return this.getCurrentTree().getCurrentKey()
    },
    // 调用 TreeMenu 的 setCurrentKey 方法
    setCurrentKey(key) {
      this.getCurrentTree().setCurrentKey(key)
    },
    // 调用 TreeMenu 的 getCurrentNode 方法
    getCurrentNode() {
      return this.getCurrentTree().getCurrentNode()
    },
    // 调用TreeMenu 的 getCheckNodes 方法, 多选时获取选中的节点
    getCheckedNodes() {
      return this.getCurrentTree().getCheckedNodes()
    },
    // 调用 TreeMenu 的 setCurrentNode 方法
    setCurrentNode(node) {
      this.getCurrentTree().setCurrentNode(node)
    },
    // 调用 TreeMenu 的 getNode 方法, (data) 要获得 node 的 key 或者 data
    getNode(data) {
      return this.getCurrentTree().getNode(data)
    },
    // 该方法即将弃用，替换方法 setCurrentKey ，删除前需将项目中调用该方法的地方替换为 setCurrentKey
    setCurrent(node) {
      this.setCurrentKey(node)
    },
    // 选中或取消选中选择框
    checkNode(nodeData, checked) {
      this.getCurrentTree().setChecked(nodeData, checked)
    }
  }
}
</script>

<style lang="scss" scoped>
  .strategy-tree>>>.el-tabs__content{
    height: calc( 100% - 40px);
    padding: 0;
  }
  .strategy-tree>>>.el-tabs__item.is-top{
    padding: 0 10px;
  }
  .no-header-tab>>>.el-tabs__header{
    display: none;
  }
  .no-header-tab>>>.el-tabs__content{
    height: 100%;
  }
  .widthResize{
    // background: red;
    width: 4px;
    position: absolute;
    height: 100%;
    top: -5px;
    right: 0px;
    cursor: col-resize;
    // transition: background 1s;
    // &:hover{
    //   background: #5555d9;
    // }
  }
  .mask {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 1;
    z-index: 11;
  }
  .text {
    width: 60px;
    height: 24px;
    line-height: 24px;
    position: absolute;
    top: calc(50% - 24px);
    left: calc(50% - 30px);
    background: #000;
    text-align: center;
  }
</style>
