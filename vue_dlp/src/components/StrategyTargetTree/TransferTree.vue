
<!--
  作为各个页面与公共树组件之间通信的组件，通过 __NONCOMMON_STRATEGY_TARGET_TREE__ 也可切换为各个页面单独使用 strategy-target-tree
  调用示例：
    <TransferTree
      ref="transferTree"                              // 组件 ref
      :show-tree.sync="showTree"                      // 控制树组件显示或隐藏，需加上.sync 实现更新 showTree 的操作
      :showed-tree="['terminal']"                     // 
      :os-type-filter="7"                             // 手动控制显示的终端类型，1-win,2-linux,4-mac,8-移动设备（数值的和）
      :terminal-filter-key="terminalFilterKey"        // 过滤终端的方法，若使用公共组件，则会在点击节点时进行判断，返回false则不继续执行代码
      @data-change="strategyTargetNodeChange"         // 选中数据发生变化的回调方法
    />
-->
<template>
  <div v-if="!__NONCOMMON_STRATEGY_TARGET_TREE__" :class="'hidden'"></div>
  <div v-else class="tree-container" :class="(listable && treeable && showTree) ? '' : 'hidden'">
    <strategy-target-tree
      v-if="listable && treeable"
      ref="strategyTargetTree"
      :showed-tree="showedTree"
      :os-type-filter="osTypeFilter"
      :terminal-filter-key="terminalFilterKey"
      :is-log-terminal-tree="isLogTerminalTree"
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import { EventBus } from '@/layout/event-bus';

export default {
  name: 'TransferTree',
  props: {
    listable: { type: Boolean, default: true },
    treeable: { type: Boolean, default: true },
    showTree: { type: Boolean, default: true },
    showedTree: { type: Array, default() { return ['terminal', 'user'] } },
    // 手动控制显示的终端类型，1-win,2-linux,4-mac,8-移动设备（数值的和）, 当后台配置数据不存在时才会生效
    osTypeFilter: { type: Number, default: null },
    terminalFilterKey: { type: Function, default: null },
    isLogTerminalTree: { type: Boolean, default: false }
  },
  data() {
    return {
      routerKey: '', // 记录组件创建时的路由path
      isCreated: false
    }
  },
  watch: {
    showTree(val) {
      this.$nextTick(() => {
        EventBus.$emit('showTree', val)
      })
    },
    $route(val) {
      this.$nextTick(() => {
        this.emitFunction()
      })
    }
  },
  created() {
    this.routerKey = this.$route.path
    // 每个页面共用选中的树节点的情况
    if (!this.$store.getters.cacheSelectedNode) {
      const commonTree = this.getStrategyTargetTree()
      const checkedNode = commonTree ? commonTree.getCurrentNode() : null
      // 有选中节点的情况下，设置 $route.query.autoload = false，阻止 GridTable 自动加载数据
      if (checkedNode) {
        this.$route.query.autoload = false
      }
    }
    
    EventBus.$on(`${this.routerKey}-dataChange`, this.dataChange)
    EventBus.$on(`${this.routerKey}-showTree`, this.showTreeChange)
    EventBus.$emit('usedScope', this.listable && this.treeable)
    this.emitFunction()
    const commonShowTree = EventBus.commonShowTree
    if (this.showTree != commonShowTree) {
      commonShowTree != undefined && this.showTreeChange(commonShowTree)
    }
    this.isCreated = true
  },
  methods: {
    emitFunction() {
      if (this.$route.path == this.routerKey) {
        EventBus.$emit('showedTree', this.showedTree)
        EventBus.$emit('osTypeFilter', this.osTypeFilter)
        EventBus.$emit('terminalFilterKey', this.terminalFilterKey)
        EventBus.$emit('isLogTerminalTree', this.isLogTerminalTree)
      }
    },
    getStrategyTargetTree() {
      if (!this.__NONCOMMON_STRATEGY_TARGET_TREE__) {
        return this.$store.getters.commonVm.strategyTargetTree
      } else {
        return this.$refs.strategyTargetTree
      }
    },
    dataChange(...args) {
      this.$emit('data-change', ...args)
    },
    showTreeChange(val) {
      this.$emit('update:showTree', val)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
