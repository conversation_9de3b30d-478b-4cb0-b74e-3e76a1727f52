<!--
  /**
  * 策略应用对象组件，用于策略窗口中应用对象的选择。
  * @update 2022-07-05
  * 调用示例：(完整配置见 props)
    <stg-target-form-item
      ref="formItem"
      tree-width="470"                                 // 树的宽度（单位px）
      entity-type-span="8"                             // 策略应用树，占用span
      :stg-code="5"                                    // 策略编码，stg_base_config的number字段值
      :stg-type="1"                                    // 策略类型，普通策略为1，多继承策略为2
      :form-data="temp"                                // 从父组件form表单数据
      :is-disabled="isDisabled"                        // 是否禁用
      :strategy-def-type="strategyDefType"             // 1 预定义策略 0 应用策略
      rewrite-node-click-fuc                           // 是否重写树节点单击事件，默认为false
      :node-click-fuc="nodeClickFunction"              // 重写树节点单击事件，rewriteNodeClickFuc 需要为true
      @entityNodeData="nodeData"                       // 选中节点的数据
    />
  */
-->
<template>
  <el-row v-if="isGeneralStrategy">
    <el-col :span="entityTypeSpan">
      <FormItem :label="$t('pages.effectiveObject')" class="required" :label-width="labelWidth">
        <el-select v-model="entityType" class="targetType" :disabled="isDisabled || entityTypeDisabled" @change="entityTypeChange">
          <el-option v-show="terminalUseable" :value="termEntityTypeValue" :label="$t('components.terminalG')"/>
          <el-option v-show="userUseable" :value="userEntityTypeValue" :label="$t('components.userG')"/>
        </el-select>
      </FormItem>
    </el-col>
    <el-col :span="24-entityTypeSpan">
      <FormItem label-width="0px">
        <tree-select
          ref="objectTree"
          node-key="id"
          class="targetObject"
          :disabled="isDisabled || !entityType"
          :height="350"
          :width="treeWidth"
          :local-search="false"
          :checked-keys="checkedKeys"
          is-filter
          :multiple="multiple"
          check-strictly
          :collapse-tags="collapseTags"
          :leaf-key="entityType === 1 || entityType === 3 ? 'terminal' : 'user'"
          :filter-key="filterKey"
          :rewrite-node-click-fuc="rewriteNodeClickFuc"
          :node-click-fuc="nodeClickFuc"
          @change="entityIdChange"
        />
      </FormItem>
    </el-col>
  </el-row>
</template>

<script>
import { getUsedScope } from '@/api/stgCommon'
import { getMstgUsedScope } from '@/api/system/baseData/strategyType'
import { isEmpty } from '@/utils/validate'
import { mapGetters } from 'vuex'

export default {
  name: 'StgTargetFormItem',
  props: {
    treeWidth: { type: Number, default: 470 },                                // 树的宽度（单位px）
    treeVisible: { type: Boolean, default: true },                            // 是否显示树
    stgCode: { type: Number, default: 0 },                                    // 策略编码，stg_base_config的number字段值
    stgType: { type: Number, default: 1 },                                    // 策略类型，普通策略为1，多继承策略为2
    strategyDefType: { type: Number, default: 0 },                            // 1 预定义策略 0 应用策略
    termEntityTypeValue: { type: Number, default: 3 },                        // 生效对象类型：终端的类型值，一般是3或1
    userEntityTypeValue: { type: Number, default: 4 },                        // 生效对象类型：操作员的类型值，一般是4或2
    entityTypeSpan: { type: Number, default: 12 },                            // 策略应用类型，占用span
    formData: { type: Object, default() { return {} } },                      // 从父组件传入form表单绑定的对象
    labelWidth: { type: String, default: '' },                                // label宽度，默认由form设置，修改需要带单位，如 100px
    isDisabled: { type: Boolean, default: false },                            // 是否禁用整个组件
    isDisabledType: { type: Boolean, default: false },                        // （废弃，使用 entityTypeDisable 代替）是否禁用应用类型
    entityTypeDisabled: { type: Boolean, default: false },                    // 是否禁用应用类型
    multiple: { type: Boolean, default: true },                               // 是否提供多选，即是否提供复选框
    collapseTags: { type: Boolean, default: true },                           // 多选时是否将选中值按文字的形式展示
    terminalFilter: { type: Function, default: null },                        // 终端树节点的过滤方法
    userFilter: { type: Function, default: null },                            // 操作员树节点的过滤方法
    rewriteNodeClickFuc: { type: Boolean, default: false },                   // 是否重写树节点单击事件，默认为false
    nodeClickFuc: { type: Function, default: function(data, node, vm) {} }    // 重写树节点单击事件，rewriteNodeClickFuc 需要为true
  },
  data() {
    return {
      usedScope: 3,                                             // 策略应用类型（1终端、2操作员、3两者）
      osType: 7,                                                // 终端节点可配置的类型的总值，根据 策略基础设置 进行调整
      entityType: this.termEntityTypeValue,                     // 3 终端（组）， 4 操作员（组）
      checkedKeys: [],                                          // 选择的节点的key
      oldCheckedIds: [],                                        // 记录历史选中节点的id
      innerSetObjectInfo: {                                     // 记录内部赋值信息，内部赋值操作，不需要再进行值监听，避免陷入死循环
        objIds: null,
        groupIds: null
      }
    }
  },
  computed: {
    ...mapGetters([
      'stgBaseConfig',
      'mstgBaseConfig'
    ]),
    // 是否应用策略
    isGeneralStrategy() {
      return this.strategyDefType === 0
    },
    // 终端是否可用
    terminalUseable() {
      return this.usedScope != 2
    },
    // 操作员是否可用
    userUseable() {
      return this.usedScope != 1
    },
    // 根据选择的应用对象，返回 filterKey 方法
    filterKey() {
      return this.entityType == 3 ? this.terminalFilterFunc : this.userFilter
    },
    // 终端树节点的过滤方法
    terminalFilterFunc() {
      return node => {
        if (typeof this.terminalFilter == 'function') {
          return this.terminalFilter(node)
        }
        // 分组显示
        if (node.type == 3) {
          return true
        }
        return this.filterTermNode(node)
      }
    }
  },
  watch: {
    formData: {
      deep: true,
      handler(val) {
        this.setEffectiveObject(val)
      }
    }
  },
  mounted: function() {
    if (this.stgCode) {
      const optionMap = {
        1: { configMap: this.stgBaseConfig, getUsedScopeFunction: getUsedScope },
        2: { configMap: this.mstgBaseConfig, getUsedScopeFunction: getMstgUsedScope }
      }
      const option = optionMap[this.stgType]
      if (!option) return
      const { configMap, getUsedScopeFunction } = option

      // 获取 策略基础设置，并设置 usedScope，osType
      if (configMap) {
        this.usedScope = configMap[this.stgCode].usedScope
        const oldOsType = this.osType
        this.osType = configMap[this.stgCode].osType
        if (oldOsType != this.osType) {
          // 如果osType发生变化，则重新过滤一遍
          this.$refs.objectTree.handleFilter()
        }
      } else {
        getUsedScopeFunction(this.stgCode).then(response => {
          this.usedScope = response.data
        })
      }
    }
    this.setEffectiveObject(this.formData)
    this.$nextTick(() => {
      if (this.formData && isEmpty(this.formData.strategyDefType)) {
        this.formData.strategyDefType = this.strategyDefType
      }
    })
  },
  created() {
    // 策略类型（应用策略、预定义策略）由 isGeneralStrategy 计算属性判断
  },
  methods: {
    // 通过终端类型过滤节点，返回 true 显示
    filterTermNode(node) {
      const dataType = node.dataType
      // 分组显示
      if (dataType == 'G') {
        return true
      }
      const termOsType = this.osTypeConver(dataType)
      // 使用 & 判断 osType 是否包含 termOsType
      return (this.osType & termOsType) > 0
    },
    // 终端类型的值转换，Windows：0 -> 1; Linux：1 -> 2; Mac：2 -> 4; 移动终端：3 -> 8;
    osTypeConver(value) {
      if (value >= 0x80) {
        // 离线终端，dataType >= 128
        return Math.pow(2, value - 0x80)
      } else if (value >= 0x20) {
        // U盘终端，dataType >= 32
        return Math.pow(2, value - 0x20)
      } else if (value >= 0x10) {
        // 老板终端，dataType >= 16
        return Math.pow(2, value - 0x10)
      } else {
        // 普通终端
        return Math.pow(2, value)
      }
    },
    // 设置应用对象（选择类型，并勾选节点）
    setEffectiveObject(data) {
      this.setEntityType(data)
      this.$nextTick(() => {
        const { entityId, entityType, objectIds, objectGroupIds, objectType } = data
        const objIds = []
        const objGroupIds = []
        // 如果 objectIds、objectGroupIds 都没有值
        if (!objectIds && !objectGroupIds) {
          // entityId 有值，将 entityId 添加到对应的数组
          if (!isEmpty(entityId)) {
            const type = entityType || objectType
            if (type == 3 || type == 4) {
              objGroupIds.push(entityId)
            } else {
              objIds.push(entityId)
            }
          }
        }
        // 给 formData 对应的属性赋值
        this.formData.objectIds = objectIds || objIds
        this.formData.objectGroupIds = objectGroupIds || objGroupIds

        // 判断数据中勾选的节点和历史勾选的节点是否一样，一样则返回
        const sameType = this.entityType == objectType
        const sameIds = this.formData.objectIds.concat(this.formData.objectGroupIds).toString() == this.oldCheckedIds.toString()
        if (sameType && sameIds) {
          return
        }

        this.commonCheck(data)
      })
    },
    // 设置应用对象类型
    setEntityType(data) {
      // 策略总览数据用objectType保存应用对象的类型，所以分别对entityType，objectType 取值
      const entityType = data.entityType || data.objectType
      this.entityType = entityType ? { 1: this.termEntityTypeValue, 3: this.termEntityTypeValue, 2: this.userEntityTypeValue, 4: this.userEntityTypeValue }[entityType] : ''
    },
    /**
     * 根据表单数据选中节点
     * @param option 表单数据对象
     */
    commonCheck(option) {
      const entityType = this.entityType
      this.checkedKeys.splice(0)
      this.$nextTick(() => {
        if (entityType) {
          const { objectIds, objectGroupIds } = option
          const isUOrG = entityType == 2 || entityType == 4
          this.oldCheckedIds = (objectIds || []).concat(objectGroupIds || [])
          if (objectIds) {
            objectIds.forEach(objId => {
              this.checkedKeys.splice(0, 0, (isUOrG ? 'U' : 'T') + objId)
            })
          }
          if (objectGroupIds) {
            objectGroupIds.forEach(objId => {
              this.checkedKeys.splice(0, 0, 'G' + objId)
            })
          }
        } else {
          // 应用对象类型没有值时，删除历史选中节点
          this.oldCheckedIds.splice(0)
        }
        // 给 strategyDefType 赋值
        if (this.formData && isEmpty(this.formData.strategyDefType)) {
          this.formData.strategyDefType = this.strategyDefType
        }
      })
    },
    entityTypeChange(data) {
      this.checkedKeys.splice(0)
      this.formData.entityType = data
      this.formData.entityId = undefined
      this.formData.objectType = data
      this.formData.objectIds = null
      this.formData.objectGroupIds = null
      this.$emit('entityTypeData', data)
    },
    entityIdChange(key, nodeData) {
      const data = this.multiple ? nodeData : (nodeData[0] || nodeData)

      // 部分页面还有使用 entityType、entityId， 所以这边暂时不删除
      this.formData.entityType = this.formData.entityType || this.formData.objectType
      this.formData.entityId = this.multiple ? '' : data.dataId

      this.formatObjectInfo(Array.isArray(nodeData) ? nodeData : [nodeData])
      this.$emit('entityNodeData', data)
    },
    /**
     * 对数据进行格式化处理，勾选的节点 dataId 根据类型分别存入 objectIds，objectGroupIds
     */
    formatObjectInfo(nodeData) {
      const checkedNodeDatas = Array.isArray(nodeData) ? nodeData : [nodeData]
      const objIds = []
      const objGroupIds = []
      checkedNodeDatas.forEach(nData => {
        if (nData.dataType === 'G') {
          objGroupIds.push(nData.dataId)
        } else {
          objIds.push(nData.dataId)
        }
      })
      // 给 objectType 赋值
      this.formData.objectType = this.entityType
      // 给 objectIds 赋值
      this.formData.objectIds = objIds
      // 给 objectGroupIds 赋值
      this.formData.objectGroupIds = objGroupIds
      // 记录勾选的节点的id
      this.oldCheckedIds = objIds.concat(objGroupIds)
    },
    valid() { // 校验应用对象的类型和ID是否有值，如果需要校验需要主动调用此方法
      // if (!this.isGeneralStrategy) {
      //   // 如果是预定义策略，那么不需要校验直接返回
      //   return true
      // }
      // const { entityType, objectType, entityId, objectIds, objectGroupIds } = this.formData
      // const type = entityType || objectType
      // if (isEmpty(type) || isEmpty(entityId) && isEmpty(objectIds) && isEmpty(objectGroupIds)) {
      //   this.$message({
      //     message: this.$t('components.chooseApplicationObj'),
      //     type: 'error',
      //     duration: 2000
      //   })
      //   return false
      // }
      // return true
      // 策略应用对象的校验改到后台：因为子管理员的权限范围较低时，界面可以置空应用对象，由后台判断
      return true
    }
  }
}
</script>
