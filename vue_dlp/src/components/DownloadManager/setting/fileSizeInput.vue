<template>
  <div class="input-file-size el-form-item__content" :class="{ 'el-form-item': showError, 'is-error': showError }">
    <el-input v-model="size" :disabled="disabled" :maxlength="maxlength" aria-required="true" @input="handleInput">
      <!-- el-select 添加 collapse-tags 属性，避免 disabled 变化时 el-select 中 输入框高度发生变化 -->
      <!-- el-select 添加 @click.native 事件，用于监听组件根元素的原生DOM事件，阻止 click 事件冒泡 -->
      <el-select slot="append" v-model="unit" :disabled="disabled" collapse-tags :placeholder="$t('text.select')" @change="handleChange" @click.native.stop.prevent>
        <el-option v-for="(val, key) in units" :key="val" :label="key" :value="key"/>
      </el-select>
    </el-input>
    <div v-if="showError" class="el-form-item__error">
      {{ $t('text.cantNull') }}
    </div>
  </div>
</template>

<script>
import { onlyInt } from '@/utils/inputLimit'

export default {
  name: 'FileSizeInput',
  props: {
    value: {
      type: Number,
      default: undefined
    },
    disabled: {
      type: Boolean,
      default: false
    },
    max: { // 最大值，默认2GB
      type: Number,
      default: 2 * 1024 * 1024 * 1024
    },
    maxlength: {
      type: Number,
      default: 7
    },
    initUnit: {
      type: String,
      default: undefined
    },
    displayUnits: {
      type: Object,
      default: undefined
    },
    autoValidate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      size: undefined,
      unit: undefined,
      changed: false
    }
  },
  computed: {
    units() {
      if (!this.displayUnits || Object.keys(this.displayUnits).length === 0) {
        return {
          // B: 1,
          // KB: 1024,
          MB: 1024 * 1024,
          GB: Math.pow(1024, 3)
          // TB: Math.pow(1024, 4)
        }
      }
      return this.displayUnits
    },
    currentValue() {
      const currentSize = this.calcSize()
      if (currentSize) {
        return currentSize * this.units[this.unit]
      }
      return undefined
    },
    showError() {
      return this.autoValidate && !this.value
    }
  },
  watch: {
    value() {
      if (this.changed) {
        this.changed = false
        return
      }
      this.parseValue()
    }
  },
  created() {
    this.parseValue()
  },
  methods: {
    resetFileSize() {
      this.size = undefined
      if (this.initUnit && this.initUnit in this.units) {
        this.unit = this.initUnit
      } else {
        this.unit = Object.keys(this.units)[0]
      }
    },
    parseValue() {
      if (this.value) {
        this.unit = undefined
        if (this.units.TB && this.value >= this.units.TB) {
          this.size = this.value / this.units.TB
          this.unit = this.size === Math.floor(this.size) ? 'TB' : undefined
        }
        if (!this.unit && this.units.GB && this.value >= this.units.GB) {
          this.size = this.value / this.units.GB
          this.unit = this.size === Math.floor(this.size) ? 'GB' : undefined
        }
        if (!this.unit && this.units.MB && this.value >= this.units.MB) {
          this.size = this.value / this.units.MB
          this.unit = this.size === Math.floor(this.size) ? 'MB' : undefined
        }
        if (!this.unit && this.units.KB && this.value >= this.units.KB) {
          this.size = this.value / this.units.KB
          this.unit = this.size === Math.floor(this.size) ? 'KB' : undefined
        }
        if (!this.unit && this.units.B && this.value >= this.units.B) {
          this.size = this.value
          this.unit = this.size === Math.floor(this.size) ? 'B' : undefined
        }
      }
      if (!this.unit) {
        this.resetFileSize()
      }
    },
    setSizeUnit(size, unit) {
      this.size = size;
      this.unit = unit;
      this.handleChange()
    },
    getSizeUnit() {
      return { size: this.size, unit: this.unit }
    },
    calcSize() {
      if (typeof this.size === 'string') {
        this.size = parseFloat(this.size)
      }
      if (isNaN(this.size) || this.size === 0) {
        this.size = undefined
      } else if (this.max) {
        const sizeB = this.size * this.units[this.unit]
        if (sizeB > this.max) {
          this.size = this.max / this.units[this.unit]
        }
      }
      return this.size
    },
    handleInput(value) {
      this.size = onlyInt(value)
      this.handleChange()
    },
    handleChange() {
      if (this.value !== this.currentValue) {
        this.changed = true
        this.$emit('input', this.currentValue)
        this.$emit('change', this.currentValue)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .input-file-size {
    display: inline-block;
    margin: 0;
    .el-input-group {
      width: 200px;
      vertical-align: middle;
      >>>.el-input-group__append {
        width: 80px;
        padding: 0;
        .el-select {
          margin: 0;
          .el-input__inner:disabled {
            color: inherit !important;
          }
          &:hover .el-input__inner {
            color: #666;
            &:disabled {
              background-color: #e4e7e9;
            }
          }
        }
      }
      >>>.el-input__inner {
        text-align: center;
      }
    }
  }
</style>
