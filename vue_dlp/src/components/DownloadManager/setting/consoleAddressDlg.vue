<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.ConsoleWebServerConfig')"
    width="660px"
    :modal="false"
    :append-to-body="appendToBody"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
  >
    <console-address ref="addrCfg" hide-save-button/>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="updateConfig">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import ConsoleAddress from './consoleAddress'

export default {
  name: 'ConsoleAddressDialog',
  components: { ConsoleAddress },
  props: {
    appendToBody: { type: <PERSON><PERSON><PERSON>, default: false }
  },
  data() {
    return {
      visible: false,
      submitting: false
    }
  },
  methods: {
    show() {
      if (this.$refs.addrCfg) {
        this.$refs.addrCfg.clearValidate()
        this.$refs.addrCfg.initConfig()
      }
      this.submitting = false
      this.visible = true
    },
    updateConfig() {
      this.submitting = true
      this.$refs.addrCfg.handleUpdateConfig().then(() => {
        this.submitting = false
        this.visible = false
        this.$emit('updated')
      }).catch(() => { this.submitting = false })
    }
  }
}
</script>
