<template>
  <el-tabs ref="tabs" v-model="tabName" type="card" @tab-click="tabClick">
    <el-tab-pane name="addrTab">
      <template slot="label">
        {{ $t('pages.ConsoleWebServerConfig') }}
        <el-tooltip class="item" effect="dark" placement="right" :content="$t('pages.globalConfig_configInstruction')">
          <i class="el-icon-info" />
        </el-tooltip>
      </template>
      <console-address/>
    </el-tab-pane>
    <el-tab-pane v-if="hasPermission('205')" :label="$t('pages.extractTerminalProfile')" name="termTab">
      <term-file-extract/>
    </el-tab-pane>
    <el-tab-pane v-if="false" :label="$t('components.auditFileBatchDownloadConfig')" name="auditTab">
      <audit-file-batch/>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import ConsoleAddress from './consoleAddress'
import TermFileExtract from './termFileExtract'
import AuditFileBatch from './auditFileBatch'

export default {
  name: 'DownloadSetting',
  components: { ConsoleAddress, TermFileExtract, AuditFileBatch },
  data() {
    return {
      tabName: 'addrTab'
    }
  },
  created() {
    this.initTab()
  },
  activated() {
    this.initTab()
  },
  methods: {
    initTab() {
      if (this.$route.params.tab) {
        this.tabName = this.$route.params.tab
      }
    },
    tabClick(tab, event) {
      // console.log(tab, event)
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-tabs__content {
    padding: 30px;
    overflow: auto;
  }
</style>
