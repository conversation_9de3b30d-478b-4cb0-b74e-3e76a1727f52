<template>
  <div>
    <label>{{ $t('pages.fileSumSize') }}:</label>
    <file-size-input v-model="fileSumSize"/>
    <el-button type="primary" size="mini" :loading="submitting" @click="updateConfig">
      {{ $t('button.save') }}
    </el-button>
  </div>
</template>

<script>
import FileSizeInput from './fileSizeInput'

export default {
  name: 'AuditFileBatch',
  components: { FileSizeInput },
  data() {
    return {
      fileSumSize: undefined,
      submitting: false
    }
  },
  methods: {
    updateConfig() {

    }
  }
}
</script>
