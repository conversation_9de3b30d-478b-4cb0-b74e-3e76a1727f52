<template>
  <Form ref="addrForm" class="addr-config" :model="temp" :rules="rules" label-width="210px" :extra-width="{ en: 120 }" label-position="right">
    <FormItem :label="$t('pages.consoleIntranetIP')" prop="consoleIntranetIP">
      <el-autocomplete
        v-model="temp.consoleIntranetIP"
        v-trim
        popper-class="addr-autocomplete"
        value-key="ipv4"
        :fetch-suggestions="fetchIntranetIPs"
      >
        <template slot-scope="{ item }">
          <div class="addr-ip">{{ item.ipv4 }}</div>
          <span class="addr-name" :title="item.name">{{ item.name }}</span>
        </template>
      </el-autocomplete>
    </FormItem>
    <FormItem :label="$t('pages.consoleIntranetIPv6')" prop="consoleIntranetIpv6">
      <el-autocomplete
        v-model="temp.consoleIntranetIpv6"
        v-trim
        popper-class="addr-autocomplete"
        value-key="ipv6"
        :fetch-suggestions="fetchIntranetIPv6s"
      >
        <template slot-scope="{ item }">
          <div class="addr-ip">{{ item.ipv6 }}</div>
          <span class="addr-name" :title="item.name">{{ item.name }}</span>
        </template>
      </el-autocomplete>
    </FormItem>
    <!-- <FormItem :label="$t('pages.consoleIntranetPort')" prop="consoleIntranetPort">
      <el-input v-model="temp.consoleIntranetPort" readonly/>
    </FormItem> -->
    <FormItem label-width="0">
      <el-checkbox v-model="temp.consoleExtranetEnable" true-label="1" false-label="0">
        {{ $t('pages.enableExtranetMapping') }}
      </el-checkbox>
    </FormItem>
    <template v-if="extranetShow">
      <FormItem :label="$t('pages.consoleExtranetIPv4')" prop="consoleExtranetIP">
        <el-input v-model="temp.consoleExtranetIP" v-trim maxlength=""/>
      </FormItem>
      <FormItem :label="$t('pages.consoleExtranetIPv6')" prop="consoleExtranetIpv6">
        <el-input v-model="temp.consoleExtranetIpv6" v-trim maxlength=""/>
      </FormItem>
      <FormItem :label="$t('pages.consoleExtranetPort')" :tooltip-content="$t('pages.consoleExtranetPortTip')" prop="consoleExtranetPort">
        <el-input v-model.number="temp.consoleExtranetPort" maxlength="" @input="handlePortInput"/>
      </FormItem>
    </template>
    <FormItem v-if="!hideSaveButton" label-width="432px">
      <el-button type="primary" size="mini" :loading="submitting" @click="handleUpdateConfig">
        {{ $t('button.save') }}
      </el-button>
    </FormItem>
  </Form>
</template>

<script>
import { onlyInt } from '@/utils/inputLimit'
import { getConsoleIpPortConfig, getConfigYmlIp, getLocalNetworkCards, updateConfig } from '@/api/system/configManage/globalConfig'

export default {
  name: 'ConsoleAddress',
  props: {
    getConfigFunc: {
      type: Function,
      default() {
        return getConsoleIpPortConfig().then(res => res.data)
      }
    },
    hideSaveButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      validateMessageInstance: undefined,
      initPromise: undefined,
      initialized: false,
      submitting: false,
      temp: {
        consoleIntranetIP: '',
        consoleIntranetIpv6: '',
        consoleIntranetPort: '',
        consoleExtranetEnable: '0',
        consoleExtranetIP: '',
        consoleExtranetIpv6: '',
        consoleExtranetPort: ''
      },
      intranetData: [],
      domainReg: new RegExp('^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$'),
      rules: {
        consoleIntranetIP: [{ validator: this.intranetIpValidator, trigger: 'blur' }],
        consoleExtranetIP: [{ validator: this.extranetIpValidator, trigger: 'blur' }],
        consoleExtranetPort: [{ validator: this.extranetPortValidator, trigger: 'blur' }]
      }
    }
  },
  computed: {
    ipv4s() {
      return this.intranetData.filter(({ ipv4 }) => typeof ipv4 === 'string' && ipv4.trim().length > 0)
    },
    ipv6s() {
      return this.intranetData
        .filter(({ ipv6s }) => Array.isArray(ipv6s) && ipv6s.length > 0)
        .flatMap(({ name, ipv6s }) => ipv6s.filter(ipv6 => !!ipv6).map(ipv6 => ({ name, ipv6 })))
    },
    extranetShow() {
      return Boolean(parseInt(this.temp.consoleExtranetEnable))
    }
  },
  created() {
    this.initPromise = new Promise(this.initConfig)
    this.$nextTick(() => {
      this.clearValidate()
    })
  },
  methods: {
    initConfig(resolve, reject) {
      return this.getConfigFunc().then(configs => {
        for (const key in this.temp) {
          if (!this.temp.hasOwnProperty(key)) {
            continue
          }
          if (configs.hasOwnProperty(key)) {
            this.temp[key] = configs[key]
          } else if (key === 'consoleExtranetEnable') {
            this.temp[key] = '0'
          } else {
            this.temp[key] = ''
          }
        }
      }).then(() => this.getIntranetData(resolve)).catch(reject)
    },
    async getIntranetData(resolve) {
      const networkCards = await getLocalNetworkCards()
      this.intranetData = networkCards.data
      const needCheckIpv4 = !this.temp.consoleIntranetIpv6 && !this.extranetShow
      const ipv4Configured = !!this.temp.consoleIntranetIP
      if (needCheckIpv4 && !ipv4Configured) {
        const ymlIp = await getConfigYmlIp()
        const { ipv4, ipv6 } = ymlIp.data
        if (ipv4) {
          this.temp.consoleIntranetIP = ipv4
        } else if (ipv6) {
          this.temp.consoleIntranetIpv6 = ipv6
        }
      }
      const needRepairIpv4 = needCheckIpv4 || ipv4Configured
      if (needRepairIpv4 && this.ipv4s.length === 1) {
        this.temp.consoleIntranetIP = this.ipv4s[0].ipv4
      }
      resolve()
      this.initialized = true
    },
    fetchIntranetIPs(queryString, callback) {
      this.fetchSuggestions(queryString, callback, this.ipv4s, 'ipv4')
    },
    fetchIntranetIPv6s(queryString, callback) {
      this.fetchSuggestions(queryString, callback, this.ipv6s, 'ipv6')
    },
    fetchSuggestions(queryString, callback, addresses, key) {
      if (queryString) {
        const results = addresses.filter(item => item[key].indexOf(queryString) >= 0)
        callback(results)
      } else {
        callback(addresses)
      }
    },
    clearValidate() {
      if (this.validateMessageInstance) {
        this.validateMessageInstance.close()
        this.validateMessageInstance = undefined
      }
      this.submitting = false
      this.$refs.addrForm.clearValidate()
    },
    validate() {
      return this.$refs.addrForm.validate().then((valid, fields) => {
        if (this.extranetShow) {
          if (!this.temp.consoleExtranetIP && !this.temp.consoleExtranetIpv6) {
            this.showErrorMessage(this.$t('pages.globalConfig_Msg5'))
            return Promise.reject()
          }
        } else {
          if (!this.temp.consoleIntranetIP && !this.temp.consoleIntranetIpv6) {
            this.showErrorMessage(this.$t('pages.globalConfig_Msg3'))
            return Promise.reject()
          }
        }
        return valid ? Promise.resolve(valid) : Promise.reject(fields)
      })
    },
    getFormData() {
      return Object.keys(this.temp)
        .filter(key => {
          if (key.startsWith('consoleIntranet')) {
            return key !== 'consoleIntranetPort'
          }
          return this.extranetShow || key === 'consoleExtranetEnable'
        })
        .map(key => ({ key, value: this.temp[key], isProp: true }))
    },
    handleUpdateConfig() {
      return this.validate().then(() => {
        this.submitting = true
        const data = this.getFormData()
        return updateConfig(data).then(() => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
        }).finally(() => {
          this.submitting = false
        })
      })
    },
    intranetIpValidator(rule, value, callback) {
      if (!value) {
        callback()
        return
      }
      const fn = () => {
        console.warn('check console server ipv4s')
        for (let i = 0; i < this.ipv4s.length; i++) {
          if (this.ipv4s[i].ipv4 === value) {
            callback()
            return
          }
        }
        callback(new Error(this.$t('pages.globalConfig_Msg4')))
      }
      if (this.initialized) {
        fn()
      } else {
        this.initPromise.then(fn)
      }
    },
    extranetIpValidator(rule, value, callback) {
      if (value === '0.0.0.0' || value === '127.0.0.1' || value === 'localhost') {
        callback(this.$t('pages.globalConfig_Msg6', { ip: value }))
      } else {
        callback()
      }
    },
    extranetPortValidator(rule, value, callback) {
      if (value == null || value === '') {
        callback(this.$t('pages.globalConfig_Msg9'))
        return
      }
      if (!/^[1-9]\d*$/.test(value)) {
        callback(this.$t('pages.globalConfig_Msg8'))
        return
      }
      const port = Number(value)
      if (isNaN(port)) {
        callback(this.$t('pages.globalConfig_Msg8'))
        return
      }
      if (port > 0 && port <= 65535) {
        callback()
        return
      }
      callback(this.$t('pages.globalConfig_Msg10'))
    },
    handlePortInput(value) {
      const port = onlyInt(value)
      if (port.length > 1) {
        const portInt = parseInt(port)
        if (portInt > 65535) {
          this.temp.consoleExtranetPort = 65535
        } else {
          this.temp.consoleExtranetPort = portInt
        }
      } else {
        this.temp.consoleExtranetPort = port
      }
    },
    showErrorMessage(message) {
      this.validateMessageInstance = this.$message({
        message,
        type: 'error',
        duration: 2000,
        onClose: () => {
          this.validateMessageInstance = undefined
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .addr-config {
    .el-input, .el-autocomplete {
      width: 288px;
    }
  }
</style>
