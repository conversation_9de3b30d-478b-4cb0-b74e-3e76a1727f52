<template>
  <div class="setting-box">
    <label>{{ $t('pages.fileSumSize') }}</label>
    <file-size-input v-model="fileSumSize"/>
    <el-button v-if="!hideSave" type="primary" size="mini" :loading="submitting" :disabled="!fileSumSize" @click="updateConfig">
      {{ $t('button.save') }}
    </el-button>
  </div>
</template>

<script>
import FileSizeInput from './fileSizeInput'
import { updateConfig } from '@/api/assets/systemMaintenance/explorer'
import { getExtractFileConfig } from '@/api/system/configManage/globalConfig'

export default {
  name: 'TermFileExtract',
  components: { FileSizeInput },
  props: {
    hideSave: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileSumSize: undefined,
      submitting: false
    }
  },
  created() {
    this.initConfig()
  },
  methods: {
    initConfig() {
      getExtractFileConfig().then(respond => {
        const configs = respond.data || []
        for (let i = 0; i < configs.length; i++) {
          if (configs[i].key === 'fileSumSize' && configs[i].value) {
            this.fileSumSize = parseInt(configs[i].value)
            return
          }
        }
        this.fileSumSize = undefined
      })
    },
    updateConfig() {
      if (!this.fileSumSize) {
        const err = this.$t('text.cantNullInfo', { info: this.$t('pages.fileSumSize') })
        this.$message({
          message: err,
          type: 'error',
          duration: 2000
        })
        return Promise.reject(err)
      }
      this.submitting = true
      const data = [{ key: 'fileSumSize', value: this.fileSumSize }]
      return updateConfig(data).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).finally(() => {
        this.submitting = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .setting-box {
    height: 60px;
    label {
      display: inline-block;
      font-size: 14px;
    }
    .input-file-size {
      margin: 0 10px;
      >>>.el-input-group__append {
        background-color: #F5F7FA;
        div.el-select {
          width: inherit;
          .el-input__inner {
            border-color: transparent;
            background-color: transparent;
            border-top: 0;
            border-bottom: 0;
          }
        }
      }
    }
  }
</style>
