<!--
  下载管理器，控制台右上角下载图标及下载列表展示
-->
<template>
  <el-popover v-model="visible" trigger="manual" placement="bottom" popper-class="download-popover">
    <div class="download-box">
      <div v-if="showSearch" class="download-header">
        <div class="download-header__name" :title="$t('components.exitSearch')" @click="exitSearch">
          <i class="el-icon-back"></i>
        </div>
        <div class="download-header__search">
          <el-input ref="input" v-model="keyword" prefix-icon="el-icon-search" clearable :placeholder="$t('components.searchDownloadItems')"/>
        </div>
      </div>
      <div v-else-if="showConfirm" class="download-header">
        <div class="download-header__confirm">
          {{ $t('components.whetherToDeleteAllDownloadHistory') }}
        </div>
      </div>
      <div v-else class="download-header">
        <!--<div class="download-header__name" :title="$t('route.openDownloadPage')" @click="openDownloadHistory">
          {{ $t('components.download') }}
        </div>-->
        <div class="download-header__name">{{ $t('components.download') }}</div>
        <div class="download-header__tool">
          <el-checkbox v-show="false" :value="downloadLogEnabled" @click.native.prevent="enableLog">
            {{ $t('pages.serverlog_debug_open') }}
          </el-checkbox>
          <i class="el-icon-search" :title="$t('components.searchDownloadItems')" @click="enterSearch"></i>
          <!--<i class="el-icon-setting" :title="$t('route.downloadSetting')" @click="openSetting"></i>-->
          <i class="el-icon-delete" :title="$t('components.clearDownloadHistory')" @click="enterConfirm"></i>
        </div>
      </div>
      <hr>
      <div v-if="showConfirm" class="download-body">
        <div class="download-body__text">{{ $t('components.stillAccessDownloadedLocalFile') }}</div>
        <div class="download-body__buttons">
          <el-button size="mini" type="primary" @click="clearHistories">{{ $t('components.deleteAll') }}</el-button>
          <el-button size="mini" @click="exitConfirm">{{ $t('button.cancel') }}</el-button>
        </div>
      </div>
      <div v-else class="download-body" :style="downloadBodyStyle">
        <div v-if="!!keyword && searchedFiles.length === 0">{{ $t('components.noSearchResults') }}</div>
        <download-item v-for="(item, index) in displayFiles" :key="index" :item="item" :keyword="keyword"/>
        <div v-if="showSeeMoreBtn" class="download-body__more">
          <el-button type="text" @click="showMore = true">{{ $t('components.seeMore') }}</el-button>
        </div>
      </div>
    </div>

    <el-tooltip slot="reference" class="item" effect="light" :content="$t('components.download')" placement="bottom">
      <i class="el-icon-download" @click="togglePopover"></i>
    </el-tooltip>
  </el-popover>
</template>

<script>
import { mapGetters } from 'vuex'
import DownloadItem from './item'
import { isDownloadFileRemovable, logDownloadMessage } from '@/utils/download/helper';
import { debounce } from '@/utils'

export default {
  name: 'DownloadManager',
  components: { DownloadItem },
  data() {
    return {
      visible: false,
      height: undefined,
      showSearch: false,
      showConfirm: false,
      showMore: false,
      showLimit: 5,
      keyword: undefined
    }
  },
  computed: {
    ...mapGetters(['downloadFiles', 'downloadLogEnabled', 'downloadListVisible']),
    searchedFiles() {
      if (!this.keyword) {
        return this.downloadFiles
      }
      return this.downloadFiles.filter(item => item.name.indexOf(this.keyword) >= 0)
    },
    showSeeMoreBtn() {
      return this.searchedFiles.length > this.showLimit && !this.showMore
    },
    displayFiles() {
      if (this.showSeeMoreBtn) {
        return this.searchedFiles.slice(0, this.showLimit)
      }
      return this.searchedFiles
    },
    downloadBodyStyle() {
      if (this.showSeeMoreBtn) {
        return null
      }
      const filesHeight = this.displayFiles.length * 46
      if (filesHeight < this.height) {
        return null
      }
      return `height: ${this.height}px; overflow: auto; margin-right: -8px; scrollbar-width: thin;`
    }
  },
  watch: {
    visible(val) {
      logDownloadMessage(() => ['[download] watch visible: ', val], 'trace')
      if (val) {
        this.exitSearch()
        this.exitConfirm()
        this.showMore = false
      }
      this.$store.dispatch('downloader/visible', val)
    },
    downloadListVisible(val) {
      logDownloadMessage(() => ['[download] watch downloadListVisible: ', val], 'trace')
      if (val) {
        this.visible = true
      }
    }
  },
  mounted() {
    // 防抖
    this.__resizeHandler = debounce(() => {
      this.calcDownloadBodyHeight()
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)
  },
  created() {
    this.downloadFileTimeoutJob()
    this.calcDownloadBodyHeight()
    document.addEventListener('click', this.closePopover)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.__resizeHandler)
  },
  destroyed() {
    document.removeEventListener('click', this.closePopover)
  },
  methods: {
    downloadFileTimeoutJob() {
      setInterval(() => {
        const now = Date.now()
        this.downloadFiles.forEach(file => {
          if (isDownloadFileRemovable(file)) {
            return
          }
          const mtime = file.mtime || 0
          if (now - mtime <= 1800000) {
            return
          }
          if (!file.error) {
            file.error = this.$t('text.requestTimeout')
          }
          if (typeof file.abort === 'function') {
            file.abort()
          }
          file.canceled = true
        })
      }, 10000)
    },
    calcDownloadBodyHeight() {
      this.height = (document.documentElement.clientHeight || document.body.clientHeight) - 135
    },
    closePopover(event) {
      logDownloadMessage(() => ['[download] closePopover', event], 'trace')
      this.visible = false
    },
    togglePopover(event) {
      logDownloadMessage('[download] togglePopover', 'trace')
      this.visible = !this.visible
      event.preventDefault()
      event.stopPropagation()
    },
    openDownloadHistory() {
      this.$router.push({ name: 'DownloadHistory' })
    },
    enableLog() {
      this.$store.dispatch('downloader/enableLog', !this.downloadLogEnabled)
    },
    enterSearch() {
      this.showSearch = true
      this.$nextTick(() => {
        this.$refs.input.focus()
      })
    },
    exitSearch() {
      this.showSearch = false
      this.keyword = undefined
    },
    openSetting() {
      this.$router.push({ name: 'DownloadSetting' })
    },
    enterConfirm() {
      this.showConfirm = true
    },
    exitConfirm() {
      this.showConfirm = false
    },
    clearHistories() {
      this.$store.dispatch('downloader/reset', this.downloadFiles.filter(item => !isDownloadFileRemovable(item)))
      this.exitConfirm()
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-icon-download {
    cursor: pointer;
  }
  .download-box {
    width: 300px;
    overflow-wrap: break-word;
    .download-header {
      >div {
        display: inline-block;
        height: 30px;
        line-height: 30px;
      }
      .download-header__name {
        /*cursor: pointer;*/
        cursor: default;
        color: #333333;
        font-weight: 700;
        padding: 0 5px;
        /*&:hover {
          background: #e5e5e5;
        }*/
      }
      .download-header__tool {
        float: right;
        font-size: 16px;
        [class^=el-icon-] {
          cursor: pointer;
          padding: 5px;
          &:hover {
            background: #e5e5e5;
          }
        }
      }
      .download-header__search {
        width: 270px;
      }
      .download-header__confirm {
        color: #333333;
        font-size: 21px;
        font-weight: 700;
      }
    }
    .download-body {
      min-height: 80px;
      .download-body__more {
        .el-button {
          padding: 5px 5px 0;
          &:hover {
            color: #66b1ff;
            >>>span {
              border-bottom: #66b1ff 1px solid;
            }
          }
        }
      }
      .download-body__text {
        line-height: 30px;
      }
      .download-body__buttons {
        margin-top: 15px;
        .el-button {
          width: 142px;
          font-size: 14px;
        }
      }
    }
  }
  /* 隐藏滚动条，但仍能保持滚动 */
  .scrollbar-hidden {
    &::-webkit-scrollbar {
      display: none; /* Chrome、Safari and Opera */
    }
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
</style>
