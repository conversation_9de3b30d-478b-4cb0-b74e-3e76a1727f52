<template>
  <div class="history-body">
    <div v-for="(item, index) in histories" :key="index" class="download-item">
      <div class="download-item__icon">
        <svg-icon :icon-class="item.icon"/>
      </div>
      <div class="download-item__info">
        <span class="download-item__name">{{ item.name }}</span>
        <span>{{ item.time }}</span>
        <span>{{ item.status }}</span>
      </div>
      <div class="download-item__cancel">
        <i class="el-icon-close" title="取消"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DownloadHistory',
  data() {
    return {
      histories: [
        { icon: 'docx', name: '123.zip', time: '2023-1-2', status: '已完成' },
        { icon: 'iso', name: '123.zip', time: '2023-1-2', status: '已取消' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
  .history-body{
    width: 70%;
    height: 100%;
    margin: auto;
    .download-item{
      position: relative;
      width: 100%;
      height: 105px;
      border: 1px solid #2674b2;
      border-radius: 5px;
      margin-top: 20px;
      .download-item__icon{
        display: inline-block;
        width: 95px;
        height: 95px;
        padding: 5px;
        >>>.svg-icon{
          width: 100%;
          height: 100%;
        }
      }
      .download-item__info{
        display: inline-block;
        margin-left: 15px;
        .download-item__name{
          font-weight: bold;
        }
        span{
          display: block;
          height: 25px;
          line-height: 25px;
        }
      }
      .download-item__cancel{
        display: inline-block;
        position: absolute;
        width: 30px;
        height: 30px;
        /* background: pink; */
        text-align: center;
        line-height: 30px;
        /*border-radius: 15px;*/
        cursor: pointer;
        right: 5px;
        top: 5px;
        &:hover{
          background: #295577;
          color: #ffffff;
          cursor: pointer;
        }
      }
    }
  }

</style>
