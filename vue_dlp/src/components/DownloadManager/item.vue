<template>
  <div class="download-item">
    <div class="download-item__icon">
      <svg-icon :icon-class="item.icon"/>
    </div>
    <div class="download-item__info">
      <div class="download-item__name" :title="item.name" v-html="highlightFileName(item.name)"/>
      <div class="download-item__progress">
        <div class="el-progress-bar__outer" :title="item.progress">
          <div class="el-progress-bar__inner" :style="barStyle"/>
        </div>
        <div class="download-item__status">
          <div class="download-item__status-left" :style="{ color }" :title="status">{{ status }}</div>
          <div class="download-item__status-right">{{ item.progress }}</div>
        </div>
      </div>
    </div>
    <div class="download-item__cancel">
      <i v-if="removable" class="el-icon-delete" :title="$t('components.deleteRecord')" @click="removeHistory(item)"/>
      <i v-else class="el-icon-close" :title="$t('components.cancelDownload')" @click="abortDownload(item)"/>
    </div>
  </div>
</template>

<script>
import { formatDownloadFileStatus, getDownloadFileColor, isDownloadFileRemovable } from '@/utils/download/helper'

export default {
  name: 'DownloadItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    keyword: {
      type: String,
      default: undefined
    }
  },
  computed: {
    color() {
      return getDownloadFileColor(this.item)
    },
    status() {
      return formatDownloadFileStatus(this.item)
    },
    barStyle() {
      return { width: this.item.progress, backgroundColor: this.color }
    },
    removable() {
      return isDownloadFileRemovable(this.item)
    }
  },
  methods: {
    highlightFileName(filename) {
      if (!filename || !this.keyword) {
        return this.escapeChars(filename)
      }
      const reg = new RegExp(this.escapeChars(this.keyword), 'ig')
      return this.escapeChars(filename).replace(reg, word => `<span class="filename-highlight">${word}</span>`)
    },
    // 转义
    escapeChars(content) {
      if (!content) {
        return ''
      }
      return content.replace(/&/ig, '&amp;').replace(/</ig, '&lt;').replace(/>/ig, '&gt;')
    },
    removeHistory(item) {
      this.$store.dispatch('downloader/remove', item)
    },
    abortDownload(item) {
      if (typeof item.abort === 'function') {
        item.abort()
      }
      item.canceled = true
    }
  }
}
</script>

<style lang="scss" scoped>
  .download-item {
    height: 46px;
    padding: 0 5px;
    position: relative;
    &:hover {
      background: #f2f2f2;
      .download-item__cancel {
        display: block;
      }
    }
    .download-item__icon {
      display: inline-block;
      width: 16px;
      margin: 14px 3px 0 1px;
      position: relative;
      >>>.svg-icon {
        width: 16px;
        height: 16px;
      }
    }
    .download-item__info {
      cursor: default;
      display: inline-block;
      /*width: 265px;*/
      width: calc(100% - 25px);
      vertical-align: top;
      .download-item__name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .download-item__progress {
        .el-progress-bar__outer {
          height: 4px;
        }
        .download-item__status {
          width: 100%;
          height: 18px;
          line-height: 18px;
          font-size: 12px;
          display: inline-block;
          vertical-align: middle;
          div {
            display: inline-block;
          }
          .download-item__status-left {
            width: 100%;
            padding-right: 30px;
            margin-right: -40px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .download-item__status-right {
            float: right;
          }
        }
      }
      .download-item__name, .download-item__progress {
        height: 22px;
        line-height: 20px;
      }
      >>>.filename-highlight {
        background: #ffeb3b;
      }
    }
    .download-item__cancel {
      display: none;
      background: linear-gradient(to right, transparent, #f2f2f2 10%);
      width: 50px;
      height: 44px;
      line-height: 44px;
      text-align: center;
      font-size: 16px;
      position: absolute;
      right: 0;
      top: 0;
      &:hover [class^=el-icon-] {
        background: #e5e5e5;
        padding: 8px 10px;
        cursor: pointer;
      }
    }
  }
</style>
