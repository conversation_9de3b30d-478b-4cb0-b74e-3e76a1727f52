<!--
  统一下载通用下载器

  <common-downloader
    float="none"                              // 组件浮动样式，默认“none”
    :loading="loading"                        // 是否加载中状态，默认false
    :disabled="disabled"                      // 是否禁用下载，默认false
    :name="getFilename"                       // 下载文件名（带后缀名），可为字符串或函数（函数必须返回一个带后缀名的文件名，使用函数的常见场景为文件名包含实时日期）
    topic="terminal"                          // 下载主题分类，默认使用页面的路由名称，一般不必写此属性
    :steps="3"                                // 下载过程总共有几个步骤（默认一个步骤：下载）
    :show-button="showButton"                 // 是否显示下载按钮，默认true
    :button-name="$t('button.export')"        // 下载按钮名称，默认“下载”
    :button-type="buttonType"                 // 下载按钮类型，默认为空，可选值：<NUL>/primary/success/warning/danger/info/text
    :button-size="buttonSize"                 // 下载按钮尺寸，默认为空，可选值：<NUL>/medium/small/mini
    :button-icon="buttonIcon"                 // 下载按钮图标类名，默认“el-icon-download”，该属性为空时不显示图标
    :before-download="beforeDownload"         // 下载前校验函数，返回值类型为Boolean或Promise，若返回false或返回Promise.reject()，则不触发下载事件
    @download="handleDownload"                // 下载事件处理，Function(file)，file为当前下载文件信息
  />
-->
<template>
  <download-executor
    :float="float"
    :loading="loading"
    :disabled="disabled"
    :show-button="showButton"
    :button-name="buttonName"
    :button-type="buttonType"
    :button-size="buttonSize"
    :button-icon="buttonIcon"
    :before-download="beforeDownload"
    @download="handleDownload"
  />
</template>

<script>
import DownloadExecutor from './executor'
import { buildDownloadFileByName } from '@/utils/download/helper'

export default {
  name: 'CommonDownloader',
  components: { DownloadExecutor },
  props: {
    float: {
      type: String,
      default: undefined
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    name: {
      type: [String, Function],
      default() {
        const label = this.$route.meta.title
        const hasKey = this.$te(`route.${label}`);
        return hasKey ? this.$t(`route.${label}`) : label;
      }
    },
    suffix: {
      type: String,
      default: undefined
    },
    topic: {
      type: String,
      default() {
        return this.$route.name
      }
    },
    steps: {
      type: Number,
      default: 1
    },
    showButton: {
      type: Boolean,
      default: true
    },
    buttonName: {
      type: String,
      default() {
        return this.$t('components.download')
      }
    },
    buttonType: {
      type: String,
      default: ''
    },
    buttonSize: {
      type: String,
      default: ''
    },
    buttonIcon: {
      type: String,
      default: 'el-icon-download'
    },
    beforeDownload: {
      type: Function,
      default() {
        return true
      }
    }
  },
  methods: {
    handleDownload() {
      let filename = typeof this.name === 'function' ? this.name() : this.name
      if (this.suffix) {
        if (this.suffix.charAt(0) !== '.') {
          filename += '.'
        }
        filename += this.suffix
      }
      const downloadFile = buildDownloadFileByName(filename)
      downloadFile.steps = this.steps
      this.$emit('download', downloadFile)
    }
  }
}
</script>
