<!--
  下载执行器，用于下载资源管理器文件、终端日志、服务器日志和审计日志导出、审计日志备份文件下载等

  <download-executor
    float="none"                              // 组件浮动样式，默认“none”
    :loading="loading"                        // 是否加载中状态，默认false
    :disabled="disabled"                      // 是否禁用下载，默认false
    :show-zip-mode="showed"                   // 是否显示压缩复选框，默认false
    :show-button="showButton"                 // 是否显示下载按钮，默认true
    :button-name="buttonName"                 // 下载按钮名称，默认“下载”
    :button-type="buttonType"                 // 下载按钮类型，默认为空，可选值：<NUL>/primary/success/warning/danger/info/text
    :button-size="buttonSize"                 // 下载按钮尺寸，默认为空，可选值：<NUL>/medium/small/mini
    :button-icon="buttonIcon"                 // 下载按钮图标类名，默认“el-icon-download”
    :need-check-address="false"               // 是否需要检测控制台IP地址配置，默认不需要
    @download="handleDownload"                // 下载事件回调, function(zipped)
  />
-->
<template>
  <div class="download-executor" :style="{ float }">
    <el-checkbox v-show="showZipMode" v-model="zipped">
      {{ $t('components.compress') }}
    </el-checkbox>
    <el-button
      v-show="showButton"
      :loading="loading || downloading"
      :disabled="disabled"
      :type="buttonType"
      :size="buttonSize"
      :icon="buttonIcon"
      :title="buttonTitle"
      class="ellipsis"
      @click.stop="handleDownload"
    >
      {{ buttonName }}
    </el-button>

    <console-address-dialog ref="addrDlg" :append-to-body="appendToBody" @updated="emitDownloadEvent"/>
  </div>
</template>

<script>
import ConsoleAddressDialog from '@/components/DownloadManager/setting/consoleAddressDlg'
import { checkIntranetIp } from '@/api/assets/systemMaintenance/explorer'

export default {
  name: 'DownloadExecutor',
  components: { ConsoleAddressDialog },
  props: {
    appendToBody: { type: Boolean, default: false },
    float: {
      type: String,
      default: undefined
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showZipMode: {
      type: Boolean,
      default: false
    },
    showButton: {
      type: Boolean,
      default: true
    },
    buttonName: {
      type: String,
      default() {
        return this.$t('components.download')
      }
    },
    buttonTitle: {
      type: String,
      default() {
        return this.buttonName
      }
    },
    buttonType: {
      type: String,
      default: ''
    },
    buttonSize: {
      type: String,
      default: ''
    },
    buttonIcon: {
      type: String,
      default: 'el-icon-download'
    },
    needCheckAddress: {
      type: Boolean,
      default: false
    },
    beforeDownload: {
      type: Function,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      zipped: false,
      downloading: false,
      clickTime: 0
    }
  },
  methods: {
    handleDownload() {
      const now = Date.now();
      if (now - this.clickTime < 3000) {
        console.log('repeat click...............')
        return
      }
      this.clickTime = now
      const downloadFunc = () => {
        this.downloading = true
        if (!this.needCheckAddress) {
          this.emitDownloadEvent()
          this.downloading = false
          return
        }
        checkIntranetIp().then(res => {
          if (res.data) {
            this.emitDownloadEvent()
          } else {
            this.$alert(this.$t('components.consoleServerAddressConfigTips'), this.$t('text.prompt'), {
              confirmButtonText: this.$t('button.confirm2'),
              type: 'warning',
              callback: action => {
                if (action === 'confirm') {
                  this.$refs.addrDlg.show()
                }
              }
            })
          }
        }).finally(() => {
          this.downloading = false
        })
      }
      const result = typeof this.beforeDownload === 'function' ? this.beforeDownload(this.zipped) : true
      if (result instanceof Promise) {
        result.then(downloadFunc).catch(() => {})
        return
      }
      if (result) {
        downloadFunc()
      }
    },
    emitDownloadEvent() {
      this.$emit('download', this.zipped)
    }
  }
}
</script>

<style lang="scss" scoped>
  .download-executor {
    display: inline-block;
    margin-left: 10px;
    z-index: 1;
    .el-checkbox {
      margin-right: 0;
    }
  }
</style>
