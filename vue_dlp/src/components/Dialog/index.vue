<!--
    /**
     * 封装 el-dialog
     * 调用示例：
        <Dialog
          ref="dialog"
          :title="title"                              // Dialog 的标题，也可通过具名 slot='title'传入
          :visible.sync="visible"                     // 是否显示 Dialog，支持 .sync 修饰符
          :modal="modal"                              // 是否需要遮罩层，默认false
          :modal-append-to-body="modalAppendToBody"   // 遮罩层是否插入至 body 元素上，若为 false，则遮罩层会插入至 Dialog 的父元素上
          :append-to-body="appendToBody"              // Dialog 自身是否插入至 body 元素上，默认false
          :before-close="beforeClose"                 // 关闭前的回调，会暂停 Dialog 的关闭.function(done)，done 用于关闭 Dialog
          :custom-class="customClass"                 // Dialog 的自定义类名
          :with-footer="withFooter"                   // 控制是否显示 footer 栏, 默认为 true
          ----------- 以下配置在使用内置的 footer 按钮提交数据时才需要配置 -----------
          :status="status"                            // 弹窗的状态，create 新增，update 修改
          :submitting.sync="submitting"               // 是否正在提交数据
          :create-data="createData"                   // 新增数据的方法，返回执行结果（Promise 或 Boolean）
          :update-data="updateData"                   // 修改数据的方法，返回执行结果（Promise 或 Boolean）
        >

          ...内容

          <div slot="footer">                         // 如果 withFooter 为 true，且不使用 slot="footer"，则会使用内置的 footer，需要配置 createData、updateData 方法
            <el-button>按钮</el-button>
          </div>
        </Dialog>
    */
-->
<template>
  <el-dialog
    ref="dialog"
    :title="title"
    :modal="modal"
    :fullscreen="dialogFullscreen"
    :modal-append-to-body="modalAppendToBody"
    :append-to-body="appendToBody"
    :close-on-press-escape="false"
    :custom-class="dialogClass"
    :visible.sync="dialogVisible"
    :before-close="beforeClose"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <!-- title的slot -->
    <template slot="title">
      <slot name="title"></slot>
    </template>

    <!-- body的slot -->
    <slot></slot>

    <!-- footer的slot -->
    <div v-if="withFooter" slot="footer" >
      <!-- 若外部没有传入slot，则使用内置的按钮 -->
      <slot name="footer" class="dialog-footer">
        <!-- 自定义按钮插槽 -->
        <slot name="btn-group" />
        <!-- 确认按钮 -->
        <el-button type="primary" :loading="submitting" @click="handleConfirm">{{ $t('button.confirm') }}</el-button>
        <!-- 取消按钮 -->
        <el-button @click="handleCancel">{{ $t('button.cancel') }}</el-button>
      </slot>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'Dialog',
  props: {
    title: { type: String, default: '' },                        // Dialog 的标题，也可通过具名 slot='title'传入
    visible: { type: Boolean, default: false },                  // 是否显示 Dialog，支持 .sync 修饰符
    modal: { type: Boolean, default: false },                    // 是否需要遮罩层
    fullscreen: { type: Boolean, default: false },               // 是否显示 Dialog，支持 .sync 修饰符
    modalAppendToBody: { type: Boolean, default: false },        // 遮罩层是否插入至 body 元素上，若为 false，则遮罩层会插入至 Dialog 的父元素上
    appendToBody: { type: Boolean, default: false },             // Dialog 自身是否插入至 body 元素上
    beforeClose: { type: Function, default: null },              // 关闭前的回调，会暂停 Dialog 的关闭.function(done)，done 用于关闭 Dialog
    customClass: { type: String, default: '' },                  // Dialog 的自定义类名
    withFooter: { type: Boolean, default: true },                // 控制是否显示 footer 栏, 默认为 true
    status: { type: String, default: 'create' },                 // 弹窗的状态，create 新增，update 修改
    submitting: { type: Boolean, default: false },               // 是否正在提交数据
    createData: { type: Function, default: null },               // 新增数据的方法，返回执行结果（Promise 或 Boolean）
    updateData: { type: Function, default: null }                // 修改数据的方法，返回执行结果（Promise 或 Boolean）
  },
  data() {
    return {
      dialogFullscreen: false,
      dialogVisible: false                     // 是否显示 Dialog
    }
  },
  computed: {
    // Dialog 的自定义类名
    dialogClass() {
      const classArray = this.withFooter ? ['with-footer'] : []
      const newClassArr = classArray.concat(this.customClass.split(' ')).filter(Boolean)
      return newClassArr.join(' ')
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    fullscreen(val) {
      this.dialogFullscreen = val;
    },
    dialogFullscreen(val) {
      this.$emit('update:fullscreen', val)
    }
  },
  created() {
  },
  methods: {
    // 内置确认按钮的方法，需要外部传入 createData, updateData 方法才能正常工作
    handleConfirm() {
      const confirmFunc = {
        create: this.createData,
        update: this.updateData
      }[this.status]
      if (typeof confirmFunc != 'function') return
      this.$emit('update:submitting', true);
      const result = confirmFunc();
      if (result) {
        if (result instanceof Promise) {
          result.then(valid => {
            this.$emit('update:submitting', false)
          }).catch(() => {
            this.$emit('update:submitting', false)
          })
        } else {
          this.$emit('update:submitting', false)
        }
      } else {
        this.$emit('update:submitting', false)
      }
    },
    // 调用关闭方法
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.with-footer .el-drawer__body {
    margin-bottom: 60px;
  }
</style>
