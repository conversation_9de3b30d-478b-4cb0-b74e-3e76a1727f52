<!--
  /**
  * svg图标
  * 调用示例：
    <svg-icon
      icon-class="star"       // svg文件名，文件路径 src/icons/svg
      class-name="className"  // 可添加其他类名,支持string 和 object 类型：{class1:true, class2:true}
      title="tips"            // 鼠标悬停提示信息
    />
  */
-->
<template>
  <div v-if="isExternal" :style="styleExternalIcon" class="svg-external-icon svg-icon" :title="title" v-on="$listeners"/>
  <svg v-else :class="svgClass" aria-hidden="true" v-on="$listeners">
    <use :xlink:href="iconName">
      <title v-if="title">{{ title }}</title>
    </use>
  </svg>
</template>

<script>
// doc: https://panjiachen.github.io/vue-element-admin-site/feature/component/svg-icon.html#usage
import { isExternal } from '@/utils/validate'

export default {
  name: 'SvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: [String, Object],
      default: ''
    },
    title: {
      type: String,
      default: undefined
    }
  },
  computed: {
    isExternal() {
      return isExternal(this.iconClass)
    },
    iconName() {
      return `#icon-${this.iconClass}`
    },
    svgClass() {
      if (this.className) {
        if (typeof this.className == 'string') {
          return 'svg-icon ' + this.className
        } else {
          return { 'svg-icon': true, ...this.className }
        }
      } else {
        return 'svg-icon'
      }
    },
    styleExternalIcon() {
      return {
        mask: `url(${this.iconClass}) no-repeat 50% 50%`,
        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.svg-icon {
  width: 1.1em;
  height: 1.1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.svg-external-icon {
  background-color: currentColor;
  mask-size: cover!important;
  display: inline-block;
}
</style>
