<template>
  <el-table
    :ref="tableRef"
    :key="keyRandom()"
    v-loading="loading"
    v-table-scroll="loadMoreData"
    :data="rowData"
    :default-sort="defaultSort"
    :height="height"
    :row-key="rowKey"
    :stripe="stripe"
    :border="border"
    :size="size"
    :row-class-name="rowClassName"
    :cell-class-name="cellClassName"
    :cell-style="cellStyle"
    fit
    :default-expand-all="defaultExpandAll"
    highlight-current-row
    tooltip-effect="light"
    :style="tableStyle"
    @sort-change="sortChange"
    @select="select"
    @select-all="selectAll"
    @selection-change="selectionChange"
    @current-change="currentChange"
    @expand-change="expandChange"
    @row-dblclick="rowDblclick"
    @row-click="rowClick"
    @cell-click="cellClick"
  >
    <!-- 显示为一个可展开的按钮 -->
    <el-table-column v-if="expandable" type="expand" width="40" :fixed="true">
      <slot slot-scope="scope" name="expand" :rowData="scope.row" />
    </el-table-column>
    <!-- 显示多选框 -->
    <el-table-column v-if="multiSelect" type="selection" width="40" :selectable="selectable" :fixed="expandable" ></el-table-column>
    <!-- 显示该行的索引（从 1 开始计算） -->
    <el-table-column v-if="rowNoLabel" type="index" :index="indexMethod" width="50" >
      <template slot="header">
        <span :title="rowNoLabel">{{ rowNoLabel }}</span>
      </template>
    </el-table-column>
    <el-table-column
      v-for="(col, colIndex) in colModel"
      :key="col.prop"
      :prop="col.prop"
      :label="labelFormat(col.label)"
      :fixed="col.fixed"
      :min-width="col.width"
      :width="widthFormat(col, col.fixedWidth)"
      :class-name="classNmaeFormat(col)"
      :sortable="sortable && col.sort"
      :sort-orders="['descending', 'ascending']"
      :render-header="col.renderHeader"
      :sort-method="(a, b) => defaultSortMethod(a, b, col)"
      :show-overflow-tooltip="ellipsisFormat(col, col.ellipsis)"
    >
      <template slot="header" slot-scope="scope">
        <span v-if="col.headerType === 'checkbox'">
          <el-checkbox v-model="col.headerChecked" @change="(v) => { return typeof col.headerCheckboxChange == 'function' ? col.headerCheckboxChange(v, scope, col) : false }"></el-checkbox>
        </span>
        <span :title="headerLabelFormat(col, colIndex)">{{ headerLabelFormat(col, colIndex) }}</span>
        <svg-icon v-if="headIconShow(scope, col)" :icon-class="col.headIcon" class="custom-icon" @click="col.headIconClick"></svg-icon>
      </template>
      <template slot-scope="scope">
        <span v-for="(icon,index) in iconArrayFormat(col, scope.row)" :key="'icon_' + index" :title="icon.title" :style="Object.assign({ 'margin-right': '3px' }, icon.css)">
          <img v-if="icon.imgIcon" :src="icon.src" :alt="icon.alt" :style="icon.style" :class="`img-icon ${icon.class}`">
          <svg-icon v-if="!icon.imgIcon" :icon-class="icon.class || icon" :class="icon.className" :style="icon.style" @click="handleIcon(scope.row, icon.class || icon)"/>
        </span>

        <!-- popover -->
        <el-popover v-if="col.type === 'popover'" v-bind="col.attributes" :placement="col.placement || 'left'" :open-delay="200" :title="labelFormat(col.title || col.label)" :width="col.popoverWidth" trigger="hover">
          <!--作用域插槽的子组件-->
          <slot name="popoverContent" :detail="col.originData ? scope.row : scope.row[col.childData || col.prop]"></slot>
          <span slot="reference" v-html="tableColDataFormat(col, scope.row)"></span>
        </el-popover>

        <!-- copiable-->
        <pre v-if="col.type === 'copiable'" @copy="col.copy">{{ scope.row[col.prop] }}</pre>

        <!-- text -->
        <span
          v-if="!col.type || col.type === 'text'"
          :style="{ color: textColorFormat(col, scope.row) }"
          @click="typeof col.click === 'function'? col.click(scope.row, scope.$index):()=>{}"
          v-html="tableColDataFormat(col, scope.row)"
        ></span>

        <!-- checkbox -->
        <span v-if="col.type === 'checkbox'">
          <i :class="checkboxClass(scope.row, col)" @click="checkboxChange(scope.row[col.prop], scope.row, col)"></i>
        </span>

        <!-- switch -->
        <span v-if="col.type === 'switch'">
          <el-switch
            v-model="scope.row[col.prop]"
            v-bind="col.attributes"
            :disabled="disabledFunc(col, scope.row)"
            :active-value="typeof col.active == 'undefined' ? true : col.active"
            :inactive-value="typeof col.inactive == 'undefined' ? false : col.inactive"
            @change="(v) => { return typeof col.switchChange == 'function' ? col.switchChange(v, scope.row, scope.$index) : false }"
          />
        </span>

        <!-- buttons -->
        <el-button
          v-for="(btn, index) in col.buttons"
          :key="'btn_' + index"
          :title="tableColBtnFormat(btn, scope.row)"
          :type="btn.type || 'text'"
          :class="{hidden: !showButton(btn, scope.row)}"
          :style="btnStyleFormat(col, btn)"
          :disabled="tableColBtnDisabledFormat(btn, scope.row)"
          :loading="tableColBtnLoadingFormat(btn, scope.row)"
          @click.stop="btn.click(scope.row)"
        >{{ tableColBtnFormat(btn, scope.row) }}</el-button>

        <!-- select -->
        <span v-if="col.type === 'select'">
          <span v-show="!col.alwaysEdit && !isColEdit(scope.row, col.prop)">
            <i
              v-show="(col.editIconShow === undefined || col.editIconShow === null ? true : col.editIconShow)
                && (typeof col.editIconShowFunc === 'function' ? col.editIconShowFunc(scope.row) : () => { return true })"
              class="el-icon-edit row-edit"
              @click="setColEdit(scope.row, col.prop, scope.$index)"
            />
            <span>{{ getSelectLabel(scope.row[col.prop], col, scope.row) }}</span>
          </span>
          <el-select
            v-if="col.alwaysEdit || isColEdit(scope.row, col.prop)"
            v-model="scope.row[col.prop]"
            v-bind="col.attributes"
            :multiple="col.multiple"
            :placeholder="$t('text.select')"
            :disabled="disabledFunc(col, rowData)"
            @change="typeof col.change === 'function'?col.change(scope.row, col, scope.$index):()=>{}"
          >
            <el-option
              v-for="item in ((col.rowOptions && scope.row[col.rowOptions]) || col.options)"
              :key="item.value"
              :label="labelFormat(item.label)"
              :value="item.value"
              :disabled="typeof col.optionDisabled === 'function' ? col.optionDisabled(item, scope.row, col) : false"
            >
              <div v-if="col.prop==='blockType'">
                <span style="display:inline-block;margin-right:5px">{{ item.label }}</span>
                <el-tooltip v-show="item.value!==0" class="item" effect="dark" placement="top-start">
                  <div slot="content">{{ item.tooltip }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </div>
            </el-option>
          </el-select>
        </span>

        <!-- treeSelect -->
        <span v-if="col.type === 'treeSelect'">
          <span v-show="!col.alwaysEdit && !isColEdit(scope.row, col.prop) && !disabledFunc(col, scope.row)">
            <i class="el-icon-edit row-edit" @click="setColEdit(scope.row, col.prop, scope.$index)"></i>
            <span>{{ typeof col.showContent === 'function'? col.showContent(scope.row) : getTreeSelectLabel(scope.row[col.checkedKeysFieldName], col) }}</span>
          </span>
          <tree-select
            v-if="col.alwaysEdit || isColEdit(scope.row, col.prop)"
            :ref="'treeComponent' + col.prop"
            v-bind="col.attributes"
            :data="col.treeData"
            :lazy="col.treeLazy"
            :load="col.treeLoad"
            :leaf-key="col.leafKey || scope.row['leafKey'] || undefined"
            :clearable="col.clearable"
            :disabled="disabledFunc(col, scope.row)"
            :is-filter="col.isFilter"
            :local-search="col.localSearch"
            :icon-option="col.iconOption"
            :node-key="col.nodeKey ? col.nodeKey : 'id'"
            :checked-keys="scope.row[col.checkedKeysFieldName] || []"
            :multiple="col.multiple || false"
            :check-strictly="col.checkStrictly || false"
            :filter-node-method="typeof col.filterNodeMethod === 'function' ? (value, data, node) => col.filterNodeMethod(value, data, node) : null"
            :rewrite-node-click-fuc="col.rewriteNodeClickFuc != false"
            :node-click-fuc="(data, node, vm)=> nodeChange(data, node, vm,scope.row,col)"
            @change="(keys, options) => typeof col.changeSelectedNodes === 'function' && col.changeSelectedNodes(scope.row, scope.$index, keys, options)"
          />
        </span>

        <!-- input -->
        <span v-if="col.type === 'input'">
          <template v-if="typeof col.showText === 'function' ? col.showText(col, scope.row) : col.showText">
            <span>{{ scope.row[col.prop] }}</span>
          </template>
          <template v-else>
            <span v-show="col.editMode && !isColEdit(scope.row, col.prop)">
              <i class="el-icon-edit row-edit" @click="setColEdit(scope.row, col.prop, scope.$index)"></i>
              <span>{{ scope.row[col.prop] }}</span>
            </span>
            <el-input
              v-if="!col.editMode || isColEdit(scope.row, col.prop)"
              v-model="scope.row[col.prop]"
              v-trim
              v-bind="col.attributes"
              :disabled="disabledFunc(col, scope.row)"
              :title="scope.row[col.prop]"
              :clearable="typeof col.clearable === 'boolean' ? col.clearable : true"
              :style="col.saveCell ? 'width: calc(100% - 20px);' : ''"
              :maxlength="col.maxlength ? col.maxlength : 255"
              @input="col.saveCell ? $set(scope.row, 'saveCellAble', true) : null"
              @change="value => inputChange(value, scope, col)"
            />
            <svg-icon
              v-if="col.saveCell"
              icon-class="save"
              :class="{ 'save-icon': true, disabled: !scope.row.saveCellAble}"
              @click="e => saveCell(col, scope.row)"
            />
          </template>
        </span>

        <!-- input-number -->
        <span v-if="col.type === 'input-number'">
          <template v-if="typeof col.showText === 'function' ? col.showText(col, scope.row) : col.showText">
            <span>{{ scope.row[col.prop] }}</span>
          </template>
          <template v-else>
            <span v-show="col.editMode && !isColEdit(scope.row, col.prop)">
              <i class="el-icon-edit row-edit" @click="setColEdit(scope.row, col.prop, scope.$index)"></i>
              <span>{{ scope.row[col.prop] }}</span>
            </span>
            <el-input-number
              v-if="!col.editMode || isColEdit(scope.row, col.prop)"
              v-model="scope.row[col.prop]"
              v-bind="col.attributes"
              :disabled="disabledFunc(col, scope.row)"
              :title="scope.row[col.prop]"
              :style="col.saveCell ? 'width: calc(100% - 20px);' : ''"
              :maxlength="col.maxlength ? col.maxlength : 255"
              :controls="false"
              :step="col.numberStep ? col.numberStep : 1"
              step-strictly
              :max="col.numberMax ? col.numberMax : 999"
              :min="col.numberMin ? col.numberMin : 1"
              @blur="event => inputNumberChange(event, scope, col)"
            />
          </template>
        </span>

        <!-- icon -->
        <svg-icon
          v-if="col.type === 'icon' && (scope.row[col.prop] || tableColIconFormat(col, scope.row)) == true"
          v-bind="col.attributes"
          :icon-class="col.iconClass"
          :style="col.style"
          @click="(e) => { return typeof col.click == 'function' ? col.click(e, scope.row, scope.$index) : false }"
        />

        <!-- img -->
        <img v-if="col.type === 'img'" v-bind="col.attributes" :src="scope.row[col.prop]" :alt="col.alt">

        <!-- showDetail -->
        <span v-if="col.type === 'showDetail'">
          <show-detail
            v-bind="col.attributes"
            :search-param="searchParamFormat(col.searchParam, scope.row)"
            :label="detailLabelFormat(col,scope.row)"
            :label-indent="col.labelIndent || 0"
            :search-type="col.searchType"
            :search-url="col.searchUrl"
            :child-data="scope.row[col.childData]"
          >
          </show-detail>
        </span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import ShowDetail from '@/components/ShowDetail';
import { debounce } from '@/utils'
import { setTheFirstLetterToUppercase } from '@/utils/i18n'

export default {
  name: 'TableTemp',
  directives: {
    tableScroll: {
      bind(el, binding, vnode) {
        // 组件实例
        const vm = vnode.componentInstance.$parent
        // 不分页的情况下，通过滚动加载数据
        if (!vm.paging) {
          // el-table 内容滚动区域
          const bodyWrap = el.querySelector('.el-table__body-wrapper')
          bodyWrap.addEventListener('scroll', debounce(function() {
            // 动态切换是否分页的情况下，已添加监听器时，需要再判断是否分页，分页则返回
            if (vm.paging) return
            // 数据总页数
            const totalPages = Math.ceil(vm.total / vm.limit)
            // 距离上下边界的距离
            const sign = 300
            // 离底部的距离
            const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight
            // 滚动方向，向下true， 向上false
            const direction = this.scrollTop - vm.oldScrollTop > 0
            // 当前页码
            const page = vm.page
            // 向下滚到到指定位置
            if (scrollDistance <= sign && direction) {
              // 这边因为是一次加载2页数据，所以page比总页数小1的时候，数据已全部加载完毕
              if (page + 1 < totalPages) {
                binding.value(1)
                bodyWrap.scrollTo(0, this.scrollHeight / 2 - this.clientHeight - scrollDistance)
              }
            }
            // 向上滚动到指定位置
            if (this.scrollTop <= sign && !direction) {
              if (page - 1 > 0) {
                binding.value(-1)
                let location = this.scrollHeight / 2 + this.scrollTop
                // 最后一页的数据可能比较少，需要对定位的位置重新计算
                if (page + 1 == totalPages) {
                  const extra = vm.total % vm.limit || vm.limit
                  location = this.scrollHeight / (vm.limit + extra) * vm.limit + this.scrollTop
                }
                bodyWrap.scrollTo(0, location)
              }
            }
            // 记录上次滚动后距离顶部的距离
            vm.oldScrollTop = this.scrollTop
          }, 10))
        }
      }
    }
  },
  components: { ShowDetail },
  props: {
    sortable: {
      type: Boolean,
      default: true
    },
    defaultSort: {
      type: Object,
      default: function() {
        return {};
      }
    },
    compareFunc: {
      type: Function,
      default: function(a, b) {
        return (a > b) ? 1 : (a < b) ? -1 : 0
      }
    },
    stripe: {
      type: Boolean,
      default: true
    },
    border: {
      type: Boolean,
      default: true
    },
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    size: {
      type: String,
      default: 'medium'
    },
    limit: {
      type: Number,
      default: 50
    },
    total: {
      type: Number,
      default: 0
    },
    page: {
      type: Number,
      default: 1
    },
    // 是否分页,不分页则通过滚动加载数据
    paging: {
      type: Boolean,
      default: false
    },
    multiSelect: {
      // 是否提供多选，即是否提供复选框
      type: Boolean,
      default: true
    },
    checkStrictly: {
      // 树结构数据，勾选行数据时，父子数据是否相关联，默认 false
      type: Boolean,
      default: false
    },
    rowNoLabel: {
      // 是否显示行号，并设置行号列表头名
      type: String,
      default: null
    },
    defaultExpandAll: {
      // 是否默认展开所有行数据
      type: Boolean,
      default: false
    },
    expandable: {
      // 是否可以展开
      type: Boolean,
      default: false
    },
    selectable: {
      type: Function,
      default: function() {
        return true;
      }
    },
    height: {
      type: String,
      default: ''
    },
    minHeight: {
      type: Number,
      default: 100
    },
    colModel: {
      // 列模板配置
      type: Array,
      default: function() {
        return [];
      }
    },
    // 是否支持表头发生变化后重新渲染
    headerRendering: {
      type: Boolean,
      default: false
    },
    customCol: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Array,
      default: function() {
        return [];
      }
    },
    rowClassName: {
      type: [Function, String],
      default: ''
    },
    cellClassName: {
      type: [Function, String],
      default: ''
    },
    cellStyle: {
      type: Function,
      default: function() {
        return '';
      }
    },
    loading: {
      type: Boolean,
      default: false
    },
    localLazy: { // 本地数据滚动翻页的情况
      type: Boolean,
      default: false
    },
    selectedData: { // 分页情况下，页面勾选的数据
      type: Array,
      default() {
        return []
      }
    },
    savedSelectedDatas: { // 跨页保存，或不分页的情况下，勾选的数据
      type: Array,
      default() {
        return []
      }
    }
  },
  data: function() {
    return {
      tableRef: '',
      selectLabelMap: {},               // type为 select、treeSelect的列，选项的map
      oldScrollTop: 0,
      oldCurrentRowKey: null            // 记录上次点击的行的rowKey
    };
  },
  computed: {
    tableStyle() {
      const style = `width: 100%; flex-grow: 1; min-height: ${this.minHeight}px;`;
      return style;
    },
    lang() {
      return this.$store.getters.language
    },
    // 不同语言环境下的额外宽度
    extraWidth() {
      return { en: 20 }[this.lang] || 0
    },
    // 行数据rowKey的父子关系map
    relationMap() {
      return this.checkStrictly ? this.rowData.reduce(this.rowKeyRelation, {}) : {}
    }
  },
  mounted() {},
  created: function() {
    this.tableRef = 'table' + new Date().getTime();
  },
  methods: {
    getTable() {
      return this.$refs[this.tableRef]
    },
    // reduce 的回调方法，获得各个行数据rowKey的父子关系map， map 的 key 是子行数据的 rowKey， value 是父行数据的 rowKey
    rowKeyRelation(map, row, index, arr, parentKey = null) {
      const rowKey = row[this.rowKey]
      map[rowKey] = parentKey
      if (row.children) {
        row.children.reduce((map, row, index, arr) => this.rowKeyRelation(map, row, index, arr, rowKey), map)
      }
      return map
    },
    // col.sortArr: 传入prop数组，可以根据多个列进行排序，当前面列的值相同时，对下个列的值进行排序。
    // col.sortOriginal: 1. Boolean类型，值为true时，对prop的原始数据进行比较，值为false或不设置时，则根据formatter或原始数据进行比较
    //                   2. Array类型，值与sortArr项一一对应，Boolean类型
    defaultSortMethod(a, b, col) {
      const sortArr = col.sortArr ? [...col.sortArr] : [col.prop];
      const sortOriginal = Array.isArray(col.sortOriginal)
        ? [...col.sortOriginal]
        : Array(sortArr.length).fill(col.sortOriginal);
      let result = 0;
      while (result === 0 && sortArr.length > 0) {
        const sortProp = sortArr.shift();
        const sortO = sortOriginal.shift();
        let v1 = this.getAttribute(a, sortProp);
        let v2 = this.getAttribute(b, sortProp);
        if (!sortO) {
          v1 = col.formatter ? col.formatter(a, v1, col) : v1;
          v2 = col.formatter ? col.formatter(b, v2, col) : v2;
        }
        result = this.compareFunc(v1, v2);
      }
      return result;
    },
    // 通过key获取对象的值，对于深层属性(aa.bb)也能获取到值
    getAttribute(obj, key) {
      let val = '';
      if (key) {
        val = obj;
        const arr = key.split('.');
        while (arr.length > 0) {
          val = val[arr.shift()];
          // 如果val不是对象或者val是null，则直接返回val
          if (typeof val != 'object' || val == null) return val;
        }
      }
      return val;
    },
    indexMethod(index) {
      return this.paging ? index + 1 : this.limit * (this.page - 1) + index + 1
    },
    checkSelectedRows(checkedKeys) {
      this.clearSelection();
      this.$nextTick(() => {
        if (checkedKeys.length > 0 && this.rowData && this.rowData.length > 0) {
          const tableRef = this.getTable()
          this.rowData.forEach((el) => {
            const index = checkedKeys.indexOf(el[this.rowKey]);
            if (index > -1) {
              this.selectedData.splice(index, 1, el);
              tableRef.toggleRowSelection(el, true);
            }
          });
        }
      });
    },
    toggleAllSelection() {
      // 选中全部
      const tableRef = this.getTable()
      this.rowData.forEach((el) => {
        tableRef.toggleRowSelection(el, true);
      });
    },
    toggleRowSelection(row, selected = true) {
      this.getTable() && this.getTable().toggleRowSelection(row, selected);
    },
    doLayout() {
      this.getTable() && this.getTable().doLayout();
    },
    nodeChange(data, node, vm, row, col) {
      if (col.nodeChange) {
        return col.nodeChange(data, node, vm, row, col);
      }
    },
    headerLabelFormat(col, index) {
      const { escapeFormat } = col;
      if (escapeFormat) {
        return col.label
      }
      const { headerFormat } = col;
      if (headerFormat && typeof headerFormat === 'function') {
        return this.lang == 'en' ? setTheFirstLetterToUppercase(headerFormat(col, index)) : headerFormat(col, index)
      } else {
        return this.lang == 'en' ? setTheFirstLetterToUppercase(this.labelFormat(col.label)) : this.labelFormat(col.label)
      }
    },
    labelFormat(label) {
      const hasKey = this.$te(`table.${label}`);
      return hasKey ? this.$t(`table.${label}`) : label;
    },
    /**
     * 表头渲染（表头使用v-for进行动态渲染时，每一项都绑定了key,当更改表头数据对象顺序时，vue会通过当时绑定的key找到对应元素，当vue发现二者并没有差异时并不会重新渲染）
     * @param prop
     * @returns {number|*}
     */
    keyRandom() {
      if (this.headerRendering) {
        // 需要重新渲染表头时，给key生成一个随机数
        return Math.random()
      } else {
        return ''
      }
    },
    // 列宽度格式化
    widthFormat(col, width) {
      // type是button，且设置了 width。根据按钮宽度和额外宽度，增加width
      if (col.type == 'button' && width) {
        // 按钮文字总数
        let allBtnLength = 0
        // 显示的按钮数
        let showBtnNum = 0
        // 显示的按钮文字数
        let showBtnLength = 0
        // 额外减掉的宽度
        let reducedWidth = 0
        let reduceBtnNum = 0
        col.buttons.forEach(btn => {
          const btnLabel = this.labelFormat(btn.label)
          const btnLength = btnLabel ? btnLabel.length : 2
          allBtnLength += btnLength
          // 有 isShow 方法
          if (btn.isShow) {
            try {
              if (btn.isShow()) {
                showBtnNum++
                showBtnLength += btnLength
              }
            // 因为 btn.isShow() 没有传入行数据，如果读取行数据的属性会报错，视为按钮存在
            } catch (error) {
              if (btn.width) {
                // 有设置 width 的按钮，不参与重置 width，而是在重置后加上
                allBtnLength -= btnLength
                // 额外减掉的 宽度 + extraWidth
                reducedWidth += Number(btn.width)
                reduceBtnNum++
              } else {
                showBtnNum++
                showBtnLength += btnLength
              }
            }
          } else {
            showBtnNum++
            showBtnLength += btnLength
          }
        })
        // 总按钮数不为 0，按照实际显示按钮数量计算宽度
        if (allBtnLength) {
          showBtnLength = showBtnLength || 1
          const resetWidth = (Number(width) - reducedWidth) * showBtnLength / allBtnLength
          const extraWidth = this.extraWidth * (showBtnNum + reduceBtnNum) + reducedWidth
          const actualWidth = resetWidth + extraWidth
          // 如果 actualWidth 小于 width ，则额外加上 padding 的值
          return actualWidth < width ? String(actualWidth + 20) : String(actualWidth)
        } else {
          // 总按钮数为 0，返回原宽度
          return String(Number(width) + this.extraWidth * col.buttons.length)
        }
      }
      return width || ''
    },
    classNmaeFormat(col) {
      if (col.type == 'button') {
        return 'flex-col'
      }
      return ''
    },
    // 按钮的样式
    btnStyleFormat(col, btn) {
      return `text-align: left; ${btn.style}`
    },
    ellipsisFormat(col, ellipsis) {
      if (col.type == 'button') {
        return false
      }
      return ellipsis === false ? ellipsis : true
    },
    iconArrayFormat(col, data) {
      // grid 对于type='icon'的列，有独立的处理代码，因此这边要过滤掉
      if ('icon' === col.type) {
        return [];
      }
      const icons = this.tableColIconFormat(col, data);
      if (Array.isArray(icons) || !icons) {
        return icons;
      }
      return [icons];
    },
    tableColIconFormat: function(col, data) {
      if (col.iconFormatter) {
        return col.iconFormatter(data);
      } else if (col.iconClass) {
        return col.iconClass;
      }
      return null;
    },
    tableColBtnFormat: function(btn, data) {
      if (btn.formatter) {
        return btn.formatter(data, btn);
      }
      return this.labelFormat(btn.label);
    },
    showButton: function(btn, data) {
      if (btn.isShow) {
        return btn.isShow(data, btn);
      } else {
        return true;
      }
    },
    tableColBtnDisabledFormat: function(btn, data) {
      if (btn.disabledFormatter) {
        return btn.disabledFormatter(data, btn);
      }
      if (typeof btn.disabled === 'boolean') {
        return btn.disabled;
      }
      return false;
    },
    tableColBtnLoadingFormat: function(btn, data) {
      if (btn.loadingFormatter) {
        return btn.loadingFormatter(data, btn);
      }
      return false;
    },
    tableColDataFormat: function(col, data) {
      const prop = col.prop;
      let propValue = data;
      if (prop) {
        prop.split('.').forEach(function(value) {
          if (propValue) {
            propValue = propValue[value];
          } else {
            propValue = '';
          }
        });
      }
      if (col.formatter) {
        return col.formatter(data, propValue, col);
      } else if (col.type === 'popover') {
        return propValue;
      } else if (typeof propValue === 'string') {
        propValue = this.html2Escape(propValue);
      } else if (!prop) {
        return '';
      }
      return propValue;
    },
    // 返回文本的颜色
    textColorFormat(col, data) {
      if (typeof col.colorFormatter == 'function') {
        // 配置了 colorFormatter
        return col.colorFormatter(data, data[col.prop])
      } else if (col.textColor && data[col.textColor]) {
        // 配置了 textColor
        return data[col.textColor]
      } else {
        // 未配置 colorFormatter、textColor，则不设置颜色
        return ''
      }
    },
    checkboxClass(rowData, col) {
      const check = rowData[col.prop]
      const disabled = !this.selectable(rowData) || this.disabledFunc(col, rowData)

      return {
        'el-icon-my-checked': check && !disabled,
        'el-icon-my-checkbox': !check && !disabled,
        'el-icon-my-checked-disabled': check && disabled,
        'el-icon-my-checkbox-disabled': !check && disabled,
        'row-edit': !disabled,
        'row-disabled': disabled
      }
    },
    checkboxChange(value, rowData, col) {
      const disabled = !this.selectable(rowData) || this.disabledFunc(col, rowData)
      if (disabled) return
      if (typeof col.checkboxChange == 'function') {
        return col.checkboxChange(!value, rowData, col);
      } else {
        rowData[col.prop] = !value;
      }
    },
    // 单元格正在编辑
    isColEdit(rowData, prop) {
      return rowData.rowEdit && rowData.rowEdit[prop];
    },
    setColEdit(rowData, prop, index) {
      if (!rowData.rowEdit) rowData.rowEdit = {};
      this.$set(rowData.rowEdit, prop, true);
      this.setCurrentRow(rowData);
      const selectedKeys = this.getSelectedKeys();
      this.rowData.splice(0, 0); // 触发table重新渲染，否则同一条记录多个可编辑列无法变为编辑状态
      this.$nextTick(() => {
        // table重新渲染会导致已选记录被取消，所以手动勾选
        this.checkSelectedRows(selectedKeys);
      });
    },
    disabledFunc(col, row) {
      if (typeof col.disabled === 'function') {
        return col.disabled(col, row)
      } else {
        return !!col.disabled
      }
    },
    // 当 input 值改变后，失去焦点或按下回车键触发该方法
    inputChange(value, scope, col) {
      if (typeof col.change === 'function') {
        col.change(value, scope.row, scope.$index)
      } else {
        scope.row[col.prop] = value.trim()
        if (col.editMode) {
          this.$set(scope.row.rowEdit, col.prop, false)
          // 触发table重新渲染，使输入框隐藏
          this.rowData.splice(0, 0);
        }
      }
    },
    // 当 input-number 值改变后，失去焦点或按下回车键触发该方法
    inputNumberChange(event, scope, col) {
      if (typeof col.change === 'function') {
        col.change(scope.row[col.prop], scope.row, scope.$index)
      } else {
        if (col.editMode) {
          this.$set(scope.row.rowEdit, col.prop, false)
          // 触发table重新渲染，使输入框隐藏
          this.rowData.splice(0, 0);
        }
      }
    },
    saveCell(col, row) {
      if (row.saveCellAble) {
        row.saveCellAble = false
        return typeof col.saveCell == 'function' ? col.saveCell(row[col.prop], col, row) : ''
      }
    },
    getSelectLabel(key, col, row) {
      const { options, prop, multiple, rowOptions } = col;
      let map = this.selectLabelMap[prop];
      if (rowOptions && row && row[col.rowOptions]) {
        const index = row[col.rowOptions].findIndex(item => item.value === key)
        if (index > -1) {
          return this.labelFormat(row[col.rowOptions][index].label)
        }
        return null
      }
      if (!map) {
        map = {};
        options.forEach((op) => {
          map[op.value] = this.labelFormat(op.label);
        });
        this.selectLabelMap[prop] = map;
      }
      if (multiple) {
        return key.map((k) => map[k]).join('、');
      } else {
        return map[key];
      }
    },
    getTreeSelectLabel(checkedKeys, col) {
      const { treeData, prop, nodeKey } = col;
      const labels = [];
      if (!checkedKeys) return '';
      for (let i = 0; i < checkedKeys.length; i++) {
        const el = checkedKeys[i];
        if (typeof el === 'object') {
          labels.push(el.label);
        } else {
          let map = this.selectLabelMap[prop];
          if (!map) {
            map = {};
            const temp = [...treeData];
            while (temp.length > 0) {
              const item = temp.shift();
              if (item.children) temp.push(...item.children);
              map[item[nodeKey]] = item.label;
            }
            this.selectLabelMap[prop] = map;
          }
          labels.push(map[el]);
        }
      }
      return labels.join('、');
    },
    headIconShow(scope, col) {
      if (this.customIconShow(scope)) {
        // 说明需要显示自定义列图标
        // 设置图标
        col.headIcon = 'register'
        // 设置单击函数
        col.headIconClick = this.handleCustomCol
        return true
      }
      // 如果列有设置headIcon属性，则显示
      return !!col.headIcon
    },
    customIconShow(scope) {
      // const lastCol = this.colModel.at(-1)
      const lastCol = this.colModel[this.colModel.length - 1]
      const label = this.labelFormat(lastCol.label)
      return this.customCol && scope.column.label == label
    },
    handleCustomCol() {
      this.$emit('custom-col')
    },
    loadMoreData(type) {
      this.$emit('load-more-data', type)
    },
    sortChange(obj) {
      this.$emit('sort-change', obj)
    },
    select(selection, row) {
      if (this.checkStrictly) {
        // 选中父节点时，子节点一起选中/取消
        const isSelect = selection.some((el) => { return row[this.rowKey] === el[this.rowKey] })
        if (isSelect) {
          // 勾选，则把子节点都勾选上
          if (row.children) {
            // 解决子组件没有被勾选到
            this.setChildren(row.children, true)
            // selection = selection.concat(row.children)
          }
        } else {
          // 取消勾选，则把子节点都取消勾选
          if (row.children) {
            this.setChildren(row.children, false)
          }
          // 取消父节点的勾选状态
          this.setParentUnselect(selection, row)
        }
      }
      this.$emit('select', selection, row);
    },
    selectAll(selection) {
      if (this.checkStrictly) {
        // rowData第一层只要有在selection里面就是全选
        const isSelect = selection.some((el) => {
          const rowDataKeys = this.rowData.map((row) => row[this.rowKey])
          return rowDataKeys.includes(el[this.rowKey])
        })
        // rowData第一层只要有不在selection里面就是全不选
        const isCancel = !this.rowData.every((el) => {
          const selectIds = selection.map((row) => row[this.rowKey])
          return selectIds.includes(el[this.rowKey])
        })
        if (isSelect) {
          selection.map((el) => {
            if (el.children) {
              // 解决子组件没有被勾选到
              this.setChildren(el.children, true)
            }
          })
        }
        if (isCancel) {
          this.rowData.map((el) => {
            if (el.children) {
              // 解决子组件没有被勾选到
              this.setChildren(el.children, false)
            }
          })
        }
      }
      this.$emit('select-all', selection);
    },
    // 设置子数据勾选/取消勾选
    setChildren(children, select) {
      // 遍历多个子层级
      children.forEach((row) => {
        this.toggleSelection(row, select)
        if (row.children) {
          this.setChildren(row.children, select)
        }
      })
    },
    // 设置父数据取消勾选，selection 已勾选的数据
    setParentUnselect(selection, row) {
      const rowKey = row[this.rowKey]
      const parentKey = this.relationMap[rowKey]
      const parentRow = selection.find(data => data[this.rowKey] == parentKey)
      if (parentRow) {
        this.toggleSelection(parentRow, false)
        this.setParentUnselect(selection, parentRow)
      }
    },
    // 切换某一行的选中状态
    toggleSelection(row, select) {
      if (row) {
        this.$nextTick(() => {
          this.toggleRowSelection(row, select)
        })
      }
    },
    selectionChange(selection) {
      this.$emit('selection-change', selection);
    },
    // 行数据展开或收起时的回调
    expandChange(currentRow, expandedRows) {
      this.$emit('expand-change', currentRow, expandedRows)
    },
    // 当前选中行变化时的回调
    currentChange(currentRow, oldCurrentRow) {
      // 当前点击行与上次点击行不一样时，将上次点击行的编辑框取消掉
      if (currentRow && currentRow[this.rowKey] != this.oldCurrentRowKey) {
        // 因为行数据可能产生变化，所以要重新获取
        const oldData = this.rowData.find(data => data[this.rowKey] == this.oldCurrentRowKey)
        if (oldData && oldData.rowEdit) {
          for (const key in oldData.rowEdit) {
            oldData.rowEdit[key] = false
          }
        }
      }
      this.$emit('current-change', currentRow, oldCurrentRow);
    },
    rowDblclick(rowData, column, event) {
      this.$emit('row-dblclick', rowData, column, event);
    },
    rowClick(rowData, column, event) {
      this.$emit('row-click', rowData, column, event);
    },
    cellClick(row, column, cell, event) {
      // 当前点击行与上一次点击行是同一条数据
      if (this.oldCurrentRowKey == row[this.rowKey]) {
        if (row.rowEdit) {
          // 将rowEdit中的值设置为 false，除了当前点击单元格
          for (const key in row.rowEdit) {
            if (key !== column.property) {
              row.rowEdit[key] = false
            }
          }
          // 触发table重新渲染，否则同一条记录多个可编辑列无法变为编辑状态
          this.rowData.splice(0, 0)
        }
      }
      // 记录当前行数据的rowkey
      this.oldCurrentRowKey = row[this.rowKey]
      this.$emit('cell-click', row, column, cell, event)
    },
    clearSelection() {
      if (this.multiSelect) {
        this.getTable() && this.getTable().clearSelection();
      } else {
        this.setCurrentRow(null);
      }
    },
    clearSort() {
      this.getTable() && this.getTable().clearSort();
    },
    setCurrentRow(row) {
      this.getTable() && this.getTable().setCurrentRow(row);
    },
    getSelectedKeys: function() {
      const selectedKeys = []
      const selectedData = this.localLazy ? this.savedSelectedDatas : this.selectedData
      selectedData.forEach((item, index) => {
        selectedKeys.push(item[this.rowKey])
      })
      return selectedKeys;
    },
    handleIcon(row, value) {
      this.$emit('handleIcon', row, value);
    },
    // 特殊情况名称的处理
    // 处理formatter函数
    detailLabelFormat(col, row) {
      if (undefined === col.formatter) {
        return this.getAttribute(row, col.prop);
      }
      return col.formatter(row, row[col.prop], col);
    },
    // 根据传入的searchParam，判断参数类型
    // 若类型为字符串(string)：则说明是单个参数，可省略不写对应的值，例如：searchParam : 'terminalId'
    // 若类型为对象（object）：则说明是多个参数组成的对象，故需要对每个参数转换成对应的值，组装成需要查询的参数，例如：searchParam : { terminalId: 'terminalId', parentId: 'parentId' }
    searchParamFormat(prop, row) {
      if (undefined === prop) {
        return null;
      }
      const data = {};
      if (typeof prop == 'string') {
        data['searchId'] = this.getAttribute(row, prop) || undefined;
      } else if (typeof prop === 'object') {
        const paramArr = Object.keys(prop);
        if (paramArr.length > 0) {
          paramArr.forEach((param) => {
            data[param] = this.getAttribute(row, prop[param]);
          });
        }
      } else if (typeof prop === 'function') {
        return prop(row);
      }
      return data;
    }
  }
};
</script>

<style lang="scss" scoped>
.el-table .el-icon-arrow-right {
  color: white;
}
.el-table-column--selection .cell{
  padding: 0 10px;
  text-align: center;
}
>>>.flex-col .cell{
  display: flex;
}
.el-table .cell {
  button {
    margin-left: 0;
    margin-right: 10px;
  }
  button:last-child{
    margin-right: 0;
  }
}
.img-icon{
  margin-right: -6px;
  vertical-align: text-bottom;
}
.row-edit{
  cursor: pointer;
  color: #0086da;
}
.row-disabled{
  cursor: not-allowed;
}
.save-icon {
  color: #2da1e9;
  cursor: pointer;
}
.save-icon.disabled {
  color: #808080;
  cursor: not-allowed;
}
>>>.el-button--text {
  height: 19px;
}
>>>.el-button {
  max-width: 100%;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.custom-icon {
  margin: 4px 0px;
  position: absolute;
  right: 2px;
  cursor: pointer;
}
>>>.el-table__expand-icon {
  color: inherit;
}
</style>
