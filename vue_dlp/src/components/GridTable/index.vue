<!--
    /**
     * 列表显示Table组件，提供列表显示、分页显示、获取选中节点的功能，方便其他模块调用
     * @date 2019-10-17
     * 调用示例：
      <grid-table
        ref="tableRefId"
        default-expand-all                          // 是否默认展开所有行数据，
        expandable                                  // 行数据是否可展开，不写该属性则为不可展开,适用于动态加载子表的情况
        :col-model="colModel"                       // 列表属性配置，eg:[{prop: 'username', label: '名称', width: '150', fixed: true, sort: true}]
        :sub-col-model="subColModel"                // 子列表属性配置，eg:[{prop: 'username', label: '名称', width: '150', fixed: true, sort: true}]
        :row-datas="rowDatas"                       // 传入列表数据
        :row-data-api="rowDataApi"                  // 查询列表数据的API方法，方法返回值为Promise对象
        :sub-row-data-api="subRowDataApi"           // 查询子列表数据的API方法，方法返回值为Promise对象
        :sortable="sortable"                        // 是否可以使用排序功能
        :height="height"                            // 设置表格高度，数值类型，如：100 , 不设置则可自适应父元素高度
        :min-height="200"                           // 表格的最小高度，默认100，当实际高度小于最小高度时，则固定为最小高度
        :stripe="false"	                            // 是否为斑马纹 table, 默认为 true
        :custom-col="customCol"                     // 是否支持自定义列，固定的列不允许取消选中
        :multi-select="false"                       // 是否支持多选，即是否显示复选框，默认显示
        :sub-multi-select="false"                   // 子表是否支持多选，即是否显示复选框，默认显示
        row-key="id"                                // 行数据的 Key，用来优化 Table 的渲染
        :checked-row-keys="checkedRowKeys"          // 勾选行的key，checkedRowKeys为数组
        :is-saved-selected="true"                   // 是否保存不同页面行数据的勾选状态
        saved-selected-prop="name"                  // 已选择列表展示的列的prop值
        :selected-data-label-formatter="selectedDataLabelFormatter"             // 已选择列表展示的 label 的格式化方法
        :selected-data-title-formatter="selectedDataTitleFormatter"             // 已选择列表展示的 label 的 title 格式化方法
        :selectable="selectable"                    // 仅对 type=selection 的列有效，类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选. Function(row, index)
        :row-class-name="rowClassName"              // 行的 className 的回调方法，也可以使用字符串为所有行设置一个固定的 className
        :cell-class-name="cellClassName"            // 单元格的 className 的回调方法，也可以使用字符串为所有单元格设置一个固定的 className。
        :show-pager="true"                          // 是否显示页码工具条
        pager-small                                 // 页码工具条使用小型分页样式
        :page-sizes="pageSizes"                     // 每页显示个数选择器的选项设置，eg：[10, 20, 30, 50]
        no-page-total                               // 页码栏是否显示数据总数(此处为前端配置，noTotal的值为后台配置，两者共同决定是否显示数据总数)
        disabled-last-page                          // 是否禁用最后一页的跳转功能，默认false （主要用于数据量大，查询耗时的情况，不允许直接跳转到最后一页）
        :disabled-last-page-limit="10000"           // 禁用最后一页跳转功能的限制条件，总数据量 total 大于这个值才会禁用
        :after-load="afterLoad"                     // 表格数据加载后事件
        :autoload="true"                            // 自动加载表格数据,默认为true
        :header-rendering="false"                   // 表头顺序重新渲染,默认为false
        @selectionChangeEnd="selectionChangeEnd"    // 回调选中列表后执行的函数
        @currentChange="currentChange"              // 当前行改变时执行的函数，参数 currentRow ， oldRow
      />
      *属性说明：
      rowDataApi、subRowDataApi 方法的返回值Promise对象包括
          {
            code: 20000,
            data: {
              total: 100,
              items: [ { account: 'admin', name: 'administrator', role: '超级管理员' } ]
            }
          }
      colModel属性配置的属性介绍：
          prop：这一列的属性名，列表通过此值匹配rowDataApi返回值items中的属性名，并得到对应的值
          label：这一列的表头名称
          width：这一列的宽度，超出可自适应
          fixedWidth：这一列的固定宽度
          fixed：是否固定列，可选值：true right
          hidden: 是否隐藏该列，可接受函数(需要返回值)、布尔值，值为true时隐藏
          sort：是否支持排序, true, false, 'custom'，值为custom则代表用户希望远程排序，需要监听 Table 的 sort-change 事件
          sortArr: 传入prop数组，可以根据多个列进行排序，当前面列的值相同时，对下个列的值进行排序。
          sortOriginal: 1. Boolean类型，值为true时，对prop的原始数据进行比较，值为false或不设置时，则根据formatter或原始数据进行比较
                        2. Array类型，值与sortArr项一一对应，Boolean类型
          type: 这一列的类型，可选值：selection、button、input、select、treeSelect、img、showDetail 等，可为空
              select、treeSelect: 默认显示 编辑icon + 选中项label。点击 icon 切换为可编辑的组件，点击其他行恢复为不可编辑状态。
                                  通过配置 alwaysEdit 为 true，则 select、treeSelect 一直为可编辑状态
              input：默认显示 input 输入框，
                     通过配置 showText，可以只显示文字。类型为 Boolean|function，返回true则显示文字，false显示input输入框
                     通过配置 editMode 为 true，默认显示 编辑icon + 输入框的值。点击 icon 切换为 input 组件。
                     通过配置 saveCell 实现对单元格数据进行保存，需要传入函数，参数为 col, row
          attributes: 当type为input、select等组件时，可通过该属性传递组件的配置项
          formatter: 数据格式化方法
          iconFormatter: 格式化每一列显示的图标
          ellipsis: 当内容过长被隐藏时显示 tooltip，默认值true，当设置为false时，不显示tooltip
          buttons:这一列显示的按钮集合，数组元素为{label:'修改', click: function(row) {}}
          renderHeader: 列标题 Label 区域渲染使用的函数： renderHeader(h, { column, $index }) {}
          disabled: 设置组件是否禁用的配置，可传递 Boolean 或 function
          checkboxChange: checkbox值变更的回调函数
          switchChange: switch值变更的回调函数
          escapeFormat: Boolean类型，值为true时，忽略对label的格式化，比如多语言转换等等，值为false或不设置时走原有逻辑

      subColModel属性配置的属性介绍：（暂时屏蔽）
          prop：这一列的属性名，列表通过此值匹配subRowDataApi返回值items中的属性名，并得到对应的值
          label：这一列的表头名称
          width：这一列的宽度，超出可自适应
          fixedWidth：这一列的固定宽度
          fixed：是否固定列，可选值：true right
          hidden: 是否隐藏该列，可接受函数(需要返回值)、布尔值，值为true时隐藏
          sort：是否支持排序, true, false, 'custom'，值为custom则代表用户希望远程排序，需要监听 Table 的 sort-change 事件
          type:这一列的类型，可选值：selection、button，可为空
          formatter: 数据格式化方法
          iconFormatter: 格式化每一列显示的图标，支持的格式有：'iconClass' 或 {class: 'iconClass', title: '图标提示', css: {图标样式} } 或 多个图标的数组形式
          ellipsis: 当内容过长被隐藏时显示 tooltip，默认值true，当设置为false时，不显示tooltip
          buttons:这一列显示的按钮集合，数组元素为{label:'修改', click: function(row) {}}
      *接口说明：供外部组件调用的方法
      this.$refs.tableRefId.getSelectedIds()    //获取选中的ID
      this.$refs.tableRefId.getSelectedKeys()    //获取选中的Key
      this.$refs.tableRefId.getSelectedDatas()  //获取选中的数据
    */
-->
<template>
  <div ref="tableBox" :style="boxStyle" class="tableBox">
    <table-temp
      :ref="tableRef"
      v-bind="$attrs"
      :loading="listLoading"
      :row-data="rowData"
      :header-rendering="headerRendering"
      :sortable="sortable"
      :default-sort="defaultSortOpt"
      :compare-func="compare"
      :height="tableHeight"
      :row-key="rowKey"
      :stripe="stripe"
      :border="border"
      :size="size"
      :limit="limit"
      :total="rowDatas.length"
      :page="page"
      :paging="showPager"
      :row-class-name="rowClassName"
      :cell-class-name="cellClassName"
      :cell-style="cellStyle"
      :default-expand-all="defaultExpandAll"
      :style="tableStyle"
      :expandable="expandable"
      :selectable="selectable"
      :col-model="customColModel"
      :custom-col="customCol"
      :multi-select="multiSelect"
      :row-no-label="rowNoLabel"
      :local-lazy="localLazy"
      :selected-data="selectedData"
      :saved-selected-datas="savedSelectedDatas"
      @custom-col="handleCustomCol"
      @load-more-data="loadMoreData"
      @sort-change="sortChange"
      @select="select"
      @select-all="selectAll"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @row-dblclick="rowDblclick"
      @expand-change="expandChange"
      @row-click="rowClick"
      @cell-click="cellClick"
      @handleIcon="handleIcon"
    >
      <template slot="popoverContent" slot-scope="props">
        <slot name="popoverContent" :detail="props.detail"></slot>
      </template>
      <!-- 暂时屏蔽子表功能 -->
      <!-- <template v-if="expandable" slot="expand" slot-scope="scope">
        <table-temp
          v-if="showSubColModel.length > 0"
          :ref="'subTable' + scope.rowData.id"
          v-loading="subListLoading"
          :height="'200px'"
          :col-model="showSubColModel"
          :row-data="scope.rowData.children"
          :multi-select="subMultiSelect"
          :autoload="false"
          :expandable="false"
          style="width: 100%;padding-left: 40px;"
        />
      </template> -->
    </table-temp>
    <pagination
      v-show="showPager"
      :total="total?total:0"
      :small="pagerSmall"
      :page="page"
      :limit="limit"
      :page-sizes="pageSizes"
      :layout="currentLayout"
      :disabled="paginationDisabled"
      :disabled-last-page="disabledLastPage"
      :disabled-last-page-limit="disabledLastPageLimit"
      :hide-on-single-page="hideOnSinglePage"
      style="width: 100%; flex-shrink: 0;"
      @update:limit="updateLimit"
      @update:page="updatePage"
      @update:max-page="updateMaxPage"
      @pagination="handlePagination"
    >
      <!-- 跨页保存勾选数据的功能 -->
      <span v-show="isSavedSelected && savedSelectedDatas.length>0" class="custom-slot" style="float:left;">
        <el-popover
          v-model="selectedListVisible"
          placement="top-start"
          :offset="7"
          width="350"
          trigger="click"
        >
          <span class="selected-title">
            {{ $t('components.selectedList') }}
            <span>（{{ savedSelectedDatas.length }}）</span>
            <el-button type="text" class="delete-icon" @click="selectedDatasDelete">{{ $t('button.clear') }}</el-button>
          </span>
          <ul class="selected-list">
            <li v-for="(row, index) in savedSelectedDatas" :key="index">
              <span :title="selectedDataTitle(row, savedSelectedProp)">{{ selectedDataLabel(row, savedSelectedProp) }}</span>
              <svg-icon icon-class="delete" class="delete-icon" @click="e => { selectedDatasDelete(e, index) }"></svg-icon>
            </li>
          </ul>
          <el-tooltip slot="reference" effect="dark" placement="top">
            <div slot="content">
              {{ $t('components.selectedTooltip') }}
            </div>
            <i class="el-icon-my-checked-green checked-icon"></i>
          </el-tooltip>
        </el-popover>
      </span>
      <!-- 不显示数据总条数时，slot位置显示当前页码 -->
      <span v-if="noTotalShow" style="text-align: center;" class="el-pagination__total">{{ page }}</span>
      <!--  -->
      <span v-if="showCurrentPageOnSlot" style="text-align: center;" class="el-pagination__total">{{ page }}</span>
      <div v-if="totalApi && !showLoadTotal" v-loading="totalLoading" style="float:left;" ><span style="text-align: center;color: #4ea2e4;cursor: pointer;" @click="execTotalApi">{{ $t('table.queryTotal') }}</span></div>
      <span v-if="totalApi && showLoadTotal" style="float:left;" class="el-pagination__total">{{ $t('table.total', { total: loadTotalValue }) }}</span>
    </pagination>

    <el-dialog
      v-if="customCol"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.customColumnSetting')"
      :visible.sync="customColDlgVisible"
      width="600px"
    >
      <el-button type="text" style="margin-left: 25px;" @click="checkAll(true)">{{ $t('button.selectAll') }}</el-button>
      <el-button type="text" @click="checkAll(false)">{{ $t('button.cancelSelectAll') }}</el-button>
      <el-checkbox-group v-model="customKeys" class="custom-checkbox">
        <el-checkbox v-for="(item, key) in showColModel" :key="key" :label="key" class="ellipsis" :disabled="!!item.fixed">
          <span :title="$te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label">
            {{ $te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label }}
          </span>
        </el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveCustomCol()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="customColDlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import TableTemp from './tableTemp'
import { getValueByCondition, insertPersonalization } from '@/api/user'
import { isIPv4 } from '@/utils/validate'
import { ipv4ToInt } from '@/utils'

export default {
  name: 'GridTable',
  components: { Pagination, TableTemp },
  props: {
    // 默认勾选的节点key数组
    checkedRowKeys: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否支持表头发生变化后重新渲染
    headerRendering: {
      type: Boolean,
      default: false
    },
    // 是否可以使用排序功能
    sortable: {
      type: Boolean,
      default: true
    },
    defaultSort: {
      type: Object,
      default: function() {
        return {}
      }
    },
    stripe: {
      type: Boolean,
      default: true
    },
    border: {
      type: Boolean,
      default: true
    },
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    size: {
      type: String,
      default: 'medium'
    },
    multiSelect: {// 是否提供多选，即是否提供复选框
      type: Boolean,
      default: true
    },
    rowNoLabel: {// 是否显示行号，并设置行号列表头名
      type: String,
      default: null
    },
    subMultiSelect: {// 子表是否提供多选，即是否提供复选框
      type: Boolean,
      default: false
    },
    defaultExpandAll: { // 是否默认展开所有行数据
      type: Boolean,
      default: false
    },
    expandable: {// 是否可以展开
      type: Boolean,
      default: false
    },
    selectable: {
      type: Function,
      default: function() {
        return true
      }
    },
    // 是否显示页码工具条
    showPager: {
      type: Boolean,
      default: true
    },
    // 页码工具条使用小型分页样式
    pagerSmall: {
      type: Boolean,
      default: false
    },
    // 每页显示个数选择器的选项设置，eg：[10, 20, 30, 50]
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 50, 100, 200]
      }
    },
    noPageTotal: {
      type: Boolean,
      default: false
    },
    // 是否禁用最后一页的跳转功能
    disabledLastPage: {
      type: Boolean,
      default: false
    },
    // 禁用最后一页跳转功能的限制条件，total > disabledLastPageLimit
    disabledLastPageLimit: {
      type: Number,
      default: 10000
    },
    hideOnSinglePage: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 0
    },
    minHeight: {
      type: Number,
      default: 100
    },
    // 当 GridTable 被 LocalZoomIn 组件包裹时，不能设置固定的 maxHeight，否则当放大时，GridTable 高度无法撑开
    // 如果需要调整高度，可以使用 max-height="calc(100% - 100px)"
    maxHeight: {
      type: [Number, String],
      default: 0
    },
    rowHeight: { // 行高
      type: Number,
      default: 31
    },
    // 是否根据行数和rowHeight自动调整高度，直至maxHeight（如果maxHeight>0）
    autoHeight: {
      type: Boolean,
      default: false
    },
    colModel: {// 列模板配置
      type: Array,
      default: function() {
        return []
      }
    },
    subColModel: {// 子表列模板配置
      type: Array,
      default: function() {
        return []
      }
    },
    rowDatas: {
      type: Array,
      default: function() {
        return []
      }
    },
    // Function({row, rowIndex})/String
    rowClassName: {
      type: [Function, String],
      default: ''
    },
    // Function({row, column, rowIndex, columnIndex})/String
    cellClassName: {
      type: [Function, String],
      default: ''
    },
    cellStyle: {
      type: Function,
      default: function() {
        return ''
      }
    },
    rowDataApi: {// 返回行数据
      type: Function,
      // 当外部未定义 rowDataApi 方法，而是使用 rowDatas 传数据时，使用下面的默认方法
      default: function(option) {
        return new Promise((resolve, reject) => {
          // 外部未定义 rowDataApi 方法，则将 getDatasByApi 标记为 false
          this.getDatasByApi = false
          // 备份数据，避免影响原数据
          this.tempRowDatas = [...this.rowDatas]
          // 查询条件有 sortName 才会进行排序
          if (option.sortName) {
            option.sortOrder = option.sortOrder && option.sortOrder.startsWith('a') ? 'asc' : 'desc'
            this.tempRowDatas.sort((a, b) => {
              const result = this.compare(a[option.sortName], b[option.sortName])
              return option.sortOrder === 'asc' ? result : -result
            })
          }
          // 不显示分页时，是通过滚动加载数据的，如果加载的范围超出数据长度，则需要将page减少
          while (!this.showPager && (this.page - 1) * this.limit > this.tempRowDatas.length) {
            this.page--
          }
          // 不显示分页时，显示的数据的下标
          const index = (this.page - 1) * this.limit
          // 显示数据的数量
          const limit = this.showPager ? this.limit : this.limit * 2
          // 显示的数据
          const showDatas = this.showPager ? [...this.rowDatas] : this.tempRowDatas.slice(index, index + limit)
          const total = this.showPager ? this.total : this.tempRowDatas.length
          resolve({
            code: 20000,
            data: {
              total: total,
              items: showDatas // 数组元素格式为{username:'zhangsan',id:1,...}
            }
          })
        })
      }
    },
    subRowDataApi: {// 返回子表行数据
      type: Function,
      default: function(option) {
        return new Promise((resolve, reject) => {
          const index = (this.page - 1) * this.limit
          const limit = this.limit * 2
          const showDatas = this.rowDatas.slice(index, index + limit)[option.index]
          resolve({
            code: 20000,
            data: {
              total: showDatas.children.length,
              items: showDatas.children // 数组元素格式为{username:'zhangsan',id:1,...}
            }
          })
        })
      }
    },
    afterLoad: {// 表格刷新后调用的方法
      type: Function,
      default: function(rowData, grid) {
      }
    },
    autoload: {// 自动加载数据
      type: Boolean,
      default: true
    },
    localType: { // 部分使用api加载data，而删除操作又需要以本地的方式，则该值须设为true
      type: Boolean,
      default: false
    },
    layout: {
      type: String,
      default: 'slot, total, sizes, prev, pager, next, jumper'
    },
    showCurrentPageOnSlot: { // 是否在layout的slot位置显示当前页码
      type: Boolean,
      default: false
    },
    totalApi: {// 返回总条数
      type: Function,
      default: null
    },
    isSavedSelected: { // 是否保存不同页面行数据的勾选状态
      type: Boolean,
      default: false
    },
    savedSelectedProp: { // 已选择列表展示的列的prop
      type: String,
      default: 'name'
    },
    selectedDataLabelFormatter: { // 已选择列表展示的 label 的格式化方法
      type: Function,
      default: null
    },
    selectedDataTitleFormatter: { // 已选择列表展示的 label 的 title 格式化方法
      type: Function,
      default: null
    },
    notLimitHeight: {// 不限制table高度
      type: Boolean,
      default: false
    },
    onSortChange: { // 如果设置了此方法，则排序变更时，只调用此方法，否则调用内部的排序方法
      type: Function,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    retainPages: { // 文档追踪记录和盲水印记录翻页后保留最大页数
      type: Boolean,
      default: false
    },
    customCol: {  // 是否支持自定义列
      type: Boolean,
      default: false
    },
    customTableKey: {  // table 的自定义key，用于区分同一个table 显示的字段不同的情况
      type: String,
      default: ''
    },
    customColFunc: { // 如果设置了此方法，则点击自定义列设置时，只调用此方法，否则调用内部的设置方法
      type: Function,
      default: null
    },
    //  若rowDatas发送变化时，是否清空选中状态，默认为清空
    isClearSavedSelectedRowDatasChange: {
      type: Boolean,
      default: true
    }
  },
  data: function() {
    return {
      tableRef: '',
      selectedData: [],                  // 选中的数据
      listLoading: false,
      subListLoading: false,
      paginationDisabled: false,         // 列表正在请求数据时，禁用分页组件
      paginationTimer: null,             // 禁用分页组件的延时器
      rowData: [],                       // 行数据，数组元素格式为{username:'zhangsan',id:1,...}
      savedSelectedDatas: [],            // 保存的已选择数据，当isSavedSelected为true时，或 不分页的情况下,保存数据
      savedSelectedKeys: [],             // 保存的已选择数据的key，用于自动勾选行
      tempRowDatas: [],                  // rowDatas排序后的数据，用于滚动加载数据使用
      oldCurrentRowKey: null,            // 记录上次点击的行的rowKey
      total: 0,                          // 总记录数
      page: 1,                           // 当前页码
      limit: 20,                         // 每页数据数量
      maxPage: 1,                        // 总页码数量
      sortName: undefined,
      sortOrder: undefined,
      selectedListVisible: false,
      customColDlgVisible: false,
      customColKeys: [],                 // GridTable 展示的列的key
      customKeys: [],                    // 自定义列窗口勾选的列的key
      userId: null,
      menuCode: '',
      tableKey: '',
      submitting: false,
      noTotal: false,
      noTotalLayout: 'sizes, prev, slot, next',
      noJumperLayout: 'slot, total, sizes, prev, pager, next',
      showLoadTotal: false,
      loadTotalValue: 0,
      boxHeight: 0,
      totalLoading: false,
      getDatasByApi: true,               // 是否使用外部传入的api请求数据，用于判断查询数据有误时是否重新请求数据
      curQueryParam: undefined,          // 当前查询条件，用于与新的查询条件进行对比，判断查询是否变化（不判断页码、页数和排序）
      dlpTotal: 0,                       // 记录dlp系统中数据总数
      reSearchCount: 0                   // 重新请求数据的次数
    }
  },
  computed: {
    boxStyle() {
      const boxHeight = this.boxHeight ? this.boxHeight : this.height
      const height = boxHeight ? `height: ${boxHeight}px;` : ''
      const minHeight = this.minHeight ? `min-height: ${this.minHeight}px;` : ''
      const type = typeof this.maxHeight
      const value = type === 'number' ? `${this.maxHeight}px` : this.maxHeight
      const maxHeight = this.maxHeight ? `max-height: ${value};` : ''
      return `${maxHeight} ${minHeight} ${height} ${this.style}`
    },
    tableStyle() {
      const style = `width: 100%; flex-grow: 1; min-height: ${this.minHeight}px;`
      return style
    },
    tableHeight() {
      if (this.notLimitHeight) {
        return ''
      }
      // 显示页码 且 单页不隐藏
      const showPager = this.showPager && !(this.hideOnSinglePage && this.maxPage == 1)
      const pagerH = showPager ? 40 : 0
      let height
      if (!this.autoHeight && this.height) {
        height = (this.height - pagerH) + 'px'
      } else {
        height = `calc(100% - ${pagerH}px)`
      }
      return height
    },
    defaultSortOpt() {
      // el-table接受的 order: ascending, descending，所以这边进行转换
      const defaultSort = { prop: 'id', order: 'descending', ...this.defaultSort }
      const order = defaultSort.order.startsWith('desc') ? 'descending' : 'ascending'
      defaultSort.order = order
      return defaultSort
    },
    localLazy() {
      return this.rowDatas.length > 0 && !this.showPager
    },
    showColModel() {
      // 过滤掉需要隐藏的列
      return this.colModel.filter((item, index) => {
        if (typeof item.hidden == 'function') {
          return !item.hidden()
        }
        return !item.hidden
      })
    },
    // 设置为固定列的key
    fixedColKey() {
      const fixedColKey = []
      this.showColModel.forEach((item, index) => {
        if (item.fixed) {
          fixedColKey.push(index)
        }
      })
      return fixedColKey
    },
    customColModel() {
      this.$nextTick(() => {
        // 重置table，避免表格错位
        this.doLayout()
      })
      // 当启用自定义列
      if (this.customCol) {
        // 设置了该方法，则通过其他方式自定义列，显示完整的列
        if (this.customColFunc) {
          return this.showColModel
        }
        // this.customColKeys 有数据时，显示自定义的列
        if (this.customColKeys.length > 0) {
          const keysMap = this.customColKeys.reduce((map, key) => {
            map[key] = true
            return map
          }, {})
          return this.showColModel.filter((item, index) => {
            return keysMap[index]
          })
        }
      }
      // 显示完整的列
      return this.showColModel
    },
    showSubColModel() {
      return this.subColModel.filter((item, index) => {
        return !item.hidden
      })
    },
    currentLayout() {
      if (this.noTotalShow) {
        return this.noTotalLayout
      } else if (this.disabledLastPage) {
        return this.noJumperLayout
      } else {
        return this.layout
      }
    },
    noTotalShow() {
      return this.noPageTotal || this.noTotal
    }
  },
  watch: {
    rowDatas(val) {
      if (this.isClearSavedSelectedRowDatasChange) {
        this.clearSaveSelection()
      }
      this.execRowDataApi()
    },
    rowData(val) {
      if (this.autoHeight) {
        // 计算总行数
        let rowSize = 0
        if (val) {
          val.forEach(t => {
            rowSize += 1
            if (t.children && Array.isArray(t.children)) {
              rowSize += t.children.length
            }
          })
        }
        this.boxHeight = rowSize ? (rowSize + 1) * this.rowHeight : 0
      }
      if (this.isSavedSelected || this.localLazy) {
        this.checkSelectedRows(this.savedSelectedKeys)
      } else {
        this.checkSelectedRows(this.checkedRowKeys)
      }
    },
    checkedRowKeys(val) {
      this.checkSelectedRows(val)
    },
    loading(val) {
      this.listLoading = val
    },
    savedSelectedDatas(val) {
      this.initPopLocation()
    },
    customTableKey() {
      this.loadCustomCol()
    }
  },
  mounted() {
  },
  async created() {
    const autoload = this.getAutoload()
    this.loadCustomCol()
    this.tableRef = 'gridTable' + new Date().getTime()
    if (!this.showPager) {
      this.limit = this.pageSizes && this.pageSizes.length > 0 ? this.pageSizes[this.pageSizes.length - 1] : 50
    } else {
      this.limit = this.pageSizes.indexOf(this.limit) > -1 ? this.limit : this.pageSizes[0]
    }
    if (this.autoload && autoload) {
      this.execRowDataApi()
    }
  },
  deactivated() {
    // 切换页面时，隐藏表格内容的tooltip
    const list = document.getElementsByClassName('el-tooltip__popper');
    if (list.length > 0) {
      Array.from(list).forEach(item => { item.style.display = 'none' })
    }
  },
  methods: {
    getTable() {
      return this.$refs[this.tableRef]
    },
    getCustomColKeys() {
      return this.customColKeys
    },
    // 获取路由query, params中的 autoload，如果该值为 false 或 'false'，则返回 false，反之返回 true
    getAutoload() {
      const { query, params } = this.$route
      const queryAutoload = query ? query.autoload : ''
      const paramsAutoload = params ? params.autoload : ''
      setTimeout(() => {
        // 删除 query, params 中的 autoload
        query && delete this.$route.query.autoload
        params && delete this.$route.params.autoload
      }, 300);
      return !(String(queryAutoload) === 'false' || String(paramsAutoload) === 'false')
    },
    compare(a, b) {
      if (isIPv4(a) && isIPv4(b)) {
        const m = ipv4ToInt(a)
        const n = ipv4ToInt(b)
        return (m > n) ? 1 : (m < n) ? -1 : 0
      } else {
        if (typeof a == 'string') {
          return a.localeCompare(b, 'zh-CN')
        } else {
          return (a > b) ? 1 : (a < b) ? -1 : 0
        }
      }
    },
    loadMoreData(type) {
      if (!this.showPager && this.tempRowDatas.length > this.limit) {
        this.page = this.page + type
        const index = (this.page - 1) * this.limit
        const showDatas = this.tempRowDatas.slice(index, index + this.limit * 2)
        this.rowData.splice(0, this.rowData.length, ...showDatas)
      }
    },
    sortChange(obj) {
      this.sortName = obj.prop
      this.sortOrder = obj.order
      const sortProp = !obj.prop ? this.defaultSortOpt.prop : obj.prop
      let sortOrder = !obj.order ? this.defaultSortOpt.order : obj.order
      sortOrder = sortOrder.startsWith('a') ? 'asc' : 'desc'
      if (this.onSortChange) {
        this.onSortChange({ sortName: sortProp, sortOrder: sortOrder, sortable: obj.column.sortable })
      } else {
        this.execRowDataApi({ sortName: sortProp, sortOrder: sortOrder, searchCount: false })
      }
    },
    checkSelectedRows(checkedKeys) {
      const tempCheckedKeys = [...checkedKeys]
      tempCheckedKeys.length == 0 && this.clearSelection()
      this.$nextTick(() => {
        if (tempCheckedKeys.length > 0 && this.rowData && this.rowData.length > 0) {
          this.rowData.forEach(el => {
            const index = tempCheckedKeys.indexOf(el[this.rowKey])
            if (index > -1) {
              this.toggleRowSelection(el)
            } else {
              this.toggleRowSelection(el, false)
            }
          })
        }
      })
    },
    toggleAllSelection() { // 选中全部
      const tableRef = this.getTable()
      this.rowData.forEach(el => {
        tableRef.toggleRowSelection(el, true)
      })
      if (this.isSavedSelected || this.localLazy) {
        const selection = this.rowDatas.filter(this.selectable)
        this.savedSelectedChange(selection, selection)
      }
    },
    toggleRowSelection(row, selected = true) {
      this.getTable().toggleRowSelection(row, selected)
      if (this.isSavedSelected || this.localLazy) {
        const selection = selected ? this.savedSelectedDatas.concat([row]) : this.savedSelectedDatas
        this.savedSelectedChange(selection, row)
      }
    },
    // 重置 reset table
    doLayout() {
      this.getTable() && this.getTable().doLayout()
    },
    updateLimit(val) {
      this.limit = val
      this.$emit('updateLimit', val)
    },
    updatePage(val) {
      this.page = val
      this.$emit('updatePage', val)
    },
    updateMaxPage(val) {
      this.maxPage = val
    },
    handlePagination(page, pageSize) {
      this.execRowDataApi({ searchCount: false, dlpTotal: this.dlpTotal })
      // 滚动到首行
      const el = this.$el.querySelector('.el-table__body-wrapper')
      el.scrollTo(0, 0)
    },
    disablePagination() {
      this.enablePagination()
      // 设置 200毫秒后禁用，避免 禁用->启用 耗时很短时分页组件闪烁的情况
      this.paginationTimer = setTimeout(() => {
        this.paginationDisabled = true
      }, 200);
    },
    enablePagination() {
      clearTimeout(this.paginationTimer)
      this.paginationDisabled = false
    },
    // 执行加载表数据
    execRowDataApi(option) {
      this.disablePagination()
      const oldTotal = this.total
      if (option) {
        option = JSON.parse(JSON.stringify(option))
        this.page = option.page ? option.page : this.page
        this.limit = option.limit ? option.limit : this.limit
        this.sortName = option.searchReport == 3 ? 'createTime' : (option.sortName ? option.sortName : this.sortName)
        this.sortOrder = option.searchReport == 3 ? 'desc' : (option.sortOrder ? option.sortOrder : this.sortOrder)
        this.dlpTotal = option.dlpTotal ? option.dlpTotal : 0
      } else {
        option = {}
      }
      if (!option.slient) {
        this.listLoading = true
      }
      option.page = this.page
      option.limit = this.limit
      option.sortName = option.searchReport == 3 ? 'createTime' : (this.sortName ? this.sortName : this.defaultSortOpt.prop)
      option.sortOrder = option.searchReport == 3 ? 'desc' : (this.sortOrder ? this.sortOrder : this.defaultSortOpt.order)
      option.dlpTotal = this.dlpTotal
      if (option.sortName) {
        option.sortOrder = option.sortOrder && option.sortOrder.startsWith('a') ? 'asc' : 'desc'
      }
      if (option.searchCount !== false) {
        this.showLoadTotal = false
      }
      if (option.searchReport == 3) {
        this.getTable().clearSort()
      }
      this.curQueryParam = option
      return this.rowDataApi(option).then(response => {
        this.enablePagination()
        if (!response.data) return
        this.noTotal = !!response.data.hideTotal
        if (response.data instanceof Array) {
          this.rowData = response.data
          this.total = this.rowData.length
        } else {
          this.rowData = response.data.items || []
          this.total = response.data.total
          this.dlpTotal = response.data.dlpTotal
        }
        // 这边需要排除空值的情况，因此使用option.searchCount === false
        if (option.searchCount === false && this.retainPages && oldTotal > this.total) {
          this.total = oldTotal
        }
        this.listLoading = false
        this.afterLoad(this.rowData, this)
        this.$nextTick(() => {
          // 分页的情况下，表格中没有数据，但是 total 大于 0，说明可能是查询到范围以外的页面，需要根据当前的 page 重新查询数据
          if (this.showPager && this.getDatasByApi && this.rowData.length == 0 && this.total > 0) {
            // 限制重新查询的次数
            if (this.reSearchCount < 1) {
              this.reSearchCount++
              this.execRowDataApi({ page: this.page, searchCount: false, dlpTotal: this.dlpTotal })
            } else {
              console.error('查询数据有误，需要排查！')
              this.reSearchCount = 0
            }
          } else {
            this.reSearchCount = 0
          }
          // 重置table，避免表格错位(固定列)
          this.doLayout()
        })
      }).catch(() => {
        this.rowData = []
        this.total = 0
        this.listLoading = false
      })
    },
    // 执行加载子表数据
    execSubRowDataApi(index, option) {
      this.subListLoading = true
      const opt = {
        page: 1,
        limit: 1000,
        sortName: 'id',
        sortOrder: 'desc',
        ...option
      }
      this.subRowDataApi(opt).then(response => {
        // 子表数据放在对应行数据的children
        this.rowData[index].children = response.data instanceof Array ? response.data : response.data.items
        this.subListLoading = false
      })
    },
    // 执行获取总数
    execTotalApi() {
      this.totalLoading = true
      this.totalApi(this.curQueryParam).then(response => {
        this.loadTotalValue = response.data
        this.showLoadTotal = true
        this.totalLoading = false
      })
    },
    // 当前选中行变化时的回调
    currentChange(currentRow, oldCurrentRow) {
      this.$emit('currentChange', currentRow, oldCurrentRow)
    },
    rowDblclick(rowData, column, event) {
      this.$emit('row-dblclick', rowData, column, event)
    },
    rowClick(rowData, column, event) {
      this.$emit('row-click', rowData, column, event)
    },
    cellClick(row, column, cell, event) {
      this.$emit('cell-click', row, column, cell, event)
    },
    checkboxChange(value, rowData, col) {
      if (typeof col.checkboxChange == 'function') {
        return col.checkboxChange(!value, rowData, col)
      } else {
        rowData[col.prop] = !value
      }
    },
    select(selection, row) {
      this.$emit('select', selection, row)
      if (this.isSavedSelected || this.localLazy) {
        this.savedSelectedChange(selection, row)
      }
    },
    selectAll(selection) {
      if (this.localLazy) {
        const select = selection.length > 0
        if (select) {
          selection = this.rowDatas.filter(this.selectable)
        } else {
          this.clearSaveSelection()
        }
      }
      this.$emit('select-all', selection)
      if (this.isSavedSelected || this.localLazy) {
        this.savedSelectedChange(selection, selection)
      }
    },
    savedSelectedChange(selection, row) {
      let selectedArr = row.length == undefined ? [row] : row
      const key = this.rowKey
      // 当selectedArr为空数组时，说明是当前页面取消全选，则将当前页面的rowData从已选数据中删除
      if (selectedArr.length == 0) selectedArr = this.rowData
      selectedArr.forEach(el => {
        // 当前记录在已保存数组中的索引
        const saveIndex = this.savedSelectedKeys.indexOf(el[key])
        if (saveIndex == -1) {
          if (selection.indexOf(el) > -1) {
            // 当前记录在勾选记录中，所以将当前记录添加到已保存数组中
            this.savedSelectedDatas.push(el)
            this.savedSelectedKeys.push(el[key])
          }
        } else {
          if (selection.indexOf(el) > -1) {
            // 当前记录在勾选记录中，更新已保存数组中的数据
            this.savedSelectedDatas.splice(saveIndex, 1, el)
            this.savedSelectedKeys.splice(saveIndex, 1, el[key])
          } else {
            // 当前记录不在勾选记录中，删除已保存数组中的数据
            this.savedSelectedDatas.splice(saveIndex, 1)
            this.savedSelectedKeys.splice(saveIndex, 1)
          }
        }
      })
    },
    // 已选列表 数据的 title
    selectedDataTitle(row, prop) {
      if (typeof this.selectedDataTitleFormatter == 'function') {
        return this.selectedDataTitleFormatter(row, prop)
      } else {
        return this.selectedDataLabel(row, prop)
      }
    },
    // 已选列表 数据的 label
    selectedDataLabel(row, prop) {
      if (typeof this.selectedDataLabelFormatter == 'function') {
        return this.selectedDataLabelFormatter(row, prop)
      } else if (!row.hasOwnProperty(prop)) {
        prop = this.showColModel[0].prop
      }
      return row[prop]
    },
    selectedDatasDelete(e, index) {
      if (typeof index == 'number') {
        this.savedSelectedDatas.splice(index, 1)
        this.savedSelectedKeys.splice(index, 1)
      } else {
        this.clearSaveSelection()
      }
      this.checkSelectedRows(this.savedSelectedKeys)
    },
    initPopLocation() {
      if (this.selectedListVisible) {
        // 手动关闭popover再打开，促使popover框重新定位
        this.selectedListVisible = false
        if (this.savedSelectedKeys.length > 0) {
          this.$nextTick(() => {
            this.selectedListVisible = true
          })
        }
      }
    },
    selectionChange(selection) {
      if (this.isSavedSelected || this.localLazy) {
        selection = this.savedSelectedDatas
      }
      this.selectedData = selection
      this.$nextTick(() => {
        this.$emit('selectionChangeEnd', selection)
      })
    },
    // 行数据展开或收起时的回调
    expandChange(currentRow, expandedRows) {
      if (Array.isArray(expandedRows)) {
        // 当前行在table中的位置
        const index = this.rowData.indexOf(currentRow)
        // true：展开，false：收起
        const expand = expandedRows.indexOf(currentRow) > -1
        if (expand) {
          this.execSubRowDataApi(index, { id: currentRow.id, index })
        } else {
          this.rowData[index].children = []
        }
      }
    },
    clearSelection() {
      if (this.multiSelect) {
        this.getTable().clearSelection()
        this.clearSaveSelection()
      } else {
        this.setCurrentRow(null)
      }
    },
    // 当使用is-save-select时，通过此方法清空
    clearSaveSelection() {
      this.savedSelectedDatas.splice(0)
      this.savedSelectedKeys.splice(0)
    },
    setCurrentRow(row) {
      this.getTable().setCurrentRow(row)
    },
    getSelectedIds() {
      const selectedData = (this.isSavedSelected || this.localLazy) ? this.savedSelectedDatas : this.selectedData
      return selectedData.map(data => data.id)
    },
    getSelectedKeys() {
      const selectedData = (this.isSavedSelected || this.localLazy) ? this.savedSelectedDatas : this.selectedData
      return selectedData.map(data => data[this.rowKey])
    },
    getSelectedDatas() {
      const selectedData = (this.isSavedSelected || this.localLazy) ? this.savedSelectedDatas : this.selectedData
      return [...selectedData]
    },
    getDataByParam(filed, value) {
      const rowData = this.localLazy ? this.rowDatas : this.rowData
      return rowData.find(data => data[filed] == value)
    },
    getDatas() {
      const datas = this.localLazy ? this.rowDatas : this.rowData
      return [...datas]
    },
    getIds() {
      const datas = this.localLazy ? this.rowDatas : this.rowData
      return datas.map(data => data.id)
    },
    getCurrPage() {
      return this.page
    },
    getTotalPages() {
      return Math.ceil(this.total / this.limit)
    },
    getTotal() {
      return this.total
    },
    getDLPTotal() {
      return this.dlpTotal
    },
    // 当前页面是否是最后一页，是返回页数，不是返回false
    isLastPage() {
      const isLastPage = this.page === this.getTotalPages()
      return isLastPage && this.page
    },
    // 通过data传入数据的情况下，使用该方法需要传入datas，外部的数据才能更新
    addRowData: function(data, datas) {
      if (data) {
        const rowDatas = datas || (this.localLazy ? this.rowDatas : this.rowData)
        rowDatas.unshift(data)
        this.total++
      }
    },
    // 通过data传入数据的情况下，使用该方法需要传入datas，外部的数据才能更新
    updateRowData: function(data, datas) {
      if (data) {
        const rowDatas = datas || (this.localLazy ? this.rowDatas : this.rowData)
        for (let i = 0; i < rowDatas.length; i++) {
          const row = rowDatas[i]
          const key = this.rowKey
          if (row[key] === data[key]) {
            if (Object.prototype.hasOwnProperty.call(row, 'id')) {
              data.id = row.id
            }
            rowDatas.splice(i, 1, data)
            break
          }
        }
      }
    },
    // 通过data传入数据的情况下，使用该方法需要传入datas，外部的数据才能更新
    deleteRowData: function(keys, datas) { // keys数组，eg [1,2]
      if (this.$props.rowDatas.length > 0 || this.localType) {
        // 通过rowDatas加载数据，则直接删除对应rowKey的数据
        const rowDatas = datas || (this.localLazy ? this.rowDatas : this.rowData)
        const key = this.rowKey
        const tempData = rowDatas.filter(data => {
          return keys.indexOf(data[key]) == -1
        })
        rowDatas.splice(0, rowDatas.length, ...tempData)
        return rowDatas
      } else {
        // 通过api加载数据的情况下，修改查询条件并重新加载数据
        // 删除的数据量会使当前页码减少的值
        const backPages = Math.floor((keys.length - this.rowData.length) / this.limit + 1)
        if (this.isLastPage()) {
          // 当前处于最后一页时，需要减掉 backPages
          const page = this.page - backPages
          this.page = page > 0 ? page : 1
        } else {
          // 当前页码和删除数据后的最大页码，两者取小
          const page = this.getTotalPages() - backPages
          this.page = Math.min(this.page, page)
        }
        this.execRowDataApi()
      }
    },
    clearRowData: function() { // 清空所有的表数据
      this.localLazy ? this.rowDatas.splice(0) : this.rowData.splice(0)
    },
    clearPageData: function() {
      this.page = 1;
      this.total = 0
    },
    handleIcon(row, value) {
      this.$emit('handleIcon', row, value)
    },
    // 自定义列全选/取消全选的方法
    checkAll(boolean) {
      if (boolean) {
        this.customKeys = this.showColModel.map((item, index) => index)
      } else {
        this.customKeys = this.fixedColKey
      }
    },
    async handleCustomCol() {
      if (this.customColFunc) {
        this.customColFunc()
      } else {
        // 避免固定列没有存到数据库，导致没有勾选，这边再次添加
        this.customKeys = Array.from(new Set([...this.customColKeys, ...this.fixedColKey]))
        this.customColDlgVisible = true
      }
    },
    async loadCustomCol() {
      // 自定义列
      if (this.customCol) {
        this.userId = this.$store.getters.userId  // 用户id
        this.menuCode = this.$route.meta.code     // 当前菜单code
        this.tableKey = this.$vnode.data.ref      // 当前GridTable的ref
        let customCol = null                      // 从后台获取自定义列数据
        this.listLoading = true                   // 从后台请求列数据是耗时操作，这边将 table 设置为加载状态
        // 如果 customTableKey 有值，则将 tableKey-customTableKey 作为 tableKey
        const tableKey = this.customTableKey ? `${this.tableKey}-${this.customTableKey}` : this.tableKey
        await getValueByCondition({ sysUserId: this.userId, menuCode: this.menuCode, tableKey: tableKey, type: 4 }).then(res => {
          if (res.data) {
            // 后台保存的自定义列数据和固定列合并、去重
            customCol = Array.from(new Set(res.data.value.split(',').map(Number).concat(this.fixedColKey)))
          }
        })
        // 后台没有保存数据
        if (!customCol) {
          const defaultShowColIndex = []
          this.showColModel.forEach((item, index) => {
            // 配置了 custom 的列，默认不显示，可通过自定义列窗口修改
            if (!item.custom) {
              defaultShowColIndex.push(index)
            }
          })
          customCol = defaultShowColIndex
        }
        this.customColKeys = customCol
        this.listLoading = false
        this.$nextTick(() => {
          this.doLayout()
        })
      }
    },
    saveCustomCol() {
      // 至少需要勾选一个非禁用选项
      if (this.customKeys.length == this.fixedColKey.length) {
        this.$message({
          message: this.$t('pages.chooseAtLeastOneOption'),
          type: 'error',
          duration: 2000
        })
        return
      }
      // todo 增加新字段hiddenProp，储存不显示的列的prop，展示时，先按照该字段展示，若该字段没有值，则按原来的字段 value 来展示
      // 存到后台
      const tableKey = this.customTableKey ? `${this.tableKey}-${this.customTableKey}` : this.tableKey
      insertPersonalization({
        sysUserId: this.userId,
        value: [...this.customKeys].join(','),
        menuCode: this.menuCode,
        tableKey: tableKey,
        type: 4
      }).then(res => {
        this.customColKeys = res.data.value.split(',').map(Number)
        this.customColDlgVisible = false
        this.submitting = false
        this.$nextTick(() => {
          // 重置table，避免表格错位
          this.doLayout()
        })
      }).catch(res => {
        this.submitting = false
      })
    }
  }
}
export function reloadTableData(postData) {
  this.execRowDataApi(postData)
}
</script>

<style lang="scss" scoped>
.tableBox{
  height: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  clear: both;
}
.custom-slot{
  margin-left: -2px;
  text-align: center;
}
.checked-icon{
  cursor: pointer;
}
.selected-title{
  display: block;
  position: relative;
  background: #fff;
  height: 30px;
  line-height: 28px;
  font-size: 16px;
  font-weight: bold;
  margin: 0 16px;
}
.selected-list{
  list-style: none;
  margin: 0;
  padding: 0 18px 0 24px;
  max-height: 350px;
  overflow: auto;
  li{
    min-height: 30px;
    position: relative;
    padding: 2px 22px 3px 5px;
    cursor: default;
    &:hover{
      background: #e5e5e5;
    }
    span{
      width: 250px;
      display: inline-block;
      position: relative;
      top: 4px;
      word-break:keep-all;
      text-overflow:ellipsis;
      overflow:hidden;
      white-space: nowrap;
    }
    .delete-icon {
      visibility: hidden;
    }
    &:hover .delete-icon {
      visibility: visible;
    }
  }
}
.delete-icon{
  position: absolute;
  right: 5px;
  top: 4px;
  cursor: pointer;
  margin-top: 2px;
  padding: 0;
  &.el-button{
    margin-top: 7px;
  }
}
.custom-checkbox {
  padding-left: 20px;
  label{
    width: 30%;
    margin: 5px 5px 5px 10px;
  }
}
</style>
