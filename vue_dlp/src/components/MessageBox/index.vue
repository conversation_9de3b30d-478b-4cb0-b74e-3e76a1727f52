<!--
  /**
  * 封装 message 组件，可以在任意组件下使用，弥补全屏模式下 element UI 的 message 无法显示的问题
  * eg: <messageBox ref="messageBox"></messageBox>
  *     this.$refs.messageBox.showMessage(option)
  *     option: 
  *            message   消息文字 string
  *            type      消息类型 string  [success/warning/info/error]  默认 info
  *            duration  显示时间, 毫秒。设为 0 则不会自动关闭。  number  默认 3000
  */
-->
<template>
  <div ref="message" :class="messageClass">
    <!-- 消息提示 -->
    <i :class="icon"></i>
    <span>
      {{ message }}
    </span>
    <i class="el-icon-close" @click="closeMessage"></i>
  </div>
</template>

<script>

export default {
  name: 'MessageBox',
  props: {
  },
  data() {
    return {
      message: '',        // 消息文字
      type: 'info',       // 消息类型
      timer: null         // 计时器
    }
  },
  computed: {
    icon() {
      const iconMap = {
        info: 'el-icon el-icon-info',
        success: 'el-icon el-icon-success',
        warning: 'el-icon el-icon-warning',
        error: 'el-icon el-icon-error'
      }
      return iconMap[this.type]
    },
    messageClass() {
      const classMap = {
        info: 'message-info',
        success: 'message-success',
        warning: 'message-warning',
        error: 'message-error'
      }
      const className = classMap[this.type] || ''
      return `message-box ${className}`
    }
  },
  methods: {
    // 打开的方法
    showMessage(option) {
      const { message, type = 'info', duration = 3000 } = option
      this.type = type;
      if (type === 'success') {
        this.message = message || '这是一条成功消息';
      } else if (type === 'error') {
        this.message = message || '这是一条失败消息';
      } else if (type === 'warning') {
        this.message = message || '这是一条警告消息';
      } else if (type === 'info') {
        this.message = message || '这是一条普通消息';
      } else {
        this.message = message || '';
      }
      // 过渡
      this.$refs.message.style.transform = 'translate(-50%,75px)';
      if (this.timer) {
        this.timer = clearTimeout(this.timer);
      }
      if (duration != 0) {
        this.timer = setTimeout(() => {
          this.closeMessage();
        }, duration);
      }
    },
    // 关闭的方法
    closeMessage() {
      if (this.timer) {
        this.timer = clearTimeout(this.timer);
      }
      // 过渡
      this.$refs.message.style.transform = 'translate(-50%, -10px)';
    }
  }
}
</script>

<style scoped lang="scss">
  .message-box {
    min-width: 380px;
    width: 20%;
    height: 50px;

    position: absolute;
    top: -55px;
    left: 50%;
    padding: 15px 15px 15px 20px;
    transform: translateX(-50%);

    font-size: 14px;
    display: flex;
    align-items: center;
    text-align: center;
    background-color: #edf2fc;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    transition: 0.55s;
    z-index: 99999;
  }
  
  .message-success {
    color: #67c23a;
    background-color: #f0f9eb;
    border-color: #e1f3d8;
  }
  .message-error {
    color: #f56c6c;
    background-color: #fef0f0;
    border-color: #fde2e2;
  }
  .message-warning {
    color: #e6a23c;
    background-color: #fdf6ec;
    border-color: #faecd8;
  }

  .message-info {
    color: #909399;
    background-color: #edf2fc;
    border-color: #ebeef5;
  }
  
  .el-icon {
    margin-right: 10px;
    margin-top: -1px;
  }
  
  .el-icon-close {
    position: absolute;
    top: 50%;
    right: 15px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    cursor: pointer;
    color: #C0C4CC;
    font-size: 16px;
    &:hover {
      color: #909399;
    }
  }
</style>
