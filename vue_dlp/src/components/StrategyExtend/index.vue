<template>
  <div v-if="stgExtendShow" class="button-container">
    <el-button size="mini" @click="handleClick">
      <svg-icon icon-class="linkBreak" />
      {{ $t('pages.inheritConfig') }}
    </el-button>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="false"
      :title="$t('pages.inheritConfig')"
      :visible.sync="configVisible"
      width="700px"
    >
      <div slot="title" class="el-dialog__title">
        <svg-icon icon-class="linkBreak" />
        {{ $t('pages.inheritConfig') }}
      </div>
      <div class="rule-grid">
        <div class="toolbar">
          <span>{{ $t('pages.inheritConfigTip') }}</span>
          <span style="margin-left: auto">
            <el-button icon="el-icon-plus" size="small" style="margin: 0" @click="handleAdd">
              {{ $t('button.add') }}
            </el-button>
            <el-button icon="el-icon-delete" size="small" :disabled="!deleteAble" style="margin: 0" @click="handleDelete">
              {{ $t('button.delete') }}
            </el-button>
          </span>
        </div>
        <grid-table
          ref="gridTable"
          :height="300"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          :show-pager="false"
          @selectionChangeEnd="selectionChangeEnd"
        />
      </div>
      <div slot="footer" class="dialog-footer" style="margin-right: 18px">
        <common-downloader button-type="primary" :button-name="$t('button.export')" button-size="mini" @download="exportStgObjInterrupt"/>
        <el-button @click="configVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="false"
      :title="objFormTextMap[isAddObj ? 'add' : 'update']"
      :visible.sync="formDlgVisible"
      width="440px"
    >
      <Form ref="objForm" :model="formTemp" :rules="formRule" label-position="right" label-width="100px">
        <FormItem :label="$t('table.applicationObjType')">
          <el-select v-model="formTemp.objectType" style="padding-left: 5px" @change="changeObjType">
            <el-option v-show="terminalUseAble" :value="3" :label="$t('components.terminalG')"/>
            <el-option v-show="userUseAble" :value="4" :label="$t('components.userG')"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.applicationObj')" prop="objectIds">
          <tree-select
            v-show="isAddObj"
            ref="multiObjectTree"
            class="targetObject"
            :height="350"
            :width="400"
            :local-search="false"
            multiple
            check-strictly
            :checked-keys="formTemp.objectIdsForAdd"
            is-filter
            :filter-key="filterKey"
            :leaf-key="formTemp.objectType === 3 ? 'terminal' : 'user'"
            @change="changeObjIds"
          />
          <tree-select
            v-show="!isAddObj"
            ref="singleObjectTree"
            class="targetObject"
            :height="350"
            :width="400"
            :local-search="false"
            :checked-keys="formTemp.objectIdsForUpdate"
            is-filter
            :filter-key="filterKey"
            :leaf-key="formTemp.objectType === 3 ? 'terminal' : 'user'"
            @change="changeObjIds"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="isAddObj ? addObjects() : updateObjects()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="formDlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addStgObjInterrupt,
  deleteStgObjInterrupt,
  listStgObjInterrupt,
  updateStgObjInterrupt,
  exportStgObjInterrupt,
  listNotExistStgAndInterruptObj
} from '@/api/strategyExtend'
import { findNode, getNodePathByList } from '@/utils/tree'
import StgTargetFormItem from '@/components/StrategyTargetTree/StgTargetFormItem'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'StrategyExtend',
  components: { CommonDownloader },
  props: {
    stgType: {
      // 策略编号
      type: [Number, String],
      default: ''
    },
    scope: {
      // 策略应用类型（1终端、2操作员、3两者）
      type: Number,
      default: undefined
    },
    osTypeFilter: {
      type: Number,
      default: undefined
    },
    terminalFilter: { type: Function, default: null },                        // 终端树节点的过滤方法
    userFilter: { type: Function, default: null }                            // 操作员树节点的过滤方法
  },
  data() {
    return {
      stgExtendShow: true,
      configVisible: false,
      submitting: false,
      formDlgVisible: false,
      deleteAble: false,
      isAddObj: true,
      usedScope: 3, // 策略应用类型（1终端、2操作员、3两者）
      osType: 7,    // 终端节点可配置的类型的总值，根据 策略基础设置 进行调整
      terminalFilterFunc: undefined,
      objFormTextMap: {
        'add': this.i18nConcatText(this.$t('table.applicationObj'), 'create'),
        'update': this.i18nConcatText(this.$t('table.applicationObj'), 'update')
      },
      colModel: [
        { prop: 'objectType', label: 'applicationObjType', width: '60', formatter: this.objectTypeFormatter, sort: 'custom', iconFormatter: this.objectTypeIconFormatter },
        { prop: 'objectName', label: 'applicationObj', width: '120' },
        { prop: '', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      // 临时更新对象
      tempUpdateObj: {},
      stgObjInterruptMap: {},
      formTemp: {
        objectType: 3,
        objectIdsForAdd: [],
        objectIdsForUpdate: []
      },
      formRule: {
        objectIds: { validator: this.objectIdsValidator, trigger: 'blur' }
      }
    }
  },
  computed: {
    // 终端是否可用
    terminalUseAble() {
      return this.usedScope != 2
    },
    // 操作员是否可用
    userUseAble() {
      return this.usedScope != 1
    },
    gridTable() {
      return this.$refs['gridTable']
    },
    filterKey() {
      if (this.formTemp.objectType === 3) {
        return typeof this.terminalFilter == 'function' ? this.terminalFilter : this.terminalFilterFunc
      }
      return this.userFilter
    }
  },
  watch: {
    'stgType'(val) {
      this.stgObjInterruptMap = {}
      this.useStgTypeInit()
      this.loadStgObjInterrupt()
    },
    'osType'() {
      this.initFilterKey()
    }
  },
  created() {
    this.initBaseStgConfig()
    this.initFilterKey()
    this.stgExtendShow = !this.$route.path.endsWith('/prepare')
    this.loadStgObjInterrupt()
  },
  methods: {
    exportStgObjInterrupt(file) {
      const opts = { file, jwt: true, topic: this.$route.name }
      const title = this.$t('pages.inheritConfig') + '_' + this.$t('route.' + `${this.$route.meta.title}`)
      const titleI18nKey = 'route.' + `${this.$route.meta.title}`
      exportStgObjInterrupt(this.stgType, opts, title, titleI18nKey);
    },
    objectTypeFormatter(row, data) {
      let str
      switch (Number(data)) {
        case 1: str = this.$t('table.terminal'); break
        case 2: str = this.$t('table.user'); break
        case 3: str = this.$t('table.terminalGroup'); break
        case 4: str = this.$t('table.userGroupName'); break
        default: str = this.$t('text.unknown')
      }
      return str
    },
    objectTypeIconFormatter(row) {
      let icon
      const title = this.objectTypeFormatter(row, row.objectType)
      switch (Number(row.objectType)) {
        case 1: icon = [{ class: 'terminal', title }]; break;
        case 2: icon = [{ class: 'user', title }]; break;
        case 3: icon = [{ class: 'terminalGroup', title }]; break;
        case 4: icon = [{ class: 'userGroup', title }]; break;
        default: icon = undefined
      }
      return icon
    },
    objectIdsValidator(rule, value, callback) {
      if ((this.isAddObj && this.formTemp.objectIdsForAdd.length === 0) || (!this.isAddObj && this.formTemp.objectIdsForUpdate.length === 0)) {
        callback(new Error(this.$t('pages.placeChooseApplicationObj')))
      }
      if (!this.isAddObj) {
        const selectNode = this.$refs['singleObjectTree'].getSelectedNode()[0]
        // 当获取不到选择的节点时，抛异常
        if (!selectNode) {
          callback(new Error(this.$t('pages.placeChooseApplicationObj')))
          return
        }
        const rowDatas = this.gridTable.getDatas() || []
        for (const rowData of rowDatas) {
          if (rowData['objectType'] == selectNode['type'] &&
            rowData['objectId'] == selectNode['dataId'] &&
            this.tempUpdateObj.id != rowData.id) {
            callback(new Error(this.$t('valid.customizeSameName', { obj: this.$t('table.applicationObj') })))
          }
        }
      }
      callback()
    },
    changeObjType(val) {
      this.formTemp.objectType = val
      this.isAddObj && this.$refs['multiObjectTree'] && this.$refs['multiObjectTree'].clearSelectedKeys()
      !this.isAddObj && this.$refs['singleObjectTree'] && this.$refs['singleObjectTree'].clearSelectedKeys()
    },
    changeObjIds(selectKeys) {
      this.isAddObj && (this.formTemp.objectIdsForAdd = selectKeys)
      !this.isAddObj && (this.formTemp.objectIdsForUpdate = [selectKeys])
      this.$refs['objForm'] && this.$refs['objForm'].validateField(['objectIds'])
    },
    rowDataApi(option) {
      return listStgObjInterrupt(this.stgType)
    },
    loadStgObjInterrupt() {
      if (!this.stgType) {
        return
      }
      this.$nextTick(() => {
        this.getStgObjInterruptMap().then(resp => {
          console.log('stgObjInterruptMap', resp)
          this.stgObjInterruptMap = resp
        })
      })
    },
    /**
     * 继承中断配置数据，格式如下：{1: { 0 : {'G':{2:终端组名1, 3:终端组名2}, 'D': {10001: 终端名1}} }, 2: { 0 : {'G':{2:操作员组名1, 3:操作员组名2}, 'D': {1: 操作员名}} }}
     * {数据类型（1-终端）: { 部门ID : {'G':{部门id: 部门名}, 'D': {终端id: 终端名}} }}
     * {数据类型（2-操作员）: { 部门ID : {'G':{部门id: 部门名}, 'D': {操作员id: 操作员名}} }}
     * @returns {Promise<{}>}
     */
    async getStgObjInterruptMap() {
      // 延迟执行，确保store.getters.termTreeList、userTreeList、deptTreeList 已经获取到数据，不然数据会不准
      return new Promise(resolve => setTimeout(async() => {
        // 继承中断配置数据，格式如下：{1: { 0 : {'G':[2,3], 'D': [1001]} }, 2: { 0 : {'G':[2,3], 'D': [1,2]} }}
        const stgObjInterruptMap = {}
        await listNotExistStgAndInterruptObj(this.stgType).then(resp => {
          const termNodeList = this.$store.getters.termTreeList
          const userNodeList = this.$store.getters.userTreeList
          const groupNodeList = this.$store.getters.deptTreeList
          if (!resp || !resp.data) {
            resolve(stgObjInterruptMap)
            return
          }
          for (const row of resp.data) {
            const objType = row.objectType
            const objId = row.objectId
            let objName = ''
            const targetObjType = objType === 1 || objType === 3 ? 1 : 2
            var isGroup = false
            var groupNodeId = null
            if (objType === 1) { // 终端节点
              const termNode = findNode(termNodeList, objId, 'dataId')
              groupNodeId = termNode && termNode.parentId
              objName = termNode && termNode.label
            } else if (objType === 2) { // 操作员节点
              const userNode = findNode(userNodeList, objId, 'dataId')
              groupNodeId = userNode && userNode.parentId
              objName = userNode && userNode.label
            } else {
              isGroup = true
              groupNodeId = 'G' + objId
            }
            const groupNodePath = getNodePathByList(groupNodeList, 'id', groupNodeId)
            if (!stgObjInterruptMap.hasOwnProperty(targetObjType)) {
              stgObjInterruptMap[targetObjType] = {}
            }
            // 格式如下：{ 0 : {'G':{2:终端组名1, 3:终端组名2}, 'D': {10001: 终端名1}} }
            const thisInterruptMap = stgObjInterruptMap[targetObjType]
            for (const groupNode of groupNodePath) {
              const groupId = groupNode.dataId
              if (isGroup && groupId == objId) {
                objName = groupNode.label
                continue
              }
              if (!thisInterruptMap.hasOwnProperty(groupId)) {
                thisInterruptMap[groupId] = {}
              }
              if (!thisInterruptMap[groupId][isGroup ? 'G' : 'D']) {
                thisInterruptMap[groupId][isGroup ? 'G' : 'D'] = {}
              }
              thisInterruptMap[groupId][isGroup ? 'G' : 'D'][objId] = objName
            }
          }
        })
        resolve(stgObjInterruptMap)
      }, 2000));
    },
    /**
     *
     * @param row
     * @param extendDatas    继承中断配置数据，格式如下：{1: { 0 : {'G':[2,3], 'D': [1001]} }, 2: { 0 : {'G':[2,3], 'D': [1,2]} }}
     * @returns {[{class: string, title: *}]|[{class: string, title: *, style: string}]}
     */
    stgExtendIconFormatter(row) {
      const { objectType, objectGroupIds } = row
      if (!objectType || !objectGroupIds) {
        return [] // 应用对象为分组时，才可能存在策略配置但是下级对象被中断的情况
      }
      const extendDatas = this.stgObjInterruptMap
      const objTypeId = objectType === 1 || objectType === 3 ? 1 : 2
      const interruptGroupObjIdMap = extendDatas[objTypeId]
      const thisStgInterruptObj = {}
      const thisStgInterruptGroup = {}
      if (interruptGroupObjIdMap) {
        for (const i in objectGroupIds) {
          const groupId = objectGroupIds[i]
          const interruptObjMap = interruptGroupObjIdMap[groupId]
          if (interruptObjMap) {
            if (interruptObjMap['D']) {
              Object.assign(thisStgInterruptObj, interruptObjMap['D'])
            }
            if (interruptObjMap['G']) {
              Object.assign(thisStgInterruptGroup, interruptObjMap['G'])
            }
          }
        }
      }
      let iconLabel = ''
      if (Object.keys(thisStgInterruptObj).length > 0) {
        iconLabel += this.$t(objTypeId === 1 ? 'pages.terminal' : 'pages.user') + ': ' + Object.values(thisStgInterruptObj).join(',') + '\r\n'
      }
      if (Object.keys(thisStgInterruptGroup).length > 0) {
        iconLabel += this.$t(objTypeId === 1 ? 'pages.terminalGroup' : 'pages.userGroup1') + ': ' + Object.values(thisStgInterruptGroup).join(',') + '\r\n'
      }
      return iconLabel ? [{ class: 'linkBreak', title: '继承配置中，不继承当前策略且自身未配置策略的下级应用对象\r\n' + iconLabel }] : []
    },
    handleClick() {
      this.configVisible = true
      this.$nextTick(() => {
        this.gridTable && this.gridTable.execRowDataApi()
      })
    },
    initBaseStgConfig() {
      // 获取顺序  优先从prop获取，其次从route的参数中获取，最后再利用策略编号获取，如果全部没有，则取默认两者都有
      this.usedScope = this.scope || this.$route.meta.usedScope || undefined
      this.osType = this.osTypeFilter || this.$route.meta.osType || undefined
      if (this.usedScope > 0 && this.osType > 0) { return }
      // 获取 策略基础设置，并设置 usedScope
      const stgBaseConfigMap = this.$store.getters.stgBaseConfig || {}
      let stgBaseConfig
      if (this.stgType) {
        stgBaseConfig = stgBaseConfigMap[this.stgType]
      } else {
        const menuCode = this.$route.meta.code
        const stgCode = this.$route.meta.stgCode
        for (const config of Object.values(stgBaseConfigMap)) {
          if (config['number'] === stgCode || config['routerPath'] === menuCode) {
            stgBaseConfig = config
            break
          }
        }
      }
      stgBaseConfig = stgBaseConfig || {}
      this.usedScope = this.usedScope || stgBaseConfig['usedScope'] || 3
      this.osType = this.osType || stgBaseConfig['osType'] || 7
    },
    useStgTypeInit() {
      if (this.stgType) {
        const stgBaseConfigMap = this.$store.getters.stgBaseConfig || {}
        const baseConfig = stgBaseConfigMap[this.stgType]
        this.usedScope = baseConfig['usedScope'] || 3
        this.osType = baseConfig['osType'] || 7
      }
    },
    initFilterKey() {
      this.terminalFilterFunc = node => {
        // 分组显示
        if (node.type == 3) { return true }
        // 通过终端类型过滤节点，返回 true 显示
        const dataType = node.dataType
        // 分组显示
        if (dataType == 'G') { return true }
        const termOsType = StgTargetFormItem.methods.osTypeConver(dataType)
        // 使用 & 判断 osType 是否包含 termOsType
        return (this.osType & termOsType) > 0
      }
    },
    handleAdd() {
      // 初始化默认值
      this.formTemp.objectType = this.usedScope === 2 ? 4 : 3
      this.formTemp.objectIdsForAdd = []
      this.isAddObj = true
      this.submitting = false
      this.formDlgVisible = true
      this.$refs['objForm'] && this.$refs['objForm'].clearValidate()
    },
    handleUpdate(row) {
      this.isAddObj = false
      this.tempUpdateObj = row
      const type = Number(row.objectType)
      const dataType = { 1: 'T', 3: 'G', 2: 'U', 4: 'G' }[type]
      this.formTemp.objectType = { 1: 3, 3: 3, 2: 4, 4: 4 }[type]
      this.formTemp.objectIdsForUpdate = [dataType + row.objectId]
      this.submitting = false
      this.formDlgVisible = true
      this.$nextTick(() => {
        this.$refs['objForm'] && this.$refs['objForm'].clearValidate()
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const selectDatas = this.gridTable.getSelectedDatas() || []
        if (selectDatas.length == 0) { return }
        const requestData = selectDatas.map(({ id, stgTypeNumber, objectType, objectId, otherIds }) => { return { id, stgTypeNumber, objectType, objectId, otherIds } })
        deleteStgObjInterrupt(requestData).then(res => {
          this.gridTable.execRowDataApi()
          this.loadStgObjInterrupt()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    addObjects() {
      this.$refs['objForm'].validate(valid => {
        if (valid) {
          const selectedNodes = this.$refs['multiObjectTree'].getSelectedNode() || []
          this.submitting = true
          const rowDatas = this.gridTable.getDatas() || []
          const prepareList = selectedNodes.map(node => {
            const existSomeData = rowDatas.some(rowData => node['type'] == rowData.objectType && node['dataId'] == rowData.objectId)
            return existSomeData ? null : { objectType: node['type'], objectId: node['dataId'] }
          }).filter(node => !!node)
          addStgObjInterrupt(prepareList, this.stgType).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.insertSuccess'),
              type: 'success',
              duration: 2000
            })
            this.formDlgVisible = false
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.loadStgObjInterrupt()
          })
        }
      })
    },
    updateObjects() {
      this.$refs['objForm'].validate(valid => {
        if (valid) {
          const node = this.$refs['singleObjectTree'].getSelectedNode()[0]
          Object.assign(this.tempUpdateObj, { objectType: node['type'], objectId: node['dataId'], objectName: node['label'] })
          this.submitting = true
          updateStgObjInterrupt(this.tempUpdateObj).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.formDlgVisible = false
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.loadStgObjInterrupt()
          })
        }
      })
    },
    selectionChangeEnd(rowDatas) {
      this.deleteAble = rowDatas && rowDatas.length > 0
    }
  }
}
</script>

<style lang="scss" scoped>
  .button-container {
    display: inline-block;
    vertical-align: top;
    >>>.el-button {
      margin-bottom: 0;
    }
  }
  >>>.el-divider.el-divider--horizontal {
    margin: 15px 0;
  }
  .toolbar {
    display: flex;
    height: auto;
    align-items: center;
    margin-bottom: 8px;
    color: #409eff
  }
  >>>.el-form-item__error {
    margin-left: 8px;
  }
</style>
