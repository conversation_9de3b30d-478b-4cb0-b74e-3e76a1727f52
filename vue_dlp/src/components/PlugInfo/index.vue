<!--
  功能：显示控制台插件信息组件
  特点：内部支持心跳机制,支持插件版本校验
  使用方法：
        <plug-info
          ref="plugInfo"
          :loading="loading"
          :support-min-version="supportMinVersion"
          @plugInfoListener="plugInfoEvent"
        />
        supportMinVersion: 支持插件方法功能最低插件版本号
        @plugInfoListener: 插件信息监听器，每次心跳检测时都会向外部发布插件信息
        执行 this.$refs.plugInfo.startPlug()方法，将创建client进行连接，同时开启心跳机制。
        强烈建议：不需要使用组件时手动关闭插件，调用this.$refs.plugInfo.closePlug()方法
-->
<template>
  <usb-plug-info
    ref="plugInfo"
    :loading="loading"
    :connected="pluginWorkOk"
    :interrupted="pluginInterrupted"
    :version="pluginWorkVer"
    :custom-desc="plugVersionLow"
    @connect="testConnect"
  >
    <div slot="customDescSlot">
      <i18n path="pages.pluginUpdateMsg2">
        <common-downloader
          slot="button"
          style="margin-left: 0 !important;"
          :show-button="true"
          :button-name="$t('pages.downloadPlugin')"
          :name="filename"
          button-type="text"
          button-icon=""
          @download="downloadPluginPkg"
        />
        <span slot="operator">{{ $t('table.operate').toLowerCase() }}</span>
      </i18n>
    </div>
  </usb-plug-info>
</template>

<script>
import UsbPlugInfo from '@/views/system/terminalManage/terminal/uterm/plug'
import { Level, Logger, UTermPluginClient } from '@/views/system/terminalManage/terminal/uterm/client';
import { downloadPlugin } from '@/api/system/terminalManage/uterm';
import CommonDownloader from '@/components/DownloadManager/common'
import { getSystemResources } from '@/utils/i18n';

export default {
  name: 'PlugInfo',
  components: { UsbPlugInfo, CommonDownloader },
  props: {
    //  支持该功能最低插件版本号
    supportMinVersion: {
      type: String,
      default: null
    },
    //  日志等级
    logLevel: {
      type: String,
      default: Level.WARN.name
    }
  },
  data() {
    return {
      client: undefined,
      packetEncrypted: true, // 插件报文是否加密
      loading: false,       //  查询信息加载状态
      pluginWorkOk: false, // 插件工作状态是否正常
      pluginWorkVer: undefined, // 当前插件版本
      pluginInterrupted: false, // 插件连接中断
      heartbeatTimer: undefined, // 心跳计时器
      heartbeatEnable: true, // 是否启用心跳检测
      heartbeatNotified: false, // 心跳错误是否已通知，确保插件出错时只抛出弹窗一次
      lastRequestTime: null,    //  最近一次请求插件的时间
      lastHeartbeatInterval: null,  //  计算上次的时间间隔
      reconnectCount: 0, //  重连次数
      plugRequestTimeout: 15000   //   插件获取信息的超时时间 单位：毫秒
    }
  },
  computed: {
    //  最近一次请求超过这个时间，则表示插件已中断
    maxIntervalTime() {
      return this.heartbeatInterval * 3
    },
    // 心跳间隔
    heartbeatInterval() {
      if (this.reconnectCount < 12) {
        return 5000
      }
      if (this.reconnectCount < 24) {
        return 10000
      }
      if (this.reconnectCount < 36) {
        return 15000
      }
      if (this.reconnectCount < 54) {
        return 30000
      }
      if (this.reconnectCount < 69) {
        return 60000
      }
      return 300000
    },
    //  插件版本过低
    plugVersionLow() {
      return this.supportMinVersion && this.pluginWorkVer < this.supportMinVersion
    },
    filename() {
      const systemTitle = getSystemResources('pluginName')
      // `数据泄露防护系统插件(V${this.version}).exe`
      return this.$t('pages.dlpPluginName', { systemTitle: systemTitle || '控制台插件', version: this.version || '1.01.230824.SC' })
    }
  },
  created() {
    this.logger = new Logger(Level.INFO)
  },
  deactivated() {
    this.logger.warn('UsbTerm component is deactivated.')
  },
  destroyed() {
    this.logger.warn('UsbTerm component is destroyed.')
    this.handleClose()
  },
  methods: {
    /**
     * 启动
     */
    start() {
      this.handleClose()
      this.testConnect().then(res => {
        if (res.code === 1) {
          this.heartbeat()
        }
      })
    },
    /**
     * 初始化数据
     */
    initData() {
      this.lastHeartbeatInterval = null
      this.lastRequestTime = null
      this.heartbeatNotified = false
      this.reconnectCount = 0
      this.heartbeatTimer = undefined
      this.heartbeatEnable = true
      this.pluginWorkOk = false
      this.pluginWorkVer = undefined
      this.pluginInterrupted = false
      this.client = undefined
    },
    /**
     * 关闭插件插件
     */
    handleClose() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        console.log('<plug-info> close heartbeat!')
      }
      if (this.client) {
        this.client.close()
      }
      this.initData();
    },
    /**
     * 测试连接
     * @param port
     */
    testConnect(port) {
      if (this.client) {
        this.client.close()
      }
      this.client = new UTermPluginClient(this, port, Level.of(this.logLevel), this.packetEncrypted)
      this.publishPlugEvent()
      return this.connect();
    },
    /**
     * 连接
     * code = 1   连接成功
     * code = 2   版本过低
     * code = 0   连接失败
     */
    connect() {
      this.checkClient()
      if (this.client.close) {
        this.client.connect()
      }
      return this.client.getPlugInfo(this.plugRequestTimeout).then(data => {
        //  当前插件版本号
        const plugVersion = data.PlugVersion || ''
        this.pluginWorkVer = plugVersion
        //  若有配置支持的版本号时，校验版本号
        if (this.supportMinVersion && !this.verifyPlugVersion(plugVersion, this.supportMinVersion)) {
          this.pluginWorkOk = false
          this.pluginInterrupted = true
          //  插件版本过低，请升级插件
          this.$notify({
            title: this.$t('text.fail'),
            message: UTermPluginClient.normalizeErrs(this.$t('pages.consolePlugVersionLow')),
            type: 'error',
            duration: 2000
          })
          return Promise.resolve({
            code: 2,
            pluginWorkOk: this.pluginWorkOk,
            pluginInterrupted: this.pluginInterrupted,
            data,
            error: this.$t('pages.consolePlugVersionLow')
          })
        }
        this.pluginWorkOk = data.PlugWorkType === 0
        this.pluginInterrupted = !this.pluginWorkOk
        return Promise.resolve({
          code: 1,
          pluginWorkOk: this.pluginWorkOk,
          pluginInterrupted: this.pluginInterrupted,
          data
        })
      }).catch(error => {
        this.pluginWorkOk = false
        this.notifyFail(error)
        this.pluginInterrupted = true
        return Promise.resolve({
          code: 0,
          pluginWorkOk: this.pluginWorkOk,
          pluginInterrupted: this.pluginInterrupted,
          error
        })
      }).finally(() => {
        this.heartbeat()
        this.publishPlugEvent();
      })
    },
    /**
     * 通知插件连接失败
     * @param errMsg
     */
    notifyFail(errMsg) {
      if (!this.heartbeatNotified) {
        this.$notify({
          title: this.$t('text.fail'),
          message: UTermPluginClient.normalizeErrs(errMsg),
          type: 'error',
          duration: 2000
        })
        this.heartbeatNotified = true
      }
    },
    /**
     * 校验插件版本号是否支持该功能
     * currentPlugVersion: 当前插件版本号
     * supportMinVersion: 当前功能插件支持的最低版本
     * @returns {boolean}
     */
    verifyPlugVersion(currentPlugVersion, supportMinVersion) {
      return currentPlugVersion >= supportMinVersion;
    },
    /**
     * 心跳机制
     * 通过heartbeat方法内部循环调用达到循环重复执行效果，通过setTimeout来达到间隔效果
     */
    heartbeat() {
      if (!this.heartbeatEnable) {
        return;
      }
      if (this.heartbeatTimer) {
        return
      }
      console.log('<plug-info> start heartbeat!')
      this.lastHeartbeatInterval = this.heartbeatInterval
      this.heartbeatTimer = setInterval(() => {
        if (!this.client || !this.heartbeatEnable) {
          clearInterval(this.heartbeatTimer)
          this.heartbeatTimer = undefined
          this.lastRequestTime = undefined
          this.lastHeartbeatInterval = undefined
          return
        }

        //  若上次执行的时间超过指定时间时，直接返回失败，目的：当插件正在连接被中断时，会导致解析异常，插件无法返回then或catch，导致无法感知插件的状态，
        if (this.lastRequestTime !== null && new Date().getTime() - this.lastRequestTime > this.maxIntervalTime) {
          this.pluginWorkOk = false
          this.pluginInterrupted = true
          this.heartbeatEnable = false
          this.notifyFail(this.$t('pages.plugBreakOff'))
          return
        }

        //  当心跳机制的时间间隔发生改变时，重新创建Interval
        if (this.heartbeatTimer && this.lastHeartbeatInterval !== this.heartbeatInterval) {
          clearInterval(this.heartbeatTimer)
          this.heartbeatTimer = undefined
          this.heartbeat();
        }

        //  尝试连接
        this.connect().then(res => {
          if (res.code === 1) { //  连接成功
            this.reconnectCount = 0
            if (res.pluginWorkOk) {
              this.heartbeatNotified = false
            }
          } else if (res.code === 0 || res.code === 2) {    //  连接失败
            this.notifyFail(res.error)
            //  若插件-client已关闭时，尝试一次重新连接
            if (this.client.closed) {
              this.reconnectCount++
              this.client.connect()
            }
          }
        }).finally(() => {
          //  记录最新一次的请求时间
          this.lastRequestTime = new Date().getTime();
        });
      }, this.heartbeatInterval)
    },
    /**
     * 发布插件状态事件
     */
    publishPlugEvent() {
      this.$emit('plugInfoListener', this.getPlugInfo())
    },
    /**
     * 启动插件
     */
    startPlug() {
      this.start();
      return this.client;
    },
    /**
     * 关闭插件，关闭心跳机制
     */
    closePlug() {
      this.handleClose()
    },
    /**
     * 获取插件信息
     * @returns {{pluginWorkVer, pluginInterrupted: boolean, pluginWorkOk: boolean}}
     */
    getPlugInfo() {
      this.checkClient();
      return { client: this.client, pluginWorkOk: this.pluginWorkOk, pluginWorkVer: this.pluginWorkVer, pluginInterrupted: this.pluginInterrupted }
    },
    /**
     * 检查插件
     * 提示：控制台插件使用错误，<plug-info>组件使用错误，请确保 plugStart 方法先执行，再调用 getPlugInfo 方法
     */
    checkClient() {
      if (!this.client) {
        if (this.isDev()) {
          this.$message({
            message: '<plug-info>组件使用错误 !，请确保 startPlug 方法先执行，再调用 getPlugInfo 方法',
            type: 'error',
            duration: 5000
          })
        } else {
          console.warn('<plug-info> module use error !，make sure that the plugStart method is executed first, and then the getPlugInfo method is called.')
        }
      }
    },
    /**
     * 下载最新控制台插件
     * @param file
     * @returns {Promise<T>}
     */
    downloadPluginPkg(file) {
      const opts = { file, jwt: true, topic: 'uterm' }
      return downloadPlugin(undefined, opts);
    }
  }
}
</script>

<style scoped>

</style>
