<template>
  <div class="approval-set">
    <!-- <h2 v-if="status==='updateProcess'">{{ $t('components.operatorSteps') }}</h2>
    <h2 v-else>{{ $t('components.processSteps') }}</h2> -->
    <h2>{{ $t('components.processSteps') }}</h2>
    <div v-if="editable" class="clear-flow-pop">
      <el-popover
        v-model="visible"
        placement="right-end"
        width="180"
        @after-leave="disableDel=false"
      >
        <p>{{ $t('components.deleteSteps') }}</p>
        <div style="text-align: right; margin: 0">
          <el-button type="primary" size="mini" :disabled="disableDel" @click="delNode">{{ $t('button.confirm') }}</el-button>
          <el-button size="mini" @click="visible = false">{{ $t('button.cancel') }}</el-button>
        </div>
        <i slot="reference" class="el-icon-close"></i>
      </el-popover>
    </div>
    <div class="add-flow-pop">
      <div class="add-flow-pop-content">
        <span class="add-flow-pop-content-clear" @click="closePop(1)">
          <i class="el-icon-close"></i>
        </span>
        <div class="add-flow-pop-content-wrap">
          <div class="item" @click="addNode(2)">
            <i class="el-icon-user-solid iconshenpi iconfont"></i>
            <span>{{ $t('components.approver') }}</span>
          </div>
          <!-- <div class="item" @click="addNode(3)">
            <i class="el-icon-star-on iconfont iconchaosongwode-tianchong"></i>
            <span>{{ $t('components.ccPeople') }}</span>
          </div> -->
        </div>
      </div>
    </div>

    <div id="container" style="wdith:100%"></div>
    <tree-table-transfer ref="treeTable" :dialog-visible="dialogShow" :node-model="nodeModel" @sendApprover="getApprover" @close="handleClose">
      <div v-if="selAddNodeId != 'modelRect_launch_1'" slot="delegate" style="margin-left:32px;margin-bottom:32px">
        <!-- <div style="margin-bottom:10px">
          <label>{{ $t('pages.inheritStrategy') }}:</label>
          <el-checkbox v-model="delegateLimitModel.inherit" :disabled="inheritDisabled">{{ $t('pages.approvalProcess_Msg') }}</el-checkbox>
        </div> -->
        <!-- {{ $t('pages.noLimit') }} -->
        <div style="margin-bottom:10px">
          <label>{{ this.$t('pages.approvalType1') }}:</label>
          <el-radio-group v-model="nodeModel.auditType">
            <el-radio label="1">{{ this.$t('pages.levelApproval') }}</el-radio>  
            <el-radio label="2">{{ this.$t('pages.positionApproval') }}</el-radio>
            <el-radio label="3">{{ this.$t('pages.userApproval') }}</el-radio>
            <el-radio label="4">{{ this.$t('pages.branchApproval') }}</el-radio>
            <el-radio label="5">{{ this.$t('pages.directApproval') }}</el-radio>
          </el-radio-group>
        </div>
        <div v-show="nodeModel.auditType == 1" style="margin-bottom:10px">
          <label>{{ this.$t('pages.leaderLevel') }}:</label>
          <el-radio-group v-model="nodeModel.mainDirectorLevel">
            <el-radio v-for="item in directorLevelOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </div>
        <div v-show="nodeModel.auditType == 1 || nodeModel.auditType == 4 || nodeModel.auditType == 5" style="margin-bottom:10px">
          <el-row type="flex" align="middle">
            <el-col :span="3">
              <label>{{ this.$t('components.responseType') }}:</label>
            </el-col>
            <el-col :span="5">
              <el-select v-model="nodeModel.replyType" >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>
        <div v-show="nodeModel.auditType == 1" style="margin-bottom:10px">
          <el-checkbox v-model="nodeModel.noDirectorToParent" true-label="true" false-label="false">{{ this.$t('pages.noDirectorToParent') }}</el-checkbox>
        </div>
        <div v-show="nodeModel.auditType == 3" style="margin-bottom:10px">
          <el-checkbox v-model="nodeModel.multiple" @change="multipleChange"><span>{{ this.$t('pages.approvalProcess_Msg74') }}</span>
          <!-- <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.ImTool_text10') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip> -->
          </el-checkbox>
        </div>
        <div v-show="nodeModel.auditType != 3" >
          <el-row type="flex" align="middle">
            <el-col :span="8">
              <el-checkbox v-model="nodeModel.emptyAuditorToUserSelected" true-label="true" false-label="false">{{ this.$t('pages.emptyAuditorToUserSelected') }}</el-checkbox>
            </el-col>
            <el-col :span="8">
              <tree-select
                clearable
                is-filter
                :disabled="nodeModel.emptyAuditorToUserSelected == 'false'"
                :local-search="false"
                :default-expand-all="false"
                node-key="id"
                :checked-keys="['U' + nodeModel.emptyAuditorToUser ]"
                :placeholder="$t('pages.pleaseSelectContent', { content: $t('table.approver') })"
                :rewrite-node-click-fuc="true"
                :node-click-fuc="userTreeNodeCheckChange"
                @change="branchLeaderSelectChange"
              />
            </el-col>
          </el-row>
        </div>
      </div>
    </tree-table-transfer>
  </div>
</template>

<script>
import G6 from '@antv/g6'
import addImg from '@/assets/add.png'
import TreeTableTransfer from '@/components/TreeTableTransfer'
import { mapGetters } from 'vuex';

const fittingString = (node, maxWidth, fontSize) => {
  const ellipsis = '...'
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0]
  let currentWidth = 0
  const str = node.description
  let res = str
  const pattern = new RegExp('[\u4E00-\u9FA5]+') // distinguish the Chinese charactors and letters
  str && str.split('').forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength) return
    if (pattern.test(letter)) {
      // Chinese charactors
      currentWidth += fontSize
    } else {
      // get the width of single letter according to the fontSize
      currentWidth += G6.Util.getLetterWidth(letter, fontSize)
    }
    if (currentWidth > maxWidth - ellipsisLength) {
      if (node.id === 'modelRect_launch_1') {
        const arr = str.split('；')
        if (str.includes('\n')) {
          res = fittingString({ id: '1', description: arr[0] }, 200, 12) + '；' + fittingString({ id: '1', description: arr[1] }, 200, 12) + '；' + fittingString({ id: '1', description: arr[2] }, 200, 12)
        } else {
          // res = fittingString({ id: '1', description: arr[0] }, 200, 12) + '；' + '\n' + fittingString({ id: '1', description: arr[1] }, 200, 12) + '；' + '\n' + fittingString({ id: '1', description: arr[2] }, 200, 12)
          res = fittingString({ id: '1', description: arr[0] }, 200, 12) + '；' + (arr[1] && arr[1].length > 0 ? '\n' : '') + fittingString({ id: '1', description: arr[1] }, 200, 12) + (arr[1] && arr[1].length > 0 ? '；' : '') + (arr[1] && arr[1].length > 0 ? '\n' : '') + fittingString({ id: '1', description: arr[2] }, 200, 12)
        }
      } else {
        res = `${str.substr(0, i)}${ellipsis}`
      }
    }
  })
  return res || ''
}

export default {
  name: 'ApprovalSet',
  components: { TreeTableTransfer },
  props: {
    status: {
      type: String,
      default() {
        return ''
      }
    },
    approverList: {
      type: Array,
      default() {
        return []
      }
    },
    initiatorList: {
      type: Array,
      default() {
        return []
      }
    },
    editable: {
      type: Boolean,
      default() {
        return true
      }
    },
    verifycode: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    const options = [
      { value: '0', label: this.$t('components.manualApprover') },
      { value: '1', label: this.$t('components.allAuto') },
      { value: '2', label: this.$t('components.offlineAuto') },
      { value: '4', label: this.$t('components.manualApproverOver') }
    ]
    const directorLevelOptions = [
      { value: '1', label: this.$t('pages.firstLevel') },
      { value: '2', label: this.$t('pages.secondLevel') },
      { value: '3', label: this.$t('pages.thirdLevel') },
      { value: '4', label: this.$t('pages.fourthLevel') },
      { value: '5', label: this.$t('pages.fiveLevel') }
    ]
    const directorLevelMap = {
      '1': this.$t('pages.firstLevel'),
      '2': this.$t('pages.secondLevel'),
      '3': this.$t('pages.thirdLevel'),
      '4': this.$t('pages.fourthLevel'),
      '5': this.$t('pages.fiveLevel')
    }
    return {
      g6Data: {}, // G6数据
      // 节点类型 1发起 2审批 3抄送
      nodeOptionalTypes: {
        1: 'launch',
        2: 'approval'
        // 3: 'copy'
      },
      graph: {}, // g6实例
      selAddNodeId: 0, // 点击添加节点按钮的id
      containerHeight: 450, // 容器高度
      selDelNodeId: 0, // 删除节点id,
      addImg: addImg,
      dialogShow: false,
      visible: false,
      nodeModel: {},
      disableDel: false,
      // auditType: '1', // 审批类型
      // mainDirectorLevel: '1', // 主管级别
      // noDirectorToParent: 'true', // 主管为空时由上级主管审批
      // directorId: '', // 审批人为空时指定审批人ID
      // emptyAuditorToUserSelect: 'true', // 审批人为空时指定审批人
      // replyType: '0', // 应答类型 0-手动审批1-所有状态自动审批2-终端离线自动审批
      options,
      directorLevelOptions,
      directorLevelMap
    }
  },
  computed: {
    ...mapGetters([
      'userRoleOptions'
    ]),
    getNodeModelRectNum() {
      const sumArr = [0, 0] // 第一个代表modelRect节点数 第二个代表image节点数
      if ('nodes' in this.g6Data) { // 如果存在节点 则计算
        this.g6Data.nodes.forEach(item => {
          if (item.id.includes('modelRect')) { sumArr[0]++ }
          if (item.id.includes('image')) { sumArr[1]++ }
        })
      }
      return sumArr
    },
    // 获取岗位树，并将岗位树的节点类型改为R，方便跟用户树做区分
    roleData() {
      const roles = JSON.parse(JSON.stringify(this.userRoleOptions))
      roles.forEach(item => {
        item.type = 'RG'
        item.id = item.id.replace('G', 'RG')
        item.parentId = item.parentId.replace('G', 'RG')
        if (item.children) {
          item.children.forEach(child => {
            child.type = 'R'
            child.id = child.id.replace('G', 'R')
            child.parentId = child.parentId.replace('G', 'RG')
          })
        }
      })
      return roles
    }
  },
  watch: {
    verifycode(val) {
      if (val) {
        this.closePop(1)
        this.g6Data.nodes = this.g6Data.nodes.filter((item, index) => index <= 3)
        this.g6Data.nodes.push(this.getEndNode())
        this.g6Data.edges = this._g6GetEdges()
        this.graph.changeData(this.g6Data)
      }
    }
  },
  beforeMount() {
    // 区分添加流程和编辑流程
    this.approverList.length > 0 ? this._initG6DataByUpdate(this.approverList, this.initiatorList) : this._initG6Data()
  },
  mounted() {
    this.initG6()
  },
  methods: {
    _initG6DataByUpdate(approverList, initiatorList) {
      const nodes = []
      this.g6Data = {
        nodes
      }
      // if (this.editable) {
      // 编辑流程时只显示审批人
      // approverList.forEach((item, index) => {
      //   const appoint_index = index + 1
      //   const params = this.getParametersByData(item)
      //   const node = this._g6GetNodeModelRect(2, this.$t('components.approver'), appoint_index, ...params)
      //   if (index > 0) {
      //     nodes.push(this._g6GetNodeImage())
      //   }
      //   nodes.push(node)
      // })
      // } else {
      // 查看流程时显示整个流程
      const dept = initiatorList.filter(item => item.type == '4').map(item => item.label).join()
      const users = initiatorList.filter(item => item.type == '2').map(item => item.label).join()
      const groups = initiatorList.filter(item => item.type == 'R').map(item => item.label).join()
      const deptDesc = dept ? `${this.$t('components.operatorDept')}：${dept}；` : ''
      const userDesc = users ? `${this.$t('components.operatorUser')}：${users}；` : ''
      const groupDesc = groups ? `[${this.$t('table.role')}]：${groups}` : ''
      const desc = deptDesc + userDesc + groupDesc
      nodes.push(this._g6GetNodeModelRect(1, this.$t('components.applicant'), 1, desc, initiatorList))
      approverList.forEach((item, index) => {
        const appoint_index = index + 2
        const params = this.getParametersByData(item)
        const node = this._g6GetNodeModelRect(2, this.$t('components.approver'), appoint_index, ...params)
        nodes.push(this._g6GetNodeImage())
        nodes.push(node)
      })
      // }
      nodes.push(this._g6GetNodeImage())
      nodes.push(this.getEndNode())
      nodes.forEach(function(node) {
        node.description = node.description && fittingString(node, 200, 12)
      })

      this.g6Data.edges = this._g6GetEdges()
      this.containerHeight = 60 + this.getNodeModelRectNum[0] * 160 + 60
    },
    // 获取 _g6GetNodeModelRect 方法的参数，类型为 ModelRect 的 node
    getParametersByData(data) {
      let desc = ''
      let selectDatas = []
      const { auditType, mainDirectorLevel, noDirectorToParent, emptyAuditorToUserSelected, emptyAuditorToUser, replyType, multiple } = data
      if (auditType == 1) {
        const LevelDescription = this.directorLevelMap[mainDirectorLevel]
        desc = LevelDescription || this.$t('pages.leaderLevel')
      } else if (auditType == 2) {
        const { auditRoleArr } = data
        desc = auditRoleArr.length === 0 ? undefined : `[${this.$t('table.role')}]：` + auditRoleArr.map(item => item.label).join()
        selectDatas = auditRoleArr
      } else if (auditType == 3) {
        const { auditUserArr } = data
        desc = auditUserArr.length === 0 ? undefined : `${this.$t('components.operatorUser')}：` + auditUserArr.map(item => item.label).join()
        selectDatas = auditUserArr
      } else if (auditType == 4) {
        desc = this.$t('pages.branchLeader')
      } else if (auditType == 5) {
        desc = this.$t('pages.directSupervisor')
      }
      return [desc, selectDatas, auditType, mainDirectorLevel, noDirectorToParent, emptyAuditorToUserSelected, emptyAuditorToUser, replyType, multiple]
    },
    // 获取‘结束’节点数据
    getEndNode() {
      return {
        size: [100, 55],
        x: 265,
        y: this.getNodeModelRectNum[0] * 160 + 60,
        shape: 'ellipse',
        label: this.$t('components.end'),
        id: 'ellipse_end_1',
        style: {
          fill: '#E4E4E4',
          stroke: '#E4E4E4'
        }
      }
    },
    // 初始化节点信息
    _initG6Data() {
      const nodes = [this._g6GetNodeModelRect(1, this.$t('components.applicant'))]
      // 只能依次加载
      this.g6Data = {
        nodes
      }
      this.g6Data.nodes.push(this._g6GetNodeImage())
      this.g6Data.nodes.push(this._g6GetNodeModelRect(2, this.$t('components.approver')))
      this.g6Data.nodes.push(this._g6GetNodeImage())
      // this.g6Data.nodes.push(this._g6GetNodeModelRect(3, this.$t('components.ccPeople')))
      // this.g6Data.nodes.push(this._g6GetNodeImage())
      this.g6Data.nodes.push(this.getEndNode())
      this.g6Data.edges = this._g6GetEdges()
    },
    initG6() {
      const graph = new G6.Graph({
        container: 'container',
        width: 530,
        height: this.containerHeight,
        nodeStyle: {
          default: {
            fill: '#40a9ff',
            stroke: '#096dd9'
          }
        },
        // 节点不同状态下的样式集合
        nodeStateStyles: {
          // 鼠标点击节点，即 click 状态为 true 时的样式
          click: {
            fill: '#f5f5ff'
          },
          // 鼠标 hover 上节点，即 hover 状态为 true 时的样式
          hover: {
            fill: '#f5f5ff'
          }
        },
        linkCenter: true, // 使边连入节点的中心
        defaultEdge: {
          style: {
            endArrow: true,
            lineWidth: 2,
            stroke: '#888'
          }
        },
        modes: {
          default: [
            {
              type: 'tooltip', // 提示框
              formatText(model) {
                // 提示框文本内容
                if (model.id === 'modelRect_launch_1') {
                  const arr = model.word.split('；')
                  const [dept = '', user = '', role = ''] = arr
                  return dept + '<br />' + user + '<br />' + role
                } else {
                  return model.word
                }
              },
              shouldBegin(e) {
                const idArr = e.item._cfg.id.split('_')
                return idArr[0] === 'modelRect' && e.item._cfg.model.description.includes('.')
              },
              offset: 100
            }
          ]
        }
      })
      // 监听鼠标点击节点
      graph.on('node:click', e => {
        const tooltip = document.getElementsByClassName('g6-node-tooltip')[0]
        if (tooltip) {
          tooltip.style.display = 'none'
        }
        const clickNodes = graph.findAllByState('node', 'click')
        if (this.editable) {
          // 先将所有当前有 click 状态的节点的 click 状态置为 false
          clickNodes.forEach(cn => {
            graph.setItemState(cn, 'click', false)
          })
          const nodeItem = e.item
          // 设置目标节点的 click 状态 为 true
          graph.setItemState(nodeItem, 'click', true)

          if (nodeItem._cfg.currentShape === 'image') { // 判断是否是添加按钮点击
            if (!this.verifycode) {
              const addflowpop = document.getElementsByClassName('add-flow-pop')[0]
              addflowpop.style.display = 'block'
              addflowpop.style.top = nodeItem._cfg.model.y - 20 + 'px' // 添加按钮位置+160
              addflowpop.style.left = 290 + 'px'
              this.selAddNodeId = nodeItem._cfg.id
            } else {
              this.$message({
                message: this.$t('components.msg_oneStep'),
                type: 'error'
              })
            }
          } else if (nodeItem._cfg.currentShape === 'modelRect') { // 点击审批节点
          // 取消气泡
          // this.closePop(1)
          // this.showClearBtn(nodeItem)
          // 显示设置审批人对话框
            this.selAddNodeId = nodeItem._cfg.id
            this.nodeModel = Object.assign({}, nodeItem._cfg.model)
            this.nodeModel.userRoleOptions = this.roleData
            this.dialogShow = true
          } else { // 点击结束

          }
        } else {
          clickNodes.forEach(cn => {
            graph.setItemState(cn, 'click', false)
          })
        }
      })
      // 鼠标进入节点
      graph.on('node:mouseenter', e => {
        const tooltip = document.getElementsByClassName('g6-node-tooltip')[0]
        if (tooltip) {
          tooltip.style.display = 'block'
        }
        const nodeItem = e.item // 获取鼠标进入的节点元素对象
        graph.setItemState(nodeItem, 'hover', true) // 设置当前节点的 hover 状态为 true
        if (nodeItem._cfg.currentShape === 'modelRect' && this.editable) {
          this.showClearBtn(nodeItem)
        }
      })
      // 鼠标离开节点
      graph.on('node:mouseleave', e => {
        const nodeItem = e.item // 获取鼠标离开的节点元素对象
        graph.setItemState(nodeItem, 'hover', false) // 设置当前节点的 hover 状态为 false
        if (nodeItem._cfg.currentShape === 'modelRect' && this.editable) {
          const selType = nodeItem._cfg.id.split('_')[1]
          if (selType === this.nodeOptionalTypes['2'] || selType === this.nodeOptionalTypes['3']) { // 只要审批和抄送才能删除
            this.closePop(2)
            this.selDelNodeId = nodeItem._cfg.id
          }
        }
      })
      graph.read(this.g6Data)
      this.graph = graph
    },
    // 显示删除按钮
    showClearBtn(nodeItem) {
      const selType = nodeItem._cfg.id.split('_')[1]
      const deletable = this.status === 'createProcess' ? (this.getNodeModelRectNum[0] > 2) : (this.getNodeModelRectNum[0] > 1)
      if (deletable && (selType === this.nodeOptionalTypes['2'] || selType === this.nodeOptionalTypes['3'])) { // 只要审批和抄送才能删除
        const addflowpop = document.getElementsByClassName('clear-flow-pop')[0]
        addflowpop.style.display = 'block'
        addflowpop.style.top = nodeItem._cfg.model.y + 20 + 'px' // 添加按钮位置
        addflowpop.style.left = 390 + 'px'
        this.selDelNodeId = nodeItem._cfg.id
      }
    },
    // 删除节点
    delNode() {
      this.disableDel = true
      this.visible = false
      // 得到添加按钮的所属nodes的下标
      const selNodeIndex = this.g6Data.nodes.findIndex(item => item.id === this.selDelNodeId)
      const newOverNodes = []
      this.g6Data.nodes.forEach((item, index) => {
        if (index < selNodeIndex) { // 小于就直接添加原node
          newOverNodes.push(item)
        } else if (index > selNodeIndex + 1) { // 排除删除的指定node和下面那个添加按钮
          const newItem = Object.assign({}, item)
          const idArr = newItem.id.split('_')
          newItem.id = idArr[0] + '_' + idArr[1] + '_' + (idArr[2] * 1 - 1)
          newItem.y -= 160
          newOverNodes.push(newItem)
        } else { // 指定node和下面那个添加按钮
        }
      })
      // 重新赋值
      this.g6Data.nodes = [...newOverNodes]
      this.g6Data.edges = this._g6GetEdges()
      this.graph.changeData(this.g6Data)
      this.closePop(2)
    },
    // 取消气泡框 1 为添加按钮 2 为删除按钮
    closePop(type) {
      let flowPop = ''
      if (type === 1) {
        flowPop = document.getElementsByClassName('add-flow-pop')[0]
      } else { flowPop = document.getElementsByClassName('clear-flow-pop')[0] }
      flowPop.style.display = 'none'
    },
    // 点击添加节点 2审批 3抄送
    addNode(type) {
      // 得到添加按钮的所属nodes的下标
      const selNodeIndex = this.g6Data.nodes.findIndex(item => item.id === this.selAddNodeId)
      const newOverNodes = []
      this.g6Data.nodes.forEach((item, index) => {
        if (index < selNodeIndex) { // 小于就直接添加原node
          newOverNodes.push(item)
        } else if (index === selNodeIndex) { // 等于就生成node添加到数组
          newOverNodes.push(item)
          const appoint_index = item.id.split('_')[2] * 1 + 1 // 当前点击添加按钮的下标+1
          newOverNodes.push(this._g6GetNodeModelRect(type, type === 2 ? this.$t('components.approver') : this.$t('components.ccPeople'), appoint_index))
          newOverNodes.push(this._g6GetNodeImage(appoint_index))
        } else { // 大于的则重新排序 id+1 y+160
          const newItem = Object.assign({}, item)
          const idArr = newItem.id.split('_')
          newItem.id = idArr[0] + '_' + idArr[1] + '_' + (idArr[2] * 1 + 1)
          newItem.y += 160
          newOverNodes.push(newItem)
        }
      })
      // 判断最后一个y是否大于总高度
      if (newOverNodes[newOverNodes.length - 1].y > this.containerHeight) {
        this.containerHeight = newOverNodes[newOverNodes.length - 1].y + 100
        this.graph.changeSize(500, this.containerHeight)// 改变画布大小。
      }
      // 重新赋值
      // console.table(newOverNodes)
      this.g6Data.nodes = [...newOverNodes]
      this.g6Data.edges = this._g6GetEdges()
      this.graph.changeData(this.g6Data)
      this.closePop(1)
    },
    // 配置得到类型为ModelRect的node  appointIndex为指定下标 用于修改
    _g6GetNodeModelRect(type, label, appoint_index, description = type == 1 ? this.$t('components.setApplicant') : this.$t('components.setApprover'), choosedData = [], auditType = '3', mainDirectorLevel = '1', noDirectorToParent = 'false', emptyAuditorToUserSelected = 'false', emptyAuditorToUser = '', replyType = 
    '0', multiple = false, x = 265, choosedDataType) {
      let appIndex = 0
      if (appoint_index !== undefined) {
        appIndex = appoint_index
      } else {
        appIndex = this.getNodeModelRectNum[0] + 1
      }
      choosedDataType = auditType
      const theme = type == 1 ? '#3296FA' : '#FF943E'
      const id = 'modelRect_' + this.nodeOptionalTypes[type] + '_' + appIndex
      return {
        id,
        x,
        y: appIndex * 160 - 100,
        description,
        word: description,
        shape: 'modelRect',
        label,
        choosedData,
        choosedDataType,
        auditType, // 审批类型
        mainDirectorLevel, // 第几级主管审批
        noDirectorToParent, // 审批主管不存在时由上级主管审批
        emptyAuditorToUserSelected, // 审批批人不存在时是否指定审批人
        emptyAuditorToUser, // 审批人不存在时指定审批人ID
        replyType, // 应答类型
        multiple, // 会签 (当选择会签时，不能选择通过并结束流程)
        size: [300, 90],
        // 节点中icon配置
        logoIcon: {
        // 是否显示icon
          show: false
        },
        stateIcon: {
          show: false
        },
        style: {
          // fill: '#f5f5ff',
          stroke: theme, // 边框色
          lineWidth: 2,
          cursor: this.editable ? 'pointer' : 'auto'
        },
        // 左侧矩形 preRect
        preRect: {
          fill: theme, // 填充色
          width: 10
        },
        descriptionCfg: {
          style: {
            fill: '#9254de',
            fontSize: 14,
            cursor: this.editable ? 'pointer' : 'auto',
            fontWeight: 'bold',
            marginTop: '10px',
            textBaseline: 'middle'
          }
        }
      }
    },
    // 得到添加按钮node appointIndex为指定下标 用于修改
    _g6GetNodeImage(appoint_index) {
      const appIndex = appoint_index !== undefined ? appoint_index : (this.getNodeModelRectNum[1] + 1)
      return {
        id: 'image_add_' + appIndex,
        img: this.addImg,
        shape: 'image',
        size: 40,
        x: 265,
        y: appIndex * 160 - 20,
        style: {
          cursor: this.editable ? 'pointer' : 'auto'
        }
      }
    },
    // 依据节点生成边的数据
    _g6GetEdges() {
      const edges = []
      this.g6Data.nodes.forEach((item, index) => {
        if (index !== this.g6Data.nodes.length - 1) {
          const obj = {}
          obj.source = item.id
          obj.target = this.g6Data.nodes[index + 1].id
          edges.push(obj)
        }
      })
      return edges
    },
    getApprover(value) {
      if (this.selAddNodeId === 'modelRect_launch_1') {
        const dept = value.filter(item => item.type == '4').map(item => item.label).join()
        const users = value.filter(item => item.type == '2').map(item => item.label).join()
        const groups = value.filter(item => item.type == 'R').map(item => item.label).join()
        const deptDesc = dept ? `${this.$t('components.operatorDept')}：${dept}；` : ''
        const userDesc = users ? `${this.$t('components.operatorUser')}：${users}；` : ''
        const groupDesc = groups ? `[${this.$t('table.role')}]：${groups}` : ''
        const desc = (deptDesc + userDesc + groupDesc) || this.$t('components.setApplicant')

        this.g6Data.nodes.splice(0, 1, this._g6GetNodeModelRect('1', this.$t('components.applicant'), 1, desc, value))
      } else {
        const idArr = this.selAddNodeId.split('_')
        const params = this.getParametersByData({
          ...this.nodeModel,
          auditRoleArr: value,
          auditUserArr: value
        })
        params[0] = params[0] || this.$t('components.setApprover')
        
        const selNodeIndex = this.g6Data.nodes.findIndex(item => item.id === this.selAddNodeId)
        this.g6Data.nodes.splice(selNodeIndex, 1, this._g6GetNodeModelRect('2', this.$t('components.approver'), idArr[2], ...params))
      }
      this.g6Data.nodes.forEach(function(node) {
        node.description = node.description && fittingString(node, 200, 12)
      })
      this.graph.changeData(this.g6Data)
      this.dialogShow = false
    },
    handleClose() {
      this.dialogShow = false
    },
    branchLeaderSelectChange(data) {
      if (data === '') {
        this.nodeModel.emptyAuditorToUser = undefined
        return;
      }
      if (data instanceof Object) {
        this.nodeModel.emptyAuditorToUser = data.dataId
      }
    },
    userTreeNodeCheckChange(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.nodeModel.emptyAuditorToUser = data.dataId
      } else {
        return false
      }
    },
    multipleChange(val) {
      this.$refs['treeTable'].getColModel()
      // if (val) {
      //   this.nodeModel.choosedData.forEach(r => {
      //     if (r.replyType == '4') {
      //       r.replyType = '0'
      //     }
      //   })
      // }
    }
  }
}
</script>

<style lang="scss" scoped>

.approval-set {
  background: #fff;
  border: 1px solid #e8eaec;
  position: relative;
  width: 530px;
  margin: 0 auto;
  &:hover {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    border-color: #eee;
  }
  h2 {
    font-size: 20px;
    padding: 8px 0;
    text-indent: 28px;
    margin-bottom: 0;
  }
  .clear-flow-pop{
    display: none;
    z-index: 1;
    position: absolute;
    cursor: pointer;
    color: #000;
  }
  .add-flow-pop {
    display: none;
    z-index: 1;
    position: absolute;
    box-shadow: 0px 0px 18px #aaa;
    border-radius: 10px;
    .add-flow-pop-content {
      position: relative;
      top: 0px;
      left: 0px;
      width: 200px;
      height: 120px;
      background: #fff;
      -moz-border-radius: 12px;
      -webkit-border-radius: 12px;
      border-radius: 12px;

      .add-flow-pop-content-wrap{
        display: flex;
        flex-direction: row;
        .item{
          flex: 1;
          text-align: center;
          display: flex;
          flex-direction: column;
          cursor: pointer;
          .iconfont{
            margin: 5px auto;
            font-size: 34px;
            width: 50px;
            height: 50px;
            line-height: 50px;
            border-radius: 50px;
            border: 1px solid #eee;
            &:hover{
              background-color: #2d8cf0;
              color: #fff;
            }
          }
          .iconshenpi{
            color: #FF943E;
          }
          .iconchaosongwode-tianchong{
            color: #3296FA;
          }
        }
      }
      .add-flow-pop-content-clear{
        display: inline-block;
        width: 94%;
        text-align: right;
        margin-top: 5px;
        cursor: pointer;
      }
      &:before {
        position: absolute;
        content: "";
        width: 0;
        height: 0;
        right: 100%;
        top: 65px;
        border-top: 10px solid transparent;
        border-right: 15px solid #fff;
        border-bottom: 10px solid transparent;
      }
    }
  }
}

</style>

<style lang="scss">
.g6-tooltip {
    width: 230px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 14px;
    color: #000;
    line-height: 22px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 10px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
}
</style>
