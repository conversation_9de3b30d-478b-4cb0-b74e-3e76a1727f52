<!--
/**
  * 搜索条件的组件，用于实现搜索条件的变化传递到父组件 SearchToolbar，以实现和标签的联动变化。

  * 调用示例：
    <SearchItem
      model-key="query.alarmType",                // 搜索条件绑定的key，支持 xxx.xxx 的形式
      :value="query.alarmType"                    // 搜索条件绑定的值
    >
      <span>搜索条件：</span>                      // 搜索条件需要用 span 包裹，才能获取到文字
      <el-select v-model="query.alarmType">
        <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </SearchItem>
*/
-->

<template>
  <span>
    <slot></slot>
  </span>
</template>

<script>
import { EventBus } from '@/layout/event-bus';
import { isEmpty } from '@/utils'

export default {
  name: 'SearchItem',
  props: {
    // 搜索条件绑定的值
    value: {
      type: [String, Number, Boolean, Array],
      default: undefined
    },
    // 搜索条件绑定的key，支持 xxx.xxx 的形式
    modelKey: {
      type: String,
      required: true
    },
    // 组件是否可视
    visible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      menuCode: '',            // 当前页面的菜单编码，作为key使用
      componentTag: ''         // 包裹的搜索组件的标签
    };
  },
  watch: {
    value: {
      deep: true,
      handler(val) {
        this.emitValueChange(val)
      }
    },
    visible(val) {
      if (!val) {
        EventBus.$emit(`${this.menuCode}-searchItemHide`, this.modelKey)
      } else {
        this.emitValueChange(this.value)
      }
    }
  },
  created() {
    this.menuCode = this.$route.meta.code
    this.$nextTick(() => {
      this.componentTag = this.$children[0].$options._componentTag
      if (!isEmpty(this.value)) {
        this.emitValueChange(this.value)
      }
    })
  },
  methods: {
    // 将搜索条件的 key、label 传给父组件
    emitValueChange(val) {
      let label = this.getConditionLabel()
      this.$nextTick(() => {
        let value = val
        let clearable
        if (this.componentTag === 'el-select') {
          const component = this.$children[0]
          clearable = component._props.clearable
          // 搜索条件是 select 组件，获取选中选项的文字
          value = this.getConditionOption(val)
        } else if (this.componentTag === 'el-cascader') {
          // 搜索条件是 cascader 组件，获取选中选项的文字
          if (Array.isArray(val) && val.length > 0) {
            // 通过存储的条件赋值的情况，需要在nextTick中才能获得 checkedNodes
            this.$nextTick(() => {
              const checkedNodes = this.$children[0].checkedNodes
              value = checkedNodes.map(node => node.label).join()
              label = value ? label + value : ''
              const option = { label, clearable, value: val }
              EventBus.$emit(`${this.menuCode}-valueChange`, this.modelKey, option)
            })
            return
          } else {
            value = ''
          }
        }
        // 如果搜索条件有值，返回文字，否则返回 ''
        label = value ? label + value : ''
        const option = { label, clearable, value: val }
        EventBus.$emit(`${this.menuCode}-valueChange`, this.modelKey, option)
      })
    },
    // 获取搜索条件的 label
    getConditionLabel() {
      return this.$el.firstChild.innerText
    },
    // 获取搜索条件 select组件选中选项的文字
    getConditionOption(value) {
      const optionComponents = this.$children[0].cachedOptions
      if (Array.isArray(value)) {
        const valueSet = new Set(value)
        const labels = optionComponents.map(option => valueSet.has(option.value) && option.label)
        return labels.filter(Boolean).join()
      }
      for (const option of optionComponents) {
        if (option.value === value) {
          return option.label;
        }
      }
      return ''
    }
  }
};
</script>
