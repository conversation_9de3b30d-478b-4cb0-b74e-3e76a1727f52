<!--
/**
  * 搜索工具栏组件。组件将 隐藏/显示树的按钮、时间查询器、搜索功能按钮 等组件内置，同时也支持外部传入的方式来实现相关功能。
  * 组件实现将搜索条件隐藏的功能，通过点击 搜索条件 按钮，显示被隐藏的搜索条件，并且在搜索条件有值时，在下方显示搜索条件的小标签，便于更清晰的知道当前的搜索条件。
  * 点击搜索返回数据时，会将上面的搜索条件隐藏，避免占用太多空间，影响美观。
  *
  * 调用示例：
  * <SearchToolbar
      :tree-menu-btn="false"                    // 设置 false不显示切换树按钮
      :time-query="false"                       // 设置 false不显示时间查询器
      :condition-btn="false"                    // 如果没有额外的搜索条件，设置 false不显示搜索条件按钮
      :search-btn="false"                       // 设置 false不显示搜索按钮
      :tabName="tabName"                        // 页面如果是有 tabs，传入当前tabName，用于使用 保存的搜索条件 时切换 tab
      @toggleTreeMenu="toggleTreeMenu"          // 点击切换树按钮的事件
      @getTimeParams="getTimeParams"            // 时间查询器接收参数的事件
      @handleFilter="handleFilter"              // 点击搜索按钮的事件
    >
      // 通过 slot="menu-btn" 可以传入自定义切换树的按钮
      <el-button slot="menu-btn" type="primary" size="mini" @click="toggleTreeMenu">
        <svg-icon icon-class="tree" />
      </el-button>

      // 通过 slot="time" 可以传入自定义的时间查询器
      <TimeQuery slot="time" ref="timeQuery" v-on="$listeners"/>

      <SearchItem model-key="xxx" :value="xxx">
      ...
      </SearchItem>

      // 其余功能组件可通过 slot="append" 传入
      <other-component slot="append" />
    </SearchToolbar>
  *
*/
-->

<template>
  <div>
    <!-- 搜索工具栏，使用默认 slot 接收子组件 -->
    <div class="toolbar">
      <!-- 隐藏/显示树的按钮 -->
      <slot v-if="treeMenuBtn" name="menu-btn">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
      </slot>
      <!-- 时间查询器 -->
      <slot v-if="timeQuery" name="time">
        <TimeQuery ref="timeQuery" v-on="$listeners"/>
      </slot>
      <!-- 搜索条件按钮 -->
      <el-button v-if="conditionBtn" type="primary" icon="el-icon-menu" size="mini" @click="toggleCondition">
        {{ $t('pages.searchConditions') }}
      </el-button>
      <!-- 保存的搜索条件列表 -->
      <el-dropdown v-if="conditionList.length" trigger="click" placement="bottom" @command="handleSetCondition">
        <el-tooltip effect="dark" content="点击选择保存的搜索条件" placement="top-start">
          <el-button type="primary" size="mini" style="padding: 7px 7px;" @click.stop>
            <svg-icon icon-class="list" />
          </el-button>
        </el-tooltip>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(condition, index) in conditionList" :key="index" :command="index">
            <span>{{ condition.name }}</span>
            <svg-icon icon-class="delete" class="delete-icon" @click.stop="e => { conditionDelete(e, condition) }"></svg-icon>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!-- 搜索按钮 -->
      <el-button v-if="searchBtn" type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
        {{ $t('table.search') }}
      </el-button>
      <!-- 其余功能占位 -->
      <slot name="append"></slot>
    </div>
    <!-- 搜索条件占位 -->
    <div v-show="conditionShow" class="condition">
      <slot></slot>
    </div>
    <!-- 显示搜索条件小标签 -->
    <div class="search-tags">
      <!-- 已设置的搜索条件标签 -->
      <el-tag
        v-for="(option, key) in searchConditions"
        :key="key"
        :closable="option.clearable !== false"
        @close="clearCondition(key)"
      >
        {{ option.label }}
      </el-tag>
      <!-- 清空搜索条件按钮 -->
      <el-button
        v-if="showClearAll"
        type="text"
        class="clear-btn"
        @click="clearAllCondition"
      >
        {{ $t('pages.clearSearchConditions') }}
      </el-button>
      <!-- 保存搜索条件按钮 -->
      <el-button
        v-if="showSave"
        type="text"
        class="clear-btn"
        @click="handleSave"
      >
        保存搜索条件
      </el-button>
    </div>
    
    <tr-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      border="true"
      :title="'保存搜索条件'"
      :visible.sync="saveVisible"
      width="400px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="saveTemp"
        label-position="right"
        label-width="60px"
        style="width: 350px;"
      >
        <FormItem :label="'名称'" prop="name">
          <el-input v-model="saveTemp.name" v-trim maxlength="30"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveCondition">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="saveVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
  </div>
</template>

<script>
import { EventBus } from '@/layout/event-bus';
import { createSearchCondition, deleteSearchCondition } from '@/api/behaviorAuditing/searchCondition'

export default {
  name: 'SearchToolbar',
  props: {
    treeMenuBtn: { type: Boolean, default: true },      // 是否显示切换树按钮
    timeQuery: { type: Boolean, default: true },        // 是否显示时间查询器
    conditionBtn: { type: Boolean, default: true },     // 是否显示搜索条件按钮
    searchBtn: { type: Boolean, default: true },        // 是否显示搜索按钮
    tabName: { type: String, default: '' }              // 页面如果是有 tabs，记录当前tabName
  },
  data() {
    return {
      menuCode: '',                   // 当前页面的菜单编码，作为key使用
      conditionShow: false,           // 搜索条件是否显示
      searchConditions: {},           // 已设置的搜索条件map
      saveVisible: false,
      saveTemp: {                     // 保存搜索条件的temp
        name: ''
      },
      rules: {
        name: [
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    // 保存的搜索条件列表
    searchCondition() {
      return this.$store.getters.searchCondition
    },
    // 当前页面的搜索条件列表
    conditionList() {
      return this.searchCondition ? this.searchCondition.filter(condition => condition.menuCode === this.menuCode) : []
    },
    // 清空搜索条件按钮 是否显示
    showClearAll() {
      const option = Object.values(this.searchConditions)
      const clearable = option.find(opt => opt.clearable !== false)
      return !!clearable
    },
    // 保存搜索条件按钮 是否显示
    showSave() {
      const option = Object.values(this.searchConditions)
      return !!option.length
    }
  },
  created() {
    this.menuCode = this.$route.meta.code
    // 监听子组件传递的搜索条件变化
    EventBus.$on(`${this.menuCode}-valueChange`, this.updateCondition)
    EventBus.$on(`${this.menuCode}-searchItemHide`, this.removeTag)
  },
  methods: {
    // 点击切换树按钮
    toggleTreeMenu() {
      this.$emit('toggleTreeMenu')
    },
    // 点击搜索条件按钮
    toggleCondition() {
      this.conditionShow = !this.conditionShow
    },
    // 点击搜索按钮
    handleFilter() {
      this.conditionShow = false
      this.$emit('handleFilter')
    },
    // 更新已设置的搜索条件map
    updateCondition(key, value) {
      if (value.label) {
        this.$set(this.searchConditions, key, value)
      } else {
        // 判断 searchConditions 是否有 key 的属性，有的话则清除，没有就不再执行 clearCondition
        if (Object.prototype.hasOwnProperty.call(this.searchConditions, key)) {
          this.clearCondition(key)
        }
      }
      // 强制更新视图
      this.$forceUpdate()
    },
    // 移除搜索条件的 tag 标签，但清除原搜索条件的值
    removeTag(key) {
      this.$delete(this.searchConditions, key)
      this.$forceUpdate()
    },
    // 选中保存的搜索条件，并设置值
    handleSetCondition(key) {
      const { tabName, queryCondition } = this.conditionList[key]
      if (tabName) {
        this.$set(this.$parent, 'tabName', tabName)
      }
      // 将已设置的搜索条件和选中的搜索条件合并，已设置的值设为 null
      const condition = Object.keys(this.searchConditions).reduce((result, key) => {
        if (!result.hasOwnProperty(key)) {
          result[key] = 'clear'
        }
        return result
      }, queryCondition)
      this.$nextTick(() => {
        // 设置保存的搜索条件，并清空其他条件
        Object.entries(condition).forEach(([key, value]) => {
          if (value == 'clear') {
            this.clearCondition(key)
          } else {
            this.setValueByPath(this.$parent, key, value)
          }
        })
      })
    },
    // 删除保存的搜索条件
    conditionDelete(e, condition) {
      const { id, name } = condition
      this.$confirmBox(`确定删除搜索条件“${name}”？`, this.$t('text.prompt'), { appendToBody: false }).then(() => {
        // 删除搜索条件
        deleteSearchCondition({ ids: id }).then(res => {
          this.$store.dispatch('commonData/delSearchCondition', condition)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(e => {
          console.error(e);
        })
      }).catch((e) => {})
    },
    // 清除对应 key 的搜索条件
    clearCondition(key) {
      const option = this.searchConditions[key]
      const { clearable } = option
      if (clearable !== false) {
        this.$delete(this.searchConditions, key)
        this.setValueByPath(this.$parent, key, '')
      }
    },
    // 清空搜索条件
    clearAllCondition() {
      for (const key of Object.keys(this.searchConditions)) {
        this.clearCondition(key)
      }
    },
    // 设置 obj 对应属性 key 的值
    setValueByPath(obj, key, value) {
      const parts = key.split('.');
      const lastIndex = parts.length - 1;
      parts.reduce((current, part, index) => {
        if (index === lastIndex) {
          if (value || value === 0) {
            this.$set(current, part, value)
          } else {
            if (Array.isArray(current[part])) {
              this.$set(current, part, [])
            } else {
              this.$set(current, part, '')
            }
          }
        } else {
          if (!current[part]) {
            this.$set(current, part, {})
          }
          return current[part];
        }
      }, obj);
      return obj;
    },
    // 设置时间
    setDate(date) {
      this.$refs.timeQuery.setDate(date)
    },
    initSearchItem() {
      // const componentInstance = this.$slots.default.map(vNode => vNode.componentInstance).filter(Boolean)
      // console.log('initSearchItem....', componentInstance);
    },
    handleSave() {
      if (this.conditionList.length >= 10) {
        this.$message({
          message: '当前页面保存的搜索条件已达上限！',
          type: 'error',
          duration: 2000
        })
        return
      }
      this.saveTemp.name = ''
      this.saveVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    // 保存搜索条件
    saveCondition() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const condition = {}
          const { name } = this.saveTemp
          const tabName = this.tabName
          Object.entries(this.searchConditions).forEach(([key, item]) => {
            condition[key] = item.value
          })
          const item = { name, tabName, menuCode: this.menuCode, queryCondition: JSON.stringify(condition) }
          createSearchCondition(item).then(res => {
            const data = res.data
            data.queryCondition = JSON.parse(data.queryCondition)
            this.$store.dispatch('commonData/addSearchCondition', data)
            this.saveVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(e => {
            console.error(e);
          })
        }
      })
    },
    // 搜索条件名称校验
    nameValidator(rule, value, callback) {
      if (!value) {
        callback('请输入名称')
      } else {
        const nameSet = new Set(this.conditionList.map(item => item.name))
        if (nameSet.has(value)) {
          // 校验重名
          callback('名称重复，请修改！')
        } else {
          callback()
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
  .toolbar, .condition {
    .el-select {
      margin-bottom: 3px;
      >>>.el-input {
        margin-bottom: 0;
      }
    }
  }
  .condition {
    font-size: 14px;
    span {
      display: inline-block;
    }
    >>>.el-input {
      margin-bottom: 3px;
    }
    >>>.el-input__inner {
      height: 30px !important;
    }
  }
  .search-tags {
    margin: 5px 0;
    >>>.el-tag {
      height: 24px;
      line-height: 16px;
      padding: 4px 5px;
      margin-right: 5px;
    }
  }
  .clear-btn {
    padding: 0;
    font-size: 13px;
  }
  >>>.el-dropdown-menu__item {
    min-width: 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      margin-right: 10px;
    }
  }
</style>
