<!--
  /**
  * 策略另存为的组件。
  * @update 2022-07-12
  * 调用示例：
    <copy-policy
      :dialog-status="dialogStatus"                          // 上级弹窗的状态
      :stg-code="11"                                         // 策略编码，stg_base_config的number字段值
      :formable="formable"                                   // 是否可编辑状态
      :form-data="temp"                                      // 从父组件传入要拷贝的数据
      :entity-click="entityClick"                            // 跳转到另存为的节点的方法
      :create-data="createData"                              // 新增数据的方法
      :strategy-def-type="query.strategyDefType"             // 1 预定义策略 0 应用策略
      rewrite-node-click-fuc                                 // 是否重写树节点单击事件，默认为false
      :node-click-fuc="nodeClickFuc"                         // 重写树节点单击事件，rewriteNodeClickFuc 需要为true
      @entityNodeData="nodeData"                             // 选中节点的数据
    />
  */
-->
<template>
  <div style="display: inline-block">
    <el-button
      v-if="formable && useable"
      type="primary"
      @click="handleCopyClick"
    >
      {{ $t('components.saveAs') }}
    </el-button>
    <el-dialog
      v-el-drag-dialog
      append-to-body
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('components.saveAs')"
      :visible.sync="visible"
      width="600px"
    >
      <p style="padding-left: 10px;">{{ $t('components.confirmSaveAs') }}</p>
      <Form ref="copyForm4StgDialog" :model="temp" label-position="right" label-width="80px" style="width: 560px">
        <StgBaseFormItem
          ref="stgTargetItem"
          :stg-code="stgCode"
          :form-data="temp"
          :tree-width="350"
          :entity-type-span="12"
          :strategy-def-type="strategyDefType"
          :rewrite-node-click-fuc="rewriteNodeClickFuc"
          :node-click-fuc="nodeClickFuc"
          :formable="formable"
          :time-able="timeAble"
          :time-mode="timeMode"
          :active-able="isGeneralStrategy"
          @entityNodeData="nodeData => entityIdChange(nodeData, temp)"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveCopyData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { checkDuplicateName, checkActiveStg, getUsedScope } from '@/api/stgCommon'
import { getMstgUsedScope } from '@/api/system/baseData/strategyType'
import { mapGetters } from 'vuex'

export default {
  name: 'CopyPolicy',
  props: {
    dialogStatus: { type: String, default: '' },                              // 上级弹窗的状态
    formable: { type: Boolean, default: false },                              // 是否可编辑状态
    timeAble: { type: Boolean, default: true },                               // 是否显示生效时间
    timeMode: { type: Number, default: 3 },                                   // 显示生效时间的模式：1.只显示生效时间 2.只显示生效日期 3.显示生效时间+生效日期
    stgCode: { type: Number, default: 0 },                                    // 策略编码，stg_base_config的number字段值
    stgType: { type: Number, default: 1 },                                    // 策略类型，普通策略为1，多继承策略为2
    strategyDefType: { type: Number, default: 0 },                            // 1 预定义策略 0 应用策略
    formData: { type: Object, default() { return {} } },                      // 从父组件传入要拷贝的数据
    getByName: { type: Function, default: null },                             // 通过策略名称和策略编码获取策略数据的方法，用于判断是否重复名称
    createData: { type: Function, default: null },                            // 新增数据的方法
    entityClick: { type: Function, default: (row, data) => {} },              // 跳转到另存为的节点的方法
    rewriteNodeClickFuc: { type: Boolean, default: false },                   // 是否重写树节点单击事件，默认为false
    nodeClickFuc: { type: Function, default: function(data, node, vm) {} }    // 重写树节点单击事件，rewriteNodeClickFuc 需要为true
  },
  data() {
    return {
      visible: false,
      usedScope: 3,
      entityType: 3,
      temp: {},
      defaultTemp: {
        name: '',
        timeId: 1,
        active: false,
        remark: '',
        entityType: '',             // 弃用：应用对象类型
        entityId: '',               // 弃用：应用对象ID
        objectType: null,           // 应用对象类型
        objectIds: null,            // 应用对象ID，包括终端ID或操作员ID
        objectGroupIds: null,       // 应用对象ID，包括终端分组ID或操作员分组ID
        strategyDefType: null,       // 策略定义类型：0-普通策略，1-预定义策略
        isPermanent: 1,               // 策略是否长期有效， 默认长期有效
        beginTime: undefined,        // 策略生效起始时间
        endTime: undefined           // 策略生效终止时间
      }
    }
  },
  computed: {
    ...mapGetters([
      'stgBaseConfig',
      'mstgBaseConfig'
    ]),
    // 是否可另存为
    useable() {
      return ['update', 'updateS', 'updateP'].indexOf(this.dialogStatus) > -1
    },
    // 是否应用策略
    isGeneralStrategy() {
      return this.strategyDefType === 0
    },
    // 终端是否可用
    terminalUseable() {
      return this.usedScope != 2
    },
    // 操作员是否可用
    userUseable() {
      return this.usedScope != 1
    }
  },
  mounted: function() {
    if (this.stgType == 1) {
      if (this.stgBaseConfig) {
        this.usedScope = this.stgBaseConfig[this.stgCode].usedScope
      } else {
        getUsedScope(this.stgCode).then(response => {
          this.usedScope = response.data
        })
      }
    } else if (this.stgType == 2) {
      if (this.mstgBaseConfig) {
        this.usedScope = this.mstgBaseConfig[this.stgCode].usedScope
      } else {
        getMstgUsedScope(this.stgCode).then(response => {
          this.usedScope = response.data
        })
      }
    }
  },
  created() {
  },
  methods: {
    handleCopyClick() {
      this.visible = true
      const { entityType, objectType, name, timeId, remark, isPermanent, beginTime, endTime } = this.formData
      this.temp = Object.assign({}, this.defaultTemp, { entityType, objectType, name, timeId, remark, strategyDefType: this.strategyDefType, isPermanent, beginTime, endTime })
    },
    checkName() {
      const data = {
        name: this.temp.name,
        stgTypeNumber: this.stgCode
      }
      if (this.getByName) {
        return this.getByName.call(null, data)
      }
      return checkDuplicateName(data)
    },
    saveCopyData() {
      if (this.isGeneralStrategy && !this.$refs['stgTargetItem'].valid()) {
        return
      }
      if (this.temp.name == '') {
        this.$message({
          message: this.$t('components.stgNameNotNull'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (this.isGeneralStrategy && (!this.temp.objectType || ((!this.temp.objectIds || this.temp.objectIds.length === 0) && (!this.temp.objectGroupIds || this.temp.objectGroupIds.length === 0)))) {
        this.$message({
          message: this.$t('components.chooseApplicationObj'),
          type: 'error',
          duration: 2000
        })
        return;
      }
      this.checkName().then(respond => {
        const res = respond.data
        if (res != undefined) {
          this.$message({
            message: this.$t('valid.sameName'),
            type: 'error',
            duration: 2000
          })
          return
        } else {
          // 判单是否存在启用策略
          checkActiveStg({
            stgTypeNumber: this.stgCode,
            objectType: this.temp.objectType,
            objectIds: this.temp.objectIds,
            objectGroupIds: this.temp.objectGroupIds
          }).then(resp => {
            if (resp.data && this.temp.active === true) {
              this.$message({
                message: this.$t('components.enabledPoliciesMsg'),
                type: 'error',
                duration: 2000
              })
              return
            } else {
              Object.assign(this.formData, this.temp)
              if (this.formData.entityId && this.formData.entityId == -2) {
                this.$message({
                  message: this.$t('components.recycleNodeTips'),
                  type: 'error',
                  duration: 2000
                })
                return
              }
              const entityType = this.formData.entityType || this.formData.objectType
              if ((this.isEmpty(entityType) || this.isEmpty(this.formData.entityId) && this.isEmpty(this.formData.objectIds) &&
                this.isEmpty(this.formData.objectGroupIds) && this.formData.entityId != '0') && this.strategyDefType != 1) {
                this.$message({
                  message: this.$t('components.chooseApplicationObj'),
                  type: 'error',
                  duration: 2000
                })
                return
              }
              this.createData()
              this.$nextTick(() => {
                this.visible = false
                this.isGeneralStrategy && this.entityClick(this.temp, null)
              })
            }
          })
        }
      }).catch(e => {
        console.error(e);
      })
    },
    entityIdChange(nodeData) {
      this.$emit('entityNodeData', nodeData)
    },
    isEmpty(val) {
      return val === undefined || val === null || val === '' || Array.isArray(val) && val.length === 0
    }
  }
}
</script>
