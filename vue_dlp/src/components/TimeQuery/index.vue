<!--
  /**
  * 日期选择器组件，可切换单个日期和范围日期(不包含时间选择器，可考虑增加该功能)
  * <AUTHOR>
  * @date 2022-05-10
  * 调用示例：
    <TimeQuery
      value / v-model                           // 绑定值为对象，包含属性：createDate, startDate, endDate, isTimes。也可以不绑定，而是通过getTimeParams获取选中日期的值
      :type="['date', 'daterange']",            // 传入值为字符串数组，第一个元素为默认显示类型，当只有一个元素时，不显示切换选择框
      format="yyyy-MM-dd",                      //
      value-format="yyyy-MM-dd",                //
      :limit-day="30"                           // 开始日期与结束日期最大限制天数, 0表示不限制
      :auto-current-value="false"               // 是否获取当天的值
      @getTimeParams="getTimeParams"            // 获取选中日期的值
    />
  */
-->
<template>
  <div style="display:inline-block">
    <el-checkbox v-if="typeSwitch" v-model="isRange" :disabled="disabled">
      {{ leafLabel }}
      <el-tooltip v-if="showTips">
        <div slot="content">
          <i18n path="pages.appReportSetTips">
            <br slot="br"/>
          </i18n>
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </el-checkbox>
    <el-date-picker
      v-show="!isRange"
      v-model="date"
      :editable="false"
      :clearable="isClearable"
      :disabled="disabled"
      :type="datePickerType"
      :format="format"
      :value-format="valueFormat"
      :placeholder="placeholder"
      :picker-options="pickerOptions"
      @change="change"
    />
    <el-date-picker
      v-show="isRange"
      v-model="rangeDate"
      :editable="false"
      :clearable="isClearable"
      :disabled="disabled"
      :type="datePickerTypeRange"
      :format="format"
      :value-format="valueFormat"
      :start-placeholder="$t('pages.startDate')"
      :end-placeholder="$t('pages.endDate')"
      :style="rangeStyle"
      :picker-options="pickerOptions"
      @change="rangeChange"
      @blur="choiceDate = ''"
    />
  </div>
</template>

<script>
import moment from 'moment'
import { mapGetters } from 'vuex'

export default {
  name: 'TimeQuery',
  props: {
    value: {
      type: Object,
      default() {
        return null
      }
    },
    type: {
      type: Array,
      default() {
        // 传入值为字符串数组，第一个元素为默认显示类型，当只有一个元素时，不显示切换选择框
        return ['date', 'daterange']
      }
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    limitDay: { // 开始日期与结束日期最大限制天数, 0表示不限制
      type: Number,
      default: 365
    },
    leafLabel: {
      type: String,
      default() {
        return this.$t('pages.timeQuery')
      }
    },
    datePickerType: {
      type: String,
      default() {
        return 'date'
      }
    },
    datePickerTypeRange: {
      type: String,
      default() {
        return 'daterange'
      }
    },
    isClearable: {
      type: Boolean,
      default: false
    },
    rangeStyle: {
      type: Object,
      default() {
        return { width: '250px' }
      }
    },
    showTips: {
      type: Boolean,
      default: false
    },
    //  自动选中当天的值
    autoCurrentValue: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('pages.chooseDate')
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const date = moment(new Date()).format('YYYY-MM-DD')
    return {
      inited: false,
      isRange: null,
      date: this.autoCurrentValue ? date : null,
      rangeDate: this.autoCurrentValue ? [date, date] : null,
      choiceDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.choiceDate = minDate.getTime()
          if (maxDate) {
            this.choiceDate = ''
          }
        },
        disabledDate: (date) => {
          if (this.choiceDate && this.limitDay) {
            const choiceDate = new Date(this.choiceDate)
            const minDate = new Date(this.choiceDate).setDate(choiceDate.getDate() - this.limitDay) // 选中的时间前N天
            const maxDate = new Date(this.choiceDate).setDate(choiceDate.getDate() + this.limitDay) // 选中的时间后N天
            return date < minDate || date > maxDate || date > new Date()
          } else {
            return date > new Date()
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters([
      'cacheTimeQuery',
      'cacheDateObj'
    ]),
    // 是否显示切换选择框
    typeSwitch() {
      return this.type.length > 1
    }
  },
  watch: {
    isRange(val) {
      this.getTimeParams()
    },
    date(val) {
      this.getTimeParams()
    },
    rangeDate(val) {
      this.getTimeParams()
    }
  },
  created() {
    this.isRange = this.type[0] === 'daterange'
    this.updateCacheDate()
    this.getTimeParams()
    this.$nextTick(() => {
      this.inited = true
    })
  },
  activated() {
    if (this.inited) {
      this.updateCacheDate()
    }
  },
  methods: {
    updateCacheDate() {
      if (this.cacheTimeQuery && this.cacheDateObj) {
        this.setDate(this.cacheDateObj)
      }
    },
    //  设置时间
    setDate(date) {
      if (date) {
        const isObj = Object.prototype.toString.call(date) === '[object Object]'
        this.isRange = isObj ? !!date.isTimes : this.isRange
        if (!this.isRange) {
          this.date = isObj ? date.createDate : date
        } else {
          this.rangeDate = isObj ? [date.startDate, date.endDate] : [date, date]
        }
        this.getTimeParams()
      }
    },
    getTimeParams() {
      const dateObj = {
        createDate: this.isRange ? '' : this.date,
        startDate: this.isRange && this.rangeDate ? this.rangeDate[0] : '',
        endDate: this.isRange && this.rangeDate ? this.rangeDate[1] : '',
        isTimes: this.isRange
      }
      if (this.value) {
        this.value = Object.assign(this.value, dateObj)
      }
      // 保存选中的时间
      if (this.cacheTimeQuery) {
        this.$store.dispatch('user/setCacheDateObj', dateObj)
      }
      this.$emit('getTimeParams', dateObj)
    },
    rangeChange(val) {
      this.$emit('rangeChange', val)
    },
    change(val) {
      this.$emit('change', val)
    },
    clearDate() {
      this.rangeDate = null
      this.date = null
    }
  }
}
</script>
