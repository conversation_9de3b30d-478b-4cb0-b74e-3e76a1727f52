<template>
  <el-dialog
    v-el-drag-dialog
    append-to-body
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="visible"
    :title="title"
    :width="width"
  >
    <Form label-position="right" label-width="110px">
      <usb-plug-info
        ref="upi"
        :loading="loading"
        :connected="pluginWorkOk"
        :interrupted="pluginInterrupted"
        :version="pluginWorkVer"
        :gutter="gutter"
        :span="span"
        @connect="testConnect"
      />
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ $t('button.close') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import i18n from '@/lang'
import UsbPlugInfo from '@/views/system/terminalManage/terminal/uterm/plug'
import { Level, UTermPluginClient } from '@/views/system/terminalManage/terminal/uterm/client'
import { compareVersion } from '@/api/system/terminalManage/uterm'

export default {
  name: 'PluginConfig',
  components: { UsbPlugInfo },
  props: {
    title: {
      type: String,
      default: i18n.t('pages.plugConfig')
    },
    width: {
      type: String,
      default: '640px'
    },
    // 功能支持的最小版本号
    prevMinVersion: {
      type: String,
      default: ''
    },
    // 是否启用心跳检测
    heartbeatEnable: {
      type: Boolean,
      default: false
    },
    // 连接上插件时，是否自动关闭插件配置窗口
    autoClose: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      gutter: 2,
      span: 12,
      visible: false,
      loading: false,
      client: undefined,
      logLevel: Level.WARN.name,
      pluginWorkOk: false, // 插件工作状态是否正常
      pluginWorkVer: undefined, // 当前插件版本
      pluginInterrupted: false, // 插件连接中断
      heartbeatTimer: undefined, // 心跳计时器
      heartbeatNotified: false, // 心跳错误是否已通知
      lastRequestTime: null,    //  最近一次请求插件的时间
      lastHeartbeatInterval: null,  //  计算上次的时间间隔
      reconnectCount: 0, //  重连次数
      plugRequestTimeout: 15000   //   插件获取信息的超时时间 单位：毫秒
    }
  },
  computed: {
    // 心跳间隔
    heartbeatInterval() {
      if (this.reconnectCount < 12) {
        return 5000
      }
      if (this.reconnectCount < 24) {
        return 10000
      }
      if (this.reconnectCount < 36) {
        return 15000
      }
      if (this.reconnectCount < 54) {
        return 30000
      }
      if (this.reconnectCount < 69) {
        return 60000
      }
      return 300000
    },
    // 插件状态
    plugStatus() {
      if (!this.pluginWorkOk) {
        // 插件无法连接（插件未安装/端口配置错误）
        return 0
      } else if (this.prevMinVersion && compareVersion(this.pluginWorkVer, this.prevMinVersion) <= 0) {
        // 插件版本低下，需要进行升级来支持功能
        return 2
      }
      // 插件正常运行
      return 1
    }
  },
  watch: {
    plugStatus(val) {
      this.$emit('status', val)
    }
  },
  mounted() {
    this.startPlug()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.loading = false
      this.lastHeartbeatInterval = null
      this.lastRequestTime = null
      this.heartbeatNotified = false
      this.reconnectCount = 0
      this.heartbeatTimer = undefined
      this.heartbeatEnable = true
      this.pluginWorkOk = false
      this.pluginWorkVer = undefined
      this.pluginInterrupted = false
      this.client = undefined
    },
    /**
     * 启动插件
     * @returns {*}
     */
    startPlug() {
      this.handleClose()
      this.testConnect().then(res => {
        if (this.heartbeatEnable) {
          this.heartbeat()
        }
      })
      return this.client;
    },
    /**
     * 关闭插件
     */
    closePlug() {
      this.handleClose();
    },
    /**
     * 重启插件
     * @returns {*}
     */
    restartPlug() {
      return this.startPlug();
    },
    /**
     * 测试连接
     * @param port
     * @returns {Promise<T>}
     */
    testConnect(port) {
      if (this.client) {
        this.client.close()
      }
      this.client = new UTermPluginClient(this, port, Level.of(this.logLevel), this.packetEncrypted)
      return this.connect()
    },
    /**
     * 连接
     * @returns {Promise<T>}
     */
    connect() {
      if (this.client.close) {
        this.client.connect()
      }
      return this.client.getPlugInfo(this.plugRequestTimeout).then(data => {
        this.pluginWorkOk = data.PlugWorkType === 0
        this.pluginWorkVer = data.PlugVersion
        this.pluginInterrupted = !this.pluginWorkOk
        if (this.pluginWorkOk) {
          this.autoClose && (this.visible = false)
          this.client.reconnected = true
          this.heartbeatNotified = false
        }
      }).catch(error => {
        this.pluginWorkVer = null
        this.pluginInterrupted = true
        this.pluginWorkOk = false
        if (!this.heartbeatNotified) {
          this.$notify({
            title: this.$t('text.fail'),
            message: UTermPluginClient.normalizeErrs(error),
            type: 'error',
            duration: 2000
          })
          this.heartbeatNotified = true
        }
      }).finally(() => {
        this.heartbeat()
      })
    },
    /**
     * 心跳机制
     * 通过heartbeat方法内部循环调用达到循环重复执行效果，通过setTimeout来达到间隔效果
     */
    heartbeat() {
      if (this.heartbeatTimer) {
        return
      }
      console.log('<plug-config> start heartbeat!')
      this.lastHeartbeatInterval = this.heartbeatInterval
      this.heartbeatTimer = setInterval(() => {
        if (!this.client || !this.heartbeatEnable) {
          clearInterval(this.heartbeatTimer)
          this.heartbeatTimer = undefined
          this.lastRequestTime = undefined
          this.lastHeartbeatInterval = undefined
          return
        }

        //  若上次执行的时间超过指定时间时，直接返回失败，目的：当插件正在连接被中断时，会导致解析异常，插件无法返回then或catch，导致无法感知插件的状态，
        if (this.lastRequestTime !== null && new Date().getTime() - this.lastRequestTime > this.maxIntervalTime) {
          this.pluginWorkOk = false
          this.pluginInterrupted = true
          this.heartbeatEnable = false
          if (!this.heartbeatNotified) {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.$t('pages.plugBreakOff'),
              type: 'error',
              duration: 2000
            })
            this.heartbeatNotified = true
          }
          return
        }

        //  当心跳机制的时间间隔发生改变时，重新创建Interval
        if (this.heartbeatTimer && this.lastHeartbeatInterval !== this.heartbeatInterval) {
          clearInterval(this.heartbeatTimer)
          this.heartbeatTimer = undefined
          this.heartbeat();
        }

        //  尝试连接
        this.connect().then(res => {
          //  若插件-client已关闭时，尝试一次重新连接
          if (this.client.closed) {
            this.reconnectCount++
            this.client.connect()
          }
        }).finally(() => {
          //  记录最新一次的请求时间
          this.lastRequestTime = new Date().getTime();
        });
      }, this.heartbeatInterval)
    },
    /**
     *
     * 显示弹窗
     */
    show() {
      console.log('show')
      this.visible = true
      this.startPlug()
    },
    /**
     * 获取客户端实例
     * @returns {*}
     */
    getClient() {
      if (!this.client) { this.startPlug() }
      return this.client
    },
    /**
     * 关闭插件插件
     */
    handleClose() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        console.log('<plug-config> close heartbeat!')
      }
      if (this.client) {
        this.client.close()
      }
      this.initData();
    }
  }
}
</script>

<style scoped>

</style>
