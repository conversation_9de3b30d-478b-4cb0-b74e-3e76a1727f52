import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getStrategyPage(data) {
  return request({
    url: '/contentStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/contentStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function getContentStgIdCodeMap(data) {
  return request({
    url: '/contentStrategy/getStgIdCodeMap',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getContentStgIdNameMap(stgTypeNum) {
  return request({
    url: '/contentStrategy/listIdName/' + stgTypeNum,
    method: 'get'
  })
}

export function createStrategy(data) {
  return request({
    url: '/contentStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/contentStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/contentStrategy/delete',
    method: 'post',
    params: data
  })
}

export function listRuleGroupTreeNode() {
  return request({
    url: '/ruleGroup/listTree',
    method: 'get'
  })
}

export function listRespondRuleTreeNode() {
  return request({
    url: '/respondRule/listTree',
    method: 'get'
  })
}

export function importStrategy(data) {
  return request.post('/contentStrategy/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function exportStg(data, opts) {
  return fetchFile({
    ...opts,
    url: '/contentStrategy/exportStg',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data
  })
}

export function updateStatusBatch(data) {
  return request({
    url: '/contentStrategy/updateStatusBatch',
    method: 'post',
    timeout: 0,
    params: data
  })
}
// 新增零星检测策略
export function createDripStrategy(data) {
  return request({
    url: '/contentDripStrategy/add',
    method: 'post',
    data
  })
}
// 修改零星检测策略
export function updateDripStrategy(data) {
  return request({
    url: '/contentDripStrategy/update',
    method: 'post',
    data
  })
}
// 删除零星检测策略
export function deleteDripStrategy(data) {
  return request({
    url: '/contentDripStrategy/delete',
    method: 'post',
    params: data
  })
}
// 修改零星检测策略状态
export function updateDripStatusBatch(data) {
  return request({
    url: '/contentDripStrategy/updateStatusBatch',
    method: 'post',
    timeout: 0,
    params: data
  })
}

export function valiRules(data) {
  return request({
    url: '/ruleGroup/valiRules',
    method: 'post',
    data
  })
}
