import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/sensitiveBackupConfig/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/sensitiveBackupConfig/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/sensitiveBackupConfig/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/sensitiveBackupConfig/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/sensitiveBackupConfig/delete',
    method: 'post',
    params: data
  })
}
