import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/behaviorStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/behaviorStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/behaviorStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/behaviorStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/behaviorStrategy/delete',
    method: 'post',
    params: data
  })
}
