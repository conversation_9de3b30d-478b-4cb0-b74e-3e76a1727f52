import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/effectiveContentConfig/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/effectiveContentConfig/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/effectiveContentConfig/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/effectiveContentConfig/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/effectiveContentConfig/delete',
    method: 'post',
    params: data
  })
}
