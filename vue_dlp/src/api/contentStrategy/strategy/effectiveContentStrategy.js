import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/effectiveContentStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/effectiveContentStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/effectiveContentStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/effectiveContentStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/effectiveContentStrategy/delete',
    method: 'post',
    params: data
  })
}

export function createShareStgConfig(data) {
  return request({
    url: '/shareStgConfig/add',
    method: 'post',
    data
  })
}

export function updateShareStgConfig(data) {
  return request({
    url: '/shareStgConfig/update',
    method: 'post',
    data
  })
}

export function getEffectiveConfig(data) {
  return request({
    url: '/effectiveContentStrategy/getEffectiveConfigBySuperior/' + data,
    method: 'get'
  })
}

export function getShareStgConfig(data) {
  return request({
    url: '/shareStgConfig/getBySuperior/' + data,
    method: 'get'
  })
}

export function createExceptStrategy(data) {
  return request({
    url: '/exceptStrategy/add',
    method: 'post',
    data
  })
}

export function updateExceptStrategy(data) {
  return request({
    url: '/exceptStrategy/update',
    method: 'post',
    data
  })
}

export function getExceptStrategy(data) {
  return request({
    url: '/exceptStrategy/getBySuperior/' + data,
    method: 'get'
  })
}
