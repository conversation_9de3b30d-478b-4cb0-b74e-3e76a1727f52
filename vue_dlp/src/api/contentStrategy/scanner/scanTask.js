import request from '@/utils/request'

export function getScanTaskByName(data) {
  return request({
    url: '/scanTask/getByName',
    method: 'post',
    params: data
  })
}

export function createScanTask(data) {
  return request({
    url: '/scanTask/add',
    method: 'post',
    data
  })
}

export function updateScanTask(data) {
  return request({
    url: '/scanTask/update',
    method: 'post',
    data
  })
}

export function deleteScanTask(data) {
  return request({
    url: '/scanTask/delete',
    method: 'post',
    params: data
  })
}

export function operateScanTask(id, status) {
  return request({
    url: '/scanTask/operate/' + id + '/' + status,
    method: 'get'
  })
}

export function getScanTaskPage(data) {
  return request({
    url: '/scanTask/getPage',
    method: 'post',
    data
  })
}
