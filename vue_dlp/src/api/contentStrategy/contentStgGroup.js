import request from '@/utils/request'

export function getStrategyList(data) {
  return request({
    url: '/contentStgGroup/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/contentStgGroup/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/contentStgGroup/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/contentStgGroup/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/contentStgGroup/delete',
    method: 'post',
    params: data
  })
}

export function listObjectTree(id, objectType) {
  return request({
    url: '/contentStgGroup/listObjectTree/' + id + '/' + objectType,
    method: 'get'
  })
}
