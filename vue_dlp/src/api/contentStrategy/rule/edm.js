import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/edm/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/edm/getByName',
    method: 'post',
    params: data
  })
}

export function createRule(data) {
  return request({
    url: '/edm/insert',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/edm/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/edm/delete',
    method: 'post',
    data
  })
}

export function getGroupByName(data) {
  return request({
    url: '/edmGroup/getByName',
    method: 'post',
    params: data
  })
}

export function createGroup(data) {
  return request({
    url: '/edmGroup/insert',
    method: 'post',
    data
  })
}

export function updateGroup(data) {
  return request({
    url: '/edmGroup/update',
    method: 'post',
    data
  })
}

export function deleteGroup(data) {
  return request({
    url: '/edmGroup/delete/' + data,
    method: 'get'
  })
}

export function connectDB(data) {
  return request({
    url: '/edmGroup/connectable',
    method: 'post',
    data
  })
}

export function listTable(id) {
  return request({
    url: '/edmGroup/listTable/' + id,
    method: 'get'
  })
}

export function listTableCol(data) {
  return request({
    url: '/edmGroup/listTableCol',
    method: 'post',
    data
  })
}

export function getTreeNode() {
  return request({
    url: '/edmGroup/listTreeNode',
    method: 'get'
  })
}

export function uploadFile(data) {
  return request.post('/edm/uploadFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function cancelTrainEdm(data) {
  return request({
    url: '/edm/cancelTrain',
    method: 'post',
    params: data
  })
}

export function trainEdm(data) {
  return request({
    url: '/edm/train',
    method: 'post',
    params: data
  })
}

export function listFileColInfo(data) {
  return request({
    url: '/edm/listFileCol',
    method: 'post',
    data
  })
}

export function getRuleById(data) {
  return request({
    url: '/edm/getById/' + data,
    method: 'get'
  })
}

export function checkFile(data) {
  return request({
    url: '/edm/check',
    method: 'post',
    params: data
  })
}

export function getCheckResult(data) {
  return request({
    url: '/edm/getCheckResult/' + data,
    method: 'get'
  })
}

export function uploadCkeckFile(data) {
  return request.post('/edm/uploadCheckFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function countRuleByGroupId(data) {
  return request({
    url: '/edmGroup/countRuleById/' + data,
    method: 'get'
  })
}

export function listRuleById(data) {
  return request({
    url: '/edm/listById',
    method: 'post',
    params: data
  })
}
