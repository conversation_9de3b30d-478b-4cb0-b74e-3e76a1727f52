import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/ruleGroup/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/ruleGroup/getByName',
    method: 'post',
    params: data
  })
}

export function getRuleByIds(ids) {
  return request({
    url: '/ruleGroup/listById',
    method: 'post',
    params: { ids: ids }
  })
}

export function getSubRuleByIds(ids) {
  return request({
    url: '/ruleGroup/listRuleById',
    method: 'post',
    params: { ids: ids }
  })
}

export function createRule(data) {
  return request({
    url: '/ruleGroup/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/ruleGroup/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/ruleGroup/delete',
    method: 'post',
    data
  })
}

export function listRegularRuleTreeNode() {
  return request({
    url: '/regularRule/listTree',
    method: 'get'
  })
}

export function listkeywordRuleTreeNode() {
  return request({
    url: '/keywordRule/listTree',
    method: 'get'
  })
}

export function listFileAttributeRuleTreeNode() {
  return request({
    url: '/fileAttributeRule/listTree',
    method: 'get'
  })
}

export function listFileFingerprintRuleTreeNode() {
  return request({
    url: '/fileFingerprint/listTree',
    method: 'get'
  })
}

export function listEdmRuleTreeNode() {
  return request({
    url: '/edm/listTree',
    method: 'get'
  })
}

export function listVmlRuleTreeNode() {
  return request({
    url: '/vml/listTree',
    method: 'get'
  })
}

export function listIdentifierRuleTreeNode() {
  return request({
    url: '/identifierRule/listTree',
    method: 'get'
  })
}

export function createRegularRule(data) {
  return request({
    url: '/identifierRule/add',
    method: 'post',
    data
  })
}

export function listSourceCodeRuleTreeNode() {
  return request({
    url: '/sourceCodeRule/listTree',
    method: 'get'
  })
}

export function getGroupTreeNode() {
  return request({
    url: '/ruleGroup/listGroupTree',
    method: 'get'
  })
}

export function createRuleGroupClass(data) {
  return request({
    url: '/ruleGroup/addGroup',
    method: 'post',
    data
  })
}

export function updateRuleGroupClass(data) {
  return request({
    url: '/ruleGroup/updateGroup',
    method: 'post',
    data
  })
}

export function deleteRuleGroupClass(data) {
  return request({
    url: '/ruleGroup/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getRuleGroupClassByName(data) {
  return request({
    url: '/ruleGroup/getGroupByName',
    method: 'post',
    params: data
  })
}

export function moveGroup(data) {
  return request({
    url: '/ruleGroup/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/ruleGroup/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/ruleGroup/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/ruleGroup/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/ruleGroup/moveGroupToOther',
    method: 'post',
    data
  })
}

export function getRuleGroupByGroupIds(groupIds) {
  return request({
    url: '/ruleGroup/getByGroupIds',
    method: 'get',
    params: groupIds
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/ruleGroup/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function deleteRuleGroupGroup(data) {
  return request({
    url: '/ruleGroup/deleteGroup',
    method: 'post',
    params: data
  })
}

