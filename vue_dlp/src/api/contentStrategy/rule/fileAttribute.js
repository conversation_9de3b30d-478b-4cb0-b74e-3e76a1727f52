import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/fileAttributeRule/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/fileAttributeRule/getByName',
    method: 'post',
    params: data
  })
}

export function createRule(data) {
  return request({
    url: '/fileAttributeRule/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/fileAttributeRule/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/fileAttributeRule/delete',
    method: 'post',
    data
  })
}
