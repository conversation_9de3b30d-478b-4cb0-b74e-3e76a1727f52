import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/vml/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/vml/getByName',
    method: 'post',
    params: data
  })
}

export function createRule(data) {
  return request({
    url: '/vml/insert',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/vml/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/vml/delete',
    method: 'post',
    data
  })
}

export function uploadFiles(data) {
  return request.post('/vml/uploadFile', data, { 'Content-Type': 'multipart/form-data' })
}

export function uploadFile(data) {
  return request.post('/vml/uploadCheckFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function cancelTrainVml(data) {
  return request({
    url: '/vml/cancelTrain',
    method: 'post',
    params: data
  })
}

export function trainVml(data) {
  return request({
    url: '/vml/train',
    method: 'post',
    params: data
  })
}

export function checkFile(data) {
  return request({
    url: '/vml/check',
    method: 'post',
    params: data
  })
}

export function getCheckResult(data) {
  return request({
    url: '/vml/getCheckResult/' + data,
    method: 'get'
  })
}

export function getRuleById(data) {
  return request({
    url: '/vml/getById/' + data,
    method: 'get'
  })
}

export function listRuleById(data) {
  return request({
    url: '/vml/listById',
    method: 'post',
    params: data
  })
}
