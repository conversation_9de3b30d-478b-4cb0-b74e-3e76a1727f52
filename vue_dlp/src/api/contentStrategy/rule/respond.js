import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/respondRule/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/respondRule/getByName',
    method: 'post',
    params: data
  })
}

export function getRuleByIds(ids) {
  return request({
    url: '/respondRule/listById',
    method: 'post',
    params: { ids: ids }
  })
}

export function createRule(data) {
  return request({
    url: '/respondRule/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/respondRule/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/respondRule/delete',
    method: 'post',
    data
  })
}
