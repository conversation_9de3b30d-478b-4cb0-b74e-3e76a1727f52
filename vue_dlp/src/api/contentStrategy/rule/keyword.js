import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/keywordRule/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/keywordRule/getByName',
    method: 'post',
    params: data
  })
}

export function getRuleByIds(ids) {
  return request({
    url: '/keywordRule/listById',
    method: 'post',
    params: { ids: ids }
  })
}

export function createRule(data) {
  return request({
    url: '/keywordRule/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/keywordRule/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/keywordRule/delete',
    method: 'post',
    data
  })
}
