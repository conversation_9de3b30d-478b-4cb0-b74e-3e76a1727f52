import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/sourceCodeRule/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/sourceCodeRule/getByName',
    method: 'post',
    params: data
  })
}

export function createRule(data) {
  return request({
    url: '/sourceCodeRule/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/sourceCodeRule/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/sourceCodeRule/delete',
    method: 'post',
    data
  })
}

export function getFileSuffixOptions() {
  return request({
    url: '/sourceCodeRule/getFileSuffixOptions',
    method: 'get'
  })
}
