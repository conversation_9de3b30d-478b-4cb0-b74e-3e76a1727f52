import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/regularRule/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/regularRule/getByName',
    method: 'post',
    params: data
  })
}

export function getRuleByIds(ids) {
  return request({
    url: '/regularRule/listById',
    method: 'post',
    params: { ids: ids }
  })
}

export function createRule(data) {
  return request({
    url: '/regularRule/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/regularRule/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/regularRule/delete',
    method: 'post',
    data
  })
}

export function listLuaRuleTreeNode() {
  return request({
    url: '/regularRule/listLuaRuleTree',
    method: 'get'
  })
}

export function matchRegular(data) {
  return request({
    url: '/regularRule/match',
    method: 'post',
    data
  })
}

export function getMatchResult(data) {
  return request({
    url: '/regularRule/getMatchResult/' + data,
    method: 'get'
  })
}

export function createIdentifierRule(data) {
  return request({
    url: '/identifierRule/add',
    method: 'post',
    data
  })
}

export function deleteIdentifierRule(data) {
  return request({
    url: '/identifierRule/delete',
    method: 'post',
    params: data
  })
}

export function getByName(data) {
  return request({
    url: '/identifierRule/getByName',
    method: 'post',
    params: data
  })
}

export function getPreciseRulePage(data) {
  return request({
    url: '/preciseIdentifierRule/listTree',
    method: 'get',
    data
  })
}
