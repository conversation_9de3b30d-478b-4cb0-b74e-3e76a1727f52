import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/fileFingerprint/getPage',
    method: 'post',
    data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/fileFingerprint/getByName',
    method: 'post',
    params: data
  })
}

export function createRule(data) {
  return request({
    url: '/fileFingerprint/insert',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/fileFingerprint/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/fileFingerprint/delete',
    method: 'post',
    data
  })
}

export function uploadFile(data) {
  return request.post('/fileFingerprint/uploadFile', data, { 'Content-Type': 'multipart/form-data' })
}

export function cancelTrainFileFingerprint(data) {
  return request({
    url: '/fileFingerprint/cancelTrain',
    method: 'post',
    params: data
  })
}

export function trainFileFingerprint(data) {
  return request({
    url: '/fileFingerprint/train',
    method: 'post',
    params: data
  })
}

export function checkFile(data) {
  return request({
    url: '/fileFingerprint/check',
    method: 'post',
    params: data
  })
}

export function getCheckResult(data) {
  return request({
    url: '/fileFingerprint/getCheckResult/' + data,
    method: 'get'
  })
}

export function uploadCkeckFile(data) {
  return request.post('/fileFingerprint/uploadCheckFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function getRuleById(data) {
  return request({
    url: '/fileFingerprint/getById/' + data,
    method: 'get'
  })
}

export function listRuleById(data) {
  return request({
    url: '/fileFingerprint/listById',
    method: 'post',
    params: data
  })
}
