import request from '@/utils/request'

/**
 * 推送配置（列表内容获取）
 * @param data
 * @returns {AxiosPromise}
 */
export function msgPushGetPage(data) {
  return request({
    url: `/msgPush/getPage`,
    method: 'post',
    data
  })
}

/**
 * 推送配置（新增）
 * @param data
 * @returns {AxiosPromise}
 */
export function msgPushMessageTask(data) {
  return request({
    url: `/msgPush/messageTask`,
    method: 'post',
    data
  })
}

/**
 * 推送配置（修改）
 * @param data
 * @returns {AxiosPromise}
 */
export function msgPushUpdateTask(data) {
  return request({
    url: `/msgPush/updateTask`,
    method: 'post',
    data
  })
}

/**
 * 推送配置（删除）
 * @param data
 * @returns {AxiosPromise}
 */
export function msgPushDelTask(data) {
  return request({
    url: `/msgPush/delTask`,
    method: 'post',
    params: data
  })
}

/**
 * 推送记录（列表内容获取）
 * @param data
 * @returns {AxiosPromise}
 */
export function logMsgPush(data) {
  return request({
    url: `/log/msgPush/getPage`,
    method: 'post',
    data
  })
}
/**
 * 立即推送
 * @param data
 * @returns {AxiosPromise}
 */
export function msgPushSendMsg(data) {
  return request({
    url: `/msgPush/sendMsg`,
    method: 'post',
    data
  })
}
