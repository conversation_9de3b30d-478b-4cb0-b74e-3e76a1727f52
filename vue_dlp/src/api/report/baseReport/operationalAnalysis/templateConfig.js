import request from '@/utils/request'

/**
 * 获取模板列表（下拉列表）
 * @param data
 * @returns {AxiosPromise}
 */
export function getTemplateList(data) {
  return request({
    url: `/operationalReport/getTemplateList`,
    method: 'post',
    data
  })
}

/**
 * 获取模板（富文本编辑器）
 * @param data
 * @returns {AxiosPromise}
 */
export function getReportTempFile(data) {
  return request({
    url: `/operationalReport/getReportTempFile`,
    method: 'post',
    data
  })
}

/**
 * 模板测试
 * @param data
 * @returns {AxiosPromise}
 */
export function downloadReport(data) {
  return request({
    url: `/operationalReport/downloadReport`,
    method: 'post',
    params: data
  })
}

/**
 * 内置模板民初获取（列表）
 * @param data
 * @returns {AxiosPromise}
 */
export function getTemplateLibList() {
  return request({
    url: `/operationalReport/getTemplateLibList`,
    method: 'post'
  })
}

/**
 * 新增模板
 * @param data
 * @returns {AxiosPromise}
 */
export function addTemplate(data) {
  return request({
    url: `/operationalReport/addTemplate`,
    method: 'post',
    data
  })
}

/**
 * 修改模板
 * @param data
 * @returns {AxiosPromise}
 */
export function updateTemplate(data) {
  return request({
    url: `/operationalReport/updateTemplate`,
    method: 'post',
    data
  })
}

/**
 * 删除模板
 * @param data
 * @returns {AxiosPromise}
 */
export function deleteTemplate(data) {
  return request({
    url: `/operationalReport/deleteTemplate`,
    method: 'post',
    params: data
  })
}

/**
 * 检测模板名称是否重复
 * @param data
 * @returns {AxiosPromise}
 */
export function getByTitle(data) {
  return request({
    url: `/operationalReport/getByTitle`,
    method: 'post',
    params: data
  })
}

/**
 * 获取模板碎片列表（报表指标数据）
 * @param data
 * @returns {AxiosPromise}
 */
export function getTemplateFragmentList() {
  return request({
    url: `/operationalReport/getTemplateFragmentList`,
    method: 'post'
  })
}

/**
 * 获取echarts列表（报表图表数据）
 * @param data
 * @returns {AxiosPromise}
 */
export function getReportEchartList() {
  return request({
    url: `/operationalReport/getReportEchartList`,
    method: 'get'
  })
}

/**
 * 获取echarts列表（报表图表数据）
 * @param data
 * @returns {AxiosPromise}
 */
export function getReportTableTemplates() {
  return request({
    url: `/operationalReport/getReportTableTemplates`,
    method: 'get'
  })
}
