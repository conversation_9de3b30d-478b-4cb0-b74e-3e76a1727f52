import request from '@/utils/request'

export function getDimTime(timeType, token) {
  return request({
    url: '/issueReport/getDimTime/' + timeType,
    cancelToken: token,
    method: 'get'
  })
}

export function subDogMemory() {
  return request({
    url: '/register/subDog',
    method: 'get'
  })
}
export function getAppType(data) {
  return request({
    url: `/issueReport/getAppType`,
    method: 'post',
    data
  })
}

export function getReportChart(data, api) {
  return request({
    url: `/issueReport/${api}`,
    method: 'post',
    data
  })
}

export function getDetailPage(data) {
  return request({
    url: `/issueReport/getDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getTrendDetail(data) {
  return request({
    url: `/issueReport/getTrendDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function checkDetailAble(data) {
  return request({
    url: `/issueReport/checkDetailAble`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function checkDiskScanDetailAble(data) {
  return request({
    url: `/issueReport/checkDiskScanDetailAble`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function backupLogList(data) {
  return request({
    url: `/commonLog/backupLogList`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function redirectReportLog() {
  return request({
    url: `/issueReport/redirectReportLog`,
    method: 'post'
  })
}
