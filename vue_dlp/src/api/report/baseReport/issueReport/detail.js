import request from '@/utils/request'

export function getMobileStorageDetail(data) {
  return request({
    url: `/issueReport/getMobileStorageDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getMobileFileOpDetail(data) {
  return request({
    url: `/issueReport/getMobileFileOpDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getAssetLogDetail(data) {
  return request({
    url: `/issueReport/getAssetLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getTrwfeLogDetail(data) {
  return request({
    url: `/issueReport/getTrwfeLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getChatLogDetail(data) {
  return request({
    url: `/issueReport/getChatLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getChatImageLogDetail(data) {
  return request({
    url: `/issueReport/getChatImageLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getChatFileLogDetail(data) {
  return request({
    url: `/issueReport/getChatFileLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getAllChatLogDetail(data) {
  return request({
    url: `/issueReport/getAllChatLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getIllegalLogDetail(data) {
  return request({
    url: `/issueReport/getIllegalLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getDiskScanLogDetail(data) {
  return request({
    url: `/issueReport/getDiskScanLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getTrwfePermission() {
  return request({
    url: `/issueReport/getTrwfePermission`,
    method: 'get'
  })
}
