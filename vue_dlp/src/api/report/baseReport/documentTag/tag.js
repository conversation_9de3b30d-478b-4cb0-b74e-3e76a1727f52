import request from '@/utils/request'

/**
 * 加标签：记录报表首页获取
 * @param data
 * @return {AxiosPromise}
 */
export function getDocTagAddTypeHomepageData(data) {
  return request({
    url: `/sensitiveControl/getDocTagAddTypeHomepageData`,
    method: 'post',
    data
  })
}

/**
 * 加标签：部门详情左边表格数据/顶部部门弹框  共用
 * @param data
 * @return {AxiosPromise}
 */
export function listDocTagAddTypeDeptData(data) {
  return request({
    url: `/sensitiveControl/listDocTagAddTypeDeptData`,
    method: 'post',
    data
  })
}

/**
 * 加标签：部门详情右边图表数据
 * @param data
 * @return {AxiosPromise}
 */
export function getDocTagAddTypeDeptAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getDocTagAddTypeDeptAnalysisData`,
    method: 'post',
    data
  })
}

/**
 * 加标签：终端详情左边表格/顶部终端操作员弹框  共用
 * @param data
 * @return {AxiosPromise}
 */
export function listDocTagAddTypeTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listDocTagAddTypeTerminalUserData`,
    method: 'post',
    data
  })
}

/**
 * 加标签：详情详情右边图表数据
 * @param data
 * @return {AxiosPromise}
 */
export function getDocTagAddTypeTerminalUserAnalysisDataPro(data) {
  return request({
    url: `/sensitiveControl/getDocTagAddTypeTerminalUserAnalysisDataPro`,
    method: 'post',
    data
  })
}

/**
 * 加标签：趋势信息
 * @param data
 * @return {AxiosPromise}
 */
export function getDocTagAddTypeTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getDocTagAddTypeTrendAnalysisData`,
    method: 'post',
    data
  })
}

/**
 * 加标签：部门右侧放大弹框
 * @param data
 * @return {AxiosPromise}
 */
export function getDocTagAddTypeDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getDocTagAddTypeDeptChartData`,
    method: 'post',
    data
  })
}

/**
 * 加标签：终端操作员右侧放大弹框
 * @param data
 * @return {AxiosPromise}
 */
export function getDocTagAddTypeTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getDocTagAddTypeTerminalUserChartData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：任务下拉框列表数据
 * @param data  taskType 任务类型，1敏感内容扫描  2文档标签扫描
 * @return {AxiosPromise}
 */
export function getDiskScanGuidOptionsPro(data) {
  return request({
    url: `/sensitiveControl/getDiskScanGuidOptionsPro`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：信息首页数据
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagHomepageData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagHomepageData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：部门echarts放大
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagGroupChartData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagGroupChartData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：终端echarts放大
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagTermChartData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagTermChartData`,
    method: 'post',
    data
  })
}
/**
 * 全盘扫描标签：除标签内容，所有分析，右侧表格信息获取（共用）
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagDetailPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagDetailPage`,
    method: 'post',
    data
  })
}

export function getDiskScanTagContentChartData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagContentChartData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：标签内容，右侧表格信息获取
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanContentDetailPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanContentDetailPage`,
    method: 'post',
    data
  })
}
/**
 * 全盘扫描标签：终端分析，左侧表格
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagTerminalPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagTerminalPage`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：终端分析，右侧图表
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagTermAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagTermAnalysisData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：部门分析，左侧表格
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagGroupPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagGroupPage`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：部门分析，右侧图表
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagGroupAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagGroupAnalysisData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：文档等级分析，左侧表格
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagTrankPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagTrankPage`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：文档等级分析，右侧图表
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagTrankAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagTrankAnalysisData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：标签内容分析，左侧表格
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagContentPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagContentPage`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描标签：标签内容分析，右侧图表
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagContentAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagContentAnalysisData`,
    method: 'post',
    data
  })
}

/**
 * 全盘扫描对比报表
 * @param data
 * @return {AxiosPromise}
 */
export function getDiskScanTagCompareHomepageData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanTagCompareHomepageData`,
    method: 'post',
    data
  })
}
/**
 * 全盘扫描对比报表(策略获取)
 * @param data
 * @return {AxiosPromise}
 */
export function getTaskListByGuid(data) {
  return request({
    url: '/diskScan/getTaskListByGuid',
    method: 'get',
    params: data
  })
}

export function getTagLogDetail(data) {
  return request({
    url: `/sensitiveControl/getTagLogDetail`,
    method: 'post',
    timeout: 0,
    data
  })
}
