import request from '@/utils/request'

export function getOverTimePanel(data) {
  return request({
    url: `/issueReport/getOverTimePanel`,
    method: 'post',
    data
  })
}

export function getOverTimeUserPage(data) {
  return request({
    url: `/issueReport/getOverTimeUserPage`,
    method: 'post',
    data
  })
}

export function getOverTimeDeptPage(data) {
  return request({
    url: `/issueReport/getOverTimeDeptPage`,
    method: 'post',
    data
  })
}

export function getOverTimeDeptDetail(data) {
  return request({
    url: `/issueReport/getOverTimeDeptDetail`,
    method: 'post',
    data
  })
}

export function getDeptTrend(data) {
  return request({
    url: `/issueReport/getDeptTrend`,
    method: 'post',
    data
  })
}
