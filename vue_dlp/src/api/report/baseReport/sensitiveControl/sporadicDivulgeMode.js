import request from '@/utils/request'

export function getSporadicDivulgeModeHomepageData(data) {
  return request({
    url: `/sensitiveControl/getSporadicDivulgeModeHomepageData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicDivulgeModeDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getSporadicDivulgeModeDeptChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicDivulgeModeTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getSporadicDivulgeModeTerminalUserChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listSporadicDivulgeModeDeptData(data) {
  return request({
    url: `/sensitiveControl/listSporadicDivulgeModeDeptData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listSporadicDivulgeModeTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listSporadicDivulgeModeTerminalUserData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicDivulgeModeDeptAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSporadicDivulgeModeDeptAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicDivulgeModeTerminalUserAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSporadicDivulgeModeTerminalUserAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicDivulgeModeTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSporadicDivulgeModeTrendAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

