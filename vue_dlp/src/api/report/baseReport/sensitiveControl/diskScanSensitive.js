import request from '@/utils/request'

export function getDiskScanSensitiveHomepageData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveHomepageData`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveGroupChartData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveGroupChartData`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveTermChartData(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveTermChartData`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveTerminalPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveTerminalPage`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveDetailPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveDetailPage`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveGroupPage(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveGroupPage`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveObjectPie(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveObjectPie`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveRuleList(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveRuleList`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveSeverityList(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveSeverityList`,
    method: 'post',
    data
  })
}

export function getDiskScanSensitiveRuleNamePie(data) {
  return request({
    url: `/sensitiveControl/getDiskScanSensitiveRuleNamePie`,
    method: 'post',
    data
  })
}

export function getDiskScanGuidOptions() {
  return request({
    url: `/sensitiveControl/getDiskScanGuidOptions`,
    method: 'post'
  })
}

export function getTaskOptionStep1(data) {
  return request({
    url: `/sensitiveControl/getTaskOptionStep1`,
    method: 'post',
    data
  })
}

export function getTaskOptionStep2(data) {
  return request({
    url: `/sensitiveControl/getTaskOptionStep2`,
    method: 'post',
    data
  })
}
