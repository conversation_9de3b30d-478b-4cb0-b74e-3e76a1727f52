import request from '@/utils/request'

export function getSensitiveSymptomHomepageData(data) {
  return request({
    url: `/sensitiveControl/getSensitiveSymptomHomepageData`,
    method: 'post',
    data
  })
}

export function listSensitiveSymptomDeptData(data) {
  return request({
    url: `/sensitiveControl/listSensitiveSymptomDeptData`,
    method: 'post',
    data
  })
}

export function listSensitiveSymptomTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listSensitiveSymptomTerminalUserData`,
    method: 'post',
    data
  })
}

export function getSensitiveSymptomTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSensitiveSymptomTrendAnalysisData`,
    method: 'post',
    data
  })
}

export function getSensitiveSymptomDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getSensitiveSymptomDeptChartData`,
    method: 'post',
    data
  })
}

export function getSensitiveSymptomTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getSensitiveSymptomTerminalUserChartData`,
    method: 'post',
    data
  })
}
