import request from '@/utils/request'

export function getSmartBackupHomepageData(data) {
  return request({
    url: `/sensitiveControl/getSmartBackupHomepageData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSmartBackupDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getSmartBackupDeptChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSmartBackupTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getSmartBackupTerminalUserChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listSmartBackupDeptData(data) {
  return request({
    url: `/sensitiveControl/listSmartBackupDeptData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listSmartBackupTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listSmartBackupTerminalUserData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSmartBackupDeptAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSmartBackupDeptAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSmartBackupTerminalUserAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSmartBackupTerminalUserAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSmartBackupTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSmartBackupTrendAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

