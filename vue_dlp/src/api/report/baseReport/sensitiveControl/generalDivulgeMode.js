import request from '@/utils/request'

export function getGeneralDivulgeModeHomepageData(data) {
  return request({
    url: `/sensitiveControl/getGeneralDivulgeModeHomepageData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralDivulgeModeDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getGeneralDivulgeModeDeptChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralDivulgeModeTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getGeneralDivulgeModeTerminalUserChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listGeneralDivulgeModeDeptData(data) {
  return request({
    url: `/sensitiveControl/listGeneralDivulgeModeDeptData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listGeneralDivulgeModeTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listGeneralDivulgeModeTerminalUserData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralDivulgeModeDeptAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralDivulgeModeDeptAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralDivulgeModeTerminalUserAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralDivulgeModeTerminalUserAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralDivulgeModeTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralDivulgeModeTrendAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

