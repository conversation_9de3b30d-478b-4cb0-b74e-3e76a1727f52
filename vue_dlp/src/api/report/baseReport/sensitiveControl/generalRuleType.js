import request from '@/utils/request'

export function getGeneralRuleTypeHomepageData(data) {
  return request({
    url: `/sensitiveControl/getGeneralRuleTypeHomepageData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralRuleTypeDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getGeneralRuleTypeDeptChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralRuleTypeTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getGeneralRuleTypeTerminalUserChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listGeneralRuleTypeDeptData(data) {
  return request({
    url: `/sensitiveControl/listGeneralRuleTypeDeptData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listGeneralRuleTypeTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listGeneralRuleTypeTerminalUserData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralRuleTypeDeptAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralRuleTypeDeptAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralRuleTypeTerminalUserAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralRuleTypeTerminalUserAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralRuleTypeTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralRuleTypeTrendAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listGeneralRuleTypeData(data) {
  return request({
    url: `/sensitiveControl/listGeneralRuleTypeData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralRuleTypeAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralRuleTypeAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

