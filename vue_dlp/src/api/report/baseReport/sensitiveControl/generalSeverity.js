import request from '@/utils/request'

export function getGeneralSeverityHomepageData(data) {
  return request({
    url: `/sensitiveControl/getGeneralSeverityHomepageData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralSeverityDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getGeneralSeverityDeptChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralSeverityTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getGeneralSeverityTerminalUserChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listGeneralSeverityDeptData(data) {
  return request({
    url: `/sensitiveControl/listGeneralSeverityDeptData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listGeneralSeverityTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listGeneralSeverityTerminalUserData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralSeverityDeptAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralSeverityDeptAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralSeverityTerminalUserAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralSeverityTerminalUserAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getGeneralSeverityTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getGeneralSeverityTrendAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

