import request from '@/utils/request'

export function getSporadicSeverityHomepageData(data) {
  return request({
    url: `/sensitiveControl/getSporadicSeverityHomepageData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicSeverityDeptChartData(data) {
  return request({
    url: `/sensitiveControl/getSporadicSeverityDeptChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicSeverityTerminalUserChartData(data) {
  return request({
    url: `/sensitiveControl/getSporadicSeverityTerminalUserChartData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listSporadicSeverityDeptData(data) {
  return request({
    url: `/sensitiveControl/listSporadicSeverityDeptData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function listSporadicSeverityTerminalUserData(data) {
  return request({
    url: `/sensitiveControl/listSporadicSeverityTerminalUserData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicSeverityDeptAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSporadicSeverityDeptAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicSeverityTerminalUserAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSporadicSeverityTerminalUserAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

export function getSporadicSeverityTrendAnalysisData(data) {
  return request({
    url: `/sensitiveControl/getSporadicSeverityTrendAnalysisData`,
    method: 'post',
    timeout: 0,
    data
  })
}

