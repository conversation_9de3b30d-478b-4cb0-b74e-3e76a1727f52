import request from '@/utils/request'
/**
 * 获取数据增量（页面查询数据）
 * @param data
 * @returns {AxiosPromise}
 */
export function getDataIncrement(data) {
  return request({
    url: `/issueReport/getDataIncrement`,
    method: 'post',
    data
  })
}

/**
 * 获取数据增量（弹框。表格数据）
 * @param data
 * @returns {AxiosPromise}
 */
export function getIncrementDetail(data) {
  return request({
    url: `/issueReport/getIncrementDetail`,
    method: 'post',
    data
  })
}

/**
 * 获取指定表七日数据增量趋势图
 * @param data 
 * @returns 
 */
export function getIncrementTrendByTable(data) {
  return request({
    url: `/issueReport/getIncrementTrendByTable`,
    method: 'post',
    data
  })
}

/**
 * 获取指定表指定日期数据增量TOP5终端
 * @param data 
 * @returns 
 */
export function getIncrementUserByTable(data) {
  return request({
    url: `/issueReport/getIncrementUserByTable`,
    method: 'post',
    data
  })
}

/**
 * 获取审计日志增量TOP10
 * @param data
 * @returns
 */
export function getTableByUserAndDate(data) {
  return request({
    url: `/issueReport/getTableByUserAndDate`,
    method: 'post',
    data
  })
}
