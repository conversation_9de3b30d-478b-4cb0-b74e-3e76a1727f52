import request from '@/utils/request'

/**
 * 加解密文件操作数据（首次加载，会记录日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetDataSecurityPanel(data, token) {
  return request({
    url: `/displayPanel/getDataSecurityPanel`,
    method: 'post',
    cancelToken: token,
    data
  })
}

/**
 * 加解密文件操作数据（定时器刷新，不会记录日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetDataSecurityPanelNoOplog(data, token) {
  return request({
    url: `/displayPanel/getDataSecurityPanelNoOplog`,
    method: 'post',
    cancelToken: token,
    data
  })
}
/**
 * 加解密文件操作数据（今日加解密记录）
 * @returns {AxiosPromise}
 */
export function displayPanelGetEncDecRecordRecord(token) {
  return request({
    url: `/displayPanel/getEncDecRecord`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 加解密文件操作数据（今日加解密记录以外的今日数据）
 * @returns {AxiosPromise}
 */
export function displayPanelGetTodayEncDesCount(token) {
  return request({
    url: `/displayPanel/getTodayEncDesCount`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 今日风险数据
 * @returns {AxiosPromise}
 */
export function realTimeRiskReportSummary(token) {
  return request({
    url: `/realTimeRiskReport/summary`,
    cancelToken: token,
    method: 'get'
  })
}

/**
 * 今日风险数据 会记录日志
 * @returns {AxiosPromise}
 */
export function realTimeRiskReportSummaryByAudit(token) {
  return request({
    url: `/realTimeRiskReport/summaryByAudit`,
    cancelToken: token,
    method: 'get'
  })
}

/**
 * 运维数据（获取cpu内存磁盘信息）
 * @returns {AxiosPromise}
 */
export function displayPanelGetServerResourceInfo(token) {
  return request({
    url: `/displayPanel/getServerResourceInfo`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 运维数据（获取系统信息）
 * @returns {AxiosPromise}
 */
export function displayPanelGetServerInfoForOption(data) {
  return request({
    url: `/displayPanel/getServerInfoForOption`,
    method: 'post',
    params: data
  })
}

/**
 * 运维数据（获取综合报表跑批和采集记录数量）（有日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetCollectionAndBatchInfo(token) {
  return request({
    url: `/displayPanel/getCollectionAndBatchInfo`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 运维数据（获取综合报表跑批和采集记录数量）（没日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetCollectionAndBatchInfoNoOplog(token) {
  return request({
    url: `/displayPanel/getCollectionAndBatchInfoNoOplog`,
    cancelToken: token,
    method: 'post'
  })
}
/**
 * 运维数据（获取当日计算机告警信息次数）
 * @returns {AxiosPromise}
 */
export function displayPanelGetComputerWarnTimes(token) {
  return request({
    url: `/displayPanel/getComputerWarnTimes`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 运维数据（获取终端统计数）
 * @returns {AxiosPromise}
 */
export function displayPanelGetTerminalCount(token) {
  return request({
    url: `/displayPanel/getTerminalCount`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 运维数据（获取趋势图）（没日志）
 * @param data
 * @returns {AxiosPromise}
 */
export function getDataCollectTrendNoOpLog(data, token) {
  return request({
    url: `/issueReport/getDataCollectTrendNoOpLog`,
    method: 'post',
    cancelToken: token,
    data
  })
}

/**
 * 软件安装与使用数据（右上角条件查询，会记录日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetSoftwareReportHomePageData(data, token) {
  return request({
    url: `/displayPanel/getSoftwareReportHomePageData`,
    method: 'post',
    cancelToken: token,
    data
  })
}

/**
 * 软件安装与使用数据（右上角条件查询，不会记录日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetSoftwareReportHomePageDataNoOplog(data, token) {
  return request({
    url: `/displayPanel/getSoftwareReportHomePageDataNoOplog`,
    method: 'post',
    cancelToken: token,
    data
  })
}
/**
 * 软件安装与使用数据（软件安装与使用情况）
 * @returns {AxiosPromise}
 */
export function displayPanelAppGetSoftwareReportToDayInfo(token) {
  return request({
    url: `/displayPanel/getSoftwareReportToDayInfo`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 文件外发分析数据（实时文件外发事件）
 * @returns {AxiosPromise}
 */
export function displayPanelGetTodayFileOutRecord(token) {
  return request({
    url: `/displayPanel/getTodayFileOutRecord`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 文件外发分析数据（首次加载，会记录日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetFileOutPanel(data, token) {
  return request({
    url: `/displayPanel/getFileOutPanel`,
    method: 'post',
    cancelToken: token,
    data
  })
}

/**
 * 文件外发分析数据（定时器刷新，不会记录日志）
 * @returns {AxiosPromise}
 */
export function displayPanelGetFileOutPanelNoOplog(data, token) {
  return request({
    url: `/displayPanel/getFileOutPanelNoOplog`,
    method: 'post',
    cancelToken: token,
    data
  })
}

/**
 * 文件外发分析数据（今日小方块数据、今日文件外发趋势）
 * @returns {AxiosPromise}
 */
export function displayPanelGetTodayFileOutCount(token) {
  return request({
    url: `/displayPanel/getTodayFileOutCount`,
    cancelToken: token,
    method: 'post'
  })
}

/**
 * 文件外发分析数据（风险外发实时预警）
 * @returns {AxiosPromise}
 */
export function displayPanelGetTodayRiskFileOutRecord(token) {
  return request({
    url: `/displayPanel/getTodayRiskFileOutRecord`,
    cancelToken: token,
    method: 'post'
  })
}
