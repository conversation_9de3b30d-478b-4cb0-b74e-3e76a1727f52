import request from '@/utils/request'
/**
 * 获取趋势图
 * @param data
 * @returns {AxiosPromise}
 */
export function getDataCollectTrend(data) {
  return request({
    url: `/issueReport/getDataCollectTrend`,
    method: 'post',
    data
  })
}

/**
 * 获取数据采集日志分页
 * @param data
 * @returns {AxiosPromise}
 */
export function getDataCollectPage(data) {
  return request({
    url: `/issueReport/getDataCollectPage`,
    method: 'post',
    data
  })
}

// dataList为接口/dataTansaction/list返回的数据
const dataList = []
// 表格数据
const list = []
for (let i = 0; i < 2; i++) {
  const template = {
    'id': i,
    'tableName': 'src_table' + Math.ceil(Math.random() * 10),
    'businessName': '日志' + Math.ceil(Math.random() * 10),
    'logQuantity': <PERSON><PERSON>ceil(Math.random() * 10),
    'change': <PERSON>.ceil(Math.random() * 10),
    'collectionSource': '192.168.4.55'
  }
  list.push(template)
}
// 图表数据
const lineOption = {
  title: {
    text: '数据异动报表'
  },
  xAxisData: ['2023-11-01', '2023-11-02', '2023-11-03', '2023-11-04', '2023-11-05', '2023-11-06', '2023-11-07', '2023-11-08'],
  seriesData: [1, 0, 3, 0, 0, 0, 0, 37]
}
dataList.list = list
dataList.lineOption = lineOption
export default dataList
