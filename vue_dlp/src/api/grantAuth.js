import request from '@/utils/request'
import qs from 'qs'

export function isCompetitorPermision() {
  return request({
    url: '/grantAuth/canCompetitorPermision',
    method: 'post'
  })
}

export function isShowNoneEnv() {
  return request({
    url: '/grantAuth/canNoneEnv',
    method: 'post'
  })
}

export function isShowFileDownload() {
  return request({
    url: '/grantAuth/canFileDownload',
    method: 'get'
  })
}

export function uploadFile(data) {
  return request.post('/grantAuth/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function grantAuthIni(data) {
  return request({
    url: '/grantAuth/grantAuthOfIni',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function grantAuthLdk(data) {
  return request({
    url: '/grantAuth/grantAuthOfLdk',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function isShowEncryAuthorize() {
  return request({
    url: '/grantAuth/canEncryAuthorize',
    method: 'get'
  })
}
export function isShowEncryAnyDataAuthorize() {
  return request({
    url: '/grantAuth/canEncryAnyDataAuthorize',
    method: 'get'
  })
}

export function isShowEncrySvrAuthorize() {
  return request({
    url: '/grantAuth/canEncrySvrAuthorize',
    method: 'get'
  })
}
