import request from '@/utils/request'

export function getNoticeSize() {
  return request({
    url: '/stgIssue/getNoticeSize',
    method: 'get'
  })
}

export function getIssueList(data) {
  return request({
    url: '/stgIssue/list',
    method: 'post',
    data
  })
}

export function activeStrategy() {
  return request({
    url: '/stgIssue/active',
    method: 'post'
  })
}

export function getApprovalMessageList(data) {
  return request({
    url: '/approvalMessage/list',
    method: 'post',
    data
  })
}

export function gettherMessageList(data) {
  return request({
    url: '/otherMessage/list',
    method: 'post',
    data
  })
}

export function deleteDbBackupNoticeByIds(data) {
  return request({
    url: '/stgIssue/deleteDbBackupNoticeByIds',
    method: 'post',
    data
  })
}
