import request from '@/utils/request'
import { handleTrackerDisconnectResp } from './documentTrack'

export function readCodeFromImg(file, cancelToken) {
  const data = new FormData()
  data.append('file', file)
  return request({
    url: '/blindWatermark/readCodeFromImg',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 0,
    cancelToken,
    responseType: 'blob',
    data
  }).then(respond => {
    if (respond instanceof Blob) {
      return respond
    }
    return Promise.reject('Unsupported response.')
  })
}

export function parseWatermarkFromImg(file, cancelToken) {
  const data = new FormData()
  data.append('file', file)
  return request({
    url: '/blindWatermark/parseWatermarkFromImg',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 0,
    cancelToken,
    data
  })
}

export function getProcesses() {
  return request.get('/blindWatermark/processes')
}

export function getLogPage(data) {
  return request({
    url: '/blindWatermark/getLogPage',
    method: 'post',
    data
  }).then(res => {
    if (res.data && res.data.items) {
      res.data.items.forEach((item, index) => {
        item.id = index
      })
    }
    return res
  }).catch(handleTrackerDisconnectResp)
}

// export function getDetail(data) {
//   return request({
//     url: '/blindWatermark/getDetail',
//     method: 'post',
//     data
//   })
// }

export function createStrategy(data) {
  return request({
    url: '/blindWatermark/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/blindWatermark/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/blindWatermark/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/blindWatermark/getPage',
    method: 'post',
    data
  })
}

export function createImgDownloadUrl(filename) {
  return `${process.env.VUE_APP_BASE_API}/blindWatermark/img/${filename}`
}
