import request from '@/utils/request'
import moment from 'moment'
import i18n from '@/lang'
import store from '@/store'

export function isSupportedReadDocInfo() {
  return request.get('/documentTrack/isSupportedReadDocInfo')
}

export function readInfoFromFile(file) {
  const data = new FormData()
  data.append('file', file)
  return request({
    url: '/documentTrack/readInfoFromFile',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 0,
    data
  }).then(respond => respond.data)
}

export function getOriginsPage(data) {
  return request({
    url: '/documentTrack/getOriginsPage',
    method: 'post',
    data
  }).catch(handleTrackerDisconnectResp)
}

export function getSpreadPage(data) {
  return request({
    url: '/documentTrack/getSpreadPage',
    method: 'post',
    data
  }).catch(handleTrackerDisconnectResp)
}

export function getSpreadRcvPage(data) {
  return request({
    url: '/documentTrack/getSpreadRcvPage',
    method: 'post',
    data
  }).catch(handleTrackerDisconnectResp)
}

export function getSpreadSrcPage(data) {
  return request({
    url: '/documentTrack/getSpreadSrcPage',
    method: 'post',
    data
  }).catch(handleTrackerDisconnectResp)
}

export function getDetailList(data) {
  return request({
    url: '/documentTrack/getDetailList',
    method: 'post',
    data
  })
}

export function getDetail(data) {
  return request({
    url: '/documentTrack/getDetail',
    method: 'post',
    data
  })
}

export function getGlobalStg() {
  return request.get('/documentTrack/global')
}

export function updateGlobalStg(data) {
  return request({
    url: '/documentTrack/global',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/documentTrack/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/documentTrack/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/documentTrack/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/documentTrack/getPage',
    method: 'post',
    data
  })
}

const PROCESS_I18N_MAPPING = {
  'QQ.exe': {
    zh: '腾讯QQ',
    tw: '騰訊QQ',
    en: 'Tencent QQ'
  },
  'WXWork.exe': {
    zh: '企业微信',
    tw: '企業微信',
    en: 'WeCom'
  },
  'WeChat.exe': {
    zh: '微信',
    tw: '微信',
    en: 'WeChat'
  },
  '360se.exe': {
    zh: '360安全浏览器',
    tw: '360安全瀏覽器',
    en: '360 Security Browser'
  },
  '360chrome.exe': {
    zh: '360极速浏览器',
    tw: '360極速瀏覽器',
    en: '360 Extreme Browser'
  },
  '360ChromeX.exe': {
    zh: '360极速浏览器(64位)',
    tw: '360極速瀏覽器(x64)',
    en: '360 Extreme Browser(x64)'
  },
  'BaiduNetdisk.exe': {
    zh: '百度网盘',
    tw: '百度網盤',
    en: 'Baidu Netdisk'
  },
  'explorer.exe': {
    zh: 'Windows 资源管理器',
    tw: 'Windows 資料總管',
    en: 'Windows Explorer'
  },
  'LdClient.exe': {
    zh: '蓝盾终端',
    tw: '蓝盾終端',
    en: 'Lan Defender Terminal'
  },
  'VMware虚拟云桌面': {
    tw: 'VMware虛擬雲桌面',
    en: 'VMware Virtual Cloud Desktop'
  },
  '腾讯QQ(通过QQ发送到)': {
    tw: '騰訊QQ（通過QQ傳送到）',
    en: 'Tencent QQ (Sent via QQ)'
  }
}

export function getProcessMapping() {
  const lang = store.getters.language
  const promise = request.get('/documentTrack/getProcessMapping')
  if (lang === 'zh') {
    return promise
  }
  return promise.then(respond => {
    respond.data.forEach(item => {
      if (item.editable) {
        if (item.id < 17) {
          const i18nMap = PROCESS_I18N_MAPPING[item.label]
          if (i18nMap) {
            item.label = i18nMap[lang] || i18nMap.en || item.label
          }
        }
        return
      }
      const i18nMap = PROCESS_I18N_MAPPING[item.value]
      if (i18nMap) {
        item.label = i18nMap[lang] || i18nMap.en || item.label
      }
    })
    return respond
  })
}

export function addProcessMapping(data) {
  return request({
    url: '/documentTrack/addProcessMapping',
    method: 'post',
    data
  })
}

export function updateProcessMapping(data) {
  return request({
    url: '/documentTrack/updateProcessMapping',
    method: 'post',
    data
  })
}

export function deleteProcessMapping(data) {
  return request({
    url: '/documentTrack/deleteProcessMapping',
    method: 'post',
    params: data
  })
}

export function timestampFormatter(row, timestamp) {
  return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

export function formatProcess(processMapping, processName, showDetail = false) {
  if (Array.isArray(processMapping) && processName) {
    for (let i = 0; i < processMapping.length; i++) {
      const process = processMapping[i]
      if (process.value.toLowerCase() === processName.toLowerCase()) {
        let result = process.label
        if (showDetail) {
          result += '(' + processName + ')'
        }
        return result
      }
    }
  }
  return processName
}

export function encodeArrToVal(arr) {
  if (Array.isArray(arr) && arr.length > 0) {
    return arr.reduce((pre, cur) => pre + cur)
  }
  return 0
}

export function decodeValToArr(val, bits = 2) {
  const arr = []
  for (let i = 0; i < bits; i++) {
    if (((val >> i) & 1) === 1) {
      arr.push(1 << i)
    }
  }
  return arr
}

export function handleTrackerDisconnectResp(error) {
  const resp = error && error.response && error.response.data
  if (resp && resp.code === 4001 && ['文件溯源服务器未配置', '文件溯源服务器连接失败'].indexOf(resp.data) >= 0) {
    return { code: 20000, data: { total: 0, items: [] }}
  }
}

export function copy(text, callback) {
  if (navigator.clipboard) {
    // clipboard api 复制
    // console.log('clipboard api 复制')
    navigator.clipboard.writeText(text).then(r => callback && callback(r));
  } else {
    // console.log('execCommand copy 复制')
    const textarea = document.createElement('textarea');
    document.body.appendChild(textarea);
    // 隐藏此输入框
    textarea.style.position = 'fixed';
    textarea.style.clip = 'rect(0 0 0 0)';
    textarea.style.top = '10px';
    // 赋值
    textarea.value = text;
    // 选中
    textarea.select();
    // 复制
    document.execCommand('copy', true);
    callback && callback()
    // 移除输入框
    document.body.removeChild(textarea);
  }
}

export function parseCirculationType(type) {
  for (let i = 0; i < CIRCULATION_TYPES.length; i++) {
    const options = CIRCULATION_TYPES[i].options
    for (let j = 0; j < options.length; j++) {
      const item = options[j]
      if (item.value === type) {
        return item.label
      }
    }
  }
  return undefined
}

export const CIRCULATION_TYPES = [
  {
    label: '',
    prop: 0,
    options: [{ value: 0, label: i18n.t('pages.documentTrack_opprtmomentType0') }]
  },
  {
    label: i18n.t('pages.documentTrack_opprtmomentTypeGroup1'),
    prop: 2,
    options: [
      {
        value: 2,
        menuCode: 'D32', // 通讯工具文件传输管控
        type: 'IM',
        label: i18n.t('pages.documentTrack_opprtmomentType2')
      }, {
        hide: true,
        value: 8,
        type: 'Browser',
        label: i18n.t('pages.documentTrack_opprtmomentType4')
      }, {
        hide: true,
        value: 32,
        type: 'Email',
        label: i18n.t('pages.documentTrack_opprtmomentType6')
      }, {
        hide: true,
        value: 128,
        type: 'NetDisk',
        label: i18n.t('pages.documentTrack_opprtmomentType8')
      }, {
        hide: true,
        value: 512,
        menuCode: 'D71', // 0407 共享管控
        type: 'FileMgr',
        label: i18n.t('pages.documentTrack_opprtmomentType10')
      }, {
        hide: true,
        value: 2048,
        menuCode: 'C71', // 0307 U盘管控
        type: 'FileMgr',
        label: i18n.t('pages.documentTrack_opprtmomentType12')
      }
    ]
  },
  {
    label: i18n.t('pages.documentTrack_opprtmomentTypeGroup2'),
    prop: 1,
    options: [
      {
        value: 1,
        menuCode: 'D32', // 通讯工具文件传输管控
        type: 'IM',
        label: i18n.t('pages.documentTrack_opprtmomentType1')
      }, {
        value: 4,
        menuCode: 'D2B', // 网页上传文件设置
        type: 'Browser',
        label: i18n.t('pages.documentTrack_opprtmomentType3')
      }, {
        value: 16,
        type: 'Email',
        label: i18n.t('pages.documentTrack_opprtmomentType5')
      }, {
        value: 64,
        menuCode: 'D81', // 网盘传输备份限制
        type: 'NetDisk',
        label: i18n.t('pages.documentTrack_opprtmomentType7')
      }, {
        hide: true,
        value: 256,
        menuCode: 'D71', // 0407 共享管控
        type: 'FileMgr',
        label: i18n.t('pages.documentTrack_opprtmomentType9')
      }, {
        value: 1024,
        menuCode: 'C71', // 0307 U盘管控
        type: 'FileMgr',
        label: i18n.t('pages.documentTrack_opprtmomentType11')
      }
    ]
  }
]

export const WHITELIST_CHANNELS = {
  0: i18n.t('pages.documentTrack_whitelistChannels0'),
  1: i18n.t('pages.documentTrack_whitelistChannels1'),
  2: i18n.t('pages.documentTrack_whitelistChannels2'),
  3: i18n.t('pages.documentTrack_whitelistChannels3')
}
