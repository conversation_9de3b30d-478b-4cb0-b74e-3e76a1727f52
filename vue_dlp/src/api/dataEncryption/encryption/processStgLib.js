import request from '@/utils/request'
import qs from 'qs'
import store from '@/store'
import { fetchFile } from '@/utils/download/helper'

export function getStgTypeTree(data) {
  return request({
    url: '/processStgLib/listGroupTree',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getStgTree(data) {
  return request({
    url: '/processStgLib/listStgTree',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function createStgType(data) {
  return request({
    url: '/processStgLib/addGroup',
    method: 'post',
    data
  })
}
export function updateStgType(data) {
  return request({
    url: '/processStgLib/updateGroup',
    method: 'post',
    data
  })
}

export function deleteStgType(data) {
  return request({
    url: '/processStgLib/deleteGroup',
    method: 'post',
    params: data
  })
}
export function deleteStgTypeAndData(data) {
  return request({
    url: '/processStgLib/deleteGroupAndData',
    method: 'post',
    data
  })
}
export function moveStgTypeToOther(data) {
  return request({
    url: '/processStgLib/moveGroupToOther',
    method: 'post',
    data
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/processStgLib/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getStgTypeByName(data) {
  return request({
    url: '/processStgLib/getGroupByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStgPage(data) {
  return request({
    url: '/processStgLib/getStgPage',
    method: 'post',
    data
  })
}

export function createStg(data) {
  return request({
    url: '/processStgLib/addStg',
    method: 'post',
    data
  })
}
export function updateStg(data) {
  return request({
    url: '/processStgLib/updateStg',
    method: 'post',
    data
  })
}

export function deleteStg(data) {
  return request({
    url: '/processStgLib/deleteStg',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStgByName(data) {
  return request({
    url: '/processStgLib/getStgByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getImportProgress() {
  return request({
    url: '/processStgLib/getImportProgress',
    method: 'get'
  })
}

export function importStg(data) {
  return request({
    url: '/processStgLib/importStg',
    method: 'post',
    data: qs.stringify(data),
    timeout: 0
  })
}

export function importFromPrint(data) {
  return request({
    url: '/processStgLib/saveFromPrint',
    method: 'post',
    data
  })
}

export function validProcessName(data) {
  return request({
    url: '/processStgLib/validProcessName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getProcessByIds(data) {
  return request({
    url: '/processStgLib/getProcessByIds',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function saveProcessBatch(data) {
  return request({
    url: '/processStgLib/saveProcessBatch',
    method: 'post',
    timeout: Math.max(store.getters.requestTimeout, 180000),
    data
  })
}

export function importProcess(data) {
  return request.post('/processStgLib/importProcess', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function updateProcessBatch(data) {
  return request({
    url: '/processStgLib/updateProcessBatch',
    method: 'post',
    data
  })
}

export function updateRelaMd5(data) {
  return request({
    url: '/processStgLib/updateRelaMd5',
    method: 'post',
    data
  })
}

export function exportStg(data, opts) {
  return fetchFile({
    ...opts,
    url: '/processStgLib/exportStg',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data: qs.stringify(data)
  })
}

