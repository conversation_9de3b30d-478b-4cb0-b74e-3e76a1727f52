import request from '@/utils/request'

export function getConfigPage(data) {
  return request({
    url: '/operatorConfig/getPage',
    method: 'post',
    data
  })
}

export function getConfigByName(data) {
  return request({
    url: '/operatorConfig/getByName',
    method: 'post',
    data
  })
}

export function addConfig(data) {
  return request({
    url: '/operatorConfig/add',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/operatorConfig/update',
    method: 'post',
    data
  })
}

export function deleteConfig(data) {
  return request({
    url: '/operatorConfig/delete',
    method: 'post',
    params: data
  })
}
