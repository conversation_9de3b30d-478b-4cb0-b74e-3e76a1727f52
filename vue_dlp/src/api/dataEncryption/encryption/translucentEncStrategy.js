import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/translucentEncStrategy/getPage',
    method: 'post',
    params: data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/translucentEncStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/translucentEncStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/translucentEncStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/translucentEncStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/translucentEncStrategy/delete',
    method: 'post',
    params: data
  })
}

export function importStrategy(data) {
  return request.post('/translucentEncStrategy/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}
