import request from '@/utils/request'

/**
 * 获取离线策略时间延长配置列表
 * @param {Object} data 查询参数
 */
export function getTimeExtensionList(data) {
  return request({
    url: '/offlineStrategyExtendConfig/getPage',
    method: 'post',
    data
  })
}

/**
 * 新增离线策略时间延长配置
 * @param {Object} data 配置数据
 */
export function createTimeExtension(data) {
  return request({
    url: '/offlineStrategyExtendConfig/add',
    method: 'post',
    data
  })
}

/**
 * 更新离线策略时间延长配置
 * @param {Object} data 配置数据
 */
export function updateTimeExtension(data) {
  return request({
    url: '/offlineStrategyExtendConfig/update',
    method: 'post',
    data
  })
}

/**
 * 删除离线策略时间延长配置
 * @param {Array} ids 配置ID数组
 */
export function deleteTimeExtension(ids) {
  return request({
    url: '/offlineStrategyExtendConfig/delete',
    method: 'post',
    data: ids
  })
}

/**
 * 根据ID获取配置详情
 * @param {String|Number} id 配置ID
 */
export function getTimeExtensionById(id) {
  return request({
    url: `/offlineStrategyExtendConfig/getById/${id}`,
    method: 'get'
  })
}

/**
 * 同步配置到云服务
 * @param {Array} ids 配置ID数组
 */
export function syncToCloud(ids) {
  return request({
    url: '/offlineStrategyExtendConfig/syncToCloud',
    method: 'post',
    data: ids
  })
}

/**
 * 获取配额信息
 */
export function getQuotaInfo() {
  return request({
    url: '/offlineStrategyExtendQuota/get',
    method: 'get'
  })
}
