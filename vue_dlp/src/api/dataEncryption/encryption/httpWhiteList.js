import request from '@/utils/request'
import qs from 'qs'

export function getStrategy(data) {
  return request({
    url: '/httpWhiteListStrategy/getHttpWhiteList',
    method: 'post',
    data
  })
}

export function saveStrategy(data) {
  return request({
    url: '/httpWhiteListStrategy/save',
    method: 'post',
    data
  })
}

export function getServerStgPage(data) {
  return request({
    url: '/httpWhiteListStrategy/getServerPage',
    method: 'post',
    data
  })
}

export function getServerStgByName(data) {
  return request({
    url: '/httpWhiteListStrategy/getServerByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createServerStg(data) {
  return request({
    url: '/httpWhiteListStrategy/addServer',
    method: 'post',
    data
  })
}

export function updateServerStg(data) {
  return request({
    url: '/httpWhiteListStrategy/updateServer',
    method: 'post',
    data
  })
}

export function deleteServerStg(data) {
  return request({
    url: '/httpWhiteListStrategy/deleteServer',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getProcessFilterStgPage(data) {
  return request({
    url: '/httpWhiteListStrategy/getProcessPage',
    method: 'post',
    data
  })
}

export function getProcessStgPage(data) {
  return request({
    url: '/httpWhiteListStrategy/getProcessPage',
    method: 'post',
    data
  })
}

export function getProcessStgByName(data) {
  return request({
    url: '/httpWhiteListStrategy/getProcessByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createProcessStg(data) {
  return request({
    url: '/httpWhiteListStrategy/addProcess',
    method: 'post',
    data
  })
}

export function updateProcessStg(data) {
  return request({
    url: '/httpWhiteListStrategy/updateProcess',
    method: 'post',
    data
  })
}

export function deleteProcessStg(data) {
  return request({
    url: '/httpWhiteListStrategy/deleteProcess',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getServerTreeNode() {
  return request({
    url: '/serverLibrary/listTree',
    method: 'get'
  })
}

export function updateConfig(data) {
  return request({
    url: '/httpWhiteListStrategy/updateConfig',
    method: 'post',
    data
  })
}

