import request from '@/utils/request'

export function createScan(data) {
  return request({
    url: '/diskScanSelfCheck/add',
    method: 'post',
    data
  })
}

export function updateScan(data) {
  return request({
    url: '/diskScanSelfCheck/update',
    method: 'post',
    data
  })
}

export function deleteStg(data) {
  return request({
    url: '/diskScanSelfCheck/delete',
    method: 'post',
    params: data
  })
}

export function getPage(data) {
  return request({
    url: '/diskScanSelfCheck/getPage',
    method: 'post',
    data
  })
}

export function getScanContentStgIdName(stgTypeNum) {
  return request({
    url: '/contentStrategy/listIdName/' + stgTypeNum,
    method: 'get'
  })
}

export function createContentStg(data) {
  return request({
    url: '/diskScanSelfCheck/addContentStg',
    method: 'post',
    data
  })
}

export function updateContentStg(data) {
  return request({
    url: '/diskScanSelfCheck/updateContentStg',
    method: 'post',
    data
  })
}

export function deleteContentStg(data) {
  return request({
    url: '/diskScanSelfCheck/deleteContentStg',
    method: 'post',
    params: data
  })
}
