import request from '@/utils/request'
import qs from 'qs'

export function getMailWhiteList(data) {
  return request({
    url: '/mailWhiteListStrategy/getMailWhiteList',
    method: 'post',
    data
  })
}

export function getStrategy(data) {
  return request({
    url: '/mailWhiteListStrategy/getMailWhiteList',
    method: 'post',
    data
  })
}

export function saveStrategy(data) {
  return request({
    url: '/mailWhiteListStrategy/save',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/mailWhiteListStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/mailWhiteListStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/mailWhiteListStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/mailWhiteListStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/mailWhiteListStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getMailConfig() {
  return request({
    url: '/mailWhiteListStrategy/getConfig',
    method: 'get'
  })
}

export function saveMailConfig(data) {
  return request({
    url: '/mailWhiteListStrategy/saveConfig',
    method: 'post',
    data
  })
}
