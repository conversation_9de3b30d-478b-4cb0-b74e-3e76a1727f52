import request from '@/utils/request'

export function getMachineCodeWhiteListPage(data) {
  return request({
    url: '/outgoingMachineCode/getPage',
    method: 'post',
    data
  })
}

export function listMachineCodeInfo(data) {
  return request({
    url: '/outgoingMachineCode/listMachineCode',
    method: 'post',
    data
  })
}

export function machineCodeWhiteListExist(data) {
  return request({
    url: '/outgoingMachineCode/machineCodeWhiteListExist',
    method: 'post',
    data
  })
}

export function addMachineCodeWhiteList(data) {
  return request({
    url: '/outgoingMachineCode/add',
    method: 'post',
    data
  })
}

export function updateMachineCodeWhiteList(data) {
  return request({
    url: '/outgoingMachineCode/update',
    method: 'post',
    data
  })
}

export function getGroupTree() {
  return request({
    url: '/outgoingMachineCode/getGroupTree',
    method: 'post'
  })
}

export function deleteGroup(data) {
  return request({
    url: '/outgoingMachineCode/deleteGroup?id=' + data,
    method: 'post'
  })
}

export function deleteMachineCode(data) {
  return request({
    url: '/outgoingMachineCode/delete?ids=' + data,
    method: 'post'
  })
}

export function addGroup(data) {
  return request({
    url: '/outgoingMachineCode/addGroup',
    method: 'post',
    data
  })
}
export function updateGroup(data) {
  return request({
    url: '/outgoingMachineCode/updateGroup',
    method: 'post',
    data
  })
}

export function getGroupByName(data) {
  return request({
    url: '/outgoingMachineCode/getGroupByName',
    method: 'post',
    data
  })
}

export function countInfoByGroupId(groupId) {
  return request({
    url: '/outgoingMachineCode/countByGroupId?groupId=' + groupId,
    method: 'get'
  })
}
