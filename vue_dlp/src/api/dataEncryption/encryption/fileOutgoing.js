import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getCodePage(data) {
  return request({
    url: '/outgoing/code/getPage',
    method: 'post',
    data
  })
}

export function getCodeByName(data) {
  return request({
    url: '/outgoing/code/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createCode(data) {
  return request({
    url: '/outgoing/code/add',
    method: 'post',
    data
  })
}

export function updateCode(data) {
  return request({
    url: '/outgoing/code/update',
    method: 'post',
    data
  })
}

export function deleteCode(data) {
  return request({
    url: '/outgoing/code/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

// config
export function getConfigPage(data) {
  return request({
    url: '/outgoing/config/getPage',
    method: 'post',
    data
  })
}

export function getConfigByName(data) {
  return request({
    url: '/outgoing/config/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createConfig(data) {
  return request({
    url: '/outgoing/config/add',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/outgoing/config/update',
    method: 'post',
    data
  })
}

export function deleteConfig(data) {
  return request({
    url: '/outgoing/config/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getWebSitePage(data) {
  return request({
    url: '/webSite/getPage',
    method: 'post',
    data
  })
}

export function createWebSite(data) {
  return request({
    url: '/webSite/add',
    method: 'post',
    data
  })
}

export function updateWebSite(data) {
  return request({
    url: '/webSite/update',
    method: 'post',
    data
  })
}

export function deleteWebSite(data) {
  return request({
    url: '/webSite/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getWaterMarkPage(data) {
  return request({
    url: '/outgoing/' + data.mark + '/getPage',
    method: 'post',
    data
  })
}

export function getWaterMarkByName(data) {
  return request({
    url: '/outgoing/' + data.mark + '/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createWaterMark(data) {
  return request({
    url: '/outgoing/' + data.mark + '/add',
    method: 'post',
    data
  })
}

export function updateWaterMark(data) {
  return request({
    url: '/outgoing/' + data.mark + '/update',
    method: 'post',
    data
  })
}

export function deleteWaterMark(data) {
  return request({
    url: '/outgoing/' + data.mark + '/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getWaterMarkTree(data) {
  return request({
    url: '/waterMarkLib/listTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function downloadTool(data, file) {
  return fetchFile({
    file,
    jwt: true,
    topic: 'tool',
    url: '/outgoing/code/export',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data: qs.stringify(data)
  })
}

export function getToolMap(data) {
  return request({
    url: '/outgoing/code/getToolMap',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getByPriority(data) {
  return request({
    url: '/webSite/getByPriority',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getMaxPriority() {
  return request({
    url: '/webSite/getMaxPriority',
    method: 'get'
  })
}
