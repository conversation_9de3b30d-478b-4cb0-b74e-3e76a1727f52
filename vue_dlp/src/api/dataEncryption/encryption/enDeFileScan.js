import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/enDeFileScan/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/enDeFileScan/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/enDeFileScan/add',
    method: 'post',
    data
  })
}
export function updateData(data) {
  return request({
    url: '/enDeFileScan/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/enDeFileScan/delete',
    method: 'post',
    params: data
  })
}
