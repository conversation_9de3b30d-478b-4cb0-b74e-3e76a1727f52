import request from '@/utils/request'

export function getApplySecurityAccessList(data) {
  return request({
    url: '/applySecurityAccess/getPage',
    method: 'post',
    data
  })
}

export function createApplySecurityAccess(data) {
  return request({
    url: '/applySecurityAccess/add',
    method: 'post',
    data
  })
}
export function updateApplySecurityAccess(data) {
  return request({
    url: '/applySecurityAccess/update',
    method: 'post',
    data
  })
}

export function deleteApplySecurityAccess(data) {
  return request({
    url: '/applySecurityAccess/delete',
    method: 'post',
    params: data
  })
}
