import request from '@/utils/request'
import qs from 'qs'

export function getEncryptSpecialPathPage(data) {
  return request({
    url: '/encryptSpecialPath/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/encryptSpecialPath/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/encryptSpecialPath/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/encryptSpecialPath/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/encryptSpecialPath/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/encryptSpecialPath/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
