import request from '@/utils/request'

export function getSpecialFileExtStrategyPage(data) {
  return request({
    url: '/specialSuffixStrategy/getPage',
    method: 'post',
    params: data
  })
}
export function getSpecialFileExtStrategyList(data) {
  return request({
    url: '/specialSuffixStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function getSpecialFileExtStrategyByName(data) {
  return request({
    url: '/specialSuffixStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createSpecialFileExtStrategy(data) {
  return request({
    url: '/specialSuffixStrategy/add',
    method: 'post',
    data
  })
}
export function updateSpecialFileExtStrategy(data) {
  return request({
    url: '/specialSuffixStrategy/update',
    method: 'post',
    data
  })
}

export function deleteSpecialFileExtStrategy(data) {
  return request({
    url: '/specialSuffixStrategy/delete',
    method: 'post',
    params: data
  })
}
