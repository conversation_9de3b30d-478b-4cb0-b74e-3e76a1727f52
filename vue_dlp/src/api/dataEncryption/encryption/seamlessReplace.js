import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getStrategyList(data) {
  return request({
    url: '/seamlessReplaceStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/seamlessReplaceStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/seamlessReplaceStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/seamlessReplaceStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/seamlessReplaceStrategy/delete',
    method: 'post',
    params: data
  })
}

/** 查询竞品信息列表 */
export function listCompetitorInfo() {
  return request({
    url: '/seamlessReplaceStrategy/listCompetitorInfo',
    method: 'post'
  })
}

export function uploadFile(data) {
  return request.post('/seamlessReplaceStrategy/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function stop(data) {
  return request({
    url: '/seamlessReplaceStrategy/stop',
    method: 'post',
    params: data
  })
}

export function restart(data) {
  return request({
    url: '/seamlessReplaceStrategy/restart',
    method: 'post',
    params: data
  })
}

export function downloadFile(data, opts) {
  return fetchFile({
    ...opts,
    url: '/seamlessReplaceStrategy/downloadFile',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data
  })
}

export function startDownload() {
  return request({
    url: '/seamlessReplaceStrategy/startDownload',
    method: 'post'
  })
}
