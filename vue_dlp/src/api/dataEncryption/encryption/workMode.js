import request from '@/utils/request'
import qs from 'qs'
import store from '@/store'

export function getWorkModePage(data) {
  return request({
    url: '/workMode/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/workMode/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/workMode/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/workMode/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/workMode/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/workMode/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createSoftType(data) {
  return request({
    url: '/workMode/addSoftType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateSoftType(data) {
  return request({
    url: '/workMode/updateSoftType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteSoftType(data) {
  return request({
    url: '/workMode/deleteSoftType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSoftTypeByName(data) {
  return request({
    url: '/workMode/getSoftTypeByName',
    method: 'post',
    data
  })
}

export function getSoftTypeTreeNode(data) {
  return request({
    url: '/workMode/getTypeTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createSoftInfo(data) {
  return request({
    url: '/workMode/addSoft',
    method: 'post',
    data
  })
}

export function batchAddSoftInfo(data) {
  return request({
    url: '/workMode/batchAddSoft',
    method: 'post',
    timeout: Math.max(store.getters.requestTimeout, 180000),
    data
  })
}

export function updateSoftInfo(data) {
  return request({
    url: '/workMode/updateSoft',
    method: 'post',
    data
  })
}

export function deleteSoftInfo(data) {
  return request({
    url: '/workMode/deleteSoft',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSoftInfoPage(data) {
  return request({
    url: '/workMode/getSoftPage',
    method: 'post',
    data
  })
}

export function countInfoByGroupId(groupId) {
  return request({
    url: '/workMode/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function importSoftInfoFromLib(data) {
  return request({
    url: '/workMode/addFromLib',
    method: 'post',
    data
  })
}

export function getGroupIdAndSoftNameMap(data) {
  return request({
    url: '/workMode/getGroupIdAndSoftNameMap',
    method: 'post',
    data: data
  })
}
