import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/processStg/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/processStg/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/processStg/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/processStg/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/processStg/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getConfigPage(data) {
  return request({
    url: '/processStg/getConfigPage',
    method: 'post',
    data
  })
}

export function getConfigByName(data) {
  return request({
    url: '/processStg/getConfigByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createConfig(data) {
  return request({
    url: '/processStg/addConfig',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/processStg/updateConfig',
    method: 'post',
    data
  })
}

export function deleteConfig(data) {
  return request({
    url: '/processStg/deleteConfig',
    method: 'post',
    data: qs.stringify(data)
  })
}
