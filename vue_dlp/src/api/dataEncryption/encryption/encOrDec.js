import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getStrategyPage(data) {
  return request({
    url: '/encOrDec/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/encOrDec/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/encOrDec/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/encOrDec/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/encOrDec/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/encOrDec/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}
