import request from '@/utils/request'

export function getTemplatePage(data) {
  return request({
    url: '/pkeyInfo/getPage',
    method: 'post',
    data
  })
}

export function saveInfo(data) {
  return request({
    url: '/dkeyAuthInfo/update',
    method: 'post',
    data
  })
}

export function deleteInfo(data) {
  return request({
    url: '/dkeyAuthInfo/delete/' + data,
    method: 'get'
  })
}

export function getDecryptionKeyStrategy(startTime, endTime) {
  return request({
    url: '/dkeyAuthInfo/getDecryptionKey/' + startTime + '/' + endTime,
    method: 'get'
  })
}

export function listInfo() {
  return request({
    url: '/dkeyAuthInfo/list',
    method: 'get'
  })
}
