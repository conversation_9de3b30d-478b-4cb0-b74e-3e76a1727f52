import request from '@/utils/request'

export function getListBase(data) {
  return request({
    url: '/eamilBaseConfig/getList',
    method: 'post',
    data
  })
}

export function getGroupList2(data) {
  return request({
    url: '/approvalEmailGroup/getGroup2',
    method: 'post',
    data
  })
}

export function getGroupList3(data) {
  return request({
    url: '/approvalEmailGroup/groupTree',
    method: 'post',
    data
  })
}

export function addGroup(data) {
  return request({
    url: '/approvalEmailGroup/addGroup ',
    method: 'post',
    data
  })
}

export function updateGroup(data) {
  return request({
    url: '/approvalEmailGroup/updateGroup',
    method: 'post',
    data
  })
}

export function deleteGroup(data) {
  return request({
    url: '/approvalEmailGroup/deleteGroup',
    method: 'post',
    data
  })
}

export function addEmail(data) {
  return request({
    url: '/approvalEmailInfo/addEmail ',
    method: 'post',
    data
  })
}

export function updateEmail(data) {
  return request({
    url: '/approvalEmailInfo/updateEmail  ',
    method: 'post',
    data
  })
}

export function deleteEmail(data) {
  return request({
    url: '/approvalEmailInfo/deleteEmail',
    method: 'post',
    data
  })
}
