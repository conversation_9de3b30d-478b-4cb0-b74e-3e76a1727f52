import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/softwareBlacklistStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/softwareBlacklistStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/softwareBlacklistStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/softwareBlacklistStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/softwareBlacklistStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listProcess(data) {
  return request({
    url: '/softwareBlacklist/listProcess',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listProcessPage(data) {
  return request({
    url: '/softwareBlacklist/listProcessPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getGroupTree() {
  return request({
    url: '/softwareBlacklist/getGroupTree',
    method: 'post'
  })
}

export function deleteProcess(data) {
  return request({
    url: '/softwareBlacklist/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function addProcess(data) {
  return request({
    url: '/softwareBlacklist/add',
    method: 'post',
    data
  })
}

export function updateProcess(data) {
  return request({
    url: '/softwareBlacklist/update',
    method: 'post',
    data
  })
}

export function importFromLib(data) {
  return request({
    url: '/softwareBlacklist/importFromLib',
    method: 'post',
    data
  })
}

export function importFromExe(data) {
  return request({
    url: '/softwareBlacklist/importFromExe',
    method: 'post',
    data
  })
}

export function deleteAppType(data) {
  return request({
    url: '/softwareBlacklist/deleteGroup',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function addAppType(data) {
  return request({
    url: '/softwareBlacklist/addGroup',
    method: 'post',
    data
  })
}
export function updateAppType(data) {
  return request({
    url: '/softwareBlacklist/updateGroup',
    method: 'post',
    data
  })
}

export function getAppTypeByName(data) {
  return request({
    url: '/softwareBlacklist/getGroupByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countInfoByGroupId(groupId) {
  return request({
    url: '/softwareBlacklist/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function importProcess(data, onUploadProgress, token) {
  return request.post('/softwareBlacklist/importProcess', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}

//  根据id查询所关联的策略
export function getStrategyByOutgoingProcessIds(data) {
  return request({
    url: '/softwareBlacklist/getStrategyByOutgoingProcessIds',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function importProcessReturnList(data, onUploadProgress, token) {
  return request.post('/softwareBlacklist/importProcessReturnList', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}
