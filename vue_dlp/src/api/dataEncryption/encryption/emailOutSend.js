import request from '@/utils/request'

export function getSuperConfig() {
  return request({
    url: '/eamilBaseConfig/getEmailConfig',
    method: 'get'
  })
}
export function updateSuperConfig(data) {
  return request({
    url: '/eamilBaseConfig/updateEmailConfig',
    method: 'post',
    data
  })
}
export function getPage(data) {
  return request({
    url: '/eamilBaseConfig/getPage',
    method: 'post',
    data
  })
}
export function addBaseInfo(data) {
  return request({
    url: '/eamilBaseConfig/addBaseInfo',
    method: 'post',
    data
  })
}
export function updateBaseInfo(data) {
  return request({
    url: '/eamilBaseConfig/updateBaseInfo',
    method: 'post',
    data
  })
}
export function deleteBaseInfo(data) {
  return request({
    url: '/eamilBaseConfig/deleteBaseInfo',
    method: 'post',
    data
  })
}
