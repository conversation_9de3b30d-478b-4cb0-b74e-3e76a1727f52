import request from '@/utils/request'
import qs from 'qs'

export function getReadPermissionPage(data) {
  return request({
    url: '/readPermission/getPage',
    method: 'post',
    data
  })
}
export function getStrategyPage(data) {
  return request({
    url: '/readPermission/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/readPermission/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getStrategyById(data) {
  return request({
    url: '/readPermission/getById',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/readPermission/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/readPermission/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/readPermission/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
