import request from '@/utils/request'

/**
 * 获取离线策略延长日志列表
 * @param {Object} data 查询参数
 */
export function getExtensionLogList(data) {
  return request({
    url: '/offlineStrategyExtendLog/getPage',
    method: 'post',
    data
  })
}

/**
 * 根据配置ID获取操作日志
 * @param {String|Number} configId 配置ID
 */
export function getLogByConfigId(configId) {
  return request({
    url: `/offlineStrategyExtendLog/getByConfigId/${configId}`,
    method: 'get'
  })
}

/**
 * 获取日志详情
 * @param {String|Number} id 日志ID
 */
export function getLogDetail(id) {
  return request({
    url: `/offlineStrategyExtendLog/getDetail/${id}`,
    method: 'get'
  })
}

/**
 * 清理过期日志
 * @param {Number} days 保留天数
 */
export function cleanExpiredLogs(days) {
  return request({
    url: '/offlineStrategyExtendLog/cleanExpired',
    method: 'post',
    data: { days }
  })
}

/**
 * 从云服务同步日志
 */
export function syncLogsFromCloud() {
  return request({
    url: '/offlineStrategyExtendLog/syncFromCloud',
    method: 'post'
  })
}

/**
 * 获取终端同步详情（分页）
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 每页大小
 * @param {String} params.configId - 配置ID
 * @param {Number} params.version - 版本号
 */
export function getTerminalSyncDetails(params) {
  return request({
    url: '/offlineStrategyExtendLog/terminal-sync-details',
    method: 'get',
    params
  })
}

/**
 * 获取离线策略延长日志列表
 * @param {Object} data 查询参数
 */
export function getLogList(data) {
  return request({
    url: '/offlineStrategyExtendLog/getList',
    method: 'post',
    data
  })
}

