import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getProcessTree() {
  return request({
    url: '/approvalFlow/listTree',
    method: 'get'
  })
}

export function getGroupTree() {
  return request({
    url: '/approvalFlow/groupTree',
    method: 'get'
  })
}

export function getList(groupId, query) {
  return request({
    url: '/approvalFlow/getGroupAndFlow/' + groupId,
    method: 'get',
    params: query
  })
}

export function addGroup(data) {
  return request({
    url: '/approvalFlow/addGroup ',
    method: 'post',
    data
  })
}

export function updateGroup(data, id) {
  return request({
    url: '/approvalFlow/updateGroup/' + id,
    method: 'post',
    data
  })
}

export function delGroup(data, id) {
  return request({
    url: '/approvalFlow/delGroup/' + id,
    method: 'post',
    data
  })
}

export function getAllUserByDeptId(parentId) {
  return request({
    url: '/approvalFlow/allUserByDeptId/' + parentId,
    method: 'get'
  })
}

export function getAllUserById(treeId) {
  return request({
    url: '/approvalFlow/listByTreeId/' + treeId,
    method: 'get'
  })
}

export function addProcess(data) {
  return request({
    url: '/approvalFlow/addFlow',
    method: 'post',
    data
  })
}
 
export function delProcess(data, id) {
  return request({
    url: '/approvalFlow/delFlow/' + id,
    method: 'post',
    data
  })
}

export function getProcessById(id) {
  return request({
    url: '/approvalFlow/getFlow/' + id,
    method: 'get'
  })
}

export function updateProcess(data, id) {
  return request({
    url: '/approvalFlow/updateFlow/' + id,
    method: 'post',
    data
  })
}

export function batchDelProcess(data) {
  return request({
    url: '/approvalFlow/delFlows',
    method: 'post',
    data
  })
}

export function getProcessList(query) {
  return request({
    url: '/approvalFlow/getList',
    method: 'get',
    params: query
  })
}

export function batchUpdateGroup(data) {
  return request({
    url: '/approvalFlow/updateFlow/group',
    method: 'post',
    data
  })
}

export function beforeUpdateProcess(data, id) {
  return request({
    url: `/approvalFlow/updateFlow/${id}/suspendRunning`,
    method: 'post',
    data
  })
}

export function beforeDelProcess(id) {
  return request({
    url: `/approvalFlow/deleteFlow/${id}/suspendRunning`,
    method: 'post'
  })
}

export function getDelegateList(query) {
  return request({
    url: '/approvalFlow/listTrust',
    method: 'get',
    params: query
  })
}

export function getAssignedList(query) {
  return request({
    url: '/approvalFlow/getAssignedList',
    method: 'get',
    params: query
  })
}

export function assignProcess(data) {
  return request({
    url: '/approvalFlow/assign',
    method: 'post',
    data
  })
}

export function getAuditTree(userId) {
  return request({
    url: '/approvalFlow/auditTree/' + userId,
    method: 'get'
  })
}

export function addDelegation(data) {
  return request({
    url: '/approvalFlow/trust/add',
    method: 'post',
    data
  })
}

export function isAuditor(query) {
  return request({
    url: '/approvalFlow/isAuditor',
    method: 'get',
    params: query
  })
}

export function updateDelegation(data) {
  return request({
    url: '/approvalFlow/trust/update',
    method: 'post',
    data
  })
}

export function delDelegation(data) {
  return request({
    url: '/approvalFlow/trust/delete',
    method: 'post',
    data
  })
}

export function updateInitiator(data, processDefinitionId) {
  return request({ 
    url: '/approvalFlow/updateFlowAssign/' + processDefinitionId,
    method: 'post',
    data
  })
}

// 获取密级
export function getDenseSet() {
  return request({
    url: '/denseSet/getSet',
    method: 'post'
  })
}

// 获取审批类型列表
export function getCategory() {
  return request({
    url: '/approvalFlow/getCategory',
    method: 'post'
  })
}

// 设置委托范围
export function setupApprovalTrustee(data) {
  return request({
    url: '/approvalFlow/trust/range',
    method: 'post',
    data
  })
}

// 获取委托审批范围
export function getRange(data) {
  return request({
    url: '/approvalFlow/trust/getRange',
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/approvalFlow/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

