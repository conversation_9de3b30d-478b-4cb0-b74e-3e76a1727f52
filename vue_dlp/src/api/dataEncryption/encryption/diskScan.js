import request from '@/utils/request'

export function getScanByName(data) {
  return request({
    url: '/diskScan/getByName',
    method: 'post',
    params: data
  })
}

export function createScan(data) {
  return request({
    url: '/diskScan/add',
    method: 'post',
    data
  })
}

export function updateScan(data) {
  return request({
    url: '/diskScan/update',
    method: 'post',
    data
  })
}

export function startScan(data) {
  return request({
    url: '/diskScan/startScan',
    method: 'post',
    params: data
  })
}

export function stopScan(data) {
  return request({
    url: '/diskScan/stopScan',
    method: 'post',
    params: data
  })
}
export function deleteScan(data) {
  return request({
    url: '/diskScan/delete',
    method: 'post',
    params: data
  })
}

export function getScanPage(data) {
  return request({
    url: '/diskScan/getPage',
    method: 'post',
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/diskScan/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getScanDetailPage(data) {
  return request({
    url: '/log/diskScan/getDetailPage',
    method: 'post',
    data
  })
}

export function deleteLog(data, headers) {
  return request({
    url: '/log/diskScan/deleteDetail',
    method: 'post',
    headers,
    data
  })
}

export function getScanContentStgIdName(stgTypeNum) {
  return request({
    url: '/contentStrategy/listIdName/' + stgTypeNum,
    method: 'get'
  })
}

export function createStg(data) {
  return request({
    url: '/diskScan/addContentStg',
    method: 'post',
    data
  })
}

export function updateStg(data) {
  return request({
    url: '/diskScan/updateContentStg',
    method: 'post',
    data
  })
}

export function deleteScanContentStg(data) {
  return request({
    url: '/diskScan/deleteContentStg',
    method: 'post',
    params: data
  })
}

export function getTaskList(defId) {
  return request({
    url: '/diskScan/taskList?defId=' + defId,
    method: 'get'
  })
}

export function getLogList(data) {
  return request({
    url: '/diskScan/logList',
    method: 'post',
    data
  })
}
