import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getInterfaceAuthInfoList(data) {
  return request({
    url: '/interfaceAuthInfo/getPage',
    method: 'post',
    data
  })
}

export function createInterfaceAuthInfo(data) {
  return request({
    url: '/interfaceAuthInfo/add',
    method: 'post',
    data
  })
}

export function updateInterfaceAuthInfo(data) {
  return request({
    url: '/interfaceAuthInfo/update',
    method: 'post',
    data
  })
}

export function deleteInterfaceAuthInfo(data) {
  return request({
    url: '/interfaceAuthInfo/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getByName(data) {
  return request({
    url: '/interfaceAuthInfo/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function downloadFile(data, opts) {
  return fetchFile({
    ...opts,
    url: '/interfaceAuthInfo/downloadFile',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data
  })
}

export function startDownload(id) {
  return request({
    url: '/interfaceAuthInfo/startDownload/' + id,
    method: 'get'
  })
}
