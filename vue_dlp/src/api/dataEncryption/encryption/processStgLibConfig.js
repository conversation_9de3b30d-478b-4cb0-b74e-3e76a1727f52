import request from '@/utils/request'
import qs from 'qs'

export function getStgTree(data) {
  return request({
    url: '/processStgLib/listStgTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getAppMd5Tree() {
  return request({
    url: '/softwareClass/listFingerprintTree',
    method: 'post'
  })
}

// ### 子进程策略
export function createChildStg(data) {
  return request({
    url: '/processStgConfig/addChild',
    method: 'post',
    data
  })
}
export function updateChildStg(data) {
  return request({
    url: '/processStgConfig/updateChild',
    method: 'post',
    data
  })
}

export function deleteChildStg(data) {
  return request({
    url: '/processStgConfig/deleteChild',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getChildStgByName(data) {
  return request({
    url: '/processStgConfig/getChildByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getChildStgPage(data) {
  return request({
    url: '/processStgConfig/getChildPage',
    method: 'post',
    data
  })
}

// ### 限制上网策略
export function createDisableNet(data) {
  return request({
    url: '/processStgConfig/addNet',
    method: 'post',
    data
  })
}
export function updateDisableNet(data) {
  return request({
    url: '/processStgConfig/updateNet',
    method: 'post',
    data
  })
}

export function deleteDisableNet(data) {
  return request({
    url: '/processStgConfig/deleteNet',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getDisableNetByName(data) {
  return request({
    url: '/processStgConfig/getNetByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getDisableNetPage(data) {
  return request({
    url: '/processStgConfig/getNetPage',
    method: 'post',
    data
  })
}
// ### 高级策略
export function createOtherConfig(data) {
  return request({
    url: '/processStgConfig/addConfig',
    method: 'post',
    data
  })
}
export function updateOtherConfig(data) {
  return request({
    url: '/processStgConfig/updateConfig',
    method: 'post',
    data
  })
}

export function deleteOtherConfig(data) {
  return request({
    url: '/processStgConfig/deleteConfig',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getOtherConfigByName(data) {
  return request({
    url: '/processStgConfig/getConfigByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getOtherConfigPage(data) {
  return request({
    url: '/processStgConfig/getConfigPage',
    method: 'post',
    data
  })
}

export function getConfigByProcessNames(data) {
  return request({
    url: '/processStgConfig/getConfigByProcessNames',
    method: 'post',
    data: qs.stringify(data)
  })
}

// ###进程防伪冒策略
export function getCheckMD5Page(data) {
  return request({
    url: '/processStgConfig/getMd5Page',
    method: 'post',
    data
  })
}
export function createCheckMD5(data) {
  return request({
    url: '/processStgConfig/addMd5',
    method: 'post',
    data
  })
}

export function deleteCheckMD5(data) {
  return request({
    url: '/processStgConfig/deleteMd5',
    method: 'post',
    data: qs.stringify(data)
  })
}
