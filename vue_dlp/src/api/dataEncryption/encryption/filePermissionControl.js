import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/filePermissionControlStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyById(data) {
  return request({
    url: '/filePermissionControlStrategy/getById',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/filePermissionControlStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/filePermissionControlStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/filePermissionControlStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/filePermissionControlStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
