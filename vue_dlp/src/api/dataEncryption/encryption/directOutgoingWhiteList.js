import request from '@/utils/request'
import qs from 'qs'

export function createStrategy(data) {
  return request({
    url: '/directOutgoingWhiteListStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/directOutgoingWhiteListStrategy/update',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: `/directOutgoingWhiteListStrategy/getByName`,
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/directOutgoingWhiteListStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyList(data) {
  return request({
    url: '/directOutgoingWhiteListStrategy/getPage',
    method: 'post',
    data
  })
}
