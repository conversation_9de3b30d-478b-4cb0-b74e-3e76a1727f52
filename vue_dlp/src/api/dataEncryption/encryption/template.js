import request from '@/utils/request'
import qs from 'qs'
export function getTemplateTree() {
  return request({
    url: '/outgoingTemplate/getTree',
    method: 'get'
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/outgoing/template/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/outgoing/template/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/outgoing/template/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/outgoing/template/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/outgoing/template/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
