import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/userDense/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/userDense/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/userDense/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/userDense/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/userDense/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
