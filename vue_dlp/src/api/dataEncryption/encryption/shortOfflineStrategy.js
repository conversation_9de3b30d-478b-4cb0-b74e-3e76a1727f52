import request from '@/utils/request'

export function getshortOfflineStrategyPage(data) {
  return request({
    url: '/shortOfflineStrategy/getPage',
    method: 'post',
    params: data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/shortOfflineStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/shortOfflineStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/shortOfflineStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/shortOfflineStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/shortOfflineStrategy/delete',
    method: 'post',
    params: data
  })
}
