import request from '@/utils/request'
import qs from 'qs'
import store from '@/store'

export function getStrategyPage(data) {
  return request({
    url: '/outgoingProcessStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/outgoingProcessStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/outgoingProcessStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/outgoingProcessStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/outgoingProcessStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listProcess(data) {
  return request({
    url: '/outgoingProcess/listProcess',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listProcessPage(data) {
  return request({
    url: '/outgoingProcess/listProcessPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getGroupTree() {
  return request({
    url: '/outgoingProcess/getGroupTree',
    method: 'post'
  })
}

export function deleteProcess(data) {
  return request({
    url: '/outgoingProcess/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function addProcess(data) {
  return request({
    url: '/outgoingProcess/add',
    method: 'post',
    data
  })
}

export function updateProcess(data) {
  return request({
    url: '/outgoingProcess/update',
    method: 'post',
    data
  })
}

export function importFromLib(data) {
  return request({
    url: '/outgoingProcess/importFromLib',
    method: 'post',
    data
  })
}

export function importFromExe(data) {
  return request({
    url: '/outgoingProcess/importFromExe',
    method: 'post',
    timeout: Math.max(store.getters.requestTimeout, 180000),
    data
  })
}

export function deleteAppType(data) {
  return request({
    url: '/outgoingProcess/deleteGroup',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function addAppType(data) {
  return request({
    url: '/outgoingProcess/addGroup',
    method: 'post',
    data
  })
}
export function updateAppType(data) {
  return request({
    url: '/outgoingProcess/updateGroup',
    method: 'post',
    data
  })
}

export function getAppTypeByName(data) {
  return request({
    url: '/outgoingProcess/getGroupByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countInfoByGroupId(groupId) {
  return request({
    url: '/outgoingProcess/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function importProcess(data, onUploadProgress, token) {
  return request.post('/outgoingProcess/importProcess', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}

//  根据id查询所关联的策略
export function getStrategyByOutgoingProcessIds(data) {
  return request({
    url: '/outgoingProcess/getStrategyByOutgoingProcessIds',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function importProcessReturnList(data, onUploadProgress, token) {
  return request.post('/outgoingProcess/importProcessReturnList', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}

