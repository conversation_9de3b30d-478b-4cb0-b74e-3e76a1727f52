import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/backUpConfig/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/backUpConfig/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/backUpConfig/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/backUpConfig/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/backUpConfig/delete',
    method: 'post',
    params: data
  })
}
