import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function exportOfflineStrategyFile(data, opts) {
  return fetchFile({
    ...opts,
    url: '/offlineStrategy/exportOfflineStrategyFile',
    method: 'post',
    timeout: 0,
    responseType: 'blob',
    data
  })
}

export function uploadStrategyFile(data) {
  return request({
    url: '/offlineStrategy/uploadStrategyFile',
    method: 'post',
    data
  })
}

export function getCloudOfflineStrategyFile(data) {
  return request({
    url: '/offlineStrategy/getCloudOfflineStrategyList',
    method: 'post',
    data
  })
}

export function deleteCloudOfflineStrategyFile(id) {
  return request({
    url: '/offlineStrategy/deleteCloudOfflineStrategy/' + id,
    method: 'get'
  })
}

export function downloadFile(data) {
  return request({
    url: '/offlineStrategy/downloadFile',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data: qs.stringify(data)
  })
}

export function getStartTime() {
  return request({
    url: '/offlineStrategy/getStartTime',
    method: 'get'
  })
}
