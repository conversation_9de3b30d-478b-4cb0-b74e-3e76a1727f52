import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/iconRefreshDir/findPage',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/iconRefreshDir/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/iconRefreshDir/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/iconRefreshDir/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listByName(data) {
  return request({
    url: '/iconRefreshDir/listByName',
    method: 'post',
    data: qs.stringify(data)
  })
}
