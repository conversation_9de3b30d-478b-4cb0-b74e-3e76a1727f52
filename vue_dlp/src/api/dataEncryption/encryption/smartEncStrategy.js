import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/smartEncStrategy/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/smartEncStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/smartEncStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/smartEncStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/smartEncStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/smartEncStrategy/delete',
    method: 'post',
    params: data
  })
}

export function createContentStg(data) {
  return request({
    url: '/smartEncStrategy/addContentStg',
    method: 'post',
    data
  })
}

export function updateContentStg(data) {
  return request({
    url: '/smartEncStrategy/updateContentStg',
    method: 'post',
    data
  })
}

export function deleteContentStg(data) {
  return request({
    url: '/smartEncStrategy/deleteContentStg',
    method: 'post',
    params: data
  })
}
