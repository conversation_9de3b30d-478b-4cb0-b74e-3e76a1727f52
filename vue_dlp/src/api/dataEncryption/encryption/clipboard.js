import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/clipboardStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/clipboardStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/clipboardStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/clipboardStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/clipboardStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getTreeNode(data) {
  return request({
    url: '/processStgLib/listStgTree',
    method: 'post',
    data: qs.stringify(data)
  })
}
