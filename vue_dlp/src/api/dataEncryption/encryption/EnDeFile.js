// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/endeFile/getPage',
    method: 'post',
    timeout: 0,
    data
  })
}

export function getChartData(data) {
  return request({
    url: '/log/endeFile/getChartData',
    method: 'post',
    timeout: 0,
    data
  })
}

export function getCountPage(data) {
  return request({
    url: '/log/endeFile/getCountPage',
    method: 'post',
    timeout: 0,
    data
  })
}

export function exportLog(data) {
  return request({
    url: '/log/endeFile/export',
    method: 'post',
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/endeFile/exportChart',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/endeFile/delete',
    method: 'post',
    data
  })
}
