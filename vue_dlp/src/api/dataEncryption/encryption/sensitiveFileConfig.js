import request from '@/utils/request'

export function getLossType() {
  return request({
    url: '/sensitiveFileLossType/getPage',
    method: 'post'
  })
}

export function updateLossType(data) {
  return request({
    url: '/sensitiveFileLossType/update',
    method: 'post',
    data
  })
}

export function getConfig() {
  return request({
    url: '/sensitiveFileOutsendConfig/get',
    method: 'post'
  })
}

export function updateConfig(data) {
  return request({
    url: '/sensitiveFileOutsendConfig/update',
    method: 'post',
    data
  })
}
