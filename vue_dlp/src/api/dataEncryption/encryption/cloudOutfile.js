import request from '@/utils/request'

export function listCloudOutfile(data) {
  return request({
    url: '/cloudOutfile/listCloudOutfile',
    method: 'post',
    data
  })
}

export function listCloudOutfileRecord(data) {
  return request({
    url: '/cloudOutfile/listCloudOutfileRecord',
    method: 'post',
    data
  })
}

export function listCloudOutfileMachineCode(data) {
  return request({
    url: '/cloudOutfile/listCloudOutfileMachineCode',
    method: 'post',
    data
  })
}

export function getOutFileLogCount(data) {
  return request({
    url: '/cloudOutfile/getOutFileLogCount',
    method: 'post',
    data
  })
}

export function getMachineCode(data) {
  return request({
    url: '/cloudOutfile/getMachineCode?id=' + data,
    method: 'get'
  })
}

export function syncMachineCode(data) {
  return request({
    url: '/cloudOutfile/syncMachineCode?id=' + data,
    method: 'get'
  })
}

export function getOutFile(data) {
  return request({
    url: '/cloudOutfile/getOutFile?uuid=' + data,
    method: 'get'
  })
}

export function syncOutFile(data) {
  return request({
    url: '/cloudOutfile/syncOutFile?uuid=' + data,
    method: 'get'
  })
}

export function getTrustSoftware(data) {
  return request({
    url: '/cloudOutfile/getTrustSoftware?id=' + data,
    method: 'get'
  })
}

export function syncTrustSoftware(data) {
  return request({
    url: '/cloudOutfile/syncTrustSoftware?id=' + data,
    method: 'get'
  })
}

export function updateCloudOutfile(data) {
  return request({
    url: '/cloudOutfile/updateCloudOutfile',
    method: 'post',
    data
  })
}

export function addCloudOutfileMachineCode(data) {
  return request({
    url: '/cloudOutfile/addCloudOutfileMachineCode',
    method: 'post',
    data
  })
}

export function batchAddCloudOutfileMachineCode(data) {
  return request({
    url: '/cloudOutfile/batchAddCloudOutfileMachineCode',
    method: 'post',
    data
  })
}

export function updateCloudOutfileMachineCode(data) {
  return request({
    url: '/cloudOutfile/updateCloudOutfileMachineCode',
    method: 'post',
    data
  })
}
export function deleteCloudOutfileMachineCode(data) {
  return request({
    url: '/cloudOutfile/deleteCloudOutfileMachineCode',
    method: 'post',
    data
    // params: data
  })
}

export function listCloudOutfileTrustSoftware(data) {
  return request({
    url: '/cloudOutfile/listCloudOutfileTrustSoftware',
    method: 'post',
    data
  })
}

export function addCloudOutfileTrustSoftware(data) {
  return request({
    url: '/cloudOutfile/addCloudOutfileTrustSoftware',
    method: 'post',
    data
  })
}

export function batchAddCloudOutfileTrustSoftware(data) {
  return request({
    url: '/cloudOutfile/batchAddCloudOutfileTrustSoftware',
    method: 'post',
    data
  })
}

export function updateCloudOutfileTrustSoftware(data) {
  return request({
    url: '/cloudOutfile/updateCloudOutfileTrustSoftware',
    method: 'post',
    data
  })
}
export function deleteCloudOutfileTrustSoftware(data) {
  return request({
    url: '/cloudOutfile/deleteCloudOutfileTrustSoftware',
    method: 'post',
    data
    // params: data
  })
}

export function listOpenRightOptions() {
  return request({
    url: '/cloudOutfile/listOpenRightOptions',
    method: 'post'
  })
}

