import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getOfflineTermInfo(terminalId) {
  return request({
    url: '/offlineTerm/getOfflineTermInfo?terminalId=' + terminalId,
    method: 'get'
  })
}

export function checkBindUserAndModules(terminalId) {
  return request({
    url: '/offlineTerm/checkBindUserAndModules?terminalId=' + terminalId,
    method: 'get'
  })
}

export function genLicense(data, opts) {
  return fetchFile({
    ...opts,
    url: '/offlineTerm/genLicense',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function exportLicense(data, opts) {
  return fetchFile({
    ...opts,
    url: '/offlineTerm/exportLicense',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getUninstallCode(terminalId) {
  return request({
    url: '/offlineTerm/getUninstallCode?terminalId=' + terminalId,
    method: 'get'
  })
}

export function uninstallOfflineTerminal(data) {
  return request({
    url: '/offlineTerm/uninstall',
    method: 'post',
    data
  })
}

export function downloadExtInfo(data, opts) {
  return fetchFile({
    ...opts,
    url: '/offlineTerm/downloadExtInfo',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function inputNumber(value) {
  if (!value) {
    return value
  }
  const codes = []
  let code
  for (let i = 0; i < value.length; i++) {
    code = value.charCodeAt(i)
    if (code > 47 && code < 58) {
      // 数字0~9
      codes.push(code)
    }
  }
  return String.fromCharCode(...codes)
}
