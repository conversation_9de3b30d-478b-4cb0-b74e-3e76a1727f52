import request from '@/utils/request'

export function listCloudOutfile(data) {
  return request({
    url: '/dlpCloudOutFile/listCloudOutfile',
    method: 'post',
    data
  })
}

export function listCloudOutfileRecord(data) {
  return request({
    url: '/dlpCloudOutFile/listCloudOutfileRecord',
    method: 'post',
    data
  })
}

export function listMachineCode(data) {
  return request({
    url: '/dlpCloudOutFile/listMachineCode',
    method: 'post',
    data
  })
}

export function machineExist(data) {
  return request({
    url: '/dlpCloudOutFile/machineExist',
    method: 'post',
    data
  })
}

export function listTrustSoftware(data) {
  return request({
    url: '/dlpCloudOutFile/listTrustSoftware',
    method: 'post',
    data
  })
}

export function instantlySync() {
  return request({
    url: '/dlpCloudOutFile/instantlySync',
    method: 'post',
    timeout: 600000
  })
}

export function delCloudOutFile() {
  return request({
    url: '/dlpCloudOutFile/delCloudOutFile',
    method: 'post'
  })
}

export function changeSyncTime(time) {
  return request({
    url: '/dlpCloudOutFile/changeSyncTime?time=' + time,
    method: 'get'
  })
}

export function enableSync(open) {
  return request({
    url: '/dlpCloudOutFile/enableSync?open=' + open,
    method: 'get'
  })
}

export function getSyncLogList(data) {
  return request({
    url: '/dlpCloudOutFile/syncLog/getPage',
    method: 'post',
    data
  })
}

export function getOutFileLogList(data) {
  return request({
    url: '/dlpCloudOutFile/outfileLogList',
    method: 'post',
    data
  })
}
