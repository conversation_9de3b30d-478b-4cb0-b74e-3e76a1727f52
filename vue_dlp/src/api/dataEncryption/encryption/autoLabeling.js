import request from '@/utils/request'

export function insertStg(data) {
  return request({
    url: '/autoLabeling/add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/autoLabeling/update',
    method: 'post',
    data
  })
}

export function deleteStg(data) {
  return request({
    url: '/autoLabeling/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/autoLabeling/getPage',
    method: 'post',
    data
  })
}

export function countNum() {
  return request({
    url: '/autoLabeling/countNum',
    method: 'get'
  })
}

export function getScanContentStgIdName(stgTypeNum) {
  return request({
    url: '/contentStrategy/listIdName/' + stgTypeNum,
    method: 'get'
  })
}

export function createContentStg(data) {
  return request({
    url: '/autoLabeling/addContentStg',
    method: 'post',
    data
  })
}

export function updateContentStg(data) {
  return request({
    url: '/autoLabeling/updateContentStg',
    method: 'post',
    data
  })
}

export function deleteContentStg(data) {
  return request({
    url: '/autoLabeling/deleteContentStg',
    method: 'post',
    params: data
  })
}

export function updateConfig(data) {
  return request({
    url: '/autoLabeling/updateConfig',
    method: 'post',
    data
  })
}
