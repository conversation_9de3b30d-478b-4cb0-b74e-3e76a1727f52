import request from '@/utils/request'

export function getAutoBackupFilterStrategyPage(data) {
  return request({
    url: '/autoBackupFilterStrategy/getPage',
    method: 'post',
    params: data
  })
}
export function getAutoBackupFilterStrategyList(data) {
  return request({
    url: '/autoBackupFilterStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function getAutoBackupFilterStrategyByName(data) {
  return request({
    url: '/autoBackupFilterStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createAutoBackupFilterStrategy(data) {
  return request({
    url: '/autoBackupFilterStrategy/add',
    method: 'post',
    data
  })
}
export function updateAutoBackupFilterStrategy(data) {
  return request({
    url: '/autoBackupFilterStrategy/update',
    method: 'post',
    data
  })
}

export function deleteAutoBackupFilterStrategy(data) {
  return request({
    url: '/autoBackupFilterStrategy/delete',
    method: 'post',
    params: data
  })
}
