import request from '@/utils/request'

export function getBackupRepositoryNode(data) {
  return request({
    url: '/backupRepository/getPage',
    method: 'post',
    data
  })
}

export function recoverFile(data) {
  return request({
    url: '/backupRepository/recoverFile',
    method: 'post',
    data
  })
}

export function recoverFileHistory(data) {
  return request({
    url: '/backupRepository/recoverFileHistory',
    method: 'post',
    data
  })
}

export function deleteFile(data) {
  return request({
    url: '/backupRepository/deleteFile',
    method: 'post',
    data
  })
}

export function getRecyclePage(data) {
  return request({
    url: '/backupRepository/getRecyclePage',
    method: 'post',
    data
  })
}

export function getFileHistory(data) {
  return request({
    url: '/backupRepository/getFileHistory',
    method: 'post',
    data
  })
}

export function recoverFileFromRecycle(data) {
  return request({
    url: '/backupRepository/recoverFileFromRecycle',
    method: 'post',
    data
  })
}

export function deleteFileFromRecycle(data) {
  return request({
    url: '/backupRepository/deleteFileFromRecycle',
    method: 'post',
    data
  })
}

export function deleteFileFromHistory(data) {
  return request({
    url: '/backupRepository/deleteFileFromHistory',
    method: 'post',
    data
  })
}

export function getTaskLibPage(data) {
  return request({
    url: '/backupRepository/getTaskLibPage',
    method: 'post',
    data
  })
}

export function getTaskDetail(data) {
  return request({
    url: '/backupRepository/getTaskDetail',
    method: 'post',
    data
  })
}

export function getTaskFileList(data) {
  return request({
    url: '/backupRepository/getTaskFileList',
    method: 'post',
    data
  })
}

export function deleteTask(data) {
  return request({
    url: '/backupRepository/deleteTask',
    method: 'post',
    data
  })
}

export function updateTaskStatus(data) {
  return request({
    url: '/backupRepository/updateTaskStatus',
    method: 'post',
    data
  })
}

export function doBackupClear(data) {
  return request({
    url: '/backupRepository/doBackupClear' + (data ? ('/' + data) : ''),
    method: 'post',
    data: { termId: data }
  })
}

export function pauseTaskByIds(data) {
  return request({
    url: '/backupRepository/pauseTaskByIds',
    method: 'post',
    params: data
  })
}

export function continueTaskByIds(data) {
  return request({
    url: '/backupRepository/continueTaskByIds',
    method: 'post',
    params: data
  })
}

export function deleteTaskByIds(data) {
  return request({
    url: '/backupRepository/deleteTaskByIds',
    method: 'post',
    params: data
  })
}

export function getCurrentTaskMap(data) {
  return request({
    url: '/backupRepository/getCurrentTaskMap',
    method: 'post',
    data
  })
}

export function validateNodeStatus(data) {
  return request({
    url: '/backupRepository/validateNodeStatus',
    method: 'post',
    data
  })
}
