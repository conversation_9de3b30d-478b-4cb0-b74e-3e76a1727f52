import request from '@/utils/request'

export function getTimelyBackupStrategyPage(data) {
  return request({
    url: '/timelyBackupStrategy/getPage',
    method: 'post',
    params: data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/timelyBackupStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/timelyBackupStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/timelyBackupStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/timelyBackupStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/timelyBackupStrategy/delete',
    method: 'post',
    params: data
  })
}

export function firstBackup(termId) {
  return request({
    url: '/timelyBackupStrategy/firstBackup/' + termId,
    method: 'get'
  })
}

export function getGroupDetail(data) {
  return request({
    url: '/timelyBackupStrategy/getGroupDetail',
    method: 'post',
    data
  })
}

export function getGroupTerminalIds(data) {
  return request({
    url: '/timelyBackupStrategy/getGroupTerminalIds',
    method: 'post',
    data
  })
}

export function getTermDetail(data) {
  return request({
    url: '/timelyBackupStrategy/getTermDetail',
    method: 'post',
    data
  })
}

export function getClearCache(termId) {
  return request({
    url: '/timelyBackupStrategy/getClearCache/' + termId,
    method: 'get'
  })
}

export function clearCache(data) {
  return request({
    url: '/timelyBackupStrategy/updateClearCache',
    method: 'post',
    data
  })
}

export function getVerificationCode(data) {
  return request({
    url: '/timelyBackupStrategy/getVerificationCode',
    method: 'post',
    data
  })
}

export function enableTermStatus(termId) {
  return request({
    url: '/timelyBackupStrategy/enableTermStatus/' + termId,
    method: 'get'
  })
}
