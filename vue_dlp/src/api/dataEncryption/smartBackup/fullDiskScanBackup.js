import request from '@/utils/request'

export function getScanByName(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createScan(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/add',
    method: 'post',
    data
  })
}

export function updateScan(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/update',
    method: 'post',
    data
  })
}

export function startScan(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/startScan',
    method: 'post',
    params: data
  })
}

export function stopScan(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/stopScan',
    method: 'post',
    params: data
  })
}

export function pauseScan(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/pauseScan',
    method: 'post',
    params: data
  })
}
export function deleteScan(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/delete',
    method: 'post',
    params: data
  })
}

export function getScanPage(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/fullDiskScanBackupStrategy/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getScanDetailPage(data) {
  return request({
    url: '/log/fullDiskScanBackupStrategy/getDetailPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/fullDiskScanBackupStrategy/deleteDetail',
    method: 'post',
    data
  })
}

export function getTaskList(defId) {
  return request({
    url: '/fullDiskScanBackupStrategy/taskList?defId=' + defId,
    method: 'get'
  })
}

export function getTermStatusList(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/status/termList',
    method: 'post',
    data
  })
}

export function getTaskDetail(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/getTaskDetail',
    method: 'post',
    data
  })
}

export function getBackupTaskStatusPage(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/status/list',
    method: 'post',
    data
  })
}

export function getCurrentTaskMap(data) {
  return request({
    url: '/fullDiskScanBackupStrategy/getCurrentTaskMap',
    method: 'post',
    data
  })
}

