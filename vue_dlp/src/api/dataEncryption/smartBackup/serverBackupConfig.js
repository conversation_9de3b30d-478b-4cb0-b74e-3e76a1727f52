import request from '@/utils/request'

export function getServerBackupConfigStrategyPage(data) {
  return request({
    url: '/serverBackupConfigStrategy/getPage',
    method: 'post',
    params: data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/serverBackupConfigStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/serverBackupConfigStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/serverBackupConfigStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/serverBackupConfigStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/serverBackupConfigStrategy/delete',
    method: 'post',
    params: data
  })
}
