import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/decToolLimit/getPage',
    method: 'post',
    data
  })
}

export function getDecToolLimitByTypeAndValue(data) {
  return request({
    url: '/decToolLimit/getDecToolLimitByTypeAndValue',
    method: 'post',
    data
  })
}

export function insertDecToolLimit(data) {
  return request({
    url: '/decToolLimit/insert',
    method: 'post',
    data
  })
}

export function updateDecToolLimit(data) {
  return request({
    url: '/decToolLimit/update',
    method: 'post',
    data
  })
}

export function deleteDecToolLimit(data) {
  return request({
    url: '/decToolLimit/delete',
    method: 'post',
    data
  })
}
