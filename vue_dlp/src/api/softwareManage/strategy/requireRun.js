import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/softRequireRunStrategy/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/softRequireRunStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/softRequireRunStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/softRequireRunStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/softRequireRunStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/softRequireRunStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyById(data) {
  return request({
    url: '/softRequireRunStrategy/getById/' + data,
    method: 'get'
  })
}
