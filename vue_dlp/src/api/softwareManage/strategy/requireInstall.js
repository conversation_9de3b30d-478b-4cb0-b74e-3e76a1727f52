import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/softRequireInstallStrategy/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/softRequireInstallStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/softRequireInstallStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/softRequireInstallStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/softRequireInstallStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/softRequireInstallStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyById(data) {
  return request({
    url: '/softRequireInstallStrategy/getById/' + data,
    method: 'get'
  })
}
