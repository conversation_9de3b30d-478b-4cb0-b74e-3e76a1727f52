import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/decToolLimitStg/findPage',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/decToolLimitStg/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/decToolLimitStg/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/decToolLimitStg/delete',
    method: 'post',
    params: data
  })
}

export function getDecToolLimitStgByName(data) {
  return request({
    url: '/decToolLimitStg/getByName',
    method: 'post',
    params: data
  })
}
