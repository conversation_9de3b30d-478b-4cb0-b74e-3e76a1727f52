import request from '@/utils/request'
import qs from 'qs'
import store from '@/store'
import { fetchFile } from '@/utils/download/helper'

export function getSoftPageGroupByTerm(data) {
  return request({
    url: '/softwareInfo/getSoftPageGroupByTerm',
    method: 'post',
    data,
    timeout: 0
  })
}
export function getSoftPageGroupByName(data) {
  return request({
    url: '/softwareInfo/getSoftPageGroupByName',
    method: 'post',
    data,
    timeout: 0
  })
}

export function getSoftAndExtAndOrderPageGroupByName(data) {
  return request({
    url: '/softwareInfo/getSoftAndExtAndOrderPageGroupByName',
    method: 'post',
    timeout: 0,
    data
  })
}
export function getSoftAndExtPageGroupByName(data) {
  return request({
    url: '/softwareInfo/getSoftAndExtPageGroupByName',
    method: 'post',
    timeout: 0,
    data
  })
}
export function countSoftGroupByName(data) {
  return request({
    url: '/softwareInfo/countSoftGroupByName',
    method: 'post',
    timeout: 0,
    data
  })
}

export function countSoftAndExtAndOrderPageGroupByName(data) {
  return request({
    url: '/softwareInfo/countSoftAndExtAndOrderPageGroupByName',
    method: 'post',
    timeout: 0,
    data
  })
}
export function listSoftStatisticByName(data) {
  return request({
    url: '/softwareInfo/listSoftStatisticByName',
    method: 'post',
    data
  })
}
export function listSoftStatisticByTerm(data) {
  return request({
    url: '/softwareInfo/listSoftStatisticByTerm',
    method: 'post',
    data
  })
}

export function listSoftStatisticByOriginalName(data) {
  return request({
    url: '/softwareInfo/listSoftStatisticByOriginalName',
    method: 'post',
    data
  })
}
export function listSoftName(data) {
  return request({
    url: '/softwareInfo/listSoftName',
    method: 'post',
    data
  })
}

export function getTermPageBySoftNameVer(data) {
  return request({
    url: '/softwareInfo/getTermPageBySoftNameVer',
    method: 'post',
    data
  })
}

export function countSoftwareInfo(data) {
  return request({
    url: '/softwareInfo/countSoftwareInfo',
    method: 'post',
    timeout: 0,
    data
  })
}
export function getSoftwareInfoPage(data) {
  return request({
    url: '/softwareInfo/getSoftwareInfoPage',
    method: 'post',
    data,
    timeout: 0
  })
}

export function getTerminalSoftwarePage(data) {
  return request({
    url: '/softwareInfo/getTerminalSoftwarePage',
    method: 'post',
    data
  })
}

export function getSoftwarePermitPage(data) {
  return request({
    url: '/softwareInfo/getSoftwarePermitPage',
    method: 'post',
    data,
    timeout: 0
  })
}

export function listVersion(data) {
  return request({
    url: '/softwareInfo/listVersion',
    method: 'post',
    params: data
  })
}

export function getVersionMap(data) {
  return request({
    url: '/softwareInfo/getVersionMap',
    method: 'post',
    params: data
  })
}

export function updateSoftwareInfo(data) {
  return request({
    url: '/softwareInfo/update',
    method: 'post',
    data
  })
}

export function createSoftType(data) {
  return request({
    url: '/softwareInfo/addSoftType',
    method: 'post',
    data
  })
}

export function updateSoftType(data) {
  return request({
    url: '/softwareInfo/updateSoftType',
    method: 'post',
    data
  })
}

export function deleteSoftType(data) {
  return request({
    url: '/softwareInfo/deleteSoftType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSoftTypeByName(data) {
  return request({
    url: '/softwareInfo/getSoftTypeByName',
    method: 'post',
    data
  })
}

export function getSoftTypeTreeNode(data) {
  return request({
    url: '/softwareInfo/getTypeTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createSoftInfo(data) {
  return request({
    url: '/softwareInfo/addSoft',
    method: 'post',
    data
  })
}

export function batchAddSoftInfo(data) {
  return request({
    url: '/softwareInfo/batchAddSoft',
    timeout: Math.max(store.getters.requestTimeout, 180000),
    method: 'post',
    data
  })
}

export function updateSoftInfo(data) {
  return request({
    url: '/softwareInfo/updateSoft',
    method: 'post',
    data
  })
}

export function deleteSoftInfo(data) {
  return request({
    url: '/softwareInfo/deleteSoft',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSoftInfoPage(data) {
  return request({
    url: '/softwareInfo/getSoftPage',
    method: 'post',
    data
  })
}

export function countInfoByGroupId(groupId) {
  return request({
    url: '/softwareInfo/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function importSoftInfoFromLib(data) {
  return request({
    url: '/softwareInfo/addFromLib',
    method: 'post',
    data
  })
}

export function listSoftVerInfo(data) {
  return request({
    url: '/softwareInfo/listSoftVer',
    method: 'post',
    data,
    timeout: 0
  })
}

export function getSoftwareVersionPageWithoutLimit(data) {
  return request({
    url: '/softwareInfo/listSoftwareVersion',
    method: 'post',
    params: data
  })
}

export function listSoftwareTypes(data) {
  return request({
    url: '/softwareInfo/listSoftwareTypes',
    method: 'get'
  })
}

export function listChargeTypes(data) {
  return request({
    url: '/softwareInfo/listChargeTypes',
    method: 'get'
  })
}

export function listIndustryTypes(data) {
  return request({
    url: '/softwareInfo/listIndustryTypes',
    method: 'get'
  })
}

export function listSoftwareGroup(type) {
  return request({
    url: '/softwareGroup/listSoftwareGroup/' + type,
    method: 'get'
  })
}

export function createSoftwareGroup(data) {
  return request({
    url: '/softwareGroup/add',
    method: 'post',
    data
  })
}

export function updateSoftwareGroup(data) {
  return request({
    url: '/softwareGroup/update',
    method: 'post',
    data
  })
}

export function deleteoftwareGroup(data) {
  return request({
    url: '/softwareGroup/delete/' + data,
    method: 'get'
  })
}

export function getSoftwareGroupByName(data) {
  return request({
    url: '/softwareGroup/getByName',
    method: 'post',
    data
  })
}
export function getSoftwareExtInfo(data) {
  return request({
    url: '/softwareExtInfo/getSoftwareExtInfo',
    method: 'post',
    params: data
  })
}

export function exportSoftInfo(data, opts, name) {
  let url = '/softwareInfo/exportSoft'
  if (name) {
    url += '?filename=' + name
  }
  return fetchFile({
    ...opts,
    url,
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function exportSoftwareInfo(data, opts, name) {
  let url = '/softwareInfo/exportSoftwareInfo'
  if (name) {
    url += '?filename=' + name
  }
  return fetchFile({
    ...opts,
    url,
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function exportSoftIndex(data, opts) {
  return fetchFile({
    ...opts,
    url: '/softwareInfo/exportSoftInfo',
    method: 'post',
    responseType: 'blob',
    data
  })
}

/** 导出软件资产授权-软件资产 **/
export function exportSoftwareAssetInfo(data, opts) {
  return fetchFile({
    ...opts,
    url: '/softwareInfo/exportSoftwareAssetInfo',
    method: 'post',
    responseType: 'blob',
    data
  })
}

/** 导出软件资产授权-软件模式 **/
export function exportSoftwareModuleInfo(data, opts) {
  return fetchFile({
    ...opts,
    url: '/softwareInfo/exportSoftwareModuleInfo',
    method: 'post',
    responseType: 'blob',
    data
  })
}

/** 导出软件资产授权-终端模式 **/
export function exportTerminalModuleInfo(data, opts) {
  return fetchFile({
    ...opts,
    url: '/softwareInfo/exportTerminalModuleInfo',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// prop:  installSize 安装数, authorizedSize 授权数, unAuthSize 违规安装数
export function getStatistic(type, prop, row) {
  const value = row ? row[prop] : -1
  const loading = !Number.isInteger(value) || value < 0
  if (type == 'value') {
    return loading ? '' : value
  } else if (type === 'loading') {
    return loading
  }
}

export function saveSoftwareIdentifyConfig(data) {
  return request({
    url: '/softwareIdentify/save',
    method: 'post',
    data
  })
}

export function getSoftwareIdentifyConfig() {
  return request({
    url: '/softwareIdentify/getConfig',
    method: 'get'
  })
}

export function getLastSyncTimestamp() {
  return request({
    url: '/softwareIdentify/getLastSyncTimestamp',
    method: 'get'
  })
}

export function getSoftwareIdentifyStatus() {
  return request({
    url: '/softwareIdentify/getSyncStatus',
    method: 'get'

  })
}

export function saveSoftwareDef(data) {
  return request({
    url: '/softwareDef/saveSoftwareDef',
    method: 'post',
    params: data
  })
}

export function getSoftAssetInfoDetail(id) {
  return request({
    url: '/softwareInfo/getAssetDetail/' + id,
    method: 'get'
  })
}

export function saveCustomSoftAssetInfo(data) {
  return request({
    url: '/customSoftAssetInfo/save',
    method: 'post',
    data
  })
}
