import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/softwareCopyrightStrategy/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/softwareCopyrightStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/softwareCopyrightStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/softwareCopyrightStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/softwareCopyrightStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/softwareCopyrightStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyById(data) {
  return request({
    url: '/softwareCopyrightStrategy/getById/' + data,
    method: 'get'
  })
}
