import request from '@/utils/request'

export function getOrderPage(data) {
  return request({
    url: '/softwareOrder/getPage',
    method: 'post',
    data
  })
}

export function getSoftwareOrderByName(data) {
  return request({
    url: '/softwareOrder/getByName',
    method: 'post',
    params: data
  })
}

export function getSoftwareOrderByOrderId(data) {
  return request({
    url: '/softwareOrder/getByOrderId',
    method: 'post',
    params: data
  })
}

export function createSoftwareOrder(data) {
  return request({
    url: '/softwareOrder/add',
    method: 'post',
    data
  })
}

export function updateSoftwareOrder(data) {
  return request({
    url: '/softwareOrder/update',
    method: 'post',
    data
  })
}

export function deleteSoftwareOrder(data) {
  return request({
    url: '/softwareOrder/delete',
    method: 'post',
    params: data
  })
}

export function authorized(data) {
  return request({
    url: '/softwareOrder/authorized',
    method: 'post',
    data
  })
}

export function recycle(data) {
  return request({
    url: '/softwareOrder/recycle',
    method: 'post',
    data
  })
}

export function getSoftwareOrderPage(data) {
  return request({
    url: '/softwareOrder/getSoftwareOrderPage',
    method: 'post',
    data
  })
}

export function getRestNum(data) {
  return request({
    url: '/softwareOrder/getRestNum',
    method: 'post',
    params: data
  })
}

export function getOrderAndExtByOrderId(data) {
  return request({
    url: '/softwareOrder/getOrderAndExtByOrderId',
    method: 'post',
    params: data
  })
}

export function getAlarmSetup() {
  return request({
    url: '/softwareOrderAlarmSetup/getAlarmSetup',
    method: 'get'
  })
}

export function saveAlarmSetup(data) {
  return request({
    url: '/softwareOrderAlarmSetup/save',
    method: 'post',
    data
  })
}

export function scanTriggerSoftwareOrderAlarm(data) {
  return request({
    url: '/softwareOrderAlarmSetup/scanTriggerAlarm',
    method: 'get'
  })
}

export function getEnableUsingTimeConfig(data) {
  return request({
    url: '/softwareOrder/getConfig',
    method: 'get'
  })
}

export function saveEnableUsingTimeConfig(data) {
  return request({
    url: '/softwareOrder/saveConfig',
    method: 'post',
    params: data
  })
}

export function updateSoftwareOrderActive(data) {
  return request({
    url: '/softwareOrder/updateActive',
    method: 'post',
    data
  })
}
