import request from '@/utils/request'

export function getStrategy(data) {
  return request({
    url: '/patchAutoInstallStrategy/getStrategy',
    method: 'post',
    data
  })
}
export function getStrategyByName(data) {
  return request({
    url: '/patchAutoInstallStrategy/getByName',
    method: 'post',
    params: data
  })
}
export function getPage(data) {
  return request({
    url: '/patchAutoInstallStrategy/getPage',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/patchAutoInstallStrategy/saveStrategy',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/patchAutoInstallStrategy/updateStrategy',
    method: 'post',
    data
  })
}
export function deleteStrategy(data) {
  return request({
    url: '/patchAutoInstallStrategy/delete',
    method: 'post',
    params: data
  })
}
