import request from '@/utils/request'

export function getAutoPatchPage(data) {
  return request({
    url: '/installStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/installStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/installStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/installStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/installStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/installStrategy/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyById(id) {
  return request({
    url: '/installStrategy/get/' + id,
    method: 'get'
  })
}
