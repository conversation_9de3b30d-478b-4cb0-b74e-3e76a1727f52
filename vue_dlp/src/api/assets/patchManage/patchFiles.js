import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper';
import axios from 'axios';

export function getPatchFilesPage(data) {
  return request({
    url: '/patchFile/getPatchPage',
    method: 'post',
    data
  })
}

export function updatePatchDetection(data) {
  return request({
    url: '/patchFile/update',
    method: 'post',
    params: data
  })
}
export function getDownloadCycle(data) {
  return request({
    url: '/patchFile/getDownloadCycle',
    method: 'post',
    params: data
  })
}
export function updateDownloadCycle(data) {
  return request({
    url: '/patchFile/updateDownloadCycle',
    method: 'post',
    params: data
  })
}

export function clickToDownload(data) {
  return request({
    url: '/patchFile/clickToDownload',
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/patchFile/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}
export function checkFile(data) {
  return request({
    url: '/patchFile/checkFile',
    method: 'post',
    data
  })
}

const chunkSize = 64 * 1024 * 1024

export function uploadChunk(file, md5, index, onProgress) {
  if (index >= file.size) {
    if ('function' === typeof onProgress) {
      console.log('onProgress 33333 ')
      onProgress(file.name.split('.')[0], file.size, file.size)
    }
    return Promise.resolve(file.name.split('.')[0])
  }
  let chunkEnd = index + chunkSize
  if (chunkEnd > file.size) {
    chunkEnd = file.size
  }
  const chunk = file.slice(index, chunkEnd)
  const source = axios.CancelToken.source()
  return request({
    url: '/patchFile/uploadChunk',
    cancelToken: source.token,
    method: 'post',
    headers: {
      'Content-Type': 'application/octet-stream',
      'X-File-Size': file.size,
      'X-File-Md5': md5,
      'X-File-Name': file.name,
      'X-Chunk-Index': index
    },
    data: chunk,
    timeout: 0,
    onUploadProgress(progressEvent) {
      if ('function' === typeof onProgress) {
        // console.log('onProgress 11111 ', file.name, index + progressEvent.loaded)
        onProgress(file.name.split('.')[0], index + progressEvent.loaded, file.size, source.cancel)
      }
    }
  }).then(() => {
    // console.log('onProgress 22222 ')
    if ('function' === typeof onProgress && onProgress(file.name.split('.')[0], index + chunk.size, file.size, () => {})) {
      // return Promise.reject(new Error('canceled'))
      return Promise.resolve(file.name.split('.')[0])
    }
    return uploadChunk(file, md5, index + chunk.size, onProgress)
  })
}

export function getPatchFilesByPath(data) {
  return request({
    url: '/patchFile/getPatchFilesByPath',
    method: 'post',
    data
  })
}
export function syncFile(data) {
  return request({
    url: '/patchFile/syncFile',
    method: 'post',
    data
  })
}

export function uploadPatchLog(data) {
  return request({
    url: '/patchFile/uploadPatchLog',
    method: 'post',
    data
  })
}

export function getSyncParam(data) {
  return request({
    url: '/patchFile/getSyncParam',
    method: 'post',
    data
  })
}
