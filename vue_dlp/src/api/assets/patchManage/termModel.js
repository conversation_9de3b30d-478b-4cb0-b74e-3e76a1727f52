import request from '@/utils/request'

export function getTermPatchScanPage(data) {
  return request({
    url: '/patchInfo/getTermPatchScanPage',
    method: 'post',
    data
  })
}

export function getPatchInfoPage(data) {
  return request({
    url: '/patchInfo/getPatchInfoPage',
    method: 'post',
    data
  })
}
export function availableTerm(data) {
  return request({
    url: '/patchInfo/availableTerm',
    method: 'post',
    data
  })
}
export function scanTerm(data) {
  return request({
    url: '/patchInfo/scanTerm',
    method: 'post',
    data
  })
}
export function availableScanTerm(data) {
  return request({
    url: '/patchInfo/availableScanTerm',
    method: 'post',
    data
  })
}
export function installPatch(data) {
  return request({
    url: '/patchInfo/installPatch',
    method: 'post',
    data
  })
}

