import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper';

export function getDetectingFilesPage(data) {
  return request({
    url: '/patchDetectionFile/getPatchDetectionPage',
    method: 'post',
    data
  })
}
export function uploadFile(data) {
  return request.post('/patchDetectionFile/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}
export function cancelUploadFile(data) {
  return request({
    url: '/patchDetectionFile/cancelUpload',
    method: 'post',
    data: `taskId=${data}`
  })
}

export function updatePatchDetection(data) {
  return request({
    url: '/patchDetectionFile/update',
    method: 'post',
    params: data
  })
}

export function clickToDownload(data) {
  return request({
    url: '/patchDetectionFile/clickToDownload',
    method: 'post',
    data
  })
}
export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/patchDetectionFile/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}
