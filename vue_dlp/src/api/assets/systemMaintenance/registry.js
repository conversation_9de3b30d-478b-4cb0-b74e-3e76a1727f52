import request from '@/utils/request'

export function getRegistry(data) {
  return request({
    url: '/terminal/registry/getRegistryInfo',
    method: 'post',
    data: data
  })
}

export function addRegistryItem(data) {
  return request({
    url: '/terminal/registry/addRegistryItem',
    method: 'post',
    data
  })
}

export function deleteRegistryItem(data) {
  return request({
    url: '/terminal/registry/deleteRegistryItem',
    method: 'post',
    data
  })
}

export function updateRegistryItem(data) {
  return request({
    url: '/terminal/registry/updateRegistryItem',
    method: 'post',
    data
  })
}

export function addRegistryKV(data) {
  return request({
    url: '/terminal/registry/addRegistryKV',
    method: 'post',
    data
  })
}

export function updateRegistryKV(data) {
  return request({
    url: '/terminal/registry/updateRegistryKV',
    method: 'post',
    data
  })
}

export function deleteRegistryKV(data) {
  return request({
    url: '/terminal/registry/deleteRegistryKV',
    method: 'post',
    data
  })
}

export function updateRegistryKVName(data) {
  return request({
    url: '/terminal/registry/updateRegistryKVName',
    method: 'post',
    data
  })
}
