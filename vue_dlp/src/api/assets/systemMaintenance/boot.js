import request from '@/utils/request'

export function getSystemBootStart(data, isLog) {
  return request({
    url: '/terminal/boot/' + (isLog ? 'listStart' : 'listStartContinue'),
    method: 'post',
    data: { termId: data }
  })
}

export function updateDisableSystemBootStart(data) {
  return request({
    url: '/terminal/boot/updateDisableStart',
    method: 'post',
    data
  })
}

export function updateEnableSystemBootStart(data) {
  return request({
    url: '/terminal/boot/updateEnableStart',
    method: 'post',
    data
  })
}

export function deleteSystemBootStart(data) {
  return request({
    url: '/terminal/boot/deleteStart',
    method: 'post',
    data
  })
}

export function exportSystemBootStart(data) {
  return request({
    url: '/terminal/boot/exportStart',
    method: 'post',
    data
  })
}

export function getSystemBootService(data, isLog) {
  return request({
    url: '/terminal/boot/' + (isLog ? 'listService' : 'listServiceContinue'),
    method: 'post',
    data: { termId: data }
  })
}

export function updateSystemBootService(data) {
  return request({
    url: '/terminal/boot/updateService',
    method: 'post',
    data
  })
}

export function exportSystemBootService(data) {
  return request({
    url: '/terminal/boot/exportService',
    method: 'post',
    data
  })
}

export function getSystemBootTask(data, isLog) {
  return request({
    url: '/terminal/boot/' + (isLog ? 'listTask' : 'listTaskContinue'),
    method: 'post',
    data: { termId: data }
  })
}

export function updateSystemBootTask(data) {
  return request({
    url: '/terminal/boot/updateTask',
    method: 'post',
    data
  })
}

export function deleteSystemBootTask(data) {
  return request({
    url: '/terminal/boot/deleteTask',
    method: 'post',
    data
  })
}

export function exportSystemBootTask(data) {
  return request({
    url: '/terminal/boot/exportTask',
    method: 'post',
    data
  })
}

