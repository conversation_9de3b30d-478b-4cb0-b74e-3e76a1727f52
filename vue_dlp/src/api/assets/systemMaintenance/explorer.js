import axios from 'axios'
import store from '@/store'
import request, { setHash<PERSON>odeHeader } from '@/utils/request'
import { getToken } from '@/utils/auth'
import { fetchDownload } from '@/utils/download/helper'
import { formatFileType } from '@/icons/extension'
import i18n from '@/lang';

export function checkIntranetIp() {
  return request({
    url: '/explorer/checkIntranetIp',
    method: 'post'
  })
}

export function listChildFile(data) {
  return request({
    url: '/explorer/' + (data.continueFlag && data.continueFlag == 1 ? 'listChildFileContinue' : 'listChildFile'),
    method: 'post',
    data
  })
}

export function getExtractStatus4Log(data) {
  return request({
    url: '/explorer/getExtractStatus4Log',
    method: 'post',
    data
  })
}

export function getExtractStatus(data) {
  return request({
    url: '/explorer/getExtractStatus',
    method: 'post',
    data
  })
}

export function notifyUpload(data) {
  return request({
    url: '/explorer/notifyUpload',
    method: 'post',
    data
  })
}

export function notifyUploaded(termId, guid) {
  return request({
    url: '/explorer/notifyUploaded',
    method: 'post',
    params: { termId, guid }
  })
}

export function pauseDownload(data) {
  return request({
    url: '/explorer/pauseDownload',
    method: 'post',
    data
  })
}

export function getUploadChunks(data) {
  return request({
    url: '/explorer/getUploadChunks',
    method: 'post',
    data
  })
}

export function reportExtractComplete(data) {
  return request({
    url: '/explorer/reportExtractComplete',
    method: 'post',
    data
  })
}

export function generateGuid() {
  return request({
    url: '/explorer/generateGuid',
    method: 'post'
  })
}

export function upload(data) {
  return request.post('/explorer/uploadMultipartFile', data, { 'Content-Type': 'multipart/form-data', timeout: 180000 })
}

export function getTermClipboard(data) {
  return request({
    url: '/explorer/getClipboard',
    method: 'post',
    data
  })
}
export function setTermClipboard(data) {
  return request({
    url: '/explorer/setClipboard',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/explorer/updateConfig',
    method: 'post',
    data
  })
}

export function ieDownload(data, file) {
  const filename = encodeURIComponent(data.filename)
  const { downloadTaskId, size, md5, guid } = data
  const url = `/explorer/ieDownload/${downloadTaskId}/${md5}/${guid}/${filename}`
  fetchDownload({ topic: 'terminalfile', url, file, size })
}

export function download(retry, data, onProgress, reason) {
  if (retry > 1) {
    console.warn('提取终端文件下载失败: ', reason, data)
    return Promise.reject('Extract file chunk retry: ' + retry)
  }
  const url = '/explorer/download'
  const headers = {}
  if (store.getters.token) {
    // 让每个请求携带 token
    // ['X-Token'] 是一个自定义头键
    // 请根据实际情况修改
    headers['Authorization'] = getToken()
    setHashCodeHeader(headers, url)
  }
  const source = axios.CancelToken.source();
  let lastLoaded = 0
  return axios({
    url: process.env.VUE_APP_BASE_API + url,
    method: 'post',
    headers,
    data,
    responseType: 'blob',
    cancelToken: source.token,
    onDownloadProgress(progressEvent) {
      progressEvent.last = lastLoaded
      if (typeof onProgress === 'function') {
        onProgress(progressEvent, source)
      }
      lastLoaded = progressEvent.loaded
    }
  }).then(response => {
    const authorization = response.headers.authorization
    if (authorization && authorization !== store.getters.token) {
      store.dispatch('user/setToken', authorization)
    }
    const contentLength = response.headers['content-length']
    if (contentLength === '0' || response.data.size < data.chunkLength) {
      console.log('retry for not transfer enough bytes')
      return download(retry, data, onProgress, reason)
    }
    return response
  }).catch(reason => {
    if (axios.isCancel(reason)) {
      console.log('Request canceled', reason.message)
      return Promise.reject(reason)
    } else {
      // 处理错误
      return download(retry + 1, data, onProgress, reason)
    }
  })
}

export function fileTypeFormatter(row, data) {
  if (0 === data) {
    // return row.oriData.attributes === 1 ? '本地磁盘' : '可移动磁盘'
    return row.oriData.attributes === 1 ? i18n.t('pages.localDisk') : i18n.t('pages.removableDisk')
  }
  if (1 === data) {
    // '文件夹'
    return i18n.t('pages.folder')
  }
  return formatFileType(row.label)
}

export function fileSizeFormatter(row, data) {
  if (row.oriData.type === 0) { // 磁盘
    return (data / 1024).toFixed(1) + ' GB'
  }
  if (row.oriData.type === 1) { // 文件夹
    return ''
  }
  const sizeArr = (Math.ceil(data / 1024) + '').split('')
  let str = ''
  for (let i = sizeArr.length - 1, j = 1; i >= 0; i--) {
    str = sizeArr[i] + str
    if (i > 0 && (j % 3) === 0) {
      str = ',' + str
    }
    j++
  }
  return str + ' KB'
}
