import request from '@/utils/request'

export function getSystemService(data, isLog) {
  return request({
    url: '/terminal/service/' + (isLog ? 'list' : 'listContinue'),
    method: 'post',
    data: { termId: data }
  })
}

export function updateSystemService(data) {
  return request({
    url: '/terminal/service/update',
    method: 'post',
    data
  })
}

export function exportSystemService(data) {
  return request({
    url: '/terminal/service/export',
    method: 'post',
    data
  })
}

