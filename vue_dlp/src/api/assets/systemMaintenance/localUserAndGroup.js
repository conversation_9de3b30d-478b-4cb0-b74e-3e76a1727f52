import request from '@/utils/request'

export function listLocalGroup(data) {
  return request({
    url: '/terminal/localUserAndGroup/listLocalGroup',
    method: 'post',
    data: { termId: data.termId, groupName: data.groupName }
  })
}

export function exportLocalGroup(data) {
  return request({
    url: '/terminal/localUserAndGroup/exportLocalGroup',
    method: 'post',
    data: { termId: data.termId, groupName: data.groupName }
  })
}

export function listLocalUser(data) {
  return request({
    url: '/terminal/localUserAndGroup/listLocalUser',
    method: 'post',
    data: { termId: data.termId, userName: data.userName }
  })
}

export function updateLocalUser(data) {
  return request({
    url: '/terminal/localUserAndGroup/updateLocalUser',
    method: 'post',
    data: { termId: data.termId, status: data.status, userNameLength: data.userNameLength, userName: data.userName }
  })
}

export function exportLocalUser(data) {
  return request({
    url: '/terminal/localUserAndGroup/exportLocalUser',
    method: 'post',
    data: { termId: data.termId, userName: data.userName }
  })
}
