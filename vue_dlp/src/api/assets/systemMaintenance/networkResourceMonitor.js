import request from '@/utils/request'

export function listTCPConnection(data) {
  return request({
    url: '/terminal/networkResourceMonitor/listTCPConnection',
    method: 'post',
    data: { termId: data.termId, processName: data.processName }
  })
}

export function exportTCPConnection(data) {
  return request({
    url: '/terminal/networkResourceMonitor/exportTCPConnection',
    method: 'post',
    data: { termId: data.termId, processName: data.processName }
  })
}

export function listListenPort(data) {
  return request({
    url: '/terminal/networkResourceMonitor/listListenPort',
    method: 'post',
    data: { termId: data.termId, processName: data.processName }
  })
}

export function exportListenPort(data) {
  return request({
    url: '/terminal/networkResourceMonitor/exportListenPort',
    method: 'post',
    data: { termId: data.termId, processName: data.processName }
  })
}
