import request from '@/utils/request'
import qs from 'qs'
import { buildDownloadFileByName, fetchDownload } from '@/utils/download/helper'
import { aesEncode, formatAesKey } from '@/utils/encrypt'

export function uploadSoft(data, onUploadProgress, token) {
  return request.post('/terminalUpgrade/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}

export function deletePack(data) {
  return request({
    url: '/terminalUpgrade/deletePack',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getPacketList(data) {
  return request({
    url: '/terminalUpgrade/getPackagePage',
    method: 'post',
    data
  })
}

export function getPackageFilterPage(data) {
  return request({
    url: '/terminalUpgrade/getPackageFilterPage',
    method: 'post',
    data
  })
}

export function getMakePkgPage(data) {
  return request({
    url: '/terminalUpgrade/getMakePkgPage',
    method: 'post',
    data
  })
}

export function addTerminalUpgrade(data) {
  return request({
    url: '/terminalUpgrade/addTerminalUpgrade',
    method: 'post',
    timeout: 0,
    data
  })
}

export function updateTerminalUpgrade(data) {
  return request({
    url: '/terminalUpgrade/updateTerminalUpgrade',
    method: 'post',
    timeout: 0,
    data
  })
}

export function deleteTerminalUpgrade(data) {
  return request({
    url: '/terminalUpgrade/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/terminalUpgrade/getStrategyPage',
    method: 'post',
    data
  })
}

export function reUploadFile(data) {
  return request({
    url: '/terminalUpgrade/reUploadFile',
    method: 'post',
    data
  })
}

export function getEffectiveStrategy(data) {
  return request({
    url: '/terminalUpgrade/getEffectiveStrategy',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/terminalUpgrade/getByName',
    method: 'post',
    params: data
  })
}

/**
 * 获取当前日期
 */
export function getNowDate() {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}_${month}_${day}`;
}

export function makeInstallationPackage(data) {
  const lastDotIndex = data.fileName.lastIndexOf('.')
  let name = lastDotIndex < 0 ? data.fileName : data.fileName.slice(0, lastDotIndex)
  const ext = lastDotIndex < 0 ? '' : data.fileName.slice(lastDotIndex)
  if (data.type === 1) {
    name += `（${data.userName && data.userPassword ? '有' : '未'}设置用户和密码）`
  } else if (data.type === 2 && data.forceSetServerAdd === 1) {
    name += '(内置参数)'
  }
  //  文件名称添加版本号和创建时间
  name += (data.version ? '_' + data.version : '') + '_' + getNowDate()
  const tempData = JSON.parse(JSON.stringify(data))
  tempData.userPassword && (tempData.userPassword = aesEncode(tempData.userPassword, formatAesKey('tr838408', tempData.name || '')))
  const file = buildDownloadFileByName(name + ext, false)
  return fetchDownload({
    url: '/terminalUpgrade/makeInstallationPackage',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data: tempData,
    topic: 'TerminalPackage',
    jwt: true,
    file
  })
}

export function getMackVersion(data) {
  return request({
    url: '/terminalUpgrade/getMackVersion',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getTargetObject(data) {
  return request({
    url: '/terminalUpgrade/getTargetObject/' + data,
    method: 'get'
  })
}

export function getValidMsg(data) {
  return request({
    url: '/terminalUpgrade/getValidMsg',
    method: 'post',
    data
  })
}

export function isNewInstallPackage(data) {
  return request({
    url: '/terminalUpgrade/isNewInstallPackage',
    method: 'post',
    params: data
  })
}

export function updateConfig(data) {
  return request({
    url: '/terminalUpgrade/updateConfig',
    method: 'post',
    data
  })
}

export function fetchDistinctVersions() {
  return request({
    url: '/terminalUpgrade/fetchDistinctVersions',
    method: 'get'
  })
}

/**
 * 制作服务器安装包
 * @param data
 * @returns {AxiosPromise}
 */
export function makeServerPkg(data) {
  const lastDotIndex = data.fileName.lastIndexOf('.')
  const name = lastDotIndex < 0 ? data.fileName : data.fileName.slice(0, lastDotIndex)
  const ext = lastDotIndex < 0 ? '' : data.fileName.slice(lastDotIndex)
  const file = buildDownloadFileByName(name + ext, false)
  return fetchDownload({
    url: '/serverInstallPkg/makeInstallPkg',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    topic: 'ServerPackage',
    jwt: true,
    data,
    file
  })
}
