import request from '@/utils/request'

export function getSystemProcess(termId, isLog) {
  return request({
    url: '/terminal/process/' + (isLog ? 'list' : 'listContinue'),
    method: 'post',
    data: { termId: termId }
  })
}

export function updateSystemProcess(data) {
  return request({
    url: '/terminal/process/update',
    method: 'post',
    data
  })
}

export function exportSystemProcess(data) {
  return request({
    url: '/terminal/process/export',
    method: 'post',
    data
  })
}
