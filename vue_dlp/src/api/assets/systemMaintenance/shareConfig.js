import request from '@/utils/request'
import qs from 'qs'

export function getShareConfigPage(data) {
  return request({
    url: '/shareConfig/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/shareConfig/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/shareConfig/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/shareConfig/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/shareConfig/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/shareConfig/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
