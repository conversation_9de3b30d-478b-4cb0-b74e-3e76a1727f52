import request from '@/utils/request'

export function getSystemInfo(termId, isLog) {
  return request({
    url: '/terminal/system/' + (isLog ? 'list' : 'listContinue'),
    method: 'post',
    data: { termId: termId }
  })
}

export function updateComputerName(data) {
  return request({
    url: '/terminal/system/updateComputerName',
    method: 'post',
    data
  })
}

export function getScreenTrack(termId, isLog) {
  return request({
    url: '/terminal/track/' + (isLog ? 'list' : 'listContinue'),
    method: 'post',
    data: { termId: termId }
  })
}

export function getMultiPleTrack(termIds, qualityType, isLog) {
  return request({
    url: '/terminal/track/' + (isLog ? 'multipleTrack' : 'multipleTrackContinue'),
    method: 'post',
    data: { termIds: termIds, qualityType: qualityType }
  })
}
