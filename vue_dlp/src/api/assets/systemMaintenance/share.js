import request from '@/utils/request'

export function getSystemShare(data) {
  return request({
    url: '/terminal/share/listInfo',
    method: 'post',
    data: { termId: data.termId, filterFileName: data.fileName }
  })
}
export function getSystemShareObj(termId, fileName) {
  return request({
    url: '/terminal/share/listObject',
    method: 'post',
    data: { termId: termId, fileName: fileName }
  })
}

export function updateSystemShare(data) {
  return request({
    url: '/terminal/share/update',
    method: 'post',
    data
  })
}

export function exportSystemShare(data) {
  return request({
    url: '/terminal/share/export',
    method: 'post',
    data: { termId: data.termId, filterFileName: data.fileName }
  })
}

export function getSystemShareSession(data) {
  return request({
    url: '/terminal/share/listSessionInfo',
    method: 'post',
    data: { termId: data.termId, filterUserName: data.userName }
  })
}

export function exportSystemShareSession(data) {
  return request({
    url: '/terminal/share/exportSession',
    method: 'post',
    data: { termId: data.termId, filterUserName: data.userName }
  })
}

export function getSystemShareOpenFile(data) {
  return request({
    url: '/terminal/share/listOpenFileInfo',
    method: 'post',
    data: { termId: data.termId, filterFilePath: data.filePath }
  })
}

export function exportSystemShareOpenFile(data) {
  return request({
    url: '/terminal/share/exportOpenFile',
    method: 'post',
    data: { termId: data.termId, filterFilePath: data.filePath }
  })
}

