import request from '@/utils/request'

export function getSystemSoft(data, isLog) {
  return request({
    url: '/terminal/soft/' + (isLog ? 'list' : 'listContinue'),
    method: 'post',
    data: { termId: data }
  })
}

export function updateSystemSoft(data) {
  return request({
    url: '/terminal/soft/update',
    method: 'post',
    data
  })
}

export function exportSystemSoft(data) {
  return request({
    url: '/terminal/soft/export',
    method: 'post',
    data
  })
}

