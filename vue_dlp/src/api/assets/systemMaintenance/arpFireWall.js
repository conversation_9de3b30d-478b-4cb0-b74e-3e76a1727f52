import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/arpFireWallStrategy/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/arpFireWallStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/arpFireWallStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/arpFireWallStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/arpFireWallStrategy/delete',
    method: 'post',
    params: data
  })
}
