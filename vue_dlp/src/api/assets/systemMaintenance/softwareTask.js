import request from '@/utils/request'
import qs from 'qs'

export function getSoftList(data) {
  return request({
    url: '/softWareTask/getSoftList',
    method: 'post',
    data
  })
}

export function getTaskList(data) {
  return request({
    url: '/softWareTask/getList',
    method: 'post',
    data
  })
}

export function insertTask(data) {
  return request({
    url: '/softWareTask/insert',
    method: 'post',
    data
  })
}

export function updateTask(data) {
  return request({
    url: '/softWareTask/update',
    method: 'post',
    data
  })
}

export function deleteTask(data) {
  return request({
    url: '/softWareTask/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getByName(data) {
  return request({
    url: '/softWareTask/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listObjectTree(data) {
  return request({
    url: '/softWareTask/listObjectTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

