import request from '@/utils/request'
import qs from 'qs'

export function uploadSoft(data, onUploadProgress, token) {
  return request.post('/mobileTermUpgrade/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}

export function deletePack(data) {
  return request({
    url: '/mobileTermUpgrade/deletePack',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getPacketList(data) {
  return request({
    url: '/mobileTermUpgrade/getPackagePage',
    method: 'post',
    data
  })
}

export function getPackageFilterPage(data) {
  return request({
    url: '/terminalUpgrade/getPackageFilterPage',
    method: 'post',
    data
  })
}

export function addTerminalUpgrade(data) {
  return request({
    url: '/mobileTermUpgrade/add',
    method: 'post',
    timeout: 0,
    data
  })
}

export function updateTerminalUpgrade(data) {
  return request({
    url: '/mobileTermUpgrade/update',
    method: 'post',
    timeout: 0,
    data
  })
}

export function deleteTerminalUpgrade(data) {
  return request({
    url: '/mobileTermUpgrade/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/mobileTermUpgrade/getStrategyPage',
    method: 'post',
    data
  })
}

export function reUploadFile(data) {
  return request({
    url: '/terminalUpgrade/reUploadFile',
    method: 'post',
    data
  })
}

export function getPage(data) {
  return request({
    url: '/log/mobileTermUpgradeStatus/getPage',
    method: 'post',
    data
  })
}

export function getNewStatusPage(data) {
  return request({
    url: '/log/mobileTermUpgradeStatus/getNewStatusPage',
    method: 'post',
    data
  })
}

export function exportMobileTerminalUpgradeLog(data) {
  return request({
    url: '/log/mobileTermUpgradeStatus/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/mobileTermUpgradeStatus/delete',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/mobileTermUpgrade/getByName',
    method: 'post',
    params: data
  })
}

export function makeInstallationPackage(data) {
  return request({
    url: '/terminalUpgrade/makeInstallationPackage',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data: data
  })
}

export function getMackVersion(data) {
  return request({
    url: '/terminalUpgrade/getMackVersion',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getValidMsg(data) {
  return request({
    url: '/terminalUpgrade/getValidMsg',
    method: 'post',
    data
  })
}

export function isNewInstallPackage(data) {
  return request({
    url: '/terminalUpgrade/isNewInstallPackage',
    method: 'post',
    params: data
  })
}
