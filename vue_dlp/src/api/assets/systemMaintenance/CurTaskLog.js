// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/curTaskInfo/getPage',
    method: 'post',
    data
  })
}

export function exportCurTaskLog(data) {
  return request({
    url: '/log/curTaskInfo/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/curTaskInfo/delete',
    method: 'post',
    data
  })
}
