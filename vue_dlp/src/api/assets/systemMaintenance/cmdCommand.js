import request from '@/utils/request'

export function connectionCmd(termIds, option, webSocketKey) {
  return request({
    url: '/cmdCommand/connectionCmd',
    method: 'post',
    data: { termIds: termIds, option: option, webSocketKey: webSocketKey }
  })
}

export function sendCmdCommand(data) {
  return request({
    url: '/cmdCommand/sendCmdCommand',
    method: 'post',
    data
  })
}

export function heartbeatOrCloseCmd(termIds, option, webSocketKey) {
  return request({
    url: '/cmdCommand/heartbeatOrCloseCmd',
    method: 'post',
    data: { termIds: termIds, option: option, webSocketKey: webSocketKey }
  })
}

export function terminalCloseConnection(termIds, webSocketKey) {
  return request({
    url: '/cmdCommand/terminalCloseConnection',
    method: 'post',
    data: { termIds: termIds, webSocketKey: webSocketKey }
  })
}

export function cmdGrantAuth() {
  return request({
    url: '/cmdCommand/cmdGrantAuth',
    method: 'get'
  })
}

export function stopConnection(termIds, webSocketKey) {
  return request({
    url: '/cmdCommand/stopConnection',
    method: 'post',
    data: { termIds: termIds, webSocketKey: webSocketKey }
  })
}
