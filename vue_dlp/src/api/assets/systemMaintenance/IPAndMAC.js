import request from '@/utils/request'

export function getIpAndMacPage(data) {
  return request({
    url: '/ipAndMacBind/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/ipAndMacBind/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/ipAndMacBind/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/ipAndMacBind/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/ipAndMacBind/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/ipAndMacBind/delete',
    method: 'post',
    params: data
  })
}

export function bindTerminal(data) {
  return request({
    url: '/ipAndMacBind/bind',
    method: 'post',
    data
  })
}
