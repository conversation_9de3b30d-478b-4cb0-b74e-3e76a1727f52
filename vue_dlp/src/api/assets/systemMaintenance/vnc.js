import request from '@/utils/request'

export function getByTermId(id) {
  return request({
    url: '/vnc/getByTermId/' + (!id ? -1 : id),
    method: 'get'
  })
}

export function updateVNC(data) {
  return request({
    url: '/vnc/update',
    method: 'post',
    data
  })
}

export function requestRemoteAssist(data) {
  return request({
    url: '/vnc/requestRemoteAssist/',
    method: 'post',
    data
  })
}
