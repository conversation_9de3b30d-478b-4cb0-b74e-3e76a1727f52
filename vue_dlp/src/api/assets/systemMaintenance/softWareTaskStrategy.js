import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/softWareTaskStrategy/getPage',
    method: 'post',
    data
  })
}

export function saveStrategy(data) {
  return request({
    url: '/softWareTaskStrategy/save',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/softWareTaskStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/softWareTaskStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getColumns(data) {
  return request({
    url: '/assetProp/getColumns',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listObjectTree(objectId, objectType) {
  return request({
    url: '/softWareTaskStrategy/listObjectTree/' + objectId + '/' + objectType,
    method: 'get'
  })
}

