// import { parseTime } from '@/utils'
import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getFromDataTree(data) {
  return request({
    url: '/assetProp/listTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getToDataTree(data) {
  return request({
    url: '/assetPropUser/listTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function saveSetting(data) {
  return request({
    url: '/assetPropUser/add',
    method: 'post',
    data
  })
}

export function getColumns(data) {
  return request({
    url: '/assetPropUser/getColumns',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countAssetInfo(data) {
  return request({
    url: '/assetInfo/countAssetInfo',
    method: 'post',
    timeout: 0,
    data
  })
}
export function getAssetInfo(data) {
  return request({
    url: '/assetInfo/getAssetInfo',
    method: 'post',
    timeout: 0,
    data
  })
}

export function getDetailTree(data) {
  return request({
    url: '/assetProp/getDetailTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getAssetInfoDetail(data) {
  return request({
    url: '/assetInfo/getAssetInfoDetail',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportAssetInfo(data, opts) {
  return fetchFile({
    ...opts,
    url: '/assetInfo/exportAssetInfo',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getAssetPropPage(data) {
  return request({
    url: '/customAssetProp/getPage',
    method: 'post',
    data
  })
}

export function createAssetProp(data) {
  return request({
    url: '/customAssetProp/add',
    method: 'post',
    data
  })
}

export function updateAssetProp(data) {
  return request({
    url: '/customAssetProp/update',
    method: 'post',
    data
  })
}

export function deleteAssetProp(data) {
  return request({
    url: '/customAssetProp/delete',
    method: 'post',
    params: data
  })
}

export function getCustomAssetColumns(data) {
  return request({
    url: '/customAssetProp/getColumns',
    method: 'post',
    data
  })
}

export function createAssetInfo(data) {
  return request({
    url: '/customAssetInfo/add',
    method: 'post',
    data
  })
}

export function updateAssetInfo(data) {
  return request({
    url: '/customAssetInfo/update',
    method: 'post',
    data
  })
}

export function existAssetInfo(data) {
  return request({
    url: '/customAssetInfo/existCustomAssetInfo',
    method: 'post',
    params: data
  })
}

export function batchSaveAssetInfo(data) {
  return request({
    url: '/customAssetInfo/batchSave',
    method: 'post',
    data
  })
}

export function listCustomAssetIno(data) {
  return request({
    url: '/customAssetInfo/list',
    method: 'post',
    data
  })
}

export function listHardAssetInfo(data) {
  return request({
    url: '/assetInfo/listHardAssetInfo',
    method: 'post',
    timeout: 0,
    data
  })
}

export function activeSyncAssetBtn() {
  return request({
    url: '/assetInfo/activeSyncAssetBtn',
    method: 'get'
  })
}

export function notifySyncAssetInfo(data) {
  return request({
    url: '/assetInfo/notifySyncAssetInfo',
    method: 'post',
    data
  })
}
