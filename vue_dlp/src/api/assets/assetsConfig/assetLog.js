import request from '@/utils/request'
import qs from 'qs'

export function getLogPage(data) {
  return request({
    url: '/log/assetLog/getPage',
    method: 'post',
    data
  })
}
export function getHardAlarmPage(data) {
  return request({
    url: '/log/assetLog/getHardAlarmPage',
    method: 'post',
    data
  })
}
export function getHardLogPage(data) {
  return request({
    url: '/log/assetLog/getHardLogPage',
    method: 'post',
    data
  })
}
export function getSoftAlarmPage(data) {
  return request({
    url: '/log/assetLog/getSoftAlarmPage',
    method: 'post',
    data
  })
}
export function getSoftLogPage(data) {
  return request({
    url: '/log/assetLog/getSoftLogPage',
    method: 'post',
    data
  })
}

export function listPropType(data) {
  return request({
    url: '/assetProp/listPropType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/assetLog/delete',
    method: 'post',
    data
  })
}

export function dealHardAlarm(data) {
  return request({
    url: '/log/assetLog/updateHardReadStatus',
    method: 'post',
    data
  })
}
export function dealSoftAlarm(data) {
  return request({
    url: '/log/assetLog/updateSoftReadStatus',
    method: 'post',
    data
  })
}

export function exportHardAlarm(data) {
  return request({
    url: '/log/assetLog/exportHardAlarm',
    method: 'post',
    data
  })
}
export function exportHardLog(data) {
  return request({
    url: '/log/assetLog/exportHardLog',
    method: 'post',
    data
  })
}
export function exportSoftAlarm(data) {
  return request({
    url: '/log/assetLog/exportSoftAlarm',
    method: 'post',
    data
  })
}
export function exportSoftLog(data) {
  return request({
    url: '/log/assetLog/exportSoftLog',
    method: 'post',
    data
  })
}
