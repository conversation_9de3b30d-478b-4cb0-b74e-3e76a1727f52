import request from '@/utils/request'

export function importTransferFile(data) {
  return request.post('/transferFile/upload', data, { 'Content-Type': 'multipart/form-data' })
}

export function getFileDistributeList(data) {
  return request({
    url: '/fileDistribute/getPage',
    method: 'post',
    data
  })
}

export function getStrategyList(data) {
  return request({
    url: '/fileDistribute/getPage',
    method: 'post',
    data
  })
}

export function findList(data) {
  return request({
    url: '/transferFile/findList',
    method: 'post',
    params: data
  })
}

export function deleteFile(data) {
  return request({
    url: '/transferFile/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/fileDistribute/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/fileDistribute/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/fileDistribute/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/fileDistribute/delete',
    method: 'post',
    params: data
  })
}
