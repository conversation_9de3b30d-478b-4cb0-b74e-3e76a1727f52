import request from '@/utils/request'

export function getVideoPage(data) {
  return request({
    url: '/video/getPage',
    method: 'post',
    data
  })
}

export function getStrategyList(data) {
  return request({
    url: '/video/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/video/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/video/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/video/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/video/delete',
    method: 'post',
    params: data
  })
}
