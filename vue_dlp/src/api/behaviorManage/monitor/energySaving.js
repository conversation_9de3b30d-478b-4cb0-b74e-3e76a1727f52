import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/computerEnergySaving/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/computerEnergySaving/getByName',
    method: 'get',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/computerEnergySaving/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/computerEnergySaving/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/computerEnergySaving/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
