import request from '@/utils/request'
import qs from 'qs'

export function getFileFilterPage(data) {
  return request({
    url: '/fileFilterStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/fileFilterStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/fileFilterStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/fileFilterStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/fileFilterStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/fileFilterStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
