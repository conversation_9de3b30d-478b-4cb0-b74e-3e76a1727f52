import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'
import { bytes2ShortLittle, bytes2IntLittle, arrayBufferToBase64 } from '@/utils'
// import qs from 'qs'

export function getLogPage(data) {
  return request({
    url: '/log/videoRecorder/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data, headers) {
  return request({
    url: '/log/videoRecorder/delete',
    method: 'post',
    headers,
    data
  })
}

export function playVideo(data) {
  return request({
    url: '/log/videoRecorder/play',
    method: 'post',
    data
  })
}

export function resendFailedImage(data) {
  return request({
    url: '/log/videoRecorder/resend',
    method: 'post',
    data
  })
}

export function getRelLogVideo(data) {
  return request({
    url: '/log/videoRecorder/relLogVideo',
    method: 'post',
    data
  })
}

export function getMultipleVideoInfo(data) {
  return request({
    url: '/log/videoRecorder/multipleVideoInfo',
    method: 'post',
    data
  })
}

export function stopVideo(data) {
  return request({
    url: '/log/videoRecorder/stopPlay',
    method: 'post',
    params: data
  })
}

export function refleshVideo(data) {
  return request({
    url: '/log/videoRecorder/refleshPlay',
    method: 'post',
    params: data
  })
}

export function getVideoDataInfo(data) {
  return request({
    url: '/log/videoRecorder/getVideoDataInfo',
    method: 'post',
    params: data
  })
}

export function downloadFile(data, opts) {
  return fetchFile({
    ...opts,
    url: '/log/videoRecorder/download',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data
  })
}

export function exportVideoRecord(data) {
  return request({
    url: '/log/videoRecorder/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function transformMP4File(data) {
  return request({
    url: '/log/videoRecorder/transformMP4File',
    method: 'post',
    data
  })
}

export function reTransformMP4File(data) {
  return request({
    url: '/log/videoRecorder/reTransformMP4File',
    method: 'post',
    data
  })
}

export function deleteMP4Task(data) {
  return request({
    url: '/log/videoRecorder/deleteMP4Task',
    method: 'post',
    data
  })
}

export function listTransformRecorder(data) {
  return request({
    url: '/log/videoRecorder/listTransformRecorder',
    method: 'post',
    data
  })
}

export function getMP4TaskPage(data) {
  return request({
    url: '/log/videoRecorder/getMP4TaskPage',
    method: 'post',
    data
  })
}

export function getTaskProgress(data) {
  return request({
    url: '/log/videoRecorder/getTaskProgress/' + data,
    method: 'get'
  })
}

export function hasScreenMonitorModule() {
  return request({
    url: '/log/videoRecorder/allowViewVideo',
    method: 'get'
  })
}

/**
 * 读取屏幕录像数据
 * @param indexObj           索引数据
 * @param convertIndex       当前转换索引
 * @param indexSize          总共有多少所引数
 * @param dataBytes          对应这个索引的录屏数据
 * @returns {{}|null|*[]}
 */
export function readVideoData(indexObj, convertIndex, indexSize, dataBytes) {
  const frameNo = indexObj.frameNo
  const areaNo = indexObj.areaNo
  const baseFrameNo = indexObj.baseFrameNo
  // curIndex前端排序时使用
  const curIndex = convertIndex + 1

  // const offset = indexObj.offset
  // const imageSize = indexObj.imageSize
  // const tempDataBytes = dataBytes.slice(0, imageSize);
  // const unCompressedDataBytes = tempDataBytes;
  // 说明：参数的dataBytes就是对应这个索引的录屏数据
  const unCompressedDataBytes = dataBytes;
  if (unCompressedDataBytes == null || unCompressedDataBytes.length === 0) {
    return null;
  }
  if (areaNo > 0) {
    const dataList = [];
    // 如果是差异区域帧，需要按照结构体里面保存的是多张图片
    let i = 0;
    while (i < unCompressedDataBytes.length) {
      const dataObjChild = {};
      if (curIndex !== 0) {
        dataObjChild['curIndex'] = curIndex
      }
      dataObjChild['index'] = frameNo
      dataObjChild['baseIndex'] = baseFrameNo
      dataObjChild['total'] = indexSize
      // 标识头
      // const tagBytes = unCompressedDataBytes.slice(i, i + 2)
      // const tag = bytes2ShortLittle(tagBytes);
      i += 2;
      // 变化区域号
      const changeIndex = bytes2ShortLittle(unCompressedDataBytes.slice(i, i + 2));
      i += 2;
      // x轴方向分割数量
      const xSize = unCompressedDataBytes[i];
      i += 1;
      // y轴分割数量
      const ySize = unCompressedDataBytes[i];
      i += 1;
      // 数据大小
      const dataSizeBytes = unCompressedDataBytes.slice(i, i + 4)
      const dataSize = bytes2IntLittle(dataSizeBytes);
      if (dataSize < 0) {
        break
      }
      i += 4;
      // 图片数据
      const data = unCompressedDataBytes.slice(i, i + dataSize);
      i += dataSize;
      // 行列中的位置
      dataObjChild['position'] = changeIndex
      // 分为几列
      dataObjChild['col'] = xSize
      // 分为几行
      dataObjChild['row'] = ySize
      dataObjChild['data'] = arrayBufferToBase64(data)
      dataList.push(dataObjChild);
    }
    return dataList;
  } else {
    const dataObj = {};
    if (curIndex !== 0) {
      dataObj['curIndex'] = curIndex
    }
    dataObj['index'] = frameNo
    dataObj['baseIndex'] = baseFrameNo
    dataObj['total'] = indexSize
    // 区域号为0，表示为一张完整图片
    dataObj['position'] = 1
    dataObj['col'] = 1
    dataObj['row'] = 1
    dataObj['data'] = arrayBufferToBase64(unCompressedDataBytes)
    return dataObj;
  }
}
