import request from '@/utils/request'
import qs from 'qs'

export function getLogFilterPage(data) {
  return request({
    url: '/logFilter/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/logFilter/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/logFilter/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/logFilter/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/logFilter/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/logFilter/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
