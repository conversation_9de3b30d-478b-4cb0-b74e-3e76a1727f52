import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getStrategyList(data) {
  return request({
    url: '/strategy/getStrategyTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategy() {
  return request({
    url: '/strategy/getStrategy',
    method: 'get'
  })
}

/**
 * 得到策略分页数据
 * @param data
 */
export function getPage(data) {
  return request({
    url: '/strategy/getPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getEntityNode(data) {
  return request({
    url: '/strategy/getEntityNode',
    method: 'post',
    data
  })
}
export function getRuleStrategyList(data) {
  return request({
    url: '/strategy/getRuleStgPage',
    method: 'post',
    data
  })
}

export function getByStgType(data) {
  return request({
    url: '/strategy/getByStgType',
    method: 'post',
    data
  })
}

export function excelExportData(data, opts) {
  return fetchFile({
    ...opts,
    url: '/strategy/exportData',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getParentName(data) {
  return request({
    url: '/strategy/getParentName',
    method: 'post',
    data
  })
}

export function clearStrategy(data) {
  return request({
    url: '/strategy/clearStrategy',
    method: 'post',
    data
  })
}

export function getSupportTimeStgTypeNumbers() {
  return request({
    url: '/strategy/getSupportTimeStgTypeNumbers',
    method: 'get'
  })
}
