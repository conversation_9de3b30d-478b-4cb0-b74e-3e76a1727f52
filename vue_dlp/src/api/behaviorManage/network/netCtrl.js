import request from '@/utils/request'
import qs from 'qs'
export function getNetCtrlStrategyPage(data) {
  return request({
    url: '/netCtrlStrategy/listPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function fetchList(data) {
  return request({
    url: '/netCtrlStrategy/listPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getByName(data) {
  return request({
    url: '/netCtrlStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/netCtrlStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/netCtrlStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/netCtrlStrategy/delete',
    method: 'post',
    params: data
  })
}
