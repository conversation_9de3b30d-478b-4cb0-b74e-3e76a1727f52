import request from '@/utils/request'

export function getDataPage(data) {
  return request({
    url: '/emailAttachFile/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/emailAttachFile/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/emailAttachFile/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/emailAttachFile/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/emailAttachFile/delete',
    method: 'post',
    params: data
  })
}
