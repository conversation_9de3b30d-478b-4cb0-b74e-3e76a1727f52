import request from '@/utils/request'
export function getWebpageBrowseAuditStrategyPage(data) {
  return request({
    url: '/webpageBrowseAudit/getPage',
    method: 'post',
    data
  })
}

export function getWebpageBrowseAuditByName(data) {
  return request({
    url: '/webpageBrowseAudit/getByName',
    method: 'post',
    params: data
  })
}

export function createWebpageBrowseAudit(data) {
  return request({
    url: '/webpageBrowseAudit/add',
    method: 'post',
    data
  })
}

export function updateWebpageBrowseAudit(data) {
  return request({
    url: '/webpageBrowseAudit/update',
    method: 'post',
    data
  })
}

export function deleteWebpageBrowseAudit(data) {
  return request({
    url: '/webpageBrowseAudit/delete',
    method: 'post',
    params: data
  })
}
