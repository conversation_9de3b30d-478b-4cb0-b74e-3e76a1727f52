import request from '@/utils/request'

export function getWebPostStrategyPage(data) {
  return request({
    url: '/webPostStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyList(data) {
  return request({
    url: '/webPostStrategy/getPage',
    method: 'post',
    data
  })
}

export function fetchDetail(id) {
  return request({
    url: '/webPostStrategy/get/' + id,
    method: 'get'
  })
}

export function getByName(data) {
  return request({
    url: '/webPostStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/webPostStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/webPostStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/webPostStrategy/delete',
    method: 'post',
    params: data
  })
}
