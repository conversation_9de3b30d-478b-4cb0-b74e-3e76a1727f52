import request from '@/utils/request'

export function getStrategyList(data) {
  return request({
    url: '/aiChatStrategy/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/aiChatStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/aiChatStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/aiChatStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/aiChatStrategy/delete',
    method: 'post',
    params: data
  })
}
