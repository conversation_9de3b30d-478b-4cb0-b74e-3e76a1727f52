import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/wifiCollectStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/wifiCollectStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/wifiCollectStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/wifiCollectStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/wifiCollectStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

