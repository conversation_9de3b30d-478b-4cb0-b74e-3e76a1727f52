import request from '@/utils/request'
export function getWebpagePasteAuditStrategyPage(data) {
  return request({
    url: '/webpagePasteAudit/getPage',
    method: 'post',
    data
  })
}

export function getWebpagePasteAuditByName(data) {
  return request({
    url: '/webpagePasteAudit/getByName',
    method: 'post',
    params: data
  })
}

export function createWebpagePasteAudit(data) {
  return request({
    url: '/webpagePasteAudit/add',
    method: 'post',
    data
  })
}

export function updateWebpagePasteAudit(data) {
  return request({
    url: '/webpagePasteAudit/update',
    method: 'post',
    data
  })
}

export function deleteWebpagePasteAudit(data) {
  return request({
    url: '/webpagePasteAudit/delete',
    method: 'post',
    params: data
  })
}
