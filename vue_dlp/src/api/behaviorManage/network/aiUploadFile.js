import request from '@/utils/request'

export function getStrategyList(data) {
  return request({
    url: '/aiFileStrategy/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/aiFileStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/aiFileStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/aiFileStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/aiFileStrategy/delete',
    method: 'post',
    params: data
  })
}
