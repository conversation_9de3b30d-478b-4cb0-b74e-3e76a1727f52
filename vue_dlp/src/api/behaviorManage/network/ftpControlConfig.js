import request from '@/utils/request'

export function createStrategy(data) {
  return request({
    url: '/ftpControlConfig/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/ftpControlConfig/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/ftpControlConfig/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/ftpControlConfig/getPage',
    method: 'post',
    data
  })
}
