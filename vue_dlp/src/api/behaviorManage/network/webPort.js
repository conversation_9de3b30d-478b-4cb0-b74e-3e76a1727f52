import request from '@/utils/request'

export function getWebPortStrategyPage(data) {
  return request({
    url: '/webPortStrategy/getPage',
    method: 'post',
    params: data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/webPortStrategy/getPage',
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/webPortStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/webPortStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/webPortStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/webPortStrategy/delete',
    method: 'post',
    params: data
  })
}
