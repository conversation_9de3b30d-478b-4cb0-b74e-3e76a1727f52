import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/forumPostingRecordLimit/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/forumPostingRecordLimit/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/forumPostingRecordLimit/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/forumPostingRecordLimit/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/forumPostingRecordLimit/delete',
    method: 'post',
    params: data
  })
}
