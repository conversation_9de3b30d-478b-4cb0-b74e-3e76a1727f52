import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/emailRecordLimit/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/emailRecordLimit/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/emailRecordLimit/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/emailRecordLimit/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/emailRecordLimit/delete',
    method: 'post',
    params: data
  })
}
