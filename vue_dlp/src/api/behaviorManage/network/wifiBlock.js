import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/wifiBlock/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/wifiBlock/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/wifiBlock/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/wifiBlock/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/wifiBlock/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

