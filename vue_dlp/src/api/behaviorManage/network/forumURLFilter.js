import request from '@/utils/request'
export function getForumURLFilterStrategyPage(data) {
  return request({
    url: '/forumURLFilter/getPage',
    method: 'post',
    data
  })
}

export function fetchList(data) {
  return request({
    url: '/forumURLFilter/getPage',
    method: 'post',
    data
  })
}

export function getForumURLFilterByName(data) {
  return request({
    url: '/forumURLFilter/getByName',
    method: 'post',
    params: data
  })
}

export function createForumURLFilter(data) {
  return request({
    url: '/forumURLFilter/add',
    method: 'post',
    data
  })
}

export function updateForumURLFilter(data) {
  return request({
    url: '/forumURLFilter/update',
    method: 'post',
    data
  })
}

export function deleteForumURLFilter(data) {
  return request({
    url: '/forumURLFilter/delete',
    method: 'post',
    params: data
  })
}
