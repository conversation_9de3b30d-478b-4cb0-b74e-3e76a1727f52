import request from '@/utils/request'
export function getWebBrowseURLFilterStrategyPage(data) {
  return request({
    url: '/webBrowseURLFilter/getPage',
    method: 'post',
    data
  })
}

export function fetchList(data) {
  return request({
    url: '/webBrowseURLFilter/getPage',
    method: 'post',
    data
  })
}

export function getWebBrowseURLFilterByName(data) {
  return request({
    url: '/webBrowseURLFilter/getByName',
    method: 'post',
    params: data
  })
}

export function createWebBrowseURLFilter(data) {
  return request({
    url: '/webBrowseURLFilter/add',
    method: 'post',
    data
  })
}

export function updateWebBrowseURLFilter(data) {
  return request({
    url: '/webBrowseURLFilter/update',
    method: 'post',
    data
  })
}

export function deleteWebBrowseURLFilter(data) {
  return request({
    url: '/webBrowseURLFilter/delete',
    method: 'post',
    params: data
  })
}
