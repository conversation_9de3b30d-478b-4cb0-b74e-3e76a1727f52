import request from '@/utils/request'

export function getUrlStrategyPage(data) {
  return request({
    url: '/urlStrategy/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/urlStrategy/getPage',
    method: 'post',
    data
  })
}

export function fetchDetail(id) {
  return request({
    url: '/urlStrategy/get/' + id,
    method: 'get'
  })
}

export function getByName(data) {
  return request({
    url: '/urlStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/urlStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/urlStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/urlStrategy/delete',
    method: 'post',
    params: data
  })
}

export function getTreeNode(data) {
  return request({
    url: '/url/listTreeNode',
    method: 'get'
  })
}
