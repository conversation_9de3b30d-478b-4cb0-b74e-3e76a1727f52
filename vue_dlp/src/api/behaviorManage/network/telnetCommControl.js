import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/telnetCommControl/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/telnetCommControl/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/telnetCommControl/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/telnetCommControl/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/telnetCommControl/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

