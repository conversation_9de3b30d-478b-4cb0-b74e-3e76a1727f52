import request from '@/utils/request'

export function getDataPage(data) {
  return request({
    url: '/netDiskStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/netDiskStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/netDiskStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/netDiskStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/netDiskStrategy/delete',
    method: 'post',
    params: data
  })
}
