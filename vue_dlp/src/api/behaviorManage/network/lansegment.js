import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/lanSegment/list',
    method: 'post',
    params: data
  })
}

export function listAll() {
  return request({
    url: '/lanSegment/listAll',
    method: 'post'
  })
}

export function createData(data) {
  return request({
    url: '/lanSegment/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/lanSegment/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/lanSegment/deleteById',
    method: 'post',
    params: data
  })
}
