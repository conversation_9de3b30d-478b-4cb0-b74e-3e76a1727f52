import request from '@/utils/request'

export function getStrategyList(data) {
  return request({
    url: '/aiModelStrategy/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/aiModelStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/aiModelStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/aiModelStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/aiModelStrategy/delete',
    method: 'post',
    params: data
  })
}
