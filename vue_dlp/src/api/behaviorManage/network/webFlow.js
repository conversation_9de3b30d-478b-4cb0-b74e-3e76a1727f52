import request from '@/utils/request'

export function listIPPort(data) {
  return request({
    url: '/flowIPPort/list',
    method: 'post',
    params: data
  })
}

export function createIPPort(data) {
  return request({
    url: '/flowIPPort/add',
    method: 'post',
    data
  })
}

export function updateIPPort(data) {
  return request({
    url: '/flowIPPort/update',
    method: 'post',
    data
  })
}

export function deleteIPPort(data) {
  return request({
    url: '/flowIPPort/deleteById',
    method: 'post',
    params: data
  })
}

export function getIPPortByRemark(data) {
  return request({
    url: '/flowIPPort/getByRemark',
    method: 'post',
    params: data
  })
}

export function listProcess(data) {
  return request({
    url: '/flowProcess/list',
    method: 'post',
    params: data
  })
}

export function createProcess(data) {
  return request({
    url: '/flowProcess/add',
    method: 'post',
    data
  })
}

export function updateProcess(data) {
  return request({
    url: '/flowProcess/update',
    method: 'post',
    data
  })
}

export function deleteProcess(data) {
  return request({
    url: '/flowProcess/deleteById',
    method: 'post',
    params: data
  })
}

export function getProcessByName(data) {
  return request({
    url: '/flowProcess/getByName',
    method: 'post',
    params: data
  })
}

export function getWebFlowStrategyPage(data) {
  return request({
    url: '/webFlowStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyList(data) {
  return request({
    url: '/webFlowStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/webFlowStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/webFlowStrategy/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/webFlowStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/webFlowStrategy/delete',
    method: 'post',
    params: data
  })
}

export function getTermPage(data) {
  return request({
    url: '/webFlowStrategy/getTermPage',
    method: 'post',
    data
  })
}

export function unLimit(data) {
  return request({
    url: '/webFlowStrategy/unLimit',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/webFlowStrategy/updateConfig',
    method: 'post',
    data
  })
}

export function getWebFlowConfig(data) {
  return request({
    url: '/webFlowStrategy/getWebFlowConfig',
    method: 'get',
    data
  })
}
