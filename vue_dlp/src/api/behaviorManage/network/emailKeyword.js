import request from '@/utils/request'
export function getEmailKeywordStrategyPage(data) {
  return request({
    url: '/emailKey<PERSON>ordBlock/getPage',
    method: 'post',
    data
  })
}

export function fetchList(data) {
  return request({
    url: '/emailKeyWordBlock/getPage',
    method: 'post',
    data
  })
}

export function getEmailKeywordByName(data) {
  return request({
    url: '/emailKeyWordBlock/getByName',
    method: 'post',
    params: data
  })
}

export function createEmailKeyword(data) {
  return request({
    url: '/emailKeyWordBlock/add',
    method: 'post',
    data
  })
}

export function updateEmail<PERSON>eyword(data) {
  return request({
    url: '/emailKeyWordBlock/update',
    method: 'post',
    data
  })
}

export function deleteEmailKeyword(data) {
  return request({
    url: '/emailKeyWordBlock/delete',
    method: 'post',
    params: data
  })
}
