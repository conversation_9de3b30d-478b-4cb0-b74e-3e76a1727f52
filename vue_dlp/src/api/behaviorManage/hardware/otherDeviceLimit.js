import request from '@/utils/request'
import qs from 'qs'

export async function getTree() {
  return request({
    url: '/otherDeviceLimit/listTree',
    method: 'get'
  })
}

export function getOtherDeviceLimitPage(data) {
  return request({
    url: '/otherDeviceLimit/findPage',
    method: 'post',
    data
  })
}

export function getStrategyList(data) {
  return request({
    url: '/otherDeviceLimit/findPage',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/otherDeviceLimit/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/otherDeviceLimit/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/otherDeviceLimit/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listByName(data) {
  return request({
    url: '/otherDeviceLimit/listByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getDeviceLibPage(data) {
  return request({
    url: '/otherDeviceLimit/getDeviceLibPage',
    method: 'post',
    data
  })
}

export function addDevice(data) {
  return request({
    url: '/otherDeviceLimit/addDevice',
    method: 'post',
    data
  })
}

export function updateDevice(data) {
  return request({
    url: '/otherDeviceLimit/updateDevice',
    method: 'post',
    data
  })
}

export function deleteDevice(data) {
  return request({
    url: '/otherDeviceLimit/deleteDevice',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getDevByInstanceId(data) {
  return request({
    url: '/otherDeviceLimit/getDevByInstanceId',
    method: 'post',
    data
  })
}
