import request from '@/utils/request'
import qs from 'qs'

export function getBlueToothPage(data) {
  return request({
    url: '/blueToothStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/blueToothStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/blueToothStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/blueToothStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/blueToothStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/blueToothStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
