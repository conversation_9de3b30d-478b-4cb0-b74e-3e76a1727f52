import request from '@/utils/request'
import qs from 'qs'

export function getPringerSetPage(data) {
  return request({
    url: '/printer/findPage',
    method: 'post',
    data
  })
}

export function getStrategyList(data) {
  return request({
    url: '/printer/findPage',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/printer/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/printer/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/printer/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
