import request from '@/utils/request'

export function getTreeNode() {
  return request({
    url: '/pictureLib/listGroupTreeNode',
    method: 'get'
  })
}

export function getPictureLibList(data) {
  return request({
    url: '/pictureLib/getPage',
    method: 'post',
    data
  })
}

export function createPictureLib(data) {
  return request({
    url: '/pictureLib/insert',
    method: 'post',
    params: data
  })
}

export function createPictureLibGroup(data) {
  return request({
    url: '/pictureLib/insertGroup',
    method: 'post',
    params: data
  })
}

export function updatePictureLib(data) {
  return request({
    url: '/pictureLib/update',
    method: 'post',
    params: data
  })
}

export function updatePictureGroup(data) {
  return request({
    url: '/pictureLib/updateGroup',
    method: 'post',
    params: data
  })
}

export function deletePicture(data) {
  return request({
    url: '/pictureLib/delete',
    method: 'post',
    params: data
  })
}

export function deletePictureLibGroup(data) {
  return request({
    url: '/pictureLib/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getUrlGroupByName(data) {
  return request({
    url: '/pictureLib/getGroupByName',
    method: 'post',
    params: data
  })
}

export function countPictureLibByGroupId(groupId) {
  return request({
    url: '/pictureLib/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getPictureLibByName(data) {
  return request({
    url: '/pictureLib/getByName',
    method: 'post',
    params: data
  })
}

/**
 * 得到网址库的内置数据树形结构
 */
// export function getUrlLibTree() {
//   return request({
//     url: '/pictureLib/listUrlLibTree',
//     method: 'get'
//   })
// }

// export function addFromUrlLib(data) {
//   return request({
//     url: '/pictureLib/addFromUrlLib',
//     method: 'post',
//     params: data
//   })
// }

export function moveGroup(data) {
  return request({
    url: '/pictureLib/moveGroup',
    method: 'post',
    data
  })
}

// export function exportExcel(data) {
//   return request({
//     url: '/pictureLib/export',
//     method: 'post',
//     responseType: 'blob',
//     data
//   })
// }

export function uploadFile(data) {
  return request.post('/pictureLib/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function uploadBatchFile(data, onUploadProgress, token) {
  return request.post('/pictureLib/uploadBatchFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}

export function getTransferStatus() {
  return request({
    url: '/pictureLib/getTransferStatus',
    method: 'get'
  })
}
