import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/personalizePolicy/findPage',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/personalizePolicy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/personalizePolicy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/personalizePolicy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listByName(data) {
  return request({
    url: '/personalizePolicy/listByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

