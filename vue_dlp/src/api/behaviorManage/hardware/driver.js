import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper';

export function getDriverPage(data) {
  return request({
    url: '/driver/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/driver/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/driver/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/driver/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/driver/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/driver/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportStrategy(data, opts) {
  return fetchFile({
    ...opts,
    url: `/driver/export`,
    method: 'post',
    data
  })
}
