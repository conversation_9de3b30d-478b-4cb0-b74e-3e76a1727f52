import request from '@/utils/request'

export function getDataPage(data) {
  return request({
    url: '/usbFileConfig/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/usbFileConfig/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/usbFileConfig/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/usbFileConfig/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/usbFileConfig/delete',
    method: 'post',
    params: data
  })
}
