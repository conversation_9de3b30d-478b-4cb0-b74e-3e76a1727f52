import request from '@/utils/request'
import qs from 'qs'

// 拼接url
function appendUrl(baseUrl, data) {
  const pathMap = { 2: 'Print', 44: 'Screen', 151: 'Office' }
  let stgCode
  if (Array.isArray(data) && data.length > 0) {
    stgCode = data[0].stgTypeNumber
  } else {
    stgCode = data.stgTypeNumber
  }
  return baseUrl + pathMap[stgCode]
}

export function getWaterMarkPage(data) {
  return request({
    url: '/waterMark/getPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyList(data) {
  return request({
    url: '/waterMark/getPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: appendUrl('/waterMark/save', data),
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: appendUrl('/waterMark/update', data),
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: appendUrl('/waterMark/delete', data),
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/waterMark/getByName',
    method: 'post',
    params: data
  })
}

export function findPrintAppList(data) {
  return request({
    url: '/waterMark/findPrintAppList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getBatchPrintAppList(data) {
  return request({
    url: '/waterMark/getBatchPrintAppList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getPrintAppByName(data) {
  return request({
    url: '/waterMark/getPrintAppByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getPrintAppByNames(appType, processNames) {
  return request({
    url: '/waterMark/getPrintAppByNames',
    method: 'post',
    data: qs.stringify({ appType, processNames })
  })
}

export function savePrintApp(data) {
  return request({
    url: '/waterMark/savePrintApp',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function savePrintAppBatch(data) {
  return request({
    url: '/waterMark/savePrintAppBatch',
    method: 'post',
    data
  })
}

export function editPrintApp(data) {
  return request({
    url: '/waterMark/editPrintApp',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function removePrintAppBatch(data) {
  return request({
    url: '/waterMark/removePrintAppBatch',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getFontOptions(data) {
  return request({
    url: '/waterMark/getFontOptions',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getWatermarkUrlPage(data) {
  return request({
    url: '/waterMark/getWatermarkUrlPage',
    method: 'post',
    data
  })
}

export function saveWatermarkUrl(data) {
  return request({
    url: '/waterMark/screenWatermarkUrl',
    method: 'post',
    data
  })
}

export function updateWatermarkUrl(data) {
  return request({
    url: '/waterMark/updateScreenWatermarkUrl',
    method: 'post',
    data
  })
}

export function deleteWatermarkUrl(data) {
  return request({
    url: '/waterMark/deleteScreenWatermarkUrl',
    method: 'post',
    params: data
  })
}

export function getWatermarkUrlByIds(data) {
  return request({
    url: '/waterMark/getWatermarkUrlList',
    method: 'post',
    data
  })
}

export function getWatermarkUrlByName(data) {
  return request({
    url: '/waterMark/getWatermarkUrlByName',
    method: 'post',
    params: data
  })
}

export function fromLibToWatermarkUrl(data) {
  return request({
    url: '/waterMark/fromLibToWatermarkUrl',
    method: 'post',
    data
  })
}

export function getAdvanceOpts() {
  return request({
    url: '/waterMark/officeAdvanceOpts',
    method: 'get'
  })
}

export function saveOfficeAdvanceOpts(data) {
  return request({
    url: '/waterMark/saveOfficeAdvanceOpts',
    method: 'post',
    data
  })
}
