import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/netInterfaceLimit/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/netInterfaceLimit/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/netInterfaceLimit/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/netInterfaceLimit/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/netInterfaceLimit/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
