import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getUsbConfigPage(data) {
  return request({
    url: '/usbConfig/findUsbConfigList',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getStrategyList(data) {
  return request({
    url: '/usbConfig/findUsbConfigList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/usbConfig/addUsbConfig',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/usbConfig/updateUsbConfig',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/usbConfig/deleteUsbConfig',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function findUsbDeviceList(data) {
  return request({
    url: '/usbConfig/listUsbDevice',
    method: 'post',
    data
  })
}

export function addUsbDevice(data) {
  return request({
    url: '/usbConfig/addUsbDevice',
    method: 'post',
    data
  })
}

export function batchAddUsbDevice(data) {
  return request({
    url: '/usbConfig/batchAddUsbDevice',
    method: 'post',
    data
  })
}

export function updateUsbDevice(data) {
  return request({
    url: '/usbConfig/updateUsbDevice',
    method: 'post',
    data
  })
}

export function deleteUsbDevice(data) {
  return request({
    url: '/usbConfig/deleteUsbDevice',
    method: 'post',
    data
  })
}

export function addUsbGroup(data) {
  return request({
    url: '/usbConfig/addUsbGroup',
    method: 'post',
    data
  })
}

export function updateUsbGroup(data) {
  return request({
    url: '/usbConfig/updateUsbGroup',
    method: 'post',
    data
  })
}

export function moveUsbGroup(data) {
  return request({
    url: '/usbConfig/moveGroup',
    method: 'post',
    data
  })
}

export function deleteUsbGroup(data) {
  return request({
    url: '/usbConfig/deleteUsbGroup',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function findUsbGroupTree(data) {
  return request({
    url: '/usbConfig/findUsbGroupTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function findAllUsbGroup(data) {
  return request({
    url: '/usbConfig/findAllUsbGroup',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listUsbDeviceByGroupIds(data) {
  return request({
    url: '/usbConfig/listUsbDeviceByGroupIds',
    method: 'post',
    data
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/usbConfig/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getGroupByName(data) {
  return request({
    url: '/usbConfig/getGroupByName',
    method: 'post',
    data
  })
}

export function findUsbDeviceTree() {
  return request({
    url: '/usbConfig/listUsbDeviceTree',
    method: 'post'
  })
}

export function getApprovalList(data) {
  return request({
    url: '/approvalUsbauthApply/getPage',
    method: 'post',
    data
  })
}
export function getApprovalCount() {
  return request({
    url: '/approvalUsbauthApply/getCount',
    method: 'post'
  })
}

export function saveApproval(data) {
  return request({
    url: '/approvalUsbauthApply/approval',
    method: 'post',
    data
  })
}

export function validUsbCode(data) {
  return request({
    url: '/approvalUsbauthApply/validUsbCode',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listAllUsbDevice() {
  return request({
    url: '/usbConfig/listAllUsbDevice',
    method: 'get'
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/usbConfig/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/usbConfig/moveGroupToOther',
    method: 'post',
    data
  })
}

export function importExcel(data) {
  return request({
    url: '/usbConfig/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data,
    timeout: 0
  })
}

/** 导出USB设备 **/
export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/usbConfig/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function listDeviceByCode(data) {
  return request({
    url: '/usbConfig/listDeviceByCode',
    method: 'post',
    data
  })
}

export function listDeviceById(data) {
  return request({
    url: '/usbConfig/listDeviceById',
    method: 'post',
    data
  })
}
