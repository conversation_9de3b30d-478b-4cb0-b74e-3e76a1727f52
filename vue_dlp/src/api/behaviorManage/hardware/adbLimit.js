import request from '@/utils/request'
import qs from 'qs'

export function getDriverPage(data) {
  return request({
    url: '/adbLimit/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/adbLimit/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/adbLimit/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/adbLimit/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/adbLimit/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/adbLimit/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getTreeNode() {
  return request({
    url: '/adbLimit/getTree',
    method: 'get'
  })
}

export function listStgIdBySuffixIds(data) {
  return request({
    url: '/adbLimit/listStgIdBySuffixIds',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteFileSuffix(data) {
  return request({
    url: '/adbLimit/deleteFileSuffix',
    method: 'post',
    data: qs.stringify(data)
  })
}
