import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/sensitiveOp/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/sensitiveOp/delete',
    method: 'post',
    data
  })
}
// 截屏
export function getLogDetailPage(data) {
  return request({
    url: '/log/sensitiveOp/getDetailPage',
    method: 'post',
    params: data
  })
}
// 录屏
export function getProcessRecscreenLog(data) {
  return request({
    url: '/log/sensitiveOp/getProcessRecscreenLog',
    method: 'post',
    params: data
  })
}

export function deleteLogDetail(data) {
  return request({
    url: '/log/sensitiveOp/deleteDetail',
    method: 'post',
    params: data
  })
}

export function getImages() {
  return request({
    url: '/log/sensitiveOp/downloadtest',
    method: 'get'
  })
}

export function downloadFile(data) {
  return request({
    url: '/log/sensitiveOp/download',
    method: 'post',
    timeout: 0,
    data
  })
}

export function exportSensitiveOpLog(data) {
  return request({
    url: '/log/sensitiveOp/export',
    method: 'post',
    data
  })
}
