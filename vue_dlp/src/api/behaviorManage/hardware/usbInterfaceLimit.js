import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/usbInterfaceLimit/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/usbInterfaceLimit/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/usbInterfaceLimit/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/usbInterfaceLimit/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/usbInterfaceLimit/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
