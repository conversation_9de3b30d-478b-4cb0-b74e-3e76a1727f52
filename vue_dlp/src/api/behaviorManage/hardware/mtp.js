import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/mtpStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(name) {
  return request({
    url: '/mtpStrategy/getByName',
    method: 'post',
    data: 'name=' + encodeURIComponent(name)
  })
}

export function createStrategy(data) {
  return request({
    url: '/mtpStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/mtpStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/mtpStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}
