import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/processMonitor/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/processMonitor/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/processMonitor/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/processMonitor/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/processMonitor/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/processMonitor/delete',
    method: 'post',
    params: data
  })
}
