import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/stgGroup/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/stgGroup/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/stgGroup/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/stgGroup/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/stgGroup/delete',
    method: 'post',
    params: data
  })
}

export function listObjectTree(id, objectType) {
  return request({
    url: '/stgGroup/listObjectTree/' + id + '/' + objectType,
    method: 'get'
  })
}

export function listAll() {
  return request({
    url: '/stgGroup/list',
    method: 'post'
  })
}

export function listStgByGroupType(groupType) {
  return request({
    url: '/stgGroup/listSimpleStgDef/' + groupType,
    method: 'get'
  })
}

export function getGroupByGroupType(groupType) {
  return request({
    url: '/stgGroup/getGroupByGroupType/' + groupType,
    method: 'get'
  })
}

export function getStgIdCodeMap(data) {
  return request({
    url: '/strategy/getStgIdCodeMap',
    method: 'post',
    data: qs.stringify(data)
  })
}
