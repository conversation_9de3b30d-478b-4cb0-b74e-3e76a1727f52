import request from '@/utils/request'

export function getBurnConfigPage(data) {
  return request({
    url: '/burnConfig/getPage',
    method: 'post',
    data
  })
}

export function getStrategyList(data) {
  return request({
    url: '/burnConfig/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/burnConfig/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/burnConfig/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/burnConfig/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/burnConfig/delete',
    method: 'post',
    params: data
  })
}
