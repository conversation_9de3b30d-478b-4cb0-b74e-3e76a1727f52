import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/mstsc/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/mstsc/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/mstsc/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/mstsc/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/mstsc/delete',
    method: 'post',
    params: data
  })
}

