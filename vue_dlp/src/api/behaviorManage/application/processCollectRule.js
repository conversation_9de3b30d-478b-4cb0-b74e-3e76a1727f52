import request from '@/utils/request'
import qs from 'qs'

export function getDataPage(data) {
  return request({
    url: '/processCollectRuleStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/processCollectRuleStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/processCollectRuleStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/processCollectRuleStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/processCollectRuleStrategy/delete',
    method: 'post',
    params: data
  })
}

export function getRuleList(data) {
  return request({
    url: '/processCollectRule/list',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listByStgId(stgId) {
  return request({
    url: '/processCollectRule/listByStgId',
    method: 'post',
    data: qs.stringify(stgId)
  })
}

export function createRule(data) {
  return request({
    url: '/processCollectRule/add',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createBatchRule(data) {
  return request({
    url: '/processCollectRule/batchAdd',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateRule(data) {
  return request({
    url: '/processCollectRule/update',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteRule(data) {
  return request({
    url: '/processCollectRule/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function clearRuleList(data) {
  return request({
    url: '/processCollectRule/deleteAll',
    method: 'post',
    data: qs.stringify(data)
  })
}
