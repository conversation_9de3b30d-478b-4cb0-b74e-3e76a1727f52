// import { parseTime } from '@/utils'
import request from '@/utils/request'
import qs from 'qs'

export function getLogPage(data) {
  return request({
    url: '/log/telnetMonitor/getPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/telnetMonitor/delete',
    method: 'post',
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/telnetMonitor/export',
    method: 'post',
    data
  })
}
