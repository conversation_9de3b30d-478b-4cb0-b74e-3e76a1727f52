import request from '@/utils/request'
import qs from 'qs'

export function getTreeNode() {
  return request({
    url: '/fileSuffix/findFileSuffixClassTree',
    method: 'post'
  })
}

export function createData(data) {
  return request({
    url: '/fileSuffix/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/fileSuffix/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/fileSuffix/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/fileSuffix/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getGroupByName(data) {
  return request({
    url: '/fileSuffix/getGroupByName',
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/fileSuffix/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function updateSuffixGroup(data) {
  return request({
    url: '/fileSuffix/saveClassIdForFileSuffixInfo',
    method: 'post',
    data
  })
}

export function getFileSuffixPage(data) {
  return request({
    url: '/fileSuffix/getFileSuffixPage',
    method: 'post',
    data
  })
}

export function addFileSuffix(data) {
  return request({
    url: '/fileSuffix/addFileSuffixInfo',
    method: 'post',
    data
  })
}

export function deleteFileSuffix(data) {
  return request({
    url: '/fileSuffix/deleteFileSuffixInfo',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/fileSuffix/moveGroupToOther',
    method: 'post',
    data
  })
}
