import request from '@/utils/request'
import qs from 'qs'
import axios from 'axios'

export function createData(data) {
  return request({
    url: '/softwareClass/add',
    method: 'post',
    data
  })
}

export function createAppGroup(data) {
  return createData(data)
}

export function updateData(data) {
  return request({
    url: '/softwareClass/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/softwareClass/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSoftwarePage(data) {
  return request({
    url: '/softwareClass/getSoftwarePage',
    method: 'post',
    data
  })
}

export function listSoftwareInfo(data) {
  return request({
    url: '/softwareClass/getSoftwareInfotable',
    method: 'post',
    data
  })
}

export function updateSoft(data) {
  return request({
    url: '/softwareClass/saveClassIdForSoftwareInfo',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/softwareClass/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/softwareClass/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function upload(data, onUploadProgress, token) {
  return request.post('/softwareClass/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}

/**
 * 循环上传文件
 * @param files 要上传的文件
 * @param vm Vue实例
 * @param tableRef 表格引用名称（默认 fileList），为 false 时表示不滚动
 * @returns {Promise<void>}
 */
export async function loopUploadFiles(files, vm, tableRef) {
  vm.fileSubmitting = true
  vm.percentage = 0
  vm.source = vm.connectionSource()
  let progress = vm.percentage
  const weight = 100 / files.length
  for (let i = 0; i < files.length; i++) {
    try {
      const percent = progress
      const formData = new FormData()
      formData.append('uploadFile', files[i])// 传文件
      const res = await upload(formData, progressEvent => {
        progress = percent + (progressEvent.loaded / progressEvent.total * weight)
        vm.percentage = Math.floor(progress)
      }, vm.source.token)
      const appended = vm.appendFile(res.data)
      if (appended instanceof Promise) {
        await appended
      } else if (tableRef !== false) {
        await new Promise(resolve => setTimeout(resolve, 5))
      }
      scrollToTableBottom(vm, tableRef)
    } catch (e) {
      if (axios.isCancel(e)) {
        // 取消上传后的操作，待补充
      }
      break
    }
  }
  vm.resetUploadComponent()
}

/**
 * 处理上传文件变化
 * @param files 要上传的文件
 * @param vm Vue实例
 * @param tableRef 表格引用名称（默认 fileList），为 false 时表示不滚动
 * @returns {Promise<void>}
 */
export async function changeFiles(files, vm, tableRef) {
  if (files.length > 0) {
    await loopUploadFiles(files, vm, tableRef)
  } else {
    vm.$message({
      title: vm.$t('text.prompt'),
      message: vm.$t('pages.appGroup_text8'),
      type: 'error',
      duration: 2000
    })
  }
  vm.$refs.uploadDir.clearValue()
  scrollToTableBottom(vm, tableRef)
}

/**
 * 表格纵向滚动条滚动到表格底部
 * @param vm Vue实例
 * @param tableRef 表格引用名称（默认 fileList），为 false 时表示不滚动
 */
export function scrollToTableBottom(vm, tableRef) {
  if (tableRef === false) {
    return
  }
  vm.$nextTick(() => {
    const bodyWrapper = vm.$refs[tableRef || 'fileList'].$el.querySelector('.el-table__body-wrapper')
    bodyWrapper.scrollTop = bodyWrapper.scrollHeight
  })
}

export function addSoft(data) {
  return request({
    url: '/softwareClass/addSoftwareInfo',
    method: 'post',
    data
  })
}

export function deleteSoft(data) {
  return request({
    url: '/softwareClass/deleteSoftwareInfo',
    method: 'post',
    data
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/softwareClass/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getGroupByName(data) {
  return request({
    url: '/softwareClass/getGroupByName',
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/softwareClass/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/softwareClass/moveGroupToOther',
    method: 'post',
    data
  })
}

export function listSoftGroup() {
  return request({
    url: '/softwareClass/listSoftGroup',
    method: 'post'
  })
}
