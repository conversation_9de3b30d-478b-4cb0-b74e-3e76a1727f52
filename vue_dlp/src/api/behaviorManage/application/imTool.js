import request from '@/utils/request'
import qs from 'qs'
export function getImToolPage(data) {
  return request({
    url: '/imToolStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataPage(data) {
  return request({
    url: '/imToolStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/imToolStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function getDataById(id) {
  return request({
    url: '/imToolStrategy/get/' + id,
    method: 'get'
  })
}

export function createData(data) {
  return request({
    url: '/imToolStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/imToolStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/imToolStrategy/delete',
    method: 'post',
    params: data
  })
}

export function getWhiteList(data) {
  return request({
    url: '/imToolStrategy/listWhiteList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getWhitListPage(data) {
  return request({
    url: '/imToolStrategy/listWhiteListPage',
    method: 'post',
    data
  })
}

export function getWhiteListInfo(data) {
  return request({
    url: '/imToolStrategy/getWhiteList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createWhite(data) {
  return request({
    url: '/imToolStrategy/addWhiteList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateWhite(data) {
  return request({
    url: '/imToolStrategy/updateWhiteList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteWhite(data) {
  return request({
    url: '/imToolStrategy/deleteWhiteList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function clearWhiteList(data) {
  return request({
    url: '/imToolStrategy/clearWhiteList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createWhiteBatch(data) {
  return request({
    url: '/imToolStrategy/addWhiteListBatch',
    method: 'post',
    data
  })
}

export function createChatGroup(data) {
  return request({
    url: '/imToolStrategy/addType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteChatGroup(data) {
  return request({
    url: '/imToolStrategy/deleteType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateChatGroup(data) {
  return request({
    url: '/imToolStrategy/updateType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getTypeByName(data) {
  return request({
    url: '/imToolStrategy/getTypeByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getTypeTree(data) {
  return request({
    url: '/imToolStrategy/getTypeTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getChatsByType(data) {
  return request({
    url: '/imToolStrategy/getChatsByType',
    method: 'post',
    data
  })
}

export function countByGroupId(data) {
  return request({
    url: '/imToolStrategy/countByGroupId/' + data,
    method: 'get'
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/imToolStrategy/deleteGroupAndData/',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/imToolStrategy/moveGroupToOther',
    method: 'post',
    data
  })
}

export function getTerminalImtool(data) {
  return request({
    url: '/imToolStrategy/getTerminalImtool',
    method: 'post',
    data
  })
}

export function keepDataCache(data) {
  return request({
    url: '/imToolStrategy/keepDataCache',
    method: 'get',
    params: data
  })
}

export function dropDataCache(data) {
  return request({
    url: '/imToolStrategy/dropDataCache',
    method: 'get',
    params: data
  })
}

export function validImportDatas(data) {
  return request({
    url: '/imToolStrategy/validImportDatas',
    method: 'post',
    data
  })
}

export function importTerminalImtool(data) {
  return request({
    url: '/imToolStrategy/importTerminalImtool',
    method: 'post',
    data
  })
}

