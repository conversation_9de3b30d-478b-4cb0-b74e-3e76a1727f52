import request from '@/utils/request'

export function getImFilePage(data) {
  return request({
    url: '/imFileStrategy/getPage',
    method: 'post',
    data
  })
}
export function getDataPage(data) {
  return request({
    url: '/imFileStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/imFileStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/imFileStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/imFileStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/imFileStrategy/delete',
    method: 'post',
    params: data
  })
}
