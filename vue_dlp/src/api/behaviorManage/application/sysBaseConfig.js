import request from '@/utils/request'

export function getDataPage(data) {
  return request({
    url: '/sysBaseConfigStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/sysBaseConfigStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/sysBaseConfigStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/sysBaseConfigStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/sysBaseConfigStrategy/delete',
    method: 'post',
    params: data
  })
}

export function loadFuncOptions(data) {
  return request({
    url: '/sysBaseConfigStrategy/getFuncOptions',
    method: 'get'
  })
}

export function getTree(data) {
  return request({
    url: '/sysBaseConfigStrategy/getTree',
    method: 'get'
  })
}
