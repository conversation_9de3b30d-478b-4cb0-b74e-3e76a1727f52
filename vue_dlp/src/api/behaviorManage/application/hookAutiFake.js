import request from '@/utils/request'
import qs from 'qs'
import store from '@/store'

export function getPage(data) {
  return request({
    url: `/hookProcessAutiFake/getPage`,
    method: 'post',
    data
  })
}

/**
 * @param data 请求体
 * @param reqRoute 请求路由
 */
export function addHookProcess(data, reqRoute) {
  return request({
    url: `/${getReqRoute(reqRoute)}/insert`,
    method: 'post',
    data
  })
}

export function addHookProcessBatch(data, reqRoute) {
  return request({
    url: `/${getReqRoute(reqRoute)}/addImToolProcessBatch`,
    method: 'post',
    timeout: Math.max(store.getters.requestTimeout, 180000),
    data
  })
}

export function updateHookProcess(data, reqRoute) {
  return request({
    url: `/${getReqRoute(reqRoute)}/update`,
    method: 'post',
    data
  })
}

export function deleteHookProcess(data, reqRoute) {
  return request({
    url: `/${getReqRoute(reqRoute)}/delete`,
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getFingerprintRela(data) {
  return request({
    url: `/hookProcessAutiFake/fingerprintRela`,
    method: 'get',
    params: data
  })
}

function getReqRoute(routePath) {
  return routePath || 'hookProcessAutiFake'
}
