import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/sysAlarmConfigStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/sysAlarmConfigStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/sysAlarmConfigStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/sysAlarmConfigStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/sysAlarmConfigStrategy/delete',
    method: 'post',
    params: data
  })
}
