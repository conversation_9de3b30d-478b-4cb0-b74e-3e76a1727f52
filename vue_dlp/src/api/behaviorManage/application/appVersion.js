import request from '@/utils/request'
import qs from 'qs'
export function getAppVersionPage(data) {
  return request({
    url: '/appVersion/getPage',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/appVersion/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/appVersion/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/appVersion/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/appVersion/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/appVersion/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSoftList(data) {
  return request({
    url: '/softwareClass/getSoftwareInfotable',
    method: 'post',
    data
  })
}

export function getTypeTree() {
  return request({
    url: '/softwareClass/findSoftwareClassTree',
    method: 'post'
  })
}

export function getFormTree(data) {
  return request({
    url: '/softwareClass/findParentSelectTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyById(data) {
  return request({
    url: '/appVersion/getById',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function upload(data, onUploadProgress, token) {
  return request.post('/appVersion/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0, onUploadProgress, cancelToken: token })
}
