import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper';

export function getTreeNode() {
  return request({
    url: '/softProcess/getSoftTree',
    method: 'post'
  })
}

export function getById(id) {
  return request({
    url: `/installPacket/getById`,
    method: 'post',
    params: { id: id === undefined ? -1 : id }
  })
}

export function getInstallList(data) {
  const type = data.limitType === 1 ? 'install' : 'uninstall'
  return request({
    url: `/installPacket/${type}/findPage`,
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createData(data, limitType) {
  const type = limitType === 1 ? 'install' : 'uninstall'
  return request({
    url: `/installPacket/${type}/add`,
    method: 'post',
    data
  })
}

export function updateData(data, limitType) {
  const type = limitType === 1 ? 'install' : 'uninstall'
  return request({
    url: `/installPacket/${type}/update`,
    method: 'post',
    data
  })
}

export function deleteData(data, limitType) {
  const type = limitType === 1 ? 'install' : 'uninstall'
  return request({
    url: `/installPacket/${type}/delete`,
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportData(data, limitType, opts) {
  const type = limitType === 1 ? 'install' : 'uninstall'
  return fetchFile({
    ...opts,
    url: `/installPacket/${type}/export`,
    method: 'post',
    data
  })
}

export function getByName(data, limitType) {
  const type = limitType === 1 ? 'install' : 'uninstall'
  return request({
    url: `/installPacket/${type}/getByName`,
    method: 'post',
    params: data
  })
}

export function getProximityStrategyData(data, limitType) {
  const type = limitType === 1 ? 'install/getInstall' : 'uninstall/getUninstall'
  return request({
    url: `/installPacket/${type}ProximityStrategy`,
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSpecialPath(data) {
  return request({
    url: '/installPacket/getSpecialPath',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function saveSpecialPath(data) {
  return request({
    url: '/installPacket/saveSpecialPath',
    method: 'post',
    data
  })
}

export function updateSpecialPath(data) {
  return request({
    url: '/installPacket/updateSpecialPath',
    method: 'post',
    data
  })
}

export function deleteSpecialPath(data) {
  return request({
    url: '/installPacket/deleteSpecialPath',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportSpecialPath(data, opts) {
  return fetchFile({
    ...opts,
    url: '/installPacket/exportSpecialPath',
    method: 'post',
    data
  })
}

export function getApprovalSoftCount(data) {
  return request({
    url: '/installPacket/getApprovalSoftCount',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function upload(data) {
  return request.post('/installPacket/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function getTypeTree(data) {
  return request({
    url: '/installPacket/getTypeTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function findApprovalSoftList(data) {
  return request({
    url: '/installPacket/findApprovalSoftList',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function approvalSoft(data) {
  return request({
    url: '/installPacket/approvalSoft',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteApprovalSoft(data) {
  return request({
    url: '/installPacket/deleteApprovalSoft',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStrategyById(data, limitType) {
  const type = limitType === 1 ? 'install' : 'uninstall'
  return request({
    url: `/installPacket/${type}/getById`,
    method: 'post',
    data: qs.stringify(data)
  })
}
