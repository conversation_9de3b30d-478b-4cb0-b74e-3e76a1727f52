import request from '@/utils/request'

export function createStrategy(data) {
  return request({
    url: '/offlineLockScreen/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/offlineLockScreen/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/offlineLockScreen/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/offlineLockScreen/getByName',
    method: 'post',
    data
  })
}

export function getStrategyPage(data) {
  return request({
    url: '/offlineLockScreen/getPage',
    method: 'post',
    data
  })
}
