import request from '@/utils/request'
import qs from 'qs'

export function getWinTitlePage(data) {
  return request({
    url: '/winTitleBlock/getPage',
    method: 'post',
    data
  })
}

export function getWinTitleByName(data) {
  return request({
    url: '/winTitleBlock/getByName',
    method: 'post',
    params: data
  })
}

export function createWinTitle(data) {
  return request({
    url: '/winTitleBlock/add',
    method: 'post',
    data
  })
}

export function updateWinTitle(data) {
  return request({
    url: '/winTitleBlock/update',
    method: 'post',
    data
  })
}

export function deleteWinTitle(data) {
  return request({
    url: '/winTitleBlock/delete',
    method: 'post',
    params: data
  })
}

export function getStrategyById(data) {
  return request({
    url: '/winTitleBlock/getById',
    method: 'post',
    data: qs.stringify(data)
  })
}
