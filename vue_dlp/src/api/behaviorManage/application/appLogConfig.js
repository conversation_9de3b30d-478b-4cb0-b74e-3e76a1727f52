import request from '@/utils/request'

export function getDataPage(data) {
  return request({
    url: '/appLogConfig/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/appLogConfig/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/appLogConfig/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/appLogConfig/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/appLogConfig/delete',
    method: 'post',
    params: data
  })
}

export function saveOptions(data) {
  return request({
    url: '/appLogConfig/saveOptions',
    method: 'post',
    data
  })
}

export function listOptions(data) {
  return request({
    url: '/appLogConfig/listOptions',
    method: 'post',
    params: data
  })
}
