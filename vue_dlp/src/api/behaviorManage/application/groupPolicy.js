import request from '@/utils/request'
import qs from 'qs'

export function getStrategyList(data) {
  return request({
    url: '/groupPolicyStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/groupPolicyStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/groupPolicyStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/groupPolicyStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/groupPolicyStrategy/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSettingValue(data) {
  return request({
    url: '/groupPolicyStrategy/getSettingValue',
    method: 'post',
    data
  })
}

export function getGroupPolicyStg(data) {
  return request({
    url: '/groupPolicyStrategy/getGroupPolicyStg',
    method: 'post',
    data
  })
}

export function listFunctionMap(data) {
  return request({
    url: '/groupPolicyStrategy/listFunctionMap',
    method: 'get'
  })
}
