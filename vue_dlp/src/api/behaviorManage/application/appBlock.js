import request from '@/utils/request'
import qs from 'qs'

export function getAppBlockPage(data) {
  return request({
    url: '/appBlock/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/appBlock/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/appBlock/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/appBlock/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/appBlock/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/appBlock/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getTreeNode() {
  return request({
    url: '/appInfo/getAppTree',
    method: 'post'
  })
}
