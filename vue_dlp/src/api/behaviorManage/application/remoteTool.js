import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/remoteToolStrategy/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/remoteToolStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/remoteToolStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/remoteToolStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/remoteToolStrategy/delete',
    method: 'post',
    params: data
  })
}
