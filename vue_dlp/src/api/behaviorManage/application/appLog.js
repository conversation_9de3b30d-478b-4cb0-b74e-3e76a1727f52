import request from '@/utils/request'
import qs from 'qs'

export function getLogPage(data) {
  return request({
    url: '/log/app/getPage',
    method: 'post',
    data: qs.stringify(data),
    timeout: 0
  })
}

export function getTitlePage(data) {
  return request({
    url: '/log/app/getTitlePage',
    method: 'post',
    data: qs.stringify(data),
    timeout: 0
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/app/delete',
    method: 'post',
    data
  })
}

export function exportAppLogExcel(data) {
  return request({
    url: '/log/app/export',
    method: 'post',
    data
  })
}

export function exportTitleExcel(data) {
  return request({
    url: '/log/app/exportTitle',
    method: 'post',
    data
  })
}
