import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper';

export function listStgObjInterrupt(data) {
  return request({
    url: '/strategyExtend/listStgObjInterrupt' + (data && `?type=${data}`),
    method: 'get'
  })
}

/**
 * 查询自身未配置策略，并且不继承上级策略的对象
 * @param data
 * @returns {*}
 */
export function listNotExistStgAndInterruptObj(data) {
  return request({
    url: '/strategyExtend/listNotExistStgAndInterruptObj?type=' + data,
    method: 'get'
  })
}

export function addStgObjInterrupt(data, stgType) {
  return request({
    url: '/strategyExtend/addInterrupt' + (stgType && `?type=${stgType}`),
    method: 'post',
    data
  })
}

export function updateStgObjInterrupt(data) {
  return request({
    url: '/strategyExtend/updateInterrupt',
    method: 'post',
    data
  })
}

export function deleteStgObjInterrupt(data) {
  return request({
    url: '/strategyExtend/deleteInterrupt',
    method: 'post',
    data
  })
}

export function exportStgObjInterrupt(stgType, opts, title, titleI18nKey) {
  return fetchFile({
    ...opts,
    url: '/strategyExtend/exportStgObjInterrupt',
    method: 'post',
    data: { stgType, title, titleI18nKey }
  })
}
