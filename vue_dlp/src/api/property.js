import request from '@/utils/request'

export function getPropertyPage(data) {
  return request({
    url: '/property/getPage',
    method: 'post',
    data
  })
}

export function getPropertyByCode(data) {
  return request({
    url: '/property/getByCode',
    method: 'post',
    data: 'code=' + data
  })
}
export function getPropertyValueByCode(data) {
  return request({
    url: '/property/getValueByCode',
    method: 'post',
    data: 'code=' + data
  })
}

export function getIsMasterSub() {
  return request({
    url: '/property/getIsMasterSub',
    method: 'get'
  })
}

export function getIsThreeUserDeleteAble() {
  return request({
    url: '/property/getIsThreeUserDeleteAble',
    method: 'get'
  })
}

export function getEnableConfig() {
  return request({
    url: '/property/getEnableConfig',
    method: 'get'
  })
}

export function getIsAdminLogDeleteAble() {
  return request({
    url: '/property/getIsAdminLogDeleteAble',
    method: 'get'
  })
}

export function getIsAuditingLogDeleteAble() {
  return request({
    url: '/property/getIsAuditingLogDeleteAble',
    method: 'get'
  })
}

export function getIsViewMyselfLogAble() {
  return request({
    url: '/property/getIsViewMyselfLogAble',
    method: 'get'
  })
}

export function isAllowConfigAdminValidity() {
  return request({
    url: '/property/isAllowConfigAdminValidity',
    method: 'get'
  })
}

export function isAllowConfigUserValidity() {
  return request({
    url: '/property/isAllowConfigUserValidity',
    method: 'get'
  })
}

export function getHidden3DataSource() {
  return request({
    url: '/property/getHidden3DataSource',
    method: 'get'
  })
}

export function getEncVer4EditAble() {
  return request({
    url: '/property/getEncVer4EditAble',
    method: 'get'
  })
}

export function updateProperty(data) {
  return request({
    url: '/property/update',
    method: 'post',
    data
  })
}

export function activeEnhancedPlug() {
  return request({
    url: '/chargePlugAuth/activeEnhancedPlug',
    method: 'get'
  })
}

