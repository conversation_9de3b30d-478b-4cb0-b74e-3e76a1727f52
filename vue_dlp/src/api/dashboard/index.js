import request from '@/utils/request'

// 是否购买报表系统
export function isOwn() {
  return request({
    url: '/reportPanel/isOwn',
    method: 'get'
  })
}

// 默认页面的数据接口
export function getEchartData() {
  return request({
    url: '/dashboard/getEchartData',
    method: 'post'
  })
}

// 默认页面得默认面板（只有标题没有数据）
export function getEchartTemplate() {
  return request({
    url: '/dashboard/getEchart',
    method: 'post'
  })
}

// 根据用户id获取页面
export function getReportListByUserId(userId) {
  return request({
    url: '/reportPanel/getReportList/' + userId,
    method: 'get'
  })
}
export function getReportPanelByUserId(id) {
  return request({
    url: '/reportPanel/query/' + id,
    method: 'get'
  })
}

export function getEchartDataByCode(code) {
  return request({
    url: '/reportPanel/getEchartData/' + code,
    method: 'get'
  })
}

// 批量获取报表数据， data: { codeStr: '1,2,3' }
export function getEchartDataByCodes(data) {
  return request({
    url: '/reportPanel/getReportDataByBatch',
    method: 'post',
    data
  })
}

export function addPanel(data) {
  return request({
    url: '/reportPanel/add',
    method: 'post',
    data
  })
}

export function updatePanel(data) {
  return request({
    url: '/reportPanel/update',
    method: 'post',
    data
  })
}

export function deletePanel(id) {
  return request({
    url: '/reportPanel/delete/' + id,
    method: 'post'
  })
}

export function checkReportPanelNameExists(data) {
  return request({
    url: `/reportPanel/checkReportPanelNameExists`,
    method: 'post',
    data
  })
}
