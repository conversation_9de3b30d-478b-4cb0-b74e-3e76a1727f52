import request from '@/utils/request'

export function settingMultiAuthData(data) {
  return request({
    url: '/sysUserMultiAuth/settingMultiAuthData',
    method: 'post',
    data
  })
}

export function getMultiAuthData() {
  return request({
    url: '/sysUserMultiAuth/getMultiAuthData',
    method: 'get'
  })
}

export function getMultiAuthDataBySysUserId(data) {
  return request({
    url: '/sysUserMultiAuth/getMultiAuthDataBySysUserId',
    method: 'get',
    params: { sysUserId: data }
  })
}

export function getMultiAuthSysUser(ignoreSuperAdmin) {
  return request({
    url: '/sysUserMultiAuth/getMultiAuthSysUser',
    method: 'get'
  })
}

// 多重登录认证校验
export async function verifyMultiLoginAuth(data) {
  return request({
    url: '/sysUserMultiAuth/verifyMultiLoginAuth',
    method: 'post',
    data
  })
}
