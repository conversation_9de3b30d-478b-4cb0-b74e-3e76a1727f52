import request from '@/utils/request'

export function getLogDirList() {
  return request({
    url: '/logFile/listDir',
    method: 'post'
  })
}

export function saveLogDir(data) {
  return request({
    url: '/logFile/saveDir',
    method: 'post',
    data
  })
}

export function deleteLogDir(id) {
  return request({
    url: '/logFile/deleteDir',
    method: 'post',
    data: `id=${id}`
  })
}

export function updateUserLogDir(ids) {
  return request({
    url: '/logFile/personalDir',
    method: 'post',
    data: `ids=${ids}`
  })
}

export function getLogFilePage(data) {
  return request({
    url: '/logFile/getPage',
    method: 'post',
    data
  })
}

export function getLogFileChildren(data) {
  return request({
    url: '/logFile/children',
    method: 'post',
    data
  })
}

export function previewLogFile(data) {
  return request({
    url: '/logFile/preview',
    method: 'post',
    data
  })
}

export function downloadLogFile(data) {
  return request({
    url: `/logFile/download/${encodeURIComponent(data.dir)}/${encodeURIComponent(data.name)}`,
    method: 'get',
    responseType: 'blob'
  })
}

export function downloadZipLogFile(data) {
  return request({
    url: '/logFile/zip/download',
    method: 'post',
    responseType: 'blob',
    data
  })
}
