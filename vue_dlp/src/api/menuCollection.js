import request from '@/utils/request'

export function listMenuCollection() {
  return request({
    url: '/menuCollection/list',
    method: 'get'
  })
}

export function createMenuCollection(data) {
  return request({
    url: '/menuCollection/add',
    method: 'post',
    data
  })
}

export function deleteMenuCollection(data) {
  return request({
    url: '/menuCollection/delete',
    method: 'post',
    data
  })
}
