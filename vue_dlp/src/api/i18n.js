import request from '@/utils/request'

export function getI18nData() {
  return request({
    url: '/i18n/data',
    method: 'get'
  })
}

export function exportI18nData(lang) {
  return request({
    url: '/i18n/export?lang=' + lang,
    method: 'post',
    responseType: 'blob'
  })
}

export function importAndConvert(data) {
  return request({
    url: '/i18n/import',
    method: 'post',
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}
