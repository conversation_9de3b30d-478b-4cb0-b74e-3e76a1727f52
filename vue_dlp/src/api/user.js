import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/login',
    method: 'post',
    data
  })
}

export function getUserToken(data) {
  return request({
    url: '/api/auth/token',
    method: 'post',
    params: data
  })
}

export function validToken(data) {
  return request({
    url: '/api/auth/validToken',
    method: 'post',
    params: data
  })
}

export function getSysUserByRoleId(data) {
  return request({
    url: '/sysUser/getSysUserByRoleId',
    method: 'post',
    data
  })
}

export function getLoginAuthSysUser(data) {
  return request({
    url: '/sysUser/getLoginAuthSysUser',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/sysUser/getLoginUser',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

export function existDB() {
  return request({
    url: '/init/config/existDB',
    method: 'get'
  })
}

export function initDB(data) {
  return request({
    url: '/init/config/update',
    method: 'post',
    data
  })
}

export function sendFindPwdMail(data) {
  return request({
    url: '/sysUser/findPwd/send',
    method: 'post',
    data
  })
}

export function sendFindPwdSms(data) {
  return request({
    url: '/sysUser/findPwd/sendSms',
    method: 'post',
    data
  })
}

export function updateFindPwd(data) {
  return request({
    url: '/sysUser/findPwd/update',
    method: 'post',
    data
  })
}

export function getVersion() {
  return request({
    url: '/resource/getVer',
    method: 'get'
  })
}

export function getResources() {
  return request({
    url: '/resource/get',
    method: 'get'
  })
}

export function validatePassword(data) {
  return request({
    url: '/sysUser/validatePassword',
    method: 'post',
    data
  })
}

export function insertPersonalization(data) {
  return request({
    url: '/personalization/insert',
    method: 'post',
    params: data
  })
}

export function getValueByCondition(data, token) {
  return request({
    url: '/personalization/getValueByCondition',
    method: 'post',
    cancelToken: token,
    params: data
  })
}
