// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/urlsSearch/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/urlsSearch/delete',
    method: 'post',
    data
  })
}

export function exportUrlsSearch(data) {
  return request({
    url: '/log/urlsSearch/export',
    method: 'post',
    data
  })
}
