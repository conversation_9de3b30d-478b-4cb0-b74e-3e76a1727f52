// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/netFlow/getPage',
    method: 'post',
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/netFlow/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/netFlow/delete',
    method: 'post',
    data
  })
}
