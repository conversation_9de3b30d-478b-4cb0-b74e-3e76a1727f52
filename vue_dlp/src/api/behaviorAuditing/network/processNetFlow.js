// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/processNetFlow/getPage',
    method: 'post',
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/processNetFlow/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/processNetFlow/delete',
    method: 'post',
    data
  })
}
