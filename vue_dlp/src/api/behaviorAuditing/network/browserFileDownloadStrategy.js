import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/browserFileDownloadStrategy/getPage',
    method: 'post',
    data
  })
}
export function getDataPage(data) {
  return request({
    url: '/browserFileDownloadStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/browserFileDownloadStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/browserFileDownloadStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/browserFileDownloadStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/browserFileDownloadStrategy/delete',
    method: 'post',
    params: data
  })
}
