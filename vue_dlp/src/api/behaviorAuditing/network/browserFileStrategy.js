import request from '@/utils/request'

export function getImFilePage(data) {
  return request({
    url: '/browserFileStrategy/getPage',
    method: 'post',
    data
  })
}
export function getDataPage(data) {
  return request({
    url: '/browserFileStrategy/getPage',
    method: 'post',
    data
  })
}

export function getDataByName(data) {
  return request({
    url: '/browserFileStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/browserFileStrategy/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/browserFileStrategy/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/browserFileStrategy/delete',
    method: 'post',
    params: data
  })
}
