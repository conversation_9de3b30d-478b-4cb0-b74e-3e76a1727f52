// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/forumdataLog/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/forumdataLog/delete',
    method: 'post',
    data
  })
}

export function exportForumdataLog(data) {
  return request({
    url: '/log/forumdataLog/export',
    method: 'post',
    data
  })
}
