// import { parseTime } from '@/utils'
import request from '@/utils/request'
import qs from 'qs'

export function getSessionTree(data) {
  return request({
    url: '/log/chatText/getChatSessionInfo',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/chatText/delete',
    method: 'post',
    params: data
  })
}

export function getMsgList(data) {
  return request({
    url: '/log/chatText/list',
    method: 'post',
    data
  })
}
