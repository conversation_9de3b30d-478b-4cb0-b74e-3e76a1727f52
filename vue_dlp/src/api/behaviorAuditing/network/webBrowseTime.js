// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/webBrowseTime/getPage',
    method: 'post',
    data
  })
}

export function exportWebBrowseTimeLog(data) {
  return request({
    url: '/log/webBrowseTime/export',
    method: 'post',
    data
  })
}

export function deleteLog(data, headers) {
  return request({
    url: '/log/webBrowseTime/delete',
    method: 'post',
    headers,
    data
  })
}
