// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/browserUpload/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/browserUpload/delete',
    method: 'post',
    data
  })
}

export function exportBrowserUploadLog(data) {
  return request({
    url: '/log/browserUpload/export',
    method: 'post',
    data
  })
}
