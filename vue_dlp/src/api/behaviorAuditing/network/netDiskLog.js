import request from '@/utils/request'
import qs from 'qs'

export function getLogPage(data) {
  return request({
    url: '/log/netDisk/getPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/netDisk/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/netDisk/delete',
    method: 'post',
    data
  })
}
