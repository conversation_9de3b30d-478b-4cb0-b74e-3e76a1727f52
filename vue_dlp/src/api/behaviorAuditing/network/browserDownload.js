// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/browserDownload/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/browserDownload/delete',
    method: 'post',
    data
  })
}

export function exportBrowserDownloadLog(data) {
  return request({
    url: '/log/browserDownload/export',
    method: 'post',
    data
  })
}
