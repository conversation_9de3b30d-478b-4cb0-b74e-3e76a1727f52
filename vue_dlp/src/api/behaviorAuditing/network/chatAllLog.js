// import { parseTime } from '@/utils'
import request from '@/utils/request'
import qs from 'qs'

export function getSessionTree(data) {
  return request({
    url: '/log/chatAll/getChatSessionInfo',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getMsgList(data) {
  return request({
    url: '/log/chatAll/list',
    method: 'post',
    data
  })
}

export function exportChatAllLog(data) {
  return request({
    url: '/log/chatAll/export',
    method: 'post',
    data
  })
}

export function getRootNode(data) {
  return request({
    url: '/log/chatAll/getRootNode',
    method: 'post',
    data: qs.stringify(data),
    timeout: 0
  })
}

export function getChildren(data) {
  return request({
    url: '/log/chatAll/getChildren',
    method: 'post',
    data: qs.stringify(data),
    timeout: 0
  })
}

export function downloadFile(data) {
  return request({
    url: '/log/chatAll/download',
    method: 'post',
    // responseType: 'blob',
    timeout: 0,
    data
  })
}

export function checkLocalFileExist(data) {
  return request({
    url: '/log/chatAll/checkLocalFileExist',
    method: 'post',
    data
  })
}
