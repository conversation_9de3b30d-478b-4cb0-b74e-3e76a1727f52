// import { parseTime } from '@/utils'
import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/url/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/url/delete',
    method: 'post',
    data
  })
}

export function exportWebBrowseLog(data) {
  return request({
    url: '/log/url/export',
    method: 'post',
    data
  })
}
