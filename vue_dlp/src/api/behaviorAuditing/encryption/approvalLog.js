import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/approval/getPage',
    method: 'post',
    timeout: 0,
    data
  })
}

export function getLogDetail(id) {
  return request({
    url: '/log/approval/getComment/' + id,
    method: 'get'
  })
}

export function downloadLog(data) {
  return request({
    url: '/log/approval/file/download',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/approval/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/approval/delete',
    method: 'post',
    data
  })
}

