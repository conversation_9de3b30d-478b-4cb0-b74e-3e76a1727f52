import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/mailWhiteList/getPage',
    method: 'post',
    data
  })
}

export function exportLog(data) {
  return request({
    url: '/log/mailWhiteList/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/mailWhiteList/delete',
    method: 'post',
    data
  })
}

export function getAttachNum(data) {
  return request({
    url: '/log/mailWhiteList/getAttachNum',
    method: 'post',
    data
  })
}

export function getPageAttach(data) {
  return request({
    url: '/log/mailWhiteList/getPageAttach',
    method: 'post',
    data
  })
}
