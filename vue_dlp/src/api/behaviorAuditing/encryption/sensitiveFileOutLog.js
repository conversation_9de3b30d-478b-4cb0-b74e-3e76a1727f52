import request from '@/utils/request'

export function getFlowTree() {
  return request({
    url: '/log/sensitiveFileOutsend/sensitiveOutsendTree',
    method: 'get'
  })
}

export function getLogPage(data) {
  return request({
    url: '/log/sensitiveFileOutsend/getPage',
    method: 'post',
    data
  })
}

export function getDetail(data) {
  return request({
    url: '/log/approval/getPage',
    method: 'post',
    data
  })
}

export function getFiles(data) {
  return request({
    url: '/log/approval/getFile',
    method: 'get',
    params: data
  })
}

export function getAttachFilePage(data) {
  return request({
    url: '/log/approval/getAttachmentFile',
    method: 'get',
    params: data
  })
}

export function getAttachFileByRow(fileData) {
  return new Promise((resolve, reject) => {
    // 成功的情况下调用resolve
    resolve({
      code: 20000,
      data: {
        total: fileData.length,
        items: fileData
      }
    })
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/sensitiveFileOutsend/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/sensitiveFileOutsend/delete',
    method: 'post',
    data
  })
}
