
import request from '@/utils/request'

export function getScanDetailPage(data) {
  return request({
    url: '/log/diskScanSelfCheck/getDetailPage',
    method: 'post',
    data
  })
}

export function getLogPage(data) {
  return request({
    url: '/log/diskScanSelfCheck/getPage',
    method: 'post',
    params: data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/diskScanSelfCheck/delete',
    method: 'post',
    params: data
  })
}

export function getSensitivePage(data) {
  return request({
    url: '/log/diskScanSelfCheck/getSensitivePage',
    method: 'post',
    params: data
  })
}

export function deleteSensitiveLog(data) {
  return request({
    url: '/log/diskScanSelfCheck/deleteSensitive',
    method: 'post',
    params: data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/diskScanSelfCheck/export',
    method: 'post',
    data
  })
}
