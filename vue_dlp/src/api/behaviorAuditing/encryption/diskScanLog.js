import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/diskScan/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data, headers) {
  return request({
    url: '/log/diskScan/delete',
    method: 'post',
    headers,
    data
  })
}

export function getSensitivePage(data) {
  return request({
    url: '/log/diskScan/getSensitivePage',
    method: 'post',
    data
  })
}

export function deleteSensitiveLog(data, headers) {
  return request({
    url: '/log/diskScan/deleteSensitive',
    method: 'post',
    headers,
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/diskScan/export',
    method: 'post',
    timeout: 300000,
    data
  })
}

export function getSensFileLogPage(data) {
  return request({
    url: '/log/diskScan/getSensFileLogPage',
    method: 'post',
    data
  })
}
