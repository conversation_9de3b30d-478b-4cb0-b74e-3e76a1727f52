import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/auditLogUnifiedQuery/getPage2',
    method: 'post',
    data
  })
}

export function sceneOptions() {
  return request({
    url: '/auditLogUnifiedQuery/sceneOptions',
    method: 'get'
  })
}

export function query() {
  return request({
    url: '/auditLogUnifiedQuery/query',
    method: 'post'
  })
}
export function getParams(data, sceneName, sceneDetail) {
  return request({
    url: '/auditLogUnifiedQuery/getParams/' + sceneName + '-' + sceneDetail,
    method: 'post',
    data
  })
}

export function getDetail(id, sceneDetail) {
  return request({
    url: '/auditLogUnifiedQuery/getDetail',
    method: 'post',
    data: { id: id, sceneDetail: sceneDetail }
  })
}

export function getSceneDescription(sceneName) {
  return request({
    url: '/auditLogUnifiedQuery/getSceneDescription/' + sceneName,
    method: 'get'
  })
}
