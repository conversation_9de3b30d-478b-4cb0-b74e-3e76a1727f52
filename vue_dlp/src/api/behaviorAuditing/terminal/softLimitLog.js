import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/log/softLimitLog/getPage',
    method: 'post',
    data: data
  })
}

export function importProcess(data) {
  return request({
    url: '/log/softLimitLog/importProcess',
    method: 'post',
    data: data
  })
}

export function isRepeatTerm(data) {
  return request({
    url: '/log/softLimitLog/isRepeatTerm',
    method: 'post',
    data: data
  })
}

export function exportSoftLimitLog(data) {
  return request({
    url: '/log/softLimitLog/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/softLimitLog/delete',
    method: 'post',
    data
  })
}
