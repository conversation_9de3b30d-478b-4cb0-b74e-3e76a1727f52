import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: 'log/approvalSoftwareSubmitLog/getPage',
    method: 'post',
    data
  })
}

export function exportLog(data) {
  return request({
    url: '/log/approvalSoftwareSubmitLog/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/approvalSoftwareSubmitLog/delete',
    method: 'post',
    data
  })
}

export function pullSoftware(data) {
  return request({
    url: '/log/approvalSoftwareSubmitLog/pullSoftware',
    method: 'post'
  })
}

export function deleteSoftware(data) {
  return request({
    url: '/log/approvalSoftwareSubmitLog/deleteSoftware',
    method: 'post'
  })
}
