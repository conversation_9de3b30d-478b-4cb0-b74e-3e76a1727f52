import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getLogPage(data) {
  return request({
    url: '/log/email/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/email/delete',
    method: 'post',
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/email/export',
    method: 'post',
    data
  })
}

export function getRelAttachment(data) {
  return request({
    url: '/log/email/getRelAttachment',
    method: 'post',
    data
  })
}

export function downloadAttachment(topic, data, file) {
  let url = '/log/email/downloadAttachment'
  if (data.length > 1) {
    url += '?filename=' + decodeURIComponent(file.name)
  }
  return fetchFile({
    topic,
    jwt: true,
    url,
    method: 'post',
    data,
    file,
    keepSteps: true
  })
}
