import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/highRiskSoftwareLog/getPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/highRiskSoftwareLog/delete',
    method: 'post',
    data
  })
}

export function exportLog(data) {
  return request({
    url: '/log/highRiskSoftwareLog/export',
    method: 'post',
    data
  })
}

export function checkExistInLibrary(data) {
  return request({
    url: '/log/highRiskSoftwareLog/checkExistInLibrary',
    method: 'post',
    data
  })
}

export function batchAddToLibrary(data) {
  return request({
    url: '/log/highRiskSoftwareLog/batchAddToLibrary',
    method: 'post',
    data
  })
}

