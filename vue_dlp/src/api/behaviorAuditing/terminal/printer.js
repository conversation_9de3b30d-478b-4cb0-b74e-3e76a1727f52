// import { parseTime } from '@/utils'
import request from '@/utils/request'
import qs from 'qs'

export function getLogPage(data) {
  return request({
    url: '/log/printer/getPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/printer/delete',
    method: 'post',
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/printer/export',
    method: 'post',
    data
  })
}
