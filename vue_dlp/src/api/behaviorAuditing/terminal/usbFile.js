// import { parseTime } from '@/utils'
import request from '@/utils/request'
// import qs from 'qs'

export function getLogPage(data) {
  return request({
    url: '/log/usbFile/getPage',
    method: 'post',
    data
  })
}

export function downloadFile(data) {
  return request({
    url: '/log/usbFile/download',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data
  })
}

export function exportExcel(data) {
  return request({
    url: '/log/usbFile/export',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/usbFile/delete',
    method: 'post',
    data
  })
}
