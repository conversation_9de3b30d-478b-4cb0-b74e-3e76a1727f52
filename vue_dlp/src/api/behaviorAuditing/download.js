import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function downloadFile(topic, data, file) {
  let url = '/transferTask/download'
  if (data.length > 1) {
    url += '?filename=' + decodeURIComponent(file.name)
  }
  return fetchFile({
    topic,
    jwt: true,
    url,
    method: 'post',
    data,
    file,
    keepSteps: true
  })
}

export function getDownloadTask(data) {
  return request({
    url: '/transferTask/getDownloadTask',
    method: 'post',
    timeout: 0,
    data
  })
}

export function cancelDownloadTask(data) {
  return request({
    url: '/transferTask/cancelDownloadTask',
    method: 'post',
    timeout: 0,
    data
  })
}

export function getFtpStatus() {
  return request({
    url: '/transferTask/getTransferStatus',
    method: 'get'
  })
}
