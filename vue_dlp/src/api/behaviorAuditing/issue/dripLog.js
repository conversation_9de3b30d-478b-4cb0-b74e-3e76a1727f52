import request from '@/utils/request'

export function getIssueLogPage(data) {
  return request({
    url: '/log/drip/getIssuePage',
    method: 'post',
    data
  })
}

export function deleteIssueLog(data) {
  return request({
    url: '/log/drip/deleteIssue',
    method: 'post',
    data
  })
}

export function getMatchLogPage(data) {
  return request({
    url: '/log/drip/getMatchPage',
    method: 'post',
    data
  })
}

export function deleteMatchLog(data) {
  return request({
    url: '/log/drip/deleteMatch',
    method: 'post',
    params: data
  })
}

export function exportIssueExcel(data) {
  return request({
    url: '/log/drip/export',
    method: 'post',
    data
  })
}
