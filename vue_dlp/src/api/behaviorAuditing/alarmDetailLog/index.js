import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/alarmLog/findPage',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/alarmLog/delete',
    method: 'post',
    data
  })
}

export function getSensitiveLogPage(data) {
  return request({
    url: '/log/SensitiveAlarmLog/findPage',
    method: 'post',
    data
  })
}

export function deleteSensitiveLog(data) {
  return request({
    url: '/log/SensitiveAlarmLog/delete',
    method: 'post',
    data
  })
}
export function getAlarmType() {
  return request({
    url: '/log/alarmLog/getAlarmType',
    method: 'post'
  })
}

export function getAlarmAction(data) {
  return request({
    url: '/log/alarmLog/getAlarmAction',
    method: 'post',
    params: data
  })
}

export function getSensitiveLossType(data) {
  return request({
    url: '/log/SensitiveAlarmLog/getSensitiveLossType',
    method: 'post',
    params: data
  })
}

export function getAllSensitiveLossType() {
  return request({
    url: '/log/SensitiveAlarmLog/getAllSensitiveLossType',
    method: 'post'
  })
}

export function getScreenshotLogPage(data) {
  return request({
    url: '/log/alarmLog/getAlarmScreenshotLog',
    method: 'post',
    params: data
  })
}

export function downloadFile(data) {
  return request({
    url: '/log/alarmLog/download',
    method: 'post',
    params: data
  })
}

export function getAlarmIds() {
  return request({
    url: '/log/alarmLog/listMsgIds',
    method: 'post'
  })
}

export function getSysAlarmMsgIds() {
  return request({
    url: '/alarmMsg/getAlarmPopMsgIds',
    method: 'post'
  })
}

export function getSysAlarmMsgByIds(data) {
  return request({
    url: '/alarmMsg/getAlarmMsgById',
    method: 'post',
    params: data
  })
}

export function getAlarmMsg(data) {
  return request({
    url: '/log/alarmLog/listMsg',
    method: 'post',
    data
  })
}

export function dealAlarmMsg(data) {
  return request({
    url: '/log/alarmLog/updateMsg',
    method: 'post',
    data
  })
}

export function dealSysAlarmMsg(data) {
  return request({
    url: '/alarmMsg/dealAlarmMsg',
    method: 'post',
    params: data
  })
}

export function dealAllSysAlarmMsg() {
  return request({
    url: '/alarmMsg/updateAllMsg',
    method: 'post'
  })
}

export function dealAllAlarmMsg() {
  return request({
    url: '/log/alarmLog/updateAllMsg',
    method: 'post'
  })
}

export function getLogDetailPage(data) {
  return request({
    url: '/log/alarmLog/getAlarmRecscreenLog',
    method: 'post',
    data
  })
}

export function exportCommonAlarmLog(data) {
  return request({
    url: '/log/alarmLog/export',
    method: 'post',
    data
  })
}

export function exportSensitiveAlarmLog(data) {
  return request({
    url: '/log/SensitiveAlarmLog/export',
    method: 'post',
    data
  })
}

export function getAlarmMsgList(data) {
  return request({
    url: '/log/alarmLog/getAlarmMsgList',
    method: 'post',
    data
  })
}
