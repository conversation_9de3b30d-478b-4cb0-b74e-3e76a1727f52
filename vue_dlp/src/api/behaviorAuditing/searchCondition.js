import request from '@/utils/request';

/**
 * 返回当前用户所有保存的搜索条件
 * @returns Array 搜索条件数组
 */
export function getSearchCondition() {
  return request({
    url: '/searchCondition/list',
    method: 'get'
  })
}

/**
 * 保存搜索条件
 * @param {*} data 搜索条件数据 { name，key, tabName, condition }
 * @returns 返回新增的数据
 */
export function createSearchCondition(data) {
  return request({
    url: '/searchCondition/add',
    method: 'post',
    data
  })
}

/**
 * 删除搜索条件
 * @param {*} data 删除的数据
 * @returns 
 */
export function deleteSearchCondition(data) {
  return request({
    url: '/searchCondition/delete',
    method: 'post',
    data
  })
}
