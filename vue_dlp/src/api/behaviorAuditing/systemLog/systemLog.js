import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/systemLog/getPage',
    method: 'post',
    data
  })
}

export function getLogStatePage(data) {
  return request({
    url: '/log/systemLog/getPageState',
    method: 'post',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/systemLog/delete',
    method: 'post',
    data
  })
}

export function deleteStateLog(data) {
  return request({
    url: '/log/systemLog/deleteState',
    method: 'post',
    data
  })
}

export function exportSystemLog(data) {
  return request({
    url: '/log/systemLog/export',
    method: 'post',
    data
  })
}

export function exportSystemStateLog(data) {
  return request({
    url: '/log/systemLog/exportState',
    method: 'post',
    data
  })
}
