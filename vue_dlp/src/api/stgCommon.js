import request from '@/utils/request'

/**
 * 检查策略名称是否重复
 * @param data
 * {
 *   name: '123',  // 策略名称
 *   stgTypeNumber: 1 // 策略类型编号
 * }
 * @returns {AxiosPromise}
 */
export function checkDuplicateName(data) {
  return request({
    url: '/stgCommon/checkDuplicateName',
    method: 'post',
    data
  })
}

/**
 * 检查是否存在已启用的策略
 * @param stgTypeNumber   策略类型编号
 * @returns {AxiosPromise}
 */
export function checkActiveStg(data) {
  return request({
    url: '/stgCommon/checkActiveStg',
    method: 'post',
    data
  })
}

/**
 * 根据策略类型编号获取生效范围（1终端、2操作员、3两者）
 * @param stgTypeNumber   策略类型编号
 * @returns {AxiosPromise}
 */
export function getUsedScope(stgTypeNumber) {
  return request({
    url: '/stgCommon/getUsedScope',
    method: 'get',
    params: { stgTypeNumber }
  })
}

/**
 * 公共策略导出功能
 * @param StrategyVO
 */
export function exportStg(data) {
  return request({
    url: '/stgCommon/exportStg',
    method: 'post',
    responseType: 'blob',
    data
  })
}

/**
 * 自动匹配策略导入功能
 * @param StrategyVO
 */
export function autoImportStg(data) {
  return request({
    url: '/stgCommon/autoImportStg',
    method: 'post',
    data
  })
}

/**
 * 所有策略导出功能
 * @param StrategyVO
 */
export function multiExportStg(data) {
  return request({
    url: '/stgCommon/multiExportStg',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getExportPageStg(data) {
  return request({
    url: '/stgCommon/getExportPageStg',
    method: 'post',
    data
  })
}

/** 获取策略导出的后缀名 **/
export function getExportSuffix(strategyTypeNumbers) {
  return request({
    url: '/stgCommon/getExportSuffix',
    method: 'get',
    params: { strategyTypeNumbers }
  })
}

/** 策略导入文件解析 **/
export function importAnalysisFile(data) {
  return request({
    url: '/stgCommon/importAnalysisFile',
    method: 'post',
    responseType: 'blob',
    data
  })
}
