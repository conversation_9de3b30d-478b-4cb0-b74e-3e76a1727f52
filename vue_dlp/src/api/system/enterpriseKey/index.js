import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'
// import http from './index'

export function getTemplatePage(data) {
  return request({
    url: '/pkeyInfo/getPage',
    method: 'post',
    data
  })
}

export function saveInfo(data) {
  return request({
    url: '/pkeyInfo/insert',
    method: 'post',
    params: data
  })
}

// export function outPutInfo() {
//   return request({
//     url: '/pkeyInfo/outPut',
//     method: 'post',
//     responseType: 'blob'
//   })
// }

export function outPutInfo() {
  return request({
    url: '/pkeyInfo/outPut',
    method: 'post'
  })
}

export function downloadFile(data, opts) {
  return fetchFile({
    ...opts,
    url: '/pkeyInfo/downloadFile',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data
  })
}

export function uploadFile(data) {
  return request({
    method: 'post',
    url: '/pkeyInfo/inPut',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }}
  )
}

export function getDecryptionKeyStrategy(startTime, endTime) {
  return request({
    url: '/pkeyInfo/getDecryptionKeyStrategy/' + startTime + '/' + endTime,
    method: 'get'
  })
}

export function updateEncAlgorithm(data) {
  return request({
    url: '/pkeyInfo/updateEncAlgorithm',
    method: 'post',
    data
  })
}

export function getEncAlgorithm() {
  return request({
    url: '/pkeyInfo/getEncAlgorithm',
    method: 'get'
  })
}
