import request from '@/utils/request'

export function getCodePage(data) {
  return request({
    url: '/installPkg/getCodePage',
    method: 'post',
    timeout: 90000,
    data
  })
}

export function getInstallPkgCode() {
  return request({
    url: '/installPkg/getCode',
    method: 'get'
  })
}

export function addInstallPkgCode(data) {
  return request({
    url: '/installPkg/addCode',
    method: 'post',
    data
  })
}

export function updateInstallPkgCode(data) {
  return request({
    url: '/installPkg/updateCode',
    method: 'post',
    data
  })
}

/**
 * 获取主识别码（主安装码）
 * @returns {AxiosPromise}
 */
export function getMainCode() {
  return request({
    url: '/installPkg/getMainCode',
    method: 'get'
  })
}

/**
 * 指定加密安装码是否存在审批接入表中
 * @returns {AxiosPromise}
 */
export function validInstallCodeExitsApprovalAccess(data) {
  return request({
    url: '/installPkg/validInstallCodeExitsApprovalAccess',
    method: 'post',
    data
  })
}

/**
 * 记录下载日志
 * @returns {AxiosPromise}
 */
export function recordInstallPackageLog(data) {
  return request({
    url: '/installPkg/recordInstallPackageLog',
    method: 'post',
    data
  })
}

/**
 * 打开或关闭 校验接入码功能
 * @param enable  1-打开，0-关闭
 * @returns {AxiosPromise}
 */
export function changeInstallCodeSwitch(enable) {
  return request({
    url: '/installPkg/changeInstallCodeSwitch',
    method: 'post',
    params: enable
  })
}

/**
 * 校验指定终端安装包是否允许上传或制作
 * @returns {AxiosPromise}
 */
export function checkTermPkgAllowUploadOrMake(data) {
  return request({
    url: '/installPkg/checkTermPkgAllowUploadOrMake',
    method: 'post',
    params: { termInstallPkgVersion: data }
  })
}
