import request from '@/utils/request'
import Cookies from 'js-cookie';

/**
 * 制作安装包，实际：生成下载token
 * @returns {AxiosPromise}
 */
export function makeMsiTerm() {
  const taskId = buildTaskId()
  const data = { taskId }
  return request({
    url: '/msiTermPackage/make',
    method: 'post',
    data
  })
}

/**
 * 获取控制台版本号
 * @returns {AxiosPromise}
 */
export function getWebVersion() {
  return request({
    url: '/msiTermPackage/getWebVersion',
    method: 'get'
  })
}

function buildTaskId() {
  return encodeURIComponent(Cookies.get('sid') + '_' + Date.now())
}

export function buildDownloadUrl(taskId) {
  let url = location.origin
  const ctx = process.env.VUE_APP_BASE_API
  if (!ctx.startsWith('/')) {
    url += '/'
  }
  url += ctx
  if (!ctx.endsWith('/')) {
    url += '/'
  }
  url += 'msiTermPackage/download'
  if (taskId) {
    url += '?t=' + taskId
  }
  return url
}
