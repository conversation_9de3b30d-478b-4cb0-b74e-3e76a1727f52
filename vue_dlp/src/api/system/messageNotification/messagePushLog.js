import request from '@/utils/request';

export function getMsgLogPage(data) {
  return request({
    url: '/log/msgPush/getPage',
    method: 'post',
    data
  })
}

export function getLogDetail(id) {
  return request({
    url: `/log/msgPush/detail/${id}`,
    method: 'get'
  })
}

export function listMsgSendLog(data) {
  return request({
    url: '/log/msgPush/listSendLog',
    method: 'post',
    data
  })
}

export function exportMsgLog(data) {
  return request({
    url: '/log/msgPush/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function resendMsg(data) {
  return request({
    url: '/log/msgPush/resendMsg',
    method: 'post',
    data
  })
}

