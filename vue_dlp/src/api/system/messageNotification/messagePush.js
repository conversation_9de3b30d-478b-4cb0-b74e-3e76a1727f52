import request from '@/utils/request';
import qs from 'qs'

export function bindCloudInfo(data) {
  return request({
    url: '/cloudInfo/addCloudInfoBind',
    method: 'post',
    data
  })
}

export function updateUserStatus(data) {
  return request({
    url: '/cloudInfo/updateUserStatus/',
    method: 'post',
    data
  })
}

export function updateGivingInfo(data) {
  return request({
    url: '/cloudInfo/updateGivingInfo/',
    method: 'post',
    data
  })
}

export function getCloudInfoByName(data) {
  return request({
    url: '/cloudInfo/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportCloudBindCode(data) {
  return request({
    url: '/cloudInfo/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getCloudInfoBind(data) {
  return request({
    url: '/cloudInfo/getPage',
    method: 'post',
    data
  })
}

export function getCloudInfoBindTree() {
  return request({
    url: '/cloudInfo/getBindInfos',
    method: 'get'
  })
}

export function testCloudPlatformConnect(data) {
  return request({
    url: '/cloudInfo/testCloudPlatformAddr',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function addMsgPushTask(data) {
  return request({
    url: '/msgPush/messageTask',
    method: 'post',
    data
  })
}

export function getMsgPushTaskPage(data) {
  return request({
    url: '/msgPush/getPage',
    method: 'post',
    data
  })
}

export function delMsgPushTask(data) {
  return request({
    url: '/msgPush/delTask',
    method: 'post',
    params: data
  })
}

export function updateMsgPushTask(data) {
  return request({
    url: '/msgPush/updateTask',
    method: 'post',
    data
  })
}

export function getTaskByName(data) {
  return request({
    url: '/msgPush/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getReportCurWeek() {
  return request({
    url: '/msgPush/reportCurWeek',
    method: 'get'
  })
}

export function getPreviewTemplate() {
  return request({
    url: '/msgPush/previewTemplate',
    method: 'get'
  })
}

export function updatePreviewTemplate() {
  return request({
    url: '/msgPush/updatePreviewTemplate',
    method: 'post'
  })
}
