import request from '@/utils/request';
import qs from 'qs'

export function getReportTemplatePage(data) {
  return request({
    url: '/customizeReport/getPage',
    method: 'post',
    data
  })
}

export function addReportTemplate(data) {
  return request({
    url: '/customizeReport/reportTemplate',
    method: 'post',
    data
  })
}

export function updateReportTemplate(data) {
  return request({
    url: '/customizeReport/updateTemplate',
    method: 'post',
    data
  })
}

export function updateReportTemplateChip(data) {
  return request({
    url: '/customizeReport/updateTemplateChip',
    method: 'post',
    data
  })
}

export function delReportTemplate(data) {
  return request({
    url: '/customizeReport/deleteTemplate',
    method: 'post',
    data
  })
}

// 获取报表类型
export function getReportType() {
  return request({
    url: '/customizeReport/reportType',
    method: 'get'
  })
}

// 获取报表类型
export function getReportTemplateChipTree() {
  return request({
    url: '/customizeReport/reportTemplateChip',
    method: 'get'
  })
}

export function getReportTemplateByName(data) {
  return request({
    url: '/customizeReport/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}
