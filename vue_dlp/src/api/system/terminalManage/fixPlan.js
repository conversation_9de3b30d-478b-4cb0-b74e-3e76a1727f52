import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getTreeNode() {
  return request({
    url: '/fixPlan/findFixPlanClassTree',
    method: 'post'
  })
}

export function createData(data) {
  return request({
    url: '/fixPlan/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/fixPlan/update',
    method: 'post',
    data
  })
}

export function deleteGroup(data) {
  return request({
    url: '/fixPlan/deleteGroup',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/fixPlan/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getGroupByName(data) {
  return request({
    url: '/fixPlan/getGroupByName',
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/fixPlan/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/fixPlan/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/fixPlan/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function getFixPlanPage(data) {
  return request({
    url: '/fixPlan/getFixPlanPage',
    method: 'post',
    data
  })
}

export function addFixPlan(data) {
  return request({
    url: '/fixPlan/addFixPlanInfo',
    method: 'post',
    data
  })
}

export function updateFixPlan(data) {
  return request({
    url: '/fixPlan/updateFixPlanInfo',
    method: 'post',
    data
  })
}

export function deleteFixPlan(data) {
  return request({
    url: '/fixPlan/delete',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/fixPlan/moveGroupToOther',
    method: 'post',
    data
  })
}

export function listFixPlanTree(data) {
  return request({
    url: '/fixPlan/listFixPlanTree',
    method: 'post',
    data: data ? qs.stringify(data) : null
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/fixPlan/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getByName(data) {
  return request(({
    url: '/fixPlan/getByName',
    method: 'get',
    params: { name: data }
  }))
}
