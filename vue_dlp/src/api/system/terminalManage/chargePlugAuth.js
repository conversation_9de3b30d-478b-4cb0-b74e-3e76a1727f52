import request from '@/utils/request'
import qs from 'qs'

export function getAuthInfoPage(data) {
  return request({
    url: '/chargePlugAuth/getPage',
    method: 'post',
    data
  })
}
export function getAlarmSetup() {
  return request({
    url: '/chargePlugAlarmSetup/getAlarmSetup',
    method: 'get'
  })
}

export function saveAlarmSetup(data) {
  return request({
    url: '/chargePlugAlarmSetup/updateAlarmSetup',
    method: 'post',
    data
  })
}

export function uploadFile(data) {
  return request.post('/chargePlugAuth/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function grantAuthIni(data) {
  return request({
    url: '/chargePlugAuth/grantAuthOfIni',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exchangeTrwfe() {
  return request({
    url: '/chargePlugAuth/exchangeTrwfe',
    method: 'get'
  })
}

export function getAlarmMsg() {
  return request({
    url: '/chargePlugAuth/getAlarmMsg',
    method: 'get'
  })
}

