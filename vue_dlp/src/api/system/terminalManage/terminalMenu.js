import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/terminalMenuStrategy/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/terminalMenuStrategy/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/terminalMenuStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/terminalMenuStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/terminalMenuStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/terminalMenuStrategy/delete',
    method: 'post',
    params: data
  })
}
