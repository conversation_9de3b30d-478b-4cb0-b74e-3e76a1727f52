import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/domainAccount/getPage',
    method: 'post',
    timeout: 90000,
    data
  })
}

export function add(data) {
  return request({
    url: '/domainAccount/insert',
    method: 'post',
    timeout: 120000,
    data
  })
}

export function update(data) {
  return request({
    url: '/domainAccount/update',
    method: 'post',
    data
  })
}

export function deleteAccount(data) {
  return request({
    url: '/domainAccount/delete',
    method: 'post',
    params: data
  })
}

export function getByAccount(data) {
  return request({
    url: '/domainAccount/getByAccount',
    method: 'post',
    params: data
  })
}

