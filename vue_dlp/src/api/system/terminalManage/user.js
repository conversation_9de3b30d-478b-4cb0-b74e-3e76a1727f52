import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function fetchList(data) {
  return request({
    url: '/user/getPage',
    method: 'post',
    data
  })
}
export function getUserPage(data) {
  return request({
    url: '/user/getPage',
    method: 'post',
    data
  })
}

export function getUserByIds(data) {
  return request({
    url: '/user/getUserByIds',
    method: 'post',
    data
  })
}

export function getUserById(id) {
  return request({
    url: '/user/get/' + id,
    method: 'get'
  })
}

export function fetchDetail(id) {
  return request({
    url: '/user/get/' + id,
    method: 'get'
  })
}

export function getByAccount(data) {
  return request({
    url: '/user/getByAccount',
    method: 'post',
    params: data
  })
}

export function getByPhone(data) {
  return request({
    url: '/user/getByPhone',
    method: 'post',
    params: data
  })
}

export function createUser(data) {
  return request({
    url: '/user/add',
    method: 'post',
    data
  })
}

export function updateUser(data) {
  return request({
    url: '/user/update',
    method: 'post',
    data
  })
}

export function deleteUser(data) {
  return request({
    url: '/user/delete',
    method: 'post',
    data
  })
}

export function deleteAllUser(data) {
  return request({
    url: '/user/deleteAll',
    method: 'post',
    timeout: 0,
    data
  })
}

export function listTerminalByUserId(data) {
  return request({
    url: '/user/listTerminalByUserId',
    method: 'post',
    params: data
  })
}

export function getUserTree(id) {
  return request({
    url: '/user/listTree/' + (!id ? -1 : id),
    method: 'get'
  })
}
// 获取所有操作员节点（包含deleted的数据），不包含部门, key为部门ID,value为操作员树节点，minModifyVer为查询大于此值的数据
export function getAllUserNode(minModifyVer) {
  return request({
    url: '/user/listUserNode',
    method: 'get',
    params: { minModifyVer: minModifyVer === undefined || minModifyVer === null ? -1 : minModifyVer }
  })
}

// 获取完整操作员树
export function listUserTreeNode() {
  return request({
    url: '/user/listUserTreeNode',
    method: 'get'
  })
}

export function listUserByGroupId(groupId) {
  return request({
    url: '/user/listByGroupId/' + groupId,
    method: 'get'
  })
}

export function getUserNodeByTermId(termId) {
  return request({
    url: '/user/listNodeByTermId/' + termId,
    method: 'get'
  })
}

/**
 * 得到终端可以登录操作员树
 * @param data
 */
export function listTreeNodeByTermId(data) {
  return request({
    url: '/user/listTreeNodeByTermId',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getLockScreenVerify(data) {
  return request({
    url: '/terminal/getLockScreenVerify',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getOperatorUnlockVerify(data) {
  return request({
    url: '/user/getOperatorUnlockVerify',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/user/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}
export function getUserDetailById(id) {
  return request({
    url: '/user/getUserDetailById/' + id,
    method: 'get'
  })
}
export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/user/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/user/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function validUserIsAutoLoginUser(userId) {
  return request({
    url: '/user/validUserIsAutoLoginUser/' + userId,
    method: 'post'
  })
}

export function listUser(data) {
  return request({
    url: '/user/list',
    method: 'post',
    data
  })
}

export function updateValidTime(data) {
  return request({
    url: '/user/updateValidTime',
    method: 'post',
    data
  })
}

export function validUserForbidden(userId) {
  return request({
    url: '/user/validUserForbidden/' + userId,
    method: 'post'
  })
}

export function updateConfigById(data) {
  return request({
    url: '/user/updateById',
    method: 'post',
    data
  })
}
