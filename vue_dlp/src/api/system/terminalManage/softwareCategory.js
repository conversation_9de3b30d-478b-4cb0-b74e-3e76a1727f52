import request from '@/utils/request'

export function getByName(name) {
  return request.get('/softwareCategory/getByName?name=' + encodeURIComponent(name))
}

export function listCategories() {
  return request.get('/softwareCategory/list')
}

export function addCategory(data) {
  return request({
    url: '/softwareCategory/add',
    method: 'post',
    data
  })
}

export function updateCategory(data) {
  return request({
    url: '/softwareCategory/update',
    method: 'post',
    data
  })
}

export function deleteCategory(data) {
  return request({
    url: '/softwareCategory/delete',
    method: 'post',
    data
  })
}
