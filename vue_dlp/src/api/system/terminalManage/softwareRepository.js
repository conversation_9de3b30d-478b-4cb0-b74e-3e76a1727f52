import axios from 'axios'
import i18n from '@/lang'
import store from '@/store'
import request from '@/utils/request'

export function checkFile(data) {
  return request({
    url: '/softwareRepository/checkFile',
    method: 'post',
    data
  })
}

const chunkSize = 64 * 1024 * 1024

export function uploadChunk(file, md5, index, onProgress) {
  if (index >= file.size) {
    if ('function' === typeof onProgress) {
      onProgress(file.size, file.size)
    }
    return Promise.resolve()
  }
  let chunkEnd = index + chunkSize
  if (chunkEnd > file.size) {
    chunkEnd = file.size
  }
  const chunk = file.slice(index, chunkEnd)
  const source = axios.CancelToken.source()
  return request({
    url: '/softwareRepository/uploadChunk',
    cancelToken: source.token,
    method: 'post',
    headers: {
      'Content-Type': 'application/octet-stream',
      'X-File-Size': file.size,
      'X-File-Md5': md5,
      'X-Chunk-Index': index
    },
    data: chunk,
    timeout: 0,
    onUploadProgress(progressEvent) {
      if ('function' === typeof onProgress) {
        onProgress(index + progressEvent.loaded, file.size, source.cancel)
      }
    }
  }).then(() => {
    if ('function' === typeof onProgress && onProgress(index + chunk.size, file.size, () => {})) {
      return Promise.reject('canceled')
    }
    return uploadChunk(file, md5, index + chunk.size, onProgress)
  })
}

export function getZipEntries(md5) {
  return request.get('/softwareRepository/getZipEntries/' + md5)
}

export function getSoftwareProps(data) {
  return request({
    url: '/softwareRepository/getSoftwareProps',
    method: 'post',
    timeout: 0,
    data
  })
}

export function getPage(data) {
  return request({
    url: '/softwareRepository/getPage',
    method: 'post',
    data
  })
}

export function listByCategories(data) {
  return request({
    url: '/softwareRepository/listByCategories',
    method: 'post',
    data
  })
}

export function listNamesByIds(softIds) {
  if (!softIds || softIds.length === 0) {
    return Promise.resolve({})
  }
  // 去重
  const data = softIds.length > 1 ? softIds.filter((item, index, arr) => arr.indexOf(item, 0) === index) : softIds
  return request({
    url: '/softwareRepository/listNamesByIds',
    method: 'post',
    data
  })
}

export function listByIds(softIds) {
  if (!softIds || softIds.length === 0) {
    return Promise.resolve({})
  }
  // 去重
  const data = softIds.length > 1 ? softIds.filter((item, index, arr) => arr.indexOf(item, 0) === index) : softIds
  return request({
    url: '/softwareRepository/listByIds',
    method: 'post',
    data
  })
}

export function getByName(name) {
  return request.get('/softwareRepository/getByName?name=' + encodeURIComponent(name))
}

export function countByCategory(id) {
  return request.get('/softwareRepository/countByCategory?id=' + id)
}

export function batchUpdateCategory(categoryId, data) {
  return request({
    url: '/softwareRepository/batchUpdateCategory/' + categoryId,
    method: 'post',
    data
  }).then(respond => {
    store.dispatch('commonData/setSoftwareRepositoryVer')
    return respond
  })
}

export function addSoftware(data) {
  return request({
    url: '/softwareRepository/add',
    method: 'post',
    data
  }).then(respond => {
    store.dispatch('commonData/setSoftwareRepositoryVer')
    return respond
  })
}

export function reUploadFtp(data) {
  return request({
    url: '/softwareRepository/reUploadFtp',
    method: 'post',
    data
  })
}

export function updateSoftware(data) {
  return request({
    url: '/softwareRepository/update',
    method: 'post',
    data
  }).then(respond => {
    store.dispatch('commonData/setSoftwareRepositoryVer')
    return respond
  })
}

export function deleteSoftware(data) {
  return request({
    url: '/softwareRepository/delete',
    method: 'post',
    data
  }).then(respond => {
    store.dispatch('commonData/setSoftwareRepositoryVer')
    return respond
  })
}

export function getFileSuffix(filename) {
  if (!filename) {
    return ''
  }
  const index = filename.lastIndexOf('.')
  if (index < 0) {
    return ''
  }
  return filename.slice(index).toLowerCase()
}

export const supportedSuffixes = [
  { value: '.exe', archived: false },
  { value: '.msi', archived: false },
  { value: '.7z', archived: true },
  { value: '.zip', archived: true },
  { value: '.rar', archived: true },
  { value: '.iso', archived: true }
]

export const supportedPlatforms = [
  { value: 1, label: 'Windows', disabled: false },
  { value: 2, label: 'macOS', disabled: true },
  { value: 3, label: 'Linux', disabled: true }
]

export const softwareArchitectures = [
  { value: 1, label: 'x86', disabled: false },
  { value: 2, label: 'x64', disabled: false },
  { value: 4, label: 'Arm32', disabled: true },
  { value: 8, label: 'Arm64', disabled: false }
]

export const supportedOsVersions = [
  { value: 1, label: 'Windows XP' },
  { value: 2, label: 'Windows Server 2003' },
  { value: 3, label: 'Windows Vista' },
  { value: 4, label: 'Windows 7' },
  { value: 5, label: 'Windows Server 2008' },
  { value: 6, label: 'Windows 8' },
  { value: 7, label: 'Windows Server 2012' },
  { value: 8, label: 'Windows Server 2016' },
  { value: 9, label: 'Windows Server 2019' },
  { value: 10, label: 'Windows 10' },
  { value: 11, label: 'Windows 11' },
  { value: 12, label: 'Windows Server 2022' }
]

export const uploadSoftwareStatus = {
  0: i18n.t('pages.notUpload'), // 未上传
  1: i18n.t('pages.uploading'), // 上传中
  2: i18n.t('pages.uploaded'), // 已上传
  // 区域块下载(屏幕录像文件)
  1018: i18n.t('pages.transferStatusDict8'), // 文件大小为0
  1019: i18n.t('pages.transferStatusDict9'), // 下载文件过大
  1020: i18n.t('pages.transferStatusDict10'), // 偏移量大于等于服务器文件大小
  // 服务器错误
  1021: i18n.t('pages.transferStatusDict11'), // 数据库错误
  1022: i18n.t('pages.transferStatusDict12'), // 上传文件不存在
  1023: i18n.t('pages.transferStatusDict13'), // 读取本地文件失败
  1024: i18n.t('pages.transferStatusDict14'), // 文件无法访问
  1025: i18n.t('pages.transferStatusDict15'), // 文件打开失败
  1026: i18n.t('pages.transferStatusDict16'), // 设置文件偏移失败
  1027: i18n.t('pages.transferStatusDict17'), // Ftp用户密码错误
  // 服务器传输错误
  1028: i18n.t('pages.transferStatusDict18'), // 连接文件服务器失败
  1029: i18n.t('pages.transferStatusDict19'), // 服务器数据库错误
  1030: i18n.t('pages.transferStatusDict20'), // 网络配置信息不合法
  1031: i18n.t('pages.transferStatusDict21'), // Ftp传输失败
  1032: i18n.t('pages.transferStatusDict22'), // FtpStream失败
  1033: i18n.t('pages.transferStatusDict23'), // 文件服务器没有可用的磁盘
  1034: i18n.t('pages.transferStatusDict24'), // 文件服务器的磁盘空间不足
  1035: i18n.t('pages.transferStatusDict25'), // 文件服务器文件不存在
  1036: i18n.t('pages.transferStatusDict26'), // 文件服务器文件无法访问
  1037: i18n.t('pages.transferStatusDict27'), // 设置文件服务器文件偏移失败
  1046: i18n.t('pages.transferStatusDict33'), // 服务器暂无空闲数据端口
  // 下载错误
  1038: i18n.t('pages.transferStatusDict28'), // 文件写入失败
  1039: i18n.t('pages.transferStatusDict29'), // 没有可下载的内容
  1040: i18n.t('pages.transferStatusDict30'), // 本地文件创建打开失败
  1041: i18n.t('pages.transferStatusDict31'), // 服务器文件不完整
  // 其它异常
  1043: i18n.t('pages.transferStatusDict32'), // 其他异常错误
  1044: i18n.t('exception.noFoundFileServer'), // 未找到文件对应服务器错误
  1045: i18n.t('text.requestTimeout'), // 超时错误
  9999: i18n.t('pages.transferStatusDict34') // 执行成功
}
