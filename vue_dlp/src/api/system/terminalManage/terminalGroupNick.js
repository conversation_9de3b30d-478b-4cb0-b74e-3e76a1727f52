import request from '@/utils/request'

export function getTerminalGroupNickNamePage(data) {
  return request({
    url: '/terminalGroupName/getPage',
    method: 'post',
    data
  })
}
export function getTermGroupNickNameToSncPage(data) {
  return request({
    url: '/terminalGroupName/getToSncPage',
    method: 'post',
    data
  })
}
export function addTerminalGroupNickName(data) {
  return request({
    url: '/terminalGroupName/add',
    method: 'post',
    data
  })
}
export function deleteTerminalGroupNickName(data) {
  return request({
    url: '/terminalGroupName/delete',
    method: 'post',
    data
  })
}
export function deleteTerminalGroupNickNameV2(data) {
  return request({
    url: '/terminalGroupName/deleteV2',
    method: 'post',
    data
  })
}
export function deleteAllTerminalGroupNickName(data) {
  return request({
    url: '/terminalGroupName/deleteAll',
    method: 'post',
    data
  })
}
export function startCollectTerminal(data) {
  return request({
    url: '/terminalGroupName/startCollectTerminal',
    method: 'post',
    data
  })
}
export function addStrategy(data) {
  return request({
    url: '/terminalGroupName/add',
    method: 'post',
    data
  })
}
export function restartCollectTerminal(data) {
  return request({
    url: '/terminalGroupName/restartCollectTerminal',
    method: 'post',
    data
  })
}
export function updateGroupNickName(data) {
  return request({
    url: '/terminalGroupName/updateGroupNickName',
    method: 'post',
    data
  })
}
export function synGroupNickName(data) {
  return request({
    url: '/terminalGroupName/synGroupNickName',
    method: 'post',
    data
  })
}
export function getTerminalStatus(data) {
  return request({
    url: '/terminalGroupName/getTerminalStatus',
    method: 'post',
    data
  })
}
export function getTerminalStatusV2(data) {
  return request({
    url: '/terminalGroupName/getTerminalStatusV2',
    method: 'post',
    data
  })
}
export function getActiveTermPage(data) {
  return request({
    url: '/terminalGroupName/getActiveTermPage',
    method: 'post',
    data
  })
}
export function getActiveTermPageV2(data) {
  return request({
    url: '/terminalGroupName/getActiveTermPageV2',
    method: 'post',
    data
  })
}
