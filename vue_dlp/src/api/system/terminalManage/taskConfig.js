import request from '@/utils/request'
import qs from 'qs'

export function checkFile(data) {
  return request({
    url: '/taskDistribute/checkFile',
    method: 'post',
    data
  })
}

export function getFileProps(data) {
  return request({
    url: '/taskDistribute/getFileProps',
    method: 'post',
    data
  })
}

export function createStrategy(data) {
  return request({
    url: '/taskDistribute/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/taskDistribute/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/taskDistribute/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function endTask(data) {
  return request({
    url: '/taskDistribute/endTask',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getStategyPage(data) {
  return request({
    url: '/taskDistribute/getPage',
    method: 'post',
    data
  })
}

export function reUploadFtp(data) {
  return request({
    url: '/taskDistribute/reUploadFtp',
    method: 'post',
    data
  })
}

export function getFileSuffix(filename) {
  if (!filename) {
    return ''
  }
  const index = filename.lastIndexOf('.')
  if (index < 0) {
    return ''
  }
  return filename.slice(index).toLowerCase()
}

export const supportedSuffixes = [
  { value: '.bat', archived: false, type: 2 },
  { value: '.exe', archived: false, type: 2 },
  { value: '.exe', archived: false, type: 1 },
  { value: '.msi', archived: false, type: 1 },
  { value: '.7z', archived: true, type: 1 },
  { value: '.zip', archived: true, type: 1 },
  { value: '.rar', archived: true, type: 1 },
  { value: '.iso', archived: true, type: 1 }
]

export const supportedPlatforms = [
  { value: 1, label: 'Windows', disabled: false },
  { value: 2, label: 'macOS', disabled: true },
  { value: 3, label: 'Linux', disabled: true }
]

export const architectures = [
  { value: 1, label: 'x86', disabled: false },
  { value: 2, label: 'x64', disabled: false },
  { value: 8, label: 'Arm64', disabled: false }
]

export const supportedOsVersions = [
  { value: 1, label: 'Windows XP' },
  { value: 2, label: 'Windows Server 2003' },
  { value: 3, label: 'Windows Vista' },
  { value: 4, label: 'Windows 7' },
  { value: 5, label: 'Windows Server 2008' },
  { value: 6, label: 'Windows 8' },
  { value: 7, label: 'Windows Server 2012' },
  { value: 8, label: 'Windows Server 2016' },
  { value: 9, label: 'Windows Server 2019' },
  { value: 10, label: 'Windows 10' },
  { value: 11, label: 'Windows 11' },
  { value: 12, label: 'Windows Server 2022' }
]
