import request from '@/utils/request'

export function getGroupTreeNode() {
  return request({
    url: '/userRole/listGroupTree',
    method: 'get'
  })
}

export function getUserRoleTreeNode() {
  return request({
    url: '/userRole/listTree',
    method: 'get'
  })
}

export function getUserRolePage(data) {
  return request({
    url: '/userRole/getPage',
    method: 'post',
    data
  })
}

export function createUserRole(data) {
  return request({
    url: '/userRole/add',
    method: 'post',
    data
  })
}

export function createUserRoleGroup(data) {
  return request({
    url: '/userRole/addGroup',
    method: 'post',
    data
  })
}

export function updateUserRole(data) {
  return request({
    url: '/userRole/update',
    method: 'post',
    data
  })
}

export function updateUserRoleGroup(data) {
  return request({
    url: '/userRole/updateGroup',
    method: 'post',
    data
  })
}

export function deleteUserRole(data) {
  return request({
    url: '/userRole/delete',
    method: 'post',
    data
  })
}

export function deleteUserRoleGroup(data) {
  return request({
    url: '/userRole/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getUserRoleGroupByName(data) {
  return request({
    url: '/userRole/getGroupByName',
    method: 'post',
    params: data
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/userRole/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function moveGroup(data) {
  return request({
    url: '/userRole/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/userRole/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/userRole/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/userRole/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/userRole/moveGroupToOther',
    method: 'post',
    data
  })
}
export function getRoleByName(data) {
  return request({
    url: '/userRole/getRoleByName',
    method: 'post',
    params: data
  })
}

export function listUsersByRoleId(data) {
  return request({
    url: '/userRole/listUsersByRoleId',
    method: 'post',
    params: data
  })
}

export function checkOnlyBindTheRole(data) {
  return request({
    url: '/userRole/checkOnlyBindTheRole',
    method: 'post',
    data
  })
}

export function updateRelatedUsers(data) {
  return request({
    url: '/userRole/updateRelatedUsers',
    method: 'post',
    data
  })
}

export function insertDefaultOperatorRole(data) {
  return request({
    url: '/userRole/insertDefaultOperatorRole',
    method: 'post',
    data
  })
}
