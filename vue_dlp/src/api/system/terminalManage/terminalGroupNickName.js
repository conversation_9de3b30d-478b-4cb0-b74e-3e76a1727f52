import request from '@/utils/request'

export function getTerminalGroupNickNamePage(data) {
  return request({
    url: '/terminalGroupNickName/getPage',
    method: 'post',
    data
  })
}
export function addTerminalGroupNickName(data) {
  return request({
    url: '/terminalGroupNickName/add',
    method: 'post',
    data
  })
}
export function deleteTerminalGroupNickName(data) {
  return request({
    url: '/terminalGroupNickName/delete',
    method: 'post',
    data
  })
}
export function deleteAllTerminalGroupNickName(data) {
  return request({
    url: '/terminalGroupNickName/deleteAll',
    method: 'post',
    data
  })
}
export function startCollectTerminal(data) {
  return request({
    url: '/terminalGroupNickName/startCollectTerminal',
    method: 'post',
    data
  })
}
export function restartCollectTerminal(data) {
  return request({
    url: '/terminalGroupNickName/restartCollectTerminal',
    method: 'post',
    data
  })
}
export function updateGroupNickName(data) {
  return request({
    url: '/terminalGroupNickName/updateGroupNickName',
    method: 'post',
    data
  })
}
export function synGroupNickName(data) {
  return request({
    url: '/terminalGroupNickName/synGroupNickName',
    method: 'post',
    data
  })
}
export function isExistOffline(data) {
  return request({
    url: '/terminalGroupNickName/isExistOffline',
    method: 'post',
    data
  })
}
