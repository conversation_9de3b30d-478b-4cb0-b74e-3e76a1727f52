import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper';

export function getPage(data) {
  return request({
    url: '/log/termSecurityDetection/getPage',
    method: 'post',
    data
  })
}

export function getLastRecordPage(data) {
  return request({
    url: '/log/termSecurityDetection/getLastRecordPage',
    method: 'post',
    data
  })
}

export function exportTermSecurityDetectionLog(data) {
  return request({
    url: '/log/termSecurityDetection/export',
    method: 'post',
    data
  })
}

export function exportTermSecurityDetectionLogDetails(data, opts) {
  return fetchFile({
    ...opts,
    url: '/log/termSecurityDetection/exportDetails',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function deleteLog(data) {
  return request({
    url: '/log/termSecurityDetection/delete',
    method: 'post',
    data
  })
}

export function getById(data) {
  return request({
    url: '/log/termSecurityDetection/getById',
    method: 'post',
    data
  })
}
/** 根据Id获取终端检测状态 **/
export function getLastById(data) {
  return request({
    url: '/log/termSecurityDetection/getLastById',
    method: 'post',
    data
  })
}

export function getStatisticalResult(data) {
  return request({
    url: '/log/termSecurityDetection/getStatisticalResult',
    method: 'get',
    data
  })
}

