import request from '@/utils/request'
import qs from 'qs'
import i18n from '@/lang'
import { fetchFile } from '@/utils/download/helper';

export function listModule() {
  return request({
    url: '/module/list',
    method: 'get'
  })
}

export function getModuleInfoByProdId(prodId) {
  return request({
    url: '/module/getSaleModuleInfo/' + prodId,
    method: 'get'
  })
}

export function existSaleModule(saleModuleId) {
  return request({
    url: '/module/existSaleModule/' + saleModuleId,
    method: 'get'
  })
}

export function listSaleModuleId() {
  return request({
    url: '/module/listSaleModuleId',
    method: 'get'
  })
}

export function listTerminalModule(data) {
  return request({
    url: '/module/listTerminalModule',
    method: 'post',
    data
  })
}

export function notifyCountModule(data) {
  return request({
    url: '/module/notify',
    method: 'post',
    params: data
  })
}

export function saveModule(data) {
  return request({
    url: '/module/update',
    method: 'post',
    data
  })
}

export function enableCtrlTerm(moduleKey, termId, notSupportOsTypes, funcReleaseVer) {
  const data = { moduleKey: moduleKey, objectId: termId, notSupportOsTypes: notSupportOsTypes || null, funcReleaseVer: funcReleaseVer }
  return request({
    url: '/module/enableCtrlTerm',
    method: 'post',
    data
  })
}

export function enableCtrlMultipleTerm(moduleKey, termIds, notSupportOsTypes, funcReleaseVer) {
  const data = { moduleKey: moduleKey, termIds: termIds, notSupportOsTypes: notSupportOsTypes || null, funcReleaseVer: funcReleaseVer }
  return request({
    url: '/module/enableCtrlMultipleTerm',
    method: 'post',
    data
  })
}

export function ctrlErrorMap(code) {
  return {
    '-1': i18n.t('pages.termNotOnline'),
    '-2': i18n.t('pages.termTypeNotSupport'),
    '0': i18n.t('pages.termNotUsedModule'),
    '-3': i18n.t('pages.termVersionNotSupport')
  }[code]
}

export function upload(data) {
  return request.post('/unregisteredModule/uploadFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function updateModule(data) {
  return request({
    url: '/unregisteredModule/updateModule',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getFilterSalesModule() {
  return request('/module/filterSalesModule')
}

export function enableCtrlTermNoIncludeOnline(moduleKey, termId, notSupportOsTypes) {
  const data = { moduleKey: moduleKey, objectId: termId, notSupportOsTypes: notSupportOsTypes || null }
  return request({
    url: '/module/enableCtrlTermNoIncludeOnline',
    method: 'post',
    data
  })
}

export function exportData(data, opts) {
  return fetchFile({
    ...opts,
    url: '/module/exportData',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function listTermOsType(data) {
  return request({
    url: '/module/listTermOsType',
    method: 'post',
    data
  })
}

export function saveModuleAllocation(data) {
  return request({
    url: '/module/allocation',
    method: 'post',
    data
  })
}
