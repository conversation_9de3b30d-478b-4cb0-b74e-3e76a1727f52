import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getTerminalPage(data) {
  return request({
    url: '/terminal/getPage',
    method: 'post',
    timeout: 90000,
    data
  })
}
export function getTerminalPageByWaterCode(data) {
  return request({
    url: '/terminal/getPageByWaterCode',
    method: 'post',
    params: data
  })
}

export function getTermById(id) {
  return request({
    url: '/terminal/get/' + (!id ? -1 : id),
    method: 'get'
  })
}

// 参数 data: { ids："1，2" }
export function listTerminal(data) {
  return request({
    url: '/terminal/list',
    method: 'post',
    data
  })
}

export function fetchDetail(id) {
  return request({
    url: '/terminal/get/' + id,
    method: 'get'
  })
}

export function addTerminal(data) {
  return request({
    url: '/terminal/add',
    method: 'post',
    timeout: 120000,
    data
  })
}

export function updateTerminal(data) {
  return request({
    url: '/terminal/update',
    method: 'post',
    data
  })
}

export function deleteTerminal(data) {
  return request({
    url: '/terminal/deleteById',
    method: 'post',
    params: data
  })
}

// 获取所有终端节点（包含deleted的数据），不包含部门, key为部门ID,value为终端树节点，minModifyVer为查询大于此值的数据
export function getTermNodeMap(minModifyVer) {
  return request({
    url: '/terminal/listTermNode',
    method: 'get',
    params: { minModifyVer: minModifyVer === undefined || minModifyVer === null ? -1 : minModifyVer }
  })
}

// 获取终端树, data控制是否显示回收站分组 格式:{ recycle: 0/1 } 0:不显示，1:显示
export function listTermTreeNode(data) {
  return request({
    url: '/terminal/listTermTreeNode/',
    method: 'get',
    params: data
  })
}

// 获取完整终端树, data控制是否显示回收站分组 格式:{ recycle: 0/1 } 0:不显示，1:显示
export function listCompleteTermTreeNode(data) {
  return request({
    url: '/terminal/listCompleteTermTreeNode/',
    method: 'get',
    params: data
  })
}

export function getRefreshTermNodeMap() {
  return request({
    url: '/terminal/getRefreshTermNodeMap',
    method: 'get'
  })
}

export function getTerminalTree(id) {
  return request({
    url: '/terminal/listTree/' + (!id ? -1 : id),
    method: 'get'
  })
}

export function getDeactiveTerminalTreeNode() {
  return request({
    url: '/terminal/listDeactiveTerminalTree',
    method: 'get'
  })
}

export function listTerminalByGroupId(groupId) {
  return request({
    url: '/terminal/listByGroupId/' + groupId,
    method: 'get'
  })
}
export function listTerminalByGroupIdOnly(groupId) {
  return request({
    url: '/terminal/listByGroupIdOnly/' + groupId,
    method: 'get'
  })
}

export function remoteControl(data) {
  return request({
    url: '/terminal/remoteControl',
    method: 'post',
    data
  })
}

export function remoteControlAll(data) {
  return request({
    url: '/terminal/remoteControlAll',
    method: 'post',
    data
  })
}

export function getAutoLoginTerminalTree(id) {
  return request({
    url: '/terminal/listAutoLoginTerminalTreeByUserId/' + id,
    method: 'get'
  })
}

export function getAutoLoginTerminalListByUserIds(data) {
  return request({
    url: '/terminal/listAutoLoginTerminalListByUserId',
    method: 'post',
    data
  })
}

export function getBindedTerminalTree(id) {
  return request({
    url: '/terminal/listBindedTerminalTreeByUserId/' + id,
    method: 'get'
  })
}

export function getBindedUserIdExceptTermId(id) {
  return request({
    url: '/terminal/listBindedUserIdExceptTermId/' + id,
    method: 'get'
  })
}

export function updateAutoLoginUser(data) {
  return request({
    url: '/terminal/addAutoLoginUser',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function bindUser(data) {
  return request({
    url: '/terminal/bindUser',
    method: 'post',
    data
  })
}

export function updateLoginMode(data) {
  return request({
    url: '/terminal/updateLoginMode',
    method: 'post',
    data
  })
}

export function getTerminalStatus(minModifyVer) {
  return request({
    url: '/terminal/getTerminalStatus',
    method: 'post',
    params: { minModifyVer: minModifyVer === undefined ? -1 : minModifyVer }
  })
}

export function getUserStatus() {
  return request({
    url: '/terminal/getUserStatus',
    method: 'post'
  })
}

export function uninstallTerminal(data) {
  return request({
    url: '/terminal/uninstall',
    method: 'post',
    data
  })
}

export function uninstallAllTerminal(data) {
  return request({
    url: '/terminal/uninstallAll',
    method: 'post',
    data
  })
}

export function updateAssignServer(data) {
  return request({
    url: '/terminal/updateAssignServer',
    method: 'post',
    data
  })
}

export function updateAllAssignServer(data) {
  return request({
    url: '/terminal/updateAllAssignServer',
    method: 'post',
    data
  })
}

export function getBossCode() {
  return request({
    url: '/terminalBoss/get',
    method: 'get'
  })
}
export function addBossCode(data) {
  return request({
    url: '/terminalBoss/add',
    method: 'post',
    data
  })
}
export function deleteBossCode(data) {
  return request({
    url: '/terminalBoss/delete',
    method: 'post',
    params: data
  })
}
export function getBossCodePage(data) {
  return request({
    url: '/terminalBoss/getPage',
    method: 'post',
    data
  })
}
export function getTerminalByName(data) {
  return request({
    url: '/terminal/getByName/' + data,
    method: 'get'
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/terminal/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getTerminalOnlineNum() {
  return request({
    url: '/terminal/getOnlineNum',
    method: 'get'
  })
}

export function getTerminalOfflineWeekNum() {
  return request({
    url: '/terminal/getOfflineWeekNum',
    method: 'get'
  })
}

export function getTerminalOfflineMonthNum() {
  return request({
    url: '/terminal/getOfflineMonthNum',
    method: 'get'
  })
}

export function getUserOnlineNum() {
  return request({
    url: '/user/getOnlineNum',
    method: 'get'
  })
}

export function getTermUserSyncPage(data) {
  return request({
    url: '/terminal/getTermUserSyncPage',
    method: 'post',
    data
  })
}

export function updateTermUserSync(data) {
  return request({
    url: '/terminal/updateTermUserSync',
    method: 'post',
    data
  })
}

export function listByTerminalVo(data) {
  return request({
    url: '/terminal/listByTerminalVo',
    method: 'post',
    data
  })
}
export function getTerminalDetailById(id, term) {
  if (term) {
    term = JSON.stringify(term)
    term = encodeURIComponent(term)
  } else {
    term = ''
  }
  return request({
    headers: { term: term },
    url: '/terminal/getTerminalDetailById/' + id,
    method: 'get'
  })
}

export function getTermIdByVO(data) {
  return request({
    url: '/terminal/getTermIdByVO',
    method: 'post',
    data
  })
}

export function countUnderVersionTerminal(data) {
  return request({
    url: '/terminal/countUnderVersionTerminal',
    method: 'get',
    params: data
  })
}

export function getNeedReallocateTerm(data) {
  return request({
    url: '/terminal/getNeedReallocateTerm',
    method: 'post',
    data
  })
}

export function validPassword(data) {
  return request({
    url: '/terminal/validPassword',
    method: 'post',
    data
  })
}

export function needSyncTermUserSync(data) {
  return request({
    url: '/terminal/needSyncTermUserSync',
    method: 'post',
    data
  })
}

