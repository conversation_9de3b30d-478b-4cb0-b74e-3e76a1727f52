import request from '@/utils/request'
import qs from 'qs'

export function getStrategyPage(data) {
  return request({
    url: '/termSecurityDetection/getPage',
    method: 'post',
    data
  })
}

export function addStrategy(data) {
  return request({
    url: '/termSecurityDetection/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/termSecurityDetection/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/termSecurityDetection/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getByName(data) {
  return request(({
    url: '/termSecurityDetection/getByName',
    method: 'get',
    params: { name: data }
  }))
}

export function getById(id) {
  return request(({
    url: '/termSecurityDetection/getById/' + id,
    method: 'get'
  }))
}

export function isValidRegex(data) {
  return request(({
    url: '/termSecurityDetection/isValidRegex',
    method: 'get',
    params: { regular: data }
  }))
}

