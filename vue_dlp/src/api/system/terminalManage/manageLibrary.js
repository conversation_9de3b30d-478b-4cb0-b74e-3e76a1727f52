import request from '@/utils/request'
import qs from 'qs'
import Cookies from 'js-cookie';
import { fetchFile } from '@/utils/download/helper'

export function getCurrentDataBaseServerInfo() {
  return request({
    url: '/databaseBackupInfo/getCurrentDataBaseServerInfo',
    method: 'get'
  })
}

//  管理库备份
export function manageDataBaseBack(data) {
  return request({
    url: '/databaseBackupInfo/manageDataBaseBack',
    method: 'post',
    data
  })
}

/** 本地备份文件列表 **/
export function getLocalList(data) {
  return request({
    url: '/databaseBackupInfo/getLocalList',
    method: 'post',
    data
  })
}

/** 云平台备份文件列表 **/
export function getCloudList(data) {
  return request({
    url: '/databaseBackupInfo/getCloudList',
    method: 'get'
  })
}

/** 云平台备份加密文件下载 **/
export function downloadFile(data) {
  return request({
    url: '/databaseBackupInfo/downloadFile',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data: qs.stringify(data)
  })
}

/** 云平台备份加密文件下载 **/
export function downloadProclaimedInWritingFile(data) {
  return request({
    url: '/databaseBackupInfo/downloadProclaimedInWritingFile',
    method: 'post',
    responseType: 'blob',
    timeout: 0,
    data: qs.stringify(data)
  })
}

/** 判断数据库备份文件恢复 **/
export function pretreatmentRecover(data) {
  return request({
    url: '/databaseBackupInfo/pretreatmentRecover',
    method: 'get'
  })
}

/** 校验安全管理员和审计管理员密码 **/
export function backupRecoverByPassword(data) {
  return request({
    url: '/databaseBackupInfo/backupRecoverByPassword',
    method: 'post',
    data
  })
}

/** 备份恢复（无需密码） **/
export function backupRecover(data) {
  return request({
    url: '/databaseBackupInfo/backupRecover',
    method: 'post',
    data
  })
}

/** 测试连接 **/
export function testConnection(data) {
  return request({
    url: '/databaseBackupInfo/testConnection',
    method: 'post',
    timeout: 600000,
    data
  })
}

/** 获取数据库配置本地备份地址 **/
export function getLocalBackupPath(data) {
  return request({
    url: '/databaseBackupInfo/getLocalBackupPath',
    method: 'get'
  })
}

/** 获取数据库恢复状态 **/
export function getBackupRecovering(type) {
  return request({
    url: '/databaseBackupInfo/getBackupRecovering',
    method: 'get',
    timeout: 10000,
    params: { type: type }
  })
}

/** 备份文件解密 */
export function decodeBackupFile(data, opts) {
  return fetchFile({
    ...opts,
    url: '/databaseBackupInfo/fileDecode',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data,
    timeout: 0,
    responseType: 'blob'
  })
}

/** 控制台向引擎服务发送确认通知 **/
export function confirmRecover(data) {
  return request({
    url: '/databaseBackupInfo/confirmRecover',
    method: 'get',
    params: data
  })
}

/** 控制台向引擎服务发送恢复完成确认通知 **/
export function confirmRecoverFinish(data) {
  return request({
    url: '/databaseBackupInfo/confirmRecoverFinish',
    method: 'get',
    params: data
  })
}

/** 测试获取dlp系统版本号 **/
export function testGetBackupFileSystemVersion() {
  return request({
    url: '/databaseBackupInfo/testGetBackupFileSystemVersion',
    method: 'get'
  })
}

/** 重启服务 **/
export function restartApp(data) {
  return request({
    url: '/databaseBackupInfo/restartApp',
    method: 'get',
    params: data
  })
}

//  清空恢复状态
export function clearStatusByFailed(data) {
  return request({
    url: '/databaseBackupInfo/clearStatusByFailed',
    method: 'post'
  })
}

//  设置恢复状态
export function setIsRecovering(status) {
  if (status !== undefined && status !== null) {
    const seconds = 300
    const expires = new Date(new Date() * 1 + seconds * 1000);
    //  设置5分钟有效期
    Cookies.set('isRecovering', status, { expires: expires })
  }
}
//  删除恢复状态
export function removeIsRecovering() {
  Cookies.remove('isRecovering');
}
//  获取恢复状态
export function getIsRecovering() {
  return Cookies.get('isRecovering');
}

//  保存提示语
export function saveMessage(data) {
  if (data !== undefined && data !== null) {
    const seconds = 300
    const expires = new Date(new Date() * 1 + seconds * 1000);
    //  设置5分钟有效期
    return Cookies.set('recover_message', data, { expires: expires })
  }
}

//  获取提示语
export function getMessage() {
  return Cookies.get('recover_message');
}

export function removeMessage() {
  return Cookies.remove('recover_message');
}
//  保存进度
export function saveProcess(data) {
  if (data !== undefined && data !== null) {
    const seconds = 600
    const expires = new Date(new Date() * 1 + seconds * 1000);
    return Cookies.set('recover_process', data, expires);
  }
}

//  获取进度
export function getProcess() {
  const process = Cookies.get('recover_process');
  return process === undefined || process === null ? 0 : parseInt((process + ''));
}
//  移除进度
export function removeProcess() {
  return Cookies.remove('recover_process');
}

