import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/appOpenSuffix/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/appOpenSuffix/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/appOpenSuffix/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/appOpenSuffix/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/appOpenSuffix/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/appOpenSuffix/delete',
    method: 'post',
    params: data
  })
}
