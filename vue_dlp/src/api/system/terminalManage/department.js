import store from '@/store'
import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getDeptByName(data) {
  return request({
    url: '/department/getByName',
    method: 'post',
    params: data
  })
}

export function countByDept(data) {
  return request({
    url: '/department/countByDept',
    method: 'post',
    params: data
  })
}

export function getIdByDept(data) {
  return request({
    url: '/department/getNameById',
    method: 'post',
    params: data
  })
}

export function createDept(data) {
  return request({
    url: '/department/add',
    method: 'post',
    data
  })
}

export function updateDept(data) {
  return request({
    url: '/department/update',
    method: 'post',
    data
  })
}

export function deleteDept(data) {
  return request({
    url: '/department/delete',
    method: 'post',
    data
  })
}

export function getIds(data) {
  return request({
    url: '/department/getIds',
    method: 'post',
    data
  })
}

export function validateDelDept(data) {
  return request({
    url: '/department/validateDel',
    method: 'post',
    data
  })
}

export function getDeptTree() {
  return request({
    url: '/department/listTree',
    method: 'get'
  })
}

/**
 * 从缓存中获取部门树：如果缓存中不存在，则从后台获取
 * @returns {*}
 */
export function getDeptTreeFromCache() {
  const tree = store.getters.deptTree
  if (tree && tree.length > 0) {
    return new Promise((resolve, reject) => {
      resolve({
        code: 20000,
        data: JSON.parse(JSON.stringify(tree))
      })
    })
  }
  return getDeptTree()
}

export function getAllDeptTree() {
  return request({
    url: '/department/listAllTree',
    method: 'get'
  })
}

export function getDeptPage(data) {
  return request({
    url: '/department/getPage',
    method: 'post',
    data
  })
}

export function bindTerminal(data) {
  return request({
    url: '/terminal/updateGroup',
    method: 'post',
    data
  })
}
export function batchUpdateGroup(data) {
  return request({
    url: '/terminal/batchUpdateGroup',
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data) {
  return request({
    url: '/terminal/batchUpdateAllGroup',
    method: 'post',
    data
  })
}
export function bindUser(data) {
  return request({
    url: '/user/updateGroup',
    method: 'post',
    data
  })
}

export function moveDept(data) {
  return request({
    url: '/department/moveDept',
    method: 'post',
    data
  })
}

export function updateSortDept(data) {
  return request({
    url: '/department/sortDept',
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/department/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getDeptDetailById(id) {
  return request({
    url: '/department/getDeptDetailById/' + id,
    method: 'get'
  })
}

//  根据终端编号获取部门
export function listParentByTermId(id) {
  return request({
    url: '/department/listParentByTermId/' + id,
    method: 'get'
  })
}

export function getById(id) {
  return request({
    url: '/department/get/' + id,
    method: 'get'
  })
}
