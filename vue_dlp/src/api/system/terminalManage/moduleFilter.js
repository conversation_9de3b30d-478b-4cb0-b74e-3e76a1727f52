import request from '@/utils/request'

export function getPageModuleFilter(data) {
  return request({
    url: '/moduleFilter/getPageModuleFilter',
    method: 'post',
    data
  })
}

export function listExistSelfStgObjName(data) {
  return request({
    url: '/moduleFilter/listExistSelfStgObjName',
    method: 'post',
    data
  })
}

export function listSubModule() {
  return request({
    url: '/moduleFilter/list',
    method: 'get'
  })
}

export function createModuleFilter(data) {
  return request({
    url: '/moduleFilter/add',
    method: 'post',
    data
  })
}

export function updateModuleFilter(data) {
  return request({
    url: '/moduleFilter/update',
    method: 'post',
    data
  })
}

export function batchUpdateModuleFilter(data) {
  return request({
    url: '/moduleFilter/batchUpdate',
    method: 'post',
    data
  })
}

export function deleteModuleFilter(data) {
  return request({
    url: '/moduleFilter/delete',
    method: 'post',
    params: data
  })
}

