import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper';

export function fetchList(data) {
  return request({
    url: '/processInject/getPage',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/processInject/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/processInject/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/processInject/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/processInject/delete',
    method: 'post',
    params: data
  })
}

export function importFile(data) {
  return request({
    url: '/processInject/import',
    method: 'post',
    data
  })
}

export function exportFile(data, opts) {
  return fetchFile({
    ...opts,
    url: '/processInject/export',
    method: 'post',
    data,
    timeout: 0,
    responseType: 'blob'
  })
}
