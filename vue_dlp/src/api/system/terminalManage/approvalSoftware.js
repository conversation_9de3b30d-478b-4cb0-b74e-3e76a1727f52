import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/softwareApproval/getPage',
    method: 'post',
    data
  })
}
export function agree(data) {
  return request({
    url: '/softwareApproval/agree',
    method: 'post',
    data
  })
}
export function refuse(data) {
  return request({
    url: '/softwareApproval/refuse',
    method: 'post',
    data
  })
}

export function approval(data) {
  return request({
    url: '/softwareApproval/approval',
    method: 'post',
    data
  })
}
