import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/mobileOpenSuffix/getPage',
    method: 'post',
    data
  })
}
export function getStrategyList(data) {
  return request({
    url: '/mobileOpenSuffix/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/mobileOpenSuffix/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/mobileOpenSuffix/add',
    method: 'post',
    data
  })
}
export function updateStrategy(data) {
  return request({
    url: '/mobileOpenSuffix/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/mobileOpenSuffix/delete',
    method: 'post',
    params: data
  })
}
