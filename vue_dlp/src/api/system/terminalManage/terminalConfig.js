import request from '@/utils/request'

export function getConfigPage(data) {
  return request({
    url: '/terminalConfig/getPage',
    method: 'post',
    data
  })
}

export function getConfig(data) {
  return request({
    url: '/terminalConfig/get',
    method: 'post',
    data
  })
}

export function getConfigByName(data) {
  return request({
    url: '/terminalConfig/getByName',
    method: 'post',
    data
  })
}

export function addConfig(data) {
  return request({
    url: '/terminalConfig/add',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/terminalConfig/update',
    method: 'post',
    data
  })
}

export function deleteConfig(data) {
  return request({
    url: '/terminalConfig/delete',
    method: 'post',
    params: data
  })
}
