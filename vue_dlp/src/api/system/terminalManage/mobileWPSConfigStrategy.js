import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/mobileWPSConfigStrategy/getPage',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/mobileWPSConfigStrategy/getByName',
    method: 'post',
    params: data
  })
}

export function createStrategy(data) {
  return request({
    url: '/mobileWPSConfigStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/mobileWPSConfigStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/mobileWPSConfigStrategy/delete',
    method: 'post',
    params: data
  })
}
