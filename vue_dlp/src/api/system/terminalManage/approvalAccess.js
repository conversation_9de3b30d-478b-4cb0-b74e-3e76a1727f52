import request from '@/utils/request'

export function getApprovalInfoPage(data) {
  return request({
    url: '/approvalAccess/getPage',
    method: 'post',
    data
  })
}

export function approvalAccess(data) {
  return request({
    url: '/approvalAccess/approval',
    method: 'post',
    data
  })
}

export function deleteApprovalInfo(data) {
  return request({
    url: '/approvalAccess/deleteApprovalAccess',
    method: 'post',
    data
  })
}

export function getAccessConfig(data) {
  return request({
    url: '/approvalAccess/getAccessConfig',
    method: 'post',
    params: data
  })
}

export function getAccessConfigIntelList() {
  return request({
    url: '/approvalAccess/list',
    method: 'get'
  })
}

export function addAccessConfigIntel(data) {
  return request({
    url: '/approvalAccess/addAccessConfigIntel',
    method: 'post',
    data
  })
}

export function updateAccessConfigIntel(data) {
  return request({
    url: '/approvalAccess/updateAccessConfigIntel',
    method: 'post',
    data
  })
}

export function deleteAccessConfigIntel(data) {
  return request({
    url: '/approvalAccess/deleteAccessConfigIntel',
    method: 'post',
    params: data
  })
}

export function addAccessConfig(data) {
  return request({
    url: '/approvalAccess/addAccessConfig',
    method: 'post',
    data
  })
}

export function updateAccessConfig(data) {
  return request({
    url: '/approvalAccess/updateAccessConfig',
    method: 'post',
    data
  })
}

export function batchApprovalNewTerm(data) {
  return request({
    url: '/approvalAccess/batchApprovalNewTerm',
    method: 'post',
    data
  })
}

export function getDlpVersionLimit() {
  return request({
    url: '/approvalAccess/getDlpVersionLimit',
    method: 'get'
  })
}

export function getTerminalAccessVersion(type) {
  return request({
    url: '/approvalAccess/getTerminalAccessVersion/' + type,
    method: 'get'
  })
}

export function updateDlpVersionLimit(data) {
  return request({
    url: '/approvalAccess/updateDlpVersionLimit',
    method: 'post',
    data: { 'lowestVersion': data }
  })
}

export function updateConfigById(data) {
  return request({
    url: '/approvalAccess/updateConfigById',
    method: 'post',
    data
  })
}
