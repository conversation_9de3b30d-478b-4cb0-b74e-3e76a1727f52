import request from '@/utils/request'

export function getLogPage(data) {
  return request({
    url: '/log/appReportEx/getPage',
    method: 'post',
    data
  })
}

export function getDetailPage(data) {
  return request({
    url: '/log/appReportEx/getDetailPage',
    method: 'post',
    data
  })
}

export function deleteLog(data, headers) {
  return request({
    url: '/log/appReportEx/delete',
    method: 'post',
    headers,
    data
  })
}

export function exportAppReportEx(data) {
  return request({
    url: '/log/appReportEx/export',
    method: 'post',
    data
  })
}

