import request from '@/utils/request'
import qs from 'qs'
import { listNamesByIds } from '@/api/system/terminalManage/softwareRepository'

export function getStrategyPage(data) {
  return request({
    url: '/softwareDownloadStrategy/getPage',
    method: 'post',
    data
  }).then(res => {
    const softIds = res.data.items
      .flatMap(item => item.softwareList || [])
      .filter(item => !item.objType)
      .map(item => item.id)
    if (softIds.length === 0) {
      return res
    }
    return listNamesByIds(softIds).then(respond => {
      const idNameMap = respond.data
      res.data.items.forEach(item => {
        item.softwareList = item.softwareList.map(software => {
          software.name = software.objType ? '-' : idNameMap[software.id]
          return software
        }).filter(software => !!software.name)
      })
      return res
    })
  })
}

export function countStgByCategory(categoryId) {
  return request.get('/softwareDownloadStrategy/countStgByCategory?categoryId=' + categoryId)
}

export function getStgNamesByCategoryId(categoryId) {
  return request.get('/softwareDownloadStrategy/getStgNamesByCategoryId?categoryId=' + categoryId)
}

export function getStrategyByName(data) {
  return request({
    url: '/softwareDownloadStrategy/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createStrategy(data) {
  return request({
    url: '/softwareDownloadStrategy/add',
    method: 'post',
    data
  })
}

export function updateStrategy(data) {
  return request({
    url: '/softwareDownloadStrategy/update',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/softwareDownloadStrategy/delete',
    method: 'post',
    params: data
  })
}
