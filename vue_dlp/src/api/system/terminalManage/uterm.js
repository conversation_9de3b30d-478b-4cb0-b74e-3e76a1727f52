import request from '@/utils/request'
import Cookies from 'js-cookie'
import { fetchFile } from '@/utils/download/helper'

export function getPluginVersion() {
  return request.get('/uterm/plugin/version').then(res => res.data)
}

export function downloadPlugin(data, opts) {
  return fetchFile({
    ...opts,
    url: '/uterm/plugin/download',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getUTermVersion() {
  return request.get('/uterm/package/version').then(res => res.data)
}

export function uploadUTermPkg(file, onUploadProgress) {
  const data = new FormData()
  data.append('file', file)
  return request({
    url: 'uterm/package/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 0,
    onUploadProgress,
    data
  }).then(respond => respond.data)
}

export function getServerTime() {
  return request.get('/uterm/time').then(res => res.data)
}

export function generateUsbGuid(terminalId, usbTermGuid) {
  return request.get('/uterm/gen-guid', { params: { terminalId, usbTermGuid }})
}

export function getUTermByGuid(guid) {
  return request.get('/uterm/guid/' + guid)
}

export function makeUTerm(temp) {
  const taskId = buildTaskId()
  const data = { ...temp, taskId }
  return request({
    url: '/uterm/make',
    method: 'post',
    data
  })
}

export function preUpgradeUTermPkg() {
  return request({
    url: '/uterm/pre-upgrade',
    method: 'post',
    params: { taskId: buildTaskId() }
  })
}

export function upgradeUTermPkg(data) {
  return request({
    url: '/uterm/upgrade',
    method: 'post',
    data
  })
}

export function deleteUTerm(data) {
  return request({
    url: '/uterm/delete/usb',
    method: 'post',
    data
  })
}

export function getUninstallCode(terminalId) {
  return request.get('/uterm/delete/code?terminalId=' + terminalId)
}

export function deleteUTermByCode(data) {
  return request({
    url: '/uterm/delete/code',
    method: 'post',
    data
  })
}

export function prepareStrategy(terminalId) {
  return request.get('/uterm/strategy/prepare?terminalId=' + terminalId)
}

export function updateStrategy(temp) {
  const taskId = buildTaskId()
  const data = { ...temp, taskId }
  return request({
    url: '/uterm/strategy/update',
    method: 'post',
    data
  })
}

export function genPasswordResetLicence(data, opts) {
  return fetchFile({
    ...opts,
    url: '/uterm/password/reset/licence',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function exportStrategy(data, opts) {
  return fetchFile({
    ...opts,
    url: '/uterm/strategy/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

/**
 * 根据加密类型、密钥类型解密
 * @param data
 * @returns {AxiosPromise}
 */
export function decodeInstallPkgJsonStr(data) {
  return request({
    url: '/uterm/decodeInstallPkgJsonStr',
    method: 'post',
    data
  })
}

function buildTaskId() {
  return encodeURIComponent(Cookies.get('sid') + '_' + Date.now())
}

export function compareVersion(ver1, ver2) {
  const verArr1 = (ver1 || '').split('.')
  const verArr2 = (ver2 || '').split('.')
  const len = Math.max(verArr1.length, verArr2.length)
  let compare = 0
  for (let i = 0; i < len; i++) {
    compare = (Number(verArr1[i]) || 0) - (Number(verArr2[i]) || 0)
    if (compare !== 0) {
      break
    }
  }
  return compare
}

export function buildDownloadUrl(taskId, stgFlag) {
  let url = location.origin
  const ctx = process.env.VUE_APP_BASE_API
  if (!ctx.startsWith('/')) {
    url += '/'
  }
  url += ctx
  if (!ctx.endsWith('/')) {
    url += '/'
  }
  url += 'uterm'
  if (stgFlag === 1) {
    url += '/strategy'
  } else {
    url += '/package'
  }
  url += '/download'
  if (taskId) {
    url += '?t=' + taskId
    if (stgFlag === 2) {
      url += '&s=1'
    }
  }
  return url
}
