import request from '@/utils/request'

export function getStrategyPage(data) {
  return request({
    url: '/sendCount/getPage',
    method: 'post',
    data
  })
}

export function deleteStrategy(data) {
  return request({
    url: '/sendCount/delete',
    method: 'post',
    params: data
  })
}

export function getDataByName(data) {
  return request({
    url: '/sendCount/getByName',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/sendCount/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/sendCount/update',
    method: 'post',
    data
  })
}
