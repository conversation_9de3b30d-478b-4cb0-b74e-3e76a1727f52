import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/monitorMailPortConfig/list',
    method: 'post',
    params: data
  })
}

export function createData(data) {
  return request({
    url: '/monitorMailPortConfig/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/monitorMailPortConfig/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/monitorMailPortConfig/delete',
    method: 'post',
    params: data
  })
}
