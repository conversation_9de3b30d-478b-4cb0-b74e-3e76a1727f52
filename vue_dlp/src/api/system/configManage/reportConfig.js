import request from '@/utils/request'

export function getAddress() {
  return request({
    url: '/reportServer/get',
    method: 'get'
  })
}

export function updateAddress(data) {
  return request({
    url: '/reportServer/update',
    method: 'post',
    timeout: 20000,
    data
  })
}

export function testConnect(query) {
  return request({
    url: '/reportServer/testConnect',
    method: 'get',
    params: query
  })
}

export function activeServer(data) {
  return request({
    url: '/reportServer/activeServer',
    method: 'post',
    data
  })
}

export function getActiveCode() {
  return request({
    url: '/reportServer/getActiveCode',
    method: 'post'
  })
}

