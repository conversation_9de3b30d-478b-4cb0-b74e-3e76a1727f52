import request from '@/utils/request'

export function cleanNow(cleanDuration) {
  return request({
    url: '/cleanUpConfig/cleanNow',
    method: 'post',
    data: `cleanDuration=${cleanDuration}`
  })
}

export function recoverTerms(data) {
  return request({
    url: '/cleanUpConfig/recoverTerms',
    method: 'post',
    data
  })
}

export function saveConfig(data) {
  return request({
    url: '/cleanUpConfig/saveConfig',
    method: 'post',
    data
  })
}

export function getConfig() {
  return request({
    url: '/cleanUpConfig/getConfig',
    method: 'get'
  })
}

export function getOfflineTerminals() {
  return request({
    url: '/cleanUpConfig/getOfflineTerminals',
    method: 'get'
  })
}

export function cleanOfflineTerm(data) {
  return request({
    url: '/cleanUpConfig/cleanTerm',
    method: 'post',
    data
  })
}

export function recoverOfflineTerm(data) {
  return request({
    url: '/cleanUpConfig/recoverTerm',
    method: 'post',
    data
  })
}
