import request from '@/utils/request'

export function listSyncSource(sourceType) {
  return request({
    url: '/syncSource/list/' + sourceType,
    method: 'get'
  })
}

export function listSources() {
  return request({
    url: '/syncSource/listSource',
    method: 'get'
  })
}

export function listSyncFieldMapper(sourceType, syncId) {
  return request({
    url: '/syncSource/listFieldMapper/' + sourceType + '/' + syncId,
    method: 'get'
  })
}

export function addSyncSource(data) {
  return request({
    url: '/syncSource/add',
    method: 'post',
    data
  })
}

export function updateSyncSource(data) {
  return request({
    url: '/syncSource/update',
    method: 'post',
    data
  })
}

export function updateSyncConfig(data) {
  return request({
    url: '/syncSource/updateSync',
    method: 'post',
    data
  })
}

export function getSyncConfig() {
  return request({
    url: '/syncSource/getSync',
    method: 'get'
  })
}

export function doSyncNow(sourceType, syncAll) {
  return request({
    url: '/syncSource/sync/' + syncAll + '/' + sourceType,
    method: 'get'
  })
}

/**
 * 根据sourceType以及部门id获取部门数（主要用于钉钉逐级异步加载数据时适用）
 * @param sourceType
 * @param syncId
 * @param deptId
 * @returns {*}
 */
export function getSyncGroupTree(sourceType, syncId, deptId) {
  return request({
    url: '/syncGroup/getSyncGroupTree/' + sourceType + '/' + syncId + '/' + deptId,
    method: 'get'
  })
}

/**
 * 根据sourceType获取所有部门树（3:IDM 4:企业微信 5:钉钉）
 * @param sourceType
 * @param syncId
 * @returns {*}
 */
export function getAllSyncGroupTree(sourceType, syncId) {
  return request({
    url: '/syncGroup/getAllSyncGroupTree/' + sourceType + (undefined === syncId ? '' : ('/' + syncId)),
    method: 'get'
  })
}

/**
 * 保存过滤树
 * @param data 树
 * @returns {*}
 */
export function saveSyncGroupFilter(data) {
  return request({
    url: '/syncGroupFilter/add',
    method: 'post',
    data
  })
}

export function getSyncGroupFilter(sourceType, syncId) {
  return request({
    url: '/syncGroupFilter/getSyncGroupFilter/' + sourceType + '/' + syncId,
    method: 'get'
  })
}

export function getSyncGroupFilterNode(id, syncId) {
  return request({
    url: '/syncGroupFilter/getSyncGroupFilterNode/' + id + '/' + syncId,
    method: 'get'
  })
}

export function getSyncConflict(data) {
  return request({
    url: '/syncConflict/list',
    method: 'post',
    data
  })
}

export function updateSyncConflict(data) {
  return request({
    url: '/syncConflict/update',
    method: 'post',
    data
  })
}

export function batchUpdateConflictData(data) {
  return request({
    url: '/syncConflict/batchUpdateConflictData',
    method: 'post',
    data
  })
}

export function autoConflictConfig(data) {
  return request({
    url: '/syncConflict/autoConflictConfig',
    method: 'post',
    data
  })
}

export function getAutoConflictConfig() {
  return request({
    url: '/syncConflict/getAutoConflictConfig',
    method: 'get'
  })
}

/**
 * 测试连接是否正常
 * @param data
 * @returns {*}
 */
export function testConnect(data) {
  return request({
    url: '/syncSource/testConnect',
    method: 'post',
    data
  })
}

/**
 * 根据sync_Id删除数据
 * @param data
 * @returns {*}
 */
export function deleteByIds(data) {
  return request({
    url: '/syncSource/deleteByIds',
    method: 'post',
    params: data
  })
}

export function deleteSourceAndAdByIds(data) {
  return request({
    url: '/syncSource/deleteSourceAndAdByIds',
    method: 'post',
    data
  })
}

export function uploadFile(data) {
  return request.post('/syncSource/uploadFile', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}

export function listSourceAndAd(data) {
  return request({
    url: '/syncSource/listSourceAndAd',
    method: 'post',
    data
  })
}

export function validConfig(sourceType) {
  return request({
    url: '/syncSource/validConfig/' + sourceType,
    method: 'get'
  })
}

/**
 * 检查是否是切换数据源
 * @param source
 * @returns {*}
 */
export function getCheckSwitchSource(source) {
  return request({
    url: '/syncSource/getCheckSwitchSource/' + source,
    method: 'get'
  })
}

/**
 * 获取切换的数据源操作员数据
 * @returns {*}
 * @param data
 */
export function getSyncUserTemp(data) {
  return request({
    url: '/syncSource/getSyncUserTemp',
    method: 'post',
    data
  })
}

/**
 * 下载第三方数据到临时表
 * @param source
 * @returns {*}
 */
export function downloadSyncTemp(source) {
  return request({
    url: '/syncSource/downloadSyncTemp/' + source,
    method: 'get'
  })
}

/**
 * 清除第三方临时表数据
 * @returns {*}
 */
export function clearSyncTemp() {
  return request({
    url: '/syncSource/clearSyncTemp',
    method: 'get'
  })
}

/**
 * 获取同步字段数据(根据数据源类型)
 * @param source
 * @returns {*}
 */
export function getSyncFieldData(source) {
  return request({
    url: '/syncSource/getSyncFieldData/' + source,
    method: 'get'
  })
}

/**
 * 获取进度
 * @returns {*}
 */
export function getPercent() {
  return request({
    url: '/syncSource/getPercent',
    method: 'get'
  })
}

export function getSyncStatusPage(data) {
  return request({
    url: '/syncLog/getPage',
    method: 'post',
    data
  })
}

export function getDetails(taskId) {
  return request({
    url: '/syncLog/getDetails/' + taskId,
    method: 'get'
  })
}

export function updateHighConfig(data) {
  return request({
    url: '/syncSource/updateHighConfig',
    method: 'post',
    data
  })
}

/**
 * 忽略同步数据
 * @param syncId
 * @param id
 * @returns {*}
 */
export function updateIgnoreData(syncId, id) {
  return request({
    url: '/syncSource/updateIgnoreData/' + syncId + '/' + id,
    method: 'get'
  })
}

/**
 * 取消忽略同步数据
 * @param syncId
 * @param id
 * @returns {*}
 */
export function cancelIgnoreData(syncId, id) {
  return request({
    url: '/syncSource/cancelIgnoreData/' + syncId + '/' + id,
    method: 'get'
  })
}

export function updateSwitchSyncTempData(selectSource) {
  return request({
    url: '/syncSource/updateSwitchSyncTempData/' + selectSource,
    method: 'get'
  })
}

/**
 * 获取同步告警配置
 * @param source
 * @returns {*}
 */
export function getSyncAlarmConfig() {
  return request({
    url: '/syncAlarm/getAlarm',
    method: 'get'
  })
}

export function updateSyncAlarmConfig(data) {
  return request({
    url: '/syncAlarm/updateAlarm',
    method: 'post',
    data
  })
}

/**
 * 获取IDM接口数据
 * @param data
 * @returns {*}
 */
export function getIdmData(data) {
  return request({
    url: '/syncSource/getIdmData',
    method: 'post',
    data
  })
}

export function checkPortExists(type, port) {
  return request({
    url: '/synExtApi/checkPort/' + type + '/' + port,
    method: 'get'
  })
}

export function getSyncSourceByName(data) {
  return request({
    url: '/syncSource/getByName',
    method: 'post',
    params: data
  })
}

