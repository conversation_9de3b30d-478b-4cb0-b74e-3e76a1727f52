import request from '@/utils/request'

export function getSyncExtApp(id) {
  return request({
    url: '/syncExt/apps/getDetail/' + id,
    method: 'get'
  })
}
export function getSyncExtAppBussIdMap() {
  return request({
    url: '/syncExt/apps/getBussIdMap',
    method: 'get'
  })
}

export function getSyncExtAppPage(data) {
  return request({
    url: '/syncExt/apps/list',
    method: 'post',
    data
  })
}

export function addSyncExtApp(data) {
  return request({
    url: '/syncExt/apps/add',
    method: 'post',
    data
  })
}

export function updateSyncExtApp(data) {
  return request({
    url: '/syncExt/apps/update',
    method: 'post',
    data
  })
}

export function deleteSyncExtApp(data) {
  return request({
    url: '/syncExt/apps/deleteByIds',
    method: 'post',
    params: data
  })
}

