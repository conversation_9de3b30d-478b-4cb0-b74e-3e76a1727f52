import request from '@/utils/request'

export function getEmailTemplateOptions() {
  return request({
    url: '/emailConfig/getEmailTemplateOptions',
    method: 'get'
  })
}

export function getTemplateBySubject(data) {
  return request({
    url: '/emailConfig/getBySubject',
    method: 'post',
    params: data
  })
}

export function createTemplate(data) {
  return request({
    url: '/emailConfig/add',
    method: 'post',
    data
  })
}

export function updateTemplate(data) {
  return request({
    url: '/emailConfig/update',
    method: 'post',
    data
  })
}

export function deleteTemplate(data) {
  return request({
    url: '/emailConfig/delete',
    method: 'post',
    params: data
  })
}

export function getTemplatePage(data) {
  return request({
    url: '/emailConfig/getPage',
    method: 'post',
    data
  })
}
