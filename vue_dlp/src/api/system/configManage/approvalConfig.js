import request from '@/utils/request'

export function getAddress() {
  return request({
    url: '/approvalConfig/getApprovalServer',
    method: 'get'
  })
}

export function updateAddress(data) {
  return request({
    url: '/approvalConfig/updateApprovalServer',
    method: 'post',
    timeout: 0,
    data
  })
}

export function testConnect(query) {
  return request({
    url: '/approvalConfig/testConnect',
    method: 'get',
    params: query
  })
}

export function getApprovalConfig() {
  return request({
    url: '/approvalConfig/get',
    method: 'get'
  })
}

export function updateApprovalConfig(data) {
  return request({
    url: '/approvalConfig/update',
    method: 'post',
    data
  })
}

export function allotBackupServer(data) {
  return request({
    url: '/approvalConfig/allotBackupServer',
    method: 'post',
    data: data
  })
}

export function bindServer(data) {
  return request({
    url: '/approvalConfig/bindServer',
    method: 'post',
    data: data
  })
}

export function getBindServer(devId) {
  return request({
    url: '/approvalConfig/getBindServer/' + devId,
    method: 'get'
  })
}
