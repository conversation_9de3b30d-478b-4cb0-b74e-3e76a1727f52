import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/addomain/list',
    method: 'post',
    data
  })
}

export function create(data) {
  return request({
    url: '/addomain/add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/addomain/update',
    method: 'post',
    data
  })
}

export function testConnect(data) {
  return request({
    url: '/addomain/testConnect',
    method: 'post',
    data
  })
}

export function deleteById(data) {
  return request({
    url: '/addomain/deleteById',
    method: 'post',
    params: data
  })
}

export function saveAdDomainFilter(data) {
  return request({
    url: '/addomain/saveAdDomainFilter',
    method: 'post',
    data
  })
}

export function getAdDomainFilterTree(data) {
  return request({
    url: '/addomain/getAdDomainFilterTree',
    method: 'post',
    params: data
  })
}

export function listByOptIds(data) {
  return request({
    url: '/sysOptions/listByOptIds',
    method: 'post',
    params: data
  })
}
export function saveSetting(data) {
  return request({
    url: '/sysOptions/save',
    method: 'post',
    data
  })
}
export function importData(data) {
  return request({
    url: '/addomain/import',
    method: 'post',
    data
  })
}

export function getAdDomainDeptTree(data) {
  return request({
    url: '/addomain/getAdDomainDeptTree',
    method: 'post',
    params: data
  })
}

export function updateHighConfig(data) {
  return request({
    url: '/addomain/updateHighConfig',
    method: 'post',
    data
  })
}
