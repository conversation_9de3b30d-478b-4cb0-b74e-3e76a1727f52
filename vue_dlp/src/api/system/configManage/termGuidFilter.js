import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/termGuidFilter/getPage',
    method: 'post',
    data
  })
}

export function createData(data) {
  return request({
    url: '/termGuidFilter/add',
    method: 'post',
    data
  })
}
export function deleteData(data) {
  return request({
    url: '/termGuidFilter/delete',
    method: 'post',
    params: data
  })
}

export function updateData(data) {
  return request({
    url: '/termGuidFilter/update',
    method: 'post',
    data
  })
}

export function getByGuid(data) {
  return request({
    url: '/termGuidFilter/getByGuid',
    method: 'get',
    params: data
  })
}
