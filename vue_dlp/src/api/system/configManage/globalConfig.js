import request from '@/utils/request'

export function uploadResourceBundle(data) {
  return request({
    url: '/globalConfig/uploadResourceBundle',
    method: 'post',
    headers: {
      'Content-Type': 'text/html'
    },
    data
  })
}

export function resolveResourceBundle(data) {
  return request({
    url: '/globalConfig/resolveResourceBundle',
    method: 'post',
    headers: {
      'Content-Type': 'text/html'
    },
    data
  })
}

export function getResourceBundle() {
  return request('/globalConfig/getResourceBundle')
}

export function deleteResourceBundle() {
  return request({
    url: '/globalConfig/deleteResourceBundle',
    method: 'post'
  })
}

export function getConfig() {
  return request({
    url: '/globalConfig/getPage',
    method: 'post'
  })
}

export function uploadResourceBundleAndReplace(data) {
  return request({
    url: '/globalConfig/uploadResourceBundle',
    method: 'post',
    headers: {
      'Content-Type': 'application/text'
    },
    data
  })
}

export function getConfigByKey(data) {
  return request({
    url: '/globalConfig/getByKey',
    method: 'post',
    params: data
  })
}

export function updateConfig(data) {
  return request({
    url: '/globalConfig/update',
    method: 'post',
    data
  })
}

export function getExtractFileConfig() {
  return request({
    url: '/globalConfig/getExtractFileConfig',
    method: 'post'
  })
}

export function getLabelConfig() {
  return request({
    url: '/globalConfig/getLabelConfig',
    method: 'post'
  })
}

export function getConsoleIpPortConfig() {
  return request({
    url: '/globalConfig/getConsoleIpPortConfig',
    method: 'post'
  })
}

export function getConfigYmlIp() {
  return request({
    url: '/globalConfig/getConfigYmlIp',
    method: 'post'
  })
}

export function getLocalNetworkCards() {
  return request({
    url: '/globalConfig/getLocalNetworkCards',
    method: 'post'
  })
}

export function getGeneralCode(data) {
  return request({
    url: '/globalConfig/getGeneralCode',
    method: 'post',
    data
  })
}

export function getGeneralCodePage() {
  return request({
    url: '/globalConfig/getGeneralCodePage',
    method: 'post'
  })
}

export function getPartConfig() {
  return request({
    url: '/globalConfig/getConfig',
    method: 'GET'
  })
}

export function encodeResourceBundle(data) {
  return request({
    url: '/globalConfig/encodeResourceBundle',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getRecordTimeConfig() {
  return request({
    url: 'globalConfig/getRecordTimeConfig',
    method: 'post'
  })
}

/**
 * 校验【违规响应规则的配置】或【程序动作监控策略的配置】的时长是否低于此时间间隔时，是将返回
 * @param time
 * @returns {*}
 */
export function validateGlobalConfigTime(time) {
  return request({
    url: 'globalConfig/validateGlobalConfigTime',
    method: 'get',
    params: { time }
  })
}

/**
 * 校验【违规响应规则的配置】或【程序动作监控策略的配置】的时长是否低于此时间间隔时，是将返回
 * @returns {*}
 * @param intervalTime
 * @param responseTime
 * @param stgTime
 */
export function updateRecordTime(intervalTime, responseTime, stgTime) {
  return request({
    url: 'globalConfig/updateRecordTime',
    method: 'post',
    params: { intervalTime, responseTime, stgTime }
  })
}

