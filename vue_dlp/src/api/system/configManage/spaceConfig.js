import request from '@/utils/request'

export function getConfigByUsePercent(data) {
  return request({
    url: '/spaceConfig/getByUsePercent',
    method: 'post',
    params: data
  })
}

export function createConfig(data) {
  return request({
    url: '/spaceConfig/add',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/spaceConfig/update',
    method: 'post',
    data
  })
}

export function deleteConfig(data) {
  return request({
    url: '/spaceConfig/delete',
    method: 'post',
    params: data
  })
}

export function getConfigPage(data) {
  return request({
    url: '/spaceConfig/getPage',
    method: 'post',
    data
  })
}

export function getSystemSpace(data) {
  return request({
    url: '/spaceConfig/getSystemSpace',
    method: 'get'
  })
}
