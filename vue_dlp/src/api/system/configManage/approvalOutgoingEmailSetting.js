import request from '@/utils/request'

//  分页解密审批后邮件外发配置信息
export function getPage(data) {
  return request({
    url: '/eamilAssign/getPage',
    method: 'post',
    data
  })
}

//  新增解密审批后邮件外发配置信息
export function addData(data) {
  return request({
    url: '/eamilAssign/add',
    method: 'post',
    data
  })
}

//  删除解密审批后邮件外发配置信息
export function deleteData(data) {
  return request({
    url: '/eamilAssign/delete',
    method: 'post',
    data
  })
}

//  修改解密审批后邮件外发配置信息
export function updateData(data) {
  return request({
    url: '/eamilAssign/update',
    method: 'post',
    data
  })
}

/** 接收人邮箱设置 **/

//  分页查询收件人邮箱信息
export function getReceiverEmailInfoPage(data) {
  return request({
    url: '/approvalEmailInfo/getPage',
    method: 'post',
    data
  })
}

//  添加接收人邮箱信息
export function addReceiverEmailInfo(data) {
  return request({
    url: '/approvalEmailInfo/add',
    method: 'post',
    data
  })
}

//  修改接收人邮箱信息
export function updateReceiverEmailInfo(data) {
  return request({
    url: '/approvalEmailInfo/update',
    method: 'post',
    data
  })
}

//  删除接收人邮箱信息
export function deleteReceiverEmailInfo(data) {
  return request({
    url: '/approvalEmailInfo/delete',
    method: 'post',
    data
  })
}

//  根据邮箱地址获取邮箱信息
export function getReceiverEmailInfoByEmailAddress(data) {
  return request({
    url: '/approvalEmailInfo/getPage',
    method: 'post',
    data
  }).then(res => {
    res.data = res.data.items && res.data.items.length ? res.data.items[0] : null
    return res;
  })
}

//  根据邮箱信息ID获取邮箱信息
export function getReceiverEmailInfoByIds(data) {
  return request({
    url: '/approvalEmailInfo/getPage',
    method: 'post',
    data
  }).then(res => {
    res.data = res.data.items
    return res;
  })
}

//   获取接收人邮箱分组
export function getReceiverEmailInfoGroupTree(data) {
  return getCommonGroupTree(data, 1)
}

//  新增接收人邮箱分组
export function createReceiverEmailInfoGroup(data) {
  changeGroupName(data)
  return addCommonGroup(data, 1);
}

//  修改接收人邮箱分组
export function updateReceiverEmailInfoGroup(data) {
  changeGroupName(data)
  return updateCommonGroup(data, 1);
}

//  删除接收人邮箱分组
export function deleteReceiverEmailInfoGroup(data) {
  handlerDelGroup(data)
  return deleteCommonGroup(data, 1);
}

//  根据接收人邮箱分组名获取信息
export function getReceiverEmailInfoGroupByName(data) {
  return getGroupByName(data.name, 1);
}

/** 邮箱模板设置 **/

//  分页查询邮箱模板信息
export function getTemplateEmailInfoPage(data) {
  return request({
    url: '/approvalEmailTemp/getPage',
    method: 'post',
    data
  })
}

//  添加邮箱模板信息
export function addTemplateEmailInfo(data) {
  return request({
    url: '/approvalEmailTemp/add',
    method: 'post',
    data
  })
}

//  修改邮箱模板信息
export function updateTemplateEmailInfo(data) {
  return request({
    url: '/approvalEmailTemp/update',
    method: 'post',
    data
  })
}

//  删除邮箱模板信息
export function deleteTemplateEmailInfo(data) {
  return request({
    url: '/approvalEmailTemp/delete',
    method: 'post',
    data
  })
}

//  根据邮箱模板名称获取邮箱模板信息
export function getTemplateEmailInfoByTemplateName(data) {
  return request({
    url: '/approvalEmailTemp/getPage',
    method: 'post',
    data
  }).then(res => {
    res.data = res.data.items && res.data.items.length ? res.data.items[0] : null
    return res;
  })
}

//  根据邮箱信息ID获取邮箱信息
export function getTemplateEmailInfoByIds(data) {
  return request({
    url: '/approvalEmailTemp/getPage',
    method: 'post',
    data
  }).then(res => {
    res.data = res.data.items
    return res;
  })
}

//   获取邮箱模板分组树
export function getTemplateEmailInfoGroupTree(data) {
  return getCommonGroupTree(data, 2);
}

//  新增邮箱模板分组
export function createTemplateEmailInfoGroup(data) {
  changeGroupName(data)
  return addCommonGroup(data, 2);
}

//  修改邮箱模板分组
export function updateTemplateEmailInfoGroup(data) {
  changeGroupName(data)
  return updateCommonGroup(data, 2);
}

//  删除邮箱模板分组
export function deleteTemplateEmailInfoGroup(data) {
  handlerDelGroup(data)
  return deleteCommonGroup(data, 2);
}

//  根据邮箱模板分组名获取信息
export function getTemplateEmailInfoGroupByName(data) {
  return getGroupByName(data.name, 2);
}

/** 发件人邮箱设置 **/

//  分页查询发送邮箱信息
export function getSendEmailInfoPage(data) {
  return request({
    url: '/approvalSysEmail/getPage',
    method: 'post',
    data
  })
}

//  添加发送邮箱信息
export function addSendEmailInfo(data) {
  return request({
    url: '/approvalSysEmail/add',
    method: 'post',
    data
  })
}

//  修改发送邮箱信息
export function updateSendEmailInfo(data) {
  return request({
    url: '/approvalSysEmail/update',
    method: 'post',
    data
  })
}

//  删除发送邮箱信息
export function deleteSendEmailInfo(data) {
  return request({
    url: '/approvalSysEmail/delete',
    method: 'post',
    data
  })
}

//  根据邮箱账号获取发送邮箱信息
export function getSendEmailInfoByEmailAccount(data) {
  return request({
    url: '/approvalSysEmail/getPage',
    method: 'post',
    data
  }).then(res => {
    res.data = res.data.items && res.data.items.length ? res.data.items[0] : null
    return res;
  })
}

//  根据发送邮箱信息ID获取发送邮箱信息
export function getSendEmailInfoByIds(data) {
  return request({
    url: '/approvalSysEmail/getPage',
    method: 'post',
    data
  }).then(res => {
    res.data = res.data.items
    return res;
  })
}

//   获取发送邮箱分组
export function getSendEmailInfoGroupTree(data) {
  return getCommonGroupTree(data, 4);
}

//  新增发送邮箱分组
export function createSendEmailInfoGroup(data) {
  changeGroupName(data);
  return addCommonGroup(data, 4);
}

//  修改发送邮箱分组
export function updateSendEmailInfoGroup(data) {
  changeGroupName(data)
  return updateCommonGroup(data, 4);
}

//  删除发送邮箱分组
export function deleteSendEmailInfoGroup(data) {
  handlerDelGroup(data)
  return deleteCommonGroup(data, 4);
}

//  根据发送邮箱分组名获取信息
export function getSendEmailInfoGroupByName(data) {
  return getGroupByName(data.name, 4);
}

//  测试发送邮箱服务器是否正常通讯
export function checkConnect(data) {
  return request({
    url: '/approvalSysEmail/validate',
    method: 'post',
    data
  })
}

//  通用-获取分组树接口
function getCommonGroupTree(data, groupType) {
  data = { groupType, ...data }
  return request({
    url: '/approvalEmailGroup/groupTree',
    method: 'post',
    data
  })
}

//  通用-新增分组树接口
function addCommonGroup(data, groupType) {
  data = { groupType, ...data }
  return request({
    url: '/approvalEmailGroup/addGroup',
    method: 'post',
    data
  }).then(res => {
    if (res.data) {
      res.data.id = res.data.groupId
    }
    return res;
  })
}

//  通用-新增分组树接口
function updateCommonGroup(data, groupType) {
  data = { groupType, ...data }
  return request({
    url: '/approvalEmailGroup/updateGroup',
    method: 'post',
    data
  })
}

//  通用-新增分组树接口
function deleteCommonGroup(data, groupType) {
  data = { groupType, ...data }
  return request({
    url: '/approvalEmailGroup/deleteGroup',
    method: 'post',
    data
  })
}

//  根据分组名称获取分组信息
function getGroupByName(groupName, groupType) {
  const data = { groupType, groupName }
  return request({
    url: '/approvalEmailGroup/getGroupByName',
    method: 'post',
    data
  }).then(res => {
    if (res.data) {
      res.data.id = res.data.groupId
    }
    return res;
  })
}

//  所有分组name 转换成 groupName
function changeGroupName(data) {
  data.groupName = data.name
}

//  删除分组数据之前的数据处理
function handlerDelGroup(data) {
  data.groupId = data.id
}
