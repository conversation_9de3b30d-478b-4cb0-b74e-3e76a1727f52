import request from '@/utils/request'

export function getGroupTreeNode() {
  return request({
    url: '/alarmTemplate/listGroupTree',
    method: 'get'
  })
}

export function createAlarmTemplateGroup(data) {
  return request({
    url: '/alarmTemplate/addGroup',
    method: 'post',
    data
  })
}

export function updateAlarmTemplateGroup(data) {
  return request({
    url: '/alarmTemplate/updateGroup',
    method: 'post',
    data
  })
}

export function deleteAlarmTemplateGroup(data) {
  return request({
    url: '/alarmTemplate/deleteGroup',
    method: 'post',
    data
  })
}

export function getAlarmTemplatePage(data) {
  return request({
    url: '/alarmTemplate/getPage',
    method: 'post',
    data
  })
}

export function getAlarmTypeList(data) {
  return request({
    url: '/alarmTemplate/getAlarmTypeList',
    method: 'get'
  })
}

export function createAlarmTemplate(data) {
  return request({
    url: '/alarmTemplate/add',
    method: 'post',
    data
  })
}

export function updateAlarmTemplate(data) {
  return request({
    url: '/alarmTemplate/update',
    method: 'post',
    data
  })
}

export function deleteAlarmTemplate(data) {
  return request({
    url: '/alarmTemplate/delete',
    method: 'post',
    data
  })
}

export function moveGroup(data) {
  return request({
    url: '/alarmTemplate/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/alarmTemplate/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/alarmTemplate/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/alarmTemplate/moveGroupToOther',
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/alarmTemplate/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function getParamList(alarmType, sstType, bizType) {
  return request({
    url: '/alarmTemplate/getParamList',
    method: 'get',
    params: { alarmType, sstType, bizType }
  })
}

export function getDetailList(id) {
  return request(`/alarmTemplate/getDetail?alarmTemplateId=${id}`)
}

export function getDetailAndLanguage(id, language, tableType) {
  return request(`/alarmTemplate/getDetailAndLanguage?alarmTemplateId=${id}&language=${language}&tableType=${tableType}`)
}

export function getInitTemplate(lang, key) {
  return request(`/alarmTemplate/getInitTemplate?lang=${lang}&key=${key}`)
}

export function saveTemplate(data) {
  return request({
    url: '/alarmTemplate/saveTemplate',
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/alarmTemplate/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function getAlarmTemplateGroupByName(data) {
  return request({
    url: '/alarmTemplate/getGroupByName',
    method: 'post',
    params: data
  })
}
