import request from '@/utils/request'

export function listGroupTreeNode(type) {
  return request({
    url: '/filePermissionControlData/listGroupTreeNode',
    method: 'post',
    data: { type }
  })
}

export function listAllGroup() {
  return request({
    url: '/filePermissionControlData/listAllGroup',
    method: 'get'
  })
}

export function createGroup(data) {
  return request({
    url: '/filePermissionControlData/insertGroup',
    method: 'post',
    data
  })
}

export function updateGroup(data) {
  return request({
    url: '/filePermissionControlData/updateGroup',
    method: 'post',
    data
  })
}

export function deleteGroup(data) {
  return request({
    url: '/filePermissionControlData/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getGroupByName(data) {
  return request({
    url: '/filePermissionControlData/getGroupByName',
    method: 'post',
    data
  })
}

export function groupIdExistReference(groupId) {
  return request({
    url: '/filePermissionControlData/groupIdExistReference/' + groupId,
    method: 'get'
  })
}

export function getPage(data) {
  return request({
    url: '/filePermissionControlData/getPage',
    method: 'post',
    data: data
  })
}

export function batchInsert(data) {
  return request({
    url: '/filePermissionControlData/batchInsert',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/filePermissionControlData/update',
    method: 'post',
    data
  })
}

export function batchDelete(data) {
  return request({
    url: '/filePermissionControlData/delete',
    method: 'post',
    params: data
  })
}

export function getByValues(data) {
  return request({
    url: `/filePermissionControlData/getByValues`,
    method: 'post',
    data
  })
}

export function getByTypeAndGroupId(data) {
  return request({
    url: `/filePermissionControlData/getByTypeAndGroupId`,
    method: 'post',
    data
  })
}

