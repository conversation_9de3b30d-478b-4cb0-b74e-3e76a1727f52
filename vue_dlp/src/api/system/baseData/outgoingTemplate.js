import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/outgoingTemplate/getPage',
    method: 'post',
    data
  })
}

export function createData(data) {
  return request({
    url: '/outgoingTemplate/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/outgoingTemplate/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/outgoingTemplate/delete',
    method: 'post',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/outgoingTemplate/getByName',
    method: 'post',
    params: data
  })
}
