import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/signatureData/getPage',
    method: 'post',
    data
  })
}

export function getBuildTime() {
  return request({
    url: '/signatureData/getBuildTime',
    method: 'post'
  })
}

export function createData(data) {
  return request.post('/signatureData/save', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
}
