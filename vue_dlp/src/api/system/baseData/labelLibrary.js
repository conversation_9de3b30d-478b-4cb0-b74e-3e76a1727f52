import request from '@/utils/request'
// import { fetchFile } from '@/components/DownloadManager/helper'

export function getGroupTreeNode() {
  return request({
    url: '/labelLibrary/listGroupTree',
    method: 'get'
  })
}

export function getTreeNode() {
  return request({
    url: '/labelLibrary/listTree',
    method: 'get'
  })
}

export function getLabelLibPage(data) {
  return request({
    url: '/labelLibrary/getPage',
    method: 'post',
    data
  })
}

export function getIdleTimePage(data) {
  return request({
    url: '/labelLibrary/getIdleTimePage',
    method: 'post',
    data
  })
}

export function createLabelLib(data) {
  return request({
    url: '/labelLibrary/add',
    method: 'post',
    data
  })
}

export function createLabelLibGroup(data) {
  return request({
    url: '/labelLibrary/addGroup',
    method: 'post',
    data
  })
}

export function updateLabelLib(data) {
  return request({
    url: '/labelLibrary/update',
    method: 'post',
    data
  })
}

export function updateLabelLibGroup(data) {
  return request({
    url: '/labelLibrary/updateGroup',
    method: 'post',
    data
  })
}

export function deleteLabelLib(data) {
  return request({
    url: '/labelLibrary/delete',
    method: 'post',
    data
  })
}

export function deleteLabelLibGroup(data) {
  return request({
    url: '/labelLibrary/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getLabelLibGroupByName(data) {
  return request({
    url: '/labelLibrary/getGroupByName',
    method: 'post',
    params: data
  })
}

export function getMailLibByAddress(data) {
  return request({
    url: '/labelLibrary/getByAddress',
    method: 'post',
    params: data
  })
}

export function saveMailSender(data) {
  return request({
    url: '/labelLibrary/saveMailSender',
    method: 'post',
    data
  })
}

export function listMailSenderID() {
  return request({
    url: '/labelLibrary/listMailSenderId',
    method: 'get'
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/labelLibrary/countByGroupId/' + groupId,
    method: 'get'
  })
}

// export function exportExcel(data, opts) {
//   return fetchFile({
//     ...opts,
//     url: '/labelLibrary/export',
//     method: 'post',
//     responseType: 'blob',
//     data
//   })
// }

export function moveGroup(data) {
  return request({
    url: '/labelLibrary/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/labelLibrary/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/labelLibrary/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/labelLibrary/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/labelLibrary/moveGroupToOther',
    method: 'post',
    data
  })
}

export function getlabelLibraryByGroupIds(groupIds) {
  return request({
    url: '/labelLibrary/getByGroupIds',
    method: 'get',
    params: groupIds
  })
}

export function getlabelLibraryByIds(ids) {
  return request({
    url: '/labelLibrary/getByIds',
    method: 'post',
    data: ids
  })
}

export function getIdleTimeSetting() {
  return request({
    url: '/labelLibrary/getIdleTimeSetting',
    method: 'post'
  })
}

export function saveIdleTimeSetting(data) {
  return request({
    url: '/labelLibrary/saveIdleTimeSetting',
    method: 'post',
    data
  })
}

export function getLabelLibraryByIds(ids) {
  return request({
    url: '/labelLibrary/getByIds',
    method: 'post',
    data: ids
  })
}

export function countByLabel(data) {
  return request({
    url: '/labelLibrary/countByLabel',
    method: 'post',
    params: data
  })
}
