import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/appRobot/getPage',
    method: 'post',
    data
  })
}

export function addAppRobot(data) {
  return request({
    url: '/appRobot/add',
    method: 'post',
    data
  })
}

export function updateAppRobot(data) {
  return request({
    url: '/appRobot/update',
    method: 'post',
    data
  })
}

export function deleteAppRobot(id) {
  return request({
    url: '/appRobot/delete?id=' + id,
    method: 'post'
  })
}

export function listForOptions() {
  return request({
    url: '/appRobot/listForOptions',
    method: 'get'
  })
}
