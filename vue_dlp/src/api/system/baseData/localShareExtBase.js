import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper';

export function getLocalShareExtBasePage(data) {
  return request({
    url: '/localShareExtBase/getPage',
    method: 'post',
    data
  })
}

export function createLocalShareExtBase(data, canCover) {
  return request({
    url: '/localShareExtBase/add/' + canCover,
    method: 'post',
    data
  })
}

export function updateLocalShareExtBase(data) {
  return request({
    url: '/localShareExtBase/update',
    method: 'post',
    data
  })
}

export function deleteLocalShareExtBase(data) {
  return request({
    url: '/localShareExtBase/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getLocalShareExtBaseBySuffix(data) {
  return request({
    url: '/localShareExtBase/getBySuffix',
    method: 'post',
    data
  })
}

export function listSuffixTree() {
  return request({
    url: '/localShareExtBase/listTree',
    method: 'get'
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/localShareExtBase/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}
