import request from '@/utils/request'
import qs from 'qs'
import { fetchFile } from '@/utils/download/helper'

export function getTreeNode() {
  return request({
    url: '/fileSuffix/findFileSuffixClassTree',
    method: 'post'
  })
}

export function createData(data) {
  return request({
    url: '/fileSuffix/add',
    method: 'post',
    data
  })
}

export function updateData(data) {
  return request({
    url: '/fileSuffix/update',
    method: 'post',
    data
  })
}

export function deleteData(data) {
  return request({
    url: '/fileSuffix/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/fileSuffix/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getGroupByName(data) {
  return request({
    url: '/fileSuffix/getGroupByName',
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/fileSuffix/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function updateSuffixGroup(data) {
  return request({
    url: '/fileSuffix/saveClassIdForFileSuffixInfo',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/fileSuffix/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function getIdsBySuffixList(data) {
  return request({
    url: '/fileSuffix/getIdsBySuffixList/',
    method: 'post',
    data: {
      suffix: data
    }
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/fileSuffix/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function getFileSuffixPage(data) {
  return request({
    url: '/fileSuffix/getFileSuffixPage',
    method: 'post',
    data
  })
}

export function getLibFileSuffixPage(data) {
  return request({
    url: '/fileSuffix/getLibFileSuffixPage',
    method: 'post',
    data
  })
}

export function getLibTreeNode() {
  return request({
    url: '/fileSuffix/listFileSuffixLibClassTree',
    method: 'get'
  })
}

export function addFileSuffix(data) {
  return request({
    url: '/fileSuffix/addFileSuffixInfo',
    method: 'post',
    data
  })
}

export function batchAddFileSuffix(data) {
  return request({
    url: '/fileSuffix/addFileSuffixInfoBatch',
    method: 'post',
    data
  })
}
export function updateFileSuffix(data) {
  return request({
    url: '/fileSuffix/updateFileSuffixInfo',
    method: 'post',
    data
  })
}

export function deleteFileSuffix(data) {
  return request({
    url: '/fileSuffix/deleteFileSuffixInfo',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/fileSuffix/moveGroupToOther',
    method: 'post',
    data
  })
}

export function getFileSuffixBySuffix(data) {
  return request({
    url: '/fileSuffix/getBySuffix',
    method: 'post',
    data
  })
}

export function listFileSuffixTree(data) {
  return request({
    url: '/fileSuffix/listFileSuffixTree',
    method: 'post',
    data: data ? qs.stringify(data) : null
  })
}

export function addFromFileSuffixLib(data) {
  return request({
    url: '/fileSuffix/addFromFileSuffixLib',
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/fileSuffix/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}
