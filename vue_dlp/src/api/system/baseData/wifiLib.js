import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getGroupTreeNode() {
  return request({
    url: '/wifi/listGroupTreeNode',
    method: 'get'
  })
}

export function getWifiPage(data) {
  return request({
    url: '/wifi/getPage',
    method: 'post',
    data
  })
}

// /**
//  * 查询WiFi信息内置库
//  * @param data
//  * @returns {AxiosPromise}
//  */
// export function getWifiLibList(data) {
//   return request({
//     url: '/wifi/getWifiLibPage',
//     method: 'post',
//     data
//   })
// }

export function createWifi(data) {
  return request({
    url: '/wifi/insert',
    method: 'post',
    data
  })
}

export function createWifiGroup(data) {
  return request({
    url: '/wifi/insertGroup',
    method: 'post',
    params: data
  })
}

export function updateWifi(data) {
  return request({
    url: '/wifi/update',
    method: 'post',
    data
  })
}

export function updateWifiGroup(data) {
  return request({
    url: '/wifi/updateGroup',
    method: 'post',
    data
  })
}

export function deleteWifi(data) {
  return request({
    url: '/wifi/delete',
    method: 'post',
    data
  })
}

export function deleteWifiGroup(data) {
  return request({
    url: '/wifi/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getWifiGroupByName(data) {
  return request({
    url: '/wifi/getGroupByName',
    method: 'post',
    params: data
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/wifi/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function listWifiByGroupId(data) {
  return request({
    url: '/wifi/listByGroupId',
    method: 'post',
    data
  })
}

export function listWifiByGroupIds(data) {
  return request({
    url: '/wifi/listByGroupIds',
    method: 'post',
    data
  })
}

export function getWifiByVO(data) {
  return request({
    url: '/wifi/getWifiByVO',
    method: 'post',
    data
  })
}

/**
 * 得到WiFi信息库的内置数据树形结构
 */
export function getWifiGroupTree() {
  return request({
    url: '/wifi/listGroupTreeNode',
    method: 'get'
  })
}
export function getImportProgress() {
  return request({
    url: '/wifi/getImportProgress',
    method: 'get'
  })
}
// export function addFromWifiLib(data) {
//   return request({
//     url: '/wifi/addFromWifiLib',
//     method: 'post',
//     params: data
//   })
// }

export function moveGroup(data) {
  return request({
    url: '/wifi/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/wifi/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/wifi/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/wifi/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/wifi/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/wifi/moveGroupToOther',
    method: 'post',
    data
  })
}

// /** 根据Ids 获取wifi数据 **/
// export function getByIds(ids) {
//   return request({
//     url: '/wifi/getByIds',
//     method: 'get',
//     params: ids
//   })
// }

export function countUrlByGroupId(groupId) {
  return request({
    url: '/wifi/countByGroupId/' + groupId,
    method: 'get'
  })
}
