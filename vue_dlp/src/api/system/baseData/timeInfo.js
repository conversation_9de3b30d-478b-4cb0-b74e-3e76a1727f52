import request from '@/utils/request'

export function getTimeInfoPage(data) {
  return request({
    url: '/timeInfo/getPage',
    method: 'post',
    data
  })
}
export function getTimeInfoPageFilterAllDay(data) {
  return request({
    url: '/timeInfo/getPageFilterAllDay',
    method: 'post',
    data
  })
}

export function getTimeInfoByName(data) {
  return request({
    url: '/timeInfo/getByName',
    method: 'post',
    params: data
  })
}

export function createTimeInfo(data) {
  return request({
    url: '/timeInfo/add',
    method: 'post',
    data
  })
}

export function updateTimeInfo(data) {
  return request({
    url: '/timeInfo/update',
    method: 'post',
    data
  })
}

export function deleteTimeInfo(data) {
  return request({
    url: '/timeInfo/delete',
    method: 'post',
    data
  })
}

export function getTimeInfoOptions(data) {
  return request({
    url: '/timeInfo/getTimeInfoOptions',
    method: 'post',
    params: data
  })
}

export function getTimeInfoById(id) {
  return request({
    url: '/timeInfo/getById?id=' + (id || -1),
    method: 'get'
  })
}
