import request from '@/utils/request'

export function getGroupTreeNode() {
  return request({
    url: '/waterMarkLib/listGroupTree',
    method: 'get'
  })
}

export function getTreeNode() {
  return request({
    url: '/waterMarkLib/listTree',
    method: 'get'
  })
}

export function createGroup(data) {
  return request({
    url: '/waterMarkLib/addGroup',
    method: 'post',
    data
  })
}

export function updateGroup(data) {
  return request({
    url: '/waterMarkLib/updateGroup',
    method: 'post',
    data
  })
}

export function deleteGroup(data) {
  return request({
    url: '/waterMarkLib/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getGroupByName(data) {
  return request({
    url: '/waterMarkLib/getGroupByName',
    method: 'post',
    params: data
  })
}

export function getLibPage(data) {
  return request({
    url: '/waterMarkLib/getPage',
    method: 'post',
    data
  })
}

export function getLibList(data) {
  return request({
    url: '/waterMarkLib/listByVo',
    method: 'post',
    data
  })
}

export function createLib(data) {
  return request.post('/waterMarkLib/add', data, { 'Content-Type': 'multipart/form-data' })
}

export function updateLib(data) {
  return request.post('/waterMarkLib/update', data, { 'Content-Type': 'multipart/form-data' })
}

export function deleteLib(data) {
  return request({
    url: '/waterMarkLib/delete',
    method: 'post',
    data
  })
}

export function getLibByName(data) {
  return request({
    url: '/waterMarkLib/getByName',
    method: 'post',
    data
  })
}

export function countLibByGroupId(groupId) {
  return request({
    url: '/waterMarkLib/countByGroupId/' + groupId,
    method: 'get'
  })
}
export function countByVo(data) {
  return request({
    url: '/waterMarkLib/countByVo',
    method: 'post',
    data
  })
}

export function moveGroup(data) {
  return request({
    url: '/waterMarkLib/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/waterMarkLib/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/waterMarkLib/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/waterMarkLib/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/waterMarkLib/moveGroupToOther',
    method: 'post',
    data
  })
}

export function moveGroupToOtherByType(parentId, groupId, type) {
  return request({
    url: '/waterMarkLib/moveGroupToOtherByType',
    method: 'get',
    params: { parentId: parentId, groupId: groupId, type: type }
  })
}
