import request from '@/utils/request'
import qs from 'qs'
import store from '@/store'

export function getTreeNode(data) {
  return request({
    url: '/appInfo/listAppTypeTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getAppInfoList(data) {
  return request({
    url: '/appInfo/getAppInfoPage',
    method: 'post',
    data
  })
}

export function createAppInfo(data) {
  return request({
    url: '/appInfo/addAppInfo',
    method: 'post',
    data
  })
}

export function batchCreateAppInfo(data) {
  return request({
    url: '/appInfo/addAppInfoBatch',
    method: 'post',
    data,
    timeout: Math.max(store.getters.requestTimeout, 180000)
  })
}

export function updateAppInfo(data) {
  return request({
    url: '/appInfo/updateAppInfo',
    method: 'post',
    data
  })
}

export function deleteAppInfo(data) {
  return request({
    url: '/appInfo/deleteAppInfo',
    method: 'post',
    params: data
  })
}

export function createAppType(data) {
  return request({
    url: '/appInfo/addAppType',
    method: 'post',
    params: data
  })
}

export function updateAppType(data) {
  return request({
    url: '/appInfo/updateAppType',
    method: 'post',
    params: data
  })
}

export function deleteAppType(data) {
  return request({
    url: '/appInfo/deleteAppType',
    method: 'post',
    params: data
  })
}

export function getAppTypeByName(data) {
  return request({
    url: '/appInfo/getAppTypeByName',
    method: 'post',
    params: data
  })
}

export function getAppTypeById(data) {
  return request({
    url: '/appInfo/getAppTypeById',
    method: 'post',
    params: data
  })
}

export function upload(data) {
  return request.post('/appInfo/upload', data, { 'Content-Type': 'multipart/form-data' })
}

export function getAppTreeNode() {
  return request({
    url: '/appInfo/getAppTree',
    method: 'post'
  })
}

export function countInfoByGroupId(groupId) {
  return request({
    url: '/appInfo/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function importFromLib(data) {
  return request({
    url: '/appInfo/addFromLib',
    method: 'post',
    data
  })
}

export function getAppIdsByTypeIds(data) {
  return request({
    url: '/appInfo/getAppIdsByTypeIds',
    method: 'post',
    data
  })
}

