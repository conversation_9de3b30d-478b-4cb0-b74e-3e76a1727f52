import request from '@/utils/request'
import qs from 'qs'
import store from '@/store'
import { fetchFile } from '@/utils/download/helper';

export function getTreeNode(data) {
  return request({
    url: '/softProcess/getTypeTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

/**
 * 获取类型树
 * @param data
 * @returns {操作系统类型: [树节点]}
 */
export function getTypeTreeNodeMap(data) {
  return request({
    url: '/softProcess/getTypeTreeMap',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getPackegeList(data) {
  return request({
    url: '/softProcess/findSoftProcessList',
    method: 'post',
    data
  })
}

export function listSoftProcess(data) {
  return request({
    url: '/softProcess/listSoftProcess',
    method: 'post',
    data
  })
}

export function createSoftPacket(data) {
  return request({
    url: '/softProcess/addSoft',
    method: 'post',
    data
  })
}

export function batchCreateSoftPacket(data) {
  return request({
    url: '/softProcess/saveProcessBatch',
    method: 'post',
    timeout: Math.max(store.getters.requestTimeout, 180000),
    data
  })
}

export function createType(data) {
  return request({
    url: '/softProcess/addType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateSoftPacket(data) {
  return request({
    url: '/softProcess/updateSoft',
    method: 'post',
    data
  })
}

export function updateType(data) {
  return request({
    url: '/softProcess/updateType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function deleteSoftPacket(data) {
  return request({
    url: '/softProcess/deleteSoft',
    method: 'post',
    params: data
  })
}

export function exportSoftPacket(data, opts) {
  return fetchFile({
    ...opts,
    url: '/softProcess/exportSoft',
    method: 'post',
    data
  })
}

export function deleteType(data) {
  return request({
    url: '/softProcess/deleteType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/softProcess/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getTypeByName(data) {
  return request({
    url: '/softProcess/getTypeByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function importFromLib(data) {
  return request({
    url: '/softProcess/addFromLib',
    method: 'post',
    data
  })
}

export function getProcessByIds(data) {
  return request({
    url: '/softProcess/getProcessByIds',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getSpecialPage(data) {
  return request({
    url: '/softProcess/getSoftLimitSpecialPage',
    method: 'post',
    data
  })
}

export function deleteSpecial(data) {
  return request({
    url: '/softProcess/deleteSoftLimitSpecial',
    method: 'post',
    params: data
  })
}
export function addSpecial(data) {
  return request({
    url: '/softProcess/addSoftLimitSpecial',
    method: 'post',
    data
  })
}

export function getStrategyBySoftProcessIds(data) {
  return request({
    url: '/softProcess/getStrategyBySoftProcessIds',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listSoftProcessByProcessName(data) {
  return request({
    url: '/softProcess/listSoftProcessByProcessName',
    method: 'post',
    data
  })
}

export function mergeProcess(data) {
  return request({
    url: '/softProcess/mergeProcess',
    method: 'post',
    data
  })
}

export function batchMergeProcess(data) {
  return request({
    url: '/softProcess/batchMergeProcess',
    method: 'post',
    data
  })
}

export function batchUpdateSoftPacket(data) {
  return request({
    url: '/softProcess/batchUpdateSoft',
    method: 'post',
    data
  })
}

export function isMergeInSoftLibrary(data) {
  return request({
    url: '/softProcess/isMergeInSoftLibrary',
    method: 'get',
    data
  })
}

export function getAllMergeProcess(data) {
  return request({
    url: '/softProcess/getAllMergeProcess',
    method: 'post',
    data
  })
}
