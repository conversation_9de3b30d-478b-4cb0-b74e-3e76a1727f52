import request from '@/utils/request'
import qs from 'qs'

export function getPage(data) {
  return request({
    url: '/stgBaseConfig/getPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function createData(data) {
  return request({
    url: '/stgBaseConfig/add',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateData(data) {
  return request({
    url: '/stgBaseConfig/update',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listByGroupId(id) {
  return request({
    url: '/stgBaseConfig/listByGroupId/' + id,
    method: 'get'
  })
}

export function getMap() {
  return request({
    url: '/stgBaseConfig/getMap',
    method: 'get'
  })
}

export function getMstgMap() {
  return request({
    url: '/mstgBaseConfig/getMap',
    method: 'get'
  })
}

/**
 * 根据策略类型编号获取生效范围（1终端、2操作员、3两者）
 * @param stgTypeNumber   策略类型编号
 * @returns {AxiosPromise}
 */
export function getMstgUsedScope(stgTypeNumber) {
  return request({
    url: '/mstgBaseConfig/getUsedScope',
    method: 'get',
    params: { stgTypeNumber }
  })
}
