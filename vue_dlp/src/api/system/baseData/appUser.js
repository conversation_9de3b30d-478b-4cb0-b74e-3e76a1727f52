import request from '@/utils/request'

export function getPage(data) {
  return request({
    url: '/appUser/getPage',
    method: 'post',
    data
  })
}
export function getPageByTag(data) {
  return request({
    url: '/appUser/getPageByTag',
    method: 'post',
    data
  })
}

export function addAppUser(data) {
  return request({
    url: '/appUser/add',
    method: 'post',
    data
  })
}

export function updateAppUser(data) {
  return request({
    url: '/appUser/update',
    method: 'post',
    data
  })
}

export function deleteAppUser(ids) {
  return request({
    url: '/appUser/delete?ids=' + ids,
    method: 'post'
  })
}

export function getAllTags() {
  return request({
    url: '/appUser/listTagForOptions',
    method: 'get'
  })
}

export function getDingTalkUserIdByMobile(param) {
  return request({
    url: '/appUser/getDingTalkUserIdByMobile',
    method: 'get',
    params: param
  })
}

