import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getTreeNode() {
  return request({
    url: '/url/listGroupTreeNode',
    method: 'get'
  })
}

export function getUrlList(data) {
  return request({
    url: '/url/getPage',
    method: 'post',
    data
  })
}

/**
 * 查询网址内置库
 * @param data
 * @returns {AxiosPromise}
 */
export function getUrlLibList(data) {
  return request({
    url: '/url/getUrlLibPage',
    method: 'post',
    data
  })
}

export function createUrl(data) {
  return request({
    url: '/url/insert',
    method: 'post',
    params: data
  })
}

export function createBatchUrl(data) {
  return request({
    url: '/url/insertBatch',
    method: 'post',
    data
  })
}

export function createUrlGroup(data) {
  return request({
    url: '/url/insertGroup',
    method: 'post',
    params: data
  })
}

export function updateUrl(data) {
  return request({
    url: '/url/update',
    method: 'post',
    params: data
  })
}

export function updateUrlGroup(data) {
  return request({
    url: '/url/updateGroup',
    method: 'post',
    params: data
  })
}

export function deleteUrl(data) {
  return request({
    url: '/url/delete',
    method: 'post',
    data
  })
}

export function deleteUrlGroup(data) {
  return request({
    url: '/url/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getUrlGroupByName(data) {
  return request({
    url: '/url/getGroupByName',
    method: 'post',
    params: data
  })
}

export function countUrlByGroupId(groupId) {
  return request({
    url: '/url/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function listUrlByGroupId(data) {
  return request({
    url: '/url/listByGroupId',
    method: 'post',
    data
  })
}

export function getUrlByAdress(data) {
  return request({
    url: '/url/getByUrl',
    method: 'post',
    params: data
  })
}

/**
 * 得到网址库的内置数据树形结构(旧版)
 */
export function getUrlLibTree() {
  return request({
    url: '/url/listUrlLibTree',
    method: 'get'
  })
}

/**
* 得到网址库的内置数据树形结构(新版20220901)
*/
export function getUrlGroupTree() {
  return request({
    url: '/url/getUrlGroupTree',
    method: 'get'
  })
}
export function getImportProgress() {
  return request({
    url: '/url/getImportProgress',
    method: 'get'
  })
}
export function addFromUrlLib(data) {
  return request({
    url: '/url/addFromUrlLib',
    method: 'post',
    data
  })
}

export function moveGroup(data) {
  return request({
    url: '/url/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/url/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/url/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/url/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/url/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/url/moveGroupToOther',
    method: 'post',
    data
  })
}

export function getById(id) {
  return request({
    url: '/url/getById?id=' + id,
    method: 'get'
  })
}

/** 根据Ids 获取url数据 **/
export function getByIds(data) {
  return request({
    url: '/url/getByIds',
    method: 'post',
    data
  })
}

/** 根据Ids 获取url数据 **/
export function getByUrls(data) {
  return request({
    url: '/url/getByUrls',
    method: 'post',
    data
  })
}

export function listTreeNode() {
  return request({
    url: '/url/listTreeNode',
    method: 'get'
  })
}

export function urlListTreeNode() {
  return request({
    url: '/url/urlListTreeNode',
    method: 'get'
  })
}

export function listUrlGroup() {
  return request({
    url: '/url/listUrlGroup',
    method: 'get'
  })
}
