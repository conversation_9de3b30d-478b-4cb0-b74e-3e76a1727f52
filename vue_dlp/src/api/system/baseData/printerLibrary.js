import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getPrinterList(data) {
  return request({
    url: '/printer/getPage',
    method: 'post',
    data
  })
}

export function getTreeNode() {
  return request({
    url: '/printer/listGroupTreeNode',
    method: 'get'
  })
}

export function printerListTree() {
  return request({
    url: '/printer/listTree',
    method: 'post'
  })
}

export function getByIds(data) {
  return request({
    url: '/printer/getByIds',
    method: 'post',
    data
  })
}

export function createPrinterGroup(data) {
  return request({
    url: '/printer/insertGroup',
    method: 'post',
    params: data
  })
}

export function updatePrinterGroup(data) {
  return request({
    url: '/printer/updateGroup',
    method: 'post',
    params: data
  })
}

export function deletePrinterGroup(data) {
  return request({
    url: '/printer/deleteGroup',
    method: 'post',
    params: data
  })
}

export function moveGroup(data) {
  return request({
    url: '/printer/moveGroup',
    method: 'post',
    data
  })
}

export function getPrinterGroupByName(data) {
  return request({
    url: '/printer/getGroupByName',
    method: 'post',
    params: data
  })
}

export function countPrinterByGroupId(groupId) {
  return request({
    url: '/printer/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/printer/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/printer/moveGroupToOther',
    method: 'post',
    data
  })
}

export function getPrinterByName(data) {
  return request({
    url: '/printer/getPrinterByName',
    method: 'post',
    data
  })
}

export function createPrinter(data) {
  return request({
    url: '/printer/insert',
    method: 'post',
    params: data
  })
}

export function createOrUpdatePrinter(data) {
  return request({
    url: '/printer/insertOrUpdate',
    method: 'post',
    params: data
  })
}

export function createPrinterCheckRepeat(data) {
  return request({
    url: '/printer/insertCheckRepeat',
    method: 'post',
    params: data
  })
}

export function createPrinterCompatibleOldStg(data) {
  return request({
    url: '/printer/insertCompatibleOldStg',
    method: 'post',
    params: data
  })
}

export function updatePrinter(data) {
  return request({
    url: '/printer/updatePrinter',
    method: 'post',
    params: data
  })
}

export function deletePrinter(data) {
  return request({
    url: '/printer/deletePrinter',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/printer/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/printer/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/printer/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function listPrinterByGroupId(data) {
  return request({
    url: '/printer/listByGroupId',
    method: 'post',
    data
  })
}
