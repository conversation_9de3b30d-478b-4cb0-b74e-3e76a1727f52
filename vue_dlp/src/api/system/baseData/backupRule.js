import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/backupRule/findPage',
    method: 'post',
    data
  })
}

export function createRule(data) {
  return request({
    url: '/backupRule/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/backupRule/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/backupRule/delete',
    method: 'post',
    data
  })
}

export function getBackupRules() {
  return request({
    url: '/backupRule/getAllBackupRules',
    method: 'post'
  })
}

export function getBackUpRuleByName(data) {
  return request({
    url: '/backupRule/getByName',
    method: 'post',
    params: data
  })
}
