import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getGroupTreeNode() {
  return request({
    url: '/mailLibrary/listGroupTree',
    method: 'get'
  })
}

export function getTreeNode() {
  return request({
    url: '/mailLibrary/listTree',
    method: 'get'
  })
}

export function getMailLibPage(data) {
  return request({
    url: '/mailLibrary/getPage',
    method: 'post',
    data
  })
}

export function getIdleTimePage(data) {
  return request({
    url: '/mailLibrary/getIdleTimePage',
    method: 'post',
    data
  })
}

export function createMailLib(data) {
  return request({
    url: '/mailLibrary/add',
    method: 'post',
    data
  })
}

export function createMailLibGroup(data) {
  return request({
    url: '/mailLibrary/addGroup',
    method: 'post',
    data
  })
}

export function updateMailLib(data) {
  return request({
    url: '/mailLibrary/update',
    method: 'post',
    data
  })
}

export function updateMailLibGroup(data) {
  return request({
    url: '/mailLibrary/updateGroup',
    method: 'post',
    data
  })
}

export function deleteMailLib(data) {
  return request({
    url: '/mailLibrary/delete',
    method: 'post',
    data
  })
}

export function deleteMailLibGroup(data) {
  return request({
    url: '/mailLibrary/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getMailLibGroupByName(data) {
  return request({
    url: '/mailLibrary/getGroupByName',
    method: 'post',
    params: data
  })
}

export function getMailLibByAddress(data) {
  return request({
    url: '/mailLibrary/getByAddress',
    method: 'post',
    params: data
  })
}

export function saveMailSender(data) {
  return request({
    url: '/mailLibrary/saveMailSender',
    method: 'post',
    data
  })
}

export function listMailSenderID() {
  return request({
    url: '/mailLibrary/listMailSenderId',
    method: 'get'
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/mailLibrary/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/mailLibrary/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function moveGroup(data) {
  return request({
    url: '/mailLibrary/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/mailLibrary/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/mailLibrary/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/mailLibrary/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/mailLibrary/moveGroupToOther',
    method: 'post',
    data
  })
}

export function getMailLibraryByGroupIds(groupIds) {
  return request({
    url: '/mailLibrary/getByGroupIds',
    method: 'get',
    params: groupIds
  })
}

export function getMailLibraryByIds(ids) {
  return request({
    url: '/mailLibrary/getByIds',
    method: 'post',
    data: ids
  })
}

export function getIdleTimeSetting() {
  return request({
    url: '/mailLibrary/getIdleTimeSetting',
    method: 'post'
  })
}

export function saveIdleTimeSetting(data) {
  return request({
    url: '/mailLibrary/saveIdleTimeSetting',
    method: 'post',
    data
  })
}

export function flushMailUsedTime(data) {
  return request({
    url: '/mailLibrary/flushMailUsedTime',
    method: 'post',
    data
  })
}
