import request from '@/utils/request'
import qs from 'qs'

export function getFingerprintList(data) {
  return request({
    url: '/fingerprintRela/list',
    method: 'post',
    data
  })
}

export function saveFingerprintRela(data) {
  return request({
    url: '/fingerprintRela/add',
    method: 'post',
    data
  })
}

export function deleteFingerprintRela(data) {
  return request({
    url: '/fingerprintRela/deleteByIds',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listFingerprintTree(data) {
  return request({
    url: '/softwareClass/listFingerprintTree',
    method: 'post',
    data: data ? qs.stringify(data) : null
  })
}

export function listFingerprintTreeWithoutVersion(data) {
  return request({
    url: '/softwareClass/listFingerprintTreeWithoutVersion',
    method: 'post',
    data: data ? qs.stringify(data) : null
  })
}

export function getSoftwareInfotableWithVersion(data) {
  return request({
    url: '/softwareClass/getSoftwareInfotableWithVersion',
    method: 'post',
    data: data ? qs.stringify(data) : null
  })
}

export function listProcessTree(data) {
  return request({
    url: '/fingerprintRela/listProcessTree',
    method: 'post',
    data
  })
}
