import request from '@/utils/request'

export function getRulePage(data) {
  return request({
    url: '/violationResponseRule/findPage',
    method: 'post',
    data
  })
}

export function createRule(data) {
  return request({
    url: '/violationResponseRule/add',
    method: 'post',
    data
  })
}

export function updateRule(data) {
  return request({
    url: '/violationResponseRule/update',
    method: 'post',
    data
  })
}

export function deleteRule(data) {
  return request({
    url: '/violationResponseRule/delete',
    method: 'post',
    data
  })
}

export function getRules() {
  return request({
    url: '/violationResponseRule/getAllRules',
    method: 'post'
  })
}

export function getSysUserList() {
  return request({
    url: '/sysUser/listSysUser',
    method: 'post'
  })
}
export function getDingTalkAppList() {
  return request({
    url: '/violationResponseRule/getAllDingTalkApp',
    method: 'get'
  })
}

export function validRuleId(data) {
  return request({
    url: '/violationResponseRule/validRuleId',
    method: 'post',
    params: data
  })
}

export function getRuleByName(data) {
  return request({
    url: '/violationResponseRule/getByName',
    method: 'post',
    params: data
  })
}

export function updateConfig(data) {
  return request({
    url: '/violationResponseRule/updateConfig',
    method: 'post',
    data
  })
}

export function getById(id) {
  return request({
    url: '/violationResponseRule/get',
    method: 'post',
    params: { id }
  })
}
