import request from '@/utils/request'

export function add(data) {
  return request({
    url: '/labelGradeLibrary/add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/labelGradeLibrary/update',
    method: 'post',
    data
  })
}

export function deleteGrade(data) {
  return request({
    url: '/labelGradeLibrary/delete',
    method: 'post',
    data
  })
}

export function getPage(data) {
  return request({
    url: '/labelGradeLibrary/getPage',
    method: 'post',
    data
  })
}

export function getList() {
  return request({
    url: '/labelGradeLibrary/getList',
    method: 'get'
  })
}

export function getAllList() {
  return request({
    url: '/labelGradeLibrary/getAllList',
    method: 'get'
  })
}

export function getMaxGrade() {
  return request({
    url: '/labelGradeLibrary/getMaxGrade',
    method: 'get'
  })
}

export function getEnableGrade() {
  return request({
    url: '/labelGradeLibrary/getEnableGrade',
    method: 'get'
  })
}

export function countByGrade(data) {
  return request({
    url: '/labelGradeLibrary/countByGrade',
    method: 'post',
    params: data
  })
}

export function saveEnableGrade(data) {
  return request({
    url: '/labelGradeLibrary/saveEnableGrade',
    method: 'post',
    params: data
  })
}
