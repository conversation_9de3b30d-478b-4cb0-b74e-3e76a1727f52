import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper';

export function getTreeNode() {
  return request({
    url: '/webpageMatchRule/listGroupTreeNode',
    method: 'get'
  })
}

export function createWebpageMatchRuleGroup(data) {
  return request({
    url: '/webpageMatchRule/insertGroup',
    method: 'post',
    params: data
  })
}

export function updateWebpageMatchRuleGroup(data) {
  return request({
    url: '/webpageMatchRule/updateGroup',
    method: 'post',
    params: data
  })
}

export function deleteWebpageMatchRuleGroup(data) {
  return request({
    url: '/webpageMatchRule/deleteGroup',
    method: 'post',
    params: data
  })
}

export function moveGroup(data) {
  return request({
    url: '/webpageMatchRule/moveGroup',
    method: 'post',
    data
  })
}

export function getWebpageMatchRuleGroupByName(data) {
  return request({
    url: '/webpageMatchRule/getGroupByName',
    method: 'post',
    params: data
  })
}

export function countWebpageMatchRuleByGroupId(groupId) {
  return request({
    url: '/webpageMatchRule/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getWebpageMatchRuleList(data) {
  return request({
    url: '/webpageMatchRule/getPage',
    method: 'post',
    data
  })
}

export function listWebpageMatchRuleByGroupId(data) {
  return request({
    url: '/webpageMatchRule/listByGroupId',
    method: 'post',
    data
  })
}
export function getByIds(data) {
  return request({
    url: '/webpageMatchRule/getByIds',
    method: 'post',
    data
  })
}

export function createWebpageMatchRule(data) {
  return request({
    url: '/webpageMatchRule/insert',
    method: 'post',
    params: data
  })
}

export function updateWebpageMatchRule(data) {
  return request({
    url: '/webpageMatchRule/update',
    method: 'post',
    params: data
  })
}

export function deleteWebpageMatchRule(data) {
  return request({
    url: '/webpageMatchRule/delete',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/webpageMatchRule/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/webpageMatchRule/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/webpageMatchRule/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/webpageMatchRule/moveGroupToOther',
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/webpageMatchRule/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function getByName(data) {
  return request({
    url: '/webpageMatchRule/getByName',
    method: 'get',
    params: data
  })
}
