import request from '@/utils/request'
import { fetchFile } from '@/utils/download/helper'

export function getSysBrowserPage(data) {
  return request({
    url: '/serverLibrary/getSysBrowserPage',
    method: 'post',
    data
  })
}

export function getWhiteListServerPage(data) {
  return request({
    url: '/serverLibrary/getPage',
    method: 'post',
    data
  })
}

export function createWhiteListServer(data) {
  return request({
    url: '/serverLibrary/add',
    method: 'post',
    data
  })
}

export function updateDefaultEncStgServer() {
  return request({
    url: '/serverLibrary/updateDefaultEncStgServer',
    method: 'post'
  })
}

export function updateWhiteListServer(data) {
  return request({
    url: '/serverLibrary/update',
    method: 'post',
    data
  })
}

export function deleteWhiteListServer(data) {
  return request({
    url: '/serverLibrary/delete',
    method: 'post',
    data
  })
}

export function getWhiteListServerByName(data) {
  return request({
    url: '/serverLibrary/getByName',
    method: 'post',
    params: data
  })
}

export function createWhiteListServerGroup(data) {
  return request({
    url: '/serverLibrary/addGroup',
    method: 'post',
    data
  })
}

export function updateWhiteListServerGroup(data) {
  return request({
    url: '/serverLibrary/updateGroup',
    method: 'post',
    data
  })
}

export function deleteWhiteListServerGroup(data) {
  return request({
    url: '/serverLibrary/deleteGroup',
    method: 'post',
    params: data
  })
}

export function listGroupTree() {
  return request({
    url: '/serverLibrary/listGroupTree',
    method: 'get'
  })
}

export function countChildByGroupId(groupId) {
  return request({
    url: '/serverLibrary/countByGroupId/' + groupId,
    method: 'get'
  })
}

export function getGroupByName(data) {
  return request({
    url: '/serverLibrary/getGroupByName',
    method: 'post',
    params: data
  })
}

export function moveGroup(data) {
  return request({
    url: '/serverLibrary/moveGroup',
    method: 'post',
    data
  })
}

export function batchUpdateGroup(data, groupId) {
  return request({
    url: '/serverLibrary/batchUpdateGroup/' + groupId,
    method: 'post',
    data
  })
}

export function batchUpdateAllGroup(data, groupId) {
  return request({
    url: '/serverLibrary/batchUpdateAllGroup/' + groupId,
    method: 'post',
    data
  })
}

export function exportExcel(data, opts) {
  return fetchFile({
    ...opts,
    url: '/serverLibrary/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

export function deleteGroupAndData(data) {
  return request({
    url: '/serverLibrary/deleteGroupAndData',
    method: 'post',
    data
  })
}

export function moveGroupToOther(data) {
  return request({
    url: '/serverLibrary/moveGroupToOther',
    method: 'post',
    data
  })
}
export function getServerLibraryByGroupIds(groupIds) {
  return request({
    url: '/serverLibrary/getByGroupIds',
    method: 'get',
    params: groupIds
  })
}

export function getServerLibraryByIds(ids) {
  return request({
    url: '/serverLibrary/getByIds',
    method: 'get',
    params: ids
  })
}

export function editBrowserProcess(data) {
  return request({
    url: '/serverLibrary/editBrowserProcess',
    method: 'post',
    data
  })
}
