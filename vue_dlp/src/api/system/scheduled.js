import request from '@/utils/request'

export function getScheduledPage(data) {
  return request({
    url: '/scheduled/getPage',
    method: 'post',
    data
  })
}

export function startScheduledTask(data) {
  return request({
    url: '/scheduled/start',
    method: 'post',
    params: data
  })
}
export function stopScheduledTask(data) {
  return request({
    url: '/scheduled/stop',
    method: 'post',
    params: data
  })
}
export function getThreadPoolInfo(data) {
  return request({
    url: '/scheduled/getPoolInfo',
    method: 'get'
  })
}

export function getCachePoolInfo() {
  return request({
    url: '/scheduled/getCachePoolInfo',
    method: 'get'
  })
}
