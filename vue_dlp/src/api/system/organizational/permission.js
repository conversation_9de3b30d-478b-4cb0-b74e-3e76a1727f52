import request from '@/utils/request'

export function getPermissionBtnMap() {
  return request({
    url: '/permission/getPermissionBtnMap',
    method: 'get'
  })
}

export function getBtnAndMenuPermission() {
  return request({
    url: '/permission/getBtnAndMenuPermission',
    method: 'get'
  })
}

export function getBtnPermission() {
  return request({
    url: '/permission/getBtnPermission',
    method: 'get'
  })
}

export function listRoleTree(roleId) {
  return request({
    url: '/permission/listRoleTree',
    method: 'get'
  })
}

export function listMenuTree(roleId) {
  return request({
    url: '/permission/listMenu/' + (!roleId ? 0 : roleId),
    method: 'get'
  })
}

// 获取roleId对应的菜单权限，如果未配置则获取父节点的菜单权限，如果父节点未配置则继续查询上一级节点，直至到根节点获取所有的权限
export function listCascadeMenu(roleId) {
  return request({
    url: '/permission/listCascadeMenu/' + (!roleId ? 0 : roleId),
    method: 'get'
  })
}

export function listDataTree(roleId, dataType, dataId) {
  return request({
    url: '/permission/listDataTree/' + (!roleId ? 0 : roleId) + '/' + (!dataType ? 0 : dataType) + '/' + (!dataId ? -1 : dataId),
    method: 'get'
  })
}
export function listAllDataTree() {
  return request({
    url: '/permission/listAllDataTree',
    method: 'get'
  })
}
// 获取roleId对应的数据权限，如果未配置则获取父节点的数据权限，如果父节点未配置则继续查询上一级节点，直至到根节点获取所有的权限
export function listCascadeData(roleId) {
  return request({
    url: '/permission/listCascadeData/' + (!roleId ? 0 : roleId),
    method: 'get'
  })
}
export function listData(roleId) {
  return request({
    url: '/permission/listData/' + (!roleId ? 0 : roleId),
    method: 'get'
  })
}

export function updatePermission(data) {
  return request({
    url: '/permission/save',
    method: 'post',
    data
  })
}

export function updateRolesPermission(data) {
  return request({
    url: '/permission/updateRolesPermission',
    method: 'post',
    data
  })
}

export function updateRoleRangePermission(roleId) {
  return request({
    url: '/permission/updateRoleRangePermission/' + roleId,
    method: 'post'
  })
}

export function getManagerRoleByRoleId(roleId) {
  return request({
    url: '/permission/getManagerRoleByRoleId/' + (!roleId ? 0 : roleId),
    method: 'post'
  })
}

export function getManagerRoleIds(data) {
  return request({
    url: '/permission/getManagerRoleIds',
    method: 'post',
    data
  })
}

export function getIsAdminatorLogAble(roleId) {
  return request({
    url: '/permission/getIsAdminatorLogAble/' + (!roleId ? 0 : roleId),
    method: 'get'
  })
}

export function listRolePermissionId(data) {
  return request({
    url: '/permission/listId',
    method: 'post',
    data
  })
}
