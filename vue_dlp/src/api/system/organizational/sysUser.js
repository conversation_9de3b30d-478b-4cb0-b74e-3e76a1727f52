
import request from '@/utils/request'
export function getPage(data) {
  return request({
    url: '/sysUser/getPage',
    method: 'post',
    data
  })
}

export function kickoutUser(ids) {
  return request({
    url: `/sysUser/kickOut?ids=${ids}`,
    method: 'post'
  })
}

export function batchLock(data) {
  return request({
    url: `/sysUser/batchToggleLock`,
    method: 'post',
    data
  })
}

export function keepalive() {
  return request({
    url: '/sysUser/keepalive',
    method: 'get'
  })
}

export function getRoleList() {
  return request({
    url: '/sysUser/getRoleList',
    method: 'get'
  })
}

export function getCurrentRoleId() {
  return request({
    url: '/sysUser/getCurrentRoleId',
    method: 'get'
  })
}

export function getCurrentRole() {
  return request({
    url: '/sysUser/getCurrentRole',
    method: 'get'
  })
}

export function changeRole(roleId) {
  return request({
    url: `/sysUser/changeRole?roleId=${roleId}`,
    method: 'post'
  })
}

export function listByRoleId(data) {
  return request({
    url: '/sysUser/listByRoleId',
    method: 'post',
    params: data
  })
}

export function getOnlinePage(data) {
  return request({
    url: '/sysUser/getPage',
    method: 'post',
    data
  })
}

export function getDetail(id) {
  return request({
    url: '/sysUser/get/' + id,
    method: 'get'
  })
}

export function getByAccount(data) {
  return request({
    url: '/sysUser/getByAccount',
    method: 'post',
    params: data
  })
}

export function createUser(data) {
  return request({
    url: '/sysUser/add',
    method: 'post',
    data
  })
}

export function updateUser(data) {
  return request({
    url: '/sysUser/update',
    method: 'post',
    data
  })
}

export function deleteUser(data) {
  return request({
    url: '/sysUser/delete',
    method: 'post',
    data
  })
}

export function isTimeToUpdatePwd() {
  return request({
    url: '/sysUser/isTimeToUpdatePwd',
    method: 'get'
  })
}

export function unlockUser(data) {
  return request({
    url: '/sysUser/unlock',
    method: 'post',
    params: data
  })
}

export function updateValidTime(data) {
  return request({
    url: '/sysUser/updateValidTime',
    method: 'post',
    data
  })
}

export function updateConfig(data) {
  return request({
    url: '/sysUser/updateConfig',
    method: 'post',
    data
  })
}
