import request from '@/utils/request'

export function getThreeUserPage(data) {
  return request({
    url: '/threeUser/getPage',
    method: 'post',
    data
  })
}
export function isSamePassword(data) {
  return request({
    url: '/threeUser/isSamePassword',
    method: 'post',
    data
  })
}

export function updateThreeUser(data) {
  return request({
    url: '/threeUser/update',
    method: 'post',
    data
  })
}
export function deleteThreeUser(data) {
  return request({
    url: '/threeUser/delete',
    method: 'post',
    params: data
  })
}

export function updateMode(data) {
  return request({
    url: '/threeUser/updateMode',
    method: 'post',
    data
  })
}

export function getThreeUserType() {
  return request({
    url: '/threeUser/getType',
    method: 'get'
  })
}

export function getThreeUserByAccount(data) {
  return request({
    url: '/threeUser/getByAccount',
    method: 'post',
    params: data
  })
}

export function getThreeUserByName(data) {
  return request({
    url: '/threeUser/getByName',
    method: 'post',
    params: data
  })
}

export function createThreeUser(data) {
  return request({
    url: '/threeUser/add',
    method: 'post',
    data
  })
}

export function listThreeUserTree() {
  return request({
    url: '/threeUser/listTree',
    method: 'get'
  })
}

export function valiPassword(data) {
  return request({
    url: '/threeUser/valiPassword',
    method: 'post',
    data
  })
}
