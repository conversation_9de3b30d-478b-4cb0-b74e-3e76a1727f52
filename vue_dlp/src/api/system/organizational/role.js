import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/role/getPage',
    method: 'post',
    data
  })
}

export function fetchDetail(id) {
  return request({
    url: '/role/get/' + id,
    method: 'get'
  })
}

export function getByName(data) {
  return request({
    url: '/role/getByName',
    method: 'post',
    params: data
  })
}

export function listChildRoleId(id) {
  return request({
    url: '/role/listChildRoleId/' + id,
    method: 'get'
  })
}

export function getParentRoleById(id) {
  return request({
    url: '/role/getParentRoleById/' + id,
    method: 'get'
  })
}

export function createRole(data) {
  return request({
    url: '/role/add',
    method: 'post',
    data
  })
}

export function updateRole(data) {
  return request({
    url: '/role/update',
    method: 'post',
    data
  })
}

export function updateUser(data) {
  return request({
    url: '/role/updateUser',
    method: 'post',
    data
  })
}

export function deleteRole(data) {
  return request({
    url: '/role/delete',
    method: 'post',
    params: data
  })
}

export function validateDelRole(data) {
  return request({
    url: '/role/validateDel',
    method: 'post',
    params: data
  })
}

export function listSysRoleTreeNode() {
  return request({
    url: '/role/listTree',
    method: 'get'
  })
}

export function listByUserId(userId) {
  return request({
    url: '/role/listByUserId',
    method: 'get',
    params: { userId }
  })
}

export function listPermissionTreeNode() {
  return request({
    url: '/permission/listTreeNode',
    method: 'get'
  })
}

export function updatePermission(data) {
  return request({
    url: '/permission/update',
    method: 'post',
    data
  })
}

export function listRolePermissionId(data) {
  return request({
    url: '/permission/listId',
    method: 'post',
    data
  })
}
