import request from '@/utils/request'
import store from '@/store'
import qs from 'qs'

export function getLogById(data) {
  return request({
    url: '/opLog/getById/' + data,
    method: 'get'
  })
}
export function getLogPage(data) {
  return request({
    url: '/opLog/getPage',
    method: 'post',
    timeout: Math.max(store.getters.requestTimeout, 120000),
    data
  })
}
export function deleteLog(data) {
  return request({
    url: '/opLog/delete',
    method: 'post',
    data
  })
}
export function getImportResultPage(data) {
  return request({
    url: '/opLog/getImportResultPage',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function listDataLogItem(data) {
  return request({
    url: '/opLog/listDataLogItem',
    method: 'post',
    data
  })
}

export function getDataLog(data) {
  return request({
    url: '/opLog/getDataLog',
    method: 'post',
    data
  })
}

export function exportLog(data) {
  return request({
    url: '/opLog/export',
    method: 'post',
    data
  })
}
