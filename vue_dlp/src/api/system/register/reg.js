import request from '@/utils/request'

export function getRegisterStatus(data) {
  return request({
    url: '/register/getStatus',
    method: 'get'
  })
}
export function regEnable() {
  return request({
    url: '/register/regEnable',
    method: 'get'
  })
}

export function isTrialVersion() {
  return request({
    url: '/register/isTrialVersion',
    method: 'get'
  })
}

export function getLeftDaysIn() {
  return request({
    url: '/register/getLeftDaysIn',
    method: 'get'
  })
}
export function getRegisterInfo(data) {
  return request({
    url: '/register/registerInfo',
    method: 'get',
    params: data
  })
}
export function getInfo(data) {
  return request({
    url: '/register/getInfo',
    method: 'get',
    params: data
  })
}
export function getModuleInfoByProdId(prodId) {
  return request({
    url: '/register/getModuleInfo/' + prodId,
    method: 'get'
  })
}
export function getModuleInfoByName(prodId, name) {
  return request({
    url: '/register/getModuleInfoByName?prodId=' + prodId + '&name=' + name,
    method: 'get'
  })
}
export function putInfo(data) {
  return request({
    url: '/register/getMockDataFromMemory',
    method: 'get',
    params: data
  })
}

export function regBySerial(data) {
  return request({
    url: '/register/regBySerial',
    method: 'post',
    data
  })
}

export function regByFile(data) {
  return request({
    method: 'post',
    url: '/register/regByFile',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }}
  )
}
export function getModuleInfo(data) {
  return request({
    url: '/register/moduleInfo',
    method: 'get',
    params: data
  })
}

export function getTerminalModuleInfoByNo(data) {
  return request({
    url: '/register/getTerminalModuleInfo',
    method: 'post',
    params: data
  })
}

export function updateTerminalModules(data) {
  return request({
    url: '/register/updateTerminalModules',
    method: 'post',
    params: data
  })
}

export function delTerminalByNo(data) {
  return request({
    url: '/register/delTerminal',
    method: 'post',
    params: data
  })
}

export function modifyModules(data) {
  return request({
    url: '/register/modifyModules',
    method: 'post',
    params: data
  })
}

export function addDogMemory() {
  return request({
    url: '/register/addDog',
    method: 'get'
  })
}

export function subDogMemory() {
  return request({
    url: '/register/subDog',
    method: 'get'
  })
}

export function getIsWindows() {
  return request({
    url: '/register/isWindows',
    method: 'get'
  })
}

export function resetPassword() {
  return request({
    url: '/register/resetPassword',
    method: 'get'
  })
}

export function getMcode() {
  return request({
    url: '/register/getMcode',
    method: 'get'
  })
}

/**
 * 获取是否是arm国产环境
 * @returns {*}
 */
export function getArmMode() {
  return request({
    url: '/register/isArmMode',
    method: 'get'
  })
}

/**
 * 获取用户编号
 * @returns {AxiosPromise}
 */
export function getUserNo() {
  return request({
    url: '/register/getUserNo',
    method: 'get'
  })
}
