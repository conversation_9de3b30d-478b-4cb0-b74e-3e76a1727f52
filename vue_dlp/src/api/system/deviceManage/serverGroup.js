import request from '@/utils/request'

export function getServerGroupById(id) {
  return request({
    url: '/serverGroup/get/' + id,
    method: 'get'
  })
}

/**
 * 根据采集分组名称获取采集分组信息
 * @param data
 * @returns {AxiosPromise}
 */
export function getServerGroupByName(data) {
  return request({
    url: '/serverGroup/getByName',
    method: 'post',
    params: data
  })
}

export function createServerGroup(data) {
  return request({
    url: '/serverGroup/add',
    method: 'post',
    data
  })
}

export function updateServerGroup(data) {
  return request({
    url: '/serverGroup/update',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/serverGroup/updateServer',
    method: 'post',
    data
  })
}

export function deleteServerGroup(data) {
  return request({
    url: '/serverGroup/delete',
    method: 'post',
    params: data
  })
}

export function getServerGroupTree() {
  return request({
    url: '/serverGroup/listTree',
    method: 'get'
  })
}

export function getServerGroupPage(data) {
  return request({
    url: '/serverGroup/getPage',
    method: 'post',
    data
  })
}

export function validateDelServerGroup(data) {
  return request({
    url: '/serverGroup/validateDel',
    method: 'post',
    params: data
  })
}

export function bindServer(data) {
  return request({
    url: '/serverGroup/bindServer',
    method: 'post',
    data
  })
}

export function getBindServer(id) {
  return request({
    url: '/serverGroup/getBindServer/' + id,
    method: 'get'
  })
}
