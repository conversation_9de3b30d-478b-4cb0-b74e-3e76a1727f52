import request from '@/utils/request'

export function getServerByName(data) {
  return request({
    url: '/mailServer/getByName',
    method: 'post',
    params: data
  })
}

export function createServer(data) {
  return request({
    url: '/mailServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/mailServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/mailServer/delete',
    method: 'post',
    params: data
  })
}

export function getServerPage(data) {
  return request({
    url: '/mailServer/getPage',
    method: 'post',
    data
  })
}

export function checkConnect(data) {
  return request({
    url: '/mailServer/check',
    method: 'post',
    timeout: 600000,
    data
  })
}

export function hasMailServer() {
  return request({
    url: '/mailServer/hasServer',
    method: 'get'
  })
}
