import request from '@/utils/request'

export function getServerByName(data) {
  return request({
    url: '/detectionServer/getByName',
    method: 'post',
    params: data
  })
}

export function createServer(data) {
  return request({
    url: '/detectionServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/detectionServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/detectionServer/delete',
    method: 'post',
    params: data
  })
}

export function bindServer(data) {
  return request({
    url: '/detectionServer/bindServer',
    method: 'post',
    data
  })
}

export function getBindServer(devId) {
  return request({
    url: '/detectionServer/getBindServer/' + devId,
    method: 'get'
  })
}

export function getDbAndDeviceRelation(devId) {
  return request({
    url: '/dbServer/getDbAndDeviceRelation/' + devId,
    method: 'get'
  })
}

export function getServerPage(data) {
  return request({
    url: '/detectionServer/getPage',
    method: 'post',
    data
  })
}

export function listSelectedDetectionServer(data) {
  return request({
    url: '/detectionServer/listDetectionServerByRelatdServerId/' + data,
    method: 'get'
  })
}
