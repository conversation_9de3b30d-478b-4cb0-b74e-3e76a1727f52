import request from '@/utils/request'

export function listDb(data) {
  return request({
    url: '/dbUser/list',
    method: 'post',
    data
  })
}

export function createDbUser(data) {
  return request({
    url: '/dbUser/createDbUser',
    method: 'post',
    data
  })
}

export function updateDbUser(data) {
  return request({
    url: '/dbUser/updateDbUser',
    method: 'post',
    data
  })
}

export function deleteDbUser(data) {
  return request({
    url: '/dbUser/removeDbUser',
    method: 'post',
    data
  })
}

export function getById(dbServerId, username, hostname) {
  return request({
    url: '/dbUser/getById',
    method: 'get',
    params: { dbServerId, username, hostname }
  })
}

export function getDatabaseDevs() {
  return request({
    url: '/dbUser/getDatabaseDevs',
    method: 'post'
  })
}
