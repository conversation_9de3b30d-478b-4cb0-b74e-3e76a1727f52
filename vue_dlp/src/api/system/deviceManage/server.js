import request from '@/utils/request'
import qs from 'qs'

export function getServerPage(data) {
  return request({
    url: '/server/getPage',
    method: 'post',
    data
  })
}

export function allotServer(data) {
  return request({
    url: '/server/allotServer',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getServerByGroup(data) {
  return request({
    url: '/server/getServerByGroupId/' + data,
    method: 'get'
  })
}

export function getServerTree(data) {
  return request({
    url: '/server/getServerTree',
    method: 'post',
    data
  })
}
export function getServerByType(data) {
  return request({
    url: '/server/listServer',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getReflectServerMap() {
  return request({
    url: '/server/getReflectServerMap',
    method: 'get'
  })
}

export function getJvmInfo() {
  return request({
    url: '/server/getJvmInfo',
    method: 'get'
  })
}

export function getDbPoolInfo() {
  return request({
    url: '/server/getDbPoolInfo',
    method: 'get'
  })
}
