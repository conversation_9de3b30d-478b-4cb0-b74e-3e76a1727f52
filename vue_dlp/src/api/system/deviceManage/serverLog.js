import i18n from '@/lang'
import request from '@/utils/request'
import { fetchDownload } from '@/utils/download/helper'

export function getServers() {
  return request.get('/serverlog/servers')
}

export function getServerOnline(devId, devType) {
  // 控制台
  if (devId === 0 || devType === 0) {
    return Promise.resolve(1)
  }
  // 文件溯源服务器的状态引擎还没做，先按在线处理
  if (devId === 201 || devType === 22) {
    return Promise.resolve(1)
  }
  return request.get('/serverlog/online?devId=' + devId).then(res => res.data)
}

export function listFiles(data, cancelToken) {
  return request({
    url: '/serverlog/listFiles',
    method: 'post',
    timeout: 120000,
    cancelToken,
    data
  })
}

export async function extractFile(data, cancelToken) {
  const extractResp = await request({
    url: '/serverlog/extractFile',
    method: 'post',
    timeout: 120000,
    cancelToken,
    data
  })
  if (extractResp.data.endFlag !== 500) {
    return extractResp
  }
  const md5 = extractResp.data.filePathMd5
  let pollingResp
  for (let i = 0; i < 60; i++) {
    pollingResp = await pollingExtractResult(md5, cancelToken)
    if (pollingResp.data.endFlag !== 500) {
      return pollingResp
    }
  }
  return { code: 20000, data: { endFlag: 320 }}
}

function pollingExtractResult(data, cancelToken) {
  return request({
    url: '/serverlog/pollingExtractResult',
    method: 'post',
    headers: { 'Content-Type': 'text/plain' },
    timeout: 120000,
    cancelToken,
    data
  })
}

export function notifyUpload(data) {
  return request({
    url: '/serverlog/notifyUpload',
    method: 'post',
    data
  })
}

export function cancelDownload(filePathMd5, guid) {
  return request({
    url: '/serverlog/cancel',
    method: 'post',
    data: { filePathMd5, guid }
  })
}

const consoleConfigUrl = '/serverlog/config/console'

export function getConsoleLogConfigs(cancelToken) {
  return request.get(consoleConfigUrl, { cancelToken })
}

export function updateConsoleLogConfigs(data, cancelToken) {
  return request({
    url: consoleConfigUrl,
    method: 'post',
    cancelToken,
    data
  })
}

const serverConfigUrl = '/serverlog/config/device'

export function getServerLogConfig(devId, cancelToken) {
  return request.get(`${serverConfigUrl}?devId=${devId}`, { timeout: 120000, cancelToken })
}

export function setServerLogConfig(devId, data, cancelToken) {
  return request({
    url: `${serverConfigUrl}?devId=${devId}`,
    method: 'post',
    cancelToken,
    data
  })
}

export function download(data, file, devId) {
  const filename = encodeURIComponent(data.fileName)
  const url = `/serverlog/download/${devId}/${data.fileMd5}/${filename}`
  fetchDownload({ topic: 'serverlog', url, file, size: data.fileSize })
}

export const ERR_STATUS = {
  0: '未知错误',
  1: i18n.t('text.normal'),
  2: '文件不存在',
  3: '权限不足',
  4: '文件被占用',
  5: '压缩文件失败',
  6: '文件提取失败',
  7: '综合报表接口请求异常',
  100: '就绪',
  110: 'JSON解析异常',
  120: '服务器上传文件失败',
  130: '下载任务已失效，请重新下载',
  300: '控制台与引擎连接异常',
  310: '服务器未上线',
  320: '服务器繁忙，请稍后再试',
  400: '控制台地址未配置',
  500: i18n.t('text.requestTimeout')
}
