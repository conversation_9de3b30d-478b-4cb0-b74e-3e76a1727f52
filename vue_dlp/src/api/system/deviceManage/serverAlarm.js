import request from '@/utils/request';
import qs from 'qs'

export function getServerAlarmTree() {
  return request({
    url: '/serverAlarm/getServerTree',
    method: 'get'
  })
}

export function addServerAlarmStrategy(data) {
  return request({
    url: '/serverAlarm/add',
    method: 'post',
    data
  })
}

export function getStrategyByName(data) {
  return request({
    url: '/serverAlarm/getByName',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getServerAlarmPage(data) {
  return request({
    url: '/serverAlarm/getPage',
    method: 'post',
    data
  })
}

export function deleteServerAlarm(data) {
  return request({
    url: '/serverAlarm/delete',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateServerAlarmStrategy(data) {
  return request({
    url: '/serverAlarm/update',
    method: 'post',
    data
  })
}

export function getOfflineStg() {
  return request({
    url: '/serverAlarm/offlineStg',
    method: 'get'
  })
}

export function updateOfflineStg(data) {
  return request({
    url: '/serverAlarm/updateOfflineStg',
    method: 'post',
    data
  })
}
