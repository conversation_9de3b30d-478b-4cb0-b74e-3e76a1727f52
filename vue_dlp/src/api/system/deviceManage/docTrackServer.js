import request from '@/utils/request'

export function getServer() {
  return request.get('/docTrackServer/getServer')
}

export function updateServer(data) {
  return request({
    url: '/docTrackServer/update',
    method: 'post',
    data
  })
}

export function testTcp(data) {
  return request({
    url: '/docTrackServer/testTcp',
    method: 'post',
    timeout: 0,
    data: `ip=${data.Ip}&ipv6=${data.Ipv6}&port=${data.Port}`
  })
}

export function testHttp(data) {
  return request({
    url: '/docTrackServer/testHttp',
    method: 'post',
    timeout: 0,
    data: `ip=${data.Ip}&ipv6=${data.Ipv6}&port=${data.HttpPort}`
  })
}

export function isSysOptsSupported() {
  return request.get('/docTrackServer/isSysOptsSupported')
}

export function getSysOpts() {
  return request.get('/docTrackServer/getSysOpts')
}

export function setSysOpts(data) {
  return request({
    url: '/docTrackServer/setSysOpts',
    method: 'post',
    data
  })
}

export function getLastRebuildTime() {
  return request.get('/docTrackServer/getLastRebuildTime')
}

export function rebuildWaterIdx() {
  return request.post('/docTrackServer/rebuildWaterIdx')
}
