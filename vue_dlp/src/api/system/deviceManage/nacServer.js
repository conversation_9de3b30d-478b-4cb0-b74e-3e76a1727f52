import request from '@/utils/request'

export function getServerByName(data) {
  return request({
    url: '/nacServer/getByName',
    method: 'post',
    params: data
  })
}

export function createServer(data) {
  return request({
    url: '/nacServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/nacServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/nacServer/delete',
    method: 'post',
    params: data
  })
}
export function unbindNacServer(data) {
  return request({
    url: '/nacServer/unbindNacServer',
    method: 'post',
    params: data
  })
}

export function getServerPage(data) {
  return request({
    url: '/nacServer/getPage',
    method: 'post',
    data
  })
}

export function findProductList(data) {
  return request({
    url: '/nacServer/findProductList',
    method: 'post',
    data
  })
}

export function updateDeptRelatedNac(data) {
  return request({
    url: '/nacServer/updateDeptRelatedNac',
    method: 'post',
    data
  })
}

export function listDeptIdByServerId(data) {
  return request({
    url: '/nacServer/listDeptIdByServerId',
    method: 'post',
    data
  })
}

export function listSelectedNacServer(data) {
  return request({
    url: '/nacServer/listNacServerByRelatdServerId/' + data,
    method: 'get'
  })
}
export function getConsolePort(data) {
  return request({
    url: '/nacServer/getConsolePort',
    method: 'post',
    data
  })
}

export function updateConsolePort(data) {
  return request({
    url: '/nacServer/updateConsolePort',
    method: 'post',
    data
  })
}

export function getNacTokenByTicket(data) {
  return request({
    url: '/nacServer/getNacTokenByTicket',
    method: 'post',
    data
  })
}
