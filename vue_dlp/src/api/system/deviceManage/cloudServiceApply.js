import request from '@/utils/request';

export function pageCloudServiceApply(data) {
  return request({
    url: '/cloudServiceManager/page',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: '/cloudServiceManager/add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/cloudServiceManager/update',
    method: 'post',
    data
  })
}

export function get(id) {
  return request({
    url: '/cloudServiceManager/get?id=' + id,
    method: 'get'
  })
}

export function submit(id) {
  return request({
    url: '/cloudServiceManager/submit?id=' + id,
    method: 'get'
  })
}

export function addAndSubmit(data) {
  return request({
    url: '/cloudServiceManager/addAndSubmit',
    method: 'post',
    data
  })
}

export function deleteApply(id) {
  return request({
    url: '/cloudServiceManager/delete?id=' + id,
    method: 'get'
  })
}

export function sync(id) {
  return request({
    url: '/cloudServiceManager/sync?id=' + id,
    method: 'get'
  })
}

export function listAllServiceCodes() {
  return request({
    url: '/cloudServiceManager/listCloudServiceCodes',
    method: 'get'
  })
}

export function listActivatedServiceCode() {
  return request({
    url: '/cloudServiceManager/listActivatedServiceCode',
    method: 'get'
  })
}

export function activate() {
  return request({
    url: '/cloudServiceManager/activate',
    method: 'get'
  })
}

export function index() {
  return request({
    url: '/cloudServiceManager/index',
    method: 'get'
  })
}

export function testAddress(data) {
  return request({
    url: '/cloudServiceManager/testAddress',
    method: 'post',
    data
  })
}

export function saveAddress(data) {
  return request({
    url: '/cloudServiceManager/saveAddress',
    method: 'post',
    data
  })
}
export function updateCloudServiceConfig(data) {
  return request({
    url: '/cloudServiceManager/updateCloudServiceConfig',
    method: 'post',
    data
  })
}

export function changeCloudEnable(cloudEnable) {
  return request({
    url: '/cloudServiceManager/changeCloudEnable?cloudEnable=' + cloudEnable,
    method: 'post'
  })
}

export function getCloudEnableStatus() {
  return request({
    url: '/cloudServiceManager/cloudEnable',
    method: 'get'
  })
}
