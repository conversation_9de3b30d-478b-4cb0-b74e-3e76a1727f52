import request from '@/utils/request'

export function getServerByName(data) {
  return request({
    url: '/smartBackupServer/getByName',
    method: 'post',
    params: data
  })
}

export function createServer(data) {
  return request({
    url: '/smartBackupServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/smartBackupServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/smartBackupServer/delete',
    method: 'post',
    params: data
  })
}

export function getServerPage(data) {
  return request({
    url: '/smartBackupServer/getPage',
    method: 'post',
    data
  })
}

export function getFileServerPage(data) {
  return request({
    url: '/smartBackupServer/getFileServerPage',
    method: 'post',
    data
  })
}

// 获取智能备份服务器绑定的数据库服务器ID集合
export function getBindServer(devId) {
  return request({
    url: '/smartBackupServer/getBindServer/' + devId,
    method: 'get'
  })
}

// 更新智能备份服务器与数据库服务器的绑定
export function bindServer(data) {
  return request({
    url: '/smartBackupServer/bindServer',
    method: 'post',
    data
  })
}

// 获取智能备份服务器分组绑定的采集组名称集合
export function getSmartGroupRelatedDaqGroup(smartBackupGroupId) {
  return request({
    url: '/smartBackupServer/getSmartGroupRelationDaqGroup/' + smartBackupGroupId,
    method: 'get'
  })
}

// 获取智能备份服务器分组绑定的采集服务器ID和数据库服务器ID集合
export function getSmartGroupRelatedServer(smartBackupGroupId) {
  return request({
    url: '/smartBackupServer/getSmartGroupRelatedServer/' + smartBackupGroupId,
    method: 'get'
  })
}

// 更新智能备份服务器分组与采集服务器和数据库服务器的绑定
export function updateSmartGroupBindServer(data) {
  return request({
    url: '/smartBackupServer/updateSmartGroupBindServer',
    method: 'post',
    data
  })
}

export function getSmartBackupServerGroupTree() {
  return request({
    url: '/smartBackupServer/listTree',
    method: 'get'
  })
}

export function createSmartBackupServerGroup(data) {
  return request({
    url: '/smartBackupServer/addGroup',
    method: 'post',
    data
  })
}

export function updateSmartBackupServerGroup(data) {
  return request({
    url: '/smartBackupServer/updateGroup',
    method: 'post',
    data
  })
}

export function deleteSmartBackupServerGroup(data) {
  return request({
    url: '/smartBackupServer/deleteGroup',
    method: 'post',
    params: data
  })
}

export function getSmartBackupServerGroupByName(data) {
  return request({
    url: '/smartBackupServer/getGroupByName',
    method: 'post',
    params: data
  })
}

export function existSmartBackupServer(groupId) {
  return request({
    url: '/smartBackupServer/existSmartBackupServer/' + groupId,
    method: 'get'
  })
}

export function getHighConfig() {
  return request({
    url: '/smartBackupServer/getHighConfig',
    method: 'get'
  })
}

export function updateHighConfig(data) {
  return request({
    url: '/smartBackupServer/updateHighConfig',
    method: 'post',
    data
  })
}
