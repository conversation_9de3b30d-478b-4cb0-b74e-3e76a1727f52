import request from '@/utils/request'
import qs from 'qs'

export function getServerByName(data) {
  return request({
    url: '/backupServer/getByName',
    method: 'post',
    params: data
  })
}

export function createServer(data) {
  return request({
    url: '/backupServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/backupServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/backupServer/delete',
    method: 'post',
    params: data
  })
}

export function getServerPage(data) {
  return request({
    url: '/backupServer/getPage',
    method: 'post',
    data
  })
}

export function listSelectedBackupServer(data) {
  return request({
    url: '/backupServer/listBackupServerByRelatdServerId/' + data,
    method: 'get'
  })
}

export function getBindAbleBackupServerPage(data) {
  return request({
    url: '/backupServer/getBindAbleBackupServerPage',
    method: 'post',
    data
  })
}

export function listBoundServer(data) {
  return request({
    url: '/backupServer/listBoundServer',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function allotServer(data) {
  return request({
    url: '/backupServer/allotServer',
    method: 'post',
    data: data
  })
}

export function bindServer(data) {
  return request({
    url: '/backupServer/bindServer',
    method: 'post',
    data
  })
}

export function getBindServer(devId) {
  return request({
    url: '/backupServer/getBindServer/' + devId,
    method: 'get'
  })
}

export function getDbAndDeviceRelation(devId) {
  return request({
    url: '/dbServer/getDbAndDeviceRelation/' + devId,
    method: 'get'
  })
}

export function listBusinessByDevId(devId) {
  return request({
    url: '/backupServer/listBusinessByDevId/' + devId,
    method: 'get'
  })
}

export function getPoolInfo() {
  return request({
    url: '/backupServer/getPoolInfo',
    method: 'get'
  })
}

export function getServerByDevId(devId) {
  return request({
    url: '/backupServer/getServerByDevId/' + devId,
    method: 'get'
  })
}
