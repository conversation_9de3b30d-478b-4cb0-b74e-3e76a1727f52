import request from '@/utils/request'

export function getByIpPort(data) {
  return request({
    url: '/dbServer/getByIpPort',
    method: 'post',
    params: data
  })
}

export function getByName(data) {
  return request({
    url: '/dbServer/getByName',
    method: 'post',
    params: data
  })
}

export function testConnection(data) {
  return request({
    url: '/dbServer/testConnection',
    method: 'post',
    data
  })
}

export function createServer(data) {
  return request({
    url: '/dbServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/dbServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/dbServer/delete',
    method: 'post',
    params: data
  })
}

export function bindServer(data) {
  return request({
    url: '/dbServer/bindServer',
    method: 'post',
    data
  })
}

export function getBindServer(devId) {
  return request({
    url: '/dbServer/getBindServer/' + devId,
    method: 'get'
  })
}

export function getDbServerAndDevGroupRelation(devId) {
  return request({
    url: '/dbServer/getDbServerAndDevGroupRelation/' + devId,
    method: 'get'
  })
}

export function getServerPage(data) {
  return request({
    url: '/dbServer/getPage',
    method: 'post',
    data
  })
}
export function getBaseServerPage(data) {
  return request({
    url: '/dbServer/getBasePage',
    method: 'post',
    data
  })
}
export function getShardRule() {
  return request({
    url: '/dbServer/getShardRule',
    method: 'get'
  })
}

export function validateIpPort(data) {
  return request({
    url: '/dbServer/validateIpPort',
    method: 'post',
    data
  })
}

export function cloneDbServer(data) {
  return request({
    url: '/dbServer/cloneDbServer',
    method: 'post',
    data
  })
}

export function getMasterDbType() {
  return request({
    url: '/dbServer/getMasterDbType',
    method: 'get'
  })
}

export function updateDbServerPassword(data) {
  return request({
    url: '/dbServer/updateDbServerPassword',
    method: 'post',
    data,
    timeout: 130000
  })
}

export function getMaxDbPassVer() {
  return request({
    url: '/dbServer/getMaxDbPassVer',
    method: 'get'
  })
}

//  判断是否拥有修改数据库密码的权限(主要是确认是否开启$config，是否为超级管理员）
export function isUpdateDbPassword() {
  return request({
    url: '/dbServer/isUpdateDbPassword',
    method: 'get'
  })
}

//  判断是否拥有修改数据库密码的权限（确认数据库是否都在线，确认数据库的版本最低都为蓝盾系列版本，即2025-D1版本）
export function getUpdateDbPassword() {
  return request({
    url: '/dbServer/getUpdateDbPassword',
    method: 'get'
  })
}

//  获取主数据库Id
export function getMasterDbId() {
  return request({
    url: '/dbServer/getMasterDbId',
    method: 'get'
  })
}

