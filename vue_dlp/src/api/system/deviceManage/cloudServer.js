import request from '@/utils/request';

export function getCloudPlatformAddr() {
  return request({
    url: '/cloudInfo/cloudPlatformAddr',
    method: 'get'
  })
}

export function testCloudPlatformConnect(data) {
  return request({
    url: '/cloudInfo/testCloudPlatformAddr',
    method: 'post',
    data
  })
}

export function testCloudConnect() {
  return request({
    url: '/cloudInfo/testCloudConnect',
    method: 'get'
  })
}

export function saveCloudPlatformAddr(data) {
  return request({
    url: '/cloudInfo/cloudPlatformAddr',
    method: 'post',
    data
  })
}

export function resetSecretKey() {
  return request({
    url: '/cloudInfo/secretKey',
    method: 'post'
  })
}

export function validCloudEnv() {
  return request({
    url: '/cloudInfo/validCloudEnv',
    method: 'get'
  })
}

export function importCert(data) {
  return request({
    url: '/cloudInfo/importCert',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
