import request from '@/utils/request';
import i18n from '@/lang';

/** 获取审批接入设置 **/
export function getConfig() {
  return request({
    url: '/serverAccessApproval/getConfig',
    method: 'get'
  })
}

/** 保存审批接入设置 **/
export function saveConfig(data) {
  return request({
    url: '/serverAccessApproval/saveConfig',
    method: 'post',
    data
  })
}

/** 审批数据-分页查找 **/
export function getPage(data) {
  return request({
    url: '/serverAccessApproval/getPage',
    method: 'post',
    data
  })
}

/** 审批 **/
export function approve(data) {
  return request({
    url: '/serverAccessApproval/approve',
    method: 'post',
    data
  })
}

/** 批量审批 **/
export function batchApprove(data) {
  return request({
    url: '/serverAccessApproval/approve',
    method: 'post',
    data
  })
}

/** 黑名单列表数据 **/
export function blackList(data) {
  return request({
    url: '/serverAccessApproval/blackList',
    method: 'post',
    data
  })
}

/** 根据Id删除黑名单信息 **/
export function removeBlackList(data) {
  return request({
    url: '/serverAccessApproval/removeBlackList',
    method: 'post',
    data
  })
}

/** 服务器审批接入日志表 **/
export function getLogPage(data) {
  return request({
    url: '/serverAccessApproval/getLogPage',
    method: 'post',
    data
  })
}

/**
 * 根据设备类型查询各设备信息
 * @param devType   设备类型
 * @param onlineStatus  1:在线，0：离线，2：全部
 * @param packageType 安装包类型
 */
export function listDevServer(devType, onlineStatus, packageType) {
  //  默认为1-DLP
  if (!packageType) {
    packageType = 1
  }
  return request({
    url: '/serverAccessApproval/listDevServer/' + devType + '/' + onlineStatus + '/' + packageType,
    method: 'get'
  })
}

/**
 * 获取控制台所在环境的IP地址
 * @returns {AxiosPromise}
 */
export function getLocalIp() {
  return request({
    url: '/serverAccessApproval/getLocalIp',
    method: 'get'
  })
}

/**
 * 获取可绑定的在线分部数据库设备信息
 * @Param dbIds    同时查询此映射数据库绑定的分部数据库
 * @return
 */
export function listCanBindBranchDbServer(dbIds) {
  return request({
    url: '/serverAccessApproval/listCanBindBranchDbServer',
    method: 'get',
    params: dbIds ? { dbIds } : {}
  })
}

/**
 * 服务器设备接入日志导出
 * @param data
 */
export function logExport(data) {
  return request({
    url: '/serverAccessApproval/logExport',
    method: 'post',
    data
  })
}

/**
 * 更改各设备服务器的授权状态
 * @param param
 * - devId             设备Id
 * - devType           设备类型
 * - authorizedStatus  授权状态  1-开启，0-关闭
 */
export function updateAuthorizedStatus(param) {
  return request({
    url: '/serverAccessApproval/updateAuthorizedStatus',
    method: 'post',
    params: param
  })
}

/** 服务器接入映射分页查找 **/
export function getMappingPage(data) {
  return request({
    url: '/serverAccessApproval/getMappingPage',
    method: 'post',
    data
  })
}

/**
 * 获取服务器接入审批设置中已配置的主数据库Id
 * @return list  已配置的数据库设备Id
 */
export function getConfigDbIds() {
  return request({
    url: '/serverAccessApproval/getConfigDbIds',
    method: 'get'
  })
}

/**
 * 判断是否拥有设置采集服务器、检测服务器和智能备份服务器的主数据库权限
 * @returns {AxiosPromise}
 */
export function isSetAdminDb() {
  return request({
    url: '/serverAccessApproval/isSetAdminDb',
    method: 'get'
  })
}

/**
 * 根据设备编号获取网卡信息
 * @returns {AxiosPromise}
 */
export function getServerInfoByDevId(devId) {
  return request({
    url: '/serverAccessApproval/getServerInfoByDevId/' + devId,
    method: 'get'
  })
}

/** 服务器审批接入日志表 **/
export function getLoginLogPage(data) {
  return request({
    url: '/serverAccessApproval/getLoginLogPage',
    method: 'post',
    data
  })
}

/**
 * 根据数据库Id查询数据库映射关系中另外一个设备Id信息
 * @returns {AxiosPromise}
 */
export function getRelDbId(devId) {
  return request({
    url: '/serverAccessApproval/getRelDbId/' + devId,
    method: 'get'
  })
}

/**
 * 根据智能备份文件服务器查询绑定的智能备份服务器信息
 * @param fileDevId     智能备份文件服务器设备Id
 * @return
 */
export function getSmartBackupByFileServerId(fileDevId) {
  return request({
    url: '/serverAccessApproval/getSmartBackupByFileServerId/' + fileDevId,
    method: 'get'
  })
}

/**
 * 服务器审批接入支持的设备类型
 */
export function getSupportDevTypes() {
  return [
    { id: 6, label: i18n.t('route.daqServer') },
    { id: 11, label: i18n.t('route.backupServer') },
    { id: 12, label: i18n.t('route.DetectionServer') },
    { id: 172, label: i18n.t('route.softwareServer') },
    { id: 219, label: i18n.t('route.DBServer') },
    { id: 186, label: i18n.t('route.intelligentBackupServer') }
  ];
}

export function getRuleDevTypes() {
  const devTypes = getSupportDevTypes();
  devTypes.push({ id: 0, label: i18n.t('pages.allServer') });
  return devTypes;
}

export function supportDevTypeFormatter(devType, pkgType) {
  const data = getSupportDevTypes().find(data => data.id === devType);
  if (data) {
    //  若为文件服务且是智能备份安装包时
    if (devType === 11 && pkgType && pkgType === 5) {
      //  智能备份-文件服务器
      return i18n.t('pages.smartBackFileServer');
    } else if (devType === 219 && pkgType && pkgType === 5) {
      //  智能备份-数据库服务器
      return i18n.t('pages.smartBackDbServer')
    }
    return data.label;
  }
  return null;
}
