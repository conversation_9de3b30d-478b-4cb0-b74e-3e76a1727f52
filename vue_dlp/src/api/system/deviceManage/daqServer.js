import request from '@/utils/request'
import qs from 'qs'

export function getServerByName(data) {
  return request({
    url: '/daqServer/getByName',
    method: 'post',
    params: data
  })
}

export function createServer(data) {
  return request({
    url: '/daqServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/daqServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/daqServer/delete',
    method: 'post',
    params: data
  })
}

export function getServerPage(data) {
  return request({
    url: '/daqServer/getPage',
    method: 'post',
    data
  })
}

export function allotServer(data) {
  return request({
    url: '/daqServer/allotServer',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function listBoundServer(data) {
  return request({
    url: '/daqServer/listBoundServer',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function listBoundDaqServer(data) {
  return request({
    url: '/daqServer/listBoundDaqServer',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function updateLoadBanlance(data) {
  return request({
    url: '/daqServer/updateLoadBanlance',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function countServerByGroupId(data) {
  return request({
    url: '/daqServer/countByGroupId/' + data,
    method: 'get'
  })
}

export function getServerTree(data) {
  return request({
    url: '/daqServer/listTree',
    method: 'get'
  })
}

export function getBindAbleServerPage(data) {
  return request({
    url: '/daqServer/getBindAbleServerPage',
    method: 'post',
    data
  })
}

export function getBindAbleDaqServerPage(data) {
  return request({
    url: '/daqServer/getBindAbleDaqServerPage',
    method: 'post',
    data
  })
}

export function getDbServerPage(data) {
  return request({
    url: '/dbServer/getPage',
    method: 'post',
    data
  })
}

export function getBackupServerPage(data) {
  return request({
    url: '/backupServer/getPage',
    method: 'post',
    data
  })
}

export function getDetectionServerPage(data) {
  return request({
    url: '/detectionServer/getPage',
    method: 'post',
    data
  })
}

export function bindServer(data) {
  return request({
    url: '/daqServer/bindServer',
    method: 'post',
    data
  })
}

export function getBindServer(devId) {
  return request({
    url: '/daqServer/getBindServer/' + devId,
    method: 'get'
  })
}

export function getRemainConnectionNum(devId) {
  return request({
    url: '/daqServer/getRemainNum/' + devId,
    method: 'get'
  })
}
