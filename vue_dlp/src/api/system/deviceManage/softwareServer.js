import request from '@/utils/request'

export function getServerByName(data) {
  return request({
    url: '/softwareServer/getByName',
    method: 'post',
    params: data
  })
}

export function createServer(data) {
  return request({
    url: '/softwareServer/add',
    method: 'post',
    data
  })
}

export function updateServer(data) {
  return request({
    url: '/softwareServer/update',
    method: 'post',
    data
  })
}

export function deleteServer(data) {
  return request({
    url: '/softwareServer/delete',
    method: 'post',
    params: data
  })
}

export function getServerPage(data) {
  return request({
    url: '/softwareServer/getPage',
    method: 'post',
    data
  })
}

export function listServers() {
  return request.get('/softwareServer/listServers')
}

export function bindServer(data) {
  return request({
    url: '/softwareServer/bindServer',
    method: 'post',
    data
  })
}

export function getDbAndDeviceRelation(devId) {
  return request({
    url: '/dbServer/getDbAndDeviceRelation/' + devId,
    method: 'get'
  })
}

export function getBindServer(devId) {
  return request({
    url: '/softwareServer/getBindServer/' + devId,
    method: 'get'
  })
}
