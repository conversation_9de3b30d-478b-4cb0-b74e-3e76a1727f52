'use strict'
const path = require('path')
const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)
const IS_DEV = ['development'].includes(process.env.NODE_ENV)

const { isShowBathExport, genBatchExportCode } = require('./codegen/gen-batch-export')
if (isShowBathExport()) {
  genBatchExportCode()
}

const MyCodePlugin = require('./codegen/plugin/my-code-plugin')

// 打包实现Gzip压缩
// const CompressionPlugin = require('compression-webpack-plugin');

// 打包压缩混淆 ，打包的时候可以去除console,debugger等
const TerserPlugin = require('terser-webpack-plugin')

const CustomRepositoryUrlPlugin = require('./custom-repository-url-plugin')

function resolve(dir) {
  return path.join(__dirname, dir)
}

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following methods:
// port = 9528 npm run dev OR npm run dev --port = 9528
const port = process.env.port || process.env.npm_config_port || 9528 // dev port

// 所有配置项解释都可以在 https://cli.vuejs.org/config/ 中找到
module.exports = {
  /**
   * 如果您计划在子路径下部署站点，则需要设置publicPath，
   * 例如GitHub页面。如果您计划将站点部署到 https://foo.github.io/bar/ ，
   * 然后将publicPath设置为“/bar/”。
   * 在大多数情况下，请使用'/' !!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: IS_PROD ? './' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: IS_DEV,
  productionSourceMap: false,
  // 为开发环境提供的轻量级服务器
  devServer: {
    host: '::',
    port: port,
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      // change xxx-api/login => mock/login
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        // 代理配置的 target 移至 .env.development 文件
        target: process.env.VUE_APP_PROXY_TARGET,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
      [process.env.VUE_APP_BASE_API + '/websocketJS']: {
        // 代理配置的 target 移至 .env.development 文件
        target: process.env.VUE_APP_PROXY_WEBSOCKET_TARGET,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          ['^' + 'websocketJS']: ''
        }
      }
    }
    // after: require('./mock/mock-server.js')
  },
  configureWebpack: {
    // 在webpack的name字段中提供应用程序的标题，这样就可以在index.html中访问它来注入正确的标题。（由于标题是动态的，移至src/permission.js修改）
    // name: name,
    // cache: false, // 禁用缓存后所有组件都会重新编译
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    resolveLoader: {
      modules: ['./node_modules'/*, './codegen/loader'*/] // 配置loader的查找目录
    },
    plugins: IS_PROD ? [
      // new CompressionPlugin({
      //   algorithm: 'gzip', // 使用gzip压缩
      //   test: /\.js$|\.html$|\.css$/, // 匹配文件名
      //   filename: '[path].gz[query]', // 压缩后的文件名(保持原文件名，后缀加.gz)
      //   minRatio: 1, // 压缩率小于1才会压缩
      //   threshold: 10240, // 对超过10k的数据压缩
      //   deleteOriginalAssets: false // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
      // }),
      new TerserPlugin({
        cache: true,
        sourceMap: false,
        parallel: true,       // 多核打包，提升打包速度
        terserOptions: {
          ecma: undefined,
          warnings: false,
          parse: {},
          compress: {
            drop_console: ['log'],
            drop_debugger: true,
            pure_funcs: null   // 传递一个函数名称数组，打包时将删除这些调用。当前的实现增加了一些开销（压缩会更慢）。
          }
        }
      }),
      new CustomRepositoryUrlPlugin()
    ] : [new MyCodePlugin()]
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    const vueRule = config.module.rule('vue')
    // if (isShowBathExport) {
    //   // 移除 cache-loader, 避免启动项目时虽然审计日志菜单项代码修改了但批量导出模板文件无变化而不重新生成批量导出代码
    //   vueRule.uses.clear()
    // }
    vueRule
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        const opts = options || {}
        const compilerOptions = opts.compilerOptions || {}
        compilerOptions.preserveWhitespace = true // set preserveWhitespace
        opts.compilerOptions = compilerOptions
        return opts
      })
      // .end()
      // .use('my-code-loader')
      // .loader('my-code-loader')
      // .end()

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(IS_DEV,
        config => config.devtool('cheap-source-map')
      )

    config
      .when(!IS_DEV,
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',                  // initial同步，async异步，all同步或者异步
              // maxInitialRequests: 5,          // 入口点处的最大并行请求数。
              // minSize: 30 * 1024,             // 指定拆分出的块的最小大小，如果拆分出的块小于这个值，将不会被拆分。
              // minChunks: 1,                   // 块的最小被引用次数，达到这个次数才会被拆分。
              // maxSize: 1024 * 1024,
              cacheGroups: {                  // 配置缓存组，可以将多个模块合并到一个文件中，避免重复加载。
                qrcodeVendor: {
                  test: /[\\/]node_modules[\\/](qrcodejs2)[\\/]/, // 匹配 QRCode 库
                  name: 'vendor-qrcode', // 固定名称
                  chunks: 'all',
                  priority: 20, // 优先级高于默认 vendors 组
                  reuseExistingChunk: true
                },
                tiffVendor: {
                  test: /[\\/]node_modules[\\/](tiff.js)[\\/]/, // 匹配 tiff 库
                  name: 'vendor-tiff', // 固定名称
                  chunks: 'all',
                  priority: 20, // 优先级高于默认 vendors 组
                  reuseExistingChunk: true
                },
                elementUI: {
                  name: 'chunk-elementUI',    // 将elementUI拆分为一个包
                  priority: 20,               // 权重需要大于lib和app，否则将被打包到lib或app中
                  maxSize: 1024 * 1024,       // 拆分包的大小,将大于maxSize的包拆分为不小于minSize的包
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/  // 为了适应CNPM
                },
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  maxSize: 1024 * 1024,
                  chunks: 'initial'            // 只打包最初需要的第三方依赖项。
                },
                icons: {
                  name: 'chunk-icons',
                  test: resolve('src/icons'),
                  chunks: 'all',               // 处理所有类型模块（同步+异步）
                  minSize: 0,                  // 允许拆分任意大小的模块
                  maxSize: 500 * 1024,
                  priority: 10,
                  reuseExistingChunk: true
                },
                api: {
                  name: 'chunk-api',
                  test: resolve('src/api'),
                  maxSize: 500 * 1024,
                  priority: 9
                },
                utilsDownload: {
                  name: 'chunk-utilsDownload',
                  test: resolve('src/utils/download'),
                  maxSize: 500 * 1024,
                  priority: 9
                },
                components: {
                  name: 'chunk-components',
                  test: resolve('src/components'),
                  maxSize: 500 * 1024,
                  priority: 8
                },
                views: {
                  name: 'chunk-views',
                  test: resolve('src/views'),
                  minChunks: 2,
                  priority: 5,
                  maxSize: 500 * 1024
                }
              }
            })
          config.optimization.runtimeChunk('single')
        }
      )
  },
  // 指定需要被转译的依赖包，将 ES6+ 语法转译成 ES5 语法
  transpileDependencies: ['@antv/g6', 'crypto-js', 'vue-qr', 'js-md5']
}
