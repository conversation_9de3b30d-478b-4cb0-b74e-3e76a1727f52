<!--
	mitm.html is the lite "man in the middle"

	This is only meant to signal the opener's messageChannel to
	the service worker - when that is done this mitm can be closed
    but it's better to keep it alive since this also stops the sw
    from restarting

	The service worker is capable of intercepting all request and fork their
	own "fake" response - wish we are going to craft
	when the worker then receives a stream then the worker will tell the opener
	to open up a link that will start the download
-->
<script>

  // Since we need to wait for the Service Worker registration, we store the message for later
  let messages = []
  window.onmessage = evt => messages.push(evt)

  /**
   * 这个 mitm.html 是在完成 service worker 注册的工作。
   * 而 service worker 注册有很多限制：（为方便说明，下文中 mitm.html 代表“注册 service worker 的位置“，worker.js 代表“service worker 所在的位置“）
   * 1.只能在 HTTPS/localhost 环境下注册（否则报错如 Failed to register a ServiceWorker: An SSL certificate error occurred when fetching the script.）
   * 2.要注册的 worker.js 文件必须和注册它的 mitm.html 同源（否则报错如 Failed to register ServiceWorker. The origin of the provider scriptURL('sw.js 所在的域') does not match the current origin ('mitm.html 所在的域').）
   * 3.service worker 的作用域不能超出 worker.js 自己所在位置（否则报错如 Failed to register a ServiceWorker: The path of the provided scope ('mitm.html 所在的路径') is not under the max scope allowed ('worker.js 所在的路径'). Adjust the scope, move the Service Worker script, or use the Service-Worker-Allowed HTTP header to allow the scope.）
   * @returns {Promise<ServiceWorkerRegistration>}
   */
  function registerWorker() {
    return navigator.serviceWorker.getRegistration('./').then(swReg => {
      return swReg || navigator.serviceWorker.register('worker.js', { scope: './' })
    }).then(swReg => {
      if (swReg.active) {
        return swReg.active
      }
      const swRegTmp = swReg.installing || swReg.waiting
      return new Promise(resolve => {
        const statechangeListener = () => {
          if (swRegTmp.state === 'activated') {
            swRegTmp.removeEventListener('statechange', statechangeListener)
            resolve(swReg.active)
          }
        }
        swRegTmp.addEventListener('statechange', statechangeListener)
      })
    })
  }

  // Now that we have the Service Worker registered we can process messages
  function onMessage (event, sw) {
    let { data, ports, origin } = event

    // It's important to have a messageChannel, don't want to interfere
    // with other simultaneous downloads
    if (!ports || !ports.length) {
      console.warn("[StreamDownload] You didn't send a messageChannel")
      return
    }

    if (typeof data !== 'object') {
      console.warn("[StreamDownload] You didn't send a object")
      return
    }

    // the default public service worker for StreamSaver is shared among others.
    // so all download links needs to be prefixed to avoid any other conflict
    data.origin = origin

    // if we ever (in some feature versoin of streamsaver) would like to
    // redirect back to the page of who initiated a http request
    data.referrer = data.referrer || document.referrer || origin

    // This sends the message data as well as transferring
    // messageChannel.port2 to the service worker. The service worker can
    // then use the transferred port to reply via postMessage(), which
    // will in turn trigger the onmessage handler on messageChannel.port1.
    return sw.postMessage(data, ports)
  }

  if (navigator.serviceWorker) {
    registerWorker().then(sw => {
      window.onmessage = event => onMessage(event, sw)
      messages.forEach(window.onmessage)
    })
  }

</script>
