/* global self ReadableStream Response */

self.addEventListener('install', () => {
  self.skipWaiting()
})

self.addEventListener('activate', event => {
  event.waitUntil(self.clients.claim())
})

// This should be called once per download
// Each event has a dataChannel that the data will be piped through
self.onmessage = event => {
  const data = event.data
  // We send a heartbeat every x second to keep the
  // service worker alive if a transferable stream is not sent
  if (event.data === 'ping') {
    return
  }
  // Make filename RFC5987 compatible
  const filename = encodeURIComponent(data.filename.replace(/\//g, ':'))
    .replace(/['()]/g, escape)
    .replace(/\*/g, '%2A')
  const downloadUrl = `${self.registration.scope}${data.topic || Math.random()}/${filename}`
  const port = event.ports[0]

  port.onmessage = evt => {
    port.onmessage = null
    // Not comfortable letting any user control all headers
    // so we only copy over the length & disposition
    const headers = new Headers({
      'Content-Type': 'application/octet-stream; charset=utf-8',
      'Content-Disposition': "attachment; filename*=utf-8''" + filename,

      // To be on the safe side, The link can be opened in a iframe.
      // but octet-stream should stop it.
      'Content-Security-Policy': "default-src 'none'",
      'X-Content-Security-Policy': "default-src 'none'",
      'X-WebKit-CSP': "default-src 'none'",
      'X-XSS-Protection': '1; mode=block'
    })
    if (data.size != null) {
      headers.set('Content-Length', data.size)
    } else {
      headers.set('Transfer-Encoding', 'chunked')
    }
    const response = new Response(evt.data.readable, { headers })
    caches.open('dlp-download').then(cache => cache.put(downloadUrl, response).catch(e => {
      console.error(e)
      if (e instanceof DOMException && e.message === 'Cache.put() encountered a network error') {
        // https://help.hcltechsw.com/nomad/1.0_web/setup_troubleshooting_nomad.html
        //
        // If you get the Failed to install service worker message outside of a first-time setup
        // (in the browser console, this may appear as Cache.put() encountered a network error),
        // it is most likely caused by a lack of disk space on the machine. The browser needs to
        // download the new files while the existing files are running, which produces two copies
        // of the files until the update is complete. Check the machine's disk space and ensure
        // at least five gigabytes are free.
        console.warn("Check the machine's disk space and ensure at least five gigabytes are free.")
      }
      return Promise.reject(e)
    })).then(() => {
      port.postMessage({ download: downloadUrl })
    })
  }

  port.postMessage({ ready: true })
}

self.onfetch = event => {
  const url = event.request.url
  if (url.indexOf('mitm.html') >= 0) {
    return
  }
  // this only works for Firefox
  if (url.endsWith('/ping')) {
    event.respondWith(new Response('pong'))
    return
  }
  event.respondWith(caches.match(url).then(response => response || new Response(null, { status: 404 })))
}
