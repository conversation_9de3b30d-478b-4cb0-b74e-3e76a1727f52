import java.util.concurrent.*;
import java.util.function.Supplier;

public class TimeoutExecutorV2 {
    
    private final ExecutorService executorService;
    private final ScheduledExecutorService scheduler;
    
    public TimeoutExecutorV2(ExecutorService executorService) {
        this.executorService = executorService;
        this.scheduler = Executors.newScheduledThreadPool(1);
    }
    
    public <T> T executeWithTimeout(Supplier<T> operation, long timeoutMillis, String operationName) throws TimeoutException {
        CompletableFuture<T> future = CompletableFuture.supplyAsync(operation, executorService);
        
        // 设置超时强制终止任务
        ScheduledFuture<?> timeoutTask = scheduler.schedule(() -> {
            if (!future.isDone()) {
                future.cancel(true);
                log.warn("强制终止超时任务: {}", operationName);
            }
        }, timeoutMillis, TimeUnit.MILLISECONDS);
        
        try {
            T result = future.get(timeoutMillis, TimeUnit.MILLISECONDS);
            timeoutTask.cancel(false); // 取消超时任务
            log.debug("操作执行成功: {}", operationName);
            return result;
            
        } catch (TimeoutException e) {
            timeoutTask.cancel(false);
            log.warn("操作执行超时: {} - 超时时间: {}ms", operationName, timeoutMillis);
            throw new TimeoutException("操作执行超时: " + operationName + " (超时时间: " + timeoutMillis + "ms)");
            
        } catch (InterruptedException e) {
            timeoutTask.cancel(false);
            future.cancel(true);
            Thread.currentThread().interrupt();
            log.warn("操作被中断: {}", operationName);
            throw new RuntimeException("操作被中断: " + operationName, e);
            
        } catch (ExecutionException e) {
            timeoutTask.cancel(false);
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            } else {
                throw new RuntimeException("操作执行异常: " + operationName, cause);
            }
        }
    }
    
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
} 