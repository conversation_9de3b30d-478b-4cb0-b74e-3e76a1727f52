import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * 安全的超时执行器
 * 解决原版本中的线程泄漏和资源管理问题
 */
public class TimeoutExecutor {
    
    private static final Logger log = Logger.getLogger(TimeoutExecutor.class.getName());
    
    /**
     * 执行带超时的操作（推荐使用）
     * 为每个任务创建独立的线程池，确保超时后资源被正确释放
     * 
     * @param operation 要执行的操作
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws TimeoutException 超时异常
     */
    public <T> T executeWithTimeoutSafe(Supplier<T> operation, long timeoutMillis, String operationName) throws TimeoutException {
        // 为每个任务创建独立的线程池，避免资源泄漏
        ExecutorService taskExecutor = Executors.newSingleThreadExecutor();
        
        try {
            CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return operation.get();
                } catch (Exception e) {
                    throw new CompletionException(e);
                }
            }, taskExecutor);
            
            try {
                T result = future.get(timeoutMillis, TimeUnit.MILLISECONDS);
                log.fine("操作执行成功: " + operationName);
                return result;
                
            } catch (TimeoutException e) {
                // 取消Future
                boolean cancelled = future.cancel(true);
                log.warning("操作执行超时: " + operationName + " - 超时时间: " + timeoutMillis + "ms, 取消状态: " + cancelled);
                throw new TimeoutException("操作执行超时: " + operationName + " (超时时间: " + timeoutMillis + "ms)");
                
            } catch (InterruptedException e) {
                // 取消Future
                future.cancel(true);
                // 恢复中断状态
                Thread.currentThread().interrupt();
                log.warning("操作被中断: " + operationName);
                throw new RuntimeException("操作被中断: " + operationName, e);
                
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof RuntimeException) {
                    throw (RuntimeException) cause;
                } else {
                    throw new RuntimeException("操作执行异常: " + operationName, cause);
                }
            }
            
        } finally {
            // 确保线程池被关闭
            shutdownExecutor(taskExecutor, operationName);
        }
    }
    
    /**
     * 使用共享线程池执行带超时的操作
     * 适用于轻量级任务，但需要注意线程池资源管理
     * 
     * @param executorService 共享线程池
     * @param operation 要执行的操作
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws TimeoutException 超时异常
     */
    public <T> T executeWithTimeout(ExecutorService executorService, Supplier<T> operation, long timeoutMillis, String operationName) throws TimeoutException {
        // 使用可中断的任务包装
        CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
            try {
                return operation.get();
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        }, executorService);

        try {
            T result = future.get(timeoutMillis, TimeUnit.MILLISECONDS);
            log.fine("操作执行成功: " + operationName);
            return result;

        } catch (TimeoutException e) {
            // 取消Future并尝试中断任务
            boolean cancelled = future.cancel(true);
            log.warning("操作执行超时: " + operationName + " - 超时时间: " + timeoutMillis + "ms, 取消状态: " + cancelled);
            throw new TimeoutException("操作执行超时: " + operationName + " (超时时间: " + timeoutMillis + "ms)");

        } catch (InterruptedException e) {
            // 取消Future
            future.cancel(true);
            // 恢复中断状态
            Thread.currentThread().interrupt();
            log.warning("操作被中断: " + operationName);
            throw new RuntimeException("操作被中断: " + operationName, e);

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            } else {
                throw new RuntimeException("操作执行异常: " + operationName, cause);
            }
        }
    }
    
    /**
     * 安全关闭线程池
     * 
     * @param executor 要关闭的线程池
     * @param operationName 操作名称（用于日志）
     */
    private void shutdownExecutor(ExecutorService executor, String operationName) {
        if (executor == null || executor.isShutdown()) {
            return;
        }
        
        try {
            // 优雅关闭
            executor.shutdown();
            
            // 等待任务完成，最多等待1秒
            if (!executor.awaitTermination(1, TimeUnit.SECONDS)) {
                log.warning("线程池未在1秒内优雅关闭，强制关闭: " + operationName);
                // 强制关闭
                executor.shutdownNow();
                
                // 再次等待，最多等待500毫秒
                if (!executor.awaitTermination(500, TimeUnit.MILLISECONDS)) {
                    log.severe("线程池强制关闭失败: " + operationName);
                }
            }
        } catch (InterruptedException e) {
            log.warning("关闭线程池时被中断: " + operationName);
            // 强制关闭
            executor.shutdownNow();
            // 恢复中断状态
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 执行无返回值的操作
     * 
     * @param operation 要执行的操作
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @throws TimeoutException 超时异常
     */
    public void executeWithTimeoutSafe(Runnable operation, long timeoutMillis, String operationName) throws TimeoutException {
        executeWithTimeoutSafe(() -> {
            operation.run();
            return null;
        }, timeoutMillis, operationName);
    }
    
    /**
     * 使用共享线程池执行无返回值的操作
     * 
     * @param executorService 共享线程池
     * @param operation 要执行的操作
     * @param timeoutMillis 超时时间（毫秒）
     * @param operationName 操作名称（用于日志）
     * @throws TimeoutException 超时异常
     */
    public void executeWithTimeout(ExecutorService executorService, Runnable operation, long timeoutMillis, String operationName) throws TimeoutException {
        executeWithTimeout(executorService, () -> {
            operation.run();
            return null;
        }, timeoutMillis, operationName);
    }
} 